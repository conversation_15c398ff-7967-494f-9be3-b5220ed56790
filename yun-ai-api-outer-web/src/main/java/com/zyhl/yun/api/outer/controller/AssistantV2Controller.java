package com.zyhl.yun.api.outer.controller;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.chatv2.dto.*;
import com.zyhl.yun.api.outer.application.chatv2.service.AlgorithmChatV2ContentService;
import com.zyhl.yun.api.outer.application.chatv2.service.AlgorithmChatV2MessageService;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueResultV2VO;
import com.zyhl.yun.api.outer.application.chatv2.vo.MessageResultV2VO;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.domain.vo.common.PageInfoVO;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 对话相关
 * @Author: WeiJingKun
 */
@Slf4j
@RestController
@RequestMapping(headers = {ReqHeadConst.API_VERSION, ReqHeadConst.CLIENT_INFO, ReqHeadConst.APP_CHANNEL})
public class AssistantV2Controller {

    @Resource
    private AlgorithmChatV2MessageService messageService;

    @Resource
    private AlgorithmChatV2ContentService contentService;

    /**
     * 历史会话列表查询接口
     */
    @PostMapping(value = "/assistant/chat/v2/list", headers = ReqHeadConst.API_VERSION_V1)
    public BaseResult<PageInfoVO<MessageResultV2VO>> chatList(@RequestBody @Valid AlgorithmChatV2ListDTO dto) {
        if (dto == null) {
            dto = new AlgorithmChatV2ListDTO();
        }
        // 参数校验
        dto.validate();
        return BaseResult.success(messageService.chatList(dto));
    }

    /**
     * 历史对话列表查询接口
     * 根据会话id查询相应会话内容（对话）信息。
     */
    @PostMapping(value = "/assistant/chat/v2/contentList", headers = ReqHeadConst.API_VERSION_V1)
    public BaseResult<PageInfoVO<DialogueResultV2VO>> contentList(@RequestBody @Valid AlgorithmChatV2ContentListDTO dto) {
        // 参数校验
        dto.validate();
//        ChatV2ContentListInnerDTO handleDTO = new ChatV2ContentListInnerDTO(dto);
        return BaseResult.success(contentService.contentList(dto));
    }

    /**
     * 查询对话输出接口
     * 根据对话id查询AI助手异步结果信息，并更新对话表输出内容。
     */
    @PostMapping("/assistant/chat/v2/pollingUpdate")
    public BaseResult<DialogueResultV2VO> pollingUpdate(@RequestBody AssistantChatV2PollingUpdateDTO dto) {
        // 参数校验
        dto.validate();
        DialogueResultV2VO vo = contentService.pollingUpdate(dto);
        //清理不需要使用的字段
        vo.cleanField();
        // 是否中断轮巡
        if (isInterrupt(vo)) {
            // 中断
            return BaseResult.error(vo.getResultCode(), vo.getResultMsg(), vo);
        } else {
            return BaseResult.success(vo);
        }
    }

    /**
     * 是否中断轮巡
     * @Author: WeiJingKun
     *
     * @param vo 对话结果
     * @return true-中断轮巡，false-继续轮巡
     */
    private boolean isInterrupt(DialogueResultV2VO vo) {
        if (ObjectUtil.isNotNull(vo)) {
            /** 中断轮巡状态 */
            Integer chatStatus = vo.getChatStatus();
            // 对话状态不为null，且对话失败，中断轮巡
            return ChatStatusEnum.isChatFail(chatStatus);
        }
        return false;
    }

}
