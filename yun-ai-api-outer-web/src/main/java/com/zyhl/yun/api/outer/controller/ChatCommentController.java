package com.zyhl.yun.api.outer.controller;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.ChatCommentDTO;
import com.zyhl.yun.api.outer.application.service.chat.ChatCommentService;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.domain.vo.ChatCommentGetResult;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 对话内容评价Controller
 *
 * <AUTHOR>
 * @version 2024年03月01日 15:33
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(headers = {"x-yun-api-version", "x-yun-client-info", "x-yun-app-channel"})
public class ChatCommentController {


    private final ChatCommentService chatCommentService;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 对话内容评价结果接口
     *
     * @param dto 对话内容评价结果参数
     * @return BaseResult
     */
    @PostMapping("/assistant/chat/comment/add")
    public BaseResult<?> add(@RequestBody @Validated ChatCommentDTO dto) {
        // 参数校验
        final AbstractResultCode check = dto.checkParam();
        if (check != null) {
            log.info("【评论对话】参数校验失败");
            return BaseResult.error(check);
        }

        // 加锁，防止频繁操作
        final RLock lock = redissonClient.getLock(String.format(RedisConstants.COMMENT_LOCK, dto.getDialogueId()));
        try {
            if (!lock.tryLock(RedisConstants.WAIT_TIME_1, RedisConstants.LEASE_TIME_5, TimeUnit.SECONDS)) {
                log.info("【评论对话】正在评论，请稍后再试，用户id：{}", dto.getUserId());
                return BaseResult.error(ResultCodeEnum.REQUEST_TOO_FREQUENTLY);
            }

            // 保存数据库
            try {
                boolean result = chatCommentService.add(dto);
                log.info("【评论对话】结果：{}", result);
            } catch (Exception e) {
                if (e instanceof YunAiBusinessException) {
                    return BaseResult.error(((YunAiBusinessException) e).getExceptionEnum());
                } else {
                    log.error(e.getMessage(), e);
                    return BaseResult.error(ResultCodeEnum.UNKNOWN_ERROR);
                }
            }
        } catch (Exception e) {
            log.error("【评论对话】评论获取锁异常：{}", e.getMessage(), e);
            return BaseResult.error(ResultCodeEnum.UNKNOWN_ERROR);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        return BaseResult.success();
    }

    /**
     * 对话内容评价结果查询接口
     *
     * @param dto 对话内容评价结果查询参数
     * @return BaseResult
     */
    @PostMapping("/assistant/chat/comment/get")
    public BaseResult<List<ChatCommentGetResult>> get(@RequestBody ChatCommentDTO dto) {
        // 参数校验
        if (dto.getSessionId() == null) {
            log.info("【评论结果】会话id为空");
            return BaseResult.error(ResultCodeEnum.ERROR_PARAMS);
        }
        final AbstractResultCode check = dto.checkUserId();
        if (check != null) {
            log.info("【评论结果】参数校验失败");
            return BaseResult.error(check);
        }

        return BaseResult.success(chatCommentService.get(dto));
    }

}
