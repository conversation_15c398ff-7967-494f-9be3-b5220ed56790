package com.zyhl.yun.api.outer.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;


/**
 * 用户域feign配置类
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(
		prefix = "hcy.auth-interceptor"
)
public class InterceptorWhiteConfig {

	/**
	 * 不需要拦截的路径
	 */
	private List<String> excludePath;

	/**
	 * 拦截的路径
	 */
	private List<String> whitePath;

	public List<String> getWhitePath() {
		return whitePath;
	}

	public void setWhitePath(List<String> whitePath) {
		this.whitePath = whitePath;
	}

	public List<String> getExcludePath() {
		return excludePath;
	}

	public void setExcludePath(List<String> excludePath) {
		this.excludePath = excludePath;
	}
}
