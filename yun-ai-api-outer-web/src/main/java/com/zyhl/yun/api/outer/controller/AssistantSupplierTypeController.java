package com.zyhl.yun.api.outer.controller;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.python.jline.internal.Log;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.enums.SupplierTypeEnum;
import com.zyhl.yun.api.outer.application.config.OuterToolAesConfig;
import com.zyhl.yun.api.outer.application.dto.AssistantSupplierTypeDTO;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 助手厂商控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(headers = { ReqHeadConst.API_VERSION })
public class AssistantSupplierTypeController {

	@Resource
	private OuterToolAesConfig outerToolAesConfig;
	@Resource
	private SourceChannelsProperties sourceChannelsProperties;

	/**
	 * 获取厂商token
	 */
	@SuppressWarnings("unchecked")
	@PostMapping(value = "/assistant/getSupplierTypeToken", headers = ReqHeadConst.API_VERSION_V1)
	public BaseResult<String> getSupplierTypeToken(@RequestBody @Valid AssistantSupplierTypeDTO dto) {
		String token = null;
		if (!sourceChannelsProperties.isExist(dto.getSourceChannel())) {
			log.info("渠道来源不存在：{}", dto.getSourceChannel());
			return BaseResult.error(ResultCodeEnum.ERROR_PARAMS);
		}
		try {
			if (String.valueOf(SupplierTypeEnum.ALI.getCode()).equals(dto.getSupplierType())) {
				token = outerToolAesConfig.getAES().encryptBase64(RequestContextHolder.getToken());
			}
		} catch (Exception e) {
			Log.error("getSupplierTypeToken dto:{}, error:", JSONUtil.toJsonStr(dto), e);
		}
		return BaseResult.success(token);
	}
}
