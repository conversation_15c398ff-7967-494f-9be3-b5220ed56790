package com.zyhl.yun.api.outer.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.FileOperationUtil;
import com.zyhl.yun.api.outer.anno.SSEApplicationAnnotation;
import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.ChatAddVoConvertor;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.handle.chat.AbstractChatAddHandler;
import com.zyhl.yun.api.outer.application.service.chat.HistoryDialogInfoRedisService;
import com.zyhl.yun.api.outer.application.util.SseEmitterDataUtils;
import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;
import com.zyhl.yun.api.outer.application.vo.ChatAddBaseVO;
import com.zyhl.yun.api.outer.config.FlowTypeProperties;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddResultTypeEnum;
import com.zyhl.yun.api.outer.exception.SseApplicationException;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * AI助手对话接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class ChatAddController {

    @Resource
    private List<AbstractChatAddHandler> chatAddHandlerList;
    @Resource
    private ChatAddVoConvertor chatAddVoConvertor;
    @Resource
    private FlowTypeProperties flowTypeProperties;
    @Resource
    private HistoryDialogInfoRedisService historyRedisService;

    /**
     * 初始化处理器列表，排序
     */
    @PostConstruct
    private void init() {
        chatAddHandlerList.sort(Comparator.comparing(AbstractChatAddHandler::order));
    }


    /**
     * 会话输入接口 V1 实时接口返回，对话生成的文件存云盘
     *
     * @param dto 会话输入参数DTO
     * @return 公共返回结果Result
     */
    @PostMapping(value = "/assistant/chat/add", headers = ReqHeadConst.API_VERSION_V1)
    public BaseResult<ChatAddBaseVO> chatAddV1(@RequestBody @Valid AlgorithmChatAddDTO dto) {
        ChatAddInnerDTO innerDTO = new ChatAddInnerDTO(dto, null);
        for (AbstractChatAddHandler handler : chatAddHandlerList) {
            long startTime = System.currentTimeMillis();
            try {
                if (handler.execute(innerDTO) && !handler.run(innerDTO)) {
                    break;
                }
            } catch (YunAiBusinessException e) {
                if (!CharSequenceUtil.isEmpty(e.getCode())) {
                    return BaseResult.error(e.getCode(), e.getMessage());
                }
                AbstractResultCode resultCode = e.getExceptionEnum() == null ? ResultCodeEnum.UNKNOWN_ERROR : e.getExceptionEnum();

                return BaseResult.error(resultCode);
            } catch (Exception e) {
                log.error("AI助手对话异常，异常信息：{}", e.getMessage(), e);
                return BaseResult.error(ResultCodeEnum.UNKNOWN_ERROR);
            } finally {
                MDC.put(LogConstants.DURATION, String.valueOf(System.currentTimeMillis() - startTime));
                log.info("执行处理器：{}，耗时：{}ms", handler.getClass().getSimpleName(), System.currentTimeMillis() - startTime);
                MDC.remove(LogConstants.DURATION);
            }
        }

        // 响应数据
        AlgorithmChatAddVO vo = innerDTO.getRespParams();
        return BaseResult.success(chatAddVoConvertor.chatAddBaseToVO(vo));
    }

    /**
     * 会话输入接口 V2 流式接口返回，对话生成的文件存云盘
     *
     * @param dto 会话输入参数DTO
     * @return SseEmitter对象
     */
    @SSEApplicationAnnotation
    @PostMapping(value = "/assistant/chat/add", headers = ReqHeadConst.API_VERSION_V2, produces = {MediaType.TEXT_EVENT_STREAM_VALUE})
    public SseEmitter chatAddV2(@RequestBody @Valid AlgorithmChatAddDTO dto) {
        return sseHandle(dto);
    }

    /**
     * 会话输入接口 V3 实时接口返回，对话生成的文件存EOS  V3先不上线 2024-06-07
     *
     * @param dto 会话输入参数DTO
     * @return 公共返回结果Result
     */
    /*@PostMapping(value = "/assistant/chat/add", headers = "x-yun-api-version=v3")
    public BaseResult<?> chatAddV3(@RequestBody @Valid AlgorithmChatAddDTO dto) {
        return packageService.chatAddPackage(dto);
    }*/

    /**
     * 会话输入接口 V4 流式接口返回，对话生成的文件存EOS
     *
     * @param dto 会话输入参数DTO
     * @return SseEmitter对象
     */
    @SSEApplicationAnnotation
    @PostMapping(value = "/assistant/chat/add", headers = ReqHeadConst.API_VERSION_V4, produces = {MediaType.TEXT_EVENT_STREAM_VALUE})
    public SseEmitter chatAddV4(@RequestBody @Valid AlgorithmChatAddDTO dto) {
        return sseHandle(dto);
    }

    /**
     * 会话输入接口 V6
     * 1、流式接口返回
     * 2、对话生成的文件存EOS
     * 3、支持联网搜索结果返回，正式回答和思维链拆分，流式对话每次带编号
     *
     * @param dto 会话输入参数DTO
     * @return SseEmitter对象
     */
    @SSEApplicationAnnotation
    @PostMapping(value = "/assistant/chat/add", headers = ReqHeadConst.API_VERSION_V6, produces = {MediaType.TEXT_EVENT_STREAM_VALUE})
    public SseEmitter chatAddV6(@RequestBody @Valid AlgorithmChatAddDTO dto) {
        return sseHandle(dto);
    }

    /**
     * 流式对话处理
     *
     * @param dto
     * @return
     */
    private SseEmitter sseHandle(AlgorithmChatAddDTO dto) {
        SseEmitter sseEmitter = new SseEmitter(flowTypeProperties.getTimeout());
        AtomicBoolean completeFlag = new AtomicBoolean();

        // 鉴权异常信息
        SseApplicationException sseException = RequestContextHolder.getSseException();
        if (null != sseException) {
            SseEmitterDataUtils.sendMsgAndCompleteOthers(sseEmitter, BaseResult.error(sseException.getCode(), sseException.getMessage()), completeFlag);
            return sseEmitter;
        }

        // 执行业务逻辑
        ChatAddInnerDTO innerDTO = new ChatAddInnerDTO(dto, sseEmitter);
        try {
            for (AbstractChatAddHandler handler : chatAddHandlerList) {
                long startTime = System.currentTimeMillis();
                try {
                    if (handler.execute(innerDTO) && !handler.run(innerDTO)) {
                        // 保存redis历史对话信息
                        historyRedisService.setRedisHistoryDialogInfo(innerDTO);
                        break;
                    }
                } catch (YunAiBusinessException e) {
                    if (!CharSequenceUtil.isEmpty(e.getCode())) {
                        if (AbstractChatAddHandler.isTextModelHandler(handler)) {
                            SseEmitterDataUtils.sendMsgAndComplete(sseEmitter, BaseResult.error(e.getCode(), e.getMessage()), completeFlag);
                        } else {
                            SseEmitterDataUtils.sendMsgAndCompleteOthers(sseEmitter, BaseResult.error(e.getCode(), e.getMessage()), completeFlag);
                        }
                    } else {
                        if (AbstractChatAddHandler.isTextModelHandler(handler)) {
                            SseEmitterDataUtils.sendMsgAndComplete(sseEmitter, BaseResult.error(ResultCodeEnum.UNKNOWN_ERROR), completeFlag);
                        } else {
                            SseEmitterDataUtils.sendMsgAndCompleteOthers(sseEmitter, BaseResult.error(ResultCodeEnum.UNKNOWN_ERROR), completeFlag);
                        }
                    }
                    return sseEmitter;
                } catch (Exception e) {
                    log.error("AI助手对话异常，异常信息：{}", e.getMessage(), e);
                    SseEmitterDataUtils.sendMsgAndComplete(sseEmitter, BaseResult.error(ResultCodeEnum.UNKNOWN_ERROR), completeFlag);
                    return sseEmitter;
                } finally {
                    MDC.put(LogConstants.DURATION, String.valueOf(System.currentTimeMillis() - startTime));
                    log.info("执行处理器：{}，耗时：{}ms", handler.getClass().getSimpleName(), System.currentTimeMillis() - startTime);
                    MDC.remove(LogConstants.DURATION);
                }
            }
        } finally {
            if (null != dto.getContent()) {
                // 删除本地共享存储文件（文档上传场景会存入共享存储）
                FileOperationUtil.deleteLocalFiles(dto.getContent().getLongTextFileLocalPathList());
            }
        }

        // 响应数据
        AlgorithmChatAddVO vo = innerDTO.getRespParams();
        if (ChatAddResultTypeEnum.isSynchronous(vo.getResultType()) || ChatAddResultTypeEnum.isAsynchronous(vo.getResultType())) {
            SseEmitterDataUtils.sendMsgAndComplete(sseEmitter, BaseResult.success(vo), completeFlag);
        }
        return sseEmitter;
    }

}
