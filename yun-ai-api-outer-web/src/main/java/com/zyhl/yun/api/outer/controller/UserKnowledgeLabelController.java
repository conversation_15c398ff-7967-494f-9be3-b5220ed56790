package com.zyhl.yun.api.outer.controller;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeLabelAddReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeLabelDeleteReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeLabelListReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeLabelSortReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.UserKnowledgeLabelService;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.controller.validate.UserKnowledgeLabelValid;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.zyhl.yun.api.outer.constants.Const.NUM_16;

/**
 * 个人知识库标签控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(headers = {ReqHeadConst.CLIENT_INFO, ReqHeadConst.APP_CHANNEL})
public class UserKnowledgeLabelController {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private UserKnowledgeLabelService userKnowledgeLabelService;
    @Resource
    private UserKnowledgeLabelValid userKnowledgeLabelValid;

    /**
     * 标签保存
     *
     * @param dto 入参
     * @return
     */
    @PostMapping(value = "/assistant/knowledge/personal/label/save", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<Object> add(@RequestBody KnowledgeLabelAddReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeLabelValid.addValid(dto);
        if (check != null) {
            log.warn("【知识库标签保存】参数校验失败");
            return BaseResult.error(check);
        }

        // 加锁，防止频繁报名
        final RLock lock = redissonClient.getLock(String.format(RedisConstants.KNOWLEDGE_LABEL_LOCK, dto.getUserId()));
        try {
            if (!lock.tryLock(RedisConstants.WAIT_TIME_1, RedisConstants.LEASE_TIME_5, TimeUnit.SECONDS)) {
                log.warn("【知识库标签保存】正在保存标签，请稍后再试，用户id：{}", dto.getUserId());
                return BaseResult.error(ResultCodeEnum.REQUEST_TOO_FREQUENTLY);
            }

            // 保存数据库
            Map<String, Object> result = new HashMap<>(NUM_16);
            result.put("labelId", String.valueOf(userKnowledgeLabelService.save(dto)));

            return BaseResult.success(result);
        } catch (InterruptedException e) {
            log.error("【知识库标签保存】保存失败：{}", e.getMessage(), e);
            return BaseResult.error(ResultCodeEnum.UNKNOWN_ERROR);
        } catch (Exception e) {
            log.error("【知识库标签保存】保存失败：{}", e.getMessage(), e);
            if (e instanceof YunAiBusinessException) {
                return BaseResult.error(((YunAiBusinessException) e).getExceptionEnum());
            }
            return BaseResult.error(ResultCodeEnum.UNKNOWN_ERROR);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 标签删除
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/assistant/knowledge/personal/label/delete", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<Object> delete(@RequestBody KnowledgeLabelDeleteReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeLabelValid.deleteValid(dto);
        if (check != null) {
            log.warn("【知识库标签删除】参数校验失败");
            return BaseResult.error(check);
        }

        // 删除
        userKnowledgeLabelService.delete(dto);

        return BaseResult.success();
    }

    /**
     * 标签列表
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/assistant/knowledge/personal/label/list", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<Object> list(@RequestBody KnowledgeLabelListReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeLabelValid.listValid(dto);
        if (check != null) {
            log.warn("【知识库标签列表】参数校验失败");
            return BaseResult.error(check);
        }

        // 响应结果
        final Map<String, Object> result = new HashMap<>(NUM_16);
        result.put("labelList", userKnowledgeLabelService.list(dto));

        return BaseResult.success(result);
    }

    /**
     * 标签排序
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/assistant/knowledge/personal/label/sort", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<Object> sort(@RequestBody KnowledgeLabelSortReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeLabelValid.sortValid(dto);
        if (check != null) {
            log.warn("【知识库标签排序】参数校验失败");
            return BaseResult.error(check);
        }

        // 排序
        userKnowledgeLabelService.sort(dto);

        return BaseResult.success();
    }

}
