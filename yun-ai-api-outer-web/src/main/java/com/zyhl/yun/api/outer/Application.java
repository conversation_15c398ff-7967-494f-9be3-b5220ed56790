package com.zyhl.yun.api.outer;

import java.net.InetAddress;
import java.util.Arrays;
import java.util.Collection;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableScheduling;

//@ComponentScan
//@SpringBootApplication(exclude = { com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure.class,DataSourceAutoConfiguration.class })
/**
 * 包扫描，除了自己的项目：com.zyhl.yun.api.outer
 * 还包含common包的注解：com.zyhl.hcy.yun.ai.common.base.annotation
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages={"com.zyhl.yun.api.outer",
        "com.zyhl.hcy.yun.ai.common.base.annotation"
})
@EnableFeignClients(basePackages = {"com.zyhl.yun.api.outer",
        "com.zyhl.hcy.yun.ai.common.platform.third.client.note",
        "com.zyhl.hcy.yun.ai.common.platform.third.client.mail",
        "com.zyhl.hcy.yun.ai.common.platform.third.client.check",
        "com.zyhl.hcy.yun.ai.common.model.api.client.cmicdialogue.feignclient",
        "com.zyhl.hcy.yun.ai.common.model.api.client.centertask",
        "com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk",
        "com.zyhl.hcy.yun.ai.common.platform.third.client.yunuser",
        "com.zyhl.hcy.yun.ai.common.platform.third.client.membercenter",
        "com.zyhl.hcy.yun.ai.common.model.api.client.cmictext",
        "com.zyhl.hcy.yun.ai.common.model.api.client.cmicocr",
        "com.zyhl.hcy.yun.ai.common.model.api.client.ali",
        "com.zyhl.hcy.yun.ai.common.model.api.client.alippt"
})
@EnableConfigurationProperties
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableScheduling

public class Application {

    private static final Logger log = LoggerFactory.getLogger(Application.class);


    @Autowired
    private Environment env;

    @PostConstruct
    public void initApplication() {
        log.info("Running with Spring profile(s) :{} ", Arrays.asList(env.getActiveProfiles()));
        Collection<String> activeProfiles = Arrays.asList(env.getActiveProfiles());
        if (activeProfiles.contains(ConfigConstant.SPRING_PROFILE_DEVELOPMENT) && activeProfiles.contains(ConfigConstant.SPRING_PROFILE_PRODUCTION)) {
            log.error("You have error configured your application! It should not run " +
                    "with both the 'dev' and 'prod' profiles at the same time.");
        }
        if (activeProfiles.contains(ConfigConstant.SPRING_PROFILE_DEVELOPMENT) && activeProfiles.contains(ConfigConstant.SPRING_PROFILE_CLOUD)) {
            log.error("You have error configured your application! It should not" +
                    "run with both the 'dev' and 'cloud' profiles at the same time.");
        }
    }

    /**
     * Main method, used to run the application.
     *
     * @param args the vo line arguments
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {

        SpringApplication app = new SpringApplication(Application.class);

        ApplicationContext appContext = app.run(args);

        Environment env = appContext.getEnvironment();

        log.info("\n----------------------------------------------------------\n\t" +
                        "Application '{}' is running! Access URLs:\n\t" +
                        "Local: \t\thttp://localhost:{}\n\t" +
                        "External: \thttp://{}:{}\n----------------------------------------------------------",
                env.getProperty("spring.application.name"),
                env.getProperty("server.port"),
                InetAddress.getLocalHost().getHostAddress(),
                env.getProperty("server.port"));


    }
}
