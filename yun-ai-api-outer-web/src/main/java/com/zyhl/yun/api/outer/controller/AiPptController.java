package com.zyhl.yun.api.outer.controller;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.plugin.logger.annotation.LogAnnotation;
import com.zyhl.hcy.plugin.logger.enums.LogType;
import com.zyhl.yun.api.outer.application.dto.AiPptContentSwitchReqDTO;
import com.zyhl.yun.api.outer.application.dto.AiPptContentSwitchRespDTO;
import com.zyhl.yun.api.outer.application.dto.AiPptFileSaveReqDTO;
import com.zyhl.yun.api.outer.application.dto.AiPptResultInformReqDTO;
import com.zyhl.yun.api.outer.application.dto.AiPptResultInformRespDTO;
import com.zyhl.yun.api.outer.application.service.AiPptService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.domain.dto.aippt.AipptCodeGetRequestDTO;
import com.zyhl.yun.api.outer.domain.vo.aippt.AipptCodeGetResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * AIPPT控制器
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@RestController
@RequestMapping(headers = {ReqHeadConst.API_VERSION, ReqHeadConst.CLIENT_INFO, ReqHeadConst.APP_CHANNEL})
public class AiPptController {

    @Resource
    private AiPptService aiPptService;

    @Resource
    private DataSaveService dataSaveService;

    /**
     * AIPPT结果通知事件接口
     *
     * @param reqDTO 请求参数
     * @return PPT对话ID响应
     */
    @LogAnnotation(LogType.INTERFACE)
    @PostMapping("/assistant/aippt/pixelbloom/result/inform")
    public BaseResult<AiPptResultInformRespDTO> resultInform(@RequestBody @Valid AiPptResultInformReqDTO reqDTO) {
        log.info("接收到AIPPT结果通知事件请求, contentDialogueId: {}, taskId: {}", reqDTO.getContentDialogueId(), reqDTO.getTaskId());
        AiPptResultInformRespDTO respDTO = aiPptService.resultInform(reqDTO);
        return BaseResult.success(respDTO);
    }

    /**
     * AIPPT文件保存接口
     *
     * @param reqDTO 请求参数
     * @return 操作结果
     */
    @LogAnnotation(LogType.INTERFACE)
    @PostMapping("/assistant/aippt/pixelbloom/result/file/save")
    public BaseResult<?> fileSave(@RequestBody @Valid AiPptFileSaveReqDTO reqDTO) {
        log.info("接收到AIPPT文件保存请求, dialogueId: {}, designId: {}",
                reqDTO.getDialogueId(), reqDTO.getDesignId());

        aiPptService.fileSave(reqDTO);
        return BaseResult.success();
    }

    /**
     * 获取鉴权code接口
     *
     * @param requestDTO 请求参数
     * @return 鉴权code响应
     */
    @LogAnnotation(LogType.INTERFACE)
    @PostMapping(value = "/assistant/aippt/pixelbloom/code/get", headers = "x-yun-api-version=v1")
    public BaseResult<AipptCodeGetResponseVO> getCode(@RequestBody @Valid AipptCodeGetRequestDTO requestDTO) {
        log.info("接收到AiPPT获取Code请求, sourceChannel: {}, userId: {}",
                requestDTO.getSourceChannel(), requestDTO.getUserId());

        AipptCodeGetResponseVO responseVO = aiPptService.getCode(requestDTO);
        return BaseResult.success(responseVO);
    }

    /**
     * HBase测试查询接口
     *
     * @param userId 用户ID
     * @param dialogueId 对话ID
     * @return HBase查询结果
     */
    @LogAnnotation(LogType.INTERFACE)
    @PostMapping("/assistant/aippt/hbase/test")
    public BaseResult<AiTextResultEntity> hbaseTest(@RequestParam String userId, @RequestParam String dialogueId) {
        log.info("接收到HBase测试查询请求, userId: {}, dialogueId: {}", userId, dialogueId);

        AiTextResultEntity hbaseResult = dataSaveService.getHbaseResult(userId, dialogueId);
        return BaseResult.success(hbaseResult);
    }

    /**
     * AIPPT内容格式转换接口
     *
     * @param reqDTO 请求参数
     * @return 转换后的内容响应
     */
    @LogAnnotation(LogType.INTERFACE)
    @PostMapping("/assistant/aippt/pixelbloom/content/switch")
    public BaseResult<AiPptContentSwitchRespDTO> contentSwitch(@RequestBody @Valid AiPptContentSwitchReqDTO reqDTO) {
        log.info("接收到AIPPT内容格式转换请求, sourceChannel: {}, userId: {}, content长度: {}",
                reqDTO.getSourceChannel(), reqDTO.getUserId(),
                reqDTO.getContent() != null ? reqDTO.getContent().length() : 0);

        AiPptContentSwitchRespDTO respDTO = aiPptService.contentSwitch(reqDTO);
        return BaseResult.success(respDTO);
    }
}