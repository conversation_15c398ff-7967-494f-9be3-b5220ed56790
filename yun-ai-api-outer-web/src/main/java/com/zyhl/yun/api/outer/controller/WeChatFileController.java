package com.zyhl.yun.api.outer.controller;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.plugin.logger.annotation.LogAnnotation;
import com.zyhl.hcy.plugin.logger.enums.LogType;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.FileCompleteDTO;
import com.zyhl.yun.api.outer.application.dto.FileCreateDTO;
import com.zyhl.yun.api.outer.application.dto.GetSharpUploadUrlReqDTO;
import com.zyhl.yun.api.outer.application.service.WeChatFileService;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.domain.vo.FileCompleteVO;
import com.zyhl.yun.api.outer.domain.vo.FileCreateVO;
import com.zyhl.yun.api.outer.domain.vo.GetSharpUploadUrlReqVO;
import com.zyhl.yun.api.outer.enums.AiResultCode;

import javax.annotation.Resource;
import javax.validation.Valid;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description}
 * <p>
 *
 * <AUTHOR>
 * @since 4月17 2025
 */
@Slf4j
@RestController
public class WeChatFileController {
    @Resource
    private WeChatFileService weChatFileService;

    /**
     * 文件上传一阶段：创建文件
     */
    @LogAnnotation(LogType.INTERFACE)
    @PostMapping(value = "/assistant/knowledge/personal/v2/file/upload/createFile", headers = {
            ReqHeadConst.API_VERSION_V1})
    public BaseResult<FileCreateVO> createFile(@RequestBody @Valid FileCreateDTO dto) {
        FileCreateVO file = null;
        try {
            file = weChatFileService.createFile(dto);
        } catch (Exception e) {
            if (e instanceof YunAiBusinessException) {
                YunAiBusinessException ex = (YunAiBusinessException) e;
                return BaseResult.error(ex.getCode(), ex.getMessage());
            }
            log.error("创建文件异常：", e);
            return BaseResult.error(AiResultCode.CODE_9999.getCode(), AiResultCode.CODE_9999.getMsg());
        }
        return BaseResult.success(file);
    }

    /**
     * 文件上传三阶段：完成上传
     */
    @LogAnnotation(LogType.INTERFACE)
    @PostMapping(value = "/assistant/knowledge/personal/v2/file/upload/complete", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<FileCompleteVO> completeFile(@RequestBody @Valid FileCompleteDTO dto) {
        FileCompleteVO file = null;
        try {
            file = weChatFileService.completeFile(dto);
        } catch (Exception e) {
            if (e instanceof YunAiBusinessException) {
                YunAiBusinessException ex = (YunAiBusinessException) e;
                return BaseResult.error(ex.getCode(), ex.getMessage());
            }
            log.error("创建文件异常：", e);
            return BaseResult.error(AiResultCode.CODE_9999.getCode(), AiResultCode.CODE_9999.getMsg());
        }
        return BaseResult.success(file);
    }

    /**
     * 获取个人知识库文件分片上传地址接口
     * 根据上传 ID 获取指定文件的上传地址，上传链接失效后，可调用该接口重新获取上传地址。
     * 注：仅支持分片上传任务。
     *
     * @param dto 请求参数
     * @return 返回结果
     */
    @LogAnnotation(LogType.INTERFACE)
    @PostMapping(value = "/assistant/knowledge/personal/v2/file/upload/getUploadUrl", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<GetSharpUploadUrlReqVO> getUploadUrl(@RequestBody @Valid GetSharpUploadUrlReqDTO dto) {
        GetSharpUploadUrlReqVO vo = weChatFileService.getUploadUrl(dto);
        log.info("【获取文件上传地址】流程执行完毕，结果：{}", vo);
        return BaseResult.success(vo);
    }
}
