package com.zyhl.yun.api.outer.config;


import com.zyhl.yun.api.outer.application.service.external.UserAuthService;
import com.zyhl.yun.api.outer.global.AiOuterAuthInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 认证配置
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class OuterAuthConfig {

    @Bean
    public AiOuterAuthInterceptor aiOuterAuthInterceptor(@Lazy UserAuthService userAuthService, InterceptorWhiteConfig interceptorWhiteConfig) {
        return new AiOuterAuthInterceptor(userAuthService, interceptorWhiteConfig);
    }

    @Configuration
    @ConditionalOnProperty(
            value = {"hcy.auth-interceptor.enable"},
            havingValue = "true",
            matchIfMissing = false
    )
    public static class AuthMvcConfigurer implements WebMvcConfigurer {

        private final InterceptorWhiteConfig interceptorWhiteConfig;

        private final AiOuterAuthInterceptor albumSaasAuthInterceptor;

        public void addInterceptors(InterceptorRegistry registry) {
            log.info("初始化 aiOuterAuthInterceptor");
            registry.addInterceptor(this.albumSaasAuthInterceptor)
                    .addPathPatterns("/**")
                    .excludePathPatterns(interceptorWhiteConfig.getExcludePath())
                    .order(9999);
        }

        @Autowired
        public AuthMvcConfigurer(AiOuterAuthInterceptor aiOuterAuthInterceptor, InterceptorWhiteConfig interceptorWhiteConfig) {
            this.albumSaasAuthInterceptor = aiOuterAuthInterceptor;
            this.interceptorWhiteConfig = interceptorWhiteConfig;
        }
    }
}
