package com.zyhl.yun.api.outer.controller;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.hcy.commons.enums.CommonResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.logger.util.LogUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.client.qwen.enums.QwenModelEnum;
import com.zyhl.yun.api.outer.anno.SSEApplicationAnnotation;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatPlatformAddDTO;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatPlatformService;
import com.zyhl.yun.api.outer.application.util.SseEmitterDataUtils;
import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddResultTypeEnum;
import com.zyhl.yun.api.outer.exception.SseApplicationException;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.zyhl.yun.api.outer.constants.Const.NUM_16;

/**
 * 第三方平台请求，临时需求处理
 *
 * <AUTHOR>
 * @Date 2024/4/10 10:32
 */
@Slf4j
@RestController
@RequestMapping(headers = {OutPlatformController.X_YUN_PLATFORM_INFO})
public class OutPlatformController {

    static final String X_YUN_PLATFORM_INFO = "x-yun-platform-info";

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private AlgorithmChatPlatformService algorithmChatPlatformService;

    /**
     * 第三方平台请求：对话接口
     *
     * @param dto
     * @return
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/assistant/chat/add")
    public BaseResult chatAdd(@RequestBody @Valid AlgorithmChatAddDTO dto) {
        AlgorithmChatAddVO vo = AlgorithmChatAddVO.builder().build();
        if (dto == null) {
            return BaseResult.error(CommonResultCode.ERROR_PARAMS, null);
        }
        // 第三方平台请求场景-文生文
        String platformInfo = request.getHeader(X_YUN_PLATFORM_INFO);
        if (CharSequenceUtil.isNotBlank(platformInfo) && null != dto.getContent()
                && DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode().equals(dto.getContent().getCommands())) {
            Map<String, Object> headerMap = new HashMap<>(NUM_16);
            try {
                //platformInfo记录为JSONObject
                headerMap.put(X_YUN_PLATFORM_INFO, JSONUtil.parseObj(platformInfo));
            } catch (Exception e) {
                // json转换失败，则直接记录文字
                headerMap.put(X_YUN_PLATFORM_INFO, platformInfo);
            }
            LogUtil.setBusinessExtInfo(headerMap);
            String dialogue = dto.getContent().getDialogue();
            AlgorithmChatPlatformAddDTO addDto = new AlgorithmChatPlatformAddDTO();
            addDto.setPlatformInfo(platformInfo);
            addDto.setSessionId(dto.getSessionId());
            addDto.setUserId(dto.getUserId());
            addDto.setContent(dto.getContent());
            // 内部逻辑：文生文，请求自研千问接口--20240429部署Q05B
            vo = algorithmChatPlatformService.submitAlgorithmChatText(QwenModelEnum.Q05B.getValue(), addDto);
            vo.setCommands(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());
            if (CharSequenceUtil.isBlank(dialogue)) {
                log.error("platformInfo chatAdd dto:{} | 参数异常，dialogue为空", JSONUtil.toJsonStr(dto));
                // 参数异常
                return BaseResult.error(CommonResultCode.ERROR_PARAMS, null);
            }
            return BaseResult.success(vo);
        } else {
            log.error("platformInfo chatAdd dto:{} | 参数异常，其他参数不合法", JSONUtil.toJsonStr(dto));
            return BaseResult.error(CommonResultCode.ERROR_PARAMS, null);
        }
    }

    /**
     * 流式刷量接口
     *
     * @param dto
     * @return
     */
    @SSEApplicationAnnotation
    @PostMapping(value = "/assistant/chat/add", headers = "x-yun-api-version", produces = {MediaType.TEXT_EVENT_STREAM_VALUE})
    public SseEmitter chatAddV4(@RequestBody @Valid AlgorithmChatAddDTO dto) {
        Long timeout = 300000L;
        Long reconnectTimeMillis = 5000L;
        SseEmitter sseEmitter = new SseEmitter(timeout);
        AtomicBoolean completeFlag = new AtomicBoolean();

        try {
            String apiVersion = "";
            String platformInfo = "";
            String appChannel = "";
            Map<String, String> requestHeaders = RequestContextHolder.getRequestHeaders();
            if (requestHeaders != null) {
                apiVersion = requestHeaders.get(ReqHeadConst.API_VERSION);
                platformInfo = requestHeaders.get(ReqHeadConst.PLATFORM_INFO);
                appChannel = requestHeaders.get(ReqHeadConst.APP_CHANNEL);
            }

            Map<String, Object> headerMap = new HashMap<>(NUM_16);
            try {
                //platformInfo记录为JSONObject
                headerMap.put(X_YUN_PLATFORM_INFO, JSONUtil.parseObj(platformInfo));
            } catch (Exception e) {
                // json转换失败，则直接记录文字
                headerMap.put(X_YUN_PLATFORM_INFO, platformInfo);
            }
            LogUtil.setBusinessExtInfo(headerMap);
            log.info("【流式第三方平台】接口调用，api版本：{}，平台信息：{}，渠道号：{}，用户id：{}", apiVersion, platformInfo, appChannel, RequestContextHolder.getUserId());
            SseApplicationException sseException = RequestContextHolder.getSseException();
            if (null != sseException) {
                //AiOuterAuthInterceptor拦截器出现异常，流式接口会设置到threadLocal，send异常信息 start
                BaseResult<?> authRes = BaseResult.error(sseException.getCode(), sseException.getMessage());
                SseEmitterDataUtils.sendMsgAndCompleteOthers(sseEmitter, authRes, completeFlag);
                return sseEmitter;
            }

            // 响应成功
            AlgorithmChatAddVO vo = new AlgorithmChatAddVO();
            vo.setResultType(ChatAddResultTypeEnum.FLOW_TYPE.getType());
            vo.setCommands("000");
            SseEmitterDataUtils.sendMsgAndComplete(sseEmitter, BaseResult.success(vo), completeFlag);
            return sseEmitter;

        } catch (YunAiBusinessException e) {
            // 发送业务异常消息
            log.info("chatAddV2 YunAiBusinessException dto:{} | e:", JsonUtil.toJson(dto), e);
            SseEmitterDataUtils.sendMsgAndComplete(sseEmitter, BaseResult.error(e.getCode(), e.getMessage()), completeFlag);
        } catch (Exception e) {
            // 发送默认异常消息
            log.error("chatAddV2 Exception dto:{} | error:", JsonUtil.toJson(dto), e);
            SseEmitterDataUtils.sendDefaultErrorMsgAndComplete(sseEmitter, completeFlag);
        }

        return sseEmitter;
    }
}
