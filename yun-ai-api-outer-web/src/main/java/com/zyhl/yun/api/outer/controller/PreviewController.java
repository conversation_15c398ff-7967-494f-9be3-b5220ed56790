package com.zyhl.yun.api.outer.controller;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.plugin.logger.annotation.LogAnnotation;
import com.zyhl.hcy.plugin.logger.enums.LogType;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeHtmlPreviewReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeMailPreviewReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.PersonalKnowledgeNotePreviewDTO;
import com.zyhl.yun.api.outer.application.service.KnowledgeFilePreviewService;
import com.zyhl.yun.api.outer.application.service.knowledge.KnowledgeMailPreviewService;
import com.zyhl.yun.api.outer.application.service.knowledge.PersonalKnowledgeNoteService;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.controller.validate.KnowledgeFilePreviewValid;
import com.zyhl.yun.api.outer.controller.validate.SourceChannelValid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description  预览
 * @date 2025/4/17 15:58
 */
@Slf4j
@RestController
@RequestMapping(headers = {ReqHeadConst.API_VERSION, ReqHeadConst.CLIENT_INFO, ReqHeadConst.APP_CHANNEL},value = "/assistant/knowledge/personal/v2/base/preview")
public class PreviewController {

    @Resource
    private PersonalKnowledgeNoteService personalKnowledgeNoteService;

    @Resource
    private KnowledgeFilePreviewService knowledgeFilePreviewService;

    @Resource
    private SourceChannelValid sourceChannelValid;

    @Resource
    private KnowledgeFilePreviewValid knowledgeFilePreviewValid;

    /**
     * 笔记及其附件预览
     * @param dto
     * @return
     */
    @PostMapping(value = "note", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> notePreview(@RequestBody PersonalKnowledgeNotePreviewDTO dto){
        // 参数校验
        final AbstractResultCode check = knowledgeFilePreviewValid.notePreviewValid(dto);
        if (check != null) {
            log.info("【笔记及其附件预览】参数校验失败");
            return BaseResult.error(check);
        }
        return BaseResult.success(personalKnowledgeNoteService.preview(dto));
    }

    /**
     * 图配文封装
     */
    @PostMapping(value = "html",headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> htmlPreview(@RequestBody @Valid KnowledgeHtmlPreviewReqDTO dto) {
        AbstractResultCode abstractResultCode = sourceChannelValid.channelValid(dto);
        if (Objects.nonNull(abstractResultCode)){
            return BaseResult.error(abstractResultCode);
        }
        String htmlContent = knowledgeFilePreviewService.htmlPreview(dto);
        HashMap<String, String> map = new HashMap<>(Const.NUM_1);
        map.put("htmlString", htmlContent);
        return BaseResult.success(map);
    }

    @Resource
    private KnowledgeMailPreviewService knowledgeMailPreviewService;

    /**
     * 知识库邮件预览接口
     *
     * @param dto 请求参数
     * @return 邮件预览信息
     */
    @LogAnnotation(LogType.INTERFACE)
    @PostMapping(value = "/mail")
    public BaseResult<?> previewMailResource(@RequestBody KnowledgeMailPreviewReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = knowledgeFilePreviewValid.previewMailValid(dto);
        if (check != null) {
            log.info("【知识库邮件预览】参数校验失败");
            return BaseResult.error(check);
        }
        return BaseResult.success(knowledgeMailPreviewService.previewMailResource(dto));
    }

}
