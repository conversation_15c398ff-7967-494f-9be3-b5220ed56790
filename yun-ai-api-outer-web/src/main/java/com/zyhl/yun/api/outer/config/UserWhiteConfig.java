package com.zyhl.yun.api.outer.config;


import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@ConfigurationProperties("hcy.user-white")
public class UserWhiteConfig {

    /**
     * 用户白名单
     */
    private List<String> userWhiteList;

    public List<String> getUserWhiteList() {
        return userWhiteList;
    }

    public void setUserWhiteList(List<String> list) {
        this.userWhiteList = list;
    }
}
