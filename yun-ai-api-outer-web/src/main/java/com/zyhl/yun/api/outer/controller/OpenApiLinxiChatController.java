package com.zyhl.yun.api.outer.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.NumberUtil;
import com.zyhl.yun.api.outer.anno.SSEApplicationAnnotation;
import com.zyhl.yun.api.outer.application.annotation.DisableAuthorizationParser;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.OpenApiLingxiChatReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.impl.BeforeChatInfoHandlerImpl;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.impl.ParamResetHandlerImpl;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.impl.ParamValidationHandlerImpl;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.impl.openapi.OpenApiLingxiMeetingHandlerImpl;
import com.zyhl.yun.api.outer.application.chatv2.pojo.OpenApiLingxiParamInfo;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.service.HistoryDialogueSaveRedisService;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO;
import com.zyhl.yun.api.outer.application.config.ApplicationAgentLingxiConfig;
import com.zyhl.yun.api.outer.application.service.AlgorithmAiRegisterService;
import com.zyhl.yun.api.outer.application.service.external.UserAuthService;
import com.zyhl.yun.api.outer.domainservice.SseEmitterOperate;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.TalkTypeEnum;
import com.zyhl.yun.api.outer.exception.SseApplicationException;
import com.zyhl.yun.api.outer.external.client.resp.UserDomainRspDTO;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.RequestContextHolder.LingxiHeaderParam;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * openapi lingxi AI助手接口
 *
 * <AUTHOR>
 * @date 205-06-18 19:39
 */
@Slf4j
@RestController
@RequestMapping("/openapi/lingxi")
public class OpenApiLinxiChatController {

	@Resource
	private HistoryDialogueSaveRedisService historyRedisService;
	@Resource
	private DataSaveService dataSaveService;
	@Resource
	private UserAuthService userAuthService;
	@Resource
	private AlgorithmAiRegisterService algorithmAiRegisterService;
	@Resource
	private ParamResetHandlerImpl paramResetHandlerImpl;
	@Resource
	private ParamValidationHandlerImpl paramValidationHandlerImpl;
	@Resource
	private BeforeChatInfoHandlerImpl beforeChatInfoHandlerImpl;
	@Resource
	private OpenApiLingxiMeetingHandlerImpl openApiLingxiMeetingHandlerImpl;
	@Resource
	private ApplicationAgentLingxiConfig applicationAgentLingxiConfig;

	@DisableAuthorizationParser
	@SSEApplicationAnnotation
	@PostMapping(value = "/assistant/chat/add", produces = { MediaType.TEXT_EVENT_STREAM_VALUE })
	public SseEmitter chat(@RequestBody OpenApiLingxiChatReqDTO reqDTO) {
		ChatAddReqDTO chatAddReqDTO = reqDTO.getChatAddReqDTO();
		SseEmitterOperate sseEmitterOperate = new SseEmitterOperate();

		ChatAddHandleDTO handleDTO = null;
		// 鉴权异常信息
		SseApplicationException sseException = RequestContextHolder.getSseException();
		if (null != sseException) {
			sseEmitterOperate.sendAndComplete(OpenApiLingxiChatRespVO.getOpenApiLingxiChatErrorResp(handleDTO,
					sseException.getCode(), sseException.getMessage()));
			return sseEmitterOperate.getSseEmitter();
		}

		// 按顺序执行-执行器
		List<AbstractChatAddV2Handler> newChatAddHandlerList = new ArrayList<>();
		newChatAddHandlerList.add(paramResetHandlerImpl);
		newChatAddHandlerList.add(paramValidationHandlerImpl);
		newChatAddHandlerList.add(beforeChatInfoHandlerImpl);
		newChatAddHandlerList.add(openApiLingxiMeetingHandlerImpl);
		try {
			UserDomainRspDTO userDomainRspDTO = null;
			// 灵犀终端请求头参数
			LingxiHeaderParam lingxiHeaderParam = RequestContextHolder.getHeaderParams().getLingxiHeaderParam();
			// 存在兼容灵犀终端请求头 basic token
			boolean hasBasicTokenParam = null != lingxiHeaderParam
					&& StringUtils.isNotEmpty(lingxiHeaderParam.getBasicToken());
			/**
			 * 获取token鉴权 start
			 */
			if (Boolean.TRUE.equals(applicationAgentLingxiConfig.getDemoConfig().getDemo()) || hasBasicTokenParam) {
				// 测试demo鉴权
				String token = applicationAgentLingxiConfig.getDemoConfig().getBasicToken();
				if (hasBasicTokenParam) {
					log.info("使用兼容灵犀终端请求头 basic token");
					token = lingxiHeaderParam.getBasicToken();
				}
				// Token鉴权
				userDomainRspDTO = userAuthService.validateTokenOnly(token);
				if (Objects.isNull(userDomainRspDTO) || Objects.isNull(userDomainRspDTO.getUserDomainId())) {
					log.error("用户鉴权失败");
					throw new YunAiBusinessException(ResultCodeEnum.ERROR_NOT_AUTH);
				}
				userDomainRspDTO.setBasicToken(token);
			} else {
				// 正式终端鉴权
				// 统一认证token鉴权
				userDomainRspDTO = userAuthService.validateStToken(lingxiHeaderParam.getStToken(),
						lingxiHeaderParam.getSourceId(), NumberUtil.getLongValue(lingxiHeaderParam.getTs()),
						lingxiHeaderParam.getSign());
			}
			if (StringUtils.isEmpty(userDomainRspDTO.getBasicToken())) {
				throw new YunAiBusinessException(ResultCodeEnum.ERROR_NOT_AUTH);
			}
			// 将userId存于线程中
			RequestContextHolder.setUserInfo(userDomainRspDTO.getUserDomainId(), userDomainRspDTO.getPhoneNumber(),
					userDomainRspDTO.getBelongsPlatform());
			// 将云盘token存于线程中
			RequestContextHolder.setToken(userDomainRspDTO.getBasicToken());
			/**
			 * 获取token鉴权 end
			 */

			//检查未报名，自动报名AI助手
			algorithmAiRegisterService.autoRegisterAiAssistant(RequestContextHolder.getUserId());
			
			chatAddReqDTO.setSourceChannel(applicationAgentLingxiConfig.getBusinessConfig().getChannel());
			chatAddReqDTO.setApplicationId(applicationAgentLingxiConfig.getBusinessConfig().getApplicationId());
			chatAddReqDTO.setApplicationType(ApplicationTypeEnum.INTELLIGENT.getCode());
			handleDTO = new ChatAddHandleDTO(chatAddReqDTO, sseEmitterOperate);
			// 设置lingxi响应标识
			OpenApiLingxiParamInfo paramInfo = new OpenApiLingxiParamInfo();
			paramInfo.setLingxiRespFlag(true);
			paramInfo.setReqId(reqDTO.getReqId());
			paramInfo.setDeviceId(reqDTO.getDeviceId());
			paramInfo.setModel(reqDTO.getModel());
			paramInfo.setLastChatSession(reqDTO.getSession());
			handleDTO.setLingxiParamInfo(paramInfo);

		} catch (Exception e) {
			if (e instanceof YunAiBusinessException) {
				YunAiBusinessException yunAiBusinessException = (YunAiBusinessException) e;
				if (ObjectUtil.isNotEmpty(yunAiBusinessException.getCode())) {
					sseEmitterOperate.sendAndComplete(OpenApiLingxiChatRespVO.getOpenApiLingxiChatErrorResp(handleDTO,
							yunAiBusinessException.getCode(), yunAiBusinessException.getMessage()));
				} else {
					sseEmitterOperate.sendAndComplete(OpenApiLingxiChatRespVO.getOpenApiLingxiChatErrorResp(handleDTO,
							ResultCodeEnum.UNKNOWN_ERROR.getResultCode(), ResultCodeEnum.UNKNOWN_ERROR.getResultMsg()));
				}
				return sseEmitterOperate.getSseEmitter();
			}
			sseEmitterOperate.sendAndComplete(OpenApiLingxiChatRespVO.getOpenApiLingxiChatErrorResp(handleDTO,
					ResultCodeEnum.UNKNOWN_ERROR.getResultCode(), ResultCodeEnum.UNKNOWN_ERROR.getResultMsg()));
			return sseEmitterOperate.getSseEmitter();
		}

		// 执行业务逻辑
		for (AbstractChatAddV2Handler handler : newChatAddHandlerList) {
			long startTime = System.currentTimeMillis();
			try {
				if (handler.execute(handleDTO) && !handler.run(handleDTO)) {
					if (null != handleDTO.getInputInfoDTO()) {
						// 保存redis历史对话信息
						if (TalkTypeEnum.isTask(handleDTO.getInputInfoDTO().getDialogueType())) {
							log.info("openapi lingxi 不记录RedisHistoryDialogInfo dialogueId:{}",
									handleDTO.getDialogueId());
						} else {
							historyRedisService.setRedisHistoryDialogInfo(handleDTO);
						}
					}
					break;
				}
			} catch (Exception e) {
				log.error("openapi lingxi chat AI助手对话异常，异常信息：{}", e.getMessage(), e);

				// 对话异常，更新失败状态
				try {
					if (null != handleDTO) {
						dataSaveService.updateChatFail(handleDTO.getDialogueId());
					}
				} catch (Exception ee) {
					log.error("openapi lingxi chat Exception dataSaveService.updateFail dialogueId:{}, error:",
							handleDTO.getDialogueId(), e);
				}

				if (e instanceof YunAiBusinessException) {
					YunAiBusinessException yunAiBusinessException = (YunAiBusinessException) e;
					if (ObjectUtil.isNotEmpty(yunAiBusinessException.getCode())) {
						sseEmitterOperate.sendAndComplete(OpenApiLingxiChatRespVO.getOpenApiLingxiChatErrorResp(
								handleDTO, yunAiBusinessException.getCode(), yunAiBusinessException.getMessage()));
					} else {
						sseEmitterOperate.sendAndComplete(OpenApiLingxiChatRespVO.getOpenApiLingxiChatErrorResp(
								handleDTO, ResultCodeEnum.UNKNOWN_ERROR.getResultCode(),
								ResultCodeEnum.UNKNOWN_ERROR.getResultMsg()));
					}
					return sseEmitterOperate.getSseEmitter();
				}

				sseEmitterOperate.sendAndComplete(OpenApiLingxiChatRespVO.getOpenApiLingxiChatErrorResp(handleDTO,
						ResultCodeEnum.UNKNOWN_ERROR.getResultCode(), ResultCodeEnum.UNKNOWN_ERROR.getResultMsg()));
				return sseEmitterOperate.getSseEmitter();
			} finally {
				MDC.put(LogConstants.DURATION, String.valueOf(System.currentTimeMillis() - startTime));
				log.info("openapi lingxi chat 执行处理器：{}，耗时：{}ms", handler.getClass().getSimpleName(),
						System.currentTimeMillis() - startTime);
				MDC.remove(LogConstants.DURATION);
			}
		}

		return sseEmitterOperate.getSseEmitter();
	}

}
