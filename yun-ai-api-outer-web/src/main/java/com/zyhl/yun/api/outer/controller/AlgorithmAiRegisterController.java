package com.zyhl.yun.api.outer.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.enums.BaseResultCodeEnum;
import com.zyhl.yun.api.outer.application.dto.AiRegisterReqDTO;
import com.zyhl.yun.api.outer.application.dto.PopUpProtocolQueryWebDTO;
import com.zyhl.yun.api.outer.application.service.AlgorithmAiRegisterService;
import com.zyhl.yun.api.outer.constants.CommonConstant;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.domain.vo.AiRegisterVO;
import com.zyhl.yun.api.outer.enums.BusinessSourceEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.external.client.resp.UserDomainRspDTO;
import com.zyhl.yun.api.outer.external.service.UserEtnService;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.PopUpProtocolQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 云邮AI助手1.1重构报名
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(headers = {"x-yun-api-version", "x-yun-client-info", "x-yun-app-channel"})
public class AlgorithmAiRegisterController {

    @Resource
    private AlgorithmAiRegisterService algorithmAiRegisterService;
    @Resource
    private UserEtnService userEtnService;
    @Resource
    private RedissonClient redissonClient;

    /**
     * 查询是否已经报名（授权）
     *
     * @param dto 请求参数
     * @return 返回报名状态
     */
    @PostMapping("/assistant/accredit/get")
    public BaseResult<AiRegisterVO> get(@RequestBody AiRegisterReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = dto.checkParam();
        if (check != null) {
            log.info("查询是否已经报名参数校验失败");
            return BaseResult.error(check);
        }

        log.info("用户{}开始查询报名状态", RequestContextHolder.getUserId());
        return BaseResult.success(algorithmAiRegisterService.get(dto));
    }

    /**
     * 用户授权报名
     *
     * @param dto 请求参数
     * @return 返回报名状态
     */
    @PostMapping("/assistant/accredit/submit")
    public BaseResult<AiRegisterVO> accredit(@RequestBody AiRegisterReqDTO dto) {
        BaseResult<AiRegisterVO> checkResult = checkData(dto);
        if (checkResult != null) {
            return checkResult;
        }

        // 加锁，防止频繁报名
        final RLock lock = redissonClient.getLock(String.format(CommonConstant.USER_ACCREDIT_LOCK_CACHE_KEY, dto.getUserId()));
        try {
            final boolean acquire = lock.tryLock(RedisConstants.WAIT_TIME_5, RedisConstants.LEASE_TIME_30, TimeUnit.SECONDS);
            if (!acquire) {
                log.info("用户{}正在报名", dto.getUserId());
                return BaseResult.error(ResultCodeEnum.ACCREDIT_HANDLE_ING);
            }

            // 视频分析逻辑
            if(BusinessSourceEnum.AI_VIDEO_ANALYSIS.getCode().equals(dto.getSourceBusiness())){
                return  algorithmAiRegisterService.setVideoStatus(dto);
            }

            log.info("用户{}开始报名", dto.getUserId());
            return BaseResult.success(algorithmAiRegisterService.accredit(dto));
        } catch (Exception e) {
            log.error("用户{}报名异常：{}", dto.getUserId(), e.getMessage(), e);
            return BaseResult.error(ResultCodeEnum.AI_FAILURE_DEFAULT);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 授权/取消授权前置检查
     *
     */
    @Nullable
    private BaseResult<AiRegisterVO> checkData(AiRegisterReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = dto.checkParam();
        if (check != null) {
            log.info("报名参数校验失败");
            return BaseResult.error(check);
        }

        if (CharSequenceUtil.isEmpty(RequestContextHolder.getUserId())) {
            // 本地线程没有用户信息，则查询用户信息
            UserDomainRspDTO userInfo = userEtnService.getUserInfo(Long.valueOf(dto.getUserId()));
            if (userInfo == null) {
                log.info("用户不存在，用户id：{}", dto.getUserId());
                return BaseResult.error(BaseResultCodeEnum.ERROR_PARAMS);
            }
            RequestContextHolder.setUserInfo(userInfo.getUserDomainId(), userInfo.getPhoneNumber(), userInfo.getBelongsPlatform());
        }
        return null;
    }

    /**
     * 获取AI弹窗协议配置
     */
    @PostMapping("/assistant/accredit/protocol/get")
    public BaseResult<PopUpProtocolQueryVO> protocolGet(@RequestBody PopUpProtocolQueryWebDTO dto) {
        return BaseResult.success(algorithmAiRegisterService.protocolGet(dto));
    }

    /**
     * 用户取消授权报名
     */
    @PostMapping("/assistant/accredit/cancel")
    public BaseResult cancel(@RequestBody AiRegisterReqDTO dto) {
        BaseResult checkResult = checkData(dto);
        if (checkResult != null) {
            return checkResult;
        }
        //防止频繁取消
        final RLock lock = redissonClient.getLock(String.format(CommonConstant.USER_ACCREDIT_CANCEL_LOCK_CACHE_KEY, dto.getUserId()));
        try {
            final boolean acquire = lock.tryLock(RedisConstants.WAIT_TIME_5, RedisConstants.LEASE_TIME_30, TimeUnit.SECONDS);
            if (!acquire) {
                log.info("###cancel 当前用户正在取消报名 userId:{}", dto.getUserId());
                return BaseResult.error(ResultCodeEnum.ACCREDIT_CANCEL_HANDLE_ING);
            }
            algorithmAiRegisterService.cancel(dto);
        } catch (Exception e) {
            log.error("###cancel 取消报名异常 userId:{},message:{}", dto.getUserId(), e.getMessage(), e);
            return BaseResult.error(ResultCodeEnum.AI_FAILURE_DEFAULT);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return BaseResult.success();
    }

}
