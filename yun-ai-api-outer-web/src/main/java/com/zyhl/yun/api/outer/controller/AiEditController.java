package com.zyhl.yun.api.outer.controller;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.plugin.logger.annotation.LogAnnotation;
import com.zyhl.hcy.plugin.logger.enums.LogType;
import com.zyhl.yun.api.outer.application.dto.AiEditUpdateReqDTO;
import com.zyhl.yun.api.outer.application.service.AiEditService;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * AI编辑控制器
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@RestController
@RequestMapping(headers = {ReqHeadConst.API_VERSION, ReqHeadConst.CLIENT_INFO, ReqHeadConst.APP_CHANNEL})
public class AiEditController {

    @Resource
    private AiEditService aiEditService;

    /**
     * 更新AI编辑内容
     *
     * @param reqDTO 更新请求DTO
     * @return 操作结果
     */
    @LogAnnotation(LogType.INTERFACE)
    @PostMapping("/assistant/edit/update")
    public BaseResult<?> updateContent(@RequestBody @Valid AiEditUpdateReqDTO reqDTO) {
        log.info("接收到AI编辑更新请求, dialogueId: {}, fileId: {}", reqDTO.getDialogueId(), reqDTO.getFileId());
            boolean result = aiEditService.updateOutContent(reqDTO);
            return BaseResult.success();
    }
} 