package com.zyhl.yun.api.outer.controller.validate;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 文件入参校验类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SourceChannelValid {

    @Resource
    public SourceChannelsProperties sourceChannelsProperties;


    /**
     * 渠道来源校验
     *
     * @param dto
     * @return
     */
    public AbstractResultCode channelValid(BaseChannelDTO dto) {
        if (ObjectUtil.isEmpty(dto.getSourceChannel())) {
            log.info("渠道为空，sourceChannel：{}", dto.getSourceChannel());
            return ResultCodeEnum.ERROR_PARAMS;
        } else if (!sourceChannelsProperties.isExist(dto.getSourceChannel())) {
            log.info("渠道号不存在，sourceChannel:{}", dto.getSourceChannel());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        return dto.checkUserId();
    }

}
