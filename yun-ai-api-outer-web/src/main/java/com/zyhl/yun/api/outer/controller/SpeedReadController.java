package com.zyhl.yun.api.outer.controller;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.dto.ReadTaskDTO;
import com.zyhl.yun.api.outer.application.dto.ReadTaskReqDTO;
import com.zyhl.yun.api.outer.application.dto.SpeedReadResultVO;
import com.zyhl.yun.api.outer.application.service.TaskApplicationTypeService;
import com.zyhl.yun.api.outer.application.vo.SpeedReadTaskVO;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.domain.vo.common.PageInfoVO;
import com.zyhl.yun.api.outer.enums.AiResultCode;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

import javax.annotation.Resource;
import javax.validation.Valid;

import lombok.extern.slf4j.Slf4j;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description} 快速阅读器
 * <p>
 *
 * <AUTHOR>
 * @since 4月15 2025
 */
@Slf4j
@RestController
@RequestMapping(value = "/assistant/speedread",
        headers = {ReqHeadConst.API_VERSION, ReqHeadConst.CLIENT_INFO, ReqHeadConst.APP_CHANNEL})
public class SpeedReadController {

    @Resource
    private TaskApplicationTypeService speedReadService;

    /**
     * 创建图书处理任务接口
     */
    @PostMapping("/task/create")
    public BaseResult<SpeedReadTaskVO> readTaskCreate(@RequestBody @Valid ReadTaskDTO dto) {
        SpeedReadTaskVO readTask = speedReadService.createReadTask(dto);
        return BaseResult.success(readTask);
    }

    /**
     * 查询图书处理任务列表接口
     */
    @PostMapping("/task/list")
    public BaseResult<PageInfoVO<SpeedReadTaskVO>> readTaskList(@RequestBody @Valid ReadTaskReqDTO dto) {
        PageInfoVO<SpeedReadTaskVO> pageInfoVO = speedReadService.readTaskList(dto);
        return BaseResult.success(pageInfoVO);
    }

    /**
     * 删除图书处理任务详情接口
     */
    @PostMapping("/task/delete")
    public BaseResult<?> readTaskDelete(@RequestBody @Valid ReadTaskDTO dto) {
        speedReadService.readTaskDelete(dto);
        return BaseResult.success();
    }

    /**
     * 获取快速阅读详情接口
     */
    @PostMapping("/task/get")
    public BaseResult<SpeedReadResultVO> readTaskGet(@RequestBody @Valid ReadTaskDTO dto) {
        SpeedReadResultVO vo = speedReadService.readTaskGet(dto);
        if (Objects.isNull(vo.getAiResultCode())) {
            return BaseResult.success(vo);
        } else {
            AiResultCode aiResultCode = vo.getAiResultCode();
            vo.setAiResultCode(null);
            return BaseResult.error(aiResultCode.getCode(), aiResultCode.getMsg(), vo);
        }
    }
}
