package com.zyhl.yun.api.outer.controller;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileDeleteReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileListBatchReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileMoveBatchReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.UserKnowledgeFileMoveService;
import com.zyhl.yun.api.outer.application.service.knowledge.UserKnowledgeFileTaskService;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.controller.validate.UserKnowledgeFileValid;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zyhl.yun.api.outer.constants.Const.NUM_16;

/**
 * 个人知识库资源信息控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(headers = {ReqHeadConst.CLIENT_INFO, ReqHeadConst.APP_CHANNEL})
public class UserKnowledgeFileResourceController {

    @Resource
    private UserKnowledgeFileValid userKnowledgeFileValid;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private UserKnowledgeFileTaskService userKnowledgeFileTaskService;
    @Resource
    private UserKnowledgeFileMoveService userKnowledgeFileMoveService;


    /**
     * 个人知识库资源批量删除接口
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/resource/batchDelete", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> batchDelete(@RequestBody KnowledgeFileListBatchReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeFileValid.batchDelete(dto);
        if (check != null) {
            log.info("【个人知识库资源批量删除接口】参数校验失败");
            return BaseResult.error(check);
        }

        // 加锁，防止频繁删除
        final RLock lock = redissonClient.getLock(String.format(RedisConstants.KNOWLEDGE_FILE_DEL_LOCK, dto.getUserId()));
        try {
            if (!lock.tryLock(RedisConstants.WAIT_TIME_1, RedisConstants.LEASE_TIME_10, TimeUnit.SECONDS)) {
                log.info("【个人知识库资源批量删除】正在删除文件，请稍后再试，用户id：{}", dto.getUserId());
                return BaseResult.error(ResultCodeEnum.REQUEST_TOO_FREQUENTLY);
            }

            List<File> fileList = dto.getResourceIdList().stream()
                    .map(resourceId -> {
                        File file = new File();
                        file.setFileId(resourceId);
                        return file;
                    }).collect(Collectors.toList());
            KnowledgeFileDeleteReqDTO dto1 = new KnowledgeFileDeleteReqDTO();
            BeanUtils.copyProperties(dto, dto1);
            dto1.setFileList(fileList);
            userKnowledgeFileTaskService.batchDelete(dto1);
            return BaseResult.success();
        } catch (YunAiBusinessException e) {
            throw new YunAiBusinessException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("【知识库文件删除】删除文件异常：{}", e.getMessage(), e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        return BaseResult.success();
    }

    /**
     * 个人知识库资源批量删除接口(异步)
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/resource/batchDelete", headers = {ReqHeadConst.API_VERSION_V2})
    public BaseResult<?> batchDeleteAsync(@RequestBody KnowledgeFileListBatchReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeFileValid.batchDelete(dto);
        if (check != null) {
            log.info("【个人知识库资源批量删除接口(异步)】参数校验失败");
            return BaseResult.error(check);
        }

        // 加锁，防止频繁删除
        final RLock lock = redissonClient.getLock(String.format(RedisConstants.KNOWLEDGE_FILE_DEL_LOCK, dto.getUserId()));
        try {
            if (!lock.tryLock(RedisConstants.WAIT_TIME_1, RedisConstants.LEASE_TIME_10, TimeUnit.SECONDS)) {
                log.info("【个人知识库资源批量删除(异步)】正在删除文件，请稍后再试，用户id：{}", dto.getUserId());
                return BaseResult.error(ResultCodeEnum.REQUEST_TOO_FREQUENTLY);
            }
            Map<String, Object> result = new HashMap<>(NUM_16);
            result.put("taskId", String.valueOf(userKnowledgeFileTaskService.batchDeleteAsync(dto)));
            return BaseResult.success(result);
        } catch (YunAiBusinessException e) {
            throw new YunAiBusinessException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("【知识库文件删除(异步)】删除文件异常：{}", e.getMessage(), e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        return BaseResult.success();
    }

    /**
     * 个人知识库资源批量移动接口
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/resource/batchMove", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> batchMove(@RequestBody KnowledgeFileMoveBatchReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeFileValid.batchMove(dto);
        if (check != null) {
            log.info("【个人知识库资源批量移动接口】参数校验失败");
            return BaseResult.error(check);
        }

        // 加锁，防止频繁移动
        final RLock lock = redissonClient.getLock(String.format(RedisConstants.KNOWLEDGE_FILE_MOVE_LOCK, dto.getUserId()));
        try {
            if (!lock.tryLock(RedisConstants.WAIT_TIME_1, RedisConstants.LEASE_TIME_10, TimeUnit.SECONDS)) {
                log.info("【个人知识库资源批量移动接口】正在移动文件夹，请稍后再试，用户id：{}", dto.getUserId());
                return BaseResult.error(ResultCodeEnum.REQUEST_TOO_FREQUENTLY);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("taskId", String.valueOf(userKnowledgeFileMoveService.batchMoveAsync(dto)));
            return BaseResult.success(result);

        } catch (YunAiBusinessException e) {
            throw new YunAiBusinessException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("【个人知识库资源批量移动接口】dto:{} | 移动文件夹异常：", JsonUtil.toJson(dto), e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        return BaseResult.success();
    }
}
