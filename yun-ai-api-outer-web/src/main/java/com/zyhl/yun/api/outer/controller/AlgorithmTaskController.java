package com.zyhl.yun.api.outer.controller;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.logger.annotation.LogAnnotation;
import com.zyhl.hcy.plugin.logger.enums.LogType;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.enums.BaseResultCodeEnum;
import com.zyhl.yun.api.outer.application.dto.AITaskBenefitReqDTO;
import com.zyhl.yun.api.outer.application.dto.AITaskResultPageReqDTO;
import com.zyhl.yun.api.outer.application.dto.AiTaskResultReqDTO;
import com.zyhl.yun.api.outer.application.dto.AIToolConfigDTO;
import com.zyhl.yun.api.outer.application.dto.AIToolsConsumeDTO;
import com.zyhl.yun.api.outer.application.dto.tool.ToolConfigFaceSwapParamDTO;
import com.zyhl.yun.api.outer.application.service.task.AlToolConfigService;
import com.zyhl.yun.api.outer.application.service.task.AlgorithmTaskBenefitService;
import com.zyhl.yun.api.outer.application.service.task.AlgorithmTaskService;
import com.zyhl.yun.api.outer.domain.vo.common.PageInfoVO;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.AIToolConfigVO;
import com.zyhl.yun.api.outer.vo.AlgorithmTaskListResultVO;
import com.zyhl.yun.api.outer.vo.AlgorithmTaskResultVO;
import com.zyhl.yun.api.outer.vo.BenefitPayResultVO;
import com.zyhl.yun.api.outer.vo.BenefitUseTimesLimitVO;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

import javax.annotation.Resource;
import javax.validation.Valid;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 算法任务控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(headers = "x-yun-api-version=v1")
public class AlgorithmTaskController {

    @Resource
    private AlgorithmTaskService taskService;
    @Resource
    private AlgorithmTaskBenefitService algorithmTaskBenefitService;

    @Resource
    private AlToolConfigService alToolConfigService;

    /**
     * 查询AI算法任务结果
     *
     * @param dto 参数
     * @return 创建结果
     */
    @LogAnnotation(LogType.INTERFACE)
    @PostMapping("/async/task/result")
    public BaseResult<AlgorithmTaskResultVO> taskResult(@Valid @RequestBody AiTaskResultReqDTO dto) {
        log.info("AlgorithmTaskController taskResult dto:{}", JsonUtil.toJson(dto));
        AlgorithmTaskResultVO taskResultVO = taskService.getAiTaskResult(dto);
        if (Objects.isNull(taskResultVO.getAiResultCode())) {
            return BaseResult.success(taskResultVO);
        } else {
            AiResultCode aiResultCode = taskResultVO.getAiResultCode();
            taskResultVO.setAiResultCode(null);
            return BaseResult.error(aiResultCode.getCode(), aiResultCode.getMsg(), taskResultVO);
        }
    }

    /**
     * 查询AI算法任务结果历史记录列表
     *
     * @param dto 参数dto
     * @return 响应结果
     */
    @LogAnnotation(LogType.INTERFACE)
    @PostMapping("/async/task/listTaskRecord")
    public BaseResult<PageInfoVO<AlgorithmTaskListResultVO>> listTaskRecord(@Valid @RequestBody AITaskResultPageReqDTO dto) {
        log.info("AlgorithmTaskController taskResult dto:{}", JsonUtil.toJson(dto));
        // 参数校验（最小分页3）
        dto.validate(3);
        return BaseResult.success(taskService.listTaskRecord(dto));
    }

    /**
     * 算法任务权益扣费接口
     *
     * @param dto 参数dto
     * @return 响应结果
     */
    @LogAnnotation(LogType.INTERFACE)
    @PostMapping("/benefit/algorithmTask/pay")
    public BaseResult<BenefitPayResultVO> benefitAlgorithmTaskPay(@Valid @RequestBody AITaskBenefitReqDTO dto) {
        BenefitPayResultVO payResultVO = null;
        try {
            // 将上下文userId设置为用户id
            if (CharSequenceUtil.isNotEmpty(RequestContextHolder.getUserId())) {
                dto.setUserId(RequestContextHolder.getUserId());
            }
            payResultVO = algorithmTaskBenefitService.benefitPay(dto);
        } catch (Exception e) {
            log.error("algorithmTaskBenefitService.benefitPay dto:{}, error:", JsonUtil.toJson(dto), e);
            if (e instanceof YunAiBusinessException) {
                YunAiBusinessException ex = (YunAiBusinessException) e;
                return BaseResult.error(ex.getCode(), ex.getMessage());
            }
            return BaseResult.error(AiResultCode.CODE_10000204.getCode(), AiResultCode.CODE_10000204.getMsg());
        }
        return BaseResult.success(payResultVO);
    }

    /**
     * 算法任务权益核销
     *
     * @param dto the dto
     * @return {@link BaseResult<BenefitUseTimesLimitVO>}
     * <AUTHOR>
     * @date 2024-8-17 12:06
     */
    @LogAnnotation(LogType.INTERFACE)
    @PostMapping("/benefit/algorithmTask/toolsConsume")
    public BaseResult<BenefitUseTimesLimitVO> algorithmTaskToolsConsume(@Valid @RequestBody AIToolsConsumeDTO dto) {
        BenefitUseTimesLimitVO benefitUseTimesLimitVO = algorithmTaskBenefitService.algorithmTaskToolsConsume(dto);
        return BaseResult.success(benefitUseTimesLimitVO);
    }

    /**
     * AI工具配置获取接口
     *
     * @param dto the dto
     * @return {@link BaseResult<AIToolConfigVO>}
     * <AUTHOR>
     * @date 2024-11-28 10:15
     */
    @LogAnnotation(LogType.INTERFACE)
    @PostMapping("/aitool/config/get")
    public BaseResult<AIToolConfigVO> aiToolConfigGet(@Valid @RequestBody AIToolConfigDTO dto) {
        DialogueIntentionEnum dialogueIntentionEnum = DialogueIntentionEnum.getByCode(dto.getCommand());
        if (DialogueIntentionEnum.FACE_SWAP.equals(dialogueIntentionEnum)) {
            // AI照相馆
            if (ObjectUtil.isEmpty(dto.getBusinessParam())) {
                log.error("AI照相馆业务参数为空，BusinessParam={}", dto.getBusinessParam());
                return BaseResult.error(BaseResultCodeEnum.ERROR_PARAMS);
            }
            ToolConfigFaceSwapParamDTO param = JsonUtil.parseObject(dto.getBusinessParam(), ToolConfigFaceSwapParamDTO.class);
            if (Objects.isNull(param) || ObjectUtil.isEmpty(param.getStyle()) || ObjectUtil.isEmpty(param.getPoseId())) {
                log.error("AI照相馆业务参数错误，BusinessParam={}", dto.getBusinessParam());
                return BaseResult.error(BaseResultCodeEnum.ERROR_PARAMS);
            }
            return BaseResult.success(alToolConfigService.getFaceSwapConfig(param));
        }
        AIToolConfigVO aiToolConfigVO = alToolConfigService.aiToolConfigGet(dto);
        return BaseResult.success(aiToolConfigVO);
    }

}
