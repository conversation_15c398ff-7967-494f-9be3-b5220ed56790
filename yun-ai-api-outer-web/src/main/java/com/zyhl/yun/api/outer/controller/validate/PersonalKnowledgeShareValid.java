package com.zyhl.yun.api.outer.controller.validate;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.yun.ai.common.base.enums.NumberEnum;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.knowledge.*;
import com.zyhl.yun.api.outer.constants.RegConst;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.repository.UserKnowledgeRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 个人知识库2.0入参校验类
 *
 * <AUTHOR>
 * @date 2025/04/21
 */
@Slf4j
@Component
public class PersonalKnowledgeShareValid extends SourceChannelValid {

    @Resource
    private UserKnowledgeRepository userKnowledgeRepository;

    /**
     * 个人知识库分享加入的入参校验
     */
    public AbstractResultCode join(KnowledgeShareAddReqDTO dto) {

        // 校验用户id
        if (CharSequenceUtil.isEmpty(RequestContextHolder.getUserId())) {
            log.info("用户ID为空");
            return ResultCodeEnum.ERROR_INVALID_USERID;
        }

        // 校验baseId
        if (CharSequenceUtil.isEmpty(dto.getBaseId())) {
            log.info("joinKnowledge-知识库ID为空");
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_ID_NOT_PROVIDE.getResultCode(), "知识库ID不能为空");
        }
        // 校验知识库id格式
        if (!dto.getBaseId().matches(RegConst.REG_ID_STR)) {
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_ID_NOT_EXIST.getResultCode(),
                    "知识库ID不存在");
        }

        return null;

    }

    /**
     * 个人知识库分享加入的入参校验
     */
    public AbstractResultCode get(KnowledgeShareGetReqDTO dto) {

        String userId = RequestContextHolder.getUserId();
        // 校验用户id
        if (CharSequenceUtil.isEmpty(userId)) {
            log.info("用户ID为空");
            return ResultCodeEnum.ERROR_INVALID_USERID;
        }

        // 校验知识库id
        if (CharSequenceUtil.isEmpty(dto.getBaseId())) {
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_ID_NOT_PROVIDE.getResultCode(),
                    "知识库ID不能为空");
        }

        // 校验知识库id格式和根据知识库id查询是否有该知识库
        if (!dto.getBaseId().matches(RegConst.REG_ID_STR)) {
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_ID_NOT_EXIST.getResultCode(),
                    "知识库ID不存在");
        }
        if (userKnowledgeRepository.countByBaseId(Long.valueOf(dto.getBaseId())) < NumberEnum.ONE.getValue()) {
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_ID_NOT_EXIST.getResultCode(),
                    "知识库ID不存在");
        }

        return null;

    }

}
