package com.zyhl.yun.api.outer.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.logger.annotation.LogAnnotation;
import com.zyhl.hcy.plugin.logger.enums.LogType;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.AIAssistantPayReqDTO;
import com.zyhl.yun.api.outer.application.dto.AgentBatchGetDTO;
import com.zyhl.yun.api.outer.application.dto.AiAssistantStatusReqDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatContentListDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatDeleteDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatListDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatPollingUpdateDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatStopDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmTransferDTO;
import com.zyhl.yun.api.outer.application.dto.ApplicationTypeListDTO;
import com.zyhl.yun.api.outer.application.dto.ShareBatchGetDTO;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatContentService;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatHistoryService;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatPayService;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatTransferService;
import com.zyhl.yun.api.outer.application.service.chat.ChatApplicationTypeService;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.controller.validate.ChatContentValid;
import com.zyhl.yun.api.outer.controller.validate.SourceChannelValid;
import com.zyhl.yun.api.outer.domain.vo.TabChatApplicationType;
import com.zyhl.yun.api.outer.domain.vo.chat.ContentResultVO;
import com.zyhl.yun.api.outer.domain.vo.chat.ContentResultVOV2;
import com.zyhl.yun.api.outer.domain.vo.chat.MessageVO;
import com.zyhl.yun.api.outer.domain.vo.chat.PollingUpdateV2VO;
import com.zyhl.yun.api.outer.domain.vo.chat.PollingUpdateV3VO;
import com.zyhl.yun.api.outer.domain.vo.common.PageInfoVO;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.ChatPayResultVO;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2024/1/6 16:32
 */
@Slf4j
@RestController
@RequestMapping(headers = {ReqHeadConst.API_VERSION, ReqHeadConst.CLIENT_INFO, ReqHeadConst.APP_CHANNEL})
public class AssistantController {

    @Resource
    private AlgorithmChatHistoryService algorithmChatHistoryService;
    @Resource
    private AlgorithmChatTransferService algorithmChatTransferService;
    @Resource
    private AlgorithmChatPayService algorithmChatPayService;
    @Resource
    private ChatApplicationTypeService chatApplicationTypeService;
    @Resource
    private SourceChannelsProperties sourceChannelsProperties;
    @Resource
    private AlgorithmChatContentService algorithmChatContentService;
    @Resource
    private ChatContentValid chatContentValid;
    @Resource
    private SourceChannelValid sourceChannelValid;

    /**
     * 5.1.2 历史会话列表查询
     */
    @PostMapping("/assistant/chat/list")
    public BaseResult<PageInfoVO<MessageVO>> chatList(@RequestBody @Valid AlgorithmChatListDTO dto) {
        if (dto == null) {
            dto = new AlgorithmChatListDTO();
        }
        // 参数校验
        dto.validate();
        return BaseResult.success(algorithmChatHistoryService.chatList(dto));
    }

    /**
     * 5.1.3 单个会话的历史（对话内容）列表查询V1
     */
    @PostMapping(value = "/assistant/chat/contentList")
    public BaseResult<PageInfoVO<ContentResultVO>> chatContentListV1(@RequestBody @Valid AlgorithmChatContentListDTO dto) {
        // 参数校验
        dto.validate();
        return BaseResult.success(algorithmChatHistoryService.contentList(dto));
    }

    /**
     * 5.1.3 单个会话的历史（对话内容）列表查询V2
     */
    @PostMapping(value = "/assistant/chat/contentList", headers = ReqHeadConst.API_VERSION_V2)
    public BaseResult<PageInfoVO<ContentResultVOV2>> chatContentListV2(@RequestBody @Valid AlgorithmChatContentListDTO dto) {
        // 参数校验
        dto.validate();
        return BaseResult.success(algorithmChatHistoryService.contentList(dto));
    }

    /**
     * 5.1.4 查询对话输出接口V1（轮巡查结果）
     */
    @PostMapping("/assistant/chat/pollingUpdate")
    public BaseResult<ContentResultVO> chatPollingUpdate(@RequestBody AlgorithmChatPollingUpdateDTO dto) {
        // 参数校验
        dto.validate();
        ContentResultVO contentResultVO = algorithmChatHistoryService.chatPollingUpdateV1(dto);

        // 是否中断轮巡
        if (isInterrupt(contentResultVO)) {
            // 中断
            return BaseResult.error(contentResultVO.getResultCode(), contentResultVO.getResultMsg(), contentResultVO);
        } else {
            return BaseResult.success(contentResultVO);
        }
    }

    /**
     * 5.1.5 查询对话输出接口V2（轮巡查结果）
     */
    @PostMapping(value = "/assistant/chat/pollingUpdate", headers = "x-yun-api-version=v2")
    public BaseResult<PollingUpdateV2VO> chatPollingUpdateV2(@RequestBody AlgorithmChatPollingUpdateDTO dto) {
        // 参数校验
        dto.validate();
        PollingUpdateV2VO pollingUpdateV2VO = algorithmChatHistoryService.chatPollingUpdateV2(dto);
        if (null == pollingUpdateV2VO) {
            return BaseResult.success((PollingUpdateV2VO) null);
        }

        ContentResultVO contentResultVO = pollingUpdateV2VO.getResult();
        // 是否中断轮巡
        if (isInterrupt(contentResultVO)) {
            // 中断
            return BaseResult.error(contentResultVO.getResultCode(), contentResultVO.getResultMsg(), pollingUpdateV2VO);
        } else {
            return BaseResult.success(pollingUpdateV2VO);
        }
    }

    /**
     * 5.1.5 查询对话输出接口V3（轮巡查结果）【增加思维链和联网搜索来源信息】
     */
    @PostMapping(value = "/assistant/chat/pollingUpdate", headers = "x-yun-api-version=v3")
    public BaseResult<PollingUpdateV3VO> chatPollingUpdateV3(@RequestBody AlgorithmChatPollingUpdateDTO dto) {
        // 参数校验
        dto.validate();
        PollingUpdateV3VO pollingUpdateV3VO = algorithmChatHistoryService.chatPollingUpdateV3(dto);
        if (null == pollingUpdateV3VO) {
            return BaseResult.success((PollingUpdateV3VO) null);
        }

        ContentResultVO contentResultVO = pollingUpdateV3VO.getResult();
        // 是否中断轮巡
        if (isInterrupt(contentResultVO)) {
            // 中断
            return BaseResult.error(contentResultVO.getResultCode(), contentResultVO.getResultMsg(), pollingUpdateV3VO);
        } else {
            return BaseResult.success(pollingUpdateV3VO);
        }
    }

    /**
     * 是否中断轮巡
     *
     * @Author: WeiJingKun
     */
    private boolean isInterrupt(ContentResultVO contentResultVO) {
        if (contentResultVO != null) {
            /** 中断轮巡状态 */
            Integer taskStatus = contentResultVO.getTaskStatus();
            // 任务状态不为null，且任务失败，中断轮巡
            if (taskStatus != null && TaskStatusEnum.isTaskFail(taskStatus)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 5.1.6 历史会话删除
     */
    @PostMapping("/assistant/chat/delete")
    public BaseResult<?> chatDelete(@RequestBody @Valid AlgorithmChatDeleteDTO dto) {
        // 参数校验
        dto.validate();
        algorithmChatHistoryService.chatDelete(dto);
        return BaseResult.success();
    }

    /**
     * 对话应用类型信息列表接口
     */
    @PostMapping("/assistant/chat/application/type/list")
    public BaseResult<List<TabChatApplicationType>> applicationTypeList(
            @RequestBody @Valid ApplicationTypeListDTO dto) {
        return BaseResult.success(chatApplicationTypeService.typeList(dto));
    }

    /**
     * 5.1.13 对话内容停止输出接口
     *
     * @param dto
     * @return
     */
    @PostMapping("/assistant/chat/stop")
    public BaseResult chatStop(@RequestBody AlgorithmChatStopDTO dto) {
        // 参数校验
        final AbstractResultCode check = dto.check(sourceChannelsProperties);
        if (check != null) {
            log.info("参数校验失败");
            return BaseResult.error(check);
        }

        // 对话停止
        algorithmChatHistoryService.chatStop(dto);
        return BaseResult.success();
    }

    /**
     * 对话结果转储
     *
     * @param dto
     * @return
     */
    @LogAnnotation(LogType.INTERFACE)
    @PostMapping("/assistant/chat/transfer")
    public BaseResult<?> transfer(@RequestBody AlgorithmTransferDTO dto) {
        // 参数校验
        final AbstractResultCode check = dto.check();
        if (check != null) {
            log.info("参数校验失败");
            return BaseResult.error(check);
        }

        // 对话结果转储
        return BaseResult.success(algorithmChatTransferService.transfer(dto));
    }

    /**
     * 对话结果扣费
     *
     * @param dto
     * @return
     */
    @LogAnnotation(LogType.INTERFACE)
    @PostMapping("/assistant/chat/pay")
    public BaseResult<?> chatPay(@RequestBody AIAssistantPayReqDTO dto) {
        ChatPayResultVO payResultVO = null;
        try {
            // 将上下文userId设置为用户id
            if (CharSequenceUtil.isNotEmpty(RequestContextHolder.getUserId())) {
                dto.setUserId(RequestContextHolder.getUserId());
            }
            payResultVO = algorithmChatPayService.chatPay(dto);
        } catch (Exception e) {
            log.error("algorithmChatPayService.chatPay dto:{}, error:", JsonUtil.toJson(dto), e);
            if (e instanceof YunAiBusinessException) {
                YunAiBusinessException ex = (YunAiBusinessException) e;
                return BaseResult.error(ex.getCode(), ex.getMessage());
            }
            return BaseResult.error(AiResultCode.CODE_10000204.getCode(), AiResultCode.CODE_10000204.getMsg());
        }

        return BaseResult.success(payResultVO);
    }

    /**
     * 查询对话状态
     *
     * @param dto 请求参数
     * @return 响应结果
     */
    @PostMapping("/assistant/chat/getMarketStatus")
    public BaseResult<?> chatStatus(@RequestBody AiAssistantStatusReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = dto.checkParam();
        if (check != null) {
            log.info("报名参数校验失败");
            return BaseResult.error(check);
        }

        return BaseResult.success(algorithmChatContentService.getChatStatus(dto));
    }

    /**
     * 批量分享历史对话结果查询
     *
     * @param dto 请求参数
     * @return 响应结果
     */
    @PostMapping(value = "/assistant/chat/share/batchGet", headers = ReqHeadConst.API_VERSION_V1)
    public BaseResult<?> shareBatchGet(@RequestBody ShareBatchGetDTO dto) {
        // 参数校验
        AbstractResultCode check = chatContentValid.shareBatchGetValid(dto);
        if (Objects.nonNull(check)) {
            return BaseResult.error(check);
        }

        // 返回结果
        Map<String, Object> result = new HashMap<>(Const.NUM_16);
        result.put("list", algorithmChatContentService.shareBatchGet(dto));
        return BaseResult.success(result);
    }

    /**
     * 批量查询对话结果
     *
     * @param dto 请求参数
     * @return 智能体列表
     */
    @PostMapping(value = "/assistant/chat/application/get", headers = ReqHeadConst.API_VERSION_V1)
    public BaseResult<?> agentList(@RequestBody AgentBatchGetDTO dto) {
        AbstractResultCode check = sourceChannelValid.channelValid(dto);
        return BaseResult.success(chatApplicationTypeService.getByAppList(dto.getApplicationIdList()));
    }
}
