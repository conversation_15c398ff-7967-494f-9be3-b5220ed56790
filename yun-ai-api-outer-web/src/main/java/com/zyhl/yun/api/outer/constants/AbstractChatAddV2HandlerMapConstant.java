package com.zyhl.yun.api.outer.constants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.python.jline.internal.Log;

import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.enums.AssistantEnum;

import cn.hutool.core.collection.CollUtil;

/**
 * 抽象对话v2执行器map映射
 * 
 * <AUTHOR>
 */
public abstract class AbstractChatAddV2HandlerMapConstant {

	private AbstractChatAddV2HandlerMapConstant() {
	}

	/**
	 * map 映射
	 */
	private static Map<String, List<AbstractChatAddV2Handler>> businessTypes = new HashMap<>();

	/**
	 * 过滤执行器 by 助手类型
	 * 
	 * @param assistantEnum      助手类型
	 * @param chatAddHandlerList 执行器列表
	 * @return 执行器列表
	 */
	public static List<AbstractChatAddV2Handler> filterByAssistantEnum(AssistantEnum assistantEnum,
			List<AbstractChatAddV2Handler> chatAddHandlerList) {
		String code = assistantEnum.getCode();
		if (businessTypes.containsKey(code) && CollUtil.isNotEmpty(businessTypes.get(code))) {
			return businessTypes.get(code);
		}
		List<AbstractChatAddV2Handler> newList = realtimeFilterByAssistantEnum(assistantEnum, chatAddHandlerList);
		businessTypes.put(code, newList);
		return newList;
	}

	/**
	 * 实时过滤执行器 by 助手类型
	 * 
	 * @param assistantEnum      助手类型
	 * @param chatAddHandlerList 执行器列表
	 * @return 执行器列表
	 */
	private static List<AbstractChatAddV2Handler> realtimeFilterByAssistantEnum(AssistantEnum assistantEnum,
			List<AbstractChatAddV2Handler> chatAddHandlerList) {
		if (CollUtil.isEmpty(chatAddHandlerList) || null == assistantEnum) {
			return null;
		}
		List<AbstractChatAddV2Handler> newList = new ArrayList<>();
		for (AbstractChatAddV2Handler chatAddHandler : chatAddHandlerList) {
			if (CollUtil.isEmpty(chatAddHandler.getBusinessTypes())) {
				continue;
			}
			for (ChatBusinessTypeEnum businessTypes : chatAddHandler.getBusinessTypes()) {
				if (null != businessTypes.getType() && businessTypes.getType().equals(assistantEnum.getCode())) {
					newList.add(chatAddHandler);
					break;
				}
			}
		}
		Log.info("filterByAssistantEnum assistantEnum:{} newList size:{}", assistantEnum.getCode(), newList.size());
		return newList;
	}
}
