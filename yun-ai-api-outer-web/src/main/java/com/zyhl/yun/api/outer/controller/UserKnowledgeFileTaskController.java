package com.zyhl.yun.api.outer.controller;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileAddReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileDeleteReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileTaskResultReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.UserKnowledgeFileTaskService;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.controller.validate.UserKnowledgeFileValid;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.zyhl.yun.api.outer.constants.Const.NUM_16;

/**
 * 个人知识库文件上传任务控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(headers = {ReqHeadConst.CLIENT_INFO, ReqHeadConst.APP_CHANNEL})
public class UserKnowledgeFileTaskController {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private UserKnowledgeFileTaskService userKnowledgeFileTaskService;
    @Resource
    private UserKnowledgeFileValid userKnowledgeFileValid;

    /**
     * 个人知识库批量上传文档
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/assistant/knowledge/personal/file/upload", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> add(@RequestBody KnowledgeFileAddReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeFileValid.addValid(dto);
        if (check != null) {
            log.info("【知识库文件新增】参数校验失败");
            return BaseResult.error(check);
        }

        // 加锁，防止频繁新增
        final RLock lock = redissonClient.getLock(String.format(RedisConstants.KNOWLEDGE_FILE_ADD_LOCK, dto.getUserId()));
        try {
            if (!lock.tryLock(RedisConstants.WAIT_TIME_1, RedisConstants.LEASE_TIME_10, TimeUnit.SECONDS)) {
                log.info("【知识库文件新增】正在保存文件，请稍后再试，用户id：{}", dto.getUserId());
                return BaseResult.error(ResultCodeEnum.REQUEST_TOO_FREQUENTLY);
            }

            // 保存数据库
            Map<String, Object> result = new HashMap<>(NUM_16);
            result.put("taskId", String.valueOf(userKnowledgeFileTaskService.add(dto)));

            return BaseResult.success(result);
        } catch (InterruptedException e) {
            log.error("【知识库文件新增】新增文件获取锁异常：{}", e.getMessage(), e);
            return BaseResult.error(ResultCodeEnum.UNKNOWN_ERROR);
        } catch (Exception e) {
            log.error("【知识库文件新增】新增文件异常：{}", e.getMessage(), e);
            if (e instanceof YunAiBusinessException) {
                YunAiBusinessException ex = (YunAiBusinessException) e;
                if (ex.getExceptionEnum() != null) {
                    return BaseResult.error(ex.getExceptionEnum());
                }
                return BaseResult.error(ex.getCode(), ex.getMessage());
            }
            return BaseResult.error(ResultCodeEnum.UNKNOWN_ERROR);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


    /**
     * 个人知识库批量上传文档
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/assistant/knowledge/personal/file/upload", headers = {ReqHeadConst.API_VERSION_V2})
    public BaseResult<?> add180(@RequestBody KnowledgeFileAddReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeFileValid.addValid(dto);
        if (check != null) {
            log.info("【知识库文件新增】参数校验失败");
            return BaseResult.error(check);
        }

        // 加锁，防止频繁新增
        final RLock lock = redissonClient.getLock(String.format(RedisConstants.KNOWLEDGE_FILE_ADD_LOCK, dto.getUserId()));
        try {
            if (!lock.tryLock(RedisConstants.WAIT_TIME_1, RedisConstants.LEASE_TIME_10, TimeUnit.SECONDS)) {
                log.info("【知识库文件新增】正在保存文件，请稍后再试，用户id：{}", dto.getUserId());
                return BaseResult.error(ResultCodeEnum.REQUEST_TOO_FREQUENTLY);
            }

            // 保存数据库
            return BaseResult.success(userKnowledgeFileTaskService.add180(dto));
        } catch (InterruptedException e) {
            log.error("【知识库文件新增】新增文件获取锁异常：{}", e.getMessage(), e);
            return BaseResult.error(ResultCodeEnum.UNKNOWN_ERROR);
        } catch (Exception e) {
            log.error("【知识库文件新增】新增文件异常：{}", e.getMessage(), e);
            if (e instanceof YunAiBusinessException) {
                YunAiBusinessException ex = (YunAiBusinessException) e;
                if (ex.getExceptionEnum() != null) {
                    return BaseResult.error(ex.getExceptionEnum());
                }
                return BaseResult.error(ex.getCode(), ex.getMessage());
            }
            return BaseResult.error(ResultCodeEnum.UNKNOWN_ERROR);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 获取知识库文档上传任务结果
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/assistant/knowledge/personal/file/getUploadTask", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> getTaskResult(@RequestBody KnowledgeFileTaskResultReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeFileValid.taskValid(dto);
        if (check != null) {
            log.info("【知识库文件任务结果】参数校验失败");
            return BaseResult.error(check);
        }

        // 查询任务结果
        return BaseResult.success(userKnowledgeFileTaskService.getTaskResult(dto));
    }


    /**
     * 删除知识库文件
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/assistant/knowledge/personal/file/delete", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> delete(@RequestBody KnowledgeFileDeleteReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeFileValid.deleteValid(dto);
        if (check != null) {
            log.info("【知识库文件删除】参数校验失败");
            return BaseResult.error(check);
        }

        // 加锁，防止频繁删除
        final RLock lock = redissonClient.getLock(String.format(RedisConstants.KNOWLEDGE_FILE_DEL_LOCK, dto.getUserId()));
        try {
            if (!lock.tryLock(RedisConstants.WAIT_TIME_1, RedisConstants.LEASE_TIME_10, TimeUnit.SECONDS)) {
                log.info("【知识库文件删除】正在删除文件，请稍后再试，用户id：{}", dto.getUserId());
                return BaseResult.error(ResultCodeEnum.REQUEST_TOO_FREQUENTLY);
            }

            // 删除数据
            userKnowledgeFileTaskService.delete(dto);
            return BaseResult.success();
        } catch (Exception e) {
            log.error("【知识库文件删除】删除文件获取锁异常：{}", e.getMessage(), e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        return BaseResult.success();
    }

    /**
     *  获取异步任务信息接口
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/task/get", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> getTask(@RequestBody KnowledgeFileTaskResultReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeFileValid.taskValid(dto);
        if (check != null) {
            log.info("【获取异步任务信息】参数校验失败");
            return BaseResult.error(check);
        }

        // 查询任务结果
        return BaseResult.success(userKnowledgeFileTaskService.getTask(dto));
    }
}
