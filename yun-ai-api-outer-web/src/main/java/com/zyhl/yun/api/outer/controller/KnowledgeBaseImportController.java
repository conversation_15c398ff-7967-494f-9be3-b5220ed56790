package com.zyhl.yun.api.outer.controller;
import cn.hutool.core.util.StrUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.plugin.logger.annotation.LogAnnotation;
import com.zyhl.hcy.plugin.logger.enums.LogType;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.knowledge.*;
import com.zyhl.yun.api.outer.application.service.KnowledgeBaseImportService;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeCheckResultVO;
import com.zyhl.yun.api.outer.application.vo.knowledge.PersonalKnowledgeImportTaskVO;
import com.zyhl.yun.api.outer.config.KnowledgePersonalProperties;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.controller.validate.UserKnowledgeFileValid;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 知识库导入控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping
@Slf4j
public class KnowledgeBaseImportController {

    @Resource
    private KnowledgeBaseImportService knowledgeBaseImportService;

    @Resource
    private UserKnowledgeFileValid userKnowledgeFileValid;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private KnowledgePersonalProperties knowledgePersonalProperties;

    /**
     * 个人知识库批量导入资源
     *
     * @param dto 请求参数
     * @return 结果
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/file/batchImport", headers = {ReqHeadConst.API_VERSION_V1})
    @LogAnnotation(LogType.INTERFACE)
    public BaseResult<?> batchImport(@RequestBody @Validated KnowledgeFileBatchImportReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeFileValid.batchImportValid(dto);
        if (check != null) {
            return BaseResult.error(check);
        }
        // 加锁，防止重复提交
        final RLock lock = redissonClient.getLock(String.format(RedisConstants.KNOWLEDGE_FILE_IMPORT_LOCK, dto.getUserId()));
        try {
            if (!lock.tryLock(RedisConstants.WAIT_TIME_1, RedisConstants.LEASE_TIME_10, TimeUnit.SECONDS)) {
                return BaseResult.error(ResultCodeEnum.REQUEST_TOO_FREQUENTLY);
            }
            // 批量导入资源
            return BaseResult.success(knowledgeBaseImportService.batchImport(dto));
        } catch (YunAiBusinessException e) {
            return BaseResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            return BaseResult.error(ResultCodeEnum.KNOWLEDGE_UPLOAD_IMPORT_ERROR);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 批量删除知识库导入任务
     *
     * @param dto 请求参数
     * @return 结果
     */
    @PostMapping("/assistant/knowledge/personal/v2/base/importTask/delete")
    @LogAnnotation(LogType.INTERFACE)
    public BaseResult<?> batchDelete(@RequestBody KnowledgeFileTaskBatchDeleteReqDTO dto) {
        // 校验参数
        AbstractResultCode resultCode = userKnowledgeFileValid.validBatchDelete(dto);
        if (resultCode != null) {
            return BaseResult.error(resultCode);
        }
        // 批量删除任务
        boolean success = knowledgeBaseImportService.batchDelete(dto.getUserId(), dto.getTaskIdList());
        return BaseResult.success(success);
    }
    

    /**
     * 查看知识库资源导入任务列表
     *
     * @param dto 请求参数
     * @return 结果
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/importTask/list", headers = {ReqHeadConst.API_VERSION_V1})
    @LogAnnotation(LogType.INTERFACE)
    public BaseResult<?> getTaskList(@RequestBody KnowledgeFileTaskListReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeFileValid.taskListValid(dto);
        if (check != null) {
            log.info("【知识库资源导入任务列表】参数校验失败");
            return BaseResult.error(check);
        }
        // 查询任务列表
        return BaseResult.success(knowledgeBaseImportService.listTasks(dto));
    }

    /**
     * 知识库导入资源任务重试
     *
     * @param dto 请求参数
     * @return 结果
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/importTask/retry", headers = {ReqHeadConst.API_VERSION_V1})
    @LogAnnotation(LogType.INTERFACE)
    public BaseResult<?> retryTask(@RequestBody KnowledgeFileTaskRetryReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeFileValid.taskRetryValid(dto);
        if (check != null) {
            return BaseResult.error(check);
        }
        // 加锁，防止重复提交
        final RLock lock = redissonClient.getLock(String.format(RedisConstants.KNOWLEDGE_FILE_RETRY_LOCK, dto.getUserId(), dto.getTaskId()));
        try {
            if (!lock.tryLock(RedisConstants.WAIT_TIME_1, RedisConstants.LEASE_TIME_10, TimeUnit.SECONDS)) {
                return BaseResult.error(ResultCodeEnum.REQUEST_TOO_FREQUENTLY);
            }
            // 重试任务
            PersonalKnowledgeImportTaskVO task = knowledgeBaseImportService.retryTask(dto.getUserId(), dto.getTaskId());
            return BaseResult.success(task);
        } catch (Exception e) {
            log.error("【知识库资源导入任务重试】重试任务获取锁异常：{}", e.getMessage(), e);
            return BaseResult.error(ResultCodeEnum.KNOWLEDGE_UPLOAD_RETRY_ERROR);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 个人知识库资源校验
     *
     * @param dto 请求参数
     * @return 结果
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/importResource/check", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> checkResource(@RequestBody @Validated KnowledgeFileCheckReqDTO dto) {

        AbstractResultCode check = userKnowledgeFileValid.channelValid(dto);
        if (check != null) {
            log.info("【个人知识库资源校验】参数校验失败");
            return BaseResult.error(check);
        }
        KnowledgeCheckResultVO checkResultVO = new KnowledgeCheckResultVO();
        try {
            knowledgeBaseImportService.checkResource(dto);
        } catch (Exception e) {
            if (e instanceof YunAiBusinessException) {
                AbstractResultCode exceptionEnum = ((YunAiBusinessException) e).getExceptionEnum();
                String errorMessage = knowledgePersonalProperties.getParseFailedReason().get(exceptionEnum.getResultCode());
                String resultMsg = StrUtil.isNotBlank(errorMessage)?errorMessage:exceptionEnum.getResultMsg();
                checkResultVO.checkFail(resultMsg);
                return BaseResult.error(exceptionEnum.getResultCode(),resultMsg,checkResultVO);
            } else {
                log.error("【个人知识库资源校验】异常：{}", e.getMessage(), e);
                return BaseResult.error(ResultCodeEnum.UNKNOWN_ERROR);
            }
        }
        checkResultVO.checkSuccess();
        return BaseResult.success(checkResultVO);

    }
} 