package com.zyhl.yun.api.outer.controller.validate;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeLabelAddReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeLabelDeleteReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeLabelListReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeLabelSortReqDTO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowLedgeLabelListVO;
import com.zyhl.yun.api.outer.config.KnowledgePersonalProperties;
import com.zyhl.yun.api.outer.constants.RegConst;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeLabelEnum;
import com.zyhl.yun.api.outer.repository.UserKnowledgeLabelRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 标签入参校验类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UserKnowledgeLabelValid extends SourceChannelValid {

    @Resource
    private KnowledgePersonalProperties knowledgePersonalProperties;
    @Resource
    private UserKnowledgeLabelRepository userKnowledgeLabelRepository;

    /**
     * 参数校验
     *
     * @param dto
     * @return
     */
    public AbstractResultCode addValid(KnowledgeLabelAddReqDTO dto) {

        if (ObjectUtil.isEmpty(dto.getLabelName())) {
            log.info("标签名称为空，labelName：{}", dto.getLabelName());
            return ResultCodeEnum.ERROR_PARAMS;
        } else if (KnowledgeLabelEnum.isExist(dto.getLabelName())) {
            log.info("标签名称已存在，labelName：{}", dto.getLabelName());
            return ResultCodeEnum.LABEL_NAME_EXIST;
        } else if (dto.getLabelName().length() > knowledgePersonalProperties.getLabelNameLen()) {
            log.info("标签名称长度不能超过64个字符，labelName：{}", dto.getLabelName());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        if (!ObjectUtil.isEmpty(dto.getLabelId()) && !dto.getLabelId().matches(RegConst.REG_ID_STR)) {
            log.info("标签id格式不对，labelId：{}", dto.getLabelId());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        return channelValid(dto);
    }

    /**
     * 参数校验
     *
     * @return 枚举
     */
    public AbstractResultCode deleteValid(KnowledgeLabelDeleteReqDTO dto) {

        if (ObjectUtil.isEmpty(dto.getLabelId())) {
            log.info("标签id为空，labelId：{}", dto.getLabelId());
            return ResultCodeEnum.ERROR_PARAMS;
        } else if (!dto.getLabelId().matches(RegConst.REG_ID_STR)) {
            log.info("标签id格式不对，labelId：{}", dto.getLabelId());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        return channelValid(dto);
    }

    /**
     * 参数校验
     *
     * @param dto
     * @return
     */
    public AbstractResultCode listValid(KnowledgeLabelListReqDTO dto) {

        return channelValid(dto);
    }

    /**
     * 参数校验
     *
     * @param dto
     * @return
     */
    public AbstractResultCode sortValid(KnowledgeLabelSortReqDTO dto) {
        // 渠道和用户校验
        AbstractResultCode check = channelValid(dto);
        if (check != null) {
            return check;
        }

        if (ObjectUtil.isEmpty(dto.getLabelList())) {
            log.info("标签列表为空，labelList：{}", dto.getLabelList());
            return ResultCodeEnum.ERROR_PARAMS;
        }
        for (KnowLedgeLabelListVO vo : dto.getLabelList()) {
            if (ObjectUtil.isEmpty(vo.getLabelId()) || !vo.getLabelId().matches(RegConst.REG_ID_STR)) {
                log.info("标签id为空或者格式不正确，labelId：{}", vo.getLabelId());
                return ResultCodeEnum.ERROR_PARAMS;
            }
        }

        // 判断总数
        if (dto.getLabelList().size() != userKnowledgeLabelRepository.count(dto.getUserId())) {
            log.info("排序标签数与数据库的标签数不一致，labelList：{}", dto.getLabelList().size());
            return ResultCodeEnum.ERROR_PARAMS;
        }


        return null;
    }
}
