package com.zyhl.yun.api.outer.controller.validate;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.knowledge.*;
import com.zyhl.yun.api.outer.config.KnowledgePersonalProperties;
import com.zyhl.yun.api.outer.constants.RegConst;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeProfilePhoto;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.*;
import com.zyhl.yun.api.outer.repository.UserKnowledgeRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 个人知识库2.0入参校验类
 *
 * <AUTHOR>
 * @date 2025/04/15
 */
@Slf4j
@Component
public class PersonalKnowledgeValid extends SourceChannelValid {

    @Resource
    UserKnowledgeRepository userKnowledgeRepository;

    @Resource
    private KnowledgePersonalProperties knowledgePersonalProperties;

    /**
     * 创建个人知识库入参校验
     */
    public AbstractResultCode addValid(PersonalKnowledgeAddReqDTO dto) {

        // 知识库名称判空校验，也不允许为空白串
        if (ObjectUtil.isEmpty(dto.getName()) || dto.getName().trim().isEmpty()) {
            log.info("知识库名称为空，name：{}", dto.getName());
            return ResultCodeEnum.ERROR_PARAMS;
        }
        // 对知识库名字做判断，包含"[^/:*?\"<>\\|]+"这些特殊字符即抛出错误
        if (!isNameValid(dto.getName())) {
            log.info("知识库名称包含非法字符，name：{}", dto.getName());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS.getResultCode(), "知识库名称不能包含[^/:*?\"<>\\|]+等特殊字符");
        }
        // 知识库名称限制长度
        if (dto.getName().length() > knowledgePersonalProperties.getNameLength()) {
            log.info("知识库名称字符超过阈值，name：{}", dto.getName());
            String msg = String.format("知识库名称字符超过阈值，最多支持%d个字符", knowledgePersonalProperties.getNameLength());
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_NAME_OUT_NUMBER.getResultCode(), msg);
        }

        // 知识库描述限制长度
        if (ObjectUtil.isNotEmpty(dto.getDescription()) && dto.getDescription().length() >
                knowledgePersonalProperties.getDescriptionLength()) {
            log.info("知识库描述字符超过阈值，description：{}", dto.getDescription());
            String msg = String.format("知识库描述字符超过阈值，最多支持%d个字符", knowledgePersonalProperties.getDescriptionLength());
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_DESCRIPTION_OUT_NUMBER.getResultCode(), msg);
        }

        // 有上传头像信息即校验
        PersonalKnowledgeProfilePhoto profilePhoto = dto.getProfilePhoto();
        if (ObjectUtil.isNotEmpty(profilePhoto)) {
            if (ObjectUtil.isEmpty(profilePhoto.getType())) {
                log.info("头像类型为空，type：{}", profilePhoto.getType());
                return ResultCodeEnum.ERROR_PARAMS;
            }
            if (ObjectUtil.isEmpty(profilePhoto.getPhotoId())) {
                log.info("头像ID为空，photoId：{}", profilePhoto.getPhotoId());
                return ResultCodeEnum.ERROR_PARAMS;
            }

            if (ObjectUtil.isEmpty(profilePhoto.getUrl())) {
                log.info("头像url为空，url：{}", profilePhoto.getUrl());
                return ResultCodeEnum.ERROR_PARAMS;
            }

            // 知识库头像信息校验
            List<KnowledgePersonalProperties.ProfilePhoto> profilePhotoList = knowledgePersonalProperties.getProfilePhotoList();
            if (profilePhotoList.stream().noneMatch(photo ->
                    profilePhoto.getPhotoId().equals(photo.getPhotoId()) &&
                    profilePhoto.getType().equals(photo.getType()) &&
                            profilePhoto.getUrl().equals(photo.getUrl()))) {
                log.info("个人知识库头像信息错误，不符合规范 profilePhoto: {}", JsonUtil.toJson(profilePhoto));
                throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_PHOTO_URL_ERROR);
            }
        }

        // 校验知识库开放私密参数
        Integer openLevel = dto.getOpenLevel();
        if (null != openLevel && !KnowledgeStatusEnum.OPEN.getStatus().equals(openLevel) &&
                !KnowledgeStatusEnum.PRIVATE.getStatus().equals(openLevel)) {
            return ResultCodeEnum.ERROR_PARAMS;
        }

        // 渠道校验
        return channelValid(dto); 

    }

    private static boolean isNameValid(String input) {
        // 定义被排除的字符集合
        Set<Character> forbiddenSet = new HashSet<>();
        // 排除的字符
        String forbiddenChars = "[^/:*?\"<>\\|]+"; 
        for (char c : forbiddenChars.toCharArray()) {
            forbiddenSet.add(c);
        }

        // 遍历输入字符串的每个字符
        for (int i = 0; i < input.length(); i++) {
            char currentChar = input.charAt(i);
            if (forbiddenSet.contains(currentChar)) {
            	// 发现非法字符
                return false; 
            }
        }
        // 所有字符均合法
        return true; 
    }

    /**
     * 删除个人知识库入参校验
     */
    public AbstractResultCode deletelValid(PersonalKnowledgeDeleteReqDTO dto) {
        if (ObjectUtil.isEmpty(dto.getSourceChannel())) {
            log.info("渠道为空，sourceChannel：{}", dto.getSourceChannel());
            return ResultCodeEnum.ERROR_PARAMS;
        } else if (!sourceChannelsProperties.isExist(dto.getSourceChannel())) {
            log.info("渠道号不存在，sourceChannel:{}", dto.getSourceChannel());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        if (ObjectUtil.isEmpty(dto.getBaseId())) {
            log.info("知识库id为空，baseId：{}", dto.getBaseId());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        return dto.checkUserId();
    }

    /**
     * 更新个人知识库入参校验
     */
    public AbstractResultCode updateValid(PersonalKnowledgeUpdateReqDTO dto) {

        // 知识库ID校验
        if (ObjectUtil.isEmpty(dto.getBaseId())) {
            log.info("知识库ID为空，baseId：{}", dto.getBaseId());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        // 知识库名称判空校验，也不允许为空白串
        if (ObjectUtil.isEmpty(dto.getName()) || dto.getName().trim().isEmpty()) {
            log.info("知识库名称为空，name：{}", dto.getName());
            return ResultCodeEnum.ERROR_PARAMS;
        }
        // 对知识库名字做判断，包含"[^/:*?\"<>\\|]+"这些特殊字符即抛出错误
        if (!isNameValid(dto.getName())) {
            log.info("知识库名称包含非法字符，name：{}", dto.getName());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS.getResultCode(), "知识库名称不能包含[^/:*?\"<>\\|]+等特殊字符");
        }
        // 知识库名称限制长度
        if (ObjectUtil.isNotEmpty(dto.getName()) && dto.getName().length() > knowledgePersonalProperties.getNameLength()) {
            log.info("知识库名称字符超过阈值，name：{}", dto.getName());
            String msg = String.format("知识库名称字符超过阈值，最多支持%d个字符", knowledgePersonalProperties.getNameLength());
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_NAME_OUT_NUMBER.getResultCode(), msg);
        }

        // 知识库描述限制长度
        if (ObjectUtil.isNotEmpty(dto.getDescription()) && dto.getDescription().length() >
                knowledgePersonalProperties.getDescriptionLength()) {
            log.info("知识库描述字符超过阈值，description：{}", dto.getDescription());
            String msg = String.format("知识库描述字符超过阈值，最多支持%d个字符", knowledgePersonalProperties.getDescriptionLength());
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_DESCRIPTION_OUT_NUMBER.getResultCode(), msg);
        }

        // 知识库公开私密校验
        if (ObjectUtil.isNotEmpty(dto.getOpenLevel())) {
            if (KnowledgeStatusEnum.OPEN.getStatus().equals(dto.getOpenLevel()) ||
                    KnowledgeStatusEnum.PRIVATE.getStatus().equals(dto.getOpenLevel())) {
                log.info("知识库公开私密参数正确，openLevel：{}", dto.getOpenLevel());
            } else {
                log.info("知识库公开私密参数错误，openLevel：{}", dto.getOpenLevel());
                return ResultCodeEnum.ERROR_PARAMS;
            }
        }

        // 知识库头像信息校验
        PersonalKnowledgeProfilePhoto profilePhoto = dto.getProfilePhoto();
        if (ObjectUtil.isNotEmpty(profilePhoto) ) {
            // 知识库头像信息校验
            List<KnowledgePersonalProperties.ProfilePhoto> profilePhotoList = knowledgePersonalProperties.getProfilePhotoList();
            if (profilePhotoList.stream().noneMatch(photo ->
                    profilePhoto.getPhotoId().equals(photo.getPhotoId()) &&
                            profilePhoto.getType().equals(photo.getType()) &&
                            profilePhoto.getUrl().equals(photo.getUrl()))) {
                log.info("个人知识库头像信息错误，不符合规范 profilePhoto: {}", JsonUtil.toJson(profilePhoto));
                throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_PHOTO_URL_ERROR);
            }
        }

        // 渠道校验
        return channelValid(dto); 

    }

    /**
     * 批量获取个人知识库入参校验
     */
    public AbstractResultCode batchGetValid(KnowledgeBatchGetReqDTO dto) {

        if (ObjectUtil.isEmpty(dto.getSourceChannel())) {
            log.info("渠道为空，sourceChannel：{}", dto.getSourceChannel());
            return ResultCodeEnum.ERROR_PARAMS;
        } else if (!sourceChannelsProperties.isExist(dto.getSourceChannel())) {
            log.info("渠道号不存在，sourceChannel:{}", dto.getSourceChannel());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        if (ObjectUtil.isEmpty(dto.getBaseIdList())) {
            log.info("知识库ID列表为空，baseIdList：{}", dto);
            return ResultCodeEnum.ERROR_PARAMS;
        }

        try {
            dto.getBaseIdList().stream().map(Long::valueOf).collect(Collectors.toList());
        } catch (Exception e) {
            return ResultCodeEnum.ERROR_PARAMS;
        }


        return dto.checkUserId();
    }

    /**
     * 查询个人知识库列表入参校验
     */
    public AbstractResultCode listValid(PersonalKnowledgeListReqDTO dto) {

        // baseType知识库类型不为空的时候即校验
        if (ObjectUtil.isNotEmpty(dto.getBaseType())) {
            if (KnowledgeBaseTypeEnum.isExist(dto.getBaseType())) {
                log.info("知识库类型参数正确，baseType：{}", dto.getBaseType());
            } else {
                log.info("知识库类型参数错误，baseType：{}", dto.getBaseType());
                return ResultCodeEnum.ERROR_PARAMS;
            }
        }

        // 分页信息校验
        PageInfoDTO pageInfo = PageInfoDTO.getReqDTO(dto.getPageInfo());
        pageInfo.validate(10);
        dto.setPageInfo(pageInfo);
        // 渠道校验
        return channelValid(dto); 

    }


    /**
     * 查询个人知识库资源列表入参校验
     */
    public AbstractResultCode resourceListValid(PersonalKnowledgeResourceListReqDTO dto) {
        // 知识库ID校验
        if (ObjectUtil.isEmpty(dto.getBaseId())) {
            log.info("知识库ID为空，baseId：{}", dto.getBaseId());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        // 分页信息校验
        if (dto.getPageInfo() == null) {
            dto.setPageInfo(PageInfoDTO.builder().pageCursor("0").build());
        }
        if (CharSequenceUtil.isEmpty(dto.getPageInfo().getPageCursor())) {
            dto.getPageInfo().setPageCursor("0");
        }
        if (!dto.getPageInfo().getPageCursor().matches(RegConst.REG_DATA_STR)) {
            log.info("分页游标格式的不正确，pageCursor：{}", dto.getPageInfo().getPageCursor());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        if (dto.getPageInfo().getPageSize() == null || dto.getPageInfo().getPageSize() < 0) {
            dto.getPageInfo().setPageSize(10);
        }
        // 校验分页数量最小为10
        dto.getPageInfo().validate(10);

        // 解析状态校验
        if (Objects.nonNull(dto.getAiStatus()) && (!FileAiStatusParamEnum.isExist(dto.getAiStatus()))) {
                log.info("aiStatus格式不正确，aiStatus：{}", dto.getAiStatus());
                return ResultCodeEnum.ERROR_PARAMS;

        }

        // 资源类型校验
        if (Objects.nonNull(dto.getResourceType()) && (!KnowledgeResourceTypeEnum.isExist(dto.getResourceType()))) {
                log.info("resourceType格式不正确，resourceType：{}", dto.getResourceType());
                return ResultCodeEnum.ERROR_PARAMS;

        }

        // 排序字段校验
        if (Objects.nonNull(dto.getSortType()) && (!FileSortTypeEnum.isExist(dto.getSortType()))) {
                log.info("sortType格式不正确，sortType：{}", dto.getSortType());
                return ResultCodeEnum.ERROR_PARAMS;

        }

        // 升降序校验
        if (Objects.nonNull(dto.getSortDirection()) && (!FileSortDirectionEnum.isExist(dto.getSortDirection()))) {
                log.info("sortDirection格式不正确，sortDirection为空：{}", dto.getSortDirection());
                return ResultCodeEnum.ERROR_PARAMS;

        }

        // 资源类型校验
        if (ObjectUtil.isNotEmpty(dto.getType()) && FileTypeEnum.getByYunDiskFileType(dto.getType()) == null) {
            log.info("type格式不正确，type：{}", dto.getType());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        // 文件分类列表校验
        if (!FileCategoryEnum.checkCategoryList(dto.getCategoryList())) {
            log.info("categoryList格式不正确，categoryList：{}", dto.getCategoryList());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        return channelValid(dto);
    }

    /**
     * 知识库文件下载地址参数校验
     *
     * @return 枚举
     */
    public AbstractResultCode downloadUrlValid(KnowledgeFileUrlReqDTO dto) {

        if (CharSequenceUtil.isEmpty(dto.getFileId())) {
            log.info("文件id为空，fileId:{}", dto.getFileId());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        return channelValid(dto);
    }

    public AbstractResultCode countValid(PersonalKnowledgeCountReqDTO dto) {
        if (StringUtils.isBlank(dto.getCountTime())) {
            log.info("新成功解析文件的统计开始时间为空，countTime:{}", dto.getCountTime());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        return channelValid(dto);
    }
}
