package com.zyhl.yun.api.outer.config;


import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 拦截器路径配置
 * @author: yang<PERSON><PERSON>n
 */
@Component
@ConfigurationProperties("hcy.interceptor-path")
public class InterceptorPathConfig {

    private List<String> interceptionPath;

    public List<String> getInterceptionPath() {
        return interceptionPath;
    }

    public void setInterceptionPath(List<String> list) {
        this.interceptionPath = list;
    }
}
