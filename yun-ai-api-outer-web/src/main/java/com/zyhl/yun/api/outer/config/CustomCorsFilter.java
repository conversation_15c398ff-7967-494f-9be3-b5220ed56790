//package com.zyhl.yun.api.outer.config;
//
//import org.springframework.stereotype.Component;
//
//import javax.servlet.*;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//
//@Component
//public class CustomCorsFilter implements Filter {
//
//    @Override
//    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException {
//        HttpServletRequest request = (HttpServletRequest) req;
//        HttpServletResponse response = (HttpServletResponse) res;
//
//        // 添加必要的CORS头
//        String[] allowedOrigins = {"http://localhost:8080", "https://miniprogram.mail.10086.cn", "https://yun.139.com"};
//        String originHeader = request.getHeader("Origin");
//        for (String allowedOrigin : allowedOrigins) {
//            if (allowedOrigin.equals(originHeader)) {
//                response.setHeader("Access-Control-Allow-Origin", allowedOrigin);
//                break;
//            }
//        }
//        response.setHeader("Access-Control-Allow-Methods", "**");
//        response.setHeader("Access-Control-Allow-Headers", "x-DeviceInfo,Content-Type,CMS-DEVICE,x-yun-channel-source,x-huawei-channelSrc,x-yun-svc-type,x-SvcType,x-m4c-caller,x-m4c-src,x-inner-ntwk,mcloud-route,mcloud-version,mcloud-channel,mcloud-client,mcloud-sign,mcloud-skey,INNER-HCY-ROUTER-HTTPS,x-yun-sbox-session-id,x-yun-module-type,x-yun-app-channel,x-yun-client-info,Authorization,x-m4c-token,x-m4c-account,x-yun-api-version,mcloud-address-book-token,mcloud-address-book-version,x-UserAgent,Accept,Range,rangeType,contentSize,uploadtaskID,partNumber,partSize,uploadtaskID,CMS-CLIENT,Access-Control-Allow-Credentials,Host,Accept-Encoding,Connection,Content-Encoding,Content-Length,X-Forwarded-For,Date,User-Agent,x-yun-op-type,x-yun-sub-op-type,x-yun-sdk-channel,x-yun-device-id,APP_CP,CP_VERSION,API_VERSION,Accept-Language,APP_AUTH,APP_NUMBER,NOTE_TOKEN,X-Tingyun-Id,x-southAuth,X-WSSE,x-NetType,x-MM-Source,CMS-SKey,clientId,caller,signature,version,timeStamp,msgId"); // 允许content-type和其他请求头
//        response.setHeader("Access-Control-Allow-Credentials", "true");
//        response.setHeader("Access-Control-Max-Age", "3600");
//
//        // 如果是预检请求（OPTIONS请求），则直接返回
//        if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
//            response.setStatus(HttpServletResponse.SC_OK);
//        } else {
//            // 否则，继续处理请求
//            chain.doFilter(req, res);
//        }
//    }
//
//    @Override
//    public void init(FilterConfig filterConfig) {
//        // 可以在这里进行初始化操作（如果有需要的话）
//    }
//
//    @Override
//    public void destroy() {
//        // 可以在这里进行资源清理操作（如果有需要的话）
//    }
//}
