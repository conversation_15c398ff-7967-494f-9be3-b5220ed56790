package com.zyhl.yun.api.outer.controller;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.dto.PptTemplateDTO;
import com.zyhl.yun.api.outer.application.service.PptTemplateService;
import com.zyhl.yun.api.outer.application.vo.AiPptTemplateInfoVO;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 阿里AI-PPT模板 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/23
 */
@Slf4j
@RestController
@RequestMapping(value = "/assistant/aippt",
        headers = {ReqHeadConst.API_VERSION, ReqHeadConst.CLIENT_INFO, ReqHeadConst.APP_CHANNEL})
public class PptTemplateController {

    @Resource
    PptTemplateService pptTemplateService;

    /**
     * 查询AI-PPT模板列表接口
     *
     * @param dto 阿里AI-PPT模板入参
     * @return Map key为分类名称，value为模板列表
     */
    @PostMapping("/template/list")
    public BaseResult<?> pptTemplateList(@RequestBody @Valid PptTemplateDTO dto) {
        Map<String, List<AiPptTemplateInfoVO>> resultMap = pptTemplateService.pptTemplateList(dto);
        return BaseResult.success(resultMap);
    }

}
