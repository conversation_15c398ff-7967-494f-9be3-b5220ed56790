package com.zyhl.yun.api.outer.controller;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeShareAddReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeShareGetReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.KnowledgeShareService;
import com.zyhl.yun.api.outer.application.vo.knowledge.PersonalKnowledgeJoinResultVO;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.controller.validate.PersonalKnowledgeShareValid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * className:KnowledgeShareController
 * description: 知识库分享控制器
 *
 * <AUTHOR>
 * @date 2025/04/12
 */
@Slf4j
@RestController
@RequestMapping(headers = {ReqHeadConst.CLIENT_INFO, ReqHeadConst.APP_CHANNEL})
public class KnowledgeShareController {

    @Resource
    KnowledgeShareService knowledgeShareService;

    @Resource
    private PersonalKnowledgeShareValid personalKnowledgeShareValid;

    /**
     * 个人知识库分享加入
     * @param dto 入参
     * @return 返回
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/share/join", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> join(@RequestBody KnowledgeShareAddReqDTO dto) {

        // 参数校验
        final AbstractResultCode check = personalKnowledgeShareValid.join(dto);
        if (check != null) {
            log.info("【个人知识库分享加入】参数校验失败");
            return BaseResult.error(check);
        }

        knowledgeShareService.joinKnowledge(dto);
        return BaseResult.success();
    }

    /**
     * 查询是否已加入分享知识库
     * @param dto 入参
     * @return 返回
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/share/get", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<PersonalKnowledgeJoinResultVO> get(@RequestBody KnowledgeShareGetReqDTO dto) {

        // 参数校验
        final AbstractResultCode check = personalKnowledgeShareValid.get(dto);
        if (check != null) {
            log.info("【查询是否已加入分享知识库】参数校验失败");
            return BaseResult.error(check);
        }
        return BaseResult.success(new PersonalKnowledgeJoinResultVO(knowledgeShareService.get(dto)));

    }

}
