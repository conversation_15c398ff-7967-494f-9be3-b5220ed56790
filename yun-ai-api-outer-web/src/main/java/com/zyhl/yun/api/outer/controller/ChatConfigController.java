package com.zyhl.yun.api.outer.controller;

import static com.zyhl.yun.api.outer.constants.Const.NUM_16;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.dto.ChatConfigDTO;
import com.zyhl.yun.api.outer.application.dto.ChatConfigGetDTO;
import com.zyhl.yun.api.outer.application.dto.ConfigDTO;
import com.zyhl.yun.api.outer.application.service.chat.ChatConfigService;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.domain.vo.ChatConfigV2VO;
import com.zyhl.yun.api.outer.domain.vo.ChatConfigVO;
import com.zyhl.yun.api.outer.domain.vo.ModelConfigVO;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 会话大语言模型设定Controller类
 *
 * <AUTHOR>
 * @version 2024年02月28日 14:44
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(headers = {"x-yun-api-version", "x-yun-client-info", "x-yun-app-channel"})
public class ChatConfigController {

    private final ChatConfigService chatConfigService;
    @Resource
    private ModelProperties modelProperties;
    @Resource
    private SourceChannelsProperties sourceChannelsProperties;

    /**
     * 用户会话设置接口
     *
     * @param dto 会话设置参数
     * @return BaseResult
     */
    @PostMapping("/assistant/chat/config")
	public BaseResult setUserConfig(@RequestBody ChatConfigDTO dto) {
		// 参数校验
		AbstractResultCode check = dto.check(sourceChannelsProperties);
		if (check != null) {
			return BaseResult.error(check);
		}
		String modelType = dto.getModelType();
		// 是否校验模型
		boolean checkModelType = false;
		if (null == dto.getNetworkSearchStatus()) {
			// 联网状态空，需要校验modelType
			checkModelType = true;
		} else {
			// 联网状态不为空，模型编码不为空，也需要校验modelType
			checkModelType = StringUtils.isNotEmpty(modelType);
		}
		if (checkModelType && !modelProperties.isExist(dto.getAssistantEnum(), dto.getBusinessType(), modelType, true)) {
			log.info("【会话设置】模型编码不存在，助手枚举：{}，入参模型编码：{}", dto.getAssistantEnum().getCode(), dto.getModelType());
			return BaseResult.error(ResultCodeEnum.ERROR_PARAMS);
		}
		boolean updated = chatConfigService.config(dto);
		return updated ? BaseResult.success() : BaseResult.error();
	}

    /**
     * 会话设置查询接口，返回用户会话设置
     *
     * @param dto 会话设置查询参数
     * @return BaseResult
     */
    @PostMapping("/assistant/chat/config/get")
    public BaseResult<List<ChatConfigVO>> getUserConfig(@RequestBody ChatConfigGetDTO dto) {
        // 参数校验
        AbstractResultCode check = dto.check(sourceChannelsProperties);
        if (check != null) {
            return BaseResult.error(check);
        }

        return BaseResult.success(chatConfigService.get(dto));
    }
    
    /**
     * 会话设置查询接口，返回用户会话设置 V2
     *
     * @param dto 会话设置查询参数
     * @return BaseResult
     */
    @PostMapping(value = "/assistant/chat/config/get", headers = ReqHeadConst.API_VERSION_V2)
    public BaseResult<ChatConfigV2VO> getUserConfigV2(@RequestBody ChatConfigGetDTO dto) {
        // 参数校验
        AbstractResultCode check = dto.check(sourceChannelsProperties);
        if (check != null) {
            return BaseResult.error(check);
        }

        return BaseResult.success(chatConfigService.getConfigV2(dto));
    }


    /**
     * 小天助手1.2.0版本开始
     * 获取助手配置信息
     *
     * @param dto 请求参数
     * @return
     */
    @PostMapping("/assistant/config/get")
    public BaseResult<?> getChatConfig(@RequestBody ConfigDTO dto) {
        // 参数校验
        AbstractResultCode check = dto.check(sourceChannelsProperties);
        if (check != null) {
            return BaseResult.error(check);
        }
        Map<String, ModelProperties.ModelLimitConfig> limit = modelProperties.getLimitByAssistantEnum(dto.getAssistantEnum(), dto.getBusinessType());
        if (ObjectUtil.isEmpty(limit)) {
            log.info("模型配置为空，model 配置为空");
            return BaseResult.error(ResultCodeEnum.ERROR_NACOS_CONFIG);
        }

        // 代码量比较少，不需要写service类，如果后续有扩展再写
        final List<ModelConfigVO> list = new ArrayList<>();
        limit.forEach((k, v) -> list.add(new ModelConfigVO(v.getName(), k, v.getLength(), v.getSort())));

        // 返回结果
        Map<String, Object> result = new HashMap<>(NUM_16);
		List<ModelConfigVO> modelConfigList = list.stream().sorted(Comparator.comparing(ModelConfigVO::getSort))
				.collect(Collectors.toList());
        result.put("modelConfig", modelConfigList);

        return BaseResult.success(result);
    }

}
