package com.zyhl.yun.api.outer.controller.validate;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.yun.api.outer.application.dto.ShareBatchGetDTO;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatMessageService;
import com.zyhl.yun.api.outer.constants.RegConst;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 描述：助手对话参数校验
 *
 * <AUTHOR> zhumaoxian  2025/3/10 11:46
 */
@Slf4j
@Component
public class ChatContentValid extends SourceChannelValid {

    @Resource
    private AlgorithmChatMessageService algorithmChatMessageService;

    /**
     * 批量获取对话内容校验
     *
     * @param dto 请求参数
     * @return 错误码
     */
    public AbstractResultCode shareBatchGetValid(ShareBatchGetDTO dto) {
        AbstractResultCode resultCode = channelValid(dto);
        if (Objects.nonNull(resultCode)) {
            return resultCode;
        }

        if ((ObjectUtil.isEmpty(dto.getSessionId()) && ObjectUtil.isEmpty(dto.getApplicationType()))
                || (ObjectUtil.isNotEmpty(dto.getSessionId()) && ObjectUtil.isNotEmpty(dto.getApplicationType()))) {
            log.info("【参数校验】sessionId和applicationType不能同时为空也不能同时有值，sessionId:{},applicationType:{}", dto.getSessionId(), dto.getApplicationType());
            return ResultCodeEnum.ERROR_PARAMS;
        }
        if (ObjectUtil.isNotEmpty(dto.getSessionId())) {
            if (!dto.getSessionId().matches(RegConst.REG_ID_STR)) {
                log.info("【参数校验】sessionId格式不正确，sessionId:{}", dto.getSessionId());
                return ResultCodeEnum.ERROR_PARAMS;
            }
            if (!algorithmChatMessageService.exist(dto.getSessionId(), dto.getUserId())) {
                log.info("【参数校验】sessionId不存在，sessionId:{}", dto.getSessionId());
                return ResultCodeEnum.SESSION_INFO_NOT_FOUND;
            }
        }
        if (ObjectUtil.isNotEmpty(dto.getApplicationType()) && !ApplicationTypeEnum.isExist(dto.getApplicationType())) {
            log.info("【参数校验】applicationType格式不正确，applicationType:{}", dto.getApplicationType());
            return ResultCodeEnum.ERROR_PARAMS;
        }
        if (ObjectUtil.isEmpty(dto.getDialogueIdList())) {
            log.info("【参数校验】dialogueIdList不能为空");
            return ResultCodeEnum.ERROR_PARAMS;
        }

        for (String id : dto.getDialogueIdList()) {
            if (!id.matches(RegConst.REG_ID_STR)) {
                log.info("【参数校验】dialogueIdList格式不正确，dialogueIdList:{}", id);
                return ResultCodeEnum.ERROR_PARAMS;
            }
        }

        return null;
    }

}
