package com.zyhl.yun.api.outer.controller.validate;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeMailPreviewReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.PersonalKnowledgeNotePreviewDTO;
import com.zyhl.yun.api.outer.enums.ModelTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 知识库文件预览校验
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Slf4j
@Component
public class KnowledgeFilePreviewValid extends SourceChannelValid {

    public AbstractResultCode notePreviewValid(PersonalKnowledgeNotePreviewDTO dto) {
        if (StringUtils.isBlank(dto.getResourceId())) {
            log.info("资源id为空，resourceId:{}", dto.getResourceId());
            return ResultCodeEnum.ERROR_RESOURCEID_CODE;
        }
        return channelValid(dto);
    }

    public AbstractResultCode previewMailValid(KnowledgeMailPreviewReqDTO dto) {
        if (StringUtils.isBlank(dto.getResourceId())) {
            log.info("资源id为空，resourceId:{}", dto.getResourceId());
            return ResultCodeEnum.ERROR_RESOURCEID_CODE;
        }
        if (StringUtils.isBlank(dto.getBaseId())) {
            log.info("知识库id为空，baseId:{}", dto.getBaseId());
            return ResultCodeEnum.KNOWLEDGE_ID_NOT_PROVIDE;
        }
        if (dto.getModel() == null) {
            log.info("返回内容模式model为空");
            return ResultCodeEnum.ERROR_PARAMS;
        }
        if (!ModelTypeEnum.contains(dto.getModel().getValue())) {
            log.info("返回内容模式model类型不存在，model:{}",  dto.getModel().getValue());
            return ResultCodeEnum.ERROR_PARAMS;
        }
        return channelValid(dto);
    }
}
