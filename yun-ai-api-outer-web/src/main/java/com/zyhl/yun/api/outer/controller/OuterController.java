package com.zyhl.yun.api.outer.controller;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.dto.CardInfoDTO;
import com.zyhl.yun.api.outer.application.dto.FaceSearchDTO;
import com.zyhl.yun.api.outer.application.dto.SearchDTO;
import com.zyhl.yun.api.outer.application.dto.TemplateMatchDTO;
import com.zyhl.yun.api.outer.application.service.CardOrcService;
import com.zyhl.yun.api.outer.application.service.ModelLimitService;
import com.zyhl.yun.api.outer.application.service.external.SearchService;
import com.zyhl.yun.api.outer.application.vo.FaceSearchVO;
import com.zyhl.yun.api.outer.application.vo.SearchPageVO;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.domain.resp.TemplateMatchRsqEntity;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 对外Controller
 * <AUTHOR>
 */
@Slf4j
@RestController
public class OuterController {

    @Autowired
    CardOrcService cardOrcService;
    @Resource
    private SearchService searchService;
    @Resource
    private ModelLimitService modelLimitService;
    @Resource
    private AiTextResultRepository aiTextResultRepository;

    @Value("${faceSearch.maxFileSize:10}")
    private String maxFileSize;

    /**
     * 获取卡片信息
     *
     * @param dto 卡片信息数据传输对象
     * @return 基础结果类
     */
    @PostMapping("/ocr/getCardInfo")
    public BaseResult getCardInfo(@RequestBody @Valid CardInfoDTO dto) {
        return BaseResult.success(cardOrcService.getCardInfo(dto));
    }

    /**
     * 智能搜图接口
     *
     * @param dto
     * @return
     */
    @PostMapping("/intelligent/search")
    public BaseResult<SearchPageVO> search(@RequestBody @Valid SearchDTO dto) {
        SearchPageVO pageVO = searchService.getSearchPage(dto);
        return BaseResult.success(pageVO);
    }

    @PostMapping("/assistant/modelLimit")
    public BaseResult modelLimit() {
        // 已弃用的接口，目前只有云邮助手调用
        return BaseResult.success(modelLimitService.queryModelLimit());
    }

    /**
     * 人脸搜图
     *
     * @param dto
     * @return
     */
    @PostMapping("/face/search")
    public BaseResult<FaceSearchVO> search(@RequestBody @Valid FaceSearchDTO dto) {
        dto.base64SizeDetermination(Integer.parseInt(maxFileSize));
        FaceSearchVO faceSearchVO = searchService.getClassFaceInfo(dto);
        return BaseResult.success(faceSearchVO);
    }

    @PostMapping("/hbase/query")
    public BaseResult hbaseQuery(@RequestBody String rowKey) {
        AiTextResultEntity entity = aiTextResultRepository.getByRowKey(rowKey);
        return BaseResult.success(entity);
    }

    /**
     * 图片标签与影集模板匹配模型接口
     */
    @PostMapping("/image/template/match")
    public BaseResult<TemplateMatchRsqEntity> templateMatch(@RequestBody @Valid TemplateMatchDTO dto) {
        return searchService.templateMatch(dto);
    }

}
