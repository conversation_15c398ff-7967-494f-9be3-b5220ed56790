package com.zyhl.yun.api.outer.controller;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.dto.AiMigrationAddReqDTO;
import com.zyhl.yun.api.outer.application.dto.BaseDTO;
import com.zyhl.yun.api.outer.application.service.AlgorithmAiMigrationService;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * AI迁移表：小天助手1.0.1
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(headers = {ReqHeadConst.API_VERSION, ReqHeadConst.CLIENT_INFO})
public class AlgorithmAiMigrationController {

    @Resource
    private AlgorithmAiMigrationService algorithmAiMigrationService;
    @Resource
    private RedissonClient redissonClient;

    /**
     * 报名数据迁移
     *
     * @param dto 入参
     * @return 无返回值
     */
    @PostMapping("/migration/submit")
    public BaseResult<?> add(@RequestBody AiMigrationAddReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = dto.checkParam();
        if (check != null) {
            log.info("【报名数据迁移】参数校验失败");
            return BaseResult.error(check);
        }

        // 加锁，防止频繁报名
        final RLock lock = redissonClient.getLock(String.format(RedisConstants.MIGRATION_LOCK, dto.getUserId()));
        try {
            if (!lock.tryLock(RedisConstants.WAIT_TIME_1, RedisConstants.LEASE_TIME_5, TimeUnit.SECONDS)) {
                log.info("【报名数据迁移】用户{}正在报名，请稍后再试", dto.getUserId());
                return BaseResult.error(ResultCodeEnum.REQUEST_TOO_FREQUENTLY);
            }

            // 保存数据库
            algorithmAiMigrationService.add(dto);
        } catch (Exception e) {
            log.error("【报名数据迁移】新增获取锁异常：{}", e.getMessage(), e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        return BaseResult.success();
    }

    /**
     * 数据迁移状态
     *
     * @param dto 入参
     * @return 状态
     */
    @PostMapping("/migration/status/get")
    public BaseResult<?> get(BaseDTO dto) {
        // 参数校验
        final AbstractResultCode check = dto.checkUserId();
        if (check != null) {
            log.info("【报名数据迁移】报名参数校验失败");
            return BaseResult.error(check);
        }

        // 查询状态
        Map<String,Integer> result = new HashMap<>(Const.NUM_16);
        result.put("status", algorithmAiMigrationService.getStatus(dto.getUserId()));
        return BaseResult.success(result);
    }


}
