package com.zyhl.yun.api.outer.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.anno.SSEApplicationAnnotation;
import com.zyhl.yun.api.outer.application.dto.SecondStreamChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.SecondStreamChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.service.chat.SecondChatAddService;
import com.zyhl.yun.api.outer.application.util.SseEmitterDataUtils;
import com.zyhl.yun.api.outer.config.SecondStreamChatAddProperties;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.exception.SseApplicationException;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 二次对话控制器
 *
 * <AUTHOR>
 * @date 2024/12/16 11:14
 */
@Slf4j
@RestController
@RequestMapping(headers = {ReqHeadConst.CLIENT_INFO, ReqHeadConst.APP_CHANNEL})
public class SecondChatAddController {

    @Resource
    private SecondStreamChatAddProperties config;

    @Resource
    private SecondChatAddService secondChatAddService;

    /**
     * 用户二次对话流式接口
     *
     * @param dto 二次对话流式接口入参DTO
     * @return SseEmitter对象
     */
    @SSEApplicationAnnotation
    @PostMapping(value = "/assistant/chat/secondStream", headers = ReqHeadConst.API_VERSION_V1, produces = {MediaType.TEXT_EVENT_STREAM_VALUE})
    public SseEmitter secondStreamChatAdd(@RequestBody @Valid SecondStreamChatAddDTO dto) {
        return sseHandle(dto);
    }

    /**
     * 二次对话流式处理
     *
     * @param dto 二次对话流式接口入参DTO
     * @return SseEmitter对象
     */
    private SseEmitter sseHandle(SecondStreamChatAddDTO dto) {
        // SseEmitter配置
        SseEmitter sseEmitter = new SseEmitter(config.getTimeout());
        AtomicBoolean completeFlag = new AtomicBoolean();

        // 鉴权异常信息
        SseApplicationException sseException = RequestContextHolder.getSseException();
        if (null != sseException) {
            SseEmitterDataUtils.sendMsgAndCompleteOthers(sseEmitter, BaseResult.error(sseException.getCode(), sseException.getMessage()), completeFlag);
            return sseEmitter;
        }

        // 初始化二次流式对话接口内部数据传输对象
        SecondStreamChatAddInnerDTO innerDTO = new SecondStreamChatAddInnerDTO(dto, sseEmitter);
        long startTime = System.currentTimeMillis();
        try {

            // 用户id校验
            dto.checkTokenUserId();

            // 二次流式对话接口
            secondChatAddService.secondStreamChatAdd(innerDTO);

        } catch (YunAiBusinessException e) {
            if (!CharSequenceUtil.isEmpty(e.getCode())) {
                SseEmitterDataUtils.sendMsgAndComplete(sseEmitter, BaseResult.error(e.getCode(), e.getMessage()), completeFlag);
            } else {
                SseEmitterDataUtils.sendMsgAndComplete(sseEmitter, BaseResult.error(ResultCodeEnum.UNKNOWN_ERROR), completeFlag);
            }
            return sseEmitter;
        } catch (Exception e) {
            log.error("二次对话流式对话异常，异常信息：{}", e.getMessage(), e);
            SseEmitterDataUtils.sendMsgAndComplete(sseEmitter, BaseResult.error(ResultCodeEnum.UNKNOWN_ERROR), completeFlag);
            return sseEmitter;
        } finally {
            MDC.put(LogConstants.DURATION, String.valueOf(System.currentTimeMillis() - startTime));
            log.info("二次对话流式对话，耗时：{}ms", System.currentTimeMillis() - startTime);
            MDC.remove(LogConstants.DURATION);
        }

        return sseEmitter;
    }

}
