package com.zyhl.yun.api.outer.controller;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.enums.BaseResultCodeEnum;
import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.*;
import com.zyhl.yun.api.outer.application.service.knowledge.PersonalKnowledgeService;
import com.zyhl.yun.api.outer.application.service.knowledge.UserKnowledgeFileService;
import com.zyhl.yun.api.outer.application.vo.knowledge.PersonalKnowledgeResultVO;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.controller.validate.PersonalKnowledgeValid;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.zyhl.yun.api.outer.constants.Const.NUM_16;

/**
 * className:UserKnowledgeController
 * description: 个人知识库2.0控制器
 *
 * <AUTHOR>
 * @date 2025/04/14
 */
@Slf4j
@RestController
@RequestMapping(headers = {ReqHeadConst.CLIENT_INFO, ReqHeadConst.APP_CHANNEL})
public class UserKnowledgeController {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    PersonalKnowledgeService personalKnowledgeService;

    @Resource
    private PersonalKnowledgeValid valid;

    @Resource
    private PersonalKnowledgeValid personalKnowledgeValid;

    @Resource
    private UserKnowledgeFileService userKnowledgeFileService;

    /**
     * 不存在错误码
     */
    private static final String NOT_EXIST_CODE = "13000010";


    /**
     * 创建个人知识库
     * @param dto 入参
     * @return 返回
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/create", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<PersonalKnowledgeResultVO> add(@RequestBody @Validated PersonalKnowledgeAddReqDTO dto) {

        // 参数校验
        final AbstractResultCode check = personalKnowledgeValid.addValid(dto);
        if (check != null) {
            log.info("【创建个人知识库】参数校验失败");
            return BaseResult.error(check);
        }

        return BaseResult.success(new PersonalKnowledgeResultVO(personalKnowledgeService.add(dto)));
    }

    /**
     * 更新个人知识库
     * @param dto 入参
     * @return 返回
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/update", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<PersonalKnowledgeResultVO> update(@RequestBody PersonalKnowledgeUpdateReqDTO dto) {

        // 参数校验
        final AbstractResultCode check = personalKnowledgeValid.updateValid(dto);
        if (check != null) {
            log.info("【更新个人知识库】参数校验失败");
            return BaseResult.error(check);
        }

        return BaseResult.success(new PersonalKnowledgeResultVO(personalKnowledgeService.update(dto)));
    }

    /**
     * 删除个人知识库
     * @param dto {@link PersonalKnowledgeDeleteReqDTO}
     * @return 返回
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/delete", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> delete(@RequestBody @Validated PersonalKnowledgeDeleteReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = personalKnowledgeValid.deletelValid(dto);
        if (check != null) {
            log.info("【个人知识库文件删除】参数校验失败");
            return BaseResult.error(check);
        }

        // 加锁，防止频繁删除
        final RLock lock = redissonClient.getLock(
                String.format(RedisConstants.USER_KNOWLEDGE_FILE_DEL_LOCK, dto.getBaseId()));
        try {
            if (!lock.tryLock(RedisConstants.WAIT_TIME_1, RedisConstants.LEASE_TIME_30, TimeUnit.SECONDS)) {
                log.info("【个人知识库文件删除】正在删除文件，请稍后再试，用户id：{}", dto.getUserId());
                return BaseResult.error(ResultCodeEnum.REQUEST_TOO_FREQUENTLY);
            }

            // 删除数据
            personalKnowledgeService.delete(dto);
            return BaseResult.success();
        } catch (Exception e) {
            if (e instanceof YunAiBusinessException) {
                YunAiBusinessException ex = (YunAiBusinessException) e;
                return BaseResult.error(ex.getExceptionEnum());
            }else{
                log.error("【个人知识库文件删除】删除文件获取锁异常：{}", e.getMessage(), e);
            }
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return BaseResult.success();
    }

    /**
     * 知识库状态统计接口
     * @param dto 入参
     * @return 返回
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/count", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> count(@RequestBody PersonalKnowledgeCountReqDTO dto) {
        // 参数校验
        AbstractResultCode check = valid.countValid(dto);
        if (Objects.nonNull(check)) {
            return BaseResult.error(check);
        }

        return BaseResult.success(personalKnowledgeService.count(dto));
    }

    @PostMapping(value = "/assistant/knowledge/personal/v2/base/batchGet", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> update(@RequestBody @Validated KnowledgeBatchGetReqDTO dto) {
        // 参数校验
        AbstractResultCode check = valid.batchGetValid(dto);
        if (Objects.nonNull(check)) {
            return BaseResult.error(check);
        }

        return BaseResult.success(personalKnowledgeService.getInfoListByIds(dto));
    }

    /**
     * 查询个人知识库头像列表
     * @param dto 入参
     * @return 返回
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/profilePhoto/list", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> profilePhotoList(@RequestBody BaseChannelDTO dto) {
        final AbstractResultCode check = personalKnowledgeValid.channelValid(dto);
        if (check != null) {
            log.info("【查询个人知识库头像列表】参数校验失败");
            return BaseResult.error(check);
        }

        return BaseResult.success(personalKnowledgeService.profilePhotoList(dto));
    }


    /**
     * 知识库列表
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/list", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> baseList(@RequestBody PersonalKnowledgeListReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = personalKnowledgeValid.listValid(dto);
        if (check != null) {
            log.info("【知识库列表】参数校验失败");
            return BaseResult.error(check);
        }

        // 查询数据
        return BaseResult.success(personalKnowledgeService.list(dto));
    }

    /**
     * 知识库文件下载地址
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/resource/getDownloadUrl", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> getDownloadUrl(@RequestBody KnowledgeFileUrlReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = personalKnowledgeValid.downloadUrlValid(dto);
        if (check != null) {
            log.info("【知识库文件下载地址】参数校验失败");
            return BaseResult.error(check);
        }

        // 下载地址
        String url = "";
        try {
            KnowledgeFileInfoReqDTO req = new KnowledgeFileInfoReqDTO();
            BeanUtils.copyProperties(dto, req);
            url = userKnowledgeFileService.getUrlAndCheckAuditStatus(req);
        } catch (Exception e) {
            log.error("【知识库文件下载地址】获取下载地址异常：{}", e.getMessage(), e);
            if (e instanceof YunAiBusinessException) {
                YunAiBusinessException ex = (YunAiBusinessException) e;
                if (NOT_EXIST_CODE.equals(ex.getCode())) {
                    return BaseResult.error(BaseResultCodeEnum.ERROR_NOT_FOUND);
                } else if (ex.getExceptionEnum() != null) {
                    return BaseResult.error(ex.getExceptionEnum());
                }
            }
            return BaseResult.error(BaseResultCodeEnum.UNKNOWN_ERROR);
        }

        // 响应结果
        Map<String, Object> result = new HashMap<>(NUM_16);
        result.put("url", url);

        return BaseResult.success(result);
    }

}