package com.zyhl.yun.api.outer.global;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.hcy.plugin.logger.enums.AccountTypeEnum;
import com.zyhl.hcy.plugin.neauth.handler.ThirdAuthHandler;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.wrapper.PostRequestWrapper;
import com.zyhl.yun.api.outer.anno.SSEApplicationAnnotation;
import com.zyhl.yun.api.outer.application.annotation.DisableAuthorizationParser;
import com.zyhl.yun.api.outer.application.config.HeaderConfig;
import com.zyhl.yun.api.outer.application.service.external.UserAuthService;
import com.zyhl.yun.api.outer.config.InterceptorPathConfig;
import com.zyhl.yun.api.outer.config.InterceptorWhiteConfig;
import com.zyhl.yun.api.outer.config.UserWhiteConfig;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.domain.dto.UserInfoDTO;
import com.zyhl.yun.api.outer.domainservice.QpsLimitService;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.exception.SseApplicationException;
import com.zyhl.yun.api.outer.external.client.resp.UserDomainRspDTO;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;
import com.zyhl.yun.api.outer.util.HttpUtils;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/11 下午6:30
 */
@Slf4j
public class AiOuterAuthInterceptor implements HandlerInterceptor {

    private static final String URL_KNOWLEDGE_PERSONAL_FILE_UPLOAD = "/assistant/knowledge/personal/file/upload";

	private final UserAuthService userAuthService;

    private static final String PARAM = "userId";

    private final InterceptorWhiteConfig interceptorWhiteConfig;

    @Value("${server.servlet.context-path}")
    private String contextPath;

    @Resource
    private ThirdAuthHandler thirdAuthHandler;

    @Resource
    private InterceptorPathConfig interceptorPathConfig;

    @Resource
    private UserWhiteConfig userWhiteConfig;

    @Resource
    private QpsLimitService qpsLimitService;

    @Resource
    private HeaderConfig headerConfig;
    @Resource
    private RedisOperateRepository redisOperateRepository;

    public AiOuterAuthInterceptor(UserAuthService userAuthService, InterceptorWhiteConfig interceptorWhiteConfig) {
        this.userAuthService = userAuthService;
        this.interceptorWhiteConfig = interceptorWhiteConfig;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        boolean isSse = false;
        String url = null;
        boolean success = false;
        RequestContextHolder.clear();
        RequestContextHolder.setHeaderParams(request);
        try {
            url = request.getRequestURI();

            // 重放拦截，暂时只做一个地址的拦截
            if ((contextPath + URL_KNOWLEDGE_PERSONAL_FILE_UPLOAD).equals(url)) {
                if (ObjectUtil.isEmpty(RequestContextHolder.getXYunTid())) {
                    log.error("请求头参数x-yun-tid为空");
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }
                if (!redisOperateRepository.saveXYunTid(url, RequestContextHolder.getXYunTid())) {
                    throw new YunAiBusinessException(ResultCodeEnum.REQUEST_TOO_FREQUENTLY);
                }
            }

            if (handler instanceof HandlerMethod) {
                HandlerMethod handlerMethod = (HandlerMethod) handler;
                // 根据注解做相应处理
                // 注解存在的逻辑
                if (handlerMethod.hasMethodAnnotation(SSEApplicationAnnotation.class)) {
                    isSse = true;
                }
				// 免鉴权注解
				Method method = handlerMethod.getMethod();
				DisableAuthorizationParser disableAuthorizationParser = method
						.getAnnotation(DisableAuthorizationParser.class);
				if (disableAuthorizationParser != null) {
					return true;
				}
            }
            success = normalPreHandle(request, isSse);
        } catch (Throwable e) {
            if (isSse) {
                if (e instanceof YunAiBusinessException) {
                    YunAiBusinessException yunAiBusinessException = (YunAiBusinessException) e;
                    RequestContextHolder.setSseException(new SseApplicationException(yunAiBusinessException.getCode(),
                            yunAiBusinessException.getMessage()));
                } else {
                    log.error(e.getMessage(), e);
                    RequestContextHolder.setSseException(
                            new SseApplicationException(ResultCodeEnum.ERROR_SERVER_INTERNAL));
                }
                // 拦截当成成功，但是会由流式接口进行输出异常信息
                success = true;
            } else {
                throw e;
            }
        } finally {
            log.info("url:{} | isSse:{} | preHandle success:{}", url, isSse, success);
        }
        return success;
    }

    private boolean normalPreHandle(HttpServletRequest request, boolean isSse) {
        try {
            printHeaderInfo(request);

            /** 接口开始，就把必要的请求头存储到线程里 */
            Map<String, String> headers = HttpUtils.getHeaders(false, null);
            if (MapUtil.isNotEmpty(headers)) {
                RequestContextHolder.setRequestHeaders(headers);
            }
            RequestContextHolder.setRequestId(String.valueOf(MDC.get(LogConstants.TRACE_ID)));

            String contentType = request.getContentType() == null ? "" : request.getContentType();
            boolean isPostRequestWrapper = (request instanceof PostRequestWrapper);
            if (!isPostRequestWrapper && !contentType.contains(MediaType.MULTIPART_FORM_DATA_VALUE)) {
                String jsonBody = null;
                if (!isSse) {
                    jsonBody = getJsonBody(request);
                }
                log.warn(
                        "请求类型不支持, headers:{}, contentType:{}, isPostRequestWrapper:{}, request method:{}, 普通请求 jsonBody:{}",
                        JSONUtil.toJsonStr(headers), contentType, isPostRequestWrapper, request.getMethod(), jsonBody);
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_NOT_AUTH);
            }
            String token = request.getHeader(ReqHeadConst.AUTHORIZATION);
            // 第三方鉴权
            if (StringUtils.isBlank(token)) {
                boolean result = false;
                try {
                    log.info("==> token为空，走第三方鉴权 enter");
                    result = doAuthThird(request);
                } finally {
                    Object userId = null;
                    if (!isSse) {
                        try {
                            String jsonBody = getJsonBody(request);
                            JSONObject body = JSONUtil.parseObj(jsonBody);
                            userId = body.get("userId");
                            //注意：bi采集需要的ACCOUNT_TYPE和ACCOUNT start
                            MDC.put(LogConstants.ACCOUNT_TYPE, AccountTypeEnum.ID);
                            MDC.put(LogConstants.ACCOUNT, userId);
                            //注意：bi采集需要的ACCOUNT_TYPE和ACCOUNT end
                            UserInfoDTO userInfoDTO = userAuthService.validateUserId((String) userId);
                            if (Objects.nonNull(userInfoDTO)) {
                                RequestContextHolder.setUserInfo(userInfoDTO.getUserId(), userInfoDTO.getPhoneNumber(), userInfoDTO.getBelongsPlatform());
                            }
                        } catch (Exception e) {
                            log.info("==> token为空，走第三方鉴权 error:", e);
                        }
                    }
                    log.info("==> token为空，走第三方鉴权 userId:{}, result:{}", userId, result);
                }
                return result;
            }
            // Token鉴权
            UserDomainRspDTO userDomainRspDTO = userAuthService.validateTokenOnly(token);
            if (Objects.isNull(userDomainRspDTO) || Objects.isNull(userDomainRspDTO.getUserDomainId())) {
                log.error("用户鉴权失败");
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_NOT_AUTH);
            }
            // 鉴权通过之后限流
            qpsLimitService.qpsLimit(request.getRequestURI());
            // 将userId存于线程中
            RequestContextHolder.setUserInfo(userDomainRspDTO.getUserDomainId(), userDomainRspDTO.getPhoneNumber(),
                    userDomainRspDTO.getBelongsPlatform());

            // 将云盘token存于线程中
            RequestContextHolder.setToken(token);

            // 智能搜图接口 用户白名单校验
            String relativePath = request.getRequestURI().substring(contextPath.length());
            List<String> path = interceptorPathConfig.getInterceptionPath();
            // 判断是否需要拦截的路径
            if (CollUtil.isNotEmpty(path) && path.contains(relativePath)) {
                // 用户白名单校验
                List<String> userWhiteList = userWhiteConfig.getUserWhiteList();
                if (!userWhiteList.contains(String.valueOf(userDomainRspDTO.getUserDomainId()))) {
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_NON_TARGET_USERS);
                }
            }

            // 文件上传
            if (contentType.contains(MediaType.MULTIPART_FORM_DATA_VALUE)) {
                return true;
            }

            // 校验body中的userId是否与请求的一致
            String jsonBody = null;
            if (request instanceof PostRequestWrapper) {
                jsonBody = ((PostRequestWrapper) request).getBody();
            }
            // if jsonBody is null, then return true
            if (StringUtils.isBlank(jsonBody)) {
                return true;
            }
            JSONObject body = new JSONObject();
            try {
                body = JSONUtil.parseObj(jsonBody);
            } catch (Exception e) {
                log.error("json解析异常 error:", e);
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS.getResultCode(),
                        ResultCodeEnum.ERROR_PARAMS.getResultMsg() + "[json解析异常]");
            }
            String useridParam = body.getStr(PARAM);
            if (StringUtils.isBlank(useridParam) && userDomainRspDTO.getUserDomainId() != null) {
                return true;
            }
            if (!Objects.equals(String.valueOf(userDomainRspDTO.getUserDomainId()), useridParam)) {
                log.error("userId和用户域token解析出来的userId不一致");
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_USER_ID);
            }
            return true;
        } catch (YunAiBusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("用户鉴权异常", e);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_NOT_AUTH);
        }
    }

    /**
     * 从请求中获取body
     *
     * @param request
     * @return
     */
    private String getJsonBody(HttpServletRequest request) {
        try {
            if (request.getMethod().equalsIgnoreCase(HttpMethod.POST.name())) {
                PostRequestWrapper wrappedRequest = new PostRequestWrapper(request);
                return wrappedRequest.getBody();
            }
        } catch (Exception e) {
            return ("getJsonBody error:" + e.getMessage());
        }
        return null;
    }

    /**
     * 第三方鉴权
     */
    private boolean doAuthThird(HttpServletRequest request) {
        try {
            thirdAuthHandler.check(request);
            return true;
        } catch (Exception e) {
            log.error("第三方鉴权异常", e);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_NOT_AUTH);
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
                                Exception ex) {
        // 清理 ThreadLocal 数据
        RequestContextHolder.clear();
    }

    private void printHeaderInfo(HttpServletRequest httpServletRequest) {
        // 判断是否开启了请求头打印开关
        if (headerConfig != null && headerConfig.getEnableLog()) {
            List<String> logWhiteList = headerConfig.getLogWhiteList();
            if (CollUtil.isEmpty(logWhiteList)) {
                return;
            }
            HashMap<String, String> map = new HashMap<>(16);
            logWhiteList.forEach(headerKey -> {
                String headerValue = httpServletRequest.getHeader(headerKey);
                if (Objects.nonNull(headerValue)) {
                    map.put(headerKey, headerValue);
                }
            });
            log.info("请求地址：{}，请求头信息: {}", httpServletRequest.getServletPath(), map);
        }
    }
}
