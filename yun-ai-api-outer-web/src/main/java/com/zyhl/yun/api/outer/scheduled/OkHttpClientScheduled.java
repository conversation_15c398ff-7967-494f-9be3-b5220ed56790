package com.zyhl.yun.api.outer.scheduled;

import java.lang.invoke.ConstantCallSite;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.alibaba.dashscope.protocol.okhttp.OkHttpClientFactory;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.common.okhttp.CommonOkHttpProxy;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.config.CommonOkHttpProxyProperties;
import com.zyhl.yun.api.outer.ConfigConstant;
import com.zyhl.yun.api.outer.constants.Const;

import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import okhttp3.OkHttpClient;

/**
 * <AUTHOR>
 *
 *         2025年2月16日
 */

@Slf4j
@RefreshScope
@Component
public class OkHttpClientScheduled {

	/**
	 * 是否开启配置，热更新
	 */
	@Value("${scheduled.okHttpClient.enabled:false}")
	private Boolean enabled;

	@Resource
	private CommonOkHttpProxyProperties commonOkHttpProxyProperties;

	/**
	 * 每1分钟执行一次
	 */
	@Scheduled(cron = "0 0/1 * * * ?")
	public void execute() {
		long startTime = System.currentTimeMillis();
		try {
			log.info("OkHttpClientScheduled start...");
			final String tid = UuidUtils.generateUuid();
			final String serviceName = String.valueOf(ConfigConstant.SERVICE_NAME);
			LogCommonUtils.initLogMDC(tid, serviceName);
			if (Boolean.TRUE.equals(enabled)) {
				CommonOkHttpProxy commonOkHttpProxy = new CommonOkHttpProxy(commonOkHttpProxyProperties);
				OkHttpClient zyOkHttpClient = commonOkHttpProxy.getOkHttpClientSSE();
				if (null != commonOkHttpProxy && null != zyOkHttpClient) {
					printOkHttpClientMonitorInfo("自研zyOkHttpClientSSE", zyOkHttpClient);
				}
				OkHttpClient dashscopeOkHttpClient = OkHttpClientFactory.getOkHttpClient();
				if (null != dashscopeOkHttpClient) {
					printOkHttpClientMonitorInfo("阿里dashscopeOkHttpClientSSE", dashscopeOkHttpClient);
				}
			} else {
				log.info("OkHttpClientScheduled 不执行 printOkHttpClientMonitorInfo");
			}
		} catch (Exception e) {
			log.error("OkHttpClientScheduled printOkHttpClientMonitorInfo error:", e);
		} finally {
			log.info("OkHttpClientScheduled enabled:{}, end... total ms:{}", enabled,
					(System.currentTimeMillis() - startTime));
		}
	}

	/**
	 * 打印okhttpclient 监控数据
	 * 
	 * @param name
	 * @param okHttpClient
	 */
	private void printOkHttpClientMonitorInfo(String name, OkHttpClient okHttpClient) {
		ConnectionPool connectionPool = okHttpClient.connectionPool();
		// 获取连接池的内部状态
		if (null != connectionPool) {
			int activeconnectionCount = connectionPool.connectionCount();
			int idleConnectionCount = connectionPool.idleConnectionCount();
			log.info("OkHttpClientScheduled printOkHttpClientMonitorInfo 执行 name:{}, 活跃连接数:{}, 空闲连接数:{}", name,
					activeconnectionCount, idleConnectionCount);
		}
		Dispatcher dispatcher = okHttpClient.dispatcher();
		if (null != dispatcher) {
			log.info(
					"OkHttpClientScheduled printOkHttpClientMonitorInfo 执行 name:{}, dispatcher 最大请求数:{}, 最大主机请求数:{}, 运行数:{}, 队列数:{}",
					name, dispatcher.getMaxRequests(), dispatcher.getMaxRequestsPerHost(),
					dispatcher.runningCallsCount(), dispatcher.queuedCallsCount());
			Map<String, Integer> mapRunning = new HashMap<>(Const.NUM_16);
			if (null != dispatcher.runningCalls()) {
				dispatcher.runningCalls().forEach(call -> {
					String host = call.request().url().host();
					Integer count = mapRunning.get(host);
					if (null == count) {
						count = 0;
					}
					mapRunning.put(host, (count + 1));
				});
				for (String host : mapRunning.keySet()) {
					log.info(
							"OkHttpClientScheduled printOkHttpClientMonitorInfo 执行 name:{}, dispatcher 运行的 host:{}, 当前运行数:{}",
							name, host, mapRunning.get(host));
				}
			}

			Map<String, Integer> mapQueued = new HashMap<>(Const.NUM_16);
			if (null != dispatcher.queuedCalls()) {
				dispatcher.queuedCalls().forEach(call -> {
					String host = call.request().url().host();
					Integer count = mapRunning.get(host);
					if (null == count) {
						count = 0;
					}
					mapQueued.put(host, (count + 1));
				});
				for (String host : mapQueued.keySet()) {
					log.info(
							"OkHttpClientScheduled printOkHttpClientMonitorInfo 执行 name:{}, dispatcher 队列的 host:{}, 当前队列数:{}",
							name, host, mapQueued.get(host));
				}
			}
		}
	}
}
