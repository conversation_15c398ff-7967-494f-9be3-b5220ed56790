package com.zyhl.yun.api.outer.controller;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeInviteExitReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeInviteListReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.UserKnowledgeInviteService;
import com.zyhl.yun.api.outer.application.vo.knowledge.PersonalKnowledgeShareMemberVO;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.controller.validate.PersonalKnowledgeValid;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.vo.common.PageInfoVO;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * User Knowledge Invite Controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-04-16 23:45:38
 */
@Slf4j
@RestController
@RequestMapping(headers = {ReqHeadConst.CLIENT_INFO, ReqHeadConst.APP_CHANNEL})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class UserKnowledgeInviteController {

    private final UserKnowledgeInviteService userKnowledgeInviteService;

    private final PersonalKnowledgeValid valid;

    @Value("${yun.api.outer.knowledge-invite.list.minPageSize:10}")
    private int minPageSize;

    /**
     * 查询个人知识库分享成员列表接口
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/share/list", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<PageInfoVO<PersonalKnowledgeShareMemberVO>> list(@RequestBody @Valid KnowledgeInviteListReqDTO dto) {
        AbstractResultCode check = valid.channelValid(dto);
        // 普通参数校验
        if (check != null) {
            log.warn("【查询个人知识库分享成员列表】【入参校验】【参数异常】");
            return BaseResult.error(check);
        }
        // 校验分页参数
        PageInfoDTO page = PageInfoDTO.getReqDTO(dto.getPageInfo());
        page.validate(minPageSize);
        if (page.getPageSize() > 100) {
            log.warn("【列举询个人知识库分享成员列表】【入参校验】【分页数量超最大限制】");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS.getResultCode(), ("分页数量最大" + 100));
        }
        dto.setPageInfo(page);
        return BaseResult.success(userKnowledgeInviteService.list(dto));
    }


    /**
     * 个人知识库分享退出接口
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/share/exit", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<Void> exit(@RequestBody @Valid KnowledgeInviteExitReqDTO dto) {
        AbstractResultCode check = valid.channelValid(dto);
        // 参数校验
        if (check != null) {
            log.warn("【个人知识库分享退出接口】【入参校验】【参数异常】");
            return BaseResult.error(check);
        }
        userKnowledgeInviteService.exit(dto);
        return BaseResult.success();
    }
}
