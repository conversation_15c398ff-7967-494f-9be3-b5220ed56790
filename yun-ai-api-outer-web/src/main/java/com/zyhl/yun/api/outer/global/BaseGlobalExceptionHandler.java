package com.zyhl.yun.api.outer.global;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.commons.enums.CommonResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.enums.BaseResultCodeEnum;
import com.zyhl.yun.api.outer.application.base.BaseExceptionResultVO;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.exception.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.validation.ConstraintViolationException;
import javax.validation.ValidationException;

/**
 * Copyright © 2022 ChinaMobile Info. Tech Ltd. All rights reserved.
 * <p>
 * TODO (用一句话描述该文件做什么)
 *
 * @author: <a href="<EMAIL>">ZhiFeng.Wu</a>
 * @date: 2022/7/13 10:08
 */
@Slf4j
@RestControllerAdvice
public class BaseGlobalExceptionHandler {

    /**
     * 处理 Post 请求参数格式错误： @RequestBody 注解的请求参数校验失败后抛出的异常是 MethodArgumentNotValidException
     *
     * @param e MethodArgumentNotValidException
     * @return BaseResult
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public BaseResult<BaseExceptionResultVO> validExceptionHandler(MethodArgumentNotValidException e) {
        log.error(e.getMessage(), e);
        String errorMsg = CommonResultCode.ERROR_PARAMS.getResultMsg();
        FieldError filedError;
        if ((filedError = e.getBindingResult().getFieldError()) != null) {
            errorMsg = filedError.getDefaultMessage();
        }
        return BaseResult.error(CommonResultCode.ERROR_PARAMS.getResultCode(), errorMsg, null);
    }

    /**
     * 处理 Post 上传文件超大： @RequestBody 注解的请求参数校验失败后抛出的异常是 MaxUploadSizeExceededException
     *
     * @param e MaxUploadSizeExceededException
     * @return BaseResult
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public BaseResult<BaseExceptionResultVO> validExceptionHandler(MaxUploadSizeExceededException e) {
        log.error(e.getMessage(), e);
        String errorMsg = ResultCodeEnum.FILE_SIZE_LARGE.getResultMsg();
        return BaseResult.error(ResultCodeEnum.FILE_SIZE_LARGE.getResultCode(), errorMsg, null);
    }

    /**
     * 处理 Get 请求参数格式错误： @PathVariable 或 @RequestParam 注解的请求参数校验失败后抛出的异常是 ConstraintViolationException
     *
     * @param e ConstraintViolationException
     * @return BaseResult
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public BaseResult<BaseExceptionResultVO> validExceptionHandler1(ConstraintViolationException e) {
        String message = e.getMessage();
        int index = message.indexOf(":");
        if (index > 0) {
            message = message.substring(index + 2);
            log.error(message, e);
            return BaseResult.error(CommonResultCode.ERROR_PARAMS.getResultCode(), message, null);
        }
        log.error(message, e);
        return BaseResult.error(CommonResultCode.ERROR_PARAMS, null);
    }

    /**
     * Http请求的媒体格式相关异常
     *
     * @param e HttpMediaTypeNotSupportedException
     * @return BaseResult
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public BaseResult<BaseExceptionResultVO> handleHttpMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException e) {
        log.error(e.getMessage(), e);
        return BaseResult.error(CommonResultCode.ERROR_MIME_NOT_SUPPORT, null);
    }

    /**
     * Http请求相关异常
     *
     * @param e handleHttpMessageNotReadableException
     * @return BaseResult
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public BaseResult<BaseExceptionResultVO> handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        log.error(e.getMessage(), e);
        return BaseResult.error(CommonResultCode.ERROR_PARAMS, null);
    }

    @ExceptionHandler(NoHandlerFoundException.class)
    public BaseResult<BaseExceptionResultVO> handleNoHandlerFoundException(NoHandlerFoundException e) {
        log.error(e.getMessage(), e);
        return BaseResult.error(CommonResultCode.ERROR_NOT_FOUND, null);
    }

    /**
     * 其余异常
     *
     * @param e Exception
     * @return BaseResult
     */
    @ExceptionHandler(RuntimeException.class)
    public BaseResult<BaseExceptionResultVO> handleException(RuntimeException e) {
        log.error(e.getMessage(), e);
        return BaseResult.error(CommonResultCode.ERROR_SERVER_INTERNAL);
    }

    /**
     * 业务异常
     */
    @ExceptionHandler(YunAiBusinessException.class)
    public BaseResult<Void> diyError(YunAiBusinessException e) {
        String resultMsg = CharSequenceUtil.isNotBlank(e.getMessage()) ? e.getMessage() : e.getExceptionEnum().getResultMsg();
        String resultCode = CharSequenceUtil.isNotBlank(e.getCode()) ? e.getCode() : e.getExceptionEnum().getResultCode();
        return BaseResult.error(resultCode, resultMsg);
    }

    /**
     * validation校验异常
     */
    @ExceptionHandler(ValidationException.class)
    public BaseResult<BaseExceptionResultVO> handleValidationException(ValidationException e) {
        log.warn(e.getMessage(), e);
        if (e.getCause() instanceof BaseException) {
            return BaseResult.error((((BaseException) e.getCause()).getCode()),
                    e.getCause().getMessage());
        } else {
            return BaseResult.error(BaseResultCodeEnum.ERROR_SERVER_INTERNAL);
        }

    }

}