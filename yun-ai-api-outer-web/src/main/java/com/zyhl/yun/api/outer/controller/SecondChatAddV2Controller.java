package com.zyhl.yun.api.outer.controller;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.anno.SSEApplicationAnnotation;
import com.zyhl.yun.api.outer.application.chatv2.dto.SecondStreamChatAddV2InnerDTO;
import com.zyhl.yun.api.outer.application.chatv2.service.SecondChatAddV2Service;
import com.zyhl.yun.api.outer.application.dto.SecondStreamChatAddV2DTO;
import com.zyhl.yun.api.outer.config.SecondStreamChatAddProperties;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.domainservice.SseEmitterOperate;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.SseNameEnum;
import com.zyhl.yun.api.outer.exception.SseApplicationException;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/6/3 19:32
 */
@Slf4j
@RestController
@RequestMapping(headers = {ReqHeadConst.CLIENT_INFO, ReqHeadConst.APP_CHANNEL})
public class SecondChatAddV2Controller {

    @Resource
    private SecondStreamChatAddProperties config;

    @Resource
    private SecondChatAddV2Service secondChatAddV2Service;

    /**
     * 用户二次对话流式接口
     *
     * @param dto 二次对话流式接口入参DTO
     * @return SseEmitter对象
     */
    @SSEApplicationAnnotation
    @PostMapping(value = "/assistant/chat/v2/secondStream", headers = ReqHeadConst.API_VERSION_V1, produces = {MediaType.TEXT_EVENT_STREAM_VALUE})
    public SseEmitter secondStreamChatAdd(@RequestBody @Valid SecondStreamChatAddV2DTO dto) {
        SseEmitterOperate sseEmitterOperate = new SseEmitterOperate();
        // 鉴权异常信息
        SseApplicationException sseException = RequestContextHolder.getSseException();
        if (null != sseException) {
            sseEmitterOperate.sendAndComplete(BaseResult.error(sseException.getCode(), sseException.getMessage()));
            return sseEmitterOperate.getSseEmitter();
        }
        try {
        	sseEmitterOperate.setSseName(SseNameEnum.SECOND_SSE_NAME.getCode());
            dto.checkTokenUserId();
            SecondStreamChatAddV2InnerDTO innerDto = new SecondStreamChatAddV2InnerDTO(dto, sseEmitterOperate);
        	secondChatAddV2Service.secondStreamChatAdd(innerDto);
        } catch (Exception e) {
        	log.error("v2 secondStreamChatAdd error:", e);
            if (e instanceof YunAiBusinessException) {
                YunAiBusinessException yunAiBusinessException = (YunAiBusinessException) e;
                if (ObjectUtil.isNotEmpty(yunAiBusinessException.getCode())) {
                    sseEmitterOperate.sendAndComplete(BaseResult.error(yunAiBusinessException.getCode(),
                            yunAiBusinessException.getMessage()));
                } else {
                    sseEmitterOperate.sendAndComplete(BaseResult.error(ResultCodeEnum.UNKNOWN_ERROR));
                }
                return sseEmitterOperate.getSseEmitter();
            }
            sseEmitterOperate.sendAndComplete(BaseResult.error(ResultCodeEnum.UNKNOWN_ERROR));
            return sseEmitterOperate.getSseEmitter();
        }
        return sseEmitterOperate.getSseEmitter();
    }

}
