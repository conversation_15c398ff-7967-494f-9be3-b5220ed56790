package com.zyhl.yun.api.outer.controller.validate;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.logger.util.LogUtil;
import com.zyhl.hcy.yun.ai.common.base.enums.RenameModeEnum;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.dto.CheckTextReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.*;
import com.zyhl.yun.api.outer.application.service.AlgorithmAiRegisterService;
import com.zyhl.yun.api.outer.config.KnowledgePersonalProperties;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.constants.RegConst;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeLabelEntity;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeLabelEnum;
import com.zyhl.yun.api.outer.external.CheckSystemExternalService;
import com.zyhl.yun.api.outer.external.ose.enums.UserBelongsPlatformEnum;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeLabelRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 文件入参校验类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UserKnowledgeFileValid extends SourceChannelValid {

    @Resource
    private UserKnowledgeLabelRepository userKnowledgeLabelRepository;
    @Resource
    private AlgorithmAiRegisterService algorithmAiRegisterService;
    @Resource
    private KnowledgePersonalProperties knowledgePersonalProperties;
    @Resource
    private UserKnowledgeFileRepository userKnowledgeFileRepository;
    @Resource
    private CheckSystemExternalService checkSystemExternalService;

    /**
     * 一次最多获取数量
     */
    private final static Integer GET_FILE_MAX_SIZE = 100;


    private final static Integer GET_FILE_MIN_SIZE = 10;

    private static final String ROOT_PATH = "/";

    /**
     * 新增知识库文件参数校验
     */
    public AbstractResultCode addValid(KnowledgeFileAddReqDTO dto) {
        // 渠道和用户校验
        AbstractResultCode check = channelValid(dto);
        if (check != null) {
            return check;
        } else if (UserBelongsPlatformEnum.OSE.getBelongsPlatform().equals(RequestContextHolder.getBelongsPlatform())) {
            log.info("个人知识库暂不支持老底座用户");
            return ResultCodeEnum.NOT_TARGET_USER;
        }


        // 判断是否报名
        if (!algorithmAiRegisterService.checkAiAssistant(dto.getUserId())) {
            log.info("用户未报名");
            return ResultCodeEnum.ERROR_FORBIDDEN;
        }

        // 标签id校验
        if (ObjectUtil.isEmpty(dto.getLabelId()) || KnowledgeLabelEnum.isUnClassify(Long.valueOf(dto.getLabelId()))) {
            dto.setLabelId(String.valueOf(KnowledgeLabelEnum.UNCLASSIFIED.getId()));
        } else if (!dto.getLabelId().matches(RegConst.REG_ID_STR)) {
            log.info("标签id格式不对，labelId:{}", dto.getLabelId());
            return ResultCodeEnum.ERROR_PARAMS;
        } else {
            UserKnowledgeLabelEntity labelEntity = userKnowledgeLabelRepository.selectById(Long.valueOf(dto.getLabelId()));
            if (labelEntity == null || !labelEntity.getUserId().equals(dto.getUserId())) {
                log.info("标签id不存在，labelId:{}", dto.getLabelId());
                return ResultCodeEnum.ERROR_PARAMS;
            }
        }

        // 资源类型校验
        if (!KnowledgeResourceTypeEnum.isExist(dto.getResourceType())) {
            log.info("资源类型错误，resourceType：{}", dto.getResourceType());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        // 个人云文件
        if (KnowledgeResourceTypeEnum.isPersonalFile(dto.getResourceType())) {
            // 文件信息校验
            if (ObjectUtil.isEmpty(dto.getFileList())) {
                log.info("文件信息为空，fileList：{}", JsonUtil.toJson(dto.getFileList()));
                return ResultCodeEnum.ERROR_PARAMS;
            } else if (dto.getFileList().size() > knowledgePersonalProperties.getUploadNumLimit()) {
                log.info("文件数量超过{}个，fileList size：{}", knowledgePersonalProperties.getUploadNumLimit(), dto.getFileList().size());
                return ResultCodeEnum.ERROR_PARAMS;
            }
            Set<String> set = new HashSet<>();
            List<File> fileList = new ArrayList<>();
            for (File f : dto.getFileList()) {
                if (CharSequenceUtil.isEmpty(f.getFileId())) {
                    log.info("文件id为空，fileId：{}", f.getFileId());
                    return ResultCodeEnum.ERROR_PARAMS;
                } else if (CharSequenceUtil.isEmpty(f.getName())) {
                    log.info("文件名称为空，name：{}", f.getName());
                    return ResultCodeEnum.ERROR_PARAMS;
                } else if (CharSequenceUtil.isEmpty(f.getType())) {
                    log.info("文件类型为空，type：{}", f.getType());
                    return ResultCodeEnum.ERROR_PARAMS;
                } else if (CharSequenceUtil.isEmpty(f.getCategory())) {
                    log.info("文件分类为空，category：{}", f.getCategory());
                    return ResultCodeEnum.ERROR_PARAMS;
                } else if (f.getSize() == null) {
                    log.info("文件大小为空");
                    return ResultCodeEnum.ERROR_PARAMS;
                } else if (CharSequenceUtil.isEmpty(f.getFileExtension())) {
                    log.info("文件扩展名为空，fileExtension：{}", f.getFileExtension());
                    return ResultCodeEnum.ERROR_PARAMS;
                }

                // 去重
                if (!set.contains(f.getFileId())) {
                    set.add(f.getFileId());
                    fileList.add(f);
                }
            }

            dto.setFileList(fileList);
        } else if (KnowledgeResourceTypeEnum.isMail(dto.getResourceType()) || KnowledgeResourceTypeEnum.isNote(dto.getResourceType())) {
            // 文件信息校验
            if (ObjectUtil.isEmpty(dto.getFileList())) {
                log.info("资源信息为空，fileList：{}", JsonUtil.toJson(dto.getFileList()));
                return ResultCodeEnum.ERROR_PARAMS;
            } else if (dto.getFileList().size() > knowledgePersonalProperties.getUploadNumLimit()) {
                log.info("资源数量超过{}个，fileList size：{}", knowledgePersonalProperties.getUploadNumLimit(), dto.getFileList().size());
                return ResultCodeEnum.ERROR_PARAMS;
            }

            Set<String> set = new HashSet<>();
            List<File> fileList = new ArrayList<>();
            for (File f : dto.getFileList()) {
                if (CharSequenceUtil.isEmpty(f.getFileId())) {
                    log.info("资源id为空，fileId：{}", f.getFileId());
                    return ResultCodeEnum.ERROR_PARAMS;
                }

                // 去重
                if (!set.contains(f.getFileId())) {
                    set.add(f.getFileId());
                    fileList.add(f);
                }
            }

            dto.setFileList(fileList);
        } else if (KnowledgeResourceTypeEnum.isHtml(dto.getResourceType())) {
            // 文件信息校验
            if (ObjectUtil.isEmpty(dto.getHtmlList())) {
                log.info("html信息为空，fileList：{}", JsonUtil.toJson(dto.getHtmlList()));
                return ResultCodeEnum.ERROR_PARAMS;
            } else if (dto.getHtmlList().size() > knowledgePersonalProperties.getUploadNumLimit()) {
                log.info("html数量超过{}个，fileList size：{}", knowledgePersonalProperties.getUploadNumLimit(), dto.getHtmlList().size());
                return ResultCodeEnum.ERROR_PARAMS;
            }

            Set<String> set = new HashSet<>();
            List<UserKnowledgeFileEntity.HtmlInfo> htmlList = new ArrayList<>();
            for (UserKnowledgeFileEntity.HtmlInfo html : dto.getHtmlList()) {
                if (ObjectUtil.isEmpty(html.getIndex())) {
                    log.info("html编号为空，name：{}", html.getIndex());
                    return ResultCodeEnum.ERROR_PARAMS;
                } else if (ObjectUtil.isEmpty(html.getTitle())) {
                    log.info("html主题为空，url：{}", html.getTitle());
                    return ResultCodeEnum.ERROR_PARAMS;
                } else if (ObjectUtil.isEmpty(html.getUrl())) {
                    log.info("html地址为空，url：{}", html.getUrl());
                    return ResultCodeEnum.ERROR_PARAMS;
                } else if (!html.getUrl().startsWith("http")) {
                    log.info("html格式不正确，必须是以http开头的地址，url：{}", html.getUrl());
                    return ResultCodeEnum.ERROR_PARAMS;
                }

                // 去重
                if (!set.contains(html.getUrl())) {
                    set.add(html.getUrl());
                    htmlList.add(html);
                }
            }

            dto.setHtmlList(htmlList);
        }

        return null;
    }

    /**
     * 知识库文件任务结果参数校验
     */
    public AbstractResultCode taskValid(KnowledgeFileTaskResultReqDTO dto) {

        if (ObjectUtil.isEmpty(dto.getTaskId()) || !dto.getTaskId().matches(RegConst.REG_DATA_STR)) {
            log.info("任务id为空，taskId:{}", dto.getTaskId());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        return channelValid(dto);
    }


    /**
     * 知识库文件删除参数校验
     */
    public AbstractResultCode deleteValid(KnowledgeFileDeleteReqDTO dto) {

        if (ObjectUtil.isEmpty(dto.getFileList())) {
            log.info("文件信息为空，fileList：{}", JsonUtil.toJson(dto.getFileList()));
            return ResultCodeEnum.ERROR_PARAMS;
        }
        if (dto.getFileList().size() > knowledgePersonalProperties.getMaxFileDeleteSize()) {
            log.info("删除知识库文件数量超过阈值，最多支持{}个资源，当前数量:{}", knowledgePersonalProperties.getMaxFileDeleteSize(), dto.getFileList().size());
            String msg = String.format("删除知识库文件数量超过阈值，最多支持%d个文件", knowledgePersonalProperties.getMaxFileDeleteSize());
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_DELETE_FILE_NUM_LIMIT.getResultCode(), msg);
        }

        // 文件信息校验
        for (File f : dto.getFileList()) {
            if (CharSequenceUtil.isEmpty(f.getFileId())) {
                log.info("文件id为空，fileId：{}", f.getFileId());
                return ResultCodeEnum.ERROR_PARAMS;
            }
        }

        return channelValid(dto);
    }


    /**
     * 知识库文件列表参数校验
     *
     * @return 枚举
     */
    public AbstractResultCode listValid(KnowledgeFileListReqDTO dto) {

        if (CharSequenceUtil.isEmpty(dto.getLabelId())) {
            dto.setLabelId(String.valueOf(KnowledgeLabelEnum.ALL.getId()));
        } else if (!String.valueOf(KnowledgeLabelEnum.ALL.getId()).equals(dto.getLabelId()) && !dto.getLabelId().matches(RegConst.REG_DATA_STR)) {
            log.info("标签id格式不正确，labelId:{}", dto.getLabelId());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        // 分页信息校验
        if (dto.getPageInfo() == null) {
            dto.setPageInfo(PageInfoDTO.builder().pageCursor("0").build());
        }
        if (CharSequenceUtil.isEmpty(dto.getPageInfo().getPageCursor())) {
            dto.getPageInfo().setPageCursor("0");
        }
        if (!dto.getPageInfo().getPageCursor().matches(RegConst.REG_DATA_STR)) {
            log.info("分页游标格式的不正确，pageCursor：{}", dto.getPageInfo().getPageCursor());
            return ResultCodeEnum.ERROR_PARAMS;
        }
        if (dto.getPageInfo().getPageSize() == null || dto.getPageInfo().getPageSize() < 0) {
            dto.getPageInfo().setPageSize(10);
        }

        return channelValid(dto);
    }

    /**
     * 知识库文件列表参数校验
     *
     * @return 枚举
     */
    public AbstractResultCode listValid180(KnowledgeFileListReqDTO dto) {

        // 分页信息校验
        if (dto.getPageInfo() == null) {
            dto.setPageInfo(PageInfoDTO.builder().pageCursor("0").build());
        }
        if (CharSequenceUtil.isEmpty(dto.getPageInfo().getPageCursor())) {
            dto.getPageInfo().setPageCursor("0");
        }
        if (!dto.getPageInfo().getPageCursor().matches(RegConst.REG_DATA_STR)) {
            log.info("分页游标格式的不正确，pageCursor：{}", dto.getPageInfo().getPageCursor());
            return ResultCodeEnum.ERROR_PARAMS;
        }
        if (dto.getPageInfo().getPageSize() == null || dto.getPageInfo().getPageSize() < 0) {
            dto.getPageInfo().setPageSize(10);
        }

        if (Objects.nonNull(dto.getStatus())) {
            if (dto.getStatus() < -1 || dto.getStatus() > 1) {
                log.info("status格式不正确，status：{}", dto.getStatus());
                return ResultCodeEnum.ERROR_PARAMS;
            }
        }
        if (Objects.isNull(dto.getOrderBy())) {
            log.info("orderBy为空");
            return ResultCodeEnum.ERROR_PARAMS;
        } else if (dto.getOrderBy() < 1 || dto.getOrderBy() > 2) {
            log.info("orderBy格式不正确，orderBy：{}", dto.getOrderBy());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        return channelValid(dto);
    }

    /**
     * 知识库文件信息参数校验
     *
     * @return 枚举
     */
    public AbstractResultCode infoValid(KnowledgeFileInfoReqDTO dto) {

        if (CharSequenceUtil.isEmpty(dto.getFileId())) {
            log.info("文件id为空，fileId:{}", dto.getFileId());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        return channelValid(dto);
    }

    /**
     * 知识库文件下载地址参数校验
     *
     * @return 枚举
     */
    public AbstractResultCode downloadUrlValid(KnowledgeFileUrlReqDTO dto) {

        if (CharSequenceUtil.isEmpty(dto.getFileId())) {
            log.info("文件id为空，fileId:{}", dto.getFileId());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        return channelValid(dto);
    }

    /**
     * 校验批量删除参数
     *
     * @param dto 请求参数
     */
    public AbstractResultCode validBatchDelete(KnowledgeFileTaskBatchDeleteReqDTO dto) {
        // 渠道和用户校验
        AbstractResultCode check = channelValid(dto);
        if (check != null) {
            return check;
        }
        if (CollUtil.isEmpty(dto.getTaskIdList())) {
            return ResultCodeEnum.ERROR_PARAMS;
        }
        return null;
    }

    /**
     * 批量导入知识库文件参数校验
     *
     * @param dto 请求参数
     * @return 校验结果
     */
    public AbstractResultCode batchImportValid(KnowledgeFileBatchImportReqDTO dto) {
        // 渠道和用户校验
        AbstractResultCode check = channelValid(dto);
        if (check != null) {
            return check;
        }
        // 判断是否报名
        if (!algorithmAiRegisterService.checkAiAssistant(dto.getUserId())) {
            log.info("用户未报名,用户为：{}", dto.getUserId());
            return ResultCodeEnum.ERROR_FORBIDDEN;
        }

        if (ObjectUtil.isNotEmpty(dto.getBaseId())) {
            if (!dto.getBaseId().matches(RegConst.REG_ID_STR)) {
                log.info("知识库id格式不对，baseId：{}", dto.getBaseId());
                return ResultCodeEnum.ERROR_PARAMS;
            }
        }

        // 校验资源总数不超过50个
        int totalResources = 0;
        if (KnowledgeResourceTypeEnum.isPersonalFile(dto.getResourceType())) {
            totalResources = ObjectUtil.isEmpty(dto.getFileList()) ? 0 : dto.getFileList().size();
        } else if (KnowledgeResourceTypeEnum.isMail(dto.getResourceType())) {
            totalResources = ObjectUtil.isEmpty(dto.getMailList()) ? 0 : dto.getMailList().size();
        } else if (KnowledgeResourceTypeEnum.isNote(dto.getResourceType()) || KnowledgeResourceTypeEnum.isNoteSync(dto.getResourceType())) {
            totalResources = ObjectUtil.isEmpty(dto.getNoteList()) ? 0 : dto.getNoteList().size();
        } else if ((KnowledgeResourceTypeEnum.isHtml(dto.getResourceType()))) {
            totalResources = ObjectUtil.isEmpty(dto.getHtmlInfo()) ? 0 : 1;
        }

        if (totalResources == 0) {
            log.info("没有要导入的资源");
            return ResultCodeEnum.ERROR_PARAMS;
        }
        if (totalResources > knowledgePersonalProperties.getUploadNumLimit()) {
            log.info("导入资源数量超过限制，最多支持{}个资源，当前数量:{}", knowledgePersonalProperties.getUploadNumLimit(), totalResources);
            return ResultCodeEnum.ERROR_PARAMS;
        }

        // 父目录id校验
        if (ObjectUtil.isNotEmpty(dto.getParentFileId())) {
            UserKnowledgeFileEntity fileEntity = userKnowledgeFileRepository.selectByFileId(dto.getUserId(), dto.getParentFileId());
            if (Objects.isNull(fileEntity)) {
                log.info("父目录id不存在：{}", dto.getParentFileId());
                return ResultCodeEnum.KNOWLEDGE_FILE_PARENT_NOT_EXIST;
            }
        }

        return null;
    }

    public AbstractResultCode taskListValid(KnowledgeFileTaskListReqDTO dto) {
        // 分页信息校验
        if (ObjectUtil.isEmpty(dto.getPageInfo())) {
            dto.setPageInfo(PageInfoDTO.builder().pageCursor("0").build());
        }
        if (ObjectUtil.isEmpty(dto.getPageInfo().getPageCursor())) {
            dto.getPageInfo().setPageCursor("0");
        }
        if (!dto.getPageInfo().getPageCursor().matches(RegConst.REG_DATA_STR)) {
            log.info("分页游标格式的不正确，pageCursor：{}", dto.getPageInfo().getPageCursor());
            return ResultCodeEnum.ERROR_PARAMS;
        }
        if (ObjectUtil.isEmpty(dto.getPageInfo().getPageSize()) || dto.getPageInfo().getPageSize() < GET_FILE_MIN_SIZE) {
            dto.getPageInfo().setPageSize(GET_FILE_MIN_SIZE);
        } else if (dto.getPageInfo().getPageSize() > GET_FILE_MAX_SIZE) {
            dto.getPageInfo().setPageSize(GET_FILE_MAX_SIZE);
        }
        // 渠道和用户校验
        return channelValid(dto);
    }

    public AbstractResultCode taskRetryValid(KnowledgeFileTaskRetryReqDTO dto) {
        if (ObjectUtil.isEmpty(dto.getTaskId())) {
            return ResultCodeEnum.ERROR_PARAMS;
        }
        // 渠道和用户校验
        return channelValid(dto);
    }

    public AbstractResultCode batchGetValid(KnowledgeFileListBatchReqDTO dto) {
        if (ObjectUtil.isEmpty(dto.getResourceIdList())) {
            log.info("资源id为空，resourceIdList：{}", JsonUtil.toJson(dto.getResourceIdList()));
            return ResultCodeEnum.ERROR_PARAMS;
        }
        if (dto.getResourceIdList().size() > GET_FILE_MAX_SIZE) {
            log.info("资源id数量超过限制，最多支持{}个资源，当前数量:{}", GET_FILE_MAX_SIZE, dto.getResourceIdList().size());
            return ResultCodeEnum.ERROR_PARAMS;
        }
        return channelValid(dto);
    }

    public AbstractResultCode batchDelete(KnowledgeFileListBatchReqDTO dto) {
        if (ObjectUtil.isEmpty(dto.getResourceIdList())) {
            log.info("资源id为空，resourceIdList：{}", JsonUtil.toJson(dto.getResourceIdList()));
            return ResultCodeEnum.ERROR_PARAMS;
        }
        if (dto.getResourceIdList().size() > knowledgePersonalProperties.getMaxFileDeleteSize()) {
            log.info("删除知识库文件数量超过阈值，最多支持{}个资源，当前数量:{}", knowledgePersonalProperties.getMaxFileDeleteSize(), dto.getResourceIdList().size());
            String msg = String.format("删除知识库文件数量超过阈值，最多支持%d个文件", knowledgePersonalProperties.getMaxFileDeleteSize());
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_DELETE_FILE_NUM_LIMIT.getResultCode(), msg);
        }
        return channelValid(dto);
    }

    /**
     * 创建文件夹参数校验
     *
     * @param dto 请求参数
     * @return 校验结果
     */
    public AbstractResultCode createFolderValid(KnowledgeCreateFolderReqDTO dto) {
        // 渠道和用户校验
        AbstractResultCode check = channelValid(dto);
        if (check != null) {
            return check;
        }

        if (ObjectUtil.isEmpty(dto.getBaseId()) || !dto.getBaseId().matches(RegConst.REG_ID_STR)) {
            log.info("知识库id为空或者格式不正确，baseId：{}", dto.getBaseId());
            return ResultCodeEnum.ERROR_PARAMS;
        }
        if (ObjectUtil.isEmpty(dto.getName()) || dto.getName().length() > knowledgePersonalProperties.getFileNameLength()) {
            log.info("文件夹名称不能为空或者过长，name：{}，名字长度：{}，限制长度：{}", dto.getName(), dto.getName().length(), knowledgePersonalProperties.getFileNameLength());
            return ResultCodeEnum.KNOWLEDGE_FILE_NAME_EMPTY;
        }
        if (ObjectUtil.isEmpty(dto.getParentFileId())) {
            log.info("父目录id为空，parentFileId：{}", dto.getParentFileId());
            return ResultCodeEnum.ERROR_PARAMS;
        }
        if (ObjectUtil.isNotEmpty(dto.getRenameMode())) {
            if (!RenameModeEnum.isExist(dto.getRenameMode())) {
                log.info("重命名模式格式不正确，renameMode：{}", dto.getRenameMode());
                return ResultCodeEnum.ERROR_PARAMS;
            }
        }

        // 名称送审
        CheckTextReqDTO checkTextReqDTO = new CheckTextReqDTO();
        checkTextReqDTO.setTaskId(LogUtil.getTraceId());
        checkTextReqDTO.setAccount(dto.getUserId());
        checkTextReqDTO.setContent(dto.getName());
        checkSystemExternalService.checkText(knowledgePersonalProperties.getCheckContentSwitch(), checkTextReqDTO);

        return null;
    }

    public AbstractResultCode updateValid(KnowledgeFileUpdateReqDTO dto) {
        if (ObjectUtil.isNotEmpty(dto.getRenameMode())) {
            RenameModeEnum renameModeEnum = RenameModeEnum.getByCode(dto.getRenameMode());
            if (renameModeEnum == null) {
                log.info("重命名规则为空");
                return ResultCodeEnum.ERROR_PARAMS;
            }
        }

        if (ObjectUtil.isEmpty(dto.getResourceId())) {
            log.info("资源id为空,resourceId：{}", dto.getResourceId());
            return ResultCodeEnum.ERROR_PARAMS;
        }
        if (ObjectUtil.isEmpty(dto.getBaseId())) {
            log.info("知识库id为空,baseId:{}", dto.getBaseId());
            return ResultCodeEnum.KNOWLEDGE_ID_NOT_PROVIDE;
        }
        String name = FileUtil.getPrefix(dto.getName());
        if (ObjectUtil.isEmpty(name)) {
            log.info("知识库文件新名称为空,name:{}", dto.getBaseId());
            return ResultCodeEnum.KNOWLEDGE_FILE_NAME_EMPTY;
        }
        return channelValid(dto);
    }

    public AbstractResultCode batchMove(KnowledgeFileMoveBatchReqDTO dto) {
        if (CollectionUtils.isEmpty(dto.getResourceIdList())) {
            log.info("资源id为空，resourceIdList：{}", JsonUtil.toJson(dto.getResourceIdList()));
            return ResultCodeEnum.ERROR_PARAMS;
        }

        if (ObjectUtil.isEmpty(dto.getBaseId())) {
            log.info("知识库id为空,baseId:{}", dto.getBaseId());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        if (ObjectUtil.isEmpty(dto.getToParentFileId())) {
            log.info("目的父文件夹id为空,toParentFileId:{}", dto.getToParentFileId());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        // 资源ID列表包含根目录
        if (dto.getResourceIdList().contains(ROOT_PATH) || dto.getResourceIdList().contains("")) {
            log.info("资源id非法 resourceIdList:{}", JsonUtil.toJson(dto.getResourceIdList()));
            return ResultCodeEnum.ERROR_PARAMS;
        }

        return channelValid(dto);
    }
}
