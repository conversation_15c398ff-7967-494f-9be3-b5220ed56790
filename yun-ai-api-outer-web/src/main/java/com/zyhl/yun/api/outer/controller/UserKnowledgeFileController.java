package com.zyhl.yun.api.outer.controller;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.enums.BaseResultCodeEnum;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeCreateFolderReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileInfoReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileListBatchReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileListReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileUpdateReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileUrlReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.PersonalKnowledgeResourceListReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.UserKnowledgeFileService;
import static com.zyhl.yun.api.outer.constants.Const.NUM_16;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.controller.validate.PersonalKnowledgeValid;
import com.zyhl.yun.api.outer.controller.validate.UserKnowledgeFileValid;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeFileVO;
import com.zyhl.yun.api.outer.enums.knowledge.FileAuditStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.external.UserDriveExternalService;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 个人知识库文件控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(headers = {ReqHeadConst.CLIENT_INFO, ReqHeadConst.APP_CHANNEL})
public class UserKnowledgeFileController {

    @Resource
    private UserKnowledgeFileService userKnowledgeFileService;
    @Resource
    private UserKnowledgeFileValid userKnowledgeFileValid;
    @Resource
    private UserDriveExternalService userDriveExternalService;
    @Resource
    private PersonalKnowledgeValid personalKnowledgeValid;

    @Value("${audit.not-pass.thumbnail-url}")
    private String auditNotPassThumbnailUrl;

    /**
     * 不存在错误码
     */
    private static final String NOT_EXIST_CODE = "13000010";


    /**
     * 知识库文件列表
     */
    @PostMapping(value = "/assistant/knowledge/personal/file/list", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> list(@RequestBody KnowledgeFileListReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeFileValid.listValid(dto);
        if (check != null) {
            log.info("【知识库文件列表】参数校验失败");
            return BaseResult.error(check);
        }

        // 查询数据
        return BaseResult.success(userKnowledgeFileService.list(dto));
    }


    /**
     * 知识库文件列表
     */
    @PostMapping(value = "/assistant/knowledge/personal/file/list", headers = {ReqHeadConst.API_VERSION_V2})
    public BaseResult<?> list180(@RequestBody KnowledgeFileListReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeFileValid.listValid180(dto);
        if (check != null) {
            log.info("【知识库文件列表】参数校验失败");
            return BaseResult.error(check);
        }

        // 查询数据
        return BaseResult.success(userKnowledgeFileService.list180(dto));
    }

    /**
     * 知识库文件信息
     */
    @PostMapping(value = "/assistant/knowledge/personal/file/get", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> info(@RequestBody KnowledgeFileInfoReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeFileValid.infoValid(dto);
        if (check != null) {
            log.info("【知识库文件信息】参数校验失败");
            return BaseResult.error(check);
        }

        // 查询数据
        Map<String, Object> result = new HashMap<>(NUM_16);
        result.put("file", userKnowledgeFileService.info(dto));
        return BaseResult.success(result);
    }

    /**
     * 知识库文件下载地址
     */
    @PostMapping(value = "/assistant/knowledge/personal/file/getDownloadUrl", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> getDownloadUrl(@RequestBody KnowledgeFileUrlReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeFileValid.downloadUrlValid(dto);
        if (check != null) {
            log.info("【知识库文件下载地址】参数校验失败");
            return BaseResult.error(check);
        }

        // 下载地址
        String url = "";
        try {
            KnowledgeFileInfoReqDTO req = new KnowledgeFileInfoReqDTO();
            BeanUtils.copyProperties(dto, req);
            PersonalKnowledgeFileVO vo = userKnowledgeFileService.info(req);
            if (vo == null) {
                log.info("【知识库文件下载地址】文件不存在");
                return BaseResult.error(BaseResultCodeEnum.ERROR_PARAMS);
            }
            if (KnowledgeResourceTypeEnum.isHtml(vo.getOriginFileType())) {
                url = vo.getHtmlInfo().getUrl();
            } else if (KnowledgeResourceTypeEnum.isPersonalFile(vo.getOriginFileType()) || KnowledgeResourceTypeEnum.isNote(vo.getOriginFileType())) {
                if (KnowledgeResourceTypeEnum.isNote(vo.getOriginFileType()) && vo.getAuditStatus() != null && !FileAuditStatusEnum.isPass(vo.getAuditStatus())) {
                    // 审核中，审核不通过 返回默认地址
                    url = auditNotPassThumbnailUrl;
                } else {
                    url = userDriveExternalService.getDownloadUrl(vo.getUserId(), dto.getFileId());
                }
            }
        } catch (Exception e) {
            log.error("【知识库文件下载地址】获取下载地址异常：{}", e.getMessage(), e);
            if (e instanceof YunAiBusinessException) {
                YunAiBusinessException ex = (YunAiBusinessException) e;
                if (NOT_EXIST_CODE.equals(ex.getCode())) {
                    return BaseResult.error(BaseResultCodeEnum.ERROR_NOT_FOUND);
                } else if (ex.getExceptionEnum() != null) {
                    return BaseResult.error(ex.getExceptionEnum());
                }
            }
            return BaseResult.error(BaseResultCodeEnum.UNKNOWN_ERROR);
        }

        // 响应结果
        Map<String, Object> result = new HashMap<>(NUM_16);
        result.put("url", url);

        return BaseResult.success(result);
    }


    /**
     * 查询个人知识库资源列表
     *
     * @param dto 入参
     * @return 返回
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/resource/list", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> resourceList(@RequestBody PersonalKnowledgeResourceListReqDTO dto) {
        final AbstractResultCode check = personalKnowledgeValid.resourceListValid(dto);
        if (check != null) {
            log.info("【查询个人知识库资源列表】参数校验失败");
            return BaseResult.error(check);
        }

        return BaseResult.success(userKnowledgeFileService.resourceList(dto));
    }


    /**
     * 个人知识库资源信息批量查询接口
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/resource/batchGet", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> batchGet(@RequestBody KnowledgeFileListBatchReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeFileValid.batchGetValid(dto);
        if (check != null) {
            log.info("【个人知识库资源信息批量查询】参数校验失败");
            return BaseResult.error(check);
        }

        // 查询数据
        return BaseResult.success(userKnowledgeFileService.batchGet(dto));
    }

    /**
     * 创建个人知识库文件夹
     *
     * @param dto 入参
     * @return 响应
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/folder/create", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> createCatalog(@RequestBody KnowledgeCreateFolderReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeFileValid.createFolderValid(dto);
        if (check != null) {
            log.info("【个人知识库创建文件夹】参数校验失败");
            return BaseResult.error(check);
        }

        return BaseResult.success(userKnowledgeFileService.createFolder(dto));
    }


    /**
     * 个人知识库资源信息批量查询接口
     */
    @PostMapping(value = "/assistant/knowledge/personal/v2/base/resource/update", headers = {ReqHeadConst.API_VERSION_V1})
    public BaseResult<?> update(@RequestBody KnowledgeFileUpdateReqDTO dto) {
        // 参数校验
        final AbstractResultCode check = userKnowledgeFileValid.updateValid(dto);
        if (check != null) {
            log.info("【个人知识库资源信息批量查询】参数校验失败");
            return BaseResult.error(check);
        }

        // 查询数据
        return BaseResult.success(userKnowledgeFileService.update(dto));
    }

}
