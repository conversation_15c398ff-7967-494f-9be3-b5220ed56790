package com.zyhl.yun.api.outer.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.TaskCommentDTO;
import com.zyhl.yun.api.outer.application.dto.TaskCommentQueryDTO;
import com.zyhl.yun.api.outer.application.dto.TaskCommentRetDTO;
import com.zyhl.yun.api.outer.application.service.task.AlgorithmTaskCommentService;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 算法任务评价接口
 * @date 2025/4/21 15:35
 */
@Slf4j
@RestController
@RequestMapping(headers = {"x-yun-api-version", "x-yun-client-info", "x-yun-app-channel"})
public class AlgorithmTaskCommentController {

    @Resource
    private AlgorithmTaskCommentService algorithmTaskCommentService;


    @PostMapping("/task/comment/add")
    public BaseResult<?> add(@RequestBody @Validated TaskCommentDTO dto) {
        try {
            if (Objects.isNull(dto.getUserId()) && CharSequenceUtil.isNotEmpty(RequestContextHolder.getUserId())) {
                // 将上下文userId设置为用户ID
                dto.setUserId(RequestContextHolder.getUserId());
            }
            // 保存任务评价信息
            algorithmTaskCommentService.addTaskComment(dto);
        } catch (Exception e) {
            log.error("add task comment error|errMsg={}", e.getMessage(), e);
            if (e instanceof YunAiBusinessException) {
                YunAiBusinessException ex = (YunAiBusinessException) e;
                return BaseResult.error(ex.getCode(), ex.getMessage());
            }
            return BaseResult.error(AiResultCode.CODE_9999.getCode(), AiResultCode.CODE_9999.getMsg());
        }

        return BaseResult.success();
    }

    @PostMapping("/task/comment/get")
    public BaseResult<?> get(@RequestBody @Validated TaskCommentQueryDTO dto) {
        TaskCommentRetDTO taskCommentRet = null;

        try {
            if (Objects.isNull(dto.getUserId()) && CharSequenceUtil.isNotEmpty(RequestContextHolder.getUserId())) {
                // 将上下文userId设置为用户ID
                dto.setUserId(RequestContextHolder.getUserId());
            }
            // 获取任务评价信息
            taskCommentRet = algorithmTaskCommentService.getTaskComment(dto);
        } catch (Exception e) {
            log.error("get task comment error|errMsg={}", e.getMessage(), e);
            if (e instanceof YunAiBusinessException) {
                YunAiBusinessException ex = (YunAiBusinessException) e;
                return BaseResult.error(ex.getCode(), ex.getMessage());
            }
            return BaseResult.error(AiResultCode.CODE_9999.getCode(), AiResultCode.CODE_9999.getMsg());
        }

        return BaseResult.success(taskCommentRet);
    }

}