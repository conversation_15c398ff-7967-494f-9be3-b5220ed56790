package com.zyhl.yun.api.outer.controller;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.dto.ObjectKeysDTO;
import com.zyhl.yun.api.outer.application.dto.ReportInfoDTO;
import com.zyhl.yun.api.outer.application.service.ReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

/**
 * 举报Controller
 * @Author: <PERSON><PERSON><PERSON><PERSON>_<PERSON>
 */
@Slf4j
@RestController
@RequestMapping(headers = {"x-yun-api-version","x-yun-client-info","x-yun-app-channel"})
public class ReportController {

    @Autowired
    ReportService reportService;

    /**
     * 上传图片
     *
     * @param pictureInfo 图片信息数据传输对象
     * @return 基础结果类
     */
    @PostMapping(value = "/classify/report/uplaod/picture", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BaseResult uplaodPicture(@RequestParam(value = "pictureInfo") MultipartFile pictureInfo) {
        return BaseResult.success(reportService.uplaodPicture(pictureInfo));
    }

    /**
     * 取消举报
     *
     * @param dto 图片信息数据传输对象
     * @return 基础结果类
     */
    @PostMapping("/classify/report/cancel")
    public BaseResult cancel(@RequestBody @Valid ObjectKeysDTO dto) {
        return BaseResult.success(reportService.cancel(dto));
    }

    /**
     * 提交举报
     *
     * @param dto 图片信息数据传输对象
     * @return 基础结果类
     */
    @PostMapping("/classify/report/submit")
    public BaseResult submit(@RequestBody @Valid ReportInfoDTO dto) {
        return BaseResult.success(reportService.submit(dto));
    }

}
