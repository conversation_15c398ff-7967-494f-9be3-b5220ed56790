package com.zyhl.yun.api.outer.controller;

import java.util.Comparator;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.slf4j.MDC;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.anno.SSEApplicationAnnotation;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.service.HistoryDialogueSaveRedisService;
import com.zyhl.yun.api.outer.constants.AbstractChatAddV2HandlerMapConstant;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.domainservice.SseEmitterOperate;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.TalkTypeEnum;
import com.zyhl.yun.api.outer.exception.SseApplicationException;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * AI助手2.0版本接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(headers = {ReqHeadConst.API_VERSION, ReqHeadConst.CLIENT_INFO, ReqHeadConst.APP_CHANNEL})
public class ChatV2Controller {

    @Resource
    private List<AbstractChatAddV2Handler> chatAddHandlerList;
    @Resource
    private HistoryDialogueSaveRedisService historyRedisService;
    @Resource
    private DataSaveService dataSaveService;
    /**
     * 初始化处理器列表，排序
     */
    @PostConstruct
	private void init() {
		chatAddHandlerList.sort(Comparator.comparing(AbstractChatAddV2Handler::order));
	}

    
    @SSEApplicationAnnotation
    @PostMapping(value = "/assistant/chat/v2/add", headers = ReqHeadConst.API_VERSION_V1, produces = {MediaType.TEXT_EVENT_STREAM_VALUE})
    public SseEmitter chatV2(@RequestBody ChatAddReqDTO reqDTO) {
        SseEmitterOperate sseEmitterOperate = new SseEmitterOperate();
        ChatAddHandleDTO handleDTO = new ChatAddHandleDTO(reqDTO, sseEmitterOperate);

        // 鉴权异常信息
        SseApplicationException sseException = RequestContextHolder.getSseException();
        if (null != sseException) {
            sseEmitterOperate.sendAndComplete(BaseResult.error(sseException.getCode(), sseException.getMessage()));
            return sseEmitterOperate.getSseEmitter();
        }
        
		// 获取助手类型
		AssistantEnum assistantEnum = AssistantEnum.getByChannel(handleDTO.getReqDTO().getSourceChannel());
		// 按助手业务类型，过滤执行器
		List<AbstractChatAddV2Handler> newChatAddHandlerList = AbstractChatAddV2HandlerMapConstant
				.filterByAssistantEnum(assistantEnum, chatAddHandlerList);
		if (CollUtil.isEmpty(newChatAddHandlerList)) {
			// 参数异常
			log.info("newChatAddHandlerList 为空");
			sseEmitterOperate.sendAndComplete(BaseResult.error(ResultCodeEnum.ERROR_PARAMS));
			return sseEmitterOperate.getSseEmitter();
		}
		
        // 执行业务逻辑
        for (AbstractChatAddV2Handler handler : newChatAddHandlerList) {
            long startTime = System.currentTimeMillis();
            try {
                if (handler.execute(handleDTO) && !handler.run(handleDTO)) {
                    if(null != handleDTO.getInputInfoDTO()) {
                    	// 保存redis历史对话信息
                    	if (TalkTypeEnum.isTask(handleDTO.getInputInfoDTO().getDialogueType())) {
                    		log.info("不记录RedisHistoryDialogInfo dialogueId:{}", handleDTO.getDialogueId());
                    	}else {
                    		historyRedisService.setRedisHistoryDialogInfo(handleDTO);
                    	}
                    }
                    break;
                }
            } catch (Exception e) {
            	log.error("AI助手对话异常，异常信息：{}", e.getMessage(), e);

            	// 对话异常，更新失败状态
            	try {
            		dataSaveService.updateChatFail(handleDTO.getDialogueId());
            	}catch (Exception ee) {
            		log.error("Exception dataSaveService.updateFail dialogueId:{}, error:", handleDTO.getDialogueId(), e);
				}
            	
				if (e instanceof YunAiBusinessException) {
					YunAiBusinessException yunAiBusinessException = (YunAiBusinessException) e;
					if (ObjectUtil.isNotEmpty(yunAiBusinessException.getCode())) {
						AiResultCode aiResultCode = AiResultCode.getByCodeOrMsg(yunAiBusinessException.getCode(),
								yunAiBusinessException.getMessage());
						sseEmitterOperate
								.sendAndComplete(BaseResult.error(aiResultCode.getCode(), aiResultCode.getMsg()));
					} else {
						sseEmitterOperate.sendAndComplete(BaseResult.error(ResultCodeEnum.UNKNOWN_ERROR));
					}
					return sseEmitterOperate.getSseEmitter();
				}
				
                sseEmitterOperate.sendAndComplete(BaseResult.error(ResultCodeEnum.UNKNOWN_ERROR));
                return sseEmitterOperate.getSseEmitter();
            } finally {
                MDC.put(LogConstants.DURATION, String.valueOf(System.currentTimeMillis() - startTime));
                log.info("执行处理器：{}，耗时：{}ms", handler.getClass().getSimpleName(), System.currentTimeMillis() - startTime);
                MDC.remove(LogConstants.DURATION);
            }
        }

        return sseEmitterOperate.getSseEmitter();
    }

}
