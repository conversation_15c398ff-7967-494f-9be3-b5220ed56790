package com.zyhl.yun.api.outer.controller;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.dto.CaptureCaptionDTO;
import com.zyhl.yun.api.outer.application.service.task.ImageCaptionService;
import com.zyhl.yun.api.outer.application.vo.ImageCaptionVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 内部请求接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("/inner")
public class InnerController {

    @Resource
    private ImageCaptionService imageCaptionService;

    /**
     * 图配文封装
     */
    @PostMapping("/imageCaption")
    public BaseResult<ImageCaptionVO> getImageCaption(@RequestBody @Valid CaptureCaptionDTO dto) {
        ImageCaptionVO imageCaptionVO = imageCaptionService.getImageCaption(dto);

        if (ObjectUtil.isEmpty(imageCaptionVO)) {
            return BaseResult.error(imageCaptionVO);
        }
        return BaseResult.success(imageCaptionVO);
    }
}
