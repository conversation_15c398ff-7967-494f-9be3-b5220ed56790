/**
 * Copyright © 2021 ChinaMobile Info. Tech Ltd. All rights reserved.
 * <p>
 * TODO (用一句话描述该文件做什么)
 *
 * @author: <a href="<EMAIL>">ZhiFeng.Wu</a>
 * @date: 2021/11/9 10:52
 */
package com.zyhl.yun;

/***
 * - 可以包含部分网关的功能，同时整合多个application服务，通常包含工作：
 *
 *   网络协议的转化：通常这个已经由各种框架给封装掉了，我们需要构建的类要么是被注解的bean，要么是继承了某个接口的bean。
 *
 *   1.    **统一鉴权**：比如在一些需要AppKey+Secret的场景，需要针对某个租户做鉴权的，包括一些加密串的校验
 *   2.    **Session管理**：一般在面向用户的接口或者有登陆态的，通过Session或者RPC上下文可以拿到当前调用的用户，以便传递给下游服务。
 *   3.    **限流配置**：对接口做限流避免大流量打到下游服务
 *   4.    **前置缓存**：针对变更不是很频繁的只读场景，可以前置结果缓存到接口层
 *   5.    **异常处理**：通常在接口层要避免将异常直接暴露给调用端，所以需要在接口层做统一的异常捕获，转化为调用端可以理解的数据格式
 *   6.    **日志**：在接口层打调用日志，用来做统计和debug等。一般微服务框架可能都直接包含了这些功能。
 *
 */