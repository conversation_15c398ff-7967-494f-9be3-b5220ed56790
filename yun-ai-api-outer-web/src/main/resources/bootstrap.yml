spring:
  application:
    name: yun-ai-api-outer
    system: yun-ai
  profiles:
    # The commented value for `active` can be replaced with valid Spring profiles to load.
    # Otherwise, it will be filled in by maven when building the WAR file
    # Either way, it can be overridden by `--spring.profiles.active` value passed in the commandline or `-Dspring.profiles.active` set in `JAVA_OPTS`
    active: ${ACTIVE_PROFILE:local}
  mvc:
    throw-exception-if-no-handler-found: true
  web:
    resources:
      add-mappings: false
  lifecycle:
    timeout-per-shutdown-phase: "30s"

  servlet:
    multipart:
      maxFileSize: 15MB
      maxRequestSize: 15MB
  main:
    allow-bean-definition-overriding: true

server:
  shutdown: "graceful"
  port: 19027
  servlet:
    context-path: /ai/api/outer

management:
  endpoints:
    enabled-by-default: false
  server:
    port: -1

hcy:
  plugin:
    uid: #雪花算法插件生成配置
      snowflake:
        timeBits: 32
        workerBits: 18
        seqBits: 13
        epochStr: 2007-06-05  #不能高于此时间点。此种情况下，固定为19位雪花id
        paddingFactor: 50

cache:
  algorithm-config: # 算法配置缓存
    timeout:
      minutes: 120 # 缓存时长
    time-refresh:
      seconds: 300 # 缓存时长剩余多长时更新缓存
thread-pool-config:
  algorithm-config-refresh-workers: # 更新算法配置缓存的线程池配置
    core-size: 10
    max-size: 20
    keep-alive-minutes: 1
    work-queue-size: 200

yun:
  ai-tool-config:
    group-name: yun-ai-tool-config

---
spring:
  config:
    activate:
      on-profile: local
    import:
      - classpath:local/bootstrap-local.yml
      - classpath:local/redis-local.yml
      - classpath:local/feign-local.yml
      - classpath:local/tidb-local.yml
      - classpath:local/mq-local.yml
      - classpath:local/elastic-local.yml
      - classpath:local/recommend-local.yml
      - classpath:local/temporary-local.yml
      - classpath:local/whitelist-local.yml
      - classpath:local/knowledge-local.yml
      - classpath:local/source-channel-local.yml
      - classpath:local/benefits-local.yml
      - classpath:local/client-type-local.yml
      - classpath:local/lead-copy-local.yml
      - classpath:local/search-local.yml
      - classpath:local/ai-tool-config-local.yml
      - classpath:local/second-stream-local.yml
      - classpath:local/intention-local.yml
      - classpath:local/check-sensitive-local.yml
      - classpath:local/tool-face-swap-local.yml
      - classpath:local/text-model-local.yml
      - classpath:local/business-param-local.yml
      - classpath:local/special-prompt-local.yml
      - classpath:local/thing-label-local.yml
      - classpath:local/chat-agent-local.yml
      - classpath:local/note-dialogue-local.yml
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_SERVER:**********:8848} # 你的nacos地址
        namespace: local
        ip-type: IPv6
        username: nacos-dev-user
        password: r@Knh7t!Ok
      config:
        server-addr: ${NACOS_SERVER:**********:8848}
        namespace: local
        ip-type: IPv6
        username: nacos-dev-user
        password: r@Knh7t!Ok

---
spring:
  config:
    activate:
      on-profile: dev
    import:
      - classpath:local/bootstrap-local.yml
      - classpath:local/redis-local.yml
      - classpath:local/feign-local.yml
      - classpath:local/mq-local.yml
      - classpath:local/recommend-local.yml
      - classpath:local/temporary-local.yml
      - classpath:local/whitelist-local.yml
      - classpath:local/knowledge-local.yml
      - classpath:local/source-channel-local.yml
      - classpath:local/benefits-local.yml
      - classpath:local/client-type-local.yml
      - classpath:local/lead-copy-local.yml
      - classpath:local/search-local.yml
      - classpath:local/ai-tool-config-local.yml
      - classpath:local/second-stream-local.yml
      - classpath:local/intention-local.yml
      - classpath:local/tool-face-swap-local.yml
      - classpath:local/text-model-local.yml
      - classpath:local/business-param-local.yml
      - classpath:local/note-dialogue-local.yml
      - optional:nacos:bootstrap-dev.yml?group=${spring.application.name}
      - optional:nacos:redis-dev.yml?group=${spring.application.name}
      - optional:nacos:feign-dev.yml?group=${spring.application.name}
      - optional:nacos:mq-dev.yml?group=${spring.application.name}
      - optional:nacos:recommend-local.yml?group=${spring.application.name}
      - optional:nacos:temporary-local.yml?group=${spring.application.name}
      - optional:nacos:whitelist-local.yml?group=${spring.application.name}
      - optional:nacos:knowledge-dev.yml?group=${spring.application.name}
      - optional:nacos:source-channel-dev.yml?group=${spring.application.name}
      - optional:nacos:benefits-dev.yml?group=${spring.application.name}
      - optional:nacos:client-type-dev.yml?group=${spring.application.name}
      - optional:nacos:lead-copy-dev.yml?group=${spring.application.name}
      - optional:nacos:search-dev.yml?group=${spring.application.name}
      - optional:nacos:second-stream-dev.yml?group=${spring.application.name}
      - optional:nacos:intention-dev.yml?group=${spring.application.name}
      - optional:nacos:check-sensitive-dev.yml?group=${spring.application.name}
      - optional:nacos:tool-face-swap-dev.yml?group=${spring.application.name}
      - optional:nacos:text-model-dev.yml?group=${spring.application.name}
      - optional:nacos:business-param-dev.yml?group=${spring.application.name}
      - optional:nacos:config-dev.yml?group=${yun.ai-tool-config.group-name}
      - optional:nacos:special-prompt-dev.yml?group=${spring.application.name}
      - optional:nacos:thing-label-dev.yml?group=${spring.application.name}
      - optional:nacos:chat-agent-dev.yml?group=${spring.application.name}
      - optional:nacos:note-dialogue-dev.yml?group=${spring.application.name}
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_SERVER:**********:8848} # 你的nacos地址
        namespace: dev
        ip-type: IPv6
        username: nacos-dev-user
        password: r@Knh7t!Ok
      config:
        server-addr: ${NACOS_SERVER:**********:8848}
        namespace: dev
        ip-type: IPv6
        username: nacos-dev-user
        password: r@Knh7t!Ok
    inetutils:
      preferred-networks:
        - fd22:2222.*
---
spring:
  config:
    activate:
      on-profile: test
    import:
      - optional:nacos:bootstrap-test.yml?group=${spring.application.name}
      - optional:nacos:redis-test.yml?group=${spring.application.name}
      - optional:nacos:feign-test.yml?group=${spring.application.name}
      - optional:nacos:tidb-test.yml?group=${spring.application.name}
      - optional:nacos:mq-test.yml?group=${spring.application.name}
      - optional:nacos:elastic-test.yml?group=${spring.application.name}
      - optional:nacos:recommend-test.yml?group=${spring.application.name}
      - optional:nacos:temporary-test.yml?group=${spring.application.name}
      - optional:nacos:whitelist-test.yml?group=${spring.application.name}
      - optional:nacos:knowledge-test.yml?group=${spring.application.name}
      - optional:nacos:source-channel-test.yml?group=${spring.application.name}
      - optional:nacos:benefits-test.yml?group=${spring.application.name}
      - optional:nacos:client-type-test.yml?group=${spring.application.name}
      - optional:nacos:lead-copy-test.yml?group=${spring.application.name}
      - optional:nacos:search-test.yml?group=${spring.application.name}
      - optional:nacos:second-stream-test.yml?group=${spring.application.name}
      - optional:nacos:intention-test.yml?group=${spring.application.name}
      - optional:nacos:check-sensitive-test.yml?group=${spring.application.name}
      - optional:nacos:tool-face-swap-test.yml?group=${spring.application.name}
      - optional:nacos:text-model-test.yml?group=${spring.application.name}
      - optional:nacos:business-param-test.yml?group=${spring.application.name}
      - optional:nacos:config-test.yml?group=${yun.ai-tool-config.group-name}
      - optional:nacos:special-prompt-test.yml?group=${spring.application.name}
      - optional:nacos:thing-label-test.yml?group=${spring.application.name}
      - optional:nacos:chat-agent-test.yml?group=${spring.application.name}
      - optional:nacos:note-dialogue-test.yml?group=${spring.application.name}
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_SERVER:a50-nacos-t01-client.nacos-a50-ypcg-t01-nacos-01:8848}
        username: nacos-test-user
        password: cbkikPO&I@
        namespace: test
        ip-type: IPv6
      config:
        server-addr: ${NACOS_SERVER:a50-nacos-t01-client.nacos-a50-ypcg-t01-nacos-01:8848}
        username: nacos-test-user
        password: cbkikPO&I@
        namespace: test
    inetutils:
      preferred-networks:
        - fd22:2222.*

# 南基移动云生产配置
---
spring:
  config:
    activate:
      on-profile: prod
    import:
      - optional:nacos:bootstrap-prod.yml?group=${spring.application.name}
      - optional:nacos:redis-prod.yml?group=${spring.application.name}
      - optional:nacos:feign-prod.yml?group=${spring.application.name}
      - optional:nacos:tidb-prod.yml?group=${spring.application.name}
      - optional:nacos:mq-prod.yml?group=${spring.application.name}
      - optional:nacos:elastic-prod.yml?group=${spring.application.name}
      - optional:nacos:recommend-prod.yml?group=${spring.application.name}
      - optional:nacos:temporary-prod.yml?group=${spring.application.name}
      - optional:nacos:whitelist-prod.yml?group=${spring.application.name}
      - optional:nacos:knowledge-prod.yml?group=${spring.application.name}
      - optional:nacos:source-channel-prod.yml?group=${spring.application.name}
      - optional:nacos:benefits-prod.yml?group=${spring.application.name}
      - optional:nacos:client-type-prod.yml?group=${spring.application.name}
      - optional:nacos:lead-copy-prod.yml?group=${spring.application.name}
      - optional:nacos:search-prod.yml?group=${spring.application.name}
      - optional:nacos:second-stream-prod.yml?group=${spring.application.name}
      - optional:nacos:intention-prod.yml?group=${spring.application.name}
      - optional:nacos:check-sensitive-prod.yml?group=${spring.application.name}
      - optional:nacos:tool-face-swap-prod.yml?group=${spring.application.name}
      - optional:nacos:text-model-prod.yml?group=${spring.application.name}
      - optional:nacos:business-param-prod.yml?group=${spring.application.name}
      - optional:nacos:config-prod.yml?group=${yun.ai-tool-config.group-name}
      - optional:nacos:special-prompt-prod.yml?group=${spring.application.name}
      - optional:nacos:thing-label-prod.yml?group=${spring.application.name}
      - optional:nacos:chat-agent-prod.yml?group=${spring.application.name}
      - optional:nacos:note-dialogue-prod.yml?group=${spring.application.name}
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_SERVER:b66-ypcg-ai-nacos-p01-client.nacos-b66-ai-p1-e2fpohrir8.svc.cluster.local:8848}
        namespace: prod
        username: nacos-ai-p1
        password: ywcP8#OOvcc1x7KOBzpr
        ip-type: IPv6
      config:
        server-addr: ${NACOS_SERVER:b66-ypcg-ai-nacos-p01-client.nacos-b66-ai-p1-e2fpohrir8.svc.cluster.local:8848}
        namespace: prod
        username: nacos-ai-p1
        password: ywcP8#OOvcc1x7KOBzpr
    inetutils:
      preferred-networks:
        - fd22:2222.*