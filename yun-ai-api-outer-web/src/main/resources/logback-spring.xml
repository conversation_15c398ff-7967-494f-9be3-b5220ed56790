<?xml version="1.0" encoding="UTF-8" ?>
<!--扫描配置文件,有变化时重新加载，时间间隔60s -->
<configuration scan="false" scanPeriod="60 seconds" debug="false">
    <conversionRule conversionWord="hostname"
                    converterClass="com.zyhl.hcy.plugin.logger.util.ServerIpConverter"/>

    <springProperty scope="context" name="applicationName" source="spring.application.name" defaultValue="hcy"/>
    <springProperty scope="context" name="logFileDir" source="hcy.plugin.logger.log-file-dir"
                    defaultValue="./logs/${applicationName}/${hostname}/"/>
    <springProperty scope="context" name="logLevel" source="hcy.plugin.logger.level" defaultValue="INFO"/>
    <springProperty scope="context" name="logCharset" source="hcy.plugin.logger.charset" defaultValue="UTF-8"/>
    <springProperty scope="context" name="logEnableConsole" source="hcy.plugin.logger.enable-console"
                    defaultValue="ON"/>
    <springProperty scope="context" name="logReportMaxNumber" source="hcy.plugin.logger.report-log.max-number"
                    defaultValue="5"/>
    <springProperty scope="context" name="logReportMaxSize" source="hcy.plugin.logger.report-log.max-size"
                    defaultValue="500MB"/>
    <springProperty scope="context" name="logHistoryFileMaxHistory" source="hcy.plugin.logger.history-log.max-history"
                    defaultValue="100"/>
    <springProperty scope="context" name="logHistoryFileMaxSize" source="hcy.plugin.logger.history-log.max-size"
                    defaultValue="200MB"/>
    <springProperty scope="context" name="logErrorFileMaxHistory" source="hcy.plugin.logger.error-log.max-history"
                    defaultValue="100"/>
    <springProperty scope="context" name="logErrorFileMaxSize" source="hcy.plugin.logger.error-log.max-size"
                    defaultValue="200MB"/>

    <!--日志目录 -->
    <property scope="context" name="LOG_FILE_DIR" value="${logFileDir}"/>
    <property scope="context" name="PROJECT_NAME" value="${applicationName}"/>
    <property scope="context" name="PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS}|%thread|%-5level|%logger{30}.%M:%L|%X{log_type:-IGNORE}|%X{host_ip}|%X{remote_ip}|%X{system}|%X{service}|%X{request_url}|%X{trace_id}|%X{parent_span_id}|%X{span_id}|%X{account}|%X{account_type}|%X{http_status}|%X{start_time}|%X{end_time}|%X{duration}|%X{app_version}|%X{device_no}|%X{network}|%X{device_system}|%X{device_type}|%X{api_version}|%X{channel}|%X{device_info}|%X{auth_type}|%X{result_code}|%X{market_source}|%X{op_type}|%X{sub_op_type}|%X{sw8_trace_id}|%.-5000msg%n"/>

    <!-- CONSOLE 日志， 由配置项 logEnableConsole 控制是否开启 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>${logLevel}</level>
        </filter>
        <encoder>
            <pattern>${PATTERN}</pattern>
            <charset class="java.nio.charset.Charset">${logCharset}</charset>
        </encoder>
    </appender>

    <!--备份用gzip访问日志 -->
    <appender name="gzipAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>${logLevel}</level>
        </filter>
        <file>${LOG_FILE_DIR}/${PROJECT_NAME}-log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE_DIR}/${PROJECT_NAME}.%d{yyyy-MM-dd}.log.%i.gz
            </fileNamePattern>
            <maxHistory>${logHistoryFileMaxHistory}</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${logHistoryFileMaxSize}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>${PATTERN}</pattern>
            <charset class="java.nio.charset.Charset">${logCharset}</charset>
        </encoder>
    </appender>

    <!--所有的访问日志 -->
    <appender name="${projName}"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>${logLevel}</level>
        </filter>
        <file>${LOG_FILE_DIR}/${PROJECT_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${LOG_FILE_DIR}/${PROJECT_NAME}-%i.log</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>${logReportMaxNumber}</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>${logReportMaxSize}</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>${PATTERN}</pattern>
            <charset class="java.nio.charset.Charset">${logCharset}</charset>
        </encoder>
    </appender>

    <!--错误的访问日志 -->
    <appender name="errorLogAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <file>${LOG_FILE_DIR}/${PROJECT_NAME}-error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE_DIR}/${PROJECT_NAME}-error.%d{yyyy-MM-dd}.log.%i.gz
            </fileNamePattern>
            <maxHistory>${logErrorFileMaxHistory}</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${logErrorFileMaxSize}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>${PATTERN}</pattern>
            <charset class="java.nio.charset.Charset">${logCharset}</charset>
        </encoder>
    </appender>

    <!-- 异步输出(所有日志) -->
    <appender name="ASYNC-ALL" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 设置0，不丢失日志。默认的情况下，如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="${projName}"/>
        <includeCallerData>true</includeCallerData>
    </appender>

    <!-- 异步输出(gzip日志)-->
    <appender name="ASYNC-GZIP" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 设置0，不丢失日志。默认的情况下，如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="gzipAppender"/>
    </appender>

    <!-- 异步输出(错误日志) -->
    <appender name="ASYNC-ERROR" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 设置0，不丢失日志。默认的情况下，如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="errorLogAppender"/>
    </appender>

    <!-- 主日志 -->
    <root level="${logLevel}">
        <appender-ref ref="ASYNC-ALL"/>
        <appender-ref ref="ASYNC-GZIP"/>
        <appender-ref ref="ASYNC-ERROR"/>
        <!--<appender-ref ref="syslog" />-->
        <if condition='java.util.regex.Pattern.matches("ON|TRUE|1|YES", property("logEnableConsole").toUpperCase())'>
            <then>
                <appender-ref ref="STDOUT"/>
            </then>
        </if>
    </root>

</configuration>