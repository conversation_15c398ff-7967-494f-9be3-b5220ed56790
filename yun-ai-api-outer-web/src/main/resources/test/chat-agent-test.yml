# 应用智能体配置
application-agent:
  # 应用白名单配置
  white-config:
    # 应用id列表维度配置
    application-id-list:
      #配置渠道开启deepseek智能体
      - ids: ['3000000001','3000000002']
        #空则不限制渠道
        support-channels: ['10102','10103','10104','10105','10106','10108','10109','10110','10112','10120','10114','10130','10116','10140','10118','10150','10160','10170','10171','10172','10173','10174','10175','10101','10107','10161']
        #空则不限制用户
        support-phone-numbers: []
      #灵犀-中国移动云盘会议助手
      - ids: ['3000000003']
        #空则不限制渠道
        support-channels: ['10176']
        #空则不限制用户
        support-phone-numbers: []
  # 灵犀智能体默认配置
  lingxi:
    # 演示使用配置
    demo-config:
      #演示控制参数，true为固定basic token  
      demo: false
      basic-token: 'Basic ****************************************************************************************************************************************************************************************************************************************************************************************************'
    # 业务配置
    business-config:
      channel: '10176'
      application-id: '3000000003'
      mail-edit-jump-url: 'https://**************:10800/logins/?func=login:authTokenPE&token={token}&targetSourceId=001003&loginSuccessUrl=https%3A%2F%2Fipad.mail.10086ts.cn%2Fhtml%2Fcompose.html%3FdialogueId%3D{dialogueId}%26clientId%3D10176'
      mail-send-jump-url: 'https://**************:10800/logins/?func=login:authTokenPE&token={token}&targetSourceId=001003&loginSuccessUrl=https%3A%2F%2Fipad.mail.10086ts.cn%2Fhtml%2FmailList.html%3Ffid%3D3%26clientId%3D10176'
      note-voice-jump-url: 'https://test.yun.139.com/viewtest/ai-helper-lingxi/pages/notes/audioRecord?token={token}'
      ai-helper-jump-url: 'https://test.yun.139.com/viewtest/ai-helper-lingxi/?clientId=10176&tab=aiChat&toolType=4&token={token}'
      