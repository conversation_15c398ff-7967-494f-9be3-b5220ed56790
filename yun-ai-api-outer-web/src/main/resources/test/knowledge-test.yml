#知识库相关配置

#知识库
knowledge:
  # 个人知识库
  personal:
    # 文件大小限制（B）
    size: 629145600
    # 扩展名
    ext-list: [ "docx","xlsx","csv","txt","pptx","doc","xls","ppt","pdf","md" ]
    # 标签个数
    label-num: 50
    # 标签名字数
    label-name-len: 6

    # 转存任务过期时长（秒）
    transfer-expire-time: 86400
    # 删除任务过期时长（秒）
    delete-expire-time: 86400

    # 删除任务查询间隔时间（毫秒）
    delete-task-query-sleep: 1
    # 删除任务查询次数（每次10秒钟）
    delete-task-query-times: 0

    # 解析失败原因（错误码对应的描述）
    parse-failed-reason:
      "0000": ""
      "9999": "哎呀，解析过程出了点小意外，请稍后再试试看"
      "10090027": "接口突然罢工啦，请稍后再试试看"
      "10090018": "文件空空如也，补充点有效内容再来试试吧"
      "10090026": "文档没有文字哦，补充点有效内容再来试试吧"
      "999999999": "该文件格式无法解析哦，去换一个格式再试试看吧"
      "105": "文件可能包含敏感内容哦，请重新检查后再试"
      "10070404": "哎呀，解析过程出了点小意外，请稍后再试试看"
      "10070405": "文件空空如也，补充点内容再来试试吧"
      "10070406": "文件可能包含敏感内容哦，请重新检查后再试"
      "10090030": "哎呀，解析过程出了点小意外，请稍后再试试看"
      "10000017": "哎呀，解析过程出了点小意外，请稍后再试试看"
      "01000012": "文件可能包含敏感内容哦，请重新检查后再试"
      "10000015": "文件内容字数太多哦，精简内容后再试试看吧"
      "10090015": "该文件大小超限了哦，去换一个大小再试试看吧"
      "10000019": "该文件格式无法解析哦，去换一个格式再试试看吧"
      "10000006": "该文件大小无法解析哦，去换一个大小再试试看吧"
      "01000005": "文件空空如也，补充点有效内容再来试试吧"
      "10090039": "该文件无法解析哦，去换一个文档再试试看吧"
      "10090013": "文件真实格式与文件后缀不适配"
      "10030407": "网页地址不正确"
      "10030408": "目前仅支持识别微信公众号/小红书/知乎/百度贴吧链接"

    #个人知识库2.0名称最大长度
    name-length: 25
    #个人知识库2.0描述最大长度
    description-length: 150
    #个人知识库2.0创建知识库最大数量
    create-max-num: 10
    #个人知识库2.0知识库名称和描述送审开关
    check-content-switch: true

    #个人知识库头像信息列表
    profile-photo-list:
      - type: 1
        photoId: "man_1"
        url: "https://yun.mcloud.139.com/aiassistant/static/knowledge/head/man_1.png"
      - type: 1
        photoId: "man_2"
        url: "https://yun.mcloud.139.com/aiassistant/static/knowledge/head/man_2.png"
      - type: 1
        photoId: "man_3"
        url: "https://yun.mcloud.139.com/aiassistant/static/knowledge/head/man_3.png"
      - type: 1
        photoId: "man_4"
        url: "https://yun.mcloud.139.com/aiassistant/static/knowledge/head/man_4.png"
      - type: 1
        photoId: "man_5"
        url: "https://yun.mcloud.139.com/aiassistant/static/knowledge/head/man_5.png"
      - type: 1
        photoId: "female_1"
        url: "https://yun.mcloud.139.com/aiassistant/static/knowledge/head/female_1.png"
      - type: 1
        photoId: "female_2"
        url: "https://yun.mcloud.139.com/aiassistant/static/knowledge/head/female_2.png"
      - type: 1
        photoId: "female_3"
        url: "https://yun.mcloud.139.com/aiassistant/static/knowledge/head/female_3.png"
      - type: 1
        photoId: "female_4"
        url: "https://yun.mcloud.139.com/aiassistant/static/knowledge/head/female_4.png"
      - type: 1
        photoId: "female_5"
        url: "https://yun.mcloud.139.com/aiassistant/static/knowledge/head/female_5.png"

    #知识库上传html合法域名白名单
    htmlUrlLegalHosts:
      - mp.weixin.qq.com
      - xiaohongshu.com
      - zhihu.com
      - tieba.baidu.com
      - xhslink.com

  # 知识库对话
  dialogue:
    # 指定使用的公共知识库的标识
    knowledge-base-id: "common"
    # 公共知识库白名单
    white-list: [ ]
    # 个人知识库开关
    personal-switch: true
    # 知识库名词库开关
    enable-noun-library: true
    # 多路重排开关
    enable-multi-rerank: true

    # 开头文案，编码对应枚举KnowledgeBaseEnum
    title-map:
      # 只命中个人知识库
      personal: "根据你的知识库："
      # 只命中公共知识库
      common: "根据我获取的知识："
      # 命中个人知识库和公共知识库
      knowledge: "根据我获取的知识及你的知识库："
      # 没有命中知识库，大模型有联网能力
      support-network: "当前知识库文件中未找到相关内容，已为你自动联网搜索"
      # 没有命中知识库，大模型无联网能力
      unsupport-network: "当前知识库文件中未找到相关内容，已用大模型为你处理"

    # 可用知识库的渠道
    enable-channel-list: [ 10175 ]

    # 重写配置
    rewrite-config:
      # 重写开关
      enabled: true
      # 重写使用的大模型
      model-code: "qwen_32b"
      # 重写输入内容系统提示
      system-prompt: "你是一个旨在帮助用户更有效检索信息的助手。\n你的主要职责是在用户输入表达不明确的情况下，通过参考#历史对话摘要#和#关键词列表#，对原始问题进行重写。\n你的目标是使问题更加具体和容易被检索，并保持与用户原始意图的一致性\n并且，请不要忽略#原始用户问题#中的内容\n你应该1. 理解背景: 通过阅读历史对话摘要，了解用户之前的对话内容，把握对话的上下文和主题。\n2. 利用关键词: 将关键词融入问题，确保#重写后的问题#包含这些关键词，提高检索的相关性。\n3. 增加细节: 如果用户的问题过于宽泛或模糊，适当添加细节使问题更具体，但不要引入新的概念或信息。\n4. 保持一致性: 确保#重写后的问题#不偏离用户原始的意图或信息需求。\n5. 简洁明了: 保持问题简短而明确，避免冗长或复杂的表述。\n#重写后的问题#只能在#原始用户问题#的基础上增加10-20个字\n#原始用户问题#，#重写后的问题#，#历史对话摘要#，#关键词列表#都不允许出现在#重写后的问题#中\n#历史对话摘要#:{history}\n#关键词列表#:{keywords}"
      # 重写输入内容用户提示
      user-prompt: "#原始用户问题#:{query}\n#重写后的问题#:"
      #问题重写最大字数
      maxLength: 128
      #历史问题数量
      historyNum: 5

    # 关键字提取配置
    keyword-config:
      # 关键字开关
      enabled: true
      # 键词提取方法，支持"tfidf"、"textrank"和"llm"，默认为"tfidf"；"llm"当前版本不可用
      method: "tfidf"
      # 最大提取关键词数量，默认为-1（-1即返回所有关键词）
      maxKeywords: -1
      # 重写输入内容用户提示
      allowPos: ["n", "nr", "ns", "nt", "nz", "vn"]
      #最小词长度，默认为2
      minWordLen: 2

    # 召回配置
    recall-config:
      # 召回超时时间，单位毫秒（默认10秒）
      await-time: 60000
      # 结果日志拆分打印，每组数量
      log-group-size: 4
      # 【公共知识库】base配置
      common-base:
        # 启用开关，true-打开（默认），false-关闭
        enabled: true
        # 向量大切片数量
        big-split-size: 10
        # 上分片数
        pre-count: 1
        # 下分片数
        next-count: 1
        # 需要合并上下分片开关，true-打开，false-关闭
        enabled-merge-context: false
      # 【个人知识库】base配置
      personal-base:
        # 启用开关，true-打开（默认），false-关闭
        enabled: true
        # 向量大切片数量
        big-split-size: 40
        # 上分片数
        pre-count: 1
        # 下分片数
        next-count: 1
        # 需要合并上下分片开关，true-打开，false-关闭
        enabled-merge-context: false

      # 【公共知识库】切片配置
      common-split:
        # 公共知识库id
        common-base-id: "common"
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: true
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false
        # small to big 配置
        small-to-big-config:
          enabled: true
          min-score: 0.1
          top-n: 100
          merge-size: 2

      # 【公共知识库】问答配置
      common-qa:
        # 公共知识库id
        common-base-id: "common"
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【公共知识库】切片的假设性问题配置
      common-split-gqa:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 300
        # 问答对，查询条数
        qa-top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【公共知识库】语义切块配置
      common-gsplit:
        # 公共知识库id
        common-base-id: "common"
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【公共知识库】文档总结配置
      common-summary:
        # 公共知识库id
        common-base-id: "common"
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【公共知识库】text查询配置
      common-text:
        # 公共知识库id
        common-base-id: "common"
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: true
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【个人知识库】切片配置
      personal-split:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: true
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false
        # small to big 配置
        small-to-big-config:
          enabled: true
          min-score: 0.1
          top-n: 100
          merge-size: 2

      # 【个人知识库】切片假设性问题配置
      personal-split-gqa:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 300
        # 问答对，查询条数
        qa-top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【个人知识库】语义切片配置
      personal-gsplit:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【个人知识库】文档总结配置
      personal-summary:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【个人知识库】text查询配置
      personal-text:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: true
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【个人知识库】keyword查询配置
      personal-keyword:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: true
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

    # 【默认】算法重排配置
    rerank-config:
      # 批处理大小，默认256
      batch-size: 256
      # 分段内容的最大长度，超出会截断，默认512
      max-length: 1024
      # 结果日志拆分打印，每组数量
      log-group-size: 4
      # 重排返回条数
      top-n: 20
      # 重排后最小评分
      min-score: 0.3
      # 重排后返回分块字符总长度：25600（默认）
      text-max-length: 25600
    # 【向量】算法重排配置
    vector-rerank-config:
      # 批处理大小，默认256
      batch-size: 256
      # 分段内容的最大长度，超出会截断，默认512
      max-length: 1024
      # 结果日志拆分打印，每组数量
      log-group-size: 4
      # 重排返回条数
      top-n: 40
      # 重排后最小评分
      min-score: 0.1
      # 重排后返回分块字符总长度：25600（默认）
      text-max-length: 25600
    # 【全文】算法重排配置
    text-rerank-config:
      # 批处理大小，默认256
      batch-size: 256
      # 分段内容的最大长度，超出会截断，默认512
      max-length: 1024
      # 结果日志拆分打印，每组数量
      log-group-size: 4
      # 重排返回条数
      top-n: 15
      # 重排后最小评分
      min-score: 0.03
      # 重排后返回分块字符总长度：25600（默认）
      text-max-length: 25600
    # 【关键字】算法重排配置
    keyword-rerank-config:
      # 批处理大小，默认256
      batch-size: 256
      # 分段内容的最大长度，超出会截断，默认512
      max-length: 1024
      # 结果日志拆分打印，每组数量
      log-group-size: 4
      # 重排返回条数
      top-n: 15
      # 重排后最小评分
      min-score: 0.03
      # 重排后返回分块字符总长度：25600（默认）
      text-max-length: 25600

    # 重排结果相关性配置
    relevancy-config:
      # 相关性功能开关，true-启用，false-停用
      enabled: false
      # 相关性使用的模型编码
      model-code: "qwen"
      # 相关性用户输入的模板
      user-prompt: "#任务描述\n你是一个判断助手，需要判断每个文本块在回答问题时是否有用。\n\n#输入格式\n- 用户问题：一个问题\n- 文本块列表：{textSize}个文本块\n\n#输出要求\n1. 必须返回一个包含{textSize}个布尔值的列表\n2. True表示这个文本块对回答问题有用\n3. False表示这个文本块对回答问题没用\n4. 返回顺序必须和文本块顺序一致\n\n#示例\n输入：\n用户问题：\"什么是太阳？\"\n文本块列表：[\n    \"太阳是一颗恒星\",\n    \"月亮是地球的卫星\",\n    \"太阳提供光和热\"\n]\n\n输出：\n[True,False,True]\n\n#注意事项\n- 只返回布尔值列表\n- 不要包含任何解释\n- 不要包含任何标点符号\n- 列表长度必须是{textSize}\n\n#实际输入\n用户问题：{query}\n文本块列表：{texts}"
      # 相关性大模型参数配置
      text-model-config:
        # 大模型温度参数
        temperature: 0.0
        # 大模型top_p参数
        top-p: 0.1
        # 随机种子（-1表示不传值）
        seed: 1234

    # 大模型对话配置
    dialogue-config:
      # 模型编码
      model-code: "blian"
      # 大模型系统提示词（role:system）
      system-prompt: "#角色\n你是一个智能助手。\n\n#要求：\n1.过滤并分析相关知识中符合渠道号和客户端类型条件的内容。\n2.依据过滤后的知识，拆解用户的问题，提供完整的答案，确保覆盖用户问题的所有方面。\n3.回答应当语句通顺，简洁精炼，内容全面。\n4.返回的答案中不能出现渠道号和客户端类型的相关描述。\n5.如果相关知识中有网页链接，则必须使用以下HTML超链接格式返回：\n<a data-href='http://your-url.com'>文本</a>\n确保链接格式正确且一致。\n6.如果相关知识中没有有效信息时，则依据你的知识或使用搜索功能回答用户。\n\n7.涉及数学计算时，不要依赖知识库，调用数学工具计算。\n\n8.当涉及具体信息的查询时，比如产品名称、文件编号等，如果知识库有该内容则原样返回，否则回复不知道。\n\n示例: \n相关知识: 适应范围：渠道号（101，102）+客户端类型（1，2） \nQ1：如何开启/关闭手机自动备份？ \nA1：1）在云盘app首页选择【手机备份】 \n2）开启自动备份相册、通讯录、微信。\n渠道号: 101，102\n客户端类型: 1,2\n用户输入: 如何找到自动备份？\n回答: 找到移动云盘的自动备份，按照以下步骤操作：\n\n1）在云盘app首页选择【手机备份】\n2）自动备份相册、通讯录、微信。\n\n#当前信息：\n历史对话：{history}\n相关知识:{knowledge}\n渠道号: {sourceChannel}\n客户端类型: {clientType}\n"
      # 大模型输入内容（role:user）
      user-prompt: "用户输入: {query}"
      # 大模型参数配置
      text-model-config:
        # 大模型温度参数
        temperature: 0.7
        # 大模型top_p参数
        top-p: 0.7
        # 随机种子（-1表示不传值）
        seed: -1

    # 数据搜索超时配置（秒）
    search-timeout: 300

    # 行政人物
    politician-enabled: true
    politician-list:
      - name: "习近平"
        position: "中国共产党中央委员会总书记，中共中央军事委员会主席，中华人民共和国主席，中华人民共和国中央军事委员会主席"
        sort: 50000
        replace-info-list:
          - { replace: "习近平", honorific: "习主席" }
      - name: "李强"
        position: "中共二十届中央政治局常委，国务院总理、党组书记"
        sort: 49900
        replace-info-list:
          - { replace: "李强",honorific: "李总理" }
      - name: "赵乐际"
        position: "中共二十届中央政治局常委，十四届全国人大常委会委员长"
        sort: 49800
        replace-info-list:
          - { replace: "赵乐际", honorific: "赵委员长" }
      - name: "王沪宁"
        position: "中共二十届中央政治局常委，十四届全国政协主席，中央全面深化改革委员会办公室主任"
        sort: 49700
        replace-info-list:
          - { replace: "王沪宁", honorific: "王主席" }
      - name: "蔡奇"
        position: "中央政治局常委、中央书记处书记，中央办公厅主任、中央和国家机关工委书记"
        sort: 49600
        replace-info-list:
          - { replace: "蔡奇", honorific: "蔡书记" }
      - name: "丁薛祥"
        position: "中共二十届中央政治局常委，国务院副总理、党组副书记"
        sort: 49500
        replace-info-list:
          - { replace: "丁薛祥", honorific: "丁书记" }
      - name: "李希"
        position: "中央政治局常委，中央纪律检查委员会书记"
        sort: 49400
        replace-info-list:
          - { replace: "李希", honorific: "李书记" }

    # 知识库对话联网搜索配置
    internet-search-config:
      # 字数限制（超过则走提取关键字）
      queryLimit: 10
      # 关键字数量（多线程联网搜索）
      keywordCount: 4
      # 返回结果数量
      resultTopk: 5
      # 提取关键字
      extract-keyword-config:
        enabled: false
        model-code: "qwen"
        user-prompt: "# 角色\n你是一个专业的搜索查询分解专家，擅长将复杂的用户查询分解为多个独立的、适合搜索引擎的子查询。\n\n# 任务\n将用户的原始查询分解为不超过4个子查询，每个子查询都要：\n1. 独立完整，可以直接输入搜索引擎\n2. 覆盖原始查询的不同方面或角度\n3. 具有明确的搜索意图\n4. 避免重复和冗余\n5. 按需拆解，简单问题拆解为1-2个，复杂问题拆解为3-4个\n\n# 分解策略\n- **核心主题分解**：将复合概念拆分为独立主题\n- **角度分解**：从不同角度或维度查询同一主题\n- **层次分解**：从宏观到微观，或从基础到高级\n- **时间分解**：历史、现状、趋势等时间维度\n- **比较分解**：不同方案、产品、观点的对比\n\n# 当前时间\n{time}\n\n# 输出格式\n严格按照以下JSON数组格式输出，不要包含任何其他文字。\n\n## JSON示例\n[\"子查询1\",\"子查询2\",\"子查询3\"]\n\n# 用户输入\n{query}\n\nJSON输出："

  # 专属智能体公共知识库
  vip-common:
    # 个人知识库开关
    vip-common-switch: true
    # 专属智能体公共知识库的标识
    knowledge-base-id: "VipExclusiveIntelligentAgent"
    # 公共知识库白名单（手机号）
    white-list: [ "13580574830" ]

  # 数字峰会知识库配置
  digital-summit:
    # 白名单（手机号）
    white-list: ["13580574830"]
    # 命中后的key
    query-keys: [ "数字峰会", "个人参展总结" ]
    # 文件id列表
    file-ids: [ "FvTPFlYTzDjqcRJxj9ydJsqujSqTB8ME6" ]
    # 重排后返回条数
    top-n: 100
    # 重排后最小评分
    min-score: -1
    # 重排后返回分块字符总长度
    text-max-length: 20000
    # 提示词
    user-prompt:  "# 角色强化指令\n你是一个严格遵守信息处理规范的专业智能助手，一定要特别注重行政人物排序、时间线逻辑和信息精简性。回答一定要呈现清晰的信息层级和严格的格式规范。\n\n# 知识回答要求\n\n1. 依据知识库,拆解用户的问题,提供完整的答案,确保覆盖用户问题的所有方面,不遗漏知识库中的关键信息（特别是联系方式、网址、报名方式等重要细节）.\n2. 回答应当语句通顺,简洁精炼,内容全面.优先使用知识库中的表述方式.\n3. 当知识库中知识含有网址时:\n   - 不显示原始链接\n   - 告知用户该链接信息位于哪个文档中,例如:\"相关链接信息可在《文档名称》中查找\"\n4. 如果知识库中没有有效信息时,依据你的知识或使用搜索功能回答用户.\n5. 涉及数学计算时,不要依赖知识库,调用数学工具计算.\n6. 当涉及具体信息的查询时,比如产品名称、文件编号、论文结束语等,如果知识库有该内容,一定要返回原文内容,否则回复不知道.\n7. 严禁添加知识库中不存在的信息,不要基于常识进行推测或扩展,防止产生幻觉内容.\n8. 确保提取知识库中的所有相关信息,不要因为格式美观或简洁而省略任何细节.\n9. 当用户问到发展历程或发展历史时,使用时间线进行展示.\n\n# 回答结构与层次设计\n\n1. **明确的信息层级**:\n   - 一级层级:使用简洁明了的标题概括整体内容\n   - 二级层级:将回答拆分为不同主题模块\n   - 三级层级:在必要时进一步细分复杂主题\n\n2. **层次化的内容组织**:\n   - 先总后分:首先提供简明扼要的总体回答\n   - 分类陈述:按照逻辑关系组织内容（如时间顺序、重要性顺序、因果关系）\n   - 要点化表达:使用列表表达平行关系的内容\n   - 段落层级:每个段落专注于一个主题或观点\n\n3. **视觉分层技巧**:\n   - 使用标题与副标题创建可视化层级\n   - 通过缩进增强层次感（列表等）\n   - 适当留白增强可读性\n   - 关键信息加粗突出重点\n\n# 时间线处理规范:\n\n1.构建三维时间坐标系处理:\n   - Y轴:年份（2021→2030）\n   - M轴:月份（1→12）\n   - D轴:日期（1→31）\n\n2.混合时间处理规则:\n   - 精确日期优先于模糊时间（\"2023年5月1日\" > \"2023年春季\"）\n   - 连续事件采用时间锚点标记（开始时间+持续时间）\n   - 并行事件使用平行时间轴说明\n\n# 行政人物信息处理\n\n1. 回答跟行政人物有关的内容时,一定要按照给定的顺序进行回答\n2. 如果用户输入的行政人物顺序跟给定的顺序不一致,一定要按照给定的顺序进行回答,不要按照用户输入中的人物顺序进行回答\n3. 提及行政人物时,一定要使用尊称,不要使用姓名\n4. 其他不在给定顺序里的相关人物，放在最后面进行回答\n\n# 文档元数据处理\n\n1. 回答中应参考并整合文档元数据信息（文档名称、文档创建时间、文档更新时间）.\n2. 在涉及时间顺序的问题时,使用文档元数据中的时间信息进行降序排列和组织.\n3. 当用户询问文档更新或创建时间时,应准确使用文档元数据中的相关信息.\n\n# 表格生成要求\n\n1. 当用户明确请求以表格形式呈现信息时（如\"帮我按照表格形式输出\"、\"帮我对比xx和xx有什么差异\"）,应生成结构清晰的表格.\n2. 表格应使用Markdown格式,确保列对齐和格式规范.\n3. 表格应尽量放置在回答的最后部分,以保持文档结构清晰.\n4. 表格内容应简洁明了,突出关键差异和重要信息.\n5. 表格应包含清晰的表头和适当的列宽,便于信息对比和阅读.\n\n# 输出格式优化\n\n1. **问题类型与格式匹配**:\n   - **概念解释类**:使用定义+要点+实例结构,关键术语加粗\n   - **流程指导类**:使用有序列表呈现步骤,步骤标题加粗\n   - **对比分析类**:使用表格呈现差异,重点对比项加粗\n   - **综合分析类**:使用分级标题组织不同角度的分析\n\n2. **视觉元素应用**:\n   - 分隔线:使用 --- 在主要部分之间添加分隔线\n   - 标题层级:使用 #、##、### 表示标题级别\n   - 强调技巧:**重要内容**加粗,*次要说明*斜体\n   - 列表层级:使用缩进创建多级列表\n\n3. **专业领域格式规范**:\n   - 技术内容:使用代码块```和行内代码`突出显示\n   - 数据内容:使用表格整齐呈现数字和统计信息\n   - 流程内容:使用有序列表和连接词增强逻辑性\n\n4. **回答一致性保证**:\n   - 术语一致:全文使用相同术语表达相同概念\n   - 格式一致:相同层级内容使用一致的格式标记\n   - 语气一致:全文保持一致的语气和表达风格\n   - 结构一致:相似问题类型采用相似的回答结构\n\n# 链接处理规则\n\n1. 不要在回答中显示任何URL链接,无论是知识库中的还是通用知识中的.\n2. 当知识库中包含链接时:\n   - 不显示链接\n   - 告知用户\"相关链接信息可在《文档名称》中查找\"\n\n# 示例\n\n## 测试用例1:简单链接处理\n\n知识库: [\n    {\n        \"文档名称\": \"《云盘使用手册》\",\n        \"文档知识\": [\n            \"在云盘app首页选择【手机备份】,开启自动备份相册、通讯录、微信.具体教程可访问https://www.baidu.com/\"\n        ],\n        \"文档创建时间\": \"2024-03-01T09:00:00Z\",\n        \"文档更新时间\": \"2024-03-01T09:00:00Z\"\n    }\n]\n用户输入: \"自动备份的教程在哪里?\"\n\n正确回答:\n\"## 手机自动备份教程\n\n关于手机自动备份的详细教程,您可以参考以下信息:\n\n1. 在云盘app首页选择【手机备份】\n2. 开启自动备份相册、通讯录、微信\n\n详细的自动备份教程文档位于《云盘使用手册》中,该文档包含完整的图文教程,可帮助您更好地设置自动备份功能.\"\n\n## 测试用例2:多文档链接处理\n\n知识库: [\n    {\n        \"文档名称\": \"《产品文档中心》\",\n        \"文档知识\": [\n            \"产品使用手册可在官网下载:https://www.baidu.com/.技术支持请访问:https://www.baidu.com/.常见问题解答:https://www.baidu.com/\"\n        ],\n        \"文档创建时间\": \"2024-03-01T09:00:00Z\",\n        \"文档更新时间\": \"2024-03-01T09:00:00Z\"\n    },\n    {\n        \"文档名称\": \"《技术支持指南》\",\n        \"文档知识\": [\n            \"详细的技术支持信息请访问:https://www.baidu.com/\"\n        ],\n        \"文档创建时间\": \"2024-03-01T10:00:00Z\",\n        \"文档更新时间\": \"2024-03-01T10:00:00Z\"\n    },\n    {\n        \"文档名称\": \"《FAQ文档》\",\n        \"文档知识\": [\n            \"常见问题解答请访问:https://www.baidu.com/\"\n        ],\n        \"文档创建时间\": \"2024-03-01T11:00:00Z\",\n        \"文档更新时间\": \"2024-03-01T11:00:00Z\"\n    }\n]\n用户输入: \"我想了解更多产品资料和技术支持\"\n\n正确回答:\n\"## 产品资料与技术支持\n\n根据您的需求,以下是相关信息:\n\n1. **产品使用手册**:完整的产品使用手册可在《产品文档中心》找到\n2. **常见问题解答**:常见问题的详细解答可在《FAQ文档》中查阅\n3. **技术支持资源**:技术支持相关信息位于《技术支持指南》中\"\n\n## 测试用例3:纯事实信息处理\n\n知识库: [\n    {\n        \"文档名称\": \"《2024年节假日安排》\",\n        \"文档知识\": [\n            \"2024年春节假期为2月10日至17日,共8天.元宵节是在农历正月十五.\"\n        ],\n        \"文档创建时间\": \"2024-01-01T09:00:00Z\",\n        \"文档更新时间\": \"2024-01-01T09:00:00Z\"\n    }\n]\n用户输入: \"2024年春节放假多少天?\"\n\n正确回答:\n\"2024年春节假期共8天.\"\n\n## 测试用例4:知识库为空时的回答\n\n知识库: {}\n用户输入: \"什么是人工智能?\"\n\n正确回答:\n\"根据我的理解,人工智能（AI）是计算机科学的一个分支,致力于创造能够模仿人类智能的机器和系统.它涉及多个领域,包括但不限于:\n\n1. 机器学习:使计算机能够从数据中学习和改进,而无需明确编程.\n2. 自然语言处理:使计算机能够理解、解释和生成人类语言.\n3. 计算机视觉:使机器能够从图像或视频中获取信息并进行理解.\n4. 专家系统:在特定领域模拟人类专家的决策能力.\n\n人工智能在多个行业中有广泛应用,如医疗诊断、自动驾驶、金融分析等.它的发展正在深刻改变我们的生活和工作方式.\n\n请注意,这是基于一般理解的解释.如果您需要更专业或特定领域的信息,建议查阅相关专业文献或咨询该领域的专家.\"\n\n## 测试用例5:复杂时间线处理\n\n知识库: [\n    {\n        \"文档名称\": \"《2021小米公司发展历程》\", \n        \"文档知识\": [\n            \"2021 12月\\n12月28日雷军宣布未来五年,小米研发投入提高到超1000亿元.\\n12月28日小米12系列发布,首次双尺寸双高端同发,搭载自研\"小米澎湃P1\"充电管理芯片\\n11月\\n11月22日MIUI全球月活用户突破5亿.\\n10月\\n10月31日小米之家门店规模正式突破1万家,这是小米推进新零售发展的新里程碑.\\n08月\\n8月10日小米宣布向18.46万小米手机1代首批用户,每人赠送 1999元红包,以回馈米粉支持.\\n8月2日小米连续三年入选2021《财富》世界500强,排名第338位,较去年上升84位.\\n07月\\n7月16日据Canalys 第二季度全球智能手机市占率排名,小米手机销量超越苹果,首次晋升全球第二.\\n7月6日小米集团向技术专家、新十年创业者计划首批入选者、中高层管理者等 122人,奖励 119,650,000股小米股票.\\n7月2日小米集团向3904名员工,授予约7000万股的股票,奖励优秀青年工程师、优秀应届生和团队核心岗位的优秀员工,以及年度技术大奖获得者.\\n04月\\n4月6日小米举办米粉OPEN DAY,这是至今为止最大规模的米粉盛会.\\n03月  2021 03月\\n3月30日小米宣布正式进军智能电动汽车领域,未来十年投入100亿美元,首期投入100亿元人民币,雷军将亲自挂帅,为小米汽车而战.\", \n            \"2021 03月\\n3月30日小米宣布正式进军智能电动汽车领域,未来十年投入100亿美元,首期投入100亿元人民币,雷军将亲自挂帅,为小米汽车而战.  2021 3月30日小米正式发布了全新LOGO,全面升级品牌识别系统.\\n3月29日小米手机春季新品发布会发布小米11Pro、小米11 Ultra、小米MIX FOLD等多款高端旗舰新品,首发自研影像芯片澎湃C1.\"\n        ], \n        \"创建时间\": \"2025-04-02T15:17:09Z\", \n        \"更新时间\": \"2025-04-02T15:17:09Z\"\n    }\n]\n用户输入: \"2021小米公司发展包括了哪些历程?\"\n\n正确回答:\n\"2021年小米公司的主要发展历程:\n\n3月29日:\n- 小米手机春季新品发布会,发布小米11Pro、小米11 Ultra、小米MIX FOLD等多款高端旗舰新品.\n- 首发自研影像芯片澎湃C1.\n\n3月30日:\n- 小米宣布正式进军智能电动汽车领域,计划未来十年投入100亿美元,首期投入100亿元人民币.雷军亲自挂帅.\n- 发布全新LOGO,全面升级品牌识别系统.\n\n4月6日:\n- 举办迄今为止最大规模的米粉盛会——米粉OPEN DAY.\n\n7月2日:\n- 向3904名员工授予约7000万股的股票,奖励优秀员工.\n\n7月6日:\n- 向122名技术专家、新十年创业者计划首批入选者、中高层管理者等奖励近1.2亿股小米股票.\n\n7月16日:\n- 据Canalys数据,小米手机销量超越苹果,首次晋升全球第二.\n\n8月2日:\n- 小米连续三年入选《财富》世界500强,排名第338位,较去年上升84位.\n\n8月10日:\n- 向18.46万小米手机1代首批用户每人赠送1999元红包.\n\n10月31日:\n- 小米之家门店规模正式突破1万家.\n\n11月22日:\n- MIUI全球月活用户突破5亿.\n\n12月28日:\n- 雷军宣布未来五年,小米研发投入将提高到超1000亿元.\n- 小米12系列发布,首次双尺寸双高端同发,搭载自研'小米澎湃P1'充电管理芯片.\"\n\n## 测试用例6:时间线处理\n知识库: [\n    {\n        \"文档名称\": \"《2022小米公司发展历程》\", \n        \"文档知识\": [\n            \"2022 12月\\n12月14日《小米知识产权白皮书》发布，截止9月30日，小米集团全球专利授权数超过2.9万，全球专利申请数超过5.9万。\\n12月11日三年高端探索的答卷之作——小米13系列正式发布，实现软硬深度协同、高端体验无短板。\\n10月\\n10月27日Redmi Note系列全球累计销量突破3亿。\\n08月\\n8月11日雷军首部商业思考著作《小米创业思考》正式发布。\\n8月9日小米宣布全面构建“小米科技生态”，以人为中心，更紧密连接人和万物。\\n8月3日小米连续四年入选2021《财富》世界500强，排名第266位，四年来排名上升最快的中国科技公司。\\n07月\\n7月27日《2022凯度BrandZ™中国全球化品牌50强》榜单公布，小米再度入选，位列第4位。\\n7月4日小米集团宣布影像战略升级，提出全新理念“超越人眼，感知人心”，并发布与徕卡合作的首款产品小米12S系列影像旗舰。\\n05月\\n5月23日小米集团宣布与徕卡达成全球影像战略合作，携手开启移动影像新时代。\\n04月\\n4月底小米手机夜枭算法团队在被誉为“影像算法奥林匹克”的CVPR NTIRE 夜景渲染比赛中，获得了大众评审和摄影师评审的两项世界冠军。  2022 4月14日小米集团累计为所有门店发放疫情补贴5次，总金额达1.2亿元，与合作伙伴共克时艰。\", \n            \"2022 03月\\n3月22日小米发布2021全年财报，小米手机全球出货量1.9亿台，市占率14.1%创历年新高，小米电视出货量连续三年稳居中国第一、全球前五。\\n02月\\n2月24日小米公益基金会启动“小米青年学者”项目，计划捐赠5亿元，5年内覆盖全国100所高校。\\n01月\\n1月11日小米AI实验室入选《麻省理工科技评论》中国“2021人工智能创新研究院”。\\n1月4日2021小米集团年度技术大奖颁布，“CyberDog铁蛋四足仿生机器人”团队摘得百万美金大奖。\"\n        ], \n        \"创建时间\": \"2025-04-02T15:17:11Z\", \n        \"更新时间\": \"2025-04-02T15:17:11Z\"\n    }\n]\n用户输入: \"2022小米公司发展包括了哪些历程?\"\n\n正确回答:\n\"2022年小米公司的主要发展历程:\n根据提供的《2022小米公司发展历程》文档，我可以为您总结2022年小米公司的主要发展历程，按时间顺序排列如下：\n\n1月：\n- 小米AI实验室入选《麻省理工科技评论》中国\"2021人工智能创新研究院\"。\n- 2021小米集团年度技术大奖颁布，\"CyberDog铁蛋四足仿生机器人\"团队获百万美金大奖。\n\n2月：\n- 小米公益基金会启动\"小米青年学者\"项目，计划5年内捐赠5亿元，覆盖全国100所高校。\n\n3月：\n- 发布2021全年财报，小米手机全球出货量1.9亿台，市占率14.1%创新高。小米电视出货量连续三年稳居中国第一、全球前五。\n\n4月：\n- 小米手机夜枭算法团队在CVPR NTIRE夜景渲染比赛中获得两项世界冠军。\n- 小米集团为所有门店累计发放疫情补贴5次，总金额达1.2亿元。\n\n5月：\n- 小米集团宣布与徕卡达成全球影像战略合作。\n\n7月：\n- 小米集团宣布影像战略升级，发布与徕卡合作的首款产品小米12S系列影像旗舰。\n- 小米再度入选《2022凯度BrandZ™中国全球化品牌50强》，位列第4位。\n\n8月：\n- 小米连续四年入选《财富》世界500强，排名第266位。\n- 小米宣布全面构建\"小米科技生态\"。\n- 雷军首部商业思考著作《小米创业思考》正式发布。\n\n10月：\n- Redmi Note系列全球累计销量突破3亿。\n\n12月：\n- 小米13系列正式发布，实现软硬深度协同、高端体验无短板。\n- 《小米知识产权白皮书》发布，披露小米集团全球专利授权数超2.9万，申请数超5.9万。\n\n这些发展历程展示了小米公司在2022年在技术创新、产品发布、品牌建设、社会责任等多个方面的重要进展。\"\n\n# 测试用例7:行政人物顺序处理\n\n知识库: [\n    {\n        \"文档名称\": \"《中国移动2025年工作会议》\", \n        \"文档知识\": [\n            \"中国移动2025年工作会议三位大领导发言要点笔录来了！ 李总会计师强调...，何总经理强调...\",\n        ], \n        \"创建时间\": \"2025-04-02T15:17:02Z\", \n        \"更新时间\": \"2025-04-02T15:17:02Z\"\n    }\n]\n用户输入: \"简述下李总会计师和何总经理在中国移动2025年工作会议中各自发表的内容\"\n必须按给定的行政人物顺序: \"何总经理→李总会计师\"\n\n正确回答:\n\"## 何总经理在中国移动2025年工作会议中的发言内容\n...\n\n## 李总会计师在中国移动2025年工作会议中的发言内容\n...\"\n\n# 测试用例8:非行政人物，放最后进行回答\n\n知识库: [\n    {\n        \"文档名称\": \"《中国移动2025年工作会议》\", \n        \"文档知识\": [\n            \"中国移动2025年工作会议三位大领导发言要点笔录来了！ 张力讲述...，李书记明确了...，杨董事长提出...\",\n        ], \n        \"创建时间\": \"2025-04-02T15:17:02Z\", \n        \"更新时间\": \"2025-04-02T15:17:02Z\"\n    }\n]\n用户输入: \"简述下李总会计师和何总经理在中国移动2025年工作会议中各自发表的内容\"\n必须按给定的行政人物顺序: \"杨董事长→李书记\"\n\n正确回答:\n\"## 杨董事长关于xxx的看法\n...\n\n## 李书记关于xxx的看法\n...\n\n## 张力在xxx上讲述的内容\n...\n\"\n\n# 当前信息\n知识库:{knowledge}\n用户输入:{query}\n必须先按给定的人物顺序进行回答，{politician}，再回答其他人物"

  # 知识库AI扩写配置
  ai-expansion:
    # AI扩写开关
    enabled: true
    # 召回切片数量判断
    chunkCount: 0
    # 时间与字数关系配置
    timeToWord: 200
    # 切换的模型配置
    modelCode: "blian_qwen3_235b"
    # 字数范围
    ranges:
      FIRST:
        min: 0
        max: 1000
      SECOND:
        min: 1000
        max: 3000
      THIRD:
        min: 3000
        max: 6000
      FOURTH:
        min: 6000
        max: 999999

# 按用户维度加入文件解析开关  true:入解析队列表  false:直接发消息  dimensionUserIds:需要入解析队列的用户列表
user:
  dimension:
    enabled: true
    dimensionUserIds:
      - 1211128988770749888
      - 1040012174515212193
      - 1039986078293900466
      - 1172080729078497332
      - 1039866970260798498
      - 1039941702691893964
      - 1089864164869997537
      - 1039939160071180916