# 白名单
white-list:
  # 邮件搜索
  mail-search:
    # 总开关
    enable: true
    # 邮箱后缀
    suffix: "@139.com"
    # 非白名单用户提示
    prompt: "重要邮件提取功能逐步开放中，敬请期待…"
    # 白名单（用户id）
    white-list:
      - "1105420961611623080"
      - "1128732667564408895"
      - "1125978923512143905"
      - "1075667021620396114"
      - "1076042092524486710"
      - "1155878641134477391"
      - "1332432359982690962"
      - "1137865986377154611"
      - "1100591545764405325"
      - "1155142535279484935"
      - "1154956105781362991"
      - "1092372812309774639"
      - "1135267440979910815"
      - "1134484066862375045"
      - "1049319044374651030"
      - "1113749343433277455"
      - "1128968624477787568"
      - "1113749343433277455"
      - "1154956105781363165"
      - "1332677241838249685"
      - "1330862738414841906"
      - "1155905405223190706"
      - "1115610226062172232"
      - "1061811620329283097"
  # 异步保存搜索数据，白名单配置
  async-search-save-data:
    # 总开关
    enable: true
    # 白名单（用户id）
    white-list:
      - "1049319044374651030"
  # 大模型对话VIP用户，第一版需要百炼大模型配置apiKeyVip，否则会报错
  text-model-vip-user:
    - "18142851953"
    - "15019443317"
    - "15989492351"
  # 文本模型编码默认白名单用户
  text-model-code-white-user:
    - "18142851953"
    - "15113838573"
    - "19864394830"
    - "18211572924"
    - "13533867807"

  # 全网搜推荐默认白名单用户
  all-network-search-white-user:

  #送审白名单用户
  audit-white-user:
    - "18142851953"

  # aippt白名单配置
  aippt-white:
    # 总开关
    enable: false
    # 白名单列表（手机号）
    white-list:
      - "18142851953"
      - "15019443317"
      - "15989492351"
    # 支持的渠道列表（助手编码）
    supported-channels:
      - "yunmail"
      - "xiaotian"
