# 笔记对话的配置
note:
  # 笔记搜索配置
  search:
    pre-tags: <keywordsTag>
    post-tags: </keywordsTag>
    # 召回配置
    recall-config:
      # 召回超时时间，单位毫秒（默认10秒）
      await-time: 60000
      # 结果日志拆分打印，每组数量
      log-group-size: 4
      # 【公共知识库】base配置
      common-base:
        # 启用开关，true-打开（默认），false-关闭
        enabled: false
        # 向量大切片数量
        big-split-size: 10
        # 上分片数
        pre-count: 1
        # 下分片数
        next-count: 1
        # 需要合并上下分片开关，true-打开，false-关闭
        enabled-merge-context: false
      # 【个人知识库】base配置
      personal-base:
        # 启用开关，true-打开（默认），false-关闭
        enabled: true
        # 向量大切片数量
        big-split-size: 40
        # 上分片数
        pre-count: 1
        # 下分片数
        next-count: 1
        # 需要合并上下分片开关，true-打开，false-关闭
        enabled-merge-context: false

      # 【公共知识库】切片配置
      common-split:
        # 公共知识库id
        common-base-id: "common"
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false
        # small to big 配置
        small-to-big-config:
          enabled: true
          min-score: 0.1
          top-n: 100
          merge-size: 2

      # 【公共知识库】问答配置
      common-qa:
        # 公共知识库id
        common-base-id: "common"
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【公共知识库】切片的假设性问题配置
      common-split-gqa:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 300
        # 问答对，查询条数
        qa-top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【公共知识库】语义切块配置
      common-gsplit:
        # 公共知识库id
        common-base-id: "common"
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【公共知识库】文档总结配置
      common-summary:
        # 公共知识库id
        common-base-id: "common"
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【公共知识库】text查询配置
      common-text:
        # 公共知识库id
        common-base-id: "common"
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【个人知识库】切片配置
      personal-split:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: true
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false
        # small to big 配置
        small-to-big-config:
          enabled: true
          min-score: 0.1
          top-n: 100
          merge-size: 2

      # 【个人知识库】切片假设性问题配置
      personal-split-gqa:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 300
        # 问答对，查询条数
        qa-top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【个人知识库】语义切片配置
      personal-gsplit:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【个人知识库】文档总结配置
      personal-summary:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【个人知识库】text查询配置
      personal-text:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: true
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【个人知识库】keyword查询配置
      personal-keyword:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false
    # 【向量】算法重排配置
    vector-rerank-config:
      # 批处理大小，默认256
      batch-size: 256
      # 分段内容的最大长度，超出会截断，默认512
      max-length: 1024
      # 结果日志拆分打印，每组数量
      log-group-size: 4
      # 重排返回条数
      top-n: 40
      # 重排后最小评分
      min-score: 0.1
      # 重排后返回分块字符总长度：25600（默认）
      text-max-length: 25600

    # 【关键字】算法重排配置
    keyword-rerank-config:
      # 批处理大小，默认256
      batch-size: 256
      # 分段内容的最大长度，超出会截断，默认512
      max-length: 1024
      # 结果日志拆分打印，每组数量
      log-group-size: 4
      # 重排返回条数
      top-n: 15
      # 重排后最小评分
      min-score: 0.03
      # 重排后返回分块字符总长度：25600（默认）
      text-max-length: 25600

  # 笔记知识库对话配置
  knowledge:
    # 笔记知识库对话
    dialogue:
      # 多路重排开关
      enable-multi-rerank: true

      # 重写配置
      rewrite-config:
        # 重写开关
        enabled: true
        # 重写使用的大模型
        model-code: "qwen_32b"
        # 重写输入内容系统提示
        system-prompt: "你是一个旨在帮助用户更有效检索信息的助手。\n你的主要职责是在用户输入表达不明确的情况下，通过参考#历史对话摘要#和#关键词列表#，对原始问题进行重写。\n你的目标是使问题更加具体和容易被检索，并保持与用户原始意图的一致性\n并且，请不要忽略#原始用户问题#中的内容\n你应该1. 理解背景: 通过阅读历史对话摘要，了解用户之前的对话内容，把握对话的上下文和主题。\n2. 利用关键词: 将关键词融入问题，确保#重写后的问题#包含这些关键词，提高检索的相关性。\n3. 增加细节: 如果用户的问题过于宽泛或模糊，适当添加细节使问题更具体，但不要引入新的概念或信息。\n4. 保持一致性: 确保#重写后的问题#不偏离用户原始的意图或信息需求。\n5. 简洁明了: 保持问题简短而明确，避免冗长或复杂的表述。\n#重写后的问题#只能在#原始用户问题#的基础上增加10-20个字\n#原始用户问题#，#重写后的问题#，#历史对话摘要#，#关键词列表#都不允许出现在#重写后的问题#中\n#历史对话摘要#:{history}\n#关键词列表#:{keywords}"
        # 重写输入内容用户提示
        user-prompt: "#原始用户问题#:{query}\n#重写后的问题#:"
        #问题重写最大字数
        maxLength: 128
        #历史问题数量
        historyNum: 5

      # 关键字提取配置
      keyword-config:
        # 关键字开关
        enabled: true
        # 键词提取方法，支持"tfidf"、"textrank"和"llm"，默认为"tfidf"；"llm"当前版本不可用
        method: "tfidf"
        # 最大提取关键词数量，默认为-1（-1即返回所有关键词）
        maxKeywords: -1
        # 重写输入内容用户提示
        allowPos: [ "n", "nr", "ns", "nt", "nz", "vn" ]
        #最小词长度，默认为2
        minWordLen: 2

      # 召回配置
      recall-config:
        # 召回超时时间，单位毫秒（默认10秒）
        await-time: 60000
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 【公共知识库】base配置
        common-base:
          # 启用开关，true-打开（默认），false-关闭
          enabled: true
          # 向量大切片数量
          big-split-size: 10
          # 上分片数
          pre-count: 1
          # 下分片数
          next-count: 1
          # 需要合并上下分片开关，true-打开，false-关闭
          enabled-merge-context: false
        # 【个人知识库】base配置
        personal-base:
          # 启用开关，true-打开（默认），false-关闭
          enabled: true
          # 向量大切片数量
          big-split-size: 40
          # 上分片数
          pre-count: 1
          # 下分片数
          next-count: 1
          # 需要合并上下分片开关，true-打开，false-关闭
          enabled-merge-context: false

        # 【公共知识库】切片配置
        common-split:
          # 公共知识库id
          common-base-id: "common"
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: true
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 100
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false
          # small to big 配置
          small-to-big-config:
            enabled: true
            min-score: 0.1
            top-n: 100
            merge-size: 2

        # 【公共知识库】问答配置
        common-qa:
          # 公共知识库id
          common-base-id: "common"
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: false
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 100
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false

        # 【公共知识库】切片的假设性问题配置
        common-split-gqa:
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: false
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 300
          # 问答对，查询条数
          qa-top-n: 100
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false

        # 【公共知识库】语义切块配置
        common-gsplit:
          # 公共知识库id
          common-base-id: "common"
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: false
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 100
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false

        # 【公共知识库】文档总结配置
        common-summary:
          # 公共知识库id
          common-base-id: "common"
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: false
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 10
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false

        # 【公共知识库】text查询配置
        common-text:
          # 公共知识库id
          common-base-id: "common"
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: true
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 10
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false

        # 【个人知识库】切片配置
        personal-split:
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: true
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 100
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false
          # small to big 配置
          small-to-big-config:
            enabled: true
            min-score: 0.1
            top-n: 100
            merge-size: 2

        # 【个人知识库】切片假设性问题配置
        personal-split-gqa:
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: false
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 300
          # 问答对，查询条数
          qa-top-n: 100
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false

        # 【个人知识库】语义切片配置
        personal-gsplit:
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: false
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 100
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false

        # 【个人知识库】文档总结配置
        personal-summary:
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: false
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 10
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false

        # 【个人知识库】text查询配置
        personal-text:
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: true
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 10
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false

        # 【个人知识库】keyword查询配置
        personal-keyword:
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: true
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 10
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false

      # 召回配置（总结、建议、发言）
      recall-config-summary:
        # 召回超时时间，单位毫秒（默认10秒）
        await-time: 60000
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 【公共知识库】base配置
        common-base:
          # 启用开关，true-打开（默认），false-关闭
          enabled: true
          # 向量大切片数量
          big-split-size: 10
          # 上分片数
          pre-count: 1
          # 下分片数
          next-count: 1
          # 需要合并上下分片开关，true-打开，false-关闭
          enabled-merge-context: false
        # 【个人知识库】base配置
        personal-base:
          # 启用开关，true-打开（默认），false-关闭
          enabled: true
          # 向量大切片数量
          big-split-size: 80
          # 上分片数
          pre-count: 1
          # 下分片数
          next-count: 1
          # 需要合并上下分片开关，true-打开，false-关闭
          enabled-merge-context: false

        # 【公共知识库】切片配置
        common-split:
          # 公共知识库id
          common-base-id: "common"
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: true
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 100
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false
          # small to big 配置
          small-to-big-config:
            enabled: true
            min-score: 0.1
            top-n: 100
            merge-size: 2

        # 【公共知识库】问答配置
        common-qa:
          # 公共知识库id
          common-base-id: "common"
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: false
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 100
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false

        # 【公共知识库】切片的假设性问题配置
        common-split-gqa:
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: false
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 300
          # 问答对，查询条数
          qa-top-n: 100
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false

        # 【公共知识库】语义切块配置
        common-gsplit:
          # 公共知识库id
          common-base-id: "common"
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: false
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 100
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false

        # 【公共知识库】文档总结配置
        common-summary:
          # 公共知识库id
          common-base-id: "common"
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: false
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 10
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false

        # 【公共知识库】text查询配置
        common-text:
          # 公共知识库id
          common-base-id: "common"
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: true
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 10
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false

        # 【个人知识库】切片配置
        personal-split:
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: true
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 200
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false
          # small to big 配置
          small-to-big-config:
            enabled: true
            min-score: 0.1
            top-n: 200
            merge-size: 2

        # 【个人知识库】切片假设性问题配置
        personal-split-gqa:
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: false
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 300
          # 问答对，查询条数
          qa-top-n: 100
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false

        # 【个人知识库】语义切片配置
        personal-gsplit:
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: false
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 100
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false

        # 【个人知识库】文档总结配置
        personal-summary:
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: false
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 10
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false

        # 【个人知识库】text查询配置
        personal-text:
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: true
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 10
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false

        # 【个人知识库】keyword查询配置
        personal-keyword:
          # 召回开关，true-需要召回（默认），false-不需要召回
          enabled: true
          # 查询最低分数
          min-score: 0.1
          # 查询条数
          top-n: 10
          # 查询权重
          query-weight: 0.0
          # 重评查询权重
          rescore-query-weight: 1.0
          # 结果日志拆分打印，每组数量
          log-group-size: 4
          # 文档标签条件：true-查询，false-不查询（默认）
          label-condition: false

      # 【默认】算法重排配置
      rerank-config:
        # 批处理大小，默认256
        batch-size: 256
        # 分段内容的最大长度，超出会截断，默认512
        max-length: 1024
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 重排返回条数
        top-n: 20
        # 重排后最小评分
        min-score: 0.3
        # 重排后返回分块字符总长度：25600（默认）
        text-max-length: 25600

      # 【默认】算法重排配置（总结、建议、发言）
      rerank-config-summary:
        # 批处理大小，默认256
        batch-size: 256
        # 分段内容的最大长度，超出会截断，默认512
        max-length: 1024
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 重排返回条数
        top-n: 20
        # 重排后最小评分
        min-score: 0.3
        # 重排后返回分块字符总长度：25600（默认）
        text-max-length: 25600

      # 【向量】算法重排配置
      vector-rerank-config:
        # 批处理大小，默认256
        batch-size: 256
        # 分段内容的最大长度，超出会截断，默认512
        max-length: 1024
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 重排返回条数
        top-n: 40
        # 重排后最小评分
        min-score: 0.1
        # 重排后返回分块字符总长度：25600（默认）
        text-max-length: 25600

      # 【向量】算法重排配置（总结、建议、发言）
      vector-rerank-config-summary:
        # 批处理大小，默认256
        batch-size: 256
        # 分段内容的最大长度，超出会截断，默认512
        max-length: 1024
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 重排返回条数
        top-n: 40
        # 重排后最小评分
        min-score: 0.1
        # 重排后返回分块字符总长度：25600（默认）
        text-max-length: 25600

      # 【全文】算法重排配置
      text-rerank-config:
        # 批处理大小，默认256
        batch-size: 256
        # 分段内容的最大长度，超出会截断，默认512
        max-length: 1024
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 重排返回条数
        top-n: 15
        # 重排后最小评分
        min-score: 0.03
        # 重排后返回分块字符总长度：25600（默认）
        text-max-length: 25600

      # 【全文】算法重排配置（总结、建议、发言）
      text-rerank-config-summary:
        # 批处理大小，默认256
        batch-size: 256
        # 分段内容的最大长度，超出会截断，默认512
        max-length: 1024
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 重排返回条数
        top-n: 15
        # 重排后最小评分
        min-score: 0.03
        # 重排后返回分块字符总长度：25600（默认）
        text-max-length: 25600

      # 【关键字】算法重排配置
      keyword-rerank-config:
        # 批处理大小，默认256
        batch-size: 256
        # 分段内容的最大长度，超出会截断，默认512
        max-length: 1024
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 重排返回条数
        top-n: 15
        # 重排后最小评分
        min-score: 0.03
        # 重排后返回分块字符总长度：25600（默认）
        text-max-length: 25600

      # 【关键字】算法重排配置（总结、建议、发言）
      keyword-rerank-config-summary:
        # 批处理大小，默认256
        batch-size: 256
        # 分段内容的最大长度，超出会截断，默认512
        max-length: 1024
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 重排返回条数
        top-n: 15
        # 重排后最小评分
        min-score: 0.03
        # 重排后返回分块字符总长度：25600（默认）
        text-max-length: 25600

      # 重排结果相关性配置
      relevancy-config:
        # 相关性功能开关，true-启用，false-停用
        enabled: false
        # 相关性使用的模型编码
        model-code: "qwen"
        # 相关性用户输入的模板
        user-prompt: "#任务描述\n你是一个判断助手，需要判断每个文本块在回答问题时是否有用。\n\n#输入格式\n- 用户问题：一个问题\n- 文本块列表：{textSize}个文本块\n\n#输出要求\n1. 必须返回一个包含{textSize}个布尔值的列表\n2. True表示这个文本块对回答问题有用\n3. False表示这个文本块对回答问题没用\n4. 返回顺序必须和文本块顺序一致\n\n#示例\n输入：\n用户问题：\"什么是太阳？\"\n文本块列表：[\n    \"太阳是一颗恒星\",\n    \"月亮是地球的卫星\",\n    \"太阳提供光和热\"\n]\n\n输出：\n[True,False,True]\n\n#注意事项\n- 只返回布尔值列表\n- 不要包含任何解释\n- 不要包含任何标点符号\n- 列表长度必须是{textSize}\n\n#实际输入\n用户问题：{query}\n文本块列表：{texts}"
        # 相关性大模型参数配置
        text-model-config:
          # 大模型温度参数
          temperature: 0.0
          # 大模型top_p参数
          top-p: 0.1
          # 随机种子（-1表示不传值）
          seed: 1234

