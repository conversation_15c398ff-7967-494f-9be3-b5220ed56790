# 对话结果引导文案
lead-copy:
  # 创建笔记意图结果标题
  create-note-title: "正在为你打开笔记"
  # 创建语音笔记意图结果标题
  create-voice-note-title: "正在为你打开语音笔记"
  # 创建笔记意图结果标题（英文）
  create-note-title-en: "Opening Notes for you"
  # 创建语音笔记意图结果标题（英文）
  create-voice-note-title-en: "Opening Voice Notes for you"

  # 发邮件意图结果标题
  send-mail-title: "识别到你想要发送邮件，正在前往…"
  # 引导文案对象列表
  copy-list:
    - instruction: picture-generate-text
      type: 1
      prompt-copy: "图片配文是帮助您快速地为图片添加文字的功能，请选择一张需要图片配文处理的图片："
      button-copy: "选择图片"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/picToTextOffice?enableShare=1&token=#ssoToken#
    - instruction: ai-head-sculpture
      type: 1
      prompt-copy: "AI头像可以帮你生成精美的创意头像，请选择一张想制作为AI头像的图片："
      button-copy: "选择图片"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/aiAvatarOffice?enableShare=1&token=#ssoToken#
    - instruction: picture-comic-style
      type: 1
      prompt-copy: "AI漫画风帮你把图片一键转化为漫画风格，请选择一张需要进行AI漫画风处理的图片："
      button-copy: "选择图片"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/aiComicOffice?enableShare=1&token=#ssoToken#
    - instruction: old-photos-repair
      type: 1
      prompt-copy: "老照片修复可以修复你的图片画质与颜色，请选择一张需要进行修复处理的图片："
      button-copy: "选择图片"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/picRepairOffice?enableShare=1&token=#ssoToken#
    - instruction: live-photos
      type: 1
      prompt-copy: "活照片支持将人像图片转化为动画表情，请选择一张需要进行处理的人像图片："
      button-copy: "选择图片"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/picAnimateOffice?enableShare=1&token=#ssoToken#
    - instruction: image-quality-restoration
      type: 1
      prompt-copy: "画质修复让你的模糊图片重现清晰细节"
      button-copy: "选择图片"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/resolutionRepairOffice?enableShare=1&token=#ssoToken#
    - instruction: ai-eliminate
      type: 2
      prompt-copy: "AI消除可为您智能消除图片内的指定内容"
      button-copy: "去使用"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/picEliminateOffice?enableShare=1&token=#ssoToken#
    - instruction: intelligent-cutout
      type: 2
      prompt-copy: "智能抠图可为您智能抠取人像图片，进行背景替换操作"
      button-copy: "去使用"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/faceMattingOffice?enableShare=1&token=#ssoToken#
    - instruction: cloud-camera-external
      type: 3
      prompt-copy: "妙云相机升级为AI写真啦，AI写真帮你制作数字形象，一键生成艺术大片"
      button-copy: "去使用"
      link-url: https://caiyun.feixin.10086.cn:7071/portal/clientDL/index.html?linkUrl=mcloud%3A%2F%2FmcMiniProgram%3Fparams%3DeyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLCJuYW1lIjoi5aaZ5LqR55u45py6IiwibWluaVR5cGUiOiIxIn0%3D
    - instruction: cloud-camera-within
      type: 3
      prompt-copy: "妙云相机升级为AI写真啦，AI写真帮你制作数字形象，一键生成艺术大片"
      button-copy: "去使用"
      link-url: mcloud://mcMiniProgram?params=eyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLAoibmFtZSI6IuWwj+eoi+W6jyIsCiJtaW5pVHlwZSI6IjEiLAoibWluaVBhdGgiOiJwYWdlcy9ob21lL21haW4iLAoibWluaVVybCI6IiIsCiJtaW5pTGlua1R5cGUiOiIxIn0=
    - instruction: ai-photography-external
      type: 3
      prompt-copy: "AI写真帮你制作数字形象，一键生成艺术大片"
      button-copy: "去使用"
      link-url: https://caiyun.feixin.10086.cn:7071/portal/clientDL/index.html?linkUrl=mcloud%3A%2F%2FmcMiniProgram%3Fparams%3DeyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLCJuYW1lIjoi5aaZ5LqR55u45py6IiwibWluaVR5cGUiOiIxIn0%3D
    - instruction: ai-photography-within
      type: 3
      prompt-copy: "AI写真帮你制作数字形象，一键生成艺术大片"
      button-copy: "去使用"
      link-url: mcloud://mcMiniProgram?params=eyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLAoibmFtZSI6IuWwj+eoi+W6jyIsCiJtaW5pVHlwZSI6IjEiLAoibWluaVBhdGgiOiJwYWdlcy9ob21lL21haW4iLAoibWluaVVybCI6IiIsCiJtaW5pTGlua1R5cGUiOiIxIn0=
    - instruction: create-note
      type: 4
      prompt-copy: "为你找到笔记功能，快去记录吧！"
      button-copy: "去记录"
      prompt-copy-en: "Found the Notes feature, start jotting down now!"
      button-copy-en: "Go"
      link-url:
    - instruction: create-voice-note
      type: 4
      prompt-copy: "为你找到语音笔记功能，快去记录吧！"
      button-copy: "去记录"
      prompt-copy-en: "Found the Voice Notes feature, start recording now!"
      button-copy-en: "Go"
      link-url:
    - instruction: ai-expansion-map
      type: 2
      prompt-copy: "AI扩图帮你一键拓展图片，让创意无限延伸"
      button-copy: "去使用"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/picExpandOffice?enableShare=1&token=#ssoToken#&sourceId=170066
    - instruction: one-click-puzzle
      type: 2
      prompt-copy: "一键拼图，九图齐发，让你的朋友圈更精彩"
      button-copy: "去使用"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/wechat9picOffice?enableShare=1&token=#ssoToken#&sourceId=170067
    - instruction: send-mail
      type: 4
      prompt-copy: "识别到你想要发邮件，推荐你使用发邮件功能"
      button-copy: "发邮件"
      link-url:
    - instruction: appearance-prediction
      type: 2
      prompt-copy: "宝宝长相预测，帮你预测和TA的宝宝长什么样？"
      button-copy: "开始预测"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/babyForecastOffice
    - instruction: baby-time-machine
      type: 2
      prompt-copy: "宝宝时光机，帮你提前看宝宝长大的样子"
      button-copy: "立即体验"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/babyGrowthOffice
    - instruction: ai-emoticon
      type: 2
      prompt-copy: "AI表情包，打造你的专属表情包"
      button-copy: "去使用"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/aiEmojiOffice
    - instruction: smart-fake-check
      type: 1
      prompt-copy: "智能鉴别，帮你快速识别图片是否AI生成"
      button-copy: "去使用"
      link-url:
    - instruction: knowledge_entrance
      type: 7
      prompt-copy: "知识库功能上线啦，导入你的文档完成解析，完成你的知识库搭建"
      button-copy: "去使用"
      link-url: mcloud://main/aiAssistant?params=ewogICAgInBhcmFtcyI6IHsKICAgICAgICAianVtcENvbmZpZyI6IHsKICAgICAgICAgICAgInRhcmdldFVybCI6ICJwYWdlcyUyRmtub3dsZWRnZUJhc2UlMkZpbmRleCIKICAgICAgICB9CiAgICB9Cn0=
      image-url: https://yun.mcloud.139.com/aiassistant/static/knowledge/leadcopy.png
      icon-url: https://yun.mcloud.139.com/aiassistant/static/knowledge/icon.png
    # 知识库旧底座
    - instruction: knowledge_entrance_0
      type: 7
      prompt-copy: "知识库功能上线啦，导入你的文档完成解析，完成你的知识库搭建"
      button-copy: "去使用"
      link-url: https://test.yun.139.com/viewtest/ai-search-start/?frompage=1
      image-url: https://yun.mcloud.139.com/aiassistant/static/knowledge/leadcopy.png
      icon-url: https://yun.mcloud.139.com/aiassistant/static/knowledge/icon.png
    # 搜索知识库资源（没有知识库）
    - instruction: search-knowledge-base-resource-no-knowledge
      type: 7
      prompt-copy: "您还没有创建知识库哦"
      button-copy: "导入文件 创建知识库"
      link-url: https://yun.139.com/ai-search-start/?frompage=1
      image-url: https://yun.mcloud.139.com/aiassistant/static/knowledge/leadcopy.png
      icon-url: https://yun.mcloud.139.com/aiassistant/static/knowledge/icon.png
    - instruction: ai-photo-edit
      type: 1
      prompt-copy: "AI改图支持文字指令快速修图，释放创作灵感。"
      button-copy: "选择图片"
      link-url:
  # 引导文案对象列表
  keyword-list:
    - instruction: cloud-camera
      keywords:
        - "妙云相机"
        - "妙云"
        - "数字分身"
        - "数字写真"
        - "数字形象"
        - "艺术照"
        - "艺术形象"
        - "艺术照片"
        - "miao云相机"
        - "妙云xiangji"
        - "妙云xiang机"
        - "miaoyun相机"
        - "miaoyunxiangji"
    - instruction: ai-photography
      keywords:
        - "AI写真"
        - "写真"
        - "艺术写真"
        - "xiezhen"
        - "AIxiezhen"
        - "xie真"
        - "AIxie真"
  # 文本工具推荐引导文案(036文本工具意图)
  text-tool-list:
    - key: '爆款文案'
      type: 1
      prompt-copy: '输入文章主题，为你生成一篇网络爆文，“如周末游记”'
      button-copy: '去使用'
      link-name: '爆款文案'
      prompt-code: 'HIGH_READING_ARTICLES'
      input-box-copy: '请在此输入文章主题，如“周未游记”'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Hot_copywriting.png
    - key: '创意标题'
      type: 1
      prompt-copy: '输入要生成创意标题的内容，帮你快速生成爆款标题'
      button-copy: '去使用'
      link-name: '创意标题'
      prompt-code: 'SEED_PLANTING_TITLE'
      input-box-copy: '请输入要生成创意标题的内容'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Creative_headlines.png
    - key: '内容润色'
      type: 1
      prompt-copy: '输入需要润色的内容或选择一个文件，帮你将内容变得高大上'
      button-copy: '去使用'
      link-name: '内容润色'
      prompt-code: 'CONTENT_POLISHING'
      input-box-copy: '请输入或复制要润色的内容'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Content_polishing.png
    - key: '总结概括'
      type: 1
      prompt-copy: '输入需要总结的内容或选择一个文件，帮你提炼重点，梳理清晰'
      button-copy: '去使用'
      link-name: '总结概括'
      prompt-code: 'SUMMARY_SYNTHESIS'
      input-box-copy: '请输入或复制要总结概括的内容'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Summary_outline.png
    - key: '写工作计划'
      type: 1
      prompt-copy: '简要描述工作任务，帮你制定计划，如“推广移动电源”'
      button-copy: '去使用'
      link-name: '写工作计划'
      prompt-code: 'WRITE_WORK_PLAN'
      input-box-copy: '请简要描述你的工作任务'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Career_planning.png
    - key: '语法校对'
      type: 1
      prompt-copy: '输入你需要校对的内容或选择一个文件，帮你检查修正内容错误'
      button-copy: '去使用'
      link-name: '语法校对'
      prompt-code: 'FULL_TEXT_ERROR_CORRECTION'
      input-box-copy: '请输入或复制要校对的内容'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Grammar_proofreading.png
    - key: 'PPT大纲'
      type: 1
      prompt-copy: '输入要写的PPT主题，帮你生成对应的PPT大纲，如“工作竞聘”'
      button-copy: '去使用'
      link-name: 'PPT大纲'
      prompt-code: 'PPT_OUTLINE'
      input-box-copy: '请输入要写的PPT主题'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/PPT_outline.png
    - key: '职业规划'
      type: 1
      prompt-copy: '输入你感兴趣的职业名称，帮你进行职业规划，如“产品经理”'
      button-copy: '去使用'
      link-name: '职业规划'
      prompt-code: 'CAREER_PLAN'
      input-box-copy: '请输入职业名称，如“产品经理”'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Career_planning.png
    - key: '广告创意'
      type: 1
      prompt-copy: '输入产品或业务的名称，帮你提供广告灵感，如“移动电源”'
      button-copy: '去使用'
      link-name: '广告创意'
      prompt-code: 'ADVERTISING_CREATIVITY'
      input-box-copy: '请输入产品或业务的名称'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Advertising_ideas.png
    - key: '营销方案'
      type: 1
      prompt-copy: '输入产品或业务的名称，帮你定制营销方案，如“点心”'
      button-copy: '去使用'
      link-name: '营销方案'
      prompt-code: 'SMART_MARKETING'
      input-box-copy: '请输入产品或业务的名称'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Marketing_proposals.png
    - key: '商品好评'
      type: 1
      prompt-copy: '输入商品名称，帮你解决好物点评难题'
      button-copy: '去使用'
      link-name: '商品好评'
      prompt-code: 'POSITIVE_REVIEW_HELPER'
      input-box-copy: '请输入商品名称'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Product_reviews.png
    - key: '会议纪要'
      type: 1
      prompt-copy: '输入或复制你的会议内容，快速帮你生成专业的会议纪要'
      button-copy: '去使用'
      link-name: '会议纪要'
      prompt-code: 'MINUTES_ORGANIZATION'
      input-box-copy: '请输入或复制你的会议内容'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Meeting_minutes.png

# 对话结果引导文案
lead-copy-v2:
  # 创建笔记意图结果标题
  create-note-title: "正在为你打开笔记"
  # 创建语音笔记意图结果标题
  create-voice-note-title: "正在为你打开语音笔记"
  # 创建笔记意图结果标题（英文）
  create-note-title-en: "Opening Notes for you"
  # 创建语音笔记意图结果标题（英文）
  create-voice-note-title-en: "Opening Voice Notes for you"

  # 发邮件意图结果标题
  send-mail-title: "识别到你想要发送邮件，正在前往…"
  # 引导文案对象列表
  copy-list:
    - instruction: picture-generate-text
      type: 1
      prompt-copy: "图片配文是帮助您快速地为图片添加文字的功能，请选择一张需要图片配文处理的图片："
      button-copy: "选择图片"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/picToTextOffice?enableShare=1&token=#ssoToken#
    - instruction: ai-head-sculpture
      type: 1
      prompt-copy: "AI头像可以帮你生成精美的创意头像，请选择一张想制作为AI头像的图片："
      button-copy: "选择图片"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/aiAvatarOffice?enableShare=1&token=#ssoToken#
    - instruction: picture-comic-style
      type: 1
      prompt-copy: "AI漫画风帮你把图片一键转化为漫画风格，请选择一张需要进行AI漫画风处理的图片："
      button-copy: "选择图片"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/aiComicOffice?enableShare=1&token=#ssoToken#
    - instruction: old-photos-repair
      type: 1
      prompt-copy: "老照片修复可以修复你的图片画质与颜色，请选择一张需要进行修复处理的图片："
      button-copy: "选择图片"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/picRepairOffice?enableShare=1&token=#ssoToken#
    - instruction: live-photos
      type: 2
      prompt-copy: "活照片支持将人像图片转化为动画表情，请选择一张需要进行处理的人像图片："
      button-copy: "去使用"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/picAnimateOffice?enableShare=1&token=#ssoToken#
    - instruction: image-quality-restoration
      type: 1
      prompt-copy: "画质修复让你的模糊图片重现清晰细节"
      button-copy: "选择图片"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/resolutionRepairOffice?enableShare=1&token=#ssoToken#
    - instruction: ai-eliminate
      type: 2
      prompt-copy: "AI消除可为您智能消除图片内的指定内容"
      button-copy: "去使用"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/picEliminateOffice?enableShare=1&token=#ssoToken#
    - instruction: intelligent-cutout
      type: 1
      prompt-copy: "智能抠图可为您智能抠取人像图片，进行背景替换操作"
      button-copy: "选择图片"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/faceMattingOffice?enableShare=1&token=#ssoToken#
    - instruction: cloud-camera-external
      type: 3
      prompt-copy: "妙云相机升级为AI写真啦，AI写真帮你制作数字形象，一键生成艺术大片"
      button-copy: "去使用"
      link-url: https://caiyun.feixin.10086.cn:7071/portal/clientDL/index.html?linkUrl=mcloud%3A%2F%2FmcMiniProgram%3Fparams%3DeyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLCJuYW1lIjoi5aaZ5LqR55u45py6IiwibWluaVR5cGUiOiIxIn0%3D
    - instruction: cloud-camera-within
      type: 3
      prompt-copy: "妙云相机升级为AI写真啦，AI写真帮你制作数字形象，一键生成艺术大片"
      button-copy: "去使用"
      link-url: mcloud://mcMiniProgram?params=eyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLAoibmFtZSI6IuWwj+eoi+W6jyIsCiJtaW5pVHlwZSI6IjEiLAoibWluaVBhdGgiOiJwYWdlcy9ob21lL21haW4iLAoibWluaVVybCI6IiIsCiJtaW5pTGlua1R5cGUiOiIxIn0=
    - instruction: ai-photography-external
      type: 3
      prompt-copy: "AI写真帮你制作数字形象，一键生成艺术大片"
      button-copy: "去使用"
      link-url: https://caiyun.feixin.10086.cn:7071/portal/clientDL/index.html?linkUrl=mcloud%3A%2F%2FmcMiniProgram%3Fparams%3DeyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLCJuYW1lIjoi5aaZ5LqR55u45py6IiwibWluaVR5cGUiOiIxIn0%3D
    - instruction: ai-photography-within
      type: 3
      prompt-copy: "AI写真帮你制作数字形象，一键生成艺术大片"
      button-copy: "去使用"
      link-url: mcloud://mcMiniProgram?params=eyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLAoibmFtZSI6IuWwj+eoi+W6jyIsCiJtaW5pVHlwZSI6IjEiLAoibWluaVBhdGgiOiJwYWdlcy9ob21lL21haW4iLAoibWluaVVybCI6IiIsCiJtaW5pTGlua1R5cGUiOiIxIn0=
    - instruction: create-note
      type: 4
      prompt-copy: "为你找到笔记功能，快去记录吧！"
      button-copy: "去记录"
      prompt-copy-en: "Found the Notes feature, start jotting down now!"
      button-copy-en: "Go"
      link-url:
    - instruction: create-voice-note
      type: 4
      prompt-copy: "为你找到语音笔记功能，快去记录吧！"
      button-copy: "去记录"
      prompt-copy-en: "Found the Voice Notes feature, start recording now!"
      button-copy-en: "Go"
      link-url:
    - instruction: create-voice-note-intelligent
      type: 1
      prompt-copy: "点击开始录音"
      button-copy: "会议录音"
      link-url: "https://test.yun.139.com/viewtest/ai-helper-lingxi/pages/notes/audioRecord?noteTitle="
    - instruction: ai-expansion-map
      type: 1
      prompt-copy: "AI扩图帮你一键拓展图片，让创意无限延伸"
      button-copy: "选择图片"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/picExpandOffice?enableShare=1&token=#ssoToken#&sourceId=170066
    - instruction: one-click-puzzle
      type: 2
      prompt-copy: "一键拼图，九图齐发，让你的朋友圈更精彩"
      button-copy: "去使用"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/wechat9picOffice?enableShare=1&token=#ssoToken#&sourceId=170067
    - instruction: send-mail
      type: 4
      prompt-copy: "识别到你想要发邮件，推荐你使用发邮件功能"
      button-copy: "发邮件"
      link-url:
    - instruction: appearance-prediction
      type: 2
      prompt-copy: "宝宝长相预测，帮你预测和TA的宝宝长什么样？"
      button-copy: "开始预测"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/babyForecastOffice?enableShare=1&token=#ssoToken#&sourceId=170163
    - instruction: baby-time-machine
      type: 2
      prompt-copy: "宝宝时光机，帮你提前看宝宝长大的样子"
      button-copy: "立即体验"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/babyGrowthOffice?enableShare=1&token=#ssoToken#&sourceId=170164
    - instruction: ai-emoticon
      type: 2
      prompt-copy: "AI表情包，打造你的专属表情包"
      button-copy: "去使用"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/aiEmojiOffice?enableShare=1&token=#ssoToken#&sourceId=170165
    - instruction: smart-fake-check
      type: 1
      prompt-copy: "智能鉴别，帮你快速识别图片是否AI生成"
      button-copy: "去使用"
      link-url:
    - instruction: knowledge_entrance
      type: 7
      prompt-copy: "知识库功能上线啦，导入你的文档完成解析，完成你的知识库搭建"
      button-copy: "去使用"
      link-url: mcloud://main/aiAssistant?params=ewogICAgInBhcmFtcyI6IHsKICAgICAgICAianVtcENvbmZpZyI6IHsKICAgICAgICAgICAgInRhcmdldFVybCI6ICJwYWdlcyUyRmtub3dsZWRnZUJhc2UlMkZpbmRleCIKICAgICAgICB9CiAgICB9Cn0=
      image-url: https://yun.mcloud.139.com/aiassistant/static/knowledge/leadcopy.png
      icon-url: https://yun.mcloud.139.com/aiassistant/static/knowledge/icon.png
    # 知识库旧底座
    - instruction: knowledge_entrance_0
      type: 7
      prompt-copy: "知识库功能上线啦，导入你的文档完成解析，完成你的知识库搭建"
      button-copy: "去使用"
      link-url: https://test.yun.139.com/viewtest/ai-search-start/?frompage=1
      image-url: https://yun.mcloud.139.com/aiassistant/static/knowledge/leadcopy.png
      icon-url: https://yun.mcloud.139.com/aiassistant/static/knowledge/icon.png
    # 搜索知识库资源（没有知识库）
    - instruction: search-knowledge-base-resource-no-knowledge
      type: 7
      prompt-copy: "您还没有创建知识库哦"
      button-copy: "导入文件 创建知识库"
      link-url: https://yun.139.com/ai-search-start/?frompage=1
      image-url: https://yun.mcloud.139.com/aiassistant/static/knowledge/leadcopy.png
      icon-url: https://yun.mcloud.139.com/aiassistant/static/knowledge/icon.png
    - instruction: ai-photo-edit
      type: 1
      prompt-copy: "AI改图支持文字指令快速修图，释放创作灵感。"
      button-copy: "选择图片"
      link-url:
    # 以下为云手机的意图处理(30001~30009)
    - instruction: cloud-phone-send-sms
      type: 2
      prompt-copy: "发送短信给指定联系人、号码"
      button-copy: "发短信"
      link-url: cloudphone://app/ai/intent?intention=发短信{entityParams}
    - instruction: cloud-phone-call
      type: 2
      prompt-copy: "拨打电话给指定联系人、号码"
      button-copy: "打电话"
      link-url: cloudphone://app/ai/intent?intention=打电话{entityParams}
    - instruction: cloud-phone-open-app
      type: 2
      prompt-copy: "通过对话调起目标应用"
      button-copy: "打开APP"
      link-url: cloudphone://app/ai/intent?intention=打开APP{entityParams}
    - instruction: cloud-phone-book-ticket
      type: 2
      prompt-copy: "通过对话查询机票"
      button-copy: "查机票"
      link-url: cloudphone://app/ai/intent?intention=订机票{entityParams}
    - instruction: cloud-phone-film-and-tv
      type: 2
      prompt-copy: "通过对话搜索视频"
      button-copy: "找影视剧"
      link-url: cloudphone://app/ai/intent?intention=找影视剧{entityParams}
    - instruction: cloud-phone-listen-book
      type: 2
      prompt-copy: "通过对话搜书、搜小说、搜相声、搜评书等"
      button-copy: "听书"
      link-url: cloudphone://app/ai/intent?intention=听书{entityParams}
    - instruction: cloud-phone-mobile-charges
      type: 2
      prompt-copy: "通过对话查询话费"
      button-copy: "查话费"
      link-url: cloudphone://app/ai/intent?intention=查话费{entityParams}
    - instruction: cloud-phone-data-usage
      type: 2
      prompt-copy: "通过对话查询流量"
      button-copy: "查流量"
      link-url: cloudphone://app/ai/intent?intention=查流量{entityParams}
    - instruction: cloud-phone-query-hotel
      type: 2
      prompt-copy: "通过对话查询酒店"
      button-copy: "查酒店"
      link-url: cloudphone://app/ai/intent?intention=查酒店{entityParams}
    - instruction: cloud-phone-map-navigation
      type: 2
      prompt-copy: "通过对话地图导航"
      button-copy: "地图导航"
      link-url: cloudphone://app/ai/intent?intention=地图导航{entityParams}
    - instruction: cloud-phone-download-or-update-app
      type: 2
      prompt-copy: "通过对话下载/更新应用"
      button-copy: "下载/更新应用"
      link-url: cloudphone://app/ai/intent?intention=下载/更新应用{entityParams}
  # 引导文案对象列表
  keyword-list:
    - instruction: cloud-camera
      keywords:
        - "妙云相机"
        - "妙云"
        - "数字分身"
        - "数字写真"
        - "数字形象"
        - "艺术照"
        - "艺术形象"
        - "艺术照片"
        - "miao云相机"
        - "妙云xiangji"
        - "妙云xiang机"
        - "miaoyun相机"
        - "miaoyunxiangji"
    - instruction: ai-photography
      keywords:
        - "AI写真"
        - "写真"
        - "艺术写真"
        - "xiezhen"
        - "AIxiezhen"
        - "xie真"
        - "AIxie真"
  # 文本工具推荐引导文案(036文本工具意图)
  text-tool-list:
    - key: '爆款文案'
      code: '036008'
      type: 1
      prompt-copy: '输入文章主题，为你生成一篇网络爆文，“如周末游记”'
      button-copy: '去使用'
      link-name: '爆款文案'
      prompt-code-list:
        - prompt-name: '爆款文案'
          prompt-code: 'HIGH_READING_ARTICLES'
      input-box-copy: '请在此输入文章主题，如“周未游记”'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Hot_copywriting.png
    - key: '创意标题'
      code: '036009'
      type: 1
      prompt-copy: '输入要生成创意标题的内容，帮你快速生成爆款标题'
      button-copy: '去使用'
      link-name: '创意标题'
      prompt-code-list:
        - prompt-name: '创意标题'
          prompt-code: 'SEED_PLANTING_TITLE'
      input-box-copy: '请输入要生成创意标题的内容'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Creative_headlines.png
    - key: '内容润色'
      code: '036010'
      type: 1
      prompt-copy: '输入需要润色的内容或选择一个文件，帮你将内容变得高大上'
      button-copy: '去使用'
      link-name: '内容润色'
      prompt-code-list:
        - prompt-name: '内容润色'
          prompt-code: 'CONTENT_POLISHING'
      input-box-copy: '请输入或复制要润色的内容'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Content_polishing.png
    - key: '总结概括'
      code: '036011'
      type: 1
      prompt-copy: '输入需要总结的内容或选择一个文件，帮你提炼重点，梳理清晰'
      button-copy: '去使用'
      link-name: '总结概括'
      prompt-code-list:
        - prompt-name: '总结概括'
          prompt-code: 'SUMMARY_SYNTHESIS'
      input-box-copy: '请输入或复制要总结概括的内容'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Summary_outline.png
    - key: '写工作计划'
      code: '036012'
      type: 1
      prompt-copy: '简要描述工作任务，帮你制定计划，如“推广移动电源”'
      button-copy: '去使用'
      link-name: '写工作计划'
      prompt-code-list:
        - prompt-name: '写工作计划'
          prompt-code: 'WRITE_WORK_PLAN'
      input-box-copy: '请简要描述你的工作任务'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Career_planning.png
    - key: '语法校对'
      code: '036013'
      type: 1
      prompt-copy: '输入你需要校对的内容或选择一个文件，帮你检查修正内容错误'
      button-copy: '去使用'
      link-name: '语法校对'
      prompt-code-list:
        - prompt-name: '语法校对'
          prompt-code: 'FULL_TEXT_ERROR_CORRECTION'
      input-box-copy: '请输入或复制要校对的内容'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Grammar_proofreading.png
    - key: '职业规划'
      code: '036014'
      type: 1
      prompt-copy: '输入你感兴趣的职业名称，帮你进行职业规划，如“产品经理”'
      button-copy: '去使用'
      link-name: '职业规划'
      prompt-code-list:
        - prompt-name: '职业规划'
          prompt-code: 'CAREER_PLAN'
      input-box-copy: '请输入职业名称，如“产品经理”'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Career_planning.png
    - key: '广告创意'
      code: '036015'
      type: 1
      prompt-copy: '输入产品或业务的名称，帮你提供广告灵感，如“移动电源”'
      button-copy: '去使用'
      link-name: '广告创意'
      prompt-code-list:
        - prompt-name: '广告创意'
          prompt-code: 'ADVERTISING_CREATIVITY'
      input-box-copy: '请输入产品或业务的名称'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Advertising_ideas.png
    - key: '营销方案'
      code: '036016'
      type: 1
      prompt-copy: '输入产品或业务的名称，帮你定制营销方案，如“点心”'
      button-copy: '去使用'
      link-name: '营销方案'
      prompt-code-list:
        - prompt-name: '营销方案'
          prompt-code: 'SMART_MARKETING'
      input-box-copy: '请输入产品或业务的名称'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Marketing_proposals.png
    - key: '商品好评'
      code: '036017'
      type: 1
      prompt-copy: '输入商品名称，帮你解决好物点评难题'
      button-copy: '去使用'
      link-name: '商品好评'
      prompt-code-list:
        - prompt-name: '商品好评'
          prompt-code: 'POSITIVE_REVIEW_HELPER'
      input-box-copy: '请输入商品名称'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Product_reviews.png
    ##以下配置为特殊AI工具
    - key: 'AI生成PPT'
      code: '036001'
      type: 1
      prompt-copy: '请您输入创作主题，或者导入相应的文档给我们提供创作参考'
      button-copy: '确定'
      link-name: 'AI生成PPT'
      prompt-code-list:
        - prompt-name: 'AI生成PPT'
          prompt-code: ''
      input-box-copy: '输入您想创作的PPT主题'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Ai_ppt_outline.png
    - key: 'AI生成会议纪要'
      code: '036002'
      type: 1
      prompt-copy: 'AI助手帮你完成会议纪要'
      button-copy: '确定'
      link-name: 'AI生成会议纪要'
      prompt-code-list:
        - prompt-name: '通用模板'
          prompt-code: 'MINUTES_ORGANIZATION_GENERAL'
          prompt-remark: '智能分段总结会议摘要'
          prompt-background-url: 'https://yun.mcloud.139.com/aiassistant/static/tools/Ai_meeting_minutes1.svg'
          prompt-desc-list: [ '会议时间：','参会人：','参会地点：','参会主题：' ]
        - prompt-name: '会议待办纪要'
          prompt-code: 'MINUTES_ORGANIZATION_TODO'
          prompt-remark: '智能总结会议内容与待办'
          prompt-background-url: 'https://yun.mcloud.139.com/aiassistant/static/tools/Ai_meeting_minutes2.svg'
          prompt-desc-list: [ '会议时间：','参会人：','参会主题：','待办事项：' ]
        - prompt-name: '问答纪要'
          prompt-code: 'MINUTES_ORGANIZATION_QA'
          prompt-remark: '问答内容整理，适用于访谈'
          prompt-background-url: 'https://yun.mcloud.139.com/aiassistant/static/tools/Ai_meeting_minutes3.svg'
          prompt-desc-list: [ '会议时间：','参会人：','问答实录：','问：','答：' ]
        - prompt-name: '调研纪要'
          prompt-code: 'MINUTES_ORGANIZATION_SURVERY'
          prompt-remark: '智能总结核心业务信息'
          prompt-background-url: 'https://yun.mcloud.139.com/aiassistant/static/tools/Ai_meeting_minutes4.svg'
          prompt-desc-list: [ '会议时间：','参会人：','参会主题：','关键数据：' ]
      input-box-copy: '输入你想总结的会议内容'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Ai_meeting_minutes.png
    - key: 'AI编程'
      code: '036003'
      type: 1
      prompt-copy: "AI代码助手帮您编程"
      button-copy: "确定"
      link-name: "AI编程"
      prompt-code-list:
        - prompt-mame: 'AI编程'
          prompt-code: 'AI_CODER_ASSISTANT'
      input-box-copy: '输入你的想法和要求（建议说明编程语言）'
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Ai_coder_assistant.png
    - key: 'AI识图拍照解题'
      code: '036004'
      type: 1
      prompt-copy: "为您推荐以下功能"
      button-copy: ""
      link-name: "AI相机"
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Ai_camera.png
    - key: 'AI识图拍照翻译'
      code: '036005'
      type: 1
      prompt-copy: "为您推荐以下功能"
      button-copy: ""
      link-name: "AI相机"
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Ai_camera.png
    - key: 'AI识图拍照问答'
      code: '036006'
      type: 1
      prompt-copy: "为您推荐以下功能"
      button-copy: ""
      link-name: "AI相机"
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Ai_camera.png
    - key: '图书快速阅读'
      code: '036007'
      type: 2
      prompt-copy: "为您推荐以下功能"
      button-copy: ""
      link-name: "图书快速阅读"
      icon-url: https://yun.mcloud.139.com/aiassistant/static/tools/Quick_read.png
      link-url: 'https://test.yun.139.com/viewtest/ai-helper-dev/pages/minutes/quick-read-list'
