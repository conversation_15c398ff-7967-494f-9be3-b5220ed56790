# 推荐提示词模板
recommend-prompt-template:
  #默认统一追加提示词
  default-append-prompt: ''
  # 推荐提问语句提示词模板
  query-template:
    model-code: "qwen_32b"
    template: 'Human当前问题是: %s\n请帮我预测Human接下来最可能提出的三个简洁的问题，每个问题不少于6个字，但不超过15个字符，问题必须积极、合法、政治正确。\n \n约束条件：\n- 不要翻译Human的提问。\n- 每个问题结尾必须有标点符号。\n- 问题不需要携带如"我"，"你"等主语。\n- 问题应与Human当前问题问题密切相关。\n- 问题不能推荐与Human当前问题的类似的语句。\n- 从你的角度出发，考虑这些问题人工智能助手是否可以回答的。\n- 必须用中文输出三个简洁问题，不要超过15个字，严格按照以下JSON数组格式输出：["问题1","问题2","问题3"]\n \n#示例\nquestion1: "为什么牛奶需要冷藏保存？"\n问题: ["牛奶冷藏能保存多久？","冷藏对牛奶营养有影响吗？","怎么才能让牛奶保存更久？"]\n \nquestion2: "你吃晚饭了吗？请翻译成俄文"\n问题: ["能翻译成其他语言吗？", "怎么快速学习俄语？", "俄语有什么特点？"]\n \nquestion3: "这句话的原文是哪种语言？"\n问题: ["这个语言的字母系统是怎样的？", "这个短语是在哪个文化中流行起来的呢", "请告诉我更多关于该语言的细节。"] \n \nquestions:'

  # 多意图推荐提示词模板
  intention-template:
    model-code: "qwen_32b"
    #如果使用统一模板，template-list模板不用，但是template-list对应intention配置copy的还是需要使用
    unified-template: '# 系统设定\n你是一个帮助用户进行提问的智能助手，根据用户意图生成功能指令或者相似问题，你不会回复用户的问题或者会帮助用户执行指令或者任务，只会根据下面的<任务>设定和<条件>限定完成自己的工作。\n# 任务\n用户输入的问题可能包含【其他、普通对话意图、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、智能美颜、AI写真、搜图片、搜云盘文档、搜视频、搜音频、搜文件夹、搜笔记、搜综合、AI写真大屏版、活动搜索、功能搜索、搜索发现、搜索圈子、创建普通笔记、AI扩图、朋友圈9图、创建语音笔记、搜邮件、宝宝时光机、宝宝长相预测、AI表情包、发邮件、图片智能鉴伪、AI照相馆、知识库入口、AI改图、搜知识库】等意图中的一个或多个\n请将用户的问题分解为主意图问题和推荐意图问题，每个问题只包含一个明确的意图。\n\n# 总体条件\n- 问题需要简短，每个问题不少于6个字，但不超过15个字符\n- 不要翻译用户输入的问题\n- 只需要分解主意图问题和推荐意图问题，不要对问题进行回答\n- 每个问题结尾必须有标点符号\n- 使用中文输出\n- 作为用户本人来生成推荐问题\n- 主意图问题放在列表第一位，其他推荐意图问题按照顺序排列，需要严格按照<推荐意图>提供的意图顺序\n- 提取出来的意图必须在用户提供的主意图或者推荐意图中\n- 从提供的多个示例中学习，参考其分解问题和推荐问题的方法\n# 针对特定意图的条件\n<普通对话>\n- 根据用户的问题生成一个相似的问题\n- 生成的问题不能是对用户问题的追问\n- 生成的问题不能带有"能为你做什么"，"还需要什么"等类似机器人回复的字眼\n\n问题必须积极、合法、完整、有意义并且政治正确, 输出严格按照以下json格式数组：\n["意图:主意图问题","意图:推荐问题问题1","意图:推荐问题问题2", "意图:推荐问题问题3"，...]\n---\n# 示例\n输入: 周杰伦的电影\n主意图：普通对话\n推荐意图: 搜视频、功能搜索\n问题: ["普通对话:周杰伦的电影有哪些？", "搜视频:搜索周杰伦的电影视频。", "功能搜索:找与看电影相关的功能。"]\n输入: 创建一篇新笔记，同时帮我找图片配文、智能抠图以及小云果园的入口。\n主意图：创建普通笔记\n推荐意图: 图片配文、智能抠图、功能搜索\n问题: ["创建普通笔记:新建笔记。", "图片配文:图片配文找图片配文入口。", "智能抠图:使用智能抠图。", "功能搜索:找小云果园的入口。"]\n输入：我需要新建一篇笔记做一下记录，同时帮忙搜搜云朵大作战、移动云盘会员日以及会员订购的入口。\n主意图：创建普通笔记\n推荐意图: 功能搜索\n问题: ["创建普通笔记:新建笔记。", "功能搜索:搜搜云朵大作战。","功能搜索:搜移动云盘会员日。","功能搜索:订购会员入口。"]\n输入：优先找一下139邮箱文件夹，之后再给我创建一篇新的笔记\n主意图：搜文件夹\n推荐意图: 创建普通笔记\n问题: ["搜文件夹:找139邮箱文件夹。", "创建普通笔记:新建笔记。"]\n输入：我希望体验一下妙云相机、云朵大作战\n主意图：活动搜索\n推荐意图: 妙云相机\n问题: ["活动搜索:体验云朵大作战。", "妙云相机:了解妙云相机详情。"]\n输入: 搜索1 的文件\n主意图：综合搜索\n推荐意图: 普通对话\n问题: ["综合搜索:搜索文件名为1的文件。", "普通对话:怎样搜索特定文件？"]\n输入：今年大海的图片\n主意图：搜图片\n推荐意图: 文生图\n问题: ["搜图片:搜索大海的图片。", "文生图:生成大海的图片。"]\n# Begin\n输入: %s\n主意图：{main_intent}\n推荐意图: {suggest_intent}\n问题:'
    template-list:
      - intention: '000'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{普通对话}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{普通对话}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：今天天气真好，搜一下好天气的照片\n  {普通对话}意图提取：["今天天气怎么样"]\n  输入：帮我找广州的图，以及查一下广州人口，以及打开老照片修复\n  {普通对话}意图提取：["广州人口有多少"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{普通对话}>最相关的意图并总结提炼。\n  {普通对话}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '001'
        copy: '帮我打开图配文'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{图片配文}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{图片配文}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：今天天气真好，搜一下好天气的照片，并给它配上符合意境的文字说明\n  {图片配文}意图提取：["给照片配文"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{图片配文}>最相关的意图并总结提炼。\n  {图片配文}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '002'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{文生图}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{文生图}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：搜广州旅游的照片，并配上符合意境的文字说明，参考梵高《星月夜》作一幅画，并让画活起来\n  {文生图}意图提取：["创作星空主题画"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{文生图}>最相关的意图并总结提炼。\n  {文生图}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '003'
        copy: '帮我打开智能抠图'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{智能抠图}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{智能抠图}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：搜广州旅游的照片，并让画活起来，如何使用智能抠图进行照片修复和增强处理？\n  {智能抠图}意图提取：["如何使用智能抠图"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{智能抠图}>最相关的意图并总结提炼。\n  {智能抠图}意图提取:'
        link-url-inner: 'https://test.yun.139.com/viewtest/aiTools/#/faceMattingOffice?enableShare=1&token=#ssoToken#'
        link-url-outer: 'https://test.yun.139.com/viewtest/aiTools/#/faceMattingOffice?enableShare=1&token=#ssoToken#'
      - intention: '004'
        copy: '帮我打开AI漫画风'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{图片动漫化}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{图片动漫化}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：搜广州旅游的照片，并转为漫画风，如何使用智能抠图进行照片修复和增强处理？\n  {图片动漫化}意图提取：["照片转为漫画风"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{图片动漫化}>最相关的意图并总结提炼。\n  {图片动漫化}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '005'
        copy: '帮我打开活照片'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{活照片}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{活照片}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：搜手机内最近的照片，并转为活照片模式\n  {活照片}意图提取：["照片转为活照片"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{活照片}>最相关的意图并总结提炼。\n  {活照片}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '006'
        copy: '帮我打开AI消除'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{AI消除}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{AI消除}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：搜合照，并把图中的杂物去掉\n  {AI消除}意图提取：["合照去杂物"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{AI消除}>最相关的意图并总结提炼。\n  {AI消除}意图提取:'
        link-url-inner: 'https://test.yun.139.com/viewtest/aiTools/#/picEliminateOffice?enableShare=1&token=#ssoToken#'
        link-url-outer: 'https://test.yun.139.com/viewtest/aiTools/#/picEliminateOffice?enableShare=1&token=#ssoToken#'
      - intention: '007'
        copy: '帮我打开AI头像'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{AI头像}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{AI头像}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：找我的照片，并基于我的面部特征设计AI头像\n  {AI头像}意图提取：["设计AI头像"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{AI头像}>最相关的意图并总结提炼。\n  {AI头像}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '008'
        copy: '帮我打开老照片修复'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{老照片修复}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{老照片修复}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：找19世纪的清朝图片，并对其进行修复\n  {老照片修复}意图提取：["清朝照片修复"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{老照片修复}>最相关的意图并总结提炼。\n  {老照片修复}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '009'
        copy: '帮我打开画质修复'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{画质修复}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{画质修复}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：生成夏日海边沙滩图片，并进行超分处理\n  {画质修复}意图提取：["沙滩图片超分"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{画质修复}>最相关的意图并总结提炼。\n  {画质修复}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '011'
        copy: '帮我打开妙云相机'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{妙云相机}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{妙云相机}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：生成夏日海边沙滩图片，搜学习资料文件夹，生成学院风格的艺术照\n  {妙云相机}意图提取：["生成学院艺术照"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{妙云相机}>最相关的意图并总结提炼。\n  {妙云相机}意图提取:'
        link-url-inner: 'mcloud://mcMiniProgram?params=eyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLAoibmFtZSI6IuWwj+eoi+W6jyIsCiJtaW5pVHlwZSI6IjEiLAoibWluaVBhdGgiOiJwYWdlcy9ob21lL21haW4iLAoibWluaVVybCI6IiIsCiJtaW5pTGlua1R5cGUiOiIxIn0='
        link-url-outer: 'https://caiyun.feixin.10086.cn:7071/portal/clientDL/index.html?linkUrl=mcloud%3A%2F%2FmcMiniProgram%3Fparams%3DeyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLCJuYW1lIjoi5aaZ5LqR55u45py6IiwibWluaVR5cGUiOiIxIn0%3D'
      - intention: '012'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{搜图片}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{搜图片}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：生成夏日海边沙滩图片，搜我在广州旅游的照片，生成学院风格的艺术照\n  {搜图片}意图提取：["搜广州旅游照片"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{搜图片}>最相关的意图并总结提炼。\n  {搜图片}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '013'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{搜文档}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{搜文档}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：生成夏日海边沙滩图片，生成AI头像，搜手机内学习文档\n  {搜文档}意图提取：["搜学习文档"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{搜文档}>最相关的意图并总结提炼。\n  {搜文档}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '014'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{搜视频}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{搜视频}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：生成AI头像，搜手机内学习视频，翻译中文文稿\n  {搜视频}意图提取：["搜学习视频"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{搜视频}>最相关的意图并总结提炼。\n  {搜视频}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '015'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{搜音频}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{搜音频}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：生成AI头像，搜手机内开会音频，翻译中文文稿\n  {搜音频}意图提取：["搜开会音频"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{搜音频}>最相关的意图并总结提炼。\n  {搜音频}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '016'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{搜文件夹}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{搜文件夹}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：打开AI头像，找手机内AI文件夹并展示对应的时间\n  {搜文件夹}意图提取：["搜AI文件夹"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{搜文件夹}>最相关的意图并总结提炼。\n  {搜文件夹}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '017'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{搜笔记}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{搜笔记}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：打开妙云相机，找最近1个月撰写的笔记，并生成总结\n  {搜笔记}意图提取：["搜索最近1个月的笔记"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{搜笔记}>最相关的意图并总结提炼。\n  {搜笔记}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '018'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{综合搜索}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{综合搜索}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：找旅游照片，搜索学习文档，并生成总结，打开AI头像\n  {综合搜索}意图提取：["找旅游照片、学习文档"]\n  输入：帮我找所有文件，打开AI头像\n  {综合搜索}意图提取：["找文件"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{综合搜索}>最相关的意图并总结提炼。\n  {综合搜索}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '020'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{活动搜索}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{活动搜索}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：找旅游照片，打开AI头像，天天开盲盒活动在哪？\n  {活动搜索}意图提取：["搜天天开盲盒活动"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{活动搜索}>最相关的意图并总结提炼。\n  {活动搜索}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '021'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{功能搜索}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{功能搜索}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：找旅游照片，打开AI头像，照片备份怎么使用？功能入口在哪里？\n  {功能搜索}意图提取：["搜照片备份功能"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{功能搜索}>最相关的意图并总结提炼。\n  {功能搜索}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '022'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{发现广场搜索}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{发现广场搜索}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：找旅游照片，打开AI头像，衡水题目难吗？帮我找几套高三数学试卷\n  {发现广场搜索}意图提取：["找衡水高三数学试卷"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{发现广场搜索}>最相关的意图并总结提炼。\n  {发现广场搜索}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '024'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{创建笔记}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{创建笔记}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：打开AI头像，帮我找AI学习文档，并总结内容，基于内容创建笔记记录\n  {创建笔记}意图提取：["创建AI学习笔记"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{创建笔记}>最相关的意图并总结提炼。\n  {创建笔记}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '999'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{普通对话}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{普通对话}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：今天天气真好，搜一下好天气的照片\n  {普通对话}意图提取：["今天天气怎么样"]\n  输入：帮我找广州的图，以及查一下广州人口，以及打开老照片修复\n  {普通对话}意图提取：["广州人口有多少"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{普通对话}>最相关的意图并总结提炼。\n  {普通对话}意图提取:'
        link-url-inner: ''
        link-url-outer: ''

# 对话结果推荐
dialogue-result-recommend:
  # 意图推荐文本配置列表
  intention-text-list:
    - intention-command: "000"
      copy: ""
  # 意图推荐AI工具配置列表
  intention-list:
    - intention-command: "001"
      copy: "猜你想对图片进行处理，推荐您使用图片配文，图片配文是帮助您快速地为图片添加文字的功能"
    - intention-command: "007"
      copy: "猜你想对图片进行处理，推荐您使用AI头像，AI头像可以帮您生成精美的创意头像"
    - intention-command: "004"
      copy: "猜你想对图片进行处理，推荐您使用AI漫画，AI漫画风帮您把图片一键转化为漫画风格"
    - intention-command: "008"
      copy: "猜你想对图片进行处理，推荐您使用老照片修复，老照片修复可以修复您的图片画质与颜色"
    - intention-command: "005"
      copy: "猜你想对图片进行处理，推荐您使用活照片，活照片支持将人像图片转化为动画表情"
    - intention-command: "009"
      copy: "猜你想对图片进行处理，推荐您使用画质修复，画质修复让您的模糊图片重现清晰细节"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/resolutionRepairOffice?enableShare=1&token=#ssoToken#
    - intention-command: "006"
      copy: "猜你想对图片进行处理，推荐您使用AI消除，AI消除可为您智能消除图片内的指定内容"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/picEliminateOffice?enableShare=1&token=#ssoToken#
    - intention-command: "003"
      copy: "猜你想对图片进行处理，推荐您使用智能抠图，智能抠图可为您智能抠取人像图片，进行背景替换操作"
      link-url: https://test.yun.139.com/viewtest/aiTools/#/faceMattingOffice?enableShare=1&token=#ssoToken#
  # 对话内容推荐-每个类型推荐的数量
  content-recommend-quantity: 3
  # 对话内容推荐-keyword分隔符
  content-recommend-split: "、"
  # 对话内容推荐配置列表
  content-list:
    - type: 2
      desc: "发现类运营推荐"
      keyword-list:
        - keyword: "影视、电影、大片、影片、影音、院线、动画、喜剧片、悬疑片、警匪片、动作片、恐怖片、科幻片、惊悚片、剧情片、影后、影帝、金鸡奖、奥斯卡、金像奖、百花奖、华表奖"
          priority: 1
        - keyword: "综艺"
          priority: 3
        - keyword: "书籍、小说、阅读、电子书、新书"
          priority: 5
        - keyword: "试卷、真题、高考、中考、小升初、期末"
          priority: 6
        - keyword: "健身、运动、锻炼、养生、健康、形体"
          priority: 7
        - keyword: "职场、面试、求职、毕业"
          priority: 8
    - type: 3
      desc: "推荐圈子类运营推荐"
      keyword-list:
        - keyword: "电视剧、剧集、剧荒、美剧、英剧、日剧、韩剧、泰剧、台剧、华语剧、港剧、国产剧"
          priority: 4
    - type: 6
      desc: "其他运营推荐"
      keyword-list:
        - keyword: "新春、春节、拜年、团圆、红包、年味、辞旧迎新、春联、年货、瑞雪兆丰年"
          priority: 2
  # 文本工具推荐
  text-tool:
    random-num: 4
    recomend-file-ext-list: [ 'doc','docx','pdf','ppt','pptx','txt' ]
    tool-list:
      - tool-name: '总结概括工具'
        command: '000'
        use-ype: '1'
        prompt: 'SPEED_READ_SUMMARY_SYNTHESIS'
        prompt-copy: '总结概括'
      - tool-name: '文档大纲工具'
        command: '000'
        use-ype: '1'
        prompt: 'SPEED_READ_OUTLINE'
        prompt-copy: '文档大纲'
      - tool-name: '重点提炼工具'
        command: '000'
        use-ype: '1'
        prompt: 'KEY_POINT_EXTRACTION'
        prompt-copy: '重点提炼'
      - tool-name: '全文翻译工具'
        command: '000'
        use-ype: '1'
        prompt: 'TRANSLATION'
        prompt-copy: '全文翻译'
      - tool-name: '简化语言工具'
        command: '000'
        use-ype: '1'
        prompt: 'SIMPLIFY_LANGUAGE'
        prompt-copy: '简化语言'
  # 特殊快速阅读推荐引流工具
  middle-speed-read-tool:
    random-num: 1
    tool-list:
      - tool-name: '快速阅读引流专区'
        command: '036'
        sub-command: '036007'
        use-ype: '2'
        link-url: 'https://test.yun.139.com/viewtest/ai-helper-dev/pages/minutes/progress-preview?fileId=#fileId#'
        prompt-copy: '精读文档，快速掌握核心要点！'
        button-copy: '深度阅读'
  # 可执行的文本工具意图
  execute-text-tool:
    random-num: 1
    tool-list:
      - tool-name: 'AI生成PPT工具'
        command: '036'
        sub-command: '036001'
        use-ype: '1'
        prompt-copy: '帮我生成PPT'
# 返回词模板配置
search-return-terms:
  # 是否返回系统默认提示词：true-默认；false-使用模板
  returnSystemDefault: true
  # 过滤关键字
  exclude-keywords: [ "的文件资源","的所有文件","的所有内容","的全部","的地方","的图片","的照片","的音频","的视频","的文档","的笔记","的文件夹","的文件","的活动","的功能","的发现","的广场","的资源","的圈子","的动态","的社群","的社区","的交流圈","的交流群","的讨论圈","的聚集地","的讨论区","的交流平台","的群体","的平台","的圈","图片","照片","音频","视频","文档","笔记","文件夹","文件","活动","功能","发现","广场","资源","圈子","动态","社群","社区","交流圈","交流群","讨论圈","聚集地","讨论区","交流平台","群体","平台" ]
  # 过滤意图
  exclude-intention-list: [ "020","021" ]
  # 意图模板配置
  intention-template:
    # 千问模型
    model-code: "qwen_32b"
    # 大模型提示词模板
    unified-template: '#任务背景\n你是一名专业的搜索结果提取专家，能够从用户的输入文本中准确地提取出相关的搜索结果返回词。在实际生活和工作中，我们经常需要快速准确地找到特定的信息，而你的任务就是帮助用户从他们的输入中提取出关键的搜索结果返回词，以便更高效地进行搜索。\n\n#任务要求\n1.分析用户输入：接收到用户输入后，仔细阅读并理解文本内容，从搜索内容提取出来对应的搜索结果返回词。\n2.提取规则：\n-搜索结果返回词需要简短，搜索结果返回词长度在6-15个字符之间，只提取一个搜索结果返回词，每个搜索结果返回词结尾不要有标点符号\n-不重写或总结，直接提取用户输入的内容，不要对搜索结果返回词进行回答\n-提取搜索结果返回词的时候，不要提取意图或文件类型（如：xxx照片、xxx图片、xxx文档、xxx文件）\n-只需要对【搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、圈子搜索、发现广场搜索】进行分解和提取搜索结果返回词，【搜索活动、搜索功能】不要提取搜索结果返回词\n-从提供的多个示例中学习，参考其分解和提取的方法，对于多个关键词的输入，优先选择与搜索内容最相关的关键词进行提取\n-对于包含时间范围的输入，可以将时间范围融入到提取结果中，形成如"2023年广州佛山"的格式。\n \n \n#输出格式\n最终输出结果必须严格按照以下json格式：["搜索结果返回词"]\n \n#示例\n##搜索图片示例\n输入：广州和佛山的图片\n输出：["广州和佛山"]\n \n输入：帮我搜索2023年我在广州和佛山的图片\n输出：["2023年我在广州和佛山"]\n \n输入：帮我找一下花朵的照片，2019年拍的\n输出：["2019年拍的花朵"]\n \n输入：帮我找找逛公园的照片，在广州和佛山和深圳城市的，最好是最近3年的，合照的不要，只要单人照片\n输出：["2021年至2024年广州和佛山和深圳逛公园单人"]\n\n\n##搜索视频示例\n输入：搜索带有“城市发展”字眼的视频\n输出：["带有城市发展字眼"]\n\n输入：找2023年上传的和2024年的视频\n输出：["2023年和2024年上传"]\n\n输入：找2023年年度总结和2024年党建工作视频\n输出：["2023年年度总结和2024年党建工作"]\n\n输入：找一些视频，带“玫瑰的故事”和“城市发展”字眼的\n输出：["玫瑰的故事和城市发展"]\n\n\n##搜索音频示例\n输入：搜索语音备忘录音频\n输出：["语音备忘录"]\n\n输入：找2023年年度总结和2024年党建工作音频\n输出：["2023年年度总结和2024年党建工作"]\n\n输入：找一些音频或者MP3出来，语音备忘录和“笔记附件”\n输出：["语音备忘录笔记附件"]\n\n\n##搜索文档示例\n输入：帮我搜索今年10月份保存的文档，带有“测试”字眼的，除文档格式以外的不要\n输出：["2024年10月保存的带有测试字眼"]\n\n输入：帮我查找一些文档，文档标题为测试的\n输出：["标题为测试"]\n\n输入：帮我查找2023年和2024年的党建工作文档\n输出：["2023年和2024年党建工作"]\n\n输入：找2023年产品工作文档和2024年产品工作文档\n输出：["2023年和2024年产品工作"]\n\n输入：涉及广州市、城市建设、城市规划、2024年这些词的都给我找出来，其他不要，只要文档格式的\n输出：["广州市、城市建设、城市规划、2024年"]\n\n输入：带有“党建工作”、“工作汇报”、“需求评审”、“规划设计”、“设计”、测试、产品需求这些字眼的文档统统找出来，时间范围筛选到2023年至2024年\n输出：["2023年至2024年带有党建工作、工作汇报、需求评审、规划设计、设计、测试、产品需求的字眼"]\n\n\n##搜索笔记示例\n输入：帮我搜索AI助手笔记\n输出：["AI助手"]\n\n输入：带有“2024”“测试”字眼的笔记\n输出：["带有2024测试字眼"]\n\n输入：有没有个人工作汇报笔记？最好是最近3年的\n输出：["2021年至2024年个人工作汇报"]\n\n\n##搜索文件夹示例\n输入：搜索名称为文档2024文件夹\n输出：["文档2024"]\n\n输入：找出一些文件夹来，要带有“AI助手”字眼的\n输出：["带有AI助手字眼"]\n\n输入：帮我找云盘的文件夹，最好是最近2年创建的，并且带有“AI助手”字眼的\n输出：["2022年至2024年带有创建AI助手字眼"]\n\n\n##搜索活动示例\n输入：帮我搜索云朵活动\n输出：[""]\n\n\n##搜索功能示例\n输入：帮我搜索备份功能\n输出：[""]\n\n\n##搜索发现广场示例\n输入：帮我找找广州市中考试卷\n输出：["广州市中考试卷"]\n\n输入：找周杰伦和林俊杰一起出演的电影\n输出：["周杰伦和林俊杰一起出演"]\n\n输入：我看到发现广场有许多许多的资源，有动画片、悬疑片、爱情片，能帮我推荐一些喜剧片看看吗？\n输出：["动画片、悬疑片、爱情片、喜剧片"]\n\n\n##搜索圈子示例\n输入：帮我找找影视相关的圈子\n输出：["影视"]\n\n输入：有没有能交流刘德华电影的平台？想聊下他获奖的那些\n输出：["刘德华电影"]\n输入：有没有能交流刘德华电影的平台？想聊下他获奖的那些\n输出：["刘德华获奖电影"]\n\n输入：圈子是不是有很多影视资源，我想看看最新的影视作品，帮我推荐推荐几个\n输出：["影视资源最新影视作品"]\n\n\n##搜索综合示例\n输入：搜索关于养生的文档和视频\n输出：["养生"]\n\n输入：找到AI的圈子并搜索一下23年上传的视频\n输出：["AI的圈子、23年上传"]\n\n输入：搜索关于新能源汽车的文档、视频和文章\n输出：["新能源汽车"]\n\n输入：有没有个人工作汇报文档、产品需求评审纪要的笔记，还有最近一年我上传的在广州的图片？统统帮我找出来\n输出：["个人工作汇报文档、产品需求评审纪要笔记、2023年至2024年广州图片"]\n\n现在，请根据你所学到的知识和经验，对以下用户输入进行抽取（注意：无需返回思考分析过程）：\n%s'
    # 意图-返回词模板映射
    template-list:
      - intention: '012'
        template: '为您找到云盘内“%s”的图片'
        default-text: '为您找到云盘内的图片'
        default-text-en: 'Images found in your cloud drive'
        filterFlag: true
      - intention: '013'
        template: '为您找到云盘内“%s”的文档'
        default-text: '为您找到云盘内的文档'
        default-text-en: 'Documents found in your cloud drive'
        filterFlag: true
      - intention: '014'
        template: '为您找到云盘内“%s”的视频'
        default-text: '为您找到云盘内的视频'
        default-text-en: 'Videos found in your cloud drive'
        filterFlag: true
      - intention: '015'
        template: '为您找到云盘内“%s”的音频'
        default-text: '为您找到云盘内的音频'
        default-text-en: 'Audios found in your cloud drive'
        filterFlag: true
      - intention: '016'
        template: '为您找到云盘内“%s”的文件夹'
        default-text: '为您找到云盘内的文件夹'
        default-text-en: 'Folders found in your cloud drive'
        filterFlag: true
      - intention: '017'
        template: '为您找到云盘内“%s”的笔记'
        default-text: '为您找到云盘内的笔记'
        default-text-en: 'Notes found in your cloud drive'
        filterFlag: true
      - intention: '018'
        template: '为您找到“%s”的相关内容'
        default-text: '为您找到云盘里的相关内容：'
        default-text-en: 'Here are the related results for you'
        filterFlag: false
      - intention: '020'
        template: '为您推荐以下活动'
        default-text: '为您推荐以下活动'
        default-text-en: 'Recommended promotions for you'
        filterFlag: true
      - intention: '021'
        template: '为您推荐以下功能'
        default-text: '为您推荐以下功能'
        default-text-en: 'Recommended features for you'
        filterFlag: true
      - intention: '022'
        template: '为您找到发现广场的“%s”'
        default-text: '为您找到发现广场的内容'
        default-text-en: 'Content found in Resources'
        filterFlag: true
      - intention: '023'
        template: '为您找到“%s”相关的圈子'
        default-text: '为您找到相关圈子'
        default-text-en: 'Related circles found'
        filterFlag: true
# 推荐人物相册配置
recommend-search-album-template:
  name-map-list:
    - name: '伴侣'
      value: '伴侣'
      value-en: 'Partner'
    - name: '孩子'
      value: '孩子'
      value-en: 'Child'
    - name: '爸爸'
      value: '爸爸'
      value-en: 'Dad'
    - name: '妈妈'
      value-en: 'Mom'
    - name: '亲属'
      value: '亲属'
      value-en: 'Relatives'
    - name: '朋友'
      value: '朋友'
      value-en: 'Friend'
    - name: '同事'
      value: '同事'
      value-en: 'Colleague'
    - name: '家人'
      value: '家人'
      value-en: 'Family'
  myself-prompt-copy: '我还不知道哪个是您，您可以去相册把人物设置为本人哦～'
  myself-prompt-copy-en: 'I don’t know which one is you yet. You can set up your relationship~'
  onlyone-prompt-copy: '我还不认识谁是您的{name}，您可以去相册给人物设置姓名关系哦～'
  onlyone-prompt-copy-en: 'I don’t know who your {name} is yet. You can set up their relationship.'
  multiple-prompt-copy: '我还不认识您要找的人，您可以去相册设置人物和您的关系哦～'
  multiple-prompt-copy-en: 'I don’t know the person you’re looking for yet. You can set up their relationship with you.'
  button-copy: "去相册设置"
  button-copy-en: "Go to Album"