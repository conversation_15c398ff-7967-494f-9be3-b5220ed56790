# 二次对话sse流式配置
second-stream:
  # 配置信息
  config:
    audit-size: 30
    mode-max-tokens: 15000
    mode-code: "qwen"
    default-email-content: "无邮件内容。"
    timeout: 30000
    reconnect-time-millis: 5000
    allow-intention: [ "032" ]
    prompt-template: "# 1. 任务背景\n\n你是一名专业的**实体识别专家**，能够从用户的输入文本中准确地提取出相关的**邮件内容**。\n\n# 2. 任务要求\n\n1.**分析用户输入**：接收到用户输入后，仔细阅读并理解文本内容，从中提取出所有可能的邮件内容。\n\n2.**精准提取**：尽可能提取完整和准确的信息。\n\n3.**提取原则**：如果文本中没有明确提到邮件内容（如“帮我发邮件”、“我要发邮件”、“帮我给小李发邮件”等），则输出   \n\n\n# 3. 示例\n\n**示例1**\n\n**用户输入**：\n\n“发送邮件给李明，邮件内容是：亲爱的李明，我们计划在10月16日早上9点对APP V1.0的需求方案进行深度审议，期待您的参与，请提前做好准备，准时出席。感谢关注。”\n\n**输出**：\n\n亲爱的李明，我们计划在10月16日早上9点对APP V1.0的需求方案进行深度审议，期待您的参与，请提前做好准备，准时出席。感谢关注。\n\n\n**示例2**\n\n**用户输入**：\n\n“请给15820780084发邮件，提醒他提交月总结报告”\n\n**输出**：\n提交月总结报告\n\n\n**示例3**\n\n**用户输入**：\n\n“给李明发送邮件”\n\n**输出**：\n   \n\n\n# 4. 现在的任务\n\n请根据上述要求，对以下用户输入进行实体抽取（注意：无需返回思考分析过程和模型解释）：\n\n**用户输入**：\n\n{query}"
  # 智能体相关配置
  intelligent:
    # 会议发邮件配置
    meeting-send-mail:
      mode-code: qwen
      prompt-template: '输入指令：\n{query}\n请根据输入指令直接生成会议邀请邮件，仅输出标准输出格式下的邀请邮件，不添加任何额外说明，格式保持一致。信息提取逻辑如下：\n**会议主题**：从指令中提取核心关键词（如 “智能体打造推进会”）。\n**时间**：精准抓取指令中的时间描述（如 “明天下午 2 点 - 3 点”）。\n**地点**：提取明确的会议室编号或地点（如 “1515 会议室”），无则标注 “需协调”。\n**参会人员**：列出指令中所有姓名（如 “张三、李四”），无则标注 “待定”。\n**会议内容**：根据指令要求拆解为具体条目（如 “各团队汇报当前进展”），补充必要细节（如汇报时长、材料要求）。\n**注意事项**：注意事项按照标准输出格式输出，如果用户指令中有其他的注意事项就进行补充。\n\n标准输出格式：\n各位好，\n  计划开展{会议主题}，请各位积极参加，谢谢。\n\n**·时间**: {会议时间}\n**·地点**: {会议室地点}\n**·参会人员**: {参会人员名单}\n**·会议内容**:\n\n1.会议内容条目点1。\n\n2.会议内容条目点2。\n\n3.会议内容条目点3。\n**·注意事项**:\n\n1.请各位准时参加会议，如有特殊情况请提前请假。\n\n2.请携带团队提前准备汇报材料，内容包括当前进展、存在问题及下一步计划。\n\n3.会议期间请将手机调至静音状态。\n期待大家积极参与，共同推进项目进展！\n\n  祝好！\n\n输出示例：\n各位好，\n  计划开展智能体打造推进会，请各位积极参加，谢谢。\n\n**·时间**: 明天下午2点-3点\n**·地点**: 1515会议室\n**·参会人员**: 张三、李四\n**·会议内容**:\n\n1.各团队汇报智能体打造当前进展。\n\n2.讨论推进中的问题及解决方案。\n\n3.明确下一阶段的工作计划和目标。\n**·注意事项**:\n\n1.请各位准时参加会议，如有特殊情况请提前请假。\n\n2.请携带团队提前准备汇报材料，内容包括当前进展、存在问题及下一步计划。\n\n3.会议期间请将手机调至静音状态。\n期待大家积极参与，共同推进项目进展！\n\n  祝好！\n'
    # ppt生成发邮件配置
    ppt-send-mail:
      mode-code: qwen
      prompt-template: '请根据输入指令直接生成邮件回复内容，仅输出标准邮件格式，不添加额外说明，逻辑通顺。信息提取逻辑如下：\n\n会议主题：从指令中提取最核心关键词（如 “绩效考核方案修订会”），禁止提取如人名会议时间等与主题不相关的信息。\n会议主题方案：基于会议主题完成的方案名（如 “绩效考核方案修订方案”），默认提及附件（如 “相关材料已整理，见附件”）。\n称呼：统一使用 “各位好” 。\n结尾：固定使用 “烦请查收，谢谢。”\n\n标准输出模板格式：\n{称呼}，\n根据 {会议主题} 沟通，完成{会议主题方案}如附件。\n烦请查收，谢谢。\n\n输入指令：\n{query}\n\n输出示例：\n各位好，\n根据智能体打造推进会沟通，完成智能体规划方案如附件。\n烦请查收，谢谢。\n'