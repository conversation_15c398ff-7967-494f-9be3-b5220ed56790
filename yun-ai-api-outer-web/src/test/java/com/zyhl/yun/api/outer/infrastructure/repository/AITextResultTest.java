package com.zyhl.yun.api.outer.infrastructure.repository;

import javax.annotation.Resource;

import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 会员权益接口测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class AITextResultTest {

    @Resource
    private AiTextResultRepository aiTextResultRepository;

    @Test
    public void save() {
        String rowkey = "rowkey";
        AiTextResultEntity newEntity = AiTextResultEntity.builder()
                .rowKey(rowkey)
                .userId("1")
                .taskId("1")
                .reqParameters("1")
                .attachment("1")
                .history("1")
                .respParameters("1")
                .build();

        // 新增
        aiTextResultRepository.save(newEntity);

        // 更新
        AiTextResultEntity updateEntity = AiTextResultEntity.builder()
                .rowKey(rowkey)
                .history("2")
                .respParameters("2")
                .build();
        aiTextResultRepository.save(updateEntity);

        // 查询
        AiTextResultEntity selectEntity = aiTextResultRepository.getByRowKey(rowkey);

        // 结果输出
        System.out.println(JsonUtil.toJson(selectEntity));
    }

    @Test
    public void update() {
        String userId = "1";
        Long dialogId = 1L;
        AiTextResultEntity newEntity = AiTextResultEntity.builder()
                .rowKey(AiTextResultRepository.createRowKey(userId, dialogId))
                .userId(userId)
                .taskId("1")
                .reqParameters("1")
                .attachment("1")
                .history("1")
                .respParameters("1")
                .build();

        // 新增
        aiTextResultRepository.save(newEntity);

        // 更新
        aiTextResultRepository.update(userId, dialogId, "", "");

        // 查询
        System.out.println(JsonUtil.toJson(aiTextResultRepository.getByRowKey(userId, dialogId)));

        // 更新
        aiTextResultRepository.update(userId, dialogId, "历史对话", "响应结果");

        // 查询
        System.out.println(JsonUtil.toJson(aiTextResultRepository.getByRowKey(userId, dialogId)));
    }

    @Test
    public void getByRowKey() {
        String userId = "1136385042998681887";
        Long dialogId = 1157164861429056360L;
        // 查询
        System.out.println(JsonUtil.toJson(aiTextResultRepository.getByRowKey("1136385042998681887_1157164861429056360")));
    }
}
