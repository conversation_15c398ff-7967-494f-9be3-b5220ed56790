package com.zyhl.yun.api.outer.domain.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.corpus.tag.Nature;
import com.hankcs.hanlp.seg.common.Term;
import com.zyhl.hcy.yun.ai.common.intention.client.executepre.util.AhoCorasick;
import com.zyhl.hcy.yun.ai.common.intention.client.intentutil.IntentionUtilClient;
import com.zyhl.yun.api.outer.domain.vo.SearchReturnTermsVO;
import com.zyhl.yun.api.outer.domain.vo.common.AhoCorasickResult;
import com.zyhl.yun.api.outer.domainservice.SearchReturnTermsService;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.IntentEntityVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class SearchReturnTermsServiceTest {

	@Resource
	private SearchReturnTermsService searchReturnTermsService;

	@Resource
	private IntentionUtilClient intentionUtilClient;

	@Test
	public void getOptimizeReturnTermsFutureV1() {
		RequestContextHolder.setUserId("1154956105781362991");
		Long dialogueId = 11111L;
		String dialogue = "试卷帮我搜试卷资源，不对，先帮我搜英语试卷吧";

		Future<String> object = searchReturnTermsService.getOptimizeReturnTermsFutureV1(dialogueId, dialogue);
		String result = searchReturnTermsService.getSearchReturnTermsV1(object, DialogueIntentionEnum.SEARCH_IMAGE.getCode(), dialogueId, dialogue);
		System.out.println(result);

		dialogue = "帮我搜试卷资源，不对，先帮我搜英语试卷吧";
		object = searchReturnTermsService.getOptimizeReturnTermsFutureV1(dialogueId, dialogue);
		result = searchReturnTermsService.getSearchReturnTermsV1(object, DialogueIntentionEnum.SEARCH_IMAGE.getCode(), dialogueId, dialogue);
		System.out.println(result);

		dialogue = "我不要八年级语文试卷资源了，先帮我搜英语试卷吧";
		object = searchReturnTermsService.getOptimizeReturnTermsFutureV1(dialogueId, dialogue);
		result = searchReturnTermsService.getSearchReturnTermsV1(object, DialogueIntentionEnum.SEARCH_IMAGE.getCode(), dialogueId, dialogue);
		System.out.println(result);
	}

	@Test
	public void testAhoCorasick() {
		List<String> keywordList = new ArrayList<>();
//		keywordList.add("广州");
//		keywordList.add("白云山");
//		keywordList.add("中秋节");
//		keywordList.add("图片1");
//		String dialogue = "帮我搜索中秋节广州白云山的图片";
		keywordList.add("试卷");
		keywordList.add("英语");
		String dialogue = "我不要八年级语文试卷资源了，先帮我搜英语试卷吧";

		List<int[]> searchIndexList = new ArrayList<>();
		for(String keyword: keywordList){
			// 根据AC自动机，获取对话内容的下标
			AhoCorasick ahoCorasick = intentionUtilClient.getAhoCorasick(CollUtil.toList(keyword));
			if (ObjectUtil.isNotNull(ahoCorasick)) {
				// int[]的值：[0]匹配的下标，[1]匹配的长度
				List<int[]> resultList = ahoCorasick.search(dialogue);
				if (CollUtil.isNotEmpty(resultList)) {
					// 取匹配的最后一个
					searchIndexList.add(resultList.get(resultList.size()-1));
				}
			}
		}

		if (CollUtil.isEmpty(searchIndexList)) {
			log.info("生成搜索返回词V2，searchIndexList为空，返回默认值");
			return;
		}

		// 构建AC自动机结果列表，并根据index字段正序排序
		List<AhoCorasickResult> ahoCorasickResultList = AhoCorasickResult.createAhoCorasickResultList(searchIndexList);
		// 根据下标截取字符串
		int lastIndex = ahoCorasickResultList.size() - 1;
		// 结果int[]的值：[0]匹配的下标，[1]匹配的长度
		System.out.println(CharSequenceUtil.sub(dialogue, searchIndexList.get(0)[0], searchIndexList.get(lastIndex)[0]+searchIndexList.get(lastIndex)[1]));
		System.out.println(CharSequenceUtil.sub(dialogue, ahoCorasickResultList.get(0).getIndex(), ahoCorasickResultList.get(lastIndex).getIndex()+ahoCorasickResultList.get(lastIndex).getLength()));
		// 获取匹配的完整字符串
		List<String> matchedValues = new ArrayList<>();
		for (int[] match : searchIndexList) {
			// 如果返回的值都是0，则不添加
			if (match[0] == 0 && match[1] == 0) {
				continue;
			}
			matchedValues.add(dialogue.substring(match[0], match[0] + match[1]));
		}
		System.out.println(matchedValues);
	}

	@Test
	public void getOptimizeReturnTermsFutureV2Test1() {
		String intention = DialogueIntentionEnum.SEARCH_IMAGE.getCode();
		String dialogueId = "666";
		String dialogue = "帮我搜索中秋节广州白云山的图片";
		List<IntentEntityVO> entityList = new ArrayList<>();
		IntentEntityVO vo1 = new IntentEntityVO();
		List<String> placeList1 = new ArrayList<>();
		placeList1.add("广州");
		placeList1.add("中秋节");
		vo1.setPlaceList(placeList1);
		entityList.add(vo1);
		IntentEntityVO vo2 = new IntentEntityVO();
		List<String> placeList2 = new ArrayList<>();
		placeList2.add("白云山");
		placeList2.add("中秋节");
		placeList2.add("图片1");
		vo2.setPlaceList(placeList2);
		entityList.add(vo2);
		DialogueIntentionVO.IntentionInfo firstIntentionInfo = new DialogueIntentionVO.IntentionInfo();
		firstIntentionInfo.setEntityList(entityList);
		// 生成搜索结果的标题V2
		Future<SearchReturnTermsVO> optimizeReturnTermsFutureV2 = searchReturnTermsService.getOptimizeReturnTermsFutureV2(intention, dialogueId, dialogue, firstIntentionInfo);
		// 获取搜索结果的标题（异常时返回默认值）
		String terms = searchReturnTermsService.getSearchReturnTermsV2(optimizeReturnTermsFutureV2, intention, dialogueId, dialogue);
		System.out.println(terms);
	}

	@Test
	public void getOptimizeReturnTermsFutureV2Test2() {
		String intention = DialogueIntentionEnum.SEARCH_IMAGE.getCode();
		String dialogueId = "666";
//		String dialogue = "我不要八年级语文试卷资源了，先帮我搜英语试卷吧";
		String dialogue = "试卷帮我搜试卷资源，不对，先帮我搜英语试卷吧";
//		String dialogue = "帮我搜试卷资源，不对，先帮我搜英语试卷吧";
//		String dialogue = "帮我搜英语试卷资源，不对，先帮我搜试卷吧";
		List<IntentEntityVO> entityList = new ArrayList<>();
		IntentEntityVO vo1 = new IntentEntityVO();
		List<String> placeList1 = new ArrayList<>();
//		placeList1.add("八年级");
		placeList1.add("试卷");
		vo1.setPlaceList(placeList1);
		entityList.add(vo1);
		IntentEntityVO vo2 = new IntentEntityVO();
		List<String> placeList2 = new ArrayList<>();
		placeList2.add("英语");
		vo2.setPlaceList(placeList2);
		entityList.add(vo2);
		DialogueIntentionVO.IntentionInfo firstIntentionInfo = new DialogueIntentionVO.IntentionInfo();
		firstIntentionInfo.setEntityList(entityList);
		// 生成搜索结果的标题V2
		Future<SearchReturnTermsVO> optimizeReturnTermsFutureV2 = searchReturnTermsService.getOptimizeReturnTermsFutureV2(intention, dialogueId, dialogue, firstIntentionInfo);
		// 获取搜索结果的标题（异常时返回默认值）
		String result = searchReturnTermsService.getSearchReturnTermsV2(optimizeReturnTermsFutureV2, intention, dialogueId, dialogue);
		System.out.println(dialogue);
		System.out.println(result);

		dialogue = "帮我搜试卷资源，不对，先帮我搜英语试卷吧";
		// 生成搜索结果的标题V2
		optimizeReturnTermsFutureV2 = searchReturnTermsService.getOptimizeReturnTermsFutureV2(intention, dialogueId, dialogue, firstIntentionInfo);
		// 获取搜索结果的标题（异常时返回默认值）
		result = searchReturnTermsService.getSearchReturnTermsV2(optimizeReturnTermsFutureV2, intention, dialogueId, dialogue);
		System.out.println("============================================");
		System.out.println("============================================");
		System.out.println(dialogue);
		System.out.println(result);

		dialogue = "帮我搜英语试卷资源，不对，先帮我搜试卷吧";
		// 生成搜索结果的标题V2
		optimizeReturnTermsFutureV2 = searchReturnTermsService.getOptimizeReturnTermsFutureV2(intention, dialogueId, dialogue, firstIntentionInfo);
		// 获取搜索结果的标题（异常时返回默认值）
		result = searchReturnTermsService.getSearchReturnTermsV2(optimizeReturnTermsFutureV2, intention, dialogueId, dialogue);
		System.out.println("============================================");
		System.out.println("============================================");
		System.out.println("原文："+dialogue);
		System.out.println("返回词："+result);

//		result = "试卷资源，不对，先帮我搜英语试卷";
//		List<String> keys = new ArrayList<>();
//		keys.add("试卷");
//		keys.add("语文");
//		keys.add("八年级");
//		getReturnWork(result, dialogue, keys);
	}

	public static void main(String[] args) {
		// 示例文本，包含邮件地址
		String dialogue = "我不要八年级语文试卷资源了，先帮我搜英语试卷吧";
		String result = "试卷资源了，先帮我搜英语试卷";
		List<String> keys = new ArrayList<>();
		keys.add("英语");
		keys.add("试卷");
		System.out.println("============================================");
		System.out.println("============================================");
		getReturnWork(result, dialogue, keys);

		System.out.println("============================================");
		System.out.println("============================================");
		result = "八年级语文试卷资源了，先帮我搜英语试卷";
		keys = new ArrayList<>();
		keys.add("八年级");
		keys.add("语文");
		keys.add("试卷");
		getReturnWork(result, dialogue, keys);

		System.out.println("============================================");
		System.out.println("============================================");
		result = "八年级语文试卷资源了先帮我搜英语试卷";
		getReturnWork(result, dialogue, keys);

	}

	private static void getReturnWork(String result, String dialogue, List<String> keys) {
		// 使用HanLP进行分词
		List<Term> termList = HanLP.segment(result);
		StringBuilder simple = new StringBuilder();
		// 是否包括标点符号
		boolean isW = termList.stream().anyMatch(term -> term.nature == Nature.w);
		if (isW) {
			getSimple(keys, termList, simple);
		} else {
			getSimple(keys, termList, simple);
//            simple.append(text);
		}

		System.out.println("原文 = " + dialogue);
		System.out.println("意图识别的实体 = " + keys);
		System.out.println("意图识别的实体关键字截取 = " + result);
		System.out.println("意图识别的实体关键字截取二次处理 (hanlp只保留动词和实体) = " + simple);
	}

	private static void getSimple(List<String> keys, List<Term> termList, StringBuilder simple) {
		for (Term term : termList) {
//            System.out.println("Found word: " + term.toString());
			boolean match = keys.stream().anyMatch(key -> key.contains(term.word));
			if (match) {
				simple.append(term.word);
			} else if (term.nature == Nature.v) {
				simple.append(term.word);
			}
		}
	}

}
