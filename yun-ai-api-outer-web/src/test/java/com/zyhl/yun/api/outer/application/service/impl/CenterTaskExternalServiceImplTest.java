package com.zyhl.yun.api.outer.application.service.impl;

import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;

import static com.zyhl.yun.api.outer.enums.DialogueIntentionEnum.PICTURE_GENERATE_TEXT;

class CenterTaskExternalServiceImplTest {


    public static void main(String[] args) {
        DialogueIntentionEnum  dialogueIntentionEnum = PICTURE_GENERATE_TEXT;
        switch (dialogueIntentionEnum) {
            case PICTURE_GENERATE_TEXT:

                break;
            case PICTURE_COMIC_STYLE:

                break;
            case LIVE_PHOTOS:

                break;
            case AI_HEAD_SCULPTURE:

                break;
            case OLD_PHOTOS_REPAIR:

                break;
            default:
                break;
        }
    }

}