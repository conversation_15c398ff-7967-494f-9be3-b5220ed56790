package com.zyhl.yun.api.outer.es;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.HashBasedTable;
import com.zyhl.hcy.yun.ai.common.base.enums.EsEnum;
import com.zyhl.hcy.yun.ai.common.base.enums.YunAiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.base.es.domain.datahelper.repository.EsPersonalKnowledgeRepository;
import com.zyhl.hcy.yun.ai.common.base.es.entity.PersonalKnowledgeEsEntity;
import com.zyhl.hcy.yun.ai.common.base.es.properties.ElasticSearchPropertiesTwo;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.Application;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeResource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.MultiSearchRequest;
import org.elasticsearch.action.search.MultiSearchResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description}
 * <p>
 *
 * <AUTHOR>
 * @since 4月21 2025
 */
@SpringBootTest(classes = Application.class)
@Slf4j
public class EsBatchTest {

  @Resource
  private RestHighLevelClient restHighLevelClientTwo;
  @Resource
  private ElasticSearchPropertiesTwo esPropertiesTwo;

  @Resource
  private EsPersonalKnowledgeRepository esPersonalKnowledgeRepository;

  @Test
  void test() {
    esPersonalKnowledgeRepository.batchUpdateDocumentForMigration("1157749354943094818",
        "FjNR6zHgNNFEOFOsxVz9MDZjpwJJHj-_R", "9876", 100);
  }

  private final static String FILE_NAME_STR = "\\[文件名]:";
  private final static String FILE_CONTENT_STR = "\\[文件内容]:";

  @Test
  void queryESTest() {
    String userId = "1208001745773028289";
    List<PersonalKnowledgeEsEntity> sourceList = new ArrayList<>();
    PersonalKnowledgeEsEntity resource = new PersonalKnowledgeEsEntity();
    resource.setUserId(userId);
    resource.setFileId("FveQMDj2DA5LoDQNpRkRNCasTdwC1AMyS");
    resource.setBaseId("1221026498231156753");
    sourceList.add(resource);
    List<Integer> indexes = ListUtil.toList(0, 100);
    List<PersonalKnowledgeEsEntity> result = esPersonalKnowledgeRepository.getFirstSplitContentList(
        sourceList, indexes);
    HashBasedTable<String, Integer, String> table = HashBasedTable.create();
    result.forEach(item -> table.put(item.getFileId(), item.getIndex(), item.getText()));
    result.forEach(item -> {
      String text = table.get(item.getFileId(), 0);
      if (StrUtil.isEmpty(text)) {
        text = table.get(item.getFileId(), 100);
      }
      if (StrUtil.isNotBlank(text)) {
        String prefixRegex = FILE_NAME_STR + "\\s*.*?" + FILE_CONTENT_STR + "\\s*";
        text = text.replaceFirst(prefixRegex, StrUtil.EMPTY);
        String[] parts = text.split("   ");
        List<String> nonEmptyParts = new ArrayList<>();
        for (String part : parts) {
          String trimmed = part.trim();
          if (StrUtil.isNotBlank(trimmed)) {
            nonEmptyParts.add(trimmed);
          }
        }
        int size = nonEmptyParts.size();

        if (size > 1) {
          String content = nonEmptyParts.get(size - 1);
          System.out.println("nonEmptyParts：" + nonEmptyParts);
          System.out.println("title：" + nonEmptyParts.get(0));
          System.out.println("content：" + content);
        } else {
          String content = nonEmptyParts.get(0);
          System.out.println("nonEmptyParts：" + nonEmptyParts);
          System.out.println("content：" + content);
        }
      }
    });
  }

  @Test
  void getTest() {

    List<PersonalKnowledgeResource> list = new ArrayList<>();
    PersonalKnowledgeResource resource = new PersonalKnowledgeResource();
    String userId = "1208001745773028289";
    resource.setResourceId("FkuzT0x-8pE--zs5ZXHdNV4QbqN2XP2tP");
    resource.setBaseId("1221026498231156753");
    list.add(resource);

    // 创建请求对象
    List<Integer> indexes = new ArrayList<>();
    indexes.add(0);
    indexes.add(100);
    List<PersonalKnowledgeEsEntity> result = new ArrayList<>();
    // 1. 使用批量查询代替循环内单次查询
    MultiSearchRequest multiRequest = new MultiSearchRequest();

    list.forEach(item -> {
      SearchRequest searchRequest = new SearchRequest(
          getPersonalKnowledgeIndexNameByUserId(userId));
      searchRequest.routing(userId);

      // 3. 优化查询构建
      BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
          .must(QueryBuilders.termQuery(EsEnum.BASE_ID.getCode(), item.getBaseId()))
          .must(QueryBuilders.termsQuery(EsEnum.INDEX.getCode(), indexes))
          .must(QueryBuilders.termQuery(EsEnum.FILE_ID.getCode(), item.getResourceId()))
          .must(QueryBuilders.termQuery(EsEnum.PARSE_TYPE.getCode(), "SPLIT"))
          .must(QueryBuilders.termQuery(EsEnum.SPLIT_SIZE.getCode(), 128));

      // 4. 添加源过滤减少数据传输
      SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
          .query(boolQuery)
          .sort("index", SortOrder.ASC);
      searchRequest.source(sourceBuilder);
      multiRequest.add(searchRequest);
    });

    try {
      // 5. 执行批量查询
      MultiSearchResponse responses = restHighLevelClientTwo.msearch(multiRequest,
          RequestOptions.DEFAULT);

      for (MultiSearchResponse.Item responseItem : responses.getResponses()) {
        if (responseItem.isFailure()) {
          log.error("ES query failed: {}", responseItem.getFailureMessage());
          continue;
        }

        SearchResponse searchResponse = responseItem.getResponse();
        SearchHits searchHits = searchResponse.getHits();

        if (searchHits.getTotalHits() != null && searchHits.getTotalHits().value > 0) {
          for (SearchHit hit : searchHits.getHits()) {
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            if (sourceAsMap != null) {
              // 将Map转换为Java对象
              PersonalKnowledgeEsEntity knowledgeEsEntity = JSONUtil.toBean(
                  JSONUtil.toJsonStr(sourceAsMap),
                  PersonalKnowledgeEsEntity.class);
              result.add(knowledgeEsEntity);
            }
          }
        }
      }
    } catch (IOException e) {
      // 7. 改进异常处理
      log.error("ES query error for user: {}", userId, e);
    }
    System.out.println(result);
  }

  public String getPersonalKnowledgeIndexNameByUserId(String userId) {
    if (StringUtils.isEmpty(userId)) {
      throw new YunAiBusinessException(YunAiCommonResultCode.ES_USER_ID_NOT_PROVIDE);
    }
    //不分表
    if (ObjectUtil.isEmpty(esPropertiesTwo.getPersonalKnowledgeShards())) {
      return esPropertiesTwo.getPersonalKnowledgeIndexName();
    }
    long id = Long.parseLong(userId);
    //分表 userId对总分表数取模
    return esPropertiesTwo.getPersonalKnowledgeIndexName() + (id
        % esPropertiesTwo.getPersonalKnowledgeShards());
  }
}
