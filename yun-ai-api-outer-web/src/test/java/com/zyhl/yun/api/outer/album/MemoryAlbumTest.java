package com.zyhl.yun.api.outer.album;

import cn.hutool.json.JSONUtil;
import com.zyhl.yun.api.outer.application.dto.ImageSelectionReqDTO;
import com.zyhl.yun.api.outer.application.service.GenerateMemoryService;
import com.zyhl.yun.api.outer.domain.dto.QualityAlbumSelectionRespDTO;
import com.zyhl.yun.api.outer.domain.entity.image.AlbumConditionEntity;
import com.zyhl.yun.api.outer.enums.AlbumConditionRuleEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * className: AiInternetSearchTest description: AI全网搜测试类
 *
 * <AUTHOR>
 * @date 2025/5/8
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class MemoryAlbumTest {

    @Resource
    private GenerateMemoryService memoryService;

    /**
     * 阿里云标准搜索接口——测试方法
     */
    @Test
    public void testMemoryAlbumGenerate() {
        ArrayList<String> fileIdList = new ArrayList<>();
        fileIdList.add("FmAvJv6sbnCJZLCw_YndMcI8GheMmmFMi");
        fileIdList.add("FjKwq-dLTW1M87tLp-OVBBpuhZqVfUJRT");
        fileIdList.add("Fn16_bzDd7IVfEyZVhQNOr4hT8zO9XiOF");
        fileIdList.add("FhPWEkdzxsbIK66hD9kdNgJP4yM1Rcn6y");
        fileIdList.add("FjQPPJSZcenG-UH8IV0JGP5oOg3mr37Vx");
        fileIdList.add("FiidCGO8d-kzmS-LSFWVP950qDkRinjVM");
        fileIdList.add("FiWXKvUhhGLztqgNjz35GVp5ohuHF4te8");
        String userId = "1208001780132767705";
        Integer ownerType = 1;
        ImageSelectionReqDTO reqDTO = new ImageSelectionReqDTO();
        reqDTO.setFileIds(fileIdList);
        reqDTO.setUserId(userId);
        reqDTO.setOwnerType(ownerType);
        AlbumConditionEntity build = AlbumConditionEntity.builder()
                .searchRule(AlbumConditionRuleEnum.WEEK.getName())
                .searchSize(null).build();
        List<AlbumConditionEntity.DateRange> dataRangeList = new ArrayList<>();
        AlbumConditionEntity.DateRange buidRange = AlbumConditionEntity.DateRange.builder().startDate("2024-01-01").endDate("2025-01-01").build();
        dataRangeList.add(buidRange);
        AlbumConditionEntity.DateRange buidRange1 = AlbumConditionEntity.DateRange.builder().startDate("2025-04-01").endDate("2025-06-01").build();
        dataRangeList.add(buidRange1);
        build.setDateRangeList(dataRangeList);
        reqDTO.setAlbumCondition(build);
        QualityAlbumSelectionRespDTO respDTO = memoryService.generateMemoryAlbum(reqDTO);
        //DateUtil.parse("2024-06-06 00:00:01", CommonConstant.YYYY_MM_DD)
        log.info("生成相册参数:{}", JSONUtil.toJsonStr(respDTO));
    }

    /**
     * 阿里云标准搜索接口——测试方法
     */
    @Test
    public void jsonStrMemoryAlbumGenerate() {
        String jsonStr = "{\"fileIds\":[\"Foj_gzIV5cyH1J94LKa1FcbfUOmLj-rwh\",\"Fr7ZjtpHrGnQtKGl2Dq5Ic7pduRj3aNV0\",\"FoIZtIcIYPBov6MpzBlhEJrVtt4-km_Ma\",\"FiWV0xzCeIbWz30Iyi_hKLZyOrmlWHe61\",\"Fheb26KjxafNN366k369DmZANLEbOcqbz\",\"FrO4s6k9BFVWhVGw_FOtPY7kF9kQpwtpV\",\"FhdNHfin8g46PSaKcTi5PLZA4ndBPf0yO\",\"FnZz_LAsZu4WGr2BQA8hO74h3JYJtmnSF\",\"FpAjT_Gu63RLkyc7Fuy5OlbHjCVINORIS\",\"Fr4Jz5q1Ab5hLVHBiPkFP7rppqUjLw6CY\",\"Fgrl0aIiBfUsbvPE1srZHbpdSrsbuArJL\",\"Fl3EkFu1h-wz5nkDvhb9O5YKa_riL4jQM\",\"FmYnQKjz8YtoYFqMxdLBISIxiCoRaf63a\",\"Fp2UfbeBdZMg0CvMl5CxKobKOxcOG3qvI\",\"FsrmVBtzDk-pJ__qhigpG8qdST6i6QFzq\",\"FhQqC2oubpGTSIkKHk1FPepDhWHTtGGtk\",\"FthnXDtN7OhQjKoqLbXpCCqPyDaC1-PUU\",\"Fg2nZpS2aMFRJFJjR22dGDZaCAwtLGf9U\",\"Fmb2s2WeSmVvdF7nkUvxHPIxWdncBEVZb\",\"FoFLxDmMJSLDmYUAugtxOdbW5K6AFioew\",\"FuXLGp2SNsE6VvLqSyWxFhayZHAkCDn9O\",\"Fufsh9EbLX1Wl4-OXPKVCjawQ-1ogSJBV\",\"FnifoPctqly_Dzb42nTtLIovMMpOt6Vgv\",\"FkSYhFC68LS964RedjR5C64TN-7pIP-Iv\",\"Fluo1DBX4TfP4Jnv3Q4VAWYMB76Jze4Lz\",\"FitEt4KynRUBT5ECvJTtNIZ86907KJIpA\",\"Fi5V3aNG98q8DnaMAe5dKA55-rca3Pj2v\",\"FgJJC1GcYIN79qyvtozhOFJV5mHoBm-_e\",\"FvL__kuSHGBcKzjJ2rjtCtalUJTyCBNcX\",\"FvZtslf3BQRrsHpqY8mZGzKhwtjubQo4a\",\"FjGgin-flFqerOFZaqd5MnJmD-DGBZtmn\",\"Fl-3qFaUTGWBm94rjMSpDjYIGMLvDkNZg\",\"FnR2H84d7r13kaxwiWAJC0oj2XV3h-GBd\",\"FqdxNGcIIU4GnVgSfDt1Nqrw3l7eki5yB\",\"FqKBIiNN9sRUyIan3teRIib1Lkia1_n4V\",\"FkXXTShZBknm5Z1NzTdVGjISeCeRwwl15\",\"Ft0v9ZQH4z9hSmDoGuK1AcaKgJ8tnewDY\",\"Fl-ouXRdx_MdrivCMhABProIB9PNx8jPH\",\"FnsmRNOmKjugpM64IYKhIG4siS5qPCUHo\",\"FjjL5ESBXOYpirDiSunZCDJvZI79G1PaK\",\"Ft8LWK7SO4IvaMqy1zglD8aIpDIXSDS-L\",\"Fj-FLXGDISnp7dgXQlXVGIpoKkfIGS4V6\",\"FrNvA3ltgoqPYza40YCBHbrkwGnA9422j\",\"FjqOXWD89iLlRxp2jjP1O-ZtITfZZvke5\",\"FqLBDWW9v_6ZDp899PsdAuL1bmfcJ7DCm\",\"FiQdq4jI6P7cG3T31ez9Hi5zssExUufC3\",\"FlXW5hTPV09Px_G_i-FFMxYCeYytVVhzT\",\"FiugdtbcWjep2ePVzk75NtJ8DxxvRlULq\",\"FuL1BewNlV7XlchZvlDFE-61Wm9Vl5pi1\",\"FmvUMRSiEpSCPaEOjmghBko8e1utOB2og\",\"FrRwTY56Zu1WC2A0lre1Nrbj3yc34GnRV\",\"FqJ_e3H7SVxT7WzzyAV9IHb10BHIYUZgU\",\"FoU4lPL9LnIz6zEuhSANA-LSl_5LZyFOM\",\"Fv61pBXkfm12co98psHNAOKpGs6sfnFRd\",\"FohrvCwpnxyDxFLUum2NA4LfxNaVs5Agg\",\"Fgf5_YsQ_C8wlOHZcNwdGt5QVpcyivMTM\",\"FrUV5bRO-pApqG7AEjLFKRLiuo8N1PWsK\",\"FskgXTUHeR8ziY0I3mbhDFaejzeMnXYjM\",\"FlLJDJUA6aOTLu_o-PmRLmYFZmYsmuafk\",\"FsHKv-yJQYxL9vkx_abBJmKWZdVVE06wS\",\"Fv4tUIkx7OkAJtJqK0gNJVKpgjowq-PVA\",\"FhnS7yk8AWYDjsOR_BWpIk5OfYWQpg5aA\",\"FvnZDMr_g00auP8lugNtAYqudmZzZYxxG\",\"FugAGRizNThbUywtvTgBAMa_r3OhKToEW\",\"FjZ-iBeDE1admYuI4E29Lyph0eKuGRxqn\",\"FrM_N0g70c6Es1L3hAXhHyrkkF3xod7yh\",\"FmL2NS32cPmypF7_mgfdNNo1WV-UbH_Fs\",\"FqSvKNQ38yO2YBas92UNFDLzAEJtrfwft\",\"FsO0XTbdUvX9A8rYZPVhFc6UGzePR13J_\",\"Fhh8Xr9gRfZpMwQ948LNJbpP0zQG-krKa\",\"FrbYMCIr9c4EVMv9yTadCVLhd1qbsfryB\",\"FvKJf5npNFtEa1CovuyRH96lJhUgcztnR\",\"Fr9_BA7uvaG-juY4aiDRDsLo0G63dLKdv\",\"Fp9bEdG-SW-UE4VhYF0lF_bI9HtoJEZTl\",\"Fvw07TVIs9JOfinyWV3dONarm4eM0rzuT\",\"FoquqMsm5cs-n8BqGadtGgrdAcJyvOr3P\",\"FhRKiv2RlN_Kj8pkMElVHrJD5eBEC5vjy\",\"FkCP_fB6VfwP9e7FE8odGH4XIJdJ4FrAD\",\"FgknJrAYCfUxuFXsmdbtJZJeiEwJggbJM\",\"FnoPPLSfVXLct71JGYmdOQYtoFYNBVpO3\",\"FlguSz_PMdOdmMGxpdJFKuYPgSGGVT7vn\",\"FrBF5fgUn4SWM22A-HIZHybn6o9BjpC4l\",\"Foas5bDud_lNTb5hLIUNKA7RA48JdHjFT\",\"Fo72oQObYfEr-W9AIRXtGMbZWcu6AW7NK\",\"Fpg4x_diPfXuRAgZqxWRJ8LPl61O-DLJ7\",\"FicoJXrZ4mOCF6OYimHJPtJwh0_DQ-1fg\",\"FlcMq0oegBn5j068BkbZBzYAo8HzhI8l-\",\"FurIB1BxTOX2wIciBMJ9CwK9Z23p60PZ9\",\"FppH-7GxFe6jbW599RFdOTbN6JEIKxrSo\",\"Fv7oacijYmkEOHVEUMclARapRwNxOW1VB\",\"FgaZUxKbFKNA69qDyRuBP4ZRNjmrARufQ\",\"Fv-6PsxCcC6hd2jbg_ZZFAqoFVR12H8So\",\"FqhPCkmY-vg1p7dqblQVC2L_4GDwAvXEN\",\"Fl_dUu3NnoV_5l9uhiVFOU4IcjhUV5G5f\",\"Fud12f8hRG-LE9uD2ZY1H46w2rNGu0tTi\",\"FvA-Srz47lQq3NkHycFVKtKnkSAFYuFoK\",\"Fky3wsfRBzBAF6je8EctEpYbGKh-SwgMQ\",\"FrCscvQ3rRM9QRMzP9ddJabnAxhNraIvP\",\"FpF9rXTXyQx8XrLHBTcBD-7G0sfNTcYwf\",\"FrGGa_fXEW2x8DsyZw8JGXrmKQFOTR5Rs\",\"FmgHCqxylfoIHuhW8pyRMRY_qGAV6JrGC\",\"FoU3SHJWCNed7AebZ3oZBfbSmCLLzAfrn\",\"FhqRInyM27ZxX80n5Nj1Ek5NPkjFFtSKc\",\"Fm0nwfmazTitXwG9CHGBNAI6iKtAAMIEr\",\"FghfYmJ5csuDH-G_FHABEK5f8Ajb4333g\",\"FmeB112G9IHD_J79eBK9D44wLr3kHPu9w\",\"FvvKk_oZIzS-vojLHHglCEKsZflDgywIv\",\"FgrgDMJ_kJ03fHRPmbwpFq5dT2Z75Z-hN\",\"Fls3pYX4jrCAD8LmoPthHTIMmM88YoGMg\",\"FhDX9DofRIcPt30ceSf5MMZHeJ6DhUu7D\",\"FsOnLhN5Clzz5HpyKptdP0qUCESq4wVg8\",\"Fv8k1nCBh-WtCA5Mw459P1Koi7zJG4jZr\",\"Fq1CRem0iuzJuIfKSsghMAr67S9QLoXQy\",\"FhUuAvio5Mc9NP8tK1XhJ-pCgWhBMuv7P\",\"Fqzz0s71ZyAuNeF2PKY1L277XLh3b2gcL\",\"FsCy0YIYe5WQ2WVLvjGhCTaXHbs7gnSpk\",\"FiJ3exqJZCxEOWocPbtJLXZ12BGjE2sQR\",\"Fmfjh_6nRZ4OAPP3XpfpCPIwTO1HPUqiD\",\"FuJxmyyP7WUSWCFRKzAJIgK13vGVFeJZE\",\"FtGZCnYXEj3m5JQt-HaFEnaGNmDPjR0B5\",\"Fh3h1GPn-kY8_S700PVRKOpKTr7affV6P\",\"Fhia2V9Zuejn3jpUv_LNDf5PNbPmw7bU5\",\"Ftdjzmw7_PhZ8V7qSRn9CIaAzKTVofPEW\",\"FglzQjCpSPGhI-XERWqNA_pe3CiJM0fNo\",\"FqnR5UQ2HNUPytXbN5dpMFL-fo_9rBPpD\"],\"userId\":\"1188320236378850540\",\"ownerType\":1,\"albumCondition\":{\"dateRangeList\":[{\"startDate\":\"2022-01-01\",\"endDate\":\"2024-12-31\"}],\"searchRule\":\"每季度\",\"searchSize\":1}}";
        ImageSelectionReqDTO reqDTO = JSONUtil.toBean(jsonStr, ImageSelectionReqDTO.class);
        QualityAlbumSelectionRespDTO respDTO = memoryService.generateMemoryAlbum(reqDTO);
        log.info("相册生成参数:{}", JSONUtil.toJsonStr(respDTO));
    }
    
}