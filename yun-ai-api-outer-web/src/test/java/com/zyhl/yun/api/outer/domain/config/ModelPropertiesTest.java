package com.zyhl.yun.api.outer.domain.config;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.config.ModelProperties;

import lombok.extern.slf4j.Slf4j;

/**
 * 渠道号测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class ModelPropertiesTest {

	@Resource
	private ModelProperties modelProperties;

	@Test
	public void all() {
		System.out.println("getLimitXiaoTianList:" + JsonUtil.toJson(modelProperties.getLimitXiaoTianList()));
		System.out.println("getLimitYunMailList:" + JsonUtil.toJson(modelProperties.getLimitYunMailList()));
		System.out.println("getLimitCloudPhoneList:" + JsonUtil.toJson(modelProperties.getLimitCloudPhoneList()));
	}

	@Test
	public void map() {
		System.out.println("map:" + JsonUtil.toJson(modelProperties.limitMap(null)));
	}
}
