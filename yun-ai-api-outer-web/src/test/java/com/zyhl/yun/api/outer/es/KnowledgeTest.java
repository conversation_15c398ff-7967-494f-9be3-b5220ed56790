package com.zyhl.yun.api.outer.es;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.CharsetDetector;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.enums.*;
import com.zyhl.hcy.yun.ai.common.base.es.domain.datahelper.repository.EsCommonRepository;
import com.zyhl.hcy.yun.ai.common.base.es.entity.CommonKnowledgeEsEntity;
import com.zyhl.hcy.yun.ai.common.base.es.properties.ElasticSearchPropertiesTwo;
import com.zyhl.hcy.yun.ai.common.base.es.vo.PersonalKnowledgeEsVO;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.StopWatchUtil;
import com.zyhl.hcy.yun.ai.common.base.utils.VectorUtil;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.vo.TextFeatureExtractVO;
import com.zyhl.yun.api.outer.external.CmicTextService;
import com.zyhl.yun.api.outer.util.CustomStringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 渠道号测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class KnowledgeTest {

    @Resource
    private CmicTextService cmicTextService;

    @Resource
    private EsCommonRepository esCommonRepository;

    @Resource
    private RestHighLevelClient restHighLevelClientTwo;

    @Resource
    private ElasticSearchPropertiesTwo esPropertiesTwo;

    private static final String ES_RESULT_SOURCE_AS_MAP = "sourceAsMap";

    @Test
    public void memorySplit_test() {
        //读取100w字 txt文件 按1024切分
        StopWatch stopWatch = StopWatchUtil.createStarted();
        String resultPath = "D:\\100w.txt";
        File file = FileUtil.file(resultPath);
        Charset detectedCharset =detectCharset(file);
        StringBuffer stringBuffer = new StringBuffer();
        try (BufferedReader br = FileUtil.getReader(file, detectedCharset)) {
            String line;
            while ((line = br.readLine()) != null) {
                stringBuffer.append(line);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        //按1024切割
        List<String> list = splitStringBy1024(stringBuffer.toString());
        List<TextFeatureExtractVO> featureExtractVOList = new ArrayList<>();
        list.forEach(s -> {
            TextFeatureExtractVO textFeatureExtractVO = cmicTextService.getTextFeature(s);
           featureExtractVOList.add(textFeatureExtractVO);
        });
        String inputString = "贾宝玉林黛玉";
        TextFeatureExtractVO inputExtractVO = cmicTextService.getTextFeature(inputString);
        //比较查找向量化数据
        double[] inputFeatureArray = inputExtractVO.getFeature().stream().mapToDouble(BigDecimal::doubleValue).toArray();
        for (TextFeatureExtractVO textFeatureExtractVo : featureExtractVOList) {
            double[] textFeatureArray = textFeatureExtractVo.getFeature().stream().mapToDouble(BigDecimal::doubleValue).toArray();
            // 计算余弦相似度
            double cosineSimilarity = VectorUtil.cosineSimilarity(inputFeatureArray, textFeatureArray);
            float result =  (float) cosineSimilarity;
            System.out.println(result);
        }

    }

    public static Charset detectCharset(File file) {
        Charset detectedCharset;
        try {
            detectedCharset = CharsetDetector.detect(file);
        } catch (Exception e) {
            // 如果检测失败，则使用默认的 UTF-8 编码
            log.info("文件编码检测失败:", e);
            detectedCharset = CharsetUtil.CHARSET_UTF_8;
        }
        // 返回检测到的编码或默认编码
        return detectedCharset != null ? detectedCharset : CharsetUtil.CHARSET_UTF_8;
    }
    public static List<String> splitStringBy1024(String inputString) {
        List<String> result = new ArrayList<>();
        for (int i = 0; i < inputString.length(); i += 1024) {
            result.add(inputString.substring(i, Math.min(i + 1024, inputString.length())));
        }
        return result;
    }
    /***
     * 分数匹配查询
     */
    @Test
    public void scoreEsquery() {
        String text = "四大名著";
        String baseId = "common";
        TextFeatureExtractVO textFeatureExtractVO = cmicTextService.getTextFeature(text);
        List<BigDecimal> feature = textFeatureExtractVO.getFeature();
        List<CommonKnowledgeEsEntity> knowledgeList = new ArrayList<>();
        // KNN 查询的 JSON 字符串
        String knnQueryJson = "{ " +
                "  \"_source\": {\n" +
                "    \"excludes\": [\n" +
                "      \"embeddings\"\n" +
                "    ]\n" +
                "  }," +
                "\"query\": {" +
                "    \"bool\": {" +
                "      \"must\": [" +
                "        {" +
                "          \"term\": {" +
                "            \"is_deleted\": {" +
                "              \"value\": 0," +
                "              \"boost\": 1" +
                "            }" +
                "          }" +
                "        }," +
                "        {" +
                "          \"term\": {" +
                "            \"is_effect\": {" +
                "              \"value\": 1," +
                "              \"boost\": 1" +
                "            }" +
                "          }" +
                "        }," +
                "        {" +
                "          \"term\": {" +
                "            \"base_id\": {" +
                "              \"value\":\"" + baseId + "\"" +
                "            }" +
                "          }" +
                "        }," +
                "        {" +
                "          \"exists\": {" +
                "            \"field\": \"embeddings\"" +
                "          }" +
                "        }" +
                "      ]," +
                "      \"should\": [" +
                "        {" +
                "          \"rank_feature\": {" +
                "            \"field\": \"rank\"," +
                "            \"boost\": 2," +
                "            \"log\": {" +
                "              \"scaling_factor\": 4" +
                "            }" +
                "          }" +
                "        }," +
                "        {" +
                "          \"match\": {" +
                "            \"text\": {" +
                "              \"query\": \"" + text + "\"" +
                "            }" +
                "          }" +
                "        }," +
                "        {" +
                "          \"match\": {" +
                "            \"question\": {" +
                "              \"query\": \"" + text + "\"" +
                "            }" +
                "          }" +
                "        }" +
                "      ]," +
                "      \"minimum_should_match\": 2" +
                "    }" +
                "  }," +
                "  \"rescore\": {" +
                "    \"window_size\": 100," +
                "    \"query\": {" +
                "      \"rescore_query\": {" +
                "        \"knn_nearest_neighbors\": {" +
                "          \"vec\":" + feature + "," +
                "          \"field\": \"embeddings\"," +
                "          \"similarity\": \"l2\"," +
                "          \"model\": \"lsh\"," +
                "          \"candidates\": 99999" +
                "        }" +
                "      }," +
                "      \"query_weight\": 0," +
                "      \"rescore_query_weight\": 1" +
                "    }" +
                "  }," +
                "  \"from\": 0," +
                "  \"size\": 10" +
                "}";
        System.out.println(knnQueryJson);
        // 处理查询结果
        String response = esCommonRepository.dslQuery("GET", "/common_knowledge_*/_search", knnQueryJson);
        JSONObject jsonObject = JSONUtil.parseObj(response);
        if (jsonObject.get("hits") != null) {
            String hitsString = jsonObject.get("hits").toString();
            if (StrUtil.isNotEmpty(hitsString)) {
                JSONObject hitObject = JSONUtil.parseObj(hitsString);
                JSONArray standings = JSONUtil.parseArray(hitObject.get("hits"));
                for (Object object : standings) {
                    JSONObject sourceJson = JSONUtil.parseObj(object);
                    String sourceAsString = sourceJson.get("_source").toString();
                    CommonKnowledgeEsEntity entity = JSONUtil.toBean(sourceAsString, CommonKnowledgeEsEntity.class);
                    knowledgeList.add(entity);
                }
            }
        }
        log.info("list:{}", knowledgeList);
        //commonKnowledgeDomainService.getCommonKnowledgeResult("common", text, feature);
    }


    @Test
    public void scoreEsqueryBuffer() {
        String text = "\"咄\"猜一成语";
        text = CustomStringUtil.getEsText(text);
        String baseId = "common";
        TextFeatureExtractVO textFeatureExtractVO = cmicTextService.getTextFeature(text);
        List<BigDecimal> feature = textFeatureExtractVO.getFeature();
        List<CommonKnowledgeEsEntity> knowledgeList = new ArrayList<>();
        // KNN 查询的 JSON 字符串
        StringBuilder knnQueryJson = new StringBuilder();

        knnQueryJson.append("{ " + "  \"_source\": {" + "    \"excludes\": [" + "      \"embeddings\"" + " " +
                        "   ]\n" + "  }," + "\"query\": {" + "    \"bool\": {" + "      \"must\": [" + "  " +
                        "      {" + "          \"term\": {" + "        " +
                        "    \"is_deleted\": {" + "         " +
                        "     \"value\": 0," + "            " +
                        "  \"boost\": 1" + "            }" + "  " +
                        "        }" + "        }," + "        {" + "     " +
                        "     \"term\": {" + "            \"is_effect\": {" + "   " +
                        "           \"value\": 1," + "              \"boost\": 1" + "    " +
                        "        }" + "          }" + "        }," + "        {" + "     " +
                        "     \"term\": {" + "            \"base_id\": {" + "          " +
                        "    \"value\":\"").append(baseId).append("\"").append("     " +
                        "       }").append("          }").append("        },").append(" " +
                        "       {").append("          \"exists\": {").append("   " +
                        "         \"field\": \"embeddings\"").append("          }").append("  " +
                        "      }").append("      ],").append("      \"should\": [").append(" " +
                        "       {").append("          \"rank_feature\": {").append(" " +
                        "           \"field\": \"rank\",").append("            \"boost\": 2,").append("   " +
                        "         \"log\": {").append("              \"scaling_factor\": 4").append("   " +
                        "         }").append("          }").append("        },").append("  " +
                        "      {").append("          \"match\": {").append("     " +
                        "       \"text\": {").append("              \"query\": \"").append(text).append("\"").append("            }")
                .append("          }").append("        },").append("        {").append("          \"match\": {").append("            \"question\": {")
                .append("              \"query\": \"").append(text).append("\"")
                .append("            }").append("          }").append("        }").append("      ],").append("      \"minimum_should_match\": 2")
                .append("    }").append("  },").append("  \"rescore\": {").append("    \"window_size\": 100,").append("    \"query\": {")
                .append("      \"rescore_query\": {").append("        \"knn_nearest_neighbors\": {")
                .append("          \"vec\":").append(feature).append(",").append("          \"field\": \"embeddings\",")
                .append("          \"similarity\": \"cosine\",").append("          \"model\": \"lsh\",")
                .append("          \"candidates\": 99999").append("        }").append("      },")
                .append("      \"query_weight\": 0,").append("      \"rescore_query_weight\": 1")
                .append("    }").append("  },").append("  \"from\": 0,").append("  \"size\": 10").append("}");
        System.out.println(knnQueryJson);
        // 处理查询结果
        String response = esCommonRepository.dslQuery("GET", "/common_knowledge_*/_search", knnQueryJson.toString());
        JSONObject jsonObject = JSONUtil.parseObj(response);
        if (jsonObject.get("hits") != null) {
            String hitsString = jsonObject.get("hits").toString();
            if (StrUtil.isNotEmpty(hitsString)) {
                JSONObject hitObject = JSONUtil.parseObj(hitsString);
                JSONArray standings = JSONUtil.parseArray(hitObject.get("hits"));
                for (Object object : standings) {
                    JSONObject sourceJson = JSONUtil.parseObj(object);
                    String sourceAsString = sourceJson.get("_source").toString();
                    CommonKnowledgeEsEntity entity = JSONUtil.toBean(sourceAsString, CommonKnowledgeEsEntity.class);
                    knowledgeList.add(entity);
                }
            }
        }
        log.info("list:{}", knowledgeList);
        //      commonKnowledgeDomainService.getCommonKnowledgeResult("common", text, feature);
    }

    /**
     * 多index多routing查询
     *
     * @Author: WeiJingKun
     */
    @Test
    public void multipleIndexAndMultipleRoutingQueries() {
        List<PersonalKnowledgeEsVO> resultList = new ArrayList<>();

        // 构建查询条件
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        List<String> chunkIdList = new ArrayList<>();
        chunkIdList.add("FmyAe9F7jyibokHG0pLNDNYkf8cmD4RGm_SPLIT_1024_5");
        chunkIdList.add("FkuhwirtAsgcULSBJrbBDI4a0zFJKQmmH_SPLIT_512_7");
        boolQuery.must(QueryBuilders.termsQuery(EsPersonalKnowledgeEnum.ID.getCode(), chunkIdList));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().query(boolQuery).from(0).size(chunkIdList.size());
        // 构建搜索请求
        List<String> indexList = ListUtil.toList(
                "personal_knowledge_rag_7", "personal_knowledge_rag_5");
//        List<String> indexList = ListUtil.toList(
//                "personal_knowledge_rag_5", "personal_knowledge_rag_7");
//        List<String> indexList = ListUtil.toList("personal_knowledge_rag_5");
//        List<String> indexList = ListUtil.toList("personal_knowledge_rag_7");
        String[] array = ArrayUtil.toArray(indexList, String.class);
        SearchRequest searchRequest = new SearchRequest(array, searchSourceBuilder);
        searchRequest.routing("1116192011595358845", "1076042092524486747", "1200786731143487566");
//        searchRequest.routing("1116192011595358845");
//        searchRequest.routing("1076042092524486747");
        System.out.println(searchRequest);
//            searchRequest.source(searchSourceBuilder);
        try {
            // 执行搜索
            SearchResponse searchResponse = restHighLevelClientTwo.search(searchRequest, RequestOptions.DEFAULT);
            // 处理搜索结果
            SearchHits searchHits = searchResponse.getHits();
            if (searchHits.getTotalHits().value > 0) {
                for (SearchHit hit : searchHits.getHits()) {
                    JSONObject jsonObject = JSONUtil.parseObj(hit);
                    if (jsonObject.get(ES_RESULT_SOURCE_AS_MAP) != null) {
                        String hitsString = jsonObject.get(ES_RESULT_SOURCE_AS_MAP).toString();
                        PersonalKnowledgeEsVO knowledge = JSONUtil.toBean(hitsString, PersonalKnowledgeEsVO.class);
                        knowledge.setEsScore(hit.getScore());
                        resultList.add(knowledge);
                    }
                }
            }
        } catch (IOException e) {
            log.error("【RAG重要节点日志】【个人知识库-查询es-{}】异常：", "methodLog", e);
            throw new YunAiBusinessException(YunAiCommonResultCode.ES_OPERATOR_EXCEPTION);
        } finally {
            log.info("结束：{}", JsonUtil.toJson(resultList));
        }
    }

}
