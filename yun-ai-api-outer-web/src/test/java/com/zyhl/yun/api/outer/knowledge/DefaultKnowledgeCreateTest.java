package com.zyhl.yun.api.outer.knowledge;

import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.domainservice.UserKnowledgeDomainService;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;

/**
 * 默认知识库创建测试类
 */
@Slf4j
@ExtendWith(SpringExtension.class)
@SpringBootTest
class DefaultKnowledgeCreateTest {

	@Resource
	private UserKnowledgeDomainService knowledgeDomainService;

	@Test
	void test() {

		RequestContextHolder.HEADER_PARAMS.set(new RequestContextHolder.HeaderParams());
		RequestContextHolder.getHeaderParams().setClientInfo("||2|1.0.2||Nexus 5|||android 6.0|||||");
		RequestContextHolder.getHeaderParams().setAppChannel("101");
		RequestContextHolder.setUserId("1105420961611622272");

		UserKnowledgeEntity entity = knowledgeDomainService.createDefaultKnowledge();
		System.out.print("entity:" + JsonUtil.toJson(entity));
	}
}
