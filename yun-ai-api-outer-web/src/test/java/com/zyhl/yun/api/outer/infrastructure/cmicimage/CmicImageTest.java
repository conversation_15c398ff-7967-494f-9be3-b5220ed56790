package com.zyhl.yun.api.outer.infrastructure.cmicimage;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;

import org.jetbrains.annotations.Nullable;

import com.zyhl.hcy.yun.ai.common.model.api.utils.ImageProcessorUtil;
import com.zyhl.hcy.yun.ai.common.model.api.utils.ImageProcessorUtil.ImageInput;
import com.zyhl.hcy.yun.ai.common.model.api.utils.ImageProcessorUtil.ImageOutput;
import com.zyhl.hcy.yun.ai.common.model.api.utils.TextModelUtil;
import com.zyhl.hcy.yun.ai.common.model.api.utils.auth.VolcAuthUtils;

import cn.hutool.http.ContentType;
import cn.hutool.json.JSONUtil;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;

/**
 * 图片处理测试类
 */
public class CmicImageTest {

	public static void main(String[] args) throws IOException {
		File srcFile = new File("E://data//ImageCompress//1.png");
		ImageInput input = new ImageInput(srcFile, 400, 400, 0.9, 2L * 1024 * 1024, null);
		ImageOutput out = ImageProcessorUtil.processImage(input);
		System.out.println(JSONUtil.toJsonStr(out));
	}

}