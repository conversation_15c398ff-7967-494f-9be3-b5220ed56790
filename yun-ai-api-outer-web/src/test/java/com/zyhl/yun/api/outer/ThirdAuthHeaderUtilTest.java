package com.zyhl.yun.api.outer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.zyhl.hcy.plugin.neauth.util.ThirdAuthHeaderUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

/**
 * <AUTHOR>
 *
 *         2024年11月14日
 */
@SpringBootTest()
@Slf4j
@RunWith(SpringRunner.class)
public class ThirdAuthHeaderUtilTest {

	@Test
	public void getCurl() {
		String json = "";
		log.info(get(JSON.parseObject(json)));
	}

	public static void main(String[] args) {
		String json = "{\r\n" + "    \"appSecret\": \"5yaMATbPPo_xDAhB\",\r\n"
				+ "    \"appSecretId\": \"1123749895606697993\",\r\n" + "    \"appKey\": \"1069817274011779081\"\r\n"
				+ "}";
		JSONObject dto = JSONObject.parseObject(json);
		Map<String, String> map = ThirdAuthHeaderUtil.generateHeaderMap(dto.getString("appKey"),
				dto.getString("appSecretId"), dto.getString("appSecret"), UuidUtils.generateUuid(), "1.0");
		map.put("x-yun-api-version", "v3");
		for(String mapKey:map.keySet()) {
			System.out.println(mapKey + ":" + map.get(mapKey));
		}
	}

	/**
	 * 获取curl
	 * 
	 * @param dto 信息参数
	 * @return
	 */
	public String get(JSONObject dto) {
		Map<String, String> headerMap = ThirdAuthHeaderUtil.generateHeaderMap(dto.getString("appKey"),
				dto.getString("appSecretId"), dto.getString("appSecret"), UuidUtils.generateUuid(), "1.0");
		return (generateCurl(dto.getString("url"), headerMap, dto.toJSONString()));
	}

	/**
	 * 生成curl文本
	 */
	private String generateCurl(String url, Map<String, String> headers, String json) {
		byte[] body = json.getBytes();
		String entity = body == null ? "" : new String(body).replace("\n", "").replaceAll(">\\s+<", "><");
		StringBuilder builder = new StringBuilder("curl --request POST '").append(url).append("' ");
		headers.forEach((key, values) -> {
			builder.append("--header '").append(key).append(": ").append(values).append("' ");
		});
		builder.append("--data '").append(entity).append('\'');
		return builder.toString();
	}
}
