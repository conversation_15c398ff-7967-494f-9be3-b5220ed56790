package com.zyhl.yun.api.outer.application.service.impl.knowledgeBase;

import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeBatchGetReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.PersonalKnowledgeService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 个人知识库service测试类
 *
 **/
@SpringBootTest
@RunWith(SpringRunner.class)
public class PersonalKnowledgeServiceImplTest {
    @Resource
    private PersonalKnowledgeService personalKnowledgeService;

    @Test
    public void batchGet() {
        KnowledgeBatchGetReqDTO dto = new KnowledgeBatchGetReqDTO();
        dto.setSourceChannel("101");
        dto.setUserId("1167749398167871592");
        dto.setBaseIdList(java.util.Arrays.asList("1210025768425718888", "2"));
        System.out.println(personalKnowledgeService.getInfoListByIds(dto));
    }
}
