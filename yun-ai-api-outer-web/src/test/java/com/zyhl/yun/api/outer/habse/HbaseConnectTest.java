package com.zyhl.yun.api.outer.habse;


import cn.hutool.core.collection.CollUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.hbase.domain.datahelper.repository.HbaseRepository;
import com.zyhl.yun.api.outer.persistence.po.AiTextResultPO;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * hbase连接-测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class HbaseConnectTest {

    @Resource
    private HbaseRepository hbaseRepository;

    @Test
    public void test() {
        String userId = "1105420961611623080";
        String dialogId = "1178693891808256626";
        String rowKey = AiTextResultRepository.createRowKey(userId, dialogId);
        List<AiTextResultPO> list = hbaseRepository.selectList(Collections.singletonList(rowKey), AiTextResultPO.class);
        if (CollUtil.isEmpty(list)) {
            log.info("【hbase】数据不存在，rowKey:{}", rowKey);
        }

        log.info("【hbase】数据 list:{}", JsonUtil.toJson(list));
    }

}
