package com.zyhl.yun.api.outer.external.service;

import com.zyhl.yun.api.outer.config.MemberCenterProperties;
import com.zyhl.yun.api.outer.external.MemberCenterExternalService;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 会员权益接口测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class MemberCenterExternalTest {

    @Resource
    private MemberCenterExternalService memberCenterExternalService;
    @Resource
    private MemberCenterProperties memberCenterProperties;

    final String userId = "1105420961611624143";
    String consumeSeq = "1780785777689747457";

    //    13590104823
    //    13590104826
    //    13590104827
    String phone = "13590104823";

    @Test
    public void consumeBenefit() {
        consumeSeq = memberCenterExternalService.consumeBenefit(userId, memberCenterProperties.getBenefitNo());
        System.out.println("消费码：" + consumeSeq);
    }

    @Test
    public void consumeBenefitByPhone() {
        consumeSeq = memberCenterExternalService.consumeBenefitByPhone(phone, memberCenterProperties.getBenefitNo());
        System.out.println("消费码：" + consumeSeq);
    }

    @Test
    public void consumeBenefitFail() {
        memberCenterExternalService.consumeBenefitFail(userId, memberCenterProperties.getBenefitNo(), consumeSeq);
    }

}
