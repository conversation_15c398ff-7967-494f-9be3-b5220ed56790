package com.zyhl.yun.api.outer.application.service.impl;

import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.service.external.MemberCenterService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 会员权益接口测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class MemberCenterApplicationTest {

    @Resource
    private MemberCenterService memberCenterService;

    final String userId = "1105420961611624143";

    //    13590104823
    //    13590104826
    //    13590104827
    String phone = "13590104823";

    Long dialogueId = 1143453771061101297L;

    @Test
    public void consumeBenefit() {
        AlgorithmChatAddDTO dto = new AlgorithmChatAddDTO();
        final boolean result = memberCenterService.consumeBenefit(dto, phone, dialogueId);
        System.out.println("获取会员权益：" + result);
    }


    @Test
    public void consumeBenefitFail() {
        memberCenterService.consumeBenefitFail(userId, phone, dialogueId);
        System.out.println("会员权益回滚成功");
    }

}
