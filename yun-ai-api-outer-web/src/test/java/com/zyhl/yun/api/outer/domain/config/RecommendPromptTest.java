package com.zyhl.yun.api.outer.domain.config;

import com.zyhl.yun.api.outer.config.RecommendPromptTemplateProperties;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 渠道号测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class RecommendPromptTest {

    @Resource
    private RecommendPromptTemplateProperties recommendPromptTemplateProperties;

    @Test
    public void queryPrompt() {
        System.out.println(recommendPromptTemplateProperties.getQueryTemplate().getTemplate());
    }

    @Test
    public void intentionPrompt() {
        System.out.println(recommendPromptTemplateProperties.getTemplateByIntention("000"));
    }

    @Test
    public void intentionLinkUrl() {
        System.out.println(recommendPromptTemplateProperties.getLinkUrlByIntention("011"));
    }

}
