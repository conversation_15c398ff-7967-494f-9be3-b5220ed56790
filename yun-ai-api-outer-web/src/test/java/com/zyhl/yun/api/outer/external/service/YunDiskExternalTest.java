package com.zyhl.yun.api.outer.external.service;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.JaxbUtil;
import com.zyhl.hcy.yun.ai.common.base.utils.StringUtil;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.DownloadRequestReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.enums.YunAiYunDiskResultCode;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.properties.McloudNetAuthProperties;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.properties.YunDiskPerSaasProperties;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.GetContentInfo;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.ose.OseContentResult;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.ose.OseDownloadRequestResponse;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.utils.SSLUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 云盘资源接口测试
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class YunDiskExternalTest {

    @Resource
    private McloudNetAuthProperties mcloudNetAuthProperties;

    @Test
    public void getFileInfo() {
        String token = "Basic bW9iaWxlOjE4ODg0NjgxMjA4Ok1Nc29UbEZmfDF8MXwxNzM1MDkxMjM1NTY5fFZEMzZnT0l1akVzMGJLcDh3NWdCSTRaYlRZVE50QkxaY0xUSEJfNmxPbF8zcjNwdm05N3dtWkZxeG9JQ3ZDeC5DNkY3ejNxVGJ4enUycGplRi5YYURxV19sY25hWWxtMVVUb25Na3NTbWVRM3VoSXM4ZWhlbEdzM0RaelRoUFNBSFd6dWRVcGFBQ0JSQWNJYWZBYTU3ZGpkSXJ4aHdCdGR0UlNSUHpWdDdqdy0=";
        getOldFileInfo("18884681208", "Wt11rzTFe01p00020241115143203luu", token);
    }

    /**
     * 老底座获取文件信息
     *
     * @param phone  账号
     * @param fileId 文件id
     */
    private void getOldFileInfo(String phone, String fileId, String token) {
        String severUrl = "https://test.yun.139.com/adaptivelayer/siyouyun";

        OseContentResult result = getOldContentInfo(severUrl, phone, fileId, token);
        System.out.println("OSE获取文件信息：" + JsonUtil.toJson(result));

        OseDownloadRequestResponse downloadRequestV2 = oldDownloadRequestV2(severUrl, fileId, phone, false, token);
        System.out.println("OSE下载文件请求：" + JsonUtil.toJson(downloadRequestV2));
    }

    private OseContentResult getOldContentInfo(String oseServerUrl, String phone, String fileId, String token) {
        GetContentInfo getContentInfo = new GetContentInfo();
        getContentInfo.setContentID(fileId);
        getContentInfo.setOwnerMSISDN(phone);
        getContentInfo.setEntryShareCatalogID(StringUtil.EMPTY);

        String requestParam = JaxbUtil.beanToXml(getContentInfo);
        String url = oseServerUrl + YunDiskPerSaasProperties.GET_CONTENT_URL;
        HttpRequest request = HttpRequest.post(url)
                .header("Authorization", token)
//                .header("x-ne-auth", mcloudNetAuthProperties.getNetAuthConfig())
                .contentType("application/xml")
                .body(requestParam);
        try (HttpResponse resp = request.execute()) {
            String result = resp.body();
            log.info("OSE url:{} | requestParam:{} | 底座数据响应:{}", url, requestParam, result);
            OseContentResult oseContentResult = JaxbUtil.xmlToBean(result, OseContentResult.class);
            if (!YunDiskPerSaasProperties.OSE_SUCCESS_CODE.equals(oseContentResult.getResultCode())) {
                if (YunDiskPerSaasProperties.OSE_FILE_NOT_FOUND_CODE.equals(oseContentResult.getResultCode())) {
                    throw new YunAiBusinessException(YunAiYunDiskResultCode.FILE_NOT_FOUND);
                }
                throw new YunAiBusinessException(oseContentResult.getResultCode(), oseContentResult.getDesc());
            }
            return oseContentResult;
        }
    }

    private OseDownloadRequestResponse oldDownloadRequestV2(String oseServerUrl, String contentId, String acount, Boolean innerNtwk, String token) {
        DownloadRequestReqDTO req = new DownloadRequestReqDTO();
        DownloadRequestReqDTO.DownloadRequestReqV2 requestReqV2 = new DownloadRequestReqDTO.DownloadRequestReqV2();
        requestReqV2.setInline("0");
        requestReqV2.setOwnerMsisdn(acount);
        requestReqV2.setMsisdn(acount);
        requestReqV2.setContentID(contentId);
        req.setDownloadRequestReqV2(requestReqV2);
        String requestParam = JaxbUtil.beanToXml(req);
        log.info("downloadRequestV2 请求参数:{}", requestParam);
        String url = oseServerUrl + YunDiskPerSaasProperties.UPLOAD_AND_DOWNLOAD_URL;
        HttpRequest request = HttpRequest.post(url)
                .header("Authorization", token)
//                .header("x-ne-auth", mcloudNetAuthProperties.getNetAuthConfig())
                .header("x-huawei-channelSrc", mcloudNetAuthProperties.getChannelSrc())
                .header("x-UserAgent", "wap|||1.1")
                .header("x-inner-ntwk", Boolean.TRUE.equals(innerNtwk) ? "1" : "2")
                .contentType("application/xml")
                .setSSLSocketFactory(SSLUtils.getSSLSocketFactory())
                .body(requestParam);
        try (HttpResponse resp = request.execute()) {
            String result = resp.body();
            log.info("downloadRequestV2 url:{} | requestParam:{} | 底座数据响应:{}", url, requestParam, result);
            return JaxbUtil.xmlToBean(result, OseDownloadRequestResponse.class);
        }
    }
}
