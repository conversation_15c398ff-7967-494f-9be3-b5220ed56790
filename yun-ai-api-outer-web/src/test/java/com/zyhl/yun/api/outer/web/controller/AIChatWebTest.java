package com.zyhl.yun.api.outer.web.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddContentDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatContentListDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatListDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatPollingUpdateDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatStopDTO;
import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;
import com.zyhl.yun.api.outer.application.vo.FlowTypeResultVO;
import com.zyhl.yun.api.outer.controller.AssistantController;
import com.zyhl.yun.api.outer.domain.vo.chat.ContentResultVO;
import com.zyhl.yun.api.outer.domain.vo.chat.ContentResultVOV2;
import com.zyhl.yun.api.outer.domain.vo.chat.ContentVO;
import com.zyhl.yun.api.outer.domain.vo.chat.MessageVO;
import com.zyhl.yun.api.outer.domain.vo.common.PageInfoVO;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * AI报名测试
 */
@SpringBootTest
@Slf4j
class AIChatWebTest {

    @Resource
    private AssistantController assistantController;


    private final static String domain = "http://127.0.0.1:19027/ai/api/outer";
//    private final static String domain = "https://test.yun.139.com/ai/ai-test/ai/api/outer";


    public static void main(String[] args) {
//        chatDialogueList();
//        chatDialogueAddV1();
//        chatDialogueAddV1Continue();
//        chatDialogueAddV2Continue();
//        chatDialogueAddV2();
//        chatStop();

//        String key = "|1|||||||3";
//        System.out.println(key.split("\\|").length);

//        String key = "3fda899ca6d94ac3";
//        AES aes = SecureUtil.aes(key.getBytes());
//        System.out.println(aes.decryptStr("6eb2872aa022b13d15699d36a33cfd28"));
//        System.out.println(aes.decryptStr("13cae4de4887db13049ca9626b88a78d"));
//        System.out.println(aes.decryptStr("ce9a98aeee8f28adefca769c9eccd54f"));
//        System.out.println(aes.decryptStr("f53441523a6204261033db058aa6ac9a"));
//        System.out.println(aes.decryptStr("dbac32d9220adffe984524ff2afbc117"));
//        System.out.println(aes.decryptStr("bd6fc091d813070516a5b1d022fdba21"));
//        System.out.println(aes.decryptStr("a715d6e4b6c185e344bb899207ab35d6"));
//        System.out.println(aes.decryptStr("a3e11e22bb28a0ae9ab641af9a3874c8"));
//        System.out.println(aes.decryptStr("7a2d7c709807df97102ba507a86e7d05"));
//        System.out.println(aes.decryptStr("ebe806657dc2b1f77e97f76a7276f0dc"));
//        System.out.println(aes.decryptStr("76da8cc8598a07adedd7484fb552d99d"));
//        System.out.println(aes.decryptStr("c0c8892627863d765584e5bca8325456"));
//        System.out.println(aes.decryptStr("bdd9c50721ee4cb24ca5c06df8065773"));
//        System.out.println(aes.decryptStr("80936b06a7fee4fd91a80b6169318044"));
//        System.out.println(aes.decryptStr("3e1ffabc5e58a5d07a8d17152b8abc90"));
//        System.out.println(aes.decryptStr("b658d0f41907a438dc04c8a3c2d27d24"));
        System.out.println(StringUtils.isNotBlank(" \t"));
        System.out.println(StringUtils.isNotEmpty(" "));
    }

    /**
     * 聊天会话列表
     */
    @Test
    public void chatSessionList_chat() {
        final AlgorithmChatListDTO dto = new AlgorithmChatListDTO();
        dto.setUserId("1064201535554158611");
        dto.setSourceChannel("10108");
        dto.setApplicationType(ApplicationTypeEnum.CHAT.getCode());

        final BaseResult<PageInfoVO<MessageVO>> result = assistantController.chatList(dto);
        System.out.println("响应结果：" + JsonUtil.toJson(result));
    }


    /**
     * 聊天会话列表
     */
    @Test
    public void chatSessionList_agent() {
        final AlgorithmChatListDTO dto = new AlgorithmChatListDTO();
        dto.setUserId("1064201535554158611");
        dto.setSourceChannel("10108");
        dto.setApplicationType(ApplicationTypeEnum.INTELLIGENT.getCode());

        final BaseResult<PageInfoVO<MessageVO>> result = assistantController.chatList(dto);
        System.out.println("响应结果：" + JsonUtil.toJson(result));
    }

    /**
     * 聊天对话列表
     */
    @Test
    public void chatContentList() {
        final AlgorithmChatContentListDTO dto = new AlgorithmChatContentListDTO();
        final BaseResult<PageInfoVO<ContentResultVO>> result = assistantController.chatContentListV1(dto);
        System.out.println("响应结果V1：" + JsonUtil.toJson(result));

        final BaseResult<PageInfoVO<ContentResultVOV2>> resultV2 = assistantController.chatContentListV2(dto);
        System.out.println("响应结果V2：" + JsonUtil.toJson(resultV2));
    }

    /**
     * 聊天对话列表
     */
    public static void chatDialogueList() {
        final String token = "Basic bW9iaWxlOjE5ODAyMDIxMTU4OkdZV3JKdWdqfDF8MXwxNzE1OTk5NzIyOTg5fFlxck9KX0pocWc2VG91MWlkT2xBSlBXTDNHVWowS25EU2Y1VGEuTkJhYy5TTS5nZUZJRnpYUkgwYkZZRmR2UEdRakJNZ2hkMzIybWNJVjRnM2Iud3U1c1VxUVBqeDhTNWg4VmM4Unh4N1c3MjdMcFRkOVJ5Yzk1MlRsb2FUckpwUEhxbGVoZXJ3VmY3d1RKZzR1cW53bHlCUDd2dnFaWFhDeUtFVDkwYzZDay0=";

        // 请求参数
        final AlgorithmChatContentListDTO params = new AlgorithmChatContentListDTO();
//        params.setUserId("1064201535554158611");
        params.setApplicationType(ApplicationTypeEnum.INTELLIGENT.getCode());
        params.setSourceChannel("101601");
//        params.setSessionId(1L);

        final HttpRequest request = HttpUtil.createPost(domain + "/assistant/chat/contentList");
        request.contentType("application/json");
        request.header("Authorization", token);
        request.header("x-yun-api-version", "v1");
        request.header("x-yun-client-info", "");
        request.header("x-yun-app-channel", "10160");
        request.body(JSONUtil.toJsonStr(params));
        final String result = request.execute().body();
        System.out.println(JsonUtil.toJson(result));
    }

    /**
     * 轮询对话结果
     */
    public static void chatPollingUpdate(String token, Long dialogueId, String channel) {
        if (StrUtil.isEmpty(token)) {
            token = "Basic bW9iaWxlOjE5ODAyMDIxMTU4OkdZV3JKdWdqfDF8MXwxNzE1OTk5NzIyOTg5fFlxck9KX0pocWc2VG91MWlkT2xBSlBXTDNHVWowS25EU2Y1VGEuTkJhYy5TTS5nZUZJRnpYUkgwYkZZRmR2UEdRakJNZ2hkMzIybWNJVjRnM2Iud3U1c1VxUVBqeDhTNWg4VmM4Unh4N1c3MjdMcFRkOVJ5Yzk1MlRsb2FUckpwUEhxbGVoZXJ3VmY3d1RKZzR1cW53bHlCUDd2dnFaWFhDeUtFVDkwYzZDay0=";
        }
        if (dialogueId == null) {
            dialogueId = null;
        }
        if (StrUtil.isEmpty(channel)) {
            channel = "10160";
        }

        while (true) {

            // 请求参数
            final AlgorithmChatPollingUpdateDTO params = new AlgorithmChatPollingUpdateDTO();
//        params.setUserId("1064201535554158611");
            params.setDialogueId(dialogueId);

            final HttpRequest request = HttpUtil.createPost(domain + "/assistant/chat/pollingUpdate");
            request.contentType("application/json");
            request.header("Authorization", token);
            request.header("x-yun-api-version", "v1");
            request.header("x-yun-client-info", "");
            request.header("x-yun-app-channel", channel);
            request.body(JSONUtil.toJsonStr(params));
            final String result = request.execute().body();
            System.out.println("轮询结果：" + result);
            BaseResult resp = JsonUtil.parseObject(result, BaseResult.class);
            if (!resp.isSuccess()) {
                System.out.println("接口异常");
                return;
            }
            ContentVO vo = JsonUtil.parseObject(JsonUtil.toJson(resp.getData()), ContentVO.class);
            if (vo != null && vo.getOutAuditStatus() != null) {
                if (vo.getOutAuditStatus() == 2) {
                    System.out.println(vo.getInContent());
                    System.out.println(vo.getOutContent());
                    System.out.println("正常返回结果");
                    return;
                }
            }

            try {
                Thread.sleep(1500);
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }
    }


    /**
     * 聊天接口，V1
     */
    public static void chatDialogueAddV1() {
        final String token = "Basic bW9iaWxlOjE5ODAyMDIxMTU4OkdZV3JKdWdqfDF8MXwxNzE1OTk5NzIyOTg5fFlxck9KX0pocWc2VG91MWlkT2xBSlBXTDNHVWowS25EU2Y1VGEuTkJhYy5TTS5nZUZJRnpYUkgwYkZZRmR2UEdRakJNZ2hkMzIybWNJVjRnM2Iud3U1c1VxUVBqeDhTNWg4VmM4Unh4N1c3MjdMcFRkOVJ5Yzk1MlRsb2FUckpwUEhxbGVoZXJ3VmY3d1RKZzR1cW53bHlCUDd2dnFaWFhDeUtFVDkwYzZDay0=";

        for (int i = 0; i < 1; i++) {
            new Thread(() -> {
                // 请求参数
                final AlgorithmChatAddDTO params = new AlgorithmChatAddDTO();
                params.setApplicationType(ApplicationTypeEnum.CHAT.getCode());

                final AlgorithmChatAddContentDTO content = new AlgorithmChatAddContentDTO();
                content.setResourceType(0);
                content.setDialogueType(0);
                content.setSourceChannel("102");
                content.setDialogue("你好");
                content.setTimestamp(new Date());
                params.setContent(content);

                final HttpRequest request = HttpUtil.createPost(domain + "/assistant/chat/add");
                request.contentType("application/json;charset=UTF-8");
                request.header("Authorization", token);
                request.header("x-yun-api-version", "v1");
                request.header("x-yun-client-info", "");
                request.header("x-yun-app-channel", "102");
                request.body(JSONUtil.toJsonStr(params));
                final String result = request.execute().body();
                System.out.println(JsonUtil.toJson(result));

            }).start();
        }
    }

    /**
     * 聊天接口，V1   对话
     */
    public static void chatDialogueAddV1Continue() {
        final String token = "Basic bW9iaWxlOjE5ODAyMDIxMTU4OkdZV3JKdWdqfDF8MXwxNzE1OTk5NzIyOTg5fFlxck9KX0pocWc2VG91MWlkT2xBSlBXTDNHVWowS25EU2Y1VGEuTkJhYy5TTS5nZUZJRnpYUkgwYkZZRmR2UEdRakJNZ2hkMzIybWNJVjRnM2Iud3U1c1VxUVBqeDhTNWg4VmM4Unh4N1c3MjdMcFRkOVJ5Yzk1MlRsb2FUckpwUEhxbGVoZXJ3VmY3d1RKZzR1cW53bHlCUDd2dnFaWFhDeUtFVDkwYzZDay0=";

        String sourceChannel = "102";
        String sessionId = "";
        List<AlgorithmChatAddContentDTO> dialogueList = new ArrayList<>();

        AlgorithmChatAddContentDTO dto1 = new AlgorithmChatAddContentDTO();
        dto1.setDialogue("谁说话的声音传得最远?");
        dialogueList.add(dto1);

        AlgorithmChatAddContentDTO dto2 = new AlgorithmChatAddContentDTO();
        dto2.setDialogue("谁说话的声音传得最远?");
        dto2.setPrompt("重新生成");
        dialogueList.add(dto2);

        AlgorithmChatAddContentDTO dto3 = new AlgorithmChatAddContentDTO();
        dto3.setDialogue("谁说话的声音传得最远?");
        dto3.setPrompt("重新生成");
        dialogueList.add(dto3);

        AlgorithmChatAddContentDTO dto4 = new AlgorithmChatAddContentDTO();
        dto4.setDialogue("谁说话的声音传得最远?");
        dto4.setPrompt("重新生成");
        dialogueList.add(dto4);

        for (int i = 0; i < dialogueList.size(); i++) {
            AlgorithmChatAddContentDTO dto = dialogueList.get(i);
            // 对话参数
            final AlgorithmChatAddContentDTO content = new AlgorithmChatAddContentDTO();
            content.setResourceType(0);
            content.setDialogueType(0);
            content.setSourceChannel(sourceChannel);
            content.setDialogue(dto.getDialogue());
            content.setTimestamp(new Date());
            content.setPrompt(dto.getPrompt());

            // 请求参数
            final AlgorithmChatAddDTO params = new AlgorithmChatAddDTO();
            params.setApplicationType(ApplicationTypeEnum.CHAT.getCode());
            params.setContent(content);
            params.setSessionId(sessionId);

            final HttpRequest request = HttpUtil.createPost(domain + "/assistant/chat/add");
            request.contentType("application/json;charset=UTF-8");
            request.header("Authorization", token);
            request.header("x-yun-api-version", "v1");
            request.header("x-yun-client-info", "");
            request.header("x-yun-app-channel", sourceChannel);
            request.body(JSONUtil.toJsonStr(params));
            final String result = request.execute().body();
            System.out.println("对话结果：" + result);
            BaseResult resp = JsonUtil.parseObject(result, BaseResult.class);
            if (resp != null && resp.isSuccess()) {
                AlgorithmChatAddVO vo = JsonUtil.parseObject(JsonUtil.toJson(resp.getData()), AlgorithmChatAddVO.class);
                if (vo != null) {
                    sessionId = vo.getSessionId();
                }

                // 获取结果
                chatPollingUpdate(token, Long.valueOf(vo.getDialogueId()), sourceChannel);
            }
        }
    }


    /**
     * 聊天接口，V2   对话
     */
    public static void chatDialogueAddV2Continue() {
        final String token = "Basic bW9iaWxlOjE5ODAyMDIxMTU4OkdZV3JKdWdqfDF8MXwxNzE1OTk5NzIyOTg5fFlxck9KX0pocWc2VG91MWlkT2xBSlBXTDNHVWowS25EU2Y1VGEuTkJhYy5TTS5nZUZJRnpYUkgwYkZZRmR2UEdRakJNZ2hkMzIybWNJVjRnM2Iud3U1c1VxUVBqeDhTNWg4VmM4Unh4N1c3MjdMcFRkOVJ5Yzk1MlRsb2FUckpwUEhxbGVoZXJ3VmY3d1RKZzR1cW53bHlCUDd2dnFaWFhDeUtFVDkwYzZDay0=";

        String sourceChannel = "102";
        String sessionId = "1147672833380335619";
        List<AlgorithmChatAddContentDTO> dialogueList = new ArrayList<>();

        String inContent = "此刻的时钟已经指向了晚上10：34，心情有些许的低落。也许是因为白天一连串“倒霉事”给弄的，也许是因为刚刚一个朋友提到感情的事给弄的，自己也说不清此刻到底什么心情，有点乱乱的，有点想家，想这世界上最不可能欺骗我的两个人——我的爸爸和妈妈。这个社会太花哨，从我即将毕业，走入社会的那天起，我就渐渐的了解，随着入世时间的加剧，越发觉得这个社会可怕，可怕到我想隐居，过着陶渊明一样的生活，但是这个社会又充满了太多的诱惑，弄的人矛盾着，存在着……刚刚的一片文章勾起了我无限的回忆，里面的一句“生活就是这样，最终相守到老的人，也许并不是那个曾经许下山盟海誓、承诺白头偕老、暗自发誓这辈子只爱她一个人的人”，是啊，相爱的两个人未必一定就会走到一起，会因为社会、家庭的种种牵绊，弄的最后以有缘无份草率收场，也看多了对爱情不忠贞的例子，让我更加恐惧爱情，我觉得爱情对于我来说，有点意味这是奢望，我害怕拥有，又不得不拥有，每天身边的人在不断的感叹，“你到底要什么样的啊，挑来挑去”试问自己，我真的有么，我的貌似苛刻的要求，不也就是最简单的要求么，谁不想要一份简单的幸福，但是我到现在也未能遇到，出现的人不喜欢，喜欢的人不出现，想想即使出现了，也会由于自己孤独惯了受不了两个人的约束，所以还是会剩下自己一个人。朋友前几天和我说明年要结婚了，发自内心的由衷的祝福她，但是同时她也明确且直观的告诉我，“你啊，难”，我说，“为什么呢？”她说，“因为他们是同学，他们的爱情是以感情为基础的，而我就不一样了，我将要寻找的另一半，因为没有感情基础，所以要以现实为前提，所以就难了”，想想好似真是那么回事，但说到现实，我未像其他女生一样要求他一定要有车、有房、有存款等等，但是，是不是这个首要的房子要有啊，没有房子怎么成“家”，提到家，我们都会想象到那个融洽、温馨的地方，但是如果没有这个家，怎么会有归属感、怎么会有幸福感，怎么会有家的味道……老爸老妈始终是最懂我的，他们说可以为了我放弃很多东西，我知道他们真能做的出来，但是我作为他们唯一的女儿，从小不懂事也就算了，此刻长大成人了，不能在那么不懂事，伸手就要这要那，现在是时候回报他们二老的时候了，所以我真的不希望在自己人生重大事情上，再让他们为我操太多的心，我知道他们此时已经很累，活到他们这个年纪本应儿孙满堂，但我却未能给予他们，心中未免有些酸楚";

        AlgorithmChatAddContentDTO dto1 = new AlgorithmChatAddContentDTO();
        dto1.setDialogue(inContent);
        dto1.setPrompt("续写");
        dialogueList.add(dto1);

        AlgorithmChatAddContentDTO dto2 = new AlgorithmChatAddContentDTO();
        dto2.setDialogue(inContent);
        dto2.setPrompt("重新生成，续写");
        dialogueList.add(dto2);

        AlgorithmChatAddContentDTO dto3 = new AlgorithmChatAddContentDTO();
        dto3.setDialogue(inContent);
        dto3.setPrompt("重新生成，续写");
        dialogueList.add(dto3);

        AlgorithmChatAddContentDTO dto4 = new AlgorithmChatAddContentDTO();
        dto4.setDialogue(inContent);
        dto4.setPrompt("重新生成，续写");
        dialogueList.add(dto4);

        AlgorithmChatAddContentDTO dto5 = new AlgorithmChatAddContentDTO();
        dto5.setDialogue(inContent);
        dto5.setPrompt("重新生成，续写");
        dialogueList.add(dto5);

        for (int i = 0; i < dialogueList.size(); i++) {
            AlgorithmChatAddContentDTO dto = dialogueList.get(i);
            // 对话参数
            final AlgorithmChatAddContentDTO content = new AlgorithmChatAddContentDTO();
            content.setResourceType(0);
            content.setDialogueType(0);
            content.setSourceChannel(sourceChannel);
            content.setDialogue(dto.getDialogue());
            content.setTimestamp(new Date());
            content.setPrompt(dto.getPrompt());
            content.setCommands("000");

            // 请求参数
            final AlgorithmChatAddDTO params = new AlgorithmChatAddDTO();
            params.setApplicationType(ApplicationTypeEnum.CHAT.getCode());
            params.setContent(content);
            params.setSessionId(sessionId);

            final HttpRequest request = HttpUtil.createPost(domain + "/assistant/chat/add");
            request.contentType("application/json;charset=UTF-8");
            request.header("Accept", "text/event-stream");
            request.header("Authorization", token);
            request.header("x-yun-api-version", "v2");
            request.header("x-yun-client-info", "");
            request.header("x-yun-app-channel", sourceChannel);
            request.body(JSONUtil.toJsonStr(params));
            final InputStream result = request.execute().bodyStream();
            InputStreamReader reader = new InputStreamReader(result);
            BufferedReader br = new BufferedReader(reader);
            try {
                System.out.println("问：--------------------------------------------------------");
                System.out.println(params.getContent().getDialogue());
                System.out.println("答：--------------------------------------------------------");
                String line;
                while ((line = br.readLine()) != null) {
//                    System.out.println(line);
                    if (line.indexOf("data:") != 0) {
                        continue;
                    }
                    BaseResult resp = JsonUtil.parseObject(line.substring(5), BaseResult.class);
                    if (resp == null || !resp.isSuccess()) {
                        System.out.println(resp.getMessage());
                        continue;
                    }
                    AlgorithmChatAddVO vo = JsonUtil.parseObject(JsonUtil.toJson(resp.getData()), AlgorithmChatAddVO.class);
                    if (vo == null) {
                        continue;
                    }
                    sessionId = vo.getSessionId();
                    FlowTypeResultVO flow = JsonUtil.parseObject(JsonUtil.toJson(vo.getFlowResult()), FlowTypeResultVO.class);
                    if (flow != null) {
                        System.out.println(flow.getOutContent());
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 聊天接口，V2
     */
    public static void chatDialogueAddV2() {
        final String token = "Basic bW9iaWxlOjEzNDMyODcyODk2Okg1aTZsTDlpfDF8MXwxNzE5NjIyNDIwOTU4fEZELnRBUTFfWXg5NFhETHRhQzkyNmVHMS54cWdJaEZkeVg2N0c4WUFYeVF5RzhJaWNUcktiZWRJQ2VTYXJmcUh5TW5Lc0RKSXZDYTlOSjNQMldfa04weFFIaGJDcXgxb2Z0eGZsQ2xlRmRfNkI3enc1R25PQWpxc2ZtWFRvbnVVVHU0QUxYc3FVck1JYjBwRW90aHVKd2d4TFV3RjRwWUk0NGpERE1xbks4VS0=";

        for (int i = 0; i < 1; i++) {
            new Thread(() -> {
                // 请求参数
                final AlgorithmChatAddDTO params = new AlgorithmChatAddDTO();
                params.setApplicationType(ApplicationTypeEnum.CHAT.getCode());

                final AlgorithmChatAddContentDTO content = new AlgorithmChatAddContentDTO();
                content.setResourceType(0);
                content.setDialogueType(0);
                content.setSourceChannel("102");
                content.setDialogue("你会做什么德国法国大使馆为如果而");
                content.setTimestamp(new Date());
                params.setContent(content);
//                params.setApplicationType(ApplicationTypeEnum.INTELLIGENT.getCode());
//                params.setApplicationId("2000000002");

                final HttpRequest request = HttpUtil.createPost(domain + "/assistant/chat/add");
                request.contentType("application/json;charset=UTF-8");
                request.header("Accept", "text/event-stream");
                request.header("Authorization", token);
                request.header("x-yun-api-version", "v2");
                request.header("x-yun-client-info", "");
                request.header("x-yun-app-channel", "10160");
                request.body(JSONUtil.toJsonStr(params));
                final String result = request.execute().body();
                System.out.println(JsonUtil.toJson(result));

            }).start();
        }
    }

    /**
     * 对话停止
     */
    public static void chatStop() {
        final String token = "Basic bW9iaWxlOjE5ODAyMDIxMTU4OkdZV3JKdWdqfDF8MXwxNzE1OTk5NzIyOTg5fFlxck9KX0pocWc2VG91MWlkT2xBSlBXTDNHVWowS25EU2Y1VGEuTkJhYy5TTS5nZUZJRnpYUkgwYkZZRmR2UEdRakJNZ2hkMzIybWNJVjRnM2Iud3U1c1VxUVBqeDhTNWg4VmM4Unh4N1c3MjdMcFRkOVJ5Yzk1MlRsb2FUckpwUEhxbGVoZXJ3VmY3d1RKZzR1cW53bHlCUDd2dnFaWFhDeUtFVDkwYzZDay0=";


        // 请求参数
        final AlgorithmChatStopDTO params = new AlgorithmChatStopDTO();
        params.setSourceChannel("10160");
        params.setSessionId("1145637665299087380");
        params.setDialogueId("1145637665299087378");

        final HttpRequest request = HttpUtil.createPost(domain + "/assistant/chat/stop");
        request.contentType("application/json;charset=UTF-8");
        request.header("Authorization", token);
        request.header("x-yun-api-version", "v1");
        request.header("x-yun-client-info", "");
        request.header("x-yun-app-channel", "10160");
        request.body(JSONUtil.toJsonStr(params));
        final String result = request.execute().body();
        System.out.println(JsonUtil.toJson(result));
    }

    /**
     *
     */
    @Test
    public void chatStopTest() {
        // 请求参数
        final AlgorithmChatStopDTO params = new AlgorithmChatStopDTO();
        params.setSourceChannel("10160");
        params.setSessionId("1145637665299087380");
        params.setDialogueId("1145637665299087378");
        params.setUserId("1105420961611622182");

        assistantController.chatStop(params);
    }
}