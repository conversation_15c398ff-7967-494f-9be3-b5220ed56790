package com.zyhl.yun.api.outer.infrastructure.albumsaas;

import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.req.AddMemoryStoryReq;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.req.QueryAlbumByAlbumIdReq;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.resp.AddMemoryStoryResp;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.resp.QueryAlbumByAlbumIdResp;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.service.AlbumSaasService;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.support.DependencyInjectionTestExecutionListener;

import javax.annotation.Resource;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.infrastructure.albumsaas.AlbumSaasTest} <br>
 * <b> description:</b>
 *
 * <AUTHOR>
 * @date 2025-05-20 17:46
 **/
@ActiveProfiles("local")
@TestPropertySource(properties = {"spring.profiles.active=local"})
@TestExecutionListeners({
        SpringBootDependencyInjectionTestExecutionListener.class,
        DependencyInjectionTestExecutionListener.class,
        MockitoTestExecutionListener.class,
})
@ExtendWith({
        SpringExtension.class,
        MockitoExtension.class
})
@SpringBootTest
public class AlbumSaasTest {

    @Resource
    private AlbumSaasService albumSaasService;

    @Test
    public void testQueryAlbumByAlbumId() {
        QueryAlbumByAlbumIdReq queryAlbumByAlbumIdReq = new QueryAlbumByAlbumIdReq();
        queryAlbumByAlbumIdReq.setAlbumId("1215754176794776661");
        queryAlbumByAlbumIdReq.setUserId("1105420961611622592");
        queryAlbumByAlbumIdReq.setCreateUserId("1105420961611622592");
        queryAlbumByAlbumIdReq.setOwnerType("personal");
        QueryAlbumByAlbumIdResp response = albumSaasService.queryAlbumByAlbumId(queryAlbumByAlbumIdReq);
        System.out.println("=========> " + JsonUtil.toJson(response));
    }

    @Test
    public void testAddMemoryStory() {
        AddMemoryStoryReq addMemoryStoryReq = new AddMemoryStoryReq();
        addMemoryStoryReq.setUserId("1105420961611622592");
        addMemoryStoryReq.setName("测试增加回忆相册");
        addMemoryStoryReq.setSecondTitle("测试增加回忆相册2");
        addMemoryStoryReq.setFileIds(new String[]{"Fl04BIBgiq_hxcgPlEjpIfYbH0DqwIHB4",
                "FsQAjSiZb3cbegPDs7cVCmKCJ8lCOWQZG", "FhvwouGp5321jBWYxuo5Ahpd1-aLCewTt"});
        addMemoryStoryReq.setCover("Fl04BIBgiq_hxcgPlEjpIfYbH0DqwIHB4");
        addMemoryStoryReq.setOwnerType("personal");
        addMemoryStoryReq.setToken("Basic cGM6MTM2MzE0MDY5MTA6cmszbDl2ZHZ8MXwxfDE3NTAyOTkyNzQwMjh8VjhDSTR4bE1jTmhINjVZQzU1S3loQk9oa0h4S0kzbU8zRVNHSEdYXzFzSE11XzNTa3hpd2J3NmZaSHFRWldTWHhUb3M5SzdfY3NMQ25tNG5FdGtlOEI4YmVWRVE0Q1pjeUZQb1A4WUhnLjhPM1V1T1pDS04wajRVRHJNOTJRWHBzTElUcjF2dFp3ZkVxZFFTT05tOTBLUlAzSWVqWHBEdlpLdkJPYVkxLm44LQ==");
        AddMemoryStoryResp response = albumSaasService.addMemoryStory(addMemoryStoryReq);
        System.out.println("=========> " + JsonUtil.toJson(response));
    }

}