package com.zyhl.yun.api.outer.web.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatStopDTO;
import com.zyhl.yun.api.outer.controller.AssistantController;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * AI报名测试
 */
@SpringBootTest
@Slf4j
class AIChatStopWebTest {
    //    private final static String domain = "http://127.0.0.1:19027/ai/api/outer";
    private final static String domain = "http://10.19.16.193:8883/ai-test/ai/api/outer";

    @Resource
    private AssistantController assistant<PERSON>ontroller;

    public static void main(String[] args) {
        stop();

    }

    private static final String token = "Basic bW9iaWxlOjEzNTU3NTg5NjUyOlVQSFVrVHhYfDF8MXwxNzE5NjMxNDc3NTU0fGNKN0R0S0VtcUszM3VLOTNheE1wWFpvVEpYZXAzVDlrQThacWpFX0kwNU9IbTVaVXBQcmZLbTFiT2lIVzcwQkRnODBfNXZDa0RaUVFVOFdLMmNsVldLMFBlLnVLWjBwRjFkeVdWaVJqbnk3YkRnU1ltNUJTM205T1VEdy5ySHdycE8zWld4WHl5dWhra3ZEaEY1d1FLU0RHQnJ3VnpoeWxtMXFFeW12S3VEOC0=";

    // 请求头参数
    private static HttpRequest headerParams(String authorization, String channel) {
        final HttpRequest request = HttpUtil.createPost(domain + "/assistant/chat/stop");
        request.contentType("application/json;charset=UTF-8");
        request.header("Accept", "application/json");
        request.header("Authorization", StrUtil.emptyToDefault(authorization, token));
        request.header("x-yun-api-version", "v1");
        request.header("x-yun-client-info", "");
        request.header("x-yun-app-channel", StrUtil.emptyToDefault(channel, "102"));

        return request;
    }

    // 请求体参数
    private static AlgorithmChatStopDTO bodyParams(String channel, String sessionId, String dialogueId) {
        final AlgorithmChatStopDTO params = new AlgorithmChatStopDTO();
        params.setSourceChannel(channel);
        params.setSessionId(sessionId);
        params.setDialogueId(dialogueId);

        return params;
    }

    // 停止对话
    public static void stop(String authorization, String channel, String sessionId, String dialogueId) {
        // 请求体参数
        AlgorithmChatStopDTO params = bodyParams(channel, sessionId, dialogueId);

        // 请求接口
        String result = headerParams(authorization, channel).body(JSONUtil.toJsonStr(params)).execute().body();
        System.out.println("停止对话结果：" + result);
    }

    // 停止对话
    public static void stop() {
        String channel = "102";
        String sessionId = "1154207860197204213";
        String dialogueId = "1154207860197204210";

        // 请求体参数
        AlgorithmChatStopDTO params = bodyParams(channel, sessionId, dialogueId);

        // 请求接口
        String result = headerParams(token, channel).body(JSONUtil.toJsonStr(params)).execute().body();
        System.out.println("停止对话结果：" + result);
    }


    @Test
    public void stopTest() {
        String channel = "101";
        String sessionId = "1153986188345507843";
        String dialogueId = "1153986188345507842";

        // 请求体参数
        AlgorithmChatStopDTO params = bodyParams(channel, sessionId, dialogueId);
        params.setUserId("1114312224667476010");

        // 请求接口
        BaseResult<?> result = assistantController.chatStop(params);
        System.out.println("停止对话结果：" + result);
    }

}