package com.zyhl.yun.api.outer.es;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.enums.EsInterventionStatusEnum;
import com.zyhl.hcy.yun.ai.common.base.es.domain.datahelper.repository.EsCommonRepository;
import com.zyhl.hcy.yun.ai.common.base.es.entity.EsInterventionEntity;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.vo.TextFeatureExtractVO;
import com.zyhl.yun.api.outer.external.CmicTextService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 渠道号测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class EsTest {

    @Autowired
    private RestHighLevelClient restHighLevelClient;


    @Resource
    private CmicTextService cmicTextService;

    @Resource
    private EsCommonRepository esCommonRepository;

    @Test
    public void esquery() throws IOException {
        List<String> questionList = new ArrayList<>();
        questionList.add("汉武帝是谁");
        List<EsInterventionEntity> list = new ArrayList<>();
        QueryBuilder queryBuilder = QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("question.keyword", questionList)).must(QueryBuilders.termQuery("status", EsInterventionStatusEnum.ENABLE.getCode()));

        SearchRequest request = new SearchRequest().indices("intervention_richtext").source(new SearchSourceBuilder().query(queryBuilder));
        SearchResponse searchResponse = restHighLevelClient.search(request, RequestOptions.DEFAULT);
        // 处理查询结果
        SearchHits searchHits = searchResponse.getHits();
        //精确匹配 正常只返回一条或没有
        for (SearchHit hit : searchHits) {
            // 获取文档source
            String json = hit.getSourceAsString();
            // 反序列化
            EsInterventionEntity entity = JsonUtil.parseObject(json, EsInterventionEntity.class);
            list.add(entity);
        }
        log.info("list:{}", list);
    }


    /***
     * 分数匹配查询
     * @throws IOException 异常
     */
    @Test
    public void scoreEsquery() throws IOException {
        String text = "汉武帝是谁";
        TextFeatureExtractVO textFeatureExtractVO = cmicTextService.getTextFeature(text);
        List<EsInterventionEntity> list = new ArrayList<>();
        // KNN 查询的 JSON 字符串
        String knnQueryJson = "{\"query\": {    \"bool\": {      \"must\": [        {          \"term\": {     " + "       \"status\": 0          }        },        {          \"exists\": {       " + "     \"field\": \"embeddings\"          }        }      ],      \"should\": [        {    " + "      \"rank_feature\": {            \"field\": \"rank\",            \"boost\": 2,        " + "    \"log\": {              \"scaling_factor\": 4            }          }        }      ]    }  }," + "  \"rescore\": {    \"window_size\": 200,    \"query\": {      \"rescore_query\": {      " + "  \"knn_nearest_neighbors\": {          \"field\": \"embeddings\",      " + "    \"model\": \"exact\",          \"similarity\": \"cosine\",          \"candidates\": 200,     " + "     \"vec\": {\"values\":" + textFeatureExtractVO.getFeature() + "}        }      }, " + "     \"query_weight\": 0,      \"rescore_query_weight\": 1    }  },  \"from\": 0,  \"size\": 10}";
        System.out.println(knnQueryJson);
        Request request = new Request("GET", "/intervention/_search");
        request.setJsonEntity(knnQueryJson);
        Response searchResponse = restHighLevelClient.getLowLevelClient().performRequest(request);
        // 处理查询结果
        HttpEntity httpEntity = searchResponse.getEntity();
        String responseString = EntityUtils.toString(httpEntity);
        String response = esCommonRepository.dslQuery("GET", "/intervention/_search", knnQueryJson);
        JSONObject jsonObject = JSONUtil.parseObj(response);
        if (jsonObject.get("hits") != null) {
            String hitsString = jsonObject.get("hits").toString();
            if (StrUtil.isNotEmpty(hitsString)) {
                JSONObject hitObject = JSONUtil.parseObj(hitsString);
                JSONArray standings = JSONUtil.parseArray(hitObject.get("hits"));
                for (Object object : standings) {
                    JSONObject sourceJson = JSONUtil.parseObj(object);
                    String sourceAsString = sourceJson.get("_source").toString();
                    EsInterventionEntity entity = JsonUtil.parseObject(sourceAsString, EsInterventionEntity.class);
                    list.add(entity);
                }
            }
        }
        log.info("list:{}", list);
    }
}
