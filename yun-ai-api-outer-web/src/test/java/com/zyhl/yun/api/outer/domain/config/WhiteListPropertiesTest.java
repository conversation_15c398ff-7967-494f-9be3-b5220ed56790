package com.zyhl.yun.api.outer.domain.config;

import cn.hutool.core.collection.CollUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.config.WhiteListProperties;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * 白名单测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class WhiteListPropertiesTest {

    @Resource
    private WhiteListProperties whiteListProperties;

    @Test
    public void test() {
        System.out.println(JsonUtil.toJson(whiteListProperties.getMailSearch()));
        System.out.println(whiteListProperties.getMailSearch().getSuffix());
        System.out.println(whiteListProperties.getMailSearch().canUse("1134484066862375045"));
        List<String> allNetworkSearchWhiteUser = whiteListProperties.getAllNetworkSearchWhiteUser();
        System.out.println(CollUtil.isNotEmpty(allNetworkSearchWhiteUser) && !allNetworkSearchWhiteUser.contains("13710662664"));
    }

}
