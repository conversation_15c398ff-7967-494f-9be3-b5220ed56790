package com.zyhl.yun.api.outer.web.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.application.dto.ChatCommentDTO;
import com.zyhl.yun.api.outer.controller.AssistantController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * AI报名测试
 */
@SpringBootTest
@Slf4j
class AIChatCommentWebTest {

    @Resource
    private AssistantController assistantController;


    private final static String domain = "http://127.0.0.1:19027/ai/api/outer";
//    private final static String domain = "https://test.yun.139.com/ai/ai-test/ai/api/outer";


    public static void main(String[] args) {
        addComment();
    }

    private static String token = "Basic bW9iaWxlOjE4MTQyODUxOTUzOmJEYmg4T1hTfDF8MXwxNzE5NjUzODU4NjAyfEVtWjB0Q0dTZUx2dWJDSGhvdHp5ZWU0SFcyTVYwQjFCUjcybktzWVpJaEoyVHNVLng0VXVia2FPNEQySUVSOWxUOEtJbkx4WWlJZWZRQ3RvOTI0V29YZG9sLjdBUUlsQ0k2MktUV0pIQlM3WTFHU1Y3azFkc2lBd2VYWXNBblFFS0hZaEd4WGNIQzhzZHhvcEI2R3JWU1hPLnMyOVp1aFFhZnEzUlU5TFdVay0=";

    // 请求头参数
    private static HttpRequest headerParams(String uri, String channel, String clientInfo) {
        final HttpRequest request = HttpUtil.createPost(domain + uri);
        request.contentType("application/json;charset=UTF-8");
        request.header("Authorization", token);
        request.header("x-yun-api-version", "v1");
        request.header("x-yun-client-info", StrUtil.emptyToDefault(clientInfo, ""));
        request.header("x-yun-app-channel", StrUtil.emptyToDefault(channel, "102"));

        return request;
    }

    /**
     * 评论对话
     */
    public static void addComment() {
        // 请求参数
        final ChatCommentDTO params = new ChatCommentDTO();
        params.setSessionId(1136762903494361111L);
        params.setDialogueId(1136762903494361113L);
        params.setLikeComment(0);

        String result = headerParams("/assistant/chat/comment/add", "", "").body(JSONUtil.toJsonStr(params)).execute().body();
        System.out.println(JsonUtil.toJson(result));
    }


}