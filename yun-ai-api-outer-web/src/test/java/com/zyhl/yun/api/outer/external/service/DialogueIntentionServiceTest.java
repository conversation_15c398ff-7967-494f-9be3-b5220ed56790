package com.zyhl.yun.api.outer.external.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.zyhl.hcy.yun.ai.common.model.api.client.ali.dto.GenericSearchDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.ali.vo.GenericSearchVO;
import com.zyhl.yun.api.outer.application.dto.AiMultiSearchDTO;
import com.zyhl.yun.api.outer.application.handle.chat.impl.AiInternetSearchHandlerImpl;
import com.zyhl.yun.api.outer.application.service.chat.DialogueIntentionService;
import com.zyhl.yun.api.outer.config.AllNetworkSearchProperties;
import com.zyhl.yun.api.outer.external.AiInternetSearchExternalService;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 *
 *         2024年8月15日
 */

@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class DialogueIntentionServiceTest {
	@Resource
	private DialogueIntentionService dialogueIntentionService;

	@Resource
	private AiInternetSearchExternalService aiInternetSearchExternalService;

	@Resource
	private AiInternetSearchHandlerImpl aiInternetSearchHandler;

	@Resource
	private AllNetworkSearchProperties searchProperties;

	@Test
	public void testSearchExtract() {

//		aiInternetSearchHandler.searchEntityExtract("帮我搜哪吒2魔童闹海的电影", searchProperties.getSearchEntityExtract());
	}

	@Test
	public void testDuplicateFilter() {

		/*GenericSearchVO vo1 = new GenericSearchVO();
		vo1.setLink("https://www.baidu.com/");
		GenericSearchVO vo2 = new GenericSearchVO();
		vo2.setLink("https://www.baidu.com/");
		GenericSearchVO vo3 = new GenericSearchVO();
		vo3.setLink("http://www.baidu.com/");
		GenericSearchVO vo4 = new GenericSearchVO();
		vo4.setLink("http://www.baidu.com/");
		GenericSearchVO vo5 = new GenericSearchVO();
		vo5.setLink("ftp://www.baidu.com/");
		List<GenericSearchVO> list = CollUtil.newArrayList(vo1, vo2, vo3, vo4, vo5);
		List<GenericSearchVO> result = aiInternetSearchHandler.dataFilter(list);
		assert !result.isEmpty();
		result.forEach(s -> {
			System.out.println("=======================================================================================");
			System.out.println("link = " + s.getLink());
		});*/
	}

	@Test
	public void testMultiSearch() {

		/*// 组装测试
		List<String> metadataList = CollUtil.newArrayList("哪吒", "哪吒2魔童闹海", "饺子");
		List<AiMultiSearchDTO> paramList = aiInternetSearchHandler.assemblerSearchData(metadataList);

		paramList.forEach(param -> {
			GenericSearchDTO genericSearchDTO = param.getGenericSearchDTO();
			if (genericSearchDTO != null) {
				System.out.println("query's length = " + genericSearchDTO.getQuery().length());
			}
		});
		*//*GenericSearchDTO dto1 = new GenericSearchDTO();
		dto1.setQuery("帮我搜百度云盘的凡人修仙传电视剧全集");
		AiMultiSearchDTO param1 = AiMultiSearchDTO.builder().genericSearchDTO(dto1).build();
		GenericSearchDTO dto2 = new GenericSearchDTO();
		dto2.setQuery("帮我搜夸克网盘的凡人修仙传电视剧全集");
		AiMultiSearchDTO param2 = AiMultiSearchDTO.builder().genericSearchDTO(dto1).build();
		GenericSearchDTO dto3 = new GenericSearchDTO();
		dto3.setQuery("帮我搜阿里云盘的凡人修仙传电视剧全集");
		AiMultiSearchDTO param3 = AiMultiSearchDTO.builder().genericSearchDTO(dto1).build();
		GenericSearchDTO dto4 = new GenericSearchDTO();
		dto4.setQuery("帮我搜移动小站的凡人修仙传电视剧全集");
		AiMultiSearchDTO param4 = AiMultiSearchDTO.builder().genericSearchDTO(dto1).build();
		GenericSearchDTO dto5 = new GenericSearchDTO();
		dto5.setQuery("帮我搜全网凡人修仙传电视剧全集");
		AiMultiSearchDTO param5 = AiMultiSearchDTO.builder().genericSearchDTO(dto1).build();
		List<AiMultiSearchDTO> paramList = new ArrayList<>();
		paramList.add(param1);
		paramList.add(param2);
		paramList.add(param3);
		paramList.add(param4);
		paramList.add(param5);*//*
		List<GenericSearchVO> genericSearchVOS = aiInternetSearchHandler.multiSearch(paramList);
		assert !genericSearchVOS.isEmpty();
		genericSearchVOS.forEach(genericSearchVO -> System.out.println("snippet = " + genericSearchVO.getSnippet()));
		genericSearchVOS.forEach(genericSearchVO -> System.out.println("title = " + genericSearchVO.getTitle()));
		*//*List<GenericSearchVO> dataList = aiInternetSearchHandler.dataFilter(genericSearchVOS);
		String jsonStr = JSONUtil.toJsonStr(dataList);
		System.out.println("============================================================================================");
		System.out.println("jsonStr = " + jsonStr);*/
	}

	@Test
	public void testGenericSearch() {
	/*	GenericSearchDTO genericSearchDTO = new GenericSearchDTO();
		genericSearchDTO.setQuery("帮我搜哈利波特1");
		List<GenericSearchVO> genericSearchVOS = aiInternetSearchExternalService.genericSearch(genericSearchDTO);
		assert !genericSearchVOS.isEmpty();
		genericSearchVOS.forEach(genericSearchVO -> System.out.println("snippet = " + genericSearchVO.getSnippet()));*/
	}

	@Test
	public void handleMultipleSearchIntention_NonSearchMain_ShouldReturnOriginal() {
		DialogueIntentionVO dialogueIntentionVO = new DialogueIntentionVO();
		List<IntentionInfo> intentionInfoList = new ArrayList<>();
		IntentionInfo mainIntention = new IntentionInfo();
		mainIntention.setIntention("012"); // Search Image
		intentionInfoList.add(mainIntention);
		IntentionInfo otherIntention = new IntentionInfo();
		otherIntention.setIntention("018"); // 综合搜索
		intentionInfoList.add(otherIntention);
		IntentionInfo otherIntentionText = new IntentionInfo();
		otherIntentionText.setIntention("000"); // Text
		intentionInfoList.add(otherIntentionText);
		dialogueIntentionVO.setIntentionInfoList(intentionInfoList);

		DialogueIntentionVO out = dialogueIntentionService.handleMultipleSearchIntention(dialogueIntentionVO,
				mainIntention.getIntention());
		log.info("out:{}", JSONObject.toJSONString(out));
	}
}
