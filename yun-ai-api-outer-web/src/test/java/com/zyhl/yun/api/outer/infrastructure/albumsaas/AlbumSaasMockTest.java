package com.zyhl.yun.api.outer.infrastructure.albumsaas;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.client.AlbumOutSaasFeign;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.client.AlbumSaasFeign;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.req.AddMemoryStoryReq;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.req.QueryAlbumByAlbumIdReq;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.resp.AddMemoryStoryResp;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.resp.QueryAlbumByAlbumIdResp;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.service.AlbumSaasService;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.support.DependencyInjectionTestExecutionListener;

import java.util.HashMap;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <b>className:</b>
 * {@link AlbumSaasMockTest} <br>
 * <b> description:</b>
 *
 * <AUTHOR>
 * @date 2025-05-20 17:46
 **/
@ActiveProfiles("local")
@TestPropertySource(properties = {"spring.profiles.active=local"})
@TestExecutionListeners({
        SpringBootDependencyInjectionTestExecutionListener.class,
        DependencyInjectionTestExecutionListener.class,
        MockitoTestExecutionListener.class,
})
@ExtendWith({
        SpringExtension.class,
        MockitoExtension.class
})
@SpringBootTest
public class AlbumSaasMockTest {

    @Resource
    private AlbumSaasService albumSaasService;

    @MockBean
    private AlbumSaasFeign albumSaasFeign;

    @MockBean
    private AlbumOutSaasFeign albumOutSaasFeign;

    @BeforeEach
    public void setUp() {
        assertNotNull(albumSaasFeign);
        assertNotNull(albumOutSaasFeign);
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testMockQueryAlbumByAlbumId() {
        // 准备请求参数
        QueryAlbumByAlbumIdReq queryAlbumByAlbumIdReq = new QueryAlbumByAlbumIdReq();
        queryAlbumByAlbumIdReq.setAlbumId("1215754176794776661");
        queryAlbumByAlbumIdReq.setUserId("1105420961611622592");
        queryAlbumByAlbumIdReq.setCreateUserId("1105420961611622592");
        queryAlbumByAlbumIdReq.setOwnerType("personal");

        // 构造 Feign 接口的返回值
        QueryAlbumByAlbumIdResp queryAlbumByAlbumIdResp = new QueryAlbumByAlbumIdResp();
        queryAlbumByAlbumIdResp.setId("1215754176794776661");
        BaseResult<QueryAlbumByAlbumIdResp> feignResponse = BaseResult.success(queryAlbumByAlbumIdResp);

        // 模拟 albumOutSaasFeign.addMemoryStory(...) 的行为
        when(albumSaasFeign.queryAlbumByAlbumId(any(QueryAlbumByAlbumIdReq.class), any(HashMap.class)))
                .thenReturn(feignResponse);
        QueryAlbumByAlbumIdResp response = albumSaasService.queryAlbumByAlbumId(queryAlbumByAlbumIdReq);

        // 断言非空验证
        assertNotNull(response);
        Assertions.assertEquals("1215754176794776661", response.getId());
        // 验证 Feign 方法是否被调用一次
        verify(albumSaasFeign, times(1)).queryAlbumByAlbumId(any(QueryAlbumByAlbumIdReq.class), any(HashMap.class));
    }

    @Test
    public void testMockAddMemoryStory() {
        // 准备请求参数
        AddMemoryStoryReq addMemoryStoryReq = new AddMemoryStoryReq();
        addMemoryStoryReq.setUserId("1105420961611622592");
        addMemoryStoryReq.setName("测试增加回忆相册");
        addMemoryStoryReq.setSecondTitle("测试增加回忆相册2");
        addMemoryStoryReq.setFileIds(new String[]{"Fl04BIBgiq_hxcgPlEjpIfYbH0DqwIHB4",
                "FsQAjSiZb3cbegPDs7cVCmKCJ8lCOWQZG", "FhvwouGp5321jBWYxuo5Ahpd1-aLCewTt"});
        addMemoryStoryReq.setCover("Fl04BIBgiq_hxcgPlEjpIfYbH0DqwIHB4");
        addMemoryStoryReq.setOwnerType("personal");
        addMemoryStoryReq.setToken("Basic cGM6MTM2MzE0MDY5MTA6cmszbDl2ZHZ8MXwxfDE3NTAyOTkyNzQwMjh8VjhDSTR4bE1jTmhINjVZQzU1S3loQk9oa0h4S0kzbU8zRVNHSEdYXzFzSE11XzNTa3hpd2J3NmZaSHFRWldTWHhUb3M5SzdfY3NMQ25tNG5FdGtlOEI4YmVWRVE0Q1pjeUZQb1A4WUhnLjhPM1V1T1pDS04wajRVRHJNOTJRWHBzTElUcjF2dFp3ZkVxZFFTT05tOTBLUlAzSWVqWHBEdlpLdkJPYVkxLm44LQ==");

        // 构造 Feign 接口的返回值
        AddMemoryStoryResp addMemoryStoryResp = new AddMemoryStoryResp();
        addMemoryStoryResp.setId("1215754176794776661");
        BaseResult<AddMemoryStoryResp> feignResponse = BaseResult.success(addMemoryStoryResp);

        // 模拟 albumOutSaasFeign.addMemoryStory(...) 的行为
        when(albumOutSaasFeign.addMemoryStory(any(AddMemoryStoryReq.class), any(HashMap.class)))
                .thenReturn(feignResponse);
        AddMemoryStoryResp response = albumSaasService.addMemoryStory(addMemoryStoryReq);

        // 断言非空验证
        assertNotNull(response);
        Assertions.assertEquals("1215754176794776661", response.getId());
        // 验证 Feign 方法是否被调用一次
        verify(albumOutSaasFeign, times(1)).addMemoryStory(any(AddMemoryStoryReq.class), any(HashMap.class));
    }
}