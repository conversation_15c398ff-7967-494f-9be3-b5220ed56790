package com.zyhl.yun.api.outer.redis;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Redisson测试
 *
 * <AUTHOR>
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class RedissonTest {

    @Resource
    private RedissonClient redissonClient;

    @Test
    public void testRedissonLock() throws InterruptedException {
        String cacheKey = "yun:ai:center:task:check:update:lock:" + 666;
        RLock lock = redissonClient.getLock(cacheKey);

        int numberOfThreads = 5;
        int tryLockTime = 10;
        ExecutorService executorService = Executors.newFixedThreadPool(numberOfThreads);
        CountDownLatch latch = new CountDownLatch(numberOfThreads);

        for (int i = 0; i < numberOfThreads; i++) {
            final int threadNumber = i;
            executorService.submit(() -> {
                try {
                    boolean lockAcquired = false;
                    int lockTimes = 0;
                    while (!lockAcquired) {
                        /**
                         * 尝试获取锁，等待3秒，可以被中断
                         * 第一个参数：表示线程在尝试获取锁时最多等待的时间
                         * 第二个参数：TimeUnit.SECONDS 指定了前两个参数的时间单位（秒）。
                         */
                        lockAcquired = lock.tryLock(tryLockTime, TimeUnit.SECONDS);
//                        lockAcquired = lock.tryLock(tryLockTime, tryLockTime, TimeUnit.SECONDS);
                        if (!lockAcquired) {
                            System.out.println("Thread " + threadNumber + " 尝试获取锁失败"+ ++lockTimes +"次");
                        }
                    }

                    System.out.println("Thread " + threadNumber + " 拿到锁");
                    int times = 0;
                    while (times < tryLockTime+1){
                        Thread.sleep(1000);
                        System.out.println("Thread " + threadNumber + " Thread.sleep times：" + ++times);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    System.out.println("Thread " + threadNumber + " 释放锁");
                    lock.unlock();
                    latch.countDown();
                }
            });
        }

        latch.await();
        executorService.shutdown();
    }

}
