package com.zyhl.yun.api.outer.web.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.application.dto.ChatConfigDTO;
import com.zyhl.yun.api.outer.application.dto.ChatConfigGetDTO;
import com.zyhl.yun.api.outer.controller.ChatConfigController;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * AI报名测试
 */
@SpringBootTest
@Slf4j
class AIChatConfigWebTest {
    private final static String domain = "http://127.0.0.1:19027/ai/api/outer";
//    private final static String domain = "http://10.19.16.193:8883/ai-test/ai/api/outer";

    @Resource
    private ChatConfigController chatConfigController;

    @Resource
    private ChatConfigServiceDomainService chatConfigServiceDomainService;

    public static void main(String[] args) {


    }

    private static String token = "Basic bW9iaWxlOjEzNTU3NTg5NjUyOlVQSFVrVHhYfDF8MXwxNzE5NjMxNDc3NTU0fGNKN0R0S0VtcUszM3VLOTNheE1wWFpvVEpYZXAzVDlrQThacWpFX0kwNU9IbTVaVXBQcmZLbTFiT2lIVzcwQkRnODBfNXZDa0RaUVFVOFdLMmNsVldLMFBlLnVLWjBwRjFkeVdWaVJqbnk3YkRnU1ltNUJTM205T1VEdy5ySHdycE8zWld4WHl5dWhra3ZEaEY1d1FLU0RHQnJ3VnpoeWxtMXFFeW12S3VEOC0=";

    // 请求头参数
    private static HttpRequest headerParams(String uri, String authorization, String channel) {
        final HttpRequest request = HttpUtil.createPost(domain + uri);
        request.contentType("application/json;charset=UTF-8");
        request.header("Accept", "application/json");
        request.header("Authorization", StrUtil.emptyToDefault(authorization, token));
        request.header("x-yun-api-version", "v1");
        request.header("x-yun-client-info", "");
        request.header("x-yun-app-channel", StrUtil.emptyToDefault(channel, "102"));

        return request;
    }


    @Test
    public void getTest() {

        // 请求体参数
        ChatConfigGetDTO dto = new ChatConfigGetDTO();
        dto.setUserId("1039866970260798498");
        dto.setSourceChannel("101");

        // 请求接口
        BaseResult<?> result = chatConfigController.getUserConfig(dto);
        System.out.println("获取配置结果：" + JsonUtil.toJson(result.getData()));

        // 请求体参数
        dto = new ChatConfigGetDTO();
        dto.setUserId("1039866970260798498");
        dto.setSourceChannel("10102");

        // 请求接口
        result = chatConfigController.getUserConfig(dto);
        System.out.println("获取配置结果：" + JsonUtil.toJson(result.getData()));
    }

    @Test
    public void getUserSet() {
        String userId = "1134484066862375045";
        String phone = "***********";
        AssistantEnum assistantEnum = AssistantEnum.XIAO_TIAN;
        String businessType = "101";
        ChatConfigEntity entity = chatConfigServiceDomainService.getUserCanUseModel(userId, phone, assistantEnum, businessType);
        System.out.println("获取配置结果：" + JsonUtil.toJson(entity));
    }

    @Test
    public void setTest() {

        // 请求体参数
        ChatConfigDTO dto = new ChatConfigDTO();
        dto.setUserId("1039866970260798498");
        dto.setModelType("blian");
        dto.setSourceChannel("10102");

        // 请求接口
        BaseResult<?> result = chatConfigController.setUserConfig(dto);
        System.out.println("设置配置结果：" + JsonUtil.toJson(result));
    }
}