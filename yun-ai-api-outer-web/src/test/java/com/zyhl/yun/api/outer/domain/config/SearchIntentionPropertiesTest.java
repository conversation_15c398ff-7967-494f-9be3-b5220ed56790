package com.zyhl.yun.api.outer.domain.config;

import com.zyhl.yun.api.outer.config.SearchIntentionProperties;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 搜索结果配置-测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class SearchIntentionPropertiesTest {

    @Resource
    private SearchIntentionProperties searchIntentionProperties;

    @Test
    public void test() {
        System.out.println(searchIntentionProperties.isConvertComprehensiveSearch());
    }

}
