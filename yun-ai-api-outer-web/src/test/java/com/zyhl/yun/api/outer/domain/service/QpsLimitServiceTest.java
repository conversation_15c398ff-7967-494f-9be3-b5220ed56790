package com.zyhl.yun.api.outer.domain.service;

import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum;
import com.zyhl.yun.api.outer.domainservice.QpsLimitService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.concurrent.CountDownLatch;

/**
 * 渠道号测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class QpsLimitServiceTest {

    @Resource
    private QpsLimitService qpsLimitService;

    @Test
    public void modelQpsLimit() {
        int size = 100;
        CountDownLatch costDownLatch = new CountDownLatch(size);
        String code = TextModelEnum.BLIAN.getCode();
        for (int i = 0; i < size; i++) {
            new Thread(() -> {
                boolean flag = qpsLimitService.modelQpsLimit(code);
                if (!flag) {
                    System.out.println("qps限流，模型编码：" + code);
                }
                costDownLatch.countDown();
            }).start();
        }
        try {
            costDownLatch.await();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        System.out.println("执行成功");
    }


}
