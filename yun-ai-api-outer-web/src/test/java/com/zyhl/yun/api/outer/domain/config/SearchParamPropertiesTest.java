package com.zyhl.yun.api.outer.domain.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.zyhl.yun.api.outer.config.SearchParamProperties;
import com.zyhl.yun.api.outer.enums.chat.search.SearchDiscoveryParamQueryTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.search.SearchTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import javax.swing.plaf.ListUI;
import java.util.ArrayList;
import java.util.List;

/**
 * 搜索结果配置-测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class SearchParamPropertiesTest {

    @Resource
    private SearchParamProperties searchParamProperties;

    @Test
    public void test() {
        System.out.println(searchParamProperties.filterCommonKeywords(ListUtil.toList("视频", "12")));

        List<SearchParamProperties.ParamFilter> queryTypesFilterList = searchParamProperties.getParamFilterList(SearchTypeEnum.DISCOVERY.getSearchType(), "queryTypes");

        List<String> keywords = new ArrayList<>();
        keywords.add("1");
        keywords.add("电影");
        keywords.add("短剧");
        keywords.add("4k");
        keywords.add("4k大片");

        List<Integer> queryTypeList = new ArrayList<>();
        queryTypeList.add(1);

        log.info("searchDiscoveryParamV2FilterKeywords，关键字列表过滤前：{}", JSON.toJSONString(keywords));
        if(CollUtil.isNotEmpty(queryTypesFilterList)){
            for(Integer queryType : queryTypeList) {
                for(SearchParamProperties.ParamFilter queryTypesFilter : queryTypesFilterList) {
                    // 查全部或类型匹配，则过滤关键字
                    if(SearchDiscoveryParamQueryTypeEnum.ALL.getCode().equals(queryType) || queryTypesFilter.getType().equals(queryType.toString())){
                        keywords = queryTypesFilter.filterKeywords(keywords);
                    }
                }
            }
        }
        log.info("searchDiscoveryParamV2FilterKeywords，关键字列表过滤后：{}", JSON.toJSONString(keywords));
    }

}
