package com.zyhl.yun.api.outer.web.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddContentDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.DialogueCommandTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;

/**
 * AI报名测试
 */
@SpringBootTest
@Slf4j
class AIChatAddV1WebTest {
    /**
     * 文本
     * 文本+图片
     * 文本+笔记
     * 文本+邮件
     * 指令+文本
     * 指令+图片
     * 指令+笔记
     * 指令+邮件
     * 指令+图片+文本
     * 指令+笔记+文本
     * 指令+邮件+文本
     */


//    private final static String domain = "http://127.0.0.1:19027/ai/api/outer";
    private final static String domain = "http://10.19.16.193:8883/ai-test/ai/api/outer";


    public static void main(String[] args) {
        commandType1();

    }

    private static String token = "Basic bW9iaWxlOjEzNTU3NTg5NjUyOlVQSFVrVHhYfDF8MXwxNzE5NjMxNDc3NTU0fGNKN0R0S0VtcUszM3VLOTNheE1wWFpvVEpYZXAzVDlrQThacWpFX0kwNU9IbTVaVXBQcmZLbTFiT2lIVzcwQkRnODBfNXZDa0RaUVFVOFdLMmNsVldLMFBlLnVLWjBwRjFkeVdWaVJqbnk3YkRnU1ltNUJTM205T1VEdy5ySHdycE8zWld4WHl5dWhra3ZEaEY1d1FLU0RHQnJ3VnpoeWxtMXFFeW12S3VEOC0=";

    // 请求头参数
    private static HttpRequest headerParams(String channel, String clientInfo) {
        final HttpRequest request = HttpUtil.createPost(domain + "/assistant/chat/add");
        request.contentType("application/json;charset=UTF-8");
        request.header("Authorization", token);
        request.header("x-yun-api-version", "v1");
        request.header("x-yun-client-info", StrUtil.emptyToDefault(clientInfo, ""));
        request.header("x-yun-app-channel", StrUtil.emptyToDefault(channel, "102"));

        return request;
    }

    // 请求体参数
    private static AlgorithmChatAddDTO bodyParams(String channel, String dialogue, String prompt) {
        final AlgorithmChatAddContentDTO content = new AlgorithmChatAddContentDTO();
        content.setDialogueType(0);
        content.setTimestamp(new Date());
        content.setSourceChannel(channel);

        content.setResourceType(0);
        content.setResourceId("");

        content.setCommandType(DialogueCommandTypeEnum.ORDINARY.getType());
        content.setCommands("");
        content.setPrompt(prompt);
        content.setDialogue(dialogue);
        content.setSceneTag("");
        content.setExtInfo("");
//        content.setSortInfo();

        // 请求参数
        final AlgorithmChatAddDTO params = new AlgorithmChatAddDTO();
        params.setApplicationType(ApplicationTypeEnum.CHAT.getCode());
        params.setContent(content);
        params.setSessionId("");
//        params.setApplicationId("");
        params.setApplicationType(ApplicationTypeEnum.CHAT.getCode());

        return params;
    }

    // 文本模型：普通指令，需要扣除权益
    public static void commandType1() {
        // 请求体参数
        String channel = "102";
        String dialogue = "你好";
        String prompt = "";
        AlgorithmChatAddDTO params = bodyParams(channel, dialogue, prompt);

        // 请求接口
        String result = headerParams(channel, "").body(JSONUtil.toJsonStr(params)).execute().body();

        // 结果处理
        System.out.println("对话结果：" + result);
        BaseResult resp = JsonUtil.parseObject(result, BaseResult.class);
        if (resp != null && resp.isSuccess()) {
            AlgorithmChatAddVO vo = JsonUtil.parseObject(JsonUtil.toJson(resp.getData()), AlgorithmChatAddVO.class);
            // 获取结果
            AIChatPollingUpdateWebTest.chatPollingUpdate(token, Long.valueOf(vo.getDialogueId()), channel, "");
        }
    }


}