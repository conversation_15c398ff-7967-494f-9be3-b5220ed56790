package com.zyhl.yun.api.outer.rag.service;

import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileAddReqDTO;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.application.service.knowledge.UserKnowledgeFileTaskService;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Collections;

@Slf4j
@ExtendWith(SpringExtension.class)
@SpringBootTest
class UploadFileTest {

	@MockBean
	private UserKnowledgeFileTaskService userKnowledgeFileTaskService;

	@Test
	void uploadTest() {

		File file = new File();
		file.setName("测试");
		file.setFileId("0081000445323f200000558d");
		KnowledgeFileAddReqDTO dto = new KnowledgeFileAddReqDTO();
		dto.setUserId("1049319044374651030");
		dto.setFileList(Collections.singletonList(file));
		dto.setResourceType(KnowledgeResourceTypeEnum.MAIL.getCode());
		userKnowledgeFileTaskService.add(dto);
	}
}
