package com.zyhl.yun.api.outer.web.controller;

import cn.hutool.core.collection.ListUtil;
import com.zyhl.hcy.yun.ai.common.base.hbase.domain.datahelper.repository.HbaseRepository;
import com.zyhl.yun.api.outer.Application;
import com.zyhl.yun.api.outer.application.service.impl.TaskApplicationTypeServiceImpl;
import com.zyhl.yun.api.outer.config.textmodel.TaskChatPromptConfig;
import com.zyhl.yun.api.outer.domain.entity.TaskAiAbilityEntity;
import com.zyhl.yun.api.outer.persistence.po.AISpeedReadResultPO;
import com.zyhl.yun.api.outer.repository.TaskAiAbilityRepository;
import java.util.List;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description}
 * <p>
 *
 * <AUTHOR>
 * @since 4月18 2025
 */
@SpringBootTest(classes = Application.class)
public class SpeedTest {
  @Resource
  private HbaseRepository hbaseRepository;
  @Resource
  private TaskApplicationTypeServiceImpl speedReadService;
  @Resource
  private TaskChatPromptConfig taskChatPromptConfig;


  @Test
  public void test() {
    //rowKey_1105420961611622272_1189083243748892673
    String userId = "1157367235997450256";
    String taskId = "1213154338571306656";
    String speedReadRowKey = speedReadService.getSpeedReadRowKey(userId,taskId);
    List<AISpeedReadResultPO> aiSpeedReadResultList = hbaseRepository.selectList(
        ListUtil.toList( speedReadRowKey),AISpeedReadResultPO.class);
    System.out.println(aiSpeedReadResultList);
  }
  @Test
  public void getConfigTest(){
    System.out.println(taskChatPromptConfig);
  }
  @Resource
  private TaskAiAbilityRepository taskAiAbilityRepository;
  @Test
  public void getTest(){
    TaskAiAbilityEntity task = taskAiAbilityRepository.
        getTaskEntityForSpeedRead(1213177020293603536L);
    System.out.println(task);
  }
}
