package com.zyhl.yun.api.outer.external.service;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.JaxbUtil;
import com.zyhl.hcy.yun.ai.common.base.utils.StringUtil;
import com.zyhl.hcy.yun.ai.common.platform.third.client.notethird.vo.NoteThirdDetailVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.DownloadRequestReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.enums.YunAiYunDiskResultCode;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.properties.McloudNetAuthProperties;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.properties.YunDiskPerSaasProperties;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.GetContentInfo;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.ose.OseContentResult;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.ose.OseDownloadRequestResponse;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.utils.SSLUtils;
import com.zyhl.yun.api.outer.external.NoteExternalService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 笔记接口测试
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class NoteExternalTest {

    @Resource
    private NoteExternalService noteExternalService;

    @Test
    public void getFileInfo() {
        NoteThirdDetailVO vo = noteExternalService.getNoteDetail("1100341080451498138", "c9d02459b32949a488a40288858b4723");
        System.out.println(JsonUtil.toJson(vo));
    }

}
