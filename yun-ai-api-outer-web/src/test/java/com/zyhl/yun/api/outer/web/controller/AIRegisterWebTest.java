package com.zyhl.yun.api.outer.web.controller;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.enums.AIModuleEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.YunDiskClient;
import com.zyhl.yun.api.outer.application.dto.AiRegisterReqDTO;
import com.zyhl.yun.api.outer.application.service.AlgorithmAiRegisterService;
import com.zyhl.yun.api.outer.controller.AlgorithmAiRegisterController;
import com.zyhl.yun.api.outer.domain.vo.AiRegisterVO;
import com.zyhl.yun.api.outer.enums.BusinessSourceEnum;

import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * AI报名测试
 */
@SpringBootTest
class AIRegisterWebTest {
    @Resource
    private AlgorithmAiRegisterController algorithmAiRegisterController;
    @Resource
    private AlgorithmAiRegisterService algorithmAiRegisterService;
    @Resource
    private YunDiskClient yunDiskClient;  // 云盘

//    @Test
//    public void uploadFileByPath(){
//        final FileReader reader = new FileReader("D:\\data\\yunai\\test_jimu\\imgb\\1713609165921_1713235329501_1111.jpg");
//        final byte[] array = reader.readBytes();
//        final String base64 = Base64.getEncoder().encodeToString(array);
//        String userId = "1089668370187461461";
//        // 上传图片参数
//        FilePathUploadContentReqDTO uploadReq = new FilePathUploadContentReqDTO();
//        uploadReq.setUserId(userId);
//        uploadReq.setBelongsPlatform(0);
//        uploadReq.setPath("");
//        uploadReq.setModule(AIModuleEnum.AI_ASSISTANT.getModule());
//        uploadReq.setVirtualFile(FileReqDTO.builder().base64(base64).build());
//        final FileUploadVO uploadResp = yunDiskClient.uploadFilePath(uploadReq);
//        if (uploadResp == null || StrUtil.isEmpty(uploadResp.getFileId())) {
//            System.out.println("上传失败，uploadResp="+uploadResp.toString()+"，用户id="+userId);
//        }
//    }


    /**
     * 查询是否已经报名，AI工具
     */
    @Test
    public void get_gongju() {
        final AiRegisterReqDTO dto = new AiRegisterReqDTO();
        dto.setSourceBusiness(BusinessSourceEnum.TOOLS.getCode());
        dto.setModule(AIModuleEnum.AI_FILE_LIBRARY.getModule());
        dto.setUserId("1105420961611624121");
        BaseResult<AiRegisterVO> resp = algorithmAiRegisterController.get(dto);
        System.out.println(JsonUtil.toJson(resp));
        Assert.assertFalse(resp.isSuccess());
    }

    /**
     * 查询是否已经报名，AI助手
     */
    @Test
    public void get_zhushou() {
        final AiRegisterReqDTO dto = new AiRegisterReqDTO();
        dto.setSourceBusiness(BusinessSourceEnum.ASSISTANT.getCode());
        dto.setUserId("1061811620329283097");
        BaseResult<AiRegisterVO> resp = algorithmAiRegisterController.get(dto);
        System.out.println(JsonUtil.toJson(resp));
        Assert.assertTrue(resp.isSuccess());
    }

    /**
     * 查询是否已经报名，智能相册
     */
    @Test
    public void get_ablum() {
        final AiRegisterReqDTO dto = new AiRegisterReqDTO();
        dto.setSourceBusiness(BusinessSourceEnum.ALBUM.getCode());
        dto.setUserId("1105420961611624143");
        BaseResult<AiRegisterVO> resp = algorithmAiRegisterController.get(dto);
        System.out.println(JsonUtil.toJson(resp));
        Assert.assertFalse(resp.isSuccess());
    }

    /**
     * 报名AI工具
     */
    @Test
    public void accredit_gongju() {
        final AiRegisterReqDTO dto = new AiRegisterReqDTO();
        dto.setSourceBusiness(BusinessSourceEnum.TOOLS.getCode());
        dto.setModule(AIModuleEnum.AI_FILE_LIBRARY.getModule());
        dto.setUserId("1105420961611624143");
        BaseResult<AiRegisterVO> resp = algorithmAiRegisterController.accredit(dto);
        System.out.println(JsonUtil.toJson(resp));
        Assert.assertFalse(resp.isSuccess());
    }

    /**
     * 报名AI助手
     */
    @Test
    public void accredit_zhushou() {
        final AiRegisterReqDTO dto = new AiRegisterReqDTO();
        dto.setSourceBusiness(BusinessSourceEnum.ASSISTANT.getCode());
        dto.setUserId("1061811620329283097");
        final BaseResult<AiRegisterVO> resp = algorithmAiRegisterController.accredit(dto);
        System.out.println(JsonUtil.toJson(resp));
        Assert.assertTrue(resp.isSuccess());
    }

    /**
     * 报名智能相册
     */
    @Test
    public void accredit_ablum() {
        final AiRegisterReqDTO dto = new AiRegisterReqDTO();
        dto.setSourceBusiness(BusinessSourceEnum.ALBUM.getCode());
        dto.setUserId("1105420961611624143");
        BaseResult<AiRegisterVO> resp = algorithmAiRegisterController.accredit(dto);
        System.out.println(JsonUtil.toJson(resp));
        Assert.assertFalse(resp.isSuccess());
    }

    /**
     * 校验用户是否报名AI助手
     */
    @Test
    public void checkAIAssistant() {
        final String userId = "1105420961611624143";
        boolean result = algorithmAiRegisterService.checkAiAssistant(userId);
        System.out.println("用户已经报名：" + result);
        if (!result) {
            final AiRegisterReqDTO dto = new AiRegisterReqDTO();
            dto.setSourceBusiness(BusinessSourceEnum.ASSISTANT.getCode());
            dto.setModule(AIModuleEnum.AI_FILE_LIBRARY.getModule());
            dto.setUserId(userId);
            final BaseResult<AiRegisterVO> resp = algorithmAiRegisterController.accredit(dto);
            System.out.println(JsonUtil.toJson(resp));
            Assert.assertTrue(resp.isSuccess());

            result = algorithmAiRegisterService.checkAiAssistant(userId);
            Assert.assertTrue(result);
        }
    }


    /**
     * 校验用户是否报名AI助手
     */
    @Test
    public void checkAlum() {
        final String userId = "1105420961611624143";
        boolean result = algorithmAiRegisterService.checkAlbum(userId);
        System.out.println("用户已经报名：" + result);
        if (!result) {
            final AiRegisterReqDTO dto = new AiRegisterReqDTO();
            dto.setSourceBusiness(BusinessSourceEnum.ALBUM.getCode());
            dto.setModule(AIModuleEnum.AI_FILE_LIBRARY.getModule());
            dto.setUserId(userId);
            final BaseResult<AiRegisterVO> resp = algorithmAiRegisterController.accredit(dto);
            System.out.println(JsonUtil.toJson(resp));
            Assert.assertFalse(resp.isSuccess());

            result = algorithmAiRegisterService.checkAlbum(userId);
            Assert.assertTrue(result);
        }
    }

    /**
     * 报名全流程
     */
    @Test
    public void accredit() {
        final String userId = "1105420961611622182";

        // 先查询是否报名
        final AiRegisterReqDTO dto = new AiRegisterReqDTO();
        dto.setSourceBusiness(BusinessSourceEnum.ASSISTANT.getCode());
        dto.setUserId(userId);
        BaseResult<AiRegisterVO> result = algorithmAiRegisterController.get(dto);

        if (!result.isSuccess()) {
            System.out.println("查询状态返回结果：" + JsonUtil.toJson(result));
            throw new RuntimeException(result.getMessage());
        }

        System.out.println("报名状态：" + result.getData().getAuthStatus());
        if (result.getData().getAuthStatus() == -1) {
            System.out.println("未报名");
            result = algorithmAiRegisterController.accredit(dto);
            if (!result.isSuccess()) {
                System.out.println("报名返回结果：" + JsonUtil.toJson(result));
                throw new RuntimeException(result.getMessage());
            }

            System.out.println("报名结果状态：" + result.getData().getAuthStatus());
            if (result.getData().getAuthStatus() != 1) {
                throw new RuntimeException("报名识别");
            }

            System.out.println("报名成功校验缓存");
            if (!algorithmAiRegisterService.checkAiAssistant(userId)) {
                throw new RuntimeException("校验失败");
            }
        }

        System.out.println("已经报名，校验缓存");
        if (!algorithmAiRegisterService.checkAiAssistant(userId)) {
            throw new RuntimeException("校验失败");
        }
    }
}