package com.zyhl.yun.api.outer.web.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatPollingUpdateDTO;
import com.zyhl.yun.api.outer.controller.AssistantController;
import com.zyhl.yun.api.outer.domain.vo.chat.ContentVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * AI报名测试
 */
@SpringBootTest
@Slf4j
class AIChatPollingUpdateWebTest {

    @Resource
    private AssistantController assistantController;


    private final static String domain = "http://127.0.0.1:19027/ai/api/outer";
//    private final static String domain = "https://test.yun.139.com/ai/ai-test/ai/api/outer";


    public static void main(String[] args) {
//        chatDialogueList();
//        chatDialogueAddV1();
//        chatDialogueAddV1Continue();
//        chatDialogueAddV2Continue();
//        chatDialogueAddV2();
//        chatStop();

    }

    private static String defToken = "Basic bW9iaWxlOjE5ODAyMDIxMTU4OkdZV3JKdWdqfDF8MXwxNzE1OTk5NzIyOTg5fFlxck9KX0pocWc2VG91MWlkT2xBSlBXTDNHVWowS25EU2Y1VGEuTkJhYy5TTS5nZUZJRnpYUkgwYkZZRmR2UEdRakJNZ2hkMzIybWNJVjRnM2Iud3U1c1VxUVBqeDhTNWg4VmM4Unh4N1c3MjdMcFRkOVJ5Yzk1MlRsb2FUckpwUEhxbGVoZXJ3VmY3d1RKZzR1cW53bHlCUDd2dnFaWFhDeUtFVDkwYzZDay0=";

    // 请求头参数
    private static HttpRequest headerParams(String channel, String clientInfo, String token) {
        final HttpRequest request = HttpUtil.createPost(domain + "/assistant/chat/pollingUpdate");
        request.contentType("application/json;charset=UTF-8");
        request.header("Authorization", StrUtil.emptyToDefault(token, defToken));
        request.header("x-yun-api-version", "v1");
        request.header("x-yun-client-info", StrUtil.emptyToDefault(clientInfo, ""));
        request.header("x-yun-app-channel", StrUtil.emptyToDefault(channel, "102"));

        return request;
    }

    /**
     * 轮询对话结果
     */
    public static boolean chatPollingUpdate(String token, Long dialogueId, String channel, String clientInfo) {
        // 请求参数
        final AlgorithmChatPollingUpdateDTO params = new AlgorithmChatPollingUpdateDTO();
        params.setDialogueId(dialogueId);

        long start = System.currentTimeMillis();
        while (true) {
            final HttpRequest request = headerParams(channel, clientInfo, token).body(JSONUtil.toJsonStr(params));
            final String result = request.execute().body();
            System.out.println("轮询结果：" + result);
            BaseResult resp = JsonUtil.parseObject(result, BaseResult.class);
            if (!resp.isSuccess()) {
                System.out.println("接口异常");
                return false;
            }
            ContentVO vo = JsonUtil.parseObject(JsonUtil.toJson(resp.getData()), ContentVO.class);
            if (vo != null && vo.getOutAuditStatus() != null) {
                if (vo.getOutAuditStatus() == 2) {
                    System.out.println("问：" + vo.getInContent());
                    System.out.println("答：" + vo.getOutContent());
                    System.out.println("正常返回结果");
                    return true;
                }
            }

            if (System.currentTimeMillis() - start > 60 * 1000) {
                System.out.println("轮询结果超时");
                return false;
            }
            try {
                Thread.sleep(1500);
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }
    }


}