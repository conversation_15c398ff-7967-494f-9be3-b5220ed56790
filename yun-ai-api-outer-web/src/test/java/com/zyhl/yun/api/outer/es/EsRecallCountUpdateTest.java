package com.zyhl.yun.api.outer.es;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zyhl.hcy.yun.ai.common.base.es.domain.datahelper.repository.EsCommonKnowledgeRepository;
import com.zyhl.hcy.yun.ai.common.base.es.domain.datahelper.repository.EsPersonalKnowledgeRepository;
import com.zyhl.hcy.yun.ai.common.base.es.entity.CommonKnowledgeEsEntity;
import com.zyhl.hcy.yun.ai.common.base.es.entity.PersonalKnowledgeEsEntity;
import com.zyhl.hcy.yun.ai.common.base.hbase.domain.datahelper.repository.HbaseRepository;
import com.zyhl.yun.api.outer.application.service.knowledge.impl.KnowledgeRecallUpdateServiceImpl;
import com.zyhl.yun.api.outer.config.KnowledgeDialogueProperties;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmRagTextContentPO;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.client.RestHighLevelClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 知识库召回次数更新测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class EsRecallCountUpdateTest {

    @Resource
    private EsPersonalKnowledgeRepository esPersonalKnowledgeRepository;

    @Resource
    private EsCommonKnowledgeRepository esCommonKnowledgeRepository;

    @Resource
    private KnowledgeDialogueProperties knowledgeDialogueProperties;

    @Resource(name = "knowledgeDeleteThreadPool")
    private ExecutorService knowledgeDeleteThreadPool;

    @Resource
    private HbaseRepository hbaseRepository;

    public static final String CONNECT_SPLIT = "_";

    @Test
    public void saveHbase()  {
        String userId = "1113749343433277455";
        String fileId = "008200044555ed0d000001fd11111";
        String rowKey = userId + CONNECT_SPLIT + fileId;
        List<AlgorithmRagTextContentPO> list1 = new ArrayList<>();

        list1.add(AlgorithmRagTextContentPO.builder()
                .rowKey(rowKey)
                .fileId(fileId)
                .userId(userId)
                .resourceType("image")
                .content("测试内容")
                .build());

        try {
            boolean result = hbaseRepository.saveList(list1, AlgorithmRagTextContentPO.class);
            log.info("=======单元测试保存Hbase数据，result: {}", result);
        }catch (Exception e){
            log.error("=======单元测试保存Hbase数据异常，用户ID：{}， fileId: {}, e: ", userId, fileId, e);
        }

        // 查询Hbase数据
        List<String> list = ListUtil.toList(rowKey);
        try {
            // 删除Hbase数据
            List<AlgorithmRagTextContentPO> list2 = hbaseRepository.selectList(list, AlgorithmRagTextContentPO.class);
            log.info("=======单元测试查询Hbase数据，result: {}", JSONUtil.toJsonStr(list2.get(0)));
        }catch (Exception e){
            log.error("=======单元测试查询Hbase数据异常，用户ID：{}， fileId: {}, e: ", userId, fileId, e);
        }
    }

    @Test
    public void selectHbase()  {
        String userId = "1113749343433277455";
        String fileId = "008200044555ed0d000001fd11111";
        String rowKey = userId + CONNECT_SPLIT + fileId;
        List<String> list = ListUtil.toList(rowKey);
        try {
            // 删除Hbase数据
            List<AlgorithmRagTextContentPO> list1 = hbaseRepository.selectList(list, AlgorithmRagTextContentPO.class);
            log.info("=======单元测试查询Hbase数据，result: {}", JSONUtil.toJsonStr(list1.get(0)));
        }catch (Exception e){
            log.error("=======单元测试查询Hbase数据异常，用户ID：{}， fileId: {}, e: ", userId, fileId, e);
        }
    }

    @Test
    public void delHbase() {

        String userId = "1113749343433277455";
        String fileId = "008200044555ed0d000001fd11111";
        String rowKey = userId + CONNECT_SPLIT + fileId;
        List<String> list = ListUtil.toList(rowKey);
        try {
            // 删除Hbase数据
            boolean result = hbaseRepository.delByRowKeyList(list, AlgorithmRagTextContentPO.class);
            log.info("=======单元测试删除Hbase数据，result: {}", result);
        }catch (Exception e){
            log.error("=======单元测试删除Hbase数据异常，用户ID：{}， fileId: {}, e: ", userId, fileId, e);
        }

    }

    @Test
    public void insertDocumentTest() {
        List<PersonalKnowledgeEsEntity> list = getEntityList();
        PersonalKnowledgeEsEntity entity = list.get(0);
//        for (int i=0;i<100;i++){
            entity.setId("Fshr9Lci-nVRKXPoujZBMr6SJz0psPVJU_SPLIT_512_0" + "1111");
            entity.setUserId("1200786731143487566");
            entity.setFileId("Fshr9Lci-nVRKXPoujZBMr6SJz0psPVJU");
            String result = esPersonalKnowledgeRepository.insertDocument(entity);

//        }
        System.out.println("=======result: " + "批量插入结束");

    }

    @Test
    public void queryPersonalKnowledgeEsTest()  {
        String userId = "435644323147";
        String id = "666667";
        PersonalKnowledgeEsEntity result = esPersonalKnowledgeRepository.getPersonalKnowledgeById(userId,id);
        System.out.println("=======result: "+result);

    }

    @Test
    public void updateDocumentTest()  {
        String userId = "1188320236378850767";
        String id = "FuhQY4I8U79NsDClk5a1Hxa_4azq-lyDT_SPLIT_128_12";
        boolean result = esPersonalKnowledgeRepository.updatePersonalKnowledgeRecallCount(userId,id);
        System.out.println("=======result: "+result);
        JSONObject jsonObject = new JSONObject();
//        String json = "{\"fileId\":\"Fh9vzzPl00RW7yALwvbNOU5I3gBbI9x4V\",\"id\":\"Fh9vzzPl00RW7yALwvbNOU5I3gBbI9x4V_SPLIT_256_0\",\"knowledgeBase\":\"personal\",\"parseType\":\"SPLIT\",\"recallCount\":0,\"text\":\"[文件名]:  测试333.txt [文件内容]:  问题分析\\ntransport_serialization_exception\\n客户端无法反序列化服务器返回的响应数据。\\n可能的原因：\\n服务器返回的数据格式与客户端期望的格式不匹配。\\n数据在传输过程中被损坏。\\n客户端与服务器的版本不兼容。\\nillegal_state_exception\\n反序列化过程中遇到了意外的字节 0x63（ASCII 字符 'c'）。\\n可能的原因：\\n服务器返回的数据不是有效的 JSON 格式。\\n数据被截断或损坏。\\n根本原因\\n可能是由于以下原因之一：\\n客户端与服务器的版本不兼容。\"}";
//        String json2 = "{\"fileId\":\"Fh9vzzPl00RW7yALwvbNOU5I3gBbI9x4V\",\"id\":\"Fh9vzzPl00RW7yALwvbNOU5I3gBbI9x4V_SPLIT_256_0\",\"knowledgeBase\":\"personal\",\"parseType\":\"SPLIT\",\"recallCount\":0,\"text\":\"[文件名]:  测试333.txt [文件内容]:  问题分析\\ntransport_serialization_
    }

    @Test
    public void bathDeleteDocumentTest() throws InterruptedException {
//        List<String> ids =  esPersonalKnowledgeRepository.getPersonalKnowledgeEsIdList("435644323147", "3265367432452357", 20);
//        System.out.println("--------ids:" +ids);
        String userId = "1188320236378850767";
//        Long result = esPersonalKnowledgeRepository.batchDeleteById(userId,ids);
//        System.out.println("=======result: "+result);
        List<String> list = ListUtil.toList("FtZCOVPEtctBHi3ucTsFH2aB8_Y7Arr3Q");
        esPersonalKnowledgeRepository.batchDeleteDocumentById(userId,list,100,knowledgeDeleteThreadPool);
        Thread.sleep(20000);
    }

    @Test
    public void insertCommonDocumentTest()  {
        String baseId = knowledgeDialogueProperties.getKnowledgeBaseId();
        List<CommonKnowledgeEsEntity> list = getEntityList2();
        CommonKnowledgeEsEntity entity = list.get(0);
        entity.setId("789456");
        entity.setBaseId(baseId);
        entity.setFileId("3265367432452358888");
        boolean result = esCommonKnowledgeRepository.insertDocument(entity);
        System.out.println("=======result: "+result);

    }

    @Test
    public void updateCommonDocumentTest()  {
        String baseId = knowledgeDialogueProperties.getKnowledgeBaseId();
        CommonKnowledgeEsEntity entity = new CommonKnowledgeEsEntity();
        entity.setId("789456");
        entity.setBaseId(baseId);
        entity.setFileId("3265367432452358888");
        entity.setParseType("QA");
        boolean result = esCommonKnowledgeRepository.updateCommonKnowledgeRecallCount(entity);
        System.out.println("=======result: "+result);

    }

    @Test
    public void getCommonknowledgeDialogueTest()  {
        String baseId = knowledgeDialogueProperties.getKnowledgeBaseId();
        CommonKnowledgeEsEntity entity = new CommonKnowledgeEsEntity();
        entity.setId("789456");
        entity.setBaseId(baseId);
        entity.setFileId("3265367432452358888");
        entity.setParseType("QA");
        CommonKnowledgeEsEntity result = esCommonKnowledgeRepository.getCommonKnowledgeById(entity);
        System.out.println("=======result: "+result);

    }

    public List<CommonKnowledgeEsEntity> getEntityList2() {
        List<CommonKnowledgeEsEntity> entityList = ListUtil.toList();
        CommonKnowledgeEsEntity entity = CommonKnowledgeEsEntity.builder()
                .baseId("123456123456")
                .id("789456")
                .text("哈哈哈哈 好好说说好好睡吧大")
                .index(0)
                .fileId("3265367432452358888")
                .extension("txt")
                .size(100L)
                .createAt(3232435546657L)
                .description("ceshi")
                .isDeleted(0)
                .createTime(4325436567L)
                .updateTime(2323489L)
                .parseType("QA")
                .recallCount(1)
                .build();
        entityList.add(entity);

        return entityList;
    }

    public List<PersonalKnowledgeEsEntity> getEntityList() {
        ArrayList<Double> list = ListUtil.toList(
                -0.05653977394104004,
                0.009302281774580479,
                -0.002071953844279051,
                -0.014293063431978226,
                -0.025959962978959084,
                -0.03731934726238251,
                0.02462397702038288,
                -0.02164820209145546,
                -0.031999364495277405,
                -0.017764747142791748,
                0.013018785044550896,
                0.004571432247757912,
                -0.017651386559009552,
                0.0295723844319582,
                0.016770139336586,
                -0.018717627972364426,
                -0.011395289562642574,
                0.004650839604437351,
                0.006672042887657881,
                0.01394928153604269,
                -0.017353491857647896,
                0.051148030906915665,
                -8.991968934424222E-4,
                0.025245461612939835,
                -0.020063748583197594,
                -0.010255321860313416,
                -0.005039501469582319,
                0.0024774582125246525,
                0.015611601062119007,
                -0.024498634040355682,
                0.004022959619760513,
                -0.04699059575796127,
                -0.02524847351014614,
                -0.007361855357885361,
                -0.03983209654688835,
                -0.07615086436271667,
                -0.008412286639213562,
                0.009841172955930233,
                -0.043547481298446655,
                0.0454075001180172,
                -0.042052414268255234,
                -0.033166684210300446,
                0.020556658506393433,
                -0.012318046763539314,
                0.023660823702812195,
                -0.004159805830568075,
                0.011076732538640499,
                -0.010474578477442265,
                -0.007539158221334219,
                0.01947346329689026,
                -0.023904960602521896,
                -0.004985421895980835,
                0.0025421041063964367,
                -0.06515442579984665,
                0.02893988974392414,
                0.02945241704583168,
                -0.014797931537032127,
                -0.03950192779302597,
                -0.03449736908078194,
                -0.00855566468089819,
                -0.03358195349574089,
                0.010507965460419655,
                0.04460575059056282,
                0.00660816440358758,
                0.0323428213596344,
                0.03188652545213699,
                0.019115110859274864,
                0.004114506766200066,
                -0.007632121443748474,
                -0.02089950069785118,
                0.02315686270594597,
                0.04230928421020508,
                0.003951198887079954,
                -0.019379356876015663,
                -0.011970664374530315,
                -0.025034694001078606,
                0.008280451409518719,
                -0.053308505564928055,
                0.0014170128852128983,
                0.028248677030205727,
                -0.007562883663922548,
                0.012422556057572365,
                0.018018372356891632,
                -0.008681895211338997,
                -0.04257436841726303,
                0.0012172874994575977,
                0.004886293783783913,
                0.06686912477016449,
                -0.0021934511605650187,
                -0.03625982627272606,
                -0.04049958288669586,
                -0.01997409388422966,
                0.011907392181456089,
                -0.0034910659305751324,
                0.02305271103978157,
                -1.336098648607731E-4,
                -0.022917674854397774,
                -0.005377290304750204,
                0.01871996372938156,
                0.00691539142280817,
                -0.005924091674387455,
                0.02448003552854061,
                0.03502228856086731,
                -0.007615800481289625,
                0.00413207383826375,
                -0.020692992955446243,
                0.037979304790496826,
                0.008748646825551987,
                0.0019578197970986366,
                -0.010920600034296513,
                -0.002862031338736415,
                0.023501768708229065,
                0.04351229593157768,
                0.013723528943955898,
                0.0030092273373156786,
                -0.041999466717243195,
                -0.03307619318366051,
                -0.03251580893993378,
                0.01939769834280014,
                0.01961589604616165,
                -0.008138964883983135,
                -0.0159169752150774,
                0.025113683193922043,
                0.0065169017761945724,
                0.02742009423673153,
                0.0036326011177152395,
                0.013198182918131351,
                0.012025106698274612,
                -0.02793697454035282,
                0.0634176954627037,
                -0.00937880203127861,
                0.0444730706512928,
                -0.014343248680233955,
                0.03329944610595703,
                -0.045838143676519394,
                -0.006248534191399813,
                0.01695297099649906,
                0.036442019045352936,
                0.013799218460917473,
                -0.035547446459531784,
                -0.011713391169905663,
                0.052635740488767624,
                -0.033514346927404404,
                -0.0029592758510261774,
                0.0072421119548380375,
                -0.04770195484161377,
                0.0353325754404068,
                0.045891936868429184,
                -0.0026941851247102022,
                -0.026684021577239037,
                0.016709504649043083,
                -0.0023004154209047556,
                0.022055305540561676,
                -0.0053145806305110455,
                0.005581026431173086,
                -0.03521260619163513,
                0.006138973403722048,
                0.01724352315068245,
                0.008258131332695484,
                0.002253974787890911,
                -0.020172756165266037,
                -8.923180284909904E-4,
                -0.05018052086234093,
                0.0011382094817236066,
                0.03897557407617569,
                0.008912917226552963,
                0.012015287764370441,
                -0.03485803306102753,
                -0.03683917969465256,
                0.025925306603312492,
                0.020985160022974014,
                -0.037262432277202606,
                0.017375603318214417,
                -0.019338060170412064,
                0.04369034245610237,
                0.0066914004273712635,
                0.056982532143592834,
                0.01025715284049511,
                0.004869268275797367,
                -0.017552781850099564,
                -0.03380221128463745,
                0.0020698944572359324,
                -0.04060631990432739,
                -0.038383569568395615,
                -0.00763023691251874,
                0.05727057904005051,
                -0.007290664594620466,
                -0.06296119093894958,
                -0.01359455194324255,
                0.03287899121642113,
                0.0033723812084645033,
                -0.04512065649032593,
                0.007368153426796198,
                0.0072424933314323425,
                -0.052553847432136536,
                -0.011046698316931725,
                0.0029833451844751835,
                0.05165388435125351,
                -0.02212766744196415,
                -0.00879718642681837,
                0.006020442582666874,
                -0.006282472517341375,
                0.0066524590365588665,
                -0.01807384192943573,
                -0.017102351412177086,
                -0.015223569236695766,
                0.04247251898050308,
                0.007499001920223236,
                0.02752849832177162,
                -0.033905256539583206,
                0.014345007948577404,
                0.015719633549451828,
                -0.013601155951619148,
                0.01743144914507866,
                -0.02518686279654503,
                -0.05106174945831299,
                -0.00398385152220726,
                -0.009512937627732754,
                0.0011811342556029558,
                -0.04484599083662033,
                -0.018370339646935463,
                -0.0036130347289144993,
                -0.002546400763094425,
                0.0020863376557826996,
                0.046378567814826965,
                -0.021889694035053253,
                0.020067624747753143,
                0.06008179858326912,
                0.028454668819904327,
                0.0522911436855793,
                -0.010008502751588821,
                0.052003901451826096,
                0.04878716915845871,
                -0.025307435542345047,
                0.04104778170585632,
                -0.015050084330141544,
                -0.017955036833882332,
                0.059038858860731125,
                0.04830663278698921,
                0.002657779958099127,
                -0.05444676801562309,
                -0.0722552016377449,
                -4.088309360668063E-4,
                0.0559854656457901,
                -0.08660890907049179,
                0.05017765611410141,
                0.0059099989011883736,
                0.08984631299972534,
                -0.0156712643802166,
                -0.006049542687833309,
                -0.05285587161779404,
                -0.0032046325504779816,
                7.82806717325002E-4,
                -0.041370004415512085,
                0.005581534467637539,
                -0.01647005043923855,
                0.031608082354068756,
                -0.0180500615388155,
                -0.003284278092905879,
                -0.06800315529108047,
                0.033770978450775146,
                0.02769741415977478,
                -0.001489051035605371,
                0.016875354573130608,
                0.01524295937269926,
                -0.012507915496826172,
                -0.007640020921826363,
                0.0038240912836045027,
                0.0017181368311867118,
                0.02165897563099861,
                -0.035555221140384674,
                0.03050038032233715,
                -0.01746509224176407,
                0.002617723075672984,
                -0.0016451835399493575,
                -0.006287283729761839,
                -0.0717039704322815,
                -0.018171042203903198,
                0.1031741052865982,
                -2.51533230766654E-4,
                -0.04386834427714348,
                0.0567190945148468,
                0.03551964461803436,
                -0.04270729050040245,
                6.872259837109596E-5,
                -0.03381149098277092,
                0.021125424653291702,
                0.03929509222507477,
                0.008233667351305485,
                -0.03411777690052986,
                -0.023044226691126823,
                0.006726211402565241,
                0.09521355479955673,
                -0.01764945685863495,
                1.7676492279861122E-4,
                0.008360015228390694,
                -0.005899013485759497,
                -0.17274941504001617,
                -0.027133077383041382,
                -0.010283899493515491,
                -0.02629902772605419,
                0.02513539418578148,
                0.015631137415766716,
                -0.04411216825246811,
                0.019844962283968925,
                -0.03389555960893631,
                -0.009648963809013367,
                -0.013467461802065372,
                -0.05063685402274132,
                0.008172769099473953,
                -0.009906171821057796,
                0.01922660320997238,
                0.011042752303183079,
                -0.011659267358481884,
                0.014850548468530178,
                -0.024735935032367706,
                -0.02826126664876938,
                -0.027335556223988533,
                -0.03148460388183594,
                0.029393434524536133,
                0.01664378121495247,
                -0.0582389160990715,
                -0.03155255317687988,
                0.05200500413775444,
                0.03926881402730942,
                0.010210959240794182,
                -0.01803727075457573,
                -0.028815457597374916,
                0.022050002589821815,
                0.005582684185355902,
                -0.018088681623339653,
                0.04866011440753937,
                -0.00884112436324358,
                -0.024434417486190796,
                -0.03346060588955879,
                -0.016466174274683,
                -0.008533644489943981,
                0.06749708205461502,
                0.004906008951365948,
                0.014008000493049622,
                0.030034087598323822,
                0.0060825711116194725,
                -0.008453277871012688,
                0.009049168787896633,
                -0.05184528976678848,
                -0.003741032676771283,
                -0.003869221080094576,
                -0.023643983528017998,
                -0.043950729072093964,
                0.007291063666343689,
                -0.03640701621770859,
                -0.04657944664359093,
                6.168985855765641E-4,
                -0.022645296528935432,
                0.03527187928557396,
                0.013620206154882908,
                0.013210482895374298,
                -0.030494000762701035,
                -0.01878918707370758,
                0.0034402566961944103,
                0.01972956769168377,
                -0.0311515424400568,
                -0.012187370099127293,
                0.028387561440467834,
                -0.03606438636779785,
                0.04129409417510033,
                -0.017212720587849617,
                0.010986976325511932,
                -0.0041925907135009766,
                -0.016600776463747025,
                -0.004684767685830593,
                -0.04337715357542038,
                -0.025155050680041313,
                -0.005638066213577986,
                0.020571621134877205,
                0.0016583753749728203,
                -0.14748640358448029,
                0.021797433495521545,
                0.0031235378701239824,
                -0.010027865879237652,
                0.04415010288357735,
                -0.04398650303483009,
                -0.014008867554366589,
                0.015244673937559128,
                7.468877010978758E-4,
                0.04221777245402336,
                0.2026442140340805,
                0.006995063740760088,
                -0.0015094020636752248,
                0.023226041346788406,
                0.08478254079818726,
                0.007567983120679855,
                -0.007418811786919832,
                0.04861672595143318,
                0.034459248185157776,
                -0.049669649451971054,
                0.010058149695396423,
                0.025213519111275673,
                -0.029758675023913383,
                -4.716654366347939E-4,
                0.02973799593746662,
                -0.018664618954062462,
                -0.036196596920490265,
                0.039420269429683685,
                0.049229249358177185,
                0.002508782781660557,
                -0.015125809237360954,
                -0.023553600534796715,
                0.006795630324631929,
                0.009772161953151226,
                -0.028331242501735687,
                -0.025287948548793793,
                0.00996036734431982,
                -0.013737917877733707,
                -0.0036662714555859566,
                0.08468632400035858,
                -0.022873498499393463,
                0.01906399242579937,
                0.04685711860656738,
                -0.03304082527756691,
                -0.021500494331121445,
                0.006822850089520216,
                0.04045891389250755,
                -0.00526599632576108,
                -0.025273878127336502,
                0.02490728534758091,
                -0.008989783003926277,
                -0.06166733428835869,
                0.06291281431913376,
                0.0070589338429272175,
                -0.02426362782716751,
                0.0018741413950920105,
                -0.050778426229953766,
                -0.0035166069865226746,
                -0.04743949696421623,
                -0.008915354497730732,
                0.025959892198443413,
                -0.022746296599507332,
                -0.03399888798594475,
                -0.012737453915178776,
                -0.0013320081634446979,
                -0.007910173386335373,
                -0.010953990742564201,
                -3.800858976319432E-4,
                -0.004606582690030336,
                -0.03382463380694389,
                0.014009672217071056,
                -0.011858438141644001,
                -0.009862015023827553,
                0.005079242400825024,
                -0.010294320993125439,
                0.045074887573719025,
                0.07624911516904831,
                -0.0031182472594082355,
                0.019838886335492134,
                0.024206090718507767,
                -0.06225086748600006,
                0.015654034912586212,
                0.015652989968657494,
                -0.0030489598866552114,
                0.036306723952293396,
                -0.02865121327340603,
                0.016957703977823257,
                0.0017327922396361828,
                -0.042257510125637054,
                7.454060250893235E-4,
                -0.01692976802587509,
                -0.015892190858721733,
                -0.04065381735563278,
                0.029005415737628937,
                0.013868512585759163,
                0.03941311314702034,
                -0.010705871507525444,
                0.03217590972781181,
                -0.021171564236283302,
                -0.002177390968427062,
                -0.0019001537002623081,
                -3.224603715352714E-4,
                0.0014615111285820603,
                -0.00916583277285099,
                -0.010060196742415428,
                0.009470788761973381,
                -0.00762331485748291,
                0.0018002273282036185,
                -0.0528789646923542,
                -0.01736079901456833,
                -0.013200384564697742,
                -0.015317835845053196,
                0.02682829461991787,
                -0.05659289285540581,
                0.029997875913977623,
                9.128086385317147E-4,
                -0.014283974654972553,
                -0.012699143029749393,
                -0.0022095409221947193,
                0.01698324829339981,
                -0.041590046137571335,
                0.026134129613637924,
                0.02536604553461075,
                -0.03014509007334709,
                -0.029404114931821823,
                0.05793336406350136,
                0.07260945439338684,
                0.02127152495086193,
                0.03825458139181137,
                -0.003138482803478837,
                -0.042791035026311874,
                0.01681114360690117,
                0.028776509687304497,
                0.00897962599992752,
                0.015634318813681602,
                -0.08350437879562378,
                0.03151853382587433,
                0.00305486423894763,
                -0.07658521085977554,
                0.026480386033654213,
                0.02020566165447235,
                -0.04483076557517052,
                -0.012065568938851357,
                0.044683780521154404,
                0.043134525418281555,
                -0.018095768988132477,
                0.034438710659742355,
                0.013916902244091034,
                -0.0061605023220181465,
                0.004951744806021452,
                -0.027622591704130173,
                -0.020957596600055695,
                -0.010014781728386879,
                -0.03676247224211693,
                0.02132529392838478,
                -0.015031344257295132,
                -0.0035067072603851557,
                -0.009531909599900246,
                -0.00992489978671074,
                0.018858009949326515,
                0.008459906093776226,
                0.06776951253414154,
                0.004914917983114719,
                -0.08168267458677292,
                -9.256618213839829E-4,
                -0.05470755696296692,
                -0.006702205166220665,
                -0.017684198915958405,
                -0.021259142085909843,
                -0.03964134678244591,
                -0.04856420308351517,
                0.032739751040935516,
                0.01020458247512579,
                0.05638781562447548,
                0.010114219971001148,
                0.0026438036002218723,
                0.009616049937903881,
                0.015747008845210075,
                -0.035264551639556885,
                -0.01868409663438797,
                0.030552515760064125,
                -0.0473138801753521,
                -0.05490018427371979,
                -0.04802490770816803,
                0.015881190076470375,
                0.019366173073649406,
                -0.022834526374936104,
                0.02966625802218914,
                -0.03568286448717117,
                -0.02096737176179886,
                -0.031048500910401344,
                -0.004772316198796034,
                -0.027380216866731644,
                -0.02852506749331951,
                -0.022500421851873398,
                -0.025141224265098572,
                -0.016648633405566216,
                -0.016636589542031288,
                0.030112024396657944,
                -0.01722588762640953,
                -0.012708963826298714,
                0.15366898477077484,
                0.07119505852460861,
                6.459979340434074E-5,
                0.03664397820830345,
                0.012006287463009357,
                0.0660000741481781,
                0.019121944904327393,
                0.0019001467153429985,
                0.0029889484867453575,
                -0.029246896505355835,
                0.02575318142771721,
                0.013683537021279335,
                -0.006908453535288572,
                -0.017691388726234436,
                -0.002984503284096718,
                -0.011011054739356041,
                0.02988540008664131,
                0.005264135543256998,
                0.027476200833916664,
                9.479866130277514E-4,
                -0.050272129476070404,
                -0.05717860162258148,
                -0.01838684268295765,
                0.03206677734851837,
                -0.03524601086974144,
                0.004190008156001568,
                0.05695154517889023,
                0.011950802057981491,
                -0.037995170801877975,
                -0.019436964765191078,
                0.018344035372138023,
                -0.0020528617314994335,
                -0.019825711846351624,
                0.022574519738554955,
                0.03228377550840378,
                0.01603555493056774,
                0.008750994689762592,
                -0.028902124613523483,
                -0.007789188995957375,
                -0.017364781349897385,
                0.011806687340140343,
                -0.021184151992201805,
                -0.05311361700296402,
                7.315273978747427E-4,
                0.019645970314741135,
                0.04172839596867561,
                0.009435086511075497,
                0.008814020082354546,
                -0.012328133918344975,
                -0.02642511948943138,
                -0.02478073723614216,
                0.025855453684926033,
                -0.024282190948724747,
                -0.03411204740405083,
                0.03146127611398697,
                0.003247233107686043,
                -0.02952309139072895,
                -0.003030928783118725,
                0.011951196938753128,
                -0.03009817935526371,
                -0.020234817638993263,
                -1.2787846208084375E-4,
                -0.038471296429634094,
                -0.013566670939326286,
                0.029979821294546127,
                0.033102549612522125,
                0.019835880026221275,
                -0.026264965534210205,
                -0.051252398639917374,
                -0.023648403584957123,
                0.002236083149909973,
                -0.023337509483098984,
                -0.0288971159607172,
                -0.03710578754544258,
                -0.01957021839916706,
                0.0112112732604146,
                -0.010732296854257584,
                -0.006178974639624357,
                -0.0027717757038772106,
                -0.028020456433296204,
                0.012873873114585876,
                0.0039051929488778114,
                -0.038670483976602554,
                0.024345308542251587,
                -0.022185256704688072,
                -0.0510873906314373,
                8.523333235643804E-4,
                0.006441550794988871,
                -0.04374117776751518,
                -0.04225198179483414,
                0.05276687070727348,
                0.03190740570425987,
                0.03545888140797615,
                -0.0586596317589283,
                -0.0315716378390789,
                0.044424183666706085,
                0.005622475408017635,
                -0.050989191979169846,
                -0.03252698481082916,
                -0.01215351652354002,
                -0.03360345959663391,
                -0.02840305306017399,
                0.03056732565164566,
                -0.03374785557389259,
                -0.00915758591145277,
                0.01954665780067444,
                0.0131995165720582,
                -0.013378874398767948,
                -0.007178067695349455,
                3.9342697709798813E-4,
                0.012424738146364689,
                0.015789374709129333,
                0.0035588007885962725,
                -0.010230048559606075,
                0.06058479845523834,
                -0.02201342023909092,
                -0.04845889285206795,
                0.0601472444832325,
                0.018696146085858345,
                -0.006503221578896046,
                -0.05342448502779007,
                0.03006225824356079,
                0.024243703112006187,
                0.04072260484099388,
                0.020158305764198303,
                0.04762425646185875,
                -0.006746124010533094,
                -0.03614324331283569,
                0.025709526613354683,
                -0.02177550457417965,
                -0.007903420366346836,
                -0.048171769827604294,
                0.05616016685962677,
                -0.05407147854566574,
                0.004589366726577282,
                -0.013198788277804852,
                -7.046473911032081E-4,
                -0.039875488728284836,
                0.06062404066324234,
                -0.020255211740732193,
                -0.02260022982954979,
                -0.01870092749595642,
                -0.035968344658613205,
                -0.01052345335483551,
                6.881337612867355E-4,
                0.08979271352291107,
                -0.0015481598675251007,
                -0.02781752496957779,
                -0.009427684359252453,
                0.010557941161096096,
                -0.022164449095726013,
                0.052244387567043304,
                -0.03061368316411972,
                -0.015490680001676083,
                0.0101820919662714,
                -0.041837386786937714,
                -0.0013728830963373184,
                -0.038230977952480316,
                -0.015940973535180092,
                0.0062814150005578995,
                0.017536789178848267,
                -0.010685784742236137,
                0.01280311681330204,
                0.05716109275817871,
                0.016866257414221764,
                -0.003649083198979497,
                0.005354329012334347,
                -0.002678732853382826,
                0.05893556401133537,
                -0.04739665985107422,
                0.055546339601278305,
                0.006899521220475435,
                0.009304060600697994,
                0.027279533445835114,
                -0.006364508997648954,
                0.009034211747348309,
                0.015419283881783485,
                -0.060933079570531845,
                -0.014672033488750458,
                -0.016688890755176544,
                0.020669545978307724,
                -0.005887181032449007,
                0.00961671955883503,
                0.004920650739222765,
                0.011889967136085033,
                -0.010091746225953102,
                0.027660487219691277,
                1.276780676562339E-4,
                -0.006187804508954287,
                7.630172185599804E-4,
                -0.020306983962655067,
                -0.015284974128007889,
                -0.01688503660261631,
                0.014853361994028091,
                0.017719103023409843,
                -0.015507107600569725,
                0.010904215276241302,
                0.07992766797542572,
                -0.014793499372899532,
                -0.007855786010622978,
                -0.009767689742147923,
                -0.009372902102768421,
                0.01736401580274105,
                -0.14928503334522247,
                0.01885111629962921,
                -0.04471205174922943,
                0.009789462201297283,
                -0.009871254675090313,
                0.02031538635492325,
                -0.00619081873446703,
                -0.004831626079976559,
                0.001506365486420691,
                -0.021563896909356117,
                7.462488138116896E-4,
                0.0011995701352134347,
                0.03888861835002899,
                -0.029964420944452286,
                0.03467072173953056,
                0.030751653015613556,
                -0.002449227264150977,
                -0.019463781267404556,
                0.010168636217713356,
                0.022257758304476738,
                0.002815155778080225,
                -0.01731846109032631,
                0.007395067252218723,
                0.056802500039339066,
                -0.008620047010481358,
                0.030888566747307777,
                -0.0047861444763839245,
                0.03278278186917305,
                -0.022946013137698174,
                -0.05408203601837158,
                -0.0258929580450058,
                0.01057238969951868,
                0.005932236555963755,
                0.05985109508037567,
                -0.005202340427786112,
                0.010098608210682869,
                0.023758182302117348,
                -0.008385001681745052,
                0.03335242345929146,
                0.0034664401318877935,
                -0.04252394288778305,
                0.008060449734330177,
                0.023546969518065453,
                0.01419706828892231,
                0.021753350272774696,
                0.04164932295680046,
                -0.0380670428276062,
                -0.044192757457494736,
                -0.04542238637804985,
                -0.010408470407128334,
                0.04450511932373047,
                0.031603503972291946,
                -0.0067398082464933395,
                0.008487732149660587,
                -0.007087088655680418,
                0.025183578953146935,
                -0.022098002955317497,
                -0.0018745678244158626,
                -0.02616112120449543,
                0.06537143141031265,
                -0.03474808856844902,
                0.05318606644868851,
                -0.05412690341472626,
                -0.021762657910585403,
                -0.025343043729662895,
                0.02264047972857952,
                -0.030180567875504494,
                0.026542725041508675,
                0.006456672679632902,
                0.015315407887101173,
                0.009663683362305164,
                -0.01291303988546133,
                -0.01873263716697693,
                0.019344482570886612,
                -0.003473539836704731,
                0.023705124855041504,
                0.06932763755321503,
                -0.008096854202449322,
                -0.031698498874902725,
                -0.005641569383442402,
                0.005056757014244795,
                -0.010892552323639393,
                -0.06789205223321915,
                0.06586237251758575,
                0.036579281091690063,
                0.0010653628269210458,
                0.020665844902396202,
                0.008374021388590336,
                -0.01869126223027706,
                -0.004187092185020447,
                -0.017595501616597176,
                -0.049506399780511856,
                -0.0043318807147443295,
                0.01100699882954359,
                -0.024823419749736786,
                0.019345812499523163,
                -0.023184580728411674,
                0.010546770878136158,
                0.033624786883592606,
                0.00294514955021441,
                -0.003843726124614477,
                -0.023525267839431763,
                0.0033798778895288706,
                0.004160914104431868,
                -0.0335649698972702,
                0.029674561694264412,
                -0.00612646946683526,
                -0.020272064954042435,
                0.007845534943044186,
                -0.02130000852048397,
                0.015526634640991688,
                -0.01053551584482193,
                -0.0039298636838793755,
                0.030216529965400696,
                -0.05142650380730629,
                -9.459865395911038E-4,
                0.0026579101104289293,
                0.031158996745944023,
                0.017143597826361656,
                -0.013616984710097313,
                -0.015424354001879692,
                -0.021401742473244667,
                0.004161291755735874,
                -0.028337832540273666,
                -0.02378350868821144,
                0.029998907819390297,
                0.023775577545166016,
                0.019297897815704346,
                -0.05665132403373718,
                -0.02140539325773716,
                0.01617463119328022,
                -0.022121824324131012,
                0.010218162089586258,
                0.04040196165442467,
                -0.014349274337291718,
                -0.012665494345128536,
                -0.013217566534876823,
                0.024369508028030396,
                -0.049826689064502716,
                0.008634870871901512,
                -0.027982447296380997,
                -0.032492443919181824,
                -0.02239668369293213,
                -0.024761803448200226,
                0.01061919890344143,
                -0.01802530698478222,
                0.015321815386414528,
                0.034785687923431396,
                -0.028529537841677666,
                -0.04072500392794609,
                -0.009988890960812569,
                0.04182173311710358,
                0.0028079678304493427,
                0.049436021596193314,
                -9.277588687837124E-4,
                0.05297936871647835,
                0.020657723769545555,
                0.06681054830551147,
                -0.0394047386944294,
                0.044832248240709305,
                -0.017172235995531082,
                -0.031953491270542145,
                -0.035001177340745926,
                -0.04318578913807869,
                -0.01181101892143488,
                -0.03750201687216759,
                -9.564503561705351E-4,
                -0.019400615245103836,
                -0.02136281505227089,
                -0.0048597403801977634,
                -0.04500608891248703,
                0.02052135206758976,
                -0.018563468009233475,
                0.0010902207577601075,
                0.00831429660320282,
                -0.016093535348773003,
                0.033444225788116455,
                -0.025639869272708893,
                0.033818647265434265,
                -0.02389012835919857,
                -0.00687281833961606,
                0.03352343663573265,
                0.03178862854838371,
                -0.005921985954046249,
                0.027906375005841255,
                0.002993553876876831,
                -0.025946926325559616,
                0.02126564271748066,
                0.009798379614949226,
                0.0028019635938107967,
                -0.020662821829319,
                0.017933012917637825,
                0.03458114340901375,
                0.019752997905015945,
                0.05411411076784134,
                0.02019719034433365,
                0.02873164601624012,
                0.01920214109122753,
                -9.15876473300159E-4,
                0.04924028366804123,
                -0.004486769903451204,
                -0.01507466472685337,
                -0.01357884518802166,
                0.04637736827135086,
                -0.0032847519032657146,
                0.018496723845601082,
                0.008038497529923916,
                -0.05174993723630905,
                -0.023952314630150795,
                0.007911966182291508,
                -1.355352287646383E-4,
                0.003097018925473094,
                -0.011282282881438732,
                0.040415290743112564,
                0.05308936536312103,
                5.055191577412188E-4,
                0.04207795858383179,
                -0.016149558126926422,
                -2.200473827542737E-4,
                -0.0065133520402014256,
                0.05291047319769859,
                0.035029519349336624,
                0.03992775082588196,
                0.010178258642554283,
                -0.01710403338074684,
                -0.04344535991549492,
                -0.0016512423753738403,
                0.005925435107201338,
                -0.0245882086455822,
                -0.006458741147071123,
                0.034378379583358765,
                -0.013642561621963978,
                -0.017329217866063118,
                0.04767106845974922,
                0.028739098459482193,
                0.02063729055225849,
                -0.023231035098433495,
                -0.007083586882799864,
                -0.008436460047960281,
                -0.017376169562339783,
                0.021380839869379997,
                0.006946709007024765,
                0.02661450393497944,
                -0.014555810950696468
        );

        List<PersonalKnowledgeEsEntity> entityList = ListUtil.toList();
        PersonalKnowledgeEsEntity entity = PersonalKnowledgeEsEntity.builder()
                .id("Fshr9Lci-nVRKXPoujZBMr6SJz0psPVJU_SPLIT_512_011111")
                .baseId("Fshr9Lci-nVRKXPoujZBMr6SJz0psPVJU11111")
                .text("哈哈哈哈 好好说说好好睡吧大")
                .textSize(10L)
                .embeddings(list.stream().map(BigDecimal::new).collect(Collectors.toList()))
                .index(0)
                .fileId("Fshr9Lci-nVRKXPoujZBMr6SJz0psPVJU")
                .extension("doc")
                .size(25088L)
                .createAt(3232435546657L)
                .description("ceshi")
                .isDeleted(0)
                .createTime(4325436567L)
                .updateTime(2323489L)
                .userId("1200786731143487566")
                .ownerId("232445")
                .storeType(1)
                .parseType("SPLIT")
                //.recallCount(0)
                .build();
        entityList.add(entity);

        return entityList;
    }
}
