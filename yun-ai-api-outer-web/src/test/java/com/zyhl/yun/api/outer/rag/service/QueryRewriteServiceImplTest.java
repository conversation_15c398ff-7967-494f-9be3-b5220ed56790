package com.zyhl.yun.api.outer.rag.service;

import cn.hutool.core.util.IdUtil;
import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.hcy.yun.ai.common.rag.client.RewriteClient;
import com.zyhl.yun.api.outer.config.KnowledgeDialogueProperties;
import com.zyhl.yun.api.outer.external.RagExternalService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.slf4j.MDC;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class QueryRewriteServiceImplTest {

    @Resource
    private RagExternalService regExternalService;

    @Resource
    private RewriteClient rewriteClient;

    @Resource
    private KnowledgeDialogueProperties knowledgeDialogueProperties;

    @BeforeEach
    public void setUp() {
    }

    @Test
    public void rewriteForTextModelExecute() {
        String query = "瑜伽冥想";
        TextModelTextReqDTO reqDTO = new TextModelTextReqDTO();
        reqDTO.setSessionId("111111111");
        reqDTO.setTaskId("111111111-001");
        reqDTO.setUserId("111111111-001");
        TextModelMessageDTO message = new TextModelMessageDTO();
        message.setContent(query);
        reqDTO.setMessageDtoList(Collections.singletonList(message));

        for (int i = 0; i < 5; i++) {
            String result = regExternalService.questionRewrite(reqDTO);
            log.info("query:{}, result:{}", query, result);
        }
    }


    @Test
    public void rewrite() {
        String query1 = "吴婉霓来自哪各学校";
        String query2 = "她喜欢打羽毛球不";
        String query3 = "她在哪个学校毕业";
        String query4 = "她之前在哪里工作";
        TextModelTextReqDTO reqDTO = new TextModelTextReqDTO();
        reqDTO.setSessionId("111111111");
        reqDTO.setTaskId("111111111-001");
        reqDTO.setUserId("111111111-001");

        List<TextModelMessageDTO> messageDTOList = new ArrayList<>();
        TextModelMessageDTO message = new TextModelMessageDTO();
        message.setContent(query1);
        message.setRole(TextModelRoleEnum.USER.getName());


        TextModelMessageDTO message2 = new TextModelMessageDTO();
        message2.setContent(query2);
        message2.setRole(TextModelRoleEnum.USER.getName());

        TextModelMessageDTO message3 = new TextModelMessageDTO();
        message3.setContent(query3);
        message3.setRole(TextModelRoleEnum.USER.getName());


        TextModelMessageDTO message4 = new TextModelMessageDTO();
        message4.setContent(query4);
        message4.setRole(TextModelRoleEnum.USER.getName());

        messageDTOList.add(message);
        messageDTOList.add(message2);
        messageDTOList.add(message3);
        messageDTOList.add(message4);
        reqDTO.setMessageDtoList(messageDTOList);
        MDC.put(LogConstants.TRACE_ID, IdUtil.simpleUUID());
        String result = rewriteClient.questionRewrite(reqDTO, knowledgeDialogueProperties.getRewriteConfig());
        log.info("query:{}, result:{}", query4, result);
    }
}
