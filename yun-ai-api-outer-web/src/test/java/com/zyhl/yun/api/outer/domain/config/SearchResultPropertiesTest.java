package com.zyhl.yun.api.outer.domain.config;

import com.zyhl.yun.api.outer.config.SearchResultProperties;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchActivityParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchDiscoveryParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchFunctionParam;
import com.zyhl.yun.api.outer.enums.chat.search.SearchTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 搜索结果配置-测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class SearchResultPropertiesTest {

    @Resource
    private SearchResultProperties searchResultProperties;

    @Test
    public void test() {
        SearchResultProperties.TabSort discovery = searchResultProperties.getTabSort(SearchDiscoveryParam.class);
        System.out.println(discovery.getRealSort("123"));
        System.out.println(discovery.getRealSort("测试题1"));
        SearchResultProperties.TabSort function = searchResultProperties.getTabSort(SearchFunctionParam.class);
        System.out.println(function.getRealSort("保险箱1"));
        SearchResultProperties.TabSort activity = searchResultProperties.getTabSort(SearchActivityParam.class);
        System.out.println(activity.getRealSort("云朵1"));

        SearchResultProperties.Recommend recommend = searchResultProperties.getRecommend(SearchTypeEnum.FUNCTION.getSearchType());
        System.out.println(String.format(recommend.getIntentionTemplate(), "备份"));
    }

}
