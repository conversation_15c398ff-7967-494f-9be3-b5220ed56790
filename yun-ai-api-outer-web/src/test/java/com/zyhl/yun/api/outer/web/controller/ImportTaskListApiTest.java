package com.zyhl.yun.api.outer.web.controller;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileTaskListReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileBatchImportReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileTaskBatchDeleteReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileTaskRetryReqDTO;
import com.zyhl.yun.api.outer.controller.KnowledgeBaseImportController;
import com.zyhl.yun.api.outer.domain.vo.ImportNoteInfoVO;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 知识库导入任务列表接口测试类
 */
@SpringBootTest
@AutoConfigureMockMvc
public class ImportTaskListApiTest {

    @Autowired
    private KnowledgeBaseImportController knowledgeBaseImportController;

    @Test
    public void testGetAllImportTasks() {
        // 准备请求参数
        KnowledgeFileTaskListReqDTO reqDTO = new KnowledgeFileTaskListReqDTO();
        reqDTO.setUserId("user123");
        reqDTO.setBaseId("5001");
        reqDTO.setSourceChannel("10102");
        // 不设置状态，应该返回所有状态的记录
        BaseResult<?> taskList = knowledgeBaseImportController.getTaskList(reqDTO);
        System.out.println("==============返回结果");
        System.out.println(taskList.getData());
        Assertions.assertNotNull(taskList);
    }

    @Test
    public void testBatchDeleteTasks() {
        // 准备请求参数
        KnowledgeFileTaskBatchDeleteReqDTO reqDTO = new KnowledgeFileTaskBatchDeleteReqDTO();
        reqDTO.setUserId("user123");
        reqDTO.setSourceChannel("10102");
        reqDTO.setTaskIdList(Arrays.asList("1001", "1002", "1003"));
        
        // 执行批量删除
        BaseResult<?> result = knowledgeBaseImportController.batchDelete(reqDTO);
        System.out.println("==============批量删除结果");
        System.out.println(result);
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.isSuccess());
    }

    @Test
    public void testBatchImportTasks() {
        // 准备请求参数
        KnowledgeFileBatchImportReqDTO reqDTO = new KnowledgeFileBatchImportReqDTO();
        reqDTO.setUserId("user123");
        reqDTO.setBaseId("1211303315066961926");
        reqDTO.setSourceChannel("10102");
        reqDTO.setResourceType(2); // 设置资源类型为笔记类型

        // 准备笔记列表
        List<ImportNoteInfoVO> noteList = new ArrayList<>();
        ImportNoteInfoVO note1 = new ImportNoteInfoVO();
        note1.setNoteId("note1");
        note1.setTitle("Test Note 1");
        noteList.add(note1);

        ImportNoteInfoVO note2 = new ImportNoteInfoVO();
        note2.setNoteId("note2");
        note2.setTitle("Test Note 2");
        noteList.add(note2);

        reqDTO.setNoteList(noteList);

        // 执行批量导入
        BaseResult<?> result = knowledgeBaseImportController.batchImport(reqDTO);
        System.out.println("==============批量导入结果");
        System.out.println(result);
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.isSuccess());
    }

    @Test
    public void testRetryTask() {
        // 准备请求参数
        KnowledgeFileTaskRetryReqDTO reqDTO = new KnowledgeFileTaskRetryReqDTO();
        reqDTO.setUserId("1199996031959244754");
        reqDTO.setSourceChannel("10102");
        reqDTO.setTaskId("1211186713147129857");
        
        // 执行重试
        BaseResult<?> result = knowledgeBaseImportController.retryTask(reqDTO);
        System.out.println("==============重试结果");
        System.out.println(result);
    }
} 