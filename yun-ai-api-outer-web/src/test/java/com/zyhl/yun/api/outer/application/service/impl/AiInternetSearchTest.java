package com.zyhl.yun.api.outer.application.service.impl;

import com.zyhl.hcy.yun.ai.common.model.api.client.ali.dto.GenericSearchDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.ali.vo.GenericSearchVO;
import com.zyhl.yun.api.outer.external.AiInternetSearchExternalService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * className: AiInternetSearchTest
 * description: AI全网搜测试类
 *
 * <AUTHOR>
 * @date 2025/5/8
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class AiInternetSearchTest {

    @Resource
    private AiInternetSearchExternalService aiInternetSearchExternalService;

    /**
     * 阿里云标准搜索接口——测试方法
     */
    @Test
    public void testSearch() {
        List<String> queryText = new ArrayList<>();
        queryText.add("泰坦尼克号 优酷链接");
        queryText.add("泰坦尼克号 爱奇艺链接");
        queryText.add("泰坦尼克号 腾讯视频链接");
        queryText.add("普罗米修斯 优酷链接");
        queryText.add("普罗米修斯 爱奇艺链接");
        queryText.add("普罗米修斯 腾讯视频链接");
        queryText.add("无间道 优酷链接");
        queryText.add("无间道 爱奇艺链接");
        queryText.add("无间道 腾讯视频链接");
        List<String> linkList = new CopyOnWriteArrayList<>();
        CompletableFuture.allOf(queryText.stream()
                .map(query -> CompletableFuture.runAsync(() -> {
                    GenericSearchDTO genericSearchDTO = new GenericSearchDTO();
                    genericSearchDTO.setQuery(query);
                    List<GenericSearchVO> genericSearchVOS = aiInternetSearchExternalService.genericSearch(genericSearchDTO);
                    List<String> links = genericSearchVOS.stream()
                            .map(GenericSearchVO::getLink)
                            .collect(Collectors.toList());
                    linkList.addAll(links);
                })).toArray(CompletableFuture[]::new)).join();

        AtomicInteger index = new AtomicInteger(1);
        linkList.forEach(link -> {
            System.out.println("link" + index + " = " + link);
            index.getAndIncrement();
        });
    }
}