package com.zyhl.yun.api.outer.mq;

import com.zyhl.yun.api.outer.application.service.impl.AlgorithmAiRegisterServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2024/7/30 14:17
 * @desc AI授权（文档智能搜索）报名发送MQ
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class TopicLocalAlgorithmAuthorizeTest {

    @Autowired
    private AlgorithmAiRegisterServiceImpl algorithmAiRegisterService;

    @Test
    public void test() {

        algorithmAiRegisterService.sendDocIntelligentSearchMq("1128834520566399061","enable");
    }

}
