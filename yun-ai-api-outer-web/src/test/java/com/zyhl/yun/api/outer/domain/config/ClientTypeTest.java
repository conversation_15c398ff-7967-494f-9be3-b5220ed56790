package com.zyhl.yun.api.outer.domain.config;

import com.zyhl.yun.api.outer.config.ClientTypeProperties;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 渠道号测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class ClientTypeTest {

    @Resource
    private ClientTypeProperties clientTypeProperties;

    @Test
    public void isInner() {
        Assert.assertTrue(clientTypeProperties.isInner("1"));
        Assert.assertTrue(!clientTypeProperties.isInner("3"));
        System.out.println("执行成功");
    }


}
