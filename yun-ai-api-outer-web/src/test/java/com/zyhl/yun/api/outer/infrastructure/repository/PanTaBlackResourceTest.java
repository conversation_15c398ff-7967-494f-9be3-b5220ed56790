package com.zyhl.yun.api.outer.infrastructure.repository;

import com.zyhl.yun.api.outer.domainservice.BlackResourceHandleService;
import com.zyhl.yun.api.outer.enums.BlackResourceParityEnum;
import com.zyhl.yun.api.outer.repository.BlackResourceRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 全网搜黑名单单元测试类
 **/
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class PanTaBlackResourceTest {

    @Resource
    private BlackResourceRepository blackResourceRepository;

    @Resource
    private BlackResourceHandleService blackResourceHandleService;

    private ConcurrentHashMap<String, Object> localCache = new ConcurrentHashMap<>();

    @Test
    public void getAllDate() {
        log.info("{}", blackResourceRepository.getAllDate());
    }

    @Test
    public void getAllDate1() {
        boolean isSensitive = blackResourceHandleService.isSensitive("谁有泰坦尼克号的资源呀 百度网盘", BlackResourceParityEnum.PRECISE.getType());
        log.info("----------------------isSensitive:{}", isSensitive);
    }

    @Test
    public void getResourceList() {
        List<String> names = new ArrayList<>();
        names.add("百度网盘");
        names.add("老爸当家");
        names.add("富贵逼人");
        names.add("飞鹰计划");
        List<Integer> types = new ArrayList<>();
        types.add(0);
        types.add(1);
        types.add(2);
        types.add(3);
        log.info("{}", blackResourceRepository.getResourceNameList(names, types));
    }

}
