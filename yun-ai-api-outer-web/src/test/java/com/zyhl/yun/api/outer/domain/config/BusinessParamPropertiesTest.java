package com.zyhl.yun.api.outer.domain.config;


import cn.hutool.core.date.DateUtil;
import com.zyhl.yun.api.outer.config.BusinessParamProperties;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 业务参数配置-测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class BusinessParamPropertiesTest {

    @Resource
    private BusinessParamProperties businessParamProperties;

    @Test
    public void test() {
        System.out.println(businessParamProperties.getChatContentList().getQueryMaxDays());
        System.out.println(DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -businessParamProperties.getChatContentList().getQueryMaxDays())));
    }

}
