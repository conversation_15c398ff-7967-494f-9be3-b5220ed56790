package com.zyhl.yun.api.outer.domain.config;

import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 渠道号测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class SourceChannelsTest {

    @Resource
    private SourceChannelsProperties sourceChannelsProperties;

    @Test
    public void isExist() {
        final boolean b = sourceChannelsProperties.isExist("10102");
        System.out.println("isExist:" + b);
    }

    @Test
    public void getType() {
        final String type = sourceChannelsProperties.getType("10104");
        System.out.println("getType:" + type);
    }

    @Test
    public void check() {
        final boolean b = sourceChannelsProperties.check("10106", "34243");
        System.out.println("check:" + b);
    }

    @Test
    public void xiaoTian() {
        final boolean b = sourceChannelsProperties.isXiaoTian("10108");
        System.out.println("小天渠道号：" + b);
    }

    @Test
    public void mail() {
        final boolean b = sourceChannelsProperties.isMail("10108");
        System.out.println("云邮渠道号：" + b);
    }

    @Test
    public void end() {
        sourceChannelsProperties.getChannelList().forEach(item -> {
            System.out.println(item.getChannel() + "是端内渠道号：" + sourceChannelsProperties.isInner(item.getChannel()));
        });
    }
}
