package com.zyhl.yun.api.outer.domain.service;

import com.alibaba.fastjson.JSON;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.enums.RenameModeEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDriveFileUpdateReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.resp.OwnerDriveResponse;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveFileVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.DriveVO;
import com.zyhl.yun.api.outer.application.dto.knowledge.*;
import com.zyhl.yun.api.outer.application.service.knowledge.PersonalKnowledgeService;
import com.zyhl.yun.api.outer.application.service.knowledge.UserKnowledgeFileService;
import com.zyhl.yun.api.outer.application.service.knowledge.UserKnowledgeFileTaskService;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeCountVO;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeResource;
import com.zyhl.yun.api.outer.external.UserDriveExternalService;
import com.zyhl.yun.api.outer.external.YunDiskExternalService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class personalKnowledgeServiceTest {

    @Resource
    PersonalKnowledgeService personalKnowledgeService;
    @Resource
    private UserKnowledgeFileService userKnowledgeFileService;
    @Resource
    private YunDiskExternalService yunDiskExternalService;
    @Resource
    private UserKnowledgeFileTaskService userKnowledgeFileTaskService;
    @Resource
    private UserDriveExternalService userDriveExternalService;

    @Test
    public void urlTest() {
        KnowledgeFileInfoReqDTO dto =  new KnowledgeFileInfoReqDTO();
        dto.setFileId("Ftb7ttKMlrowpXZGox9dNXqQ5rALPp3UM");
        dto.setUserId("1105420961611622455");
        String url = userKnowledgeFileService.getUrlAndCheckAuditStatus(dto);
        log.info("url:{}", url);
    }

    @Test
    public void test() {
        PersonalKnowledgeCountReqDTO dto = new PersonalKnowledgeCountReqDTO();
        dto.setUserId("1092372812309774639");
        dto.setCountTime("2025-02-20T06:51:27.292+08:00");
        PersonalKnowledgeCountVO count = personalKnowledgeService.count(dto);
        log.info("count:{}", count);

        dto.setUserId("1092372812309774639");
        dto.setCountTime("2025-03-21T06:51:27.292+08:00");
        PersonalKnowledgeCountVO count2 = personalKnowledgeService.count(dto);
        log.info("count2:{}", count2);
    }

    @Test
    public void batchGet() {
        KnowledgeFileListBatchReqDTO dto = new KnowledgeFileListBatchReqDTO();
        dto.setSourceChannel("101");
        dto.setUserId("1092372812309774639");
        dto.setResourceIdList(java.util.Arrays.asList("Fn5rsJTOk9_vn4vbgFT1DRI5yM7MJpix7", "FiB_Y475RVZP5vETsS51ETJn3B3WEU44T", "FtIgr09TeZFRvRdY29nVE6aVg9EXu3L_U"));
        List<PersonalKnowledgeResource> resources = userKnowledgeFileService.batchGet(dto);
        log.info("batchGet:{}",BaseResult.success(resources));
    }

    @Test
    public void batchDelete() {
        KnowledgeFileListBatchReqDTO dto = new KnowledgeFileListBatchReqDTO();
        dto.setSourceChannel("101");
        dto.setUserId("1199996031959244754");
        dto.setResourceIdList(java.util.Arrays.asList("FrxHBGKbE11P7NUUZhthDYLmYUjrZRxhT", "Fqr4GOKgzp28t-RMitA1Mirw3lRrXsGhv","FkxSzqVTtL0frlME-f4ZGsoWdIIsrbuBH"));
        List<File> fileList = dto.getResourceIdList().stream()
                .map(resourceId -> {
                    File file = new File();
                    file.setFileId(resourceId);
                    return file;
                }).collect(Collectors.toList());
        KnowledgeFileDeleteReqDTO dto1 = new KnowledgeFileDeleteReqDTO();
        BeanUtils.copyProperties(dto, dto1);
        dto1.setFileList(fileList);
        userKnowledgeFileTaskService.batchDelete(dto1);
    }

    @Test
    public void getDrive() {
        String userId = "1092372812309774639";
        DriveVO drive = yunDiskExternalService.getDrive(userId, 1);
        log.info("drive:{}", drive);
    }

    @Test
    public void getFileInfo() {
        String userId = "1205781715832355346";
        String fileId = "For8wj1ykERDuom8myttBYrRZJVlAp94Q";
        OwnerDriveFileVO driveFile = userDriveExternalService.getFileInfo(userId, fileId);
        log.info("driveFile:{}", JSON.toJSONString(driveFile));
    }

    @Test
    public void updateFileInfo() {
        String userId = "1105420961611622272";
        String fileId = "FrtCfYy1V7ZDbbho_gftMRrzu3Yy51zYQ";
        String name = "代开发票.docx";
        OwnerDriveFileUpdateReqDTO reqDTO = new OwnerDriveFileUpdateReqDTO();
        reqDTO.setUserId(userId);
        reqDTO.setFileId(fileId);
        reqDTO.setName(name);
        reqDTO.setFileRenameMode(RenameModeEnum.FORCE_RENAME.getCode());
        OwnerDriveResponse resp = userDriveExternalService.updateFileInfo(reqDTO);
        log.info("updateFileInfo:{}", JSON.toJSONString(resp));

        OwnerDriveFileVO driveFile = userDriveExternalService.getFileInfo(userId, fileId);
        log.info("driveFile:{}", JSON.toJSONString(driveFile));
    }

    @Test
    public void list() {
        PersonalKnowledgeListReqDTO dto = new PersonalKnowledgeListReqDTO();
        dto.setUserId("1167749398167871592");
        dto.setPageInfo(new PageInfoDTO("1", 2,1));
        PersonalKnowledgeListPageInfoVO result = personalKnowledgeService.list(dto);
        log.info("result1:{}", JSON.toJSONString(result));

        PersonalKnowledgeListReqDTO dto2 = new PersonalKnowledgeListReqDTO();
        dto2.setUserId("1167749398167871592");
        dto2.setPageInfo(new PageInfoDTO("2", 5,1));
        PersonalKnowledgeListPageInfoVO result2 = personalKnowledgeService.list(dto2);
        log.info("result2:{}", JSON.toJSONString(result2));

        PersonalKnowledgeListReqDTO dto3 = new PersonalKnowledgeListReqDTO();
        dto3.setUserId("1167749398167871592");
        dto3.setPageInfo(new PageInfoDTO("0", 5,1));
        PersonalKnowledgeListPageInfoVO result3 = personalKnowledgeService.list(dto3);
        log.info("result3:{}", JSON.toJSONString(result3));
    }
}
