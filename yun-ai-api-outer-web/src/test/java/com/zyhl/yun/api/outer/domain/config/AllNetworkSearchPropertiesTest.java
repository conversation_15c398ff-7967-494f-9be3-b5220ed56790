package com.zyhl.yun.api.outer.domain.config;


import cn.hutool.core.collection.ListUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.config.AllNetworkSearchProperties;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 小站搜索配置-测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class AllNetworkSearchPropertiesTest {

    @Resource
    private AllNetworkSearchProperties properties;

    @Test
    public void test() {
        boolean searchPanta = properties.isSearchPanta();
        if(searchPanta){
            System.out.println(searchPanta);
        }
        System.out.println(JsonUtil.toJson(properties.getSearchPantaParam()));
        Map<String, AllNetworkSearchProperties.FilterCondition> intentionFilterCondition = properties.getIntentionFilterCondition();
        System.out.println(JsonUtil.toJson(intentionFilterCondition));
        List<String> queryTypeStrList = ListUtil.toList("1", "6", "11");
        AllNetworkSearchProperties.FilterCondition filterCondition = intentionFilterCondition.get(DialogueIntentionEnum.SEARCH_DISCOVERY.getCode());
        System.out.println(filterCondition.existQueryType(queryTypeStrList));
        queryTypeStrList = null;
        System.out.println(filterCondition.existQueryType(queryTypeStrList));
        AllNetworkSearchProperties.SearchEntityExtractDTO searchEntityExtract = properties.getSearchEntityExtract();
        System.out.println(JsonUtil.toJson(searchEntityExtract));
    }

}
