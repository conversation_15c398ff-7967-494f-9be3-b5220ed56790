package com.zyhl.yun.api.outer.web.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddContentDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;
import com.zyhl.yun.api.outer.application.vo.FlowTypeResultVO;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.DialogueCommandTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Date;

/**
 * AI报名测试
 */
@SpringBootTest
@Slf4j
class AIChatAddV2WebTest {
    /**
     * 文本
     * 文本+图片
     * 文本+笔记
     * 文本+邮件
     * 指令+文本
     * 指令+图片
     * 指令+笔记
     * 指令+邮件
     * 指令+图片+文本
     * 指令+笔记+文本
     * 指令+邮件+文本
     */


    private final static String domain = "http://127.0.0.1:19027/ai/api/outer";
//    private final static String domain = "http://10.19.16.193:8883/ai-test/ai/api/outer";


    public static void main(String[] args) {
//        commandType2();
//        sceneTag();
//        commandType1();
        stop();
    }

    // 请求头参数
    private static HttpRequest headerParams(String channel, String clientInfo) {
        final HttpRequest request = HttpUtil.createPost(domain + "/assistant/chat/add");
        request.contentType("application/json;charset=UTF-8");
        request.header("Accept", "text/event-stream");
        String token = "Basic bW9iaWxlOjEzNTU3NTg5NjUyOlVQSFVrVHhYfDF8MXwxNzE5NjMxNDc3NTU0fGNKN0R0S0VtcUszM3VLOTNheE1wWFpvVEpYZXAzVDlrQThacWpFX0kwNU9IbTVaVXBQcmZLbTFiT2lIVzcwQkRnODBfNXZDa0RaUVFVOFdLMmNsVldLMFBlLnVLWjBwRjFkeVdWaVJqbnk3YkRnU1ltNUJTM205T1VEdy5ySHdycE8zWld4WHl5dWhra3ZEaEY1d1FLU0RHQnJ3VnpoeWxtMXFFeW12S3VEOC0=";
        request.header("Authorization", token);
        request.header("x-yun-api-version", "v2");
        request.header("x-yun-client-info", StrUtil.emptyToDefault(clientInfo, ""));
        request.header("x-yun-app-channel", StrUtil.emptyToDefault(channel, "102"));

        return request;
    }

    // 请求体参数
    private static AlgorithmChatAddDTO bodyParams(String channel, String dialogue, String prompt) {
        final AlgorithmChatAddContentDTO content = new AlgorithmChatAddContentDTO();
        content.setDialogueType(0);
        content.setTimestamp(new Date());
        content.setSourceChannel(channel);

        content.setResourceType(0);
        content.setResourceId("");

        content.setCommandType(DialogueCommandTypeEnum.ORDINARY.getType());
        content.setCommands("");
        content.setPrompt(prompt);
        content.setDialogue(dialogue);
        content.setSceneTag("");
        content.setExtInfo("");
//        content.setSortInfo();

        // 请求参数
        final AlgorithmChatAddDTO params = new AlgorithmChatAddDTO();
        params.setApplicationType(ApplicationTypeEnum.CHAT.getCode());
        params.setContent(content);
        params.setSessionId("");
//        params.setApplicationId("");

        return params;
    }

    // 文本模型：2-自动指令，不需要扣除权益
    public static void commandType2() {
        // 请求体参数
        String channel = "102";
        String dialogue = "你好";
        String prompt = "";
        AlgorithmChatAddDTO params = bodyParams(channel, dialogue, prompt);
        params.getContent().setCommandType(DialogueCommandTypeEnum.AUTO.getType());

        String clientInfo = "|||1|";
        resultHandle(channel, clientInfo, params);
    }

    // 文本模型：1-普通指令，场景
    public static void sceneTag() {
        // 请求体参数
        String channel = "102";
        String dialogue = "你好";
        String prompt = "";
        AlgorithmChatAddDTO params = bodyParams(channel, dialogue, prompt);
        params.getContent().setSceneTag("邮箱APP-写信AI");

        String clientInfo = "";
        resultHandle(channel, clientInfo, params);
    }

    // 文本模型：1-普通指令，需要扣除权益
    public static void commandType1() {
        // 请求体参数
        String channel = "102";
        String dialogue = "你好";
        String prompt = "";
        AlgorithmChatAddDTO params = bodyParams(channel, dialogue, prompt);

        String clientInfo = "||1|1|";
        resultHandle(channel, clientInfo, params);
    }

    // 流式停止对话
    public static void stop() {
        // 请求体参数
        String channel = "102";
        String dialogue = "以人工智能为主题，写一篇不少于800字的文章，文章体现人工智能的优点和缺点以及对人类发展的影响";
        String prompt = "";
        AlgorithmChatAddDTO params = bodyParams(channel, dialogue, prompt);
        params.setSessionId("1154533141693456387");
        System.out.println("请求参数：" + JSONUtil.toJsonStr(params));

        String clientInfo = "||1|1|";
        // 请求接口
        long s = System.currentTimeMillis();
        System.out.println("开始时间：" + new Date());
        final InputStream result = headerParams(channel, clientInfo).body(JSONUtil.toJsonStr(params)).execute().bodyStream();
        InputStreamReader reader = new InputStreamReader(result);
        BufferedReader br = new BufferedReader(reader);
        System.out.println("结束时间：" + new Date() + "，请求耗时：" + (System.currentTimeMillis() - s) + "ms");

        // 结果处理
        try {
            boolean stop = false;
            System.out.println("问：--------------------------------------------------------");
            System.out.println(params.getContent().getDialogue());
            System.out.println("答：--------------------------------------------------------");
            String line;
            long startTime = System.currentTimeMillis();
            while ((line = br.readLine()) != null) {
//                System.out.println(line);
                if (line.indexOf("data:") != 0) {
                    continue;
                }
                BaseResult resp = JsonUtil.parseObject(line.substring(5), BaseResult.class);
                if (resp == null || !resp.isSuccess()) {
                    System.out.println(resp.getMessage());
                    continue;
                }
                AlgorithmChatAddVO vo = JsonUtil.parseObject(JsonUtil.toJson(resp.getData()), AlgorithmChatAddVO.class);
                if (vo == null) {
                    continue;
                }

                FlowTypeResultVO flow = JsonUtil.parseObject(JsonUtil.toJson(vo.getFlowResult()), FlowTypeResultVO.class);
                if (flow != null) {
                    System.out.println(flow.getOutContent());
                }
            }
            System.out.println("=================================结束=================================");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    // 结果处理
    private static void resultHandle(String channel, String clientInfo, AlgorithmChatAddDTO params) {
        // 请求接口
        final InputStream result = headerParams(channel, clientInfo).body(JSONUtil.toJsonStr(params)).execute().bodyStream();
        InputStreamReader reader = new InputStreamReader(result);
        BufferedReader br = new BufferedReader(reader);

        // 结果处理
        System.out.println("对话结果：" + result);
        try {
            System.out.println("问：--------------------------------------------------------");
            System.out.println(params.getContent().getDialogue());
            System.out.println("答：--------------------------------------------------------");
            String line;
            while ((line = br.readLine()) != null) {
                System.out.println(line);
                if (line.indexOf("data:") != 0) {
                    continue;
                }
                BaseResult resp = JsonUtil.parseObject(line.substring(5), BaseResult.class);
                if (resp == null || !resp.isSuccess()) {
                    System.out.println(resp.getMessage());
                    continue;
                }
                AlgorithmChatAddVO vo = JsonUtil.parseObject(JsonUtil.toJson(resp.getData()), AlgorithmChatAddVO.class);
                if (vo == null) {
                    continue;
                }
                FlowTypeResultVO flow = JsonUtil.parseObject(JsonUtil.toJson(vo.getFlowResult()), FlowTypeResultVO.class);
                if (flow != null) {
                    System.out.println(flow.getOutContent());
                }
            }
            System.out.println("=================================结束=================================");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}