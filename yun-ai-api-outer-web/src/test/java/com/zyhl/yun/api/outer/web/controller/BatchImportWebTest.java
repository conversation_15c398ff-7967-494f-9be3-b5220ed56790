package com.zyhl.yun.api.outer.web.controller;

import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileBatchImportReqDTO;
import com.zyhl.yun.api.outer.application.service.KnowledgeBaseImportService;
import com.zyhl.yun.api.outer.domain.vo.ImportNoteInfoVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
public class BatchImportWebTest {

    @Resource
    private KnowledgeBaseImportService knowledgeBaseImportService;

    @Test
    public void testBatchImport() {
        KnowledgeFileBatchImportReqDTO dto = new KnowledgeFileBatchImportReqDTO();
        dto.setUserId("1105420961611622570");
        dto.setBaseId("1211715771486814331");
        List<ImportNoteInfoVO> noteList = new ArrayList<>();
        ImportNoteInfoVO vo = new ImportNoteInfoVO();
        vo.setNoteId("99325e7c6ab4475db5c925c3e00e0df4");
        noteList.add(vo);
        dto.setNoteList(noteList);
        dto.setResourceType(2);
        knowledgeBaseImportService.batchImport(dto);
    }
}
