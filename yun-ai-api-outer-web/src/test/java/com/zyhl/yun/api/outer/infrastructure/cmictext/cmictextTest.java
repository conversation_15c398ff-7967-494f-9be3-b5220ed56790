package com.zyhl.yun.api.outer.infrastructure.cmictext;

import cn.hutool.core.collection.ListUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.CmicTextClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.DocumentSegmentInfo;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.dto.TextFeatureExtractDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.vo.TextFeatureExtractVO;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmBusinessGroupEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmConfigEntity;
import com.zyhl.yun.api.outer.domainservice.RAGFeatureService;
import com.zyhl.yun.api.outer.repository.AlgorithmBusinessGroupRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 知识库测试
 * @Author: WeiJingKun
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class cmictextTest {

    @Resource
    private AlgorithmBusinessGroupRepository algorithmBusinessGroupRepository;

    @Resource
    private AlgorithmConfigRepository algorithmConfigRepository;

    @Resource
    private RAGFeatureService ragFeatureService;

    @Resource
    private CmicTextClient cmicTextClient;

    /**
     * 文批量向量化接口测试
     * @Author: WeiJingKun
     */
    @Test
    public void ragEmbedTest() {
        TextFeatureExtractDTO dto = new TextFeatureExtractDTO();
        DocumentSegmentInfo documentSegmentInfo = new DocumentSegmentInfo();
        documentSegmentInfo.setFileId("fileId");
        documentSegmentInfo.setSegmentId("0");
        documentSegmentInfo.setText("你好");
        dto.setDocuments(ListUtil.toList(documentSegmentInfo));
        TextFeatureExtractVO vo = cmicTextClient.ragEmbed(dto);
        System.out.println(JsonUtil.toJson(vo));

    }

    /**
     * 对话获取文本向量化数据
     * @Author: WeiJingKun
     */
    @Test
    public void getTextFeatureCommonKnowledgeHandler() {
        TextFeatureExtractVO textFeatureCommonKnowledgeHandler = ragFeatureService.getTextFeatureCommonKnowledgeHandler("common", "你好", "dialogueId");
        System.out.println(JsonUtil.toJson(textFeatureCommonKnowledgeHandler));
    }


    @Test
    public void businessGroup() {
        AlgorithmBusinessGroupEntity businessGroup = algorithmBusinessGroupRepository.queryByAlgorithmGroupCode(4);
        System.out.println(JsonUtil.toJson(businessGroup));
    }

    @Test
    public void config() {
        AlgorithmConfigEntity config = algorithmConfigRepository.queryByAlgorithmId(5L);
        System.out.println(JsonUtil.toJson(config));
    }

}
