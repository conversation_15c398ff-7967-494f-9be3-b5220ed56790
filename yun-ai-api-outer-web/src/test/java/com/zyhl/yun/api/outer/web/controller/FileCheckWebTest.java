package com.zyhl.yun.api.outer.web.controller;

import com.zyhl.hcy.yun.ai.common.platform.third.client.check.vo.CheckResultVO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileBatchImportReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.PersonalKnowledgeUpdateReqDTO;
import com.zyhl.yun.api.outer.application.service.KnowledgeBaseImportService;
import com.zyhl.yun.api.outer.application.service.knowledge.impl.PersonalKnowledgeServiceImpl;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.vo.ImportMailInfoVO;
import com.zyhl.yun.api.outer.domain.vo.ImportNoteInfoVO;
import com.zyhl.yun.api.outer.domainservice.CheckSystemDomainService;
import com.zyhl.yun.api.outer.enums.knowledge.FileTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeStatusEnum;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
public class FileCheckWebTest {

    @Resource
    private KnowledgeBaseImportService knowledgeBaseImportService;

    @Resource
    private CheckSystemDomainService checkSystemDomainService;

    @Resource
    private PersonalKnowledgeServiceImpl personalKnowledgeServiceImpl;

    @Test
    @Transactional
    public void testNoteFileCheck() {

        KnowledgeFileBatchImportReqDTO dto = new KnowledgeFileBatchImportReqDTO();
        dto.setUserId("1105420961611622570");
        dto.setBaseId("1212124396823036006");
        List<ImportNoteInfoVO> noteList = new ArrayList<>();
        ImportNoteInfoVO vo = new ImportNoteInfoVO();
        vo.setNoteId("ceshishemin1");
        vo.setTitle("习近平、开发票");
        noteList.add(vo);
        ImportNoteInfoVO vo1 = new ImportNoteInfoVO();
        vo1.setNoteId("ceshibushemin1");
        vo1.setTitle("测试");
        noteList.add(vo1);
        dto.setNoteList(noteList);
        dto.setResourceType(2);
        knowledgeBaseImportService.batchImport(dto);

    }

    @Test
    @Transactional
    public void testMailFileCheck() {

        KnowledgeFileBatchImportReqDTO dto = new KnowledgeFileBatchImportReqDTO();
        dto.setUserId("1105420961611622570");
        dto.setBaseId("1212124396823036006");
        List<ImportMailInfoVO> mailList = new ArrayList<>();
        ImportMailInfoVO vo = new ImportMailInfoVO();
        vo.setMailId("ceshishemin1");
        vo.setTitle("习近平、开发票");
        mailList.add(vo);
        ImportMailInfoVO vo1 = new ImportMailInfoVO();
        vo1.setMailId("ceshibushemin1");
        vo1.setTitle("测试");
        mailList.add(vo1);
        dto.setMailList(mailList);
        dto.setResourceType(1);
        knowledgeBaseImportService.batchImport(dto);

    }

    @Test
    @Transactional
    public void testPersonalFileCheck() {

        KnowledgeFileBatchImportReqDTO dto = new KnowledgeFileBatchImportReqDTO();
        dto.setUserId("1105420961611622570");
        dto.setBaseId("1212124396823036006");
        List<File> fileList = new ArrayList<>();
        File vo = new File();
        vo.setFileId("FnOoLCGNs03W6dT5NgSRIwI71NJDTbgj1");
        vo.setName("习近平、开发票");
        vo.setType(FileTypeEnum.FILE.getYunDiskFileType());
        fileList.add(vo);
        File vo1 = new File();
        vo1.setFileId("FkEAbLABqJvB3-re_LGREyIJfJLSwaP1w");
        vo1.setName("测试");
        vo1.setType(FileTypeEnum.FILE.getYunDiskFileType());
        fileList.add(vo1);
        dto.setFileList(fileList);
        dto.setResourceType(0);
        knowledgeBaseImportService.batchImport(dto);

    }

    @Test
    @Transactional
    public void test() {
        CheckResultVO resultVo = checkSystemDomainService.checkPlatformAndLocal(1l, "1105420961611622570", "开发票");
        System.out.println(resultVo);
    }

    @Test
    @Transactional
    public void testAsyncFileCheck1() {
        PersonalKnowledgeUpdateReqDTO dto = new PersonalKnowledgeUpdateReqDTO();
        UserKnowledgeEntity entity = new UserKnowledgeEntity();
        // 改为私密知识库
        dto.setOpenLevel(KnowledgeStatusEnum.PRIVATE.getStatus());
        personalKnowledgeServiceImpl.fileCheck(dto, entity);
    }

    @Test
    @Transactional
    public void testAsyncFileCheck2() {
        PersonalKnowledgeUpdateReqDTO dto = new PersonalKnowledgeUpdateReqDTO();
        UserKnowledgeEntity entity = new UserKnowledgeEntity();
        // 改为公开知识库，无附件
        dto.setOpenLevel(KnowledgeStatusEnum.OPEN.getStatus());
        entity.setId(1212412327135749626L);
        personalKnowledgeServiceImpl.fileCheck(dto, entity);
    }

    @Test
    @Transactional
    public void testAsyncFileCheck3() {
        PersonalKnowledgeUpdateReqDTO dto = new PersonalKnowledgeUpdateReqDTO();
        UserKnowledgeEntity entity = new UserKnowledgeEntity();
        // 改为公开知识库，有附件
        dto.setOpenLevel(KnowledgeStatusEnum.OPEN.getStatus());
        entity.setId(1215810161664598134L);
        personalKnowledgeServiceImpl.fileCheck(dto, entity);
    }

}
