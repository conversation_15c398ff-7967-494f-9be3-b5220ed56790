package com.zyhl.yun.api.outer.domain.service;

import com.zyhl.yun.api.outer.domainservice.UserTypeDomainService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collections;

import javax.annotation.Resource;

@SpringBootTest(classes = com.zyhl.yun.api.outer.Application.class)
@RunWith(SpringRunner.class)
@Slf4j
public class UserTypeDomainServiceTest {

	@Resource
	private UserTypeDomainService userTypeDomainService;

	@Test
	public void getUserType() {
		String[] phones = new String[] { "13002009682", "14763689047", "18002239030", "13450395246", "18028041987",
				"17620199811", "13501525064", "15815532205", "15013271046", "13602420417", "19167780231", "13557589652",
				"19854960127", "17846876519" };
		for (String phone : phones) {
			log.info(phone + "---"
					+ userTypeDomainService.getUserType("1", phone, Collections.singletonList("RHR070")));
		}
	}

}
