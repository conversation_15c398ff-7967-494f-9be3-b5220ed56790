package com.zyhl.yun.api.outer.es;

import com.zyhl.hcy.yun.ai.common.base.es.domain.datahelper.repository.EsPanTaResourceRepository;
import com.zyhl.hcy.yun.ai.common.base.es.dto.PanTaResourceOuterSearchDTO;
import com.zyhl.hcy.yun.ai.common.base.es.entity.EsPanTaResourceEntity;
import com.zyhl.yun.api.outer.config.AllNetworkSearchProperties;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 小站es-测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class EsPanTaResourceRepositoryTest {

    @Resource
    private AllNetworkSearchProperties allNetworkSearchProperties;

    @Resource
    private EsPanTaResourceRepository esPanTaResourceRepository;

    /**
     * 搜索小站资源
     * @Author: WeiJingKun
     */
    @Test
    public void searchPanTaResource() {
        List<String> metaDataList = new ArrayList<>();
        metaDataList.add("侏罗纪公园");
        PanTaResourceOuterSearchDTO searchDTO = createPanTaResourceOuterSearchDTO(metaDataList);
        List<EsPanTaResourceEntity> entityList = esPanTaResourceRepository.searchPanTaResource(searchDTO);
        System.out.println(entityList);
    }

    @NotNull
    private PanTaResourceOuterSearchDTO createPanTaResourceOuterSearchDTO(List<String> metaDataList) {
        AllNetworkSearchProperties.SearchPantaParam searchPantaParam = allNetworkSearchProperties.getSearchPantaParam();
        PanTaResourceOuterSearchDTO searchDTO = new PanTaResourceOuterSearchDTO();
        searchDTO.setMetaDataList(metaDataList);
        searchDTO.setReleaseYear(searchPantaParam.getReleaseYear());
        searchDTO.setIsAdult(searchPantaParam.getAdult());
        searchDTO.setIsDomestic(searchPantaParam.getDomestic());
        searchDTO.setPriority(searchPantaParam.getPriorityList());
        searchDTO.setSearchResourceTagRange(searchPantaParam.getBusinessResourceTypeList());
        return searchDTO;
    }

    /**
     * 搜索小站资源
     * @Author: WeiJingKun
     */
    @Test
    public void randomGetVideoSearchPrecaution() {
        System.out.println(allNetworkSearchProperties.randomGetVideoSearchPrecaution());
    }

}
