package com.zyhl.yun.api.outer.rag.service;

import cn.hutool.core.util.IdUtil;
import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.hcy.yun.ai.common.rag.client.KeywordClient;
import com.zyhl.yun.api.outer.config.KnowledgeDialogueProperties;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.slf4j.MDC;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class QueryKeywordServiceImplTest {

    @Resource
    private KeywordClient keywordClient;

    @Resource
    private KnowledgeDialogueProperties knowledgeDialogueProperties;


    @Test
    public void keywordExtractor() {
        String query1 = "吴婉霓来自哪各学校";
        String query2 = "她喜欢打羽毛球不";
        String query3 = "她在哪个学校毕业";
        String query4 = "她之前在哪里工作";
        List<String> list = new ArrayList<>();
        list.add(query1);
        list.add(query2);
        list.add(query3);
        list.add(query4);
        MDC.put(LogConstants.TRACE_ID, IdUtil.simpleUUID());
        List<List<String>> result = keywordClient.keywordExtract(list, knowledgeDialogueProperties.getKeywordConfig());
        log.info(" result:{}", result);
    }
}
