package com.zyhl.yun.api.outer;


import com.zyhl.hcy.yun.ai.common.base.es.domain.datahelper.repository.EsAlbumRepository;
import com.zyhl.hcy.yun.ai.common.base.es.entity.ElasticSearchEntity;
import com.zyhl.hcy.yun.ai.common.base.es.entity.ImageVectorDetectionEntity;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest()
@Slf4j
@RunWith(SpringRunner.class)
public class ImageEsTest {


    @Resource
    private EsAlbumRepository esAlbumRepository;

    @Test
    public void test()  {

        ImageVectorDetectionEntity elasticSearchEntity = new ImageVectorDetectionEntity();
        elasticSearchEntity.setUserId("1116893950985552198");
        List<String> fileId = new ArrayList<>();
        fileId.add("FvgzQS5Lw4Dp_AjrR8-dCiawJuc8pcju6");
        elasticSearchEntity.setFileIds(fileId);
        List<ElasticSearchEntity> elasticSearchEntities = esAlbumRepository.searchByFiles(elasticSearchEntity);
        System.out.println(elasticSearchEntities);


    }
}
