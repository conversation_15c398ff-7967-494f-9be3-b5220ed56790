package com.zyhl.yun.api.outer.external.service;

import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.domain.dto.UserInfoDTO;
import com.zyhl.yun.api.outer.external.client.resp.UserDomainRspDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 描述：用户域接口测试
 *
 * <AUTHOR> zhumaoxian  2025/2/19 11:09
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class UserServiceTest {

    @Resource
    private UserEtnService userEtnService;

    @Test
    public void userInfo() {
        UserInfoDTO dto = userEtnService.getUserInfoData(1105420961611622272L);
        System.out.println("用户信息：" + JsonUtil.toJson(dto));
    }

    @Test
    public void userStatus() {
        UserDomainRspDTO dto = userEtnService.getUserInfo(1105420961611622272L);
        System.out.println("用户信息：" + JsonUtil.toJson(dto));
    }
}
