package com.zyhl.yun.api.outer.web.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.event.TextModelStreamEventListener;
import com.zyhl.hcy.yun.ai.common.model.api.client.vlmodel.ExternalVlModelClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.vo.TextModelBaseVo;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageVlDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelVlReqDTO;

import lombok.extern.slf4j.Slf4j;

/**
 * 火山视觉大模型单元测试
 */
@SpringBootTest
@Slf4j
class AIChatVlHuoShanTest {

	@Resource
	private ExternalVlModelClient externalVlModelClient;

	/**
	 * 流式调用
	 */
	@Test
	public void getTest() throws InterruptedException {
		String dialogueId = "1153986188345507846";
		String userId = "1114312224667476010";
		String sessionId = "1154207860197204223";
		TextModelVlReqDTO reqDTO = new TextModelVlReqDTO();

		reqDTO.setTaskId(dialogueId);
		reqDTO.setUserId(userId);
		reqDTO.setSessionId(sessionId);
		// 设置是否强制联网搜索
		reqDTO.setEnableForceNetworkSearch(false);
		reqDTO.setModelValue("vlm_huoshan_1_5_vision_pro_32k");
		List<TextModelMessageVlDTO> allMsg = new ArrayList<>();

		TextModelMessageVlDTO firstMsg = new TextModelMessageVlDTO();
		firstMsg.setRole("user");
		firstMsg.setContent(new ArrayList<TextModelMessageVlDTO.VlContent>() {
			{
				add(TextModelMessageVlDTO.VlContent.builder().type("text").text("图片讲的是什么东西").build());
				add(TextModelMessageVlDTO.VlContent.builder().type("image_url")
						.imageUrlObject(TextModelMessageVlDTO.VlcontentImageUrl.builder().url(
								"https://gmp-public-tos-staging.tos-cn-beijing.volces.com/2100420468/df186539e88b70fa52679f8be31bd9f2")
								.build())
						.build());
			}
		});
		allMsg.add(firstMsg);
		reqDTO.setMessageVlDtoList(allMsg);

		// 2. 创建事件监听器收集流式结果
		StringBuilder streamResult = new StringBuilder();
		CountDownLatch latch = new CountDownLatch(1);
		TextModelStreamEventListener listener = new TextModelStreamEventListener() {
			@Override
			public boolean onEvent(TextModelBaseVo response) {
				System.out.println("当前输出结果片段：" + response);
				if (response.getText() != null) {
					streamResult.append(response.getText());
				}
				// 结束
				if (response.getIsLast()) {
					latch.countDown();
				}
				return true;
			}
		};
		// 3. 执行流式调用
		// externalVlModelClient.streamCompletions(reqDTO, listener);
		// 等待30秒
		// latch.await(30, TimeUnit.SECONDS);
		// 4. 验证结果
		// System.out.println("流式输出结果：" + streamResult.toString());

		// 5. 执行非流式调用
		externalVlModelClient.completions(reqDTO);

	}

}