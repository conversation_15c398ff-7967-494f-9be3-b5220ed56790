package com.zyhl.yun.api.outer.web.controller;

import java.io.IOException;
import java.util.HashMap;

import org.jetbrains.annotations.Nullable;

import com.zyhl.hcy.yun.ai.common.model.api.utils.TextModelUtil;
import com.zyhl.hcy.yun.ai.common.model.api.utils.auth.VolcAuthUtils;

import cn.hutool.http.ContentType;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;

/**
 * 火山视觉大模型测试类
 */
public class AIChatVlHuoShanApiTest {

	public static void main(String[] args) {

		String body = "{\"messages\":[{\"content\":[{\"image_url\":{\"url\":\"https://gmp-public-tos-staging.tos-cn-beijing.volces.com/2100420468/df186539e88b70fa52679f8be31bd9f2\"},\"type\":\"image_url\"},{\"text\":\"你是一个专业的多模态视觉分析助手，任务是结合图片内容回答用户问题，遵循以下标准：\\\\n1.判断用户需求类型，如视觉描述、文字文本提取、内容问答、题目解答、表格分析等。\\\\n2.关注图片关键特征与用户提问重点，针对重点，分析视觉细节，比如物品的纹理、颜色深浅变化、人物的表情动作、生物的特殊标记等。\\\\n3.若用户需求为视觉描述类（图片讲了什么，图片中有什么，描述这张图片等），总结概括视觉内容输出。\\\\n4.当用户需求为提取文本/字时，要尽可能准确地识别图片中的文字内容并清晰输出。\\\\n5.当用户需求为题目解答时，结合图片信息和相关知识进行逻辑推理和计算，并分步给出详细的解题步骤和答案。\\\\n6.若涉及表格数据分析，关注表格结构（行列、单元格数据类型、表头等），提取表格内容，解析内容并归纳数据趋势，进行统计计算（总和、均值、百分比等）。可基于用户问题查找特定数据（如某行某列的值、最大/最小值、异常值），执行计算（均值、总和、差值等），分析趋势（数据变化、对比分析），并识别异常值或规律（数据分布异常、缺失值等）。\\\\n7.若涉及图表（柱状图、折线图）等分析，在阅读图表时，⾸先关注图表的标题、坐标轴和坐标单位，然后根据图表中不同⾏、列的内容与坐标，⽣成对应的结论。\\\\n8.如有行动建议、分步骤指导或注意事项，清楚罗列出来。\\\\n9.若问题与图片无关，需说明询问的问题与图片内容不相关或在图片中不存在。\\\\n10.若图片模糊或角度问题导致关键特征不清，则说明由于图片模糊/角度问题，关键特征缺失，无法详细作答。\\\\n11.若问题涉及专业资质要求的领域（比如医疗、法律等），提醒用户咨询专业的 [相关专业人员，如医生、律师等] 获得更好的指导。\\\\n12.回答时，先直接给用户明确的肯定或否定答案。然后，结合专业分析和知识库推理详细分析与解读。如果有行动建议、分步骤指导或注意事项，清楚罗列出来。\\\\n13.结尾如有必要可提供追问引导：如“您是否需要更详细的______信息？”（补充图片细节/同类案例/延伸知识）。\\\\n14.输出标准格式（以食物热量查询为例）：\\\\n这个食物的总热量约为 330kcal。\\\\n该食物包含约 200g 鸡胸肉、100g 西兰花和 50g 米饭，蛋白质含量占比 65%（数据来源于USDA食品数据库)，建议搭配绿叶蔬菜增加膳食纤维，运动后 30 分钟内食用最佳。\\\\n用户输入的问题为：\",\"type\":\"text\"}],\"role\":\"user\"}],\"model\":\"Doubao-1.5-vision-pro-32k-250328\"}";
		String authorization = VolcAuthUtils.sign("5pS2VfCBKmU", "aB3_cD4eF5_gH6i7_0314_v1", 300, "POST",
				"/gmp/openapi/v1/aigc/chat_complement_stream/**********", new HashMap<>(), body);
		String url = "https://gmp-saas-api.console.volcengine.com/gmp/openapi/v1/aigc/chat_complement_stream/**********";
		String mainAccountId = "**********";
		Headers headers = new Headers.Builder().add("Content-Type", "application/json")
				.add(TextModelUtil.HEADER_AUTHORIZATION, authorization).add("mainAccountId", mainAccountId)
				.add("Connection", "keep-alive").build();
		RequestBody requestBody = RequestBody.create(body, MediaType.parse(ContentType.JSON.getValue()));

		Request request = (new Request.Builder()).url(url).headers(headers).post(requestBody).build();

		EventSource.Factory factory = EventSources.createFactory(new OkHttpClient());
		final StringBuffer streamResult = new StringBuffer();
		EventSourceListener listener = new EventSourceListener() {
			@Override
			public void onEvent(EventSource eventSource, String id, String type, String data) {
				System.out.println("onEvent id=" + id + ", data=" + data);
			}

			@Override
			public void onFailure(EventSource eventSource, Throwable t, Response response) {
				int code = 0;
				String body = null;
				Throwable th = t;
				if (response != null) {
					code = response.code();
					try {
						body = response.body() == null ? "" : response.body().string();
					} catch (IOException e) {
						th = e;
					}
				}

				onFailure(th, code, body);
				eventSource.cancel();
			}

			@Override
			public void onOpen(EventSource eventSource, Response response) {
				onOpen();
			}

			@Override
			public void onClosed(EventSource eventSource) {
				onClosed();
				eventSource.cancel();
			}

			/**
			 * event失败消息回调函数
			 *
			 * @param t    错误消息栈
			 * @param code 错误代码
			 * @param body 错误内容
			 */
			public void onFailure(@Nullable Throwable t, int code, String body) {
				System.out.println("onFailure code=" + code + ", body=" + body);
			}

			public void onOpen() {
				System.out.println("onOpen");
			}

			public void onClosed() {
				System.out.println("onClosed");
			}
		};
		factory.newEventSource(request, listener);
	}

}