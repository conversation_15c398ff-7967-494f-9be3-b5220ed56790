package com.zyhl.yun.api.outer;

import com.alibaba.fastjson.JSON;
import com.zyhl.yun.api.outer.external.service.UserEtnService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@SpringBootTest()
@Slf4j
@RunWith(SpringRunner.class)
public class UserDomainTest {

    @Resource
    private UserEtnService userEtnService;

    @Test
    public void test() {
        String token = userEtnService.querySpecToken("Basic cGM6MTg5Mjc5OTQ4MDk6aGZqVmEwbTZ8MXwxfDE3NTI5MDI1NDcwODJ8Zl9fa0lROXI3UVR4bTJ3TUVFdDNBaWxmUmpxcklFSWNLdUptQlhtV1pubVhydFJFTUhWNnY1TFFpY1BHR3I3bC4wRTB5dWFabVRLVDREa0kxWjYzRS4yYzBxZ21IMlZ0SUNrVTdFcXhkVkNzSnRCTFJtVHZNNzZxWmZZVHR3NDlVankyT0c0d1FyTFZfUnJycWVoR0J4dFNyeWI4S0NtYTQ3RVA1VWlDRjJBLQ==");
        System.out.print(token);
    }
}
