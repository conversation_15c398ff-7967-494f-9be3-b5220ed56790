package com.zyhl.yun.api.outer.redis;


import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.datahelper.util.RedisService;
import com.zyhl.yun.api.outer.domain.dto.redis.HistoryDialogInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * redis操作数组测试
 *
 * <AUTHOR>
 * @date 2024/12/19 16:09
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class RedisListTest {

    @Resource
    private RedisService redisService;

    @Test
    public void test1() {
        String redisKey = null;
        try {
            List<HistoryDialogInfoDTO> list = new ArrayList<>();
            list.add(HistoryDialogInfoDTO.builder()
                    .intentionCode("001")
                    .dialogId("AAA")
                    .build());

            list.add(HistoryDialogInfoDTO.builder()
                    .intentionCode("002")
                    .dialogId("BBB")
                    .build());

            list.add(HistoryDialogInfoDTO.builder()
                    .intentionCode("003")
                    .dialogId("CCC")
                    .build());

            list.add(HistoryDialogInfoDTO.builder()
                    .intentionCode("004")
                    .dialogId("DDD")
                    .build());

            redisKey = String.format(RedisConstants.AI_HISTORY_SESSION, "11111");

            for (HistoryDialogInfoDTO historyDialogInfoDTO : list) {
                long count = redisService.cacheListWithMaxLengthAndExpire(redisKey, Collections.singletonList(historyDialogInfoDTO), 5, 300);

                List<HistoryDialogInfoDTO> historyList =  redisService.getCacheList(redisKey);
                log.info("setSessionDialogInfo historyList:{}, count:{}", historyList, count);
            }

        } catch (Exception e) {
            log.error("setSessionDialogInfo redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
    }
}
