package com.zyhl.yun.api.outer.application.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiPersonResourceEntity;
import com.zyhl.yun.api.outer.domainservice.PersonResourceHandleService;
import com.zyhl.yun.api.outer.repository.AlgorithmAiPersonResourceRepository;
import com.zyhl.yun.api.outer.util.PersonResourceExcelParser;
import com.zyhl.yun.api.outer.vo.IntentEntityVO;
import com.zyhl.yun.api.outer.vo.KeyValueVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;

/**
 * 人物关系-测试
 * <AUTHOR>
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class PersonResourceTest {

    @Resource
    private AlgorithmAiPersonResourceRepository algorithmAiPersonResourceRepository;

    @Resource
    private PersonResourceHandleService PersonResourceHandleService;

    /**
     * 资源上传
     */
    @Test
    public void saveTest() {
        File file = new File("D:/彩讯科技/广州/AI/需求资料/person_resource_map.xlsx");
        try {
            List<AlgorithmAiPersonResourceEntity> dataList = PersonResourceExcelParser.parse(file);
            if (dataList != null) {
                boolean result = algorithmAiPersonResourceRepository.batchAdd(dataList);
            }
        }catch (Exception e) {
            log.error("Failed to parse person resource excel file: {}", file.getAbsolutePath(), e);
        }
    }

    @Test
    public void getResourceListTest() {
        List<String> resourceList = PersonResourceHandleService.getResourceList("周星驰", null);
        log.info("----------------------resourceList:{}", resourceList);
    }

    /**
     * personList结构转换
     */
    public static void main(String[] args) {
        /**
         * personList字段原结构，[{"key":"主演","value":["刘德华","周星驰"]},{"key":"导演","value":["刘德华","周星驰"]}]
         * 结构转换为，{"周星驰":["主演","导演"],"刘德华":["主演","导演"]}
         */
        // 构造示例数据
        List<KeyValueVO> personList1 = Arrays.asList(
                new KeyValueVO("主演", Arrays.asList("刘德华", "周星驰")),
                new KeyValueVO("导演", Arrays.asList("刘德华", "周星驰"))
        );
        IntentEntityVO vo1 = new IntentEntityVO();
        vo1.setPersonList(personList1);
        System.out.println("转换前：" + JsonUtil.toJson(personList1));

        List<KeyValueVO> personList2 = Arrays.asList(
                new KeyValueVO("编剧", Arrays.asList("周星驰"))
        );
        IntentEntityVO vo2 = new IntentEntityVO();
        vo2.setPersonList(personList2);
        System.out.println("转换前：" + JsonUtil.toJson(personList2));

        List<IntentEntityVO> intentEntityVOList = Arrays.asList(vo1, vo2);

        // 执行转换
        Map<String, List<String>> transformed = transformPersonList(intentEntityVOList);

        // 输出结果
        System.out.println("转换后的结果：" + JsonUtil.toJson(transformed));
    }

    /**
     * 转换方法：将 List<IntentEntityVO> 中的 personList 合并为 Map<姓名, 角色列表>
     */
    public static Map<String, List<String>> transformPersonList(List<IntentEntityVO> entityList) {

        Map<String, List<String>> expandMap = new HashMap<>();

        for (IntentEntityVO entity : entityList) {
            List<KeyValueVO> personList = entity.getPersonList();
            if (CollUtil.isEmpty(personList)) {
                continue;
            }
            /**
             * personList字段原结构，[{"key": "主演", "value": ["刘德华"]}, {"key": "导演", "value": ["刘德华"]}]
             * 结构转换，{"刘德华": ["主演", "导演"]}
             */
            for (KeyValueVO person : personList) {
                String personKey = person.getKey();
                List<String> personValueList = person.getValue();
                // key和value都不能为空
                if (CharSequenceUtil.isBlank(personKey) || CollUtil.isEmpty(personValueList)) {
                    continue;
                }
                for (String personValue : personValueList) {
                    /**
                     * computeIfAbsent(personValue, k -> new ArrayList<>())
                     * 如果 expandMap 中 没有键等于 personValue，则使用 lambda 表达式 k -> new ArrayList<>() 创建一个新的 ArrayList 并放入 resultMap
                     * 如果已经存在该键，则直接返回对应的 List<String>
                     */
                    expandMap.computeIfAbsent(personValue, k -> new ArrayList<>()).add(personKey);
                }
            }
        }

        return expandMap;
    }

}
