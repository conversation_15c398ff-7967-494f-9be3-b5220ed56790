From 4bb55fce0595edf3e18808e66f59607b2d103050 Mon Sep 17 00:00:00 2001
From: ji<PERSON><PERSON> <<EMAIL>>
Date: Wed, 30 Jul 2025 16:53:33 +0800
Subject: [PATCH] =?UTF-8?q?fix(AiPpt):1.inform=E7=9A=84content=E9=9D=9E?=
 =?UTF-8?q?=E5=BF=85=E5=A1=AB=202.Dialouge=E5=A2=9E=E5=8A=A0version=203.sa?=
 =?UTF-8?q?ve=E6=8E=A5=E5=8F=A3=E4=B9=9F=E8=A6=81=E4=B8=8A=E4=BC=A0?=
 =?UTF-8?q?=E5=B0=81=E9=9D=A2=EF=BC=8C=E5=B9=B6=E4=B8=94=E5=85=BC=E5=AE=B9?=
 =?UTF-8?q?hbase=E6=B2=A1=E6=9C=89=E6=96=87=E4=BB=B6=E7=9A=84=E6=83=85?=
 =?UTF-8?q?=E5=86=B5=EF=BC=8C=E4=B8=8A=E4=BC=A0=E4=B8=8B=E8=BD=BDppt?=
 =?UTF-8?q?=E5=8F=8A=E5=B0=81=E9=9D=A2=E5=81=9A=E6=88=90=E5=BC=82=E6=AD=A5?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 .../dto/AiPptResultInformReqDTO.java          |   3 +-
 .../service/impl/AiPptServiceImpl.java        | 345 ++++++++++++------
 2 files changed, 238 insertions(+), 110 deletions(-)

diff --git a/yun-ai-api-outer-application/src/main/java/com/zyhl/yun/api/outer/application/dto/AiPptResultInformReqDTO.java b/yun-ai-api-outer-application/src/main/java/com/zyhl/yun/api/outer/application/dto/AiPptResultInformReqDTO.java
index 9283303e6..b730f9ad0 100644
--- a/yun-ai-api-outer-application/src/main/java/com/zyhl/yun/api/outer/application/dto/AiPptResultInformReqDTO.java
+++ b/yun-ai-api-outer-application/src/main/java/com/zyhl/yun/api/outer/application/dto/AiPptResultInformReqDTO.java
@@ -50,8 +50,7 @@ public class AiPptResultInformReqDTO extends BaseDTO {
     private String pptName;
 
     /**
-     * PPT大纲
+     * PPT大纲（可选）
      */
-    @NotEmpty(message = "content不能为空")
     private String content;
 }
\ No newline at end of file
diff --git a/yun-ai-api-outer-application/src/main/java/com/zyhl/yun/api/outer/application/service/impl/AiPptServiceImpl.java b/yun-ai-api-outer-application/src/main/java/com/zyhl/yun/api/outer/application/service/impl/AiPptServiceImpl.java
index 37440c743..020f375fd 100644
--- a/yun-ai-api-outer-application/src/main/java/com/zyhl/yun/api/outer/application/service/impl/AiPptServiceImpl.java
+++ b/yun-ai-api-outer-application/src/main/java/com/zyhl/yun/api/outer/application/service/impl/AiPptServiceImpl.java
@@ -4,9 +4,11 @@ import cn.hutool.core.util.ObjectUtil;
 import cn.hutool.core.util.StrUtil;
 import cn.hutool.http.HttpUtil;
 import cn.hutool.json.JSONUtil;
+
 import java.util.concurrent.CompletableFuture;
 import java.util.concurrent.ExecutionException;
 import java.util.concurrent.ExecutorService;
+
 import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
 import com.zyhl.hcy.yun.ai.common.base.enums.AIModuleEnum;
 import com.zyhl.hcy.yun.ai.common.base.enums.SupplierTypeEnum;
@@ -35,7 +37,6 @@ import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
 import com.zyhl.yun.api.outer.application.chatv2.service.YunDiskV2Service;
 import com.zyhl.yun.api.outer.application.dto.YunDiskReqDTO;
 import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
-import cn.hutool.core.util.StrUtil;
 import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.vo.AipptCodeResponseVO;
 import com.zyhl.yun.api.outer.application.dto.AiPptFileSaveReqDTO;
 import com.zyhl.yun.api.outer.application.dto.AiPptResultInformReqDTO;
@@ -129,7 +130,7 @@ public class AiPptServiceImpl implements AiPptService {
                 long pptStartTime = System.currentTimeMillis();
                 log.info("AIPPT-异步任务开始: 下载并上传PPT文件, 线程: {}", Thread.currentThread().getName());
                 try {
-                    String pptFileId = downloadAndUploadPpt(reqDTO);
+                    String pptFileId = downloadAndUploadPpt(reqDTO.getDesignId(), reqDTO.getUserId(), reqDTO.getSourceChannel(), reqDTO.getPptName());
                     long pptCostTime = System.currentTimeMillis() - pptStartTime;
                     log.info("AIPPT-异步任务完成: 下载并上传PPT文件, 耗时: {}ms, pptFileId: {}, 线程: {}",
                             pptCostTime, pptFileId, Thread.currentThread().getName());
@@ -146,7 +147,7 @@ public class AiPptServiceImpl implements AiPptService {
                 long coverStartTime = System.currentTimeMillis();
                 log.info("AIPPT-异步任务开始: 下载并上传封面图片, 线程: {}", Thread.currentThread().getName());
                 try {
-                    String coverFileId = downloadAndUploadCover(reqDTO);
+                    String coverFileId = downloadAndUploadCover(reqDTO.getDesignId(), reqDTO.getUserId(), reqDTO.getPptName());
                     long coverCostTime = System.currentTimeMillis() - coverStartTime;
                     log.info("AIPPT-异步任务完成: 下载并上传封面图片, 耗时: {}ms, coverFileId: {}, 线程: {}",
                             coverCostTime, coverFileId, Thread.currentThread().getName());
@@ -158,13 +159,24 @@ public class AiPptServiceImpl implements AiPptService {
                 }
             }, platformThreadPool);
 
-            // 2.同步更新HBase中的outContent内容
+            // 2.获取HBase数据（只查询一次，供后续两个方法使用）
+            long hbaseQueryStartTime = System.currentTimeMillis();
+            AiTextResultEntity hbaseResult = dataSaveService.getHbaseResult(reqDTO.getUserId(), reqDTO.getContentDialogueId());
+            long hbaseQueryTime = System.currentTimeMillis() - hbaseQueryStartTime;
+            log.info("AIPPT-获取HBase数据耗时: {}ms", hbaseQueryTime);
+
+            // 3.同步更新HBase中的outContent内容（仅当content不为空时）
             long stage1StartTime = System.currentTimeMillis();
-            updateHbaseOutContent(reqDTO.getUserId(), reqDTO.getContentDialogueId(), reqDTO.getContent());
-            long stage1CostTime = System.currentTimeMillis() - stage1StartTime;
-            log.info("AIPPT-阶段1完成: 更新HBase内容, 耗时: {}ms", stage1CostTime);
+            long stage1CostTime = 0;
+            if (StrUtil.isNotBlank(reqDTO.getContent())) {
+                updateHbaseOutContent(reqDTO.getUserId(), reqDTO.getContentDialogueId(), reqDTO.getContent(), hbaseResult);
+                stage1CostTime = System.currentTimeMillis() - stage1StartTime;
+                log.info("AIPPT-阶段1完成: 更新HBase内容, 耗时: {}ms", stage1CostTime);
+            } else {
+                log.info("AIPPT-跳过阶段1: content为空，不更新HBase内容");
+            }
 
-            // 3.等待异步任务完成并获取结果
+            // 4.等待异步任务完成并获取结果
             log.info("AIPPT-开始等待异步文件处理任务完成");
             String coverFileId = coverFuture.get();
             String pptFileId = pptFuture.get();
@@ -172,9 +184,9 @@ public class AiPptServiceImpl implements AiPptService {
             log.info("AIPPT-异步文件处理任务全部完成, 等待耗时: {}ms, pptFileId: {}, coverFileId: {}",
                     waitTime, pptFileId, coverFileId);
 
-            // 4.插入新对话到TiDB和HBase
+            // 5.插入新对话到TiDB和HBase
             long stage4StartTime = System.currentTimeMillis();
-            String newDialogueId = insertNewDialogue(reqDTO, pptFileId, coverFileId);
+            String newDialogueId = insertNewDialogue(reqDTO, pptFileId, coverFileId, hbaseResult);
             long stage4CostTime = System.currentTimeMillis() - stage4StartTime;
             log.info("AIPPT-阶段4完成: 插入新对话, 耗时: {}ms", stage4CostTime);
 
@@ -209,7 +221,8 @@ public class AiPptServiceImpl implements AiPptService {
 
     @Override
     public void fileSave(AiPptFileSaveReqDTO reqDTO) {
-        log.info("AIPPT文件保存, dialogueId: {}, designId: {}, pptName: {}",
+        long totalStartTime = System.currentTimeMillis();
+        log.info("AIPPT文件保存开始, dialogueId: {}, designId: {}, pptName: {}",
                 reqDTO.getDialogueId(), reqDTO.getDesignId(), reqDTO.getPptName());
 
         try {
@@ -217,29 +230,79 @@ public class AiPptServiceImpl implements AiPptService {
             AiTextResultRespParameters respParams = getHbaseRespParameters(reqDTO.getUserId(), reqDTO.getDialogueId());
             log.info("AIPPT-成功获取HBase响应参数, dialogueId: {}", reqDTO.getDialogueId());
 
-            // 从响应参数中获取原文件信息
+            // 2.获取原文件信息（可能为null）
             File originalFile = getOriginalFileFromRespParams(respParams);
-            log.info("AIPPT-获取到原文件信息, fileId: {}, parentFileId: {}",
-                    originalFile.getFileId(), originalFile.getParentFileId());
+            if (originalFile != null) {
+                log.info("AIPPT-获取到原文件信息, fileId: {}, parentFileId: {}",
+                        originalFile.getFileId(), originalFile.getParentFileId());
+            } else {
+                log.info("AIPPT-原文件不存在，将使用新建模式");
+            }
+
+            // 3.启动异步任务
+            long asyncStartTime = System.currentTimeMillis();
+            log.info("AIPPT-开始异步文件处理任务");
+
+            // 异步上传PPT文件（统一处理覆盖/新建）
+            CompletableFuture<String> pptFuture = CompletableFuture.supplyAsync(() -> {
+                long pptStartTime = System.currentTimeMillis();
+                log.info("AIPPT-异步任务开始: 上传PPT文件, 线程: {}", Thread.currentThread().getName());
+                try {
+                    String pptFileId = uploadPptFileUnified(reqDTO, originalFile);
+                    long pptCostTime = System.currentTimeMillis() - pptStartTime;
+                    log.info("AIPPT-异步任务完成: 上传PPT文件, 耗时: {}ms, pptFileId: {}, 线程: {}",
+                            pptCostTime, pptFileId, Thread.currentThread().getName());
+                    return pptFileId;
+                } catch (Exception e) {
+                    log.error("AIPPT-异步任务失败: 上传PPT文件, 耗时: {}ms, 线程: {}, error: {}",
+                            System.currentTimeMillis() - pptStartTime, Thread.currentThread().getName(), e.getMessage(), e);
+                    throw new RuntimeException("PPT文件处理失败: " + e.getMessage(), e);
+                }
+            }, platformThreadPool);
+
+            // 异步下载并上传封面图片
+            CompletableFuture<String> coverFuture = CompletableFuture.supplyAsync(() -> {
+                long coverStartTime = System.currentTimeMillis();
+                log.info("AIPPT-异步任务开始: 下载并上传封面图片, 线程: {}", Thread.currentThread().getName());
+                try {
+                    String coverFileId = downloadAndUploadCover(reqDTO.getDesignId(), reqDTO.getUserId(), reqDTO.getPptName());
+                    long coverCostTime = System.currentTimeMillis() - coverStartTime;
+                    log.info("AIPPT-异步任务完成: 下载并上传封面图片, 耗时: {}ms, coverFileId: {}, 线程: {}",
+                            coverCostTime, coverFileId, Thread.currentThread().getName());
+                    return coverFileId;
+                } catch (Exception e) {
+                    log.error("AIPPT-异步任务失败: 下载并上传封面图片, 耗时: {}ms, 线程: {}, error: {}",
+                            System.currentTimeMillis() - coverStartTime, Thread.currentThread().getName(), e.getMessage(), e);
+                    throw new RuntimeException("封面图片处理失败: " + e.getMessage(), e);
+                }
+            }, platformThreadPool);
 
-            // 2.根据designId下载新的PPT作品
-            String base64Content = downloadPptByDesignId(reqDTO.getDesignId(), reqDTO.getUserId(), reqDTO.getSourceChannel());
-            log.info("AIPPT-成功下载新PPT文件, designId: {}, base64长度: {}",
-                    reqDTO.getDesignId(), base64Content.length());
+            long asyncCostTime = System.currentTimeMillis() - asyncStartTime;
+            log.info("AIPPT-异步任务启动完成, 耗时: {}ms", asyncCostTime);
 
-            // 3.覆盖上传到个人云盘
-            overwriteUploadToYunDisk(reqDTO, originalFile, base64Content);
-            log.info("AIPPT-成功覆盖上传PPT文件, fileId: {}", originalFile.getFileId());
+            // 4.等待异步任务完成并获取结果
+            long waitStartTime = System.currentTimeMillis();
+            String pptFileId = pptFuture.get();
+            String coverFileId = coverFuture.get();
+            long waitCostTime = System.currentTimeMillis() - waitStartTime;
+            log.info("AIPPT-异步任务全部完成, 等待耗时: {}ms, pptFileId: {}, coverFileId: {}",
+                    waitCostTime, pptFileId, coverFileId);
 
-            // 4.更新HBase中的文件信息
-            updateHbaseFileInfo(reqDTO.getUserId(), reqDTO.getDialogueId(), originalFile.getFileId(), respParams);
-            log.info("AIPPT-成功更新HBase文件信息, dialogueId: {}", reqDTO.getDialogueId());
+            // 5.构造AiFunctionResult并更新HBase
+            updateHbaseAiFunctionResult(reqDTO.getUserId(), reqDTO.getDialogueId(), reqDTO,
+                    pptFileId, coverFileId, respParams);
 
-            log.info("AIPPT文件保存成功, dialogueId: {}, designId: {}",
-                    reqDTO.getDialogueId(), reqDTO.getDesignId());
+            long totalCostTime = System.currentTimeMillis() - totalStartTime;
+            log.info("AIPPT文件保存成功, 总耗时: {}ms, dialogueId: {}, designId: {}",
+                    totalCostTime, reqDTO.getDialogueId(), reqDTO.getDesignId());
+        } catch (ExecutionException | InterruptedException e) {
+            long totalCostTime = System.currentTimeMillis() - totalStartTime;
+            log.error("AIPPT文件保存异步任务异常, 总耗时: {}ms, error: {}", totalCostTime, e.getMessage(), e);
+            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR, "AIPPT处理异常: " + e.getMessage());
         } catch (Exception e) {
-            log.error("AIPPT文件保存失败, dialogueId: {}, designId: {}, error: {}",
-                    reqDTO.getDialogueId(), reqDTO.getDesignId(), e.getMessage(), e);
+            long totalCostTime = System.currentTimeMillis() - totalStartTime;
+            log.error("AIPPT文件保存失败, 总耗时: {}ms, dialogueId: {}, designId: {}, error: {}",
+                    totalCostTime, reqDTO.getDialogueId(), reqDTO.getDesignId(), e.getMessage(), e);
             throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                     "AIPPT文件保存失败: " + e.getMessage());
         }
@@ -252,17 +315,12 @@ public class AiPptServiceImpl implements AiPptService {
      * @param userId     用户ID
      * @param dialogueId 对话ID
      * @param newContent 新的内容
+     * @param hbaseResult HBase对话结果实体
      */
-    private void updateHbaseOutContent(String userId, String dialogueId, String newContent) {
+    private void updateHbaseOutContent(String userId, String dialogueId, String newContent, AiTextResultEntity hbaseResult) {
         long startTime = System.currentTimeMillis();
         log.info("AIPPT-开始更新HBase对话结果中的outContent内容, userId: {}, dialogueId: {}", userId, dialogueId);
         try {
-            // 从HBase获取当前的对话结果
-            long queryStartTime = System.currentTimeMillis();
-            AiTextResultEntity hbaseResult = dataSaveService.getHbaseResult(userId, dialogueId);
-            long queryTime = System.currentTimeMillis() - queryStartTime;
-            log.info("AIPPT-HBase查询对话记录耗时: {}ms", queryTime);
-
             if (hbaseResult == null) {
                 log.error("未找到HBase对话结果, userId: {}, dialogueId: {}", userId, dialogueId);
                 throw new YunAiBusinessException(YunAiCommonResultCode.HBASE_ERROR, "未找到HBase对话结果");
@@ -298,8 +356,8 @@ public class AiPptServiceImpl implements AiPptService {
             log.info("AIPPT-HBase更新操作耗时: {}ms", updateTime);
 
             long totalTime = System.currentTimeMillis() - startTime;
-            log.info("AIPPT-成功更新HBase对话结果中的outContent内容, userId: {}, dialogueId: {}, 总耗时: {}ms [查询: {}ms, 解析: {}ms, 更新: {}ms]",
-                    userId, dialogueId, totalTime, queryTime, parseTime, updateTime);
+            log.info("AIPPT-成功更新HBase对话结果中的outContent内容, userId: {}, dialogueId: {}, 总耗时: {}ms [解析: {}ms, 更新: {}ms]",
+                    userId, dialogueId, totalTime, parseTime, updateTime);
         } catch (Exception e) {
             long totalTime = System.currentTimeMillis() - startTime;
             log.error("AIPPT-更新HBase对话结果中的outContent内容失败, 耗时: {}ms, error: {}", totalTime, e.getMessage(), e);
@@ -307,26 +365,29 @@ public class AiPptServiceImpl implements AiPptService {
         }
     }
 
+
     /**
      * 获取PPT封面图片URL并上传到个人云盘
      *
-     * @param reqDTO 请求参数
+     * @param designId 厂商作品ID
+     * @param userId   用户ID
+     * @param pptName  PPT作品名称
      * @return 上传到个人云盘的封面图片文件ID
      */
-    private String downloadAndUploadCover(AiPptResultInformReqDTO reqDTO) {
+    private String downloadAndUploadCover(String designId, String userId, String pptName) {
         long startTime = System.currentTimeMillis();
         log.info("AIPPT-开始获取并上传封面图片, userId: {}, designId: {}",
-                reqDTO.getUserId(), reqDTO.getDesignId());
+                userId, designId);
 
         try {
             // 1. 调用getDesignInfoWithRetry获取封面URL
             long getInfoStartTime = System.currentTimeMillis();
             AipptDesignInfoRequestDTO designInfoRequest = AipptDesignInfoRequestDTO.builder()
-                    .userDesignId(Long.valueOf(reqDTO.getDesignId()))
+                    .userDesignId(Long.valueOf(designId))
                     .build();
 
             AipptDesignInfoResponseVO designInfoResponse = aipptExternalService.getDesignInfoWithRetry(
-                    designInfoRequest, reqDTO.getUserId(), "");
+                    designInfoRequest, userId, "");
             long getInfoTime = System.currentTimeMillis() - getInfoStartTime;
             log.info("AIPPT-获取设计信息耗时: {}ms", getInfoTime);
 
@@ -334,26 +395,25 @@ public class AiPptServiceImpl implements AiPptService {
                     || designInfoResponse.getData() == null
                     || StrUtil.isBlank(designInfoResponse.getData().getCoverUrl())) {
                 log.warn("AIPPT-获取封面URL失败或为空, designId: {}, response: {}",
-                        reqDTO.getDesignId(), designInfoResponse);
+                        designId, designInfoResponse);
                 return null;
             }
 
             String coverUrl = designInfoResponse.getData().getCoverUrl();
-            log.info("AIPPT-成功获取封面URL, designId: {}, coverUrl: {}", reqDTO.getDesignId(), coverUrl);
+            log.info("AIPPT-成功获取封面URL, designId: {}, coverUrl: {}", designId, coverUrl);
 
             // 2. 下载封面图片并转换为base64
             long downloadStartTime = System.currentTimeMillis();
             String base64Content = downloadFileToBase64(coverUrl, "封面图片");
             long downloadTime = System.currentTimeMillis() - downloadStartTime;
             log.info("AIPPT-成功下载封面图片并转换为base64, designId: {}, base64长度: {}, 耗时: {}ms",
-                    reqDTO.getDesignId(), base64Content != null ? base64Content.length() : 0, downloadTime);
+                    designId, base64Content != null ? base64Content.length() : 0, downloadTime);
 
             // 3. 上传到个人云盘的封面路径
             long uploadStartTime = System.currentTimeMillis();
             String path = chatTextToolBusinessConfig.getAiPptGenerate().getPersonalCoverPath();
             String fileSuffix = getImageSuffix(coverUrl);
-            String fileName = reqDTO.getPptName() + "_封面";
-            String userId = reqDTO.getUserId();
+            String fileName = pptName + "_封面";
             Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();
 
             FileUploadVO fileUploadVO = yunDiskExternalService.uploadFileToCustomPath(
@@ -375,7 +435,7 @@ public class AiPptServiceImpl implements AiPptService {
         } catch (Exception e) {
             long totalTime = System.currentTimeMillis() - startTime;
             log.error("AIPPT-获取并上传封面图片失败: userId: {}, designId: {}, 耗时: {}ms, error: {}",
-                    reqDTO.getUserId(), reqDTO.getDesignId(), totalTime, e.getMessage(), e);
+                    userId, designId, totalTime, e.getMessage(), e);
             // 封面图片上传失败不影响主流程，返回null
             return null;
         }
@@ -384,35 +444,37 @@ public class AiPptServiceImpl implements AiPptService {
     /**
      * 根据designId获取PPT下载URL，下载文件并上传到个人云盘
      *
-     * @param reqDTO 请求参数
+     * @param designId      厂商作品ID
+     * @param userId        用户ID
+     * @param sourceChannel 渠道来源
+     * @param pptName       PPT作品名称
      * @return 上传到个人云盘的PPT文件ID
      */
-    private String downloadAndUploadPpt(AiPptResultInformReqDTO reqDTO) {
+    private String downloadAndUploadPpt(String designId, String userId, String sourceChannel, String pptName) {
         long startTime = System.currentTimeMillis();
         log.info("AIPPT-开始下载并上传PPT文件, userId: {}, designId: {}, pptName: {}",
-                reqDTO.getUserId(), reqDTO.getDesignId(), reqDTO.getPptName());
+                userId, designId, pptName);
 
         try {
             // 1. 根据designId获取PPT的下载exportUrl
             long getUrlStartTime = System.currentTimeMillis();
-            String exportUrl = getPptExportUrl(reqDTO.getDesignId(), reqDTO.getUserId(),reqDTO.getSourceChannel());
+            String exportUrl = getPptExportUrl(designId, userId, sourceChannel);
             long getUrlTime = System.currentTimeMillis() - getUrlStartTime;
             log.info("AIPPT-获取到PPT下载URL, designId: {}, exportUrl: {}, 耗时: {}ms",
-                    reqDTO.getDesignId(), exportUrl, getUrlTime);
+                    designId, exportUrl, getUrlTime);
 
             // 2. 根据下载exportUrl下载文件，获取文件的base64编码
             long downloadStartTime = System.currentTimeMillis();
             String base64Content = downloadFileToBase64(exportUrl, "PPT");
             long downloadTime = System.currentTimeMillis() - downloadStartTime;
             log.info("AIPPT-成功下载PPT文件并转换为base64, designId: {}, base64长度: {}, 耗时: {}ms",
-                    reqDTO.getDesignId(), base64Content != null ? base64Content.length() : 0, downloadTime);
+                    designId, base64Content != null ? base64Content.length() : 0, downloadTime);
 
             // 3. 上传到个人云盘
             long uploadStartTime = System.currentTimeMillis();
             String path = chatTextToolBusinessConfig.getAiPptGenerate().getPersonalPath();
             String fileSuffix = "pptx";
-            String fileName = reqDTO.getPptName();
-            String userId = reqDTO.getUserId();
+            String fileName = pptName;
             Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();
             FileUploadVO fileUploadVO = yunDiskExternalService.uploadFileToCustomPath(
                     path,
@@ -433,7 +495,7 @@ public class AiPptServiceImpl implements AiPptService {
         } catch (Exception e) {
             long totalTime = System.currentTimeMillis() - startTime;
             log.error("AIPPT-下载并上传PPT文件失败: userId: {}, designId: {}, 耗时: {}ms, error: {}",
-                    reqDTO.getUserId(), reqDTO.getDesignId(), totalTime, e.getMessage(), e);
+                    userId, designId, totalTime, e.getMessage(), e);
             throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                     "AIPPT-下载并上传PPT文件失败: " + e.getMessage());
         }
@@ -447,7 +509,7 @@ public class AiPptServiceImpl implements AiPptService {
      * @param userId   用户ID
      * @return PPT下载URL
      */
-    private String getPptExportUrl(String designId, String userId,String channel) {
+    private String getPptExportUrl(String designId, String userId, String channel) {
         long startTime = System.currentTimeMillis();
         log.info("AIPPT-开始获取PPT下载URL, designId: {}, userId: {}", designId, userId);
         channel = "";
@@ -534,7 +596,7 @@ public class AiPptServiceImpl implements AiPptService {
     /**
      * 下载文件并转换为base64编码
      *
-     * @param fileUrl 文件URL
+     * @param fileUrl  文件URL
      * @param fileType 文件类型（用于日志）
      * @return 文件的base64编码
      */
@@ -597,16 +659,16 @@ public class AiPptServiceImpl implements AiPptService {
     }
 
 
-
     /**
      * 插入新对话到TiDB和HBase
      *
-     * @param reqDTO       请求参数
-     * @param pptFileId    PPT文件ID
-     * @param coverFileId  封面图片文件ID
+     * @param reqDTO      请求参数
+     * @param pptFileId   PPT文件ID
+     * @param coverFileId 封面图片文件ID
+     * @param hbaseResult HBase对话结果实体
      * @return 新的对话ID
      */
-    private String insertNewDialogue(AiPptResultInformReqDTO reqDTO, String pptFileId, String coverFileId) {
+    private String insertNewDialogue(AiPptResultInformReqDTO reqDTO, String pptFileId, String coverFileId, AiTextResultEntity hbaseResult) {
         long startTime = System.currentTimeMillis();
         log.info("AIPPT-开始插入新对话, userId: {}, contentDialogueId: {}, pptName: {}, pptFileId: {}, coverFileId: {}",
                 reqDTO.getUserId(), reqDTO.getContentDialogueId(), reqDTO.getPptName(), pptFileId, coverFileId);
@@ -624,25 +686,39 @@ public class AiPptServiceImpl implements AiPptService {
             long generateIdTime = System.currentTimeMillis() - generateIdStartTime;
             log.info("AIPPT-生成新的dialogueId: {}, 耗时: {}ms", newDialogueId, generateIdTime);
 
-            // 3. 构造ChatAddHandleDTO
+            // 3. 从HBase结果中转换DialogueInputInfoDTO
+            long convertStartTime = System.currentTimeMillis();
+            DialogueInputInfoDTO originalDialogueInput = null;
+            if (hbaseResult != null && StrUtil.isNotBlank(hbaseResult.getReqParameters())) {
+                try {
+                    originalDialogueInput = JSONUtil.toBean(hbaseResult.getReqParameters(), DialogueInputInfoDTO.class);
+                    log.info("AIPPT-成功转换原始DialogueInputInfoDTO");
+                } catch (Exception e) {
+                    log.warn("AIPPT-转换原始DialogueInputInfoDTO失败, 将使用默认值, error: {}", e.getMessage());
+                }
+            }
+            long convertTime = System.currentTimeMillis() - convertStartTime;
+            log.info("AIPPT-转换DialogueInputInfoDTO耗时: {}ms", convertTime);
+
+            // 4. 构造ChatAddHandleDTO
             long buildDtoStartTime = System.currentTimeMillis();
-            ChatAddHandleDTO handleDTO = buildChatAddHandleDTO(reqDTO, sessionId, newDialogueId);
+            ChatAddHandleDTO handleDTO = buildChatAddHandleDTO(reqDTO, sessionId, newDialogueId, originalDialogueInput);
 
-            // 4. 构造AiTextResultRespParameters
+            // 5. 构造AiTextResultRespParameters
             AiTextResultRespParameters hbaseResp = buildHbaseResponse(reqDTO, pptFileId, coverFileId);
             handleDTO.setHbaseResp(hbaseResp);
             long buildDtoTime = System.currentTimeMillis() - buildDtoStartTime;
             log.info("AIPPT-构造DTO和响应参数耗时: {}ms", buildDtoTime);
 
-            // 5. 调用saveTidbAndHbaseResult保存数据
+            // 6. 调用saveTidbAndHbaseResult保存数据
             long saveStartTime = System.currentTimeMillis();
             saveTidbAndHbaseResult(handleDTO);
             long saveTime = System.currentTimeMillis() - saveStartTime;
             log.info("AIPPT-保存数据耗时: {}ms", saveTime);
 
             long totalTime = System.currentTimeMillis() - startTime;
-            log.info("AIPPT-成功插入新对话, newDialogueId: {}, sessionId: {}, 总耗时: {}ms [获取Session: {}ms, 生成ID: {}ms, 构造DTO: {}ms, 保存数据: {}ms]",
-                    newDialogueId, sessionId, totalTime, getSessionTime, generateIdTime, buildDtoTime, saveTime);
+            log.info("AIPPT-成功插入新对话, newDialogueId: {}, sessionId: {}, 总耗时: {}ms [获取Session: {}ms, 生成ID: {}ms, 转换DTO: {}ms, 构造DTO: {}ms, 保存数据: {}ms]",
+                    newDialogueId, sessionId, totalTime, getSessionTime, generateIdTime, convertTime, buildDtoTime, saveTime);
             return String.valueOf(newDialogueId);
         } catch (Exception e) {
             long totalTime = System.currentTimeMillis() - startTime;
@@ -687,12 +763,13 @@ public class AiPptServiceImpl implements AiPptService {
     /**
      * 构造ChatAddHandleDTO
      *
-     * @param reqDTO        请求参数
-     * @param sessionId     会话ID
-     * @param newDialogueId 新对话ID
+     * @param reqDTO                请求参数
+     * @param sessionId             会话ID
+     * @param newDialogueId         新对话ID
+     * @param originalDialogueInput 原始对话输入信息（从HBase获取）
      * @return ChatAddHandleDTO
      */
-    private ChatAddHandleDTO buildChatAddHandleDTO(AiPptResultInformReqDTO reqDTO, Long sessionId, Long newDialogueId) {
+    private ChatAddHandleDTO buildChatAddHandleDTO(AiPptResultInformReqDTO reqDTO, Long sessionId, Long newDialogueId, DialogueInputInfoDTO originalDialogueInput) {
         log.info("AIPPT-构造ChatAddHandleDTO, sessionId: {}, newDialogueId: {}", sessionId, newDialogueId);
 
         // 构造ChatAddReqDTO
@@ -718,9 +795,19 @@ public class AiPptServiceImpl implements AiPptService {
         dialogueInput.setEnableForceNetworkSearch(false);
         dialogueInput.setEnableAllNetworkSearch(false);
         dialogueInput.setEnableAiSearch(false);
-        dialogueInput.setExtInfo("{}");
         dialogueInput.setEnableKnowledgeAndNetworkSearch(false);
 
+        // 从原始DialogueInputInfoDTO中提取versionInfo和extInfo
+        if (originalDialogueInput != null) {
+            if (originalDialogueInput.getVersionInfo() != null) {
+                dialogueInput.setVersionInfo(originalDialogueInput.getVersionInfo());
+                log.info("AIPPT-设置versionInfo: {}", originalDialogueInput.getVersionInfo());
+            }
+            if (StrUtil.isNotBlank(originalDialogueInput.getExtInfo())) {
+                dialogueInput.setExtInfo(originalDialogueInput.getExtInfo());
+                log.info("AIPPT-设置extInfo: {}", originalDialogueInput.getExtInfo());
+            }
+        }
         chatAddReqDTO.setDialogueInput(dialogueInput);
 
         // 构造ChatAddHandleDTO
@@ -730,7 +817,7 @@ public class AiPptServiceImpl implements AiPptService {
         handleDTO.setIntentionCode(DialogueIntentionEnum.TEXT_TOOL.getCode());
         handleDTO.setSubIntentionCode(DialogueIntentionSubEnum.AI_GENERATE_PPT.getCode());
         handleDTO.setSaveMessage(false);
-//TODO确定taskId类型        handleDTO.setTaskId(Long.valueOf(reqDTO.getTaskId()));
+        handleDTO.setTaskId(Long.valueOf(reqDTO.getTaskId()));
 
         // 构造AlgorithmChatTidbSaveDTO
         AlgorithmChatTidbSaveDTO tidbSaveDTO = AlgorithmChatTidbSaveDTO.builder()
@@ -747,9 +834,9 @@ public class AiPptServiceImpl implements AiPptService {
     /**
      * 构造HBase响应数据
      *
-     * @param reqDTO       请求参数
-     * @param pptFileId    PPT文件ID
-     * @param coverFileId  封面图片文件ID
+     * @param reqDTO      请求参数
+     * @param pptFileId   PPT文件ID
+     * @param coverFileId 封面图片文件ID
      * @return AiTextResultRespParameters
      */
     private AiTextResultRespParameters buildHbaseResponse(AiPptResultInformReqDTO reqDTO, String pptFileId, String coverFileId) {
@@ -922,7 +1009,7 @@ public class AiPptServiceImpl implements AiPptService {
      * 从响应参数中获取原文件信息
      *
      * @param respParams HBase响应参数
-     * @return 原文件信息
+     * @return 原文件信息，如果不存在则返回null
      */
     private File getOriginalFileFromRespParams(AiTextResultRespParameters respParams) {
         log.info("AIPPT-开始从响应参数中获取原文件信息");
@@ -930,8 +1017,8 @@ public class AiPptServiceImpl implements AiPptService {
             // 获取第一条记录的文件信息
             DialogueFlowResult firstOutput = respParams.getOutputList().get(0);
             if (firstOutput.getAiFunctionResult() == null || firstOutput.getAiFunctionResult().getFile() == null) {
-                throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
-                        "AIPPT-未找到原文件信息");
+                log.info("AIPPT-HBase中File不存在，返回null");
+                return null;
             }
             File originalFile = firstOutput.getAiFunctionResult().getFile();
             log.info("AIPPT-成功获取原文件信息, fileId: {}, parentFileId: {}",
@@ -944,6 +1031,39 @@ public class AiPptServiceImpl implements AiPptService {
         }
     }
 
+    /**
+     * 统一的PPT上传方法，支持覆盖和新建两种模式
+     *
+     * @param reqDTO       请求参数
+     * @param originalFile 原文件信息，如果为null则新建，否则覆盖
+     * @return PPT文件ID
+     */
+    private String uploadPptFileUnified(AiPptFileSaveReqDTO reqDTO, File originalFile) {
+        log.info("AIPPT-开始统一PPT上传, designId: {}, originalFile: {}",
+                reqDTO.getDesignId(), originalFile != null ? originalFile.getFileId() : "null");
+
+        try {
+            if (originalFile != null) {
+                // 覆盖上传模式
+                log.info("AIPPT-使用覆盖上传模式, fileId: {}", originalFile.getFileId());
+                // 1. 下载新的PPT文件
+                String base64Content = downloadPptByDesignId(reqDTO.getDesignId(), reqDTO.getUserId(), reqDTO.getSourceChannel());
+                // 2. 覆盖上传到个人云盘
+                overwriteUploadToYunDisk(reqDTO, originalFile, base64Content);
+                return originalFile.getFileId();
+            } else {
+                // 新建上传模式，直接调用复用的方法
+                log.info("AIPPT-使用新建上传模式");
+                return downloadAndUploadPpt(reqDTO.getDesignId(), reqDTO.getUserId(), reqDTO.getSourceChannel(), reqDTO.getPptName());
+            }
+        } catch (Exception e) {
+            log.error("AIPPT-统一PPT上传失败, designId: {}, error: {}", reqDTO.getDesignId(), e.getMessage(), e);
+            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
+                    "AIPPT-PPT上传失败: " + e.getMessage());
+        }
+    }
+
+
     /**
      * 第二步：根据designId下载新的PPT作品
      *
@@ -957,7 +1077,7 @@ public class AiPptServiceImpl implements AiPptService {
 
         try {
             // 获取PPT下载URL
-            String exportUrl = getPptExportUrl(designId, userId,channel);
+            String exportUrl = getPptExportUrl(designId, userId, channel);
 
             // 下载文件并转换为base64
             String base64Content = downloadFileToBase64(exportUrl, "PPT");
@@ -1017,43 +1137,52 @@ public class AiPptServiceImpl implements AiPptService {
     }
 
     /**
-     * 第四步：更新HBase中的文件信息
+     * 构造AiFunctionResult并更新HBase
      *
-     * @param userId     用户ID
-     * @param dialogueId 对话ID
-     * @param fileId     文件ID
-     * @param respParams HBase响应参数
+     * @param userId      用户ID
+     * @param dialogueId  对话ID
+     * @param reqDTO      请求参数
+     * @param pptFileId   PPT文件ID
+     * @param coverFileId 封面文件ID
+     * @param respParams  HBase响应参数
      */
-    private void updateHbaseFileInfo(String userId, String dialogueId, String fileId, AiTextResultRespParameters respParams) {
-        log.info("AIPPT-开始更新HBase文件信息, userId: {}, dialogueId: {}, fileId: {}",
-                userId, dialogueId, fileId);
+    private void updateHbaseAiFunctionResult(String userId, String dialogueId, AiPptFileSaveReqDTO reqDTO,
+                                             String pptFileId, String coverFileId, AiTextResultRespParameters respParams) {
+        log.info("AIPPT-开始构造AiFunctionResult并更新HBase, userId: {}, dialogueId: {}, pptFileId: {}, coverFileId: {}",
+                userId, dialogueId, pptFileId, coverFileId);
 
         try {
-            // 获取更新后的文件信息
-            YunDiskReqDTO yunDiskReqDTO = new YunDiskReqDTO(fileId);
-            File updatedFile = yunDiskV2Service.getYunDiskContent(yunDiskReqDTO);
-
-            if (updatedFile == null) {
-                throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
-                        "AIPPT-获取更新后的文件信息失败");
-            }
+            // 1. 构造PPT File对象
+            File pptFile = buildFileFromFileId(pptFileId);
+
+            // 2. 构造封面File对象
+            File coverFile  = buildFileFromFileId(coverFileId);
+
+            // 3. 构造AiFunctionResult
+            AiFunctionResult aiFunctionResult = AiFunctionResult.builder()
+                    .code(DialogueIntentionEnum.TEXT_TOOL.getCode())
+                    .designId(reqDTO.getDesignId())
+                    .supplierType(SupplierTypeEnum.AIPPT.getCode())
+                    .progress(100)
+                    .file(pptFile)
+                    .cover(coverFile)
+                    .title(reqDTO.getPptName())
+                    .build();
 
-            // 更新响应参数中的文件信息
+            // 4. 更新响应参数中的AiFunctionResult
             if (respParams.getOutputList() != null && !respParams.getOutputList().isEmpty()) {
                 DialogueFlowResult firstOutput = respParams.getOutputList().get(0);
-                if (firstOutput.getAiFunctionResult() != null) {
-                    firstOutput.getAiFunctionResult().setFile(updatedFile);
-                }
+                firstOutput.setAiFunctionResult(aiFunctionResult);
             }
 
-            // 更新HBase中的响应参数
-            aiTextResultRepository.updateRespParameters(userId, Long.valueOf(dialogueId),JSONUtil.toJsonStr(respParams));
-            log.info("AIPPT-成功更新HBase文件信息, dialogueId: {}, fileId: {}", dialogueId, fileId);
+            // 5. 更新HBase中的响应参数
+            aiTextResultRepository.updateRespParameters(userId, Long.valueOf(dialogueId), JSONUtil.toJsonStr(respParams));
+            log.info("AIPPT-成功构造AiFunctionResult并更新HBase, dialogueId: {}", dialogueId);
         } catch (Exception e) {
-            log.error("AIPPT-更新HBase文件信息失败, userId: {}, dialogueId: {}, error: {}",
+            log.error("AIPPT-构造AiFunctionResult并更新HBase失败, userId: {}, dialogueId: {}, error: {}",
                     userId, dialogueId, e.getMessage(), e);
             throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
-                    "AIPPT-更新HBase文件信息失败: " + e.getMessage());
+                    "AIPPT-更新HBase失败: " + e.getMessage());
         }
     }
 
-- 
2.45.1.windows.1

