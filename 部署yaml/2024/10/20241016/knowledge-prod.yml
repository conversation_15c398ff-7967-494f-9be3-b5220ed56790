#知识库相关配置

#知识库
knowledge:
  # 个人知识库
  personal:
    # 文件大小限制（B）
    size: 104857600
    # 扩展名
    ext-list: [ "docx","xlsx","csv","txt","pptx" ]
    # 标签个数
    label-num: 50
    # 标签名字数
    label-name-len: 6

  # 知识库对话
  dialogue:
    # 指定使用的公共知识库的标识
    knowledge-base-id: "common"

    # 重写开关
    rewrite-switch: false
    # 重写使用的大模型
    rewrite-model-code: "qwen_32b"
    # 重写输入内容系统提示
    rewrite-system-prompt: "你是一个旨在帮助用户更有效检索信息的助手。\n你的主要职责是在用户输入表达不明确的情况下，通过参考#历史对话摘要#和#关键词列表#，对原始问题进行重写。\n你的目标是使问题更加具体和容易被检索，并保持与用户原始意图的一致性\n并且，请不要忽略#原始用户问题#中的内容\n你应该1. 理解背景: 通过阅读历史对话摘要，了解用户之前的对话内容，把握对话的上下文和主题。\n2. 利用关键词: 将关键词融入问题，确保#重写后的问题#包含这些关键词，提高检索的相关性。\n3. 增加细节: 如果用户的问题过于宽泛或模糊，适当添加细节使问题更具体，但不要引入新的概念或信息。\n4. 保持一致性: 确保#重写后的问题#不偏离用户原始的意图或信息需求。\n5. 简洁明了: 保持问题简短而明确，避免冗长或复杂的表述。\n#重写后的问题#只能在#原始用户问题#的基础上增加10-20个字\n#原始用户问题#，#重写后的问题#，#历史对话摘要#，#关键词列表#都不允许出现在#重写后的问题#中\n#历史对话摘要#:{history}\n#关键词列表#:{keywords}"
    # 重写输入内容用户提示
    rewrite-user-prompt: "#原始用户问题#:{query}\n#重写后的问题#:"

    # ES查询相似度(切片)
    es-split-min-score: 0.1
    # ES查询条数(切片)
    es-split-top-n: 10
    # ES查询相似度(问答)
    es-qa-min-score: 0.1
    # ES查询条数(问答)
    es-qa-top-n: 10

    # 重排后最小评分
    rerank-min-score: 0.3
    # 重排返回条数
    rerank-top-n: 3

    # 白名单
    white-list:
      - "13802885259"
      - "13802883435"
      - "13802885171"
      - "18928819103"
      - "18771000041"
      - "13926431390"
      - "13542899545"
      - "15521090847"
      - "13425458504"
      - "19802021524"
      - "13802885451"
      - "18316688152"
      - "15113990046"
      - "13570559600"
      - "19802025024"
      - "19802021105"
      - "19802021150"
      - "19802021522"
      - "19802021782"
      - "19802021799"
      - "13802885450"
      - "19802025559"
      - "13750326098"
      - "15219891695"
      - "18002239030"
      - "15013271046"
      - "13078882662"
      - "13802882416"
      - "18142851953"
      - "13326349056"
      - "13977527302"
      - "13246408473"
      - "13710660941"
      - "13250164151"
      - "13802885271"
      - "17846876519"
      - "13557589652"
      - "15811852975"
      - "19802024110"
      - "13802885236"
      - "15992549826"
      - "15078267817"
      - "15011750751"
      - "17825902628"
      - "15913080189"
      - "18884581208"
      - "18884681208"
      - "18902224594"
      - "15507829621"
      - "13877725304"
      - "16676302621"
      - "13501525064"
      - "13750326098"
      - "18002239030"
      - "15014317558"
      - "19120129031"
      - "19849977090"
      - "13528277450"
      - "19925811004"
      - "15216248520"
      - "18249907653"

    # 对话模型编码
    rag-model-codes: [ "qwen_32b","qwen","blian" ]
    # rag对话系统提示词
    rag-system-prompt: "你是一个知识渊博且经验丰富的助手，能够综合#历史对话#和#相关知识#来生成准确详尽的答案。\n你的核心任务是结合用户的#历史对话#和#相关知识#，针对#用户问题#生成一个全面且有教育意义的答案。\n\n你的目标是1. 提供完整信息，确保答案覆盖#用户问题#的所有方面。\n2. 增强理解，利用#历史对话#和#相关知识#加深用户对主题的理解。\n你应该1. 回顾#历史对话#：分析用户之前的提问和得到的答案，理解用户的背景和需求。2. 融合#相关知识#：从知识库中检索到的信息应与#历史对话#相结合，以提供更深入的见解。3. 教育性回答：答案应当清晰、连贯，使用具体例子和类比帮助解释复杂概念。4. 鼓励探索：鼓励用户进一步探索主题，回答应包含潜在的后续问题线索。\n#答案#应直接回答用户的问题，简洁精炼，且#答案#不宜超过200字\n#历史对话#，#相关知识#，#答案#不应该出现在#答案#中。\n#历史对话#:{history}"
    # rag对话用户输入模板
    rag-user-template: "#用户问题#: {query}\n#相关知识#:{knowledge}\n#答案#:"
