server:
  port: 19027
  servlet:
    context-path: /api/outer

logging:
  level:
    #root: INFO
    com.kaven.mybatisplus.dao: DEBUG
    com.shimain.springboot.mapper: INFO
    #pattern:
    #console: '%p%m%n'


hcy:
  plugin:
    uid: #雪花算法插件生成配置
      snowflake:
        timeBits: 32
        workerBits: 18
        seqBits: 13
        epochStr: 2007-06-05  #不能高于此时间点。此种情况下，固定为19位雪花id
        paddingFactor: 50
  auth-interceptor:
    enable: true
    exclude-path:
  # 拦截校验的路径
  interceptor-path:
    interceptionPath:
      - /intelligent/search
  #用户白名单(配合拦截校验的路径使用)
  user-white:
    userWhiteList:
      - 1079785805058449675
      - 1079785794320933136
      - 1105470804722209016
      - 1101574403945695215
      - 1079785796468425009
      - 1105470703790412094
      - 1039895866800747225
      - 1039842815364654456
      - 1085988717093673575
      - 1039998808577024877
      - 1039942905282721773
      - 1098911393222042477
      - 1079785828680868403
      - 1100491466595254728
      - 1091161670195309960
      - 1039989978124233748
      - 1080451595858182188
      - 1082681066140017755
      - 1088957647597610874
      - 1079785805058449675
      - 1079785794320933136
      - 1079785796468425009
      - 1105470804722209016
      - 1039872828596171949
      - 1093781720506057413
      - 1040008652642097125
      - 1039975220616648009
      - 1040004357674739076
      - 1039805586588119811
      - 1126080153792954403
      - 1085988944727310270
      - 1039748927379573379
      - 1088957628270192456
      - 1090443459321562338


#用户域服务 请修改成现网的配置
user:
  url: http://user-njs-internal.yun.139.com:30080
  path:
  appSecret: pPIl*%n_BshMEeyF
  appSecretId: 1079827436178702406
  appKey: 1079827436178702402

yun:
  neauth:
    provider:
      enable: true
      config:
        url: http://public-njs-internal.yun.139.com:30080/configcenter/neauth/configs
        appKey: 1079827436178702402
        appSecretId: 1079827436178702403
        appSecret: "JxR(5NMU*RYgR#w3"
        algorithmVersion: 1.0

eos:
  client:
    hostname: https://eos-guangzhou-1.cmecloud.cn
    accessKey: D25RTPTV9V0U0BQ64Y3O
    secretKey: YAsYqDAu92WBONouBkiNFOsJ82Zx185K6s3xsHqb
    signingRegion: guangzhou1
    #桶名称
    bucketName: b08-ypcg-oss-003

#上传限制为以下后缀
report:
  upload:
    allowedExtensions: jpg,jpeg,png,heic

nfs:
  #文件实际储存路径
  path: /data/yun-ai/yun-ai-api-outer/imageCaption/
  #nfs 路径校验 初始路径
  basePath: /data/yun-ai
  #转换路径（测试环境不同需要转换）
  aiPath: /data/yun-ai/yun-ai-api-outer/imageCaption/
  connectionTimeout: 1 # EOS url连接超时时间 单位:分钟
  readTimeout: 10 # NFS 读取文件超时时间 单位:分钟

aiManage:
  type: 4

userRoute:
  userRouteExpireHour: 24

templateMatch:
  threshold: 0.6

imageCaption:
  length: 10

intelligentsearch:
  IFLYTEKThreshold: 22.00
  BaiDuThreshold: 28.00
  TencentThreshold: 22.00

#  华为老平台网元鉴权参数
mcloud:
  net-auth:
    channelSrc: 10249400
    name: AISys
    pwd: 'VTB(SrQH1jzd&w5g!KZp'
    key: MODULE46DA041C44
    enabled: true

market:
  content-url: https://caiyun.feixin.10086.cn:7071
  content-path: market/rabactive/ai/invite

thread-pool:
  global:
    core-size: 10
    max-size: 100
    keep-alive-time: 2
    keep-alive-time-unit: "MINUTES"
    rejected-class: "java.util.concurrent.ThreadPoolExecutor$AbortPolicy"

faceSearch:
  maxFileSize: 20

# 文本模型配置
model:
  # 输入限制
  limit:
    qwen: { "length": 6000, "QPS": 10, session-set: true, def-model: false, history-max-length: 20000 }
    xfyun: { "length": 2000, "QPS": 10, session-set: true, def-model: false, history-max-length: 10000 }
    blian: { "length": 30000, "QPS": 10, session-set: true, def-model: true, history-max-length: 30000 }
    huoshan: { "length": 10000, "QPS": 10, session-set: true, def-model: false, history-max-length: 24000 }
    blian_72b_calc: { "length": 10000, "QPS": 10, session-set: false, def-model: false, history-max-length: 24000 }
  # 小天助手使用的模型
  xiao-tian-use: blian
  # 智能调度配置
  intelligent-schedules:
    - { code: "000", channels: [ ], execute-sort: [ "blian","blian_72b_calc","qwen","huoshan" ] }

# 跳转链接配置 注意此配置要放到nacos上
link:
  url:
    ai-eliminate: https://yun.139.com/aiTools/#/picEliminateOffice?enableShare=1&token=#ssoToken#
    intelligent-cutout: https://yun.139.com/aiTools/#/faceMattingOffice?enableShare=1&token=#ssoToken#
    ai-head-sculpture: https://yun.139.com/aiTools/#/aiAvatarOffice?enableShare=1&token=#ssoToken#
    picture-comic-style: https://yun.139.com/aiTools/#/aiComicOffice?enableShare=1&token=#ssoToken#
    old-photos-repair: https://yun.139.com/aiTools/#/picRepairOffice?enableShare=1&token=#ssoToken#
    picture-generate-text: https://yun.139.com/aiTools/#/picToTextOffice?enableShare=1&token=#ssoToken#
    live-photos: https://yun.139.com/aiTools/#/picAnimateOffice?enableShare=1&token=#ssoToken#
    image-quality-restoration: https://yun.139.com/aiTools/#/resolutionRepairOffice?enableShare=1&token=#ssoToken#
    cloud-camera-external: https://caiyun.feixin.10086.cn:7071/portal/clientDL/index.html?linkUrl=mcloud%3A%2F%2FmcMiniProgram%3Fparams%3DeyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLCJuYW1lIjoi5aaZ5LqR55u45py6IiwibWluaVR5cGUiOiIxIn0%3D
    cloud-camera-within: mcloud://mcMiniProgram?params=eyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLAoibmFtZSI6IuWwj+eoi+W6jyIsCiJtaW5pVHlwZSI6IjEiLAoibWluaVBhdGgiOiJwYWdlcy9ob21lL21haW4iLAoibWluaVVybCI6IiIsCiJtaW5pTGlua1R5cGUiOiIxIn0=

# 厂商配置 注意此配置要放到nacos上
intention:
  company:
    picture-comic-style: "0"
    ai-head-sculpture: "4"
    old-photos-repair: "8"
    live-photos: "7"
    picture-generate-text: "4"
    text-generate-picture: "8"
    image-quality-restoration: "8"
  # 个人云保存路径
  yun-path: "/我的应用收藏/AI助手"

common:
  #qps限制组件开启
  qpslimit:
    enabled: true
    #自定义参数
    limit: 600
    timeoutMillis: 1000
    expireTime: 120

hbase:
  client:
    username: cloudhbase
    accessKey: AEF9C7B415AF46F6A81076B63AE982CA
    quorum: hbase-zookeeper-0.hbase-zookeeper-headless.hbase-687acc91-ad9d-491c-aa96-6db773cdb443.svc.cluster.local:31444,hbase-zookeeper-1.hbase-zookeeper-headless.hbase-687acc91-ad9d-491c-aa96-6db773cdb443.svc.cluster.local:31444,hbase-zookeeper-2.hbase-zookeeper-headless.hbase-687acc91-ad9d-491c-aa96-6db773cdb443.svc.cluster.local:31444
    # 表示单次API的超时时间（毫秒）
    operationTimeout: 30000
    # 表示单次RPC请求的超时时间（毫秒）
    rpcTimeout: 10000
    # 重试次数，每次间隔10秒
    retries: 3

yun-ai:
  image-nfs:
    sharePath: /data/yun-ai
    fodderCatalog: fodder

# 对话应用类型信息列表接口中的标签
tabs:
  tabLabel:  [ "助手","情感","重返历史","IP复刻","趣味","萌宠" ]

#流式接口配置
flow-type:
  config:
    auditSize: 15
    timeout: 300000
    reconnectTimeMillis: 5000

# 令牌桶配置
dialogue-bucket:
  # 文本模型配置
  text-model:
    qwen: { period: 1,limit: 3,maxBurstSize: 3,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    xfyun: { period: 1,limit: 1,maxBurstSize: 1,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    blian: { period: 1,limit: 3,maxBurstSize: 3,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    blian_qwen_long: { period: 1,limit: 3,maxBurstSize: 3,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    blian_72b_calc: { period: 1,limit: 3,maxBurstSize: 3,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    xchen: { period: 3,limit: 5,maxBurstSize: 5,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    huoshan: { period: 100,limit: 100,maxBurstSize: 100,timeoutMillis: 1000,scale: 2,expireTime: 120 }

#送审平台开关
check-system:
  # true-需要送审
  open: true

# 干扰库配置
intervention:
  # 匹配最小分数
  min-score: 0.8
  # 符合条件的最小匹配度
  minimum-should-match: '90%'
  # 默认答案版本，每次升级，需要配置默认版本
  default-answer-version: 1
  # 干扰库答案匹配，any-xxxs同时需要匹配
  match-answers:
    - { answer-version: '0', any-channels: [ '400','101','102','202','401' ], any-client-types: [ ], any-version: { clientVersion: '', h5Version: '1.2.0', mode: 'lt'} }
  #渠道独立配置开关
  channel-list: []

# 大模型文件对话配置
text-file-mode:
  # 大模型上传文件数量
  file-num: 3
  # 大模型上传文件大小 150M
  file-size: 157286400
  # 大模型上传文件支持的后缀
  file-suffix-list: [ "txt","doc","docx","pdf","epub","mobi","md" ]

# 授权报名相关配置
ai-register:
  # 文档检索厂商类型
  doc-factory-type: 0
  # 文档检索算法组
  doc-algorithm-group-code: 2
  # 文档向量化厂商类型
  doc-vector-factory-type: 0
  # 文档向量化算法组
  doc-vector-algorithm-group-code: 3

# 搜索文档后缀映射
search-suffix:
  mapping:
    doc: [ "doc","dot","wps","wpt","docx","dotx","docm","dotm" ]
    xls: [ "xls","xlt","et","xlsx","xltx","xlsm","xltm" ]
    ppt: [ "ppt","pptx","pptm","ppsx","ppsm","pps","potx","potm","dpt","dps" ]
    pdf: [ "pdf" ]
    txt: [ "txt" ]

# 邮件AI总结,AI回复,智能纠错 指定模型
mail-ai-prompt:
  modelList:
    - modelCode: "qwen"
      promptKeyList: [ "MAIL_AI_SUMMARY", "MAIL_AI_REPLY","MAIL_INTELLIGENT_ERROR_CORRECTION" ]
      channelList: [ "10102","10103","10104","10105","10106","10108","10109","10110","10112","10120","10114","10130","10116","10140","10118","10150","10160","10170","10101","10107","10161" ]
      enable: true

# 业务条件参数
business-param:
  # 历史对话列表
  chat-content-list:
    # 限制搜索X天内的历史记录
    query-max-days: 90