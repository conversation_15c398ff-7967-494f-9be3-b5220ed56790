server:
  port: 19027
  servlet:
    context-path: /api/outer

logging:
  level:
    #root: INFO
    com.kaven.mybatisplus.dao: DEBUG
    com.shimain.springboot.mapper: INFO
    #pattern:
    #console: '%p%m%n'


hcy:
  plugin:
    uid: #雪花算法插件生成配置
      snowflake:
        timeBits: 32
        workerBits: 18
        seqBits: 13
        epochStr: 2007-06-05  #不能高于此时间点。此种情况下，固定为19位雪花id
        paddingFactor: 50
  auth-interceptor:
    enable: true
    exclude-path:
  # 拦截校验的路径
  interceptor-path:
    interceptionPath:
      - /intelligent/search
  #用户白名单(配合拦截校验的路径使用)
  user-white:
    userWhiteList:
      - 1079785805058449675
      - 1079785794320933136
      - 1105470804722209016
      - 1101574403945695215
      - 1079785796468425009
      - 1105470703790412094
      - 1039895866800747225
      - 1039842815364654456
      - 1085988717093673575
      - 1039998808577024877
      - 1039942905282721773
      - 1098911393222042477
      - 1079785828680868403
      - 1100491466595254728
      - 1091161670195309960
      - 1039989978124233748
      - 1080451595858182188
      - 1082681066140017755
      - 1088957647597610874
      - 1079785805058449675
      - 1079785794320933136
      - 1079785796468425009
      - 1105470804722209016
      - 1039872828596171949
      - 1093781720506057413
      - 1040008652642097125
      - 1039975220616648009
      - 1040004357674739076
      - 1039805586588119811
      - 1126080153792954403
      - 1085988944727310270
      - 1039748927379573379
      - 1088957628270192456
      - 1090443459321562338


#用户域服务 请修改成现网的配置
user:
  url: http://user-njs-internal.yun.139.com:30080
  path:
  appSecret: pPIl*%n_BshMEeyF
  appSecretId: 1079827436178702406
  appKey: 1079827436178702402

yun:
  neauth:
    provider:
      enable: true
      config:
        url: http://public-njs-internal.yun.139.com:30080/configcenter/neauth/configs
        appKey: 1079827436178702402
        appSecretId: 1079827436178702403
        appSecret: "JxR(5NMU*RYgR#w3"
        algorithmVersion: 1.0

eos:
  client:
    hostname: https://eos-guangzhou-1.cmecloud.cn
    accessKey: D25RTPTV9V0U0BQ64Y3O
    secretKey: YAsYqDAu92WBONouBkiNFOsJ82Zx185K6s3xsHqb
    signingRegion: guangzhou1
    #桶名称
    bucketName: b08-ypcg-oss-003

#上传限制为以下后缀
report:
  upload:
    allowedExtensions: jpg,jpeg,png,heic

nfs:
  #文件实际储存路径
  path: /data/yun-ai/yun-ai-api-outer/imageCaption/
  #nfs 路径校验 初始路径
  basePath: /data/yun-ai
  #转换路径（测试环境不同需要转换）
  aiPath: /data/yun-ai/yun-ai-api-outer/imageCaption/
  connectionTimeout: 1 # EOS url连接超时时间 单位:分钟
  readTimeout: 10 # NFS 读取文件超时时间 单位:分钟

aiManage:
  type: 4

userRoute:
  userRouteExpireHour: 24

templateMatch:
  threshold: 0.6

imageCaption:
  length: 10

intelligentsearch:
  IFLYTEKThreshold: 22.00
  BaiDuThreshold: 28.00
  TencentThreshold: 22.00

#  华为老平台网元鉴权参数
mcloud:
  net-auth:
    channelSrc: 10249400
    name: AISys
    pwd: 'VTB(SrQH1jzd&w5g!KZp'
    key: MODULE46DA041C44
    enabled: true

market:
  content-url: https://caiyun.feixin.10086.cn:7071
  content-path: market/rabactive/ai/invite

thread-pool:
  global:
    core-size: 10
    max-size: 100
    keep-alive-time: 2
    keep-alive-time-unit: "MINUTES"
    rejected-class: "java.util.concurrent.ThreadPoolExecutor$AbortPolicy"

faceSearch:
  maxFileSize: 20

# 文本模型配置
model:
  # 输入限制
  limit:
    qwen: { "length": 6000, "QPS": 10, session-set: true, def-model: false, history-max-length: 20000 }
    xfyun: { "length": 2000, "QPS": 10, session-set: true, def-model: false, history-max-length: 10000 }
    blian: { "length": 30000, "QPS": 10, session-set: true, def-model: true, history-max-length: 30000 }
    huoshan: { "length": 10000, "QPS": 10, session-set: true, def-model: false, history-max-length: 24000 }    
    blian_72b_calc: { "length": 10000, "QPS": 10, session-set: false, def-model: false, history-max-length: 24000 }
  # 小天助手使用的模型
  xiao-tian-use: blian
  # 智能调度配置
  intelligent-schedules:
    - { code: "000", channels: [ ], execute-sort: [ "blian","blian_72b_calc","qwen","huoshan" ] }

# 跳转链接配置 注意此配置要放到nacos上
link:
  url:
    ai-eliminate: https://yun.139.com/aiTools/#/picEliminateOffice?enableShare=1&token=#ssoToken#
    intelligent-cutout: https://yun.139.com/aiTools/#/faceMattingOffice?enableShare=1&token=#ssoToken#
    ai-head-sculpture: https://yun.139.com/aiTools/#/aiAvatarOffice?enableShare=1&token=#ssoToken#
    picture-comic-style: https://yun.139.com/aiTools/#/aiComicOffice?enableShare=1&token=#ssoToken#
    old-photos-repair: https://yun.139.com/aiTools/#/picRepairOffice?enableShare=1&token=#ssoToken#
    picture-generate-text: https://yun.139.com/aiTools/#/picToTextOffice?enableShare=1&token=#ssoToken#
    live-photos: https://yun.139.com/aiTools/#/picAnimateOffice?enableShare=1&token=#ssoToken#
    image-quality-restoration: https://yun.139.com/aiTools/#/resolutionRepairOffice?enableShare=1&token=#ssoToken#
    cloud-camera-external: https://caiyun.feixin.10086.cn:7071/portal/clientDL/index.html?linkUrl=mcloud%3A%2F%2FmcMiniProgram%3Fparams%3DeyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLCJuYW1lIjoi5aaZ5LqR55u45py6IiwibWluaVR5cGUiOiIxIn0%3D
    cloud-camera-within: mcloud://mcMiniProgram?params=eyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLAoibmFtZSI6IuWwj+eoi+W6jyIsCiJtaW5pVHlwZSI6IjEiLAoibWluaVBhdGgiOiJwYWdlcy9ob21lL21haW4iLAoibWluaVVybCI6IiIsCiJtaW5pTGlua1R5cGUiOiIxIn0=

# 厂商配置 注意此配置要放到nacos上
intention:
  company:
    picture-comic-style: "0"
    ai-head-sculpture: "4"
    old-photos-repair: "8"
    live-photos: "7"
    picture-generate-text: "4"
    text-generate-picture: "8"
    image-quality-restoration: "8"

common:
  #qps限制组件开启
  qpslimit:
    enabled: true
    #自定义参数
    limit: 600
    timeoutMillis: 1000
    expireTime: 120

hbase:
  client:
    username: cloudhbase
    accessKey: AEF9C7B415AF46F6A81076B63AE982CA
    quorum: hbase-zookeeper-0.hbase-zookeeper-headless.hbase-687acc91-ad9d-491c-aa96-6db773cdb443.svc.cluster.local:31444,hbase-zookeeper-1.hbase-zookeeper-headless.hbase-687acc91-ad9d-491c-aa96-6db773cdb443.svc.cluster.local:31444,hbase-zookeeper-2.hbase-zookeeper-headless.hbase-687acc91-ad9d-491c-aa96-6db773cdb443.svc.cluster.local:31444

yun-ai:
  image-nfs:
    sharePath: /data/yun-ai
    fodderCatalog: fodder

# 来源渠道
source-channels:
  channelList:
    - { channel: 10102, business-type: 'e-mcloud-app', mail: true, innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: false }
    - { channel: 10103, business-type: 'e-139mailold-app', mail: true, innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: false }
    - { channel: 10104, business-type: 'e-139mail-app', mail: true, innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: false }
    - { channel: 10106, business-type: 'e-139mail-app', mail: true, innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: false }
    - { channel: 10108, business-type: 'e-mcloud-app', mail: true, innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: false }
    - { channel: 10109, business-type: 'e-mcloud-app', mail: true, innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: false }
    - { channel: 10110, business-type: 'e-Cloudphone', mail: true, end: 'outer', innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: false }
    - { channel: 10112, business-type: 'e-mcloud-pc', mail: true, innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: false }
    - { channel: 10114, business-type: 'e-ChinaMobileHall', mail: true, end: 'outer', innerBenefitNo: RHR070, outerBenefitNo: RHR069, benefit-switch: false }
    - { channel: 10116, business-type: 'e-Mobileoffice', mail: true, end: 'outer', innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: false }
    - { channel: 10118, business-type: 'e-CloudphoneEE', mail: true, end: 'outer', innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: false }
    - { channel: 10160, business-type: 'e-test', mail: true, end: 'outer', innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: false }
    - { channel: 101, business-type: 'c-mcloud-app', xiao-tian: true, innerBenefitNo: RHR070, outerBenefitNo: RHR079, innerToolBenefitNo: RHR078, outerToolBenefitNo: RHR078, algorithmCodeList: [ '007' ] }
    - { channel: 102, business-type: 'c-mcloud-app', xiao-tian: true, innerBenefitNo: RHR070, outerBenefitNo: RHR079, innerToolBenefitNo: RHR078, outerToolBenefitNo: RHR078, algorithmCodeList: [ '007' ] }
    - { channel: 202, business-type: 'c-139mail-app', xiao-tian: true, innerBenefitNo: RHR070, outerBenefitNo: RHR079, innerToolBenefitNo: RHR078, outerToolBenefitNo: RHR078, algorithmCodeList: [ '007' ]}
    - { channel: 401, business-type: 'c-mcloud-app', xiao-tian: true, innerBenefitNo: RHR070, outerBenefitNo: RHR079, innerToolBenefitNo: RHR078, outerToolBenefitNo: RHR078, algorithmCodeList: [ '007' ] }
    - { channel: 400, business-type: 'c-test', xiao-tian: true, innerBenefitNo: RHR070, outerBenefitNo: RHR079, innerToolBenefitNo: RHR078, outerToolBenefitNo: RHR078, algorithmCodeList: [ '007' ] }

# 对话应用类型信息列表接口中的标签
tabs:
  tabLabel:  [ "助手","情感","重返历史","IP复刻","趣味","萌宠" ]

#会员中心权益项编号
member-center:
  # 权益开关，true-打开
  enabled: true
  # 权益编码
  benefit-no: RHR070
  # 权益编码列表
  benefits:
    - benefit-no: RHR064
      channel: 100102
      algorithm-code-list: []
      enabled: true
    - benefit-no: RHR078
      channel: 100103
      algorithm-code-list: ['006', '007']
      enabled: true
    - benefit-no: RHR065
      channel: 200101
      algorithm-code-list: ['006', '007']
      enabled: true
    - benefit-no: RHR066
      channel: 300101
      algorithm-code-list: ['006', '007']
      enabled: true
    - benefit-no: RHR075
      channel: 300201
      algorithm-code-list: ['006', '007']
      enabled: true
    - benefit-no: RHR067
      channel: 900101
      algorithm-code-list: ['006', '007']
      enabled: true
  # 场景权益配置
  scene-tag-list:
    # 邮箱APP-写信AI
    - scene-tag: 'app_writemail_ai'
      benefit-no: RHR064
      enabled: true

# 客户端类型
client-type:
  list:
    - {code: 1, end: 'inner', remark: 'Android（手机客户端）'}
    - {code: 2, end: 'inner', remark: 'iOS（手机客户端-iPhone）'}
    - {code: 3, end: 'outer', remark: 'H5'}
    - {code: 4, end: 'outer', remark: 'TV（Android-TV客户端）'}
    - {code: 5, end: 'outer', remark: 'Android（手机SDK）'}
    - {code: 6, end: 'outer', remark: 'iOS（手机SDK）'}
    - {code: 7, end: 'outer', remark: 'TV（Android-TV SDK）'}
    - {code: 8, end: 'outer', remark: '微信小程序'}
    - {code: 9, end: 'outer', remark: 'WEB版本'}
    - {code: 10, end: 'outer', remark: '产品合作版本（比如八闽视频、爱国者硬件等）'}
    - {code: 11, end: 'inner', remark: 'PC（WINDOWS）'}
    - {code: 12, end: 'outer', remark: 'iPad'}
    - {code: 13, end: 'inner', remark: 'PC（MAC）'}
    - {code: 14, end: 'outer', remark: 'QQ小程序'}
    - {code: 15, end: 'outer', remark: '营销模块'}
    - {code: 16, end: 'outer', remark: '微信公众号'}
    - {code: 17, end: 'outer', remark: 'Android（手表客户端）'}
    - {code: 18, end: 'outer', remark: '鸿蒙Harmony原子化服务'}
    - {code: 19, end: 'outer', remark: 'Android（车载端）'}
    - {code: 20, end: 'outer', remark: '移动云盘短信小程序'}
    - {code: 21, end: 'inner', remark: '客户端小程序（即卡包小程序）'}
    - {code: 22, end: 'outer', remark: '支付宝小程序'}
    - {code: 23, end: 'outer', remark: '鸿蒙原生应用'}
    - {code: 26, end: 'inner', remark: 'Android（邮箱手机客户端）'}
    - {code: 27, end: 'inner', remark: 'iOS（邮箱手机客户端）'}
    - {code: 28, end: 'inner', remark: 'Android（云邮SDK）'}
    - {code: 29, end: 'inner', remark: 'iOS（云邮SDK）'}
    - {code: 30, end: 'inner', remark: '邮箱WEB版本'}
    - {code: 31, end: 'outer', remark: '邮箱酷版'}

#搜索引导配置
search-guide:
  statement:
    search-image: "请提供搜索图片的关键词，您可以尝试输入：搜索12月在广州的图片"
    search-document: "请提供搜索文件的关键词，您可以尝试输入：搜索设计学习资料"
    search-video: "请提供搜索视频的关键词，您可以尝试输入：搜索新年晚会的视频"
    search-audio: "请提供搜索音频的关键词，您可以尝试输入：搜索英语听力练习的音频"
    search-folder: "请提供搜索文件夹的关键词，您可以尝试输入：搜索AI工具箱文件夹"
    search-note: "请提供搜索笔记的关键词，您可以尝试输入：搜索年度计划笔记"
    comprehensive-search: "请提供搜索文件夹的关键词，您可以尝试输入：搜索英语听力课程的视频和音频文件"
    search-activity: "请提供搜索活动的关键词，您可以尝试输入：搜索云朵中心活动"
    search-function: "请提供搜索功能的关键词，您可以尝试输入：搜索相册备份功能"
    search-discovery: "请提供搜索试卷的关键词，您可以尝试输入：搜索八年级的试题"

#流式接口配置
flow-type:
  config:
    auditSize: 15
    timeout: 30000
    reconnectTimeMillis: 5000

# 令牌桶配置
dialogue-bucket:
  # 文本模型配置
  text-model:
    qwen: { period: 1,limit: 3,maxBurstSize: 3,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    xfyun: { period: 1,limit: 1,maxBurstSize: 1,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    blian: { period: 1,limit: 3,maxBurstSize: 3,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    blian_qwen_long: { period: 1,limit: 3,maxBurstSize: 3,timeoutMillis: 1000,scale: 2,expireTime: 120 }    
    blian_72b_calc: { period: 1,limit: 3,maxBurstSize: 3,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    xchen: { period: 3,limit: 5,maxBurstSize: 5,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    huoshan: { period: 100,limit: 100,maxBurstSize: 100,timeoutMillis: 1000,scale: 2,expireTime: 120 }

#送审平台开关
check-system:
  # true-需要送审
  open: true

# 对话结果引导文案
lead-copy:
  # 创建笔记意图结果标题
  create-note-title: "小天正在为你打开笔记"
  # 引导文案对象列表
  copy-list:
    - instruction: picture-generate-text
      type: 1
      prompt-copy: "图片配文是帮助您快速地为图片添加文字的功能，请选择一张需要图片配文处理的图片："
      button-copy: "选择图片"
      link-url: https://yun.139.com/aiTools/#/picToTextOffice?enableShare=1&token=#ssoToken#
    - instruction: ai-head-sculpture
      type: 1
      prompt-copy: "AI头像可以帮你生成精美的创意头像，请选择一张想制作为AI头像的图片："
      button-copy: "选择图片"
      link-url: https://yun.139.com/aiTools/#/aiAvatarOffice?enableShare=1&token=#ssoToken#
    - instruction: picture-comic-style
      type: 1
      prompt-copy: "AI漫画风帮你把图片一键转化为漫画风格，请选择一张需要进行AI漫画风处理的图片："
      button-copy: "选择图片"
      link-url: https://yun.139.com/aiTools/#/aiComicOffice?enableShare=1&token=#ssoToken#
    - instruction: old-photos-repair
      type: 1
      prompt-copy: "老照片修复可以修复你的图片画质与颜色，请选择一张需要进行修复处理的图片："
      button-copy: "选择图片"
      link-url: https://yun.139.com/aiTools/#/picRepairOffice?enableShare=1&token=#ssoToken#
    - instruction: live-photos
      type: 1
      prompt-copy: "活照片支持将人像图片转化为动画表情，请选择一张需要进行处理的人像图片："
      button-copy: "选择图片"
      link-url: https://yun.139.com/aiTools/#/picAnimateOffice?enableShare=1&token=#ssoToken#
    - instruction: image-quality-restoration
      type: 1
      prompt-copy: "画质修复让你的模糊图片重现清晰细节"
      button-copy: "选择图片"
      link-url: https://yun.139.com/aiTools/#/resolutionRepairOffice?enableShare=1&token=#ssoToken#
    - instruction: ai-eliminate
      type: 2
      prompt-copy: "AI消除可为您智能消除图片内的指定功能"
      button-copy: "去使用"
      link-url: https://yun.139.com/aiTools/#/picEliminateOffice?enableShare=1&token=#ssoToken#
    - instruction: intelligent-cutout
      type: 2
      prompt-copy: "智能抠图可为您智能抠取人像图片，进行背景替换操作"
      button-copy: "去使用"
      link-url: https://yun.139.com/aiTools/#/faceMattingOffice?enableShare=1&token=#ssoToken#
    - instruction: cloud-camera-external
      type: 3
      prompt-copy: "妙云相机帮你制作数字形象，一键生成艺术大片"
      button-copy: "去使用"
      link-url: https://caiyun.feixin.10086.cn:7071/portal/clientDL/index.html?linkUrl=mcloud%3A%2F%2FmcMiniProgram%3Fparams%3DeyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLCJuYW1lIjoi5aaZ5LqR55u45py6IiwibWluaVR5cGUiOiIxIn0%3D
    - instruction: cloud-camera-within
      type: 3
      prompt-copy: "妙云相机帮你制作数字形象，一键生成艺术大片"
      button-copy: "去使用"
      link-url: mcloud://mcMiniProgram?params=eyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLAoibmFtZSI6IuWwj+eoi+W6jyIsCiJtaW5pVHlwZSI6IjEiLAoibWluaVBhdGgiOiJwYWdlcy9ob21lL21haW4iLAoibWluaVVybCI6IiIsCiJtaW5pTGlua1R5cGUiOiIxIn0=
    - instruction: create-note
      type: 4
      prompt-copy: "小天为你找到笔记功能，快去记录吧！"
      button-copy: "去记录"
      link-url:

# 对话结果推荐
dialogue-result-recommend:
  # 意图推荐文本配置列表
  intention-text-list:
    - intention-command: "000"
      copy: ""
  # 意图推荐AI工具配置列表
  intention-list:
    - intention-command: "001"
      copy: "小天猜你想对图片进行处理，推荐您使用图片配文，图片配文是帮助您快速地为图片添加文字的功能"
    - intention-command: "007"
      copy: "小天猜你想对图片进行处理，推荐您使用AI头像，AI头像可以帮您生成精美的创意头像"
    - intention-command: "004"
      copy: "小天猜你想对图片进行处理，推荐您使用AI漫画，AI漫画风帮您把图片一键转化为漫画风格"
    - intention-command: "008"
      copy: "小天猜你想对图片进行处理，推荐您使用老照片修复，老照片修复可以修复您的图片画质与颜色"
    - intention-command: "005"
      copy: "小天猜你想对图片进行处理，推荐您使用活照片，活照片支持将人像图片转化为动画表情"
    - intention-command: "009"
      copy: "小天猜你想对图片进行处理，推荐您使用画质修复，画质修复让您的模糊图片重现清晰细节"
      link-url: https://yun.139.com/aiTools/#/resolutionRepairOffice?enableShare=1&token=#ssoToken#
    - intention-command: "006"
      copy: "小天猜你想对图片进行处理，推荐您使用AI消除，AI消除可为您智能消除图片内的指定功能"
      link-url: https://yun.139.com/aiTools/#/picEliminateOffice?enableShare=1&token=#ssoToken#
    - intention-command: "003"
      copy: "小天猜你想对图片进行处理，推荐您使用智能抠图，智能抠图可为您智能抠取人像图片，进行背景替换操作"
      link-url: https://yun.139.com/aiTools/#/faceMattingOffice?enableShare=1&token=#ssoToken#
  # 对话内容推荐-每个类型推荐的数量
  content-recommend-quantity: 3
  # 对话内容推荐-keyword分隔符
  content-recommend-split: "、"
  # 对话内容推荐配置列表
  content-list:
    - type: 2
      desc: "发现类运营推荐"
      keyword-list:
        - keyword: "影视、电影、大片、影片、影音、院线、动画、喜剧片、悬疑片、警匪片、动作片、恐怖片、科幻片、惊悚片、剧情片、影后、影帝、金鸡奖、奥斯卡、金像奖、百花奖、华表奖"
          priority: 1
        - keyword: "综艺"
          priority: 2
        - keyword: "书籍、小说、阅读、电子书、新书"
          priority: 4
        - keyword: "试卷、真题、高考、中考、小升初、期末"
          priority: 5
        - keyword: "健身、运动、锻炼、养生、健康、形体"
          priority: 6
        - keyword: "职场、面试、求职、毕业"
          priority: 7
    - type: 3
      desc: "推荐圈子类运营推荐"
      keyword-list:
        - keyword: "电视剧、剧集、剧荒、美剧、英剧、日剧、韩剧、泰剧、台剧、华语剧、港剧、国产剧"
          priority: 3

# 干扰库配置
intervention:
  # 匹配最小分数
  min-score: 0.6
  # 符合条件的最小匹配度
  minimum-should-match: '70%'
  # 默认答案版本，每次升级，需要配置默认版本
  default-answer-version: 1
  # 干扰库答案匹配，any-xxxs同时需要匹配
  match-answers: 
    - { answer-version: '0', any-channels: [ '400','101','102','202','401' ], any-client-types: [ ], any-version: { clientVersion: '', h5Version: '1.2.0', mode: 'lt'} }
  #渠道独立配置开关
  channel-list: []

#临时处理的配置（注意每次加配置都应放最后面）          
temp-config:
  #不搜索增强执行的客户端版本列表
  not-search-enhance-client-versions: ['1.0.2']