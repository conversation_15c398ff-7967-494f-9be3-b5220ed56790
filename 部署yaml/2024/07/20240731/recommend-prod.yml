#搜索提示配置
search-prompt:
  # 搜索引导
  guide:
    search-image:
      - "搜索图片时，您可以尝试输入具体的主题，比如：搜索夜景的图片。"
      - "为了让搜索更准确，请描述您想要查看的图片样式，比如：搜索建筑的图片。"
      - "试着用搜索你在某个地点的图片，例如：搜索在广州拍摄的图片。"
      - "您可以尝试这样输入您的搜索内容，比如：搜索2023年12月在广州的图片。"
      - "猜你想搜图片，假如要搜索美食图片，请加上具体的食物名称或类型，比如：搜索蛋糕的图片。"
      - "想要找到某个时间段的图片吗？请输入相应的时间试试，比如：搜索2023年的图片。"
      - "如您想搜索动物图片，请输入动物的名称，比如：搜索小猫的图片。"
      - "为了获得更准确的搜索结果，请您尽可能描述您想要搜索的图片样式，比如：搜索海边度假图片。"
    search-document:
      - "请提供搜索文件的关键词，您可以尝试输入：搜索每周例会会议纪要文档。"
      - "请明确您的搜索目标，例如：搜索财务报表相关的Excel文件。"
      - "搜索文件时，您可以尝试输入文件名或文件类型，比如：搜索pdf格式的文件。"
      - "为了更精确地找到文件，请在搜索要求中加入文件标题中的关键词，比如：搜索涉及项目计划的文件。"
      - "如果您正在寻找某个格式的文件，请尝试输入：搜索DOCX格式的文件。"
      - "搜索报告文件时，请输入相关的主题或关键词，比如：搜索市场调研报告。"
      - "请提供文件的时间范围或日期，以便更精确地搜索，例如：搜索今年1月份的所有文档。"
    search-video:
      - "请提供搜索视频的关键词，您可以尝试输入：搜索mp4视频。"
      - "请提供搜索视频的关键词，您可以尝试输入：搜索今年上传的旅游视频。"
      - "搜索视频时，请明确视频格式，比如：搜索MP4格式的教学视频。"
      - "如果您记得视频的名称，请输入进行搜索，比如：搜索《星球大战》电影。"
      - "搜索特定话题的视频时，请尝试输入：搜索健身教程相关的视频。"
      - "如果想找到特定时间的视频，请输入上传时间范围，比如：搜索最近半年上传的视频。"
    search-audio:
      - "请提供搜索音频的关键词，您可以尝试输入：搜索mp3音频。"
      - "请提供搜索音频的关键词，您可以尝试输入：搜索mp3格式的演讲音频。"
      - "搜索音频时，请明确音频格式，比如：搜索mp3格式的音乐。"
      - "如果您记得音频的名称，请输入进行搜索，比如：搜索《月光奏鸣曲》音频。"
      - "为了找到特定时间的音频，请输入上传时间范围，比如：搜索去年上传的英语听力音频。"
    search-folder:
      - "请提供搜索文件夹的关键词，您可以尝试输入：搜索AI助手文件夹"
      - "尝试使用更具体的描述来搜索文件夹，以便我更好地帮助您找到它们，比如：搜索名为项目资料的文件夹。"
      - "如果您正在寻找特定日期创建的文件夹，请尝试输入：搜索2023年创建的文件夹。"
      - "请提供文件夹的名称作为关键词，比如：搜索客户合同相关的文件夹。"
    search-note:
      - "请提供搜索笔记的关键词，您可以尝试输入：搜索与“年度计划”相关的笔记。"
      - "搜索笔记时，请尝试输入笔记的标题，比如：搜索标题为“产品设计思路”的笔记。"
      - "如果您记得笔记的部分内容，请输入正文中的关键词，比如：搜索包含“市场调研”的笔记。"
      - "搜索关于某个项目的笔记时，请尝试输入项目名称，比如：搜索“XX项目进展”的笔记。"
      - "为了找到最近创建的笔记，请输入时间范围，比如：搜索本周内创建的笔记。"
      - "如果您记得笔记的某个关键词组合，请尝试输入它们，比如：搜索标题为Python编程和数据分析的笔记。"
      - "为了找到与某个主题相关的笔记，请输入主题名称，比如：搜索“人工智能”主题的笔记。"
    comprehensive-search:
      - "猜你想搜索多种内容，可以尝试输入：搜索2023年的图片和文档"
      - "猜你想更全面的搜索云盘的内容，可以尝试输入：搜索今年的图片和文档"
    search-activity:
      - "猜你想找找云盘的活动，您可以尝试输入：搜索云朵大作战。"
      - "猜你想找找云盘的活动，您可以尝试输入：搜索云朵中心。"
    search-function:
      - "请提供搜索功能的关键词，您可以尝试输入：搜索相册备份功能。"
      - "搜索云盘功能时，如果您想找到备份相关功能，可以输入：搜索手机备份相关功能。"
    search-discovery:
      - "请提供搜索试卷的关键词，您可以尝试输入：搜索八年级的试题。"
      - "请提供搜索试卷的关键词，您可以尝试输入：搜索中考模拟题。"
      - "搜索试卷资料时，可以输入想搜索的试卷类型，比如：搜索小升初的语文试卷。"
      - "准备期末复习资料，可以输入：搜索期末地理试题。"
      - "针对不同地区的考试，可以搜索不同地区的资料，例如：搜索河南省模拟试卷。"
  # 搜索提示
  prompt:
    search-image:
      - "未能搜索到相关图片，您可以尝试调整搜索关键词，获取更多结果。"
      - "似乎没有找到与您的搜索请求相匹配的图片，您可以试试其他描述或关键词。"
      - "未能找到您期望的图片结果，建议您修改搜索条件后再次尝试。"
    search-document:
      - "搜索结果中没有找到您想要的文档，建议您更换搜索条件后再次尝试。"
      - "未能找到满足你需求的文档，你可以更换搜索条件后再次尝试。"
      - "没有找到与您的搜索词匹配的文档，建议您尝试其他关键词。"
    search-video:
      - "你的云盘中未能找到符合你需求的视频，可以尝试更换搜索词或短语再试。"
      - "云盘中未能找到符合你需求的视频，请尝试使用更具体的描述或关键词进行搜索。"
    search-audio:
      - "你的云盘中未能找到符合你需求的音频，可以尝试更换搜索词或短语再试。"
      - "云盘中未能找到符合你需求的音频，请尝试使用更具体的描述或关键词进行搜索。"
    search-folder:
      - "搜索结果中没有找到您想要的文件夹，建议您更换搜索条件后再次尝试。"
      - "未能找到满足你需求的文件夹，你可以更换搜索条件后再次尝试。"
      - "没有找到与您的搜索词匹配的文件夹，建议您尝试其他关键词。"
    search-note:
      - "没有找到相匹配的笔记，你可以尝试使用其他关键词进行搜索。"
      - "未能找到相关笔记，你可以考虑到云盘笔记中查看你的全部笔记。"
    comprehensive-search:
      - "小天没有找到相匹配的内容，可以试试输入其他关键词试试哦"
      - "云盘中未能找到符合你需求的内容，可以尝试用更具体的描述进行搜索"
    search-activity:
      - "云盘中找不到相关活动内容，可以试试搜索云朵中心，体验云盘更多活动"
    search-function:
      - "云盘中找不到相关的功能哦，请换个关键词尝试一下。"
    search-discovery:
      - "没有找到相匹配的试卷，你可以尝试使用其他关键词进行搜索。"
      - "未能找到相关试卷资料，你可以考虑到发现广场中查看更多内容。"

# 推荐提示词模板
recommend-prompt-template:
  # 推荐提问语句提示词模板
  query-template:
    model-code: "qwen"
    template: '请帮我预测【%s】最可能提出的三个问题，每个问题不少于6个中文字，最多不超过20个中文字。输出必须遵循指定模式的JSON格式数组：["问题1","问题2","问题3"]，并且满足以下要求：\n  1、推荐的提问语句应简洁明了，易于理解，能融入对话流程中\n  2、推荐的提问语句需与用户输入内容和回复内容紧密相关\n  3、提问语句应具有较强的关联延展性，能够引导对话向更深入的领域或话题发展\n  4、确保推荐的提问语句在一段时间内具有多样性，避免频繁推荐相同或相似的语句\n  5、推荐的提问语句不能是用户当前输入内容或回复内容的另一种提问句式'

  # 多意图推荐提示词模板
  intention-template:
    model-code: "qwen"
    template-list:
      - intention: '000'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照对话问答格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '001'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“请描述这张图片”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '002'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“我想生成一张xxx的图片”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '003'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“请给图片抠图”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: 'https://yun.139.com/aiTools/#/faceMattingOffice?enableShare=1&token=#ssoToken#'
        link-url-outer: 'https://yun.139.com/aiTools/#/faceMattingOffice?enableShare=1&token=#ssoToken#'
      - intention: '004'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“图片转化为xxx的漫画风”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '005'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“让照片动起来”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '006'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“请给图片进行消除”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: 'https://yun.139.com/aiTools/#/picEliminateOffice?enableShare=1&token=#ssoToken#'
        link-url-outer: 'https://yun.139.com/aiTools/#/picEliminateOffice?enableShare=1&token=#ssoToken#'
      - intention: '007'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“设计一张xxx的AI头像”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '008'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“请将老照片进行修复”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '009'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“请将图片进行超分”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '011'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“我想生成艺术照”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: 'mcloud://mcMiniProgram?params=eyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLAoibmFtZSI6IuWwj+eoi+W6jyIsCiJtaW5pVHlwZSI6IjEiLAoibWluaVBhdGgiOiJwYWdlcy9ob21lL21haW4iLAoibWluaVVybCI6IiIsCiJtaW5pTGlua1R5cGUiOiIxIn0='
        link-url-outer: 'https://caiyun.feixin.10086.cn:7071/portal/clientDL/index.html?linkUrl=mcloud%3A%2F%2FmcMiniProgram%3Fparams%3DeyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLCJuYW1lIjoi5aaZ5LqR55u45py6IiwibWluaVR5cGUiOiIxIn0%3D'
      - intention: '012'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“我想搜索xxx的图片、帮我搜索xxx的照片”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '013'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“我想搜索xxx的文档、帮我搜索xxx的文档”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '014'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“我想搜索xxx的视频、帮我搜索xxx的视频”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '015'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“我想搜索xxx的音频、帮我搜索xxx的音频”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '016'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“我想搜索xxx的文件夹、帮我搜索xxx的文件夹”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '017'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“我想搜索xxx的笔记、帮我搜索xxx的笔记”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '018'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“我想搜索xxx、帮我搜索xxx”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '020'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“我想搜索xxx活动、帮我搜索xxx活动”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '021'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“我想搜索xxx功能、帮我搜索xxx功能”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '022'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“我想搜索xxx试卷、帮我搜索xxx试卷”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '024'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照“帮我创建笔记”格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '999'
        template: '请帮我预测【%s】最可能推荐的一个引导语句。输出必须遵循指定模式的JSON格式数组：["关键主体信息"]，并遵循以下要求：\n  1、保留句子的流畅性，不要分开抽取\n  2、请按照对话问答格式来总结生成，限制在6到20个中文字符之间'
        link-url-inner: ''
        link-url-outer: ''