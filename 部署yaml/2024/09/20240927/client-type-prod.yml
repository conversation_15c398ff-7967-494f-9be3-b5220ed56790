
# 客户端类型
client-type:
  list:
    - {code: 1, end: 'inner', remark: 'Android（手机客户端）'}
    - {code: 2, end: 'inner', remark: 'iOS（手机客户端-iPhone）'}
    - {code: 3, end: 'outer', remark: 'H5'}
    - {code: 4, end: 'outer', remark: 'TV（Android-TV客户端）'}
    - {code: 5, end: 'outer', remark: 'Android（手机SDK）'}
    - {code: 6, end: 'outer', remark: 'iOS（手机SDK）'}
    - {code: 7, end: 'outer', remark: 'TV（Android-TV SDK）'}
    - {code: 8, end: 'outer', remark: '微信小程序'}
    - {code: 9, end: 'outer', remark: 'WEB版本'}
    - {code: 10, end: 'outer', remark: '产品合作版本（比如八闽视频、爱国者硬件等）'}
    - {code: 11, end: 'inner', remark: 'PC（WINDOWS）'}
    - {code: 12, end: 'outer', remark: 'iPad'}
    - {code: 13, end: 'inner', remark: 'PC（MAC）'}
    - {code: 14, end: 'outer', remark: 'QQ小程序'}
    - {code: 15, end: 'outer', remark: '营销模块'}
    - {code: 16, end: 'outer', remark: '微信公众号'}
    - {code: 17, end: 'outer', remark: 'Android（手表客户端）'}
    - {code: 18, end: 'outer', remark: '鸿蒙Harmony原子化服务'}
    - {code: 19, end: 'outer', remark: 'Android（车载端）'}
    - {code: 20, end: 'outer', remark: '移动云盘短信小程序'}
    - {code: 21, end: 'inner', remark: '客户端小程序（即卡包小程序）'}
    - {code: 22, end: 'outer', remark: '支付宝小程序'}
    - {code: 23, end: 'outer', remark: '鸿蒙原生应用'}
    - {code: 26, end: 'inner', remark: 'Android（邮箱手机客户端）'}
    - {code: 27, end: 'inner', remark: 'iOS（邮箱手机客户端）'}
    - {code: 28, end: 'inner', remark: 'Android（云邮SDK）'}
    - {code: 29, end: 'inner', remark: 'iOS（云邮SDK）'}
    - {code: 30, end: 'inner', remark: '邮箱WEB版本'}
    - {code: 31, end: 'outer', remark: '邮箱酷版'}