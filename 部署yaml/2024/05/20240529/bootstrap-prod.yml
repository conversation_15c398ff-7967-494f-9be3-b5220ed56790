server:
  port: 19027
  servlet:
    context-path: /api/outer

logging:
  level:
    #root: INFO
    com.kaven.mybatisplus.dao: DEBUG
    com.shimain.springboot.mapper: INFO
    #pattern:
    #console: '%p%m%n'


hcy:
  plugin:
    uid:  #雪花算法插件生成配置
      snowflake:
        timeBits: 32
        workerBits: 18
        seqBits: 13
        epochStr: 2007-06-05  #不能高于此时间点。此种情况下，固定为19位雪花id
        paddingFactor: 50
  auth-interceptor:
    enable: true
    exclude-path:
  # 拦截校验的路径
  interceptor-path:
    interceptionPath:
      - /intelligent/search
  #用户白名单(配合拦截校验的路径使用)
  user-white:
    userWhiteList:
      - 1079785805058449675
      - 1079785794320933136
      - 1105470804722209016
      - 1101574403945695215
      - 1079785796468425009
      - 1105470703790412094
      - 1039895866800747225
      - 1039842815364654456
      - 1085988717093673575
      - 1039998808577024877
      - 1039942905282721773
      - 1098911393222042477
      - 1079785828680868403
      - 1100491466595254728
      - 1091161670195309960
      - 1039989978124233748
      - 1080451595858182188
      - 1082681066140017755
      - 1088957647597610874
      - 1079785805058449675
      - 1079785794320933136
      - 1079785796468425009
      - 1105470804722209016
      - 1039872828596171949
      - 1093781720506057413
      - 1040008652642097125
      - 1039975220616648009
      - 1040004357674739076
      - 1039805586588119811
      - 1126080153792954403
      - 1085988944727310270
      - 1039748927379573379
      - 1088957628270192456
      - 1090443459321562338


#用户域服务 请修改成现网的配置
user:
  url: http://user-njs-internal.yun.139.com:30080
  path:
  appSecret: pPIl*%n_BshMEeyF
  appSecretId: 1079827436178702406
  appKey: 1079827436178702402

yun:
  neauth:
    provider:
      enable: true
      config:
        url:  http://public-njs-internal.yun.139.com:30080/configcenter/neauth/configs
        appKey: 1079827436178702402
        appSecretId: 1079827436178702403
        appSecret: "JxR(5NMU*RYgR#w3"
        algorithmVersion: 1.0

eos:
  client:
    hostname: eos-guangzhou-1.cmecloud.cn
    accessKey: D25RTPTV9V0U0BQ64Y3O
    secretKey: YAsYqDAu92WBONouBkiNFOsJ82Zx185K6s3xsHqb
    signingRegion: guangzhou1
    #桶名称
    bucketName: b08-ypcg-oss-003

#上传限制为以下后缀
report:
  upload:
    allowedExtensions: jpg,jpeg,png,heic

nfs:
  #文件实际储存路径
  path: /data/yun-ai/yun-ai-api-outer/imageCaption/
  #nfs 路径校验 初始路径
  basePath: /data/yun-ai
  #转换路径（测试环境不同需要转换）
  aiPath: /data/yun-ai/yun-ai-api-outer/imageCaption/
  connectionTimeout: 1 # EOS url连接超时时间 单位:分钟
  readTimeout: 10 # NFS 读取文件超时时间 单位:分钟

aiManage:
  type: 4

userRoute:
  userRouteExpireHour: 24

templateMatch:
  threshold: 0.6

imageCaption:
  length: 10

intelligentsearch:
  IFLYTEKThreshold: 22.00
  BaiDuThreshold: 28.00
  TencentThreshold: 22.00

#  华为老平台网元鉴权参数
mcloud:
  net-auth:
    channelSrc: 10249400
    name: AISys
    pwd: 'VTB(SrQH1jzd&w5g!KZp'
    key: MODULE46DA041C44
    enabled: true

market:
  content-url:  https://caiyun.feixin.10086.cn:7071
  content-path: market/rabactive/ai/invite

thread-pool:
  global:
    core-size: 10
    max-size: 100
    keep-alive-time: 2
    keep-alive-time-unit: "MINUTES"
    rejected-class: "java.util.concurrent.ThreadPoolExecutor$AbortPolicy"

faceSearch:
  maxFileSize: 20

# 模型限制配置 注意此配置要放到nacos上
model:
  limit:
    qwen: { "length" : 3000, "QPS" : 10}
    xfyun: {"length" : 2000, "QPS" : 10}
    blian: {"length" : 10000, "QPS" : 10}

# 跳转链接配置 注意此配置要放到nacos上
link:
  url:
    ai-eliminate: https://yun.139.com/aiTools/#/picEliminateOffice?enableShare=1&token=#ssoToken#
    intelligent-cutout: https://yun.139.com/aiTools/#/faceMattingOffice?enableShare=1&token=#ssoToken#
    ai-head-sculpture: https://yun.139.com/aiTools/#/aiAvatarOffice?enableShare=1&token=#ssoToken#
    picture-comic-style: https://yun.139.com/aiTools/#/aiComicOffice?enableShare=1&token=#ssoToken#
    old-photos-repair: https://yun.139.com/aiTools/#/picRepairOffice?enableShare=1&token=#ssoToken#
    picture-generate-text: https://yun.139.com/aiTools/#/picToTextOffice?enableShare=1&token=#ssoToken#
    live-photos: https://yun.139.com/aiTools/#/picAnimateOffice?enableShare=1&token=#ssoToken#
    cloud-camera-external: https://caiyun.feixin.10086.cn:7071/portal/clientDL/index.html?linkUrl=mcloud%3A%2F%2FmcMiniProgram%3Fparams%3DeyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLCJuYW1lIjoi5aaZ5LqR55u45py6IiwibWluaVR5cGUiOiIxIn0%3D
    cloud-camera-within: mcloud://mcMiniProgram?params=eyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLAoibmFtZSI6IuWwj+eoi+W6jyIsCiJtaW5pVHlwZSI6IjEiLAoibWluaVBhdGgiOiJwYWdlcy9ob21lL21haW4iLAoibWluaVVybCI6IiIsCiJtaW5pTGlua1R5cGUiOiIxIn0=

# 厂商配置 注意此配置要放到nacos上
intention:
  company:
    picture-comic-style: "0"
    ai-head-sculpture: "4"
    old-photos-repair: "8"
    live-photos: "7"
    picture-generate-text: "4"
    text-generate-picture: "8"

common:
  #qps限制组件开启
  qpslimit:
    enabled: true
    #自定义参数
    limit: 600
    timeoutMillis: 1000
    expireTime: 120

hbase:
  client:
    username: cloudhbase
    accessKey: AEF9C7B415AF46F6A81076B63AE982CA
    quorum: hbase-zookeeper-0.hbase-zookeeper-headless.hbase-687acc91-ad9d-491c-aa96-6db773cdb443.svc.cluster.local:31444,hbase-zookeeper-1.hbase-zookeeper-headless.hbase-687acc91-ad9d-491c-aa96-6db773cdb443.svc.cluster.local:31444,hbase-zookeeper-2.hbase-zookeeper-headless.hbase-687acc91-ad9d-491c-aa96-6db773cdb443.svc.cluster.local:31444

yun-ai:
  image-nfs:
    sharePath: /data/yun-ai
    fodderCatalog: fodder

# 来源渠道
source-channels:
  channelList:
    - channel: 10102
      business-type: 'e-mcloud-app'
      mail: true
    - channel: 10103
      business-type: 'e-139mailold-app'
      mail: true
    - channel: 10104
      business-type: 'e-139mail-app'
      mail: true
    - channel: 10106
      business-type: 'e-139mail-app'
      mail: true
    - channel: 10108
      business-type: 'e-mcloud-app'
      mail: true
    - channel: 10109
      business-type: 'e-mcloud-app'
      mail: true
    - channel: 10110
      business-type: 'e-Cloudphone'
      mail: true
      end: 'outside'
    - channel: 10112
      business-type: 'e-mcloud-pc'
      mail: true
    - channel: 10114
      business-type: 'e-China Mobile Hall'
      mail: true
      end: 'outside'
    - channel: 10116
      business-type: 'e-Mobile office'
      mail: true
      end: 'outside'
    - channel: 10118
      business-type: 'e-Cloudphone EE'
      mail: true
      end: 'outside'
    - channel: 10160
      business-type: 'e-test'
      mail: true
      end: 'outside'
    - channel: 101
      business-type: 'c-mcloud-app'
      xiao-tian: true
    - channel: 102
      business-type: 'c-mcloud-app'
      xiao-tian: true
    - channel: 202
      business-type: 'c-139mail-app'
      xiao-tian: true
    - channel: 401
      business-type: 'c-mcloud-app'
      xiao-tian: true
    - channel: 400
      business-type: 'c-test'
      xiao-tian: true

# 文本大模型最大输入配置
text-model-max:
  #千问
  qwen: 20000
  #星火
  xfyun: 10000
  #百炼
  blian: 24000

# 对话应用类型信息列表接口中的标签
tabs:
  tabLabel: ["趣味","萌宠","IP复刻","重返历史","休闲陪聊"]

#会员中心权益项编号
member-center:
  # 权益开关，true-打开
  enabled: true
  # 权益编码
  benefit-no: RHR070
  # 权益编码列表
  benefits:
    - benefit-no: RHR064
      channel: 100102
      algorithm-code-list: ['001', '002', '003', '004', '005', '008', '009', '010']
      enabled: true
    - benefit-no: RHR078
      channel: 100103
      algorithm-code-list: ['006', '007']
      enabled: true
    - benefit-no: RHR065
      channel: 200101
      algorithm-code-list: ['001', '002', '003', '004', '005', '006', '007', '008', '009', '010']
      enabled: true
    - benefit-no: RHR066
      channel: 300101
      algorithm-code-list: ['001', '002', '003', '004', '005', '006', '007', '008', '009', '010']
      enabled: true
    - benefit-no: RHR075
      channel: 300201
      algorithm-code-list: ['001', '002', '003', '004', '005', '006', '007', '008', '009', '010']
      enabled: true
    - benefit-no: RHR067
      channel: 900101
      algorithm-code-list: ['001', '002', '003', '004', '005', '006', '007', '008', '009', '010']
      enabled: true

#搜索引导配置
search-guide:
  statement:
    search-image: "请提供搜索图片的关键词，您可以尝试输入：搜索12月在广州的图片"
    search-document: "请提供搜索文件的关键词，您可以尝试输入：搜索设计学习资料"
    search-video: "请提供搜索视频的关键词，您可以尝试输入：搜索新年晚会的视频"
    search-audio: "请提供搜索音频的关键词，您可以尝试输入：搜索英语听力练习的音频"
    search-folder: "请提供搜索文件夹的关键词，您可以尝试输入：搜索AI工具箱文件夹"
    search-note: "请提供搜索笔记的关键词，您可以尝试输入：搜索年度计划笔记"
    comprehensive-search: "请提供搜索文件夹的关键词，您可以尝试输入：搜索英语听力课程的视频和音频文件"

#流式接口配置
flowType:
  config:
    auditSize: 15
    timeout: 30000
    reconnectTimeMillis: 5000

# 令牌桶配置
dialogue-bucket:
  # 自研
  zy:
    # 通义千问
    - code: '000'
      period: 1
      limit: 3
      maxBurstSize: 3
      timeoutMillis: 1000
      scale: 2
      # 失效时间（秒）
      expireTime: 120
  # 讯飞
  xf:
    # 讯飞星火大模型
    - code: '000'
      period: 1
      limit: 1
      maxBurstSize: 1
      timeoutMillis: 1000
      scale: 2
      # 失效时间（秒）
      expireTime: 120
  # 阿里
  al:
    # 百炼
    - code: '000'
      period: 1
      limit: 3
      maxBurstSize: 3
      timeoutMillis: 1000
      scale: 2
      # 失效时间（秒）
      expireTime: 120
  # 阿里智能体
  xc:
    # 通义星尘
    - code: '000'
      period: 3
      limit: 5
      maxBurstSize: 5
      timeoutMillis: 1000
      scale: 2
      # 失效时间（秒）
      expireTime: 120

#送审平台开关
check-system:
  # true-需要送审
  open: true
