feign:
  httpclient:
    enabled: false
    max-connections: 3000 #连接池的最大连接数，默认200
    max-connections-per-route: 300 #每个路由(服务器)分配的组最大连接数，默认50
    connection-timeout: 30000 #连接超时时间，单位：毫秒
    time-to-live: 60 #连接最大存活时间，默认900秒
    time-to-live-unit: seconds
    disableSslValidation: true
  okhttp:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 15000
        readTimeout: 60000
  hystrix:
    enabled: false
  sentinel:
    enabled: true
  log:
    curl:
      enable: ON # ON: 开启curl日志打印  OFF: 关闭curl日志打印
      print-anyway: ON # ON: 打印所有日志  OFF:只打印错误日志

external:
  #邮件平台
  mail:
    enabled: true
    url: https://html5.mail.10086.cn
    path: /RmWeb/mail
    fid: 0
    func: mbox:readMessage
    maxSize: 6000
    mode: text
  #笔记平台
  note:
    enabled: true
    service-name: 'note'
    url: https://note.mcloud.139.com/noteServer
    max-size: 6000
    appCp: unknown
    cpVersion: 3.2.0
  # 送审配置
  check:
    enabled: true
    service-name: 'check-service-system'
    contextUrl: http://**********/sfap-sync/check
    channel: '1013'
    channelKey: 'sJPk9RH4oGCGcjTeTST#'
    version: v1.0
    nameServer: '*********:19876;*********:19876'
    group: 'GID_PAAS_AI_ASSISTANT'
    tags: 'ai_assistant'
    accessKey: 'ai_assistant_user'
    secretKey: 'sJPk9RH4oGCGcjTeTST#'
    topic: 'paas_sfap_audit_input_vip'
    consumerTopic: 'paas_sfap_audit_output_vip'
    textSvcType: '42 '
    imageSvcType: '43'
    imageGenSvcType: '44'
    msgTraceSwitch: true
    sendTimeout: 5000
  # 中心任务
  center-task:
    enabled: true
    name: 'yun-ai-center-task'
    url: 'http://yun-ai-center-task-svc:19012'
    path: 'yun/ai/center/task'
  ai-model:
    # 对话意图
    cmic-dialogue:
      intention:
        enabled: true
        serviceName: dialogueIntention
        serviceUrl: http://ai-internal-gzfh.yun.139.com:30080
    #百炼
    blian:
      enabled: true
      accessKeyId: LTAI5tQt3AGzkT6MPjrccEhv
      accessKeySecret: ******************************
      agentKey: 31d9c08045e54ca899b555308f5b4314_p_efm
      appId: a9b845be58bf4a6c9d64ab1f3347dae4
    #千问
    qwen:
      enabled: true
      url: http://ai-internal-gzfh.yun.139.com:30080/yun/ai/llm/chat/completions
      model-id: Qwen1.5-72B-Chat-AWQ
      #模型名称列表：模型id映射url
      models:
        - name: q05b 
          id: Qwen1.5-0.5B-Chat-AWQ
          url: http://ai-internal-dg-gpu.yun.139.com:30080/yun/ai/llm/q05b/chat/completions
    #星火
    xfyun:
      enabled: true
      appid: xtgcbapp
      appkey: 5ec7dba80f990f4c2dcbf9457ed075b6
      baseurl: http://10.20.9.52:9050
      api: api/v1/aichat/custom/interactive
      websocketBaseurl: ws://10.20.9.52:9050
      websocketApi: ws/api/v1/aichat/interactive
    #通义星尘
    xingchen:
      enabled: true
      basePath: https://nlp.aliyuncs.com
      apiKey: lm-2tcCTreN5rRM6YxyYP3HRA==
      topP: 0.95
      temperature: 0.92
  # 用户域配置
  yun-user:
    enabled: true
    service-name: yun-user-domain-service
    context-url: http://user-njs-internal.yun.139.com:30080
    context-path: user
    app-key: 1079827436178702402
    app-secret-id: 1079827436178702406
    app-secret: "pPIl*%n_BshMEeyF"
    algorithm-version: 1.0
    mod-addr-type: 2
  # 云盘配置
  yundisk:
    enabled: true
    yun-personal:
      uploadPathDir: '/data/yun-ai/temppath'
      #图片过期时间（秒）
      imgExpireSec: 86400
      app-key: 1079827436178702402
      app-secret-id: 1079827436178702410
      app-secret: "osw$mAaeEJEFIe&I"
      algorithm-version: 1.0
      api-version: v1
      pdsUrl: http://personal-pds-njs-internal.yun.139.com:30080/hcy
      hwUrl: http://personal-kd-njs-internal.yun.139.com:30080/hcy
      dspUrl: http://personal-dsp-njs-internal.yun.139.com:30080/hcy
    ose:
      # 连接超时时间 单位：毫秒
      timeout: 60000
      # 是否走内网
      x-inner-ntwk: true
  # 文本工具
  api-text:
    enabled: true
    name: 'yun-ai-api-text'
    url: 'http://yun-ai-api-text-svc:19193'
    path: '/api/text'
  # 会员中心
  membercenter:
    enabled: true
    url: https://ypqy.mcloud.139.com/isbo2/openApi
    apId: 1005
    apsecretkey: NUS34ARC2ZG8R6
    service-name: membercenter

yun:
  external:
    #    library:
    #      service-name: yun-ai-library-manage
    #      context-path: /ai/library/manage
    #      url: http://yun-ai-library-manage-svc:19015
    # 智能搜图 -科大讯飞
    intelligentSearch:
      service-name: intelligentsearch
      url: http://10.19.42.47:5008/ai/current
    #智能搜图 -百度-20240320添加
    baiduIntelligentSearch:
      service-name: baiduIntelligentSearch
      url: http://10.19.16.193:8883/ai-gpu/bd/yun/ai/current
    certificate:
      name: certificate
      url: http://10.19.41.109:12367/yun/ai
    # 图配文
    imageCaption:
      service-name: imageCaption
      url: http://10.19.41.109:12371
    # 人脸搜索
    facialImageSearch:
      service-name: facialImageSearch
      url: http://10.19.41.109:9496/yun/ai/current
    # 7.8 图片标签与影集模板匹配模型
    templateMatch:
      service-name: templateMatch
      url: 10.19.41.109:5005
    #个人云
    person:
      serviceId: personal
      appKey: 1079827436178702402
      appSecretId: 1079827436178702409
      appSecret: U9pEAU^JkU8aWLr4
      expireSec-max: 86400
      expireSec-default: 900
    # 智能搜图 -腾讯
    tencentIntelligentSearch:
      service-name: tencentIntelligentSearch
      url: http://10.19.16.193:8883/ai-gpu/tx/yun/ai/current