# 二次对话sse流式配置
second-stream:
  # 配置信息
  config:
    audit-size: 30
    mode-max-tokens: 15000
    mode-code: "qwen"
    default-email-content: "无邮件内容。"
    timeout: 30000
    reconnect-time-millis: 5000
    allow-intention: [ "032" ]
    prompt-template: "# 1. 任务背景\n\n你是一名专业的**实体识别专家**，能够从用户的输入文本中准确地提取出相关的**邮件内容**。\n\n# 2. 任务要求\n\n1.**分析用户输入**：接收到用户输入后，仔细阅读并理解文本内容，从中提取出所有可能的邮件内容。\n\n2.**精准提取**：尽可能提取完整和准确的信息。\n\n3.**提取原则**：如果文本中没有明确提到邮件内容（如“帮我发邮件”、“我要发邮件”、“帮我给小李发邮件”等），则输出   \n\n\n# 3. 示例\n\n**示例1**\n\n**用户输入**：\n\n“发送邮件给李明，邮件内容是：亲爱的李明，我们计划在10月16日早上9点对APP V1.0的需求方案进行深度审议，期待您的参与，请提前做好准备，准时出席。感谢关注。”\n\n**输出**：\n\n亲爱的李明，我们计划在10月16日早上9点对APP V1.0的需求方案进行深度审议，期待您的参与，请提前做好准备，准时出席。感谢关注。\n\n\n**示例2**\n\n**用户输入**：\n\n“请给15820780084发邮件，提醒他提交月总结报告”\n\n**输出**：\n提交月总结报告\n\n\n**示例3**\n\n**用户输入**：\n\n“给李明发送邮件”\n\n**输出**：\n   \n\n\n# 4. 现在的任务\n\n请根据上述要求，对以下用户输入进行实体抽取（注意：无需返回思考分析过程和模型解释）：\n\n**用户输入**：\n\n{query}"
