#知识库相关配置

#知识库
knowledge:
  # 个人知识库
  personal:
    # 文件大小限制（B）
    size: 104857600
    # 扩展名
    ext-list: [ "docx","xlsx","csv","txt","pptx","doc","xls","ppt" ]
    # 标签个数
    label-num: 50
    # 标签名字数
    label-name-len: 6

    # 转存任务过期时长（秒）
    transfer-expire-time: 86400
    # 删除任务过期时长（秒）
    delete-expire-time: 86400

    # 删除任务查询间隔时间（毫秒）
    delete-task-query-sleep: 5000
    # 删除任务查询次数（每次10秒钟）
    delete-task-query-times: 6

    # 解析失败原因（错误码对应的描述）
    parse-failed-reason:
      "0000": ""
      "9999": "未知错误"
      "10090027": "向量化接口调用失败"
      "10090018": "读取文档文件不存在"
      "10090026": "文件文本解析结果为空"
      "999999999": "文件真实格式与文件后缀不适配"
      "105": "审核失败"
      "10070404": "文件下载失败"
      "10070405": "下载的文件内容为空"
      "10070406": "送审失败"

  # 知识库对话
  dialogue:
    # 指定使用的公共知识库的标识
    knowledge-base-id: "common"
    # 公共知识库白名单
    white-list:
      - "13802885259"
      - "13802883435"
      - "13802885171"
      - "18928819103"
      - "18771000041"
      - "13926431390"
      - "13542899545"
      - "15521090847"
      - "13425458504"
      - "19802021524"
      - "13802885451"
      - "18316688152"
      - "15113990046"
      - "13570559600"
      - "19802025024"
      - "19802021105"
      - "19802021150"
      - "19802021522"
      - "19802021782"
      - "19802021799"
      - "13802885450"
      - "19802025559"
      - "13750326098"
      - "15219891695"
      - "18002239030"
      - "15013271046"
      - "13078882662"
      - "13802882416"
      - "18142851953"
      - "13326349056"
      - "13977527302"
      - "13246408473"
      - "13710660941"
      - "13250164151"
      - "13802885271"
      - "17846876519"
      - "13557589652"
      - "15811852975"
      - "19802024110"
      - "13802885236"
      - "15992549826"
      - "15078267817"
      - "15011750751"
      - "17825902628"
      - "15913080189"
      - "18884581208"
      - "18884681208"
      - "18902224594"
      - "15507829621"
      - "13877725304"
      - "16676302621"
      - "13501525064"
      - "13750326098"
      - "18002239030"
      - "15014317558"
      - "19120129031"
      - "19849977090"
      - "13528277450"
      - "19925811004"
      - "15216248520"
      - "18249907653"
      - "13632481841"
      - "15820472203"
      - "13802885432"
      - "13802885115"
      - "19802021462"
      - "13826074981"
      - "15531012900"
      - "17701952898"
      - "13580574830"
      - "13631272874"
      - "18327863481"
    # 个人知识库开关
    personal-switch: true

    # 重写开关
    rewrite-switch: false
    # 重写使用的大模型
    rewrite-model-code: "qwen_32b"
    # 重写输入内容系统提示
    rewrite-system-prompt: "你是一个旨在帮助用户更有效检索信息的助手。\n你的主要职责是在用户输入表达不明确的情况下，通过参考#历史对话摘要#和#关键词列表#，对原始问题进行重写。\n你的目标是使问题更加具体和容易被检索，并保持与用户原始意图的一致性\n并且，请不要忽略#原始用户问题#中的内容\n你应该1. 理解背景: 通过阅读历史对话摘要，了解用户之前的对话内容，把握对话的上下文和主题。\n2. 利用关键词: 将关键词融入问题，确保#重写后的问题#包含这些关键词，提高检索的相关性。\n3. 增加细节: 如果用户的问题过于宽泛或模糊，适当添加细节使问题更具体，但不要引入新的概念或信息。\n4. 保持一致性: 确保#重写后的问题#不偏离用户原始的意图或信息需求。\n5. 简洁明了: 保持问题简短而明确，避免冗长或复杂的表述。\n#重写后的问题#只能在#原始用户问题#的基础上增加10-20个字\n#原始用户问题#，#重写后的问题#，#历史对话摘要#，#关键词列表#都不允许出现在#重写后的问题#中\n#历史对话摘要#:{history}\n#关键词列表#:{keywords}"
    # 重写输入内容用户提示
    rewrite-user-prompt: "#原始用户问题#:{query}\n#重写后的问题#:"

    # ES查询相似度(公共知识库切片)
    es-split-min-score: 0.1
    # ES查询条数(公共知识库切片)
    es-split-top-n: 10
    # ES查询相似度(公共知识库问答)
    es-qa-min-score: 0.1
    # ES查询条数(公共知识库问答)
    es-qa-top-n: 10
    #es查询权重
    es-query-weight: 0.5
    #es rescore查询权重
    es-rescore-query-weight: 0.25
    # ES查询相似度（个人知识库）
    es-personal-min-score: 0.1
    # ES查询条数（个人知识库）
    es-personal-top-n: 10
    #es查询权重（个人知识库）
    es-personal-query-weight: 0.5
    #es rescore查询权重（个人知识库）
    es-personal-rescore-query-weight: 0.25

    # 重排后最小评分
    rerank-min-score: 0.3
    # 重排返回条数
    rerank-top-n: 3

    # 对话模型编码
    rag-model-codes: [ "qwen_32b","qwen","blian" ]
    # rag对话系统提示词
    rag-system-prompt: "你的核心任务是结合#相关知识#，针对#用户问题#生成答案。\n\n你的目标是：\n1. 过滤并分析#相关知识#中符合#渠道号#和#客户端类型#条件的内容。\n2. 依据过滤后的知识，拆解用户的问题，提供完整的答案，确保覆盖#用户问题#的所有方面。\n3. #答案#应当语句通顺，简洁精炼，不超过100字。\n4. 返回的答案中不能包含关于#渠道号#和#客户端类型#的相关描述。\n5. 所有网页地址必须使用以下HTML超链接格式返回：\n<a data-href='http://your-url.com'>文本</a>\n确保链接格式正确且一致。\n\n#示例#: \n#用户问题#: 如何找到自动备份\n#相关知识#: 适应范围：渠道号（101，102）+客户端类型（1，2） Q1：如何开启/关闭手机自动备份？ A1：1）在云盘app首页选择【手机备份】 2）开启自动备份相册、通讯录、微信。\n#渠道号#: 101，102\n#客户端类型#: 1,2\n#答案#:找到移动云盘的自动备份，按照以下步骤操作：\n\n1）在云盘app首页选择【手机备份】 \n2）自动备份相册、通讯录、微信。\n"
    # rag对话用户输入模板
    rag-user-template: "#用户问题#: {query}\n#相关知识#: {knowledge}\n#渠道号#: {sourceChannel}\n#客户端类型#: {clientType}\n#答案#:"
    # 大模型参数
    textModelConfig:
      # 大模型温度参数
      temperature: 0.6
      # 大模型top_p参数
      top-p: 0.9
      # 随机种子（-1表示不传值）
      seed: -1

    # 开头文案，编码对应枚举KnowledgeBaseEnum
    title-map:
      # 只命中个人知识库
      personal: "根据你的知识库："
      # 只命中公共知识库
      common: "根据我获取的知识："
      # 命中个人知识库和公共知识库
      knowledge: "根据我获取的知识及你的知识库："
