
# 来源渠道
source-channels:
  channelList:
    - { channel: 10102, business-type: 'e-mcloud-app', code: "yunmail", innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 10103, business-type: 'e-139mailold-app', code: "yunmail", innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 10104, business-type: 'e-139mail-app', code: "yunmail", innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 10105, business-type: 'e-139mail-H5', code: "yunmail", end: 'outer', innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 10106, business-type: 'e-139mail-app', code: "yunmail", innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 10108, business-type: 'e-mcloud-app', code: "yunmail", innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 10109, business-type: 'e-mcloud-app', code: "yunmail", innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 10110, business-type: 'e-Cloudphone', code: "yunmail", end: 'outer', innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 10112, business-type: 'e-mcloud-pc', code: "yunmail", innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 10120, business-type: 'e-mcloud-pc', code: "yunmail", innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 10114, business-type: 'e-ChinaMobileHall', code: "yunmail", end: 'outer', innerBenefitNo: RHR070, outerBenefitNo: RHR069, benefit-switch: true }
    - { channel: 10130, business-type: 'e-ChinaMobileHall', code: "yunmail", end: 'outer', innerBenefitNo: RHR070, outerBenefitNo: RHR069, benefit-switch: true }
    - { channel: 10116, business-type: 'e-Mobileoffice', code: "yunmail", end: 'outer', innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 10140, business-type: 'e-Mobileoffice', code: "yunmail", end: 'outer', innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 10118, business-type: 'e-CloudphoneEE', code: "yunmail", end: 'outer', innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 10150, business-type: 'e-CloudphoneEE', code: "yunmail", end: 'outer', innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 10160, business-type: 'e-test', code: "yunmail", end: 'outer', innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 10170, business-type: 'e-139mail-web', code: "yunmail", end: 'outer', innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 10171, business-type: 'e-Cloudphone-Enterprise', code: "yunmail", end: 'outer', innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 10172, business-type: 'e-ChinaMobileShandong', code: "yunmail", end: 'outer', innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 10101, business-type: 'e-139mail-unknown', code: "yunmail", end: 'outer', innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 10107, business-type: 'e-139mail-unknown', code: "yunmail", end: 'outer', innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 10161, business-type: 'e-139mail-unknown', code: "yunmail", end: 'outer', innerBenefitNo: RHR070, outerBenefitNo: RHR079, benefit-switch: true }
    - { channel: 101, business-type: 'c-mcloud-app', code: "xiaotian", innerBenefitNo: RHR070, outerBenefitNo: RHR079, innerToolBenefitNo: RHR078, outerToolBenefitNo: RHR078, algorithmCodeList: [ '007' ] }
    - { channel: 102, business-type: 'c-mcloud-app', code: "xiaotian", innerBenefitNo: RHR070, outerBenefitNo: RHR079, innerToolBenefitNo: RHR078, outerToolBenefitNo: RHR078, algorithmCodeList: [ '007' ] }
    - { channel: 103, business-type: 'c-mcloud-note', code: "xiaotian", innerBenefitNo: RHR070, outerBenefitNo: RHR079, innerToolBenefitNo: RHR078, outerToolBenefitNo: RHR078, algorithmCodeList: [ '007' ] }
    - { channel: 202, business-type: 'c-139mail-app', code: "xiaotian", innerBenefitNo: RHR070, outerBenefitNo: RHR079, innerToolBenefitNo: RHR078, outerToolBenefitNo: RHR078, algorithmCodeList: [ '007' ] }
    - { channel: 401, business-type: 'c-mcloud-app', code: "xiaotian", innerBenefitNo: RHR070, outerBenefitNo: RHR079, innerToolBenefitNo: RHR078, outerToolBenefitNo: RHR078, algorithmCodeList: [ '007' ] }
    - { channel: 400, business-type: 'c-test', code: "xiaotian", innerBenefitNo: RHR070, outerBenefitNo: RHR079, innerToolBenefitNo: RHR078, outerToolBenefitNo: RHR078, algorithmCodeList: [ '007' ] }