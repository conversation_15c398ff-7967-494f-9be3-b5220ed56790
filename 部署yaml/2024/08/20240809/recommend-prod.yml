#搜索提示配置
search-prompt:
  # 搜索引导
  guide:
    search-image:
      - "搜索图片时，您可以尝试输入具体的主题，比如：搜索夜景的图片。"
      - "为了让搜索更准确，请描述您想要查看的图片样式，比如：搜索建筑的图片。"
      - "试着用搜索你在某个地点的图片，例如：搜索在广州拍摄的图片。"
      - "您可以尝试这样输入您的搜索内容，比如：搜索2023年12月在广州的图片。"
      - "猜你想搜图片，假如要搜索美食图片，请加上具体的食物名称或类型，比如：搜索蛋糕的图片。"
      - "想要找到某个时间段的图片吗？请输入相应的时间试试，比如：搜索2023年的图片。"
      - "如您想搜索动物图片，请输入动物的名称，比如：搜索小猫的图片。"
      - "为了获得更准确的搜索结果，请您尽可能描述您想要搜索的图片样式，比如：搜索海边度假图片。"
    search-document:
      - "请提供搜索文件的关键词，您可以尝试输入：搜索每周例会会议纪要文档。"
      - "请明确您的搜索目标，例如：搜索财务报表相关的Excel文件。"
      - "搜索文件时，您可以尝试输入文件名或文件类型，比如：搜索pdf格式的文件。"
      - "为了更精确地找到文件，请在搜索要求中加入文件标题中的关键词，比如：搜索涉及项目计划的文件。"
      - "如果您正在寻找某个格式的文件，请尝试输入：搜索DOCX格式的文件。"
      - "搜索报告文件时，请输入相关的主题或关键词，比如：搜索市场调研报告。"
      - "请提供文件的时间范围或日期，以便更精确地搜索，例如：搜索今年1月份的所有文档。"
    search-video:
      - "请提供搜索视频的关键词，您可以尝试输入：搜索mp4视频。"
      - "请提供搜索视频的关键词，您可以尝试输入：搜索今年上传的旅游视频。"
      - "搜索视频时，请明确视频格式，比如：搜索MP4格式的教学视频。"
      - "如果您记得视频的名称，请输入进行搜索，比如：搜索《星球大战》电影。"
      - "搜索特定话题的视频时，请尝试输入：搜索健身教程相关的视频。"
      - "如果想找到特定时间的视频，请输入上传时间范围，比如：搜索最近半年上传的视频。"
    search-audio:
      - "请提供搜索音频的关键词，您可以尝试输入：搜索mp3音频。"
      - "请提供搜索音频的关键词，您可以尝试输入：搜索mp3格式的演讲音频。"
      - "搜索音频时，请明确音频格式，比如：搜索mp3格式的音乐。"
      - "如果您记得音频的名称，请输入进行搜索，比如：搜索《月光奏鸣曲》音频。"
      - "为了找到特定时间的音频，请输入上传时间范围，比如：搜索去年上传的英语听力音频。"
    search-folder:
      - "请提供搜索文件夹的关键词，您可以尝试输入：搜索AI助手文件夹"
      - "尝试使用更具体的描述来搜索文件夹，以便我更好地帮助您找到它们，比如：搜索名为项目资料的文件夹。"
      - "如果您正在寻找特定日期创建的文件夹，请尝试输入：搜索2023年创建的文件夹。"
      - "请提供文件夹的名称作为关键词，比如：搜索客户合同相关的文件夹。"
    search-note:
      - "请提供搜索笔记的关键词，您可以尝试输入：搜索与“年度计划”相关的笔记。"
      - "搜索笔记时，请尝试输入笔记的标题，比如：搜索标题为“产品设计思路”的笔记。"
      - "如果您记得笔记的部分内容，请输入正文中的关键词，比如：搜索包含“市场调研”的笔记。"
      - "搜索关于某个项目的笔记时，请尝试输入项目名称，比如：搜索“XX项目进展”的笔记。"
      - "为了找到最近创建的笔记，请输入时间范围，比如：搜索本周内创建的笔记。"
      - "如果您记得笔记的某个关键词组合，请尝试输入它们，比如：搜索标题为Python编程和数据分析的笔记。"
      - "为了找到与某个主题相关的笔记，请输入主题名称，比如：搜索“人工智能”主题的笔记。"
    comprehensive-search:
      - "猜你想搜索多种内容，可以尝试输入：搜索2023年的图片和文档"
      - "猜你想更全面的搜索云盘的内容，可以尝试输入：搜索今年的图片和文档"
    search-activity:
      - "猜你想找找云盘的活动，您可以尝试输入：搜索云朵大作战。"
      - "猜你想找找云盘的活动，您可以尝试输入：搜索云朵中心。"
    search-function:
      - "请提供搜索功能的关键词，您可以尝试输入：搜索相册备份功能。"
      - "搜索云盘功能时，如果您想找到备份相关功能，可以输入：搜索手机备份相关功能。"
    search-discovery:
      - "请提供搜索试卷的关键词，您可以尝试输入：搜索八年级的试题。"
      - "请提供搜索试卷的关键词，您可以尝试输入：搜索中考模拟题。"
      - "搜索试卷资料时，可以输入想搜索的试卷类型，比如：搜索小升初的语文试卷。"
      - "准备期末复习资料，可以输入：搜索期末地理试题。"
      - "针对不同地区的考试，可以搜索不同地区的资料，例如：搜索河南省模拟试卷。"
  # 搜索提示
  prompt:
    search-image:
      - "未能搜索到相关图片，您可以尝试调整搜索关键词，获取更多结果。"
      - "似乎没有找到与您的搜索请求相匹配的图片，您可以试试其他描述或关键词。"
      - "未能找到您期望的图片结果，建议您修改搜索条件后再次尝试。"
    search-document:
      - "搜索结果中没有找到您想要的文档，建议您更换搜索条件后再次尝试。"
      - "未能找到满足你需求的文档，你可以更换搜索条件后再次尝试。"
      - "没有找到与您的搜索词匹配的文档，建议您尝试其他关键词。"
    search-video:
      - "你的云盘中未能找到符合你需求的视频，可以尝试更换搜索词或短语再试。"
      - "云盘中未能找到符合你需求的视频，请尝试使用更具体的描述或关键词进行搜索。"
    search-audio:
      - "你的云盘中未能找到符合你需求的音频，可以尝试更换搜索词或短语再试。"
      - "云盘中未能找到符合你需求的音频，请尝试使用更具体的描述或关键词进行搜索。"
    search-folder:
      - "搜索结果中没有找到您想要的文件夹，建议您更换搜索条件后再次尝试。"
      - "未能找到满足你需求的文件夹，你可以更换搜索条件后再次尝试。"
      - "没有找到与您的搜索词匹配的文件夹，建议您尝试其他关键词。"
    search-note:
      - "没有找到相匹配的笔记，你可以尝试使用其他关键词进行搜索。"
      - "未能找到相关笔记，你可以考虑到云盘笔记中查看你的全部笔记。"
    comprehensive-search:
      - "小天没有找到相匹配的内容，可以试试输入其他关键词试试哦"
      - "云盘中未能找到符合你需求的内容，可以尝试用更具体的描述进行搜索"
    search-activity:
      - "云盘中找不到相关活动内容，可以试试搜索云朵中心，体验云盘更多活动"
    search-function:
      - "云盘中找不到相关的功能哦，请换个关键词尝试一下。"
    search-discovery:
      - "没有找到相匹配的试卷，你可以尝试使用其他关键词进行搜索。"
      - "未能找到相关试卷资料，你可以考虑到发现广场中查看更多内容。"

# 推荐提示词模板
recommend-prompt-template:
  #默认统一追加提示词
  default-append-prompt: ''
  # 推荐提问语句提示词模板
  query-template:
    model-code: "qwen2_7b"
    template: 'User当前问题是: {%s}\n  请帮我预测User接下来最可能提出的三个问题，每个问题不少于6个字，但不超过20个字符，\n  问题必须积极、合法、政治正确，与主题有较强的关联延展性与多样性。\n  输出严格按照以下数组格式：\n  ["问题1","问题2","问题3"]\n  questions：'

  # 多意图推荐提示词模板
  intention-template:
    model-code: "qwen2_7b"
    template-list:
      - intention: '000'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{普通对话}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{普通对话}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：今天天气真好，搜一下好天气的照片\n  {普通对话}意图提取：["今天天气怎么样"]\n  输入：帮我找广州的图，以及查一下广州人口，以及打开老照片修复\n  {普通对话}意图提取：["广州人口有多少"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{普通对话}>最相关的意图并总结提炼。\n  {普通对话}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '001'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{图片配文}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{图片配文}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：今天天气真好，搜一下好天气的照片，并给它配上符合意境的文字说明\n  {图片配文}意图提取：["给照片配文"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{图片配文}>最相关的意图并总结提炼。\n  {图片配文}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '002'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{文生图}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{文生图}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：搜广州旅游的照片，并配上符合意境的文字说明，参考梵高《星月夜》作一幅画，并让画活起来\n  {文生图}意图提取：["创作星空主题画"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{文生图}>最相关的意图并总结提炼。\n  {文生图}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '003'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{智能抠图}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{智能抠图}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：搜广州旅游的照片，并让画活起来，如何使用智能抠图进行照片修复和增强处理？\n  {智能抠图}意图提取：["如何使用智能抠图"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{智能抠图}>最相关的意图并总结提炼。\n  {智能抠图}意图提取:'
        link-url-inner: 'https://yun.139.com/aiTools/#/faceMattingOffice?enableShare=1&token=#ssoToken#'
        link-url-outer: 'https://yun.139.com/aiTools/#/faceMattingOffice?enableShare=1&token=#ssoToken#'
      - intention: '004'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{图片动漫化}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{图片动漫化}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：搜广州旅游的照片，并转为漫画风，如何使用智能抠图进行照片修复和增强处理？\n  {图片动漫化}意图提取：["照片转为漫画风"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{图片动漫化}>最相关的意图并总结提炼。\n  {图片动漫化}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '005'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{活照片}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{活照片}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：搜手机内最近的照片，并转为活照片模式\n  {活照片}意图提取：["照片转为活照片"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{活照片}>最相关的意图并总结提炼。\n  {活照片}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '006'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{AI消除}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{AI消除}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：搜合照，并把图中的杂物去掉\n  {AI消除}意图提取：["合照去杂物"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{AI消除}>最相关的意图并总结提炼。\n  {AI消除}意图提取:'
        link-url-inner: 'https://yun.139.com/aiTools/#/picEliminateOffice?enableShare=1&token=#ssoToken#'
        link-url-outer: 'https://yun.139.com/aiTools/#/picEliminateOffice?enableShare=1&token=#ssoToken#'
      - intention: '007'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{AI头像}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{AI头像}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：找我的照片，并基于我的面部特征设计AI头像\n  {AI头像}意图提取：["设计AI头像"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{AI头像}>最相关的意图并总结提炼。\n  {AI头像}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '008'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{老照片修复}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{老照片修复}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：找19世纪的清朝图片，并对其进行修复\n  {老照片修复}意图提取：["清朝照片修复"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{老照片修复}>最相关的意图并总结提炼。\n  {老照片修复}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '009'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{画质修复}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{画质修复}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：生成夏日海边沙滩图片，并进行超分处理\n  {画质修复}意图提取：["沙滩图片超分"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{画质修复}>最相关的意图并总结提炼。\n  {画质修复}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '011'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{妙云相机}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{妙云相机}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：生成夏日海边沙滩图片，搜学习资料文件夹，生成学院风格的艺术照\n  {妙云相机}意图提取：["生成学院艺术照"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{妙云相机}>最相关的意图并总结提炼。\n  {妙云相机}意图提取:'
        link-url-inner: 'mcloud://mcMiniProgram?params=eyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLAoibmFtZSI6IuWwj+eoi+W6jyIsCiJtaW5pVHlwZSI6IjEiLAoibWluaVBhdGgiOiJwYWdlcy9ob21lL21haW4iLAoibWluaVVybCI6IiIsCiJtaW5pTGlua1R5cGUiOiIxIn0='
        link-url-outer: 'https://caiyun.feixin.10086.cn:7071/portal/clientDL/index.html?linkUrl=mcloud%3A%2F%2FmcMiniProgram%3Fparams%3DeyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLCJuYW1lIjoi5aaZ5LqR55u45py6IiwibWluaVR5cGUiOiIxIn0%3D'
      - intention: '012'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{搜图片}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{搜图片}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：生成夏日海边沙滩图片，搜我在广州旅游的照片，生成学院风格的艺术照\n  {搜图片}意图提取：["搜广州旅游照片"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{搜图片}>最相关的意图并总结提炼。\n  {搜图片}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '013'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{搜文档}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{搜文档}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：生成夏日海边沙滩图片，生成AI头像，搜手机内学习文档\n  {搜文档}意图提取：["搜学习文档"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{搜文档}>最相关的意图并总结提炼。\n  {搜文档}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '014'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{搜视频}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{搜视频}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：生成AI头像，搜手机内学习视频，翻译中文文稿\n  {搜视频}意图提取：["搜学习视频"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{搜视频}>最相关的意图并总结提炼。\n  {搜视频}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '015'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{搜音频}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{搜音频}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：生成AI头像，搜手机内开会音频，翻译中文文稿\n  {搜音频}意图提取：["搜开会音频"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{搜音频}>最相关的意图并总结提炼。\n  {搜音频}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '016'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{搜文件夹}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{搜文件夹}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：打开AI头像，找手机内AI文件夹并展示对应的时间\n  {搜文件夹}意图提取：["搜AI文件夹"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{搜文件夹}>最相关的意图并总结提炼。\n  {搜文件夹}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '017'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{搜笔记}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{搜笔记}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：打开妙云相机，找最近1个月撰写的笔记，并生成总结\n  {搜笔记}意图提取：["搜索最近1个月的笔记"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{搜笔记}>最相关的意图并总结提炼。\n  {搜笔记}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '018'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{综合搜索}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{综合搜索}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：找旅游照片，搜索学习文档，并生成总结，打开AI头像\n  {综合搜索}意图提取：["找旅游照片、学习文档"]\n  输入：帮我找所有文件，打开AI头像\n  {综合搜索}意图提取：["找文件"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{综合搜索}>最相关的意图并总结提炼。\n  {综合搜索}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '020'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{活动搜索}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{活动搜索}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：找旅游照片，打开AI头像，天天开盲盒活动在哪？\n  {活动搜索}意图提取：["搜天天开盲盒活动"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{活动搜索}>最相关的意图并总结提炼。\n  {活动搜索}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '021'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{功能搜索}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{功能搜索}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：找旅游照片，打开AI头像，照片备份怎么使用？功能入口在哪里？\n  {功能搜索}意图提取：["搜照片备份功能"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{功能搜索}>最相关的意图并总结提炼。\n  {功能搜索}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '022'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{发现广场搜索}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{发现广场搜索}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：找旅游照片，打开AI头像，衡水题目难吗？帮我找几套高三数学试卷\n  {发现广场搜索}意图提取：["找衡水高三数学试卷"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{发现广场搜索}>最相关的意图并总结提炼。\n  {发现广场搜索}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '024'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{创建笔记}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{创建笔记}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：打开AI头像，帮我找AI学习文档，并总结内容，基于内容创建笔记记录\n  {创建笔记}意图提取：["创建AI学习笔记"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{创建笔记}>最相关的意图并总结提炼。\n  {创建笔记}意图提取:'
        link-url-inner: ''
        link-url-outer: ''
      - intention: '999'
        template: '# 任务\n  用户的输入的问题可能包含【普通对话、图片配文、文生图、智能抠图、图片动漫化、活照片、AI消除、AI头像、老照片修复、画质修复、妙云相机、搜图片、搜文档、搜视频、搜音频、搜文件夹、搜笔记、综合搜索、AI写真、活动搜索、功能搜索、发现广场搜索、创建笔记】等意图中的一个或多个\n 其中<{普通对话}>定义为：用于常规对话交互的意图\n\n  # 条件\n  - 问题需要简短，每个问题不少于6个字，但不超过20个字符 \n  - 只提取与<{普通对话}>相关的问题\n  - 只提取一个问题并保证问题的完整性\n\n 问题必须积极、合法、完整、有意义并且政治正确,输出严格按照以下数组格式：\n  ["问题"]\n\n  # 示例\n  输入：今天天气真好，搜一下好天气的照片\n  {普通对话}意图提取：["今天天气怎么样"]\n  输入：帮我找广州的图，以及查一下广州人口，以及打开老照片修复\n  {普通对话}意图提取：["广州人口有多少"]\n\n # 输入输出\n  输入: {%s}\n  思考: 用户的问题包括多个意图，我只需要从中提取一个与<{普通对话}>最相关的意图并总结提炼。\n  {普通对话}意图提取:'
        link-url-inner: ''
        link-url-outer: ''