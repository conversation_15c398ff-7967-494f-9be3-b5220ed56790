---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations: {}
  labels:
    # 修改应用名,若跟随节点部署要加上节点标识，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的D列或H列，应用模块的英文名
    app.name: yun-ai-api-outer-applet
  # 修改应用名,若跟随节点部署要加上节点标识，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的D列或H列，应用模块的英文名  
  name: yun-ai-api-outer-applet
  # 联调（研发）环境对应develop，测试环境对应test
  namespace: test
spec:
  progressDeadlineSeconds: 600
  # 如没有特殊需求，实例副本数为1即可
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      # 修改应用名,若跟随节点部署要加上节点标识，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的D列或H列，应用模块的英文名
      app.name: yun-ai-api-outer-applet
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        # 修改应用名,若跟随节点部署要加上节点标识，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的D列或H列，应用模块的英文名
        app.name: yun-ai-api-outer-applet
    spec:
      containers:
        # 按需修改环境变量，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的BC列
        - env:
            - name: ACTIVE_PROFILE
              value: test
          # 修改镜像，仓库组固定使用***********/k8s/；后面的模块名对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的D列或H列，应用模块的英文名；镜像版本以流水线执行后的镜像版本为准，是代码分支名+时间戳
          image: >-
            ***********/k8s/yun-ai-api-outer-applet:1.0
          imagePullPolicy: Always
          # 修改应用名,若跟随节点部署要加上节点标识，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的D列或H列，应用模块的英文名
          name: yun-ai-api-outer-applet
          # 容器资源根据实际情况修改，以下为4C8G的样例
          resources:
            # 修改资源限制
            limits:
              cpu: '1'
              memory: 8Gi
            # 修改资源需求
            requests:
              cpu: '1'
              memory: 8Gi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /logs
              name: logs
            - mountPath: /etc/localtime
              name: time-localtime
              readOnly: true
            - mountPath: /data
              name: volume-ai
              subPath: yun-ai-api-image
            ########### 有共享存储需求的模块需修改，具体修改内容请沟通确认，如已知不需要可移除
            ########### mountPath为挂载到容器中的目录；name为存储卷名称；subPath为文件存储中的目录，如果是两个不同应用需共享相同存储卷，要求两个模块的subPath配置的值需要一样，如果没有此需求的可以移除此行    
            # - mountPath: /data/xxx/yyy
            #   name: vol-pvc-example
            #   subPath: xxx/yyy
            ###########
            ########### 
      dnsPolicy: ClusterFirst
      # 联调（研发）环境、测试环境不需要指定主机标签
      # nodeSelector:
      #   xxx: true
      imagePullSecrets:
        - name: paas-***********-harbor-secret
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 60
      volumes:
        - hostPath:
            # 注意：联调（研发）环境的path值为/var/lib/docker/dev-logs，测试环境的path值为/var/lib/docker/logs，请注意修改
            path: /var/lib/docker/logs
            type: DirectoryOrCreate
          name: logs
        - hostPath:
            path: /etc/localtime
            type: ''
          name: time-localtime
        - name: volume-ai
          persistentVolumeClaim:
            claimName: pvc-kcs-test-nas-gz3-ypcg-1
        ########### 有共享存储需求的模块需修改，具体修改内容请沟通确认，如已知不需要可移除
        ########### name为存储卷名称；persistentVolumeClaim为pvc的名字，请沟通确认
        # - name: vol-pvc-example
        #   persistentVolumeClaim:
        #     claimName: pvc-nas-gz3-ypcg-p00X-XXX
        ###########
        ########### 
      dnsConfig:
        options:
          - name: single-request-reopen
          - name: timeout
            value: '1'
          - name: attempts
            value: '3'
          - name: ndots
            value: '2'

# 以下为service配置，如应用需要配置公网入口则需要写。若应用不需要被其他模块访问，如MQ消费模块、定时任务模块，可以移除以下内容
---
apiVersion: v1
kind: Service
metadata:
  annotations: {}
  labels:
    # 修改应用名,若跟随节点部署要加上节点标识，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的D列或H列，应用模块的英文名
    app.name: yun-ai-api-outer-applet
  # 修改为【应用名-svc】,若跟随节点部署应用名后要加上节点标识
  name: yun-ai-api-outer-applet-svc
  # 联调（研发）环境对应develop，测试环境对应test
  namespace: test
spec:
  ipFamilies:
    - IPv6
  ports:
    # 修改为应用名-port
    - name: yun-ai-api-outer-applet-port
      # 修改为应用端口，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的BB列
      port: 19027
      protocol: TCP
      # 修改为应用端口，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的BB列
      targetPort: 19027
  selector:
    # 修改应用名,若跟随节点部署要加上节点标识，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的D列或H列，应用模块的英文名
    app.name: yun-ai-api-outer-applet
  sessionAffinity: None
  type: ClusterIP


# 以下为ingress路由配置，如应用需要配置公网入口则需要写。若应用不需要被其他模块访问，如MQ消费模块、定时任务模块，可以移除以下内容
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations: {}
  labels:
    # 修改应用名,若跟随节点部署要加上节点标识，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的D列或H列，应用模块的英文名
    app.name: yun-ai-api-outer-applet
  # 修改为【应用标识-apisix】
  name: yun-ai-api-outer-applet-apisix
  # 联调（研发）环境对应develop，测试环境对应test
  namespace: test
spec:
  ingressClassName: apisix
  rules:
    # host命名规则为【模块名.环境.com】，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的BE列
    - host: yun-ai.test.com
      http:
        paths:
          - backend:
              service:
                 # 修改为【应用名-svc】
                name: yun-ai-api-outer-applet-svc
                port:
                  # 修改为应用端口，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的BB列
                  number: 19027
            # 修改为应用context-path
            path: /ai/api/outer
            pathType: Prefix
