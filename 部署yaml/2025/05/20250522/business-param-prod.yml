# 业务条件参数
business-param:
  # 历史对话列表（1.0和2.0）
  chatContentList:
    # 限制搜索X天内的历史记录
    queryMaxDays: 90
    # 业务类型查询条件映射
    businessTypeMap:
      # 针对邮箱webAI业务类型，配置展示云盘app、邮箱app、邮箱webAI渠道的历史记录
      "e-139mail-webai": [ "e-139mail-webai", "e-139mail-app", "e-mcloud-pc", "e-mcloud-app", "c-mcloud-app" ]
  # 历史会话列表查询V2接口
  assistantChatV2List:
    # 默认会话iconUrl
    defaultIconUrl: "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_100.png"
    # 会话iconUrl映射
    iconUrlMap:
      "100": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_100.png"
      "200": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_200.png"
      "300": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_300.png"
      "400": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_400.png"
      "40001": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_40001.png"
      "40002": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_40002.png"
      "40003": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_40003.png"
      "40004": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_40004.png"
      "40005": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_200.png"
      "40006": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_200.png"
      "40007": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_200.png"
# 对话业务配置
chat:
  text-tool:
    # ai ppt模块开关
    ai-ppt-open: true
    # 必须关闭的渠道列表（open=true也要拦截）
    ai-ppt-must-close-channels: ['101', '102', '103', '202', '401', '400']
    # 文本工具业务配置
    business:
      # aippt生成配置
      ai-ppt-generate:
        search-knowledge-enable: true
        personal-path: '/AI文件库/AI生成PPT'
        h5-preview-path: 'https://ai.yun.139.com/aippt/web/?taskid={taskid}&token={token}'
        pc-preview-path: 'https://ai.yun.139.com/aippt/web/?taskid={taskid}&token={token}'
        h5-ppt-path: 'https://ai.yun.139.com/aippt/web/?pptid={pptid}&token={token}'
        pc-ppt-path: 'https://ai.yun.139.com/aippt/web/?pptid={pptid}&token={token}'
  model:
    # 模型白名单列表
    model-white-list:
      # 开关为false且open-white-list不为空才校验
      - open: false
        model: deepseek_r1_671b
        # 该模型只有白名单用户能返回或设置模型
        open-white-list: [ "***********" ]