#测试开发使用同集群 生产不同  个人知识库在06 其他在05
elasticsearch:
  client:
    username: ypcg_ai_es_prod
    password: LOwYmmMnX\%Av,y/5C^0ZbQa
    host: *************
    port: 31255
    scheme: http
    commonKnowledgeIndexName: common_knowledge_rag_0
  clienttwo:
    username: ypcg_ai_es_prod
    password: LOwYmmMnX\%Av,y/5C^0ZbQa
    host: *************
    port: 31700
    scheme: http
    personalKnowledgeIndexName: personal_knowledge_rag_
    personalKnowledgeShards: 100
  clientthree:
    username: ypcg_ai_es_prod
    password: LOwYmmMnX\%Av,y/5C^0ZbQa
    host: *************
    port: 30561
    scheme: http
  image:
    #05集群 personal_photo_{0-49}
    client:
      username: ypcg_ai_es_prod
      password: LOwYmmMnX\%Av,y/5C^0ZbQa
      host: *************
      port: 31255
      scheme: http
      indexName: personal_photo_
      shards: 200
      partitionInterval: 99
      #07集群 personal_photo_{50-99}
    clienttwo:
      username: ypcg_ai_es_prod
      password: LOwYmmMnX\%Av,y/5C^0ZbQa
      host: *************
      port: 31795
      scheme: http
      #06集群 personal_photo_{100-149}
    clientthree:
      username: ypcg_ai_es_prod
      password: LOwYmmMnX\%Av,y/5C^0ZbQa
      host: *************
      port: 31700
      scheme: http
      #08集群 personal_photo_{150-199}
    clientfour:
      username: ypcg_ai_es_prod
      password: LOwYmmMnX\%Av,y/5C^0ZbQa
      host: *************
      port: 31115
      scheme: http