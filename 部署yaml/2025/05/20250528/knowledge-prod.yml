#知识库相关配置

#知识库
knowledge:
  # 个人知识库
  personal:
    # 文件大小限制（B）
    size: 209715200
    # 扩展名
    ext-list: [ "docx","xlsx","csv","txt","pptx","doc","xls","ppt","pdf","md" ]
    # 标签个数
    label-num: 50
    # 标签名字数
    label-name-len: 6

    # 转存任务过期时长（秒）
    transfer-expire-time: 86400
    # 删除任务过期时长（秒）
    delete-expire-time: 86400

    # 删除任务查询间隔时间（毫秒）
    delete-task-query-sleep: 5000
    # 删除任务查询次数（每次10秒钟）
    delete-task-query-times: 6

    # 解析失败原因（错误码对应的描述）
    parse-failed-reason:
      "0000": ""
      "9999": "哎呀，解析过程出了点小意外，请稍后再试试看"
      "10090027": "接口突然罢工啦，请稍后再试试看"
      "10090018": "文件空空如也，补充点有效内容再来试试吧"
      "10090026": "文档没有文字哦，补充点有效内容再来试试吧"
      "999999999": "该文件格式无法解析哦，去换一个格式再试试看吧"
      "105": "文件可能包含敏感内容哦，请重新检查后再试"
      "10070404": "哎呀，解析过程出了点小意外，请稍后再试试看"
      "10070405": "文件空空如也，补充点内容再来试试吧"
      "10070406": "文件可能包含敏感内容哦，请重新检查后再试"
      "10090030": "哎呀，解析过程出了点小意外，请稍后再试试看"
      "10000017": "哎呀，解析过程出了点小意外，请稍后再试试看"
      "01000012": "文件可能包含敏感内容哦，请重新检查后再试"
      "10000015": "文件内容字数太多哦，精简内容后再试试看吧"
      "10090015": "该文件大小超限了哦，去换一个大小再试试看吧"
      "10000019": "该文件格式无法解析哦，去换一个格式再试试看吧"
      "10000006": "该文件大小无法解析哦，去换一个大小再试试看吧"
      "01000005": "文件空空如也，补充点有效内容再来试试吧"
      "10090039": "该文件无法解析哦，去换一个文档再试试看吧"
      "10030407": "网页地址不正确"
      "10030408": "目前仅支持识别微信公众号/小红书/知乎/百度贴吧链接"
      "10090014": "哎呀，解析过程出了点小意外，请稍后再试试看"

    #个人知识库2.0名称最大长度
    name-length: 25
    #个人知识库2.0描述最大长度
    description-length: 150
    #个人知识库2.0创建知识库最大数量
    create-max-num: 100
    #个人知识库2.0知识库名称和描述送审开关
    check-content-switch: true

    #个人知识库头像信息列表
    profile-photo-list:
      - type: 1
        photoId: "man_1"
        url: "https://yun.mcloud.139.com/aiassistant/static/knowledge/head/man_1.png"
      - type: 1
        photoId: "man_2"
        url: "https://yun.mcloud.139.com/aiassistant/static/knowledge/head/man_2.png"
      - type: 1
        photoId: "man_3"
        url: "https://yun.mcloud.139.com/aiassistant/static/knowledge/head/man_3.png"
      - type: 1
        photoId: "man_4"
        url: "https://yun.mcloud.139.com/aiassistant/static/knowledge/head/man_4.png"
      - type: 1
        photoId: "man_5"
        url: "https://yun.mcloud.139.com/aiassistant/static/knowledge/head/man_5.png"
      - type: 1
        photoId: "female_1"
        url: "https://yun.mcloud.139.com/aiassistant/static/knowledge/head/female_1.png"
      - type: 1
        photoId: "female_2"
        url: "https://yun.mcloud.139.com/aiassistant/static/knowledge/head/female_2.png"
      - type: 1
        photoId: "female_3"
        url: "https://yun.mcloud.139.com/aiassistant/static/knowledge/head/female_3.png"
      - type: 1
        photoId: "female_4"
        url: "https://yun.mcloud.139.com/aiassistant/static/knowledge/head/female_4.png"
      - type: 1
        photoId: "female_5"
        url: "https://yun.mcloud.139.com/aiassistant/static/knowledge/head/female_5.png"

    #知识库上传html合法域名白名单
    htmlUrlLegalHosts:
      - mp.weixin.qq.com
      - xiaohongshu.com
      - zhihu.com
      - tieba.baidu.com
      - xhslink.com

  # 知识库对话
  dialogue:
    # 指定使用的公共知识库的标识
    knowledge-base-id: "common"
    # 公共知识库白名单
    white-list:
      - "13802885259"
      - "13802883435"
      - "13802885171"
      - "18928819103"
      - "18771000041"
      - "13926431390"
      - "13542899545"
      - "15521090847"
      - "13425458504"
      - "19802021524"
      - "13802885451"
      - "18316688152"
      - "15113990046"
      - "13570559600"
      - "19802025024"
      - "19802021105"
      - "19802021150"
      - "19802021522"
      - "19802021782"
      - "19802021799"
      - "13802885450"
      - "19802025559"
      - "13750326098"
      - "15219891695"
      - "18002239030"
      - "15013271046"
      - "13078882662"
      - "13802882416"
      - "18142851953"
      - "13326349056"
      - "13977527302"
      - "13246408473"
      - "13710660941"
      - "13250164151"
      - "13802885271"
      - "17846876519"
      - "13557589652"
      - "15811852975"
      - "19802024110"
      - "13802885236"
      - "15992549826"
      - "15078267817"
      - "15011750751"
      - "17825902628"
      - "15913080189"
      - "18884581208"
      - "18884681208"
      - "18902224594"
      - "15507829621"
      - "13877725304"
      - "16676302621"
      - "13501525064"
      - "13750326098"
      - "18002239030"
      - "15014317558"
      - "19120129031"
      - "19849977090"
      - "13528277450"
      - "19925811004"
      - "15216248520"
      - "18249907653"
      - "13632481841"
      - "15820472203"
      - "13802885432"
      - "13802885115"
      - "19802021462"
      - "13826074981"
      - "15531012900"
      - "17701952898"
      - "13580574830"
      - "13631272874"
      - "18327863481"
      - "13802885331"
      - "13533795240"
      - "13777490248"

    # 个人知识库开关
    personal-switch: true
    # 知识库名词库开关
    enable-noun-library: false
    # 多路重排开关
    enable-multi-rerank: true

    # 开头文案，编码对应枚举KnowledgeBaseEnum
    title-map:
      # 只命中个人知识库
      personal: "根据你的知识库："
      # 只命中公共知识库
      common: "根据我获取的知识："
      # 命中个人知识库和公共知识库
      knowledge: "根据我获取的知识及你的知识库："
      # 没有命中知识库，大模型有联网能力
      support-network: "当前知识库文件中未找到相关内容，已为你自动联网搜索"
      # 没有命中知识库，大模型无联网能力
      unsupport-network: "当前知识库文件中未找到相关内容，已用大模型为你处理"

    # 可用知识库的渠道
    enable-channel-list: [ 10175, 10104, 10112 ]

    # 重写配置
    rewrite-config:
      # 重写开关
      enabled: true
      # 重写使用的大模型
      model-code: "qwen_32b"
      # 重写输入内容系统提示
      system-prompt: "你是一个旨在帮助用户更有效检索信息的助手。\n你的主要职责是在用户输入表达不明确的情况下，通过参考#历史对话摘要#和#关键词列表#，对原始问题进行重写。\n你的目标是使问题更加具体和容易被检索，并保持与用户原始意图的一致性\n并且，请不要忽略#原始用户问题#中的内容\n你应该1. 理解背景: 通过阅读历史对话摘要，了解用户之前的对话内容，把握对话的上下文和主题。\n2. 利用关键词: 将关键词融入问题，确保#重写后的问题#包含这些关键词，提高检索的相关性。\n3. 增加细节: 如果用户的问题过于宽泛或模糊，适当添加细节使问题更具体，但不要引入新的概念或信息。\n4. 保持一致性: 确保#重写后的问题#不偏离用户原始的意图或信息需求。\n5. 简洁明了: 保持问题简短而明确，避免冗长或复杂的表述。\n#重写后的问题#只能在#原始用户问题#的基础上增加10-20个字\n#原始用户问题#，#重写后的问题#，#历史对话摘要#，#关键词列表#都不允许出现在#重写后的问题#中\n#历史对话摘要#:{history}\n#关键词列表#:{keywords}"
      # 重写输入内容用户提示
      user-prompt: "#原始用户问题#:{query}\n#重写后的问题#:"
      #问题重写最大字数
      maxLength: 128
      #历史问题数量
      historyNum: 5

    # 关键字提取配置
    keyword-config:
      # 关键字开关
      enabled: true
      # 键词提取方法，支持"tfidf"、"textrank"和"llm"，默认为"tfidf"；"llm"当前版本不可用
      method: "tfidf"
      # 最大提取关键词数量，默认为-1（-1即返回所有关键词）
      maxKeywords: -1
      # 重写输入内容用户提示
      allowPos: ["n", "nr", "ns", "nt", "nz", "vn"]
      #最小词长度，默认为2
      minWordLen: 2

    # 召回配置
    recall-config:
      # 召回超时时间，单位毫秒（默认10秒）
      await-time: 20000
      # 结果日志拆分打印，每组数量
      log-group-size: 4
      # 【公共知识库】base配置
      common-base:
        # 启用开关，true-打开（默认），false-关闭
        enabled: true
        # 向量大切片数量
        big-split-size: 10
        # 上分片数
        pre-count: 1
        # 下分片数
        next-count: 1
        # 需要合并上下分片开关，true-打开，false-关闭
        enabled-merge-context: false
      # 【个人知识库】base配置
      personal-base:
        # 启用开关，true-打开（默认），false-关闭
        enabled: true
        # 向量大切片数量
        big-split-size: 40
        # 上分片数
        pre-count: 1
        # 下分片数
        next-count: 1
        # 需要合并上下分片开关，true-打开，false-关闭
        enabled-merge-context: false

      # 【公共知识库】切片配置
      common-split:
        # 公共知识库id
        common-base-id: "common"
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: true
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false
        # small to big 配置
        small-to-big-config:
          enabled: true
          min-score: 0.1
          top-n: 100
          merge-size: 2

      # 【公共知识库】问答配置
      common-qa:
        # 公共知识库id
        common-base-id: "common"
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【公共知识库】切片的假设性问题配置
      common-split-gqa:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 300
        # 问答对，查询条数
        qa-top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【公共知识库】语义切块配置
      common-gsplit:
        # 公共知识库id
        common-base-id: "common"
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【公共知识库】文档总结配置
      common-summary:
        # 公共知识库id
        common-base-id: "common"
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【公共知识库】text查询配置
      common-text:
        # 公共知识库id
        common-base-id: "common"
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: true
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【个人知识库】切片配置
      personal-split:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: true
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false
        # small to big 配置
        small-to-big-config:
          enabled: true
          min-score: 0.1
          top-n: 100
          merge-size: 2

      # 【个人知识库】切片假设性问题配置
      personal-split-gqa:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 300
        # 问答对，查询条数
        qa-top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【个人知识库】语义切片配置
      personal-gsplit:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【个人知识库】文档总结配置
      personal-summary:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【个人知识库】text查询配置
      personal-text:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: true
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【个人知识库】keyword查询配置
      personal-keyword:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: true
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

    # 召回配置（总结、建议、发言）
    recall-config-summary:
      # 召回超时时间，单位毫秒（默认10秒）
      await-time: 20000
      # 结果日志拆分打印，每组数量
      log-group-size: 4
      # 【公共知识库】base配置
      common-base:
        # 启用开关，true-打开（默认），false-关闭
        enabled: true
        # 向量大切片数量
        big-split-size: 10
        # 上分片数
        pre-count: 1
        # 下分片数
        next-count: 1
        # 需要合并上下分片开关，true-打开，false-关闭
        enabled-merge-context: false
      # 【个人知识库】base配置
      personal-base:
        # 启用开关，true-打开（默认），false-关闭
        enabled: true
        # 向量大切片数量
        big-split-size: 80
        # 上分片数
        pre-count: 1
        # 下分片数
        next-count: 1
        # 需要合并上下分片开关，true-打开，false-关闭
        enabled-merge-context: false

      # 【公共知识库】切片配置
      common-split:
        # 公共知识库id
        common-base-id: "common"
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: true
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false
        # small to big 配置
        small-to-big-config:
          enabled: true
          min-score: 0.1
          top-n: 100
          merge-size: 2

      # 【公共知识库】问答配置
      common-qa:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false

      # 【公共知识库】切片的假设性问题配置
      common-split-gqa:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false

      # 【公共知识库】语义切块配置
      common-gsplit:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false

      # 【公共知识库】文档总结配置
      common-summary:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false

      # 【公共知识库】text查询配置
      common-text:
        # 公共知识库id
        common-base-id: "common"
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: true
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【个人知识库】切片配置
      personal-split:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: true
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 200
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false
        # small to big 配置
        small-to-big-config:
          enabled: true
          min-score: 0.1
          top-n: 200
          merge-size: 2

      # 【个人知识库】切片假设性问题配置
      personal-split-gqa:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false

      # 【个人知识库】语义切片配置
      personal-gsplit:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false

      # 【个人知识库】文档总结配置
      personal-summary:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false

      # 【个人知识库】text查询配置
      personal-text:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: true
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【个人知识库】keyword查询配置
      personal-keyword:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: true
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false


    # 【默认】算法重排配置
    rerank-config:
      # 批处理大小，默认256
      batch-size: 256
      # 分段内容的最大长度，超出会截断，默认512
      max-length: 1024
      # 结果日志拆分打印，每组数量
      log-group-size: 4
      # 重排返回条数
      top-n: 20
      # 重排后最小评分
      min-score: 0.3
      # 重排后返回分块字符总长度：25600（默认）
      text-max-length: 25600

    # 【默认】算法重排配置（总结、建议、发言）
    rerank-config-summary:
      # 批处理大小，默认256
      batch-size: 256
      # 分段内容的最大长度，超出会截断，默认512
      max-length: 1024
      # 结果日志拆分打印，每组数量
      log-group-size: 4
      # 重排返回条数
      top-n: 20
      # 重排后最小评分
      min-score: 0.3
      # 重排后返回分块字符总长度：25600（默认）
      text-max-length: 25600

    # 【向量】算法重排配置
    vector-rerank-config:
      # 批处理大小，默认256
      batch-size: 256
      # 分段内容的最大长度，超出会截断，默认512
      max-length: 1024
      # 结果日志拆分打印，每组数量
      log-group-size: 4
      # 重排返回条数
      top-n: 40
      # 重排后最小评分
      min-score: 0.1
      # 重排后返回分块字符总长度：25600（默认）
      text-max-length: 25600

    # 【向量】算法重排配置（总结、建议、发言）
    vector-rerank-config-summary:
      # 批处理大小，默认256
      batch-size: 256
      # 分段内容的最大长度，超出会截断，默认512
      max-length: 1024
      # 结果日志拆分打印，每组数量
      log-group-size: 4
      # 重排返回条数
      top-n: 80
      # 重排后最小评分
      min-score: 0.1
      # 重排后返回分块字符总长度：25600（默认）
      text-max-length: 25600

    # 【全文】算法重排配置
    text-rerank-config:
      # 批处理大小，默认256
      batch-size: 256
      # 分段内容的最大长度，超出会截断，默认512
      max-length: 1024
      # 结果日志拆分打印，每组数量
      log-group-size: 4
      # 重排返回条数
      top-n: 15
      # 重排后最小评分
      min-score: 0.03
      # 重排后返回分块字符总长度：25600（默认）
      text-max-length: 25600

    # 【全文】算法重排配置（总结、建议、发言）
    text-rerank-config-summary:
      # 批处理大小，默认256
      batch-size: 256
      # 分段内容的最大长度，超出会截断，默认512
      max-length: 1024
      # 结果日志拆分打印，每组数量
      log-group-size: 4
      # 重排返回条数
      top-n: 15
      # 重排后最小评分
      min-score: 0.03
      # 重排后返回分块字符总长度：25600（默认）
      text-max-length: 25600

    # 【关键字】算法重排配置
    keyword-rerank-config:
      # 批处理大小，默认256
      batch-size: 256
      # 分段内容的最大长度，超出会截断，默认512
      max-length: 1024
      # 结果日志拆分打印，每组数量
      log-group-size: 4
      # 重排返回条数
      top-n: 15
      # 重排后最小评分
      min-score: 0.03
      # 重排后返回分块字符总长度：25600（默认）
      text-max-length: 25600

    # 【关键字】算法重排配置（总结、建议、发言）
    keyword-rerank-config-summary:
      # 批处理大小，默认256
      batch-size: 256
      # 分段内容的最大长度，超出会截断，默认512
      max-length: 1024
      # 结果日志拆分打印，每组数量
      log-group-size: 4
      # 重排返回条数
      top-n: 15
      # 重排后最小评分
      min-score: 0.03
      # 重排后返回分块字符总长度：25600（默认）
      text-max-length: 25600

    # 重排结果相关性配置
    relevancy-config:
      # 相关性功能开关，true-启用，false-停用
      enabled: false
      # 相关性使用的模型编码
      model-code: "qwen"
      # 相关性用户输入的模板
      user-prompt: "#任务描述\n你是一个判断助手，需要判断每个文本块在回答问题时是否有用。\n\n#输入格式\n- 用户问题：一个问题\n- 文本块列表：{textSize}个文本块\n\n#输出要求\n1. 必须返回一个包含{textSize}个布尔值的列表\n2. True表示这个文本块对回答问题有用\n3. False表示这个文本块对回答问题没用\n4. 返回顺序必须和文本块顺序一致\n\n#示例\n输入：\n用户问题：\"什么是太阳？\"\n文本块列表：[\n    \"太阳是一颗恒星\",\n    \"月亮是地球的卫星\",\n    \"太阳提供光和热\"\n]\n\n输出：\n[True,False,True]\n\n#注意事项\n- 只返回布尔值列表\n- 不要包含任何解释\n- 不要包含任何标点符号\n- 列表长度必须是{textSize}\n\n#实际输入\n用户问题：{query}\n文本块列表：{texts}"
      # 相关性大模型参数配置
      text-model-config:
        # 大模型温度参数
        temperature: 0.0
        # 大模型top_p参数
        top-p: 0.1
        # 随机种子（-1表示不传值）
        seed: 1234

    # 大模型对话配置
    dialogue-config:
      # 模型编码
      model-code: "blian"
      # 大模型系统提示词（role:system）
      system-prompt: ""
      # 大模型输入内容（role:user）
      user-prompt: ""
      # 大模型参数配置
      text-model-config:
        # 大模型温度参数
        temperature: 0.1
        # 大模型top_p参数
        top-p: 0.9
        # 随机种子（-1表示不传值）
        seed: -1

    # 数据搜索超时配置（秒）
    search-timeout: 30

    # 政治人物
    politician-enabled: false
    politician-list:
      - name: "习近平"
        position: "中国共产党中央委员会总书记，中共中央军事委员会主席，中华人民共和国主席，中华人民共和国中央军事委员会主席"
        sort: 50000
        replace-info-list:
          - { replace: "习近平", honorific: "习主席" }
      - name: "李强"
        position: "中共二十届中央政治局常委，国务院总理、党组书记"
        sort: 49900
        replace-info-list:
          - { replace: "李强",honorific: "李总理" }
      - name: "赵乐际"
        position: "中共二十届中央政治局常委，十四届全国人大常委会委员长"
        sort: 49800
        replace-info-list:
          - { replace: "赵乐际", honorific: "赵委员长" }
      - name: "王沪宁"
        position: "中共二十届中央政治局常委，十四届全国政协主席，中央全面深化改革委员会办公室主任"
        sort: 49700
        replace-info-list:
          - { replace: "王沪宁", honorific: "王主席" }
      - name: "蔡奇"
        position: "中央政治局常委、中央书记处书记，中央办公厅主任、中央和国家机关工委书记"
        sort: 49600
        replace-info-list:
          - { replace: "蔡奇", honorific: "蔡书记" }
      - name: "丁薛祥"
        position: "中共二十届中央政治局常委，国务院副总理、党组副书记"
        sort: 49500
        replace-info-list:
          - { replace: "丁薛祥", honorific: "丁书记" }
      - name: "李希"
        position: "中央政治局常委，中央纪律检查委员会书记"
        sort: 49400
        replace-info-list:
          - { replace: "李希", honorific: "李书记" }
      - name: "杨杰"
        position: "董事长"
        sort: 49300
        replace-info-list:
          - { replace: "杨杰", honorific: "杨董事长" }
      - name: "李丕征"
        position: "党组副书记"
        sort: 49200
        replace-info-list:
          - { replace: "李丕征", honorific: "李书记" }
      - name: "李慧镝"
        position: "副总经理"
        sort: 49100
        replace-info-list:
          - { replace: "李慧镝", honorific: "李副总经理" }
      - name: "杨强"
        position: "副总经理"
        sort: 49099
        replace-info-list:
          - { replace: "杨强", honorific: "杨副总经理" }
      - name: "高同庆"
        position: "副总经理"
        sort: 49098
        replace-info-list:
          - { replace: "高同庆", honorific: "高副总经理" }
      - name: "何飚"
        position: "副总经理"
        sort: 49097
        replace-info-list:
          - { replace: "何飚", honorific: "何总经理" }
      - name: "童腾飞"
        position: "纪检监察组组长"
        sort: 49096
        replace-info-list:
          - { replace: "童腾飞", honorific: "童组长" }
      - name: "张冬"
        position: "副总经理"
        sort: 49095
        replace-info-list:
          - { replace: "张冬", honorific: "张副总经理" }
      - name: "张迎新"
        position: "副总经理"
        sort: 49094
        replace-info-list:
          - { replace: "张迎新", honorific: "张副总经理" }
      - name: "李荣华"
        position: "总会计师"
        sort: 49093
        replace-info-list:
          - { replace: "李荣华", honorific: "李总会计师" }
      - name: "陈怀达"
        position: "副总经理"
        sort: 49092
        replace-info-list:
          - { replace: "陈怀达", honorific: "陈副总经理" }
      - name: "程建军"
        position: "副总经理"
        sort: 49091
        replace-info-list:
          - { replace: "程建军", honorific: "程副总经理" }
      - name: "梁高美懿"
        position: "独立非执行董事"
        sort: 49090
        replace-info-list:
          - { replace: "梁高美懿", honorific: "梁高董事" }

  # 专属智能体公共知识库
  vip-common:
    # 个人知识库开关
    vip-common-switch: true
    # 专属智能体公共知识库的标识
    knowledge-base-id: "VipExclusiveIntelligentAgent"
    # 公共知识库白名单（手机号）
    white-list: [ "15012773384" ]

  # 数字峰会知识库配置
  digital-summit:
    # 白名单（手机号）
    white-list: [ "13710660941", "19512885508", "13246408473", "18085610998", "13422128481" ]
    # 命中后的key
    query-keys: [ "个人参展总结,参展总结,个人参展报告,参展报告" ]
    # 文件id列表
    file-ids: [ "Fne1yuJL97O5lMjrSNDxDdYhd19adffhu","FpsM5bHM0To4QtCknmPtB4bNznAJ8tFoO","FrMe2KwQMpq44IuwchrFIgLl3E0ULjLIu","Fn4zLCSkmLJU0iMd2_kZHIYo8bmcmpjgV","FvUeVo1PxteFH3cB_8BBB4Kj3MM1ccaFh","Ftc5lep4iO7q3x-G851hBu6B-wBSRoi86","FjVfY1IV4risLy_M7vw1JfJjnfbqK-Lqr","FpMWS7sXbNI8w9XUlAnhBZbY0PgUMWziP","FjPZSETW5PjYqzT258i5Jbp4H_vr8OTI2","FuTAjBUlDMn812THTfilKxavBj-6Awz5_","FtmevfBi0EzOMugc31QZJBKSWA5fRNB8z","FiXYiE2V9LfV4rk1AfoNJDJuHjvis_SH1","FgWeoCrwx7Cb2OHiWNTZLdpOWBOF1seAm","FisBgfEARBmhX-ZNC-OhPbJgxzJeJkQpo","FvS6iBEZAFV4q975_uURMsK8NU5UbQB9e","FpnwEHKgEpRuHRh-5bBJNU7Rf9Y31BK8b","Fidz7VD_tHAHBgLSu1R1G6pv_CsVi7RYB","FvdkU0YpgR0NavNuJE7dNjq_65UDXYE1D","FkJVM8E4jvFRvaaK8hpZGtYK2vWETI7ZU","FqI_D_mg5-oRFlnfZT2ZAB7qsMm81OfCE","FrplFgyt13Jwt_wNhu2FJXry6tBJ2ddac","FviDkeWnMmgQcpXU5qxtLbq0zNvkOTJCE","Fpq_q48QEAkWpZqnj_lpLwrW8OGOjhAjF","FhWAarE4vfVPQrthAOvhBaJZzyCwpr3fT","FjajVdwxIF0XcuqAyYwNBmp67B_dryB3F","Fible6gnwCsoxfe194L9G_5qqjGpucABK","FrxbJ74iEsxkFLzdpwCdL9LwFG2_vBLmZ","FrbjTPG6AQxbfgtpQ-4pJYb6rAbwJAEmW","Fp7AmJM63Fx3Eows7RDVOeLey4LHVtxMd","FkEzu3F5cdsJ-bZ79k8BGv4BOKElFXHLC","FmWjb_Dk3kqKUFQ4B5lVKPIlqHWkiN5ai","Fl7zfnNYuapJ0gSbCbJNPHIe-GQnNLm6S","Fila7Ny40Bhz3Ys9RPolAt5pUfaI1NAIc","FnXAs89kOcQxcV3ujy2RC2I1y6mbCDnUM","FmQwuRpnMxIDoSmQUEEdJcYkO6NOCzMCA" ]
    # 重排后返回条数
    top-n: 100
    # 重排后最小评分
    min-score: -1
    # 重排后返回分块字符总长度
    text-max-length: 20000
    # 提示词
    user-prompt: "# 角色强化指令\n你是一个严格遵守信息处理规范的专业智能助手，一定要特别注重行政人物排序、时间线逻辑和信息精简性。回答一定要呈现清晰的信息层级和严格的格式规范。\n\n\n## 知识回答要求\n1. 依据知识库，拆解用户的问题，提供完整的答案，确保覆盖用户问题的所有方面，不遗漏知识库中的关键信息（特别是联系方式、网址、报名方式等重要细节）\n2. 用户问题的关键字与关键概念，如果知识库中未直接提及，一定要明确指出“知识库中并未提及xxx”，即使相近或可能相关，也要明确指出”未提及“，不能用肯定口吻回答\n3. 优先使用知识库中的表述方式，问题答案必须明确出现在知识库中，且回答内容完全来自知识库\n4. 涉及数值型内容需确保与文档完全一致\n5. 构建回答时，确保包含所有必要的关键词，提高关键词评分(scoreikw)。\n6. 保持回答与原文的语义相似度，以提高向量相似度评分(scoreies)。\n7. 确保提取知识库中的所有相关信息，可适当对原文进行凝练\n8. 控制回答长度，回答应简洁、准确、完整，直接解答问题，避免不必要的解释，如果是有直接答案，则直接输出直接答案，不需要再延伸回答。\n9. 需要判断用户的问题与知识库检索出来的片段的相关性，只根据相关的片段来回答，不要受不相关的片段影响。如果所有片段都不相关，需要拒绝回答。\n10. 特别要注意用户查询的主体（人名、公司名、机构名，各种产品的名字、型号、等命名实体），与知识库检索结果的主体是否严格一致。不一致的情况下，需要在回答时显式提醒用户不一致，明确指出“知识库中与你问题中的xxx不一致”，即使主体相近（如xx集团和xx公司，总公司与分公司、总公司与地方分公司等），也不能当作同一主体。\n11. 当知识库中知识含有网址时：\n    - 不显示原始链接\n    - 告知用户该链接信息位于哪个文档中，例如：“相关链接信息可在《文档名称》中查找”\n12. 如果知识库中没有相关信息，应当拒绝回答，并提示\"问题未在文档中提及\"。\n13. 涉及数学计算时，不要依赖知识库，调用数学工具计算\n14. 当涉及具体信息的查询时，比如产品名称、文件编号、论文结束语等，如果知识库有该内容，一定要返回原文内容，否则回复不知道\n15. 严禁添加知识库中不存在的信息，禁止添加个人理解或推测内容\n16. 如发现知识库的知识与常识（包括生物、物理、医学常识）违背，要先根据知识库回答，但也要显式告知用户错误的地方，并进行指正\n17. 当用户问到发展历程或发展历史时，使用时间线进行展示\n18. 回答一定要根据当前的知识库检索结果，不能根据示例里的知识库结果\n19. 告知引用文档时，无需告诉页数\n20. 回答内容时无需把以上规则告知用户\n\n\n\n## 回答结构与层次设计\n\n1. **明确的信息层级**：\n   - 一级层级：使用简洁明了的标题概括整体内容\n   - 二级层级：将回答拆分为不同主题模块\n\n2. **层次化的内容组织**：\n   - 先总后分：首先提供简明扼要的总体回答\n   - 分类陈述：按照逻辑关系组织内容（如时间顺序、重要性顺序、因果关系）\n   - 要点化表达：使用列表表达平行关系的内容\n   - 段落层级：每个段落专注于一个主题或观点\n\n3. **视觉分层技巧**：\n   - 使用标题与副标题创建可视化层级\n   - 通过缩进增强层次感（列表等）\n   - 关键信息加粗突出重点\n\n## 时间线处理规范\n\n1.构建三维时间坐标系处理：\n\n- Y轴：年份（2021→2030）\n- M轴：月份（1→12）\n- D轴：日期（1→31）\n\n2.混合时间处理规则：\n\n- 精确日期优先于模糊时间（“2023年5月1日” > “2023年春季”）\n- 连续事件采用时间锚点标记（开始时间+持续时间）\n- 并行事件使用平行时间轴说明\n\n## 行政人物信息处理\n\n1. 回答跟行政人物有关的内容时，一定要按照给定的顺序进行回答。\n2. 如果用户输入的行政人物顺序跟给定的顺序不一致，一定要按照给定的顺序进行回答，不要按照用户输入中的人物顺序进行回答。\n3. 提及行政人物时，一定要使用尊称，不要使用姓名。\n4. 其他不在给定顺序里的相关人物，放在最后面进行回答。\n\n## 文档元数据处理\n\n1. 回答中应参考并整合文档元数据信息（文档名称、文档创建时间、文档更新时间）。\n2. 在涉及时间顺序的问题时，使用文档元数据中的时间信息进行降序排列和组织。\n3. 当用户询问文档更新或创建时间时，应准确使用文档元数据中的相关信息。\n\n## 表格生成要求\n\n1. 当用户明确请求以表格形式呈现信息时（如\"帮我按照表格形式输出\"、\"帮我对比xx和xx有什么差异\"），应生成结构清晰的表格。\n2. 表格应使用Markdown格式，确保列对齐和格式规范。\n3. 表格应尽量放置在回答的最后部分，以保持文档结构清晰。\n4. 表格内容应简洁明了，突出关键差异和重要信息。\n5. 表格应包含清晰的表头和适当的列宽，便于信息对比和阅读。\n\n## 链接处理规则\n\n1. 不要在回答中显示任何URL链接，无论是知识库中的还是通用知识中的.\n2. 当知识库中包含链接时：\n   - 不显示链接\n   - 告知用户“相关链接信息可在《文档名称》中查找”\n\n## 展会总结处理规则\n\n如果用户需要撰写一份展会总结，内容应结构清晰，涵盖如下部分：\n\n1. **展会情况**  \n   - 简要介绍展会的基本信息，包括展会名称、举办时间、地点、主办方、参展主题及亮点等。\n\n2. **会议记录**  \n   - 概述本次展会中获取的行业最新信息、趋势分析、重要演讲/论坛内容等。  \n   - 梳理与行业相关的深度交流、会谈要点、重要嘉宾发言等。\n\n3. **商机线索**  \n   - 汇总展会期间发现的潜在合作伙伴或客户。  \n   - 列举具体的合作意向沟通记录，包括意向客户/企业、沟通内容、初步合作方向等。\n\n4. **待办事项（后续跟进计划）**  \n   - 明确后续需要重点跟进的事项，如二次洽谈、资料补充、方案制定等。  \n   - 指定责任人和预计完成时间。\n\n\n## 示例\n### 示例1：简单链接处理\n\n知识库：\n<文档片段><片段><文档名称>《云盘使用手册》</文档名称><片段内容><内容>在云盘app首页选择【手机备份】，开启自动备份相册、通讯录、微信.具体教程可访问https://www.baidu.com/</内容></片段内容><文档创建时间>2024-03-01T09:00:00Z</文档创建时间><文档更新时间>2024-03-01T09:00:00Z</文档更新时间></片段></文档片段>\n\n用户输入：自动备份的教程在哪里?\n\n正确回答：\n\n## 手机自动备份教程\n\n关于手机自动备份的详细教程，您可以参考以下信息：\n\n1. 在云盘app首页选择【手机备份】\n2. 开启自动备份相册、通讯录、微信\n\n详细的自动备份教程文档位于《云盘使用手册》中，该文档包含完整的图文教程，可帮助您更好地设置自动备份功能。\n\n### 示例2：多文档链接处理\n\n知识库：\n<检索结果>\n  <文档>\n    <文档名称>《产品文档中心》</文档名称>\n    <文档片段>\n      <片段>产品使用手册可在官网下载:https://www.baidu.com/.技术支持请访问:https://www.baidu.com/.常见问题解答:https://www.baidu.com/</片段>\n    </文档片段>\n    <文档创建时间>2024-03-01T09:00:00Z</文档创建时间>\n    <文档更新时间>2024-03-01T09:00:00Z</文档更新时间>\n  </文档>\n  <文档>\n    <文档名称>《技术支持指南》</文档名称>\n    <文档片段>\n      <片段>详细的技术支持信息请访问:https://www.baidu.com/</片段>\n    </文档片段>\n    <文档创建时间>2024-03-01T10:00:00Z</文档创建时间>\n    <文档更新时间>2024-03-01T10:00:00Z</文档更新时间>\n  </文档>\n  <文档>\n    <文档名称>《FAQ文档》</文档名称>\n    <文档片段>\n      <片段>常见问题解答请访问:https://www.baidu.com/</片段>\n    </文档片段>\n    <文档创建时间>2024-03-01T11:00:00Z</文档创建时间>\n    <文档更新时间>2024-03-01T11:00:00Z</文档更新时间>\n  </文档>\n</检索结果>\n\n用户输入：我想了解更多产品资料和技术支持\n\n正确回答：\n\n## 产品资料与技术支持\n\n根据您的需求，以下是相关信息：\n\n1. **产品使用手册**：完整的产品使用手册可在《产品文档中心》找到\n2. **常见问题解答**：常见问题的详细解答可在《FAQ文档》中查阅\n3. **技术支持资源**：技术支持相关信息位于《技术支持指南》中\n\n### 示例3：纯事实信息处理\n\n知识库：\n<文档片段>\n  <片段>\n    <文档名称>《2024年节假日安排》</文档名称>\n    <片段内容>\n      <内容>2024年春节假期为2月10日至17日,共8天.元宵节是在农历正月十五.</内容>\n    </片段内容>\n    <文档创建时间>2024-01-01T09:00:00Z</文档创建时间>\n    <文档更新时间>2024-01-01T09:00:00Z</文档更新时间>\n  </片段>\n</文档片段>\n\n用户输入：2024年春节放假多少天?\n\n正确回答：\n\n2024年春节假期共8天。\n\n### 示例4：知识库为空时的回答\n\n知识库：\n<检索结果>\n</检索结果>\n\n用户输入：什么是人工智能?\n\n正确回答：\n关于这个问题，在知识库中没有找到相关资料，我无法回答这个问题。\n\n\n### 示例5：知识库内容与用户问题不相关的回答\n\n知识库：\n<检索结果><文档><文档名称>《2021小米公司发展历程》</文档名称><文档片段><片段>2021 12月\n12月28日雷军宣布未来五年，小米研发投入提高到超1000亿元.\n12月28日小米12系列发布，首次双尺寸双高端同发，搭载自研\"小米澎湃P1\"\n3月30日小米宣布正式进军智能电动汽车领域，未来十年投入100亿美元，首期投入100亿元人民币，雷军将亲自挂帅，为小米汽车而战.\n</片段>\n</文档片段><文档创建时间>2025-04-02T15:17:09Z</文档创建时间><文档更新时间>2025-04-02T15:17:09Z</文档更新时间></文档></检索结果>\n\n用户输入：2021横琴小米科技发展有限公司发展包括了哪些历程?\n\n正确回答：\n关于这个问题，在知识库里没有找到“2021横琴小米科技发展有限公司”相关资料，我无法回答你的问题，但找到了“小米公司”相关资料，可能是相关内容，以下供你参考：......\n\n##### 示例说明\n横琴小米科技发展有限公司是小米公司子公司，与知识库检索返回的内容“小米公司”不一致,需要提醒用户，并用“以下供您参考”的语句，然后再展开回答。对于这种情况，回答时用概括性语言。\n\n\n\n### 示例6：复杂时间线处理\n\n知识库：\n<检索结果><文档><文档名称>《2021小米公司发展历程》</文档名称><文档片段><片段>2021 12月\n12月28日雷军宣布未来五年，小米研发投入提高到超1000亿元.\n12月28日小米12系列发布，首次双尺寸双高端同发，搭载自研\"小米澎湃P1\"充电管理芯片\n11月\n11月22日MIUI全球月活用户突破5亿.\n10月\n10月31日小米之家门店规模正式突破1万家，这是小米推进新零售发展的新里程碑.\n08月\n8月10日小米宣布向18.46万小米手机1代首批用户，每人赠送 1999元红包，以回馈米粉支持.\n8月2日小米连续三年入选2021《财富》世界500强，排名第338位，较去年上升84位.\n07月\n7月16日据Canalys 第二季度全球智能手机市占率排名，小米手机销量超越苹果，首次晋升全球第二.\n7月6日小米集团向技术专家、新十年创业者计划首批入选者、中高层管理者等 122人，奖励 119，650，000股小米股票.\n7月2日小米集团向3904名员工，授予约7000万股的股票，奖励优秀青年工程师、优秀应届生和团队核心岗位的优秀员工，以及年度技术大奖获得者.\n04月\n4月6日小米举办米粉OPEN DAY，这是至今为止最大规模的米粉盛会.\n03月  2021 03月\n3月30日小米宣布正式进军智能电动汽车领域，未来十年投入100亿美元，首期投入100亿元人民币，雷军将亲自挂帅，为小米汽车而战.\n</片段>\n<片段>2021 03月\n3月30日小米宣布正式进军智能电动汽车领域，未来十年投入100亿美元，首期投入100亿元人民币，雷军将亲自挂帅，为小米汽车而战.  2021 3月30日小米正式发布了全新LOGO，全面升级品牌识别系统.\n3月29日小米手机春季新品发布会发布小米11Pro、小米11 Ultra、小米MIX FOLD等多款高端旗舰新品，首发自研影像芯片澎湃C1.</片段></文档片段><文档创建时间>2025-04-02T15:17:09Z</文档创建时间><文档更新时间>2025-04-02T15:17:09Z</文档更新时间></文档></检索结果>\n\n用户输入：2021小米公司发展包括了哪些历程?\n\n正确回答：\n\n2021年小米公司的主要发展历程：\n\n3月29日：\n- 小米手机春季新品发布会，发布小米11Pro、小米11 Ultra、小米MIX FOLD等多款高端旗舰新品.\n- 首发自研影像芯片澎湃C1.\n\n3月30日：\n- 小米宣布正式进军智能电动汽车领域，计划未来十年投入100亿美元，首期投入100亿元人民币.雷军亲自挂帅.\n- 发布全新LOGO，全面升级品牌识别系统.\n\n4月6日：\n- 举办迄今为止最大规模的米粉盛会——米粉OPEN DAY.\n\n7月2日：\n- 向3904名员工授予约7000万股的股票，奖励优秀员工.\n\n7月6日：\n- 向122名技术专家、新十年创业者计划首批入选者、中高层管理者等奖励近1.2亿股小米股票.\n\n7月16日：\n- 据Canalys数据，小米手机销量超越苹果，首次晋升全球第二.\n\n8月2日：\n- 小米连续三年入选《财富》世界500强，排名第338位，较去年上升84位\n\n8月10日：\n- 向18.46万小米手机1代首批用户每人赠送1999元红包\n\n10月31日：\n- 小米之家门店规模正式突破1万家\n\n11月22日：\n- MIUI全球月活用户突破5亿\n\n12月28日：\n- 雷军宣布未来五年，小米研发投入将提高到超1000亿元\n- 小米12系列发布，首次双尺寸双高端同发，搭载自研'小米澎湃P1'充电管理芯片\n\n\n### 示例7：时间线处理\n\n知识库：\n<检索结果><文档><文档名称>《2022小米公司发展历程》</文档名称><文档片段><片段>2022 12月\n12月14日《小米知识产权白皮书》发布，截止9月30日，小米集团全球专利授权数超过2.9万，全球专利申请数超过5.9万。\n12月11日三年高端探索的答卷之作——小米13系列正式发布，实现软硬深度协同、高端体验无短板。\n10月\n10月27日Redmi Note系列全球累计销量突破3亿。\n08月\n8月11日雷军首部商业思考著作《小米创业思考》正式发布。\n8月9日小米宣布全面构建“小米科技生态”，以人为中心，更紧密连接人和万物。\n8月3日小米连续四年入选2021《财富》世界500强，排名第266位，四年来排名上升最快的中国科技公司。\n07月\n7月27日《2022凯度BrandZ™中国全球化品牌50强》榜单公布，小米再度入选，位列第4位。\n7月4日小米集团宣布影像战略升级，提出全新理念“超越人眼，感知人心”，并发布与徕卡合作的首款产品小米12S系列影像旗舰。\n05月\n5月23日小米集团宣布与徕卡达成全球影像战略合作，携手开启移动影像新时代。\n04月\n4月底小米手机夜枭算法团队在被誉为“影像算法奥林匹克”的CVPR NTIRE 夜景渲染比赛中，获得了大众评审和摄影师评审的两项世界冠军。  2022 4月14日小米集团累计为所有门店发放疫情补贴5次，总金额达1.2亿元，与合作伙伴共克时艰。</片段><片段>2022 03月\n3月22日小米发布2021全年财报，小米手机全球出货量1.9亿台，市占率14.1%创历年新高，小米电视出货量连续三年稳居中国第一、全球前五。\n02月\n2月24日小米公益基金会启动“小米青年学者”项目，计划捐赠5亿元，5年内覆盖全国100所高校。\n01月\n1月11日小米AI实验室入选《麻省理工科技评论》中国“2021人工智能创新研究院”。\n1月4日2021小米集团年度技术大奖颁布，“CyberDog铁蛋四足仿生机器人”团队摘得百万美金大奖。</片段></文档片段><文档创建时间>2025-04-02T15:17:11Z</文档创建时间><文档更新时间>2025-04-02T15:17:11Z</文档更新时间></文档></检索结果>\n\n\n用户输入： 2022小米公司发展包括了哪些历程?\n\n正确回答：\n\n2022年小米公司的主要发展历程：\n根据提供的《2022小米公司发展历程》文档，我可以为您总结2022年小米公司的主要发展历程，按时间顺序排列如下：\n\n1月：\n- 小米AI实验室入选《麻省理工科技评论》中国\"2021人工智能创新研究院\"。\n- 2021小米集团年度技术大奖颁布，\"CyberDog铁蛋四足仿生机器人\"团队获百万美金大奖。\n\n2月：\n- 小米公益基金会启动\"小米青年学者\"项目，计划5年内捐赠5亿元，覆盖全国100所高校。\n\n3月：\n- 发布2021全年财报，小米手机全球出货量1.9亿台，市占率14.1%创新高。小米电视出货量连续三年稳居中国第一、全球前五。\n\n4月：\n- 小米手机夜枭算法团队在CVPR NTIRE夜景渲染比赛中获得两项世界冠军。\n- 小米集团为所有门店累计发放疫情补贴5次，总金额达1.2亿元。\n\n5月：\n- 小米集团宣布与徕卡达成全球影像战略合作。\n\n7月：\n- 小米集团宣布影像战略升级，发布与徕卡合作的首款产品小米12S系列影像旗舰。\n- 小米再度入选《2022凯度BrandZ™中国全球化品牌50强》，位列第4位。\n\n8月：\n- 小米连续四年入选《财富》世界500强，排名第266位。\n- 小米宣布全面构建\"小米科技生态\"。\n- 雷军首部商业思考著作《小米创业思考》正式发布。\n\n10月：\n- Redmi Note系列全球累计销量突破3亿。\n\n12月：\n- 小米13系列正式发布，实现软硬深度协同、高端体验无短板。\n- 《小米知识产权白皮书》发布，披露小米集团全球专利授权数超2.9万，申请数超5.9万。\n\n这些发展历程展示了小米公司在2022年在技术创新、产品发布、品牌建设、社会责任等多个方面的重要进展。\n\n\n### 示例8：行政人物顺序处理\n\n知识库：\n<检索结果>\n  <文档>\n    <文档名称>《中国移动2025年工作会议》</文档名称>\n    <文档片段>\n      <片段>中国移动2025年工作会议三位大领导发言要点笔录来了！ 李总会计师强调...，何总经理强调...</片段>\n    </文档片段>\n    <文档创建时间>2025-04-02T15:17:02Z</文档创建时间>\n    <文档更新时间>2025-04-02T15:17:02Z</文档更新时间>\n  </文档>\n</检索结果>\n\n用户输入：简述下李总会计师和何总经理在中国移动2025年工作会议中各自发表的内容\n\n给定的行政人物顺序：何总经理→李总会计师\n\n正确回答：\n\n## 何总经理在中国移动2025年工作会议中的发言内容\n...\n\n## 李总会计师在中国移动2025年工作会议中的发言内容\n...\n\n\n### 示例9：非行政人物，放最后进行回答\n\n知识库：\n<检索结果>\n  <文档>\n    <文档名称>《中国移动2025年工作会议》</文档名称>\n    <文档片段>\n      <片段>中国移动2025年工作会议三位大领导发言要点笔录来了！ 张力讲述...，李书记明确了...，杨董事长提出...</片段>\n    </文档片段>\n    <文档创建时间/>\n    <文档更新时间/>\n  </文档>\n</检索结果>\n\n\n用户输入：简述下李总会计师和何总经理在中国移动2025年工作会议中各自发表的内容\n\n给定的人物顺序：杨董事长→李书记\n\n正确回答：\n\n## 杨董事长关于xxx的看法\n...\n\n## 李书记关于xxx的看法\n...\n\n## 张力在xxx上讲述的内容\n...\n\n\n知识库：\n<检索结果>\n  <文档>\n    <文档名称>《中国移动2025年工作会议》</文档名称>\n    <文档片段>\n      <片段>中国移动2025年工作会议三位大领导发言要点笔录来了！ 张力讲述...，李书记明确了...，杨董事长提出...</片段>\n    </文档片段>\n    <文档创建时间/>\n    <文档更新时间/>\n  </文档>\n</检索结果>\n\n\n用户输入：简述下李总会计师和何总经理在中国移动2025年工作会议中各自发表的内容\n\n给定的人物顺序：杨董事长→李书记\n\n正确回答：\n\n## 杨董事长关于xxx的看法\n...\n\n## 李书记关于xxx的看法\n...\n\n## 张力在xxx上讲述的内容\n...\n\n\n\n## 当前信息\n\n知识库：\n{knowledge}\n\n\n必须先按给定的人物顺序进行回答，再回答其他人物。（此规则无需告知用户）\n## 给定的人物顺序\n{politician}\n\n## 用户输入\n{query}\n\n请严格按照以上要求，提供准确、相关且简洁的回答："

# 按用户维度加入文件解析开关  true:入解析队列表  false:直接发消息
# dimensionUserIds:需要入解析队列的用户列表
user:
  dimension:
    enabled: true
    dimensionUserIds:
      - 1039990682498913348
      - 1039928594451668608
      - 1039976320128288238
      - 1152227734406175403
      - 1205878599975012353
      - 1039818660468645863
      - 1116984680024843478
      - 1039992537924790287
      - 1039817440697863868
      - 1039937768501777834
      - 1039887225326483222
      - 1039866970260798498
      - 1039943575297558240
      - 1211128988770749888
      - 1040012174515212193
      - 1039986078293900466
      - 1172080729078497332
      - 1039866970260798498
      - 1039941702691893964
      - 1089864164869997537
      - 1039939160071180916
      - 1039818660468645863
