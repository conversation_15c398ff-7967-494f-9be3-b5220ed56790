# 业务条件参数
business-param:
  # 历史对话列表（1.0和2.0）
  chatContentList:
    # 限制搜索X天内的历史记录
    queryMaxDays: 90
    # 业务类型查询条件映射
    businessTypeMap:
      # 针对邮箱webAI业务类型，配置展示云盘app、邮箱app、邮箱webAI渠道的历史记录
      "e-139mail-webai": [ "e-139mail-webai", "e-139mail-app", "e-mcloud-pc", "e-mcloud-app", "c-mcloud-app" ]
  # 历史会话列表查询V2接口
  assistantChatV2List:
    # 默认会话iconUrl
    defaultIconUrl: "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_100.png"
    # 会话iconUrl映射
    iconUrlMap:
      "100": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_100.png"
      "200": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_200.png"
      "300": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_300.png"
      "400": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_400.png"
      "40001": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_40001.png"
      "40002": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_40002.png"
      "40003": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_40003.png"
      "40004": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_40004.png"
      "40005": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_200.png"
      "40006": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_200.png"
      "40007": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_200.png"
# 对话业务配置
chat:
  text-tool:
    # ai ppt模块开关
    ai-ppt-open: true
    # 必须关闭的渠道列表（open=true也要拦截）
    ai-ppt-must-close-channels: ['101', '102', '103', '202', '401', '400']
    # ai 生成回忆相册开关
    ai-memory-album-open: false
    # ai 生成回忆相册（open=false）关闭条件，允许开启的白名单用户
    ai-memory-album-white-list:
      - "13620403747"
      - "18520734798"
      - "17512843221"
      - "18202098072"
      - "13316189595"
      - "15992549826"
      - "13602420417"
      - "13802885271"
      - "17846876519"
      - "13680147217"
      - "13802885115"
      - "13802885331"
      - "13922200384"
      - "13922201541"
      - "13802885171"
      - "13802885432"
      - "13802883435"
      - "18390940629"
      - "13710662664"
      - "13242450662"
      - "13416244144"
      - "15876521469"
      - "15918787370"
      - "13527167720"
      - "13620403747"
      - "13580574830"
      - "15286835083"
      - "18327863481"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
    # 文本工具业务配置
    business:
      # aippt生成配置
      ai-ppt-generate:
        search-knowledge-enable: true
        personal-path: '/AI文件库/AI生成PPT'
        h5-preview-path: 'https://ai.yun.139.com/aippt/web/?taskid={taskid}&token={token}'
        pc-preview-path: 'https://ai.yun.139.com/aippt/web/?taskid={taskid}&token={token}'
        h5-ppt-path: 'https://ai.yun.139.com/aippt/web/?pptid={pptid}&token={token}'
        pc-ppt-path: 'https://ai.yun.139.com/aippt/web/?pptid={pptid}&token={token}'
      # ai生成回忆相册配置
      ai-memory-album:
        search-image-page-size: 500
        search-filter-end-strs:
          - "生成相册"
          - "做成相册"
          - "合成相册"
          - "整理成合集"
          - "做个故事集"
          - "做个纪念影集"
          - "做合集"
          - "做成影集"
          - "做成纪念册"
          - "整理成册"
          - "做个纪念册"
          - "做个相册"
          - "回忆故事集"
          - "整理成回忆相册"
          - "回忆相册"
          - "回忆集"
          - "制作影集"
          - "制作成影集"
          - "生成故事"
  model:
    # 模型白名单列表
    model-white-list:
      # 开关为false且open-white-list不为空才校验
      - open: false
        model: deepseek_r1_671b
        # 该模型只有白名单用户能返回或设置模型
        open-white-list: [ "13247589394","13922200384","13802885115","13802885331","13802885432","13620403747","18272356772" ]