server:
  port: 19027
  servlet:
    context-path: /api/outer

logging:
  level:
    #root: INFO
    com.kaven.mybatisplus.dao: DEBUG
    com.shimain.springboot.mapper: INFO
    #pattern:
    #console: '%p%m%n'


hcy:
  plugin:
    uid: #雪花算法插件生成配置
      snowflake:
        timeBits: 32
        workerBits: 18
        seqBits: 13
        epochStr: 2007-06-05  #不能高于此时间点。此种情况下，固定为19位雪花id
        paddingFactor: 50
  auth-interceptor:
    enable: true
    exclude-path:
  # 拦截校验的路径
  interceptor-path:
    interceptionPath:
      - /intelligent/search
  #用户白名单(配合拦截校验的路径使用)
  user-white:
    userWhiteList:
      - 1079785805058449675
      - 1079785794320933136
      - 1105470804722209016
      - 1101574403945695215
      - 1079785796468425009
      - 1105470703790412094
      - 1039895866800747225
      - 1039842815364654456
      - 1085988717093673575
      - 1039998808577024877
      - 1039942905282721773
      - 1098911393222042477
      - 1079785828680868403
      - 1100491466595254728
      - 1091161670195309960
      - 1039989978124233748
      - 1080451595858182188
      - 1082681066140017755
      - 1088957647597610874
      - 1079785805058449675
      - 1079785794320933136
      - 1079785796468425009
      - 1105470804722209016
      - 1039872828596171949
      - 1093781720506057413
      - 1040008652642097125
      - 1039975220616648009
      - 1040004357674739076
      - 1039805586588119811
      - 1126080153792954403
      - 1085988944727310270
      - 1039748927379573379
      - 1088957628270192456
      - 1090443459321562338


#用户域服务 请修改成现网的配置
user:
  url: http://user-njs-internal.yun.139.com:30080
  path:
  appSecret: pPIl*%n_BshMEeyF
  appSecretId: 1079827436178702406
  appKey: 1079827436178702402

yun:
  neauth:
    provider:
      enable: true
      config:
        url: http://public-njs-internal.yun.139.com:30080/configcenter/neauth/configs
        appKey: 1079827436178702402
        appSecretId: 1079827436178702403
        appSecret: "JxR(5NMU*RYgR#w3"
        algorithmVersion: 1.0

eos:
  client:
    internalHostname: http://eos-guangzhou-1-internal.cmecloud.cn
    hostname: https://eos-guangzhou-1.cmecloud.cn
    accessKey: D25RTPTV9V0U0BQ64Y3O
    secretKey: YAsYqDAu92WBONouBkiNFOsJ82Zx185K6s3xsHqb
    signingRegion: guangzhou1
    #桶名称
    bucketName: b08-ypcg-oss-003

#上传限制为以下后缀
report:
  upload:
    allowedExtensions: jpg,jpeg,png,heic

nfs:
  #文件实际储存路径
  path: /data/yun-ai/yun-ai-api-outer/imageCaption/
  #nfs 路径校验 初始路径
  basePath: /data/yun-ai
  #转换路径（测试环境不同需要转换）
  aiPath: /data/yun-ai/yun-ai-api-outer/imageCaption/
  connectionTimeout: 1 # EOS url连接超时时间 单位:分钟
  readTimeout: 10 # NFS 读取文件超时时间 单位:分钟

aiManage:
  type: 4

userRoute:
  userRouteExpireHour: 24

templateMatch:
  threshold: 0.6

imageCaption:
  length: 10

intelligentsearch:
  IFLYTEKThreshold: 22.00
  BaiDuThreshold: 28.00
  TencentThreshold: 22.00

#  华为老平台网元鉴权参数
mcloud:
  net-auth:
    channelSrc: 10249400
    name: AISys
    pwd: 'VTB(SrQH1jzd&w5g!KZp'
    key: MODULE46DA041C44
    enabled: true

market:
  content-url: https://caiyun.feixin.10086.cn:7071
  content-path: market/rabactive/ai/invite

thread-pool:
  global:
    core-size: 10
    max-size: 100
    keep-alive-time: 2
    keep-alive-time-unit: "MINUTES"
    rejected-class: "java.util.concurrent.ThreadPoolExecutor$AbortPolicy"

faceSearch:
  maxFileSize: 20

# 文本模型配置
model:
  # 【云邮（默认）】输入限制(上线后弃用，下一个版本删掉)
  limit:
    deepseek_r1_32b: { name: 'DeepSeek-R1 极速版', sort: 1, length: 8000, "QPS": 10, history-max-length: 8000, paid: false, save-path: 'DeepSeek R1 极速版', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/deepseek.svg' }
    blian_deepseek_r1: { name: 'DeepSeek-R1 满血版', sort: 2, length: 30000, "QPS": 10, history-max-length: 30000, paid: false, enable-network-search: true, save-path: 'DeepSeek R1 满血版', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/deepseek.svg' }
    huoshan_deepseek_r1: { name: 'DeepSeek-R1 满血版', sort: 2, length: 30000, "QPS": 10, history-max-length: 30000, paid: false, enable-network-search: true, save-path: 'DeepSeek R1 满血版', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/deepseek.svg' }
    blian: { name: '模型一', sort: 3, length: 30000, "QPS": 10, session-set: true, def-model: true, history-max-length: 30000, enable-network-search: false, save-path: '通义qwen plus', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/qwen.svg' }
    blian_72b_calc: { name: '阿里百炼72B计费版', sort: 4, length: 10000, "QPS": 10, history-max-length: 24000, icon-url: '' }
    jiutian: { name: '九天', sort: 5, length: 6000, "QPS": 10, history-max-length: 10000, save-path: '九天', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/jiutian.svg' }
    qwen: { name: '模型三', sort: 5, length: 6000, "QPS": 10, history-max-length: 20000, icon-url: '' }
    huoshan: { name: '模型四', sort: 6, length: 10000, "QPS": 10, history-max-length: 24000, icon-url: '' }
    xfyun: { name: '模型二', sort: 7, length: 2000, "QPS": 10, history-max-length: 10000, icon-url: '' }
  # 【小天】大模型配置(上线后弃用，下一个版本删掉)
  limit-xiao-tian:
    deepseek_r1_32b: { name: 'DeepSeek-R1 极速版', sort: 1, length: 8000, "QPS": 10, session-set: true, def-model: true, history-max-length: 8000, paid: false, save-path: 'DeepSeek R1 极速版', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/deepseek.svg' }
    blian_deepseek_r1: { name: 'DeepSeek-R1 满血版', sort: 2, length: 30000, "QPS": 10, session-set: true, history-max-length: 30000, paid: false, enable-network-search: true, save-path: 'DeepSeek R1 满血版', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/deepseek.svg' }
    huoshan_deepseek_r1: { name: 'DeepSeek-R1 满血版New', sort: 2, length: 30000, "QPS": 10, session-set: false, history-max-length: 30000, paid: false, enable-network-search: true, save-path: 'DeepSeek R1 满血版', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/deepseek.svg' }
    blian: { name: '通义qwen plus', sort: 3, length: 30000, "QPS": 10, session-set: true, history-max-length: 30000, enable-network-search: false, save-path: '通义qwen plus', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/qwen.svg' }
    blian_72b_calc: { name: '阿里百炼72B计费版', sort: 4, length: 10000, "QPS": 10, history-max-length: 24000, icon-url: '' }
    jiutian: { name: '九天', sort: 5, length: 6000, "QPS": 10, session-set: false, history-max-length: 10000, save-path: '九天', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/jiutian.svg' }
    qwen: { name: '小天', sort: 5, length: 6000, "QPS": 10, history-max-length: 20000, icon-url: '' }
    huoshan: { name: '火山', sort: 6, length: 10000, "QPS": 10, history-max-length: 24000, icon-url: '' }
    xfyun: { name: '讯飞星火', sort: 7, length: 2000, "QPS": 10, history-max-length: 10000, icon-url: '' }
  # 【云邮】大模型配置列表(按业务配置)
  limit-yun-mail-list:
    # 特殊匹配的业务类型列表(webai)
    - business-types: [ 'e-139mail-webai', 'e-mcloud-pc' ]
      # 普通对话限定搜索意图列表，空则不限定【注意：当前云邮助手开放意图仅[012,013,028,017,021]，只能减少意图，增加“搜索意图XXX”需要改配置和代码微调】
      search-include-intentions: [ '012', '013', '014', '015', '016', '028', '017', '018' ]
      # 支持的意图列表，空则全部支持
      support-intentions: [ '000','999','012', '013', '014', '015', '016', '017', '018', '028', '036' ]
      # 模型列表
      models:
        deepseek_r1_32b: { name: 'DeepSeek-R1 极速版', sort: 1, length: 8000, "QPS": 10, session-set: true, history-max-length: 8000, paid: false, enable-network-search: true, save-path: 'DeepSeek R1 极速版', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/deepseek.svg' }
        blian_deepseek_r1: { name: 'DeepSeek-R1 满血版', sort: 2, length: 30000, "QPS": 10, session-set: true, history-max-length: 30000, paid: false, enable-network-search: true, save-path: 'DeepSeek R1 满血版', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/deepseek.svg' }
        blian: { name: '通义qwen plus（2.5）', sort: 3, length: 30000, "QPS": 10, session-set: true, history-max-length: 30000, enable-network-search: true, save-path: '通义qwen plus', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/qwen.svg' }
        blian_qwen3_235b: { name: '通义qwen3', sort: 4, length: 30000, "QPS": 10, session-set: true, history-max-length: 30000, paid: true, enable-network-search: true, save-path: '通义qwen3', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/qwen.svg' }
        deepseek_r1_671b: { name: 'DeepSeek-R1-671B 满血版', sort: 5, length: 30000, "QPS": 10, session-set: true, history-max-length: 30000, paid: false, enable-network-search: true, save-path: 'DeepSeek-R1-671B 满血版', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/deepseek.svg' }
      # 按用户类型配置默认模型【配置模型时，必须确认：上面models配置中，前端是否展示的字段session-set为true】
      def-models:
        white-user: "blian_deepseek_r1"
        member: "blian_deepseek_r1"
        non-member: "deepseek_r1_32b"
        def-model: "deepseek_r1_32b"
    # 特殊匹配的业务类型列表(h5融合 e-139mail-app)
    - business-types: [ 'e-139mail-app' ]
      # 限定搜索意图列表，空则不限定【注意：当前云邮助手开放意图仅[012,013,028,017,021,038]，只能减少意图，增加“搜索意图XXX”需要改配置和代码微调】
      search-include-intentions: [ '012', '013', '028', '017', '038' ]
      # 支持的意图列表，空则全部支持
      support-intentions: [ '000','999', '012', '013', '028', '017', '001', '002', '003', '004', '005', '006', '007', '008', '009', '010', '011', '025', '026', '032', '035', '036', '037', '038' ]
      # 模型列表
      models:
        deepseek_r1_32b: { name: 'DeepSeek-R1 极速版', sort: 1, length: 8000, "QPS": 10, session-set: true, history-max-length: 8000, paid: false, enable-network-search: true, save-path: 'DeepSeek R1 极速版', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/deepseek.svg' }
        blian_deepseek_r1: { name: 'DeepSeek-R1 满血版', sort: 2, length: 30000, "QPS": 10, session-set: true, history-max-length: 30000, paid: false, enable-network-search: true, save-path: 'DeepSeek R1 满血版', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/deepseek.svg' }
        blian: { name: '通义qwen plus（2.5）', sort: 3, length: 30000, "QPS": 10, session-set: true, history-max-length: 30000, enable-network-search: true, save-path: '通义qwen plus', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/qwen.svg' }
        blian_qwen3_235b: { name: '通义qwen3', sort: 4, length: 30000, "QPS": 10, session-set: true, history-max-length: 30000, paid: true, enable-network-search: true, save-path: '通义qwen3', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/qwen.svg' }
        deepseek_r1_671b: { name: 'DeepSeek-R1-671B 满血版', sort: 5, length: 30000, "QPS": 10, session-set: true, history-max-length: 30000, paid: false, enable-network-search: true, save-path: 'DeepSeek-R1-671B 满血版', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/deepseek.svg' }
      # 按用户类型配置默认模型【配置模型时，必须确认：上面models配置中，前端是否展示的字段session-set为true】
      def-models:
        white-user: "blian"
        member: "blian"
        non-member: "blian"
        def-model: "blian"
    # 空业务代表所有的业务类型匹配（前面优先匹配到业务类型先返回）
    - business-types: [ ]
      # 限定搜索意图列表，空则不限定【注意：当前云邮助手开放意图仅[012,013,028,017,021]，只能减少意图，增加“搜索意图XXX”需要改配置和代码微调】
      search-include-intentions: [ '012', '013', '028' ]
      # 支持的意图列表，空则全部支持
      support-intentions: [ '000','999', '012', '013', '028', '001', '002', '003', '004', '005', '006', '007', '008', '009', '010', '011', '025', '026', '032' ]
      # 模型列表
      models:
        blian: { name: '模型一', sort: 1, length: 30000, "QPS": 10, session-set: true, history-max-length: 30000, enable-network-search: true, save-path: '通义qwen plus', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/qwen.svg' }
      # 按用户类型配置默认模型【配置模型时，必须确认：上面models配置中，前端是否展示的字段session-set为true】
      def-models:
        white-user: "blian"
        member: "blian"
        non-member: "blian"
        def-model: "blian"
  # 【小天】大模型配置列表(按业务配置)
  limit-xiao-tian-list:
    # 空业务代表所有的业务类型匹配（前面优先匹配到业务类型先返回）
    - business-types: [ ]
      # 模型列表
      models:
        # 现网使用
        deepseek_r1_32b: { name: 'DeepSeek-R1 极速版', sort: 1, length: 8000, "QPS": 10, session-set: true, history-max-length: 8000, paid: false, enable-network-search: true, save-path: 'DeepSeek R1 极速版', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/deepseek.svg' }
        # 现网使用
        blian_deepseek_r1: { name: 'DeepSeek-R1 满血版', sort: 2, length: 30000, "QPS": 10, session-set: true, history-max-length: 30000, paid: false, enable-network-search: true, save-path: 'DeepSeek R1 满血版', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/deepseek.svg' }
        # 待上线(测试环境开启)
        huoshan_deepseek_r1: { name: 'DeepSeek-R1 满血版(火山)', sort: 2, length: 30000, "QPS": 10, session-set: false, history-max-length: 30000, paid: false, enable-network-search: true, save-path: 'DeepSeek R1 满血版', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/deepseek.svg' }
        # 现网使用
        blian: { name: '通义qwen plus（2.5）', sort: 3, length: 30000, "QPS": 10, session-set: true, history-max-length: 30000, enable-network-search: true, save-path: '通义qwen plus', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/qwen.svg' }
        # 已下线
        blian_72b_calc: { name: '阿里百炼72B计费版', sort: 4, length: 10000, "QPS": 10, history-max-length: 24000, icon-url: '' }
        # 待上线(测试环境开启)
        jiutian: { name: '九天', sort: 5, length: 6000, "QPS": 10, session-set: false, history-max-length: 10000, save-path: '九天', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/jiutian.svg' }
        # 已下线
        qwen: { name: '小天', sort: 5, length: 6000, "QPS": 10, history-max-length: 20000, icon-url: '' }
        # 已下线
        huoshan: { name: '火山', sort: 6, length: 10000, "QPS": 10, history-max-length: 24000, icon-url: '' }
        # 已下线
        xfyun: { name: '讯飞星火', sort: 7, length: 2000, "QPS": 10, history-max-length: 10000, icon-url: '' }
        # 现网使用：百炼千问3
        blian_qwen3_235b: { name: '通义qwen3', sort: 8, length: 30000, "QPS": 10, session-set: true, history-max-length: 30000, paid: true, enable-network-search: true, save-path: '通义qwen3', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/qwen.svg' }
        # 现网使用：DeepSeek-R1-671B
        deepseek_r1_671b: { name: 'DeepSeek-R1-671B 满血版', sort: 9, length: 30000, "QPS": 10, session-set: true, history-max-length: 30000, paid: false, enable-network-search: true, save-path: 'DeepSeek-R1-671B 满血版', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/deepseek.svg' }
      # 按用户类型配置默认模型【配置模型时，必须确认：上面models配置中，前端是否展示的字段session-set为true】
      def-models:
        white-user: "blian_deepseek_r1"
        member: "blian_deepseek_r1"
        non-member: "deepseek_r1_32b"
        def-model: "deepseek_r1_32b"
    # 【云手机】大模型配置列表(按业务配置)
  limit-cloud-phone-list:
    # 云手机数字人业务类型
    - business-types: [ 'CloudphoneDigital' ]
      # 限定搜索意图列表，空则不限定
      search-include-intentions: [ '' ]
      # 支持的意图列表，空则全部支持
      support-intentions: [ '000', '30001', '30002', '30003', '30004', '30005' ,'30006','30007','30008',  '30009' ]
      # 模型列表
      models:
        # 现网使用
        blian: { name: '通义qwen plus（2.5）', sort: 3, length: 30000, "QPS": 10, session-set: true, history-max-length: 30000, paid: false, enable-network-search: true, save-path: '通义qwen plus', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/qwen.svg' }
      # 按用户类型配置默认模型【配置模型时，必须确认：上面models配置中，前端是否展示的字段session-set为true】
      def-models:
        white-user: "blian"
        member: "blian"
        non-member: "blian"
        def-model: "blian"
    # 空业务代表所有的业务类型匹配（前面优先匹配到业务类型先返回）
    - business-types: [ ]
      # 限定搜索意图列表，空则不限定
      search-include-intentions: [ '' ]
      # 支持的意图列表，空则全部支持
      support-intentions: [ '000', '30001', '30002', '30003', '30004', '30005' ,'30006','30007','30008',  '30009']
      # 模型列表
      models:
        # 现网使用
        deepseek_r1_32b: { name: 'DeepSeek-R1 极速版', sort: 1, length: 8000, "QPS": 10, session-set: true, history-max-length: 8000, paid: false, enable-network-search: true, save-path: 'DeepSeek R1 极速版', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/deepseek.svg' }
        # 现网使用
        blian_deepseek_r1: { name: 'DeepSeek-R1 满血版', sort: 2, length: 30000, "QPS": 10, session-set: true, history-max-length: 30000, paid: false, enable-network-search: true, save-path: 'DeepSeek R1 满血版', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/deepseek.svg' }
        # 现网使用
        blian: { name: '通义qwen plus（2.5）', sort: 3, length: 30000, "QPS": 10, session-set: true, history-max-length: 30000, paid: false, enable-network-search: true, save-path: '通义qwen plus', icon-url: 'https://yun.mcloud.139.com/aiassistant/static/icon/qwen.svg' }
      # 按用户类型配置默认模型【配置模型时，必须确认：上面models配置中，前端是否展示的字段session-set为true】
      def-models:
        white-user: "blian_deepseek_r1"
        member: "blian_deepseek_r1"
        non-member: "deepseek_r1_32b"
        def-model: "deepseek_r1_32b"
  # 智能体模型输入限制
  limit-agent:
    deepseek_r1_7b: { history-max-length: 8000 }
    deepseek_r1_32b: { history-max-length: 8000 }
    deepseek_r1_671b: { history-max-length: 10000 }
    blian_deepseek_r1: { history-max-length: 30000 }
    huoshan_deepseek_r1: { history-max-length: 30000 }
  # 小天助手使用的模型
  xiao-tian-use: blian
  # 智能调度配置
  intelligent-schedules:
    - { code: "000", channels: [ ], execute-sort: [ "blian","blian_72b_calc","qwen","huoshan" ] }
  # 查询会员中心信息开关(默认模型配置使用)
  query-model-member-center-open: true
  # 默认模型配置(上线后弃用，下一个版本删掉)
  def-model:
    # 小天助手【配置模型时，必须确认：上面limit-xiao-tian（小天-文本模型）配置中，前端是否展示的字段session-set为true】
    xiaotian:
      white-user: "blian_deepseek_r1"
      member: "blian_deepseek_r1"
      non-member: "deepseek_r1_32b"
      def-model: "deepseek_r1_32b"
    # 云邮助手【配置模型时，必须确认：上面limit（云邮-文本模型）配置中，前端是否展示的字段session-set为true】
    yunmail:
      white-user: "blian"
      member: "blian"
      non-member: "blian"
      def-model: "blian"

# 跳转链接配置 注意此配置要放到nacos上
link:
  url:
    ai-eliminate: https://yun.139.com/aiTools/#/picEliminateOffice?enableShare=1&token=#ssoToken#
    intelligent-cutout: https://yun.139.com/aiTools/#/faceMattingOffice?enableShare=1&token=#ssoToken#
    ai-head-sculpture: https://yun.139.com/aiTools/#/aiAvatarOffice?enableShare=1&token=#ssoToken#
    picture-comic-style: https://yun.139.com/aiTools/#/aiComicOffice?enableShare=1&token=#ssoToken#
    old-photos-repair: https://yun.139.com/aiTools/#/picRepairOffice?enableShare=1&token=#ssoToken#
    picture-generate-text: https://yun.139.com/aiTools/#/picToTextOffice?enableShare=1&token=#ssoToken#
    live-photos: https://yun.139.com/aiTools/#/picAnimateOffice?enableShare=1&token=#ssoToken#
    image-quality-restoration: https://yun.139.com/aiTools/#/resolutionRepairOffice?enableShare=1&token=#ssoToken#
    cloud-camera-external: https://caiyun.feixin.10086.cn:7071/portal/clientDL/index.html?linkUrl=mcloud%3A%2F%2FmcMiniProgram%3Fparams%3DeyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLCJuYW1lIjoi5aaZ5LqR55u45py6IiwibWluaVR5cGUiOiIxIn0%3D
    cloud-camera-within: mcloud://mcMiniProgram?params=eyJtaW5pSWQiOiI2NTA0MzVhNWVlM2IwZTAwMDEzNjhhMDkiLAoibmFtZSI6IuWwj+eoi+W6jyIsCiJtaW5pVHlwZSI6IjEiLAoibWluaVBhdGgiOiJwYWdlcy9ob21lL21haW4iLAoibWluaVVybCI6IiIsCiJtaW5pTGlua1R5cGUiOiIxIn0=

# 厂商配置 注意此配置要放到nacos上
intention:
  company:
    picture-comic-style: "4"
    ai-head-sculpture: "4"
    old-photos-repair: "8"
    live-photos: "7"
    picture-generate-text: "0,4"
    text-generate-picture: "8"
    image-quality-restoration: "8"
  tools:
    # 指定图片工具厂商
    picture-comic-style: { "supplierTypes": [ "7" ], "number": 2, "extendField": "动漫，新海诚风格，鲜艳的色彩，动漫美学，数码插画" }
    ai-head-sculpture: { "supplierTypes": [ "4" ], "number": 1 }
    old-photos-repair: { "supplierTypes": [ "8" ], "number": 1 }
    picture-generate-text: { "supplierTypes": [ "0", "4" ], "number": 1 }
    text-generate-picture: { "supplierTypes": [ "7" ], "number": 2 }
    image-quality-restoration: { "supplierTypes": [ "8" ], "number": 1 }
    ai-expansion-map: { "supplierTypes": [ "8" ], "number": 1, "extendField": "{\"top\":2,\"bottom\":2,\"left\":138,\"right\":138,\"ratio\":\"1:1\"}" }
    ai-photo-edit: { "supplierTypes": [ "7" ], "number": 2, "extendField": "AI修图" }
    intelligent-cutout: { "supplierTypes": [ "8" ], "number": 1 }

  # 个人云保存路径
  yun-path: ""
  # 意图上下文配置信息
  context:
    enable: false
    max-count: 20
    expire-time: 259200
    intention-context-code-list: [ "000", "012", "013", "014", "015", "016", "017", "018", "020", "021", "022", "023", "028", "032" ]
    intention-context-count: 4
    intention-context-tokens: 2000

common:
  #qps限制组件开启
  qpslimit:
    enabled: true
    #自定义参数
    limit: 600
    timeoutMillis: 1000
    expireTime: 120

hbase:
  client:
    username: cloudhbase
    accessKey: AEF9C7B415AF46F6A81076B63AE982CA
    quorum: hbase-zookeeper-0.hbase-zookeeper-headless.hbase-687acc91-ad9d-491c-aa96-6db773cdb443.svc.cluster.local:31444,hbase-zookeeper-1.hbase-zookeeper-headless.hbase-687acc91-ad9d-491c-aa96-6db773cdb443.svc.cluster.local:31444,hbase-zookeeper-2.hbase-zookeeper-headless.hbase-687acc91-ad9d-491c-aa96-6db773cdb443.svc.cluster.local:31444
    # 图片algorithm_metadata的hbase表
    metadataTableName: algorithm_metadata
    classFaceTableName: algorithm_faceinfo_cluster
    queryPageSize: 500
    # 表示单次API的超时时间（毫秒）
    operationTimeout: 30000
    # 表示单次RPC请求的超时时间（毫秒）
    rpcTimeout: 10000
    # 重试次数，每次间隔10秒
    retries: 3

yun-ai:
  image-nfs:
    sharePath: /data/yun-ai
    fodderCatalog: fodder

# 对话应用类型信息列表接口中的标签
tabs:
  tabLabel: [ "助手","重返历史","IP复刻","趣味","萌宠" ]

#流式接口配置
flow-type:
  config:
    auditSize: 10
    timeout: 1800000
    reconnectTimeMillis: 5000

# 令牌桶配置
dialogue-bucket:
  # 文本模型配置
  text-model:
    qwen: { period: 1,limit: 3,maxBurstSize: 3,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    xfyun: { period: 1,limit: 1,maxBurstSize: 1,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    blian: { period: 1,limit: 100,maxBurstSize: 100,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    blian_qwen3_235b: { period: 1,limit: 50,maxBurstSize: 50,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    blian_qwen_long: { period: 1,limit: 3,maxBurstSize: 3,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    blian_72b_calc: { period: 1,limit: 3,maxBurstSize: 3,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    jiutian: { period: 1,limit: 5,maxBurstSize: 5,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    xchen: { period: 3,limit: 5,maxBurstSize: 5,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    huoshan: { period: 100,limit: 100,maxBurstSize: 100,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    deepseek_r1_7b: { period: 1,limit: 60,maxBurstSize: 60,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    deepseek_r1_32b: { period: 1,limit: 58,maxBurstSize: 58,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    deepseek_r1_671b: { period: 1,limit: 5,maxBurstSize: 5,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    blian_deepseek_r1: { period: 1,limit: 1000,maxBurstSize: 1000,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    huoshan_deepseek_r1: { period: 1,limit: 1000,maxBurstSize: 1000,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    vlm_qwen25_7b: { period: 1,limit: 5,maxBurstSize: 5,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    vlm_qwen2_72b: { period: 1,limit: 1,maxBurstSize: 1,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    vlm_huoshan_1_5_vision_pro_32k: { period: 1,limit: 5,maxBurstSize: 5,timeoutMillis: 1000,scale: 2,expireTime: 120 }
    ali-ai-ppt: { period: 1,limit: 5,maxBurstSize: 5,timeoutMillis: 1000,scale: 2,expireTime: 120 }

#送审平台开关
check-system:
  # true-需要送审
  open: true
  #独立配置大模型送审（type是送审方式：all=全量送审；part=分批送审，partLocalAndAll=增量本地+最后一次全量送审；outputSize每次输出字数，appendBeforeSize送审追加截取最后文本的数量）
  text-models:
    # 默认大模型配置（default默认大模型）
    - { code: 'default', open: true, outputSize: 1, appendBeforeSize: 40, type: 'partLocalAndAll' }
    # 自研deepseekR1 32B独立配置
    - { code: 'deepseek_r1_32b', open: true, outputSize: 1, appendBeforeSize: 40, type: 'partLocalAndAll' }
    # 百炼deepseekR1独立配置
    - { code: 'blian_deepseek_r1', open: true, outputSize: 1, appendBeforeSize: 40, type: 'partLocalAndAll' }
    # 火山deepseekR1独立配置
    - { code: 'huoshan_deepseek_r1', open: true, outputSize: 1, appendBeforeSize: 40, type: 'partLocalAndAll' }
# 干扰库配置
intervention:
  # 匹配最小分数
  min-score: 0.8
  # 符合条件的最小匹配度
  minimum-should-match: '90%'
  # 默认答案版本，每次升级，需要配置默认版本
  default-answer-version: 1
  # 干扰库答案匹配，any-xxxs同时需要匹配
  match-answers:
    - { answer-version: '0', any-channels: [ '400','101','102','202','401' ], any-client-types: [ ], any-version: { clientVersion: '', h5Version: '1.2.0', mode: 'lt' } }
  #渠道独立配置开关
  channel-list: [ ]

# 大模型文件对话配置
text-file-mode:
  # 大模型上传文件数量
  file-num: 3
  # 大模型上传文件大小 150M
  file-size: 157286400
  # 大模型上传文件支持的后缀
  file-suffix-list: [ "txt","doc","docx","pdf","epub","md", "ppt", "pptx", "xls", "xlsx", "csv" ]

# 授权报名相关配置
ai-register:
  # 文档检索厂商类型
  doc-factory-type: 0
  # 文档检索算法组
  doc-algorithm-group-code: 2
  # 文档向量化厂商类型
  doc-vector-factory-type: 0
  # 文档向量化算法组
  doc-vector-algorithm-group-code: 3

# 搜索文档后缀映射
search-suffix:
  mapping:
    doc: [ "doc","dot","wps","wpt","docx","dotx","docm","dotm" ]
    xls: [ "xls","xlt","et","xlsx","xltx","xlsm","xltm" ]
    ppt: [ "ppt","pptx","pptm","ppsx","ppsm","pps","potx","potm","dpt","dps" ]
    pdf: [ "pdf" ]
    txt: [ "txt" ]

# 邮件AI总结,AI回复,智能纠错 指定模型
mail-ai-prompt:
  modelList:
    - modelCode: "qwen"
      promptKeyList: [ "MAIL_AI_SUMMARY", "MAIL_AI_REPLY","MAIL_INTELLIGENT_ERROR_CORRECTION" ]
      channelList: [ "10102","10103","10104","10105","10106","10108","10109","10110","10112","10120","10114","10130","10116","10140","10118","10150","10160","10170","10101","10107","10161" ]
      enable: true

#对话智能体配置
chat:
  agent:
    application:
      white-switch: false
      white-application-ids: [ '3000000002' ]
      user-white-list:
        - '13620403747'
        - '13710662664'
        - '15989492351'
        - '19866065340'
        - '18281686708'
        - '13002009682'
        - '13501525064'
        - '13557589652'
        - '15811852975'
        - '13450395246'
        - '18028041987'
        - '13802885271'
        - '17846876519'
        - '19802021150'
        - '13542899545'
        - '15036426513'
        - '13922201541'
        - '19802021524'
        - '13802885331'
        - '13802885432'
        - '13926431390'
        - '13710904541'
        - '13316189595'
        - '13711732256'
        - '19860408302'
        - '13434143935'
        - '18928819103'
        - '18771000041'
        - '15918830010'
        - '17806708402'
        - '17762564716'
        - '13600185050'
        - '13826108366'
        - '13922330720'
        - '13926179017'
        - '13929999543'
        - '13926244466'
        - '13902220805'
        - '13802882902'
        - '18718885099'
        - '13802882752'
        - '19802021736'
        - '19802021687'
        - '13802880655'
        - '19802025120'
        - '13802881270'
        - '13802885450'
        - '19802021105'
        - '13802885171'
        - '13902220523'
        - '13802889418'
        - '13802883435'
        - '19802021130'
        - '15019443317'
        - '13802885115'
        - '13922200384'
        - '19802021063'
        - '19802021462'
        - '19802021829'
        - '19802021860'
        - '19802025116'
        - '19802025787'
        - '13802885259'
        - '13802881360'
        - '13902220116'
        - '13802880898'
        - '13802882323'
        - '13802880247'
        - '19802021774'
        - '13802886464'
        - '19802021870'
        - '19802021816'

#本地定时任务
scheduled:
  #okHttpClient任务信息打印
  okHttpClient:
    enabled: true

#快速阅读引擎配置
speed-read:
  supplier-code-list: [ "2" ]

# 任务模块编码
task-comment:
  moduleSet:
    - "all"
    - "doc-summary"
    - "doc-mindmap"
    - "doc-outline"

# 云邮AI编辑
yun-mail-aiedit-config:
  file-path: "/AI文件库/AI编辑"
  summary-prompt-key: "YUN_MAIL_AI_EDIT_SUMMARY"
  process-prompt-key: "YUN_MAIL_AI_EDIT_MODEL_PROCESS"

audit:
  not-pass:
    thumbnail-url: https://yun.mcloud.139.com/aiassistant/static/icon/placeholderMap.png