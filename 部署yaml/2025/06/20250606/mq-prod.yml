rocketmq:
  acl:
    default:
      accessKey: C7mpKgkiEvz1TpACjO82DjzMiaJF
      secretKey: 8Z9iSlxiHCRffMPrl1uxNc29FMiOhk
      access_point_type: NAMESRV_ADDR
      nameServer: 10.169.208.13:31198;10.169.208.14:31198;10.169.208.15:31198
      instanceId: MQ_INST_1695709769806_JTYbsUlk
      instanceName: ai-mq-instance
      sendMsgTimeoutMillis: 3000
    # 用户域
    user-domain:
      accessKey: 727f6f51df47489facab1910178b3fe7
      secretKey: 826e4606957a4b2c8ca709d9eac805cc
      access_point_type: NAMESRV_ADDR
      namesrvAddr: 10.247.148.4:31502;10.247.148.5:31502
      namespace: MQ_INST_1678440563877_ChstwiJZ
      instanceName: user-mq-instance

  producer:
    # AI助手对话完成
    ai-assistant-dialogue-completed:
      groupId: GID_ALGORITHM_MARKET_AI-ASSISTANT-DIALOGUE-COMPLETED
      topic: TOPIC_ALGORITHM_AI-ASSISTANT_DIALOGUE-COMPLETED
      tag: '*'
    # 用户文档检索报名触发文档正文提取
    topic-local-algorithm-authorize:
      groupId: GID_LOCAL_ALGORITHM_AUTHORIZE_TASK
      topic: TOPIC_LOCAL_ALGORITHM_AUTHORIZE
      tag: '*'
    # 用户上传知识库触发延迟消息-查询转存任务状态
    personal-knowledge-trans-task:
      groupId: GID_LOCAL_ALGORITHM_KNOWLEDGE-FILE-TRANSFER-TASK
      topic: TOPIC_LOCAL_ALGORITHM_KNOWLEDGE-FILE-TRANSFER-DELAY
      tag: '*'
      delay-time-millis: 1000
    # 个人云目录转发/笔记、网页、邮箱文件上传消息
    personal-knowledge-category-trans-task:
      groupId: GID_LOCAL_ALGORITHM_KNOWLEDGE-FILE-UPLOAD
      topic: TOPIC_LOCAL_ALGORITHM_KNOWLEDGE-FILE-UPLOAD
      tag: '*'
    # 用户上传邮件/笔记到个人知识库，发起向量化提取任务
    personal-knowledge-dispatch-task:
      groupId: TOPIC_LOCAL_ALGORITHM_DOC-VECTOR-DISPATCH
      topic: TOPIC_LOCAL_ALGORITHM_DOC-VECTOR-DISPATCH
      tag: 'algorithm.local.doc.vector.dispatch'
    # 知识库文件删除任务
    personal-knowledge-file-delete-task:
      groupId: GID_LOCAL_ALGORITHM_KNOWLEDGE-FILE-TASK
      topic: TOPIC_LOCAL_ALGORITHM_KNOWLEDGE-FILE-TASK
      tag: '*'

  # 消费者
  consumer:
    # 用户隐私授权监听
    userPrivacyAuth:
      topic: TOPIC_USER_PRIVACY-AGREEMENT
      groupName: GID_USER_ALGORITHM_PRIVACY-AGREEMENT
      tag: 'USER.AGREE'
      instanceName: algorithmUserPrivacyAuthConsumer