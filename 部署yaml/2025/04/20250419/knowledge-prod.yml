#知识库相关配置

#知识库
knowledge:
  # 个人知识库
  personal:
    # 文件大小限制（B）
    size: 104857600
    # 扩展名
    ext-list: [ "docx","xlsx","csv","txt","pptx","doc","xls","ppt","pdf","md" ]
    # 标签个数
    label-num: 50
    # 标签名字数
    label-name-len: 6

    # 转存任务过期时长（秒）
    transfer-expire-time: 86400
    # 删除任务过期时长（秒）
    delete-expire-time: 86400

    # 删除任务查询间隔时间（毫秒）
    delete-task-query-sleep: 5000
    # 删除任务查询次数（每次10秒钟）
    delete-task-query-times: 6

    # 解析失败原因（错误码对应的描述）
    parse-failed-reason:
      "0000": ""
      "9999": "哎呀，解析过程出了点小意外，请稍后再试试看"
      "10090027": "接口突然罢工啦，请稍后再试试看"
      "10090018": "文件空空如也，补充点有效内容再来试试吧"
      "10090026": "文档没有文字哦，补充点有效内容再来试试吧"
      "999999999": "该文件格式无法解析哦，去换一个格式再试试看吧"
      "105": "文件可能包含敏感内容哦，请重新检查后再试"
      "10070404": "哎呀，解析过程出了点小意外，请稍后再试试看"
      "10070405": "文件空空如也，补充点内容再来试试吧"
      "10070406": "文件可能包含敏感内容哦，请重新检查后再试"
      "10090030": "哎呀，解析过程出了点小意外，请稍后再试试看"
      "10000017": "哎呀，解析过程出了点小意外，请稍后再试试看"
      "01000012": "文件可能包含敏感内容哦，请重新检查后再试"
      "10000015": "文件内容字数太多哦，精简内容后再试试看吧"
      "10090015": "该文件大小超限了哦，去换一个大小再试试看吧"
      "10000019": "该文件格式无法解析哦，去换一个格式再试试看吧"
      "10000006": "该文件大小无法解析哦，去换一个大小再试试看吧"
      "01000005": "文件空空如也，补充点有效内容再来试试吧"
      "10090039": "该文件无法解析哦，去换一个文档再试试看吧"
  # 知识库对话
  dialogue:
    # 指定使用的公共知识库的标识
    knowledge-base-id: "common"
    # 公共知识库白名单
    white-list:
      - "13802885259"
      - "13802883435"
      - "13802885171"
      - "18928819103"
      - "18771000041"
      - "13926431390"
      - "13542899545"
      - "15521090847"
      - "13425458504"
      - "19802021524"
      - "13802885451"
      - "18316688152"
      - "15113990046"
      - "13570559600"
      - "19802025024"
      - "19802021105"
      - "19802021150"
      - "19802021522"
      - "19802021782"
      - "19802021799"
      - "13802885450"
      - "19802025559"
      - "13750326098"
      - "15219891695"
      - "18002239030"
      - "15013271046"
      - "13078882662"
      - "13802882416"
      - "18142851953"
      - "13326349056"
      - "13977527302"
      - "13246408473"
      - "13710660941"
      - "13250164151"
      - "13802885271"
      - "17846876519"
      - "13557589652"
      - "15811852975"
      - "19802024110"
      - "13802885236"
      - "15992549826"
      - "15078267817"
      - "15011750751"
      - "17825902628"
      - "15913080189"
      - "18884581208"
      - "18884681208"
      - "18902224594"
      - "15507829621"
      - "13877725304"
      - "16676302621"
      - "13501525064"
      - "13750326098"
      - "18002239030"
      - "15014317558"
      - "19120129031"
      - "19849977090"
      - "13528277450"
      - "19925811004"
      - "15216248520"
      - "18249907653"
      - "13632481841"
      - "15820472203"
      - "13802885432"
      - "13802885115"
      - "19802021462"
      - "13826074981"
      - "15531012900"
      - "17701952898"
      - "13580574830"
      - "13631272874"
      - "18327863481"
    # 个人知识库开关
    personal-switch: true

    # 开头文案，编码对应枚举KnowledgeBaseEnum
    title-map:
      # 只命中个人知识库
      personal: "根据你的知识库："
      # 只命中公共知识库
      common: "根据我获取的知识："
      # 命中个人知识库和公共知识库
      knowledge: "根据我获取的知识及你的知识库："

    # 可用知识库的渠道
    enable-channel-list: [ 10175 ]

    # 重写配置
    rewrite-config:
      # 重写开关
      enabled: true
      # 重写使用的大模型
      model-code: "qwen_32b"
      # 重写输入内容系统提示
      system-prompt: "你是一个旨在帮助用户更有效检索信息的助手。\n你的主要职责是在用户输入表达不明确的情况下，通过参考#历史对话摘要#和#关键词列表#，对原始问题进行重写。\n你的目标是使问题更加具体和容易被检索，并保持与用户原始意图的一致性\n并且，请不要忽略#原始用户问题#中的内容\n你应该1. 理解背景: 通过阅读历史对话摘要，了解用户之前的对话内容，把握对话的上下文和主题。\n2. 利用关键词: 将关键词融入问题，确保#重写后的问题#包含这些关键词，提高检索的相关性。\n3. 增加细节: 如果用户的问题过于宽泛或模糊，适当添加细节使问题更具体，但不要引入新的概念或信息。\n4. 保持一致性: 确保#重写后的问题#不偏离用户原始的意图或信息需求。\n5. 简洁明了: 保持问题简短而明确，避免冗长或复杂的表述。\n#重写后的问题#只能在#原始用户问题#的基础上增加10-20个字\n#原始用户问题#，#重写后的问题#，#历史对话摘要#，#关键词列表#都不允许出现在#重写后的问题#中\n#历史对话摘要#:{history}\n#关键词列表#:{keywords}"
      # 重写输入内容用户提示
      user-prompt: "#原始用户问题#:{query}\n#重写后的问题#:"
      #问题重写最大字数
      maxLength: 128
      #历史问题数量
      historyNum: 5
    # 召回配置
    recall-config:
      # 召回超时时间，单位毫秒（默认10秒）
      await-time: 1000
      # 结果日志拆分打印，每组数量
      log-group-size: 4
      # commonBase的具体配置项
      common-base:
        # 启用开关，true-打开（默认），false-关闭
        enabled: false
      # personalBase的具体配置项
      personal-base:
        # 启用开关，true-打开（默认），false-关闭
        enabled: true
        # 向量大切片数量
        big-split-size: 10
        # 上分片数
        pre-count: 1
        # 下分片数
        next-count: 1
        # 需要合并上下分片开关，true-打开，false-关闭
        enabled-merge-context: false
      # 【公共知识库】切片配置
      common-split:
        # 公共知识库id
        common-base-id: "common"
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # small to big 配置
        small-to-big-config:
          enabled: true
          min-score: 0.1
          top-n: 100
          merge-size: 2

      # 【公共知识库】问答配置
      common-qa:
        # 公共知识库id
        common-base-id: "common"
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0

      # 【公共知识库】切片的假设性问题配置
      common-split-gqa:
        # 公共知识库id
        common-base-id: "common"
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0

      # 【公共知识库】语义切块配置
      common-gsplit:
        # 公共知识库id
        common-base-id: "common"
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0

      # 【公共知识库】文档总结配置
      common-summary:
        # 公共知识库id
        common-base-id: "common"
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0

      # 【公共知识库】标量查询配置
      common-text:
        # 公共知识库id
        common-base-id: "common"
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0

      # 【个人知识库】切片配置
      personal-split:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: true
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false
        # small to big 配置
        small-to-big-config:
          enabled: true
          min-score: 0.1
          top-n: 100
          merge-size: 2

      # 【个人知识库】切片假设性问题配置
      personal-split-gqa:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 300
        # 问答对，查询条数
        qa-top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【个人知识库】语义切片配置
      personal-gsplit:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 100
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【个人知识库】文档总结配置
      personal-summary:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: false
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

      # 【个人知识库】标量查询配置
      personal-text:
        # 召回开关，true-需要召回（默认），false-不需要召回
        enabled: true
        # 查询最低分数
        min-score: 0.1
        # 查询条数
        top-n: 10
        # 查询权重
        query-weight: 0.0
        # 重评查询权重
        rescore-query-weight: 1.0
        # 结果日志拆分打印，每组数量
        log-group-size: 4
        # 文档标签条件：true-查询，false-不查询（默认）
        label-condition: false

    # 算法重排配置
    rerank-config:
      # 批处理大小，默认256
      batch-size: 256
      # 分段内容的最大长度，超出会截断，默认512
      max-length: 1024
      # 结果日志拆分打印，每组数量
      log-group-size: 4
      # 重排返回条数
      top-n: 20
      # 重排后最小评分
      min-score: 0.3
      # 重排后返回分块字符总长度：25600（默认）
      text-max-length: 25600

    # 重排结果相关性配置
    relevancy-config:
      # 相关性功能开关，true-启用，false-停用
      enabled: false
      # 相关性使用的模型编码
      model-code: "qwen"
      # 相关性用户输入的模板
      user-prompt: "#任务描述\n你是一个判断助手，需要判断每个文本块在回答问题时是否有用。\n\n#输入格式\n- 用户问题：一个问题\n- 文本块列表：{textSize}个文本块\n\n#输出要求\n1. 必须返回一个包含{textSize}个布尔值的列表\n2. True表示这个文本块对回答问题有用\n3. False表示这个文本块对回答问题没用\n4. 返回顺序必须和文本块顺序一致\n\n#示例\n输入：\n用户问题：\"什么是太阳？\"\n文本块列表：[\n    \"太阳是一颗恒星\",\n    \"月亮是地球的卫星\",\n    \"太阳提供光和热\"\n]\n\n输出：\n[True,False,True]\n\n#注意事项\n- 只返回布尔值列表\n- 不要包含任何解释\n- 不要包含任何标点符号\n- 列表长度必须是{textSize}\n\n#实际输入\n用户问题：{query}\n文本块列表：{texts}"
      # 相关性大模型参数配置
      text-model-config:
        # 大模型温度参数
        temperature: 0.0
        # 大模型top_p参数
        top-p: 0.1
        # 随机种子（-1表示不传值）
        seed: 1234

    # 大模型对话配置
    dialogue-config:
      # 模型编码
      model-code: "blian"
      # 大模型系统提示词（role:system）
      system-prompt: ""
      # 大模型输入内容（role:user）
      user-prompt: ""
      # 大模型参数配置
      text-model-config:
        # 大模型温度参数
        temperature: 0.1
        # 大模型top_p参数
        top-p: 0.9
        # 随机种子（-1表示不传值）
        seed: -1

    # 政治人物
    politician-enabled: false
    politician-list:
      - name: "习近平"
        position: "中国共产党中央委员会总书记，中共中央军事委员会主席，中华人民共和国主席，中华人民共和国中央军事委员会主席"
        sort: 50000
        replace-info-list:
          - { replace: "习近平", honorific: "习主席" }
      - name: "李强"
        position: "中共二十届中央政治局常委，国务院总理、党组书记"
        sort: 49900
        replace-info-list:
          - { replace: "李强",honorific: "李总理" }
      - name: "赵乐际"
        position: "中共二十届中央政治局常委，十四届全国人大常委会委员长"
        sort: 49800
        replace-info-list:
          - { replace: "赵乐际", honorific: "赵委员长" }
      - name: "王沪宁"
        position: "中共二十届中央政治局常委，十四届全国政协主席，中央全面深化改革委员会办公室主任"
        sort: 49700
        replace-info-list:
          - { replace: "王沪宁", honorific: "王主席" }
      - name: "蔡奇"
        position: "中央政治局常委、中央书记处书记，中央办公厅主任、中央和国家机关工委书记"
        sort: 49600
        replace-info-list:
          - { replace: "蔡奇", honorific: "蔡书记" }
      - name: "丁薛祥"
        position: "中共二十届中央政治局常委，国务院副总理、党组副书记"
        sort: 49500
        replace-info-list:
          - { replace: "丁薛祥", honorific: "丁书记" }
      - name: "李希"
        position: "中央政治局常委，中央纪律检查委员会书记"
        sort: 49400
        replace-info-list:
          - { replace: "李希", honorific: "李书记" }
      - name: "杨杰"
        position: "董事长"
        sort: 49300
        replace-info-list:
          - { replace: "杨杰", honorific: "杨董事长" }
      - name: "李丕征"
        position: "党组副书记"
        sort: 49200
        replace-info-list:
          - { replace: "李丕征", honorific: "李书记" }
      - name: "李慧镝"
        position: "副总经理"
        sort: 49100
        replace-info-list:
          - { replace: "李慧镝", honorific: "李副总经理" }
      - name: "杨强"
        position: "副总经理"
        sort: 49099
        replace-info-list:
          - { replace: "杨强", honorific: "杨副总经理" }
      - name: "高同庆"
        position: "副总经理"
        sort: 49098
        replace-info-list:
          - { replace: "高同庆", honorific: "高副总经理" }
      - name: "何飚"
        position: "副总经理"
        sort: 49097
        replace-info-list:
          - { replace: "何飚", honorific: "何总经理" }
      - name: "童腾飞"
        position: "纪检监察组组长"
        sort: 49096
        replace-info-list:
          - { replace: "童腾飞", honorific: "童组长" }
      - name: "张冬"
        position: "副总经理"
        sort: 49095
        replace-info-list:
          - { replace: "张冬", honorific: "张副总经理" }
      - name: "张迎新"
        position: "副总经理"
        sort: 49094
        replace-info-list:
          - { replace: "张迎新", honorific: "张副总经理" }
      - name: "李荣华"
        position: "总会计师"
        sort: 49093
        replace-info-list:
          - { replace: "李荣华", honorific: "李总会计师" }
      - name: "陈怀达"
        position: "副总经理"
        sort: 49092
        replace-info-list:
          - { replace: "陈怀达", honorific: "陈副总经理" }
      - name: "程建军"
        position: "副总经理"
        sort: 49091
        replace-info-list:
          - { replace: "程建军", honorific: "程副总经理" }
      - name: "梁高美懿"
        position: "独立非执行董事"
        sort: 49090
        replace-info-list:
          - { replace: "梁高美懿", honorific: "梁高董事" }