rocketmq:
  acl:
    default:
      accessKey: C7mpKgkiEvz1TpACjO82DjzMiaJF
      secretKey: 8Z9iSlxiHCRffMPrl1uxNc29FMiOhk
      access_point_type: NAMESRV_ADDR
      nameServer: 10.169.208.13:31198;10.169.208.14:31198;10.169.208.15:31198
      instanceId: MQ_INST_1695709769806_JTYbsUlk
      instanceName: ai-mq-instance
      sendMsgTimeoutMillis: 3000

  producer:
    # AI助手对话完成
    ai-assistant-dialogue-completed:
      groupId: GID_ALGORITHM_MARKET_AI-ASSISTANT-DIALOGUE-COMPLETED
      topic: TOPIC_ALGORITHM_AI-ASSISTANT_DIALOGUE-COMPLETED
      tag: '*'
    # 用户文档检索报名触发文档正文提取
    topic-local-algorithm-authorize:
      groupId: GID_LOCAL_ALGORITHM_AUTHORIZE_TASK
      topic: TOPIC_LOCAL_ALGORITHM_AUTHORIZE
      tag: '*'
    # 用户上传知识库触发延迟消息-查询转存任务状态
    personal-knowledge-trans-task:
      groupId: GID_LOCAL_ALGORITHM_KNOWLEDGE-FILE-TRANSFER-TASK
      topic: TOPIC_LOCAL_ALGORITHM_KNOWLEDGE-FILE-TRANSFER-DELAY
      tag: '*'
      delay-time-millis: 1000
    # 用户上传邮件/笔记到个人知识库，发起向量化提取任务
    personal-knowledge-dispatch-task:
      groupId: TOPIC_LOCAL_ALGORITHM_DOC-VECTOR-DISPATCH
      topic: TOPIC_LOCAL_ALGORITHM_DOC-VECTOR-DISPATCH
      tag: 'algorithm.local.doc.vector.dispatch'