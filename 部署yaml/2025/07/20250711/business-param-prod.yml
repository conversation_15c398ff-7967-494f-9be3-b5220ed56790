# 业务条件参数
business-param:
  # 历史对话列表（1.0和2.0）
  chatContentList:
    # 限制搜索X天内的历史记录
    queryMaxDays: 90
    # 业务类型查询条件映射
    businessTypeMap:
      # 针对【e-139mail-webai】业务类型，展示多端历史记录
      "e-139mail-webai": [ "e-139mail-webai", "e-139mail-app", "e-mcloud-pc", "e-mcloud-app", "c-mcloud-app" ]
      # 针对【e-mcloud-pc】业务类型，展示多端历史记录
      "e-mcloud-pc": [ "e-139mail-webai", "e-139mail-app", "e-mcloud-pc", "e-mcloud-app", "c-mcloud-app" ]
  # 历史会话列表查询V2接口
  assistantChatV2List:
    # 默认会话iconUrl
    defaultIconUrl: "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_100.png"
    # 会话iconUrl映射
    iconUrlMap:
      "100": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_100.png"
      "200": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_200.png"
      "300": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_300.png"
      "400": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_400.png"
      "40001": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_40001.png"
      "40002": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_40002.png"
      "40003": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_40003.png"
      "40004": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_40004.png"
      "40005": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_200.png"
      "40006": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_200.png"
      "40007": "https://yun.mcloud.139.com/aiassistant/static/tools/icon_type_200.png"
# 对话业务配置
chat:
  text-tool:
    # ai ppt模块开关
    ai-ppt-open: true
    # 必须关闭的渠道列表（open=true也要拦截）
    ai-ppt-must-close-channels: [ ]
    # ai 生成回忆相册开关
    ai-memory-album-open: true
    # ai 生成回忆相册（open=false）关闭条件，允许开启的白名单用户
    ai-memory-album-white-list:
      - "18816799054"
      - "19512885508"
      - "***********"
      - "18520734798"
      - "17512843221"
      - "18202098072"
      - "13316189595"
      - "15992549826"
      - "13602420417"
      - "13802885271"
      - "17846876519"
      - "13680147217"
      - "13802885115"
      - "13802885331"
      - "13922200384"
      - "13922201541"
      - "13802885171"
      - "13802885432"
      - "13802883435"
      - "18390940629"
      - "13710662664"
      - "13242450662"
      - "13416244144"
      - "15876521469"
      - "15918787370"
      - "13527167720"
      - "***********"
      - "13580574830"
      - "15286835083"
      - "18327863481"
      - "***********"
      - "13728075566"
      - "15202095036"
      - "13632481841"
      - "18142851953"
      - "13250164151"
      - "18777645308"
      - "15012773384"
      - "17727766137"
      - "15813304521"
      - "13246408473"
      - "13824452738"
      - "13824482381"
      - "13826074981"
      - "15820472203"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
      - "***********"
    # 文本工具业务配置
    business:
      # aippt生成配置
      ai-ppt-generate:
        search-knowledge-enable: false
        personal-path: '/AI文件库/AI生成PPT'
        personal-cover-path: '/我的应用收藏/AI助手/PPT封面'
        h5-preview-path: 'https://ai.yun.139.com/aippt/web/?taskid={taskid}&token={token}'
        pc-preview-path: 'https://ai.yun.139.com/aippt/web/?taskid={taskid}&token={token}'
        h5-ppt-path: 'https://ai.yun.139.com/aippt/web/?pptid={pptid}&token={token}'
        pc-ppt-path: 'https://ai.yun.139.com/aippt/web/?pptid={pptid}&token={token}'
        template-white-list:
          - ids: [ '5' ]
            user-list: [ '***********', '***********', '***********', '***********', '***********','***********','***********','***********','***********' ]
          # 新模板id白名单配置
          - ids: [ '37','40','41','42','43' ]
            user-list: [ '***********','***********','***********','***********','***********','***********','***********','18085610998','13802885271','17846876519','13560455719','13557589652','15992549826','17825902628','13184547604','***********','***********','18928819103','13542899545','13802883435','13802885234','15876542290','13897249249','13544553014','14797068038','***********','***********' ]
        max-history-count: 10
      # ai生成回忆相册配置
      ai-memory-album:
        search-image-page-size: 2000
        #原文搜图，默认是
        original-search-query: true
        search-filter-end-strs: []
        model-code: qwen
        model-prompt: '你是一个可以根据用户的输入内容描述图片整理需求的专家，将从输入内容中提取以下关键信息：\n输入内容：{query}\n系统时间：{formatted_now}\n\n提取规则：\n 时间范围：必须包含明确的时间周期（如“近3年”“2021-2023”“上个月”），今年、今天、这个月等当下时间描述均来源于系统时间。输出时，转换为yyyy-MM-dd格式的startDate和endDate。相对时间基于系统时间计算：\n“近N年”：startDate为当前日期减N年（保留月日），endDate为当前日期。\n“上个月”：startDate为上个月第一天，endDate为上个月最后一天。\n 绝对范围（如“2022-2024”）：startDate为起始年1月1日，endDate为结束年12月31日。\n 搜索规则：提取筛选方式，如“每年”“每月”“每季度”“每日”“全部”。如果未指定，默认为“全部”。\n 搜索张数：提取所需图片数量，如“5张”“各3张”“全部”。如果未指定，默认为“全部”。\n 搜索主体：提取用户输入内容中的角色实体和时间进行概括（如“我女儿2025年的照片”“13年的家庭合照”），禁止出现张数和其他规则，只出现主体实体和对应对话中的时间，必须唯一且明确。如果模糊（如“照片”），视为无效。\n 关键词：提取内容中的实体描述词，如“女儿”“猫咪”，多个词以英文逗号分隔（如“女儿,猫咪”）。\n输出格式：\n{"startDate": "yyyy-MM-dd","endDate": "yyyy-MM-dd","searchRule": "string","searchSize": "string","searchMainText": "string","keywords": "string"}\n如果输入内容无法识别有效信息（如无搜索主体），则只输出："请重新描述需求，感谢配合！"\n如果输入内容缺少时间信息，则startDate和endDate均为null。\n\n示例参考：\n输入内容：“从2022年到2024年，每季度找1张我和爱人的合照，做成纪念册”\n输出内容：\n{"startDate": "2022-01-01","endDate": "2024-12-31","searchRule": "每季度","searchSize": "1","searchMainText": "2022年到2024年我和爱人的合照","keywords": "爱人、我"}\n\n输入内容：“从2019年到2023年每年找2张我本人的照片，做成纪念册”\n输出内容：\n{"startDate": "2019-01-01","endDate": "2023-12-31","searchRule": "每年","searchSize": "2","searchMainText": "2019年到2023年我本人的照片","keywords": "我"}\n\n输入内容：“我和爱人的合照2张，做成纪念册”\n输出内容：\n{"startDate": "null","endDate": "null","searchRule": "null","searchSize": "2","searchMainText": "我和爱人的合照","keywords": "我、爱人"}\n\n无效输入处理示例：\n输入内容：“找一些照片” （无主体）\n输出内容："请重新描述需求，感谢配合！"'
      # 智能体-智能会议
      intelligentMeeting:
        default-model-code: blian
        default-ppt-template-id: 2
        # 会议发邮件模型和提示词
        meeting-mail-model-code: qwen
        meeting-mail-prompt: '输入指令：\n{query}\n请根据输入指令直接生成会议邀请邮件，仅输出标准输出格式下的邀请邮件，不添加任何额外说明，格式保持一致。信息提取逻辑如下：\n会议主题：从指令中提取核心关键词（如 “智能体打造推进会”）。\n时间：精准抓取指令中的时间描述（如 “明天下午 2 点 - 3 点”），无则标注 “需协调”。\n地点：提取明确的会议室编号或地点（如 “1515 号会议室”），无则标注 “需协调”。\n参会人员：列出指令中所有姓名（如 “张三、李四”），无则标注 “待定”。\n会议内容：根据指令要求拆解为具体条目（如 “各团队汇报当前进展”），补充必要细节（如汇报时长、材料要求）。\n注意事项：注意事项按照标准输出格式输出，如果用户指令中有其他的注意事项就进行补充。\n\n标准输出格式：\n各位好，\n 计划开展{会议主题}，请各位拨冗参加，谢谢。\n\n时间: {会议时间}\n\n地点: {会议室地点}\n\n参会人员: {参会人员名单}\n\n会议内容:\n\n 1.会议内容条目点1。\n\n 2.会议内容条目点2。\n\n 3.会议内容条目点3。\n\n注意事项:\n\n 1.请各位准时参加会议，如有特殊情况请提前请假。\n\n 2.请各团队提前准备汇报材料，内容包括当前进展、存在问题及下一步计划。\n\n 3.会议期间请将手机调至静音状态。期待大家积极参与，共同推进项目进展！\n\n 祝好！\n\n输出示例：\n各位好，\n 计划开展智能体打造推进会，请各位拨冗参加，谢谢。\n\n时间: 明天下午2点-3点\n\n地点: 15号会议室\n\n参会人员: 张三、李四\n\n会议内容:\n\n 1.各团队汇报智能体打造当前进展。\n\n 2.讨论推进中的问题及解决方案。\n\n 3.明确下一阶段的工作计划和目标。\n\n注意事项:\n\n 1.请各位准时参加会议，如有特殊情况请提前请假。\n\n 2.请各团队提前准备汇报材料，内容包括当前进展、存在问题及下一步计划。\n\n 3.会议期间请将手机调至静音状态。期待大家积极参与，共同推进项目进展！\n\n 祝好！'
        # 会议后ppt发邮件模型和提示词
        ppt-mail-model-code: qwen
        ppt-mail-prompt: '请根据输入指令直接生成邮件回复内容，仅输出标准邮件格式，不添加额外说明，逻辑通顺。信息提取逻辑如下：\n\n会议主题：从指令中提取最核心关键词（如 “绩效考核方案修订会”），禁止提取如人名会议时间等与主题不相关的信息。\n会议主题方案：基于会议主题完成的方案名（如 “绩效考核方案修订方案”），默认提及附件（如 “相关材料已整理，见附件”）。\n称呼：统一使用 “各位好” 。\n结尾：固定使用 “烦请查收，谢谢。”\n\n标准输出模板格式：\n{称呼}，\n根据 {会议主题} 沟通，完成{会议主题方案}如附件。\n烦请查收，谢谢。\n\n输入指令：\n{query}\n\n输出示例：\n各位好，\n根据智能体打造推进会沟通，完成智能体规划方案如附件。\n烦请查收，谢谢。\n'
  model:
    # 模型白名单列表
    model-white-list:
      # 开关为false且open-white-list不为空才校验
      - open: false
        model: deepseek_r1_671b
        # 该模型只有白名单用户能返回或设置模型
        open-white-list: [ '13175128922', '15012773384','13247589394','13922200384','13802885115','13802885331','13802885432','***********','18272356772','13570745496','13802882035','***********','13902220355','13802882323','13922200953','13802882226','13802882916','13802885261','13922201502','19802024224','19802021502','19802021744','13802881244','13802881477','13802881670','13802883392','19876456302','***********','15837139811','15918729421','18101034123' ]