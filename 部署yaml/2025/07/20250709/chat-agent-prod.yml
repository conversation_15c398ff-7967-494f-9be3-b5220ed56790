# 应用智能体配置
application-agent:
  # 应用白名单配置
  white-config:
    # 应用id列表维度配置
    application-id-list:
        #配置渠道开启deepseek智能体
      - ids: ['3000000001','3000000002']
        #空则不限制渠道
        support-channels: ['10102','10105','10106','10108','10109','10110','10112','10120','10114','10130','10116','10140','10118','10150','10160','10170','10171','10172','10173','10174','10175','10101','10107','10161']
        #空则不限制用户
        support-phone-numbers: []
        #灵犀-中国移动云盘会议助手
      - ids: ['3000000003']
        #空则不限制渠道
        support-channels: []
        #空则不限制用户
        support-phone-numbers:
          - "19864394830"
          - "13802885171"
          - "13802885259"
          - "15992549826"
          - "13802885271"
          - "13802889418"
          - "13802885331"
          - "13802885115"
          - "13922200384"
          - "13802885432"
          - "13631410550"
          - "19802024191"
          - "19802021503"
          - "19802021829"
          - "18825567261"
          - "19945001904"
          - "15570070016"
          - "13728867183"
          - "19802025209"
          - "18312799630"
          - "13926268370"
          - "13750008593"
          - "13560562127"
          - "13802883512"
          - "19802021779"
          - "15915744770"
          - "***********"
          - "***********"
          - "***********"
          - "***********"
          - "***********"
          - "***********"
          - "***********"
          - "***********"
          - "***********"
          - "***********"
          - "***********"
          - "***********"

  # 灵犀智能体默认配置
  lingxi:
    #演示使用配置
    demo-config:
      #演示控制参数，true为固定basic token  
      demo: false
      basic-token: ''
    #业务配置
    business-config:
      channel: '101'
      application-id: '3000000003'
      mail-edit-jump-url: 'https://html5.mail.10086.cn/logins/?func=login:authTokenPE&token={token}&targetSourceId=001003&loginSuccessUrl=https%3A%2F%2Fhtml5.mail.10086.cn%2Fhtml%2Fcompose.html%3FdialogueId%3D{dialogueId}%26clientId%3D10176'
      mail-send-jump-url: 'https://html5.mail.10086.cn/logins/?func=login:authTokenPE&token={token}&targetSourceId=001003&loginSuccessUrl=https%3A%2F%2Fhtml5.mail.10086.cn%2Fhtml%2FmailList.html%3Ffid%3D3%26clientId%3D10176'
      note-voice-jump-url: 'https://yun.139.com/ai-helper-lingxi/pages/notes/audioRecord?token={token}'
      ai-helper-jump-url: 'https://yun.139.com/ai-helper-lingxi/?tab=aiChat&toolType=4&token={token}'
