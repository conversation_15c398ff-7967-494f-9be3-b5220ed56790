#搜索提示配置
search-prompt:
  # 搜索引导
  guide:
    search-image:
      - "搜索图片时，您可以尝试输入具体的主题，比如：搜索夜景的图片。"
      - "为了让搜索更准确，请描述您想要查看的图片样式，比如：搜索建筑的图片。"
      - "试着用搜索你在某个地点的图片，例如：搜索在广州拍摄的图片。"
      - "您可以尝试这样输入您的搜索内容，比如：搜索2023年12月在广州的图片。"
      - "猜你想搜图片，假如要搜索美食图片，请加上具体的食物名称或类型，比如：搜索蛋糕的图片。"
      - "想要找到某个时间段的图片吗？请输入相应的时间试试，比如：搜索2023年的图片。"
      - "如您想搜索动物图片，请输入动物的名称，比如：搜索小猫的图片。"
      - "为了获得更准确的搜索结果，请您尽可能描述您想要搜索的图片样式，比如：搜索海边度假图片。"
    search-document:
      - "请提供搜索文件的关键词，您可以尝试输入：搜索每周例会会议纪要文档。"
      - "请明确您的搜索目标，例如：搜索财务报表相关的Excel文件。"
      - "搜索文件时，您可以尝试输入文件名或文件类型，比如：搜索pdf格式的文件。"
      - "为了更精确地找到文件，请在搜索要求中加入文件标题中的关键词，比如：搜索涉及项目计划的文件。"
      - "如果您正在寻找某个格式的文件，请尝试输入：搜索DOCX格式的文件。"
      - "搜索报告文件时，请输入相关的主题或关键词，比如：搜索市场调研报告。"
      - "请提供文件的时间范围或日期，以便更精确地搜索，例如：搜索今年1月份的所有文档。"
    search-video:
      - "请提供搜索视频的关键词，您可以尝试输入：搜索mp4视频。"
      - "请提供搜索视频的关键词，您可以尝试输入：搜索今年上传的旅游视频。"
      - "搜索视频时，请明确视频格式，比如：搜索MP4格式的教学视频。"
      - "如果您记得视频的名称，请输入进行搜索，比如：搜索《星球大战》电影。"
      - "搜索特定话题的视频时，请尝试输入：搜索健身教程相关的视频。"
      - "如果想找到特定时间的视频，请输入上传时间范围，比如：搜索最近半年上传的视频。"
    search-audio:
      - "请提供搜索音频的关键词，您可以尝试输入：搜索mp3音频。"
      - "请提供搜索音频的关键词，您可以尝试输入：搜索mp3格式的演讲音频。"
      - "搜索音频时，请明确音频格式，比如：搜索mp3格式的音乐。"
      - "如果您记得音频的名称，请输入进行搜索，比如：搜索《月光奏鸣曲》音频。"
      - "为了找到特定时间的音频，请输入上传时间范围，比如：搜索去年上传的英语听力音频。"
    search-folder:
      - "请提供搜索文件夹的关键词，您可以尝试输入：搜索AI助手文件夹。"
      - "尝试使用更具体的描述来搜索文件夹，以便我更好地帮助您找到它们，比如：搜索名为项目资料的文件夹。"
      - "如果您正在寻找特定日期创建的文件夹，请尝试输入：搜索2023年创建的文件夹。"
      - "请提供文件夹的名称作为关键词，比如：搜索客户合同相关的文件夹。"
    search-note:
      - "请提供搜索笔记的关键词，您可以尝试输入：搜索与“年度计划”相关的笔记。"
      - "搜索笔记时，请尝试输入笔记的标题，比如：搜索标题为“产品设计思路”的笔记。"
      - "如果您记得笔记的部分内容，请输入正文中的关键词，比如：搜索包含“市场调研”的笔记。"
      - "搜索关于某个项目的笔记时，请尝试输入项目名称，比如：搜索“XX项目进展”的笔记。"
      - "为了找到最近创建的笔记，请输入时间范围，比如：搜索本周内创建的笔记。"
      - "如果您记得笔记的某个关键词组合，请尝试输入它们，比如：搜索标题为Python编程和数据分析的笔记。"
      - "为了找到与某个主题相关的笔记，请输入主题名称，比如：搜索“人工智能”主题的笔记。"
    comprehensive-search:
      - "猜你想搜索多种内容，可以尝试输入：搜索2023年的图片和文档。"
      - "猜你想更全面的搜索云盘的内容，可以尝试输入：搜索今年的图片和文档。"
    search-activity:
      - "猜你想找找云盘的活动，您可以尝试输入：搜索云朵大作战。"
      - "猜你想找找云盘的活动，您可以尝试输入：搜索云朵中心。"
    search-function:
      - "请提供搜索功能的关键词，您可以尝试输入：搜索相册备份功能。"
      - "搜索云盘功能时，如果您想找到备份相关功能，可以输入：搜索手机备份相关功能。"
    search-discovery:
      - "请提供具体关键词，以便我能更精准地为您找到相关资源。"
      - "若能补充相关关键词，我将为您更精确地查找对应资源。"
      - "请给出明确关键词，我会帮您更准确地定位所需资源。"
      - "提供详细关键词，有助于我更精细地为您搜寻相关资源。"
      - "请明确一下关键词，我会更精准地为您检索相关内容。"
      - "补充具体关键信息，我能更精确地帮您寻找相关资源。"
      - "请给出确切关键词，我会帮您更准确地发掘相关资源。"
      - "补充关键描述词，我会更精准地为您查找匹配资源。"
      - "提供相关特定关键词，我会更精细地为您获取对应资源。"
      - "明确要搜索的资源关键词，我会更精确地为您搜索目标资源。"
      - "请给出清晰的关键词，我会帮您更精准地找到所需资料。"
      - "提供更确切的搜索内容，我会更准确地为您搜寻相关资源。"
      - "补充具体的搜索资源名称，我会帮您更精细地查找匹配内容。"
      - "明确相关的搜索内容名称，我会更精准地为您定位目标资源。"
    search-group:
      - "请提供搜索圈子关键词，您可以尝试输入：搜索影视相关的圈子。"
      - "请提供搜索圈子名称的关键词，例如可以输入：搜索办公相关的交流圈。"
      - "搜索圈子时，可以搜索圈名进行搜索，例如：搜索健身俱乐部的圈子。"
      - "如果要找到对应的圈子，请输入圈子名称，比如：帮我搜索学习PPT的圈子。"
      - "搜索圈子时，可以输入想搜索的动态内容，比如：帮我搜索高分口碑电影相关动态。"
      - "圈子中有包含影视的动态内容，比如：帮我搜索生活的相关动态。"
    search-mail:
      - "请提供搜索邮件的时间关键词，您可以尝试输入：搜索今天收到的重要邮件。"
      - "请提供搜索收件范围的关键词，您可以尝试输入：帮我查找最近7天的重要邮件。"
      - "搜索邮件时，可以通过发送人进行搜索，例如：近期小明发的有哪些未读重要来信？"
      - "如果要找到重要的邮件，可以输入：今天有什么急需处理的邮件吗？"
    search-knowledge-base-resource:
      - "请提供具体关键词，以便我能更精准地为您找到相关知识库文件。"
      - "若能补充相关关键词，我将为您更精确地查找对应知识库文件。"
      - "请给出明确的关键词或内容名称，我会帮您更准确地定位相关知识库内容。"
      - "提供更具体的关键词或内容名称，我会更精确地为您获取对应知识库内容。"

  # 搜索提示
  prompt:
    search-image:
      - "没有符合条件的图片哦，试试换个关键词搜索吧。"
      - "似乎没有找到与你的搜索请求相匹配的图片，你可以试试其他描述或关键词。"
      - "图片没找到，试试换个描述吧，越详细我找的越准。"
    search-document:
      - "没找到你想要的文档，建议更换搜索条件后再次尝试。"
      - "未能找到满足你需求的文档，换个搜索条件，我再帮你搜索一下。"
      - "没有找到与你的搜索词匹配的文档，换个关键词试试，我继续帮你找文档。"
    search-video:
      - "未找到相关视频，换个搜索词，我再帮你找找看。"
      - "未找到符合条件的视频，用更具体的描述或关键词，我帮你更快找到它。"
    search-audio:
      - "你的云盘中未能找到符合你需求的音频，换个搜索条件，我再帮你搜索一下。"
      - "没找到与你想要的音频，用更具体的描述或关键词，我帮你找出来。"
    search-folder:
      - "没有找到你想要的文件夹，换个搜索条件，我再帮你翻一翻。"
      - "没找到符合条件的文件夹，试试换个搜索条件，我帮你找出来。"
      - "没有找到与你的搜索词匹配的文件夹，换个关键词试试，我帮你精准定位文件夹。"
    search-note:
      - "笔记没找到哦，换个关键词试试，我再帮你找找看。"
      - "未能找到相关笔记，换个搜索条件，我再帮你搜索一下。"
    comprehensive-search:
      - "没找到你想要的内容，你可以换个关键词，说不定就有新发现！"
      - "未能找到相关内容，用更具体的描述试试，我帮你更快定位到它。"
    search-activity:
      - "没搜到你想要的活动，你可以去活动中心，体验云盘更多活动。"
    search-function:
      - "没搜到你想要的功能，换个关键词试试，我再帮你找找看。"
    search-discovery:
      - "资源没找到哦，换个关键词试试，我再帮你找找看。"
      - "没搜到你想要的资源，换个搜索条件，我再帮你搜索一下。"
    search-group:
      - "没找到你想要的圈子，换个关键词试试吧，我再帮你找找看。"
      - "云盘中未能找到符合你需求的圈子或动态，你可以去云盘圈子频道，有更多有趣的内容哦！"
      - "未能找到你想要的圈子和动态，换个关键词试试，我帮你找出来。"
      - "未能找到你想要的圈子和动态，修改下搜索条件，我再帮你搜索一下。"
      - "没有找到与你的搜索词匹配的圈子，建议你再试试其他关键词。"
    search-mail:
      - "未能找到对应的邮件，试试换个时间段搜索一下。"
      - "似乎没有找到你的重要邮件，建议你再试试其他描述或关键词。"
      - "未能找到你想要的邮件，试试换个关键词！"
    search-knowledge-base-resource:
      - "您的知识库中未找到相关内容，您可以上传更多文档，让知识库内容更加丰富"

  # 云邮助手-搜索引导
  mail-guide:
    search-mail:
      - "请提供搜索邮件的时间关键词，您可以尝试输入：搜索今天收到的邮件。"
      - "请提供搜索收件范围的关键词，您可以尝试输入：帮我查找最近7天的邮件。"
      - "搜索邮件时，可以通过发送人进行搜索，例如：近期小明发的有哪些未读来信？"
      - "如果要找到重要的邮件，可以输入：今天有什么急需处理的邮件吗？"
    search-knowledge-base-resource:
      - "请提供具体关键词，以便我能更精准地为您找到相关知识库文件。"
      - "若能补充相关关键词，我将为您更精确地查找对应知识库文件。"
      - "请给出明确的关键词或内容名称，我会帮您更准确地定位相关知识库内容。"
      - "提供更具体的关键词或内容名称，我会更精确地为您获取对应知识库内容。"
  # 云邮助手-搜索提示
  mail-prompt:
    search-image:
      - "抱歉，没有搜索相应的数据，请重新搜索。"
    search-document:
      - "没找到你想要的文档，建议更换搜索条件后再次尝试。"
      - "未能找到满足你需求的文档，换个搜索条件，我再帮你搜索一下。"
      - "没有找到与你的搜索词匹配的文档，换个关键词试试，我继续帮你找文档。"
    search-mail:
      - "未能找到对应的邮件，试试换个时间段搜索一下。"
      - "似乎没有找到你的邮件，建议你再试试其他描述或关键词。"
      - "未能找到你想要的邮件，试试换个关键词！"
    search-note:
      - "笔记没找到哦，换个关键词试试，我再帮你找找看。"
      - "未能找到相关笔记，换个搜索条件，我再帮你搜索一下。"
    search-knowledge-base-resource:
      - "您的知识库中未找到相关内容，您可以上传更多文档，让知识库内容更加丰富"
# 搜索意图配置
search-intention-properties:
  # 搜索意图转综合搜索开关：true-打开；false-关闭（默认）
  convertComprehensiveSearch: true
  # 拼接的搜索意图前缀
  searchPrefix: "帮我搜"

# 搜索结果配置
search-result-properties:
  # 排序开关 true表示以配置文件的排序为主 false表示以算法的排序为主
  enableSort: true
  # tab展示顺序
  tab-sort-map:
    # 语义搜图
    image: { type: "image", name: "图片", nameEn: "Photo", defaultSort: 102, matchSort: 102, keywords: "" }
    # 个人资产搜索
    file: { type: "file", name: "文件", nameEn: "Document", defaultSort: 101, matchSort: 101, keywords: "" }
    # 笔记搜索
    note: { type: "note", name: "笔记", nameEn: "Note", defaultSort: 103, matchSort: 103, keywords: "" }
    # 邮件搜索
    mail: { type: "mail", name: "邮件", nameEn: "Mail", defaultSort: 104, matchSort: 104, keywords: "" }
    # 发现广场搜索
    discovery: { type: "discovery", name: "发现", nameEn: "Resource", defaultSort: 105, matchSort: 105, keywords: "试卷、试题、测试题、检测题、单元检测、单元测试、测试卷、综合评测、练习卷、练习试题、AB卷、专题强化练、复习资料、真题、新题、练、题、专项训练、专练、考点专题、讲练测、押题、甲乙卷、电影、短剧、综艺节目、纪录片、动画、电视剧、综艺、节目、大片、4K电影、高清大片、蓝光、原片、剧集、番剧、院线电影、新电影、图书、书籍、书本、书、童书、读物、原著、著作、册、画册、词典、教材、小说、漫画、音频、MP3、无损音乐、音乐、听歌、电台、伴奏、歌单、录音、听力材料、播客、视频、短片、短剧、纪录片、TV、集锦、花絮、volg、实录、特辑、数字藏品、数字艺术藏品、区块链藏品、数字收藏品、有声书、有声读物、电子书、有声、故事、图画、插画、壁纸、摄影作品、全景图、封面、海报、PPT模板、素材、模版、手册、指南" }
    # 功能搜索
    function: { type: "function", name: "功能", nameEn: "Function", defaultSort: 108, matchSort: 108, keywords: "备份、保险箱、云换机、拍照上传、美图相册、家庭云、共享群、文件收集、照片直播、亲情圈、漂流瓶、老照片修复、智能抠图、活照片、AI头像、AI漫画风、AI消除、画质修复、AI写真、文生图、图片配文、朋友圈9图、图片清理、找合照、影集创作、AI修图、PDF工具、文件、文档、视频、音频、图片、最近在看、最近在听、创建故事、圈子首页、圈子发布、新建家庭、分享管理、笔记、语音转写、文字识别、发现、会员订购、会员查询、回收站、我的收藏、我的卡包" }
    # 活动搜索
    activity: { type: "activity", name: "活动", nameEn: "Promotion", defaultSort: 109, matchSort: 109, keywords: "云朵、福利、签到、兑换、抽抽乐、摇一摇、抽奖、备份有礼、欢乐透、月月抽好礼、赢好礼、抽奖码、开奖、焕新礼、3个月会员、新人体验礼、免费会员、 领1T、大空间、免费空间、备份有礼、云朵大作战、玩转公众号、公众号、小云果园、果园、种树、新年新头像、AI头像、AI新头像、换头像、邀请、组团、立减金、红包、现金、助力、猜谜、猜灯谜、灯谜、猜灯谜开红包、盲盒、回馈、云端看电影、云手机抽奖、相册达人、AI新魔法、会员日、宠粉日、宠爱日、会员专享、每月16日" }
    # 我的圈子搜索
    myGroup: { type: "myGroup", name: "我的圈子", nameEn: "My Circle", defaultSort: 106, matchSort: 106, keywords: "" }
    # 热门圈子搜索
    recommendGroup: { type: "recommendGroup", name: "热门圈子", nameEn: "Featured Circle", defaultSort: 107, matchSort: 107, keywords: "" }
    # 邮件附件搜索
    mailAttachment: { type: "mailAttachment", name: "邮件附件", nameEn: "Mail Attachment", defaultSort: 110, matchSort: 110, keywords: "" }
    # 知识库资源搜索
    knowledgeBaseResource: { type: "knowledgeBaseResource", name: "知识库资源", nameEn: "knowledgeBaseResource", defaultSort: 120, matchSort: 120, keywords: "" }
  # 搜索结果推荐配置
  recommend-map:
    # 功能搜索
    function: { type: "function", intentionTemplate: "如何使用%s功能？" }
    # 知识库资源搜索
    knowledgeBaseResource: { type: "knowledgeBaseResource", intentionTemplate: "知识库也支持搜索，试试搜一下" }
    # 大模型回答
    largeModelAnswer: { type: "largeModelAnswer", intentionTemplate: "试一试让大模型来帮你解答" }

# 搜索条件配置
search-param-properties:
  # 过滤器
  filter-map:
    # 搜索范畴
    category:
      film:
        - { type: "film", desc: "影视类", filterKeywords: "视频、影视、影集、综艺节目、集锦、电影、短剧、综艺节目、电视剧、综艺、节目、片子、大片、4K、4K电影、资源、大片资源、高清大片、蓝光、原片、剧集、番剧、院线电影、新电影、高清片、蓝光片、电影大片、4K大片、影视、影视资源、作品、导演、影视剧、高清、动画片、纪录片、动画、蓝光电影、热门、种子、链接、片源、韩剧、短剧、微剧、微短剧、短剧集、小剧场、剧情短片、竖屏短剧、迷你剧、超短剧、短视频、短视频剧、单元剧、热剧、全剧、影片" }
      book:
        - { type: "book", desc: "书籍类", filterKeywords: "图书、书籍、书本、童书、读物、原著、著作、画册、小说、教材、教科书、漫画、作品、绘本、杂志、期刊、字典、专刊" }
      document:
        - { type: "document", desc: "文档类", filterKeywords: "文档、文件、附件、压缩文件、账单、报表、材料、工作文档、项目文档、需求文档、原型文件、汇报材料、测试用例、报告、演示文稿、接口文档、教学资料、作业、课件、病历、合同、法律文件、电子表格、试卷、试题、测试题、复习资料、真题、素材、模版、手册、指南、说明书" }
      audio:
        - { type: "audio", desc: "音频类", filterKeywords: "音频、手机音乐、音乐、电子书、录音、MP3、有声书" }
      note:
        - { type: "note", desc: "笔记", filterKeywords: "笔记" }
      mail:
        - { type: "mail", desc: "邮件", filterKeywords: "邮件、电子邮件、信件" }
      function:
        - { type: "function", desc: "功能类", filterKeywords: "功能、能力" }
      activity:
        - { type: "activity", desc: "活动类", filterKeywords: "活动、新活动、福利活动" }
      image:
        - { type: "image", desc: "图片", filterKeywords: "图片、照片、相册、合影、合照、图像、截图" }
      group:
        - { type: "group", desc: "圈子", filterKeywords: "圈子、社群、论坛、群组、社区、联盟、粉丝团、交流群、小组" }
    # 公共
    common:
      queryTypes:
        - { type: "common", desc: "公共", filterKeywords: "电影、短剧、综艺节目、电视剧、综艺、节目、片子、大片、4K、4K电影、资源、发现、发现资源、云盘资源、高清大片、蓝光、原片、剧集、番剧、院线电影、新电影、高清片、蓝光片、电影大片、4K大片、影视、影视资源、作品、导演、影视剧、高清、动画片、纪录片、动画、蓝光电影、热门、种子、链接、片源、视频、电影资源、资源链接、视频链接、动漫资源、动漫电影、视频文件、大电影、奇幻电影、1080p、TS电影、TD电影、宽屏、竖屏、短视频、全集、哼唱、旋律、歌词" }
    # 发现广场搜索
    discovery:
      queryTypes:
        - { type: "1", desc: "全部", filterKeywords: "" }
        - { type: "3", desc: "文档", filterKeywords: "文档、资料、合同、文件、报表、策划书、Excel模版、word模版、简历模版、论文答辩模版、工作模版、实用模版、模版素材、热门" }
        - { type: "6", desc: "视频", filterKeywords: "短片、短剧、纪录片、vlog、热门" }
        - { type: "10", desc: "试卷", filterKeywords: "题目、严选、仿真题、备考题、卷子、试题集、评估卷、答卷、题库、小考、训练题、基础知识测试、单元测评、同步检测、过关练习、过关训练、强化练习、高考卷、热门" }
        - { type: "11", desc: "影视", filterKeywords: "电影、短剧、综艺节目、电视剧、综艺、节目、片子、大片、4K、4K电影、资源、发现、发现资源、云盘资源、高清大片、蓝光、原片、剧集、番剧、院线电影、新电影、高清片、蓝光片、电影大片、4K大片、影视、影视资源、作品、导演、影视剧、高清、动画片、纪录片、动画、蓝光电影、热门、种子、链接、片源" }
        - { type: "12", desc: "其他", filterKeywords: "MP3、无损音乐、FLAC、WAV、AAC、音乐下载、在线听歌、音乐视频、原创音乐、音乐、音频、图片、图、图像、图画、照片、壁纸、音乐电台、KTV伴奏、翻唱歌曲、流行歌曲、经典老歌、摇滚乐、爵士乐、民谣、电子音乐、嘻哈音乐、乡村音乐、世界音乐、歌单、语言学习、日语口语、在线课程音频、教育讲座、教材录音、考试辅导、学术讲座、名校公开课、技能教程、Podcast、电台节目、音频博客、访谈节目、科技播客、文化评论、旅行播客、心理健康播客、历史播客、讲座录音、演讲音频、公开课音频、TED演讲、行业论坛、专家讲座、学术研讨会、励志演讲、自然声音、白噪音、放松音乐、睡眠辅助音频、冥想引导、大自然音效、环境音疗愈、新闻广播、壁纸、截图、高清图、摄影作品、全景图、封面、海报、渲染图、热门、发现、资源" }
        - { type: "13", desc: "书籍", filterKeywords: "图书、书籍、书本、书、咪咕阅读、掌阅小说、小说、参考书、教材辅导、童书、同名原著、画册、词典、历史书籍、传记纪实、工具书、漫画书、教材辅导、心灵鸡汤、休闲阅读、杂文随笔、好书推荐、热门书籍、必读清单、有声读物、电子书音频、有声小说、播客、有声故事、有声漫画、有声教材、有声剧本、有声杂志、有声新闻、有声传记、有声科普、漫画风、热门" }
        - { type: "15", desc: "短剧", filterKeywords: "短剧、微剧、微短剧、短剧集、小剧场、剧情短片、竖屏短剧、迷你剧、超短剧、短视频、短视频剧、单元剧、热剧、全剧、影片" }

# 全网搜配置
all-network-search:
  # 执行开关
  execute: true
  # 模型编码
  modelCode: "blian_qwen3_235b"
  # 意图过滤条件
  intentionFilterCondition:
    # 语义搜图
    "014": { queryTypeList: [] }
    # 发现广场（1-全部；6-视频；11-影视）
    "022": { queryTypeList: [ "1", "6", "11" ] }
  # 拼接的后缀
  suffix: "在云盘找不到？试试全网搜索"
  # 按钮文案
  buttonCopy: "马上搜"
  # 【全网搜查看更多推荐】推荐问题
  searchMoreRecommendQuery: "没有想要的？点击查找更多"
  # 【全网搜查看更多推荐】按钮文案
  searchMoreRecommendButtonCopy: "马上搜"
  # 是否搜小站：true-是，false-否
  searchPanta: true
  # 小站搜索参数
  searchPantaParam:
    # 是否搜成人：1-成人；0-未成年人
    adult: 0
    # 是否搜国内：1-国内；0-国外
    # domestic: 1
    # 搜索优先级列表（默认0, 1）
    # priorityList: [ 0, 1 ]
    # 搜索文件类型范围（1-视频；2-音频；3-文档）
    businessResourceTypeList: [ 1 ]
  # 搜索实体抽取配置
  search-entity-extract:
    # 是否返回全部实体列表
    returnAll: false
    # 单条文本内容的最大长度，默认最大1024字符，上限4096字符
    maxLength: 1024
    # 搜索实体抽取标签类型
    entityTypeList: [ "电影", "电视剧", "主演", "导演", "动漫" ]
  # 搜索实体重写正则
  searchEntityRewriteRegex: "的?(?:电影|电视剧|视频|资源|影视作品|作品|片子|短剧|动画|动漫|剧集|连续剧|综艺|纪录片)"
  # 第三方搜索配置 网盘链接
  third-party-kind:
    - " 百度网盘 资源分享 提取码 网盘链接 提取码失效修复 网友分享 百度云链接 分享码 百度盘补档 网盘更新"
    - " 夸克网盘 资源分享 夸克下载 提取码 网盘链接 夸克种子 资料合集 高清 完整版"
    - " 天翼云盘 资源分享 天翼下载 天翼云提取码 天翼云链接 天翼种子 资料合集 高清 完整版"
    - " 迅雷 资源分享 迅雷下载 提取码 种子链接 种子 BT下载 磁力链接 电影种子 迅雷资源 迅雷分享"
    - " 阿里云盘 资源分享 阿里云盘下载 资源分享链接 阿里云链接 阿里云盘提取码 阿里云资源"
  # 第三方搜索配置 正版平台
  third-party-kind-genuine:
    - " 正版平台名称 版本 上线时间 提示"
  # 第三方搜索配置 在线网址
  third-party-kind-on-line:
    - " 在线观看渠道名称 渠道链接 描述"
  # 多线程混合搜索等待时间，单位：秒
  search-wait-time: 20
  # ai全网搜调大模型入参配置
  text-model-config:
    # 大模型温度参数
    temperature: 0.0
    # 大模型top_p参数
    top-p: 0.1
    # 随机种子（-1表示不传值）
    seed: 1024
  # 【正版平台】链接前缀（白名单）
  genericPlatformLinkPrefix:
    - name: "腾讯视频"
      linkList: [ "https://v.qq.com/" ]
    - name: "爱奇艺"
      linkList: [ "https://www.iqiyi.com/" ]
    - name: "优酷视频"
      linkList: [ "https://www.youku.com/" ]
    - name: "芒果TV"
      linkList: [ "https://www.mgtv.com/" ]
    - name: "哔哩哔哩"
      linkList: [ "https://www.bilibili.com/" ]
    - name: "西瓜视频"
      linkList: [ "https://www.ixigua.com/" ]
    - name: "咪咕视频"
      linkList: [ "https://www.miguvideo.com/" ]
    - name: "央视频"
      linkList: [ "https://www.yangshipin.cn/" ]
    - name: "搜狐视频"
      linkList: [ "https://tv.sohu.com/" ]
    - name: "人人视频"
      linkList: [ "https://www.rr.tv/" ]
    - name: "PP视频"
      linkList: [ "https://www.pptv.com/" ]
    - name: "风行视频"
      linkList: [ "https://www.fongson.com/" ]
    - name: "百度视频"
      linkList: [ "https://haokan.baidu.com/" ]
    - name: "好看视频"
      linkList: [ "https://www.360kan.com/" ]
    - name: "乐视视频"
      linkList: [ "https://www.le.com/" ]
    - name: "腾讯微视"
      linkList: [ "https://www.weishi.com/" ]
    - name: "火山视频"
      linkList: [ "https://www.huoshan.com/" ]
    - name: "抖音"
      linkList: [ "https://www.douyin.com/" ]
    - name: "快手"
      linkList: [ "https://www.kuaishou.com/" ]
    - name: "AcFun"
      linkList: [ "https://www.acfun.cn/" ]
    - name: "HBO"
      linkList: [ "https://www.hbomax.com/" ]
    - name: "Netflix"
      linkList: [ "https://www.netflix.com/" ]
    - name: "Disney+"
      linkList: [ "https://www.disneyplus.com/" ]
  # 【网盘链接】链接前缀（白名单）
  cloudStorageLinkPrefix:
    - "https://pan.baidu.com/"
    - "http://pan.baidu.com/"
    - "https://pan.quark.cn/"
    - "http://pan.quark.cn/"
    - "https://cloud.189.cn/"
    - "http://cloud.189.cn/"
    - "https://pan.xunlei.com/"
    - "http://pan.xunlei.com/"
    - "https://www.aliyundrive.com/"
    - "http://www.aliyundrive.com/"
    - "http://caiyun.139.com/"
    - "https://caiyun.139.com/"
    - "https://www.91panta.com/"
    - "http://www.91panta.com/"
    - "https://yun.139.com/"
    - "http://yun.139.com/"
    - "https://www.aliyundrive.com/"
    - "http://www.aliyundrive.com"
  # 【在线网址】链接前缀（黑名单）
  onlineUrlLinkPrefix:
    - "www.ahsnxc.com"
    - "www.meijui.cc"
    - "www.hrdjy.com"
    - "www.hktvyun.com"
    - "www.922vod.com"
    - "vip.fnztbw.com"
    - "m.shnakun.com"
    - "m.njbaige.com"
    - "www.houde100.com"
    - "v2.dilidili18.com"
    - "www.pgyxy.com"
    - "t.maicai.la"
    - "www.fvvfu.com"
    - "fvvfu.com"
    - "www.9293tv.com"
    - "www.53t.net"
    - "qi5bgq.huhu348.com"
    - "www.826dm.com"
    - "www.hzbohai.com"
    - "www.syyayan.com"
    - "www.yueyu2.com"
    - "www.tvyb10.com"
    - "cupfoxappp.com"
    - "www.115tv.com"
    - "vip.fnztbw.com"
    - "www.zyparking.com"
    - "www.zdxyzys.com"
    - "lymxdl.com"
    - "www.maaa.cc"
    - "www.sxgujue.com"
    - "www.skkpsj.com"
    - "www.nidetech.com"
    - "www.zyparking.com"
    - "www.gxqht.cn"
    - "m.yidian68.com"
    - "www.czylffbw.com"
    - "www.933vod.com"
    - "www.53t.net"
    - "m.923vod.com"
    - "m.yidian68.com"
    - "www.pkmkv.com"
    - "m.soyouf.com"
    - "www.kiradesign.cn"
    - "m.idvdi.com"
    - "www.cmsyy.cc"
    - "www.ccoop.cn"
    - "m.jcdtx.com"
    - "www.jiasyoule.com"
    - "www.kingmax.me"
    - "www.yingyuanvs.net"
    - "www.gangjuwang.cc"
    - "www.huolangdm.com"
    - "www.dgd3x.com"
    - "m.idvdi.com"
    - "v.huashayy.cc"
    - "qiqidm7.com"
    - "www.00o.cc"
    - "www.xygzxy.com"
    - "www.hailan.tv"
    - "www.biquanzi.cc"
    - "www.xxshida.com"
    - "m.cupfoxap.com"
    - "mylhz.com"
    - "www.ksxlkt.com"
    - "www.twhitec.com"
    - "www.lfwomen.com"
    - "www.gzqgdg.com"
    - "www.e-cust.com"
    - "www.viphktv.com"
    - "www.poxiwar.com"
    - "www.dadatu98.com"
    - "www.shunshigaoke.com"
    - "www.damiysw1.com"
    - "www.gy810.com"
    - "m.hfctwy.com"
    - "www.viphktv.com"
    - "www.poxiwar.com"
    - "www.qimush.com"
    - "www.houde100.com"
    - "yhdm73.com"
    - "vip.fnztbw.com"
    - "m.ximalaya.com"
    - "v.xsszy.com"
    - "v.lzhsqh.com"
    - "www.922vod.com"
    - "www.hktvyun.com"
    - "m.hzczwh.com"
    - "ttketang.com"
    - "m.idvdi.com"
  # 需要检查的关键字
  checkKeywords: [ "天翼云盘", "百度网盘", "阿里云盘", "夸克网盘", "迅雷" ]
  # 搜索为空的返回语句
  empty-result-text:
    - "没有找到你想要的资源，调整一下搜索条件，我再帮你找找看。"
    - "没搜你想要的资源，换个关键词试试，我继续为你搜寻！"
  # 搜索实体/搜索结果命中黑名单返回语句
  black-text: "为了保护创作者权益和您的合法权益，我们建议您通过正规视频平台、官方授权渠道或线下合法购买等方式观看/获取，支持正版就是支持创作！此外，根据相关法律法规，未经授权的传播或下载行为可能涉及侵权风险，感谢您的理解与配合，让我们共同维护健康的网络环境！"
  # 版权提示
  copyrightNotice: "为了保护创作者权益和您的合法权益，我们建议您通过正规视频平台、官方授权渠道或线下合法购买等方式观看/获取，支持正版就是支持创作！此外，根据相关法律法规，未经授权的传播或下载行为可能涉及侵权风险，感谢您的理解与配合，让我们共同维护健康的网络环境！"
  # 【视频搜索】注意事项列表
  videoSearchPrecautionList:
    - "\n**注意事项**\n**1.版权与合法性**\n**支持正版**：请优先选择正版平台观看（如腾讯视频、爱奇艺、优酷、哔哩哔哩等），避免使用非法下载链接。\n**法律风险**：根据《中华人民共和国著作权法》，传播未经授权的影视资源可能面临行政处罚或民事赔偿。\n**2.安全性**\n使用非官方资源时需谨慎，避免恶意软件或诈骗链接。\n"
    - "\n**注意事项**\n**1.支持正版**：推荐通过腾讯视频、爱奇艺、优酷等官方平台观看，享受高清正版内容。\n**2.安全提示**：非正规渠道可能存在恶意软件或隐私泄露风险，请谨慎选择。\n**3.法律声明**：未经授权的影视传播违反《著作权法》，请自觉遵守法律法规。\n"
    - "\n**注意事项**\n**1.版权问题**：非官方渠道的资源可能涉及侵权，建议优先选择正版平台。\n**2.安全性**：非官方资源链接需通过可信来源验证，避免下载不明文件。\n"
    - "\n**注意事项**\n**1.支持正版**：推荐使用腾讯视频、爱奇艺、芒果TV 等官方平台，享受高清流畅的正版内容。\n**2.法律须知**：根据《著作权法》，传播或下载盗版资源可能涉及法律风险，请遵守法规。\n**3.安全提醒**：非官方渠道可能存在病毒、诈骗链接，请谨慎访问，保护个人信息安全。\n"
    - "\n**注意事项**\n**1.正版推荐**：建议通过爱奇艺、腾讯视频、优酷等官方平台观看，支持优质原创内容。\n**2.法律风险**：未经授权的影视资源传播可能违反《著作权法》，请遵守相关法律法规。\n**3.安全提示**：非正规来源可能存在钓鱼网站或恶意软件，请提高警惕，避免财产损失。\n"
    - "\n**注意事项**\n**1.支持正版**：请通过爱奇艺、优酷、哔哩哔哩等正规渠道观看，保障观影质量。\n**2.法律提醒**：未经许可的影视资源下载或转发可能涉及侵权，请谨慎操作。\n**3.风险提示**：非官方资源可能篡改内容或植入恶意代码，建议优先选择可信平台。\n"
    - "\n**注意事项**\n**1.版权合规**：优先选择哔哩哔哩、腾讯视频、芒果TV等正版平台，拒绝盗版侵权行为。\n**2.法律须知**：下载或分享未授权影视资源可能面临法律追责，请合法观影。\n**3.安全警示**：谨慎对待“免费资源”广告，避免点击不明链接，防止隐私泄露。\n"
    - "\n**注意事项**\n**1.正版观影**：推荐使用优酷、腾讯视频、西瓜视频等授权平台，享受稳定高清体验。\n**2.版权声明**：根据《网络安全法》，非法传播影视内容属于违法行为。\n**3.安全建议**：陌生网站可能存在病毒或诈骗，建议安装安全软件防护。\n"
    - "\n**注意事项**\n**1.正版渠道**：建议通过爱奇艺、腾讯视频、优酷等正规平台观看，享受更优质的观影体验。\n**2.版权保护**：根据相关法律法规，传播盗版资源属于侵权行为，请自觉抵制。\n**3.安全观影**：请勿轻信不明来源的影视链接，谨防个人信息泄露和财产损失。\n"
    - "\n**注意事项**\n**1.版权提示**：影视作品受法律保护，请通过芒果TV、哔哩哔哩等正版平台观看。\n**2.法律须知**：任何未经授权的传播行为都可能面临法律风险。\n**3.安全建议**：使用正版平台可有效避免恶意软件和网络诈骗的风险。\n"
  # 合并到prompt总条数
  total-size: 80
  # 小站合并到prompt条数
  panta-size: 20
  # 网盘链接合并到prompt条数
  ali-size: 20
  # 正版平台合并到prompt条数
  genuine-size: 20
  # 在线网址合并到prompt条数
  online-size: 20
  # 阿里搜索接口参数配置
  ali-search-params:
    # 查询的时间范围
    # 支持可选值：
    # OneDay：1天内
    # OneWeek：1周内
    # OneMonth：1月内
    # OneYear：1年内
    # NoLimit：无限制（默认值）
    timeRange: NoLimit
    # 行业搜索，指定后只返回行业站点的搜索结果，多个行业使用逗号分隔
    # 支持可选值：
    # finance：金融
    # law：法律
    # medical：医疗
    # internet：互联网（精选）
    # tax：税务
    # news_province：新闻省级
    # news_center：新闻中央
    industry: ~
    # 页码（翻页使用），默认值：1
    page: 1
    # 是否返回网页正文，默认为true
    returnMainText: true
    # 是否返回markdown格式的网页内容，默认为true
    returnMarkdownText: true
    # 是否重排序，默认为true
    enableRerank: false

# 邮箱ai搜索界面相关配置
mailai-search-properties:
  # 是否统一转义意图
  transfer-intention: true
  # 是否走全网搜流程
  auto-all-network-search: true
  # 所有搜索意图列表（包括盘内搜索意图、站内搜索意图、知识库搜索意图、综合搜索意图） todo 功能搜、活动搜
  all-search-intentions:
    - "012" # 搜图片
    - "013" # 搜文档
    - "014" # 搜视频
    - "015" # 搜音频
    - "016" # 搜文件夹
    - "017" # 搜笔记
    - "018" # 综合搜索
    - "022" # 搜发现
    - "023" # 搜圈子
    - "028" # 搜邮件、搜邮件附件
    - "038" # 搜索知识库资源
  # 综合搜索需要排除的搜索子意图
  exclude-comprehensive-search-intentions:
    - "018"
    - "038"
    - "999"
    - "012"
  # 邮箱ai搜索界面用到的意图编码：搜索意图：012-搜图片，013-搜文档，014-搜视频，015-搜音频，016-搜文件夹， 017-搜笔记，028-搜邮件&邮件附件。
  search-intention:
    - "012"
    - "013"
    - "014"
    - "015"
    - "016"
    - "017"
    - "018"
    - "028"
  # 为用户的输入拼接搜索意图
  splicing-prefix: ""
  #没有搜到资源的提示语
  search-intention-prefix:
    - "帮我检索"
    - "帮忙检索"
    - "查一下"
    - "找一下"
    - "搜一下"
    - "找一找"
    - "搜一搜"
    - "查一查"
    - "帮我找"
    - "帮我搜"
    - "帮我查"
    - "帮忙找"
    - "帮忙搜"
    - "帮忙查"
    - "查找"
    - "查询"
    - "搜索"
    - "搜寻"
    - "检索"
    - "查阅"
    - "找出"
    - "查查"
    - "找找"
    - "搜搜"
    - "筛查"
    - "找"
    - "查"
    - "搜"
  #没有搜到资源的提示语
  no-result-prompt-copy: "暂时没有找到相关邮件或云盘文件等资产，我根据你刚才的问题进行了联网搜索，整理了一些可能对你有用的内容如下："
  # 普通对话搜不到资源的提示语
  dialogue-search-no-result: "没搜到你想要的结果，换个关键词试试，我继续为你搜寻！"
  # 不同渠道来源设置不同搜索意图
  search-source-channel-intention:
    #  10175-139邮箱-webai
    "10175":
      - "012"
      - "013"
      - "014"
      - "015"
      - "016"
      - "017"
      - "018"
      - "028"
    # 10112-139邮箱-pc
    "10112":
      - "012"
      - "013"
      - "014"
      - "015"
      - "016"
      - "017"
      - "018"
      - "028"