package com.zyhl.yun.api.outer.domain.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zyhl.yun.api.outer.enums.task.FileExpiredStatusEnum;
import com.zyhl.yun.api.outer.enums.task.TaskFeePaidStatusEnum;
import com.zyhl.yun.api.outer.enums.task.TaskFeeTypeEnum;
import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * AI输入算法任务Entity
 *
 * <AUTHOR>
 * @since 2024/03/07 14:48
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Slf4j
public class TaskAiAbilityEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键;任务ID
     */
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 算法编码,多算法时以｜分割
     */
    private String algorithmCode;

    /**
     * 厂商编码,多厂商时以｜分割
     */
    private String supplierTypes;

    /**
     * 任务状态
     *
     * @see TaskStatusEnum
     */
    private Integer taskStatus;

    /**
     * 响应参数
     */
    private String respParam;

    /**
     * 优先级 数字越大优先级越高，建议取值范围为[0,99]
     */
    private Integer priority;

    /**
     * 错误结果码
     */
    private String resultCode;

    /**
     * 错误信息
     */
    private String resultMsg;

    /**
     * 当次任务执行开始时间;YYYY-MM-DD HH24:MM:SS.NNN
     */
    private Date startTime;

    /**
     * 当次任务执行结束时间;YYYY-MM-DD HH24:MM:SS.NNN
     */
    private Date finishTime;

    /**
     * 过期时间 NULL--代表任务无超时限制
     */
    private Date expireTime;

    /**
     * 执行ID
     */
    private String executeId;

    /**
     * 执行次数
     */
    private Integer executeCount;

    /**
     * 来源渠道
     */
    private String sourceChannel;

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 业务参数
     */
    private String businessParam;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 付费类型
     *
     * @see TaskFeeTypeEnum
     */
    private Integer feeType;

    /**
     * 扣费标识
     *
     * @see TaskFeePaidStatusEnum
     */
    private Integer feePaidStatus;

    /**
     * 文件过期状态
     *
     * @see FileExpiredStatusEnum
     */
    private Integer fileExpiredStatus;

    /**
     * 删除标记：0正常，1 已删除
     */
    private Integer delFlag;

    /**
     * 子意图编码
     */
    private String subAlgorithmCode;

    /**
     * 父任务ID
     */
    private Long parentTaskId;

    /**
     * 附加extInfo
     *
     * @param jsonObject json对象
     */
    public String appendJsonExtInfo(JSONObject jsonObject) {
        JSONObject json = null;
        String extinfo = this.getExtInfo();
        try {
            if (null == jsonObject) {
                return extinfo;
            }
            if (StringUtils.isNotBlank(extinfo)) {
                json = JSON.parseObject(extinfo);
            } else {
                json = new JSONObject();
            }
            for (String key : jsonObject.keySet()) {
                json.put(key, jsonObject.get(key));
            }
        } catch (Exception e) {
            log.error("appendJSONExtInfo extInfo:{}, jsonObject:{}, error:", this.getExtInfo(),
                jsonObject.toJSONString(), e);
            return extinfo;
        } finally {
            log.info("appendJSONExtInfo extInfo:{}, jsonObject:{}, json:{}", this.getExtInfo(),
                jsonObject, (null != json ? json.toJSONString() : null));
        }
        return null != json ? json.toJSONString() : null;
    }


    public boolean isFee() {
        //非未付费状态都是true
        return !TaskFeePaidStatusEnum.UNPAID.getCode().equals(this.getFeePaidStatus());
    }

}
