package com.zyhl.yun.api.outer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务类型枚举
 * 业务类型:云盘APP(mcloud-app); 云盘PC(mcloud-pc); 139邮箱h5(139mail-h5)
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum BusinessTypeEnum {

    /**
     * 云盘APP
     */
    M_CLOUD_APP("mcloud-app", "云盘APP"),

    /**
     * 云盘PC
     */
    M_CLOUD_PC("mcloud-pc", "云盘PC"),

    /**
     * 139邮箱h5
     */
    MAIL_H5("139mail-h5", "139邮箱h5"),

    ;

    /**
     * 对应code
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

}
