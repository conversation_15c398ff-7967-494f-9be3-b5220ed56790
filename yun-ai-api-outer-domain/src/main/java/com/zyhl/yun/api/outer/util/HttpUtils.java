package com.zyhl.yun.api.outer.util;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * http工具类
 *
 * @Author: WeiJingKun
 */
@Slf4j
public class HttpUtils {

    /**
     * 获取 request 中的所有的 header 值
     */
    public static Map<String, String> getHeaders(Boolean isGetAll, String url) {
        Map<String, String> map = new LinkedHashMap<>();
        ServletRequestAttributes attributes = (ServletRequestAttributes) org.springframework.web.context.request.RequestContextHolder.getRequestAttributes();
        // 不设置，子线程获取不到
        org.springframework.web.context.request.RequestContextHolder.setRequestAttributes(attributes, true);
        if (isGetAll) {
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                Enumeration<String> headerNames = request.getHeaderNames();
                while (headerNames.hasMoreElements()) {
                    String key = headerNames.nextElement();
                    String value = request.getHeader(key);
                    //   发现多余的header导致请求失败
                    if (key.equalsIgnoreCase("Authorization")) {
                        map.put("Authorization", value);
                    } else if (key.equalsIgnoreCase("x-huawei-channelSrc")) {
                        map.put("x-yun-app-channel", value);
                    } else if (key.equalsIgnoreCase("x-DeviceInfo")) {
                        map.put("x-yun-client-info", value);
                    } else if (key.equalsIgnoreCase("x-yun-client-info")) {
                        map.put("x-yun-client-info", value);
                    } else if (key.equalsIgnoreCase("x-yun-app-channel")) {
                        map.put("x-yun-app-channel", value);
                    } else if (key.equalsIgnoreCase("x-yun-api-version")) {
                        map.put("x-yun-api-version", value);
                    } else if (key.equalsIgnoreCase("x-yun-platform-info")) {
                        map.put("x-yun-platform-info", value);
                    } else {
                        if (key.equalsIgnoreCase("x-yun-app-key") || key.equalsIgnoreCase("x-yun-neauth") || key.equalsIgnoreCase("x-yun-neauth-version") || key.equalsIgnoreCase("x-yun-rand") || key.equalsIgnoreCase("x-yun-tid") || key.equalsIgnoreCase("Content-Length")) {
                        } else {
                            map.put(key, value);
                        }
                    }
                }
            }
            log.info("getHeaders[map]:" + map);
            return map;
        }
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String key = headerNames.nextElement();
                String value = request.getHeader(key);
                //   发现多余的header导致请求失败
                if (key.equalsIgnoreCase("Authorization")) {
                    map.put("Authorization", value);
                }
                if (key.equalsIgnoreCase("x-huawei-channelSrc")) {
                    map.put("x-yun-app-channel", value);
                }
                if (key.equalsIgnoreCase("x-DeviceInfo")) {
                    if (CharSequenceUtil.equals(url, "/thirdlogin")) {
                        map.put(key, value);
                    } else {
                        map.put("x-yun-client-info", value);
                    }
                }
                if (key.equalsIgnoreCase("x-yun-client-info")) {
                    map.put("x-yun-client-info", value);
                }
                if (key.equalsIgnoreCase("x-yun-app-channel")) {
                    map.put("x-yun-app-channel", value);
                }
                if (key.equalsIgnoreCase("x-yun-api-version")) {
                    map.put("x-yun-api-version", value);
                }
                if (key.equalsIgnoreCase("x-yun-platform-info")) {
                    map.put("x-yun-platform-info", value);
                }
            }
        }
        /** 如果前面获取的请求头数据都没有，则使用线程里边存储的数据 */
        if (MapUtil.isEmpty(map) && MapUtil.isNotEmpty(RequestContextHolder.getRequestHeaders())) {
            map.putAll(RequestContextHolder.getRequestHeaders());
        }
        log.info("getHeaders[map]:" + map);
        return map;
    }

}
