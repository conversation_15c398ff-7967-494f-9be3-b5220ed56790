package com.zyhl.yun.api.outer.enums.chat;


/**
 * 传送类型
 * <AUTHOR>
 */
public enum SendTypeEnum {

    /**
     * URL地址
     */
    URL(1, "URL地址"),

    /**
     * base64传送
     */
    BASE64(2, "base64传送"),

    /**
     * 文件id
     */
    FILE_ID(3, "文件id"),

    /**
     * 共享文件存储地址
     */
    LOCAL_PATH(4, "共享文件存储地址"),
    ;

    private final int code;

    SendTypeEnum(int code, String message) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

}
