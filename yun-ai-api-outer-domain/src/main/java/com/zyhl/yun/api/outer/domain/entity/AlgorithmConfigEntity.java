package com.zyhl.yun.api.outer.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 算法配置表-Entity
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AlgorithmConfigEntity {

	/**
	 * 任务类型,1.图片元数据分析,2.人脸聚类,3.相似度聚类
	 */
	private Integer taskType;

	/**
	 * 节点标识;建议采用areaCode,中心节点-0
	 */
	private Integer timelineRangeFlag;

	/**
	 * 执行类型;默认0
	 * 0.默认元数据提取算法顺序执行（多个算法按照execution_order顺序执行）
	 * 1.执行一次（例如图片向量化算法）
	 */
	private Integer executionType;

	/**
	 * 执行顺序
	 */
	private Integer executionOrder;

	/**
	 * 算法URL
	 */
	private String algorithmUrl;

	/**
	 * 算法ID
	 */
	private Long algorithmId;

}