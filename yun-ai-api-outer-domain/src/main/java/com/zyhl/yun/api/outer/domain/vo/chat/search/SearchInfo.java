package com.zyhl.yun.api.outer.domain.vo.chat.search;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

import com.zyhl.yun.api.outer.enums.chat.search.SearchTypeEnum;

import cn.hutool.core.collection.CollUtil;

/**
 * 对话信息-搜索信息
 *
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchInfo implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	/** tab名字 */
	private String tagName;

	/**
	 * 搜索类型
	 *
	 * @see com.zyhl.yun.api.outer.enums.chat.search.SearchTypeEnum
	 */
	private String searchType;

	/** 排序 */
	private Integer sort;

	/**
	 * 搜索条件
	 * searchType="file"：类型为SearchFileParam；
	 * searchType="image"：类型为SearchImageParam；
	 * searchType="note"：类型为searchNoteParam；
	 * searchType="function"：类型为SearchFunctionParam；
	 * searchType="activity"：类型为SearchActivityParam；
	 * searchType="discovery"：11.3.2之前版本类型为：SearchDiscoveryParam，
	 * 11.3.2及以后版本类型为：SearchDiscoveryParamV2；
	 * searchType="myGroup"：类型为SearchGroupParam；
	 * searchType="recommendGroup"：类型为SearchGroupParam；
	 * searchType="mail"：类型为SearchMailParam；
	 * searchType="noteContent"：类型为SearchNoteContentParam；
	 */
	private Object searchParam;

	/**
	 * 搜索结果
	 * searchType="file"：类型为SearchFileResult；
	 * searchType="image"：类型为SearchImageResult；
	 * searchType="note"：类型为SearchNoteResult；
	 * searchType="function"：类型为SearchFunctionResult；
	 * searchType="activity"：类型为SearchActivityResult；
	 * searchType="discovery"：类型为SearchDiscoveryResult；
	 * searchType="myGroup"：类型为SearchGroupResult；
	 * searchType="recommendGroup"：类型为SearchGroupResult；
	 * searchType="mail"：类型为SearchMailResult；
	 * searchType="noteContent"：类型为SearchNoteContentResult；
	 */
	private Object searchResult;

	public static Object getSearchTypeResultByList(List<SearchInfo> searchInfoList, SearchTypeEnum searchTypeEnum) {
		if (CollUtil.isEmpty(searchInfoList) || null == searchTypeEnum) {
			return null;
		}
		for (SearchInfo searchInfo : searchInfoList) {
			if (null != searchTypeEnum.getSearchType()
					&& searchTypeEnum.getSearchType().equals(searchInfo.getSearchType())) {
				return searchInfo.getSearchResult();
			}
		}
		return null;
	}

}
