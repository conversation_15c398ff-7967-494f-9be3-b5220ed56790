package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 升降序枚举
 * @date 2025/4/29 11:31
 */

@Getter
@AllArgsConstructor
public enum FileSortDirectionEnum {

    ASC(1, "升序"),

    DESC(2, "降序"),

    ;

    private final Integer code;

    private final String description;

    /**
     * 获取所有 code 的列表
     * @return 包含所有 code 的列表
     */
    public static List<Integer> getSortDirections() {
        return Arrays.stream(FileSortDirectionEnum.values())
                .map(FileSortDirectionEnum::getCode)
                .collect(Collectors.toList());
    }

    /**
     * 是否存在此枚举code
     * @param code 传入code
     * @return 是否存在
     */
    public static boolean isExist(Integer code) {
        return getSortDirections().contains(code);
    }
}
