package com.zyhl.yun.api.outer.config;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.RandomUtil;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.entity.KeyValueEntity;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelConfigDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 小站搜索配置
 * @Author: WeiJingKun
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "all-network-search")
public class AllNetworkSearchProperties {

    /** 执行开关 */
    private boolean execute;

    /** 模型编码 */
    private String modelCode;

    /** 意图过滤条件 */
    private Map<String, FilterCondition> intentionFilterCondition;

    /** 拼接的后缀 */
    private String suffix;

    /** 按钮文案 */
    private String buttonCopy;

    /** 【全网搜查看更多推荐】推荐问题 */
    private String searchMoreRecommendQuery;

    /** 【全网搜查看更多推荐】按钮文案 */
    private String searchMoreRecommendButtonCopy;

    /** 是否搜小站：true-是，false-否 */
    private boolean searchPanta;

    /** 小站搜索参数 */
    private SearchPantaParam searchPantaParam;

    /** 搜索实体抽取配置 */
    private SearchEntityExtractDTO searchEntityExtract;

    /** 搜索实体重写正则 */
    private String searchEntityRewriteRegex;

    /** 第三方搜索接口配置 */
    private Set<String> thirdPartyKind;

    /** 第三方搜索接口配置 */
    private Set<String> thirdPartyKindGenuine;

    /** 第三方搜索接口配置 */
    private Set<String> thirdPartyKindOnLine;

    /** 多线程混合搜索等待时间，单位：秒 */
    private int searchWaitTime;

    /** 文本大模型配置 */
    private TextModelConfigDTO textModelConfig;

    /** 【正版平台】链接前缀（白名单） */
    private List<LinkPrefix> genericPlatformLinkPrefix;

    /** 【网盘链接】链接前缀（白名单） */
    private List<String> cloudStorageLinkPrefix;

    /** 【在线网址】链接前缀（黑名单） */
    private List<String> onlineUrlLinkPrefix;

    /** 需要检查的关键字 */
    private List<String> checkKeywords;

    /** 搜索为空的返回语句 */
    private List<String> emptyResultText;

    /** 搜索实体/搜索结果命中黑名单返回语句 */
    private String blackText;

    /** 版权提示 */
    private String copyrightNotice;

    /** 【视频搜索】注意事项列表 */
    private List<String> videoSearchPrecautionList;

    /** 合并到prompt总条数 */
    private int totalSize;

    /** 小站合并到prompt条数 */
    private int pantaSize;

    private int aliSize;

    private int genuineSize;

    private int onlineSize;

    /** 搜索实体热更新数据集合 */
    private List<EntityHotUpdate> entityHotUpdateList;

    /**
     * 阿里云搜索参数配置
     */
    private AliSearchParams aliSearchParams;

    /**
     * 搜索参数配置类
     */
    @Data
    public static class AliSearchParams {

        /**
         * 查询的时间范围
         */
        private String timeRange;

        /**
         * 行业搜索
         * 指定后只返回行业站点的搜索结果，多个行业使用逗号分隔
         */
        private String industry = null;

        /**
         * 页码（翻页使用），默认值：1
         */
        private Integer page;

        /**
         * 是否返回网页正文，默认为true
         */
        private Boolean returnMainText;

        /**
         * 是否返回markdown格式的网页内容，默认为true
         */
        private Boolean returnMarkdownText;

        /**
         * 是否重排序，默认为true
         */
        private Boolean enableRerank;
    }

    /**
     * 过滤条件
     * @Author: WeiJingKun
     */
    @Data
    public static class FilterCondition {

        /** 查询类别列表 */
        private List<String> queryTypeList;

        /**
         * 配置中是否存在至少一个查询类别
         * @Author: WeiJingKun
         *
         * @param queryTypeStrList 查询类别集合
         * @return true-存在；false-不存在
         */
        public boolean existQueryType(List<String> queryTypeStrList) {
            if(CollUtil.isEmpty(queryTypeList)){
                return true;
            } else {
                // 其中一个集合在另一个集合中是否至少包含一个元素，即是两个集合是否至少有一个共同的元素
                return CollUtil.containsAny(queryTypeList, queryTypeStrList);
            }
        }

    }

    /**
     * 小站搜索参数
     * @Author: WeiJingKun
     */
    @Data
    public static class SearchPantaParam {

        /** 发布年份 */
        private Integer releaseYear;

        /** 是否搜成人：1-成人；0-未成年人 */
        private Integer adult;

        /** 是否搜国内：1-国内；0-国外 */
        private Integer domestic;

        /** 搜索优先级（默认0） */
        private List<Integer> priorityList;

        /** 搜索文件类型范围 */
        private List<Integer> businessResourceTypeList;

    }

    /**
     * 搜索实体抽取配置
     */
    @Data
    public static class SearchEntityExtractDTO {

        /** 是否返回全部实体列表，true-是，false-否 */
        private boolean returnAll;

        /** 单条文本内容的最大长度，默认最大1024字符，上限4096字符 */
        private int maxLength;

        /** 搜索实体抽取标签类型 */
        private List<String> entityTypeList;

        /** 实体列表，用于热更新特定词语到全部实体列表 */
        private List<String> allEntityExample;

        /** 实体映射关系，用于热更新特定词语到实体类型的映射 */
        private List<KeyValueEntity> typeEntityExample;
    }

    /**
     * 判断是否执行【小站资源搜索】
     * @param intention 意图编码
     * @param queryTypeStrList 查询类别集合
     */
    public boolean judgeSearchPanta(String intention, List<String> queryTypeStrList) {
        boolean flag = false;
        // 搜小站：true-是 && 意图过滤条件有值
        if(this.searchPanta && MapUtil.isNotEmpty(intentionFilterCondition)){
            FilterCondition filterCondition = intentionFilterCondition.get(intention);
            // 过滤条件有值
            if(ObjectUtil.isNotNull(filterCondition)){
                // 配置中是否存在至少一个查询类别
                flag = filterCondition.existQueryType(queryTypeStrList);
            }
        }
        return flag;
    }

    /**
     * 搜索实体热更新数据实体
     */
    @Data
    public static class EntityHotUpdate {

        /** 标签类型 */
        private String label;

        /** 内容 */
        private String value;
    }

    /**
     * 链接前缀实体数据
     */
    @Data
    public static class LinkPrefix {
        /** 名称 */
        private String name;

        /** 链接列表 */
        private List<String> linkList;
    }

    /**
     * 判断链接是否【符合】业务要求范围
     *
     * @param link 匹配链接
     * @param whiteLinkPrefix 白名单链接集合
     * @return 是否符合
     */
    public static boolean whiteLinkPrefixFilter(String link, List<String> whiteLinkPrefix) {
        if(CharSequenceUtil.isBlank(link)){
            return false;
        }
        if(CollUtil.isEmpty(whiteLinkPrefix)){
            return true;
        }
        // link在【白名单】链接集合中
        return whiteLinkPrefix.stream().anyMatch(link::contains);
    }

    /**
     * 判断链接是否【不符合】业务要求范围
     *
     * @param link 匹配链接
     * @param blackLinkPrefix 黑名单链接集合
     * @return 是否符合
     */
    public static boolean blackLinkPrefixFilter(String link, List<String> blackLinkPrefix) {
        if(CharSequenceUtil.isBlank(link)){
            return false;
        }
        if(CollUtil.isEmpty(blackLinkPrefix)){
            return true;
        }
        // link不在【黑名单】链接集合中
        return blackLinkPrefix.stream().noneMatch(link::contains);
    }

    public static void main(String[] args) {
        // 黑名单链接
        List<String> blackLinkPrefix = ListUtil.toList(
                "bad.example.com",
                "https://evil.site.com"
        );

        // 测试链接
        String[] testLinks = {
                "http://bad.example.com/123",
                "https://evil.site.com/123",
                "https://good.site.com/123",
                "http://normal.com/123"
        };

        // 调用过滤方法进行测试
        for (String link : testLinks) {
            boolean result = blackLinkPrefixFilter(link, blackLinkPrefix);
            System.out.println("链接: " + link + "，过滤结果: " + result);
        }
    }

    /**
     * 随机获取视频搜索注意事项
     * @return
     */
    public String randomGetVideoSearchPrecaution() {
        if(CollUtil.isEmpty(videoSearchPrecautionList)){
            return CharSequenceUtil.EMPTY;
        }
        return videoSearchPrecautionList.get(RandomUtil.randomInt(videoSearchPrecautionList.size()));
    }

}
