package com.zyhl.yun.api.outer.domain.entity.knowledge;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * 知识库业务映射表
 * <AUTHOR>
 */
@Data
@Builder
public class KnowledgeBusinessEntity {

    /**
     * 主键
     */
    private String id;

    /**
     * 知识库的标识
     * common 公共知识库
     * customer 客服
     */
    private String baseId;

    /**
     * xiaotian 云盘-小天
     * yunmail  邮箱
     */
    private String businessCode;

    /**
     * 1 打开（默认）
     * 0 关闭
     */
    private int open;

    /**
     * 创建人Id
     */
    private String createdBy;

    /**
     * 更新人Id
     */
    private String updatedBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否打开
     *
     * @return true-打开知识库
     */
    public boolean isOpen() {
        return open == 1;
    }
}
