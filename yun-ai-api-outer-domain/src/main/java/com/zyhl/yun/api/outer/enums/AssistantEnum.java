package com.zyhl.yun.api.outer.enums;

import com.zyhl.yun.api.outer.config.SourceChannelsProperties;

import org.python.jline.internal.Log;

import cn.hutool.extra.spring.SpringUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 助手枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AssistantEnum {

    /**
     * 云邮助手
     */
    YUN_MAIL("yunmail", "云邮助手", "云邮助手"),

    /**
     * 小天助手
     */
    XIAO_TIAN("xiaotian", "小天助手", "小天助手"),

    /**
     * 云手机
     */
    CLOUD_PHONE("cloud_phone", "云手机", "云手机"),

    /**
     * 5G消息
     */
    MESSAGE_5G("message_5g", "5G消息", "5G消息"),

    /**
     * 笔记助手
     */
    NOTE("note", "笔记助手", "笔记助手"),

    ;

    /**
     * 助手编码
     */
    private final String code;
    /**
     * 助手名称
     */
    private final String name;
    /**
     * 助手备注
     */
    private final String remark;

    public static AssistantEnum getByCode(String code) {
        for (AssistantEnum assistantEnum : AssistantEnum.values()) {
            if (assistantEnum.getCode().equals(code)) {
                return assistantEnum;
            }
        }
        return null;
    }

    public static boolean isExist(String code) {
        return getByCode(code) != null;
    }

    public static boolean isYunMail(String code) {
        return YUN_MAIL.equals(getByCode(code));
    }

    public static boolean isYunMail(AssistantEnum assistantEnum) {
        if (null == assistantEnum) {
            return false;
        }
        return isYunMail(assistantEnum.getCode());
    }

    public static boolean isXiaoTian(String code) {
        return XIAO_TIAN.equals(getByCode(code));
    }

    public static boolean isXiaoTian(AssistantEnum assistantEnum) {
        if (null == assistantEnum) {
            return false;
        }
        return isXiaoTian(assistantEnum.getCode());
    }

    public static boolean isCloudPhone(String code) {
        return CLOUD_PHONE.equals(getByCode(code));
    }

    public static boolean isCloudPhone(AssistantEnum assistantEnum) {
        if (null == assistantEnum) {
            return false;
        }
        return isCloudPhone(assistantEnum.getCode());
    }

    public static boolean isMessage5g(String code) {
        return MESSAGE_5G.equals(getByCode(code));
    }

    public static boolean isNote(String code) {
        return NOTE.equals(getByCode(code));
    }

    public static boolean isNote(AssistantEnum assistantEnum) {
        if (null == assistantEnum) {
            return false;
        }
        return isNote(assistantEnum.getCode());
    }


    public static boolean isMessage5g(AssistantEnum assistantEnum) {
        if (null == assistantEnum) {
            return false;
        }
        return isMessage5g(assistantEnum.getCode());
    }

    /**
     * 根据渠道获取助手类型
     *
     * @param sourceChannel
     * @return
     */
    public static AssistantEnum getByChannel(String sourceChannel) {
        try {
            SourceChannelsProperties sourceChannelsProperties = SpringUtil.getBean(SourceChannelsProperties.class);
            if (null != sourceChannelsProperties) {
                return sourceChannelsProperties.getAssistantEnumDefaultMail(sourceChannel);
            }
        } catch (Exception e) {
            Log.error("getByChannel sourceChannel:{} error:", sourceChannel, e);
        }
        return null;
    }

}
