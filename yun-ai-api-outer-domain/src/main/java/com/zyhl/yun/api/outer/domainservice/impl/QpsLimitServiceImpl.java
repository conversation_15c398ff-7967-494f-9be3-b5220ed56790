package com.zyhl.yun.api.outer.domainservice.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.qpslimit.QpsLimiterTokenBucketClient;
import com.zyhl.hcy.yun.ai.common.base.qpslimit.dto.TokenBucketDTO;
import com.zyhl.yun.api.outer.config.QpsProperties;
import com.zyhl.yun.api.outer.config.TokenBucketProperties;
import com.zyhl.yun.api.outer.constants.QpsConstants;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.constants.TokenBucketKeyConst;
import com.zyhl.yun.api.outer.domainservice.QpsLimitService;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName QpsLimitServiceImpl
 * @Description: QPS限流
 * <AUTHOR>
 * @Date 2024/3/25
 **/
@Slf4j
@Service
public class QpsLimitServiceImpl implements QpsLimitService {

    @Resource
    private TokenBucketProperties tokenBucketProperties;
    @Resource
    private QpsProperties qpsProperties;
    @Resource
    private QpsLimiterTokenBucketClient qpsClient;

    @Override
    public boolean modelQpsLimit(String modelCode) {
        log.info("【qps限流】获取令牌桶，模型编码：{}", modelCode);

        // 获取qps配置
        TokenBucketDTO tokenBucketDTO = tokenBucketProperties.toTokenBucketDTO(modelCode);

        // 没有配置，默认不限制
        log.info("【qps限流】qps限制配置：{}", JsonUtil.toJson(tokenBucketDTO));
        if (tokenBucketDTO == null) {
            return true;
        }

        // 获取令牌
        try {
            // 获取令牌，true-获取成功
            String prefix = RedisConstants.QPS_PREFIX + modelCode;
            return qpsClient.getTokenBucketAllow(prefix, TokenBucketKeyConst.DIALOGUE_ENTRANCE, tokenBucketDTO);
        } catch (Exception e) {
            log.error("【qps限流】获取qps令牌异常：{}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void qpsLimit(String url) {
        if (CharSequenceUtil.isBlank(url)) {
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        try {
            if (url.contains(QpsConstants.URL_KEY)) {
                TokenBucketDTO tokenBucketDTO = new TokenBucketDTO();
                tokenBucketDTO.setLimit(qpsProperties.getLimit());
                tokenBucketDTO.setMaxBurstSize(qpsProperties.getMaxBurstSize() != null ? qpsProperties.getMaxBurstSize() : qpsProperties.getLimit());
                tokenBucketDTO.setTimeoutMillis(qpsProperties.getTimeoutMillis());
                tokenBucketDTO.setExpireTime(qpsProperties.getExpireTime());
                boolean res = qpsClient.getTokenBucketAllow(RedisConstants.OUTER_PREFIX, QpsConstants.BUCKET_CHAT_ADD,
                        QpsConstants.QPS_TIMEOUT, tokenBucketDTO);
                if (!res) {
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_LIMITATION);
                }
            }
        } catch (Exception e) {
            log.error("outer qps limit error:{}", e.getMessage(), e);
            if (e instanceof YunAiBusinessException) {
                throw (YunAiBusinessException) e;
            }
            throw new YunAiBusinessException(ResultCodeEnum.UNKNOWN_ERROR);
        }
    }
}
