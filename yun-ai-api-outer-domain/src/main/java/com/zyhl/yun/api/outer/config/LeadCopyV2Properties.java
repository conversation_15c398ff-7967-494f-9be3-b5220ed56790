package com.zyhl.yun.api.outer.config;

import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;

/**
 * 对话结果引导文案配置
 *
 * <AUTHOR>
 * @date 2024/6/3 01:29
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "lead-copy-v2")
public class LeadCopyV2Properties {

    /**
     * 创建笔记意图标题
     */
    private String createNoteTitle;

    /**
     * 创建语音笔记意图标题
     */
    private String createVoiceNoteTitle;

    /**
     * 创建笔记意图标题(英文)
     */
    private String createNoteTitleEn;

    /**
     * 创建语音笔记意图标题(英文)
     */
    private String createVoiceNoteTitleEn;

    /**
     * 发邮件意图标题
     */
    private String sendMailTitle;

    /**
     * 引导文案配置列表
     */
    private List<Copy> copyList;

    /**
     * 匹配关键词配置列表（妙云相机、AI写真）
     */
    private List<Keyword> keywordList;

    /**
     * 文本工具文案配置列表
     */
    private List<Copy> textToolList;

    /**
     * 通过意图指令获取引导文案配置
     *
     * @param instruction 意图指令
     * @return 引导文案配置
     */
    public Copy getByInstruction(String instruction) {
        if (CollUtil.isEmpty(copyList)) {
            return null;
        }

        for (Copy item : copyList) {
            if (item.instruction.equals(instruction)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 根据意图实体value，匹配文本工具文案Copy对象
     *
     * @param intentionInfo
     * @return
     */
    public Copy getTextToolByIntentionToolCallsList(IntentionInfo intentionInfo) {
        if (null == intentionInfo) {
            return null;
        }
        if (CollUtil.isEmpty(intentionInfo.getToolCallsList())) {
            return null;
        }
        if (StringUtils.isEmpty(intentionInfo.getSubIntention())) {
            return null;
        }
        String subCode = intentionInfo.getSubIntention();
        if (StringUtils.isNotEmpty(subCode) && CollUtil.isNotEmpty(textToolList)) {
            for (Copy textTool : textToolList) {
                if (subCode.equals(textTool.getCode())) {
                    return textTool;
                }
            }
        }
        return null;
    }

    /**
     * 引导文案配置
     */
    @Data
    public static class Copy {

        /**
         * 描述key
         */
        private String key;

        /**
         * 辅助字段-匹配code
         */
        private String code;

        /**
         * 跳转名称
         */
        private String linkName;

        /**
         * 意图指令
         */
        private String instruction;

        /**
         * 场景类型
         */
        private Integer type;

        /**
         * 提示文案
         */
        private String promptCopy;

        /**
         * 按钮文案
         */
        private String buttonCopy;

        /**
         * 提示文案（英文）
         */
        private String promptCopyEn;

        /**
         * 按钮文案（英文）
         */
        private String buttonCopyEn;

        /**
         * 跳转URL
         */
        private String linkURL;

        /**
         * 图片URL
         */
        private String imageURL;

        /**
         * 图标URL
         */
        private String iconURL;

        /**
         * 提示词编码列表
         */
        private List<LeadCopyPrompt> promptCodeList;

        /**
         * 输入框提示文案
         */
        private String inputBoxCopy;

    }

    /**
     * 匹配关键词配置
     */
    @Data
    public static class Keyword {

        /**
         * 意图指令
         */
        private String instruction;

        /**
         * 提示文案
         */
        private List<String> keywords;

    }

    /**
     * 通过对话输入内容获取引导文案配置
     *
     * @param text 对话输入内容
     * @return 引导文案配置
     */
    public String getByKeyword(String text) {
        if (CollUtil.isEmpty(keywordList) || StringUtils.isBlank(text)) {
            return null;
        }

        for (Keyword keyword : keywordList) {
            if (keyword.getKeywords().stream().anyMatch(text::contains)) {
                return keyword.getInstruction();
            }
        }
        return null;
    }

    /**
     * 提示词
     */
    @Data
    public static class LeadCopyPrompt {
        /**
         * 名称
         */
        private String promptName;

        /**
         * 提示词编码
         */
        private String promptCode;

        /**
         * 备注
         */
        private String promptRemark;

        /**
         * 背景图片URL
         */
        private String promptBackgroundUrl;

        /**
         * 描述列表
         */
        private List<String> promptDescList;

    }
}
