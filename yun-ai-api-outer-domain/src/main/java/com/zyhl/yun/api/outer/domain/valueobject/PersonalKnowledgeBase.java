package com.zyhl.yun.api.outer.domain.valueobject;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeBaseTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeSelectedEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 描述：个人知识库
 *
 * <AUTHOR> zhumaoxian  2025/4/23 14:35
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PersonalKnowledgeBase {
    /**
     * 知识库Id
     */
    private String baseId;
    /**
     * 知识库类型
     * @see KnowledgeBaseTypeEnum
     */
    private Integer baseType;
    /**
     * 知识库名称
     */
    private String name;
    /**
     * 知识库描述
     */
    private String description;
    /**
     * 是否用户选择
     */
    private Boolean selected;
    /**
     * 公开/私密（1 公开，0私密，默认为0）
     */
    private Integer openLevel;

    public PersonalKnowledgeBase(UserKnowledgeEntity entity) {
        this.baseId = String.valueOf(entity.getId());
        // baseType需要查数据库
//        this.baseType = entity.getBaseType();
        this.name = entity.getName();
        this.description = entity.getDescription();
        this.selected = KnowledgeSelectedEnum.SELECTED.getStatus().equals(entity.getSelected());
        this.openLevel = entity.getOpenLevel();
    }

}
