package com.zyhl.yun.api.outer.domain.vo;

import lombok.Data;
import org.aspectj.bridge.Message;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025-04-16 16:53:29
 */
@Data
public class ImportHtmlInfoVO {

    private Integer index;

        /**
     * 网页主题
     */

    private String title;
        /**
     * 网页url
     */
     @NotBlank(message = "网页url不能为空")
    private String url;
    /**
     * 来源网站
     */
    private String siteName;
    /**
     * 来源网站图标
     */
    private String icon;

    /**
     * HTML格式的网页内容字符串
     */
    private String htmlContent;
}
