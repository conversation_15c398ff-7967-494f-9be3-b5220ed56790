package com.zyhl.yun.api.outer.repository;

/**
 * AI迁移表：小天助手1.0.1
 *
 * <AUTHOR>
 */
public interface AlgorithmAiMigrationRepository {

    /**
     * 新增
     *
     * @param userId          用户id
     * @param phone           手机号码
     * @param belongsPlatform 所属平台
     * @param device          设备
     * @param sourceChannel   渠道
     * @param type            类型
     */
    void add(String userId, String phone, Integer belongsPlatform, String device, String sourceChannel, Integer type);


    /**
     * 获取状态
     *
     * @param userId 用户id
     * @return 迁移状态
     */
    Integer getStatus(String userId);

    /**
     * 获取状态
     *
     * @param userId 用户id
     * @param type   类型
     * @return 状态
     */
    Integer getStatus(String userId, Integer type);

}
