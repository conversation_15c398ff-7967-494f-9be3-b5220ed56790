package com.zyhl.yun.api.outer.external;

import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.dto.MailLoginReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.dto.SendMailCustomizationReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.vo.MailLoginVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.vo.MailResultVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.vo.RagMailDetailVO;

/**
 * 邮件服务
 *
 * <AUTHOR>
 */
public interface MailExternalService {

    /**
     * 获取邮件内容
     *
     * @param sid    sid
     * @param rmkey  rmkey
     * @param mailId 邮件id
     * @return 邮件内容
     */
    String mailContent(String sid, String rmkey, String mailId);

    /**
     * 获取邮件信息
     *
     * @param phone  手机号码
     * @param mailId 邮件id
     * @return 邮件信息
     */
    RagMailDetailVO getMailInfo(String phone, String mailId);

	/**
	 * 定制的发邮件接口
	 * 
	 * @param reqDTO 请求参数
	 * @return 邮件结果
	 */
	MailResultVO sendMailCustomization(SendMailCustomizationReqDTO reqDTO);
	
	/**
	 * 邮箱登录接口
	 * @param reqDTO 请求参数
	 * @return 登录结果
	 */
	MailLoginVO mailLogin(MailLoginReqDTO reqDTO);
}
