package com.zyhl.yun.api.outer.domain.dto;


import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件审核结果对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class AuditResultDTO {

    /**
     * 文件内容ID。
     * 该字段存储主平台的文件ID，对于文本内容没有该字段。
     */
    private String contentId;

    /**
     * 消息ID。
     * 该字段标识该次消息的唯一ID，并透传返回。
     */
    private String msgId;

    /**
     * 审核状态。
     * 该字段存储审核状态，具体值包括：
     * -1：发起送审失败（非审核结果）
     * 0：人审中
     * 1：审核异常
     * 2：审核通过
     * 3：审核违规（封禁）
     * 4：限制分享
     * 5：恢复文件
     */
    private String status;

    /**
     * 审核结果描述。
     * 该字段存储审核结果的详细描述。
     */
    private String detail;

    /**
     * 审核结果码。
     * 该字段存储审核结果码，详情请参考映射表。
     */
    private String auditCode;

    /**
     * 审核类型。
     * 该字段存储审核类型，具体值包括：
     * 1 = 机审
     * 2 = 人审
     * 3 = 二次人审
     */
    private Integer auditType;

    /**
     * 请求上下文。
     * 该字段存储请求上下文，平台不保存，仅用于消息回传。
     */
    private String context;

    /**
     * 任务ID。
     * 该字段存储审核任务的唯一标识符。
     */
    private String taskId;


    // ------------------------- //

    /**
     * 涉敏
     *
     * @return true-涉敏
     */
    public boolean isSensitive() {
        return "3".equals(status);
    }
}
