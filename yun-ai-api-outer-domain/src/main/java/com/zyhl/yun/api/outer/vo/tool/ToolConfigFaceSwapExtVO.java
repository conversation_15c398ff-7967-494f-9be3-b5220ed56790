package com.zyhl.yun.api.outer.vo.tool;

import com.zyhl.yun.api.outer.config.tool.FacePointInfo;
import lombok.Data;

import java.util.List;

/**
 * 描述
 *
 * <AUTHOR> zhumaoxian  2025/1/4 14:40
 */
@Data
public class ToolConfigFaceSwapExtVO {

    /**
     * 风格样式
     */
    private Integer style;

    /**
     * 风格下的姿势
     */
    private Integer poseId;

    /**
     * 模板图片高度
     */
    private Integer height;

    /**
     * 模板图片宽度
     */
    private Integer width;

    /**
     * 风格样式的人脸锚点信息
     */
    private List<FacePointInfo> faceList;

}
