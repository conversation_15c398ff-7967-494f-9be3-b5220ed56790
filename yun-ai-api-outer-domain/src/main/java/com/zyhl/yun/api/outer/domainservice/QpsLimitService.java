package com.zyhl.yun.api.outer.domainservice;

/**
 * qps限流接口
 *
 * <AUTHOR>
 */
public interface QpsLimitService {

    /**
     * QPS限流，2024-06-26修改，跟aimanage的规则不一致，后面有机会再调整aimanage的规则
     *
     * @param modelCode 模型编码
     * @return true-允许访问，false-不允许访问
     */
    boolean modelQpsLimit(String modelCode);

    /***
     * outer服务限流,鉴权通过之后通过url匹配
     * @param url 请求url
     * @throws Exception 抛出异常
     */
    void qpsLimit(String url) throws Exception;
}
