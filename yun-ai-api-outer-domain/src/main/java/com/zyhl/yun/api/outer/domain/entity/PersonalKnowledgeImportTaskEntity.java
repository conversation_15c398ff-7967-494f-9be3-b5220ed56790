package com.zyhl.yun.api.outer.domain.entity;

import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.vo.ImportHtmlInfoVO;
import com.zyhl.yun.api.outer.domain.vo.ImportMailInfoVO;
import com.zyhl.yun.api.outer.domain.vo.ImportNoteInfoVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * <AUTHOR>
 * @date 2025-04-16 16:09:46
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PersonalKnowledgeImportTaskEntity {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 知识库ID
     */
    private String baseId;

    /**
     * 资源类型描述
     * 0--个人云文件
     * 1--邮件
     * 2--笔记
     * 3--在线链接
     */
    private Integer resourceType;

    /**
     * 任务状态描述
     * -1--处理失败
     * 0--处理中
     * 1--处理成功
     */
    private Integer status;

    /**
     * 个人云文件
     */
    private File file;

    /**
     * 邮件信息
     */
    private ImportMailInfoVO mail;

    /**
     * 笔记信息
     */
    private ImportNoteInfoVO note;

    /**
     * 网页链接信息
     */
    private ImportHtmlInfoVO htmlInfo;

    /**
     * 创建时间，RFC 3339 格式
     * 例如：2019-08-20T06:51:27.292+08:00
     */
    private String createdAt;

    /**
     * 更新时间，RFC 3339 格式
     * 例如：2019-08-20T06:51:27.292+08:00
     */
    private String updatedAt;

    /**
     * 错误描述，失败时返回
     */
    private String errorMessage;
    /**
     * 错误码，失败时返回
     */
    private String errorCode;
    /**
     * 文件ID
     */
    private String fileId;

    /**
     * 资源信息
     */
    private String resource;

    /**
     * 业务类型
     */
    private Integer ownerType;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 文件归属
     */
    private String ownerId;

    /**
     * 上传结果状态
     */
    private Integer uploadStatus;

    /**
     *  删除标识
     */
    private Integer delFlag;

    /**
     * 目标父目录ID
     */
    private String targetParentFileId;

    /**
     * 目标父目录路径
     */
    private String targetParentFilePath;

}

