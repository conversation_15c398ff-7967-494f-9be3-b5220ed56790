package com.zyhl.yun.api.outer.domain.vo.chat.search.param;

import cn.hutool.core.collection.ListUtil;
import lombok.*;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;

/**
 * 对话信息-搜索参数-知识库资源
 * @Author: WeiJingKun
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchKnowledgeBaseResourceParam extends SearchCommonParam implements Serializable {

    private static final long serialVersionUID = -4299197664832373449L;

    /**
     * 资源类型
     * 0--文件
     * 1--邮件
     * 2--笔记
     * 3--在线链接
     */
    private Integer resourceType;

    /**
     * 关键字集合-最大支持10条，文档支持文件名+正文搜索，其他文件只搜索文件名
     */
    private List<String> nameList;

    /**
     * 文档类型：
     * doc
     * xls
     * ppt
     * pdf
     * txt
     */
    private String docType;

    /**
     * 后缀名-最大支持10条，
     * 与docType互斥；
     * 当resourceType不为1时，该参数无效
     */
    private List<String> suffixList;

    /**
     * 搜索时间范围列表，每个时间范围均为或的关系，如果填了，TimeRange中的两个字段就是必填
     * 日期格式：yyyyMMddHHmmss
     */
    private List<SecondTimeRange> timeList;

    /** 搜索平台分页信息（默认查总数） */
    @Builder.Default
    private @Valid KnowledgeBaseResourcePageInfo pageInfo = new KnowledgeBaseResourcePageInfo(10, null, null, 1);

}
