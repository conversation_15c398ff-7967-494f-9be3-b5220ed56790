package com.zyhl.yun.api.outer.domain.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zyhl.yun.api.outer.config.LeadCopyV2Properties;
import com.zyhl.yun.api.outer.enums.AcceptLanguageEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import org.apache.commons.lang3.StringUtils;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 会话输入同步结果-引导文案对象VO
 *
 * <AUTHOR>
 * @date 2024/6/2 22:41
 */
@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LeadCopyV2VO extends LeadCopyVO {

    /**
     * 提示词编码列表（保持与V1版本字段名一致）
     */
    @JsonProperty("promptCodeList")
    private List<LeadCopyV2Properties.LeadCopyPrompt> promptCodeV2List;

    public static LeadCopyVO getLeadCopyVo(LeadCopyV2Properties.Copy copyConfig, DialogueIntentionEnum intentionEnum) {
        if (copyConfig == null) {
            return null;
        }
        //跳转名称【文本工具意图，采用配置文件配置文本工具名称，不存在则取意图名称】
        String linkName = copyConfig.getLinkName();
        if (StringUtils.isEmpty(linkName)) {
            linkName = intentionEnum.getName();
        }
        LeadCopyVO vo = LeadCopyV2VO.builder()
                .buttonCopy(copyConfig.getButtonCopy())
                .promptCopy(copyConfig.getPromptCopy())
                .type(copyConfig.getType())
                .linkName(linkName)
                .linkURL(copyConfig.getLinkURL())
                .imageURL(copyConfig.getImageURL())
                .iconURL(copyConfig.getIconURL())
                .promptCodeV2List(copyConfig.getPromptCodeList())
                .inputBoxCopy(copyConfig.getInputBoxCopy())
                .build();
        // 根据请求头判断是否需要转成英文
        if (AcceptLanguageEnum.EN_US.getCode().equalsIgnoreCase(RequestContextHolder.getAcceptLanguage())) {
            if (StringUtils.isNotBlank(copyConfig.getButtonCopyEn())) {
                vo.setButtonCopy(copyConfig.getButtonCopyEn());
            }
            if (StringUtils.isNotBlank(copyConfig.getPromptCopyEn())) {
                vo.setPromptCopy(copyConfig.getPromptCopyEn());
            }
        }
        return vo;
    }

}
