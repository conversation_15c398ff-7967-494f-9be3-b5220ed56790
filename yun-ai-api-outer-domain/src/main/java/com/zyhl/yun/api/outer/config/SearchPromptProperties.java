package com.zyhl.yun.api.outer.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * 搜索提示语句配置
 * <AUTHOR>
 */
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "search-prompt")
public class SearchPromptProperties {

    /** 默认引导语 */
    private Map<String, List<String>> guide;
    /** 默认提示语 */
    private Map<String, List<String>> prompt;
    /** 云邮助手引导语 */
    private Map<String, List<String>> mailGuide;
    /** 云邮助手提示语 */
    private Map<String, List<String>> mailPrompt;

    /**
     * 获取默认引导语，并抛出异常
     * @Author: WeiJingKun
     */
    public void getGuideRandomStringAndThrowException(String instruction) {
        throw new YunAiBusinessException(AiResultCode.CODE_10022024.getCode(), getGuideRandomString(instruction));
    }

    /**
     * 获取默认引导语
     * @Author: WeiJingKun
     */
    public String getGuideRandomString(String instruction) {
        return getMsg(instruction, guide);
    }

    /**
     * 获取默认提示语
     * @Author: WeiJingKun
     */
    public String getPromptRandomString(String instruction) {
        return getMsg(instruction, prompt);
    }

    /**
     * 获取云邮助手引导语
     * @Author: WeiJingKun
     */
    public String getMailGuideRandomString(String instruction) {
        String msg = getMsg(instruction, mailGuide);
        if(CharSequenceUtil.isBlank(msg)){
            // 没有获取到，则返回默认引导语
            msg = getMsg(instruction, guide);
        }
        return msg;
    }

    /**
     * 获取云邮助手提示语
     * @Author: WeiJingKun
     */
    public String getMailPromptRandomString(String instruction) {
        String msg = getMsg(instruction, mailPrompt);
        if(CharSequenceUtil.isBlank(msg)){
            // 没有获取到，则返回默认提示语
            msg = getMsg(instruction, prompt);
        }
        return msg;
    }

    private String getMsg(String instruction, Map<String, List<String>> msgListMap) {
        List<String> msgList = msgListMap.get(instruction);
        if(CollUtil.isEmpty(msgList)){
            return AiResultCode.CODE_10022024.getMsg();
        }
        Random random = new Random();
        int index = random.nextInt(msgList.size());
        return msgList.get(index);
    }
}
