package com.zyhl.yun.api.outer.domain.dto;

import com.zyhl.hcy.yun.ai.common.rag.dto.validate.RecallQueryDTO;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.dialogue.KnowledgeBaseConfig;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.dialogue.RecallConfig;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述：召回参数
 *
 * <AUTHOR> zhumaoxian  2025/2/18 14:27
 */
@Data
public class RecallDTO {

    /**
     * 公共知识库配置
     */
    private KnowledgeBaseConfig commonBase;
    /**
     * VIP专属智能体知识库配置
     */
    private KnowledgeBaseConfig vipCommonBase;
    /**
     * 公共知识库配置
     */
    private KnowledgeBaseConfig personalBase;

    /**
     * 检索文本
     */
    private String text;

    /**
     * 文本向量信息
     */
    private List<BigDecimal> feature;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 意图结果对象
     */
    private DialogueIntentionVO intentionVO;

    /**
     * 个人知识库文件id列表
     */
    private List<String> fileIdList;

    /**
     * 知识库分组信息
     */
    private List<RecallQueryDTO.KnowledgeGroup> knowledgeGroupList;

    /**
     * 多路召回版本
     *
     * @see com.zyhl.hcy.yun.ai.common.rag.enums.RecallVersionEnum
     */
    private String version;

    /**
     * 召回配置
     */
    private RecallConfig config;

    /**
     * 问题关键字列表
     */
    private List<String> textKeywordList;

    /**
     * 查询类型
     * 0-其他类（默认）
     * 1-总结类
     * 2-建议类
     * 3-发言稿
     *
     * @see com.zyhl.hcy.yun.ai.common.rag.enums.RewriteQueryTypeEnum
     */
    private Integer queryType = 0;

    public RecallDTO() {

    }

    public RecallDTO(boolean commonEnabled, boolean personalEnabled) {
        commonBase = new KnowledgeBaseConfig();
        commonBase.setEnabled(commonEnabled);

        personalBase = new KnowledgeBaseConfig();
        personalBase.setEnabled(personalEnabled);

        // 旧版本VIP专属智能体知识库默认为false
        vipCommonBase = new KnowledgeBaseConfig();
        vipCommonBase.setEnabled(false);
    }

    public RecallDTO(boolean commonEnabled, boolean personalEnabled, boolean vipCommonEnabled) {
        commonBase = new KnowledgeBaseConfig();
        commonBase.setEnabled(commonEnabled);

        personalBase = new KnowledgeBaseConfig();
        personalBase.setEnabled(personalEnabled);

        // 有新版本才有VIP专属智能体知识库
        vipCommonBase = new KnowledgeBaseConfig();
        vipCommonBase.setEnabled(vipCommonEnabled);
    }
}
