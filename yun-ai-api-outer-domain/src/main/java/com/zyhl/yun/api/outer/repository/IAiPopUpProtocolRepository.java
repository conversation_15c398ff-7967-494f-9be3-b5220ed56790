package com.zyhl.yun.api.outer.repository;


import com.zyhl.yun.api.outer.domain.entity.AiPopUpProtocolEntity;

/**
 * AI弹窗协议配置-Repository
 * @Author: WeiJingKun
 */
public interface IAiPopUpProtocolRepository {

    /**
     * 获取AI弹窗协议配置
     * @Author: Wei<PERSON><PERSON><PERSON><PERSON>
     */
    /**
     * 获取AI弹窗协议配置
     * @Author: WeiJing<PERSON>un
     *
     * @param entity 查询条件
     * @return AI弹窗协议配置
     */
    AiPopUpProtocolEntity query(AiPopUpProtocolEntity entity);

}
