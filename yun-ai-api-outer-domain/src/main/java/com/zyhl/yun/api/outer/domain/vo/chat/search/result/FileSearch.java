package com.zyhl.yun.api.outer.domain.vo.chat.search.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 对话信息-搜索结果-文件-接口结果
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FileSearch implements Serializable {

    /** 文件id */
    private String fileId;

    /** 文件名称 */
    private String name;

    /**
     * 文件类别
     * 0：其他
     * 1：图片
     * 2：音频
     * 3：视频
     * 4：文档
     * 5：应用
     * 6：压缩文件
     * 100：目录
     */
    private String type;

    /** 创建时间yyyyMMddHHmmss */
    private String createdTime;

    /** 修改时间yyyyMMddHHmmss */
    private String updatedTime;

    /** 文件扩展txt */
    private String extension;

    /** 大小 */
    private String size;

    /** 父文件夹id */
    private String parentFileId;

    /** 内容hash */
    private String contentHash;

    /** 内容hash算法名，当前hash算法支持sha1；旧底座是md5 */
    private String contentHashName;

    /** 全路径ID */
    private String idPath;

    /** 图片拍摄时间yyyyMMddHHmmss */
    private String shootTime;

    /** 缩略图地址 */
    private String thumbnailUrl;

}
