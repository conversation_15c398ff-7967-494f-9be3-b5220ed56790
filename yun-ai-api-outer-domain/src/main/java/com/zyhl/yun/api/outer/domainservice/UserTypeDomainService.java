package com.zyhl.yun.api.outer.domainservice;

import java.util.List;

import com.zyhl.yun.api.outer.enums.UserTypeEnum;

/**
 * 用户类型服务接口
 *
 * <AUTHOR>
 * @date 2025/2/19 09:00
 */
public interface UserTypeDomainService {

	/**
	 * 根据用户获取用户类型
	 * 
	 * @param userId     用户id
	 * @param phone      手机号
	 * @param benefitNos 权益编码列表
	 * @return 用户枚举
	 */
	public UserTypeEnum getUserType(String userId, String phone, List<String> benefitNos);
}
