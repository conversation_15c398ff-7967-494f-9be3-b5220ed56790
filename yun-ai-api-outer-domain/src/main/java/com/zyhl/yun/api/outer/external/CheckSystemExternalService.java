package com.zyhl.yun.api.outer.external;

import com.zyhl.hcy.yun.ai.common.platform.third.client.check.dto.CheckTextReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.vo.CheckResultVO;

/**
 * 送审相关接口
 *
 * <AUTHOR>
 * @date 2024/4/9 17:04
 */
public interface CheckSystemExternalService {

    /**
     * 送审文本内容，默认使用check-system.open配置
     *
     * @param checkTextReqDTO 文本送审参数
     * @return 送审结果
     */
    CheckResultVO checkText(CheckTextReqDTO checkTextReqDTO);

    /**
     * 送审文本内容
     *
     * @param isOpen 是否开关送审参数
     * @param checkTextReqDTO 文本送审参数
     * @return 送审结果
     */
    public CheckResultVO checkText(Boolean isOpen, CheckTextReqDTO checkTextReqDTO);

    /**
     * 文本送审
     *
     * @param dialogueId 对话id
     * @param userId     用户id
     * @param text       送审内容
     * @return 送审结果VO
     */
    CheckResultVO checkText(Long dialogueId, String userId, String text);

    /**
     * 文本送审，不通过则抛异常
     *
     * @param dialogueId 对话id
     * @param userId     用户id
     * @param text       送审内容
     * @return 审核失败则抛异常
     */
    CheckResultVO checkTextException(Long dialogueId, String userId, String text);

    /**
     * 送审文本内容-知识库名称
     *
     * @param isOpen 是否开关送审参数
     * @param checkTextReqDTO 文本送审参数
     * @return 送审结果
     */
    CheckResultVO checkTextName(Boolean isOpen, CheckTextReqDTO checkTextReqDTO);

    /**
     * 送审文本内容-知识库描述
     *
     * @param isOpen 是否开关送审参数
     * @param checkTextReqDTO 文本送审参数
     * @return 送审结果
     */
    CheckResultVO checkTextDescription(Boolean isOpen, CheckTextReqDTO checkTextReqDTO);

    /**
     * 送审文件
     * @param userId 用户iD
     * @param fileId 文件iD
     * @param fileSuffix 文件后缀
     * @param type 文件类型
     * @return 送审结果
     */
    CheckResultVO checkFile(String userId, String fileId, String fileSuffix, Integer type);

}
