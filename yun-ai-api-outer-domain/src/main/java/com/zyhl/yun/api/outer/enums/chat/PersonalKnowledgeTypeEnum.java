package com.zyhl.yun.api.outer.enums.chat;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 个人知识库类型枚举
 * @Author: WeiJingKun
 */
@Getter
@AllArgsConstructor
public enum PersonalKnowledgeTypeEnum {

    /** 文件 */
    FILE("file", "文件"),
    /** 文件夹 */
    FOLDER("folder", "文件夹");

    /**
     * code
     */
    private final String code;
    /**
     * 描述
     */
    private final String desc;


    private static final Map<String, PersonalKnowledgeTypeEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(PersonalKnowledgeTypeEnum.class).forEach(item -> MAP.put(item.code, item));
    }

    public static PersonalKnowledgeTypeEnum getType(String code) {
        if (null == code) {
            return null;
        }
        return MAP.get(code);
    }
}
