package com.zyhl.yun.api.outer.domain.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件处理返回实体
 *
 * @author: yangkailun
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FileRspEntity {
    /**
     * 源文件实体。
     * 该字段存储源文件的相关信息。
     */
    private FileEntity srcFile;

    /**
     * 结果文件实体。
     * 该字段存储处理后的结果文件的相关信息。
     */
    private FileEntity rstFile;

    /**
     * 错误码。
     * 该字段存储操作过程中可能出现的错误码。
     */
    private String errCode;

    /**
     * 消息。
     * 该字段存储与错误码相关的信息或操作结果的描述。
     */
    private String message;

}