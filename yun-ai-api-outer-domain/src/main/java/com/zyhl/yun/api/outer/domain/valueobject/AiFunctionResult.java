package com.zyhl.yun.api.outer.domain.valueobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI工具结果
 * <AUTHOR>
 * @date 2025/4/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiFunctionResult {
    /**
     * 工具编码，与意图编码对应
     */
    private String code;
    
    /**
     * 图片鉴伪结果
     */
    private ImageCheckResult imageCheckResult;

    /**
     * 生成进度，区间[0,100]
     */
    private Integer progress;

    /**
     * 结果文件
     */
    private File file;

    /**
     * 封面图片文件
     */
    private File cover;

    /**
     * 预览地址
     */
    private String previewUrl;

    /**
     * PC端预览地址
     */
    private String pcPreviewUrl;
    
    /**
     * 文件名称
     */
    private String title;

    /**
     * 供应商类型 SupplierTypeEnum
     * 参考SupplierType供应商类型，部分工具存在多个厂商，标识这次结果是哪个厂商的
     */
    private Integer supplierType;

    /**
     * 厂商作品ID，用于跳到厂商的编辑页面，当前仅AIPPT厂商返回
     */
    private String designId;

    /**
     * 封面图文件地址，当前仅AIPPT厂商返回
     */
    private String coverUrl;

}
