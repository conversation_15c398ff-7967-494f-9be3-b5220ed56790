package com.zyhl.yun.api.outer.domain.vo.chat.search.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;

/**
 * 对话信息-搜索参数-活动
 *
 * @Author: WeiJingKun
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchActivityParam extends SearchCommonParam implements Serializable {

    private static final long serialVersionUID = -6298249037235013765L;

    /**
     * 搜索关键字
     */
    private String keywords;

    /** 搜索关键字列表 */
    private List<String> keywordList;

    /**
     * 省份编码
     */
    private Integer provinceCode;

    /**
     * 搜索平台分页信息（默认查总数）
     */
    @Builder.Default
    private @Valid FilePageInfo pageInfo = new FilePageInfo(10, null, null, 1);
}
