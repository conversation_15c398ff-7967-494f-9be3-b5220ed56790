package com.zyhl.yun.api.outer.util;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * className:DateUtil
 * description:
 *
 * <AUTHOR>
 * @date 2024/9/18
 */
public final class DateUtil {

    private DateUtil() {
        throw new AssertionError("Utility class should not be instantiated!!!");
    }

    /**
     * 根据当前时间是否大于当天具体时间节点来判断是否需要刷新key后缀，获取对应的当天日期或前一天日期
     *
     * @param timeNode key更新的时间节点
     * @return 当前日期或前一天日期的字符串
     */
    public static String getCurrentOrPreviousDate(Integer timeNode) {

        LocalDateTime now = LocalDateTime.now();
        LocalTime timeNodeLocal = LocalTime.of(timeNode, 0);

        // is after timeNode
        if (now.toLocalTime().isAfter(timeNodeLocal)) {
            // is after timeNode
            return ":" + now.toLocalDate().format(DateTimeFormatter.ISO_LOCAL_DATE);
        } else {
            // not after timeNode
            return ":" + now.toLocalDate().minusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE);
        }
    }
}
