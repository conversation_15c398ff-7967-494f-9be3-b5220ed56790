package com.zyhl.yun.api.outer.domain.vo.common;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2024/3/5 13:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageInfoVO<T> {
    /**
     * 下一页起始资源标识符
     */
    private String nextPageCursor;

    /**
     * 记录总数，默认不返回。
     */
    private Long totalCount;

    /**
     * 数据
     */
    private List<T> list;

    @NotNull
    public static <T, E> PageInfoVO<T> getRespDTO(PageInfoDTO page, PageInfo<E> pageInfo) {
        PageInfoVO<T> result = new PageInfoVO<>();
        result.setList(Collections.emptyList());
        int nextPageCursor = Integer.parseInt(page.getPageCursor()) + page.getPageSize();
        if (Integer.valueOf(1).equals(page.getNeedTotalCount())) {
            //有total的情况直接用total判断是否有下一页
            result.setTotalCount(pageInfo.getTotal());
            if (nextPageCursor < pageInfo.getTotal()) {
                result.setNextPageCursor(String.valueOf(nextPageCursor));
            }
            return result;
        }
        if (CollUtil.isNotEmpty(pageInfo.getList())) {
            result.setNextPageCursor(String.valueOf(nextPageCursor));
        }
        return result;
    }
}
