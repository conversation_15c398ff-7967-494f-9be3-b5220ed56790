package com.zyhl.yun.api.outer.util;

import com.zyhl.hcy.plugin.redis.HcyRedisTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2024/7/18 13:38
 */
@Slf4j
@Component
public class SimpleRedisLock {

    @Resource
    private HcyRedisTemplate<String, Object> hcyRedisTemplate;

    /**
     * @param lockKey 锁的key
     * @param lockValue 锁的值
     * @param timeout 锁的持续时间
     * @param timeUnit 时间单位
     * @return true表示获取锁成功，false表示获取锁失败
     */
    public boolean tryLock(String lockKey, String lockValue, long timeout, TimeUnit timeUnit) {
        log.info("业务操作尝试加锁，业务锁key：{}，value：{}，锁持续时间：{}，时间单位：{}。", lockKey, lockValue, timeout, timeUnit);
        Boolean acquired = hcyRedisTemplate.opsForValue().setIfAbsent(lockKey, lockValue, timeout, timeUnit);
        return Boolean.TRUE.equals(acquired);
    }

    /**
     * @param lockKey 锁的键名
     * @param lockValue 锁的值，用于验证锁的所有权
     * @return true表示释放成功，false表示释放失败
     */
    public boolean unlock(String lockKey, String lockValue) {
        log.info("业务操作尝试释放锁，业务锁key：{}，value：{}。", lockKey, lockValue);
        String currentValue = String.valueOf(hcyRedisTemplate.opsForValue().get(lockKey));
        if (currentValue != null && currentValue.equals(lockValue)) {
            hcyRedisTemplate.delete(lockKey);
            log.info("业务操作尝试释放锁成功，业务锁key：{}，value：{}", lockKey, lockValue);
            return true;
        }
        log.info("业务操作尝试释放锁失败，锁不存在或已过期！业务锁key：{}，value：{}", lockKey, lockValue);
        return false;
    }
}
