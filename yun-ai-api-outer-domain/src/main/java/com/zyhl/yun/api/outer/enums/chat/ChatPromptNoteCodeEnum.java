package com.zyhl.yun.api.outer.enums.chat;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 笔记对话提示词编码枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ChatPromptNoteCodeEnum {

    RAG_NOTE_NOUN_LIBRARY("note_note_noun_library", "rag_user_note_noun_library", "rag_system_note_noun_library", "笔记知识库名词库"),

    RAG_NOTE_NETWORK_MARK("note_network_mark_0", "rag_user_note_network_mark", "rag_system_note_network_mark", "笔记知识库对话联网有角标"),
    RAG_NOTE_NETWORK_UNMARK("note_network_unmark_0", "rag_user_note_network_unmark", "rag_system_note_network_unmark", "笔记知识库对话联网无角标"),
    RAG_NOTE_UNNETWORK_MARK("note_unnetwork_mark_0", "rag_user_note_unnetwork_mark", "rag_system_note_unnetwork_mark", "笔记知识库对话无联网有角标"),
    RAG_NOTE_UNNETWORK_UNMARK("note_unnetwork_unmark_0", "rag_user_note_unnetwork_unmark", "rag_system_note_unnetwork_unmark", "笔记知识库对话无联网无角标"),

    RAG_NOTE_NETWORK_MARK_1("note_network_mark_1", "rag_user_note_network_mark_1", "rag_system_note_network_mark_1", "笔记知识库对话联网有角标"),
    RAG_NOTE_NETWORK_UNMARK_1("note_network_unmark_1", "rag_user_note_network_unmark_1", "rag_system_note_network_unmark_1", "笔记知识库对话联网无角标"),
    RAG_NOTE_UNNETWORK_MARK_1("note_unnetwork_mark_1", "rag_user_note_unnetwork_mark_1", "rag_system_note_unnetwork_mark_1", "笔记知识库对话无联网有角标"),
    RAG_NOTE_UNNETWORK_UNMARK_1("note_unnetwork_unmark_1", "rag_user_note_unnetwork_unmark_1", "rag_system_note_unnetwork_unmark_1", "笔记知识库对话无联网无角标"),

    RAG_NOTE_NETWORK_MARK_2("note_network_mark_2", "rag_user_note_network_mark_2", "rag_system_note_network_mark_2", "笔记知识库对话联网有角标"),
    RAG_NOTE_NETWORK_UNMARK_2("note_network_unmark_2", "rag_user_note_network_unmark_2", "rag_system_note_network_unmark_2", "笔记知识库对话联网无角标"),
    RAG_NOTE_UNNETWORK_MARK_2("note_unnetwork_mark_2", "rag_user_note_unnetwork_mark_2", "rag_system_note_unnetwork_mark_2", "笔记知识库对话无联网有角标"),
    RAG_NOTE_UNNETWORK_UNMARK_2("note_unnetwork_unmark_2", "rag_user_note_unnetwork_unmark_2", "rag_system_note_unnetwork_unmark_2", "笔记知识库对话无联网无角标"),

    RAG_NOTE_NETWORK_MARK_3("note_network_mark_3", "rag_user_note_network_mark_3", "rag_system_note_network_mark_3", "笔记知识库对话联网有角标"),
    RAG_NOTE_NETWORK_UNMARK_3("note_network_unmark_3", "rag_user_note_network_unmark_3", "rag_system_note_network_unmark_3", "笔记知识库对话联网无角标"),
    RAG_NOTE_UNNETWORK_MARK_3("note_unnetwork_mark_3", "rag_user_note_unnetwork_mark_3", "rag_system_note_unnetwork_mark_3", "笔记知识库对话无联网有角标"),
    RAG_NOTE_UNNETWORK_UNMARK_3("note_unnetwork_unmark_3", "rag_user_note_unnetwork_unmark_3", "rag_system_note_unnetwork_unmark_3", "笔记知识库对话无联网无角标"),

    RAG_NOTE_FORCE_NETWORK_MARK("note_force_network_mark", "rag_user_note_force_network_mark", "rag_system_note_force_network_mark", "笔记知识库对话强制联网有角标"),
    RAG_NOTE_FORCE_NETWORK_UNMARK("note_force_network_unmark", "rag_user_note_force_network_unmark", "rag_system_note_force_network_unmark", "笔记知识库对话强制联网无角标"),


    ;

    private final String code;
    private final String userPrompt;
    private final String systemPrompt;
    private final String desc;


    private static final Map<String, ChatPromptNoteCodeEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(ChatPromptNoteCodeEnum.class).forEach(item -> MAP.put(item.getCode(), item));
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return
     */
    public static ChatPromptNoteCodeEnum getByCode(String code) {
        return MAP.get(code);
    }

    public static ChatPromptNoteCodeEnum getByCode(boolean enableNetworkSearch, boolean enableMark, int type) {
        String network = enableNetworkSearch ? "network" : "unnetwork";
        String mark = enableMark ? "mark" : "unmark";
        return getByCode("note_" + network + "_" + mark + "_" + type);
    }

    public static ChatPromptNoteCodeEnum getByCode(boolean enableMark) {
        String mark = enableMark ? "mark" : "unmark";
        return getByCode("note_force_network_" + mark);
    }

}
