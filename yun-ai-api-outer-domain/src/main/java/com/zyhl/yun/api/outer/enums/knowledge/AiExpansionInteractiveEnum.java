package com.zyhl.yun.api.outer.enums.knowledge;

import com.zyhl.yun.api.outer.enums.chat.ChatEventCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识库AI扩展与前端交互枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AiExpansionInteractiveEnum {
    // 模型切换交互
    /**
     * 要求字数小于1k
     */
    SEARCHING(null, null, null, FlowResultTypeEnum.TEXT_MODEL.getType()),
    /**
     * 当前未选中qwen3模型
     */
    MODEL_SWITCH(ChatEventCodeEnum.SWITCHING_MODEL.getCode(), ChatEventCodeEnum.SWITCHING_MODEL.getDesc(), "自动调度", FlowResultTypeEnum.REASONING_RESULT.getType()),
    /**
     * 当前已选中qwen3模型
     */
    MODEL_SELECTED(ChatEventCodeEnum.SWITCHING_MODEL.getCode(), ChatEventCodeEnum.SWITCHING_MODEL.getDesc(), "使用模型", FlowResultTypeEnum.REASONING_RESULT.getType()),

    // 模型回答交互
    /**
     * 要求字数1-3k
     */
    SUMMARIZE_REPLY_SHORT(null, ChatEventCodeEnum.SWITCHING_MODEL_NEED_TIME.getDesc(), null, FlowResultTypeEnum.REASONING_RESULT.getType()),
    /**
     * 要求字数大于3k
     */
    SUMMARIZE_REPLY_LONG(ChatEventCodeEnum.SWITCHING_MODEL_NEED_TIME.getCode(), ChatEventCodeEnum.SWITCHING_MODEL_NEED_TIME.getDesc(), "预计需要2-3分钟，请耐心等待", FlowResultTypeEnum.REASONING_RESULT.getType()),

    ;

    /**
     * 中间处理状态码，正常回答时为空，不为空时，展示同时的title
     *
     * @see com.zyhl.yun.api.outer.enums.chat.ChatMiddleCodeEnum
     */
    private final String eventCode;

    /**
     * 每个模块流式回答的标题
     */
    private final String title;

    /**
     * 输出文本，流式增量出 例如： 第一句：你好， 第二句：欢迎 第三句：来到中国。
     */
    private final String outContent;

    /**
     * 结果类型
     *
     * @see com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum
     */
    private final Integer resultType;


}
