package com.zyhl.yun.api.outer.external;

import com.zyhl.hcy.yun.ai.common.base.enums.PantaLabelEnum;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.dto.TextNerExtractDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.vo.TextNerExtractVO;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.vo.TextFeatureExtractVO;
import com.zyhl.yun.api.outer.config.AllNetworkSearchProperties;

import java.util.List;
import java.util.Map;

/**
 * 文本向量化接口
 * <AUTHOR>
 */
public interface CmicTextService {

    /**
     * 获取文本向量
     *
     * @param  text 文本内容
     * @return 向量结果
     */
    TextFeatureExtractVO getTextFeature(String text);

    /**
     * 大模型实体抽取（原始接口）
     *
     * @param dialogue 用户输入对话
     * @param searchEntityExtract 搜索实体抽取配置
     * @return 大模型实体抽取-对外方法的响应参数
     */
    TextNerExtractVO textNerExtract(String dialogue, AllNetworkSearchProperties.SearchEntityExtractDTO searchEntityExtract);

    /**
     * 搜索实体抽取
     *
     * @param logPrefix 日志前缀
     * @param dto 大模型实体抽取-对外方法的请求参数
     * @return 搜索实体抽取结果
     */
    Map<PantaLabelEnum, List<String>> searchEntityExtract(String logPrefix, TextNerExtractDTO dto);

}
