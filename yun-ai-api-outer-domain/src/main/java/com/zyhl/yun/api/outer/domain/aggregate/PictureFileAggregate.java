package com.zyhl.yun.api.outer.domain.aggregate;

import cn.hutool.core.date.DateUtil;
import com.zyhl.hcy.yun.ai.common.base.hbase.infrastructure.persistence.po.metadata.FaceInfoPO;
import com.zyhl.hcy.yun.ai.common.base.hbase.infrastructure.persistence.po.metadata.ImageQualityPO;
import com.zyhl.hcy.yun.ai.common.base.hbase.infrastructure.persistence.po.metadata.ThingLabelPO;
import com.zyhl.yun.api.outer.constants.CommonConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.util.List;

/**
 * 图片文件聚合对象
 *
 * <AUTHOR>
 * @description: 图片文件聚合对象
 * @date 2025-05-20 15:43
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PictureFileAggregate {

    /**
     * 文件id
     */
    private String fileId;

    /**
     * 用户userId
     */
    private String userId;

    /**
     * 拍摄时间
     */
    private Date shootTime;

    /**
     * 质量评分
     */
    private Integer score;

    /**
     * 过滤分组字段
     */
    private String filterGroup;

    /**
     * 事物标签
     */
    private List<ThingLabelPO> thingList;

    /**
     * 质量评分
     */
    private ImageQualityPO imageQuality;

    /**
     * 人脸标签数据
     */
    private List<FaceInfoPO> faceList;

    /**
     * 图片特征向量
     */
    private List<Float> feature;

    public PictureFileAggregate setImgQualityScore(int qualityScaleFactor) {
        double scoreDouble = Double.parseDouble(String.valueOf(imageQuality.getImgQuality()));
        int intScore = (int) (scoreDouble * qualityScaleFactor);
        this.setScore(intScore);
        return this;
    }

    /**
     * 设置按年分组的字段
     * 如:2025-06-19 得到 2025
     */
    public void setYearFilter() {
        String yearStr = DateUtil.format(this.shootTime, CommonConstant.DATE_FORMAT_YEAR);
        this.setFilterGroup(yearStr);
    }

    /**
     * 设置按年分+月份组的字段
     * 如:2025-06-19 得到 202506
     */
    public void setYearMonthFilter() {
        String yearMonthStr = DateUtil.format(this.shootTime, CommonConstant.DATE_FORMAT_YEAR_MONTH);
        this.setFilterGroup(yearMonthStr);
    }

    /**
     * 设置按年分+月份组的字段
     * 如:2025-06-19 得到 202503
     */
    public void setYearSeasonFilter() {
        // 获取 shootTime 所在的年份和季度，例如 "2025-Q2"
        String yearStr = DateUtil.format(this.shootTime, "yyyy");
        int seasonStr = (DateUtil.month(this.shootTime) / 3) + 1;
        this.setFilterGroup(yearStr + "Q" + seasonStr);
    }

    /**
     * 设置按年分+月份组的字段
     * 如:2025-01-01 得到 202501
     */
    public void setYearWeekFilter() {
        int weekOfYear = DateUtil.weekOfYear(this.shootTime);
        String yearStr = DateUtil.format(this.shootTime, "yyyy");
        this.setFilterGroup(yearStr + "W" + weekOfYear);
    }
}
