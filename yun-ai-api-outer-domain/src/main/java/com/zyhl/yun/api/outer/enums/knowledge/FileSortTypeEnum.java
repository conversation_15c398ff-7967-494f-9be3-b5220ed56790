package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 排序字段枚举
 * @date 2025/4/29 11:40
 */

@Getter
@AllArgsConstructor
public enum FileSortTypeEnum {

    IMPORT_TIME(1, "导入时间"),

    RESOURCE_NAME(2, "资源名称"),

    RESOURCE_TYPE(3, "资源类型"),

    RESOURCE_SIZE(4, "资源大小"),
    ;

    private final Integer code;

    private final String description;

    /**
     * 获取所有 code 的列表
     * @return 包含所有 code 的列表
     */
    public static List<Integer> getSortTypes() {
        return Arrays.stream(FileSortTypeEnum.values())
                .map(FileSortTypeEnum::getCode)
                .collect(Collectors.toList());
    }

    /**
     * 是否存在此枚举code
     * @param code 传入code
     * @return 是否存在
     */
    public static boolean isExist(Integer code) {
        return getSortTypes().contains(code);
    }
}
