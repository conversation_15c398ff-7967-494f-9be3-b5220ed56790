package com.zyhl.yun.api.outer.domain.entity.knowledge;

import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;

/**
 * 个人知识库
 *
 * <AUTHOR>
 * @date 2025/04/14
 */
@Data
@NoArgsConstructor
public class UserKnowledgeEntity implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户是否选择知识库 选择，1--已选择；0--未选择；
     */
    private Integer selected;

    /**
     * 属主ID
     */
    private String ownerId;

    /**
     * 知识库目录ID
     */
    private String folderId;

    /**
     * 业务类型
     */
    private Integer ownerType;

    /**
     * 头像
     */
    private String profilePhoto;

    /**
     * 知识库名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 公开/私密；0私密（默认） 1 公开
     */
    private Integer openLevel;

    /**
     * 删除标识；0--正常；1--已删除
     */
    private Integer delFlag;

    /**
     * 文件已解析数量
     */
    private Integer parsedCount;

    /**
     * 资源总数量
     */
    private Integer totalCount;

    /**
     * 文件未完成数量
     */
    private Integer unparsedCount;

    /**
     * 创建人Id
     */
    private String createdBy;

    /**
     * 更新人Id
     */
    private String updatedBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 0--默认；1--笔记同步；
     */
    private Integer bizType;

}