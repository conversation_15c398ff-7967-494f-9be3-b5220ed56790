package com.zyhl.yun.api.outer.enums.chat;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * AI会话内容-删除状态
 * @Author: WeiJingKun
 */
public enum DelFlagEnum {

    /**
     * 删除状态  是
     */
    YES(1, "是"),

    /**
     * 删除状态  否
     */
    NO(0, "否"),

    ;

    private static final Map<Integer, DelFlagEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(DelFlagEnum.class).forEach(item -> MAP.put(item.code, item));
    }

    public static DelFlagEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return MAP.get(code);
    }

    /**
     * 是否存在
     * @param code 编码
     * @return true-存在
     */
    public static boolean isExist(Integer code) {
        return getByCode(code) != null;
    }

    DelFlagEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
