package com.zyhl.yun.api.outer.domain.entity.knowledge;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeDialogueConfigTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 知识库对话配置表-Entity
 *
 * @Author: zhumaoxian
 */
@Data
public class KnowledgeDialogueConfigEntity {
    /**
     * id
     */
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 参数类型：1-召回（RecallConfig），2-重排（RerankConfig），3-对话（DialogueConfig）
     *
     * @see KnowledgeDialogueConfigTypeEnum
     */
    private Integer type;

    /**
     * 参数类型对应的配置，全量json格式
     */
    private String configJson;

    /**
     * 删除标识，0--正常；1--已删除
     */
    private Integer delFlag;

    /**
     * 创建时间，默认值用current_timestamp
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间，默认值用current_timestamp
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

}
