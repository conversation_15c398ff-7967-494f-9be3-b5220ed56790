package com.zyhl.yun.api.outer.enums;

import lombok.Getter;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description}
 * <p>
 *
 * <AUTHOR>
 * @since 4月15 2025
 */
@Getter
public enum FastReadStatusEnum {
  /**
   * 任务处理状态
   * -1--处理失败
   * 0--处理中
   * 1--处理成功
   */

  /**
   * 处理失败
   */
  TASK_FAILURE(-1, "处理失败"),

  /**
   * 处理中
   */
  IN_PROCESS(0, "处理中"),

  /**
   * 处理成功
   */
  PROCESS_SUCCESS(1, "处理成功"),

  ;

  /**
   * 状态
   */
  private final int status;
  /**
   * 备注
   */
  private final String desc;

  FastReadStatusEnum(int status, String desc) {
    this.status = status;
    this.desc = desc;
  }
}
