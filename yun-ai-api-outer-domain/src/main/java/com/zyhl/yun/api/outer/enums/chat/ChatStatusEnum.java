package com.zyhl.yun.api.outer.enums.chat;

import lombok.Getter;

/**
 * 对话状态，algorithm_chat_content表
 *
 * <AUTHOR>
 */
@Getter
public enum ChatStatusEnum {

    /**
     * 对话中
     */
    CHAT_IN(0, "对话中"),

    /**
     * 对话停止
     */
    CHAT_STOP(1, "对话停止"),

    /**
     * 对话失败
     */
    CHAT_FAIL(2, "对话失败"),

    /**
     * 对话成功
     */
    CHAT_SUCCESS(3, "对话成功"),

    /**
     * 对话终止
     */
    CHAT_END(4, "对话终止"),

    /**
     * 对话过期
     */
    CHAT_EXPIRE(5, "对话过期"),

    ;


    public static boolean isChatIn(Integer code) {
        return CHAT_IN.code.equals(code);
    }

    public static boolean isChatStop(Integer code) {
        return CHAT_STOP.code.equals(code);
    }

    public static boolean isChatFail(Integer code) {
        return CHAT_FAIL.code.equals(code);
    }

    public static boolean isChatSuccess(Integer code) {
        return CHAT_SUCCESS.code.equals(code);
    }

    public static boolean isChatEnd(Integer code) {
        return CHAT_END.code.equals(code);
    }

    public static boolean isChatExpire(Integer code) {
        return CHAT_EXPIRE.code.equals(code);
    }


    private ChatStatusEnum(Integer code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 备注
     */
    private final String remark;

}
