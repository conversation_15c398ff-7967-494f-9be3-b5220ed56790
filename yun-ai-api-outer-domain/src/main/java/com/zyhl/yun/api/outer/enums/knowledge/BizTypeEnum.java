package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识库业务类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BizTypeEnum {

    /**
     * 正常
     */
    DEFAULT(0, "默认"),

    /**
     * 笔记同步
     */
    NOTE_SYNC(1, "笔记同步"),
    ;

    /**
     * 类型
     */
    private final Integer code;
    /**
     * 备注
     */
    private final String remark;

    public static boolean isDefault(Integer code) {
        return DEFAULT.code.equals(code);
    }

    public static boolean isNoteSync(Integer code) {
        return NOTE_SYNC.code.equals(code);
    }

}
