package com.zyhl.yun.api.outer.domainservice.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.redis.RedisRepository;
import com.zyhl.yun.api.outer.domain.entity.FileEntity;
import com.zyhl.yun.api.outer.domain.req.BatchGetReqEntity;
import com.zyhl.yun.api.outer.domain.req.SearchEntity;
import com.zyhl.yun.api.outer.domain.req.SearchPageEntity;
import com.zyhl.yun.api.outer.domain.resp.IntelligentSearchRespEntity;
import com.zyhl.yun.api.outer.domain.resp.PhotoSearchRespEntity;
import com.zyhl.yun.api.outer.domainservice.FileExternalService;
import com.zyhl.yun.api.outer.domainservice.IntelligentSearchService;
import com.zyhl.yun.api.outer.domainservice.SearchPageService;
import com.zyhl.yun.api.outer.enums.IntelligentSearchEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 智能搜图分页
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SearchPageServiceImpl implements SearchPageService {

    @Resource
    private RedisRepository redisRepository;

    @Resource
    private FileExternalService fileExternalService;

    @Resource
    private IntelligentSearchService intelligentSearchService;


    /**
     * 下标 1
     */
    private static final Integer SUBSCRIPT_ONE = 1;

    /**
     * 下标 0
     */
    private static final Integer SUBSCRIPT_ZERO = 0;


    /**
     * 智能搜图分页
     *
     * @param data data
     * @return 搜索结果
     */
    @Override
    public SearchPageEntity searchList(IntelligentSearchRespEntity data, SearchEntity entity) {
        String cursor = IdUtil.simpleUUID();
        //请求要求的长度
        String code = IntelligentSearchEnum.PAGE_DISPLAY_COUNT.getCode();
        long pageSize = Long.parseLong(code);
        if (ObjectUtil.isNotEmpty(entity.getPageInfo()) && StringUtils.isNotEmpty(entity.getPageInfo().getPageSize())) {
            pageSize = Long.parseLong(entity.getPageInfo().getPageSize());
        }
        dataCaching(cursor, data);
        return getFileEntityList(cursor, pageSize);
    }

    /**
     * 有游标 继续往下取
     *
     * @param entity entity
     * @return 搜索结果
     */
    @Override
    public SearchPageEntity searchList(SearchEntity entity) {
        String pageCursor = entity.getPageInfo().getPageCursor();
        String[] parts = pageCursor.split(StrPool.UNDERLINE);
        String code = IntelligentSearchEnum.PAGE_DISPLAY_COUNT.getCode();
        Long pageSize = Long.valueOf(code);
        if (ObjectUtil.isNotEmpty(entity.getPageInfo()) && StringUtils.isNotEmpty(entity.getPageInfo().getPageSize())) {
            pageSize = Long.valueOf(entity.getPageInfo().getPageSize());
        }
        //从缓存中取出文件id
        SearchPageEntity fileEntityList = getFileEntityList(pageCursor, pageSize);
        if (CollUtil.isEmpty(fileEntityList.getFileInfo())) {
            //根据key获取为空的情况处理
            BaseResult<IntelligentSearchRespEntity> result = intelligentSearchService.intelligentSearch(entity);
            if (ObjectUtil.isEmpty(result) || ObjectUtil.isEmpty(result.getData()) || CollectionUtils.isEmpty(result.getData().getPhotos())) {
                log.info("查询智能查询接口返回为空");
                return new SearchPageEntity();
            }
            String cursor = IdUtil.simpleUUID();
            dataCaching(cursor, result.getData());
            //防止游标不带_
            int integer = SUBSCRIPT_ONE;
            if (ArrayUtils.getLength(parts) > SUBSCRIPT_ONE && parts[SUBSCRIPT_ONE] != null && !parts[SUBSCRIPT_ONE].isEmpty()) {
                integer = Integer.parseInt(parts[SUBSCRIPT_ONE]) + SUBSCRIPT_ONE;
            }
            SearchPageEntity page = new SearchPageEntity();
            for (int i = SUBSCRIPT_ZERO; i < integer; i++) {
                page = getFileEntityList(cursor, pageSize);
                if (CollUtil.isEmpty(page.getFileInfo())) {
                    break;
                }
                cursor = page.getNextPageCursor();
            }
            return page;
        }
        return fileEntityList;
    }

    /**
     * 从缓存获取数据
     *
     * @param cursor   游标
     * @param pageSize 页码
     * @return SearchPageEntity
     */
    private SearchPageEntity getFileEntityList(String cursor, Long pageSize) {

        if (StringUtils.isEmpty(cursor)) {
            log.info("Key为空,从缓存获取数据");
            return new SearchPageEntity();
        }

        List<FileEntity> fileEntities = new ArrayList<>();
        //是否最后一页标识
        boolean lastGroup = Boolean.FALSE;
        //循环结束标识
        boolean cycleSwitch = Boolean.TRUE;
        //从缓存获取的长度
        Long leftSize = pageSize;
        //获取key
        String[] split = cursor.split(StrPool.UNDERLINE);

        //排序使用的集合
        List<String> order = new ArrayList<>();
        //缓存弹出的文件id
        List<String> list;
        do {
            list = redisRepository.leftPopMultiple(IntelligentSearchEnum.INTELLIGENT_SEARCH_KEY.getCode() + split[0], leftSize);
            order.addAll(list);
            //防止临界值 重新从redis获取数据 进行判断
            List<String> cacheList = redisRepository.getCacheList(IntelligentSearchEnum.INTELLIGENT_SEARCH_KEY.getCode() + split[0]);
            if (list.size() < leftSize || CollUtil.isEmpty(list) || CollUtil.isEmpty(cacheList)) {
                lastGroup = Boolean.TRUE;
            }
            List<FileEntity> fileEntity = new ArrayList<>();
            if (CollUtil.isNotEmpty(list)) {
                BatchGetReqEntity batchGetReq = new BatchGetReqEntity();
                batchGetReq.setImageThumbnailStyleList(Collections.singletonList("Large"));
                batchGetReq.setUserId(RequestContextHolder.getUserId());
                batchGetReq.setFileIds(list);
                batchGetReq.setExpireSec(86400);
                //调用个人云接口
                fileEntity = fileExternalService.fileBatchGet(batchGetReq);
            }
            if (CollUtil.isEmpty(fileEntity) && CollUtil.isEmpty(cacheList)) {
                cycleSwitch = Boolean.FALSE;
            }
            fileEntities.addAll(fileEntity);
            if (fileEntities.size() < pageSize) {
                leftSize = pageSize - fileEntities.size();
            }
            if (Boolean.TRUE.equals(lastGroup || fileEntities.size() == pageSize)) {
                cycleSwitch = Boolean.FALSE;
            }
        } while (Boolean.TRUE.equals(cycleSwitch));

        // 创建一个映射，将文件名映射到它在排序列表中的位置
        Map<String, Integer> sortOrderMap = IntStream.range(0, order.size())
                .boxed()
                .collect(Collectors.toMap(order::get, i -> i));
        //重新排序
        fileEntities.sort(Comparator.comparingInt(entity ->
                sortOrderMap.getOrDefault(entity.getFileId(), Integer.MAX_VALUE)));

        SearchPageEntity page = new SearchPageEntity();
        page.setFileInfo(fileEntities);
        String nextPageCursor;
        if (ArrayUtils.getLength(split) > SUBSCRIPT_ONE && split[SUBSCRIPT_ONE] != null && !split[SUBSCRIPT_ONE].isEmpty()) {
            // 第二个元素存在且不为空
            nextPageCursor = split[SUBSCRIPT_ZERO] + StrPool.UNDERLINE + (Integer.parseInt(split[SUBSCRIPT_ONE]) + SUBSCRIPT_ONE);
        } else {
            nextPageCursor = split[SUBSCRIPT_ZERO] + StrPool.UNDERLINE + SUBSCRIPT_ONE;
        }
        page.setNextPageCursor(Boolean.TRUE.equals(lastGroup) ? null : nextPageCursor);
        return page;
    }

    /**
     * 数据缓存
     *
     * @param cursor 游标
     * @param data   数据
     */

    private void dataCaching(String cursor, IntelligentSearchRespEntity data) {
        String key = IntelligentSearchEnum.INTELLIGENT_SEARCH_KEY.getCode() + cursor;
        List<PhotoSearchRespEntity> list = data.getPhotos();
        List<String> collect = list.stream().map(PhotoSearchRespEntity::getFileId).collect(Collectors.toList());
        collect = collect.stream().distinct().collect(Collectors.toList());
        redisRepository.setCacheList(key, collect);
        redisRepository.expire(key, 1800);
    }
}
