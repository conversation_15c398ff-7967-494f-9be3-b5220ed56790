package com.zyhl.yun.api.outer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 大模型文件对话配置
 * <AUTHOR>
 * @date 2024/7/26 14:26
 */
@Configuration
@ConfigurationProperties(prefix = "text-file-mode")
@Data
public class TextFileModeProperties {

    /**
     * 大模型上传文件数量
     */
    private Integer fileNum;

    /**
     * 大模型上传文件大小
     */
    private Integer fileSize;

    /**
     * 大模型上传文件支持的后缀
     */
    private List<String> fileSuffixList;
}
