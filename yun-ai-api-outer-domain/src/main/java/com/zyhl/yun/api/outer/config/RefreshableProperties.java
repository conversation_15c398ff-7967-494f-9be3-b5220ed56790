package com.zyhl.yun.api.outer.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @since 2023/9/9 16:47
 */
@Data
@Slf4j
@RefreshScope
@Component
public class RefreshableProperties {

    @Value("${aiManage.type}")
    private Integer type;

//    @Value("${aiManage.whiteUser}")
//    private List<String> whiteUser;

    @Value("${yun.external.person.expireSec-max}")
    private Integer expireSec;

    @Value("${nfs.path}")
    private String filePath;

    @Value("${nfs.aiPath}")
    private String aiPath;

    @Value("${nfs.basePath}")
    private String basePath;
    @Value("${nfs.connectionTimeout:15}")
    private Integer connectionTimeout;

    @Value("${nfs.readTimeout:60}")
    private Integer readTimeout;

    @PostConstruct
    private void init() {
        String separator = System.getProperties().getProperty("file.separator");
        if (!separator.equals(filePath.substring(filePath.length() - 1))) {
            filePath += separator;
        }
    }


//    public Boolean isWhiteUser(String userId) {
//        if(CollUtil.isEmpty(whiteUser)){
//            return false;
//        }
//        return whiteUser.contains(userId);
//    }

}
