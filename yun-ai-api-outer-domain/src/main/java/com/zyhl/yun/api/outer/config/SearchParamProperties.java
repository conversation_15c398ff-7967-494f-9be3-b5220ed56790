package com.zyhl.yun.api.outer.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 搜索条件配置
 * @Author: WeiJingKun
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "search-param-properties")
public class SearchParamProperties {

    /** 过滤器 */
    private Map<String, Map<String, List<ParamFilter>>> filterMap;

    /**
     * 参数值过滤
     */
    @Data
    public static class ParamFilter {

        // 搜索范畴
        public static final String CATEGORY = "category";
        // 公共
        public static final String COMMON = "common";
        // queryTypes
        public static final String QUERY_TYPES = "queryTypes";

        /** 类别 */
        private String type;

        /** 备注 */
        private String desc;

        /** 需要过滤的关键字（、隔开） */
        private String filterKeywords;

        /**
         * 搜索范畴枚举类
         * @Author: WeiJingKun
         */
        @Getter
        @AllArgsConstructor
        public enum CategoryEnum {

            /* 影视类 */
            FILM( "film", "影视类"),
            /** 书籍类 */
            BOOK("book", "书籍类"),
            /** 文档类 */
            DOCUMENT("document", "文档类"),
            /** 音频类 */
            AUDIO("audio", "音频类"),
            /** 笔记 */
            NOTE("note", "笔记"),
            /** 邮件 */
            MAIL("mail", "邮件"),
            /** 功能类 */
            FUNCTION("function", "功能类"),
            /** 活动类 */
            ACTIVITY("activity", "活动类"),
            /** 图片 */
            IMAGE("image", "图片"),
            /** 圈子 */
            GROUP("group", "圈子"),
            ;

            /** 类型 */
            private final String type;
            /** 描述 */
            private final String  remark;
        }

        /**
         * 过滤关键字
         *
         * @param keywordList 关键字列表
         * @Author: WeiJingKun
         */
        public List<String> filterKeywords(List<String> keywordList){
            if(CharSequenceUtil.isBlank(filterKeywords) || CollUtil.isEmpty(keywordList)){
                return keywordList;
            }
            List<String> filterKeywordList = CollUtil.toList(filterKeywords.split("、"));
            // list的数据转小写
            keywordList = keywordList.stream().map(String::toLowerCase).collect(Collectors.toList());
            filterKeywordList = filterKeywordList.stream().map(String::toLowerCase).collect(Collectors.toList());
            // 过滤关键字
            keywordList.removeAll(filterKeywordList);
            return keywordList;
        }

    }

    /**
     * 获取搜索参数过滤配置
     */
    public List<ParamFilter> getParamFilterList(String searchType, String field) {
        // 根据搜索类型获取搜索参数过滤配置
        if(null == filterMap){
            return null;
        }
        Map<String, List<ParamFilter>> paramFilterMap = filterMap.get(searchType);
        // 获取单个搜索类型里的字段过滤配置
        if(null == paramFilterMap){
            return null;
        }
        return paramFilterMap.get(field);
    }

    /**
     * 过滤【公共】关键字
     *
     * @param keywordList 关键字列表
     * @Author: WeiJingKun
     */
    public List<String> filterCommonKeywords(List<String> keywordList){
        if(CollUtil.isEmpty(keywordList)){
            return keywordList;
        }
        Map<String, List<ParamFilter>> filter = filterMap.get(ParamFilter.COMMON);
        if(ObjectUtil.isNull(filter)){
            return keywordList;
        }
        List<ParamFilter> paramFilterList = filter.get(ParamFilter.QUERY_TYPES);
        if(CollUtil.isEmpty(paramFilterList)){
            return keywordList;
        }
        return paramFilterList.get(0).filterKeywords(keywordList);
    }

    /**
     * 获取【搜索范畴】关键字
     *
     * @param category 搜索范畴
     * @Author: WeiJingKun
     */
    public List<String> getCategoryKeywords(String category){
        List<String> keywordList = new ArrayList<>();
        if(CharSequenceUtil.isBlank(category)){
            return keywordList;
        }
        Map<String, List<ParamFilter>> filter = filterMap.get(ParamFilter.CATEGORY);
        if(ObjectUtil.isNull(filter)){
            return keywordList;
        }
        List<ParamFilter> paramFilterList = filter.get(category);
        if(CollUtil.isNotEmpty(paramFilterList)){
            for (ParamFilter paramFilter : paramFilterList){
                keywordList.addAll(CollUtil.toList(paramFilter.getFilterKeywords().split("、")));
            }
        }
        return keywordList;
    }

}
