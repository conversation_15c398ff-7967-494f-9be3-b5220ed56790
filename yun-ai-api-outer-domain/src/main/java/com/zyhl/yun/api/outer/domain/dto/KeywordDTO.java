package com.zyhl.yun.api.outer.domain.dto;

import com.zyhl.hcy.yun.ai.common.rag.dto.validate.dialogue.KeywordConfig;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.dialogue.RerankConfig;
import com.zyhl.hcy.yun.ai.common.rag.vo.RecallResultVO;
import lombok.Data;

import java.util.List;

/**
 * 描述：重排参数
 *
 * <AUTHOR> zhumaoxian  2025/2/18 14:27
 */
@Data
public class KeywordDTO {


    /**
     * 待提取文本列表
     */
    private List<String> textList;

    /**
     * 关键字提取配置
     */
    private KeywordConfig config;
}
