package com.zyhl.yun.api.outer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/6/4 15:01
 */
@Configuration
@ConfigurationProperties(prefix = "second-stream.intelligent")
@Data
public class SecondStreamIntelligentProperties {

    /**
     * 会议邮件配置
     */
    private SecondStreamSendMailConfig meetingSendMail;

    /**
     * ppt邮件配置
     */
    private SecondStreamSendMailConfig pptSendMail;

    @Data
    public static class SecondStreamSendMailConfig{

        private String modeCode;

        private String promptTemplate;
    }

}
