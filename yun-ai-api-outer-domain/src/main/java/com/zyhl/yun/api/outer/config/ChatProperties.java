package com.zyhl.yun.api.outer.config;

import com.zyhl.yun.api.outer.config.chat.ChatOutputConfig;
import com.zyhl.yun.api.outer.config.chat.ContinueTransConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 对话配置
 */
@Data
@Configuration
@ConfigurationProperties("chat")
public class ChatProperties {

    /**
     * 断点续传配置
     */
    private ContinueTransConfig continueTrans = new ContinueTransConfig();

    /**
     * 对话输出配置
     */
    private ChatOutputConfig chatOutput = new ChatOutputConfig();
}
