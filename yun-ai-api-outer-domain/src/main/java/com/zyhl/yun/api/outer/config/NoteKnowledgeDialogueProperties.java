package com.zyhl.yun.api.outer.config;

import com.zyhl.hcy.yun.ai.common.rag.dto.validate.dialogue.*;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 笔记助手-知识库对话配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "note.knowledge.dialogue")
public class NoteKnowledgeDialogueProperties {

    /**
     * 重写配置
     */
    private RewriteConfig rewriteConfig = new RewriteConfig();

    /**
     * 关键字提取配置
     */
    private KeywordConfig keywordConfig = new KeywordConfig();

    /**
     * 多路召回配置
     */
    private RecallConfig recallConfig = new RecallConfig();

    /**
     * 多路召回配置-总结类、建议类、发言稿
     */
    private RecallConfig recallConfigSummary = new RecallConfig();

    /**
     * 【默认】算法重排配置
     */
    private RerankConfig rerankConfig = new RerankConfig();

    /**
     * 【默认】算法重排配置-总结类、建议类、发言稿
     */
    private RerankConfig rerankConfigSummary = new RerankConfig();

    /**
     * 【向量】算法重排配置
     */
    private RerankConfig vectorRerankConfig = new RerankConfig();
    /**
     * 【向量】算法重排配置-总结类、建议类、发言稿
     */
    private RerankConfig vectorRerankConfigSummary = new RerankConfig();

    /**
     * 【全文】算法重排配置
     */
    private RerankConfig textRerankConfig = new RerankConfig();
    /**
     * 【全文】算法重排配置-总结类、建议类、发言稿
     */
    private RerankConfig textRerankConfigSummary = new RerankConfig();

    /**
     * 【关键字】算法重排配置
     */
    private RerankConfig keywordRerankConfig = new RerankConfig();
    /**
     * 【关键字】算法重排配置-总结类、建议类、发言稿
     */
    private RerankConfig keywordRerankConfigSummary = new RerankConfig();

    /**
     * 相关性配置
     */
    private RelevancyConfig relevancyConfig = new RelevancyConfig();

    /**
     * 多路重排开关
     */
    private boolean enableMultiRerank = false;

    /**
     * 数据搜索超时时间
     */
    private int searchTimeout = 10;
}
