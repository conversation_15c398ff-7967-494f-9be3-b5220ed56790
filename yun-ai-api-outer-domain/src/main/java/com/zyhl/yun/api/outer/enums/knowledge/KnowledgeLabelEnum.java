package com.zyhl.yun.api.outer.enums.knowledge;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识库标签枚举类
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum KnowledgeLabelEnum {

    /**
     * 全部
     */
    ALL(-1L, "全部"),

    /**
     * 未分类
     */
    UNCLASSIFIED(0L, "未分类"),
    ;

    /**
     * id
     */
    private final Long id;
    /**
     * 名称
     */
    private final String name;



    public static KnowledgeLabelEnum getById(Long id) {
        for (KnowledgeLabelEnum e : KnowledgeLabelEnum.values()) {
            if (e.id.equals(id)) {
                return e;
            }
        }
        return null;
    }

    public static KnowledgeLabelEnum getById(String name) {
        for (KnowledgeLabelEnum e : KnowledgeLabelEnum.values()) {
            if (e.name.equals(name)) {
                return e;
            }
        }
        return null;
    }

    public static boolean isExist(Long id) {
        return id != null && getById(id) != null;
    }

    public static boolean isExist(String name) {
        return !StrUtil.isEmpty(name) && getById(name) != null;
    }

    public static boolean isAll(Long id) {
        return ALL.getId().equals(id);
    }

    public static boolean isUnClassify(Long id) {
        return UNCLASSIFIED.getId().equals(id);
    }


}
