package com.zyhl.yun.api.outer.domainservice;

import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.vo.TextFeatureExtractVO;

/**
 * RAG向量化-Service
 *
 * @Author: WeiJingKun
 */
public interface RAGFeatureService {

    /**
     * 获取文本向量化（公共知识库）
     *
     * @param baseId       知识库的标识
     * @param rewriteQuery 重写的Query
     * @param dialogueId   对话id
     * @return 向量结果
     * @Author: WeiJingKun
     */
    TextFeatureExtractVO getTextFeatureCommonKnowledgeHandler(String baseId, String rewriteQuery, Long dialogueId);

    /**
     * 获取文本向量化（公共知识库）
     *
     * @param baseId       知识库的标识
     * @param rewriteQuery 重写的Query
     * @param dialogueId   对话id
     * @return 向量结果
     * @Author: WeiJingKun
     */
    TextFeatureExtractVO getTextFeatureCommonKnowledgeHandler(String baseId, String rewriteQuery, String dialogueId);

    /**
     * 获取文本向量化
     *
     * @param userId     用户id
     * @param text       文本
     * @param dialogueId 对话id
     * @return 向量结果
     */
    TextFeatureExtractVO getTextFeature(String userId, String text, Long dialogueId);

}
