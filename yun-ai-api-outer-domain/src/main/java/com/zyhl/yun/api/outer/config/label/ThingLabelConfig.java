package com.zyhl.yun.api.outer.config.label;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 意图回忆相册-事物标签配置类
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@Configuration
@ConfigurationProperties(
        prefix = "thing.label"
)
public class ThingLabelConfig {

    /**
     * 事物反选集合
     * 小标签
     */
    private List<Long> inverses;

    /**
     * 事物标签最低分数
     */
    private int thingScore;

    private List<VirtualThingLabelEntity> includeLabels;

    public List<Long> getInverses() {
        return inverses;
    }

    public void setInverses(List<Long> inverses) {
        this.inverses = inverses;
    }

    private static List<VirtualThingLabelEntity> defualtIncludeLabels;

    static {

        /**
         * 377 #天空
         *           - 391 #星空
         *           - 392 #日出、日落
         *           - 381 #河、湖
         *           - 382 #海
         *           - 380 #瀑布
         *           - 379 #沙漠
         *           - 384 #雪景
         *           - 378 #山
         *           - 383 #草原
         *           - 393 #CBD、摩天楼
         *           - 366 #中式建筑
         *           - 367 #西式建筑
         *           - 394 #游乐场
         *           - 401 #建筑
         *           - 38 #盆栽
         *           - 385 #花
         *           - 390 #树
         *           - 400 #植物
         *           - 373
         */
        VirtualThingLabelEntity landscape = VirtualThingLabelEntity.builder().sort(1).smallLabel(Arrays.asList(377L,391L,392L,381L,382L,380L,379L,384L,378L,383L,393L,366L,367L,394L,401L,38L,385L,390L,400L,373L)).build();
        VirtualThingLabelEntity fool = VirtualThingLabelEntity.builder().sort(2).smallLabel(Arrays.asList(17L, 18L, 19L, 20L, 22L, 23L, 34L, 35L, 36L, 46L, 56L, 83L, 84L, 85L, 86L, 87L, 89L, 167L, 168L, 169L, 170L, 171L, 172L, 173L, 174L, 175L, 176L, 177L, 178L, 179L, 180L, 181L, 182L, 183L, 184L, 185L, 186L, 187L, 188L, 189L, 190L, 191L, 192L, 193L, 194L, 195L, 196L, 197L, 198L, 199L, 200L, 201L, 202L, 203L, 204L, 205L, 206L, 207L, 208L, 209L, 210L, 211L)).build();
        VirtualThingLabelEntity animal = VirtualThingLabelEntity.builder().sort(3).smallLabel(Arrays.asList(6L, 7L, 8L, 9L, 10L, 11L, 12L, 13L, 14L, 15L, 45L, 48L, 49L, 50L, 51L, 52L, 53L, 54L, 55L, 57L, 58L, 59L, 60L, 61L, 62L, 63L, 64L, 65L, 66L, 67L, 68L, 69L, 70L, 71L, 72L, 73L, 74L, 75L, 76L, 77L, 78L, 79L, 80L, 81L, 82L, 90L, 91L, 92L, 93L, 94L, 95L, 96L, 97L, 98L, 99L, 100L, 101L, 102L, 103L, 104L, 105L, 106L, 107L, 108L, 109L, 110L, 111L, 112L, 113L, 114L, 115L, 116L, 117L, 118L, 119L, 120L, 121L, 122L, 123L, 124L, 125L, 126L, 127L, 128L, 129L, 130L, 131L, 132L, 133L, 134L, 135L, 136L, 137L, 138L, 139L, 140L, 141L, 142L, 143L, 144L, 145L, 146L, 147L, 148L, 149L, 150L, 151L, 152L, 153L, 154L, 155L, 156L, 157L, 158L, 159L)).build();

        defualtIncludeLabels = new ArrayList<>();
        defualtIncludeLabels.add(landscape);
        defualtIncludeLabels.add(fool);
        defualtIncludeLabels.add(animal);
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class VirtualThingLabelEntity {

        private Integer sort;

        private List<Long> smallLabel;
    }
}
