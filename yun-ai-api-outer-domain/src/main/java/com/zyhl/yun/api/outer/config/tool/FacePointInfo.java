package com.zyhl.yun.api.outer.config.tool;

import lombok.Data;

/**
 * 人脸锚点信息
 *
 * @author: z<PERSON><PERSON><PERSON>n
 */
@Data
public class FacePointInfo {
    /**
     * 人脸序号
     */
    private Integer faceNo;
    /**
     * 人脸框位置-距离左侧距离
     */
    private Integer leftTopX;
    /**
     * 人脸框位置-距离顶部距离
     */
    private Integer leftTopY;
    /**
     * 人脸框位置-宽度
     */
    private Integer width;
    /**
     * 人脸框位置-高度
     */
    private Integer height;
}