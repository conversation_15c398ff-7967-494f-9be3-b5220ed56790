package com.zyhl.yun.api.outer.enums.chat.search;

import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 对话信息-搜索参数-文件-类别枚举
 * @Author: WeiJingKun
 */
@Getter
@AllArgsConstructor
public enum SearchFileParamTypeEnum {

    /**
     * 综合(全部)
     */
    COMPREHENSIVE(0, "综合(全部)", DialogueIntentionEnum.COMPREHENSIVE_SEARCH),
    /**
     * 图片
     */
    IMAGE(1, "图片", DialogueIntentionEnum.SEARCH_IMAGE),
    /**
     * 音频
     */
    AUDIO(2, "音频", DialogueIntentionEnum.SEARCH_AUDIO),
    /**
     * 视频
     */
    VIDEO(3, "视频", DialogueIntentionEnum.SEARCH_VIDEO),
    /**
     * 文档
     */
    DOCUMENT(4, "文档", DialogueIntentionEnum.SEARCH_DOCUMENT),
    /**
     * 目录
     */
    FOLDER(5, "目录", DialogueIntentionEnum.SEARCH_FOLDER),
    // 其他类型，前端调用【个人云资产搜索接口】时使用
    // OTHER(6, "其他", DialogueIntentionEnum.SEARCH_FOLDER),

    ;

    private static final Map<DialogueIntentionEnum, SearchFileParamTypeEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(SearchFileParamTypeEnum.class).forEach(item -> MAP.put(item.dialogueIntentionEnum, item));
    }

    public static SearchFileParamTypeEnum getByDialogueIntentionEnum(DialogueIntentionEnum dialogueIntentionEnum) {
        if (null == dialogueIntentionEnum) {
            return null;
        }
        return MAP.get(dialogueIntentionEnum);
    }

    /**
     * 是否存在
     * @param dialogueIntentionEnum 对话意图-枚举
     * @return true-存在
     */
    public static boolean isExist(DialogueIntentionEnum dialogueIntentionEnum) {
        return getByDialogueIntentionEnum(dialogueIntentionEnum) != null;
    }

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 描述
     */
    private final String desc;

    private final DialogueIntentionEnum dialogueIntentionEnum;

}
