package com.zyhl.yun.api.outer.domain.vo.chat.search.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 对话信息-搜索结果-邮件
 * @Author: WeiJingK<PERSON>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchMailResult extends SearchCommonResult implements Serializable {

    /** 邮件信息列表 */
    private List<SearchMail> searchMailList;

    /** 卡片标题 */
    private String title;

    /** 消息提示 */
    private String tips;

    /** 记录总数 */
    private Integer totalCount;

}
