package com.zyhl.yun.api.outer.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;


/**
 * 多意图合并配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties("multiple-intention-merge")
public class MultipleIntentionMergeProperties {

    /**
     * 多意图搜索合并开关，true-需要合并，false-不需要合并（默认true）
     */
    private boolean open = true;

}
