package com.zyhl.yun.api.outer.util;

import cn.hutool.core.text.CharSequenceUtil;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 处理HTML字符串的工具类
 *
 * <AUTHOR>
 * @date 2025/5/7 16:10
 */
public class HtmlResourceProcessorUtils {

    // 匹配包含HTML实体编码的src属性
    private static final Pattern SRC_TAG_PATTERN = Pattern.compile(
            "<(img|video|audio|source|iframe)[^>]+src\\s*=\\s*[\"'](\\{&quot;.*?&quot;})[\"'][^>]*>",
            Pattern.CASE_INSENSITIVE
    );


    // 匹配HTML实体编码格式的fileId
    private static final Pattern FILE_ID_PATTERN = Pattern.compile(
            "&quot;fileId&quot;:\\s*&quot;([^&]+)&quot;"
    );


    // 私有构造防止实例化
    private HtmlResourceProcessorUtils() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }

    /**
     * 从HTML内容中提取所有fileId
     *
     * @param htmlContent HTML内容
     * @return 找到的fileId集合（去重）
     */
    public static List<String> extractFileIds(String htmlContent) {
        if (CharSequenceUtil.isBlank(htmlContent)) {
            return Collections.emptyList();
        }

        Matcher matcher = SRC_TAG_PATTERN.matcher(htmlContent);
        // 保证顺序且去重
        Set<String> fileIdSet = new LinkedHashSet<>(); 

        while (matcher.find()) {
            String encodedContent = matcher.group(2);
            String fileId = extractFileIdFromEncodedJson(encodedContent);
            if (fileId != null) {
                fileIdSet.add(fileId);
            }
        }
        return new ArrayList<>(fileIdSet);
    }


    /**
     * 替换资源URL（区分已审核和未审核资源）
     *
     * @param htmlContent     HTML内容
     * @param approvedUrls    审核通过的URL映射
     * @param defaultImageUrl 默认图片URL
     * @return 处理后的HTML
     */
    public static String replaceResourceUrls(String htmlContent, Map<String, String> approvedUrls, String defaultImageUrl) {
        // 如果原始内容为空或空白，直接返回原内容
        if (CharSequenceUtil.isBlank(htmlContent)) {
            return htmlContent;
        }

        // 审核通过的 URL 映射兜底处理为不可变空 Map，防止 NPE
        Map<String, String> safeApprovedUrls = Optional.ofNullable(approvedUrls)
                .map(Collections::unmodifiableMap)
                .orElse(Collections.emptyMap());

        StringBuffer result = new StringBuffer();
        Matcher matcher = SRC_TAG_PATTERN.matcher(htmlContent);

        while (matcher.find()) {
            String fullTag = matcher.group();
            String srcValue = matcher.group(2);
            String fileId = extractFileIdFromEncodedJson(srcValue);

            String replacementUrl;
            if (!CharSequenceUtil.isBlank(fileId) && safeApprovedUrls.containsKey(fileId)) {
                // 审核通过的资源
                replacementUrl = safeApprovedUrls.get(fileId);
            } else {
                // 未审核或审核失败的资源使用默认图片
                replacementUrl = defaultImageUrl;
            }

            String replacement = createReplacementTag(fullTag, replacementUrl);
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(result);

        return result.toString();
    }

    /**
     * 创建替换后的标签
     */
    private static String createReplacementTag(String originalTag, String newUrl) {
        return originalTag.replaceFirst(
                "src\\s*=\\s*[\"']\\{&quot;.*?&quot;}[\"']",
                "src=\"" + newUrl + "\""
        );
    }

    /**
     * 从src属性值中提取fileId
     */
    private static String extractFileIdFromEncodedJson(String srcValue) {
        if (CharSequenceUtil.isBlank(srcValue)) {
            return null;
        }
        Matcher matcher = FILE_ID_PATTERN.matcher(srcValue);
        return matcher.find() ? matcher.group(1) : null;
    }
}