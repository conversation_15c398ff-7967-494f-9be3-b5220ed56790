package com.zyhl.yun.api.outer.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.IntentEntityVO;
import com.zyhl.yun.api.outer.vo.KeyValueVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2024/4/20 18:13
 */
@Slf4j
public class EntityListUtils {

    /**
     * 校验实体结果列表
     *
     * @param entityList    实体结果entity列表
     * @param message       引导文案
     * @param intentionEnum 意图枚举
     */
    public static void checkEntityList(List<IntentEntityVO> entityList, String message,
                                       DialogueIntentionEnum intentionEnum) throws YunAiBusinessException {
        if (CollectionUtils.isEmpty(entityList)) {
            throw new YunAiBusinessException(AiResultCode.CODE_10022024.getCode(), message);
        }
        
        boolean success = false;
        YunAiBusinessException yunAiBusinessException = null;
        for (IntentEntityVO entity : entityList) {
        	try {
        		checkIntentEntityVO(entity, message, intentionEnum);
        		success = true;
        	}catch (YunAiBusinessException e) {
        		yunAiBusinessException = e;
			}
        	if(success) {
        		break;
        	}
        }
        if(!success && null != yunAiBusinessException) {
        	throw yunAiBusinessException;
        }
    }

    /**
     * 校验EntityVO
     *
     * @param entity        实体结果entity
     * @param message       引导文案
     * @param intentionEnum 意图枚举
     */
    public static void checkIntentEntityVO(IntentEntityVO entity, String message, DialogueIntentionEnum intentionEnum)
            throws YunAiBusinessException {
        // 是否存在可用数据 true存在
        boolean flag = false;

        // 时间列表
        if (!containsEmptyString(entity.getTimeList())) {
            flag = true;
        }

        // 地点列表
        if (!containsEmptyString(entity.getPlaceList())) {
            flag = true;
        }

        // 图片标签列表
        if (!containsEmptyString(entity.getImageNameList())) {
            flag = true;
        }

        // 事物标签列表
        if (!containsEmptyStringValue(entity.getLabelList())) {
            flag = true;
        }

        // 实体列表
        if (!containsEmptyStringValue(entity.getMetaDataList())) {
            flag = true;
        }
        
        // 后缀列表
        if (!containsEmptyString(entity.getSuffixList())) {
            flag = true;
        }

        // 发件人列表
        if (!containsEmptyString(entity.getSenderList())) {
            flag = true;
        }

        // 邮箱列表
        if (!containsEmptyString(entity.getEmailAddressList())) {
            flag = true;
        }

        // 状态列表
        if (!containsEmptyString(entity.getStatusList())) {
            flag = true;
        }

        // 类别列表
        if (!containsEmptyString(entity.getTypeList())) {
            flag = true;
        }

        // 标题列表 032发邮件
        if (!containsEmptyString(entity.getTitleList())) {
            flag = true;
        }

        // 内容列表 032发邮件
        if (!containsEmptyString(entity.getContentList())) {
            flag = true;
        }

        // 所有列表都无可用数据，抛出异常
        if (!flag) {
            throw new YunAiBusinessException(AiResultCode.CODE_10022024.getCode(), message);
        }

        // 笔记意图要求实体列表必须有可用数据，如果没有抛出异常
        if (DialogueIntentionEnum.SEARCH_NOTE.equals(intentionEnum) && containsEmptyStringValue(entity.getMetaDataList())) {
            throw new YunAiBusinessException(AiResultCode.CODE_10022024.getCode(), message);
        }
    }

    /**
     * 校验列表
     *
     * @param list 字符串列表
     * @return true表示不存在可用数据
     */
    public static boolean containsEmptyString(List<String> list) {
        // 列表为空，直接返回true
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }

        for (String str : list) {
            // 存在可用数据
            if (StringUtils.hasText(str)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 校验KeyValueVO列表
     *
     * @param list VO结构列表
     * @return true表示不存在可用数据
     */
    public static boolean containsEmptyStringValue(List<KeyValueVO> list) {
        // 列表为空，直接返回true
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }

        for (KeyValueVO vo : list) {
            // 存在可用数据
            if (!containsEmptyString(vo.getValue())) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取所有关键字，根据实体结果entity列表
     * @Author: WeiJingKun
     *
     * @param firstIntentionInfo 第一个意图结果
     * @return 所有关键字
     */
    public static List<String> getAllKeywordByEntityList(DialogueIntentionVO.IntentionInfo firstIntentionInfo) {
        List<String> keywordList = new ArrayList<>();
        /** 获取实体结果entity列表，根据意图结果 */
        List<IntentEntityVO> entityList = getEntityListByIntentionInfo(firstIntentionInfo);
        if(CollUtil.isEmpty(entityList)){
            return keywordList;
        }

        /** 获取所有关键字，根据实体结果entity列表 */
        for (IntentEntityVO entity : entityList) {
            // 时间列表
            addKeywordByStr(keywordList, entity.getTimeList());

            // 地点列表
            addKeywordByStr(keywordList, entity.getPlaceList());

            // 事物标签列表
            addKeywordByKeyValue(keywordList, entity.getLabelList());

            // 实体列表
            addKeywordByKeyValue(keywordList, entity.getMetaDataList());

            // 图片标签列表
            addKeywordByStr(keywordList, entity.getImageNameList());

            // 后缀列表
            addKeywordByStr(keywordList, entity.getSuffixList());

            // 发件人列表（028邮件搜索）
            addKeywordByStr(keywordList, entity.getSenderList());

            // 邮箱列表（028邮件搜索）
            addKeywordByStr(keywordList, entity.getEmailAddressList());

            // 状态列表（028邮件搜索）
            addKeywordByStr(keywordList, entity.getStatusList());

            // 类别列表（028邮件搜索）
            addKeywordByStr(keywordList, entity.getTypeList());

            // 标题列表（032发邮件）
            addKeywordByStr(keywordList, entity.getTitleList());

            // 内容列表（032发邮件）
            addKeywordByStr(keywordList, entity.getContentList());

            // 收件人列表（032发邮件）
            addKeywordByStr(keywordList, entity.getRecipientList());
        }
        if (CollUtil.isNotEmpty(keywordList)){
            // 去的空数据
            keywordList.removeIf(CharSequenceUtil::isBlank);
            // 去重
            keywordList = keywordList.stream().distinct().collect(Collectors.toList());
        }
        return keywordList;
    }

    /**
     * 根据KeyValue格式数据添加关键字
     * @Author: WeiJingKun
     *
     * @param keywordList 关键字list
     * @param keyValueList 需要处理的数据
     */
    private static void addKeywordByKeyValue(List<String> keywordList, List<KeyValueVO> keyValueList) {
        if (!containsEmptyStringValue(keyValueList)) {
            for (KeyValueVO vo : keyValueList) {
                keywordList.addAll(vo.getValue());
            }
        }
    }

    /**
     * 根据字符串添加关键字
     * @Author: WeiJingKun
     *
     * @param keywordList 关键字list
     * @param valueList 需要处理的数据
     */
    private static void addKeywordByStr(List<String> keywordList, List<String> valueList) {
        if (!containsEmptyString(valueList)) {
            keywordList.addAll(valueList);
        }
    }

    /**
     * 获取实体结果entity列表，根据意图结果
     * @Author: WeiJingKun
     *
     * @param intentionInfo 意图结果
     * @return 实体结果entity列表
     */
    public static List<IntentEntityVO> getEntityListByIntentionInfo(DialogueIntentionVO.IntentionInfo intentionInfo) {
        List<IntentEntityVO> entityList = new ArrayList<>();
        if(ObjectUtil.isNotNull(intentionInfo)){
            // 综合搜索，需要获取子意图列表的entity列表
            if(DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode().equals(intentionInfo.getIntention())){
                List<DialogueIntentionVO.IntentionInfo> subIntentions = intentionInfo.getSubIntentions();
                if(CollUtil.isNotEmpty(subIntentions)){
                    for (DialogueIntentionVO.IntentionInfo subIntention : subIntentions) {
                        entityList.addAll(subIntention.getEntityList());
                    }
                }
            }
            // 前面无值，则使用当前实体列表
            if(CollUtil.isEmpty(entityList)){
                entityList = intentionInfo.getEntityList();
            }
        }
        if(CollUtil.isNotEmpty(entityList)){
            // 去的空数据
            entityList.removeIf(ObjectUtil::isNull);
        }
        return entityList;
    }

}
