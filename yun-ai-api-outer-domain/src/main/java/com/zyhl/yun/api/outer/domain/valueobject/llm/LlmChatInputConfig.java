package com.zyhl.yun.api.outer.domain.valueobject.llm;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文本大模型输入配置参数
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LlmChatInputConfig {

	/**
	 * 指定模型可生成的最大token个数。
	 */
	private Integer maxTokens;

	/**
	 * 温度参数，用于调整生成文本的确定性和创新性，默认为 0.7
	 */
	private Float temperature = 0.7F;

	/**
	 * 生成时使用的随机数种子，用于控制模型生成内容的随机性。seed支持无符号64位整数，默认值为1234。
	 */
	private Integer seed = 1234;

	/**
	 * 参数，用于调整生成文本的随机性和多样性，默认为 1.0
	 */
	private Float topP = 1.0F;

	/**
	 * 生成回答个数，默认为 1
	 */
	private Integer n = 1;

}
