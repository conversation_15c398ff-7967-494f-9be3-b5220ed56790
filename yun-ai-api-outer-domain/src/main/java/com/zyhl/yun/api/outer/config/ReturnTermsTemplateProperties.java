package com.zyhl.yun.api.outer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 返回词模板配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "search-return-terms")
public class ReturnTermsTemplateProperties {

    /** 默认千问大模型 */
    private static final String DEFAULT_MODEL_CODE = "qwen";

    /** 是否返回系统默认提示词：true-默认；false-使用模板 */
    private Boolean returnSystemDefault;

    /** 过滤关键字 */
    private List<String> excludeKeywords;

    /** 过滤意图 */
    private List<String> excludeIntentionList;

    /** 意图模板配置 */
    private IntentionTemplate intentionTemplate;

    public ReturnTermsTemplateProperties.TemplateConfig getTemplateConfig(String intentionCode) {
        if (intentionTemplate == null || intentionTemplate.getTemplateList() == null) {
            return null;
        }
        for (ReturnTermsTemplateProperties.TemplateConfig templateConfig : intentionTemplate.getTemplateList()) {
            if (intentionCode.equals(templateConfig.getIntention())) {
                return templateConfig;
            }
        }
        return null;
    }

    @Data
    public static class IntentionTemplate {
        /** 模型编码 */
        private String modelCode = DEFAULT_MODEL_CODE;
        /** 大模型提示词模板 */
        private String unifiedTemplate;
        /** 意图-返回词模板映射 */
        private List<TemplateConfig> templateList;
    }

    @Data
    public static class TemplateConfig {
        /**
         * 意图编码
         */
        private String intention;
        /**
         * 提示词模板
         */
        private String template;
        /**
         * 默认文案，大模型返回空时候使用
         */
        private String defaultText;

        /**
         * 默认文案（英文）
         */
        private String defaultTextEn;
        /**
         * 是否需要过滤关键词的标志：true-过滤；false-不过滤
         */
        private Boolean filterFlag;
    }

}
