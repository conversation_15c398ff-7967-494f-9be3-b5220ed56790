package com.zyhl.yun.api.outer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * mq消费者配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "rocketmq.consumer")
public class RocketmqConsumerProperties {

    /**
     * 用户隐私授权
     */
    private ConsumerConfig userPrivacyAuth;

    // ------------------------------------- //

    @Data
    public static class ConsumerConfig {
        /**
         * 主题
         */
        private String topic;
        /**
         * 消息队列组
         */
        private String groupName;
        /**
         * 标签
         */
        private String tag;
        /**
         * 消费超时时间（分）
         */
        private Long consumeTimeout = 15L;
        /**
         * 消费线程数
         */
        private Integer consumeThreadNums = 16;
        /**
         * 实例名称
         */
        private String instanceName;
    }

}
