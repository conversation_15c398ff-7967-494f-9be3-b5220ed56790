package com.zyhl.yun.api.outer.config.textmodel;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * 联网对话大模型配置类
 * 
 * <AUTHOR>
 * @date 2025-06-09 10:27
 */
@Configuration
@ConfigurationProperties(prefix = "text-model.network-chat")
@Data
public class NetworkChatConfig {

	/**
	 * 搜索来源字段，默认snippet（可选：mainText，snippet）
	 */
	private String searchSourceField = "snippet";
	
	/**
	 * role=system，系统联网提示词
	 */
	private String systemPrompt;

}
