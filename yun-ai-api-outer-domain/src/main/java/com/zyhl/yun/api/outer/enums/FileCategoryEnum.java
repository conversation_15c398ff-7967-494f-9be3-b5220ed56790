package com.zyhl.yun.api.outer.enums;

import java.util.Arrays;
import java.util.List;

/**
 * 文件类别枚举
 * <AUTHOR>
 * @date 2025/4/30 14:36
 */
public enum FileCategoryEnum {

    /**
     * 图片类型
     */
    IMAGE("image", Arrays.asList("bmp","ilbm","png","gif","jepg","jpg","mng","ppm","heic","webp","livp")),

    AUDIO("audio", Arrays.asList("wav","pcm","tta","flac","au","ape","mp3", "wma", "ogg", "aac", "ra", "midi",
            "mpc", "pac", "mv", "cmf", "cda", "aif", "aiff", "m4a", "mka", "mp2", "mpa", "wv", "ac3", "dts", "amr", "3gpp")),

    VIDEO("video", Arrays.asList("avi", "mpeg", "mpg", "divx", "xvid", "rm", "rmvb", "mov", "qt", "asf", "wmv",
            "navi", "vob", "3gp", "mp4", "flv", "avs", "mkv", "ogm", "ts", "tp", "nsv")),
    ;


    FileCategoryEnum(String code, List<String> extensions) {
        this.code = code;
        this.extensions = extensions;
    }

    /**
     * 类型编码
     */
    private String code;

    /**
     * 文件后缀
     */
    private List<String> extensions;

    public String getCode() {
        return code;
    }

    public List<String> getExtensions() {
        return extensions;
    }
}
