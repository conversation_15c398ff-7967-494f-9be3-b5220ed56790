package com.zyhl.yun.api.outer.constants;

/**
 * <p>
 * 正则
 * </p>
 *
 * <AUTHOR>
 */
public class RegConst {


    /**
     * 参数解析分隔符
     */
    public final static String REG_SPLIT_STR = "\\|";
    /**
     * 数字正则
     */
    public final static String REG_DATA_STR = "\\d+";

    /**
     * id正则
     */
    public final static String REG_ID_STR = "\\d{1,19}";

    /**
     * 非数字
     */
    public final static String REG_NOT_DATA = "[^0123456789]";

    /**
     * 模型推理内容
     */
    public final static String REG_MODEL_THINK = "<think>[\\S\\s]*</think>";

    /**
     * 大模型角标
     */
    public final static String TEXT_MODE_CITATION = "\\[[1-100]\\]";

    /**
     * 链接地址
     */
    public final static String ADDRESS = "(https?:\\/\\/(?:localhost|[a-zA-Z0-9][-a-zA-Z0-9]*(?:\\.[a-zA-Z0-9][-a-zA-Z0-9]*)+)(?::\\d+)?(?:\\/[-a-zA-Z0-9@:%_\\+.~#?&\\/=]*|\\?[-a-zA-Z0-9@:%_\\+.~#?&\\/=]*|#[-a-zA-Z0-9@:%_\\+.~#?&\\/=]*)*)";

    /**
     * 时间统一格式为RFC 3339格式(东八区)
     * 注：2019-10-12T14:20:50.52+08:00
     */
    public final static String TIME_RFC3339 = "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d{1,9})?\\+08:00$";

}
