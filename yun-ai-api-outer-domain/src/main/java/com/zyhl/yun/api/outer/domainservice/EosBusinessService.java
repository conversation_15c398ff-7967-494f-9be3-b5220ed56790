package com.zyhl.yun.api.outer.domainservice;

import com.zyhl.yun.api.outer.domain.entity.ObjectKeysEntity;
import com.zyhl.yun.api.outer.domain.entity.ReportInfoEntity;
import com.zyhl.yun.api.outer.domain.entity.UploadPicEntity;
import com.zyhl.yun.api.outer.vo.UploadPicVO;

/**
 * 文件上传服务
 *
 * @author: wujianwei
 */
public interface EosBusinessService {

    /**
     * 上传文件并返回文件url
     *
     * @param entity 上传文件实体
     * @return 上传结果
     */
    UploadPicVO uplaodAndGetUrl(UploadPicEntity entity);

    /**
     * 取消上传
     *
     * @param entity 上传文件实体
     * @return 数量
     */
    Integer cancel(ObjectKeysEntity entity);

    /**
     * 提交上传
     *
     * @param entity 上传文件实体
     * @return 数量
     */
    Integer submit(ReportInfoEntity entity);

}
