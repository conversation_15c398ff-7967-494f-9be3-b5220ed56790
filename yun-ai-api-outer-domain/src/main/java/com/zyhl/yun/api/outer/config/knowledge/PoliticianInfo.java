package com.zyhl.yun.api.outer.config.knowledge;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 行政人物配置
 * 
 * <AUTHOR>
 * @date 2025-03-30
 */
@Data
public class PoliticianInfo {
    /**
     * 姓名
     */
    private String name;
    /**
     * 职位
     */
    private String position;
    /**
     * 替换信息
     */
    private List<ReplaceInfo> replaceInfoList = new ArrayList<>();
    /**
     * 排序，从大到小
     */
    private int sort;

    /**
     * 替换信息
     */
    @Data
    public static class ReplaceInfo {
        /**
         * 需要替换的字符串
         */
        private String replace;
        /**
         * replace的长度
         */
        private int length;
        /**
         * 尊称
         */
        private String honorific;
    }
}