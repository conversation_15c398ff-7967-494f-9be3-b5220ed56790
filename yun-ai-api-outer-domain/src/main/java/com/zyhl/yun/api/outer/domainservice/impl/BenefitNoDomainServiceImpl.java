package com.zyhl.yun.api.outer.domainservice.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.config.ClientTypeProperties;
import com.zyhl.yun.api.outer.config.MemberCenterProperties;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domainservice.BenefitNoDomainService;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 权益编号领域
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BenefitNoDomainServiceImpl implements BenefitNoDomainService {

    @Resource
    private MemberCenterProperties memberCenterProperties;
    @Resource
    private SourceChannelsProperties sourceChannelsProperties;
    @Resource
    private ClientTypeProperties clientTypeProperties;
    @Resource
    private ChatConfigServiceDomainService chatConfigServiceDomainService;
    @Resource
    private ModelProperties modelProperties;

    @Override
    public String getBenefitNo(String sceneTag, String channel, String clientType, String phone) {
        // 先查询会话设置
        String businessCode = sourceChannelsProperties.getCode(channel);
        String businessType = sourceChannelsProperties.getType(channel);
        AssistantEnum assistantEnum = AssistantEnum.getByCode(businessCode);
        ChatConfigEntity chatConfigEntity = chatConfigServiceDomainService.getUserCanUseModel(RequestContextHolder.getUserId(), phone, assistantEnum, businessType);
        if (chatConfigEntity != null) {
            Map<String, ModelProperties.ModelLimitConfig> modelLimitConfigMap = modelProperties.getLimitByAssistantEnum(assistantEnum, businessType);
            if (ObjectUtil.isNotEmpty(modelLimitConfigMap)) {
                ModelProperties.ModelLimitConfig modelLimitConfig = modelLimitConfigMap.get(chatConfigEntity.getModelType());
                if (modelLimitConfig != null && Boolean.FALSE.equals(modelLimitConfig.isPaid())) {
                    log.info("【权益编号】免费模型，模型配置：{}", JsonUtil.toJson(modelLimitConfig));
                    return StringUtils.EMPTY;
                }
            }
        }

        MemberCenterProperties.SceneTagBenefit sceneTagBenefit = getBysceneTag(sceneTag);
        if (sceneTagBenefit != null) {
            log.info("channel:{},【权益编号】通过场景标识获取到权益编号：{}，启用：{}", channel, sceneTagBenefit.getBenefitNo(),
                    sceneTagBenefit.isEnabled());
            return sceneTagBenefit.isEnabled() ? sceneTagBenefit.getBenefitNo() : "";
        }

        String benefitNo = getByChannel(channel, clientType);
        log.info("channel:{}, clientType:{}, 【权益编号】获取到权益编号：{}", channel, clientType, benefitNo);
        return benefitNo;
    }

    /**
     * @param channel       渠道
     * @param clientType    客户端标识
     * @param algorithmCode 算法编码
     */
    @Override
    public String getBenefitNoForTool(String channel, String clientType, String algorithmCode) {
        String benefitNo = getByChannelForTool(channel, clientType, algorithmCode);
        log.info("channel:{},【权益编号】获取到的权益编号：{}", channel, benefitNo);
        return benefitNo;
    }

    /**
     * @param channel    渠道
     * @param clientType 客户端标识
     */
    @Override
    public Boolean benefitNoForToolOpen(String channel, String clientType) {
        SourceChannelsProperties.SourceChannel config = sourceChannelsProperties.getByChannel(channel);
        if (config == null || !config.isBenefitToolSwitch()) {
            log.info("【权益编号】渠道权益已关闭，渠道：{}，客户端类型：{}", channel, clientType);
            return false;
        }
        return true;
    }

    /**
     * 通过场景标识获取权益编号
     *
     * @param sceneTag 场景标识
     * @return 权益编号
     */
    private MemberCenterProperties.SceneTagBenefit getBysceneTag(String sceneTag) {
        log.info("【权益编号】通过场景标识获取权益编号，场景标识：{}", sceneTag);
        if (CharSequenceUtil.isEmpty(sceneTag)) {
            return null;
        }

        return memberCenterProperties.getSceneTagBenefitNo(sceneTag);
    }

    /**
     * 通过渠道和客户端标识获取权益编号
     *
     * @param channel    渠道
     * @param clientType 客户端标识
     * @return 权益编号
     */
    private String getByChannel(String channel, String clientType) {
        SourceChannelsProperties.SourceChannel config = sourceChannelsProperties.getByChannel(channel);
        if (config == null || !config.isBenefitSwitch()) {
            log.info("【权益编号】渠道权益已关闭，渠道：{}", channel);
            return StringUtils.EMPTY;
        }

        boolean inner = clientTypeProperties.isInner(clientType);
        log.info("channel:{},【权益编号】客户端类型：{}，端内：{}", channel, clientType, inner);

        return inner ? config.getInnerBenefitNo() : config.getOuterBenefitNo();
    }

    /**
     * 通过渠道和客户端标识获取权益编号 for Tool
     *
     * @param channel       渠道
     * @param clientType    客户端标识
     * @param algorithmCode 算法编码
     * @return 权益编号
     */
    private String getByChannelForTool(String channel, String clientType, String algorithmCode) {
        SourceChannelsProperties.SourceChannel config = sourceChannelsProperties.getByChannel(channel);
        log.info("channel:{},【权益编号】渠道权益打开，渠道配置信息：{}", channel, JsonUtil.toJson(config));
        boolean inner = clientTypeProperties.isInner(clientType);
        log.info("channel:{},【权益编号】客户端类型：{}，端内：{}", channel, clientType, inner);
        List<String> algorithmCodeList = config.getAlgorithmCodeList();
        if (CollectionUtils.isEmpty(algorithmCodeList)) {
            return StringUtils.EMPTY;
        }
        Optional<String> match = algorithmCodeList.stream()
                .filter(algorithmCodeTemp -> algorithmCodeTemp.equals(algorithmCode))
                .findFirst();
        return match.isPresent() ? (inner ? config.getInnerToolBenefitNo() : config.getOuterToolBenefitNo()) : null;
    }

}
