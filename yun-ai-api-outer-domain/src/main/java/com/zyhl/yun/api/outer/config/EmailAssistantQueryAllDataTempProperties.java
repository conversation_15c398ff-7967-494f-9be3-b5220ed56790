package com.zyhl.yun.api.outer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 小邮助手历史数据需要查询全部，临时配置
 *
 * <AUTHOR>
 */
@RefreshScope
@Data
@Configuration
@ConfigurationProperties(prefix = "temp-mail-query-all-data")
public class EmailAssistantQueryAllDataTempProperties {

    /** 时间节点 */
    private String timeNodes;

    /** 剔除的业务类型包含某个关键字的渠道 */
    private String rejectBusinessType;

    /** 查询的条件 */
    private String queryFlag;

}
