package com.zyhl.yun.api.outer.enums.mq;


import java.util.Objects;

/**
 * MQ任务类型枚举
 *
 * <AUTHOR>
 */
public enum MQTaskTypeEnum {


    /**
     * 单个
     */
    SINGLE(1, "单个"),

    /**
     * 全量
     */
    ALL(2, "全量");

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 描述
     */
    private final String msg;

    private MQTaskTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    public Integer getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }

    public static String getMsg(Integer code) {
        for (MQTaskTypeEnum resultEnum : MQTaskTypeEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.getMsg();
            }
        }
        return null;
    }

}
