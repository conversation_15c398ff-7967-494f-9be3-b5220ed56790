package com.zyhl.yun.api.outer.domain.valueobject;

import com.zyhl.hcy.yun.ai.common.model.api.client.vo.TextModelBaseVo;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;

import lombok.Data;

/**
 * HTML信息类
 * 
 * <AUTHOR>
 * @date 2025-04-15
 */
@Data
public class HtmlInfo {
    /**
     * 编号
     */
    private int index;

    /**
     * 网页主题
     */
    private String title;

    /**
     * 网页URL
     */
    private String url;

    /**
     * 来源网站
     */
    private String siteName;

    /**
     * 来源网站图标
     */
    private String icon;

    /**
     * HTML格式的网页内容字符串
     */
    private String htmlContent;

    public HtmlInfo(){

    }

    public HtmlInfo(TextModelBaseVo.NetworkSearchInfoVo searchInfo) {
        this.index = searchInfo.getIndex();
        this.title = searchInfo.getTitle();
        this.url = searchInfo.getUrl();
        this.siteName = searchInfo.getSiteName();
        this.icon = searchInfo.getIcon();
    }
    
    public HtmlInfo(AiTextResultRespParameters.NetworkSearchInfo searchInfo) {
        this.index = searchInfo.getIndex();
        this.title = searchInfo.getTitle();
        this.url = searchInfo.getUrl();
        this.siteName = searchInfo.getSiteName();
        this.icon = searchInfo.getIcon();
    }
}