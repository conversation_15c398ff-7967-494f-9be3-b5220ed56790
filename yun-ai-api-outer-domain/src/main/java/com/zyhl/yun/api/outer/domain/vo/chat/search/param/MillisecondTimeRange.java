package com.zyhl.yun.api.outer.domain.vo.chat.search.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Objects;

/**
 * 对话信息-搜索参数-毫秒时间范围
 *
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MillisecondTimeRange implements Serializable {

    /**
     * 搜索范围的开始时间
     */
    @NotEmpty(message = "搜索范围的开始时间不能为空")
    @JsonFormat(pattern = "yyyyMMddHHmmssSSS", timezone = "GMT+8")
    @Pattern(regexp = "^\\d{17}$", message = "搜索范围的开始时间不符合格式")
    private String startAt;

    /**
     * 搜索范围的结束时间
     */
    @NotEmpty(message = "搜索范围的结束时间不能为空")
    @JsonFormat(pattern = "yyyyMMddHHmmssSSS", timezone = "GMT+8")
    @Pattern(regexp = "^\\d{17}$", message = "搜索范围的开始时间不符合格式")
    private String endAt;

    /**
     * 重写 equals 和 hashCode 方法用于比较两个 TimeRange 对象是否相等
     */

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        MillisecondTimeRange timeRange = (MillisecondTimeRange) o;
        return Objects.equals(startAt, timeRange.startAt) &&
                Objects.equals(endAt, timeRange.endAt);
    }

    @Override
    public int hashCode() {
        return Objects.hash(startAt, endAt);
    }


}
