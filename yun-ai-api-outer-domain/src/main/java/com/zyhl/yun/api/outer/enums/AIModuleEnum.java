package com.zyhl.yun.api.outer.enums;

import lombok.Getter;

/**
 * AI工具模块
 *
 * <AUTHOR>
 */
@Getter
public enum AIModuleEnum {

    /**
     * 智能工具箱
     */
    AI_FILE_LIBRARY(com.zyhl.hcy.yun.ai.common.base.enums.AIModuleEnum.AI_FILE_LIBRARY.getModule(),
        com.zyhl.hcy.yun.ai.common.base.enums.AIModuleEnum.AI_FILE_LIBRARY.getCode(),
        com.zyhl.hcy.yun.ai.common.base.enums.AIModuleEnum.AI_FILE_LIBRARY.getName(),
        com.zyhl.hcy.yun.ai.common.base.enums.AIModuleEnum.AI_FILE_LIBRARY.getCatalogName());

    AIModuleEnum(int module, String code, String name, String catalogName) {

        this.module = module;
        this.code = code;
        this.name = name;
        this.catalogName = catalogName;
    }

    /**
     * 模块
     */
    private final int module;
    /**
     * 模块编码
     */
    private final String code;
    /**
     * 模块名称
     */
    private final String name;
    /**
     * 模块目录名称
     */
    private final String catalogName;


    public static AIModuleEnum getByModule(Integer module) {
        if (module == null) {
            return null;
        }
        for (AIModuleEnum moduleEnum : values()) {
            if (moduleEnum.getModule() == module) {
                return moduleEnum;
            }
        }
        return null;
    }

    public static AIModuleEnum getByCode(String code) {
        for (AIModuleEnum moduleEnum : values()) {
            if (moduleEnum.getCode().equals(code)) {
                return moduleEnum;
            }
        }
        return null;
    }

    public static boolean isExist(String code) {
        return getByCode(code) != null;
    }

    public static boolean isExistModule(Integer module) {
        return getByModule(module) != null;
    }


}
