package com.zyhl.yun.api.outer.enums;

/**
 * <AUTHOR>
 */
public enum AlgorithmInformationEnum {


    /**
     * 科大讯飞算法
     */
    IFLYTEK("1", "科大讯飞"),

    /**
     * 阿里算法
     */
    ALI("2", "阿里"),

    /**
     * 百度算法
     */
    BAIDU("3", "百度"),

    /**
     * 腾讯算法
     */
    TENCENT("4", "腾讯"),

    /**
     * 卓望算法
     */
    ZHUO_WANG("5", "卓望"),

    /**
     * 自研算法
     */
    SELF_DEVELOPED("6", "自研");


    /**
     * 算法编码
     */
    private final String code;
    /**
     * 算法名称
     */
    private final String msg;

    private AlgorithmInformationEnum(String resultCode, String resultMsg) {
        this.code = resultCode;
        this.msg = resultMsg;
    }

    public String getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }

    public static AlgorithmInformationEnum getByCode(String code) {
        for (AlgorithmInformationEnum algorithmEnum : values()) {
            if (algorithmEnum.code.equals(code)) {
                return algorithmEnum;
            }
        }
        return null;
    }

}
