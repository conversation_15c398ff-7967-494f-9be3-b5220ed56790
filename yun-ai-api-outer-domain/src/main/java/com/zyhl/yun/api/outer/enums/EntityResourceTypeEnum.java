package com.zyhl.yun.api.outer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * enumName: EntityResourceTypeEnum
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum EntityResourceTypeEnum {

    /**
     * 网盘链接
     */
    DISK(1, "网盘链接"),

    /**
     * 正版平台
     */
    GENUINE(2, "正版平台"),

    /**
     * 在线网址
     */
    ON_LINE(3, "在线网址");

    /**
     * 标签类型编码
     */
    private final int code;

    /**
     * 标签类型描述
     */
    private final String desc;

    /**
     * 根据描述获取枚举
     * @param desc 描述
     * @return 枚举
     */
    public static EntityResourceTypeEnum getByDesc(String desc) {
        for (EntityResourceTypeEnum typeEnum : EntityResourceTypeEnum.values()) {
            if (typeEnum.getDesc().equals(desc)) {
                return typeEnum;
            }
        }
        return null;
    }

}