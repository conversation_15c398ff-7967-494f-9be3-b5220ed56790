package com.zyhl.yun.api.outer.domainservice.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.zyhl.yun.api.outer.config.AllNetworkSearchProperties;
import com.zyhl.yun.api.outer.domain.vo.AllNetworkSearchRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.chat.ContentVO;
import com.zyhl.yun.api.outer.domainservice.AlgorithmChatContentDomain;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.task.FileExpiredStatusEnum;
import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;
import com.zyhl.yun.api.outer.util.IntentionUtils;

import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlgorithmChatContentDomainImpl implements AlgorithmChatContentDomain {

    @Resource
    private AllNetworkSearchProperties allNetworkSearchProperties;

    @Override
    public void judgeAndCreateResultCode(ContentVO vo) {
        // 初始化参数
        String resultCode = vo.getResultCode();
        String resultMsg = vo.getResultMsg();
        String taskId = vo.getTaskId();
        Integer taskStatus = vo.getTaskStatus();
        String toolsCommand = vo.getToolsCommand();
        String inResourceId = vo.getInResourceId();
        Integer chatStatus = vo.getChatStatus();

        // 任务状态不为null时
        if (taskStatus != null) {
            // 任务失败，则构造映射后的错误码
            if (TaskStatusEnum.isTaskFail(taskStatus)) {
                AiResultCode aiResultCode = AiResultCode.getByCodeOrMsg(resultCode, resultMsg);
                vo.setResultCode(aiResultCode.getCode());
                vo.setResultMsg(aiResultCode.getMsg());
            }
            // 任务状态等于 3【PROCESS_FINISH-任务完成】，即任务成功
            if (TaskStatusEnum.PROCESS_FINISH.getCode().equals(taskStatus)) {
                //fileExpireStatus=1为文件过期，需要设置taskStatus=4，并且设置AiResultCode.CODE_10000205
                if (FileExpiredStatusEnum.EXPIRED.getCode().equals(vo.getFileExpiredStatus())) {
                    log.warn("fileExpireStatus为文件过期，需要设置taskStatus失败，并且设置任务结果已过期 dialogueId:{}, taskId:{}",
                            vo.getDialogueId(), vo.getTaskId());
                    vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
                    vo.setResultCode(AiResultCode.CODE_10000205.getCode());
                    vo.setResultMsg(AiResultCode.CODE_10000205.getMsg());
                } else {
                    vo.setResultCode(ResultCodeEnum.SUCCESS.getResultCode());
                    vo.setResultMsg(ResultCodeEnum.SUCCESS.getResultMsg());
                }
            }
        } else {
            /**
             * 任务状态为null时，并且返回链接的条件判断为true
             * 返回【任务成功】
             */
            if (IntentionUtils.returnLinkCondition(toolsCommand, inResourceId)) {
                // 任务成功
                vo.setResultCode(ResultCodeEnum.SUCCESS.getResultCode());
                vo.setResultMsg(ResultCodeEnum.SUCCESS.getResultMsg());
            }
            /** 是否为文本意图 */
            if (DialogueIntentionEnum.isTextIntention(toolsCommand)) {
                /**
                 * 任务id为空，即V2版本，流式同步数据
                 * chat_status为0，则需要构建数据【处理中】
                 */
                if (CharSequenceUtil.isBlank(taskId) && ChatStatusEnum.isChatIn(chatStatus)) {
                    // 如果chat_status为0，则为处理中，需要构建数据
                    vo.setOutContent("");
                    vo.setTaskStatus(TaskStatusEnum.IN_PROCESS.getCode());
                    vo.setResultCode(AiResultCode.CODE_10000201.getCode());
                    vo.setResultMsg(AiResultCode.CODE_10000201.getMsg());
                    log.info("【文生文意图】流式数据处理中-AlgorithmChatContentDomainImpl-judgeAndCreateResultCode，dialogueId：{}", vo.getDialogueId());
                }
            }
        }
    }

    @Override
    public void judgeAndCreateResultCodeV2(ContentVO vo) {
        // 初始化参数
        String resultCode = vo.getResultCode();
        String resultMsg = vo.getResultMsg();
        String taskId = vo.getTaskId();
        Integer taskStatus = vo.getTaskStatus();
        String toolsCommand = vo.getToolsCommand();
        String inResourceId = vo.getInResourceId();
        Integer chatStatus = vo.getChatStatus();

        // 任务状态不为null时
        if (taskStatus != null) {
            // 任务失败，则构造映射后的错误码
            if (TaskStatusEnum.isTaskFail(taskStatus)) {
                AiResultCode aiResultCode = AiResultCode.getByCodeOrMsg(resultCode, resultMsg);
                vo.setResultCode(aiResultCode.getCode());
                vo.setResultMsg(aiResultCode.getMsg());
            }
            // 任务状态等于 3【PROCESS_FINISH-任务完成】，即任务成功
            if (TaskStatusEnum.PROCESS_FINISH.getCode().equals(taskStatus)) {
                //fileExpireStatus=1为文件过期，需要设置taskStatus=4，并且设置AiResultCode.CODE_10000205
                if (FileExpiredStatusEnum.EXPIRED.getCode().equals(vo.getFileExpiredStatus())) {
                    log.warn("fileExpireStatus为文件过期，需要设置taskStatus失败，并且设置任务结果已过期 dialogueId:{}, taskId:{}",
                            vo.getDialogueId(), vo.getTaskId());
                    vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
                    vo.setResultCode(AiResultCode.CODE_10000205.getCode());
                    vo.setResultMsg(AiResultCode.CODE_10000205.getMsg());
                } else {
                    vo.setResultCode(ResultCodeEnum.SUCCESS.getResultCode());
                    vo.setResultMsg(ResultCodeEnum.SUCCESS.getResultMsg());
                }
            }
        } else {
            /**
             * 任务状态为null时，并且返回链接的条件判断为true
             * 返回【任务成功】
             */
            if (IntentionUtils.returnLinkConditionV2(toolsCommand, inResourceId)) {
                // 任务成功
                vo.setResultCode(ResultCodeEnum.SUCCESS.getResultCode());
                vo.setResultMsg(ResultCodeEnum.SUCCESS.getResultMsg());
            }
            /** 是否为文本意图 */
            if (DialogueIntentionEnum.isTextIntention(toolsCommand)) {
                /**
                 * 任务id为空，即V2版本，流式同步数据
                 * chat_status为0，则需要构建数据【处理中】
                 */
                if (CharSequenceUtil.isBlank(taskId) && ChatStatusEnum.isChatIn(chatStatus)) {
                    // 如果chat_status为0，则为处理中，需要构建数据
                    vo.setOutContent("");
                    vo.setTaskStatus(TaskStatusEnum.IN_PROCESS.getCode());
                    vo.setResultCode(AiResultCode.CODE_10000201.getCode());
                    vo.setResultMsg(AiResultCode.CODE_10000201.getMsg());
                    log.info("【文生文意图】流式数据处理中-AlgorithmChatContentDomainImpl-judgeAndCreateResultCode，dialogueId：{}", vo.getDialogueId());
                }
            }
        }
    }

    @Override
    public DialogueRecommendVO dialogueRecommendHandle(ContentVO vo) {
        // 获取对话结果推荐
        DialogueRecommendVO recommendVO = null;
        String recommendInfo = vo.getRecommendInfo();
        if (CharSequenceUtil.isNotBlank(recommendInfo)) {
            try {
                recommendVO = JSON.parseObject(recommendInfo, DialogueRecommendVO.class);
                // 全网搜推荐列表的按钮文案，获取AllNetworkSearchProperties的buttonCopy值
                List<AllNetworkSearchRecommendVO> allNetworkSearchList = recommendVO.getAllNetworkSearchList();
                if (CollUtil.isNotEmpty(allNetworkSearchList)) {
                    for (AllNetworkSearchRecommendVO allNetworkSearchRecommendVO : allNetworkSearchList) {
                        allNetworkSearchRecommendVO.setButtonCopy(allNetworkSearchProperties.getButtonCopy());
                    }
                }
            } catch (JSONException e) {
                log.error("对话结果推荐处理-AlgorithmChatHistoryServiceImpl-dialogueRecommendHandle，JSON解析异常，recommendVO：{}，recommendInfo：{}", recommendVO, recommendInfo, e);
            } catch (Exception e) {
                log.error("对话结果推荐处理-AlgorithmChatHistoryServiceImpl-dialogueRecommendHandle，异常，recommendVO：{}，recommendInfo：{}", recommendVO, recommendInfo, e);
            }
        }
        vo.setRecommend(recommendVO);
        return recommendVO;
    }

    @Override
    public DialogueRecommendVO dialogueMiddleRecommendHandle(ContentVO vo) {
        // 获取对话结果推荐
        DialogueRecommendVO recommendVO = null;
        String middleRecommendInfo = vo.getMiddleRecommendInfo();
        if (CharSequenceUtil.isNotBlank(middleRecommendInfo)) {
            try {
                recommendVO = JSON.parseObject(middleRecommendInfo, DialogueRecommendVO.class);
            } catch (JSONException e) {
                log.error("对话结果推荐-中部-处理-AlgorithmChatHistoryServiceImpl-dialogueRecommendHandle，JSON解析异常，recommendVO：{}，recommendInfo：{}", recommendVO, middleRecommendInfo, e);
            } catch (Exception e) {
                log.error("对话结果推荐-中部-处理-AlgorithmChatHistoryServiceImpl-dialogueRecommendHandle，异常，recommendVO：{}，recommendInfo：{}", recommendVO, middleRecommendInfo, e);
            }
        }
        vo.setMiddleRecommend(recommendVO);
        return recommendVO;
    }

}
