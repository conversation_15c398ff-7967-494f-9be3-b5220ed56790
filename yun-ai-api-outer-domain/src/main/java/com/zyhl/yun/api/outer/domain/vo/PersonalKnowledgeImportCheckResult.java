package com.zyhl.yun.api.outer.domain.vo;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/27
 */
@Data
public class PersonalKnowledgeImportCheckResult {

    /**
     * 资源类型
     * 0--个人云文件
     * 1--邮件
     * 2--笔记
     * 3--在线链接
     */
    private Integer resourceType;

    /**
     * 知识库Id
     */
    private String baseId;

    /**
     * 邮件列表
     */
    private ImportMailInfoVO mailList;

    /**
     * 笔记列表
     */
    private ImportNoteInfoVO noteList;

    /**
     * 网页链接信息
     */
    private ImportHtmlInfoVO htmlInfo;

    /**
     * 个人云文件
     */
    private File file;

    /**
     * 0--校验通过
     * 1--校验失败，资源已存在
     */
    private Integer checkResult;

    /**
     * 校验失败原因
     */
    private String failMessage;

    public PersonalKnowledgeImportCheckResult recordFail(Integer resourceType, String baseId, ImportHtmlInfoVO htmlInfo, Integer checkResult, String failMessage) {
        this.resourceType = resourceType;
        this.baseId = baseId;
        this.htmlInfo = htmlInfo;
        this.checkResult = 1;
        this.failMessage = "资源已存在";
        return this;
    }

    public PersonalKnowledgeImportCheckResult personalFail(Integer resourceType, String baseId, File file, String failMessage) {
        this.resourceType = resourceType;
        this.baseId = baseId;
        this.file = file;
        this.checkResult = 1;
        this.failMessage = failMessage;
        return this;
    }

    public PersonalKnowledgeImportCheckResult personalFail(Integer resourceType, String baseId, File file, String failMessage, Integer checkResult) {
        this.resourceType = resourceType;
        this.baseId = baseId;
        this.file = file;
        this.checkResult = checkResult;
        this.failMessage = failMessage;
        return this;
    }
}
