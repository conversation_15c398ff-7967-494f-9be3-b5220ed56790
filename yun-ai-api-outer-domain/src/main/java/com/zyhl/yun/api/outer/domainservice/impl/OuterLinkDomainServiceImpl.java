package com.zyhl.yun.api.outer.domainservice.impl;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.domainservice.OuterLinkDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;

import static com.zyhl.yun.api.outer.enums.ResultCodeEnum.FILE_URL_DOWN_FAIL;

/**
 * 外部链接服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class OuterLinkDomainServiceImpl implements OuterLinkDomainService {

    @Override
    public String convertUrlToBase64(String fileUrl) {

        // 连接超时时间，单位：毫秒，这里设置为5秒
        int connectTimeout = 5000;
        // 读取超时时间，单位：毫秒，这里设置为10秒
        int readTimeout = 10000;
        HttpURLConnection connection = null;
        String base64Image = null;
        try {
            URL url = new URL(fileUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");

            // 设置超时时间
            connection.setConnectTimeout(connectTimeout);
            connection.setReadTimeout(readTimeout);

            // 检查请求是否成功（HTTP 200）
            if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                // 读取图片字节
                try (InputStream in = connection.getInputStream()) {
                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = in.read(buffer)) != -1) {
                        baos.write(buffer, 0, bytesRead);
                    }

                    byte[] imageBytes = baos.toByteArray();

                    // 将图片字节转换为Base64字符串
                    base64Image = Base64.getEncoder().encodeToString(imageBytes);
                }
            }
        } catch (IOException e) {
            log.error("文件下载失败", e);
            throw new YunAiBusinessException(FILE_URL_DOWN_FAIL);
        } finally {
            // 确保资源的正确释放
            if (connection != null) {
                connection.disconnect();
            }
        }
        return base64Image;

    }
}
