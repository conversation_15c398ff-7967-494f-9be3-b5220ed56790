package com.zyhl.yun.api.outer.repository;

import com.github.pagehelper.PageInfo;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @data 2024/2/29 14:14
 */
public interface AlgorithmChatContentRepository {

    /**
     * 查询对话分页数据
     *
     * @param entity         对话数据
     * @param offset         分页偏移量
     * @param pageSize       分页数
     * @param needTotalCount 是否需要总数
     * @return 对话分页数据
     */
    PageInfo<AlgorithmChatContentEntity> contentList(AlgorithmChatContentEntity entity, int offset, int pageSize, int needTotalCount);

    /**
     * 根据会话id删除对话
     *
     * @param entity 对话数据
     * @Author: WeiJingKun
     */
    void deleteBySessionIds(AlgorithmChatContentEntity entity);

    /**
     * 保存会话内容
     *
     * @param entity 对话数据
     * @return Boolean
     */
    Boolean saveChatContent(AlgorithmChatContentEntity entity);

    /**
     * 更新输出结果
     *
     * @param id             对话id
     * @param outAuditStatus 输出审核状态
     * @param chatStatus     对话状态
     * @param outContent     输出结果
     * @param recommendInfo 对话结果推荐信息（json格式）
     * @Author: WeiJingKun
     */
    void updateOutResult(Long id, Integer outAuditStatus, Integer chatStatus, String outContent, String recommendInfo);

    /**
     * 停止sse流式对话更新数据
     *
     * @param id 对话id
     */
    void updateOutResultStop(Long id);

    /**
     * 获取并更新对话数据
     *
     * @param entity 对话数据
     * @return 对话数据
     * @Author: WeiJingKun
     */
    AlgorithmChatContentEntity pollingUpdate(AlgorithmChatContentEntity entity);

    /**
     * 对话停止
     *
     * @param id 对话id
     * @return true:停止 false:未停止
     * @Author: WeiJingKun
     */
    boolean updateChatStopById(Long id);

    /**
     * 根据id查询对话数据
     *
     * @param id 对话id
     * @return 对话数据
     */
    AlgorithmChatContentEntity getById(Long id);

    /**
     * 根据id和用户id查询
     *
     * @param id     对话id
     * @param userId 用户id
     * @return 会话内容信息Entity
     */
    AlgorithmChatContentEntity getByIdUserId(Long id, String userId);

    /**
     * 更新对话中的对话状态
     *
     * @param entity 对话数据
     * @return true:更新成功 false:更新失败
     */
    boolean updateChatStatusForInStatus(AlgorithmChatContentEntity entity);

    /**
     * 更新输出资源id
     *
     * @param entity 对话数据
     * @return true:更新成功 false:更新失败
     * @Author: WeiJingKun
     */
    boolean updateOutResourceId(AlgorithmChatContentEntity entity);

    /**
     * 根据会话id查询最后一条对话
     *
     * @param sessionId 会话id
     * @param userId    用户id
     * @return 对话实体
     */
    AlgorithmChatContentEntity getLastOne(Long sessionId, String userId);

    /**
     * 更新对话模型
     *
     * @param dialogueId 对话id
     * @param modelCode  模型编码
     */
    void updateModelCode(Long dialogueId, String modelCode);

    /**
     * 根据会话id查询对话列表并指定最大返回条数
     *
     * @param sessionId 会话id
     * @param maxCount  查询最大条数
     * @param userId    用户id
     * @return 对话实体列表
     */
    List<AlgorithmChatContentEntity> getContentListBySessionId(Long sessionId, Integer maxCount, String userId);

    /**
     * 获取分享对话列表
     *
     * @param sessionId       会话id
     * @param applicationType 应用类型
     * @param userId          用户id
     * @param dialogueIdList  对话id列表
     * @return list
     */
    List<AlgorithmChatContentEntity> getShareContentList(Long sessionId, String applicationType, String userId, List<String> dialogueIdList);

    /**
     * 获取并更新对话数据V2
     *
     * @param entity 对话数据
     * @return 对话数据
     * @Author: WeiJingKun
     */
    AlgorithmChatContentEntity pollingUpdateV2(AlgorithmChatContentEntity entity);

    /**
     * 更新对话状态和输出内容
     *
     * @param chatContentEntity the chat content entity
     * <AUTHOR>
     * @date 2025-4-30 18:16
     */
    void updateChatStatusAndOutResource(AlgorithmChatContentEntity chatContentEntity);

    /**
     * 更新失败对话状态
     * @param dialogueId
     */
    boolean updateChatFailById(Long dialogueId);

    /**
     * 查询最新一条意图对话
     *
     * @param condition
     * @return
     */
    AlgorithmChatContentEntity selectLastIntentionDialogue(AlgorithmChatContentEntity condition);

	/**
	 * 获取用户最近的意图列表
	 * 
	 * @param userId           用户id
	 * @param sessionId        会话id
	 * @param intentionCode    主意图
	 * @param subIntentionCode 子意图【可为空】
	 * @param querySize        查询数量
	 * @return
	 */
	List<AlgorithmChatContentEntity> selectIntentionDialogueWithDesc(String userId, Long sessionId,
			String intentionCode, String subIntentionCode, int querySize);
}
