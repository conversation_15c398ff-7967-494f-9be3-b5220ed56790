package com.zyhl.yun.api.outer.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;

/**
 * 对话结果-邮件信息VO
 *
 * <AUTHOR>
 * @date 2024/12/4 14:15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MailInfoVO {

    /**
     * 收件人列表
     */
    private List<String> recipientList;

    /**
     * 收件邮箱列表
     */
    private List<String> emailAddressList;

    /**
     * 邮件标题
     */
    private String title;

    /**
     * 邮件正文
     */
    private String content;

    /**
     * 发件人
     */
    private String sender;
    
    /**
     * 邮件发送是否成功，仅发邮件场景下返回，因为发邮件是异步操作，无法在调用接口成功就返回mailId
     */
    private Boolean sendMailFlag;
}
