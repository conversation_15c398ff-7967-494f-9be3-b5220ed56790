package com.zyhl.yun.api.outer.constants;

/**
 * 知识库常量
 *
 * <AUTHOR>
 * @date 2025/4/18 14:01
 */
public class KnowledgeConstants {

    /**
     * 默认知识库名称
     */
    public static final String DEFAULT_KNOWLEDGE_NAME = "我的知识库";

    /**
     * 知识库AI扩写字数key
     */
    public static final String REQUIRE_WORD_NUM_KEY = "requireWordNums";

    /**
     * 知识库AI扩写字数desc
     */
    public static final String REQUIRE_WORD_NUM_DESC = "提取文本内容的字数要求。这是一个关于文字数量的指标，与演讲时间无关。例如：'5000字'、'一万字以内'。";

    /**
     * 知识库AI扩写时长key
     */
    public static final String DURATION_MIN_KEY = "durationMins";

    /**
     * 知识库AI扩写时长desc
     */
    public static final String DURATION_MIN_DESC = "提取演讲或发言的时长要求，并统一为分钟。这是一个关于时间的指标，与字数无关。例如：'5min'、'两小时'。";

    /**
     *  知识库重写int的key
     */
    public static final String INT_KEY = "int";
}
