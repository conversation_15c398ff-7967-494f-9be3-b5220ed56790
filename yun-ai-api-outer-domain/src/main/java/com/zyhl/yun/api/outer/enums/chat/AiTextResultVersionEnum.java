package com.zyhl.yun.api.outer.enums.chat;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

/**
 * hbase 结果版本
 * 
 * <AUTHOR>
 *
 *         2025年4月16日
 */
@Getter
@AllArgsConstructor
public enum AiTextResultVersionEnum {

	/**
	 * V1版本
	 */
	V1("v1", "V1版本"),

	/**
	 * V2版本
	 */
	V2("v2", "V2版本"),

	;

	/**
	 * 类型
	 */
	private final String version;

	/**
	 * 备注
	 */
	private final String remark;

	public static boolean isV2(String version) {
		return (V2.getVersion().equals(version));
	}
}
