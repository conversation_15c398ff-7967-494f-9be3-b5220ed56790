package com.zyhl.yun.api.outer.domain.valueobject;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.dashscope.utils.JsonUtils;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.resp.OwnerDriveThumbnailInfo;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveFileVO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.KnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.enums.chat.PersonalKnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileAiStatusParamEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileCategoryEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

/**
 * 个人知识库资源类
 * 
 * <AUTHOR>
 * @date 2025-04-15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PersonalKnowledgeResource {
    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 资源类型
     *
     * @see com.zyhl.yun.api.outer.enums.chat.PersonalKnowledgeResourceTypeEnum
     */
    private Integer resourceType;

    /**
     * 知识库ID
     */
    private String baseId;

    /**
     * 名称
     */
    private String name;

    /**
     * 创建时间，RFC 3339格式
     * 例如：2019-08-20T06:51:27.292+08:00
     */
    private String createdAt;

    /**
     * 更新时间，RFC 3339格式
     * 例如：2019-08-20T06:51:27.292+08:00
     */
    private String updatedAt;

    /**
     * AI处理结果
     *
     * @see com.zyhl.yun.api.outer.enums.chat.PersonalKnowledgeAiStatusEnum
     */
    private Integer aiStatus;

    /**
     * 失败信息，失败时返回
     */
    private String errorMessage;

    /**
     * 在线链接原URL
     */
    private String htmlUrl;

    /**
     * 类型，枚举值file/folder
     *
     * @see com.zyhl.yun.api.outer.enums.chat.PersonalKnowledgeTypeEnum
     */
    private String type;

    /**
     * 分类， 根据文件的后缀名和mime-type对文件进行分了分类
     *
     * @see com.zyhl.yun.api.outer.enums.chat.PersonalKnowledgeCategoryEnum
     */
    private String category;

    /**
     * 大小，单位：字节
     */
    private Long size;

    /**
     * 文件扩展名，一般是后缀名
     * 注：不区分大小写
     */
    private String fileExtension;

    /**
     * 文件内容 hash 值，长度64位，需要根据 contentHashAlgorithm 指定的算法
     */
    private String contentHash;

    /**
     * 文件内容hash算法名（摘要算法名称），当前仅支持sha1或者sha256，不区分大小写
     * 内容hash算法名，当前hash算法是sha1或sha256；
     * 旧底座是md5
     */
    private String contentHashAlgorithm;

    /**
     * 缩略图信息
     */
    private List<OwnerDriveThumbnailInfo> thumbnailUrls;

    public PersonalKnowledgeResource(String resourceId) {
        this.resourceId = resourceId;
    }

    public PersonalKnowledgeResource(File file) {
        if (ObjectUtil.isNull(file)) {
            return;
        }
        this.resourceId = file.getFileId();
        this.resourceType = PersonalKnowledgeResourceTypeEnum.FILE.getCode();
//        this.baseId = ;
        this.name = file.getName();
        this.createdAt = file.getCreatedAt();
        this.updatedAt = file.getUpdatedAt();
//        this.aiStatus = ;
//        this.errorMessage = ;
//        this.htmlUrl = ;
        this.type = file.getType();
        this.category = file.getCategory();
        this.size = ObjectUtil.defaultIfNull(file.getSize(), 0L);
        this.fileExtension = file.getFileExtension();
        this.contentHash = file.getContentHash();
        this.contentHashAlgorithm = file.getContentHashAlgorithm();
    }

    public PersonalKnowledgeResource(OwnerDriveFileVO file) {
        if (ObjectUtil.isNull(file)) {
            return;
        }
        this.resourceId = file.getFileId();
        this.resourceType = PersonalKnowledgeResourceTypeEnum.FILE.getCode();
//        this.baseId = ;
        this.name = file.getName();
        this.createdAt = file.getCreatedAt();
        this.updatedAt = file.getUpdatedAt();
//        this.aiStatus = ;
//        this.errorMessage = ;
//        this.htmlUrl = ;
        this.type = file.getType();
        this.category = file.getCategory();
        this.size = ObjectUtil.defaultIfNull(file.getSize(), 0L);
        this.fileExtension = file.getFileExtension();
        this.contentHash = file.getContentHash();
        this.contentHashAlgorithm = file.getContentHashAlgorithm();
    }

    public PersonalKnowledgeResource(UserKnowledgeFileEntity entity) {
        if (ObjectUtil.isNull(entity)) {
            return;
        }
        this.resourceId = entity.getFileId();
        this.resourceType = entity.getFromResourceType();
        this.baseId = String.valueOf(entity.getBaseId());
        this.name = entity.getFileName();
        this.createdAt = DateUtil.format(entity.getFileCreatedAt(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
        this.updatedAt = DateUtil.format(entity.getFileUpdatedAt(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
        this.aiStatus = FileAiStatusParamEnum.getCodeByAiStatus(entity.getAiStatus());
//        this.errorMessage = ;
        this.type = FileTypeEnum.getYunDiskFileType(entity.getFileType());
        this.category = FileCategoryEnum.getYunDiskCategory(entity.getCategory());
        this.size = ObjectUtil.defaultIfNull(entity.getFileSize(), 0L);
        this.fileExtension = entity.getExtension();
        this.contentHash = entity.getHashValue();
        this.contentHashAlgorithm = entity.getHashName();

        if (KnowledgeResourceTypeEnum.isHtml(entity.getFromResourceType())) {
            UserKnowledgeFileEntity.HtmlInfo htmlInfo = JsonUtils.fromJson(entity.getFromResource(), UserKnowledgeFileEntity.HtmlInfo.class);
            if (Objects.nonNull(htmlInfo)) {
                if (ObjectUtil.isEmpty(this.name)) {
                    this.name = htmlInfo.getTitle();
                }
                this.htmlUrl = htmlInfo.getUrl();
            }
        }
    }


    public PersonalKnowledgeResource(KnowledgeFileEntity entity) {
        if (ObjectUtil.isNull(entity)) {
            return;
        }
        this.resourceId = entity.getFileId();
        this.resourceType = KnowledgeResourceTypeEnum.PERSONAL_FILE.getCode();
        this.baseId = entity.getBaseId();
        this.name = entity.getFileName();
        this.createdAt = DateUtil.format(entity.getCreateTime(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
        this.updatedAt = DateUtil.format(entity.getUpdateTime(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
//        this.aiStatus = ;
//        this.errorMessage = ;
//        this.htmlUrl = ;
        this.type = FileTypeEnum.FILE.getYunDiskFileType();
        this.category = FileCategoryEnum.DOC.getYundiskCategory();
        this.size = ObjectUtil.defaultIfNull(entity.getFileSize(), 0L);
        this.fileExtension = entity.getExtension();
        this.contentHash = entity.getHashValue();
        this.contentHashAlgorithm = entity.getHashName();
    }
}