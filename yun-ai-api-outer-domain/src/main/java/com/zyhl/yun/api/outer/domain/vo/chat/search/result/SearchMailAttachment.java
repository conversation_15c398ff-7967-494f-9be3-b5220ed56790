package com.zyhl.yun.api.outer.domain.vo.chat.search.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 对话信息-搜索结果-邮件附件-列表数据
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchMailAttachment implements Serializable {


    /**
     * 邮件所在文件夹 ID
     */
    private Integer	fid;
    /**
     * 邮件 ID
     */
    private String	mid;
    /**
     * 邮件主题
     */
    private String	subject;
    /**
     * 发件人
     */
    private String	from;
    /**
     * 收件人
     */
    private String	to;
    /**
     * 发信时间，单位秒
     */
    private Integer	sendDate;
    /**
     * 收信时间，单位秒
     */
    private Integer	receiveDate;
    /**
     * 附件名称
     */
    private String	attachName;
    /**
     * 附件大小，单位字节
     */
    private Integer	attachSize;
    /**
     * 附件真实大小，单位字节
     */
    private Integer	attachRealSize;
    /**
     * 附件偏移
     */
    private Integer	attachOffset;
    /**
     * 附件头偏移
     */
    private Integer	attachHeadOffset;
    /**
     * 附件类型
     */
    private Integer	attachType;
    /**
     * 附件编码
     */
    private Integer	encode;

}
