package com.zyhl.yun.api.outer.domain.vo;

import org.apache.commons.lang3.StringUtils;

import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum;
import lombok.Data;

/**
 * 模型配置 VO
 *
 * <AUTHOR>
 */
@Data
public class ModelConfigVO {
    
    /**
     * 模型编码
     */
    private String code;
    
    /**
     * 模型名称
     */
    private String name;
    
    /**
     * 模型输入字数限制
     */
    private Integer lengthLimit;

    /**
     * 排序，最小在前面
     */
    private Integer sort;

    public ModelConfigVO(String name, String code, Integer lengthLimit, Integer sort) {
        this.code = code;
        if(StringUtils.isEmpty(name)) {
        	this.name = TextModelEnum.getByCode(code).getName();
        }else {
        	this.name = name;
        }
        this.lengthLimit = lengthLimit;
        this.sort = sort;
    }
}
