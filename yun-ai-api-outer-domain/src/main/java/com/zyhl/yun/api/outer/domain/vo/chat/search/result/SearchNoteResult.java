package com.zyhl.yun.api.outer.domain.vo.chat.search.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 对话信息-搜索结果-笔记
 * @Author: WeiJingK<PERSON>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchNoteResult extends SearchCommonResult implements Serializable {

    /** 下一页起始资源标识符，最后一页该值为空 */
    private String nextPageCursor;

    /** 记录总数，默认不返回 */
    private Integer totalCount;

    /** 命中关键词的笔记列表 */
    private List<NoteApiSearchResult> noteList;

}
