package com.zyhl.yun.api.outer.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 智能体标签
 *
 * @author: dinghao
 */
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "tabs")
public class TabsProperties {
    /**
     * 智能体标签
     */
    private List<String> tabLabel;

}
