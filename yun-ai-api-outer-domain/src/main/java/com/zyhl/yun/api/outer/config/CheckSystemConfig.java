package com.zyhl.yun.api.outer.config;

import cn.hutool.core.collection.CollUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 对话处理任务结果图片送审配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties("check-system")
public class CheckSystemConfig {

    /**
     * 全量送审
     */
    public static final String AUDIT_TYPE_PARTLOCAL_AND_ALL = "partLocalAndAll";

    /**
     * 全量送审
     */
    public static final String AUDIT_TYPE_ALL = "all";

    /**
     * 批量送审
     */
    public static final String AUDIT_TYPE_PART = "part";

    /**
     * 默认输出size
     */
    private static final Long DEFAULT_OUT_PUT_SIZE = 10L;

    /**
     * 拼接送审之前的size数量
     */
    public static final Long DEFAULT_APPEND_BEFORE_SIZE = 20L;
    /**
     * 默认模型编码
     */
    private static String DEFAULT_CODE = "default";

    /**
     * 文本、图片送审开关，true-需要送审
     */
    private boolean open = true;

    /**
     * 独立配置大模型送审列表（type是送审方式：all=全量送审；part=分批送审，outputSize每次输出字数）
     */
    private List<CheckSystemTextModel> textModels;

	/**
	 * 获取模型配置
	 *
	 * @param modeCode
	 * @return
	 */
	public CheckSystemTextModel getCheckSystemTextModel(String modeCode) {
		if (CollUtil.isEmpty(textModels)) {
			// 默认不送审
			return defaultTextModel();
		}
		CheckSystemTextModel textModel = getCheckSystemTextModel(modeCode, textModels);
		if (null == textModel) {
			textModel = getCheckSystemTextModel(DEFAULT_CODE, textModels);
		}
		if (null != textModel) {
			return textModel;
		}
		// 默认不送审
		return defaultTextModel();
	}

    private CheckSystemTextModel defaultTextModel() {
        // 默认不送审，每次输出10
        return new CheckSystemTextModel(false, DEFAULT_OUT_PUT_SIZE);
    }

	/**
	 * 获取模型配置
	 *
	 * @param modelCode  模型编码
	 * @param textModels 模型配置列表
	 * @return
	 */
	private CheckSystemTextModel getCheckSystemTextModel(String modelCode, List<CheckSystemTextModel> textModels) {
		if (CollUtil.isEmpty(textModels)) {
			// 默认不扣权益
			return null;
		}
		for (CheckSystemTextModel textModel : textModels) {
			if (textModel.getCode().equals(modelCode)) {
				return textModel;
			}
		}
		return null;
	}

	/**
	 * 是否部分送审
	 *
	 * @param type
	 * @return
	 */
	public static boolean isAuditPart(String type) {
		return AUDIT_TYPE_PART.equals(type);
	}

	/**
	 * 是否部分本地送审
	 *
	 * @param type
	 * @return
	 */
	public static boolean isAuditLocalPart(String type) {
		return AUDIT_TYPE_PARTLOCAL_AND_ALL.equals(type);
	}

	/**
	 * 是否全量送审
	 *
	 * @param type
	 * @return
	 */
	public static boolean isAuditAll(String type) {
		return AUDIT_TYPE_ALL.equals(type) || AUDIT_TYPE_PARTLOCAL_AND_ALL.equals(type);
	}

    /**
     * 独立配置大模型送审
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CheckSystemTextModel {

        public CheckSystemTextModel(Boolean open, Long outputSize) {
            this.open = open;
            this.outputSize = outputSize;
        }

        /**
         * 模型编码，default为默认，其他为大模型名称
         */
        private String code;

        /**
         * 是否开关送审
         */
        private Boolean open;

        /**
         * 每次输出字数
         */
        private Long outputSize;
        /**
         * 每次输出字数+appendBeforeSize送审追加截取最后文本的数量
         */
        private Long appendBeforeSize;

        /**
         * type是送审方式：all=全量送审；part=分批送审，partLocalAndAll=增量本地+最后一次全量送审
         */
        private String type;

		/**
		 * 部分送审
		 *
		 * @return true-部分送审
		 */
		public boolean isCheckPart() {
			return CheckSystemConfig.isAuditPart(type) || CheckSystemConfig.isAuditLocalPart(type);
		}
	}

}
