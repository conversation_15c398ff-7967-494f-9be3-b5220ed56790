package com.zyhl.yun.api.outer.domain.vo.chat;

import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 查询对话输出V2（轮巡查结果）-VO
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PollingUpdateV3VO implements Serializable {

    /** 对话信息 */
    private ContentResultVOV2 result;

    /** 对话结果推荐，当对话信息result不为空时返回 */
    private DialogueRecommendVO recommend;

}
