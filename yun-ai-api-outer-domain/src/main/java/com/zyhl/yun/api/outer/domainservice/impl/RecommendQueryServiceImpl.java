package com.zyhl.yun.api.outer.domainservice.impl;

import com.alibaba.fastjson.JSON;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.hcy.yun.ai.common.model.api.client.vo.TextModelBaseVo;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.yun.api.outer.config.RecommendPromptTemplateProperties;
import com.zyhl.yun.api.outer.domain.req.LlmChatReqDTO;
import com.zyhl.yun.api.outer.domain.valueobject.llm.LlmChatMessage;
import com.zyhl.yun.api.outer.domain.vo.QueryRecommendListVO;
import com.zyhl.yun.api.outer.domain.vo.QueryRecommendVO;
import com.zyhl.yun.api.outer.domainservice.CheckSystemDomainService;
import com.zyhl.yun.api.outer.domainservice.RecommendQueryService;
import com.zyhl.yun.api.outer.external.LlmChatExternalService;
import com.zyhl.yun.api.outer.util.JsonHandleUtils;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.RequestContextHolder.UserInfo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

/**
 * 问题推荐方法接口实现类
 *
 * <AUTHOR>
 * @date 2024/7/17 16:08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RecommendQueryServiceImpl implements RecommendQueryService {

    private final LlmChatExternalService chatExternalService;

    private final RecommendPromptTemplateProperties templateProperties;

    private final CheckSystemDomainService checkSystemDomainService;

	@Resource(name = "recommendQueryThreadPool")
	private ExecutorService recommendQueryThreadPool;

	/**
     * 获取问题推荐列表集合VO（并发处理）
     *
     * @param dialogue 对话内容
     * @return 问题推荐列表集合VO
     */
    @Override
    public Future<QueryRecommendListVO> getRecommendQueryFuture(Long dialogueId, String dialogue) {
        // 判断对话内容是否为空
        if (StringUtils.isBlank(dialogue)) {
            return null;
        }

        try {
            // 获取配置信息·
            RecommendPromptTemplateProperties.QueryTemplate queryTemplate = templateProperties.getQueryTemplate();
            // 设置请求参数
            final String userId = RequestContextHolder.getUserId();
    		final UserInfo userInfo = RequestContextHolder.getUserInfo();
            String content = String.format(queryTemplate.getTemplate(), dialogue) + templateProperties.getDefaultAppendPrompt();
            List<LlmChatMessage> messages = Collections.singletonList(new LlmChatMessage(TextModelRoleEnum.USER.getName(), content));
            LlmChatReqDTO req = new LlmChatReqDTO(userId, queryTemplate.getModelCode(), messages);

        	final String tid = String.valueOf(MDC.get(LogConstants.TRACE_ID));
			final String serviceName = String.valueOf(MDC.get(LogConstants.SERVICE));
            // 异步调用大模型
            return CompletableFuture.supplyAsync(() -> {
            	RequestContextHolder.setUserInfo(userInfo);
				LogCommonUtils.initLogMDC(tid, serviceName);
                TextModelBaseVo baseVo = new TextModelBaseVo();
                try {
                    // 调用大模型
                    baseVo = chatExternalService.chatNormal(req);
                    if (!baseVo.isSuccess() || StringUtils.isBlank(baseVo.getText())) {
                        log.info("【获取问题推荐列表】调用大模型返回失败 入参:{} | 返回结果:{}", JsonUtil.toJson(req), JsonUtil.toJson(baseVo));
                        return null;
                    }
                } catch (Exception e) {
                    log.error("【获取问题推荐列表】调用大模型异常 入参:{} | e:", JsonUtil.toJson(req), e);
                    return null;
                } finally {
                    log.info("【获取问题推荐列表】调用大模型入参:{} | 返回结果:{}", JsonUtil.toJson(req), JsonUtil.toJson(baseVo));
                }

                // 大模型输出内容送审
                try {
                    checkSystemDomainService.checkLocalAndPlatformException(dialogueId, userId, baseVo.getText());
                } catch (Exception e) {
                    log.error("【获取问题推荐列表】大模型结果送审异常 内容:{} | e:", baseVo.getText(), e);
                    return null;
                }

                // 转换大模型结果
                List<QueryRecommendVO> queryRecommends = new ArrayList<>();
                try {
                    String resultStr = JsonHandleUtils.formatJsonStr(baseVo.getText());
                    List<String> list = JSON.parseArray(resultStr, String.class);
                    for (String queryObj : list) {
                        queryRecommends.add(new QueryRecommendVO(queryObj));
                    }
                    return new QueryRecommendListVO(queryRecommends);
                } catch (Exception e) {
                    log.error("【获取问题推荐列表】转换大模型结果异常 结果：{} | e:", JsonUtil.toJson(baseVo), e);
                    return null;
                }
            }, recommendQueryThreadPool);

        } catch (Exception e) {
            log.error("【获取问题推荐列表】未知异常 dialogueId:{} | dialogue:{} ｜e:", dialogueId, dialogue, e);
            return null;
        }
    }


}
