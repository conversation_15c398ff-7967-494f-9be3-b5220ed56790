package com.zyhl.yun.api.outer.config.textmodel;

import java.util.ArrayList;
import java.util.List;

import com.zyhl.yun.api.outer.enums.chat.ChatPromptCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 描述：模型提示词配置
 *
 * <AUTHOR> zhumaoxian  2025/2/26 15:29
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "text-model.model-prompt")
@Slf4j
public class ModelPromptProperties {

    /**
     * 提示词配置
     */
    private List<PromptConfig> prompts;

    /**
     * 获取提示词
     *
     * @param promptKeys 提示词key列表，匹配到就立即返回
     * @param modelCode  模型code
     * @return 提示词
     */
    public String getPrompt(List<String> promptKeys, String modelCode) {
        if (CollUtil.isEmpty(promptKeys)) {
            return null;
        }
        String prompt = null;
        for (String promptKey : promptKeys) {
            String result = getPrompt(promptKey, modelCode);
            if (StringUtils.isNotEmpty(result)) {
                prompt = result;
                break;
            }
        }
        boolean successGetPrompt = StringUtils.isNotBlank(prompt);
        log.info("获取模型提示词：modelCode:{}, promptKeys:{}, successGetPrompt:{}, prompt size:{}", modelCode,
                JSONUtil.toJsonStr(promptKeys), successGetPrompt, (null != prompt ? prompt.length() : 0));
        return prompt;
    }

    /**
     * 获取提示词
     *
     * @param promptKey 提示词key
     * @param modelCode 模型code
     * @return 提示词
     */
    public String getPrompt(String promptKey, String modelCode) {
        if (ObjectUtil.isEmpty(prompts) || ObjectUtil.isEmpty(promptKey) || ObjectUtil.isEmpty(modelCode)) {
            return "";
        }

        for (PromptConfig config : prompts) {
            if (config.promptKeys.contains(promptKey) && config.modelCodes.contains(modelCode)) {
                return config.prompt;
            }
        }

        return "";
    }

    /**
     * 提示词配置
     */
    @Data
    public static class PromptConfig {
        /**
         * 提示词key
         */
        private List<String> promptKeys = new ArrayList<>();
        /**
         * 模型code
         */
        private List<String> modelCodes = new ArrayList<>();
        /**
         * 提示词
         */
        private String prompt;
    }
}
