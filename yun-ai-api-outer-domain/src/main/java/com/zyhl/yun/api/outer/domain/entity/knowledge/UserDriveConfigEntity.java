package com.zyhl.yun.api.outer.domain.entity.knowledge;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户独立空间配置
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class UserDriveConfigEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 文件归属，个人云 owner_id= user_id
     */
    private String ownerId;

    /**
     * 业务类型：
     * -1 - 未知类型
     * 1-personal 个人云
     * 2-group 圈子
     * 3-shareGroup 共享群
     * 4-family 家庭云
     * 5-activity 活动空间 照片直播
     * 6-note 笔记
     * 7-cardpackage 卡包
     * 8-system 系统空间
     * 9-partner 合作空间
     * 10 -mount 挂载盘
     *
     * @see com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum
     */
    private Integer ownerType;

    /**
     * paas平台编码
     */
    private String paasCode;

    /**
     * 知识库目录配置：{ "assistant":{  "name":"AI助手",  "path":"AI助手",  "catalogId": "目录id" }, "tools":{  "name":"AI工具",  "path":"AI工具",  "catalogId": "目录id" }, "knowledge":{  "name":"个人知识库",  "path":"个人知识库",  "catalogId": "目录id" }}
     */
    private String parentFileConfig;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

}
