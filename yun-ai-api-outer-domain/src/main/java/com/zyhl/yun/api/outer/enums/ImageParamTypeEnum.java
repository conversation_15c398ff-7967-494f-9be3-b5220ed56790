package com.zyhl.yun.api.outer.enums;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 图片参数类型
 *
 * <AUTHOR>
 */
public enum ImageParamTypeEnum {

    /**
     * 图片本地存储路径
     */
	LOCAL_PATH(0, "图片本地共享存储"),

	/**
     * 图片URL地址
     */
    URL(1, "图片url地址"),

    /**
     * 图片Base64数据，不带data:image/jpeg;base64头
     */
    BASE64(2, "图片base64数据，不带data:image/jpeg;base64头"),

    /**
     * 图片文件fileId
     */
    FILE_ID(3, "图片文件fileId");



    private static final Map<Integer, ImageParamTypeEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(ImageParamTypeEnum.class).forEach(item -> MAP.put(item.code, item));
    }

    public static ImageParamTypeEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return MAP.get(code);
    }

    /**
     * 类型是否存在
     *
     * @return true-类型存在
     */
    public static boolean isExist(Integer code) {
        return getByCode(code) != null;
    }

    public static boolean isUrl(Integer code) {
        return code != null && URL.code == code;
    }

    public static boolean isBase64(Integer code) {
        return code != null && BASE64.code == code;
    }

    public static boolean isFileId(Integer code) {
        return code != null && FILE_ID.code == code;
    }



    private ImageParamTypeEnum(int code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    /**
     * 类型
     */
    private final int code;
    /**
     * 备注
     */
    private final String remark;

    public int getCode() {
        return code;
    }

    public String getRemark() {
        return remark;
    }
}
