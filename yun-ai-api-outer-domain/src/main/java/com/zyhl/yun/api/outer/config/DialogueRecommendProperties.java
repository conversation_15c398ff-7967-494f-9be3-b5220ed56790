package com.zyhl.yun.api.outer.config;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import com.zyhl.hcy.yun.ai.common.base.utils.NumberUtil;
import com.zyhl.yun.api.outer.domain.vo.IntentionRecommendVO;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;

/**
 * 对话结果推荐配置
 *
 * <AUTHOR>
 * @date 2024/6/3 01:29
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "dialogue-result-recommend")
public class DialogueRecommendProperties {

	/**
	 * 意图推荐AI工具配置列表
	 */
	private List<Intention> intentionList;

	/**
	 * 意图推荐文本配置列表
	 */
	private List<Intention> intentionTextList;

	/**
	 * AI工具推荐-推荐的数量，默认3
	 */
	private Integer aitoolRecommendQuantity = 3;
	/**
	 * 对话内容推荐-每个类型推荐的数量
	 */
	private Integer contentRecommendQuantity;

	/**
	 * 对话内容推荐-keyword分隔符
	 */
	private String contentRecommendSplit;

	/**
	 * 对话内容推荐配置列表
	 */
	private List<ContentRecommend> contentList;

	/**
	 * 文本工具推荐
	 */
	private RecommendCommonTool textTool;

	/**
	 * 图片工具推荐
	 */
	private RecommendCommonTool imageTool;

	/**
	 * 快速阅读引流工具推荐
	 */
	private RecommendCommonTool middleSpeedReadTool;

	/**
	 * 可执行的文本工具推荐
	 */
	private RecommendCommonTool executeTextTool;

	/**
	 * 意图推荐
	 */
	@Data
	public static class Intention {

		/**
		 * 意图指令
		 */
		private String intentionCommand;

		/**
		 * 引导文案
		 */
		private String copy;

		/**
		 * 跳转URL，AI消除和智能抠图意图时返回
		 */
		private String linkURL;

	}

	/**
	 * 对话内容推荐配置
	 */
	@Data
	public static class ContentRecommend {

		/**
		 * 内容推荐类型 1--工具类运营推荐 2--发现类运营推荐 3--推荐圈子类运营推荐 4--活动类运营推荐 5--通用类运营推荐 6--其他运营推荐
		 */
		private Integer type;

		/**
		 * 内容推荐类型【描述】 1--工具类运营推荐 2--发现类运营推荐 3--推荐圈子类运营推荐 4--活动类运营推荐 5--通用类运营推荐 6--其他运营推荐
		 */
		private String desc;

		/**
		 * 内容推荐关键字列表
		 */
		private List<Keyword> keywordList;

	}

	/**
	 * 内容推荐关键字
	 */
	@Data
	public static class Keyword {

		/**
		 * 关键字
		 */
		private String keyword;

		/**
		 * 优先级（从小到大，越小越优先）
		 */
		private Integer priority;
	}

	/**
	 * 文本工具推荐
	 */
	@Data
	public static class RecommendCommonTool {

		/**
		 * 随机数量
		 */
		private Integer randomNum;

		/**
		 * 推荐文件后缀列表
		 */
		private List<String> recomendFileExtList;

		/**
		 * 推荐工具列表
		 */
		private List<RecommendTool> toolList;
	}

	/**
	 * 图片工具推荐
	 */
	@Data
	public static class RecommendImageTool {

		/**
		 * 随机数量
		 */
		private Integer randomNum;

		/**
		 * 推荐工具列表
		 */
		private List<RecommendTool> toolList;
	}

	/**
	 * 文本工具推荐
	 */
	@Data
	public static class RecommendTool {
		/**
		 * 
		 * 意图指令，枚举值参考IntentionCommands
		 * 
		 */

		private String command;

		/**
		 * 
		 * 子意图，目前与036工具调用的子意图编码一致
		 * 
		 */

		private String subCommand;

		/**
		 * 
		 * 工具名称
		 * 
		 */

		private String toolName;

		/**
		 * 
		 * 使用方式
		 * 
		 * 1--返回提示词，通过与大模型结合使用；
		 * 
		 * 2--跳转到对应的工具页面
		 * 
		 */

		private int useType;

		/**
		 * 
		 * 提示词
		 * 
		 */

		private String prompt;

		/**
		 * 
		 * 跳转URL
		 * 
		 */

		private String linkURL;

		/**
		 * 
		 * 提示文案
		 * 
		 */

		private String promptCopy;

		/**
		 * 
		 * 按钮文案
		 * 
		 */

		private String buttonCopy;
	}

	/**
	 * 随机生成N个AI工具推荐（N=aitoolRecommendQuantity）
	 * 
	 * @return
	 */
	public List<IntentionRecommendVO> randomAiToolRecommends() {
		List<IntentionRecommendVO> thisIntentionList = new ArrayList<>();
		List<Intention> aitoolList = this.getIntentionList();
		Integer recommendQuantity = this.getAitoolRecommendQuantity();
		if (CollUtil.isNotEmpty(aitoolList) && null != recommendQuantity) {
			if (aitoolList.size() > recommendQuantity.intValue()) {
				// 随机生成N个索引
				List<Integer> nums = NumberUtil.generateUniqueNumbers(0, aitoolList.size() - 1,
						recommendQuantity.intValue());
				for (Integer num : nums) {
					thisIntentionList
							.add(DialogueRecommendProperties.genIntentionRecommendVO(aitoolList.get(num.intValue())));
				}
			} else {
				for (Intention aitool : aitoolList) {
					thisIntentionList.add(DialogueRecommendProperties.genIntentionRecommendVO(aitool));
				}
			}
		}
		return thisIntentionList;
	}

	/**
	 * 转换IntentionRecommendVO
	 * 
	 * @param intention
	 * @return
	 */
	private static IntentionRecommendVO genIntentionRecommendVO(Intention intention) {
		return new IntentionRecommendVO(intention.getIntentionCommand(), intention.getCopy(), intention.getLinkURL());
	}

	/**
	 * 文本工具推荐列表
	 * 
	 * @return
	 */
	public List<RecommendTool> getTextToolRecommendList() {
		return getCommonRecommendList(this.getTextTool());
	}

	/**
	 * 文本工具推荐列表
	 * 
	 * @return
	 */
	public List<RecommendTool> getTextToolRecommendList(String filterPrompt) {
		return getCommonRecommendList(this.getTextTool(), filterPrompt);
	}

	/**
	 * 图片工具推荐列表
	 * 
	 * @return
	 */
	public List<RecommendTool> getImageToolRecommendList() {
		return getCommonRecommendList(this.getImageTool());
	}

	/**
	 * 快速阅读中部引流工具推荐列表
	 * 
	 * @return
	 */
	public List<RecommendTool> getSpeedReadMiddleRecommendList() {
		return getCommonRecommendList(this.getMiddleSpeedReadTool());
	}

	/**
	 * 可执行的文本工具推荐列表
	 * 
	 * @return
	 */
	public List<RecommendTool> getExecuteTextToolList() {
		return getCommonRecommendList(this.getExecuteTextTool());
	}

	/**
	 * 可执行的文本工具推荐(ai ppt)
	 * 
	 * @return
	 */
	public RecommendTool getExecuteAiPptTool() {
		if (null != this.getExecuteTextTool()) {
			List<RecommendTool> list = this.getExecuteTextTool().getToolList();
			if (CollUtil.isNotEmpty(list)) {
				for (RecommendTool bean : list) {
					if (DialogueIntentionEnum.isTextToolIntention(bean.getCommand())
							&& DialogueIntentionSubEnum.isAiPpt(bean.getSubCommand())) {
						return bean;
					}
				}
			}
		}
		return null;
	}

	private List<RecommendTool> getCommonRecommendList(RecommendCommonTool thisRecommendTextTool) {
		return getCommonRecommendList(thisRecommendTextTool, null);
	}

	private List<RecommendTool> getCommonRecommendList(RecommendCommonTool thisRecommendTextTool, String filterPrompt) {
		if (null != thisRecommendTextTool && null != thisRecommendTextTool.getRandomNum()
				&& CollUtil.isNotEmpty(thisRecommendTextTool.getToolList())) {
			Integer randomNum = thisRecommendTextTool.getRandomNum();
			if (randomNum == 0) {
				return null;
			}
			List<RecommendTool> tools = thisRecommendTextTool.getToolList();
			List<RecommendTool> toolsFilter = new ArrayList<>();
			if (CollUtil.isNotEmpty(tools) && StringUtils.isNotEmpty(filterPrompt)) {
				for (RecommendTool tool : tools) {
					if (filterPrompt.equals(tool.getPrompt())) {
						continue;
					}
					toolsFilter.add(tool);
				}
			} else {
				toolsFilter.addAll(tools);
			}
			if (randomNum > toolsFilter.size()) {
				return toolsFilter;
			}
			List<RecommendTool> newTools = new ArrayList<>();
			List<Integer> listNum = NumberUtil.generateUniqueNumbers(0, toolsFilter.size() - 1, randomNum);
			listNum = listNum.stream().sorted().collect(Collectors.toList());
			for (Integer idx : listNum) {
				newTools.add(toolsFilter.get(idx));
			}
			return newTools;
		}
		return null;
	}

	/**
	 * 获取文本工具推荐后缀列表
	 * 
	 * @return
	 */
	public List<String> getTextToolRecomendFileExtList() {
		RecommendCommonTool thisTextTool = this.getTextTool();
		if (null != thisTextTool && CollUtil.isNotEmpty(thisTextTool.getRecomendFileExtList())) {
			return thisTextTool.getRecomendFileExtList();
		}
		return Collections.emptyList();
	}

}
