package com.zyhl.yun.api.outer.domain.resp;

import java.util.List;

import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueIntentionOutput;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchParam;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.AiTextResultVersionEnum;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import com.zyhl.yun.api.outer.vo.MultiSearchVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * hbase的ai_text_result表，respParameters字段数据对象
 *
 * <AUTHOR>
 * @date 2024/4/18 20:22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiTextResultRespParameters {

    /**
     * 版本号
     *
     * @see AiTextResultVersionEnum
     */
    private String version;

    /**
     * code
     * 成功为0000
     *
     * @see ResultCodeEnum
     */
    private String resultCode;

    /**
     * code描述
     */
    private String resultMsg;

    /** 大模型联网搜索结果 */
    private List<NetworkSearchInfo> networkSearchInfoList;

    /** 思维链过程 */
    private String reasoningContent;

    /**
     * 文生文-流式同步结果
     */
    private String data;

    /**
     * 搜索意图-搜索参数
     */
    private SearchParam param;

    /**
     * 引导文案
     */
    private LeadCopyVO leadCopy;

    /**
     * 返回结果的标题
     */
    private String title;

    /**
     * 结束语
     */
    private String ending;

    /**
     * 回答声明
     */
    private String responseStatement;

    /**
     * 个人知识库参考文件，可选，仅第一次流式结果返回
     */
    private List<File> personalKnowledgeFileList;

    /**
     * 对话结果-邮件信息VO
     */
    private MailInfoVO mailInfo;

    /**
     * 意图结果
     */
    private List<DialogueIntentionVO.IntentionInfo> intentionInfoList;

    /**
     * 流式结果
     */
    private List<DialogueFlowResult> outputList;

    /**
     * 输出意图指令
     */
    private DialogueIntentionOutput outputCommand;

    /**
     * 意图结果对象
     */
    private DialogueIntentionVO intentionVO;


    /**
     * AI全网搜结果
     */
    private List<MultiSearchVO> aiInternetSearchResultList;

    /**
     * 扩展信息参数
     */
    private ExtInfoParam extInfoParam;
    
    /**
     * 大模型联网搜索结果
     * @Author: WeiJingKun
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class NetworkSearchInfo {

        /** 编号 */
        private Integer index;

        /** 网页主题 */
        private String title;

        /** 网页url */
        private String url;

        /** 来源网站 */
        private String siteName;

        /** 来源网站图标 */
        private String icon;

        /** HTML格式的网页内容字符串 */
        private String htmlContent;

    }
    
    /**
     * 扩展参数
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExtInfoParam {
    	/**
    	 * 对话流式结果信息
    	 */
    	private DialogueFlowResult dialogueFlowResult;
    }

    public AiTextResultRespParameters(AiResultCode result) {
        this.resultCode = result.getCode();
        this.resultMsg = result.getMsg();
    }

    public AiTextResultRespParameters(String resultCode, String resultMsg) {
        this.resultCode = resultCode;
        this.resultMsg = resultMsg;
    }

    public AiTextResultRespParameters(ResultCodeEnum result) {
        this.resultCode = result.getResultCode();
        this.resultMsg = result.getResultMsg();
    }

    public AiTextResultRespParameters(ResultCodeEnum result, List<NetworkSearchInfo> networkSearchInfoList, String reasoningContent, String data) {
        this.resultCode = result.getResultCode();
        this.resultMsg = result.getResultMsg();
        this.networkSearchInfoList = networkSearchInfoList;
        this.reasoningContent = reasoningContent;
        this.data = data;
    }

    public AiTextResultRespParameters(ResultCodeEnum result, String data, String title, List<File> personalKnowledgeFileList) {
        this.resultCode = result.getResultCode();
        this.resultMsg = result.getResultMsg();
        this.data = data;
        this.title = title;
        this.personalKnowledgeFileList = personalKnowledgeFileList;
    }

    public AiTextResultRespParameters setResult(ResultCodeEnum result) {
        this.resultCode = result.getResultCode();
        this.resultMsg = result.getResultMsg();
        return this;
    }

	public void setOutputCommandVO(DialogueIntentionVO intentionVO) {
    	IntentionInfo mainIntentionVO = DialogueIntentionVO.getMainIntention(intentionVO);
    	if(null != mainIntentionVO) {
    		this.outputCommand = new DialogueIntentionOutput(mainIntentionVO);
    	}
    }

}
