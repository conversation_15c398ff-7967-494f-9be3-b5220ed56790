package com.zyhl.yun.api.outer.domain.vo.chat.search.param;

import com.zyhl.yun.api.outer.enums.chat.search.SearchDiscoveryParamSearchTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;

/**
 * 对话信息-搜索参数-发现广场
 *
 * @Author: WeiJingKun
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchDiscoveryParam extends SearchCommonParam implements Serializable {

    /** 搜索关键字 */
    private String keyword;

    /** 搜索关键字列表 */
    private List<String> keywords;

    /** 内容类型(1-全部，3-文档，6-视频，10-试卷，11-影视，12-其他，13-图书，15-短剧)
     * @see com.zyhl.yun.api.outer.enums.chat.search.SearchDiscoveryParamQueryTypeEnum
     */
    private List<Integer> queryTypes;

    /**
     * 搜索类型（1-普通搜索，2-扩词搜索）
     * 默认1-普通搜索
     * @see com.zyhl.yun.api.outer.enums.chat.search.SearchDiscoveryParamSearchTypeEnum
     */
    @Builder.Default
    private int searchType = SearchDiscoveryParamSearchTypeEnum.REGULAR_SEARCH.getCode();

    /** 分页信息（默认查总数） */
    @Builder.Default
    private @Valid FilePageInfo pageInfo = new FilePageInfo(10, null, null, 1);

}
