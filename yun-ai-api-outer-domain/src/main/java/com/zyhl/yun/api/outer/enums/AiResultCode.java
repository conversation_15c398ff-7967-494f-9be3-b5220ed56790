package com.zyhl.yun.api.outer.enums;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/26 13:54
 */
@Getter
public enum AiResultCode {
    /**
     * 参数错误
     */
    CODE_01000001("01000001", "参数错误", CollUtil.newHashSet("1000001", "1111111", "100018", "100024", "100025", "140034", "140035", "170012", "100015", "100012", "01000001", "50200", "50201", "50204", "50206", "50209", "100004", "100005", "1111111")),

    /**
     * 令牌失效
     */
    CODE_01000003("01000003", "令牌失效", CollUtil.newHashSet("100001", "100016", "FA_INVALID_SESSION")),

    /**
     * 权限不足
     */
    CODE_01000004("01000004", "权限不足", CollUtil.newHashSet("100006", "1000004", "01000004")),

    /**
     * 资源不存在
     */
    CODE_01000005("01000005", "资源不存在", CollUtil.newHashSet("01000005")),

    /**
     * 请求消息体过大
     */
    CODE_01000006("01000006", "请求消息体过大", CollUtil.newHashSet("100022")),

    /**
     * 请求过多
     */
    CODE_01000007("01000007", "请求过多", CollUtil.newHashSet("100002", "01000007")),

    /**
     * 系统调用异常
     */
    CODE_01000010("01000010", "系统调用异常", CollUtil.newHashSet("100000", "1000010", "01000010")),

    /**
     * 服务调用超时
     */
    CODE_01000011("01000011", "服务调用超时", CollUtil.newHashSet("100013")),

    /**
     * 请求的IP不在白名单范围内
     */
    CODE_01000012("01000012", "请求的IP不在白名单范围内", CollUtil.newHashSet("100007", "200000401")),

    /**
     * 系统服务不可用
     */
    CODE_01000102("01000102", "系统服务不可用", CollUtil.newHashSet("100020", "170011")),

    /**
     * fileId信息错误，无法下载对应文件
     */
    CODE_10000003("10000003", "fileId信息错误，无法下载对应文件", CollUtil.newHashSet("10000003")),

    /**
     * 文件内容不符合规范
     */
    CODE_10000004("10000004", "文件内容不符合规范", CollUtil.newHashSet("1000006", "01000006", "100008", "100009", "100010", "101", "102", "104")),

    /**
     * 内容可能涉及敏感信息，建议更换其他内容再试
     */
    CODE_10000005("10000005", "内容可能涉及敏感信息，建议更换其他内容再试", CollUtil.newHashSet("10000005", "10030319")),

    /**
     * 文件或图片上传过大
     */
    CODE_10000006("10000006", "文件或图片上传过大", CollUtil.newHashSet("qwen_long_file_size_limit")),

    /**
     * （下游）服务器响应异常
     */
    CODE_10000101("10000101", "（下游）服务器响应异常", CollUtil.newHashSet("9999999", "1000005", "04000014", "10010108")),

    /**
     * (下游)服务器处理失败
     */
    CODE_10000102("10000102", "(下游)服务器处理失败", CollUtil.newHashSet("10000102")),

    /**
     * （下游）算法处理失败
     */
    CODE_10000103("10000103", "（下游）算法处理失败", CollUtil.newHashSet("10010501", "100027", "200001", "200004", "200005", "3333333", "100011", "100019", "100021")),

    /**
     * 中间件响应异常
     */
    CODE_10000111("10000111", "中间件响应异常", CollUtil.newHashSet("100014")),

    /**
     * 您的云盘空间不足，生成结果保存失败，请清理空间后重试
     */
    CODE_10022004("10022004", "您的云盘空间不足，生成结果保存失败，请清理空间后重试", CollUtil.newHashSet("4510004", "9424", "04000012")),

    /**
     * 当前使用人数较多，请稍后重试
     */
    CODE_10022006("10022006", "当前使用人数较多，请稍后重试", CollUtil.newHashSet("2022", "90024")),

    /**
     * 请求过于频繁，请稍后重试
     */
    CODE_10022007("10022007", "请求过于频繁，请稍后重试", CollUtil.newHashSet("2021", "29904", "FailedOperation.RequestLimitExceeded", "RequestLimitExceeded.JobNumExceed")),

    /**
     * 没有检测到完整的人脸信息，请重新选择图片
     */
    CODE_10022008("10022008", "没有检测到完整的人脸信息，请重新选择图片", CollUtil.newHashSet("10010411", "2029", "20003", "20007", "20010", "20020", "20021", "20022", "20024", "21003", "21004", "21005", "21006", "21010", "21012", "23002", "50501", "60102", "60103", "60802", "60803", "FailedOperation.Body FeatureFail", "FailedOperation.BodyFeatureFail", "FailedOperation.Body JointsFail", "FailedOperation.BodyJointsFail", "FailedOperation.ImageBodyDetectFailed", "FailedOperation.ImageFacedetectFailed", "FailedOperation.NoBodyInPhoto", "FailedOperation.DetectNoFace", "InvalidParameter.NoFaceInPhoto", "InvalidParameterValue.NoFaceInPhoto")),

    /**
     * 图片太大建议选择10M以下的图片进行处理
     */
    CODE_10022009("10022009", "图片太大建议选择10M以下的图片进行处理", CollUtil.newHashSet("2006", "2031", "50213", "FailedOperation.ImageSizeExceed", "FailedOperation.RequestEntity TooLarge")),

    /**
     * 当前选择的图片分辨率过大，请重新选择图片
     */
    CODE_10022010("10022010", "当前选择的图片分辨率过大，请重新选择图片", CollUtil.newHashSet("2005", "20011", "20012", "20013", "50205", "FailedOperation.ImageResolutionExceed", "FailedOperation.RequestEntityTooLarge")),

    /**
     * 当前选择的图片分辨率过小，请重新选择图片
     */
    CODE_10022011("10022011", "当前选择的图片分辨率过小，请重新选择图片", CollUtil.newHashSet("FailedOperation.ImageResolutionInsufficient", "FailedOperation.ImageResolutionTooSmall", "FailedOperation.FaceSizeTooSmall")),

    /**
     * 内容可能涉及敏感信息，建议更换其他内容再试
     */
    CODE_10022012("10022012", "内容可能涉及敏感信息，建议更换其他内容再试", CollUtil.newHashSet("103", "1101", "2019",
            "2020", "2032", "2033", "2034", "2035", "2036", "2037", "2038", "2039", "2040", "2041", "2042", "2043", "2044", "2045", "2046", "2047", "2048", "2049", "2050", "2051", "2052", "2053", "2054", "2055", "2056", "2057", "2058", "2059", "2060", "2061", "2062", "2063", "2064", "2065", "2066", "2067", "2068", "2069", "2070", "2071", "10025", "10026", "10027", "11002", "60208", "60108", "50511", "50411", "Operation Denied. TextlllegalDetected", "Operation Denied.ImagelllegalDetected", "OperationDenied.TextIllegalDetected", "OperationDenied.ImageIllegalDetected", "320", "350", "397", "qwen_long_file_content_sensitive", "FailedOperation.GenerateImageFailed", "InvalidParameterValue.ImageInvalid", "50411", "50511", "50412", "50512", "50413", "315")),

    /**
     * 当前图片无法识别处理，请重新选择图片
     */
    CODE_10022014("10022014", "当前图片无法识别处理，请重新选择图片", CollUtil.newHashSet("999", "1103", "10007", "10008", "10009", "20008", "20014", "20015", "20023", "20029", "21013", "60101", "60106", "10022003", "10022013", "FailedOperation.ImageDecodeFailed", "FailedOperation.ImageNotForeground", "10010565")),

    /**
     * 当前图片格式不支持处理，请重新选择图片
     */
    CODE_10022015("10022015", "当前图片格式不支持处理，请重新选择图片", CollUtil.newHashSet("FailedOperation.ImageNotSupported", "InvalidParameterValue.UrlIllegal", "InvalidParameterValue.LutImageInvalid", "InvalidParameterValue.LutImageSizeInvalid", "InvalidParameter.InvalidParameter", "Image Decode Error: image format unsupported", "50207", "InvalidParameterValue", "23101")),

    /**
     * 图片中人物过多，建议选择单人照片进行处理
     */
    CODE_10022016("10022016", "图片中人物过多，建议选择单人照片进行处理", CollUtil.newHashSet("10010412", "1102", "20004", "FailedOperation.ProfileNumExceed")),

    /**
     * 没有检测到目标内容
     */
    CODE_10022018("10022018", "没有检测到目标内容", CollUtil.newHashSet("20032", "20033", "21002", "10010501")),

    /**
     * 输入的文本太长啦，请重新输入
     */
    CODE_10022019("10022019", "输入的文本太长啦，请重新输入", CollUtil.newHashSet("2017", "InvalidParameterValue.TextLengthExceed")),

    /**
     * 建议选择小于5M的图片进行处理
     */
    CODE_10022021("10022021", "建议选择小于5M的图片进行处理", CollUtil.newHashSet("10022021")),

    /**
     * 文件(目录)已经不存在
     */
    CODE_10022023("10022023", "文件(目录)已经不存在", CollUtil.newHashSet("10020021", "FS_UNKNOWN", "9149", "04010300")),

    /**
     * 请提供搜索关键词
     */
    CODE_10022024("10022024", "请提供搜索关键词", CollUtil.newHashSet("10022024")),

    /**
     * 文本输入内容已超最大字符限制
     */
    CODE_10030301("10030301", "文本输入内容已超最大字符限制", CollUtil.newHashSet("10030301")),

    /**
     * 文件内容过多
     */
    CODE_10000015("10000015", "文件内容过多", CollUtil.newHashSet("qwen_long_file_token_limit")),

    /**
     * 文件解析失败
     */
    CODE_10000016("10000016", "文件解析失败", CollUtil.newHashSet("10000016", "qwen_long_file_parse_failed")),

    /**
     * 文件上传失败
     */
    CODE_10000017("10000017", "文件上传失败", CollUtil.newHashSet("10000017")),

    /**
     * 文件数量过多
     */
    CODE_10000018("10000018", "文件数量过多", CollUtil.newHashSet("10000018")),

    /**
     * 文件格式不支持
     */
    CODE_10000019("10000019", "文件格式不支持", CollUtil.newHashSet("10000019", "qwen_long_file_format_not_supported", "10090012")),

    /**
     * 文件内容为空
     */
    CODE_10000020("10000020", "文件内容为空", CollUtil.newHashSet("10000020", "qwen_long_file_content_blank")),

    /**
     * 文本内容为空
     */
    CODE_10000021("10000021", "文本内容为空", CollUtil.newHashSet("10000021")),

    /**
     * 任务处理中
     */
    CODE_10000201("10000201", "任务处理中", CollUtil.newHashSet("10000201")),

    /**
     * 任务失败
     */
    CODE_10000202("10000202", "任务失败", CollUtil.newHashSet("10000202")),

    /**
     * 任务扣费失败
     */
    CODE_10000203("10000203", "任务已扣费", CollUtil.newHashSet("10000203")),

    /**
     * 任务扣费失败
     */
    CODE_10000204("10000204", "任务扣费失败", CollUtil.newHashSet("10000204")),

    /**
     * 任务结果已过期
     */
    CODE_10000205("10000205", "任务结果已过期", CollUtil.newHashSet("10000205")),

    //通用错误(00)

    /**
     * 该权益可消费数量不足
     */
    CODE_10000007("10000007", "该权益可消费数量不足", CollUtil.newHashSet("10000007")),

    /**
     * 该权益可消费数量不足
     */
    CODE_10000013("10000013", "该权益可消费数量不足", CollUtil.newHashSet("10000013")),

    /**
     * 该渠道未配置权益
     */
    CODE_10000014("10000014", "该渠道未配置权益", CollUtil.newHashSet("10000014")),

    /**
     * AI模型异常
     */
    CODE_10030330("10030330", "图片读取失败，请重新选择图片", CollUtil.newHashSet("10030330")),

    /**
     * 发邮件错误码
     */
    CODE_10030331("10030331", "收件人不能为空", CollUtil.newHashSet("10030331", "PML4010100041")),
    CODE_10030332("10030332", "收件人传参为空", CollUtil.newHashSet("10030332", "PML4010100042")),
    CODE_10030333("10030333", "收件人存在多个邮件地址", CollUtil.newHashSet("10030333", "PML4010100043")),
    CODE_10030334("10030334", "收件人超过单次发送邮件人数上限", CollUtil.newHashSet("10030334", "PML4010100044")),
    CODE_10030335("10030335", "邮件主题传参为空", CollUtil.newHashSet("10030335", "PML4010100045")),
    CODE_10030336("10030336", "邮件内容传参为空", CollUtil.newHashSet("10030336", "PML4010100046")),
    /**
     * 收件人不存在，（参数中的收件人，存在查询不到收件人邮箱地址）
     */
    CODE_10030337("10030337", "收件人不存在", CollUtil.newHashSet("10030337", "PML4010100047")),
    
    /**
     * AI模型异常
     */
    CODE_10030340("10030340", "AI模型异常", CollUtil.newHashSet("10030340")),

    /**
     * 未知错误
     */
    CODE_9999("9999", "未知错误", CollUtil.newHashSet("9999", "105"));

    /**
     * 错误码
     */
    private final String code;
    /**
     * 错误消息
     */
    private final String msg;
    /**
     * 错误码集合
     */
    private final Set<String> resultCodes;

    AiResultCode(String code, String msg, Set<String> resultCodes) {
        this.code = code;
        this.msg = msg;
        this.resultCodes = resultCodes;
    }

    /**
     * 根据错误码和错误消息，获取通用错误码Enum 获取不到，返回CODE_9999（未知错误）
     *
     * @Author: WeiJingKun
     */
    public static AiResultCode getByCodeOrMsg(String resultCode, String resultMsg) {
        // 通过错误码判断 */
        if (CharSequenceUtil.isNotBlank(resultCode)) {
            for (AiResultCode value : values()) {
                // 先判断code
                if (value.code.equals(resultCode)) {
                    return value;
                }
                // 再判断resultCodes
                if (value.resultCodes.contains(resultCode)) {
                    return value;
                }
            }
        }
        // 通过错误消息判断 */
        if (CharSequenceUtil.isNotBlank(resultMsg)) {
            AiResultCode errorEnum = BaseErrorMsgEnum.getByMsg(resultMsg);
            if (errorEnum != null) {
                return errorEnum;
            }
        }
        return CODE_9999;
    }
    
    /**
	 * 发邮件错误码编码
	 * 
	 * @param code 编码
	 * @return 是否结果
	 */
	public static boolean isSendMailErrorCode(String code) {
		List<AiResultCode> codes = new ArrayList<>();
		codes.add(AiResultCode.CODE_10030331);
		codes.add(AiResultCode.CODE_10030332);
		codes.add(AiResultCode.CODE_10030333);
		codes.add(AiResultCode.CODE_10030334);
		codes.add(AiResultCode.CODE_10030335);
		codes.add(AiResultCode.CODE_10030336);
		codes.add(AiResultCode.CODE_10030337);
		for (AiResultCode codeBean : codes) {
			if (codeBean.getResultCodes().contains(code)) {
				return true;
			}
		}
		return false;
	}

}