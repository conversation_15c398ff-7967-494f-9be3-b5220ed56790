package com.zyhl.yun.api.outer.domainservice.impl;

import java.util.List;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.resp.SensitiveWordResponse;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.dto.CheckTextReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.vo.CheckResultVO;
import com.zyhl.yun.api.outer.config.CheckSensitiveConfig;
import com.zyhl.yun.api.outer.config.CheckSystemConfig;
import com.zyhl.yun.api.outer.config.CheckSystemConfig.CheckSystemTextModel;
import com.zyhl.yun.api.outer.config.WhiteListProperties;
import com.zyhl.yun.api.outer.domainservice.CheckSystemDomainService;
import com.zyhl.yun.api.outer.enums.CheckTypeEnum;
import com.zyhl.yun.api.outer.external.CheckSystemExternalService;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 送审服务接口
 *
 * <AUTHOR>
 * @date 2025/2/13 18:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CheckSystemDomainServiceImpl implements CheckSystemDomainService {

	@Resource
	private CheckSensitiveConfig checkSensitiveConfig;
	@Resource
	private WhiteListProperties whiteListProperties;

	private final CheckSystemExternalService checkSystemExternalService;

	@Override
	public CheckResultVO checkSystemCheckText(CheckSystemTextModel checkSystemTextModel, CheckTypeEnum checkType,
			CheckTextReqDTO checkTextReqDTO) {
		// 白名单用户送审直接通过
		if (checkAuditWhiteUser(String.valueOf(checkTextReqDTO.getTaskId()))) {
			return CheckResultVO.builder().success(true).taskId(checkTextReqDTO.getTaskId()).build();
		}
		if (CheckTypeEnum.LOCAL.equals(checkType)) {
			return localCheckText(checkSystemTextModel, checkTextReqDTO);
		} else if (CheckTypeEnum.API.equals(checkType)) {
			return checkSystemExternalService.checkText(checkSystemTextModel.getOpen(), checkTextReqDTO);
		}
		return CheckResultVO.builder().build();
	}

	@Override
	public CheckResultVO checkLocalAndPlatform(Long dialogueId, String userId, String text) {
		if (checkAuditWhiteUser(String.valueOf(dialogueId))) {
			return CheckResultVO.builder().success(true).taskId(String.valueOf(dialogueId)).build();
		}
		if (ObjectUtil.isEmpty(text) || ObjectUtil.isEmpty(text.trim())) {
			log.info("【文本送审】送审内容为空，对话id：{}", dialogueId);
			return CheckResultVO.builder().success(true).taskId(String.valueOf(dialogueId)).build();
		}
		// 本地送审
		List<String> localWords = checkSensitiveConfig.getLocalWords();
		if (ObjectUtil.isNotEmpty(localWords)) {
			String checkText = text.toLowerCase();
			for (String localWord : localWords) {
				if (checkText.contains(localWord.toLowerCase())) {
					log.info("【文本送审】【本地送审】本地送审涉敏，对话id：{}，命中敏感词：{}", dialogueId, localWord);
					return CheckResultVO.builder().success(false).taskId(String.valueOf(dialogueId)).build();
				}
			}
		}

		// 平台送审
		return checkSystemExternalService.checkText(dialogueId, userId, text);
	}

	@Override
	public CheckResultVO checkLocalAndPlatformException(Long dialogueId, String userId, String text) {
		CheckResultVO checkResultVO = checkLocalAndPlatform(dialogueId, userId, text);
		if (!Boolean.TRUE.equals(checkResultVO.getSuccess())) {
			throw new YunAiBusinessException(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
		}

		return checkResultVO;
	}

	@Override
	public CheckResultVO checkPlatformAndLocal(Long dialogueId, String userId, String text) {
		if (checkAuditWhiteUser(String.valueOf(dialogueId))) {
			return CheckResultVO.builder().success(true).taskId(String.valueOf(dialogueId)).build();
		}
		if (ObjectUtil.isEmpty(text) || ObjectUtil.isEmpty(text.trim())) {
			log.info("【文本送审】送审内容为空，对话id：{}", dialogueId);
			return CheckResultVO.builder().success(true).taskId(String.valueOf(dialogueId)).build();
		}
		try{
			// 平台送审
			return checkSystemExternalService.checkText(dialogueId, userId, text);
		}catch(Exception e){
			log.error("【文本送审】平台送审异常，对话id：{}", dialogueId, e);
			if (e instanceof YunAiBusinessException) {
				YunAiBusinessException exception = (YunAiBusinessException) e;
				if (ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultCode().equals(exception.getCode())) {
					// 送审平台不通过
					SensitiveWordResponse detail = new SensitiveWordResponse();
					detail.setDetail(ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultMsg());
					return CheckResultVO.builder().success(false).detail(detail).taskId(String.valueOf(dialogueId)).build();
				}
			}
			// 本地送审
			List<String> localWords = checkSensitiveConfig.getLocalWords();
			if (ObjectUtil.isNotEmpty(localWords)) {
				String checkText = text.toLowerCase();
				for (String localWord : localWords) {
					if (checkText.contains(localWord.toLowerCase())) {
						log.info("【文本送审】【本地送审】本地送审涉敏，对话id：{}，命中敏感词：{}", dialogueId, localWord);
						SensitiveWordResponse detail = new SensitiveWordResponse();
						detail.setDetail(ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultMsg());
						return CheckResultVO.builder().success(false).detail(detail).taskId(String.valueOf(dialogueId)).build();
					}
				}
			}
		}
		return CheckResultVO.builder().success(true).taskId(String.valueOf(dialogueId)).build();
	}

	/**
	 * 本地送审涉敏
	 *
	 * @param checkSystemTextModel 送审配置
	 * @param checkTextReqDTO      送审内容
	 * @return
	 */
	private CheckResultVO localCheckText(CheckSystemTextModel checkSystemTextModel, CheckTextReqDTO checkTextReqDTO) {
		if (checkAuditWhiteUser(String.valueOf(checkTextReqDTO.getTaskId()))) {
			return CheckResultVO.builder().success(true).taskId(checkTextReqDTO.getTaskId()).build();
		}
		if (Boolean.FALSE.equals(checkSystemTextModel.getOpen())) {
			log.info("本地送审对接关闭，本地直接返回送审成功 checkTextReqDTO：{}", JSON.toJSONString(checkTextReqDTO));
			return CheckResultVO.builder().success(true).taskId(checkTextReqDTO.getTaskId()).build();
		}
		if (CharSequenceUtil.isEmpty(checkTextReqDTO.getContent())) {
			log.info("本地送审内容为空：{}", JSON.toJSONString(checkTextReqDTO));
			return CheckResultVO.builder().success(true).taskId(checkTextReqDTO.getTaskId()).build();
		}
		List<String> localWords = checkSensitiveConfig.getLocalWords();
		if (CollUtil.isEmpty(localWords)) {
			log.info("本地送审库内容为空：{}", JSON.toJSONString(checkTextReqDTO));
			return CheckResultVO.builder().success(true).taskId(checkTextReqDTO.getTaskId()).build();
		}
		Long appendSize = checkSystemTextModel.getAppendBeforeSize();
		if (null == appendSize) {
			appendSize = CheckSystemConfig.DEFAULT_APPEND_BEFORE_SIZE;
		}
		int maxSize = checkSystemTextModel.getOutputSize().intValue() + appendSize.intValue();
		String checkText = getTextByMaxSize(maxSize, checkTextReqDTO.getContent());
		log.info("本地文本送审, 本地送审参数为：{}， maxSize:{}", checkText, maxSize);
		for (String localWord : localWords) {
			// 统一转换小写
			if (checkText.toLowerCase().contains(localWord.toLowerCase())) {
				log.warn("本地库文本送审涉敏 localWord:{}, checkText:{}", localWord, checkText);
				return CheckResultVO.builder().success(false).taskId(checkTextReqDTO.getTaskId()).build();
			}
		}
		// 默认成功送审
		return CheckResultVO.builder().success(true).taskId(checkTextReqDTO.getTaskId()).build();
	}

	/**
	 * 检查送审用户白名单
	 * 
	 * @param userId
	 * @param dialogueId
	 * @return
	 */
	private boolean checkAuditWhiteUser(String dialogueId) {
		if (whiteListProperties.getAuditWhiteUser().contains(RequestContextHolder.getPhoneNumber())) {
			log.info("本地送审-送审白名单用户，本地直接返回送审成功 userId:{}, dialogueId:{}", RequestContextHolder.getUserId(), dialogueId);
			return true;
		}
		return false;
	}

	/**
	 * 截取最大字符数
	 *
	 * @param maxSize 最大字符数
	 * @param content 内容
	 * @return 截取后的内容
	 */
	private String getTextByMaxSize(int maxSize, String content) {
		if (content.length() < maxSize) {
			return content;
		}
		return content.substring(content.length() - maxSize);
	}
}
