package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiBlackResourceEntity;

import java.util.List;

/**
 * className:BlackResourceRepository
 *
 * <AUTHOR>
 */
public interface BlackResourceRepository {

    /**
     * 获取全量黑名单数据
     * @return 全量黑名单数据list
     */
    List<AlgorithmAiBlackResourceEntity> getAllDate();

    /**
     * 查询资源名称列表
     * @param names 实体列表
     * @param types 类型列表
     * @return 资源名称列表
     */
    List<String> getResourceNameList(List<String> names, List<Integer> types);

}
