package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * enumName: ResourceTypeEnum
 * description: 知识库文件上传资源类型枚举类
 *
 * <AUTHOR>
 * @date 2025/2/11
 */
@Getter
@AllArgsConstructor
public enum KnowledgeResourceTypeEnum {

    /**
     * 个人云文档
     */
    PERSONAL_FILE(0, "个人云文档", ""),

    /**
     * 邮件
     */
    MAIL(1, "邮件", "eml"),

    /**
     * 笔记
     */
    NOTE(2, "笔记", "note"),

    /**
     * 网页
     */
    HTML(3, "网页", "html"),

    /**
     * 本地文件(端侧上传)
     */
    FILE_LOCAL(4, "本地文件", ""),

    /**
     * 笔记同步
     */
    NOTE_SYNC(5, "笔记同步", "note"),

    ;

    private final Integer code;

    private final String name;

    private final String ext;


    public static KnowledgeResourceTypeEnum getByCode(Integer code) {
        for (KnowledgeResourceTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }


    public static boolean isExist(Integer code) {
        return getByCode(code) != null;
    }


    public static boolean isPersonalFile(Integer code) {
        return PERSONAL_FILE.getCode().equals(code);
    }

    public static boolean isMail(Integer code) {
        return MAIL.getCode().equals(code);
    }

    public static boolean isNote(Integer code) {
        return NOTE.getCode().equals(code);
    }

    public static boolean isNoteSync(Integer code) {
        return NOTE_SYNC.getCode().equals(code);
    }

    public static boolean isHtml(Integer code) {
        return HTML.getCode().equals(code);
    }
}
