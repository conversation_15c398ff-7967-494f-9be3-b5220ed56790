package com.zyhl.yun.api.outer.domain.resp;


import com.zyhl.yun.api.outer.domain.req.TemplateReqEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 模板匹配结果
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TemplateMatchRsqEntity {

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 推荐影集模板ID列表
     */
    private List<TemplateReqEntity> templateList;
}
