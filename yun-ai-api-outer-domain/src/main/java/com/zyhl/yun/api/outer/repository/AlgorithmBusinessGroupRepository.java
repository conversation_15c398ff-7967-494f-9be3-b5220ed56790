package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.AlgorithmBusinessGroupEntity;

/**
 * 算法业务组表-Repository
 * @Author: WeiJingKun
 */
public interface AlgorithmBusinessGroupRepository {

    /**
     * 根据算法组编码，获取算法业务组
     * @Author: Wei<PERSON><PERSON><PERSON><PERSON>
     * @param algorithmGroupCode 算法组编码
     * @return 算法业务组
     */
    AlgorithmBusinessGroupEntity queryByAlgorithmGroupCode(Integer algorithmGroupCode);

}
