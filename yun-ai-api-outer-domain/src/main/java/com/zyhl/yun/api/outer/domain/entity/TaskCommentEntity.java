package com.zyhl.yun.api.outer.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 任务评论实体类
 * @date 2025/4/21 17:43
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskCommentEntity {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 模块编码
     */
    private String module;

    /**
     * 模型类型
     * qwen：通义千问，xfyun：讯飞星火大模型
     */
    private String modelType;

    /**
     * 是否喜欢
     *
     * @see com.zyhl.yun.api.outer.enums.LikeEnum
     */
    private Integer likeComment;

    /**
     * 默认评论
     */
    private String defaultComment;

    /**
     * 用户自定义评论
     */
    private String customComment;

}