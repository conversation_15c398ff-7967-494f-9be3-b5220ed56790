package com.zyhl.yun.api.outer.domain.entity;

import cn.hutool.core.collection.ListUtil;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

/**
 * AI全网搜黑名单资源entity
 * 
 * <AUTHOR>
 * @date 2025-04-10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AlgorithmAiBlackResourceEntity {

	/**
	 * 名资源称
	 */
	private String resourceName;

	/**
	 * 类型
	 */
	private String resourceType;

	/**
	 * 风险类型
	 */
	private String riskType;

	/**
	 * 默认是 0 原有黑名单资源，1，国内影视资源；2，国内演员，3，国内导演
	 */
	private Integer shieldedType;

	@Getter
	@AllArgsConstructor
	public enum ShieldedTypeEnum {
		/** 原有黑名单资源 */
		BLACK(0, "原有黑名单资源"),

		/** 国内影视资源 */
		MOVIE_RESOURCE(1, "国内影视资源"),

		/** 国内演员 */
		ACTOR(2, "国内演员"),

		/** 国内导演 */
		DIRECTOR(3, "国内导演"),

		;

		private final Integer type;

		private final String  remark;

		/**
		 * 获取黑名单相关枚举类型列表
		 * @Author: WeiJingKun
		 */
		public static List<Integer> blackList() {
			return ListUtil.toList(BLACK.getType());
		}

		/**
		 * 获取屏蔽资源相关枚举类型列表
		 * @Author: WeiJingKun
		 */
		public static List<Integer> shieldResourceList() {
			List<Integer> list = new ArrayList<>();
			list.add(MOVIE_RESOURCE.getType());
			list.add(ACTOR.getType());
			list.add(DIRECTOR.getType());
			return list;
		}

	}

}