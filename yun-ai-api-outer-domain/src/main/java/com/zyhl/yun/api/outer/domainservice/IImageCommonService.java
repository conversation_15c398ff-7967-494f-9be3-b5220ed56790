package com.zyhl.yun.api.outer.domainservice;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.domainservice.IImageCommonService} <br>
 * <b> description:</b>
 * 图片公共处理接口层
 *
 * <AUTHOR>
 * @date 2024-03-09 13:50
 **/
public interface IImageCommonService {

    /**
     * 将外链地址url下载文件返回本地路径
     *
     * @param url      the url
     * @param imageExt the image ext
     * @param userId   the user id
     * @return {@link String}
     * <AUTHOR>
     * @date 2024-3-9 13:54
     */
    String urlToLocalPath(String url, String imageExt, String userId);

    /**
     * 将图片base64转成本地文件返回本地路径
     *
     * @param base64   the base64
     * @param imageExt the image ext
     * @param userId   the user id
     * @return {@link String}
     * <AUTHOR>
     * @date 2024-3-9 13:55
     */
    String base64ToLocalPath(String base64, String imageExt, String userId);

    /**
     * 创建文件路径，同一天内相同
     *
     * @param base64OrUrl the base64 or url
     * @param imageExt    the image ext
     * @param userId      the user id
     * @return {@link String}
     * <AUTHOR>
     * @date 2024-3-9 15:40
     */
    String createPathBaseOnDate(String base64OrUrl, String imageExt, String userId);
    
    /**
     * 创建目录路径，同一天内相同
     *
     * @param base64OrUrl the base64 or url
     * @param businessPath    the business path
     * @param userId      the user id
     * @return {@link String}
     * <AUTHOR>
     * @date 2024-3-9 15:40
     */
    public String createDirPathBaseOnDate(String base64OrUrl, String businessPath, String userId);
}
