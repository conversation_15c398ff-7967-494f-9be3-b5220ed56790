package com.zyhl.yun.api.outer.domain.entity;

import com.zyhl.yun.api.outer.enums.AIModuleEnum;
import com.zyhl.yun.api.outer.enums.BusinessSourceEnum;
import com.zyhl.yun.api.outer.enums.VideoAIStatusEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.Data;

import java.io.Serializable;


/**
 * 云邮AI助手1.1版本报名
 * <AUTHOR>
 */
@Data
public class AlgorithmAiRegisterEntity implements Serializable {

    /**
     * redis缓存用，1-已报名，0-未报名
     */
    private Integer isSignUp = 1;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户所属底座
     */
    private Integer belongsPlatform;

    /**
     * 业务类型：0-AI工具，1-AI助手，2-智能相册 3-文档检索
     *
     * @see com.zyhl.yun.api.outer.enums.BusinessSourceEnum
     */
    private Integer businessType;

    /**
     * 模型枚举，AI工具有模块划分
     *
     * @see com.zyhl.yun.api.outer.enums.AIModuleEnum
     */
    private Integer module;

    /**
     * 名称
     *
     * @see com.zyhl.yun.api.outer.enums.AIModuleEnum
     */
    private String moduleName;

    /**
     * 目录id
     */
    private String dirId;

    /**
     * 云盘目录path
     */
    private String path;

    /**
     * 厂商类型，通过厂商类型判断走那个搜索平台
     *
     * @see com.zyhl.hcy.yun.ai.common.base.enums.SupplierTypeEnum
     */
    private Integer factoryType;

    /**
     * 业务算法组编码，根据算法组执行那些算法：1 华为 - 图片元数据提取任务算法组 （目前只支持，2 彩讯 - 图片元数据提取任务算法组
     */
    private Integer algorithmGroupCode;

    /**
     * business_type 0-3状态：0-启用，1-停用；
     * business_type 4状态 -1：未授权，0未开始，1视频同步中 2，视频同步完成 ，3、合集生成完成，4、刮削中，5、刮削完成
     *
     */
    private Integer status = 0;


    public AlgorithmAiRegisterEntity() {

    }

    public AlgorithmAiRegisterEntity(Integer businessType){
        this.userId = RequestContextHolder.getUserId();
        this.belongsPlatform = RequestContextHolder.getBelongsPlatform();
        this.businessType = businessType;
    }

    public AlgorithmAiRegisterEntity(Integer businessType, Integer module, String dirId, String path) {
        this.userId = RequestContextHolder.getUserId();
        this.belongsPlatform = RequestContextHolder.getBelongsPlatform();
        this.businessType = businessType;
        this.dirId = dirId;
        this.path = path;
        if (BusinessSourceEnum.isAssistant(businessType)) {
            this.module = module;

            if (module != null) {
                this.moduleName = AIModuleEnum.getByModule(this.module).getCatalogName();
            }
        }
    }

    /**
     * 已经报名
     *
     * @return true-已经报名
     */
    public boolean registered() {
        return isSignUp != null && isSignUp == 1 && status != null && status == 0;
    }

    public boolean checkStatus(){
        return  VideoAIStatusEnum.SYNCHRONIZATION_IN_PROGRESS.getStatus()> this.status;
    }

    /**
     * 未报名
     *
     * @return 当前对象
     */
    public AlgorithmAiRegisterEntity notRegistered() {
        this.isSignUp = 0;
        this.status = 1;
        return this;
    }

    /**
     * 状态改为启用
     */
    public AlgorithmAiRegisterEntity enabled() {
        this.status = 0;
        return this;
    }

    /**
     * 状态改为禁用
     * @return
     */
    public AlgorithmAiRegisterEntity disabled() {
        this.status = 1;
        return this;
    }


    /**
     * BUSINESSTYPE4:状态改为视频同步中
     * @return
     */
    public AlgorithmAiRegisterEntity videoProgress() {
        this.status = 1;
        return this;
    }
    /**
     * 状态是否禁用
     * @return
     */
    public boolean checkDisabled(){
        return 1 == this.status;
    }
}
