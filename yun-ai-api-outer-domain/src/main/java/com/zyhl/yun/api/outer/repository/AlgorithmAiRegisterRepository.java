package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiRegisterEntity;

import java.util.List;

/**
 * 云邮AI助手1.1版本重构报名
 *
 * <AUTHOR>
 */
public interface AlgorithmAiRegisterRepository {


    /**
     * 新增
     *
     * @param entity 实体对象
     */
    void insert(AlgorithmAiRegisterEntity entity);

    /**
     * 修改
     *
     * @param entity 实体对象
     */
    void updateById(AlgorithmAiRegisterEntity entity);

    /**
     * 查询用户所有的报名记录
     *
     * @param userId 用户id
     * @return list
     */
    List<AlgorithmAiRegisterEntity> queryByUserId(String userId);

    /**
     * 根据用户id和业务类型查询
     *
     * @param userId       用户id
     * @param businessType 业务类型
     * @return 实体对象
     */
    AlgorithmAiRegisterEntity queryByUserId(String userId, Integer businessType);
}
