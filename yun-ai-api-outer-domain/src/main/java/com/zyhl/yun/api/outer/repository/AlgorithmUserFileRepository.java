package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.image.AlgorithmUserFileEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 算法用户资源文件
 *
 * <AUTHOR>
 * @description 算法用户资源文件
 * @date 2023/7/8 下午3:02
 */
public interface AlgorithmUserFileRepository {

    /**
     * 查询用户图片资源文件
     *
     * @param userId 用户userId
     * @param ownerType 业务类型
     * @param fileIds 文件id列表
     * @return 用户资源文件
     */
    List<AlgorithmUserFileEntity> selectUserFileInfoHaveMetadataRowKey(String userId, Integer ownerType, List<String> fileIds);

    /**
     * 通过拍摄时间查询用户资源列表
     *
     * @param userId 用户userId
     * @param ownerType 业务类型
     * @param fileIds 文件id列表
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 用户资源列表
     */
    List<AlgorithmUserFileEntity> selectHbaseUserFileByTime(String userId, Integer ownerType, List<String> fileIds, LocalDateTime startDate, LocalDateTime endDate);
}
