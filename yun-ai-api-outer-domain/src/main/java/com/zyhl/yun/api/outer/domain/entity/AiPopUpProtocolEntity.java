package com.zyhl.yun.api.outer.domain.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * AI弹窗协议配置-Entity
 * @Author: WeiJingKun
 */
@Data
public class AiPopUpProtocolEntity implements Serializable {

    /** 渠道 */
    private String channel;

    /** 类别 */
    private String type;

    /** 标题 */
    private String title;

    /** 正文文案 */
    private String text;

    /** 协议文案 */
    private String protocol;

    /** 协议跳转的url */
    private String protocolUrl;

    /** 确认按钮文案 */
    private String confirm;

    /** 取消按钮文案 */
    private String cancel;


}
