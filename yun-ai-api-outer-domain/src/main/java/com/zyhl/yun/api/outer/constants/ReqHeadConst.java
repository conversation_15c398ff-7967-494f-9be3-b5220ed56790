package com.zyhl.yun.api.outer.constants;

/**
 * <p>
 * 请求头参数
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
public class ReqHeadConst {
    /**
     * api版本
     */
    public final static String API_VERSION = "x-yun-api-version";
    /**
     * 客户端设备信息
     */
    public final static String CLIENT_INFO = "x-yun-client-info";
    /**
     * 接入平台的渠道号
     */
    public final static String APP_CHANNEL = "x-yun-app-channel";
    /**
     * SDK集成方的APP产品名称
     */
    public final static String SDK_CHANNEL = "x-yun-sdk-channel";
    /**
     * 内部第三方平台调用时填入的相关信息，json字符串
     */
    public final static String PLATFORM_INFO = "x-yun-platform-info";

    /**
     * 鉴权token
     */
    public final static String AUTHORIZATION = "Authorization";

    /**
     * 请求ID
     */
    public final static String X_YUN_TID = "x-yun-tid";

    /**
     * 接受语言
     * 简体中文：zh-CN（默认）
     * 英文：en-US
     * 繁体中文：zh-HK
     */
    public final static String ACCEPT_LANGUAGE = "Accept-Language";

	/**
	 * 终端-灵犀 请求头key-兼容云盘 basic token
	 */
    public static final String LINGXI_KEY_OF_AUTHORIZATION = "Authorization";
    
	/**
	 * 终端-灵犀 请求头key-统一认证token
	 */
    public static final String LINGXI_KEY_OF_X_USER_TOKEN = "X-User-Token";
	/**
	 * 终端-灵犀 请求头key-统一认证sourceId
	 */
    public static final String LINGXI_KEY_OF_SOURCE_ID = "sourceId";
	/**
	 * 终端-灵犀 请求头key-时间戳
	 */
    public static final String LINGXI_KEY_OF_TS = "ts";
	/**
	 * 终端-灵犀 请求头key-加密参数
	 */
    public static final String LINGXI_KEY_OF_SIGN = "sign";

    public final static String API_VERSION_V1 = API_VERSION + "=v1";
    public final static String API_VERSION_V2 = API_VERSION + "=v2";
    public final static String API_VERSION_V3 = API_VERSION + "=v3";
    public final static String API_VERSION_V4 = API_VERSION + "=v4";
    public final static String API_VERSION_V5 = API_VERSION + "=v5";
    public final static String API_VERSION_V6 = API_VERSION + "=v6";
}
