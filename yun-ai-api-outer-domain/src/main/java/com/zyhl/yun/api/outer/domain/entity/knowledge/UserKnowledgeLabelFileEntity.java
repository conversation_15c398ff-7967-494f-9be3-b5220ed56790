package com.zyhl.yun.api.outer.domain.entity.knowledge;

import com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 个人知识库标签与文件关系表
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class UserKnowledgeLabelFileEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 业务类型：
     * -1 - 未知类型
     * 1-personal 个人云
     * 2-group 圈子
     * 3-shareGroup 共享群
     * 4-family 家庭云
     * 5-activity 活动空间 照片直播
     * 6-note 笔记
     * 7-cardpackage 卡包
     * 8-system 系统空间
     * 9-partner 合作空间
     * 10 -mount 挂载盘
     *
     * @see com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum
     */
    private Integer ownerType;

    /**
     * 标签id
     */
    private Long labelId;

    /**
     * 文件id
     */
    private String fileId;

    /**
     * 个人云的文件id
     */
    private String oldFileId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

	/**
	 * 自定义构造函数
	 * 
	 * @param userId 用户id
	 * @param fileId 文件id
	 */
    public UserKnowledgeLabelFileEntity(String userId, String fileId) {
        this.userId = userId;
        this.ownerType = OwnerTypeEnum.AI.getOwnerValue();
        this.fileId = fileId;
        this.oldFileId = fileId;
    }
}
