package com.zyhl.yun.api.outer.config.knowledge;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * 数字峰会知识库配置
 *
 * <AUTHOR>
 * @date 2025/4/26 18:43
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "knowledge.digital-summit")
public class DigitalSummitProperties {

    /**
     * 白名单（手机号）
     */
    private List<String> whiteList = new ArrayList<>();

    /**
     * 命中后的key
     */
    private List<String> queryKeys = new ArrayList<>();

    /**
     * 文件id列表
     */
    private List<String> fileIds = new ArrayList<>();

    /**
     * 重排后返回条数
     */
    private Integer topN = 100;

    /**
     * 重排后最小评分
     */
    private Float minScore = -1f;

    /**
     * 重排后返回分块字符总长度：20000（默认）
     */
    private Integer textMaxLength = 20000;

    /**
     * 用户提示词
     */
    private String userPrompt;

}
