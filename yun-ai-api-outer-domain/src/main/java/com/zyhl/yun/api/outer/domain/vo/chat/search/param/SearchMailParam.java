package com.zyhl.yun.api.outer.domain.vo.chat.search.param;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.yun.api.outer.enums.chat.search.SearchDiscoveryParamQueryTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 对话信息-搜索参数-邮件
 *
 * @Author: WeiJingKun
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchMailParam extends SearchCommonParam implements Serializable {

    /**
     * 指定发件人列表
     * 支持多个（发件人名称和发件人地址）搜索
     */
    private List<String> fromList;

    /**
     * 读状态
     * 0:不限
     * 1:未读
     * 2:已读
     * @see ReadEnum
     */
    private Integer read;

    /**
     * 搜索收信时间范围列表，每个时间范围均为或的关系，如果填了，TimeRange中的两个字段就是必填
     * 邮箱目前只支持传1个
     */
    private List<MillisecondTimeRange> receivedDateList;

    /**
     * 邮件类型
     * 1 重要（默认）
     * 0 普通
     * @see MailTypeEnum
     */
    private Integer mailType;

    /**
     * 指定收件人列表
     */
    private List<String> recipientList;

    /**
     * 邮件标题
     */
    private String title;
    /**
     * 邮件内容搜索关键字
     */
    private String content;

    @Getter
    @AllArgsConstructor
    public enum ReadEnum {
        /**
         * 不限
         */
        ALL(0, "不限"),
        /**
         * 未读
         */
        UNREAD(1, "未读"),

        /**
         * 已读
         */
        READ(2, "已读"),

        ;

        private final Integer code;

        private final String  remark;




        /**
         * 状态包含：0-不限 或 (1-未读和2-已读)，则设置为：0-不限
         * @Author: WeiJingKun
         *
         * @param statusList 读状态列表
         * @return true:包含，false:不包含
         */
        public static boolean hasAllReadStatus( List<String> statusList) {
            if (CollUtil.isNotEmpty(statusList)) {
                // 状态包含：0-不限
                if (statusList.contains(ALL.getRemark())) {
                    return true;
                }
                // 状态包含：1-未读和2-已读
                if(statusList.contains(SearchMailParam.ReadEnum.UNREAD.getRemark()) && statusList.contains(SearchMailParam.ReadEnum.READ.getRemark())){
                    return true;
                }
            }
            return false;
        }

    }

    public static Integer getReadEnumByRemark(String str) {
        for(ReadEnum read : ReadEnum.values()) {
            if(read.getRemark().equals(str)) {
                return read.getCode();
            }
        }
        return null;
    }

    @Getter
    @AllArgsConstructor
    public enum MailTypeEnum {
        /**
         * 普通邮件
         */
        GEN(0, "普通邮件"),
        /**
         * 重要邮件
         */
        IMPORTANT(1, "重要邮件"),

        ;


        private final Integer code;

        private final String  remark;

        public static MailTypeEnum getByCode(Integer code) {
            if (null == code) {
                return null;
            }
            for (MailTypeEnum type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    public static Integer getMailTypeEnumByRemark(String typeRemark) {
        for(MailTypeEnum mailTypeEnum : MailTypeEnum.values()) {
            if(mailTypeEnum.getRemark().equals(typeRemark)) {
                return mailTypeEnum.getCode();
            }
        }
        return null;
    }

}
