package com.zyhl.yun.api.outer.domain.vo.chat.search.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 对话信息-搜索结果-活动-列表数据
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchActivity implements Serializable {

    /** 标题 */
    private String title;

    /** 活动介绍 */
    private String slogn;

    /** 业务类型：1.活动 */
    private Integer serviceType;

    /** 链接地址(当serviceType为2并且linkType为0则该值为模块id) */
    private String address;

    /** 缩略图地址 */
    private String thumbnail;

    /** 跳转类型（0，本地跳转；1，跳转链接；2，小程序；5，跳转scheme） */
    private Integer linkType;

    /** 埋点id */
    private String toolId;

}
