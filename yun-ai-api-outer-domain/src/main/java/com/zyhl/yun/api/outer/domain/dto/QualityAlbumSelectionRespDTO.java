package com.zyhl.yun.api.outer.domain.dto;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/20 15:17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QualityAlbumSelectionRespDTO {

    private List<String> fileIds;

    /**
     * 业务类型：1-个人云，2-圈子，3-共享群，4-家庭云
     */
    private Integer ownerType;

    private String userId;

    private String albumId;

    /**
     * 相册名称,主标题
     */
    private String albumName;

    /**
     * 相册封面
     */
    private String albumCover;

    /**
     * 相册副标题
     */
    private String albumSecondTittle;

    /**
     * 相册文件数量
     */
    private Integer albumFileSize;

    /**
     * 相册背景音乐
     */
    private String musicId;
}
