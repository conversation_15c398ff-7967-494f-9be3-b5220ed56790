package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.knowledge.KnowledgeFileEntity;

import java.util.List;

/**
 * 公共知识库文件资源表
 *
 * <AUTHOR>
 */
public interface KnowledgeFileRepository {

    /**
     * 统计公共知识库文件数量
     *
     * @param baseId 知识库id
     * @return 数量
     */
    int count(String baseId);

    /**
     * 根据文件id查询文件信息
     *
     * @param baseId  知识库id
     * @param fileIds 文件id集合
     * @return 文件信息集合
     */
    List<KnowledgeFileEntity> selectByFileIds(String baseId, List<String> fileIds);
}
