package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识库选择状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum KnowledgeSelectedEnum {

    /**
     * 未选择
     */
    NOT_SELECTED(0, "未选择"),

    /**
     * 已选择
     */
    SELECTED(1, "已选择"),

    ;

    /**
     * 状态
     */
    private final Integer status;

    /**
     * 描述
     */
    private final String remark;


}
