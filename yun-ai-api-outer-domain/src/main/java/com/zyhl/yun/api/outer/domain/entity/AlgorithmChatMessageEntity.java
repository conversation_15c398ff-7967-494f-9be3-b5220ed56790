package com.zyhl.yun.api.outer.domain.entity;

import java.util.Date;
import java.util.List;

import com.zyhl.yun.api.outer.enums.ChatMessageStarEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 会话信息表;
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AlgorithmChatMessageEntity {
    /** 主键（会话ID） */
    private Long id;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 应用id
     */
    private String applicationId;
    /**
     * 应用类型
     * @see com.zyhl.yun.api.outer.enums.ApplicationTypeEnum
     */
    private String applicationType;
    /**
     * 业务类型
     * 详见，配置文件：source-channels
     */
    private String businessType;
    /**
     * 标题;默认取会话中的第一句对话输入内容
     */
    private String title;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 是否星标;(1是，0否)
     * @see com.zyhl.yun.api.outer.enums.ChatMessageStarEnum
     */
    private Integer enableStar;

    /**
     * 图标类型
     */
    private String iconType;

    /**
     * 图标子类型
     */
    private String subIconType;

    /**
     * 是否删除;(1是，0否)
     */
    private Integer delFlag;

    /** ============以下字段为业务参数，不是表字段==============*/
    /**
     * 会话id集合
     */
    private List<Long> sessionIdList;

    /**
     * 排序方式（不填默认1）
     * 1--按照会话【创建时间】倒序排序
     * 2--按照会话【更新时间】倒序排序
     */
    private Integer sortType;

    /** ============以下字段为临时字段，不在会话话表中============== */
    /** 渠道来源 */
    private String sourceChannel;

    public Boolean caseToEnableStar(Integer enableStar) {
        return ChatMessageStarEnum.isStar(enableStar);
    }

}