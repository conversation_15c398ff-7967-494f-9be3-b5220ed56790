package com.zyhl.yun.api.outer.strategy.chat.searchresult.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchCommonParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchFunctionParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchResult;
import com.zyhl.yun.api.outer.external.ApiTextExternalService;
import com.zyhl.yun.api.outer.strategy.chat.searchresult.SearchStrategy;

/**
 * 【功能】搜索策略
 * @Author: WeiJingKun
 */
public class FunctionSearchStrategy implements SearchStrategy {

    /**
     * 执行搜索策略，获取【功能】搜索接口结果
     * @Author: WeiJingKun
     *
     * @param searchResult 搜索结果
     * @param searchSubParam  搜索子参数
     */
    @Override
    public void performSearch(SearchResult searchResult, Object searchSubParam, SearchCommonParam searchCommonParam) {
        ApiTextExternalService apiTextExternalService = SpringUtil.getBean(ApiTextExternalService.class);
        SearchFunctionParam param = (SearchFunctionParam) searchSubParam;
        // set搜索参数-公共数据
        param.setSearchCommonParam(searchCommonParam);
        // 获取搜索结果
        searchResult.setSearchFunctionResult(apiTextExternalService.searchFunction(param));
    }

}
