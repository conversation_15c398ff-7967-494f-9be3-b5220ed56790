package com.zyhl.yun.api.outer.vo;

import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.enums.knowledge.AiExpansionRangeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 知识库AI扩写信息VO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KnowledgeAiExpansionVO {

    /**
     * 大纲
     */
    private String outline;

    /**
     * 字数
     */
    private Integer wordCount;

    /**
     * 召回切片数量
     */
    private Integer chunkCount;

    /**
     * 业务模型  注意：只有AI扩写模型被改变的情况下才有值
     *
     * @see TextModelEnum
     * 枚举值中的code编码值
     */
    private String modelType;

    /**
     * 是否支持AI扩写(判断是否为云盘会员+白名单)
     */
    private Boolean enableMember = Boolean.FALSE;

    /**
     * 是否支持AI扩写(判断是否为云盘会员+白名单)
     */
    private Boolean enableWhiteVip = Boolean.FALSE;

    /**
     * 知识库AI扩展范围枚举
     */
    private AiExpansionRangeEnum rangeEnum = AiExpansionRangeEnum.FIRST;

    /**
     * 入库hbase结果列表-大模型之前
     */
    private List<DialogueFlowResult> beforeOutputList = new ArrayList<>();

}
