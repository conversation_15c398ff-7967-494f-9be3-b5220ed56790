package com.zyhl.yun.api.outer.domainservice;


import com.zyhl.yun.api.outer.domain.entity.FileEntity;
import com.zyhl.yun.api.outer.domain.req.BatchGetReqEntity;
import com.zyhl.yun.api.outer.domain.req.FileGetDownloadUrlReqEntity;
import com.zyhl.yun.api.outer.domain.resp.FileGetDownloadUrlRespEntity;

import java.util.List;

/**
 * 文件外部服务
 *
 * <AUTHOR>
 */
public interface FileExternalService {

    /**
     * 批量获取文件信息
     *
     * @param batchGetReq entity
     * @return 集合
     */
    List<FileEntity> fileBatchGet(BatchGetReqEntity batchGetReq);

    /**
     * 获取文件下载地址
     *
     * @param query entity
     * @return 对象
     */
    FileGetDownloadUrlRespEntity fileGetDownloadUrl(FileGetDownloadUrlReqEntity query);

}
