package com.zyhl.yun.api.outer.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 媒体元数据
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MediaMetaInfoEntity {

    /**
     * 时长。
     * 该字段存储媒体文件的时长，单位为秒。
     */
    private String duration;

    /**
     * 宽度。
     * 该字段存储媒体文件的宽度，单位为像素。
     */
    private Long width;

    /**
     * 高度。
     * 该字段存储媒体文件的高度，单位为像素。
     */
    private Long height;

    /**
     * 拍摄时间。
     * 该字段存储媒体文件的拍摄时间，格式为 RFC 3339，例如 "2019-08-20T06:51:27.292Z"。
     */
    private String takenAt;

}
