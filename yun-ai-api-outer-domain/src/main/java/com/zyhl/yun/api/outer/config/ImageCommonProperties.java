package com.zyhl.yun.api.outer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.config.ImageCommonProperties} <br>
 * <b> description:</b>
 * 图片公共类配置
 *
 * <AUTHOR>
 * @date 2024-03-09 13:59
 **/
@Configuration
@ConfigurationProperties(prefix = "yun-ai.image-nfs")
@Data
public class ImageCommonProperties {

    /**
     * 共享存储基础路径
     */
    private String sharePath = "/yun-ai-api-outer/local-files";

    /**
     * 共享存储文件夹路径
     */
    private String fodderCatalog = "fodder";
}
