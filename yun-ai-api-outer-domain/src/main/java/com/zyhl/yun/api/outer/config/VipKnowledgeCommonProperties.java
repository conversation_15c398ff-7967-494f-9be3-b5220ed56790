package com.zyhl.yun.api.outer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * VIP专属智能体知识库配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "knowledge.vip-common")
public class VipKnowledgeCommonProperties {

    /**
     * VIP专属智能体知识库开关
     */
    private Boolean vipCommonSwitch = false;

    /**
     * VIP专属智能体知识库的标识
     */
    private String knowledgeBaseId = "";

    /**
     * VIP专属智能体知识库白名单列表
     */
    private List<String> whiteList = new ArrayList<>();

}
