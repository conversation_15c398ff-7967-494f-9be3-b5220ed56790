package com.zyhl.yun.api.outer.domain.valueobject;

import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.FileResp;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 相册对象
 *
 * <AUTHOR>
 * @Date 2025/3/19 16:16
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AlbumInfo implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;
    /**
     * 相册id
     */
    private String albumId;
    /**
     * 相册名称
     */
    private String name;
    /**
     * 二级标题名称
     */
    private String secondTitle;
    /**
     * 相册类型
     */
    private Integer type;

    /**
     * 封面文件信息
     */
    private FileResp cover;
    /**
     * 相册描述
     */
    private String description;
    /**
     * 相册文件数
     */
    private Integer fileNumber;
    /**
     * 是否置顶
     */
    private Integer topFlag;
    /**
     * 置顶时间
     */
    private String topTime;

    /**
     * 相册创建时间
     */
    private String createTime;
    /**
     * 相册更新时间
     */
    private String updateTime;
    /**
     * 人物相册关系名称
     */
    private String relationName;
    /**
     * 关系分组 0 --其它（原默认值）， 1 --同事， 2 --朋友， 3 --家人（本人、伴侣、孩子、爸爸、妈妈、亲属）
     */
    private Integer relationType;
    /**
     * 静态资源对象，用于人脸大头贴，当staticFile为空时，使用cover作为相册封面。 ExternalResourceInfo中的thumbnailUrl为大头贴URL
     */
    ExternalResourceInfo staticFile;

    /**
     * 静态资源对象
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExternalResourceInfo {
        /**
         * 资源id
         */
        private String resourceId;
        /**
         * 资源名称
         */
        private String name;
        /**
         * 缩略图地址
         */
        private String thumbnailUrl;

        /**
         * 音视频播放地址
         */
        private String presentUrl;

        /**
         * 云盘文件分类， 根据文件的后缀名和mime-type对文件进行分了分类，主要分类有： app：安装包 ；zip：压缩包 image：图片； doc：文档 video：视频
         * ；audio：音频 folder：目录 ；others：其他
         */
        private String category;
    }
}
