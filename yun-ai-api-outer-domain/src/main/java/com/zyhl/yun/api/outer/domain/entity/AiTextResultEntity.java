package com.zyhl.yun.api.outer.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 智能文本对话结果
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class AiTextResultEntity {

    //private static final long serialVersionUID = 1L;

    /**
     * rowkey
     */
    private String rowKey;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 请求参数
     */
    private String reqParameters;

    /**
     * 历史对话
     */
    private String history;

    /**
     * 附件
     */
    private String attachment;

    /**
     * 响应参数
     */
    private String respParameters;

    /**
     * 对话id，不存hbase，用于rowkey的生成
     */
    private Long dialogueId;

}
