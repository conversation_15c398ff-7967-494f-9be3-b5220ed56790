package com.zyhl.yun.api.outer.domain.entity;

import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 会话内容表;
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AlgorithmChatContentEntity {
	/**
	 * 主键（对话id）
	 */
	private Long id;
	/**
	 * 用户id
	 */
	private String userId;
	/**
	 * 应用id
	 */
	private String applicationId;
	/**
	 * 应用类型
	 * 
	 * @see com.zyhl.yun.api.outer.enums.ApplicationTypeEnum
	 */
	private String applicationType;
	/**
	 * 业务类型 详见，配置文件：source-channels
	 */
	private String businessType;
	/**
	 * 会话ID
	 */
	private Long sessionId;

	/**
	 * 任务ID
	 */
	private Long taskId;
	/**
	 * 对话类型;0:对话历史记录,1:智囊历史记录
	 */
	private Integer talkType;
	/**
	 * 资源类型;0-无，1 邮件， 2 笔记， 3 图片 4对话
	 */
	private Integer resourceType;
	/**
	 * 工具指令;对接意图指令
	 * 
	 * @see com.zyhl.yun.api.outer.enums.DialogueIntentionEnum
	 */
	private String toolsCommand;
    /**
     * 工具指令;对接意图指令-子意图
	 * @see com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum
     */
    private String subToolsCommand;
	/**
	 * 模型类型;模型 qwen：通义千问，xfyun：讯飞星火大模型
	 */
	private String modelType;
	/**
	 * 输入内容;输入文本内容
	 */
	private String inContent;
	/**
	 * 输入时间
	 */
	private Date inAuditTime;
	/**
	 * 输入内容审批结果;状态码：2通过，其他失败
	 */
	private Integer inAuditStatus;
	/**
	 * 输入资源ID;（笔记/邮件/图片ID；纯文本时为空）
	 */
	private String inResourceId;
	/**
	 * 输出内容
	 */
	private String outContent;
	/**
	 * 输出资源ID;（笔记/邮件/图片ID；纯文本时为空）
	 */
	private String outResourceId;
	/**
	 * 输出文本类型：1--普通文本 2--富文本
	 */
	private Integer outContentType;
	/**
	 * 输出时间
	 */
	private Date outAuditTime;
	/**
	 * 输出内容审批结果;状态码：2通过，其他失败
	 */
	private Integer outAuditStatus;
	/**
	 * 旧图片文件id;当用户从主平台迁移到云空间时，图片id会发生变化，这里存的是迁移前的图片id,格式：用户选择的旧文件id1,模型返回的旧文件id2
	 */
	private String oldResourceId;
	/**
	 * 渠道来源
	 */
	private String sourceChannel;
	/**
	 * paas底座平台编码;0 华为主平台(ose) 1 阿里pds 2 华为云空间 3 云能dsp
	 */
	private Integer belongsPlatform;

	/** 扩展信息（json格式） */
	private String extInfo;

	/** 对话结果推荐信息（json格式） */
	private String recommendInfo;
	
    /**
     * 对话结果推荐信息-中部（json格式）
     */
    private String middleRecommendInfo;

	/**
	 * 迁移标识;1-迁移数据，0-新数据
	 */
	private Integer migrationFlag;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;
	/**
	 * 是否删除;（1是0否）
	 */
	private Integer delFlag;
	/**
	 * 提示词，比如：总结概要
	 */
	private String prompt;
	/**
	 * 对话状态
	 * 
	 * @see ChatStatusEnum
	 */
	private Integer chatStatus;

	/**
	 * 业务场景标识：app_writemail_ai（邮箱APP-写信AI）
	 */
	private String sceneTag;
	/**
	 * 命令类型：1--普通命令（默认），2--自动命令
	 */
	private Integer commandType;

	/** ============以下字段为业务参数，不是表字段============== */
	/**
	 * 会话id集合
	 */
	private List<Long> sessionIdList;

	/**
	 * 对话ID清单
	 * 如果输入对话ID，则返回对应的对话结果
	 */
	private List<Long> dialogueIdList;

    /**
     * 排序方式（不填默认1）
     * 1--按照对话【创建时间】倒序排序
     * 2--按照对话【创建时间】正序排序
     */
    private Integer sortType;

	/** ============以下字段为评价结果表字段，不在对话表中============== */

	/**
	 * 是否喜欢 0:不喜欢，1:喜欢
	 */
	private Integer likeComment;

	/**
	 * 默认评论
	 */
	private String defaultComment;
	/**
	 * 用户评论
	 */
	private String customComment;

	/** ============以下字段为hbase表字段，不在对话表中============== */
	/**
	 * hbase的req字段值
	 */
	private String hbaseReqParameters;
	/**
	 * hbase的respParameters字段值
	 */
	private String hbaseRespParameters;

	/** ============以下字段为任务表字段，不在对话表中============== */
	/**
	 * 任务-状态
	 * 
	 * @see com.zyhl.yun.api.outer.enums.task.TaskStatusEnum
	 */
	private Integer taskStatus;

	/**
	 * 任务-付费扣费状态
	 * 
	 * @see com.zyhl.yun.api.outer.enums.task.TaskFeePaidStatusEnum
	 */
	private Integer feePaidStatus;
	
	/**
	 * 任务-文件过期状态
	 * 
	 * @see com.zyhl.yun.api.outer.enums.task.FileExpiredStatusEnum
	 */
	private Integer fileExpiredStatus;

	/** 任务-错误结果码 */
	private String resultCode;

	/** 任务-错误信息 */
	private String resultMsg;

	/** 响应参数 */
	private String respParam;

	/** ============以下字段为SQL搜索字段，不在对话表中============== */
	/** 查询小邮助手旧数据的条件：1-查询 */
	private String queryAllOldDataFlag;

	/** 查询小邮助手旧数据的时间节点 */
	private String queryAllOldDataTimeNodes;

	/** 所有云邮助手业务类型 */
	private List<String> yunMailAllBusinessTypeList;

	/** 查询历史记录，大于等于创建时间节点 */
	private String geCreateTime;

	/** 历史对话列表V2 */
	private boolean isContentListV2;

	/** 需要查询的业务类型列表 */
	private List<String> businessTypeList;

}