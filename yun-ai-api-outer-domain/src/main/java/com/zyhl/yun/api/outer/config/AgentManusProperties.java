package com.zyhl.yun.api.outer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * className: AgentManusProperties
 * description: 云盘MANUS智能体-配置信息
 *
 * <AUTHOR>
 * @date 2025/4/8
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "manus")
public class AgentManusProperties {

    private List<FunctionConfig> functionList;

    @Data
    static class FunctionConfig {
        private String type;
        private FunctionInfo function;
    }

    @Data
    static class FunctionInfo {
        private String name;
        private String description;
        private ParametersConfig parameters;
    }

    @Data
    static class ParametersConfig {
        private String type;
        private List<String> required;
        private Object properties;
    }
}