package com.zyhl.yun.api.outer.pool;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 业务线程池，在{@linkplain #execute(Runnable)}}提交任务的时候，对任务进行包装将{@linkplain MDC}的值设置进子线程的MDC
 */
@Slf4j
@SuppressWarnings("all")
public class BusinessThreadPoolExecutor extends ThreadPoolExecutor {

    public BusinessThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime,
                                      TimeUnit unit, BlockingQueue<Runnable> workQueue) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
    }

    public BusinessThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime,
                                      TimeUnit unit, BlockingQueue<Runnable> workQueue, ThreadFactory threadFactory) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory);
    }

    public BusinessThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime,
                                      TimeUnit unit, BlockingQueue<Runnable> workQueue,
                                      RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, handler);
    }

    public BusinessThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime,
                                      TimeUnit unit, BlockingQueue<Runnable> workQueue,
                                      ThreadFactory threadFactory, RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
    }

    @Override
    public void execute(Runnable command) {
        if (command == null) {
            throw new NullPointerException();
        }
        Map<String, String> contextMap = Optional.ofNullable(MDC.getCopyOfContextMap()).orElse(Collections.emptyMap());
        // 如果cammand 对象已经是 TtlRunnable 了，则warpper MDC的设置逻辑之后。否则就wrapper之后在对ttl进行封装。
        command = wrapperCommand(command, contextMap);
        super.execute(command);
    }

    private Runnable wrapperCommand(Runnable command, Map<String, String> contextMap) {
        return () -> {
            try {
                // 拷贝MDC上下文
                for (Map.Entry<String, String> entry : contextMap.entrySet()) {
                    MDC.put(entry.getKey(), entry.getValue());
                }
                command.run();
            } finally {
                // 任务结束清理MDC上下文
                MDC.clear();
            }
        };
    }
}
