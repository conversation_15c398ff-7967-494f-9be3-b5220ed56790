package com.zyhl.yun.api.outer.enums.config;


import java.util.Objects;

/**
 * <AUTHOR>
 */
public enum TaskTypeEnum {

    /**
     * 图片元数据分析
     */
    IMAGE(1, "图片元数据分析"),
    /**
     * 人脸聚类
     */
    HUMAN_FACE(2, "人脸聚类"),

    /**
     * 相似度聚类
     */
    SIMILARITY(3, "相似度聚类"),

    /**
     * 文档向量
     */
    DOC_VECTOR(4, "文档向量类"),

    /**
     * 视频文本提取
     */
    VIDEO_TEXT_EXTRACTION(5, "视频提取文本");

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 描述
     */
    private final String msg;

    private TaskTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    public Integer getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }

    public static String getMsg(Integer code) {
        for (TaskTypeEnum resultEnum : TaskTypeEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.getMsg();
            }
        }
        return null;
    }

    public static TaskTypeEnum getTaskTypeEnum(Integer code) {
        for (TaskTypeEnum resultEnum : TaskTypeEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum;
            }
        }
        return IMAGE;
    }

}
