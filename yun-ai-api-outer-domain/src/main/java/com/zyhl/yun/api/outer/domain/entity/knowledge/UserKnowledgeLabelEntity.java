package com.zyhl.yun.api.outer.domain.entity.knowledge;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 个人知识库标签表
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class UserKnowledgeLabelEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 业务类型：
     * -1 - 未知类型
     * 1-personal 个人云
     * 2-group 圈子
     * 3-shareGroup 共享群
     * 4-family 家庭云
     * 5-activity 活动空间 照片直播
     * 6-note 笔记
     * 7-cardpackage 卡包
     * 8-system 系统空间
     * 9-partner 合作空间
     * 10 -mount 挂载盘
     *
     * @see com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum
     */
    private Integer ownerType;

    /**
     * 标签
     */
    private String label;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 排序优先级；默认当前用户最大排序+1，第一条记录为1
     */
    private Integer sort;


}
