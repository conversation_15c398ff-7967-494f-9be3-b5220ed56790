package com.zyhl.yun.api.outer.domain.dto;

import com.zyhl.hcy.yun.ai.common.rag.dto.validate.RecallQueryDTO;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.dialogue.KnowledgeBaseConfig;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.dialogue.RecallConfig;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.dialogue.RerankConfig;
import com.zyhl.hcy.yun.ai.common.rag.vo.RecallResultVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述：重排参数
 *
 * <AUTHOR> zhumaoxian  2025/2/18 14:27
 */
@Data
public class RerankDTO {


    /**
     * 检索文本
     */
    private String text;

    /**
     * 检索结果
     */
    private List<RecallResultVO> recallList;

    /**
     * 重排配置
     */
    private RerankConfig config;

    /**
     * 重排类型 向量、标量
     *
     */
    private String rerankType;

    /**
     * 查询类型
     * 0-其他类（默认）
     * 1-总结类
     * 2-建议类
     * 3-发言稿
     *
     * @see com.zyhl.hcy.yun.ai.common.rag.enums.RewriteQueryTypeEnum
     */
    private Integer queryType = 0;
}
