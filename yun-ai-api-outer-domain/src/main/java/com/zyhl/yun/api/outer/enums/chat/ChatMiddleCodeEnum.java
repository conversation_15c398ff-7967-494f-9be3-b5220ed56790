package com.zyhl.yun.api.outer.enums.chat;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流式对话结果中间状态码枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ChatMiddleCodeEnum {

    NETWORK_SEARCHING("networkSearching", "联网搜索中"),

    KNOWLEDGE_BASE_SEARCHING("knowledgeBaseSearching", "知识库搜索中"),

    MCLOUD_IMAGE_SEARCHING("mcloudImageSearching", "查找云盘图片"),

    IMAGE_SELECTING("imageSelecting", "精选相关图片"),

    ALBUM_GENERATING("albumGenerating", "相册生成"),

    NOTE_CONTENT_SEARCHING("noteContentSearching", "笔记正文搜索中"),

    NOTE_CONTENT_ANALYSIS_PROCESSING("noteContentAnalysisProcessing", "笔记正文智能解析进行中"),

    NOTE_CONTENT_ANALYSIS_FINISHED("noteContentAnalysisFinished", "笔记正文智能解析完成"),

    SEARCH_NULL("searchNull", "搜索结果为空"),
    ;

    private final String code;

    private final String desc;
}
