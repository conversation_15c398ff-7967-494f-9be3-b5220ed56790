package com.zyhl.yun.api.outer.domain.entity.knowledge;

import com.zyhl.yun.api.outer.enums.UserKnowledgeInviteLevelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 知识库邀请表entity
 *
 * <AUTHOR>
 * @date 2025/04/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserKnowledgeInviteEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 加入人id
     */
    private String userId;

    /**
     * 邀请人id
     */
    private String inviteUserId;

    /**
     * 分享的知识库id
     */
    private Long knowledgeId;

    /**
     * 选择，1--已选择；0--未选择；
     */
    private Integer selected;

    /**
     * 0-停用，1-启用（默认）
     */
    private Integer status;

    /**
     * 分享库使用的过期时间，空表示永久
     */
    private Date expireTime;

    /**
     * 加入时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;
    /**
     * 个人知识库分享成员等级
     */
    private UserKnowledgeInviteLevelEnum level;

}