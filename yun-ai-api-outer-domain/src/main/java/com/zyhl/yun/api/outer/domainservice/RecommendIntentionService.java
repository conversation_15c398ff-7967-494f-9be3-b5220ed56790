package com.zyhl.yun.api.outer.domainservice;

import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelVlReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.utils.ImageProcessorUtil;
import com.zyhl.yun.api.outer.config.textmodel.VlModelConfig;
import com.zyhl.yun.api.outer.domain.vo.IntentionRecommendVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import java.util.List;
import java.util.concurrent.Future;

/**
 * 推荐多意图领域服务类
 *
 * <AUTHOR>
 */
public interface RecommendIntentionService {

    /**
     * 单一模板处理意图-根据对话内容和多意图进行抽离
     *
     * @param dialogueId    对话ID
     * @param dialogue      对话内容
     * @param intentionInfo 推荐意图
     * @return 异步对话
     */
    Future<IntentionRecommendVO> getRecommendIntentionFuture(Long dialogueId, String dialogue,
                                                             IntentionInfo intentionInfo);

    /**
     * 统一模板处理多意图进行抽离-根据对话内容和多意图进行抽离
     *
     * @param dialogueId        对话ID
     * @param dialogue          对话内容
     * @param intentionMain     主意图
     * @param intentionInfoList 推荐意图列表
     * @return 异步对象
     */
    Future<Object> getUnifiedRecommendIntentionFuture(Long dialogueId, String dialogue,
                                                      String intentionMain, List<IntentionInfo> intentionInfoList);

    /**
     * 文本工具-图片视觉处理-根据对话内容进行抽离
     *
     * @param vlmBusinessModelConfig the vlm business model config
     * @param imageOutput            the image output
     * @param reqDTO                 the req dto
     * @return {@link Future<List<IntentionRecommendVO>>}
     * <AUTHOR>
     * @date 2025/6/18 14:50
     */
    Future<List<IntentionRecommendVO>> getTextToolAiPhotoVisionFuture(VlModelConfig.BusinessModelConfig vlmBusinessModelConfig,
                                                                      ImageProcessorUtil.ImageOutput imageOutput,
                                                                      TextModelVlReqDTO reqDTO);

}
