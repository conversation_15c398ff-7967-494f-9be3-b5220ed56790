package com.zyhl.yun.api.outer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * sse流式配置
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "flow-type.config")
@Data
public class FlowTypeProperties {

    /**
     * 文本大模型不返回内容默认文案
     */
    private String defaultAnswer = "";
    /**
     * 文本送审字数，达到值则发送审
     */
    private Integer auditSize = 30;
    /**
     * 流式连接超时时间（毫秒）
     */
    private Long timeout = 30000L;
    /**
     * 断开连接后尝试重连时间（毫秒）
     */
    private Long reconnectTimeMillis = 5000L;

}
