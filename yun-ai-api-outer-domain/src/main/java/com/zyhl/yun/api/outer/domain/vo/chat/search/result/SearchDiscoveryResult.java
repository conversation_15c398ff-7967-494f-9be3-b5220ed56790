package com.zyhl.yun.api.outer.domain.vo.chat.search.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 对话信息-搜索结果-发现广场
 * @Author: WeiJingKun
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchDiscoveryResult extends SearchCommonResult implements Serializable {

    /** 下一页 */
    private String pageCursor;

    /** 记录总数 */
    private Integer totalCount;

    /** 发现广场信息 */
    private List<SearchDiscovery> discoveryList;

    /** 搜索关键字 */
    private String keyword;

    /** 搜索关键字列表 */
    private List<String> keywords;

    /** 搜索类型(1-全部,2-文档,3-视频,4-试卷,5-影视,6-其他,7-书籍) */
    private List<Integer> queryTypes;

}
