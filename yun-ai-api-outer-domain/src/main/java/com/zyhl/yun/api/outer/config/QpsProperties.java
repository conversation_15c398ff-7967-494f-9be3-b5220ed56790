package com.zyhl.yun.api.outer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <b>className:</b>
 * {@link QpsProperties} <br>
 * <b> description:</b>
 * qps配置类
 *
 * <AUTHOR>
 * @date 2024-03-25 13:59
 **/
@Configuration
@ConfigurationProperties(prefix = "common.qpslimit")
@Data
public class QpsProperties {

    /**
     * qps 桶限流大小
     */
    private Integer limit;

    /**
     * 每period秒内限制数（缓冲size），一般跟limit一致即可
     */
    private Integer maxBurstSize;

    /**
     * 获取token内超时时间，单位毫秒
     */
    private Long timeoutMillis;

    /**
     * qps 令牌桶 redis超时时间 秒
     */
    private Integer expireTime;



}
