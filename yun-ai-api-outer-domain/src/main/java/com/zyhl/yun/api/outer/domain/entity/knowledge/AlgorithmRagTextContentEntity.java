package com.zyhl.yun.api.outer.domain.entity.knowledge;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 知识库资源的正文内容
 * @date 2025/4/17 18:07
 */
@Data
@Builder
public class AlgorithmRagTextContentEntity {

    /**
     * 行键，唯一标识一条记录。
     */
    private String rowKey;

    /**
     * 文件ID，关联的文件标识。
     */
    private String fileId;

    /**
     * 用户ID，记录所属用户的标识。
     */
    private String userId;

    /**
     * 资源类型，描述资源的类别。
     */
    private String resourceType;

    /**
     * 内容，存储具体的文本内容。
     */
    private String content;

     /**
     * 内容，存储新的文本内容。
     */
    private String newContent;
}

