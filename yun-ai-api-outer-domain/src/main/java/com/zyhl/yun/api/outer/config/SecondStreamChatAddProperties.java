package com.zyhl.yun.api.outer.config;

import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.*;

/**
 * 二次对话sse流式配置
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "second-stream.config")
@Data
public class SecondStreamChatAddProperties {

    /**
     * 默认千问大模型
     */
    private static final String DEFAULT_MODEL_CODE = "qwen";

    /**
     * 文本送审字数，达到值则发送审
     */
    private Integer auditSize = 30;

    /**
     * 文本大模型输入限制
     */
    private Integer modeMaxTokens = 15000;

    /**
     * 文本大模型
     */
    private String modeCode = DEFAULT_MODEL_CODE;

    /**
     * 大模型默认邮件内容
     */
    private String defaultEmailContent = "无邮件内容。";

    /**
     * 流式连接超时时间（毫秒）
     */
    private Long timeout = 30000L;

    /**
     * 断开连接后尝试重连时间（毫秒）
     */
    private Long reconnectTimeMillis = 5000L;

    /**
     * 允许二次对话的意图列表
     */
    private List<String> allowIntention = Collections.singletonList(DialogueIntentionEnum.SEND_MAIL.getCode());

    /**
     * 二次对话提示词模板
     */
    private String promptTemplate;

}
