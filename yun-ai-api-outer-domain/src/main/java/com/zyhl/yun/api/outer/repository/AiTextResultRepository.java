package com.zyhl.yun.api.outer.repository;

import java.util.List;

import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;

/**
 * AI任务结果保存到hbase
 *
 * <AUTHOR>
 */
public interface AiTextResultRepository {

    /**
     * 表id生成
     *
     * @param userId   用户id
     * @param dialogId 对话id
     * @return rowkey
     */
    static String createRowKey(String userId, Long dialogId) {
        return createRowKey(userId, String.valueOf(dialogId));
    }

    /**
     * 表id生成
     *
     * @param userId   用户id
     * @param dialogId 对话id
     * @return rowkey
     */
    static String createRowKey(String userId, String dialogId) {
        return userId + "_" + dialogId;
    }

    /**
     * 根据rowKey查询任务
     *
     * @param rowKey userId_dialogueId  用户id+对话id（对话id可自定义）
     * @return 实体对象
     */
    AiTextResultEntity getByRowKey(String rowKey);

    /**
     * 根据rowKey查询任务，userId_dialogueId  用户id+对话id（对话id可自定义）
     *
     * @param userId   用户id
     * @param dialogId 对话id
     * @return 实体对象
     */
    AiTextResultEntity getByRowKey(String userId, Long dialogId);

    /**
     * 根据rowKey查询任务，userId_dialogueId  用户id+对话id（对话id可自定义）
     *
     * @param userId   用户id
     * @param dialogId 对话id
     * @return 实体对象
     */
    AiTextResultEntity getByRowKey(String userId, String dialogId);

    /**
     * 更新任务
     *
     * @param entity 实体对象
     * @return 是否更新成功
     */
    Boolean save(AiTextResultEntity entity);

    /**
     * 新增
     *
     * @param userId        用户id
     * @param dialogId      对话id
     * @param reqParameters 请求参数
     * @param attachment    附件内容
     * @param history       历史对话
     * @return Boolean
     */
    Boolean add(String userId, Long dialogId, String reqParameters, String attachment, String history);

    /**
     * 更新
     *
     * @param userId      用户id
     * @param dialogId    对话id
     * @param historyJson 历史对话
     * @param respJson    响应结果
     * @return Boolean
     */
    Boolean update(String userId, Long dialogId, String historyJson, String respJson);

    /**
     * 更新
     *
     * @param userId   用户id
     * @param dialogId 对话id
     * @param taskId   任务id
     * @return Boolean
     */
    Boolean update(String userId, Long dialogId, String taskId);

    /**
     * 更新hbase对话数据
     *
     * @param userId      用户id
     * @param dialogId    对话id
     * @param historyList 历史对话列表
     * @param respJson    hbase的respParameters字段数据
     * @return Boolean
     */
    Boolean update(String userId, Long dialogId, List<TextModelMessageDTO> historyList, String respJson);

    /**
     * 更新hbase对话数据
     *
     * @param userId      用户id
     * @param dialogId    对话id
     * @param historyList 历史对话列表
     * @param respResult  hbase的respParameters字段数据对象
     * @return 是否更新成功
     * @Author: WeiJingKun
     */
    Boolean update(String userId, Long dialogId, List<TextModelMessageDTO> historyList, AiTextResultRespParameters respResult);
    
    /**
     * 更新响应参数
     * @param userId
     * @param longValue
     * @param respParameters
     */
    void updateRespParameters(String userId, Long longValue, String respParameters);
    
    /**
     * 获取历史对话数据
     *
     * @param userId   用户id
     * @param dialogId 对话id
     * @return String
     */
    String getHistory(String userId, Long dialogId);

    /**
     * 获取历史对话数据
     *
     * @param contentEntity 对话实体对象
     * @return String
     */
    String getHistory(AlgorithmChatContentEntity contentEntity);

    /**
     * 获取历史对话数据
     *
     * @param userId    用户id
     * @param sessionId 会话id，接口请求的入参id，注意一下
     * @return List<TextModelMessageDTO>
     */
    List<TextModelMessageDTO> getHistoryList(String userId, String sessionId);

    /**
     * 获取历史对话数据
     *
     * @param userId    用户id
     * @param sessionId 会话id，接口请求的入参id，注意一下
     * @return List<TextModelMessageDTO>
     */
    List<TextModelMessageDTO> getHistoryList(String userId, Long sessionId);

    /**
     * 根据rowKey列表查询任务结果
     *
     * @param userId       用户id
     * @param dialogIdList 对话id列表
     * @return 实体对象列表
     */
    List<AiTextResultEntity> getByRowKeyList(String userId, List<String> dialogIdList);

    /**
     * 更新添加流式结果
     * 
     * @param userId
     * @param dialogId
     * @param flowResult
     */
	void updateAddFlowResult(String userId, Long dialogId, AiTextResultRespParameters respParameters);

}
