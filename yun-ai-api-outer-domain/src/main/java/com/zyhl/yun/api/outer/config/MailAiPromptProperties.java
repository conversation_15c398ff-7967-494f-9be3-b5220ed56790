package com.zyhl.yun.api.outer.config;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 邮件AI总结、回复指定模型
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "mail-ai-prompt")
@Data
public class MailAiPromptProperties {


    /**
     * 渠道号+提示词key映射文本模型
     */
    private List<MailAiPrompt> modelList;


    /**
     * 提示词+渠道映射模型
     */
    @Data
    public static class MailAiPrompt {

        /**
         * 提示词key
         */
        private List<String> promptKeyList;

        /**
         * 渠道号
         */
        private List<String> channelList;

        /**
         * 大模型编码
         */
        private String modelCode;

        /**
         * 是否启用
         */
        private boolean enable = true;

    }

    /**
     * 根据提示词+渠道号获取模型
     * @param promptKey
     * @param channel
     * @return
     */
    public MailAiPrompt getByPromptKeyAndChannel(String promptKey,String channel) {
        if (!CollUtil.isEmpty(modelList)) {
            for (MailAiPrompt item : modelList) {
                if (item.getPromptKeyList().contains(promptKey) &&
                        item.getChannelList().contains(channel)) {
                    return item;
                }
            }
        }
        return null;
    }



}
