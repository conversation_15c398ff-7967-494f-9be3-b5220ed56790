package com.zyhl.yun.api.outer.domain.entity;


import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * 基础实体
 * <AUTHOR>
 */
@Data
public class BaseEntity {


    /**
     * 请求ID
     */
    @NotNull(message = "请求序列号不可为空")
    private String requestId;

    /**
     * 文件ID
     */
    @NotNull(message = "文件ID不可为空")
    private String fileId;


    /**
     * 传送类型
     * 1——url传送
     * 2——base64传送
     * 3——文件空间
     * 4——共享存储文件路径
     */
    @Range(min = 1, max = 4)
    @NotNull(message = "请求类型不可为空")
    private int sendType;

    /**
     * 文件下载地址
     */
    private String fileUrl;

    /**
     * 图像base64
     */
    private String base64;

    /**
     * 文件信息
     */
    private String fileInfo;

    /**
     * 共享存储文件路径
     */
    private String localPath;



}
