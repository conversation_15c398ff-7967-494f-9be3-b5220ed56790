package com.zyhl.yun.api.outer.domain.dto;

import lombok.Data;

/**
 * 目录配置，内部传输类
 *
 * <AUTHOR>
 */
@Data
public class CatalogConfigDTO {

    /**
     * AI助手目录配置
     */
    private CatalogConfigInfo assistant;

    /**
     * 工具目录配置
     */
    private CatalogConfigInfo tools;

    /**
     * 知识库目录配置
     */
    private CatalogConfigInfo knowledge;


    @Data
    public static class CatalogConfigInfo {
        /**
         * 名称
         */
        private String name;

        /**
         * 路径
         */
        private String path;

        /**
         * 目录id
         */
        private String catalogId;

    }

}