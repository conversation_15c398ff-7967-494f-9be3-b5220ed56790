package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserDriveConfigEntity;

import javax.validation.constraints.NotNull;

/**
 * 用户独立空间配置
 *
 * <AUTHOR>
 */
public interface UserDriveConfigRepository {

    /**
     * 根据用户id获取用户独立空间配置
     *
     * @param userId 用户id
     * @return 实体对象
     */
    UserDriveConfigEntity get(@NotNull String userId);

    /**
     * 添加用户独立空间配置
     *
     * @param entity 实体对象
     */
    void add(@NotNull UserDriveConfigEntity entity);

    /**
     * 更新用户独立空间配置
     *
     * @param entity 实体对象
     */
    void update(@NotNull UserDriveConfigEntity entity);
}
