package com.zyhl.yun.api.outer.vo;

import com.zyhl.hcy.yun.ai.common.model.api.client.ali.vo.GenericSearchVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * className: MultiSearchVO
 * description:
 *
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MultiSearchVO {

    /**
     * 数据来源类型：panta/ali
     */
    private String type;

    /**
     * 搜索结果
     */
    private List<GenericSearchVO> voList;
}