package com.zyhl.yun.api.outer.domain.vo.chat.search.result;

import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.resp.OwnerDriveThumbnailInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 对话信息-搜索结果-知识库资源-列表数据
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PersonalKnowledgeResource implements Serializable {

    private static final long serialVersionUID = 8157400784263934978L;

    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 资源类型
     * 0--文件
     * 1--邮件
     * 2--笔记
     * 3--在线链接
     */
    private Integer resourceType;

    /**
     * 知识库Id
     */
    private String baseId;

    /**
     * 所属知识库的名字，仅知识库文件搜索时返回
     */
    private String baseName;

    /**
     * 名称
     */
    private String name;

    /**
     * 创建时间，RFC 3339格式
     * 例如：2019-08-20T06:51:27.292+08:00
     */
    private String createdAt;

    /**
     * 更新时间，RFC 3339格式
     * 例如：2019-08-20T06:51:27.292+08:00
     */
    private String updatedAt;

    /**
     * AI处理结果
     * -1--处理失败
     * 0--处理中
     * 1--处理成功
     */
    private Integer aiStatus;

    /**
     * 审核状态，默认 0
     * 0：未送审，
     * 1：送审中，
     * 2：通过，
     * 3：未通过
     */
    private Integer auditStatus;

    /**
     * 失败信息，失败时返回
     */
    private String errorMessage;

    /**
     * 在线链接原URL
     */
    private String htmlUrl;

    /**
     * 缩略图信息
     */
    private List<OwnerDriveThumbnailInfo> thumbnailUrls;

    /**
     * 文件特有属性 - 类型，枚举值file/folder
     */
    private String type;

    /**
     * 文件特有属性 - 分类
     * app：安装包；
     * zip：压缩包；
     * image：图片；
     * doc：文档；
     * video：视频；
     * audio：音频;
     * folder：目录；
     * others：其他
     */
    private String category;

    /**
     * 文件特有属性 - 大小，单位：字节
     */
    private Long size;

    /**
     * 文件特有属性 - 文件扩展名，一般是后缀名
     * 注：不区分大小写
     */
    private String fileExtension;

    /**
     * 文件特有属性 - 文件内容hash值，长度64位
     */
    private String contentHash;

    /**
     * 文件特有属性 - 文件内容hash算法名
     * 当前仅支持sha1或者sha256，不区分大小写
     */
    private String contentHashAlgorithm;

}
