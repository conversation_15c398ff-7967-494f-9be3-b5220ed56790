package com.zyhl.yun.api.outer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户类型枚举
 * 
 * <AUTHOR>
 * @date 2025/2/19 09:00
 */
@AllArgsConstructor
@Getter
public enum UserTypeEnum {

	/** 白名单用户 */
	WHITE_USER("white-user", "白名单用户"),

	/** 会员用户 */
	MEMBER("member", "会员用户"),
	
	/** 非会员用户 */
	NOT_MEMBER("non-member", "非会员用户"),

	/** 默认 */
	DEF_MODEL("def-model", "默认"),

	;

	/**
	 * 对应code
	 */
	private final String code;

	/**
	 * 描述
	 */
	private final String desc;

}
