package com.zyhl.yun.api.outer.domain.vo.knowledge;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.dashscope.utils.JsonUtils;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.resp.OwnerDriveThumbnailInfo;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.enums.knowledge.FileAiStatusParamEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileCategoryEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileProcessStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import java.util.List;
import java.util.Objects;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;


@NoArgsConstructor
@Data
public class PersonalKnowledgeResource {
    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 资源类型
     * 0--文件
     * 1--邮件
     * 2--笔记
     * 3--在线链接
     */
    private Integer resourceType;

    /**
     * 知识库Id
     */
    private String baseId;

    /**
     * 父目录id，当父文件为根目录时为"/"
     */
    private String parentFileId;

    /**
     * 名称
     */
    private String name;

    /**
     * 创建时间，RFC 3339格式
     * 例如：2019-08-20T06:51:27.292+08:00
     */
    private String createdAt;

    /**
     * 更新时间，RFC 3339格式
     * 例如：2019-08-20T06:51:27.292+08:00
     */
    private String updatedAt;

    /**
     * AI处理结果
     * -1--处理失败
     * 0--处理中
     * 1--处理成功
     */
    private Integer aiStatus;

    /**
     * 审核状态，默认 0
     * 0：未送审，
     * 1：送审中，
     * 2：通过，
     * 3：未通过
     */
    private Integer auditStatus;

    /**
     * 失败信息，失败时返回
     */
    private String errorMessage;

    /**
     * 在线链接原URL
     */
    private String htmlUrl;

    /**
     * 文件特有属性 - 类型，枚举值file/folder
     */
    private String type;

    /**
     * 文件特有属性 - 分类
     * app：安装包；
     * zip：压缩包；
     * image：图片；
     * doc：文档；
     * video：视频；
     * audio：音频;
     * folder：目录；
     * others：其他
     */
    private String category;

    /**
     * 文件特有属性 - 大小，单位：字节
     */
    private Long size;

    /**
     * 文件特有属性 - 文件扩展名，一般是后缀名
     * 注：不区分大小写
     */
    private String fileExtension;

    /**
     * 文件特有属性 - 文件内容hash值，长度64位
     */
    private String contentHash;

    /**
     * 文件特有属性 - 文件内容hash算法名
     * 当前仅支持sha1或者sha256，不区分大小写
     */
    private String contentHashAlgorithm;

    /**
     * 正文段落列表
     */
    private List<PersonalKnowledgeContentParagraph> paragraphList;

    /**
     * 缩略图信息列表
     */
    private List<OwnerDriveThumbnailInfo> thumbnailUrls;

    public PersonalKnowledgeResource(UserKnowledgeFileEntity entity) {
        this.resourceId = entity.getFileId();
        this.baseId = String.valueOf(entity.getBaseId());
        this.parentFileId = entity.getParentFileId();
        this.name = entity.getFileName();
        this.type = FileTypeEnum.getYunDiskFileType(entity.getFileType());
        this.category = FileCategoryEnum.getYunDiskCategory(entity.getCategory());
        this.createdAt = DateUtil.format(entity.getCreateTime(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
        this.updatedAt = DateUtil.format(entity.getUpdateTime(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
        this.errorMessage = entity.getResultMsg();
        this.size = entity.getFileSize();
        this.fileExtension = entity.getExtension();
        this.contentHash = entity.getHashValue();
        this.contentHashAlgorithm = entity.getHashName();
        this.resourceType = entity.getFromResourceType();
        if (KnowledgeResourceTypeEnum.isHtml(entity.getFromResourceType())) {
            UserKnowledgeFileEntity.HtmlInfo htmlInfo = JsonUtils.fromJson(entity.getFromResource(), UserKnowledgeFileEntity.HtmlInfo.class);
            if (ObjectUtil.isEmpty(this.name) && Objects.nonNull(htmlInfo)) {
                this.name = htmlInfo.getTitle();
            }
            if (Objects.nonNull(htmlInfo)) {
                this.htmlUrl = htmlInfo.getUrl();
            }
        }

        if (FileProcessStatusEnum.isSuccess(entity.getAiStatus())) {
            // 成功
            this.aiStatus = FileAiStatusParamEnum.SUCCESS.getCode();
        } else if (FileProcessStatusEnum.isFail(entity.getAiStatus())) {
            // 失败
            this.aiStatus = FileAiStatusParamEnum.FAIL.getCode();
        } else {
            this.aiStatus = FileAiStatusParamEnum.PROCESSING.getCode();
        }

        this.auditStatus = entity.getAuditStatus();
    }

    public void failed(String reason) {
        this.aiStatus = -1;
        if (CharSequenceUtil.isNotBlank(reason)) {
            this.errorMessage = reason;
        } else {
            this.errorMessage = "哎呀，解析过程出了点小意外，请稍后再试试看";
        }
    }

    public void sensitive() {
        this.aiStatus = -1;
        this.errorMessage = "该文件内容包含敏感信息，请选择其它文件重新上传";
    }

    public void processing() {
        this.aiStatus = 0;
        this.errorMessage = "该文件正在全力解析中，请耐心等待";
    }

    public void succeed() {
        this.aiStatus = 1;
        this.errorMessage = "该文件已解析完成，快来与AI助手对话试试";
    }
}