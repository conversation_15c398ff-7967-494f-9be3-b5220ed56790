package com.zyhl.yun.api.outer.domain.vo.chat.search.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 对话信息-搜索参数-知识库资源搜索接口分页信息-排序查询条件
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KnowledgeBaseResourceSortRange implements Serializable {

    private static final long serialVersionUID = -6929157829955435240L;

    /**
     * 排序字段
     * 上传时间createdAt；
     * 更新时间updatedAt；
     * 相关度 relevancy,仅支持降序；
     * 名称 name；
     * 文件类型 category;
     * 文件大小 size；
     */
    private String orderBy;

    /**
     * 排序方向
     * true：降序
     * false：升序
     */
    private Boolean orderDirection;

}
