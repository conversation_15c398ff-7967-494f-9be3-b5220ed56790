package com.zyhl.yun.api.outer.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 视频AI看状态枚举
 * 
 * <AUTHOR>
 * @date 2025-04-15
 */
@AllArgsConstructor
@Getter
public enum VideoAIStatusEnum {

	/**
	 * 未注册
	 */
    NOT_REGISTER("未注册", -1, -1),
    /**
     * 未开始
     */
    NOT_STARTED("未开始", 0, 1),
    /**
     * 视频同步中
     */
    SYNCHRONIZATION_IN_PROGRESS("视频同步中", 1, 1),
    /**
     * 视频同步完成
     */
    SYNCHRONIZATION_COMPLETED("视频同步完成", 2, 1),
    /**
     * 合集生成完成
     */
    COLLECTION_COMPLETED("合集生成完成", 3, 1),
    /**
     * 刮削中
     */
    SCRAPING_IN_PROGRESS("刮削中", 4, 1),
    /**
     * 刮削完成
     */
    SCRAPING_COMPLETED("刮削完成", 5, 2);

    /**
     * 描述
     */
    private final String desc;

    /**
     * 状态
     */
    private final Integer status;

    /**
     * 对应code   -1：未授权
     *   1：已授权，但是在处理中
     *   2：已授权，且处理完成
     */
    private final Integer code;



    public static Integer getByStatus(Integer status) {
        for (VideoAIStatusEnum value : VideoAIStatusEnum.values()) {
            if (value.getStatus().equals(status)) {
                return value.getCode();
            }
        }
        return  NOT_REGISTER.code;
    }
}
