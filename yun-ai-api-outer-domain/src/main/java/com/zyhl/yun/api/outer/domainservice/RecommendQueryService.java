package com.zyhl.yun.api.outer.domainservice;


import com.zyhl.yun.api.outer.domain.vo.QueryRecommendListVO;

import java.util.concurrent.Future;

/**
 * 问题推荐方法接口实现类
 *
 * <AUTHOR>
 * @date 2024/7/17 16:08
 */
public interface RecommendQueryService {

    /**
     * 获取问题推荐列表集合VO（并发处理）
     *
     * @param dialogueId 对话Id
     * @param dialogue   对话内容
     * @return 问题推荐列表集合VO
     */
    Future<QueryRecommendListVO> getRecommendQueryFuture(Long dialogueId, String dialogue);
}
