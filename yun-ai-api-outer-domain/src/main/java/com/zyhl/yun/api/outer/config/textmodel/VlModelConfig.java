package com.zyhl.yun.api.outer.config.textmodel;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * 视觉大模型配置类
 *
 * <AUTHOR>
 * @date 2025-02-26 14:48
 */
@Configuration
@ConfigurationProperties(prefix = "text-model.vlm-chat")
@Data
public class VlModelConfig {

    /**
     * v1默认的：对话+图片的视觉大模型类型配置
     */
    private BusinessModelConfig dialogueAndImageConfig;

    /**
     * 默认的：对话+图片的视觉大模型类型配置
     */
    private BusinessModelConfig defaultDialogueImageConfig;

    /**
     * ai拍照解题
     */
    private BusinessModelConfig aiPhotoSolveProblemsConfig;

    /**
     * ai拍照解题推荐
     */
    private BusinessModelConfig aiPhotoSolveRecommendConfig;

    /**
     * ai拍照翻译
     */
    private BusinessModelConfig aiPhotoTranslateConfig;

    /**
     * ai拍照翻译推荐
     */
    private BusinessModelConfig aiPhotoTransRecommendConfig;

    /**
     * ai拍照问答
     */
    private BusinessModelConfig aiPhotoQaConfig;

    @Data
    public static class BusinessModelConfig {

        /**
         * 所有入参最大长度（字符字数），默认空则不加历史
         */
        private Integer maxLength;

        /**
         * 模型编码（参照TextModelEnum中的VL_MODEL_XXX枚举的code值）
         */
        private String modelCode;

        /**
         * 系统提示词
         */
        private String systemPrompt;

        /**
         * 用户提示词
         */
        private String userPrompt;

        /**
         * 最大生成 token 数，默认值为 512，可根据需求调整 1 < x < 4096
         */
        private Integer maxTokens = null;

        /**
         * 温度参数，默认0.7f
         */
        private Float temperature = null;

        /**
         * topP值，默认0.7f
         */
        private Float topP = null;

        /**
         * 频率惩罚参数，默认0f
         */
        private Float frequencyPenalty = null;
        /**
         * 采样时考虑的最高概率词汇数量，默认50f
         */
        private Float topK = null;

        /**
         * 压缩图片宽度，默认20000*20000
         */
        private Integer destWidth = 20000;
        /**
         * 压缩图片高度，20000*20000
         */
        private Integer destHeight = 20000;
        /**
         * 压缩比例，默认0.9
         */
        private double accuracy = 0.9D;
        /**
         * 图片大小，单位字节 默认10M
         */
        private Long destSize = 10 * 1024 * 1024L;
    }

}
