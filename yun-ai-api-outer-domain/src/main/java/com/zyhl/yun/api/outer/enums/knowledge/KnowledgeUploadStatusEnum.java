package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.Getter;
import java.util.Collections;
import java.util.Arrays;
import java.util.List;

/**
 * 知识库导入任务状态枚举
 * <AUTHOR>
 */
@Getter
public enum KnowledgeUploadStatusEnum {
    /**
     * 未处理
     */
    NOT_PROCESSED(0, "未处理"),
    PROCESSING(1, "处理中"),
    SUCCESS(2, "成功"),
    FAIL(3, "失败"),
    REQ_FAIL(-1,"任务失败");


    /**
     * 状态
     */
    private final Integer status;
    /**
     * 描述
     */
    private final String remark;

    KnowledgeUploadStatusEnum(Integer status, String remark) {
        this.status = status;
        this.remark = remark;
    }

    /**
     * 将请求状态转换为数据库状态
     * @param requestStatus 请求状态 (-1: 失败, 0: 处理中, 1: 成功)
     * @return 数据库状态列表 处理中对应未处理状态
     */
    public static List<Integer> convertRequestStatusToDbStatus(Integer requestStatus) {
        if (requestStatus == null) {
            return null;
        }
        switch (requestStatus) {
            case -1:
                return Collections.singletonList(FAIL.getStatus());
            case 0:
                return Arrays.asList(NOT_PROCESSED.getStatus(), PROCESSING.getStatus());
            case 1:
                return Collections.singletonList(SUCCESS.getStatus());
            default:
                return null;
        }
    }

    /**
     * 将数据库状态转换为请求状态
     * @param dbStatus 数据库状态
     * @return 请求状态
     */
    public static Integer convertDbStatusToRequestStatus(Integer dbStatus) {
        if (dbStatus == null) {
            return null;
        }
        switch (dbStatus) {
            // FAIL
            case 3:
                return -1;
            // PROCESSING
            case 0:
                // 处理中
            case 1:
                return 0;
            // SUCCESS
            case 2:
                return 1;
            default:
                return null;
        }
    }

} 