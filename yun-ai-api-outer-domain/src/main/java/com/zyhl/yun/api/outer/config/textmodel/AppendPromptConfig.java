package com.zyhl.yun.api.outer.config.textmodel;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 附加提示词
 *
 * <AUTHOR>
 * @since 5月7 2025
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "text-model.append")
public class AppendPromptConfig {

	/**
	 * 重新生成提示词
	 */
	private String regeneratePrompt = "重新生成新的内容";
}
