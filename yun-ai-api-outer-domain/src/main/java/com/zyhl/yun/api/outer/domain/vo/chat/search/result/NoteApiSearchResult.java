package com.zyhl.yun.api.outer.domain.vo.chat.search.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 对话信息-搜索结果-笔记-列表数据
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NoteApiSearchResult implements Serializable {

    /** 笔记id */
    private String noteId;

    /** 笔记所属笔记本id */
    private String noteBookId;

    /** 笔记所属笔记本名称 */
    private String noteBook;

    /** 笔记标题 */
    private String title;

    /**
     * 笔记正文摘要，命中关键字的前10个字+后n个字。
     * 注意：命中的正文关键字后台会使用keywordsTag提醒（见返回样例），端侧做高亮显示后去除前后keywordsTag标签呈现给用户。
     */
    private String summary;

    /** 所有附件类型的集合 */
    private List<String> attachmentTypes;

    /** 创建时间，端侧传时间戳（ms） */
    private String createTime;

    /** 更新时间，端侧传时间戳（ms） */
    private String updateTime;

    /** 提醒时间，时间戳(ms) */
    private String remindTime;

    /**
     * 笔记提醒类型，来自笔记对象中的remindtype，短信发送周期
     * 0：无提醒
     * 1：仅一次
     * 2：每天
     * 3：工作日
     * 4：每周
     * 5：每月
     * 6：每年
     */
    private Integer remindType;

    /** 占位图url，取笔记中的第一张图片 */
    private String thumbURL;

    /** 排序字段 */
    private Integer sortOrder;

    /**
     * 是否置顶，创建时候一般传0
     * 0：非置顶
     * 1：置顶
     */
    private String topmost;

    /**
     * 笔记类型，来自笔记对象中的expands
     * 0或者空：普通类型
     * 1：语音类型
     */
    private Integer noteType;

    /** 相关录音字段，来自笔记对象中的audioInfo */
    private Audio audioInfo;

    /** 旧版笔记是否精转  0或者空：未精转  1：精转中  2：已精转 */
    private Integer magicTransferFlag;

}
