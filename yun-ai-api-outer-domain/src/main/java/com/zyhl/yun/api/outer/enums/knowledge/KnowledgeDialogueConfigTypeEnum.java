package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识库对话配置类型
 *
 * <AUTHOR>
 * @date 2025-05-02
 */
@Getter
@AllArgsConstructor
public enum KnowledgeDialogueConfigTypeEnum {

	/**
	 * 召回配置
	 */
    RECALL(1, "召回配置"),
    
    /**
     * 重排配置
     */
    RERANK(2, "重排配置"),
    
    /**
     * 对话配置
     */
    DIALOGUE(3, "对话配置"),

    ;

    private final Integer type;
    private final String desc;


    public static boolean isRecall(Integer type) {
        return RECALL.getType().equals(type);
    }

    public static boolean isRerank(Integer type) {
        return RERANK.getType().equals(type);
    }

    public static boolean isDialogue(Integer type) {
        return DIALOGUE.getType().equals(type);
    }

}
