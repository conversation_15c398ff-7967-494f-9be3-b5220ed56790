package com.zyhl.yun.api.outer.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;

import java.util.List;

import lombok.Data;

/**
 * 算法任务结果VO
 *
 * <AUTHOR>
 */
@Data
public class AlgorithmTaskResultVO {

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 排队位置
     */
    private Long queueOffset;

    /**
     * 任务状态
     *
     * @see TaskStatusEnum
     */
    private Integer status;

    /**
     * 异步算法任务结果
     */
    private List<AsyncTaskResult> resultList;

    /**
     * 错误的时候过渡字段
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private AiResultCode aiResultCode;

    /**
     * 业务参数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String businessParam;
}
