package com.zyhl.yun.api.outer.repository;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.PersonalKnowledgeImportTaskEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeUploadEntity;

import java.text.ParseException;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface AlgorithmUserKnowledgeUploadRepository {

    /**
     * 根据用户ID和任务ID列表逻辑删除记录
     * 
     * @param userId 用户ID
     * @param ids 任务ID列表
     * @return 成功删除的记录数
     */
    int deleteByUserIdAndId(String userId, List<Long> ids);

    /**
     * 根据用户ID和知识库ID分页查询导入任务
     * @param userId 用户ID
     * @param baseId 知识库ID
     * @param statusArray 任务状态数组，不填默认返回-1和0的数据
     * @param pageInfo 分页参数
     * @return 导入任务列表
     */
    PageInfo<PersonalKnowledgeImportTaskEntity> findByUserIdAndBaseId(String userId, String baseId, Integer[] statusArray,
                                                                      PageInfoDTO pageInfo);


    /**
     * 根据用户ID、知识库ID、资源类型和文件ID列表查询导入任务
     * @param userId 用户ID
     * @param baseId 知识库ID
     * @param resourceType 资源类型
     * @param fileIdList 文件ID列表
     * @return 导入任务列表
     */
    List<PersonalKnowledgeImportTaskEntity> findByUserIdAndBaseIdAndFileId(String userId, String baseId, Integer resourceType, List<String> fileIdList);

    /**
     * 根据用户ID、知识库ID、资源类型和文件ID列表查询未成功的导入任务
     * @param userId 用户ID
     * @param baseId 知识库ID
     * @param resourceType 资源类型
     * @param fileIdList 文件ID列表
     * @return 导入任务列表
     */
    List<PersonalKnowledgeImportTaskEntity> findNotSuccessByFileIds(String userId, String baseId, Integer resourceType, List<String> fileIdList);

    /**
     * 批量插入导入任务
     * @param entities 导入任务列表
     * @return 插入数量
     */
    boolean insertBatch(List<PersonalKnowledgeImportTaskEntity> entities);

    /**
     * 批量插入文件
     *
     * @param entityList 文件实体集合
     * @return int
     */
    boolean batchAdd(@NotNull List<UserKnowledgeUploadEntity> entityList);

    int add(UserKnowledgeUploadEntity entity);

    PersonalKnowledgeImportTaskEntity findById(Long aLong);

    void updateById(PersonalKnowledgeImportTaskEntity taskEntity);

    List<PersonalKnowledgeImportTaskEntity> selectHtmlResource(String userId, Long baseId,List<String> urlList);

    void updateRetryById(UserKnowledgeUploadEntity uploadEntity);

    List<PersonalKnowledgeImportTaskEntity> findListByParam(String userId, String baseId, Integer resourceType, List<String> fileIdList, Integer status);

    /**
     * 查询重复导入的个人云文件
     * @param userId
     * @param baseId
     * @param fileIdList
     * @return
     */
    Set<String> findRepeatImportPersonalCloudFile(String userId, String baseId, List<String> fileIdList);
}
