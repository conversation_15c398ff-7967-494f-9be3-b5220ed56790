package com.zyhl.yun.api.outer.domain.vo.chat.search.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;

/**
 * 对话信息-搜索参数-文件
 * @Author: WeiJingKun
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchFileParam extends SearchCommonParam implements Serializable {

    /**
     * 手机号
     * 如果手机号带值那边就是旧底座
     * 反之就是认定新底座
     */
    private Long owner;

    /**
     * 类别
     * @see com.zyhl.yun.api.outer.enums.chat.search.SearchFileParamTypeEnum
     */
    @Range(min = 0,message = "type最小0")
    private Integer type;

    /**
     * 类别列表
     * @see com.zyhl.yun.api.outer.enums.chat.search.SearchFileParamTypeEnum
     */
    private List<Integer> typeList;

    /** 关键字集合-最大支持10条，文档支持文件名+正文搜索，其他文件只搜索文件名 */
    private List<String> nameList;

    /** 地理标签集合-最大支持10条 */
    private List<String> addressList;

    /** 事物标签集合-最大支持10条 */
    private List<String> thingList;

    /**
     * 文档类型：
     * doc
     * xls
     * ppt
     * pdf
     * txt
     */
    private String docType;

    /** 后缀名-最大支持10条，与docType互斥；当type=5时，该参数无效 */
    private List<String> suffixList;

    /**
     * 搜索时间范围列表，每个时间范围均为或的关系
     * 如果填了，FileTimeRange中的两个字段就是必填
     */
    private List<@Valid SecondTimeRange> timeList;

    /** 搜索平台分页信息（默认查总数） */
    @Builder.Default
    private @Valid FilePageInfo pageInfo = new FilePageInfo(10, null, null, 1);

    /**
     * 是否启用智能分析搜索，默认为false
     * true-开启；false-关闭
     */
    @Builder.Default
    private Boolean enableIntelligentSearch = false;

    /**
     * 是否打开文档全文检索，默认为true
     * true-开启；false-关闭
     */
    @Builder.Default
    private Boolean enableFullTextSearch = true;

}
