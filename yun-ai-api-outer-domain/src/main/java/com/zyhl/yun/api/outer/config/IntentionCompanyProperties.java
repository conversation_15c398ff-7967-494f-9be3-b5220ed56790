package com.zyhl.yun.api.outer.config;


import com.zyhl.yun.api.outer.domain.entity.centertask.CenterTaskCreateEntity;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "intention")
public class IntentionCompanyProperties {

    /**
     * V1配置图片工具对应的厂商 key: DialogueIntentionEnum.instruction value: 厂商编码，多个英文逗号隔开
     */
    private Map<String, String> company;

    /**
     * V2配置图片工具对应的厂商 key: DialogueIntentionEnum.instruction value: 厂商编码，多个英文逗号隔开
     */
    private Map<String, DeployProperties> tools;


    /**
     * 保存到个人云的路径。例如：/我的应用收藏/AI助手
     */
    private String yunPath = "";

    @Data
    public static class DeployProperties {

        /**
         * 厂商
         */
        private List<String> supplierTypes;

        /**
         * 生成的图片数量
         */
        private Integer number;

        /**
         * 拓展字段，如漫画风风格style, 扩图的比例
         */
        private String extendField;
    }

    /**
     * 获取图片工具配置的厂商
     *
     * @param intention 意图
     * @return 厂商
     */
    public List<String> getSupplierTypes(DialogueIntentionEnum intention) {
        List<String> supplierTypes = new ArrayList<>();
        String companyConfig = company.get(intention.getInstruction());
        if (CharSequenceUtil.isNotEmpty(companyConfig)) {
            supplierTypes.addAll(Arrays.asList(companyConfig.split(",")));
        }

        return supplierTypes;
    }

    /**
     * 设置图片工具配置
     *
     * @param intention              the intention
     * @param centerTaskCreateEntity the center task create entity
     * <AUTHOR>
     * @date 2025-4-19 17:27
     */
    public void setDeployProperties(DialogueIntentionEnum intention, CenterTaskCreateEntity centerTaskCreateEntity) {
        if (Objects.isNull(centerTaskCreateEntity)) {
            return;
        }
        if (Objects.nonNull(tools) && Objects.nonNull(tools.get(intention.getInstruction()))) {
            DeployProperties deployProperties = tools.get(intention.getInstruction());
            centerTaskCreateEntity.setSupplierTypes(deployProperties.getSupplierTypes());
            centerTaskCreateEntity.setNumber(deployProperties.getNumber());
            centerTaskCreateEntity.setExtendField(deployProperties.getExtendField());
        }
    }

}
