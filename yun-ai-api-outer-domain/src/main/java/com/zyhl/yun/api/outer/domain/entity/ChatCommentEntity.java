package com.zyhl.yun.api.outer.domain.entity;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/1/6 16:37
 */
@Data
@Builder
public class ChatCommentEntity implements Serializable {


    /**
     *  用户id
     */
    private String userId;


    /**
     * 会话Id
     */
    private Long sessionId;

    /**
     * 当前对话Id（任务id）
     */
    private Long dialogueId;

    /**
     * 是否喜欢 0:不喜欢，1:喜欢
     *
     * @see com.zyhl.yun.api.outer.enums.LikeEnum
     */
    private Integer likeComment;

    /**
     * 默认评论
     */
    private String defaultComment;

    /**
     * 自定义评论
     */
    private String customComment;

    /**
     * 模型类型
     */
    private String modelType;

}
