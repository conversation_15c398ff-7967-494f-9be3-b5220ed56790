package com.zyhl.yun.api.outer.domain.vo;

import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDrivePartInfoDTO;
import lombok.Data;

import java.util.List;

/**
 * className: GetSharpUploadUrlReqVO
 * description: 获取文件分片上传地址VO
 *
 * <AUTHOR>
 * @date 2025/4/17
 */
@Data
public class GetSharpUploadUrlReqVO {

    /**
     * 文件id
     */
    private String fileId;

    /**
     * 上传id
     */
    private String uploadId;

    /**
     * 分片信息数组
     */
    private List<OwnerDrivePartInfoDTO> partInfos;
}