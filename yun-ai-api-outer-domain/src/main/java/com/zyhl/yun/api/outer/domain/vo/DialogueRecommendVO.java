package com.zyhl.yun.api.outer.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 对话结果推荐对象VO
 *
 * <AUTHOR>
 * @date 2024/6/2 22:59
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DialogueRecommendVO {

    /**
     * 意图推荐列表
     */
    private List<IntentionRecommendVO> intentionList;

    /**
     * 内容推荐列表
     */
    private List<ContentRecommendVO> contentList;

    /**
     * 上下文推荐列表（需要存默认值）
     */
    @Builder.Default
    private List<ContextRecommendVO> contextList = new ArrayList<>();

    /**
     * 提问语句推荐列表
     */
    private List<QueryRecommendVO> queryList;

    /**
     * 提示词指令推荐列表
     */
    private List<PromptRecommendVO> promptList;
    
    /**
     * 相册推荐列表
     */
    private List<AlbumInfoVO> albumList;

    /**
     * 全网搜推荐列表
     */
    private List<AllNetworkSearchRecommendVO> allNetworkSearchList;
    
    /**
     * 工具推荐列表
     */
    private List<ToolRecommendVO> toolList;

}
