package com.zyhl.yun.api.outer.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 云邮AI助手1.1版本重构报名，报名返回结果
 *
 * <AUTHOR>
 */
@Data
public class AiRegisterVO implements Serializable {

    /**
     * 授权状态
     * 数据库：0：已授权 1：未授权（关闭授权）
     * 接口响应：1：已授权 -1：未授权
     */
    private Integer authStatus = -1;

    /**
     * 目录路径(0或1才会有值)
     */
    private String path;

    public AiRegisterVO() {

    }

    public AiRegisterVO(Integer authStatus, String path) {
        this.authStatus = authStatus;
        this.path = path;
    }


    /**
     * 已授权
     *
     * @return
     */
    public AiRegisterVO register(String path) {
        this.authStatus = 1;
        this.path = path;
        return this;
    }


}
