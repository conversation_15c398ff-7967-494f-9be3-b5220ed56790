package com.zyhl.yun.api.outer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * mq生产者配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "rocketmq.producer")
public class RocketmqProducerProperties {

    /**
     * 用户上传知识库触发延迟消息-查询转存任务状态
     */
    private ProducerConfig personalKnowledgeTransTask;

    /**
     * 用户上传邮件/笔记/网页到个人知识库，发起向量化提取任务
     */
    private ProducerConfig personalKnowledgeDispatchTask;

    /**
     * 个人云目录转发/笔记、网页、邮箱文件上传MQ
     */
    private ProducerConfig personalKnowledgeCategoryTransTask;

    /**
     * 知识库文件删除MQ
     */
    private ProducerConfig personalKnowledgeFileDeleteTask;

    // ------------------------------------- //

    @Data
    public static class ProducerConfig {
        /**
         * 消息队列组
         */
        private String groupId;
        /**
         * 主题
         */
        private String topic;
        /**
         * 标签
         */
        private String tag;
        /**
         * 延迟时间（毫秒）
         */
        private long delayTimeMillis = 0;
    }

}
