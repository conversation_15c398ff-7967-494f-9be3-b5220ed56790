package com.zyhl.yun.api.outer.config;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 推荐 提示词模板配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "recommend-prompt-template")
public class RecommendPromptTemplateProperties {

    /**
     * 默认千问大模型
     */
	private static final String DEFAULT_MODEL_CODE = "qwen";

    /**
     * 附加默认提示词
     */
    private static final String DEFAULT_APPEND_PROMPT = "#注意:一定要用正确的json格式返回,不要输出任何其他内容,包括【```json】和【```】";

	// -------------------- 推荐提问语句 提示词模板 -------------------- //

    private QueryTemplate queryTemplate;

    private String defaultAppendPrompt = DEFAULT_APPEND_PROMPT;

    @Data
    public static class QueryTemplate {
        /**
         * 大模型编码
         */
        private String modelCode = DEFAULT_MODEL_CODE;
        /**
         * 提示词模板
         */
        private String template;
    }

    // -------------------- 推荐提问语句 提示词模板 -------------------- //

    // -------------------- 多意图推荐语句 提示词模板 -------------------- //

    private IntentionTemplate intentionTemplate;

    public TemplateConfig getTemplateConfig(String intentionCode) {
        if (intentionTemplate == null || intentionTemplate.getTemplateList() == null) {
            return null;
        }
        for (TemplateConfig templateConfig : intentionTemplate.getTemplateList()) {
            if (intentionCode.equals(templateConfig.getIntention())) {
                return templateConfig;
            }
        }
        return null;
    }

    /**
     * 根据意图编码获取提示词模板
     *
     * @param intentionCode
     * @return
     */
    public String getTemplateByIntention(String intentionCode) {
        TemplateConfig config = getTemplateConfig(intentionCode);
        return config == null ? "" : CharSequenceUtil.nullToEmpty(config.getTemplate());
    }

    public String getLinkUrlByIntention(String intentionCode) {
        TemplateConfig config = getTemplateConfig(intentionCode);
        return getLinkUrlByIntention(config);
    }
    
    public String getLinkUrlByIntention(TemplateConfig config) {
        return config == null ? "" : isInner() ? CharSequenceUtil.nullToEmpty(config.getLinkUrlInner()) : CharSequenceUtil.nullToEmpty(config.getLinkUrlOuter());
    }

    private ClientTypeProperties clientTypeProperties;

    /**
     * 端内判断
     *
     * @return
     */
    private boolean isInner() {
        if (clientTypeProperties == null) {
            clientTypeProperties = SpringUtil.getBean(ClientTypeProperties.class);
        }
        return clientTypeProperties.isInner(RequestContextHolder.getClientType());
    }

    /**
     * 判断多意图推荐是否使用统一提示词模板判断
     *
     * @return
     */
    public boolean isIntentionUnifiedTemplate() {
        if (null == intentionTemplate) {
            return false;
        }
        return StringUtils.isNotBlank(intentionTemplate.getUnifiedTemplate());
    }

    @Data
    public static class IntentionTemplate {
        private String modelCode = DEFAULT_MODEL_CODE;
        /**
         * 如果使用统一模板，template-list模板不用，但是template-list对应intention配置copy的还是需要使用
         */
        private String unifiedTemplate;
        private List<TemplateConfig> templateList;
    }

    @Data
    public static class TemplateConfig {
        /**
         * 意图编码
         */
        private String intention;
        /**
         * 固定语句推荐（存在该配置，不执行大模型推荐）
         */
        private String copy;
        /**
         * 提示词模板
         */
        private String template;
        /**
         * 图片意图链接地址 端内地址
         */
        private String linkUrlInner;
        /**
         * 图片意图链接地址 端外地址
         */
        private String linkUrlOuter;
    }

    // -------------------- 多意图推荐语句 提示词模板 -------------------- //
}
