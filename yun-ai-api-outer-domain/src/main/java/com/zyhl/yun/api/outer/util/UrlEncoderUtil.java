package com.zyhl.yun.api.outer.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;

/**
 * url编码工具类
 * 
 * <AUTHOR>
 * @date 2025-06-26 14:28
 */
public class UrlEncoderUtil {

	/**
	 * 使用Hutool对URL的全路径和参数部分进行编码
	 * 
	 * @param url 原始URL（可能包含未编码的路径和参数）
	 * @return 编码后的完整URL
	 */
	public static String encodeFullUrl(String url) {
		try {
			if (UrlEncodingChecker.isUrlEncoded(url)) {
				return url;
			}
			// 分离路径和查询参数
			String[] parts = url.split("\\?", 2);
			String path = parts[0];
			String query = parts.length > 1 ? parts[1] : "";

			// 编码路径部分（保留协议、域名、斜杠）
			String encodedPath = URLUtil.encode(path)
					// 空格编码为%20而非+
					.replace("+", "%20")
					// 保留路径中的斜杠
					.replace("%2F", "/");

			// 编码查询参数部分
			String encodedQuery = encodeQueryParams(query);

			// 组合完整URL
			return StrUtil.isEmpty(encodedQuery) ? encodedPath : encodedPath + "?" + encodedQuery;
		} catch (Exception e) {
			throw new RuntimeException("URL编码失败", e);
		}
	}

	/**
	 * 编码查询参数部分（支持键值对）
	 * 
	 * @param query 原始查询字符串（如 "q=a&b=c"）
	 * @return 编码后的查询字符串
	 */
	private static String encodeQueryParams(String query) {
		if (StrUtil.isEmpty(query)) {
			return "";
		}
		String[] params = query.split("&");
		StringBuilder encodedQuery = new StringBuilder();
		for (String param : params) {
			String[] keyValue = param.split("=", 2);
			if (keyValue.length == 0) {
				continue;
			}

			String key = URLUtil.encode(keyValue[0]);
			String value = keyValue.length > 1 ? URLUtil.encode(keyValue[1]) : "";

			if (encodedQuery.length() > 0) {
				encodedQuery.append("&");
			}
			encodedQuery.append(key);
			if (!value.isEmpty()) {
				encodedQuery.append("=").append(value);
			}
		}
		return encodedQuery.toString();
	}

//	public static void main(String[] args) {
//		String url1 = "https://example.com/搜索?q=a&b=c&page=1";
//		String encodedUrl1 = encodeFullUrl(url1);
//		System.out.println("原始URL: " + url1);
//		System.out.println("编码后URL: " + encodedUrl1);
//		
//		String url2 = "https://test.yun.139.com/ai/ai-test/aippt/web/?taskid=621&token=PA71VruHEq9rKUtVGdf69fK6chEBqvWo6DgEctpdcFryaVcsKygSM92awXlI95sbs8ROZWG89Cw8PCb0PIXyEjhDDugDSRHbA4N54ne3mxQil%2B%2FubdzA05fVDxgY5WJq1AiFHDREnWJUmOjjX24tXcxn%2FxuVMkNOQKJ18JkzpDjB1JSFkaioxrODDwNbQiKqtBxDZt8uCvH5aE%2BgsZdeTiehfH8jYIKgb1Flg9PeBpY3cedoyKe3Tc%2FPVsz8heq4uhK0m7cKbNMhCeXMlobJWkMDA%2BbX%2Bwm%2BzFkp1sQpVuQ7xhvYmxB3xBroqPw8M%2BeKF0UaY0K0zUvdWr51gK0zV3H%2Fj4vn1%2Fd0n1ZDKToQiFqowCRV64NyrDRp4RWfbMN1oZ7xqXQpjD3Qh2cuPbCQyQ%3D%3D";
//		String encodedUrl2 = encodeFullUrl(url2);
//		System.out.println("原始URL: " + url2);
//		System.out.println("编码后URL: " + encodedUrl2);
//	}
}