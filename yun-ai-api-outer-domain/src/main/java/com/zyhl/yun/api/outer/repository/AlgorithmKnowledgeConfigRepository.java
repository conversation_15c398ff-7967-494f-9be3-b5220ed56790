package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.knowledge.AlgorithmKnowledgeConfigEntity;

import java.util.List;

/**
 * 公共知识库配置表-Repository
 *
 * @Author: WeiJingKun
 */
public interface AlgorithmKnowledgeConfigRepository {

    /**
     * 根据id，获取公共知识库配置
     *
     * @param id id参数
     * @return 公共知识库配置
     */
    AlgorithmKnowledgeConfigEntity queryById(String id);

    /**
     * 根据id，获取公共知识库配置
     *
     * @param ids id参数
     * @return
     */
    List<AlgorithmKnowledgeConfigEntity> selectByIds(List<String> ids);

}
