package com.zyhl.yun.api.outer.config;


import cn.hutool.core.collection.CollUtil;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 文件后缀配置
 */
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "search-suffix")
public class SearchSuffixProperties {

    /**
     * 文档后缀映射
     */
    private Map<String, List<String>> mapping;

    /**
     * 文档后缀映射
     * @Author: WeiJingKun
     *
     * @param suffixList
     * @return java.util.List<java.lang.String>
     */
    public List<String> mapingSuffixList(List<String> suffixList) {
        List<String> resultList = new ArrayList<>();
        if(CollUtil.isNotEmpty(suffixList)){
            suffixList.forEach(suffix -> {
                if (mapping.containsKey(suffix)) {
                    resultList.addAll(mapping.get(suffix));
                } else {
                    resultList.add(suffix);
                }
            });
        }
        return resultList;
    }

}
