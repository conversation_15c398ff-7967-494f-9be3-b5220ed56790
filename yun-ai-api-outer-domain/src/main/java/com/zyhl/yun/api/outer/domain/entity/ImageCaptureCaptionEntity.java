package com.zyhl.yun.api.outer.domain.entity;


import lombok.Data;

/**
 * 图片识别
 *
 * <AUTHOR>
 */
@Data
public class ImageCaptureCaptionEntity {
    /**
     * 请求ID。
     * 该字段存储请求的唯一标识符。
     */
    private String requestId;

    /**
     * 传送类型。
     * 该字段指示传送数据的方式，可选值包括：
     * 1 - URL传送
     * 2 - Base64传送
     * 3 - 文件ID
     * 4 - 共享存储的文件路径
     */
    private Integer sendType;

    /**
     * 文件ID。
     * 该字段存储文件的唯一标识符。
     */
    private String fileId;

    /**
     * Base64编码。
     * 该字段存储文件的Base64编码字符串。
     */
    private String base64;

    /**
     * 文件信息。
     * 该字段存储文件的详细信息。
     */
    private FileEntity fileInfo;

    /**
     * 共享存储的文件路径。
     * 该字段存储文件在共享存储中的路径。
     */
    private String localPath;

    /**
     * 阈值。
     * 该字段存储用于过滤结果的阈值。
     */
    private Double threshold;

    /**
     * 返回文件数量。
     * 该字段存储返回的文件数量。
     */
    private Integer topK;


}
