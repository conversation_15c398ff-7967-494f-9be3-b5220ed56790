package com.zyhl.yun.api.outer.domain.vo.chat.search.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 对话信息-搜索参数-热门圈子
 *
 * @Author: WeiJingKun
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchRecommendGroupParam extends SearchCommonParam implements Serializable {

    /**
     * 搜索关键字
     */
    private String keyword;

    /**
     * 搜索平台分页信息（默认查总数）
     */
    @Builder.Default
    private FilePageInfo pageInfo = new FilePageInfo(200, null, null, 1);

}
