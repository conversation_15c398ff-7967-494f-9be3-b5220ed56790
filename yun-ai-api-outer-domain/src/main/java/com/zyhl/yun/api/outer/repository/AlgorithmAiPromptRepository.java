package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiPromptEntity;

import java.util.List;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.repository.AlgorithmAiPromptRepository} <br>
 * <b> description:</b>
 * AI提示词模板接口类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-06-28 10:17
 **/
public interface AlgorithmAiPromptRepository {

    /**
     * 根据promptKey查询关键字提示词
     *
     * @param promptKey the promptKey
     * @return {@link List<AlgorithmAiPromptEntity>}
     * <AUTHOR>
     * @date 2024-6-28 11:36
     */
    List<AlgorithmAiPromptEntity> queryByPromptKey(String promptKey);

}

