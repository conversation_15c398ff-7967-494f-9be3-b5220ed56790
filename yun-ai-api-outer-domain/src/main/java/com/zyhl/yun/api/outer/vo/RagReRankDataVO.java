package com.zyhl.yun.api.outer.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 检索重排结果VO
 *
 * <AUTHOR>
 * @date 2024/9/9 14:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RagReRankDataVO {


    /**
     * 要重新排序的文档对象，text以外的所有其他字段将保留在响应中。
     */
    private DocumentDataVO document;

    /**
     * 文档对象VO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DocumentDataVO {

        /**
         * 文档ID
         */
        private String fileId;

        /**
         * 分段ID
         */
        private String segmentId;

        /**
         * 分段内容
         */
        private String text;

        /**
         * 相关性分数被归一化为在[0，1]范围内。分数接近1表示与查询高度相关，分数接近0表示相关性低。
         * 搜索重排接口返回
         */
        private Float score;

        /**
         * 该分块被召回的总次数（RAG2.2新增）
         */
//        private Integer recallCount;

    }
}
