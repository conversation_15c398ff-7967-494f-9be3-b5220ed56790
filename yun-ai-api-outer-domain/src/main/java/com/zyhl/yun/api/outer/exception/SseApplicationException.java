package com.zyhl.yun.api.outer.exception;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.enums.CommonResultCode;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * <p>
 * SSE流式接口使用异常类
 * 2024年4月20日
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SseApplicationException extends RuntimeException {

    private AbstractResultCode exceptionEnum;

    private String code;

    private String message;

    public SseApplicationException(ResultCodeEnum commonResultCode) {
        super(commonResultCode.getResultMsg());
        this.exceptionEnum = commonResultCode;
        this.code = commonResultCode.getResultCode();
        this.message = commonResultCode.getResultMsg();
    }

    public SseApplicationException(CommonResultCode exceptionEnum) {
        super(exceptionEnum.getResultMsg());
        this.exceptionEnum = exceptionEnum;
        this.code = exceptionEnum.getResultCode();
        this.message = exceptionEnum.getResultMsg();
    }

    public SseApplicationException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

}
