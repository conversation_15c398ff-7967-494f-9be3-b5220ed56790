package com.zyhl.yun.api.outer.domainservice;

/**
 * 权益编号领域
 * <AUTHOR>
 */
public interface BenefitNoDomainService {

	/**
	 * 获取权益编号
	 *
	 * @param sceneTag 标签
	 * @param channel 渠道
	 * @param clientType 客户端类型
	 * @param phone 用户手机号
	 * @return 返回结果
	 */
	String getBenefitNo(String sceneTag, String channel, String clientType, String phone);

	/**
	 * 获取权益编号-工具应用
	 *
	 * @param channel 渠道
	 * @param clientType 客户端类型
	 * @param algorithmCode 算法编码
	 * @return 返回结果
	 */
	String getBenefitNoForTool(String channel, String clientType, String algorithmCode);

	/**
	 * 获取权益编号-工具应用
	 *
	 * @param channel 渠道
	 * @param clientType 客户端类型
	 * @return 返回结果
	 */
	Boolean benefitNoForToolOpen(String channel, String clientType);
}
