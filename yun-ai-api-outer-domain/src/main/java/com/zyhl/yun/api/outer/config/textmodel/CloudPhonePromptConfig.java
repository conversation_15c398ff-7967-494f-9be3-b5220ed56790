package com.zyhl.yun.api.outer.config.textmodel;

import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.config.textmodel.CloudPhonePromptConfig} <br>
 * <b> description:</b>
 * 云手机提示词配置
 *
 * <AUTHOR>
 * @date 2025-07-04 17:14
 **/
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "text-model.cloud-phone")
public class CloudPhonePromptConfig {

    /**
     * 用户问题提示词
     */
    private String systemPrompt = StringUtils.EMPTY;
}
