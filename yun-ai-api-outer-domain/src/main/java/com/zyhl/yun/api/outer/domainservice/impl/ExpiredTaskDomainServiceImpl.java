package com.zyhl.yun.api.outer.domainservice.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.plugin.redis.HcyRedisTemplate;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.domain.entity.TaskAiAbilityEntity;
import com.zyhl.yun.api.outer.domainservice.ExpiredTaskDomainService;
import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * className:ExpiredTaskDomainServiceImpl
 * description: 超时算法任务处理业务接口实现类
 *
 * <AUTHOR>
 * @date 2024/11/19
 */
@Slf4j
@Service
public class ExpiredTaskDomainServiceImpl implements ExpiredTaskDomainService {

    @Resource
    private HcyRedisTemplate<String, Object> hcyRedisTemplate;

    /**
     * 任务超时标识，默认5分钟
     */
    @Value("${monitor.log.task.expired-time:5}")
    private Integer monitorLogTaskExpiredTime;

    /**
     * 任务打印频率，默认1天
     */
    @Value("${monitor.log.task.key-ttl:1}")
    private Long monitorLogTaskKeyTtl;


    @Override
    public void expiredTaskMonitor(TaskAiAbilityEntity entity) {

        Date taskCreateTime = entity.getCreateTime();
        try {
            // check is expired or not
            if (TaskStatusEnum.isProcessing(entity.getTaskStatus())
                    && ObjectUtil.isNotNull(taskCreateTime)
                    && System.currentTimeMillis() > (taskCreateTime.getTime() + monitorLogTaskExpiredTime * 60 * 1000)) {
                checkRecordAndPrint(entity);
            }
        } catch (Exception e) {
            log.warn("expiredTaskMonitor-handle error found, pls check the system, details==>{}", e.getMessage());
        }
    }

    /**
     * 根据cache判断是否需要打印日志
     *
     * @param entity 任务参数
     */
    private void checkRecordAndPrint(TaskAiAbilityEntity entity) {

        Long taskId = entity.getId();
        // query
        Object cache = hcyRedisTemplate.opsForValue()
                .get(String.format(RedisConstants.EXPIRED_TASK_LOG_MONITOR, taskId));
        // no record then record and print
        if (ObjectUtil.isNull(cache)) {
            hcyRedisTemplate.opsForValue()
                    .set(String.format(RedisConstants.EXPIRED_TASK_LOG_MONITOR, taskId), taskId,
                            monitorLogTaskKeyTtl, TimeUnit.DAYS);
            log.error("expiredTaskMonitor taskId:{}, taskCreateTime:{}, 超过{}分钟告警, 任务结果超时未处理异常",
                    taskId, entity.getCreateTime().getTime(), monitorLogTaskExpiredTime);
        }
    }
}
