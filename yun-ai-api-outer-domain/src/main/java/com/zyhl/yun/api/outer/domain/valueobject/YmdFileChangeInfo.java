package com.zyhl.yun.api.outer.domain.valueobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 年月日文件信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class YmdFileChangeInfo {

    /**
     * 文件变更信息生成策略，目前仅支持full、latest、aggregate。
     * full：表示文件全量变更信息，多次变更会反返回多条变更信息；
     * latest：表示文件变更后的最新状态快照，多次变更仅返回最终文件状态信息；
     * aggregate：表示按变更类型聚合后的变更信息，多次变更时按照变更类型聚合后返回每一类变更信息；
     */
    private String policy;

    /**
     * 文件变更类型
     * 1--新建文件
     * 2--覆盖文件内容
     * 3--修改文件元信息
     * 4--彻底删除文件
     * 5--移动文件
     * 6--逻辑删除文件（移入回收站）
     * 7--还原文件（移出回收站）
     * 8--删除文件历史版本
     */
    private Integer changeType;

    /**
     * 发起变更操作的设备ID
     */
    private String deviceId;

    /**
     * 文件信息
     */
    private YmdFileInfo fileInfo;

    /**
     * 扩展信息
     */
    private FileChangeExtInfo extInfo;

    @Data
    public static class FileChangeExtInfo {

        /**
         * 扩展信息版本
         * 注：目前仅支持v1、v2
         */
        private String version;

        /**
         * 文件移动类型
         * 1: 同步目录内移动
         * 2: 移入同步目录
         * 3: 移出同步目录
         * 注: 仅当version=v1且changeType=5，即移动操作时返回
         */
        private Integer moveType;

        /**
         * 文件变更前的父目录ID
         * 注: 仅当 version=v1且changeType=4、5、6时，即文件彻底删除、移动、移入回收站操作时返回
         */
        private String srcParentFileId;

        /**
         * 变更后的文件在Drive下的全路径拼接上文件ID
         * 注: 仅当version=v2且 changeType=4、5、6时，即文件彻底删除、移动、移入回收站操作时返回
         */
        private String idPath;
    }
}
