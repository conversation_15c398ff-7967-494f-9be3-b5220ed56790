package com.zyhl.yun.api.outer.domain.entity.knowledge;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 公共知识库配置表-Entity
 *
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AlgorithmKnowledgeConfigEntity {

    /**
     * common-公共知识库id
     */
    private String id;

    /**
     * 知识库名称
     */
    private String name;

    /**
     * 知识库描述
     */
    private String description;

    /**
     * 配置json格式
     * {
     * fileTypes:".docx/.xlsx/.csv/.txt/.pptx", //文档类型
     * splitSize:"1024",  // 文档解析（切片大小）
     * algorithmGroupCode:4 // 文档向量化算法组-公共知识库
     * }
     */
    private String config;

    /**
     * config转的对象
     */
    private ConfigDTO configDTO;

    /**
     * 删除标识，0--正常；1--已删除
     *
     * @see com.zyhl.yun.api.outer.enums.knowledge.KnowledgeStatusEnum
     */
    private Integer delFlag;

    /**
     * 是否生效，0--不生效；1--已生效
     *
     * @see com.zyhl.yun.api.outer.enums.knowledge.KnowledgeEffectEnum
     */
    private Integer isEffect;

    @Data
    @NoArgsConstructor
    public static class ConfigDTO {

        // 文件类型
        private String fileTypes;

        // 文档解析（切片大小）
        private String splitSize;

        // 文档向量化算法组-公共知识库
        private Integer algorithmGroupCode;

    }

    /**
     * config转ConfigDTO
     *
     * @Author: WeiJingKun
     */
    public void convertConfigDTO() {
        if (CharSequenceUtil.isNotBlank(this.config)) {
            this.configDTO = JSON.parseObject(this.config, ConfigDTO.class);
        }
    }

}
