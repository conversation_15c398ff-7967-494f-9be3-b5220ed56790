package com.zyhl.yun.api.outer.domain.vo.chat.search.param;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 对话信息-搜索参数
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchParam implements Serializable {

    /** 语义搜图接口参数【有值则可用】 */
    private SearchImageParam searchImageParam;

    /** 个人资产搜索接口参数【有值则可用】 */
    private SearchFileParam searchFileParam;

    /** 笔记搜索接口参数【有值则可用】 */
    private SearchNoteParam searchNoteParam;

    /** 活动搜索接口参数【有值则可用】 */
    private SearchActivityParam searchActivityParam;

    /** 功能搜索接口参数【有值则可用】 */
    private SearchFunctionParam searchFunctionParam;

    /** 发现广场搜索接口参数【有值则可用】 */
    private SearchDiscoveryParam searchDiscoveryParam;

    /** 我的圈子搜索接口参数【有值则可用】 */
    private SearchMyGroupParam searchMyGroupParam;

    /** 热门圈子搜索接口参数【有值则可用】 */
    private SearchRecommendGroupParam searchRecommendGroupParam;

    /** 邮件搜索接口参数【有值则可用】 */
    private SearchMailParam searchMailParam;

    /** 邮件附件搜索接口参数【有值则可用】 */
    private SearchMailAttachmentParam searchMailAttachmentParam;

    /** 知识库资源搜索接口参数【有值则可用】 */
    private SearchKnowledgeBaseResourceParam searchKnowledgeBaseResourceParam;

    /** 笔记内容搜索接口参数【有值则可用】 */
    private SearchNoteContentParam searchNoteContentParam;

    /**
     * 构建需要执行搜索子参数列表
     * PS：TODO 如果有新搜索，需要添加判断
     */
    public List<Object> createNeedSearchSubParamList() {
        List<Object> needSearchSubParamList = new ArrayList<>();
        // 知识库资源搜索
        if (ObjectUtil.isNotNull(searchKnowledgeBaseResourceParam)) {
            needSearchSubParamList.add(searchKnowledgeBaseResourceParam);
        }
        // 邮件附件搜索
        if (ObjectUtil.isNotNull(searchMailAttachmentParam)) {
            needSearchSubParamList.add(searchMailAttachmentParam);
        }
        // 邮件搜索
        if (ObjectUtil.isNotNull(searchMailParam)) {
            needSearchSubParamList.add(searchMailParam);
        }
        // 热门圈子搜索
        if (ObjectUtil.isNotNull(searchRecommendGroupParam)) {
            needSearchSubParamList.add(searchRecommendGroupParam);
        }
        // 我的圈子搜索
        if (ObjectUtil.isNotNull(searchMyGroupParam)) {
            needSearchSubParamList.add(searchMyGroupParam);
        }
        // 发现广场搜索
        if (ObjectUtil.isNotNull(searchDiscoveryParam)) {
            needSearchSubParamList.add(searchDiscoveryParam);
        }
        // 功能搜索
        if (ObjectUtil.isNotNull(searchFunctionParam)) {
            needSearchSubParamList.add(searchFunctionParam);
        }
        // 活动搜索
        if (ObjectUtil.isNotNull(searchActivityParam)) {
            needSearchSubParamList.add(searchActivityParam);
        }
        // 笔记搜索
        if (ObjectUtil.isNotNull(searchNoteParam)) {
            needSearchSubParamList.add(searchNoteParam);
        }
        // 个人资产搜索
        if (ObjectUtil.isNotNull(searchFileParam)) {
            needSearchSubParamList.add(searchFileParam);
        }
        // 语义搜图
        if (ObjectUtil.isNotNull(searchImageParam)) {
            needSearchSubParamList.add(searchImageParam);
        }
        return needSearchSubParamList;
    }

}
