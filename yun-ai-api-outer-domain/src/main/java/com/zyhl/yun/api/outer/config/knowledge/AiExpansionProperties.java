package com.zyhl.yun.api.outer.config.knowledge;

import com.zyhl.yun.api.outer.enums.knowledge.AiExpansionRangeEnum;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 知识图AI扩写配置
 *
 * <AUTHOR>
 * @date 2025/7/7 16:10
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "knowledge.ai-expansion")
public class AiExpansionProperties {

    /**
     * AI扩写开关
     */
    private boolean enabled;

    /**
     * 召回切片数量配置
     */
    private Integer chunkCount = 10;

    /**
     * 切换的模型编码
     */
    private String modelCode = "blian_qwen3_235b";

    /**
     * 时间与字数关系配置（一分钟200个字）
     */
    private Integer timeToWord = 200;

    /**
     * 字数的范围
     */
    private Map<AiExpansionRangeEnum, Range> ranges;

    @Data
    public static class Range {
        /**
         * 最小字数
         */
        private int min;
        /**
         * 最大字数
         */
        private int max;
        /**
         * 是否扩写
         */
        private boolean expansion = true;
    }

    /**
     * 获取知识库扩写文字范围枚举
     *
     * @param wordCount 字数
     * @return 文字范围枚举
     */
    public AiExpansionRangeEnum getAiExpansionRange(Integer wordCount) {
        return ranges.entrySet().stream()
                .filter(entry -> {
                    AiExpansionProperties.Range range = entry.getValue();
                    return wordCount > range.getMin() && wordCount <= range.getMax();
                })
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse(AiExpansionRangeEnum.FIRST);
    }

    /**
     * 判断是否需要扩写
     *
     * @param wordCount 字数
     * @return 是否需要扩写
     */
    public boolean isExpansion(Integer wordCount) {
        for (Range range : ranges.values()) {
            if (wordCount > range.getMin() && wordCount <= range.getMax()) {
                return range.isExpansion();
            }
        }
        return false;
    }
}
