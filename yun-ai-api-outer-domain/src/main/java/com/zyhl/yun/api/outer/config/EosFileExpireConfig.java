package com.zyhl.yun.api.outer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <b>className:</b>
 * {@link EosFileExpireConfig} <br>
 * <b> description:</b>
 * EOS文件过期时间配置
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-09-13 10:22
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "ai-eos-file")
public class EosFileExpireConfig {

    /**
     * 过期时间
     */
    private long expireTime = 24 * 60 * 60 * 1000L;
}
