package com.zyhl.yun.api.outer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.*;

/**
 * 意图上下文配置
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "intention.context")
@Data
public class IntentionContextProperties {

    /**
     * 是否启用 true是 false否
     */
    private boolean enable = false;

    /**
     * 存储对话信息到历史会话 redis最大列表长度
     */
    private Integer maxCount;

    /**
     * 存储对话信息到历史会话 redis过期时间（秒）
     */
    private Long expireTime;

    /**
     * 传入意图识别上下文的对话意图编码列表
     */
    private List<String> intentionContextCodeList;

    /**
     * 传入意图识别上下文的对话次数
     */
    private Integer intentionContextCount;

    /**
     * 传入意图识别上下文的tokens
     */
    private Integer intentionContextTokens;
}
