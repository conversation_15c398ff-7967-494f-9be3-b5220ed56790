package com.zyhl.yun.api.outer.enums;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 图片后缀名枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum ImageSuffixEnum {

    /**
     * JPG格式
     */
    JPG("jpg", "JPG"),

    /**
     * PNG格式
     */
    PNG("png", "PNG"),

    /**
     * MP4格式
     */
    MP4("mp4", "MP4"),

    /**
     * GIF格式
     */
    GIF("gif", "GIF");


    private static final Map<String, ImageSuffixEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(ImageSuffixEnum.class).forEach(item -> MAP.put(item.code, item));
    }

    public static ImageSuffixEnum getByCode(String code) {
        if (null == code) {
            return null;
        }
        return MAP.get(code);
    }

    public static ImageSuffixEnum getByAlgorithmCode(String algorithmCode) {
        if (algorithmCode.equals(DialogueIntentionEnum.INTELLIGENT_CUTOUT.getCode())) {
            return PNG;
        } else if (algorithmCode.equals(DialogueIntentionEnum.LIVE_PHOTOS.getCode())) {
            return MP4;
        } else {
            return JPG;
        }
    }

    /**
     * 是否存在
     *
     * @param code 编码
     * @return true-存在
     */
    public static boolean isExist(String code) {
        return getByCode(code) != null;
    }


    /**
     * 对应code
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

}
