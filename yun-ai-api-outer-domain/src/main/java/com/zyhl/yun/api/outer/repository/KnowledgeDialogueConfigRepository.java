package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.knowledge.KnowledgeBusinessEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.KnowledgeDialogueConfigEntity;

import java.util.List;

/**
 * 知识库对话配置表
 *
 * <AUTHOR>
 */
public interface KnowledgeDialogueConfigRepository {

    /**
     * 根据用户id查询
     *
     * @param userId 用户id
     * @return 集合
     */
    List<KnowledgeDialogueConfigEntity> selectByUserId(String userId);

}
