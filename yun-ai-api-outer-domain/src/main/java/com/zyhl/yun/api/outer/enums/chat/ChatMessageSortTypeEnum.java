package com.zyhl.yun.api.outer.enums.chat;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/** 
 * 排序相关
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ChatMessageSortTypeEnum {
    /**
     * 创建时间倒序
     */
    CREATE_TIME_DESC(1, "【创建时间】倒序排序"),
    /**
     * 更新时间 倒序
     */
    UPDATE_TIME_DESC(2, "【更新时间】倒序排序"),
    ;

    private static final Map<Integer, ChatMessageSortTypeEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(ChatMessageSortTypeEnum.class).forEach(item -> MAP.put(item.code, item));
    }

    public static ChatMessageSortTypeEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return MAP.get(code);
    }

    /**
     * 是否存在
     * @param code 编码
     * @return true-存在
     */
    public static boolean isExist(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 描述
     */
    private final String desc;

}
