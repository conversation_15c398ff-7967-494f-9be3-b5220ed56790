package com.zyhl.yun.api.outer.enums.chat.search;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 对话信息-搜索参数-发现广场-类别枚举
 * @Author: WeiJingKun
 */
@Getter
@AllArgsConstructor
public enum SearchDiscoveryParamQueryTypeEnum {

    /** 全部 */
    ALL(1, "全部"),
    /** 文档 */
    DOCUMENT(3, "文档"),
    /** 视频 */
    VIDEO(6, "视频"),
    /** 试卷 */
    TEST_PAPER(10, "试卷"),
    /** 影视 */
    MOVIE(11, "影视"),
    /** 其他 */
    OTHER(12, "其他"),
    /** 书籍 */
    BOOKS(13, "书籍"),
    /** 短剧 */
    SHORT_PLAY(15, "短剧"),


    ;

    private static final Map<String, SearchDiscoveryParamQueryTypeEnum> DESC_MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(SearchDiscoveryParamQueryTypeEnum.class).forEach(item -> DESC_MAP.put(item.desc, item));
    }

    public static SearchDiscoveryParamQueryTypeEnum getByDesc(String desc) {
        if (CharSequenceUtil.isBlank(desc)) {
            return null;
        }
        return DESC_MAP.get(desc);
    }

    /**
     * 匹配不到desc，则返回默认值ALL
     * @Author: WeiJingKun
     */
    public static SearchDiscoveryParamQueryTypeEnum getByKeyReturnDefault(String desc) {
        if (CharSequenceUtil.isBlank(desc)) {
            return null;
        }
        SearchDiscoveryParamQueryTypeEnum queryTypeEnum = DESC_MAP.get(desc);
        return null == queryTypeEnum ? ALL : queryTypeEnum;
    }

    /**
     * 是否存在
     * @return true-存在
     */
    public static boolean isExist(String desc) {
        return getByDesc(desc) != null;
    }

    /**
     * 判断是否为【视频】相关类型
     * queryTypeList，不包含全部 && 只包含视频\影视\其他\短剧，其中一个或多个
     * @Author: WeiJingKun
     *
     * @param queryTypeList 查询类型集合
     * @return true-是
     */
    public static boolean judgeSearchVideo(List<Integer> queryTypeList) {
        if(CollUtil.isEmpty(queryTypeList)){
            return false;
        }
        return !CollUtil.contains(queryTypeList, ALL.code) && CollUtil.containsAny(queryTypeList, Arrays.asList(VIDEO.code, MOVIE.code, OTHER.code, SHORT_PLAY.code));
    }

    /**
     * 判断是否为【文档】相关类型
     * queryTypeList，不包含全部 && 只包含书籍、文档、试卷，其中一个或多个
     * @Author: WeiJingKun
     *
     * @param queryTypeList 查询类型集合
     * @return true-是
     */
    public static boolean judgeSearchDocument(List<Integer> queryTypeList) {
        if(CollUtil.isEmpty(queryTypeList)){
            return false;
        }
        return !CollUtil.contains(queryTypeList, ALL.code) && CollUtil.containsAny(queryTypeList, Arrays.asList(DOCUMENT.code, TEST_PAPER.code, BOOKS.code));
    }

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 描述
     */
    private final String desc;

}
