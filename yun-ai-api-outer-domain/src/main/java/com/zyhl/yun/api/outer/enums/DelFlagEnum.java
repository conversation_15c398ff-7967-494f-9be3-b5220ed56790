package com.zyhl.yun.api.outer.enums;

import lombok.Getter;


/**
 * 状态枚举
 *
 * <AUTHOR>
 * @date 2025年5月20日16:12:48
 */
@Getter
public enum DelFlagEnum {

    /**
     * 删除状态  否
     */
    NO(0, "否"),

    /**
     * 删除状态  是
     */
    YES(1, "是"),

    /**
     * 删除状态  删除中
     */
    DEL_ING(2, "删除中"),

    ;

    DelFlagEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

}
