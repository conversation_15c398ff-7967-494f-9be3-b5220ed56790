package com.zyhl.yun.api.outer.domain.resp;

import com.zyhl.yun.api.outer.domain.entity.FileRspEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 批量获取
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchGetFileRspEntity {

   /**
    * 批量获取结果
    */
   private List<FileRspEntity> batchFileResults;
}
