package com.zyhl.yun.api.outer.domain.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/1/6 16:54
 */
@Data
public class AiToolsAccreditEntity implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 模型
     */
    private Integer module;

    /**
     * 名称
     */
    private String moduleName;

    /**
     * 云盘目录path
     */
    private String path;

    /**
     * 用户所属底座
     */
    private Integer belongsPlatform;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 业务来源
     */
    private Integer sourceBusiness;

    /**
     * 目录id
     */
    private String catalogId;

}
