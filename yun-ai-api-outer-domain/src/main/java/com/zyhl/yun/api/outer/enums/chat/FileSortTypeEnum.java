package com.zyhl.yun.api.outer.enums.chat;

import lombok.Getter;

/**
 * 对话状态，algorithm_chat_content表
 *
 * <AUTHOR>
 */
@Getter
public enum FileSortTypeEnum {
    /**
     * 文件(独立空间)
     */
    FILE(10, "文件(独立空间)"),

    /**
     * 图片(独立空间)
     */
    IMAGE(15, "图片(独立空间)"),

    /**
     * 笔记
     */
    NOTE(20, "笔记"),

    /**
     * 邮件
     */
    MAIL(30, "邮件"),

    /**
     * 网址
     */
    HTML(40, "网址"),

    ;


    FileSortTypeEnum(Integer code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 备注
     */
    private final String remark;

}
