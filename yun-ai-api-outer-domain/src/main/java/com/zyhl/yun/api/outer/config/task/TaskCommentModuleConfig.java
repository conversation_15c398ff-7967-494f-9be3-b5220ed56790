package com.zyhl.yun.api.outer.config.task;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 任务评论模块配置类
 * @date 2025/4/22 11:01
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "task-comment")
public class TaskCommentModuleConfig {

    /**
     * 支持的模块编码集合
     */
    private Set<String> moduleSet;

}