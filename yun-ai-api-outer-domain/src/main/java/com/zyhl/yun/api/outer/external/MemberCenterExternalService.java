package com.zyhl.yun.api.outer.external;

import java.util.List;

import com.zyhl.hcy.yun.ai.common.platform.third.client.membercenter.resp.ConsumeAvailableBenefitRsp;

/**
 * 会员中心对外接口
 *
 * <AUTHOR>
 */
public interface MemberCenterExternalService {

    /**
     * 会员权益消费（权益预消费）
     *
     * @param userId    用户id
     * @param benefitNo 权益项编号
     * @return 消费码
     */
    String consumeBenefit(String userId, String benefitNo);

    /**
     * 会员权益消费（权益预消费）
     *
     * @param phone     手机号码
     * @param benefitNo 权益项编号
     * @return 消费码
     */
    String consumeBenefitByPhone(String phone, String benefitNo);

    /**
     * 会员权益消费结果，消费失败（回滚权益）
     *
     * @param userId     用户id
     * @param benefitNo  权益项编号
     * @param consumeSeq 消费码
     * @return boolean
     */
    boolean consumeBenefitFail(String userId, String benefitNo, String consumeSeq);

    /**
     * 会员权益消费结果，消费成功或消费失败（消费失败会回滚权益）
     *
     * @param phone      手机号码
     * @param benefitNo  权益项编号
     * @param consumeSeq 消费码
     * @return boolean
     */
    boolean consumeBenefitFailByPhone(String phone, String benefitNo, String consumeSeq);

    /**
     * 查询会员权益
     *
     * @param userId    the user id
     * @param benefitNo the benefit no
     * @return {@link ConsumeAvailableBenefitRsp.AvailableBenefitRsp}
     * <AUTHOR>
     * @date 2024-8-16 18:04
     */
    ConsumeAvailableBenefitRsp.AvailableBenefitRsp queryAvailableBenefit(String userId, String benefitNo);

    /**
     * 查询会员权益
     *
     * @param phoneNumber the phone number
     * @param benefitNo   the benefit no
     * @return {@link ConsumeAvailableBenefitRsp.AvailableBenefitRsp}
     * <AUTHOR>
     * @date 2024-8-16 18:04
     */
    ConsumeAvailableBenefitRsp.AvailableBenefitRsp queryAvailableBenefitByPhone(String phoneNumber, String benefitNo);
    
	/**
	 * 查询是否会员
	 * 
	 * @param phone      手机号
	 * @param benefitNos 权益编码列表
	 * @return 是否会员
	 */
	Boolean isMember(String phone, List<String> benefitNos);
}
