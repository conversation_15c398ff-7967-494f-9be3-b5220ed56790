package com.zyhl.yun.api.outer.domainservice;

import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.enums.AssistantEnum;

/**
 * 会话大语言模型设定DomainService类
 * @Author: WeiJingKun
 */
public interface ChatConfigServiceDomainService {

    /**
     * 获取用户可用模型
     * @see ModelProperties
     * @param userId 用户id
     * @param phone 用户手机号
     * @param assistantEnum 助手类型
     * @param businessType 助手业务类型
     * @return 会话设置实体
     */
    ChatConfigEntity getUserCanUseModel(String userId, String phone, AssistantEnum assistantEnum, String businessType);

}
