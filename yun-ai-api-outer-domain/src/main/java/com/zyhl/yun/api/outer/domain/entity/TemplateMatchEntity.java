package com.zyhl.yun.api.outer.domain.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 模板匹配结果
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TemplateMatchEntity {
    /**
     * 请求ID。
     * 该字段存储请求的唯一标识符。
     */
    private String requestId;

    /**
     * 图片标签信息。
     * 该字段存储图片的标签信息列表。
     */
    private List<LabelEntity> labelList;

    /**
     * 阈值。
     * 该字段存储用于过滤结果的阈值，若传入，则只返回评分大于该阈值的数据。
     */
    private Float threshold;

}
