package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识库送审状态
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FileAuditStatusEnum {

    /**
     * 未送审
     */

    UNAUDITED(0, "未送审"),

    /**
     * 送审中
     */
    UNDER_REVIEW(1, "送审中"),

    /**
     * 通过
     */
    PASS(2, "通过"),

    /**
     * 未通过
     */
    NOT_PASS(3, "未通过"),

    ;
    /**
     * 状态
     */
    private final Integer status;
    /**
     * 描述
     */
    private final String remark;




    public static boolean isUnAudited(Integer status) {
        return UNAUDITED.status.equals(status);
    }

    public static boolean isPass(Integer status) {
        return PASS.status.equals(status);
    }

    public static boolean isNotPass(Integer status) {
        return NOT_PASS.status.equals(status);
    }

    public static boolean isUntreated(Integer status) {
        return status == null || UNDER_REVIEW.status.equals(status) || UNAUDITED.status.equals(status);
    }

}
