package com.zyhl.yun.api.outer.enums.chat;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * AI会话内容-输出资源类型
 * @Author: WeiJingKun
 */
public enum OutResourceTypeEnum {

    /**
     * 文本
     */
    TEXT(0, "文本"),

    /**
     * 图片
     */
    IMAGE(3, "图片"),

    ;

    private static final Map<Integer, OutResourceTypeEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(OutResourceTypeEnum.class).forEach(item -> MAP.put(item.code, item));
    }

    public static OutResourceTypeEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return MAP.get(code);
    }

    /**
     * 是否存在
     * @param code 编码
     * @return true-存在
     */
    public static boolean isExist(Integer code) {
        return getByCode(code) != null;
    }

    OutResourceTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    
}
