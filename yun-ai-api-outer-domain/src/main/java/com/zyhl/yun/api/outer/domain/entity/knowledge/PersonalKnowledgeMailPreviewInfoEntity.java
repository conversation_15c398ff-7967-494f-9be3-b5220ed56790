package com.zyhl.yun.api.outer.domain.entity.knowledge;

import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.resp.MailDetailPartResult;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description 邮件正文预览信息 Entity（符合 DDD 规范）
 * @date 2025/4/17 17:37
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PersonalKnowledgeMailPreviewInfoEntity {

    /**
     * 全局唯一标识符（UUID）
     */
    private String id;

    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 知识库Id
     */
    private String baseId;

    /**
     * 收件人列表
     */
    private List<String> recipientList;

    /**
     * 抄送人列表（可选）
     */
    private List<String> carbonCopyList;

    /**
     * 邮件标题
     */
    private String title;

    /**
     * 邮件正文
     */
    private String content;

    /**
     * 发件人列表
     */
    private List<String> senderList;

    /**
     * 发送时间，遵循 RFC 3339 格式，例如：2019-08-20T06:51:27.292+08:00
     */
    private String sendDate;

    /**
     * 邮件正文text信息对象 (part object)
     */
    private MailDetailPartResult textContent;

    /**
     * 邮件正文html信息对象 (part object)
     */
    private MailDetailPartResult htmlContent;

    /**
     * 邮件附件信息列表（可选）
     */
    private List<File> attachmentList;

}
