package com.zyhl.yun.api.outer.domain.dto.redis;

import lombok.Builder;
import lombok.Data;

/**
 * 保存到redis的历史对话信息DTO
 *
 * <AUTHOR>
 * @date 2024/12/18 11:49
 */
@Data
@Builder
public class HistoryDialogInfoDTO {

    /**
     * 对话id
     */
    private String dialogId;

    /**
     * 意图编码
     */
    private String intentionCode;

    /**
     * 多意图编码 用+拼接
     */
    private String multipleIntentionCode;

    public HistoryDialogInfoDTO(String dialogId, String intentionCode, String multipleIntentionCode) {
        this.dialogId = dialogId;
        this.intentionCode = intentionCode;
        this.multipleIntentionCode = multipleIntentionCode;
    }

    public HistoryDialogInfoDTO() {
    }
}
