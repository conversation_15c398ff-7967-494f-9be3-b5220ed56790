package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiPersonResourceEntity;

import java.util.List;

/**
 * className:BlackResourceRepository
 *
 * <AUTHOR>
 */
public interface AlgorithmAiPersonResourceRepository {

    /**
     * 获取人物关系数据
     * @return 全量任务关系数据list
     */
    List<AlgorithmAiPersonResourceEntity> getAllDate();

    boolean batchAdd(List<AlgorithmAiPersonResourceEntity> list);

}
