package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 文件分类映射关系，知识库文件分类 与 个人云文件分类映射关系
 * p>
 * 分类， 根据文件的后缀名和mime-type对文件进行分了分类，主要分类有：
 * app：安装包 ；zip：压缩包 image：图片； doc：文档 video：视频 ；audio：音频 folder：目录 ；others：其他
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FileCategoryEnum {

    /**
     * 图片
     */
    IMAGE(1, "image"),

    /**
     * 音频
     */
    AUDIO(2, "audio"),

    /**
     * 视频
     */
    VIDEO(3, "video"),

    /**
     * 文档
     */
    DOC(4, "doc"),

    /**
     * 安装包
     */
    APP(5, "app"),

    /**
     * 压缩包
     */
    ZIP(6, "zip"),

    /**
     * 其他
     */
    OTHERS(0, "others"),

    /**
     * 目录
     */
    FOLDER(100, "folder"),

    ;
    /**
     * 知识库文件分类
     */
    private final Integer knowledgeCategory;
    /**
     * 个人云文件分类
     */
    private final String yundiskCategory;


    public static FileCategoryEnum getByKnowledgeCategory(Integer knowledgeCategory) {
        for (FileCategoryEnum value : FileCategoryEnum.values()) {
            if (value.getKnowledgeCategory().equals(knowledgeCategory)) {
                return value;
            }
        }
        return null;
    }

    public static FileCategoryEnum getByYunDiskCategory(String yundiskCategory) {
        for (FileCategoryEnum value : FileCategoryEnum.values()) {
            if (value.getYundiskCategory().equals(yundiskCategory)) {
                return value;
            }
        }
        return null;
    }

    public static String getYunDiskCategory(Integer knowledgeCategory) {
        FileCategoryEnum type = getByKnowledgeCategory(knowledgeCategory);
        return type == null ? "" : type.getYundiskCategory();
    }

    public static Integer getKnowledgeCategory(String yundiskCategory) {
        FileCategoryEnum type = getByYunDiskCategory(yundiskCategory);
        return type == null ? 0 : type.getKnowledgeCategory();
    }


    /**
     * 校验分类列表
     *
     * @param categoryList 分类列表
     * @return true表示正常 false表示异常
     */
    public static boolean checkCategoryList(List<Integer> categoryList) {
        if (CollectionUtils.isEmpty(categoryList)) {
            return true;
        }

        for (Integer category : categoryList) {
            if (getByKnowledgeCategory(category) == null) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断是否是图片
     *
     * @param category 分类
     * @return true表示是图片
     */
    public static boolean isKnowledgeImage(Integer category) {
        return FileCategoryEnum.IMAGE.getKnowledgeCategory().equals(category);
    }
}
