package com.zyhl.yun.api.outer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.enums.ImageTransmissionTypeEnum} <br>
 * <b> description:</b>
 * 图片存储类型枚举
 *
 * <AUTHOR>
 * @date 2024-05-09 17:39
 **/
@Getter
@AllArgsConstructor
public enum ImageTransmissionTypeEnum {

    /**
     * 个人云（默认）
     */
    YUN_DISK(1, "个人云（默认）"),

    /**
     * EOS对象存储
     */
    EOS(2, "EOS对象存储");

    /**
     * 编码
     */
    private final int code;
    /**
     * 描述
     */
    private final String name;

    /**
     * 根据枚举变量数字值返回枚举变量名称
     *
     * @param code 枚举变量数字值
     * @return 枚举变量名称
     */
    public static ImageTransmissionTypeEnum getNameByCode(Integer code) {
        if (null == code) {
            return ImageTransmissionTypeEnum.YUN_DISK;
        }
        ImageTransmissionTypeEnum[] imageTransmissionTypeEnums = values();
        for (ImageTransmissionTypeEnum imageTransmissionTypeEnum : imageTransmissionTypeEnums) {
            if (imageTransmissionTypeEnum.code == code) {
                return imageTransmissionTypeEnum;
            }
        }
        return ImageTransmissionTypeEnum.YUN_DISK;
    }

    public static boolean isYunDisk(Integer code) {
        return code != null && YUN_DISK.code == code;
    }

    public static boolean isEos(Integer code) {
        return code != null && EOS.code == code;
    }
}
