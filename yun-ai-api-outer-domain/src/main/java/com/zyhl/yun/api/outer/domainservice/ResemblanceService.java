package com.zyhl.yun.api.outer.domainservice;

import com.zyhl.yun.api.outer.domain.aggregate.PictureFileAggregate;
import com.zyhl.yun.api.outer.domain.entity.ResemblanceEntity;

import java.util.List;

/**
 * 回忆相册生成过滤重复服务接口
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
public interface ResemblanceService {

	/**
	 * 图片聚合处理
	 * 
	 * @param fileAggregates      图片文件聚合对象
	 * @param similarityThreshold 阈值
	 * @return 实体列表
	 */
	List<ResemblanceEntity> imageCluster(List<PictureFileAggregate> fileAggregates, double similarityThreshold);
}
