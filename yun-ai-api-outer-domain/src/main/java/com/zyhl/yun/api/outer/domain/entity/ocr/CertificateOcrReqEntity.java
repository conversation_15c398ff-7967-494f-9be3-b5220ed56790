package com.zyhl.yun.api.outer.domain.entity.ocr;

import cn.hutool.json.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 卡证识别请求实体
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CertificateOcrReqEntity {
    /**
     * 请求序列号
     */
    private String requestId;

    /**
     * 文件ID
     */
    private String fileId;

    /**
     * 证件类型
     */
    private int cardType;

    /**
     * 结构化数据
     */
    private JSONObject structuralData;

}
