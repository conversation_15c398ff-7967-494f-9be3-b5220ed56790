package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeLabelEntity;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 个人知识库标签
 *
 * <AUTHOR>
 */
public interface UserKnowledgeLabelRepository {

    /**
     * 新增
     *
     * @param entity 实体对象
     * @return 数量
     */
    int add(@NotNull UserKnowledgeLabelEntity entity);

    /**
     * 物理删除
     *
     * @param labelId 标签id
     * @return 数量
     */
    int delete(@NotNull Long labelId);

    /**
     * 更新
     *
     * @param entity 实体对象
     * @return 数量
     */
    int update(@NotNull UserKnowledgeLabelEntity entity);

    /**
     * 根据id查询
     *
     * @param id 标签id
     * @return 实体对象
     */
    UserKnowledgeLabelEntity selectById(@NotNull Long id);

    /**
     * 根据用户id和标签名查询
     *
     * @param userId    用户id
     * @param labelName 标签名称
     * @return 实体对象
     */
    UserKnowledgeLabelEntity selectByName(@NotNull String userId, @NotNull String labelName);

    /**
     * 根据用户id查询
     *
     * @param userId 用户id
     * @return 列表
     */
    List<UserKnowledgeLabelEntity> selectByUserId(@NotNull String userId);

    /**
     * 查询总数
     *
     * @param userId 用户id
     * @return 数量
     */
    int count(@NotNull String userId);
}
