package com.zyhl.yun.api.outer.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.Data;

import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 渠道号配置
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "source-channels")
@Data
public class SourceChannelsProperties {

    /**
     * 渠道号映射业务类型
     */
    private List<SourceChannel> channelList;


    /**
     * 通过渠道号获取渠道信息
     *
     * @param sourceChannel
     * @return
     */
    public SourceChannel getByChannel(String sourceChannel) {
        if (!CollUtil.isEmpty(channelList)) {
            for (SourceChannel item : channelList) {
                if (item.channel.equals(sourceChannel)) {
                    return item;
                }
            }
        }
        return null;
    }

    /**
     * 渠道存在
     *
     * @param sourceChannel 渠道号
     * @return true-存在
     */
    public boolean isExist(String sourceChannel) {
        return getByChannel(sourceChannel) != null;
    }


    /**
     * 获取业务类型
     *
     * @param sourceChannel 渠道号
     * @return 业务类型
     */

    public String getType(String sourceChannel) {
        final SourceChannel sc = getByChannel(sourceChannel);

        return sc == null ? "" : CharSequenceUtil.nullToDefault(sc.getBusinessType(), "");
    }

    public String getTypeNullThrowException(String sourceChannel) {
        String type = getType(sourceChannel);
        if (CharSequenceUtil.isBlank(type)) {
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_BUSINESS_TYPE);
        }
        return type;
    }


    /**
     * 校验渠道号和业务类型是否一致
     *
     * @param sourceChannel 请求参数的渠道号
     * @param type          请求参数的业务类型
     * @return true-一致
     */
    public boolean check(String sourceChannel, String type) {
        return getType(sourceChannel).equals(type);
    }

    /**
     * 小天助手渠道号
     *
     * @param sourceChannel 渠道号
     * @return true-是小天渠道号
     */
    public boolean isXiaoTian(String sourceChannel) {
        final SourceChannel sc = getByChannel(sourceChannel);
        return sc != null && AssistantEnum.isXiaoTian(sc.code);
    }

    public static boolean isXiaoTianChannel(String sourceChannel) {
        SourceChannelsProperties properties = SpringUtil.getBean(SourceChannelsProperties.class);
        return properties != null && properties.isXiaoTian(sourceChannel);
    }
    
    /**
     * 云邮助手渠道号
     *
     * @param sourceChannel 渠道号
     * @return true-是云邮渠道号
     */
    public boolean isMail(String sourceChannel) {
        final SourceChannel sc = getByChannel(sourceChannel);
        return sc != null && AssistantEnum.isYunMail(sc.code);
    }

    public static boolean isMailChannel(String sourceChannel) {
        SourceChannelsProperties properties = SpringUtil.getBean(SourceChannelsProperties.class);
        return properties != null && properties.isMail(sourceChannel);
    }

	public static boolean isCloudPhoneChannel(String sourceChannel) {
		SourceChannelsProperties properties = SpringUtil.getBean(SourceChannelsProperties.class);
		final SourceChannel sc = properties.getByChannel(sourceChannel);
		return sc != null && AssistantEnum.isCloudPhone(sc.code);
	}
	
    /**
     * 端内渠道
     *
     * @param sourceChannel 渠道号
     * @return true-是端内渠道
     */
    public boolean isInner(String sourceChannel) {
        final SourceChannel sc = getByChannel(sourceChannel);

        return sc != null && "inner".equals(sc.getEnd());
    }

    /**
     * 获取权益开关状态
     *
     * @param sourceChannel 渠道号
     * @return true-权益打开
     */
    public boolean benefitSwitch(String sourceChannel) {
        final SourceChannel sc = getByChannel(sourceChannel);
        return sc != null && sc.isBenefitSwitch();
    }

    /**
     * 获取助手编码
     *
     * @param sourceChannel 渠道号
     * @return 助手编码
     */
    public String getCode(String sourceChannel) {
        final SourceChannel sc = getByChannel(sourceChannel);
        return sc != null ? sc.getCode() : "";
    }

    /**
     * 根据渠道，获取助手类型枚举（默认云邮）
     * @Author: WeiJingKun
     *
     * @param sourceChannel 渠道来源
     * @return 助手类型
     */
    public AssistantEnum getAssistantEnumDefaultMail(String sourceChannel) {
        String businessCode = getCode(sourceChannel);
        AssistantEnum assistant = AssistantEnum.getByCode(businessCode);
        return ObjectUtil.isNull(assistant) ? AssistantEnum.YUN_MAIL : assistant;
    }

    /**
     * 获取所有云邮助手业务类型
     *
     * @param sourceChannel 渠道号
     */
    @NotNull
    public List<String> getYunMailAllBusinessType(String sourceChannel) {
        List<String> businessTypeList = new ArrayList<>();
        // 先判断是否是云邮的渠道，再获取云邮所有业务类型
        if(isMail(sourceChannel)){
            if (!CollUtil.isEmpty(channelList)) {
                for (SourceChannel item : channelList) {
                    if (AssistantEnum.YUN_MAIL.getCode().equals(item.code)) {
                        businessTypeList.add(item.businessType);
                    }
                }
            }
        }
        // 去重
        if(CollUtil.isNotEmpty(businessTypeList)){
            businessTypeList = CollUtil.distinct(businessTypeList);
        }
        return businessTypeList;
    }

    /**
     * 获取所有云邮助手业务类型（去掉包含unknown的类型）
     *
     * @param sourceChannel 渠道号
     */
    @NotNull
    public List<String> getYunMailAllBusinessTypeRemoveUnknown(String sourceChannel) {
        List<String> businessTypeList = getYunMailAllBusinessType(sourceChannel);
        // 去掉包含unknown的类型
        if(CollUtil.isNotEmpty(businessTypeList)){
            businessTypeList = businessTypeList.stream().filter(item -> !item.contains("unknown")).collect(Collectors.toList());
        }
        return businessTypeList;
    }

    /**
     * 获取所有渠道端内的文本权益
     * @return
     */
	public List<String> getAllInnerBenefitNos() {
		List<String> benefitNos = new ArrayList<>();
		if (CollUtil.isNotEmpty(this.getChannelList())) {
			for (SourceChannel channel : this.getChannelList()) {
				String inNo = channel.getInnerBenefitNo();
				if (StringUtils.isNotBlank(inNo) && !benefitNos.contains(inNo)) {
					benefitNos.add(inNo);
				}
			}
		}
		return benefitNos;
	}
    /**
     * 来源渠道
     */
    @Data
    public static class SourceChannel {
        /**
         * 渠道号
         */
        private String channel;
        /**
         * 业务类型
         */
        private String businessType;
        /**
         * 助手编码：AssistantEnum
         */
        private String code;
        /**
         * 端内-inner，端外-outer
         */
        private String end = "inner";
        /**
         * 权益开关：true-打开，false-关闭
         */
        private boolean benefitSwitch = true;
        /**
         * 端内权益编号
         */
        private String innerBenefitNo;
        /**
         * 端外权益编号
         */
        private String outerBenefitNo;
        /**
         * 权益工具开关：true-打开，false-关闭
         */
        private boolean benefitToolSwitch = true;
        /**
         * 端内工具权益编号
         */
        private String innerToolBenefitNo;
        /**
         * 端外工具权益编号
         */
        private String outerToolBenefitNo;
        /**
         * 算法列表
         */
        private List<String> algorithmCodeList;
    }

}
