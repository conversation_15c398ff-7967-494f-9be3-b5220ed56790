package com.zyhl.yun.api.outer.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

import cn.hutool.core.collection.CollUtil;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.vo.DialogueIntentionVO} <br>
 * <b> description:</b>
 * 对话意图响应VO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-03-05 14:43
 **/
@Data
public class DialogueIntentionVO implements Serializable {

    private static final long serialVersionUID = -9210144648624582842L;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 对话ID
     */
    private String dialogueId;

    /**
     * 意图结果
     */
    private List<IntentionInfo> intentionInfoList;

    /**
     * 内容推荐关键字列表
     */
    private List<ContentRecommend> contentRecommendList;

	/**
	 * 算法实际意图编码
	 */
	private String algorithmIntentionCode;

    @Data
    public static class IntentionInfo {

        /**
         * 意图
         */
        private String intention;

        /**
         * 二级意图
         */
        private String subIntention;

        /**
         * 分数
         */
        private Double score;

        /**
         * 实体识别结果列表
         */
        private List<IntentEntityVO> entityList;
        
        /**
         * 在触发 Function Calling意图后，模型回复的要调用的工具以及调用工具所需的参数。可以包含一个或多个工具响应对象。
         */
        private List<ToolObjectVO> toolCallsList;
        
        /**
         * 子意图列表，当前用于综合搜索设置子意图列表进行搜索
         */
        private List<IntentionInfo> subIntentions;

        /** 是否为接口返回的意图 */
        private boolean interfaceReturnIntention = true;

        /**
         * 实体信息
         * AI编程文件名key=aiCoderFileName
         */
        private Map<String, List<String>> argumentMap;


    }

    /**
     * 内容推荐关键词信息
     * @Author: WeiJingKun
     */
    @Data
    public static class ContentRecommend {

        /**
         * 内容推荐关键字编码
         */
        private String code;

        /**
         * 内容推荐关键字
         */
        private String name;

        /** ============以下字段为代码逻辑字段，不是【意图识别结果】的字段==============*/
        /**
         * 推荐类型
         */
        private Integer type;
        /**
         * 优先级（从小到大，越小越优先）
         */
        private Integer priority;

    }

	/**
	 * 获取主意图
	 * 
	 * @param intentionVO
	 */
	public static IntentionInfo getMainIntention(DialogueIntentionVO intentionVO) {
		if (null != intentionVO && CollUtil.isNotEmpty(intentionVO.getIntentionInfoList())) {
			return intentionVO.getIntentionInfoList().get(0);
		}
		return null;
	}

	/**
	 * 获取主意图编码
	 * 
	 * @param intentionVO
	 */
	public static String getMainIntentionCode(DialogueIntentionVO intentionVO) {
		IntentionInfo mainIntention = getMainIntention(intentionVO);
		if (null != mainIntention) {
			return mainIntention.getIntention();
		}
		return null;
	}

	/**
	 * 生成主意图对象
	 * @param intentionCode 主意图编码
	 * @return
	 */
	public static DialogueIntentionVO newMainIntention(String intentionCode) {
		DialogueIntentionVO vo = new DialogueIntentionVO();
		IntentionInfo mainIntention = new IntentionInfo();
		mainIntention.setIntention(intentionCode);
		vo.setIntentionInfoList(Collections.singletonList(mainIntention));
		return vo;
	}

	/**
	 * 生成主意图对象
	 * @param intentionCode 主意图编码
	 * @return
	 */
	public static DialogueIntentionVO newMainIntention(String intentionCode , String subIntentionCode) {
		DialogueIntentionVO vo = new DialogueIntentionVO();
		IntentionInfo mainIntention = new IntentionInfo();
		mainIntention.setIntention(intentionCode);
		mainIntention.setSubIntention(subIntentionCode);
		vo.setIntentionInfoList(Collections.singletonList(mainIntention));
		return vo;
	}

	/**
	 * 实例化主意图对象
	 * @param mainIntention
	 * @return
	 */
	public static DialogueIntentionVO instanceMainIntention(IntentionInfo mainIntention) {
		DialogueIntentionVO vo = new DialogueIntentionVO();
		vo.setIntentionInfoList(Collections.singletonList(mainIntention));
		return vo;
	}
	
	/**
	 * 根据实体获取url拼接的参数
	 * 
	 * @param mainIntention 意图
	 * @return 实体url拼接的参数
	 */
	public static String getUrlEntityParams(IntentionInfo mainIntention) {
		if (null == mainIntention) {
			return StringUtils.EMPTY;
		}
		List<IntentEntityVO> entityList = mainIntention.getEntityList();
		if (CollUtil.isEmpty(entityList)) {
			return StringUtils.EMPTY;
		}
		StringBuilder str = new StringBuilder();
		List<KeyValueVO> metaDataList = entityList.get(0).getMetaDataList();
		for (KeyValueVO metaData : metaDataList) {
			if (CollUtil.isEmpty(metaData.getValue())) {
				str.append("&" + metaData.getKey() + "=");
			} else {
				str.append(
						"&" + metaData.getKey() + "=" + metaData.getValue().stream().collect(Collectors.joining(",")));
			}
		}
		return str.toString();
	}
}
