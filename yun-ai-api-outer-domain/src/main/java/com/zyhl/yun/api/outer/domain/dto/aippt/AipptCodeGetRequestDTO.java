package com.zyhl.yun.api.outer.domain.dto.aippt;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * AiPPT获取Code请求DTO
 *
 * <AUTHOR> Assistant
 * @date 2025/1/25
 */
@Data
public class AipptCodeGetRequestDTO {

    /**
     * 渠道来源，参考ChannelId枚举值
     */
    @NotBlank(message = "渠道来源不能为空")
    private String sourceChannel;

    /**
     * 用户Id，默认从token获取，第三方平台调用时必填
     */
    private String userId;

    /**
     * 是否移动端
     */
    private Boolean mobileTerminal;
}
