package com.zyhl.yun.api.outer.domain.valueobject;

import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveFileVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.AddressDetail;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.MediaMetaInfo;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.MediaPreviewInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * pds file 文件对象
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class File {
    /**
     * 文件id
     */
    public String fileId;
    /**
     * 父目录id
     */
    public String parentFileId;
    /**
     * 名称
     */
    public String name;
    /**
     * 文档正文搜索匹配内容，带高亮标签，前端需要做好处理
     */
    public String content;
    /**
     * 类型，枚举值file/folder
     */
    public String type;
    /**
     * 文件扩展名，一般是后缀名
     */
    public String fileExtension;
    /**
     * 分类， 根据文件的后缀名和mime-type对文件进行分了分类，主要分类有doc, image, audio, video
     */
    public String category;
    /**
     * 创建时间，RFC 3339，2019-08-20T06:51:27.292Z
     */
    public String createdAt;
    /**
     * 更新时间，RFC 3339，2019-08-20T06:51:27.292Z
     */
    public String updatedAt;
    /**
     * 大小
     */
    public Long size;
    /**
     * 缩略图地址
     */
    public String thumbnailUrl;
    /**
     * 内容hash
     */
    public String contentHash;
    /**
     * 内容hash算法名，当前hash 算法支持sha1
     */
    public String contentHashAlgorithm;
    /**
     * 如果存在就返回
     */
    public MediaMetaInfo mediaMetaInfo;
    /**
     * 如果存在就返回
     */
    public AddressDetail addressDetail;

    /**
     * 是否收藏
     */
    public Boolean starred;

    /**
     * 本地创建时间，RFC 3339，
     * 2019-08-20T06:51:27.292+08:00
     */
    private String localCreatedAt;

    /**
     * 本地更新时间，RFC 3339，
     * 2019-08-20T06:51:27.292+08:00
     */
    private String localUpdatedAt;
    /**
     * 文件路径
     */
    private String namePath;

    /**
     * 【如果存在就返回】媒体预览信息，目前仅AI助手使用
     */
    public MediaPreviewInfo mediaPreviewInfo;


    /**
     * 目标父目录ID，独立空间的目录ID(用于选择知识库目录上传文件的场景)
     */
    private String targetParentFileId;

    /**
     * 目标父目录路径，独立空间的路径
     */
    private String targetParentFilePath;

    public File(OwnerDriveFileVO fileInfo) {
        this.setFileId(fileInfo.getFileId());
        this.setParentFileId(fileInfo.getParentFileId());
        this.setName(fileInfo.getName());
        this.setType(fileInfo.getType());
        this.setFileExtension(fileInfo.getFileExtension());
        this.setCategory(fileInfo.getCategory());
        this.setCreatedAt(fileInfo.getCreatedAt());
        this.setUpdatedAt(fileInfo.getUpdatedAt());
        this.setSize(fileInfo.getSize());
        this.setContentHash(fileInfo.getContentHash());
        this.setContentHashAlgorithm(fileInfo.getContentHashAlgorithm());

        if (fileInfo.getAddressDetail() != null) {
            AddressDetail detail = new AddressDetail();
            detail.setAddressline(fileInfo.getAddressDetail().getAddressline());
            detail.setCountry(fileInfo.getAddressDetail().getCountry());
            detail.setProvince(fileInfo.getAddressDetail().getProvince());
            detail.setCity(fileInfo.getAddressDetail().getCity());
            detail.setDistrict(fileInfo.getAddressDetail().getDistrict());
            detail.setTownship(fileInfo.getAddressDetail().getTownship());
            this.setAddressDetail(detail);
        }
        if (fileInfo.getMediaMetaInfo() != null) {
            MediaMetaInfo info = new MediaMetaInfo();
            info.setDuration(fileInfo.getMediaMetaInfo().getDuration());
            info.setHeight(fileInfo.getMediaMetaInfo().getHeight());
            info.setTakenAt(fileInfo.getMediaMetaInfo().getTakenAt());
            info.setWidth(fileInfo.getMediaMetaInfo().getWidth());
            this.setMediaMetaInfo(info);
        }
        if(fileInfo.getMediaPreviewInfo() != null){
            MediaPreviewInfo info = new MediaPreviewInfo();
            info.setStatus(fileInfo.getMediaPreviewInfo().getStatus());
            info.setUrl(fileInfo.getMediaPreviewInfo().getUrl());
            this.setMediaPreviewInfo(info);
        }
    }

    public File(String fileId) {
        this.fileId = fileId;
    }

}
