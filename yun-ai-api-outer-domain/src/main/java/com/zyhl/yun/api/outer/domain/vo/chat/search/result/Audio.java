package com.zyhl.yun.api.outer.domain.vo.chat.search.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 对话信息-搜索结果-笔记-接口结果-相关录音
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Audio implements Serializable {

    /**
     * 语音转换状态
     * 0：待转换
     * 1：转换中
     * 2：转换完成
     * -1：转换失败
     */
    private Integer audioStatus;

    /** 语音笔记创建时间，时间戳，字符串 */
    private String audioCTime;

    /** 语音内容更新时间，时间戳，字符串 */
    private String audioUpTime;

    /** 录音名称，字符串 */
    private String audioName;

    /** 录音时长，单位为ms */
    private Long audioDuration;

    /** 录音大小，单位b */
    private Long audioSize;

    /** 语音内容，离线转换语音后的内容，服务端解析后的内容 */
    private String audioContent;

    /** 原始语音内容，咪咕平台回调后的原始json字符串 */
    private String originalAudioContent;

}
