package com.zyhl.yun.api.outer.util;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.yun.api.outer.constants.RegConst;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.exception.SseApplicationException;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.Map;

/**
 * 请求上下文
 *
 * <AUTHOR>
 */
public class RequestContextHolder {

    /**
     * 私有构造函数，防止实例化
     */
    private RequestContextHolder() {
    }

    /**
     * ------------------------- 请求头信息 -------------------------
     */
    public static void clear() {
        REQ_HEADERS_INFO.remove();
        HEADER_PARAMS.remove();
        USER_INFO.remove();
        REQUEST_ID_LOCAL.remove();
        THREAD_LOCAL.remove();
        SSE_APPLICATION_EXCEPTION_THREAD_LOCAL.remove();
        BUSINESS_INFO.remove();
    }

    /**
     * 线程全部信息
     * @Author: WeiJingKun
     */
    @Data
    @Builder
    public static class ThreadLocalInfo {
        /** http原始请求头信息 */
        private Map<String, String> requestHeaders;
        /** 请求id */
        private String requestId;
        /** 用户token */
        private String token;
        /** 处理后请求头信息 */
        private HeaderParams headerParams;
        /** 用户信息 */
        private UserInfo userInfo;
        /** 业务信息 */
        private BusinessInfo businessInfo;
        /** sse异常 */
        private SseApplicationException sseApplicationException;
    }

    /**
     * 获取线程全部信息，并且异步调用前，将父线程中的请求信息绑定给子线程
     * @Author: WeiJingKun
     */
    public static ThreadLocalInfo getThreadLocalInfoAndBindingAttributes() {
        /** 异步调用前，将父线程中的请求信息绑定给子线程 */
        org.springframework.web.context.request.RequestContextHolder.setRequestAttributes(org.springframework.web.context.request.RequestContextHolder.getRequestAttributes(), true);
        return getThreadLocalInfo();
    }

    /**
     * 获取线程全部信息
     * @Author: WeiJingKun
     */
    public static ThreadLocalInfo getThreadLocalInfo() {
        return ThreadLocalInfo.builder()
                /** http原始请求头信息 */
                .requestHeaders(getRequestHeaders())
                /** 请求id */
                .requestId(getRequestId())
                /** 用户token */
                .token(getToken())
                /** 处理后请求头信息 */
                .headerParams(getHeaderParams())
                /** 用户信息 */
                .userInfo(getUserInfo())
                /** 业务信息 */
                .businessInfo(getBusinessInfo())
                /** sse异常 */
                .sseApplicationException(getSseException())
                .build();
    }

    /**
     * 把主线程ThreadLocal信息set到子线程
     * @param mainThreadLocalInfo 主线程ThreadLocal信息
     * @Author: WeiJingKun
     */
    public static void mainThreadInfoSetToSubThread(ThreadLocalInfo mainThreadLocalInfo) {
        // set-http原始请求头信息
        setRequestHeaders(mainThreadLocalInfo.getRequestHeaders());
        // set-请求id
        setRequestId(mainThreadLocalInfo.getRequestId());
        // set-用户token
        setToken(mainThreadLocalInfo.getToken());
        // set-处理后请求头信息
        setHeaderParams(mainThreadLocalInfo.getHeaderParams());
        // set-用户信息
        setUserInfo(mainThreadLocalInfo.getUserInfo());
        // set-业务信息
        setBusinessInfo(mainThreadLocalInfo.getBusinessInfo());
        // set-sse异常
        setSseException(mainThreadLocalInfo.getSseApplicationException());
    }

    /** ------------------------- 请求头信息 ------------------------- */
    /** http原始请求头信息 */
    private static final ThreadLocal<Map<String, String>> REQ_HEADERS_INFO = new ThreadLocal<>();

    public static Map<String, String> getRequestHeaders() {
        return REQ_HEADERS_INFO.get();
    }

    public static void setRequestHeaders(Map<String, String> requestHeaders) {
        REQ_HEADERS_INFO.set(requestHeaders);
    }

    public static String getRequestHeadersField(String field) {
        Map<String, String> headers = getRequestHeaders();
        if (headers != null) {
            return headers.getOrDefault(field, "");
        }
        return "";
    }

    /** 处理后请求头信息 */
    public static final ThreadLocal<HeaderParams> HEADER_PARAMS = new ThreadLocal<>();

    public static void setHeaderParams(HttpServletRequest request) {
        HEADER_PARAMS.set(new HeaderParams(request));
    }

    public static void setHeaderParams(HeaderParams params) {
        HEADER_PARAMS.set(params);
    }

    public static HeaderParams getHeaderParams() {
        return HEADER_PARAMS.get();
    }

    public static long getRequestTime() {
        HeaderParams params = getHeaderParams();
        return params == null ? 0 : params.getRequestTime();
    }

    public static String getUri() {
        HeaderParams params = getHeaderParams();
        return params == null ? "" : CharSequenceUtil.nullToEmpty(params.getUri());
    }

    public static String getApiVersion() {
        HeaderParams params = getHeaderParams();
        return params == null ? "" : CharSequenceUtil.nullToEmpty(params.getApiVersion());
    }

    public static String getClientType() {
        HeaderParams params = getHeaderParams();
        return params == null ? "" : CharSequenceUtil.nullToEmpty(params.getClientType());
    }

    public static String getClientVersion() {
        HeaderParams params = getHeaderParams();
        return params == null ? "" : CharSequenceUtil.nullToEmpty(params.getClientVersion());
    }

    public static String getH5Version() {
        HeaderParams params = getHeaderParams();
        return params == null ? "" : CharSequenceUtil.nullToEmpty(params.getH5Version());
    }
    
    public static String getPcVersion() {
        HeaderParams params = getHeaderParams();
        return params == null ? "" : CharSequenceUtil.nullToEmpty(params.getPcVersion());
    }

    public static String getClientInfo() {
        HeaderParams params = getHeaderParams();
        return params == null ? "" : CharSequenceUtil.nullToEmpty(params.getClientInfo());
    }

    public static String getAppChannel() {
        HeaderParams params = getHeaderParams();
        return params == null ? "" : CharSequenceUtil.nullToEmpty(params.getAppChannel());
    }

    public static String getXYunTid() {
        HeaderParams params = getHeaderParams();
        return params == null ? "" : CharSequenceUtil.nullToEmpty(params.getXYunTid());
    }

    public static String getAcceptLanguage() {
        HeaderParams params = getHeaderParams();
        return params == null ? "" : CharSequenceUtil.nullToEmpty(params.getAcceptLanguage());
    }

    /** ------------------------- 请求头信息 ------------------------- */

    /**
     * ------------------------- 用户信息 -------------------------
     */
    private static final ThreadLocal<UserInfo> USER_INFO = new ThreadLocal<>();

    public static void setUserInfo(UserInfo info) {
        USER_INFO.set(info);
    }

    public static void setUserInfo(Long userId, String phoneNumber, Integer belongsPlatform) {
        USER_INFO.set(new UserInfo(String.valueOf(userId), phoneNumber, belongsPlatform));
    }

    public static UserInfo getUserInfo() {
        return USER_INFO.get();
    }

    public static void setUserId(String userId) {
        UserInfo info = getUserInfo();
        if (info == null) {
            setUserInfo(info = new UserInfo());
        }
        info.setUserId(userId);
    }

    public static String getUserId() {
        UserInfo info = getUserInfo();
        return info == null ? "" : info.getUserId();
    }

    public static String getPhoneNumber() {
        UserInfo info = getUserInfo();
        return info == null ? "" : info.getPhoneNumber();
    }

    public static Integer getBelongsPlatform() {
        UserInfo info = getUserInfo();
        return info == null ? null : info.getBelongsPlatform();
    }


    /** ------------------------- 用户信息 ------------------------- */

    /**
     * ------------------------- 其他信息信息 -------------------------
     */

    private static final ThreadLocal<String> REQUEST_ID_LOCAL = new ThreadLocal<>();
    private static final ThreadLocal<String> THREAD_LOCAL = new ThreadLocal<>();
    private static final ThreadLocal<SseApplicationException> SSE_APPLICATION_EXCEPTION_THREAD_LOCAL = new ThreadLocal<>();
    private static final ThreadLocal<BusinessInfo> BUSINESS_INFO = new ThreadLocal<>();

    public static void setRequestId(String requestId) {
        REQUEST_ID_LOCAL.set(requestId);
    }

    public static String getRequestId() {
        return REQUEST_ID_LOCAL.get();
    }

    public static String getToken() {
        return THREAD_LOCAL.get();
    }

    public static void setToken(String token) {
        THREAD_LOCAL.set(token);
    }

    public static SseApplicationException getSseException() {
        return SSE_APPLICATION_EXCEPTION_THREAD_LOCAL.get();
    }

    public static void setSseException(SseApplicationException e) {
        SSE_APPLICATION_EXCEPTION_THREAD_LOCAL.set(e);
    }

    /**
     * 获取业务信息【渠道->业务类型->助手类型】
     * @return
     */
    public static BusinessInfo getBusinessInfo() {
    	return BUSINESS_INFO.get();
    }
    public static String getBusinessType() {
    	BusinessInfo info = getBusinessInfo();
    	if(null != info) {
    		return info.getBusinessType();
    	}
    	return null;
    }
    
    public static String getSourceChannel() {
    	BusinessInfo info = getBusinessInfo();
    	if(null != info) {
    		return info.getSourceChannel();
    	}
    	return null;
    }
    
    public static AssistantEnum getAssistantEnum() {
    	BusinessInfo info = getBusinessInfo();
    	if(null != info) {
    		return info.getAssistantEnum();
    	}
    	return null;
    }
    
    public static void setBusinessInfo(BusinessInfo businessInfo) {
    	BUSINESS_INFO.set(businessInfo);
    }
    /* ------------------------- 其他信息信息 ------------------------- */
    /**
     * 处理后请求头信息
     */
    @Data
    public static class HeaderParams {
        /**
         * 请求时间（毫秒）
         */
        private long requestTime;

        /**
         * 请求地址
         */
        private String uri;

        /**
         * 请求链路标识
         */
        private String xYunTid;

        /**
         * 应用渠道
         */
        private String appChannel;
        /**
         * api版本
         */
        private String apiVersion;
        /**
         * 平台信息
         */
        private String platformInfo;
        /**
         * 客户端信息
         */
        private String clientInfo;

        /**
         * 客户端类型
         */
        private String clientType;
        /**
         * 客户端版本
         */
        private String clientVersion;
        /**
         * h5版本
         */
        private String h5Version;
        
        /**
         * pc版本
         */
        private String pcVersion;

        /**
         * 简体中文：zh-CN（默认）
         * 英文：en-US
         * 繁体中文：zh-HK
         */
        private String acceptLanguage;
        
        
        /**
         * 终端公司灵犀请求头参数
         */
        private LingxiHeaderParam lingxiHeaderParam;

        public HeaderParams() {

        }

        public HeaderParams(HttpServletRequest request) {
            this.requestTime = System.currentTimeMillis();
            this.uri = request.getRequestURI();
            LingxiHeaderParam thisLingxiHeaderParam = new LingxiHeaderParam();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String key = headerNames.nextElement();
                if (ReqHeadConst.APP_CHANNEL.equalsIgnoreCase(key)) {
                    this.appChannel = request.getHeader(key);
                } else if (ReqHeadConst.API_VERSION.equalsIgnoreCase(key)) {
                    this.apiVersion = request.getHeader(key);
                } else if (ReqHeadConst.PLATFORM_INFO.equalsIgnoreCase(key)) {
                    this.platformInfo = request.getHeader(key);
                } else if (ReqHeadConst.X_YUN_TID.equalsIgnoreCase(key)) {
                    this.xYunTid = request.getHeader(key);
                } else if (ReqHeadConst.CLIENT_INFO.equalsIgnoreCase(key)) {
                    // 网络类型|用户终端IP|客户端类型|客户端当前版本|设备品牌|设备型号|设备标识|设备MAC地址|操作系统和版本|设备分辨率|客户端语言类型|设备经度|设备纬度|设备名称|客户端上架的应用市场编码
                    this.clientInfo = CharSequenceUtil.emptyToDefault(request.getHeader(key), "");
                    String[] clientInfoArr = this.clientInfo.split(RegConst.REG_SPLIT_STR);
                    for (int i = 0, len = clientInfoArr.length; i < len; i++) {
                        if (i == 2) {
                            this.clientType = clientInfoArr[i];
                        } else if (i == 3) {
                            this.clientVersion = clientInfoArr[i];
                        }
                    }
                } else if (ReqHeadConst.ACCEPT_LANGUAGE.equalsIgnoreCase(key)){
                    this.acceptLanguage = request.getHeader(key);
				} else if (ReqHeadConst.LINGXI_KEY_OF_X_USER_TOKEN.equalsIgnoreCase(key)) {
					thisLingxiHeaderParam.setStToken(request.getHeader(key));
				} else if (ReqHeadConst.LINGXI_KEY_OF_SOURCE_ID.equalsIgnoreCase(key)) {
					thisLingxiHeaderParam.setSourceId(request.getHeader(key));
				} else if (ReqHeadConst.LINGXI_KEY_OF_TS.equalsIgnoreCase(key)) {
					thisLingxiHeaderParam.setTs(request.getHeader(key));
				} else if (ReqHeadConst.LINGXI_KEY_OF_SIGN.equalsIgnoreCase(key)) {
					thisLingxiHeaderParam.setSign(request.getHeader(key));
				} else if (ReqHeadConst.LINGXI_KEY_OF_AUTHORIZATION.equalsIgnoreCase(key)) {
					//兼容云盘 basic token
					thisLingxiHeaderParam.setBasicToken(request.getHeader(key));
				}
            }
            this.lingxiHeaderParam = thisLingxiHeaderParam;
        }

        public HeaderParams setClientType(String clientType) {
            this.clientType = clientType;
            return this;
        }
    }

	/**
	 * 请求头-灵犀终端参数
	 */
	@Data
	public static class LingxiHeaderParam {
		/**
		 * 云盘basic token
		 */
		private String basicToken;
		/**
		 * 统一认证token
		 */
		private String stToken;
		/**
		 * 统一认证sourceId
		 */
		private String sourceId;
		/**
		 * 时间戳
		 */
		private String ts;
		/**
		 * 认证参数
		 */
		private String sign;
	}
	
    /**
     * 用户信息
     */
    @Data
    public static class UserInfo {
        private String userId;
        private String phoneNumber;
        private Integer belongsPlatform;

        public UserInfo() {
        }

        public UserInfo(String userId, String phoneNumber, Integer belongsPlatform) {
            this.userId = userId;
            this.phoneNumber = phoneNumber;
            this.belongsPlatform = belongsPlatform;
        }
    }
    
    /**
     * 业务信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BusinessInfo{
    	private String sourceChannel;
    	private String businessType;
    	private AssistantEnum assistantEnum;
    }
    
}
