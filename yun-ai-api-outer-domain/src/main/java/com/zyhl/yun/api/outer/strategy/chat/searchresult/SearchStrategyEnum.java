package com.zyhl.yun.api.outer.strategy.chat.searchresult;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.*;
import com.zyhl.yun.api.outer.strategy.chat.searchresult.impl.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 搜索策略枚举类
 * @Author: WeiJingKun
 */
@Getter
@AllArgsConstructor
@Slf4j
public enum SearchStrategyEnum {

    /**
     * 【图片】搜索策略
     */
    IMAGE(SearchImageParam.class, new ImageSearchStrategy()),

    /**
     * 【文件-个人资产】搜索策略
     */
    FILE(SearchFileParam.class, new FileSearchStrategy()),

    /**
     * 【笔记】搜索策略
     */
    NOTE(SearchNoteParam.class, new NoteSearchStrategy()),

    /**
     * 【功能】搜索策略
     */
    FUNCTION(SearchFunctionParam.class, new FunctionSearchStrategy()),

    /**
     * 【活动】搜索策略
     */
    ACTIVITY(SearchActivityParam.class, new ActivitySearchStrategy()),

    /**
     * 【发现广场】搜索策略
     */
    DISCOVERY(SearchDiscoveryParam.class, new DiscoverySearchStrategy()),

    /**
     * 【我的圈子】搜索策略
     */
    MY_GROUP(SearchMyGroupParam.class, new MyGroupSearchStrategy()),

    /**
     * 【热门圈子】搜索策略
     */
    RECOMMEND_GROUP(SearchRecommendGroupParam.class, new RecommendGroupSearchStrategy()),

    /**
     * 【邮件】搜索策略
     */
    MAIL(SearchMailParam.class, new MailSearchStrategy()),

    /**
     * 【邮件附件】搜索策略
     */
    MAIL_ATTACHMENT(SearchMailAttachmentParam.class, new MailAttachmentSearchStrategy()),

    /**
     * 【知识库资源】搜索策略
     */
    KNOWLEDGE_BASE_RESOURCE(SearchKnowledgeBaseResourceParam.class, new KnowledgeBaseResourceSearchStrategy()),

    ;

    private static final Map<Class<?>, SearchStrategyEnum> MAP = new ConcurrentHashMap<>();
    static {
        EnumSet.allOf(SearchStrategyEnum.class).forEach(item -> MAP.put(item.searchParamClass, item));
    }

    /**
     * 根据搜索参数类获取搜索策略
     * @Author: WeiJingKun
     *
     * @param searchParamClass 搜索参数类
     * @return 搜索策略
     */
    public static SearchStrategy getSearchStrategyByKey(Class<?> searchParamClass) {
        if (ObjectUtil.isNotNull(searchParamClass)) {
            SearchStrategyEnum searchStrategyEnum = MAP.get(searchParamClass);
            if(null != searchStrategyEnum){
                return searchStrategyEnum.getStrategy();
            }
        }
        log.error("不支持该搜索类别，getSearchStrategyByKey：{} ", searchParamClass);
        return null;
    }

    /** 搜索参数类 */
    private final Class<?> searchParamClass;
    /** 搜索策略 */
    private final SearchStrategy strategy;

}
