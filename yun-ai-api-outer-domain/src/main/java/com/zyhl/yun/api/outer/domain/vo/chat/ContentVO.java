package com.zyhl.yun.api.outer.domain.vo.chat;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.vo.ChatApplicationType;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.domain.vo.chat.search.SearchInfo;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchResult;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.task.FileExpiredStatusEnum;
import com.zyhl.yun.api.outer.enums.task.TaskFeePaidStatusEnum;
import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

/**
 * <AUTHOR>
 * @data 2024/3/4 10:13
 */
@Data
public class ContentVO {

	/** 对话停止后，outContent字段的值 */
	public static final String CHAT_STOP_OUT_CONTENT = "已停止";

	/**
	 * 对话ID
	 */
	private String dialogueId;
	/**
	 * 用户id
	 */
	private String userId;
	/**
	 * 会话ID
	 */
	private String sessionId;
	
	/**
	 * 状态 0--对话中；默认0 1--对话停止 2--对话失败 3--对话成功 4--对话终止（非正常结束） 5--对话过期
	 */
	private Integer status;
		
	/**
	 * 对话类型;0:对话历史记录,1:智囊历史记录
	 */
	private Integer talkType;
	/**
	 * 资源类型;0-无，1 邮件， 2 笔记， 3 图片
	 */
	private Integer resourceType;
	/**
	 * 工具指令;对接意图指令
	 */
	private String toolsCommand;
	/**
	 * 工具指令;对接意图指令-子意图
	 */
	private String subToolsCommand;
	/**
	 * 模型类型;模型 qwen：通义千问，xfyun：讯飞星火大模型
	 */
	private String modelType;
	/**
	 * 输入内容;输入文本内容
	 */
	private String inContent;
	/**
	 * 输入时间
	 */
	@JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
	private Date inAuditTime;
	/**
	 * 输入内容审批结果;状态码：2通过，其他失败
	 */
	private Integer inAuditStatus;
	/**
	 * 输入资源ID;（笔记/邮件/图片ID；纯文本时为空）
	 */
	private String inResourceId;
	/**
	 * 输出内容
	 */
	private String outContent;

	/** 思维链过程 */
	private String reasoningContent;

	/** 大模型联网搜索结果 */
	private List<AiTextResultRespParameters.NetworkSearchInfo> networkSearchInfoList;

	/**
	 * 输出时间
	 */
	@JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
	private Date outAuditTime;
	/**
	 * 输出内容审批结果;状态码：2通过，其他失败
	 */
	private Integer outAuditStatus;
	/**
	 * 输出资源类型： 1--云盘文件ID 2--文件下载地址 3--
	 * 
	 * @see com.zyhl.yun.api.outer.enums.ImageTransmissionTypeEnum
	 */
	private Integer outResourceType;
	/**
	 * 输出资源信息；（云盘文件ID/下载URL）
	 */
	private String outResourceId;
	
	/**
	 * 输出文本类型：1--普通文本 2--富文本
	 * @see com.zyhl.yun.api.outer.enums.OutContentTypeEnum
	 */
	private Integer outContentType;
	/**
	 * 渠道来源
	 */
	private String sourceChannel;
	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
	private Date createTime;
	/**
	 * 修改时间
	 */
	@JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
	private Date updateTime;

	/**
	 * 是否删除;（1是0否）
	 */
	private Integer delFlag;
	/**
	 * 提示词，比如：总结概要
	 */
	private String prompt;
	/**
	 * 【不是前端使用的字段】对话状态
	 *
	 * @see ChatStatusEnum
	 */
	private Integer chatStatus;

	/**
	 * 业务场景标识：app_writemail_ai（邮箱APP-写信AI）
	 */
	private String sceneTag;
	/**
	 * 命令类型
	 * 1--普通命令（默认）
	 * 2--自动命令
	 *
	 * @see com.zyhl.yun.api.outer.enums.chat.DialogueCommandTypeEnum
	 */
	private Integer commandType;

	/** 引导文案 */
	private LeadCopyVO leadCopy;

	/**
	 * 扩展信息（json格式）
	 */
	private String extInfo;

	/**
	 * 是否喜欢 0:不喜欢，1:喜欢
	 */
	private Integer likeComment;

	/**
	 * 默认评论
	 */
	private String defaultComment;
	/**
	 * 用户评论
	 */
	private String customComment;

	/**
	 * 任务-错误结果码
	 */
	private String resultCode;

	/**
	 * 任务-错误信息
	 */
	private String resultMsg;

	/**
	 * 任务-状态
	 * 
	 * @see TaskStatusEnum
	 */
	private Integer taskStatus;

	/** 搜索参数 */
	private SearchParam searchParam;

	/** 搜索结果 */
	private SearchResult searchResult;

	/** 对话应用类型 */
	private ChatApplicationType applicationInfo;

    /**
     * 付费扣费状态：
     *
     * -1：不扣费（该任务不涉及扣费流程） 0：未扣费 1：已扣费
     *
     * @see TaskFeePaidStatusEnum
     */
    private Integer feePaidStatus;

	/**
	 * 对话结果推荐，当对话信息不为空时返回
	 * 如果leadCopy不为空，优先展示leadCopy
	 */
	private DialogueRecommendVO recommend;

	/**
	 * 对话结果推荐-中部，当对话信息不为空时返回
	 */
	private DialogueRecommendVO middleRecommend;
	
	/**
	 * 返回结果的标题
	 */
	private String title;

	/**
	 * 个人知识库参考文件，可选，仅第一次流式结果返回
	 */
	private List<File> personalKnowledgeFileList;

	/** 搜索信息列表 */
	private List<SearchInfo> searchInfoList;

	/** ============以下字段为代码逻辑字段，不是前端使用的字段============== */
	/** 任务ID */
	private String taskId;

	/**
	 * 文件过期状态： 0：未过期 1：已过期
	 * 
	 * @see FileExpiredStatusEnum
	 */
	private Integer fileExpiredStatus;

	/** 对话结果推荐信息（json格式） */
	private String recommendInfo;
	
    /**
     * 对话结果推荐信息-中部（json格式）
     */
    private String middleRecommendInfo;

	/**
	 * hbase的req字段值
	 */
	private String hbaseReqParameters;

	/**
	 * hbase的respParameters字段值
	 */
	private String hbaseRespParameters;

	/**
	 * 邮件信息
	 */
	private MailInfoVO mailInfo;

	/**
	 * algorithm_task_ai_ability
	 * 算法任务表执行结果响应参数
	 * 针对图片智能鉴伪添加该参数进行数据流转
	 */
	private String respParam;
}
