package com.zyhl.yun.api.outer.domain.req;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * 搜索实体
 *
 * @author: yangkailun
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchEntity {

    /**
     * 搜索条件
     */
    @NotEmpty(message = "搜索条件不能为空")
    private ConditionEntity conditions;

    /**
     * 分页信息
     */
    @Valid
    private SearchPageEntity pageInfo;


    @Data
    @NoArgsConstructor
    public static class ConditionEntity {

        /**
         * 关键字
         */
        @NotEmpty(message = "关键字不能为空")
        private String keyword;

        /**
         * 算法类型 1-科大讯飞 2-阿里 3-华为
         */
        private String algorithmType;

    }


    @Data
    @NoArgsConstructor
    public static class SearchPageEntity {

        /**
         * 起始游标，为空时从第一页开始查询
         */
        private String pageCursor;

        /**
         * 每页显示数
         */
        private String pageSize;

    }

}
