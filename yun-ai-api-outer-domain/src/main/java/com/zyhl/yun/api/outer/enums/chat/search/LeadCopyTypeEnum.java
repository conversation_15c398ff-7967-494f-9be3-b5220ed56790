package com.zyhl.yun.api.outer.enums.chat.search;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 对话结果引导文案-场景类型
 *
 * @Author: WeiJingKun
 */
public enum LeadCopyTypeEnum {

    /**
     * 返回可直接在AI助手完成执行的工具
     */
    TYPE1(1, "返回可直接在AI助手完成执行的工具"),

    /**
     * 返回不可在AI助手完成执行的H5工具
     */
    TYPE2(2, "返回不可在AI助手完成执行的H5工具"),

    /**
     * 返回不可在AI助手完成执行的小程序工具
     */
    TYPE3(3, "返回不可在AI助手完成执行的小程序工具"),

    /**
     * 返回文本提示语（目前用于024创建笔记和027创建语音笔记意图）
     */
    TYPE4(4, "返回文本提示语（目前用于024创建笔记和027创建语音笔记意图）"),

    /**
     * 图片工具意图推荐，返回推荐的图片工具意图（用户上传了图片，但是输入文本识别为非图片工具意图）
     */
    TYPE5(5, "图片工具意图推荐，返回推荐的图片工具意图（用户上传了图片，但是输入文本识别为非图片工具意图）"),

    /**
     * 搜索结果引导，语义实体为空、搜索条件为空或搜索结果为空时的返回，仅返回提示文案promptCopy
     */
    TYPE6(6, "搜索结果引导，语义实体为空、搜索条件为空或搜索结果为空时的返回，仅返回提示文案promptCopy"),

    /**
     * 知识库引导，输入知识库意图时返回
     */
    TYPE7(7, "知识库引导，输入知识库意图时返回"),

    /**
     * 知识库引导，输入知识库意图时返回
     */
    TYPE8(8, "人物相册引导，搜索人物关系时返回"),

    ;

    private static final Map<Integer, LeadCopyTypeEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(LeadCopyTypeEnum.class).forEach(item -> MAP.put(item.code, item));
    }

    public static LeadCopyTypeEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return MAP.get(code);
    }

    /**
     * 是否存在
     *
     * @param code 编码
     * @return true-存在
     */
    public static boolean isExist(Integer code) {
        return getByCode(code) != null;
    }

    LeadCopyTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 编码
     */
    private Integer code;

    /**
     * 描述
     */
    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
