package com.zyhl.yun.api.outer.domain.dto.common;

import cn.hutool.core.text.CharSequenceUtil;

import org.apache.commons.lang3.StringUtils;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description 分页信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class PageInfoDTO {

	/**
	 * 开始分页游标
	 */
	private static final String DEFAULT_START_PAGE_CURSOR = "0";
;
    /**
     * 起始游标，为空时从第一页开始查询。对于传统分页，含义实际为pageCurrent+1，如果没有下一页则返回空
     */
    @Builder.Default
    private String pageCursor = "";

    /**
     * 每页显示数量默认为10(可配置)，最大限制100（部分接口限制大于100），最小限制5
     * 注：返回资源数量可能⼩于指定的数量，但不会多于指定的数量。是否还有下一页数据，以下一页起始游标nextPageCursor 为准（接口响应有特殊说明的除外）
     */
    @Builder.Default
    private Integer pageSize = 10;

    /**
     * 是否返回总数(0不返回，1返回)，默认值为0。
     */
    @Builder.Default
    private Integer needTotalCount = 0;

    public boolean isNeedTotal() {
        return needTotalCount != null && needTotalCount == 1;
    }

    /**
     * 分页信息校验
     *
     * @Author: WeiJingKun
     */
    public void validate() {
        //默认最小分页5
        validate(5);
    }

    /**
     * 分页信息校验
     *
     * @Author: WeiJingKun
     */
    public void validate(int minPageSize) {
        // 分页数量最小minPageSize
        if (pageSize < minPageSize) {
            log.error("分页数量最小" + minPageSize);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS.getResultCode(), ("分页数量最小" + minPageSize));
        }

        // 分页数量最大maxPageSize
        if (pageSize > 200) {
            log.error("分页数量最大" + 200);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS.getResultCode(), ("分页数量最大" + 100));
        }

        // 是否返回总数，取值只有0和1
        if (needTotalCount < 0 || needTotalCount > 1) {
            log.error("是否返回总数参数值有误");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS.getResultCode(), "是否返回总数参数值有误");
        }
    }

    /**
     * 分页参数构建重写
     * @param dto
     * @return
     */
    public static PageInfoDTO getReqDTO(PageInfoDTO dto) {
		PageInfoDTO page = dto == null ? new PageInfoDTO(DEFAULT_START_PAGE_CURSOR, 10, 0) : dto;
        if (CharSequenceUtil.isEmpty(page.getPageCursor())) {
            page.setPageCursor(DEFAULT_START_PAGE_CURSOR);
        }
        if (page.getPageSize() == null) {
            page.setPageSize(10);
        }
        if (page.getNeedTotalCount() == null) {
            page.setNeedTotalCount(0);
        }
        return page;
    }
    
    /**
     * 是否分页开始
     * @param page
     * @return
     */
	public static boolean isStartPage(PageInfoDTO page) {
		return (DEFAULT_START_PAGE_CURSOR.equals(page.getPageCursor()) || StringUtils.isEmpty(page.getPageCursor()));
	}

}
