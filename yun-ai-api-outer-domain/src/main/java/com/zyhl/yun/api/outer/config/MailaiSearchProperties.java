package com.zyhl.yun.api.outer.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

/**
 * 邮箱搜索页面搜索意图配置
 * 
 * <AUTHOR>
 * @date 2025-04-19
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "mailai-search-properties")
public class MailaiSearchProperties {

    /**
     * 搜索意图编码
     */
    private List<String> searchIntention;

    /**
     * 意图拼接前缀
     */
    private String splicingPrefix;

    /**
     * 搜索结果为空时，提示语
     */
    private String noResultPromptCopy;

    /**
     * 普通对话搜索结果为空时，提示语
     */
    private String dialogueSearchNoResult;

    /**
     * 不同渠道来源设置不同搜索意图
     */
    private Map<String, List<String>> searchSourceChannelIntention;

    /**
     * 所有搜索意图列表
     * 包括盘内搜索意图、站内搜索意图、知识库搜索意图、综合搜索意图等
     */
    private List<String> allSearchIntentions;

    /**
     * 综合搜索子意图排除列表
     */
    private List<String> excludeComprehensiveSearchIntentions;
    /**
     * 搜索意图前缀,有这些前缀的用户输入，不添加”搜索“前缀
     */
    private List<String> searchIntentionPrefix;

    /**
     * 是否统一转接意图
     */
    private boolean transferIntention;

    /**
     * 是否走全网搜流程
     */
    private boolean autoAllNetworkSearch;

}
