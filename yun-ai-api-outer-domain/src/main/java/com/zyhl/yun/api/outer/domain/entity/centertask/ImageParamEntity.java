package com.zyhl.yun.api.outer.domain.entity.centertask;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 创建ai算法任务-图片类-请求参数
 *
 * @Author: WeiJingKun
 */
@Data
public class ImageParamEntity implements Serializable {

    private static final long serialVersionUID = 7098923883586790869L;

    /**
     * userId_对话id 用于查询hbase数据
     */
    private String rowkey;

    /**
     * 图片传输类型 ImageParamTypeEnum 0、图片本地共享存储 1、图片url地址 2、图片base64数据，不带data:image/jpeg;base64头
     * 3、图片文件fileId
     */
    @NotBlank(message = "图片传输类型-缺失")
    private Integer imageParamType;

    /**
     * 图片url地址，sendType=1必传
     */
    private String fileUrl;

    /**
     * 图片Base64编码的内容,需要去掉编码头部，sendType=2必传
     */
    private String base64;

    /**
     * 图片文件fileId，sendType=3必传
     */
    private String fileId;

    /**
     * 图片文件共享存储
     */
    private String localPath;

    /**
     * 图片扩展名，例如：jpg、png等
     */
    @NotNull(message = "图片扩展名-缺失")
    private String imageExt;

    /**
     * 风格类型
     */
    private String style;

    private String userId;

    /**
     * 图片存储类型
     *
     * @see com.zyhl.yun.api.outer.enums.ImageTransmissionTypeEnum
     */
    private Integer imageTransmissionType;


    /**
     * 工具是否付费false不需要付费；true需要付费，不传默认不需要付费
     */
    private Boolean toolPay;

    /**
     * 保存到个人云的路径。例如：/我的应用收藏/AI助手
     */
    private String yunPath = "";

    /**
     * 非必须	扩展字段，根据接口需要自行处理
     */
    private String extendField;
}
