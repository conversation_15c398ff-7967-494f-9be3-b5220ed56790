package com.zyhl.yun.api.outer.domainservice;

import com.zyhl.yun.api.outer.domain.entity.AiPopUpProtocolEntity;
import com.zyhl.yun.api.outer.vo.PopUpProtocolQueryVO;

/**
 * AI弹窗协议配置-DomainService
 *
 * @Author: Wei<PERSON>ing<PERSON>un
 */
public interface PopUpProtocolDomainService {

    /**
     * 获取AI弹窗协议配置
     *
     * @param entity 弹窗协议对象
     * @return PopUpProtocolQueryVO
     * @Author: WeiJingKun
     */
    PopUpProtocolQueryVO protocolGet(AiPopUpProtocolEntity entity);

}
