package com.zyhl.yun.api.outer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 智能会议助手类型-枚举
 *
 * @author: liux<PERSON>wen
 * @date 2025-07-03 16:44
 */
@Getter
@AllArgsConstructor
public enum IntelligentMeetingTypeEnum {

	/**
	 * 会议通知
	 */
	MEETING_NOTICE("会议通知", DialogueIntentionEnum.TEXT_TOOL.getCode(),
			DialogueIntentionSubEnum.AI_MEETING_MAIL.getCode()),

	/**
	 * 生成PPT方案
	 */
	GEN_PPT("生成PPT方案", DialogueIntentionEnum.TEXT_TOOL.getCode(), DialogueIntentionSubEnum.AI_GENERATE_PPT.getCode()),

	/**
	 * PPT方案邮件通知
	 */
	PPT_NOTICE("PPT方案邮件通知", DialogueIntentionEnum.SEND_MAIL.getCode(), null);

	/**
	 * 类型key
	 */
	private final String typeKey;

	/**
	 * 主意图
	 */
	private final String intentionCode;

	/**
	 * 子意图
	 */
	private final String subIntentionCode;

	/**
	 * 根据typeKey获取枚举
	 * 
	 * @param typeKey 类型
	 * @return
	 */
	public static IntelligentMeetingTypeEnum getByKey(String typeKey) {
		if (null == typeKey) {
			return null;
		}
		IntelligentMeetingTypeEnum[] typeEnums = values();
		for (IntelligentMeetingTypeEnum typeEnum : typeEnums) {
			if (typeKey.equals(typeEnum.getTypeKey())) {
				return typeEnum;
			}
		}
		return null;
	}

}
