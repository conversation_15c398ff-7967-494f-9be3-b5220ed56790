package com.zyhl.yun.api.outer.domain.vo.knowledge;


import lombok.Data;

/**
 * 知识库解析文件数量统计类
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Data
public class PersonalKnowledgeCountVO {
    /**
     * 是否使用过知识库
     */
    private Boolean hasUsed;
    /**
     * 知识库数量
     */
    private Integer baseCount;
    /**
     * 知识库文件数量
     */
    private Integer fileCount;
    /**
     * 已解析文件数量
     */
    private Integer fileParsedCount;
    /**
     * 未解析文件数量
     */
    private Integer fileUnparsedCount;
    /**
     * 新成功解析文件数量
     */
    private Integer newFileParsedCount;


    public PersonalKnowledgeCountVO() {

    }

    public PersonalKnowledgeCountVO(boolean hasUsed) {
        this.hasUsed = hasUsed;
        this.baseCount = 0;
        this.fileCount = 0;
        this.fileParsedCount = 0;
        this.fileUnparsedCount = 0;
        this.newFileParsedCount = 0;
    }

}
