package com.zyhl.yun.api.outer.enums.chat;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 个人知识库分类枚举
 * @Author: WeiJingKun
 */
@Getter
@AllArgsConstructor
public enum PersonalKnowledgeCategoryEnum {

    /** 安装包 */
    APP("app", "安装包"),
    /** 压缩包 */
    ZIP("zip", "压缩包"),
    /** 图片 */
    IMAGE("image", "图片"),
    /** 文档 */
    DOC("doc", "文档"),
    /** 视频 */
    VIDEO("video", "视频"),
    /** 音频 */
    AUDIO("audio", "音频"),
    /** 目录 */
    FOLDER("folder", "目录"),
    /** 其他 */
    OTHERS("others", "其他");

    /**
     * code
     */
    private final String code;
    /**
     * 描述
     */
    private final String desc;


    private static final Map<String, PersonalKnowledgeCategoryEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(PersonalKnowledgeCategoryEnum.class).forEach(item -> MAP.put(item.code, item));
    }

    public static PersonalKnowledgeCategoryEnum getType(String code) {
        if (null == code) {
            return null;
        }
        return MAP.get(code);
    }
}
