package com.zyhl.yun.api.outer.external;

import com.zyhl.yun.api.outer.domain.vo.chat.search.param.*;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.*;

/**
 * 文本工具-ExternalService
 * @Author: WeiJingKun
 */
public interface ApiTextExternalService {

    /**
     * 语义搜图
     *
     * @param searchImageParam 搜索参数
     * @return 搜索结果
     */
    SearchImageResult searchImage(SearchImageParam searchImageParam);

    /**
     * 个人云资产搜索
     *
     * @param searchFileParam 搜索参数
     * @return 搜索结果
     */
    SearchFileResult searchFile(SearchFileParam searchFileParam);

    /**
     * 笔记搜索
     *
     * @param searchNoteParam 搜索参数
     * @return 搜索结果
     */
    SearchNoteResult searchNote(SearchNoteParam searchNoteParam);

    /**
     * 功能搜索
     *
     * @param searchFunctionParam 搜索参数
     * @return 搜索结果
     */
    SearchFunctionResult searchFunction(SearchFunctionParam searchFunctionParam);

    /**
     * 活动搜索
     *
     * @param searchActivityParam 搜索参数
     * @return 搜索结果
     */
    SearchActivityResult searchActivity(SearchActivityParam searchActivityParam);

    /**
     * 发现广场搜索
     *
     * @param searchDiscoveryParam 搜索参数
     * @return 搜索结果
     */
    SearchDiscoveryResult searchDiscovery(SearchDiscoveryParam searchDiscoveryParam);

    /**
     * 我的圈子搜索
     *
     * @param searchMyGroupParam 搜索参数
     * @return 搜索结果
     */
    SearchMyGroupResult searchMyGroup(SearchMyGroupParam searchMyGroupParam);

    /**
     * 热门圈子搜索
     *
     * @param searchRecommendGroupParam 搜索参数
     * @return 搜索结果
     */
    SearchRecommendGroupResult searchRecommendGroup(SearchRecommendGroupParam searchRecommendGroupParam);

    /**
     * 邮件搜索
     *
     * @param searchMailParam 搜索参数
     * @return 搜索结果
     */
    SearchMailResult searchMail(SearchMailParam searchMailParam);

    /**
     * 邮件附件搜索
     *
     * @param searchAttachmentParam 搜索参数
     * @return 搜索结果
     */
    SearchMailAttachmentResult searchMailAttachment(SearchMailAttachmentParam searchAttachmentParam);

    /**
     * 知识库资源搜索
     *
     * @param searchKnowledgeBaseResourceParam 搜索参数
     * @return 搜索结果
     */
    SearchKnowledgeBaseResourceResult searchKnowledgeBaseResource(SearchKnowledgeBaseResourceParam searchKnowledgeBaseResourceParam);

}
