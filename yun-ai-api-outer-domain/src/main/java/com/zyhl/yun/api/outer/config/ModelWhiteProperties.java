package com.zyhl.yun.api.outer.config;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 特殊模型白名单用户权限配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "chat.model")
public class ModelWhiteProperties {
    /**
     *  模型白名单列表
     */
    private List<ModelWhiteConfig> modelWhiteList = new ArrayList<>();

    /**
     * 模型限制业务相关配置
     */
    @Data
    public static class ModelWhiteConfig {
        /**
         * 开关，只有false且openWhiteList不为空的列表才校验
         */
        private Boolean open = true;

        /**
         * 该模型只有白名单用户能返回或设置模型
         */
        private List<String> openWhiteList;

        /**
         * 模型名称
         */
        private String model;
    }

    /**
     * 校验当前用户是否有权限操作指定模型
     * 逻辑规则：
     * 1. 如果模型未配置白名单，则所有用户均可查看
     * 2. 如果模型配置了白名单，则仅白名单内用户可查看
     *
     * @param model 模型
     * @param phone 用户手机号（唯一标识）
     * @return true表示允许查看，false表示禁止查看
     */
    public boolean isModelAccessible(String model, String phone) {
        if (StringUtils.isAnyEmpty(model, phone)) {
            return true;
        }
        // 筛选出启用了白名单控制的模型配置
        Map<String, ModelWhiteConfig> whitelistConfigMap = this.modelWhiteList.stream().filter(modelWhiteConfig ->
                        !modelWhiteConfig.getOpen() &&
                                CollectionUtils.isNotEmpty(modelWhiteConfig.getOpenWhiteList()))
                .collect(Collectors.toMap(ModelWhiteConfig::getModel, config -> config));
        ModelWhiteConfig config = whitelistConfigMap.get(model);
        if (Objects.isNull(config)) {
            return true;
        }
        return config.getOpenWhiteList().contains(phone);
    }
}
