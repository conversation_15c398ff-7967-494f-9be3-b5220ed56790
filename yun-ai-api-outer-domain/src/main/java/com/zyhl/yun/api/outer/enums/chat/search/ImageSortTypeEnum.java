package com.zyhl.yun.api.outer.enums.chat.search;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 图片搜索排序方式
 * 排序方式（不填默认1）
 * 1--按照图片拍摄时间倒序排序（如拍摄时间为空则用上传时间）
 * 2--按照相关度倒序排序
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ImageSortTypeEnum {


    /**
     * 按照图片拍摄时间倒序排序（如拍摄时间为空则用上传时间）
     */
    TIME_SORT(1, "按照图片拍摄时间倒序排序（如拍摄时间为空则用上传时间）"),

    /**
     * 按照相关度倒序排序
     */
    CORRELATION_SORT(2, "按照相关度倒序排序"),
    ;

    private final Integer code;

    private final String msg;

    public static ImageSortTypeEnum getType(Integer code) {
        if (code == null) {
            return null;
        }
        for (ImageSortTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static boolean isExist(Integer code) {
        return getType(code) != null;
    }
}
