package com.zyhl.yun.api.outer.domain.resp;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntelligentSearchRespEntity {

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 文件列表,按照score倒叙
     */
    private List<PhotoSearchRespEntity> photos;
}
