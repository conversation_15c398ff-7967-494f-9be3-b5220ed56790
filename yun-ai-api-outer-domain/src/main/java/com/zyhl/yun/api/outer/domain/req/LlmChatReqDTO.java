package com.zyhl.yun.api.outer.domain.req;

import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelSearchOptionDTO;
import com.zyhl.yun.api.outer.domain.valueobject.llm.LlmChatInputConfig;
import com.zyhl.yun.api.outer.domain.valueobject.llm.LlmChatMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

/**
 * LLM 聊天请求参数
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class LlmChatReqDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 用户Id，默认从token获取，第三方平台调用时必填。
	 */
	private String userId;

	/**
	 * 模型编码，参考TextModelEnum枚举值，不填默认blian
	 */
	private String model;

	/**
	 * 消息列表，包含历史问答和最新的问题。多轮问答以按正序排列问题与回复。LlmChatMessage中最后一个为用户本轮问题。
	 */
	private List<LlmChatMessage> messages;

	/**
	 * 模型输入配置参数
	 */
	private LlmChatInputConfig modelConfig;

	private Boolean enableNetworkSearch;

	private TextModelSearchOptionDTO searchOption;

	private boolean disabledReasoningFlag = false;

	public LlmChatReqDTO(String userId, String model, List<LlmChatMessage> messages) {
		this.userId = userId;
		this.model = model;
		this.messages = messages;
	}

}
