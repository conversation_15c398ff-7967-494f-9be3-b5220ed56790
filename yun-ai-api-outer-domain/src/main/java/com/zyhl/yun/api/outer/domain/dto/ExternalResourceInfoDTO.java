package com.zyhl.yun.api.outer.domain.dto;

import lombok.Data;

/**
 * 资源信息DTO
 *
 * <AUTHOR>
 */
@Data
public class ExternalResourceInfoDTO {
    /**
     * 资源id
     */
    private String resourceId;
    /**
     * 资源名称
     */
    private String name;
    /**
     * 缩略图地址
     */
    private String thumbnailUrl;
    /**
     * 音视频播放地址
     */
    private String presentUrl;

    /**
     * 云盘文件分类， 根据文件的后缀名和mime-type对文件进行分了分类，主要分类有：
     * app：安装包 ；zip：压缩包 image：图片； doc：文档 video：视频 ；audio：音频 folder：目录 ；others：其他
     */
    private String category;
}
