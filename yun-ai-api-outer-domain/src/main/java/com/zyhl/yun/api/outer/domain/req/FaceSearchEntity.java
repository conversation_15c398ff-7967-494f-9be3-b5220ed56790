package com.zyhl.yun.api.outer.domain.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 人脸搜索请求参数
 * @author: yangkailun
 */
@Data
public class FaceSearchEntity {

    /**
     * 请求ID
     */
    @NotBlank(message = "请求ID不能为空")
    private String requestId;
    /**
     * 图片base64
     */
    @NotBlank(message = "图片base64不可为空")
    private String base64;
    /**
     * 文件归属：用户Id/家庭Id/群组Id/圈子Id，根据ownerType界定
     */
    @NotBlank(message = "ownerId不能为空")
    private String ownerId;

    /**
     * 业务类型
     * 1-个人云，2-圈子，3-共享群，4-家庭云
     */
    @NotBlank(message = "ownerType不能为空")
    private Integer ownerType;

}
