package com.zyhl.yun.api.outer.domainservice;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.domain.req.SearchEntity;
import com.zyhl.yun.api.outer.domain.resp.IntelligentSearchRespEntity;


/**
 * 智能查询
 *
 * @author: yangkailun
 */
public interface IntelligentSearchService {


    /**
     * 智能查询 接口
     *
     * @param entity 请求参数
     * @return 结果
     */
    BaseResult<IntelligentSearchRespEntity> intelligentSearch(SearchEntity entity);


}
