package com.zyhl.yun.api.outer.exception;

import com.zyhl.hcy.commons.enums.AbstractResultCode;

/**
 * Copyright © 2022 ChinaMobile Info. Tech Ltd. All rights reserved.
 * <p>
 * 基础业务异常
 *
 * <AUTHOR> href="<EMAIL>">ZhiFeng.Wu</a>
 * @date 2022/2/28 15:52
 */
public class BaseException extends RuntimeException{

	private final String code;
	private final String message;

	public BaseException(String code, String message) {
		super(message);
		this.code = code;
		this.message = message;
	}

	public BaseException(String code, String message, Throwable e) {
		super(message, e);
		this.code = code;
		this.message = message;
	}

	public BaseException(AbstractResultCode resultCode) {
		super(resultCode.getResultMsg());
		this.code = resultCode.getResultCode();
		this.message = resultCode.getResultMsg();
	}

	@Override
	public String getMessage() {
		return this.message;
	}

	public String getCode() {
		return this.code;
	}
}
