package com.zyhl.yun.api.outer.domain.vo.chat.search.result;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.yun.api.outer.config.SearchResultProperties;
import com.zyhl.yun.api.outer.domain.vo.chat.search.SearchInfo;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 对话信息-搜索结果
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchResult implements Serializable {

    /** 语义搜图结果【有值则可用】 */
    private SearchImageResult searchImageResult;

    /** 个人资产搜索结果【有值则可用】 */
    private SearchFileResult searchFileResult;

    /** 笔记搜索结果【有值则可用】 */
    private SearchNoteResult searchNoteResult;

    /** 活动搜索结果【有值则可用】 */
    private SearchActivityResult searchActivityResult;

    /** 功能搜索结果【有值则可用】 */
    private SearchFunctionResult searchFunctionResult;

    /** 发现广场搜索结果【有值则可用】 */
    private SearchDiscoveryResult searchDiscoveryResult;

    /** 我的圈子搜索结果【有值则可用】 */
    private SearchMyGroupResult searchMyGroupResult;

    /** 热门圈子搜索结果【有值则可用】 */
    private SearchRecommendGroupResult searchRecommendGroupResult;

    /** 邮件搜索结果【有值则可用】 */
    private SearchMailResult searchMailResult;

    /** 邮件附件搜索结果【有值则可用】 */
    private SearchMailAttachmentResult searchMailAttachmentResult;

    /** 知识库资源搜索结果【有值则可用】 */
    private SearchKnowledgeBaseResourceResult searchKnowledgeBaseResourceResult;

    /**
     * 获取搜索信息list，有值则为有结果数据
     * PS：TODO 如果有新搜索，需要添加判断
     */
    public List<SearchInfo> createSearchInfoList(List<DialogueIntentionVO.IntentionInfo> intentionInfoList,
                                                 SearchResultProperties searchResultProperties){
        // 排序开关 true表示以配置文件的排序为主 false表示以算法的排序为主
        boolean enableSort = searchResultProperties.isEnableSort();
        List<SearchInfo> searchInfoList = new ArrayList<>();
        // 获取所有命中的子意图编码（interfaceReturnIntention=true）
        List<String> subIntentionCodeList = new ArrayList<>();
        if (CollUtil.isNotEmpty(intentionInfoList) && CollUtil.isNotEmpty(intentionInfoList.get(0).getSubIntentions())){
            subIntentionCodeList = intentionInfoList.get(0).getSubIntentions().stream()
                    // 仅保留 interfaceReturnIntention=true 的元素
                    .filter(DialogueIntentionVO.IntentionInfo::isInterfaceReturnIntention)
                    .map(DialogueIntentionVO.IntentionInfo::getIntention)
                    .collect(Collectors.toList());
        }

        // 知识库资源搜索
        if(!(ObjectUtil.isNull(this.searchKnowledgeBaseResourceResult) || CollUtil.isEmpty(this.searchKnowledgeBaseResourceResult.getResourceList()))){
            SearchInfo searchInfo = this.searchKnowledgeBaseResourceResult.getSearchInfo();
            int index = 1 + subIntentionCodeList.indexOf(DialogueIntentionEnum.SEARCH_KNOWLEDGE_BASE_RESOURCE.getCode());
            if(checkAlgorithmSort(index, enableSort)){
                searchInfo.setSort(index * 10);
            }
            searchInfoList.add(searchInfo);
            // 搜索信息置空，防止重复使用
            this.searchKnowledgeBaseResourceResult.setSearchInfo(null);
        }
        // 邮件附件搜索
        if(!(ObjectUtil.isNull(this.searchMailAttachmentResult) || CollUtil.isEmpty(this.searchMailAttachmentResult.getSearchMailAttachmentList()))){
            SearchInfo searchInfo = this.searchMailAttachmentResult.getSearchInfo();
            int index = 1 + subIntentionCodeList.indexOf(DialogueIntentionEnum.SEARCH_MAIL.getCode());
            if(checkAlgorithmSort(index, enableSort)){
                searchInfo.setSort(index * 10 - 1);
            }
            searchInfoList.add(searchInfo);
            // 搜索信息置空，防止重复使用
            this.searchMailAttachmentResult.setSearchInfo(null);
        }
        // 邮件搜索
        if(!(ObjectUtil.isNull(this.searchMailResult) || CollUtil.isEmpty(this.searchMailResult.getSearchMailList()))){
            SearchInfo searchInfo = this.searchMailResult.getSearchInfo();
            int index = 1 + subIntentionCodeList.indexOf(DialogueIntentionEnum.SEARCH_MAIL.getCode());
            if(checkAlgorithmSort(index, enableSort)){
                searchInfo.setSort(index * 10);
            }
            searchInfoList.add(searchInfo);
            // 搜索信息置空，防止重复使用
            this.searchMailResult.setSearchInfo(null);
        }
        // 热门圈子搜索
        if(!(ObjectUtil.isNull(this.searchRecommendGroupResult) || CollUtil.isEmpty(this.searchRecommendGroupResult.getGroupList()))){
            SearchInfo searchInfo = this.searchRecommendGroupResult.getSearchInfo();
            int index = 1 + subIntentionCodeList.indexOf(DialogueIntentionEnum.SEARCH_GROUP.getCode());
            if(checkAlgorithmSort(index, enableSort)){
                searchInfo.setSort(index * 10 - 1);
            }
            searchInfoList.add(searchInfo);
            // 搜索信息置空，防止重复使用
            this.searchRecommendGroupResult.setSearchInfo(null);
        }
        // 我的圈子搜索
        if(!(ObjectUtil.isNull(this.searchMyGroupResult) || CollUtil.isEmpty(this.searchMyGroupResult.getGroupList()))){
            SearchInfo searchInfo = this.searchMyGroupResult.getSearchInfo();
            int index = 1 + subIntentionCodeList.indexOf(DialogueIntentionEnum.SEARCH_GROUP.getCode());
            if(checkAlgorithmSort(index, enableSort)){
                searchInfo.setSort(index * 10);
            }
            searchInfoList.add(searchInfo);
            // 搜索信息置空，防止重复使用
            this.searchMyGroupResult.setSearchInfo(null);
        }
        // 发现广场搜索
        if(!(ObjectUtil.isNull(this.searchDiscoveryResult) || CollUtil.isEmpty(this.searchDiscoveryResult.getDiscoveryList()))){
            SearchInfo searchInfo = this.searchDiscoveryResult.getSearchInfo();
            int index = 1 + subIntentionCodeList.indexOf(DialogueIntentionEnum.SEARCH_DISCOVERY.getCode());
            if(checkAlgorithmSort(index, enableSort)){
                searchInfo.setSort(index * 10);
            }
            searchInfoList.add(searchInfo);
            // 搜索信息置空，防止重复使用
            this.searchDiscoveryResult.setSearchInfo(null);
        }
        // 功能搜索
        if(!(ObjectUtil.isNull(this.searchFunctionResult) || CollUtil.isEmpty(this.searchFunctionResult.getFunctionList()))){
            SearchInfo searchInfo = this.searchFunctionResult.getSearchInfo();
            int index = 1 + subIntentionCodeList.indexOf(DialogueIntentionEnum.SEARCH_FUNCTION.getCode());
            if(checkAlgorithmSort(index, enableSort)){
                searchInfo.setSort(index * 10);
            }
            searchInfoList.add(searchInfo);
            // 搜索信息置空，防止重复使用
            this.searchFunctionResult.setSearchInfo(null);
        }
        // 活动搜索
        if(!(ObjectUtil.isNull(this.searchActivityResult) || CollUtil.isEmpty(this.searchActivityResult.getActivityList()))){
            SearchInfo searchInfo = this.searchActivityResult.getSearchInfo();
            int index = 1 + subIntentionCodeList.indexOf(DialogueIntentionEnum.SEARCH_ACTIVITY.getCode());
            if(checkAlgorithmSort(index, enableSort)){
                searchInfo.setSort(index * 10);
            }
            searchInfoList.add(searchInfo);
            // 搜索信息置空，防止重复使用
            this.searchActivityResult.setSearchInfo(null);
        }
        // 笔记搜索
        if(!(ObjectUtil.isNull(this.searchNoteResult) || CollUtil.isEmpty(this.searchNoteResult.getNoteList()))){
            SearchInfo searchInfo = this.searchNoteResult.getSearchInfo();
            int index = 1 + subIntentionCodeList.indexOf(DialogueIntentionEnum.SEARCH_NOTE.getCode());
            if(checkAlgorithmSort(index, enableSort)){
                searchInfo.setSort(index * 10);
            }
            searchInfoList.add(searchInfo);
            // 搜索信息置空，防止重复使用
            this.searchNoteResult.setSearchInfo(null);
        }
        // 个人资产搜索
        if(!(ObjectUtil.isNull(this.searchFileResult) || CollUtil.isEmpty(this.searchFileResult.getFileList()))){
            SearchInfo searchInfo = this.searchFileResult.getSearchInfo();
            // 根据所有个人云搜索意图，获取最小的index
            int index = 0;
            for(DialogueIntentionEnum fileEnum : DialogueIntentionEnum.searchPersonFileEnums()){
                int tempIndex = 1 + subIntentionCodeList.indexOf(fileEnum.getCode());
                if(checkAlgorithmSort(index, enableSort) && (index == 0 || tempIndex < index)){
                    index = tempIndex;
                }
            }
            if(index > 0){
                searchInfo.setSort(index * 10);
            }
            searchInfoList.add(searchInfo);
            // 搜索信息置空，防止重复使用
            this.searchFileResult.setSearchInfo(null);
        }
        // 语义搜图
        if(!(ObjectUtil.isNull(this.searchImageResult) || CollUtil.isEmpty(this.searchImageResult.getFileList()))){
            SearchInfo searchInfo = this.searchImageResult.getSearchInfo();
            int index = 1 + subIntentionCodeList.indexOf(DialogueIntentionEnum.SEARCH_IMAGE.getCode());
            if(checkAlgorithmSort(index, enableSort)){
                searchInfo.setSort(index * 10);
            }
            searchInfoList.add(searchInfo);
            // 搜索信息置空，防止重复使用
            this.searchImageResult.setSearchInfo(null);
        }

        // 根据sort字段正序排序
        if(CollUtil.isNotEmpty(searchInfoList)){
            searchInfoList.sort(Comparator.comparing(SearchInfo::getSort, Comparator.nullsFirst(Comparator.naturalOrder())));
        }
        return searchInfoList;
    }

    /**
     * 判断是否满足算法排序条件
     * @param index 位置
     * @param enableSort 排序开关 true表示以配置文件的排序为主 false表示以算法的排序为主
     * @return true满足 false不满足
     */
    private boolean checkAlgorithmSort(int index, boolean enableSort) {
        return index > 0 && !enableSort;
    }

    /**
     * 获取邮箱搜索结果的tips
     */
    public String checkAndGetSearchMailResultTips(){
        String tips = null;
        if(null != this.searchMailResult){
            tips = this.searchMailResult.getTips();
        }
        return tips;
    }

}
