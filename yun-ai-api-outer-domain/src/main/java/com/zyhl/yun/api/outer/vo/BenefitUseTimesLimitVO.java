package com.zyhl.yun.api.outer.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.vo.BenefitUseTimesLimitVO} <br>
 * <b> description:</b>
 * 算法任务权益核销响应VO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-08-16 17:21
 **/
@Data
public class BenefitUseTimesLimitVO implements Serializable {
    private static final long serialVersionUID = 3843250572443818074L;

    /**
     * 限制次数
     */
    private Integer limit = 9999;

    /**
     * 已使用次数
     */
    private Integer use = 0;

    /**
     * 是否能够使用
     */
    private Boolean canUse;

    public Boolean getCanUse() {
        if (limit != null && use != null) {
            return limit > use;
        }
        return true;
    }
}
