package com.zyhl.yun.api.outer.enums;

/**
 * <AUTHOR>
 * @description 返回内容类型枚举
 * @date 2025/4/24 18:21
 */

public enum ModelTypeEnum {

    /**
     * 仅返回 text 的 content 值。
     *
     */
    TEXT("text"),

    /**
     * 返回 html 的 content 值，若无 html 则返回 text 的 content 值。
     *
     */
    HTML("html"),

    /**
     * 同时返回 text 和 html 的 content 值。
     *
     */
    BOTH("both");
    private final String value;

    ModelTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    /**
     * 根据字符串值获取对应的枚举类型
     *
     * @param value 字符串值
     * @return 对应的枚举类型，如果不存在则抛出异常
     */
    public static ModelTypeEnum fromValue(String value) {
        if (value == null) {
            throw new IllegalArgumentException("model 值不能为空");
        }
        for (ModelTypeEnum type : ModelTypeEnum.values()) {
            if (type.getValue().equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的 model 值: " + value);
    }

    // 判断字符串是否在枚举中
    public static boolean contains(String value) {
        for (ModelTypeEnum type : ModelTypeEnum.values()) {
            if (type.getValue().equals(value)) {
                return true;
            }
        }
        return false;
    }
}
