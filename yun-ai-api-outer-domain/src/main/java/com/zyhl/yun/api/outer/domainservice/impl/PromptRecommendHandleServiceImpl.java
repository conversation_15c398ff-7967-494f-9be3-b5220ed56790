package com.zyhl.yun.api.outer.domainservice.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zyhl.hcy.plugin.redis.HcyRedisTemplate;
import com.zyhl.hcy.yun.ai.common.base.enums.YunAiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.domain.vo.PromptRecommendVO;
import com.zyhl.yun.api.outer.domainservice.PromptRecommendHandleService;
import com.zyhl.yun.api.outer.repository.PromptRecommendRepository;
import com.zyhl.yun.api.outer.util.SimpleRedisLock;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/7/17 9:46
 * @Description prompt recommend 数据处理相关业务逻辑
 */
@Slf4j
@Service
public class PromptRecommendHandleServiceImpl implements PromptRecommendHandleService {

    @Resource
    private HcyRedisTemplate<String, Object> hcyRedisTemplate;

    @Resource
    private PromptRecommendRepository promptRecommendRepository;

    @Resource
    private SourceChannelsProperties sourceChannelsProperties;

    @Resource(name = "businessThreadPool")
    ExecutorService businessThreadPool;

    @Resource
    private SimpleRedisLock simpleRedisLock;

    private static final ObjectMapper MAPPER = new ObjectMapper();

    @Override
    public List<PromptRecommendVO> getPromptRecommendList(String channel) {

        // init，若数据量大，可考虑配置为静态变量
        List<PromptRecommendVO> resultList;

        // Cache
        String resultListStr = (String) hcyRedisTemplate.opsForValue().get(RedisConstants.CACHE_KEY_PROMPT_KEY+":"+channel);
        Long cacheTtl = hcyRedisTemplate.opsForValue().getOperations().getExpire(RedisConstants.CACHE_KEY_PROMPT_KEY+":"+channel);

        if (Strings.isNullOrEmpty(resultListStr)) {
            resultList = handleCacheInitialization(channel);
        } else if (cacheTtl != null && cacheTtl < RedisConstants.PROMPT_LOGIC_CACHE_EXPIRE_TIME) {
            resultList = handleCacheExpired(resultListStr,channel);

        } else {
            resultList = cacheConverter(resultListStr);
        }
        return randomSelect(resultList, 3);
    }

    /**
     * 处理缓存逻辑过期的场景
     * @param resultListStr 提示词缓存value
     * @return 提示词列表
     */
    private List<PromptRecommendVO> handleCacheExpired(String resultListStr,String channel) {

        List<PromptRecommendVO> list = Collections.emptyList();
        try { // setnx锁满足目前业务，如后续业务逻辑变动，考虑到锁ttl问题，可引入redisson's watchdog
            if (simpleRedisLock.tryLock(RedisConstants.PROMPT_CACHE_LOCK_KEY,
                    RedisConstants.CACHE_KEY_PROMPT_KEY+":"+channel, 300, TimeUnit.SECONDS)) {
                businessThreadPool.execute(() -> listAndHandlePromptRecommendList(channel));
                simpleRedisLock.unlock(RedisConstants.PROMPT_CACHE_LOCK_KEY, RedisConstants.CACHE_KEY_PROMPT_KEY+":"+channel);
            }
            list = cacheConverter(resultListStr);
        } catch (YunAiBusinessException e) {
            log.error("提示词业务锁操作发生异常，请注意上下文业务日志是否存在问题。", e);
        }
        return list;
    }

    /**
     * 处理缓存为空的初始化场景
     * @return 提示词列表
     */
    private List<PromptRecommendVO> handleCacheInitialization(String channel) {

        List<PromptRecommendVO> list = Collections.emptyList();
        try { // setnx锁满足目前业务，如后续业务逻辑变动，考虑到锁ttl问题，可引入redisson's watchdog
            if (simpleRedisLock.tryLock(RedisConstants.PROMPT_CACHE_LOCK_KEY,
                    RedisConstants.CACHE_KEY_PROMPT_KEY+":"+channel, 300, TimeUnit.SECONDS)) {
                // get from DB
                list = new ArrayList<>(listAndHandlePromptRecommendList(channel));
                simpleRedisLock.unlock(RedisConstants.PROMPT_CACHE_LOCK_KEY, RedisConstants.CACHE_KEY_PROMPT_KEY+":"+channel);
            } else {
                log.info("缓存中提示词为空，且当前线程抢夺锁失败，返回结果为空，等待缓存注入数据线程注入成功后再尝试获取操作。");
            }
        } catch (YunAiBusinessException e) {
            log.error("提示词业务锁操作发生异常，请注意上下文业务日志是否存在问题。", e);
        }
        return list;
    }

    /**
     * 根据缓存字符串转换为对应提示词列表对象返回
     * @param resultListStr 缓存字符串
     * @return 提示词列表
     */
    private List<PromptRecommendVO> cacheConverter(String resultListStr) {

        List<PromptRecommendVO> list;
        try {
            list = MAPPER.readValue(resultListStr,
                    new TypeReference<List<PromptRecommendVO>>() {
                    });
        } catch (JsonProcessingException e) {
            log.info("解析提示词列表JSON字符串失败，字符串参数：{}", resultListStr);
            throw new YunAiBusinessException(YunAiCommonResultCode.DOWNSTREAM_SERVICES_EXCEPTION);
        }
        return list;
    }

    /**
     * 从DB获取Prompt recommend list，并注入缓存
     * @return 提示词列表
     */
    private List<PromptRecommendVO> listAndHandlePromptRecommendList(String channel) {

        // get from DB
        String businessType = sourceChannelsProperties.getByChannel(channel).getBusinessType();
        List<PromptRecommendVO> promptRecommendVOList = promptRecommendRepository.listPromptRecommendList(businessType);
        // injection
        promptInfoInjection(promptRecommendVOList,channel);

        return promptRecommendVOList;
    }

    /**
     * 提示词列表数据注入缓存
     * @param list 提示词列表
     */
    private void promptInfoInjection(List<PromptRecommendVO> list,String channel) {

        String str;
        try {
            str = MAPPER.writeValueAsString(list);
        } catch (JsonProcessingException e) {
            log.info("提示词列表转换json失败，提示词列表参数：{}", list);
            throw new YunAiBusinessException(YunAiCommonResultCode.DOWNSTREAM_SERVICES_EXCEPTION);
        }
        hcyRedisTemplate.opsForValue().set(RedisConstants.CACHE_KEY_PROMPT_KEY+":"+channel, str,
                RedisConstants.PROMPT_CACHE_EXPIRE_TIME, TimeUnit.MINUTES);
    }

    /**
     * 根据数量随机从List获取元素
     * @param list     List参数
     * @param quantity 获取数量
     * @return List结果
     */
    private <T> List<T> randomSelect(List<T> list, int quantity) {

        if (quantity > list.size()) {
            log.info("根据数量随机从List获取元素方法出现下标越界，传入数量参数为：{}，传入List参数为：{}", quantity, list);
            return Collections.emptyList();
        }
        // random
        Collections.shuffle(list);
        return list.stream().limit(quantity).collect(Collectors.toList());
    }
}