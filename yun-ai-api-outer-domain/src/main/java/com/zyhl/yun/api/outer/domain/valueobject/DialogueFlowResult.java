package com.zyhl.yun.api.outer.domain.valueobject;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.domain.vo.chat.search.SearchInfo;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.OutAuditStatusEnum;

import java.util.Date;
import java.util.List;

import cn.hutool.core.date.DatePattern;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 对话流式结果对象
 * 
 * <AUTHOR>
 * @date 2025-04-16
 */
@Data
@NoArgsConstructor
public class DialogueFlowResult {

    /**
     * 执行序号
     */
    private Integer index = 0;

    /**
     * 结果类型
     *
     * @see com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum
     */
    private int resultType = FlowResultTypeEnum.TEXT_MODEL.getType();

    /**
     * 4.8 modelType枚举类中的编码值，如果没有使用大模型，则返回空
     */
    private String modelType;

    /**
     * 每个模块流式回答的标题
     */
    private String title;

    /**
     * 输出文本，流式增量出 例如： 第一句：你好， 第二句：欢迎 第三句：来到中国。
     */
    private String outContent = "";

    /**
     * 思维链过程
     */
    private String reasoningContent = "";

    /**
     * 事件码，前端遇到这个字段时，要根据对应的事件进行响应的处理
     *
     * @see com.zyhl.yun.api.outer.enums.chat.ChatEventCodeEnum
     */
    private String eventCode;

    /**
     * 个人知识库参考文件，可选。
     */
    private List<KnowledgeSearchInfo> personalKnowledgeFileList;

    /**
     * 大模型联网搜索结果，可选。
     */
    private List<HtmlInfo> networkSearchInfoList;

    /**
     * 云盘搜索信息列表
     */
    private List<SearchInfo> searchInfoList;

    /**
     * 个人云文件信息列表
     */
    private List<File> fileList;

    /**
     * 相册信息列表
     */
    private List<AlbumInfo> albumList;
    
    /**
     * 邮件信息
     */
    private MailInfoVO mailInfo;

    /**
     * 文件下载URL列表
     */
    private List<String> fileUrlList;

    /**
     * 完成原因 "processing"：正在处理 "stop"：结束，当前部分的流式结束标识，表示这个部分的输出已结束
     */
    private String finishReason;

    /**
     * 错误码，该部分处理失败时返回
     */
    private String errorCode = ResultCodeEnum.SUCCESS.getResultCode();

    /**
     * 错误信息，该部分处理失败时返回
     */
    private String errorMessage = ResultCodeEnum.SUCCESS.getResultMsg();

    /**
     * 输出时间，RFC 3339格式(东八区)，注： 2019-10-12T14:20:50.52+08:00
     */
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date outputTime = new Date();

    /**
     * 输出内容审批结果;状态码：2通过，其他失败
     *
     * @see OutAuditStatusEnum
     */
    private int outAuditStatus = OutAuditStatusEnum.SUCCESS.getCode();

    /**
     * AI工具结果
     */
    private AiFunctionResult aiFunctionResult;

    public DialogueFlowResult(String outContent, String reasoningContent) {
        this.outContent = outContent;
        this.reasoningContent = reasoningContent;
    }
    
    public DialogueFlowResult(int resultType, String title, String outContent) {
    	this.resultType = resultType;
    	this.title = title;
        this.outContent = outContent;
    }
    
    /**
     * 每1个 会返回的结果数量，目 前仅 resultType-4 时返 回，告诉前端预计会生 成多少个图片/文件
     */
    private Integer resultCount;
}