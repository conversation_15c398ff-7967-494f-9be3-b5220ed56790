package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.dto.UserInfoDTO;
import com.zyhl.yun.api.outer.domain.dto.redis.HistoryDialogInfoDTO;
import com.zyhl.yun.api.outer.domain.dto.redis.MemberBenefitDTO;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiRegisterEntity;
import com.zyhl.yun.api.outer.domain.vo.ChatApplicationType;

import java.util.List;

/**
 * redis数据操作
 *
 * <AUTHOR>
 */
public interface RedisOperateRepository {

    /**
     * 保存会员权益消费码
     *
     * @param userId     用户id
     * @param benefitNo  权益项编号
     * @param dialogueId 对话id
     * @param consumeSeq 消费码
     */
    void setConsumeSeq(String userId, Long dialogueId, String benefitNo, String consumeSeq);

    /**
     * 获取会员权益消费码
     *
     * @param userId     用户id
     * @param dialogueId 对话id
     * @return 消费码
     */
    MemberBenefitDTO getConsumeSeq(String userId, Long dialogueId);

    /**
     * 删除会员权益消费码
     *
     * @param userId     用户id
     * @param dialogueId 对话id
     */
    void delConsumeSeq(String userId, Long dialogueId);

    /**
     * 保存消息sessionId
     *
     * @param userId        用户id
     * @param applicationId 应用id
     * @param businessType  业务类型
     * @param sessionId     会话id
     */
    void setMessageSessionId(String userId, String applicationId, String businessType, String sessionId);

    /**
     * 获取消息sessionId
     *
     * @param userId        用户id
     * @param applicationId 应用id
     * @param businessType  业务类型
     * @return 会话id
     */
    String getMessageSessionId(String userId, String applicationId, String businessType);

    /**
     * 删除消息sessionId
     *
     * @param userId        用户id
     * @param applicationId 应用id
     * @param businessType  业务类型
     */
    void delMessageSessionId(String userId, String applicationId, String businessType);

    /**
     * 保存用户报名数据
     *
     * @param userId       用户id
     * @param businessType 业务类型
     * @param module       模块
     * @param entity       报名对象
     */
    void setRegisterEntity(String userId, Integer businessType, Integer module, AlgorithmAiRegisterEntity entity);

    /**
     * 获取用户报名对象
     *
     * @param userId       用户id
     * @param businessType 业务类型
     * @param module       模块
     * @return 报名对象
     */
    AlgorithmAiRegisterEntity getRegisterEntity(String userId, Integer businessType, Integer module);

    /**
     * 删除用户报名数据
     *
     * @param userId       用户id
     * @param businessType 业务类型
     * @param module       模块
     */
    void delRegisterEntity(String userId, Integer businessType, Integer module);

    /**
     * 获取应用关联id
     *
     * @param applicationId 应用id
     * @return 应用关联id
     */
    String getTypeRelationId(String applicationId);

    /**
     * 设置应用关联id
     *
     * @param applicationId  应用id
     * @param typeRelationId 应用关联id
     */
    void setTypeRelationId(String applicationId, String typeRelationId);


    /**
     * 获取应用信息
     *
     * @param applicationId 应用id
     * @return 应用信息
     */
    ChatApplicationType getBeanApplicationId(String applicationId);

    /**
     * 设置应用信息
     *
     * @param applicationId       应用id
     * @param chatApplicationType 应用信息
     */
    void setBeanApplicationId(String applicationId, ChatApplicationType chatApplicationType);

    /**
     * 设置停止对话
     *
     * @param dialogueId 对话id
     */
    void setStopDialogue(Long dialogueId);

    /**
     * 获取停止对话状态
     *
     * @param dialogueId 对话id
     * @return true-停止对话
     */
    boolean getStopDialogue(Long dialogueId);

    /**
     * 设置模型不可用
     *
     * @param modeCode 模型编码
     */
    void setModelDisabled(String modeCode);

    /**
     * 获取模型不可用状态
     *
     * @param modeCode 模型编码
     * @return true-模型不可用
     */
    boolean getModelDisabled(String modeCode);

    /**
     * 设置用户知识库是否有文件
     *
     * @param userId    用户id
     * @param existFile 是否存在
     */
    void setUserKnowledgeExistFile(String userId, boolean existFile);

    /**
     * 获取用户知识库是否有文件
     *
     * @param userId 用户id
     * @return Boolean
     */
    Boolean getUserKnowledgeExistFile(String userId);

    /**
     * 设置公共知识库是否有文件
     *
     * @param baseId    知识库id
     * @param existFile 是否存在
     */
    void setCommonKnowledgeExistFile(String baseId, boolean existFile);

    /**
     * 获取公共知识库是否有文件
     *
     * @param baseId 知识库id
     * @return Boolean
     */
    Boolean getCommonKnowledgeExistFile(String baseId);

    /**
     * 保存向量化地址
     *
     * @param url 向量化地址
     */
    void setFeatureUrl(String url);

    /**
     * 获取向量化地址
     *
     * @return 地址
     */
    String getFeatureUrl();

    /**
     * 保存用户空间id
     *
     * @param userId  用户id
     * @param driveId 空间id
     */
    void setUserDriveId(String userId, String driveId);

    /**
     * 获取用户空间id
     *
     * @param userId 用户id
     * @return 空间id
     */
    String getUserDriveId(String userId);

    /**
     * 存储对话信息到历史会话
     *
     * @param sessionId  会话ID
     * @param dialogInfo 对话信息对象
     * @param maxLength  List的最大长度
     * @param expireTime 过期时间（单位：秒）
     */
    void setHistoryDialogInfo(String sessionId, HistoryDialogInfoDTO dialogInfo, Integer maxLength, Long expireTime);

    /**
     * 获取历史会话中的对话信息列表
     *
     * @param sessionId 会话ID
     * @return 包含 DialogInfo 对象的集合
     */
    List<HistoryDialogInfoDTO> getHistoryDialogInfoList(String sessionId);

    /**
     * 保存请求头tid
     *
     * @param url 请求地址
     * @param tid x-yun-tid
     * @return true-保存成功，false-保存失败 | 已经存在
     */
    boolean saveXYunTid(String url, String tid);

    /**
     * 保存用户信息到redis
     *
     * @param userInfoDTO 用户信息
     */
    void setUserInfo(UserInfoDTO userInfoDTO);

    /**
     * 获取用户信息到redis
     * 
     * @param userId 用户id
     * @return 用户信息
     */
    UserInfoDTO getUserInfo(String userId);

    /**
     * 保存流式结果
     *
     * @param dialogueId         对话id
     * @param index              索引
     * @param flowTypeResultJson 流式结果
     */
    void setFlowResult(Long dialogueId, Integer index, String flowTypeResultJson);

    /**
     * 获取流式结果
     *
     * @param dialogueId 对话id
     * @param index      索引
     * @return 流式分片结果
     */
    String getFlowResult(Long dialogueId, Integer index);

    /**
     * 设置对话配置
     *
     * @param userId 用户id
     */
    void setDialogueConfig(String userId, String dialogueConfig);

    /**
     * 获取对话配置
     *
     * @param userId 用户id
     * @return 对话配置
     */
    String getDialogueConfig(String userId);
}
