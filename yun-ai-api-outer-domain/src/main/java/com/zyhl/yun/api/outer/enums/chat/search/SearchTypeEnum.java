package com.zyhl.yun.api.outer.enums.chat.search;

import com.zyhl.yun.api.outer.domain.vo.chat.search.param.*;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 搜索类别枚举类
 *
 * @Author: WeiJingKun
 */
@Getter
@AllArgsConstructor
public enum SearchTypeEnum {

    /**
     * 【图片】搜索
     */
    IMAGE(SearchImageParam.class, "image"),
    
    /**
     * 【文件-个人资产】搜索
     */
    FILE(SearchFileParam.class, "file"),
    
    /**
     * 【笔记】搜索
     */
    NOTE(SearchNoteParam.class, "note"),
    
    /**
     * 【功能】搜索
     */
    FUNCTION(SearchFunctionParam.class, "function"),
    
    /**
     * 【活动】搜索
     */
    ACTIVITY(SearchActivityParam.class, "activity"),
    
    /**
     * 【发现广场】搜索
     */
    DISCOVERY(SearchDiscoveryParam.class, "discovery"),
    
    /**
     * 【我的圈子】搜索
     */
    MY_GROUP(SearchMyGroupParam.class, "myGroup"),
    
    /**
     * 【热门圈子】搜索
     */
    RECOMMEND_GROUP(SearchRecommendGroupParam.class, "recommendGroup"),
    
    /**
     * 【邮件】搜索
     */
    MAIL(SearchMailParam.class, "mail"),

    /**
     * 【邮件附件】搜索
     */
    MAIL_ATTACHMENT(SearchMailAttachmentParam.class, "mailAttachment"),

    /**
     * 【知识库资源】搜索
     */
    KNOWLEDGE_BASE_RESOURCE(SearchKnowledgeBaseResourceParam.class, "knowledgeBaseResource"),

    /**
     * 【笔记内容】搜索
     */
    SEARCH_NOTE_CONTENT(SearchNoteContentParam.class, "noteContent"),

    ;

    private static final Map<Class<?>, SearchTypeEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(SearchTypeEnum.class).forEach(item -> MAP.put(item.searchParamClass, item));
    }

    public static SearchTypeEnum getByKey(Class<?> searchParamClass) {
        if (null == searchParamClass) {
            return null;
        }
        return MAP.get(searchParamClass);
    }

    /**
     * 是否存在
     *
     * @return true-存在
     */
    public static boolean isExist(Class<?> searchParamClass) {
        return getByKey(searchParamClass) != null;
    }

    /**
     * 搜索参数类
     */
    private final Class<?> searchParamClass;

    /**
     * 搜索策略
     */
    private final String searchType;

}
