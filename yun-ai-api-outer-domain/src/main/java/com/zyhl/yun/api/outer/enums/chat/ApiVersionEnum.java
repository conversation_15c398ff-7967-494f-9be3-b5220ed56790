package com.zyhl.yun.api.outer.enums.chat;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 接口版本号枚举
 *
 * <AUTHOR>
 * @date 2024/6/7 10:25
 */
@Getter
@AllArgsConstructor
public enum ApiVersionEnum {

    /**
     * V1版本
     */
    V1("v1", "V1版本", 1),

    /**
     * V2版本
     */
    V2("v2", "V2版本", 2),

    /**
     * V3版本
     */
    V3("v3", "V3版本", 1),

    /**
     * V4版本
     */
    V4("v4", "V4版本", 2),

    /**
     * V6版本
     */
    V6("v6", "V6版本", 2),

    ;

    /**
     * 类型
     */
    private final String version;

    /**
     * 备注
     */
    private final String remark;

    /**
     * 类型：1-普通http、2-sse
     */
    private final int type;


    public static ApiVersionEnum getByVersion(String version) {
        for (ApiVersionEnum value : values()) {
            if (value.getVersion().equalsIgnoreCase(version)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 普通http版本
     *
     * @param version 请求接口版本
     * @return true/false
     */
    public static boolean isHttp(String version) {
        ApiVersionEnum v = getByVersion(version);
        return v != null && v.getType() == 1;
    }

    /**
     * sse版本
     *
     * @param version 请求接口版本
     * @return true/false
     */
    public static boolean isSse(String version) {
        ApiVersionEnum v = getByVersion(version);
        return v != null && v.getType() == 2;
    }

    /**
     * 判断是否合并思维链内容版本
     * @param apiVersion
     * @return
     */
	public static boolean isMergeThinkContentVersion(String apiVersion) {
		return (V1.getVersion().equalsIgnoreCase(apiVersion) || V2.getVersion().equalsIgnoreCase(apiVersion)
				|| V3.getVersion().equalsIgnoreCase(apiVersion) || V4.getVersion().equalsIgnoreCase(apiVersion));
	}
}
