package com.zyhl.yun.api.outer.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 地址详情
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddressDetailEntity {

    /**
     * 详细地址。
     * 该字段存储用户的完整地址信息。
     */
    private String addressline;

    /**
     * 国家。
     * 该字段存储用户所在的国家名称。
     */
    private String country;

    /**
     * 省份。
     * 该字段存储用户所在的省份名称。
     */
    private String province;

    /**
     * 城市。
     * 该字段存储用户所在的城市名称。
     */
    private String city;

    /**
     * 区/县。
     * 该字段存储用户所在的区或县名称。
     */
    private String district;

    /**
     * 乡/镇。
     * 该字段存储用户所在的乡或镇名称。
     */
    private String township;
}
