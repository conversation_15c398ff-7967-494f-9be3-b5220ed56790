package com.zyhl.yun.api.outer.domain.entity;

import lombok.Data;

import java.util.Date;

/**
 * AI迁移表：小天助手1.0.1
 * <AUTHOR>
 */
@Data
public class AlgorithmAiMigrationEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户所属底座
     */
    private Integer belongsPlatform;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 设备
     */
    private String device;

    /**
     * 状态：1已报名（默认)，2已迁移，3已计算完成（图片向量化）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
