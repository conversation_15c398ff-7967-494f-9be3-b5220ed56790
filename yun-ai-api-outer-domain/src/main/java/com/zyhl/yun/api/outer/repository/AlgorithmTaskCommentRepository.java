package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.TaskCommentEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 【算法任务评论】repository
 * @date 2025/4/21 17:17
 */
public interface AlgorithmTaskCommentRepository {

    boolean bulkSave(List<TaskCommentEntity> entityList);

    List<TaskCommentEntity> getTaskComment(TaskCommentEntity entity);
}