package com.zyhl.yun.api.outer.enums;

import cn.hutool.core.text.CharSequenceUtil;

/**
 * 项目错误msg关键字，错误枚举
 *
 * @Author: WeiJingKun
 */
public enum BaseErrorMsgEnum {

    /**
     * 违法 输入的内容涉及违法信息，请重新输入
     */
    BREAK_THE_LAW("违法", AiResultCode.CODE_10022012),

    /**
     * 不合法 输入的内容涉及不合法信息，请重新输入
     */
    ILLEGALITY("不合法", AiResultCode.CODE_10022012),

    /**
     * 违规 输入的内容涉及违规信息，请重新输入
     */
    VIOLATION("违规", AiResultCode.CODE_10022012),

    /**
     * 不合规 输入的内容涉及不合规信息，请重新输入
     */
    NON_COMPLIANCE("不合规", AiResultCode.CODE_10022012),

    /**
     * 色情 输入的内容涉及色情信息，请重新输入
     */
    PORNOGRAPHIC("色情", AiResultCode.CODE_10022012),

    /**
     * 敏感 输入的内容涉及敏感信息，请重新输入
     */
    SENSITIVE("敏感", AiResultCode.CODE_10022012);;

    /**
     * 错误消息关键字
     */
    private final String msgKey;
    /**
     * 通用错误码Enum
     */
    private final AiResultCode codeEnum;

    BaseErrorMsgEnum(String msgKey, AiResultCode codeEnum) {
        this.msgKey = msgKey;
        this.codeEnum = codeEnum;
    }

    public String getMsgKey() {
        return msgKey;
    }

    public AiResultCode getCodeEnum() {
        return codeEnum;
    }

    /**
     * 根据错误信息关键字，获取通用错误码Enum
     * 获取不到，返回null
     *
     * @Author: WeiJingKun
     */
    public static AiResultCode getByMsg(String msg) {
        for (BaseErrorMsgEnum errorEnum : values()) {
            if (CharSequenceUtil.contains(msg, errorEnum.getMsgKey())) {
                return errorEnum.getCodeEnum();
            }
        }
        return null;
    }

}
