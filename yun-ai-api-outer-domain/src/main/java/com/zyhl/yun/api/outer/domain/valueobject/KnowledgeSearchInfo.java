package com.zyhl.yun.api.outer.domain.valueobject;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveFileVO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.AlgorithmKnowledgeConfigEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.KnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 知识库搜索信息类
 * 
 * <AUTHOR>
 * @date 2025-04-15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KnowledgeSearchInfo {
    /**
     * 知识库文件信息
     */
    private PersonalKnowledgeResource resource;

    /**
     * 分片正文
     */
    private List<String> shardingList;

    /**
     * 来自的知识库名称
     */
    private String baseName;

    public KnowledgeSearchInfo(File file) {
        this.resource = new PersonalKnowledgeResource(file);
    }

    public KnowledgeSearchInfo(OwnerDriveFileVO fileInfo) {
        this.resource = new PersonalKnowledgeResource(fileInfo);
    }

    public KnowledgeSearchInfo(UserKnowledgeFileEntity entity, List<String> shardingList, UserKnowledgeEntity knowledgeEntity) {
        this.resource = new PersonalKnowledgeResource(entity);
        this.shardingList = shardingList;
        if (ObjectUtil.isNotEmpty(knowledgeEntity)) {
            this.baseName = knowledgeEntity.getName();
        }
    }

    public KnowledgeSearchInfo(KnowledgeFileEntity entity, List<String> shardingList, AlgorithmKnowledgeConfigEntity knowledgeEntity) {
        this.resource = new PersonalKnowledgeResource(entity);
        this.shardingList = shardingList;
        if (ObjectUtil.isNotEmpty(knowledgeEntity)) {
            this.baseName = knowledgeEntity.getName();
        }
    }
}