package com.zyhl.yun.api.outer.util;

import cn.hutool.core.util.StrUtil;

/**
 * url编码检查工具类
 * 
 * <AUTHOR>
 * @date 2025-06-26 14:28
 */
public class UrlEncodingChecker {

	/**
	 * url是否包含编码
	 * @param url 链接
	 * @return 是否包含编码
	 */
	public static boolean isUrlEncoded(String url) {
		if (StrUtil.isBlank(url)) {
			return false;
		}

		// 检查URL中是否包含%后跟两个十六进制数字
		for (int i = 0; i < url.length() - 2; i++) {
			if (url.charAt(i) == '%' && isHex(url.charAt(i + 1)) && isHex(url.charAt(i + 2))) {
				return true;
			}
		}

		return false;
	}

	private static boolean isHex(char c) {
		return (c >= '0' && c <= '9') || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F');
	}

//	public static void main(String[] args) {
//		String url1 = "https://example.com/path?query=value";
//		String url2 = "https%3A%2F%2Fexample.com%2Fpath%3Fquery%3Dvalue";
//		String url3 = "https://example.com/path with space";
//		String url4 = "https://example.com/path%20with%20space";
//		String url5 = "https://example.com/搜索?q=a&b=c&page=1";
//
//		System.out.println("url1 encoded? " + isUrlEncoded(url1)); // false
//		System.out.println("url2 encoded? " + isUrlEncoded(url2)); // true
//		System.out.println("url3 encoded? " + isUrlEncoded(url3)); // false
//		System.out.println("url4 encoded? " + isUrlEncoded(url4)); // true
//		System.out.println("url5 encoded? " + isUrlEncoded(url5)); // true
//	}
}