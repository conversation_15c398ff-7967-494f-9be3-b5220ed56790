package com.zyhl.yun.api.outer.domain.req;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 批量获取文件信息请求参数
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchGetReqEntity {

    /**
     * 用户ID。
     * 该字段存储请求的用户唯一标识符。
     */
    private String userId;

    /**
     * 文件ID集合。
     * 该字段存储需要处理的文件ID列表。
     */
    private List<String> fileIds;

    /**
     * 图片缩略图处理样式列表。
     * 该字段存储图片缩略图的处理样式列表，可选值包括 Small、Middle、Big、Large，默认为 Big。
     */
    private List<String> imageThumbnailStyleList;

    /**
     * 缩略图地址过期时间。
     * 该字段存储生成的缩略图地址的有效期，单位为秒，最长为 86400 秒，默认为 900 秒。
     */
    private Integer expireSec;


}
