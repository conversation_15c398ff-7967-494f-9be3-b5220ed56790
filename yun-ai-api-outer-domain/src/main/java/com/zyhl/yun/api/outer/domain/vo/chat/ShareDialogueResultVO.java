package com.zyhl.yun.api.outer.domain.vo.chat;

import com.zyhl.yun.api.outer.domain.dto.ExternalResourceInfoDTO;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.vo.ChatApplicationType;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import lombok.Data;

import java.util.List;

/**
 * 分享对话结果的响应VO
 *
 * <AUTHOR>
 */
@Data
public class ShareDialogueResultVO {

    /**
     * 对话ID
     */
    private String dialogueId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 类型：
     * 0——对话历史记录
     * 1——智囊历史记录
     */
    private int talkType;

    /**
     * 输入资源类型：枚举resourceType
     */
    private int resourceType;

    /**
     * 对话意图指令
     *
     * @see com.zyhl.yun.api.outer.enums.DialogueIntentionEnum
     */
    private String commands;

    /**
     * 模型类型：枚举modelType
     */
    private String modelType;

    /**
     * 输入文本
     */
    private String inContent;

    /**
     * 输入时间，RFC 3339格式(东八区)
     * 注：2019-10-12T14:20:50.52+08:00
     */
    private String inAuditTime;

    /**
     * 输入内容审批结果;状态码：2通过，其他失败
     */
    private Integer inAuditStatus;

    /**
     * 输入资源ID;与resourceType一一对应：
     * 0-无资源：纯文本时为空
     * 1-邮件：邮件ID
     * 2-笔记：笔记ID
     * 3-图片：云盘图片ID
     * 4-对话ID：图片下载url
     */
    private String inResourceId;

    /**
     * 输入资源相关信息，与ResourceType一一对应：
     * 0-无资源：纯文本时为空；
     * 1-邮件：补充邮件信息，主要包括邮件标题；
     * 2-笔记：补充笔记信息，主要包括笔记标题；
     * 3-图片：补充云盘图片信息，主要包括文件名称、文件缩略图；
     * 4-对话ID+图片下载url：为空；
     */
    private ExternalResourceInfoDTO inResourceExtInfo;

    /**
     * 输出文本类型
     * 1--普通文本
     * 2--富文本
     * 3--JSONObject
     */
    private Integer outContentType;

    /**
     * 输出文本
     * outContentType=1，outContent为普通字符串；
     * outContentType=2，outContent为富文本字符串；
     * outContentType=3，outContent为JSONObject格式的字符串；
     * 意图指令为“033”时，JSONObject的数据结构为ImageCheckResult
     */
    private String outContent;

    /**
     * 思维链过程
     */
    private String reasoningContent;

    /**
     * 大模型联网搜索结果
     */
    private List<AiTextResultRespParameters.NetworkSearchInfo> networkSearchInfoList;

    /**
     * 输出时间，RFC 3339格式(东八区)
     * 注：2019-10-12T14:20:50.52+08:00
     */
    private String outAuditTime;

    /**
     * 输出内容审批结果;状态码：2通过，其他失败
     */
    private Integer outAuditStatus;

    /**
     * 输出资源类型：
     * 1--云盘文件ID
     * 2--文件下载地址
     * 3--
     */
    private Integer outResourceType;

    /**
     * 输出资源信息 与outResourceType一一对应：
     * 1--云盘文件ID：补充云盘文件信息，主要包括文件名称、文件缩略图;
     * 2--文件下载地址：为空
     */
    private ExternalResourceInfoDTO outResourceExtInfo;

    /**
     * 输出资源信息；（云盘文件ID/下载URL）
     */
    private String outResourceId;

    /**
     * 渠道来源
     */
    private String sourceChannel;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 应用类型:普通对话(chat); 智能体对话(intelligent);
     */
    private String applicationType;

    /**
     * 对话应用类型数据
     */
    private ChatApplicationType applicationInfo;

    /**
     * 个人知识库参考文件信息
     */
    private List<File> personalKnowledgeFileList;

    /**
     * json格式的附加信息
     */
    private String extInfo;

    /**
     * 引导语信息
     */
    private LeadCopyVO leadCopy;

    /**
     * 对话结果推荐，当对话信息不为空时返回。
     * 如果leadCopy不为空，优先展示leadCopy
     */
    private DialogueRecommendVO recommend;

    /**
     * 返回结果的标题
     * commands为“024”创建笔记和“027”创建语音笔记时，返回标题；
     * commands为”000“时，返回知识库开头；
     * commands为“028”邮件搜索时返回卡片标题；
     * commands为“012”-“018”、“020”-“023”和“028”搜索意图时，返回搜索结果的返回词
     */
    private String title;
}
