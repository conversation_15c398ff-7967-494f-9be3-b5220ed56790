package com.zyhl.yun.api.outer.domain.entity;

import java.io.Serializable;

import com.zyhl.yun.api.outer.enums.ChatNetworkSearchStatusEnum;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 2024年02月28日 15:58
 */
@Data
public class ChatConfigEntity implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 用户id
     */
    private String userId;

    /**
     * 业务模型，详见
     *
     */
    private String modelType;

    /**
     * 助手编码
     * @see com.zyhl.yun.api.outer.enums.AssistantEnum
     */
    private String businessCode;
    
    /**
     * 大模型联网搜索状态
     * @see com.zyhl.yun.api.outer.enums.ChatNetworkSearchStatusEnum
     */
    private Integer networkSearchStatus;

    public ChatConfigEntity() {

    }

    public ChatConfigEntity(String userId) {
        this.userId = userId;
    }
    public ChatConfigEntity(String userId, String modelType, String businessCode, Integer networkSearchStatus) {
        this.userId = userId;
        this.modelType = modelType;
        this.businessCode = businessCode;
        this.networkSearchStatus = networkSearchStatus;
    }
    
    public ChatConfigEntity(String userId, String modelType, String businessCode, Boolean networkSearchStatus) {
        this.userId = userId;
        this.modelType = modelType;
        this.businessCode = businessCode;
		if (null != networkSearchStatus) {
			//用户不设置，则不设置db状态
			this.networkSearchStatus = networkSearchStatus ? ChatNetworkSearchStatusEnum.OPEN.getCode()
					: ChatNetworkSearchStatusEnum.CLOSE.getCode();
		}else {
			this.networkSearchStatus = null;
		}
    }
}
