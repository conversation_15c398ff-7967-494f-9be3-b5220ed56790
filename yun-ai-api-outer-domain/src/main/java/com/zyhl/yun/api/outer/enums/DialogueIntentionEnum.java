package com.zyhl.yun.api.outer.enums;

import com.zyhl.yun.api.outer.config.ModelProperties;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 对话意图-枚举
 *
 * @Author: WeiJingKun
 */
@Getter
@AllArgsConstructor
@Slf4j
public enum DialogueIntentionEnum {

    // 文本意图
    /**
     * 文生文
     */
    TEXT_GENERATE_TEXT("000", "text-generate-text", "文生文", "普通对话意图", IntentionTypeEnum.TEXT),

    /**
     * 其他(泛化意图-执行搜索)
     */
    OTHER("999", "other", "其他", "其他", IntentionTypeEnum.SEARCH),

    // 工具意图
    /**
     * 图片配文
     */
    PICTURE_GENERATE_TEXT("001", "picture-generate-text", "图片配文", "图片配文", IntentionTypeEnum.IMAGE),

    /**
     * 文生图
     */
    TEXT_GENERATE_PICTURE("002", "text-generate-picture", "文生图", "文生图", IntentionTypeEnum.IMAGE),

    /**
     * 图片漫画风
     */
    PICTURE_COMIC_STYLE("004", "picture-comic-style", "图片漫画风", "图片动漫化", IntentionTypeEnum.IMAGE),

    /**
     * 活照片
     */
    LIVE_PHOTOS("005", "live-photos", "活照片", "活照片", IntentionTypeEnum.IMAGE),

    /**
     * AI头像
     */
    AI_HEAD_SCULPTURE("007", "ai-head-sculpture", "AI头像", "AI头像", IntentionTypeEnum.IMAGE),

    /**
     * 老照片修复
     */
    OLD_PHOTOS_REPAIR("008", "old-photos-repair", "老照片修复", "老照片修复", IntentionTypeEnum.IMAGE),

    /**
     * 画质修复
     */
    IMAGE_QUALITY_RESTORATION("009", "image-quality-restoration", "画质修复", "画质修复", IntentionTypeEnum.IMAGE),

    /**
     * 智能美颜
     */
    INTELLIGENT_BEAUTY("010", "intelligent-beauty", "智能美颜", "智能美颜", IntentionTypeEnum.IMAGE),

    /**
     * 宝宝长相预测
     */
    APPEARANCE_PREDICTION("030", "appearance-prediction", "宝宝长相预测", "宝宝长相预测", IntentionTypeEnum.IMAGE),

    /**
     * AI表情包
     */
    AI_EMOTICON("031", "ai-emoticon", "AI表情包", "AI表情包", IntentionTypeEnum.IMAGE),

    /**
     * 图片智能鉴伪
     */
    SMART_FAKE_CHECK("033", "smart-fake-check", "图片智能鉴伪", "图片智能鉴伪", IntentionTypeEnum.IMAGE),

    // 跳链意图
    /**
     * 智能抠图
     */
    INTELLIGENT_CUTOUT("003", "intelligent-cutout", "智能抠图", "智能抠图", IntentionTypeEnum.IMAGE),

    /**
     * AI消除
     */
    AI_ELIMINATE("006", "ai-eliminate", "AI消除", "AI消除", IntentionTypeEnum.IMAGE),

    /**
     * 妙云相机
     */
    CLOUD_CAMERA("011", "cloud-camera", "妙云相机", "AI写真", IntentionTypeEnum.IMAGE),

    /**
     * 宝宝时光机
     */
    BABY_TIME_MACHINE("029", "baby-time-machine", "宝宝时光机", "宝宝时光机", IntentionTypeEnum.IMAGE),

    // 搜索意图
    /**
     * 搜图片
     */
    SEARCH_IMAGE("012", "search-image", "搜图片", "搜图片", IntentionTypeEnum.SEARCH),

    /**
     * 搜文档
     */
    SEARCH_DOCUMENT("013", "search-document", "搜文档", "搜云盘文档", IntentionTypeEnum.SEARCH),

    /**
     * 搜视频
     */
    SEARCH_VIDEO("014", "search-video", "搜视频", "搜视频", IntentionTypeEnum.SEARCH),

    /**
     * 搜音频
     */
    SEARCH_AUDIO("015", "search-audio", "搜音频", "搜音频", IntentionTypeEnum.SEARCH),

    /**
     * 搜文件夹
     */
    SEARCH_FOLDER("016", "search-folder", "搜文件夹", "搜文件夹", IntentionTypeEnum.SEARCH),

    /**
     * 搜笔记
     */
    SEARCH_NOTE("017", "search-note", "搜笔记", "搜笔记", IntentionTypeEnum.SEARCH),

    /**
     * 综合搜索
     */
    COMPREHENSIVE_SEARCH("018", "comprehensive-search", "综合搜索", "搜综合", IntentionTypeEnum.SEARCH),

    /**
     * 活动搜索
     */
    SEARCH_ACTIVITY("020", "search-activity", "活动搜索", "活动搜索", IntentionTypeEnum.SEARCH),

    /**
     * 功能搜索
     */
    SEARCH_FUNCTION("021", "search-function", "功能搜索", "功能搜索", IntentionTypeEnum.SEARCH),

    /**
     * 发现广场搜索
     */
    SEARCH_DISCOVERY("022", "search-discovery", "发现广场搜索", "搜索发现", IntentionTypeEnum.SEARCH),

    /**
     * 圈子搜索
     */
    SEARCH_GROUP("023", "search-group", "圈子搜索", "搜索圈子", IntentionTypeEnum.SEARCH),

    /**
     * 搜索重要邮件
     */
    SEARCH_MAIL("028", "search-mail", "重要邮件", "搜邮件", IntentionTypeEnum.SEARCH),

    /**
     * 搜索知识库资源
     */
    SEARCH_KNOWLEDGE_BASE_RESOURCE("038", "search-knowledge-base-resource", "知识库资源", "搜知识库", IntentionTypeEnum.SEARCH),

    /**
     * 笔记知识库搜索
     */
    SEARCH_NOTE_KNOWLEDGE_BASE("039", "search-note-knowledge-base", "搜笔记正文", "搜笔记正文", IntentionTypeEnum.SEARCH),

    // 创建笔记意图
    /**
     * 创建普通笔记
     */
    CREATE_NOTE("024", "create-note", "创建笔记", "创建普通笔记", IntentionTypeEnum.OTHER),

    // AI扩图
    /**
     * AI扩图
     */
    AI_EXPANSION_MAP("025", "ai-expansion-map", "AI扩图", "AI扩图", IntentionTypeEnum.IMAGE),

    // 朋友圈9图
    /**
     * 朋友圈9图
     */
    ONE_CLICK_PUZZLE("026", "one-click-puzzle", "朋友圈9图", "朋友圈9图", IntentionTypeEnum.IMAGE),

    // 创建语音笔记意图
    /**
     * 创建语音笔记
     */
    CREATE_VOICE_NOTE("027", "create-voice-note", "创建语音笔记", "创建语音笔记", IntentionTypeEnum.OTHER),

    // 发邮件意图
    SEND_MAIL("032", "send-mail", "发邮件", "发邮件", IntentionTypeEnum.OTHER),

    // 独立的意图，不在AI助手处理范围内
    /**
     * AI写真
     */
    AI_PHOTOGRAPHY("019", "ai-photography", "AI写真", "AI写真大屏版", IntentionTypeEnum.IMAGE),

    /**
     * AI照相馆
     */
    FACE_SWAP("034", "face-swap", "AI照相馆", "AI照相馆", IntentionTypeEnum.IMAGE),

    /**
     * AI知识库入口
     */
    KNOWLEDGE_ENTRANCE("035", "knowledge_entrance", "AI知识库", "知识库入口", IntentionTypeEnum.OTHER),

    /**
     * 文本工具 子意图用 @see DialogueIntentionSubEnum
     */
    TEXT_TOOL("036", "text_tool", "文本工具", "AI工具意图", IntentionTypeEnum.TEXT),

    /**
     * AI改图
     */
    AI_PHOTO_EDIT("037", "ai-photo-edit", "AI改图", "AI改图", IntentionTypeEnum.IMAGE),

    /**
     * 云手机意图-发短信
     */
    CLOUD_PHONE_SEND_SMS("30001", "cloud-phone-send-sms", "发短信", "发短信", IntentionTypeEnum.CLOUD_PHONE),

    /**
     * 云手机意图-打电话
     */
    CLOUD_PHONE_CALL("30002", "cloud-phone-call", "打电话", "打电话", IntentionTypeEnum.CLOUD_PHONE),

    /**
     * 云手机意图-打开APP
     */
    CLOUD_PHONE_OPEN_APP("30003", "cloud-phone-open-app", "打开APP", "打开APP", IntentionTypeEnum.CLOUD_PHONE),

    /**
     * 云手机意图-订机票
     */
    CLOUD_PHONE_BOOK_TICKET("30004", "cloud-phone-book-ticket", "订机票", "订机票", IntentionTypeEnum.CLOUD_PHONE),

    /**
     * 云手机意图-找影视剧
     */
    CLOUD_PHONE_FILM_AND_TV("30005", "cloud-phone-film-and-tv", "找影视剧", "找影视剧", IntentionTypeEnum.CLOUD_PHONE),

    /**
     * 云手机意图-听书
     */
    CLOUD_PHONE_LISTEN_BOOK("30006", "cloud-phone-listen-book", "听书", "听书", IntentionTypeEnum.CLOUD_PHONE),

    /**
     * 云手机意图-查话费
     */
    CLOUD_PHONE_MOBILE_CHARGES("30007", "cloud-phone-mobile-charges", "查话费", "查话费", IntentionTypeEnum.CLOUD_PHONE),

    /**
     * 云手机意图-查流量
     */
    CLOUD_PHONE_DATA_USAGE("30008", "cloud-phone-data-usage", "查流量", "查流量", IntentionTypeEnum.CLOUD_PHONE),

    /**
     * 云手机意图-查话费
     */
    CLOUD_PHONE_HOTEL("30009", "cloud-phone-query-hotel", "查酒店", "查酒店", IntentionTypeEnum.CLOUD_PHONE),

    /**
     * 云手机意图-地图导航
     */
    CLOUD_PHONE_MAP_NAVIGATION("30010", "cloud-phone-map-navigation", "地图导航", "地图导航", IntentionTypeEnum.CLOUD_PHONE),

    /**
     * 云手机意图-下载/更新应用
     */
    CLOUD_PHONE_DOWNLOAD_OR_UPDATE_APP("30011", "cloud-phone-download-or-update-app", "下载/更新应用", "下载/更新应用", IntentionTypeEnum.CLOUD_PHONE),

    ;

    private static final Map<String, DialogueIntentionEnum> MAP = new ConcurrentHashMap<>();
    public static final Map<IntentionTypeEnum, List<DialogueIntentionEnum>> MAP_TYPE = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(DialogueIntentionEnum.class).forEach(item -> {
            MAP.put(item.code, item);

            List<DialogueIntentionEnum> list = MAP_TYPE.get(item.type);
            if (null == list) {
                list = new ArrayList<>();
                MAP_TYPE.put(item.type, list);
            }
            list.add(item);
        });
    }

    public static DialogueIntentionEnum getByCode(String code) {
        if (null == code) {
            return null;
        }
        return MAP.get(code);
    }

    /**
     * 非图片意图：文生文、图配文、其他
     */
    public static final List<String> NON_IMAGE_MODULE = Arrays.asList(
            DialogueIntentionEnum.PICTURE_GENERATE_TEXT.getCode(),
            DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode(),
            DialogueIntentionEnum.OTHER.getCode()
    );

    /**
     * 非AI工具意图
     */
    public static final List<String> NON_AI_TOOLS_MODULE = Arrays.asList(
            DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode(),
            DialogueIntentionEnum.OTHER.getCode()
    );

    /**
     * 根据意图code获取意图枚举（默认值为文生文意图枚举）
     *
     * @param code 意图code
     * @return 意图枚举
     */
    public static DialogueIntentionEnum getByCodeOrDefault(String code) {
        return getByCodeOrDefault(code, TEXT_GENERATE_TEXT);
    }

    /**
     * 根据意图code获取意图枚举（可传入默认意图枚举）
     *
     * @param code        意图code
     * @param defaultEnum 默认意图枚举
     * @return 意图枚举
     */
    public static DialogueIntentionEnum getByCodeOrDefault(String code, DialogueIntentionEnum defaultEnum) {
        DialogueIntentionEnum intentionEnum = getByCode(code);
        return (intentionEnum == null) ? defaultEnum : intentionEnum;
    }

    /**
     * 是否存在
     *
     * @param code 意图编码
     * @return true-存在
     */
    public static boolean isExist(String code) {
        return getByCode(code) != null;
    }

    /**
     * AI助手使用的意图是否存在，AI写真不在助手范围内
     *
     * @param code 意图编码
     * @return true-存在
     */
    public static boolean isExistOnAide(String code) {
        DialogueIntentionEnum intentionEnum = getByCode(code);
        return !(intentionEnum == null || AI_PHOTOGRAPHY.equals(intentionEnum));
    }

    /**
     * 是否跳链意图
     *
     * @param code 意图编码
     * @return true-是
     */
    public static boolean isJumpLinkIntention(String code) {
        return DialogueIntentionEnum.INTELLIGENT_CUTOUT.getCode().equals(code)
                || DialogueIntentionEnum.AI_ELIMINATE.getCode().equals(code)
                || DialogueIntentionEnum.CLOUD_CAMERA.getCode().equals(code)
                || DialogueIntentionEnum.AI_EXPANSION_MAP.getCode().equals(code)
                || DialogueIntentionEnum.ONE_CLICK_PUZZLE.getCode().equals(code)
                || DialogueIntentionEnum.APPEARANCE_PREDICTION.getCode().equals(code)
                || DialogueIntentionEnum.BABY_TIME_MACHINE.getCode().equals(code)
                || DialogueIntentionEnum.AI_EMOTICON.getCode().equals(code);
    }

    /**
     * 是否跳链意图
     *
     * @param code 意图编码
     * @return true-是
     */
    public static boolean isJumpLinkIntentionV2(String code) {
        return DialogueIntentionEnum.AI_ELIMINATE.getCode().equals(code)
                || DialogueIntentionEnum.CLOUD_CAMERA.getCode().equals(code)
                || DialogueIntentionEnum.LIVE_PHOTOS.getCode().equals(code)
                || DialogueIntentionEnum.ONE_CLICK_PUZZLE.getCode().equals(code)
                || DialogueIntentionEnum.APPEARANCE_PREDICTION.getCode().equals(code)
                || DialogueIntentionEnum.BABY_TIME_MACHINE.getCode().equals(code)
                || DialogueIntentionEnum.AI_EMOTICON.getCode().equals(code);
    }

    /**
     * 是否为搜索意图（有综合搜索）
     *
     * @param code 意图编码
     * @return true-是
     */
    public static boolean isSearchIntention(String code) {
        List<String> allSearchIntentionList = getSearchSingleIntentionList();
        allSearchIntentionList.add(DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode());
        return allSearchIntentionList.contains(code);
    }

    /**
     * 是否为搜索意图（有综合搜索意图、泛化意图）
     *
     * @param code 意图编码
     * @return true-是
     */
    public static boolean isSearchIntentionOrOther(String code) {
        List<String> allSearchIntentionList = getSearchSingleIntentionList();
        allSearchIntentionList.add(DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode());
        allSearchIntentionList.add(DialogueIntentionEnum.OTHER.getCode());
        return allSearchIntentionList.contains(code);
    }


    /**
     * 是否为创建笔记/创建语音笔记意图
     *
     * @param code 意图编码
     * @return true-是
     */
    public static boolean isCreateNoteIntention(String code) {
        return DialogueIntentionEnum.CREATE_NOTE.getCode().equals(code)
                || DialogueIntentionEnum.CREATE_VOICE_NOTE.getCode().equals(code);
    }

    /**
     * 是否为云邮助手不支持意图 除了：搜索意图（除搜图片、搜文档、搜邮件、搜笔记，功能搜索）
     *
     * @param code 意图编码
     * @return true-是
     */
    public static boolean isMailNotSupportIntention(String businessType, String code) {
        ModelProperties modelProperties = SpringUtil.getBean(ModelProperties.class);
        List<String> intentions = modelProperties.getSupportIntentions(AssistantEnum.YUN_MAIL, businessType);
        if (CollUtil.isEmpty(intentions)) {
            // 空都支持
            return false;
        }
        return !(intentions.contains(code));
    }

    /**
     * 是否为云邮助手不支持二级子意图
     *
     * @param code 意图编码
     * @return true-是
     */
    public static boolean isMailNotSupportSubIntention(String businessType, String code) {
        ModelProperties modelProperties = SpringUtil.getBean(ModelProperties.class);
        List<String> intentions = modelProperties.getSupportSubIntentions(AssistantEnum.YUN_MAIL, businessType);
        if (CollUtil.isEmpty(intentions)) {
            // 空都支持
            return false;
        }
        return !(intentions.contains(code));
    }

    /**
     * 是否为搜索意图（云邮助手）
     *
     * @param code 意图编码
     * @return true-是
     */
    public static boolean isMailSearchIntention(String businessType, String code) {
        ModelProperties modelProperties = SpringUtil.getBean(ModelProperties.class);
        List<String> intentions = modelProperties.getSearchIncludeIntentions(AssistantEnum.YUN_MAIL, businessType);
        if (CollUtil.isEmpty(intentions)) {
            // 空都支持
            return true;
        }
        return intentions.contains(code);
    }

    /**
     * 是否为云手机搜索意图
     *
     * @param businessType the business type
     * @param code         the code
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2025-07-14 11:55
     */
    public static boolean isCloudPhoneSearchIntention(String businessType, String code) {
        ModelProperties modelProperties = SpringUtil.getBean(ModelProperties.class);
        List<String> intentions = modelProperties.getSearchIncludeIntentions(AssistantEnum.CLOUD_PHONE, businessType);
        if (CollUtil.isEmpty(intentions)) {
            return Boolean.TRUE;
        }
        return intentions.contains(code);
    }

    public static boolean isCLoudPhoneNotSupportIntention(String businessType, String code) {
        ModelProperties modelProperties = SpringUtil.getBean(ModelProperties.class);
        List<String> intentions = modelProperties.getSupportIntentions(AssistantEnum.CLOUD_PHONE, businessType);
        if (CollUtil.isEmpty(intentions)) {
            // 空都支持
            return false;
        }
        return !(intentions.contains(code));
    }

    /**
     * 是否为搜索意图（无综合搜索意图）
     *
     * @param code 意图编码
     * @return true-是
     */
    public static boolean isSearchSingleIntention(String code) {
        return getSearchSingleIntentionList().contains(code);
    }

    /**
     * 所有搜索意图（无综合搜索意图）
     */
    public static List<String> getSearchSingleIntentionList() {
        List<String> list = new ArrayList<>();
        list.add(DialogueIntentionEnum.SEARCH_IMAGE.getCode());
        list.add(DialogueIntentionEnum.SEARCH_DOCUMENT.getCode());
        list.add(DialogueIntentionEnum.SEARCH_VIDEO.getCode());
        list.add(DialogueIntentionEnum.SEARCH_AUDIO.getCode());
        list.add(DialogueIntentionEnum.SEARCH_FOLDER.getCode());
        list.add(DialogueIntentionEnum.SEARCH_NOTE.getCode());
        list.add(DialogueIntentionEnum.SEARCH_ACTIVITY.getCode());
        list.add(DialogueIntentionEnum.SEARCH_FUNCTION.getCode());
        list.add(DialogueIntentionEnum.SEARCH_DISCOVERY.getCode());
        list.add(DialogueIntentionEnum.SEARCH_GROUP.getCode());
        list.add(DialogueIntentionEnum.SEARCH_MAIL.getCode());
        return list;
    }

    /**
     * 是否为文本意图
     *
     * @param code 意图编码
     * @return true-是
     */
    public static boolean isTextIntention(String code) {
        return DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode().equals(code)
                || DialogueIntentionEnum.OTHER.getCode().equals(code);
    }

    /**
     * 是否为综合搜索意图（包括泛化意图）
     *
     * @param code 意图编码
     * @return true-是
     */
    public static boolean isComprehensiveIntention(String code) {
        return DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode().equals(code)
                || DialogueIntentionEnum.OTHER.getCode().equals(code);
    }

    /**
     * 是否为AI工具意图
     *
     * @param code 意图编码
     * @return true-是
     */
    public static boolean isAiToolIntention(String code) {
        return DialogueIntentionEnum.PICTURE_GENERATE_TEXT.getCode().equals(code)
                || DialogueIntentionEnum.INTELLIGENT_CUTOUT.getCode().equals(code)
                || DialogueIntentionEnum.PICTURE_COMIC_STYLE.getCode().equals(code)
                || DialogueIntentionEnum.LIVE_PHOTOS.getCode().equals(code)
                || DialogueIntentionEnum.AI_ELIMINATE.getCode().equals(code)
                || DialogueIntentionEnum.AI_HEAD_SCULPTURE.getCode().equals(code)
                || DialogueIntentionEnum.OLD_PHOTOS_REPAIR.getCode().equals(code)
                || DialogueIntentionEnum.IMAGE_QUALITY_RESTORATION.getCode().equals(code)
                || DialogueIntentionEnum.INTELLIGENT_BEAUTY.getCode().equals(code)
                || DialogueIntentionEnum.CLOUD_CAMERA.getCode().equals(code)
                || DialogueIntentionEnum.AI_EXPANSION_MAP.getCode().equals(code)
                || DialogueIntentionEnum.ONE_CLICK_PUZZLE.getCode().equals(code)
                || DialogueIntentionEnum.APPEARANCE_PREDICTION.getCode().equals(code)
                || DialogueIntentionEnum.BABY_TIME_MACHINE.getCode().equals(code)
                || DialogueIntentionEnum.AI_EMOTICON.getCode().equals(code)
                || DialogueIntentionEnum.SMART_FAKE_CHECK.getCode().equals(code)
                || DialogueIntentionEnum.AI_PHOTO_EDIT.getCode().equals(code);
    }

    /**
     * 是否存在搜图片意图
     *
     * @param code 意图编码
     * @return true-是
     */
    public static boolean existImageIntention(String code) {
        return DialogueIntentionEnum.SEARCH_IMAGE.getCode().equals(code)
                || DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode().equals(code);
    }

    /**
     * 是否存在搜文档意图
     *
     * @param code 意图编码
     * @return true-是
     */
    public static boolean existDocumentIntention(String code) {
        return DialogueIntentionEnum.SEARCH_DOCUMENT.getCode().equals(code)
                || DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode().equals(code);
    }

    /**
     * 个人云搜索枚举列表
     */
    public static List<DialogueIntentionEnum> searchPersonFileEnums() {
        List<DialogueIntentionEnum> fileIntentionEnums = new ArrayList<>();
        fileIntentionEnums.add(DialogueIntentionEnum.SEARCH_DOCUMENT);
        fileIntentionEnums.add(DialogueIntentionEnum.SEARCH_VIDEO);
        fileIntentionEnums.add(DialogueIntentionEnum.SEARCH_AUDIO);
        fileIntentionEnums.add(DialogueIntentionEnum.SEARCH_FOLDER);
        return fileIntentionEnums;
    }

    /**
     * 旧底座个人云搜索枚举列表
     */
    public static List<DialogueIntentionEnum> searchPersonOseFileEnums() {
        List<DialogueIntentionEnum> fileIntentionEnums = new ArrayList<>();
        fileIntentionEnums.add(DialogueIntentionEnum.SEARCH_IMAGE);
        fileIntentionEnums.add(DialogueIntentionEnum.SEARCH_DOCUMENT);
        fileIntentionEnums.add(DialogueIntentionEnum.SEARCH_VIDEO);
        fileIntentionEnums.add(DialogueIntentionEnum.SEARCH_AUDIO);
        fileIntentionEnums.add(DialogueIntentionEnum.SEARCH_FOLDER);
        return fileIntentionEnums;
    }

    /**
     * 个人资产搜索意图编码列表
     */
    public static List<String> getSearchFileIntentionCodeList() {
        List<String> searchFileIntentionCodeList = new ArrayList<>();
        searchFileIntentionCodeList.add(DialogueIntentionEnum.SEARCH_IMAGE.getCode());
        searchFileIntentionCodeList.add(DialogueIntentionEnum.SEARCH_DOCUMENT.getCode());
        searchFileIntentionCodeList.add(DialogueIntentionEnum.SEARCH_VIDEO.getCode());
        searchFileIntentionCodeList.add(DialogueIntentionEnum.SEARCH_AUDIO.getCode());
        searchFileIntentionCodeList.add(DialogueIntentionEnum.SEARCH_FOLDER.getCode());
        return searchFileIntentionCodeList;
    }

    /**
     * 是否为发邮件意图
     *
     * @param code 意图编码
     * @return true-是
     */
    public static boolean isSendMail(String code) {
        return DialogueIntentionEnum.SEND_MAIL.getCode().equals(code);
    }


    /**
     * 是否文本工具意图
     *
     * @param code
     * @return
     */
    public static boolean isTextToolIntention(String code) {
        return DialogueIntentionEnum.TEXT_TOOL.getCode().equals(code);
    }

    /**
     * 特殊需要替换\\n的意图
     *
     * @param intentionCode
     * @param subIntentionCode
     * @return
     */
    public static boolean isSpecReplaceBr(String intentionCode, String subIntentionCode) {
        if (isTextToolIntention(intentionCode)) {
            if (DialogueIntentionSubEnum.isAiPhotoSolveProblems(subIntentionCode)) {
                // 拍照解题
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否为影视类意图
     */
    public static boolean checkMovieIntention(List<String> allIntentionCodes) {
        boolean isMovieIntention = false;
        if (CollUtil.isNotEmpty(allIntentionCodes)) {
            // 获取影视类意图代码列表
            List<String> movieIntentionCodes = DialogueIntentionEnum.getMovieAndTelevisionIntention();

            // 判断当前意图列表是否完全包含影视类意图
            isMovieIntention = CollUtil.containsAll(allIntentionCodes, movieIntentionCodes);
        }
        log.info("判断是否为影视类意图：{}", isMovieIntention);
        return isMovieIntention;
    }

    /**
     * 获取影视类意图列表
     */
    public static List<String> getMovieAndTelevisionIntention() {
        List<String> intentionList = new ArrayList<>();
        intentionList.add(DialogueIntentionEnum.SEARCH_FOLDER.getCode());
        intentionList.add(DialogueIntentionEnum.SEARCH_VIDEO.getCode());
        intentionList.add(DialogueIntentionEnum.SEARCH_DISCOVERY.getCode());
        intentionList.add(DialogueIntentionEnum.SEARCH_GROUP.getCode());
        return intentionList;
    }

    /**
     * 是否搜索全部意图
     *
     * @param intentionCode
     * @return
     */
    public static boolean isSearchTypeIntentionForAll(String intentionCode) {
        DialogueIntentionEnum intention = DialogueIntentionEnum.getByCode(intentionCode);
        return (null != intention && IntentionTypeEnum.isSearch(intention.getType()));
    }

    /**
     * 是否图片工具全部意图
     *
     * @param intentionCode
     * @return
     */
    public static boolean isImageTypeIntentionForAll(String intentionCode) {
        DialogueIntentionEnum intention = DialogueIntentionEnum.getByCode(intentionCode);
        return (null != intention && IntentionTypeEnum.isImage(intention.getType()));
    }

    /**
     * 是否云手机全部意图
     *
     * @param intentionCode
     * @return
     */
    public static boolean isCloudPhoneTypeIntentionForAll(String intentionCode) {
        DialogueIntentionEnum intention = DialogueIntentionEnum.getByCode(intentionCode);
        return (null != intention && IntentionTypeEnum.isCloudPhone(intention.getType()));
    }

    /**
     * 云手机1.0支持意图 (30001~30005)
     *
     * @param intentionEnum
     * @return
     */
    public static boolean isCloudPhoneV100(DialogueIntentionEnum intentionEnum) {
        if (null == intentionEnum) {
            return false;
        }
        String code = intentionEnum.getCode();
        return (CLOUD_PHONE_SEND_SMS.getCode().equals(code) || CLOUD_PHONE_CALL.getCode().equals(code)
                || CLOUD_PHONE_OPEN_APP.getCode().equals(code) || CLOUD_PHONE_BOOK_TICKET.getCode().equals(code)
                || CLOUD_PHONE_FILM_AND_TV.getCode().equals(code));
    }

    /**
     * 云手机1.1支持意图 (30001~30009)
     *
     * @param intentionEnum
     * @return
     */
    public static boolean isCloudPhoneV110(DialogueIntentionEnum intentionEnum) {
        if (null == intentionEnum) {
            return false;
        }
        String code = intentionEnum.getCode();
        return isCloudPhoneV100(intentionEnum)
                || (CLOUD_PHONE_LISTEN_BOOK.getCode().equals(code) || CLOUD_PHONE_MOBILE_CHARGES.getCode().equals(code)
                || CLOUD_PHONE_DATA_USAGE.getCode().equals(code) || CLOUD_PHONE_HOTEL.getCode().equals(code));
    }

	/**
	 * 允许返回leadCopy的意图
	 *
	 * @param toolsCommand
	 * @param subToolsCommand
	 * @return
	 */
	public static boolean allowLeadCopyIntention(String toolsCommand, String subToolsCommand) {
		if (isTextToolIntention(toolsCommand) && DialogueIntentionSubEnum.isMemoryAlbum(subToolsCommand)) {
			// AI生成回忆相册允许返回LeadCopy
			return true;
		}
		return false;
	}
    /**
     * 云手机1.2支持意图 (支持地图导航(30010)、下载/更新应用(30011)意图)
     *
     * @param intentionEnum the intention
     * @return true if cloud phone v120
     */
    public static boolean isCloudPhoneV120(DialogueIntentionEnum intentionEnum) {
        if (Objects.isNull(intentionEnum)) {
            return Boolean.FALSE;
        }
        String code = intentionEnum.getCode();
        return isCloudPhoneV110(intentionEnum) || CLOUD_PHONE_MAP_NAVIGATION.getCode().equals(code)
                || CLOUD_PHONE_DOWNLOAD_OR_UPDATE_APP.getCode().equals(code);
    }


    /**
     * 编号
     */
    private final String code;
    /**
     * 指令
     */
    private final String instruction;
    /**
     * 名称
     */
    private final String name;

    /**
     * 推荐使用的名称
     */
    private final String recommendName;


    /**
     * 意图类型
     */
    private final IntentionTypeEnum type;


}
