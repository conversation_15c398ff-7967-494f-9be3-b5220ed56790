package com.zyhl.yun.api.outer.repository;

import com.github.pagehelper.PageInfo;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 个人知识库
 *
 * <AUTHOR>
 * @date 2025/04/14
 */
public interface UserKnowledgeRepository {

    /**
     * 创建个人知识库
     *
     * @param entity 实体对象
     * @return 数量
     */
    int add(@NotNull UserKnowledgeEntity entity);

    /**
     * 批量查询个人知识库详情
     *
     * @param ids id集合
     * @return 个人知识库详情集合
     */
    List<UserKnowledgeEntity> getInfoListByIds(List<Long> ids);

    /**
     * 根据id查询详情（不过滤删除状态）
     *
     * @param id 知识库id
     * @return 实体对象
     */
    UserKnowledgeEntity selectInfoById(@NotNull Long id);

    /**
     * 根据id查询
     *
     * @param idList id集合
     * @return 实体对象
     */
    List<UserKnowledgeEntity> selectByIds(List<Long> idList);

    /**
     * 更新
     *
     * @param entity 实体对象
     * @return 数量
     */
    int update(@NotNull UserKnowledgeEntity entity);

    /**
     * 根据id查询
     *
     * @param id 知识库id
     * @return 实体对象
     */
    UserKnowledgeEntity selectById(@NotNull Long id);

    /**
     * 根据id和userId查询
     *
     * @param id 知识库id
     * @return 实体对象
     */
    UserKnowledgeEntity selectByIdAndUserId(@NotNull Long id, String userId);

    /**
     * 根据id查询
     *
     * @return 实体对象
     */
    UserKnowledgeEntity selectFirstOne(@NotNull String userId);

    /**
     * 根据userId查询
     *
     * @param userId 用户id
     * @return 实体对象
     */
    List<UserKnowledgeEntity> selectByUserId(@NotNull String userId);

    /**
     * 根据知识库名查询
     *
     * @param name 知识库名
     * @return 实体对象
     */
    UserKnowledgeEntity selectByName(String name, String userId);

    /**
     * 根据id删除，逻辑删除
     *
     * @param id 知识库id
     */
    void deleteByIdAndUserId(@NotNull Long id, String userId);

    /**
     * 分页查询知识库
     *
     * @return 实体对象
     */
    PageInfo<UserKnowledgeEntity> list(String userId, Integer baseType, PageInfoDTO pageInfo);

    /**
     * 根据知识库id查询
     *
     * @param knowledgeId 知识库id
     * @return 实体对象
     */
    UserKnowledgeEntity selectByKnowledgeId(@NotNull Long knowledgeId);

    /**
     * 获取全部删除状态 by userId
     * 
     * @param userId 用户id
     * @return id列表
     */
    List<UserKnowledgeEntity> getAllByUserId(String userId);

    /**
     * 更新选中状态
     *
     * @param userId      用户id
     * @param baseIdList 知识库id集合
     */
    void updateSelected(String userId, List<String> baseIdList);

    /**
     * 查询总数
     *
     * @param baseId 知识库id
     * @param userId 用户id
     * @return 数量
     */
    int count(Long baseId, String userId);

    /**
     * 查询总数
     *
     * @param userId 用户id
     * @return 数量
     */
    int countByUserId(String userId);

    /**
     * 查询总数
     *
     * @param baseId 知识库id
     * @return 数量
     */
    int countByBaseId(Long baseId);

    /**
     * 查询选中的知识库（自己的 + 加入的）
     *
     * @param userId
     * @return
     */
    List<UserKnowledgeEntity> getSelectedBase(String userId);

    /**
     * 查询笔记同步知识库
     * @param userId
     * @param bizType
     * @return
     */
    UserKnowledgeEntity selectNoteSync(String userId,Integer bizType);

}