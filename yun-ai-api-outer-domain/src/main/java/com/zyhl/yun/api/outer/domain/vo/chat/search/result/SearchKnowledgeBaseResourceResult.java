package com.zyhl.yun.api.outer.domain.vo.chat.search.result;

import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 对话信息-搜索结果-知识库资源
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchKnowledgeBaseResourceResult extends SearchCommonResult implements Serializable {

    private static final long serialVersionUID = -7565163884182197065L;

    /** 知识库资源列表 */
    private List<PersonalKnowledgeResource> resourceList;

    /** 查询总数 */
    private Integer totalCount;

    /** 下一页游标 */
    private List<Object> pageAfter;

}
