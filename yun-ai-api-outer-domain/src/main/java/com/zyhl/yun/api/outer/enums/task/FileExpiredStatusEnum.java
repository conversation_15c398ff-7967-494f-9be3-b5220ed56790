package com.zyhl.yun.api.outer.enums.task;


import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * AI输入算法任务-文件过期状态
 * @Author: WeiJingKun
 */
public enum FileExpiredStatusEnum {

    /**
     * 未过期
     */
    UNEXPIRED(0, "未过期"),

    /**
     * 已过期
     */
    EXPIRED(1, "已过期"),

    ;

    private static final Map<Integer, FileExpiredStatusEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(FileExpiredStatusEnum.class).forEach(item -> MAP.put(item.code, item));
    }

    public static FileExpiredStatusEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return MAP.get(code);
    }

    /**
     * 是否存在
     * @param code 编码
     * @return true-存在
     */
    public static boolean isExist(Integer code) {
        return getByCode(code) != null;
    }


    FileExpiredStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    
}
