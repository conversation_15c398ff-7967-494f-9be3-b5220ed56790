package com.zyhl.yun.api.outer.enums;

/**
 * 笔记附件类型
 * <AUTHOR>
 * @date 2025/4/18 19:49
 */
public enum NoteAttachmentTypeEnum {
    /**
     * 录音笔记
     */
    RECORD("record", "录音笔记"),
    /**
     * 音频笔记
     */
    AUDIO("audio", "音频笔记"),
    /**
     * 视频笔记
     */
    VIDEO("video", "视频笔记"),
    /**
     * 图片笔记
     */
    IMAGE("image", "图片笔记"),

    ;
    private String code;

    private String desc;

    NoteAttachmentTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
