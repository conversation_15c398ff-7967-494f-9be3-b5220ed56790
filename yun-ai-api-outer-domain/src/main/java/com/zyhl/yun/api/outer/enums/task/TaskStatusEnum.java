package com.zyhl.yun.api.outer.enums.task;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * AI输入算法任务-任务状态
 * @Author: WeiJingKun
 */
public enum TaskStatusEnum {

    /**
     * 待处理
     */
    NO_PROCESS(1, "待处理"),

    /**
     * 处理中
     */
    IN_PROCESS(2, "处理中"),

    /**
     * 任务完成
     */
    PROCESS_FINISH(3, "任务完成"),

    /**
     * 任务失败
     */
    PROCESS_FAILURE(4, "任务失败"),

    /**
     * 已过期
     */
    OVERDUE(5, "已过期"),

    ;

    private static final Map<Integer, TaskStatusEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(TaskStatusEnum.class).forEach(item -> MAP.put(item.code, item));
    }

    public static TaskStatusEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return MAP.get(code);
    }

    /**
     * 是否存在
     * @param code 编码
     * @return true-存在
     */
    public static boolean isExist(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 是否为任务失败
     * @return true-是
     */
    public static boolean isTaskFail(Integer code){
        return TaskStatusEnum.PROCESS_FAILURE.getCode().equals(code)
                || TaskStatusEnum.OVERDUE.getCode().equals(code);
    }

    /**
     * 是否为处理中
     * @return true-是
     */
    public static boolean isProcessing(Integer code){
        return TaskStatusEnum.NO_PROCESS.getCode().equals(code)
                || TaskStatusEnum.IN_PROCESS.getCode().equals(code);
    }


    TaskStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
