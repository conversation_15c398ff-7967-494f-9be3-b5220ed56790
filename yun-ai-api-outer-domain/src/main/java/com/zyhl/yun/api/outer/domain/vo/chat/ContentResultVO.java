package com.zyhl.yun.api.outer.domain.vo.chat;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.vo.ChatApplicationType;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.domain.vo.chat.search.SearchInfo;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchResult;
import com.zyhl.yun.api.outer.enums.task.TaskFeePaidStatusEnum;
import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 对话结果-VO
 * @Author: WeiJingKun
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContentResultVO implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
     * 对话ID
     */
    private String dialogueId;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 会话ID
     */
    private String sessionId;
    
	/**
	 * 状态 0--对话中；默认0 1--对话停止 2--对话失败 3--对话成功 4--对话终止（非正常结束） 5--对话过期
	 */
	private Integer status;
	
    /**
     * 对话类型;0:对话历史记录,1:智囊历史记录
     */
    private Integer talkType;
    /**
     * 资源类型;0-无，1 邮件， 2 笔记， 3 图片
     */
    private Integer resourceType;
    /**
     * 工具指令;对接意图指令
     */
    private String toolsCommand;
    /**
     * 模型类型;模型 qwen：通义千问，xfyun：讯飞星火大模型
     */
    private String modelType;
    /**
     * 输入内容;输入文本内容
     */
    private String  inContent;
    /**
     * 输入时间
     */
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date    inAuditTime;
    /**
     * 输入内容审批结果;状态码：2通过，其他失败
     */
    private Integer inAuditStatus;
    /**
     * 输入资源ID;（笔记/邮件/图片ID；纯文本时为空）
     */
    private String inResourceId;
    /**
     * 输出内容
     */
    private String outContent;
    /**
     * 输出时间
     */
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date outAuditTime;
    /**
     * 输出内容审批结果;状态码：2通过，其他失败
     */
    private Integer outAuditStatus;
    /**
     * 输出资源类型： 1--云盘文件ID 2--文件下载地址 3--
     *
     * @see com.zyhl.yun.api.outer.enums.ImageTransmissionTypeEnum
     */
    private Integer outResourceType;
    /**
     * 输出资源信息；（云盘文件ID/下载URL）
     */
    private String outResourceId;
    /**
	 * 输出文本类型：1--普通文本 2--富文本
	 * @see com.zyhl.yun.api.outer.enums.OutContentTypeEnum
	 */
	private Integer outContentType;
	
    /**
     * 渠道来源
     */
    private String sourceChannel;
   
    /**
     * 创建时间
     */
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date createTime;
    
    /** 引导文案 */
    private LeadCopyVO leadCopy;

    /**
     * 扩展信息（json格式）
     */
    private String extInfo;

    /**
     * 是否喜欢 0:不喜欢，1:喜欢
     */
    private Integer likeComment;

    /**
     * 默认评论
     */
    private String defaultComment;
    /**
     * 用户评论
     */
    private String customComment;

    /**
     * 任务-错误结果码
     */
    private String resultCode;

    /**
     * 任务-错误信息
     */
    private String resultMsg;

    /**
     * 任务-状态
     *
     * @see TaskStatusEnum
     */
    private Integer taskStatus;

    /** 搜索参数 */
    private SearchParam searchParam;

    /** 搜索结果 */
    private SearchResult searchResult;

    /** 对话应用类型 */
    private ChatApplicationType applicationInfo;

    /**
     * 付费扣费状态：
     *
     * -1：不扣费（该任务不涉及扣费流程） 0：未扣费 1：已扣费
     *
     * @see TaskFeePaidStatusEnum
     */
    private Integer feePaidStatus;

    /**
     * 对话结果推荐，当对话信息不为空时返回
     * 如果leadCopy不为空，优先展示leadCopy
     */
    private DialogueRecommendVO recommend;

    /**
     * 返回结果的标题
     */
    private String title;

    /**
     * 个人知识库参考文件，可选，仅第一次流式结果返回
     */
    private List<File> personalKnowledgeFileList;

    /** 搜索信息列表 */
    private List<SearchInfo> searchInfoList;

    /**
     * 邮件信息
     */
    private MailInfoVO mailInfo;

    /** ============以下字段为后续考虑移除的字段============== */

    /** 对话结果推荐信息（json格式）*/
    private String recommendInfo;
    
    /**
     * 对话结果推荐信息-中部（json格式）
     */
    private String middleRecommendInfo;

}
