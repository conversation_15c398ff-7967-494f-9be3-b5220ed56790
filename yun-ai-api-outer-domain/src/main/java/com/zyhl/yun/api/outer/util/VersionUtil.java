package com.zyhl.yun.api.outer.util;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.python.jline.internal.Log;

import com.zyhl.yun.api.outer.constants.Const;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 版本控制工具类
 * <AUTHOR>
 */
public class VersionUtil {

    public static final String H5_VERSION = "h5Version";
    public static final String PC_VERSION = "pcVersion";

    @Getter
    @AllArgsConstructor
    public enum XiaoTianVersionEnum {

        // 1.1.2
        V1_1_2("1.1.2", "小天H5版本"),
        // 1.2.1
        V1_2_1("1.2.1", "小天H5版本：1、多意图识别；2、推荐提问语句；3、搜素结果答复隨机化；4、模型字数限制"),
        // 1.3.0
        V1_3_0("1.3.0", "小天H5版本：1、文档搜索"),
        // 1.4.0
        V1_4_0("1.4.0", "小天H5版本：1、圈子搜索；2、创建语音笔记；；3、邮件搜索"),
        // 1.5.0
        V1_5_0("1.5.0", "小天H5版本：1、个人知识库"),
        // 1.5.1
        V1_5_1("1.5.1", "小天H5版本：1、AI扩图；2、朋友圈9图"),
        // 1.7.0
        V1_7_0("1.7.0", "小天H5版本：1、发邮件；2、图片智能鉴伪"),
        // 1.7.3
        V1_7_3("1.7.3", "小天H5版本：1、AI助手V1.7.3"),
        // 1.8.0
        V1_8_0("1.8.0", "小天H5版本（AI助手V1.8.0）：AI知识库意图"),
        // 1.9.0（弃用，由于前端提前升级版本，1.9.1替换1.9.0）
        V1_9_0("1.9.0", "小天H5版本（AI助手V1.9.0）：开联网搜索角标&&文本工具意图"),
        // 1.9.1（文本工具意图转为2.0.0，1.9.1支持开联网搜索角标）
        V1_9_1("1.9.1", "小天H5版本（AI助手V1.9.1）：开联网搜索角标"),
        // 2.0.0
        V2_0_0("2.0.0", "小天H5版本（AI助手V2.0.0）：文本工具意图&&搜索知识库资源"),
        // 2.0.2
        V2_0_2("2.0.2", "小天H5版本（AI助手V2.0.2）：生成回忆相册&&人物关系相册推荐"),
        // 2.0.3
        V2_0_3("2.0.3", "小天H5版本（AI助手V2.0.3）：功能搜索V2&&活动搜索V2"),
        // 2.1.0
        V2_1_0("2.1.0", "H5版本V2.1.0：图书快速阅读意图"),
        // 2.1.1
        V2_1_1("2.1.1", "H5版本V2.1.1：AI扩写&&生成回忆相册增加人物关系相册推荐"),
        // 最大版本号
        VX_MAX("9.9.9", "小天H5版本（AI助手VX max）：无"),
        ;

        /**
         * 版本号
         */
        private final String version;
        /**
         * 备注信息
         */
        private final String remark;

    }

    @Getter
    @AllArgsConstructor
    public enum ClientVersionEnum {

        // 11.3
        V11_3("11.3", "客户端版本：1、笔记搜索改造"),
        // 12.0.3
        V12_0_3("12.0.3", "客户端版本：1、全文检索版本控制"),
        // 12.1.0
        V12_1_0("12.1.0", "客户端版本：1、发现广场搜索扩词优化"),

        V_CLOUD_PHONE_4_4_0("4.4.0", "云手机客户端版本：支持30001~30005意图"),

        V_CLOUD_PHONE_4_5_0("4.5.0", "云手机客户端版本：支持30001~30009意图"),

        V_CLOUD_PHONE_4_6_2("4.6.2", "云手机客户端版本：支持搜索云盘图片、搜索139邮箱邮件、地图导航(30010)、下载/更新应用(30011)"),

        ;

        /**
         * 版本号
         */
        private final String version;
        /**
         * 备注信息
         */
        private final String remark;

    }

    @Getter
    @AllArgsConstructor
    public enum MailVersionEnum {

        // 云邮AI助手版本2.0.13
        V2_0_13("2.0.13", "云邮AI助手版本：1、图片搜索"),
        V2_0_15("2.0.15", "云邮AI助手版本：1、图片搜索 2、搜索文档 3、搜邮件"),
        V2_0_16("2.0.16", "云邮AI助手版本：1、图片搜索 2、搜索文档 3、搜邮件 4、搜笔记 5、功能搜索"),
        V1_6_0_PC("1.6.0", "云邮AI助手(webai)版本：增加角标功能"),

        ;

        /**
         * 版本号
         */
        private final String version;
        /**
         * 备注信息
         */
        private final String remark;

    }

    public static void main(String[] args) {
        System.out.println(compareVersion(null, null) == 0);
        System.out.println(compareVersion("", "") == 0);
        System.out.println(compareVersion("1", "") == 1);
        System.out.println(compareVersion("", "1") == -1);
        System.out.println(compareVersion("2", "1") == 1);
        System.out.println(compareVersion("2", "1.1") == 1);
        System.out.println(compareVersion("2", "2.1") == -1);
        System.out.println(compareVersion("2.1", "2.1") == 0);
        System.out.println(compareVersion("15.1", "16") == -1);
        System.out.println(compareVersion("16.1", "16") == 1);
        System.out.println(compareVersion("5.1", "16.1") == -1);
    }

    public static int compareVersion(String version1, String version2) {
        if (CharSequenceUtil.isEmpty(version1) && CharSequenceUtil.isEmpty(version2)) {
            return 0;
        }
        if (CharSequenceUtil.isEmpty(version1)) {
            return -1;
        }
        if (CharSequenceUtil.isEmpty(version2)) {
            return 1;
        }

        String[] v1 = version1.split("\\.");
        String[] v2 = version2.split("\\.");

        int len = Math.min(v1.length, v2.length);
        for (int i = 0; i < len; i++) {
            int v1i = Integer.parseInt(v1[i]);
            int v2i = Integer.parseInt(v2[i]);
            if (v1i > v2i) {
                return 1;
            } else if (v1i < v2i) {
                return -1;
            }
        }

        return Integer.compare(v1.length, v2.length);
    }

    /**
     * h5Version >= 小天1.3.0版本
     *
     * @return boolean
     * @Author: WeiJingKun
     */
    public static boolean xtH5VersionGte130(String h5Version) {
        h5Version = CharSequenceUtil.isNotBlank(h5Version) ? h5Version : RequestContextHolder.getH5Version();
        return compareVersion(h5Version, XiaoTianVersionEnum.V1_3_0.getVersion()) >= 0;
    }

    /**
     * h5Version < 小天1.4.0版本
     *
     * @return boolean
     * @Author: WeiJingKun
     */
    public static boolean xtH5VersionLt140() {
        return compareVersion(RequestContextHolder.getH5Version(), XiaoTianVersionEnum.V1_4_0.getVersion()) < 0;
    }

    /**
     * h5Version < 小天1.5.0版本
     *
     * @return boolean
     */
    public static boolean xtH5VersionLt150() {
        return compareVersion(RequestContextHolder.getH5Version(), XiaoTianVersionEnum.V1_5_0.getVersion()) < 0;
    }

    /**
     * h5Version < 小天1.5.1版本
     *
     * @return boolean
     */
    public static boolean xtH5VersionLt151(String h5Version) {
        h5Version = CharSequenceUtil.isNotBlank(h5Version) ? h5Version : RequestContextHolder.getH5Version();
        return compareVersion(h5Version, XiaoTianVersionEnum.V1_5_1.getVersion()) < 0;
    }

    /**
     * h5Version < 小天1.7.0版本
     *
     * @return boolean
     */
    public static boolean xtH5VersionLt170() {
        return compareVersion(RequestContextHolder.getH5Version(), XiaoTianVersionEnum.V1_7_0.getVersion()) < 0;
    }

    /**
     * h5Version < 小天1.7.3版本
     *
     * @return boolean
     */
    public static boolean xtH5VersionLt173() {
        return compareVersion(RequestContextHolder.getH5Version(), XiaoTianVersionEnum.V1_7_3.getVersion()) < 0;
    }

    /**
     * h5Version < 小天1.8.0版本
     *
     * @return boolean
     */
    public static boolean xtH5VersionLt180() {
        return compareVersion(RequestContextHolder.getH5Version(), XiaoTianVersionEnum.V1_8_0.getVersion()) < 0;
    }

    /**
     * h5Version < 小天1.9.0版本
     *
     * @return boolean
     */
    public static boolean xtH5VersionLt190() {
        return compareVersion(RequestContextHolder.getH5Version(), XiaoTianVersionEnum.V1_9_0.getVersion()) < 0;
    }

    /**
     * h5Version < 小天1.9.1版本
     *
     * @return boolean
     */
    public static boolean xtH5VersionLt191() {
        return compareVersion(RequestContextHolder.getH5Version(), XiaoTianVersionEnum.V1_9_1.getVersion()) < 0;
    }

    /**
     * h5Version < 小天2.0.0版本
     *
     * @return boolean
     */
    public static boolean xtH5VersionLt200() {
        return compareVersion(RequestContextHolder.getH5Version(), XiaoTianVersionEnum.V2_0_0.getVersion()) < 0;
    }


    /**
     * h5Version >= 小天2.0.0版本
     *
     * @return boolean
     */
    public static boolean xtH5VersionGte200(String h5Version) {
        return compareVersion(h5Version, XiaoTianVersionEnum.V2_0_0.getVersion()) >= 0;
    }

    /**
     * h5Version < 小天2.0.2版本
     *
     * @return boolean
     */
    public static boolean xtH5VersionLt202() {
        return compareVersion(RequestContextHolder.getH5Version(), XiaoTianVersionEnum.V2_0_2.getVersion()) < 0;
    }

    /**
     * h5Version < 小天2.0.3版本
     *
     * @return boolean
     */
    public static boolean xtH5VersionLt203(String h5Version) {
        h5Version = CharSequenceUtil.isNotBlank(h5Version) ? h5Version : RequestContextHolder.getH5Version();
        return compareVersion(h5Version, XiaoTianVersionEnum.V2_0_3.getVersion()) < 0;
    }

    /**
     * h5Version >= 小天2.0.2版本
     *
     * @return boolean
     */
    public static boolean xtH5VersionGte202(String h5Version) {
        return compareVersion(h5Version, XiaoTianVersionEnum.V2_0_2.getVersion()) >= 0;
    }

    /**
     * h5Version >= 小天2.1.0版本
     *
     * @return boolean
     */
    public static boolean xtH5VersionGte210(String h5Version) {
        return compareVersion(h5Version, XiaoTianVersionEnum.V2_1_0.getVersion()) >= 0;
    }

    /**
     * h5Version >= 小天2.1.0版本
     *
     * @return boolean
     */
    public static boolean xtH5VersionGte210() {
        return compareVersion(RequestContextHolder.getH5Version(), XiaoTianVersionEnum.V2_1_0.getVersion()) >= 0;
    }

    /**
     * h5Version >= 小天2.1.1版本
     *
     * @return boolean
     */
    public static boolean xtH5VersionGte211(String h5Version) {
        return compareVersion(h5Version, XiaoTianVersionEnum.V2_1_1.getVersion()) >= 0;
    }

    /**
     * h5Version >= 小天2.1.1版本
     *
     * @return boolean
     */
    public static boolean xtH5VersionGte211() {
        return compareVersion(RequestContextHolder.getH5Version(), XiaoTianVersionEnum.V2_1_1.getVersion()) >= 0;
    }

    /**
     * h5Version >= 小天 max版本
     *
     * @return boolean
     */
    public static boolean xtH5VersionGteMax(String h5Version) {
        return compareVersion(h5Version, XiaoTianVersionEnum.VX_MAX.getVersion()) >= 0;
    }

    /**
     * 客户端版本 < 11.3
     * @Author: WeiJingKun
     * @return boolean
     */
    public static boolean xtClientLt113() {
        return compareVersion(RequestContextHolder.getClientVersion(), ClientVersionEnum.V11_3.getVersion()) < 0;
    }

    /**
     * 客户端版本 < 12.0.3
     * @Author: WeiJingKun
     * @return boolean
     */
    public static boolean xtClientLt1203() {
        return compareVersion(RequestContextHolder.getClientVersion(), ClientVersionEnum.V12_0_3.getVersion()) < 0;
    }

    /**
     * 客户端版本 < 12.1.0
     * @Author: WeiJingKun
     * @return boolean
     */
    public static boolean xtClientLt1210() {
        return compareVersion(RequestContextHolder.getClientVersion(), ClientVersionEnum.V12_1_0.getVersion()) < 0;
    }

    /**
     * h5Version >= 邮箱2.0.13版本
     * @Author: yinxin
     * @return boolean
     */
    public static boolean mailH5VersionGte2013() {
        return compareVersion(RequestContextHolder.getH5Version(), MailVersionEnum.V2_0_13.getVersion()) >= 0;
    }

    /**
     * h5Version < 邮箱2.0.13版本
     * @Author: yinxin
     * @return boolean 是否该版本条件
     */
    public static boolean mailH5VersionLt2013() {
        return compareVersion(RequestContextHolder.getH5Version(), MailVersionEnum.V2_0_13.getVersion()) < 0;
    }

    /**
     * h5Version < 邮箱2.0.15版本
     * @Author: WeiJingKun
     * @return boolean 是否该版本条件
     */
    public static boolean mailH5VersionLt2015() {
        return compareVersion(RequestContextHolder.getH5Version(), MailVersionEnum.V2_0_15.getVersion()) < 0;
    }

    /**
     * h5Version < 邮箱2.0.16版本
     * @Author: liuxuewen
     * @return boolean 是否该版本条件
     */
    public static boolean mailH5VersionLt2016() {
        return compareVersion(RequestContextHolder.getH5Version(), MailVersionEnum.V2_0_16.getVersion()) < 0;
    }

    /**
     * pcVersion < 邮箱(pc)1.6.0版本
     * @Author: liuxuewen
     * @return boolean 是否该版本条件
     */
    public static boolean mailPcVersionLt160() {
        return compareVersion(RequestContextHolder.getPcVersion(), MailVersionEnum.V1_6_0_PC.getVersion()) < 0;
    }


    public static boolean versionLtMax() {
        return false;
    }

    /** 云手机客户端版本号 start */
    /**
     * clientVersion < 云手机客户端版本号4.4.0
     *
     * @Author: liuxuewen
     * @return boolean 是否该版本条件
     */
    public static boolean cloudPhoneClientVersionLt440() {
        return compareVersion(RequestContextHolder.getClientVersion(), ClientVersionEnum.V_CLOUD_PHONE_4_4_0.getVersion()) < 0;
    }

    /** 云手机客户端版本号 start */
    /**
     * clientVersion >= 云手机客户端版本号4.4.0
     *
     * @Author: liuxuewen
     * @return boolean 是否该版本条件
     */
    public static boolean cloudPhoneClientVersionGte440() {
        return compareVersion(RequestContextHolder.getClientVersion(), ClientVersionEnum.V_CLOUD_PHONE_4_4_0.getVersion()) >= 0;
    }

    /**
     * clientVersion >= 云手机客户端版本号4.5.0
     *
     * @Author: liuxuewen
     * @return boolean 是否该版本条件
     */
    public static boolean cloudPhoneClientVersionGte450() {
        return compareVersion(RequestContextHolder.getClientVersion(), ClientVersionEnum.V_CLOUD_PHONE_4_5_0.getVersion()) >= 0;
    }

    /**
     * clientVersion >= 云手机客户端版本号4.6.2
     *
     * @return boolean 是否该版本条件
     * @Author: liuxuewen
     */
    public static boolean cloudPhoneClientVersionGte462() {
        return compareVersion(RequestContextHolder.getClientVersion(), ClientVersionEnum.V_CLOUD_PHONE_4_6_2.getVersion()) >= 0;
    }

    /** 云手机客户端版本号 end */

    /**
     * 获取版本号map
     *
     * @return
     */
    public static Map<String, String> getVersionMap() {
        // 从RequestContextHolder获取
        return getVersionMap(null);
    }

    /**
     * 获取版本号map
     *
     * @param extInfo
     * @return
     */
    public static Map<String, String> getVersionMap(String extInfo) {
        Map<String, String> map = new HashMap<>(Const.NUM_16);
        if (StringUtils.isNotEmpty(extInfo)) {
            try {
                JSONObject object = JSONUtil.parseObj(extInfo);
                map.put(H5_VERSION, object.getStr(H5_VERSION, ""));
                map.put(PC_VERSION, object.getStr(PC_VERSION, ""));
            } catch (Exception e) {
                Log.error("extInfo parseObj extInfo:{}, error:", extInfo, e);
            }
        } else {
            map.put(H5_VERSION, RequestContextHolder.getH5Version());
            map.put(PC_VERSION, RequestContextHolder.getPcVersion());
        }
        return map;
    }

}
