package com.zyhl.yun.api.outer.external;

import com.zyhl.yun.api.outer.domain.entity.DialogueIntentionEntity;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;

import javax.validation.Valid;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.external.DialogueIntentionExternalService} <br>
 * <b> description:</b>
 * 对话意图模型接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-03-05 14:25
 **/
public interface DialogueIntentionExternalService {

    /**
     * 对话意图方法
     *
     * @param channel 渠道
     * @param dialogueIntentionEntity the dialogue intention entity
     * @return {@link DialogueIntentionVO}
     * <AUTHOR>
     * @date 2024-3-5 14:47
     */
    DialogueIntentionVO dialogueIntentionFuc(String channel, DialogueIntentionEntity dialogueIntentionEntity);
}
