package com.zyhl.yun.api.outer.domain.vo;

import java.io.Serializable;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum;
import com.zyhl.yun.api.outer.config.ModelProperties.ModelLimitConfig;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/1/6 16:34
 */
@Data
public class ChatConfigVO implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;


	/**
     * 业务模型，详见
     *
     * @see TextModelEnum
     * 枚举值中的code编码值
     */
    private String modelType;


    /**
     * 业务模型名称
     */
    private String description;

    /**
     * 当前默认模型
     * 1: 是当前默认模型
     * 0: 不是当前默认模型
     */
    private Integer defaultMode = 0;

    /**
     * 会话最大输入长度
     */
    private Integer maxInputLength;
    
    /**
     * 排序，最小在前面
     */
    @JsonIgnore
    private Integer sort;

    /**
     * 是否收费：true-收费，false-免费
     */
    private boolean paid;
    
    /**
     * 模型是否支持联网 默认false
     */
    private boolean enableNetworkSearch = false;
    
    /**
     * 大模型结果的云盘保存路径
     */
    private String savePath;
    
    /**
     * 图标地址
     */
    private String iconUrl;

    /**
     * 是否专属模型
     */
    private boolean hasExclusive;


    public ChatConfigVO(TextModelEnum configTypeEnum, ModelLimitConfig modelLimitConfig) {
        this.modelType = configTypeEnum.getCode();
        this.maxInputLength = modelLimitConfig.getLength();
        if(StringUtils.isNotEmpty(modelLimitConfig.getName())) {
        	this.description = modelLimitConfig.getName();
        }else {
        	this.description = configTypeEnum.getName();
        }
        this.sort = modelLimitConfig.getSort();
        this.paid = modelLimitConfig.isPaid();
        this.enableNetworkSearch = modelLimitConfig.isEnableNetworkSearch();
        this.iconUrl = modelLimitConfig.getIconUrl();
        this.savePath = modelLimitConfig.getSavePath();
        this.hasExclusive = modelLimitConfig.isHasExclusive();
    }
    
    public ChatConfigVO(TextModelEnum configTypeEnum, Integer maxInputLength) {
        this.modelType = configTypeEnum.getCode();
        this.description = configTypeEnum.getName();
        this.maxInputLength = maxInputLength;
    }

    public ChatConfigVO setDefMode() {
        this.defaultMode = 1;
        return this;
    }
}
