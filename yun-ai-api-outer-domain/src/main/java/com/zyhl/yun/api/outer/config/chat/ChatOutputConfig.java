package com.zyhl.yun.api.outer.config.chat;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import lombok.Data;

import java.util.Map;

/**
 * 描述
 *
 * <AUTHOR> zhumaoxian  2025/6/25 13:25
 */
@Data
public class ChatOutputConfig {


    /**
     * 回答声明
     */
    private String responseStatement = "以上内容由{model_name}模型生成，仅供参考";

    /**
     * 获取回答声明
     *
     * @param assistantEnum 助手类型
     * @param businessType  业务类型
     * @param modelCode     模型编码
     * @return 回答声明
     */
    public String getStatement(AssistantEnum assistantEnum, String businessType, String modelCode) {
        ModelProperties modelProperties = SpringUtil.getBean(ModelProperties.class);
        Map<String, ModelProperties.ModelLimitConfig> modelLimitConfigMap = modelProperties.getLimitByAssistantEnum(assistantEnum, businessType);

        // 模型名称
        String modelName = TextModelEnum.getByCode(modelCode).getName();
        if (ObjectUtil.isNotEmpty(modelLimitConfigMap)) {
            ModelProperties.ModelLimitConfig modelLimitConfig = modelLimitConfigMap.get(modelCode);
            if (ObjectUtil.isNotEmpty(modelLimitConfig)) {
                modelName = modelLimitConfig.getName();
            }
        }
        if (TextModelEnum.XCHEN.getCode().equals(modelCode)) {
            // 星尘统一使用通义
            modelName = "通义";
        }

        return responseStatement.replace("{model_name}", modelName);
    }

}
