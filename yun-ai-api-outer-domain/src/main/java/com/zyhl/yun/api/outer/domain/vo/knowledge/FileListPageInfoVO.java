package com.zyhl.yun.api.outer.domain.vo.knowledge;

import com.github.pagehelper.PageInfo;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.vo.common.BasePageInfoVO;
import lombok.Data;

import java.util.List;

/**
 * 知识库文档列表分页信息
 *
 * <AUTHOR>
 */
@Data
public class FileListPageInfoVO extends BasePageInfoVO {

    /**
     * 知识库文件列表
     */
    private List<PersonalKnowledgeFileVO> fileList;

    /**
     * 待处理文件数，首次请求返回
     */
    private Integer pendingCount;

    /**
     * 成功处理文件数，首次请求返回
     */
    private Integer successCount;

    public FileListPageInfoVO setData(PageInfoDTO pageDTO, PageInfo<?> pageVO, List<PersonalKnowledgeFileVO> fileList) {
        this.fileList = fileList;

        if (pageDTO.isNeedTotal()) {
            // 需要总数才有分页
            this.totalCount = pageVO.getTotal();

            // 判断是否有下一页
            int nextPageCursor = Integer.valueOf(pageDTO.getPageCursor()) + pageDTO.getPageSize();
            if (nextPageCursor < pageVO.getTotal()) {
                this.nextPageCursor = String.valueOf(nextPageCursor);
            }
        }

        return this;
    }
}
