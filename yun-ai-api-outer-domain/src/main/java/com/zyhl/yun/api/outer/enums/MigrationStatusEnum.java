package com.zyhl.yun.api.outer.enums;

import lombok.Getter;

/**
 * 数据迁移状态
 *
 * <AUTHOR>
 */
@Getter
public enum MigrationStatusEnum {

    /**
     * 未报名
     */
    NO_REGISTER(0, "未报名"),

    /**
     * 已报名
     */
    REGISTER(1, "已报名"),

    /**
     * 已迁移
     */
    MIGRATION(2, "已迁移"),

    /**
     * 已计算完成
     */
    FINISH(3, "已计算完成"),

    ;


    /**
     * 状态
     */
    private final int status;
    /**
     * 备注
     */
    private final String remark;

    private MigrationStatusEnum(int status, String remark) {
        this.status = status;
        this.remark = remark;
    }

}
