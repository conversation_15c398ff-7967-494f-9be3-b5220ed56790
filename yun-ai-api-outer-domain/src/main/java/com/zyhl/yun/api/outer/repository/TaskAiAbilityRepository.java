package com.zyhl.yun.api.outer.repository;

import cn.hutool.core.date.DateTime;
import com.github.pagehelper.PageInfo;
import com.zyhl.yun.api.outer.domain.entity.TaskAiAbilityEntity;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * AI输入算法任务
 *
 * <AUTHOR>
 * @since 2024/03/07 14:48
 */
public interface TaskAiAbilityRepository {

    /**
     * 根据任务id获取算法任务信息
     *
     * @param taskId 任务id
     * @return AI输入算法任务Entity
     */
    TaskAiAbilityEntity getTaskEntity(Long taskId);

    /**
     * 获取任务列表分页信息  历史记录列表 七天
     *
     * @param page           页码
     * @param pageSize       页大小
     * @param userId         用户id
     * @param command        命令
     * @param channelId      渠道id
     * @param needTotalCount 是否需要总数
     * @return AI输入算法任务Entity
     */
    PageInfo<TaskAiAbilityEntity> getTaskEntityList(Integer page, Integer pageSize, String userId,
                                                    String command, String channelId, Integer needTotalCount);

    /**
     * 根据任务idList，获取算法任务信息转map
     *
     * @param taskIdList 任务id集合
     * @return 任务数据集合
     */
    Map<Long, TaskAiAbilityEntity> getTaskEntityMap(List<Long> taskIdList);

    /**
     * 更新付费扣费状态
     *
     * @param id            id
     * @param feeType       feeType
     * @param feePaidStatus feePaidStatus
     * @param extInfo       extInfo
     * @return boolean
     */
    boolean updateFeePaidStatus(Long id, Integer feeType, Integer feePaidStatus, String extInfo);

    /**
     * 查询工具调用成功的次数
     *
     * @param taskAiAbilityEntity the task ai ability entity
     * @param beginTime           the begin time
     * @param endTime             the end time
     * @return {@link Integer}
     * <AUTHOR>
     * @date 2024-8-16 19:09
     */
    Integer countChannelUse(TaskAiAbilityEntity taskAiAbilityEntity, Date beginTime, Date endTime);

    /**
     * 计算工具调用次数
     *
     * @param taskAiAbilityEntity the task ai ability entity
     * @param list                the list
     * @param beginOfDay          the begin of day
     * @param current             the current
     * @return {@link List<TaskAiAbilityEntity>}
     * <AUTHOR>
     * @date 2024-8-17 12:32
     */
    List<TaskAiAbilityEntity> countChannelUseList(TaskAiAbilityEntity taskAiAbilityEntity, List<Integer> list, DateTime beginOfDay, Date current);

	/**
	 * 获取图书快速阅读列表
	 * 
	 * @param taskAiAbilityEntity 实体
	 * @param pageCursor          分页索引
	 * @param pageSize            分页数
	 * @param needTotalCount      是否需要总数
	 * @return 图书快速阅读列表
	 */
    PageInfo<TaskAiAbilityEntity> getFastReadTaskList(TaskAiAbilityEntity taskAiAbilityEntity, Integer pageCursor, Integer pageSize, Integer needTotalCount);

	/**
	 * 图书快速阅读任务删除
	 * 
	 * @param taskAiAbilityEntity 任务实体
	 * @return 是否删除
	 */
    Boolean readTaskDelete(TaskAiAbilityEntity taskAiAbilityEntity);

	/**
	 * 获取多任务
	 * 
	 * @param taskId 任务id
	 * @return {@link List<TaskAiAbilityEntity>}
	 * <AUTHOR>
	 * @date 2025-4-19 12:40
	 */
    List<TaskAiAbilityEntity> getMultipleTaskById(Long taskId);

    /**
     * 根据任务id获取算法任务信息
     *
     * @param taskId 任务id
     * @return AI输入算法任务Entity
     */
    TaskAiAbilityEntity getTaskEntityForSpeedRead(Long taskId);
}
