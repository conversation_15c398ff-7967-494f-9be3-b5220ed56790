package com.zyhl.yun.api.outer.domainservice.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.corpus.tag.Nature;
import com.hankcs.hanlp.seg.common.Term;
import com.zyhl.hcy.yun.ai.common.base.annotation.MethodExecutionTimeLog;
import com.zyhl.hcy.yun.ai.common.intention.client.executepre.util.AhoCorasick;
import com.zyhl.hcy.yun.ai.common.intention.client.intentutil.IntentionUtilClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.vo.TextModelBaseVo;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.yun.api.outer.config.ReturnTermsTemplateProperties;
import com.zyhl.yun.api.outer.constants.FutureConstants;
import com.zyhl.yun.api.outer.domain.req.LlmChatReqDTO;
import com.zyhl.yun.api.outer.domain.valueobject.llm.LlmChatMessage;
import com.zyhl.yun.api.outer.domain.vo.SearchReturnTermsVO;
import com.zyhl.yun.api.outer.domain.vo.common.AhoCorasickResult;
import com.zyhl.yun.api.outer.domainservice.CheckSystemDomainService;
import com.zyhl.yun.api.outer.domainservice.SearchReturnTermsService;
import com.zyhl.yun.api.outer.enums.AcceptLanguageEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.external.LlmChatExternalService;
import com.zyhl.yun.api.outer.util.EntityListUtils;
import com.zyhl.yun.api.outer.util.JsonHandleUtils;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.RequestContextHolder.UserInfo;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * 搜索返回词方法接口实现类
 *
 * <AUTHOR>
 * @date 2024/10/14
 */
@Slf4j
@Service
public class SearchReturnTermsServiceImpl implements SearchReturnTermsService {

	@Resource
	private ReturnTermsTemplateProperties returnTermsTemplateProperties;

	@Resource
	private LlmChatExternalService llmChatExternalService;

	@Resource
	private CheckSystemDomainService checkSystemDomainService;

	@Resource
	private IntentionUtilClient intentionUtilClient;

	@Resource(name = "searchReturnTermsThreadPool")
	private ExecutorService searchReturnTermsThreadPool;

	@MethodExecutionTimeLog(value = "生成搜索返回词V1", printParam = true, printResult = true)
	@Override
	public Future<String> getOptimizeReturnTermsFutureV1(Long dialogueId, String dialogue) {
		if (CharSequenceUtil.isBlank(dialogue)) {
			log.info("生成搜索返回词V1，对话内容为null，不调用大模型");
			return null;
		}
		/** 参数初始化 */
		ReturnTermsTemplateProperties.IntentionTemplate intentionTemplate = returnTermsTemplateProperties.getIntentionTemplate();
		if (ObjectUtil.isNull(intentionTemplate)) {
			log.info("生成搜索返回词V1，意图模板配置为null，不调用大模型");
			return null;
		}
		// 返回系统默认提示词为true，使用默认值
		if (Boolean.TRUE.equals(returnTermsTemplateProperties.getReturnSystemDefault())) {
			log.info("生成搜索返回词V1，返回系统默认提示词为true，不调用大模型");
			return null;
		}
		final String userId = RequestContextHolder.getUserId();
		final UserInfo userInfo = RequestContextHolder.getUserInfo();
		try {
			return CompletableFuture.supplyAsync(() -> {
				RequestContextHolder.setUserInfo(userInfo);
				/** 调用大模型获取提示词 */
				// 构建大模型提示词模板
				String unifiedTemplate = intentionTemplate.getUnifiedTemplate();
				String userMsg = String.format(unifiedTemplate, dialogue);
				// 构建大模型消息请求
				List<LlmChatMessage> messages = new ArrayList<>();
				messages.add(new LlmChatMessage(TextModelRoleEnum.USER.getName(), userMsg));
				LlmChatReqDTO chatDTO = new LlmChatReqDTO(userId, intentionTemplate.getModelCode(), messages);
				TextModelBaseVo llmChatResult = llmChatExternalService.chatNormal(chatDTO);
				if (!(null != llmChatResult && llmChatResult.isSuccess())) {
					log.info("生成搜索返回词V1，大模型结果异常，chatNormal：{}", llmChatResult);
					return null;
				}

				/** 对大模型返回的文字进行审核 */
				String text = llmChatResult.getText();
				try {
					checkSystemDomainService.checkLocalAndPlatformException(dialogueId, userId, text);
				} catch (Exception e) {
					log.error("生成搜索返回词V1，送审失败，text：{}", text, e);
					return null;
				}

				/** 解析并构建返回词对象 */
				return JsonHandleUtils.formatJsonStr(text);
			}, searchReturnTermsThreadPool);
		} catch (Exception e) {
			log.error("生成搜索返回词V1，异常", e);
			return null;
		}
	}

	@MethodExecutionTimeLog(value = "获取搜索返回词V1", printParam = true, printResult = true)
	@Override
	public String getSearchReturnTermsV1(Future<String> searchReturnTermsFutureV1, String intention, Long dialogueId, String dialogue) {
		/** 参数初始化 */
		String text = null;

		/** 构建搜索返回词 */
		ReturnTermsTemplateProperties.TemplateConfig templateConfig = returnTermsTemplateProperties.getTemplateConfig(intention);
		// templateConfig为null，无搜索返回词
		if (ObjectUtil.isNull(templateConfig)) {
			log.info("获取搜索返回词V1，templateConfig为null，无搜索返回词");
			return null;
		}
		// 当前请求接受的语言类型
		boolean enUs = AcceptLanguageEnum.EN_US.getCode().equalsIgnoreCase(RequestContextHolder.getAcceptLanguage());
		if(enUs){
			return templateConfig.getDefaultTextEn();
		}
		// 返回系统默认提示词为true，使用默认值
		if (Boolean.TRUE.equals(returnTermsTemplateProperties.getReturnSystemDefault())) {
			log.info("获取搜索返回词V1，返回系统默认提示词为true，使用默认值");
			return templateConfig.getDefaultText();
		}
		// 是过滤意图，使用默认值
		if(returnTermsTemplateProperties.getExcludeIntentionList().contains(intention)){
			log.info("获取搜索返回词V1，是过滤意图");
			return templateConfig.getDefaultText();
		}

		try {
			// 获取大模型结果
			String modelResult = null;
			if (ObjectUtil.isNotNull(searchReturnTermsFutureV1)) {
				modelResult = searchReturnTermsFutureV1.get(FutureConstants.SIXTY_SECONDS, TimeUnit.SECONDS);
			}
			// 大模型结果获取不到，使用默认值
			if(CharSequenceUtil.isBlank(modelResult)){
				log.info("获取搜索返回词V1，大模型结果获取不到");
				return templateConfig.getDefaultText();
			}

			/** 处理大模型结果 */
			// 返回的数据有可能是数组，需要格式化
			List<String> formatList = formatList(modelResult);
			if(CollUtil.isEmpty(formatList)) {
				log.info("获取搜索返回词V1，大模型结果格式化后无数据");
				return templateConfig.getDefaultText();
			}
			// 根据意图过滤关键词，并拼接
			StringBuilder termsBuilder = new StringBuilder();
			for(String str : formatList) {
				// 过滤关键词
				if(Boolean.TRUE.equals(templateConfig.getFilterFlag())) {
					for (String keyWords : returnTermsTemplateProperties.getExcludeKeywords()) {
						if(str.contains(keyWords)){
							str = str.replace(keyWords, "");
						}
					}
				}
				// 多个关键词直接拼一起
				if(StringUtils.isNotBlank(str)){
					termsBuilder.append(str);
				}
			}
			// 大模型结果解析过滤后有返回词：使用搜索返回词模板拼接
			text = termsBuilder.toString();
			if(CharSequenceUtil.isNotBlank(text)){
				text = String.format(templateConfig.getTemplate(), text);
			} else {
				text = templateConfig.getDefaultText();
			}
		} catch (Exception e) {
			log.error("获取搜索返回词V1，异常", e);
			// 异常，使用默认值
			text = templateConfig.getDefaultText();
		}
		return text;
	}

	/**
	 * 格式化文本，返回的数据有可能是数组，需要格式化一下
	 *
	 * @param text 返回词对象json字符串
	 * @return 多个关键词
	 */
	private List<String> formatList(String text) {
		if (CharSequenceUtil.isEmpty(text)) {
			return null;
		}
		if (text.indexOf(StrPool.BRACKET_START) == 0) {
			try {
				return JSON.parseArray(text, String.class);
			} catch (Exception e) {
				log.error("字符串格式化失败：{}, error：", text, e);
			}
		} else {
			return Collections.singletonList(text);
		}
		return null;
	}

	@MethodExecutionTimeLog(value = "生成搜索返回词V2", printParam = true, printResult = true)
	@Override
	public Future<SearchReturnTermsVO> getOptimizeReturnTermsFutureV2(String intention, String dialogueId, String dialogue, DialogueIntentionVO.IntentionInfo firstIntentionInfo) {
		if (StringUtils.isBlank(dialogue)) {
			return null;
		}
		/** 根据意图拼接返回词 */
		try {
			return CompletableFuture.supplyAsync(() -> {
				/** 根据意图获取返回词模板配置 */
				ReturnTermsTemplateProperties.TemplateConfig templateConfig = returnTermsTemplateProperties.getTemplateConfig(intention);
				if(ObjectUtil.isNull(templateConfig)) {
					log.info("生成搜索返回词V2，templateConfig为null");
					return null;
				}

				/** 构建搜索返回词VO */
				SearchReturnTermsVO searchReturnTermsVO = SearchReturnTermsVO.builder().intention(intention).build();
				// 返回默认值
				if (Boolean.TRUE.equals(returnTermsTemplateProperties.getReturnSystemDefault())) {
					log.info("生成搜索返回词V2，returnSystemDefault=true，返回默认值");
					searchReturnTermsVO.setText(templateConfig.getDefaultText());
					return searchReturnTermsVO;
				}
				// 搜活动、搜功能，返回默认值
				if (DialogueIntentionEnum.SEARCH_ACTIVITY.getCode().equals(intention) || DialogueIntentionEnum.SEARCH_FUNCTION.getCode().equals(intention)) {
					log.info("生成搜索返回词V2，搜活动、搜功能，返回默认值");
					searchReturnTermsVO.setText(templateConfig.getDefaultText());
					return searchReturnTermsVO;
				}

				/** 其它意图处理 */
				return otherIntentionHandle(dialogue, firstIntentionInfo, searchReturnTermsVO, templateConfig);
			}, searchReturnTermsThreadPool);
		} catch (Exception e) {
			log.error("生成搜索返回词V2，异常", e);
			return null;
		}
	}

	/**
	 * 生成搜索返回词V2-其它意图处理
	 * @Author: WeiJingKun
	 * @param dialogue 对话内容
	 * @param firstIntentionInfo 第一个意图结果
	 * @param searchReturnTermsVO 搜索返回词的Future
	 * @param templateConfig 返回词模板配置
	 * @return 搜索返回词的Future
	 */
	private SearchReturnTermsVO otherIntentionHandle(String dialogue, DialogueIntentionVO.IntentionInfo firstIntentionInfo, SearchReturnTermsVO searchReturnTermsVO, ReturnTermsTemplateProperties.TemplateConfig templateConfig) {
		/** 获取意图识别返回的所有关键字 */
		List<String> keywordList = EntityListUtils.getAllKeywordByEntityList(firstIntentionInfo);
		if(CollUtil.isEmpty(keywordList)){
			log.info("生成搜索返回词V2-其它意图处理，keywordList为空，返回默认值");
			searchReturnTermsVO.setText(templateConfig.getDefaultText());
			return searchReturnTermsVO;
		}
		log.info("生成搜索返回词V2-其它意图处理，keywordList：{}", JSON.toJSONString(keywordList));

		/** 根据AC自动机，获取对话内容的下标 */
		List<int[]> searchIndexList = new ArrayList<>();
		for(String keyword : keywordList){
			// 根据AC自动机，获取对话内容的下标
			AhoCorasick ahoCorasick = intentionUtilClient.getAhoCorasick(CollUtil.toList(keyword));
			if (ObjectUtil.isNotNull(ahoCorasick)) {
				// int[]的值：[0]匹配的下标，[1]匹配的长度
				List<int[]> resultList = ahoCorasick.search(dialogue);
				if (CollUtil.isNotEmpty(resultList)) {
					// 取匹配的最后一个
					searchIndexList.add(resultList.get(resultList.size()-1));
				}
			}
		}

		if (CollUtil.isEmpty(searchIndexList)) {
			log.info("生成搜索返回词V2-其它意图处理，searchIndexList为空，返回默认值");
			searchReturnTermsVO.setText(templateConfig.getDefaultText());
			return searchReturnTermsVO;
		}
		// 构建AC自动机结果列表，并根据index字段正序排序
		List<AhoCorasickResult> ahoCorasickResultList = AhoCorasickResult.createAhoCorasickResultList(searchIndexList);
		if (CollUtil.isEmpty(ahoCorasickResultList)) {
			log.info("生成搜索返回词V2-其它意图处理，ahoCorasickResultList为空，返回默认值");
			searchReturnTermsVO.setText(templateConfig.getDefaultText());
			return searchReturnTermsVO;
		}
		log.info("生成搜索返回词V2-其它意图处理，searchIndexList：{}，ahoCorasickResultList：{}",
				JSON.toJSONString(searchIndexList), JSON.toJSONString(ahoCorasickResultList));

		/** 根据下标截取字符串 */
		int lastIndex = ahoCorasickResultList.size() - 1;
		// 结果int[]的值：[0]匹配的下标，[1]匹配的长度
		String subStr = CharSequenceUtil.sub(dialogue, ahoCorasickResultList.get(0).getIndex(), ahoCorasickResultList.get(lastIndex).getIndex()+ahoCorasickResultList.get(lastIndex).getLength());
		// 替换配置模板中的占位符，获取完整返回词
		String replaceResult = templateConfig.getDefaultText();
		if(CharSequenceUtil.isNotBlank(subStr)){
			// 使用HanLP进行分词
			subStr = wordSegment(subStr, keywordList);
			replaceResult = String.format(templateConfig.getTemplate(), subStr);
		}
		log.info("生成搜索返回词V2-其它意图处理，原文：{}，截取：{}，替换后：{}", dialogue, subStr, replaceResult);
		searchReturnTermsVO.setText(replaceResult);

		return searchReturnTermsVO;
	}

	/**
	 * 使用HanLP进行分词
	 * @Author: WeiJingKun
	 * @param replaceResult 返回词
	 * @param keywordList 意图识别返回的所有关键字
	 * @return void
	 */
	private String wordSegment(String replaceResult, List<String> keywordList) {
		// 使用HanLP进行分词
		List<Term> termList = HanLP.segment(replaceResult);
		StringBuilder simple = new StringBuilder();
		for (Term term : termList) {
			boolean match = keywordList.stream().anyMatch(key -> key.contains(term.word));
			if (match) {
				simple.append(term.word);
			} else if (term.nature == Nature.v) {
				simple.append(term.word);
			}
		}
		return simple.toString();
	}

	@MethodExecutionTimeLog(value = "获取搜索返回词V2", printParam = true, printResult = true)
	@Override
	public String getSearchReturnTermsV2(Future<SearchReturnTermsVO> searchReturnTermsFutureV2, String intention, String dialogueId, String dialogue) {
		String title = null;
		try {
			/** 获取搜索返回词 */
			if(null != searchReturnTermsFutureV2){
				SearchReturnTermsVO searchReturnTermsVO = searchReturnTermsFutureV2.get(FutureConstants.SIXTY_SECONDS, TimeUnit.SECONDS);
				if(ObjectUtil.isNotNull(searchReturnTermsVO)){
					title = searchReturnTermsVO.getText();
				}
			}
		} catch (Exception e) {
			log.error("获取搜索返回词V2，异常", e);
		}

		/** 搜索返回词获取不到，获取默认值 */
		if(CharSequenceUtil.isBlank(title)){
			ReturnTermsTemplateProperties.TemplateConfig templateConfig = returnTermsTemplateProperties.getTemplateConfig(intention);
			if(ObjectUtil.isNotNull(templateConfig)){
				title = templateConfig.getDefaultText();
			}
		}
		return title;
	}

}