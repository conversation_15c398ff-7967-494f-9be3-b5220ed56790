package com.zyhl.yun.api.outer.domainservice;

import com.zyhl.yun.api.outer.domain.dto.ExternalResourceInfoDTO;

/**
 * 资源信息域服务
 *
 * <AUTHOR>
 */
public interface ResourceInfoDomainService {

    /**
     * 邮件信息
     *
     * @param phone      手机号码
     * @param resourceId 资源id
     * @return dto
     */
    ExternalResourceInfoDTO getMailInfo(String phone, String resourceId);

    /**
     * 笔记信息
     *
     * @param userId     用户id
     * @param resourceId 资源id
     * @return dto
     */
    ExternalResourceInfoDTO getNoteInfo(String userId, String resourceId);

	/**
	 * 图片信息
	 *
	 * @param userId          userId
	 * @param belongsPlatform 归属平台
	 * @param resourceId      资源id
	 * @return dto
	 */
    ExternalResourceInfoDTO getImgInfo(String userId, Integer belongsPlatform, String resourceId);

	/**
	 * 获取邮件内容
	 *
	 * @param sid   sid
	 * @param rmkey rmkey
	 * @return 邮件内容，纯文本
	 */
	String getMailContent(String sid, String rmkey, String resourceId);

    /**
     * 获取笔记内容
     *
     * @param token      token
     * @param resourceId 邮件id
     * @return 笔记内容，纯文本
     */
    String getNoteContent(String token, String resourceId);
}
