package com.zyhl.yun.api.outer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 意图类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum IntentionTypeEnum {

	/**
	 * 文本类型
	 */
	TEXT("text", "文本类型"),

	/**
	 * 图片类型
	 */
	IMAGE("image", "图片类型"),

	/**
	 * 搜索类型
	 */
	SEARCH("search", "搜索类型"),

	/**
	 * 云手机类型
	 */
	CLOUD_PHONE("cloud_phone", "云手机类型"),

	/**
	 * 其他类型
	 */
	OTHER("other", "其他类型"),;

	/**
	 * 类型
	 */
	private final String type;
	/**
	 * 备注
	 */
	private final String remark;

	public static boolean isText(IntentionTypeEnum type) {
		if (null == type) {
			return false;
		}
		return TEXT.getType().equals(type.getType());
	}

	public static boolean isImage(IntentionTypeEnum type) {
		if (null == type) {
			return false;
		}
		return IMAGE.getType().equals(type.getType());
	}

	public static boolean isSearch(IntentionTypeEnum type) {
		if (null == type) {
			return false;
		}
		return SEARCH.getType().equals(type.getType());
	}

	public static boolean isCloudPhone(IntentionTypeEnum type) {
		if (null == type) {
			return false;
		}
		return CLOUD_PHONE.getType().equals(type.getType());
	}

	public static boolean isOther(IntentionTypeEnum type) {
		if (null == type) {
			return false;
		}
		return OTHER.getType().equals(type.getType());
	}
}
