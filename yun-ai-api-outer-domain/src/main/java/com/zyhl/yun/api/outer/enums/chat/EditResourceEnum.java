package com.zyhl.yun.api.outer.enums.chat;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public enum EditResourceEnum {
    
    /**
     * 1-输入
     */
    IN(1, "输入"),
    
    /**
     * 输出
     */
    OUT(2, "输出"),
    
    /**
     * 输入输出
     */
    ALL(3, "输入输出"),

    ;

    private static final Map<Integer, EditResourceEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(EditResourceEnum.class).forEach(item -> MAP.put(item.code, item));
    }

    public static EditResourceEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return MAP.get(code);
    }

    /**
     * 是否存在
     * @param code 编码
     * @return true-存在
     */
    public static boolean isExist(Integer code) {
        return getByCode(code) != null;
    }


    EditResourceEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
