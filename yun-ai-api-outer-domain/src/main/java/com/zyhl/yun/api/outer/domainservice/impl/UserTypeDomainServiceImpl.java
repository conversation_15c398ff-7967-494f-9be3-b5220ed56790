package com.zyhl.yun.api.outer.domainservice.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.config.WhiteListProperties;
import com.zyhl.yun.api.outer.domainservice.UserTypeDomainService;
import com.zyhl.yun.api.outer.enums.UserTypeEnum;
import com.zyhl.yun.api.outer.external.MemberCenterExternalService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户类型服务接口
 *
 * <AUTHOR>
 * @date 2025/2/19 09:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserTypeDomainServiceImpl implements UserTypeDomainService {

	@Resource
	private WhiteListProperties whiteListProperties;
	@Resource
	private ModelProperties modelProperties;

	@Resource
	private MemberCenterExternalService memberCenterExternalService;

	@Override
	public UserTypeEnum getUserType(String userId, String phone, List<String> benefitNos) {
		try {
			if (whiteListProperties.getTextModelCodeWhiteUser().contains(phone)) {
				log.info("用户为白名单用户 getUserType userId:{}", userId);
				return UserTypeEnum.WHITE_USER;
			}
			if (Boolean.TRUE.equals(modelProperties.getQueryModelMemberCenterOpen())) {
				if (Boolean.TRUE.equals(memberCenterExternalService.isMember(phone, benefitNos))) {
					log.info("用户为会员用户 getUserType userId:{}", userId);
					return UserTypeEnum.MEMBER;
				}
			} else {
				log.info("用户为会员查询关闭，使用默认模型 getUserType userId:{}", userId);
				return UserTypeEnum.DEF_MODEL;
			}
		} catch (Exception e) {
			log.error("用户类型获取异常返回默认，使用默认模型，getUserType userId:{}, error:", userId, e);
			return UserTypeEnum.DEF_MODEL;
		}
		log.info("用户为非会员用户 getUserType userId:{}", userId);
		return UserTypeEnum.NOT_MEMBER;
	}

}
