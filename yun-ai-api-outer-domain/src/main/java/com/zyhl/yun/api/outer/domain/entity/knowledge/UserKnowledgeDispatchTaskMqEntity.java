package com.zyhl.yun.api.outer.domain.entity.knowledge;

import lombok.Data;

/**
 * className: UserKnowledgeMailAndNoteDispatchEntity
 * description: 个人知识库 向量化提取任务实体类
 *
 * <AUTHOR>
 * @date 2025/2/12
 */
@Data
public class UserKnowledgeDispatchTaskMqEntity {

    /**
     * 用户资源表主键 ID
     */
    private String id;

    /**
     * 任务类型。
     * 可选值：
     * 1. 图片元数据分析 -- 默认
     * 2. 人脸聚类
     * 3. 相似度聚类
     * 4. 文档正文索引
     * 5. 文档向量化索引(个人知识库)
     *
     * @see com.zyhl.yun.api.outer.enums.mq.MetaTaskTypeEnum
     */
    private Integer metaTaskType;

    /**
     * 邮件/笔记 ID
     */
    private String fileId;

    /**
     * 用户 ID
     */
    private String userId;

    /**
     * 所属 ID
     */
    private String ownerId;

    /**
     * 所属类型
     */
    private Integer ownerType;

    /**
     * 知识库ID
     */
    private  Long baseId;

    /**
     * 批量类型。
     * 可选值：
     * 1. 单个
     * 2. 批量
     */
    private Integer taskType;

    /**
     * 哈希名称
     */
    private String hashName;

    /**
     * 哈希值
     */
    private String hashValue;

    /**
     * 文件类型，1 表示文件，2 表示目录
     */
    private Integer fileType;

    /**
     * 文件/目录分类，见字典定义。
     * 可选值：
     * 1 图片
     * 2 音频
     * 3 视频
     * 4 文档
     * 5 应用
     * 6 压缩文件
     * 0 其他
     * 100 普通目录
     */
    private Integer category;

    /**
     * 空间 ID
     */
    private String driveId;

    /**
     * paas 平台编码
     */
    private String paasCode;

    /**
     * paas 集群区域
     */
    private String areaCode;

    /**
     * 算法组编码
     */
    private String algorithmGroupCode;

    /**
     * 是否回刷 hbase rowkey，默认 false 表示否，true 表示是
     */
    private boolean refresh;

    /**
     * 扩展名，可选字段
     */
    private String extension;

    /**
     * 资源类型
     * 枚举类型：
     * 0 - 个人云文档（缺省时默认）
     * 1 - 邮件
     * 2 - 笔记
     */
    private Integer resourceType = 0;
}
