package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.AIToolsConsumeRecordEntity;

import java.util.List;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.repository.AIToolsConsumeRecordRepository} <br>
 * <b> description:</b>
 * 算法任务权益核销记录仓储层接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-08-16 18:27
 **/
public interface AIToolsConsumeRecordRepository {

    /**
     * 获取今日记录
     *
     * @param entity the entity
     * @return {@link List<AIToolsConsumeRecordEntity>}
     * <AUTHOR>
     * @date 2024-8-16 18:29
     */
    List<AIToolsConsumeRecordEntity> selectTodayListByChannelId(AIToolsConsumeRecordEntity entity);

    /**
     * 保存记录
     *
     * @param consumeRecord the consume record
     * @return {@link AIToolsConsumeRecordEntity}
     * <AUTHOR>
     * @date 2024-8-16 19:34
     */
    AIToolsConsumeRecordEntity save(AIToolsConsumeRecordEntity consumeRecord);

    /**
     * 查询今日核销次数
     *
     * @param condition the condition
     * @return {@link Integer}
     * <AUTHOR>
     * @date 2024-8-17 12:02
     */
    Integer getTodayConsumeTimes(AIToolsConsumeRecordEntity condition);
}
