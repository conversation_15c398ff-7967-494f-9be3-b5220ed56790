package com.zyhl.yun.api.outer.domain.valueobject.llm;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文本大模型输入配置参数
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LlmChatMessage {

	/**
	 * 消息的角色，TextModelRoleEnum 枚举值为 "user" 或 "assistant" 或 "system"
	 */
	private String role;
	/**
	 * 消息内容
	 */
	private String content;

	private String name;

	public LlmChatMessage(String role, String content) {
		this.role = role;
		this.content = content;
	}

}
