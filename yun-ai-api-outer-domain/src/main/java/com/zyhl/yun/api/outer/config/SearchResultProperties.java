package com.zyhl.yun.api.outer.config;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.yun.api.outer.enums.chat.search.SearchTypeEnum;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 搜索结果配置
 */
@Slf4j
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "search-result-properties")
public class SearchResultProperties {

    /** tab展示顺序 */
    private Map<String, TabSort> tabSortMap;

    /** 搜索结果推荐配置 */
    private Map<String, Recommend> recommendMap;

    /**
     * 排序开关 true表示以配置文件的排序为主 false表示以算法的排序为主
     */
    private boolean enableSort = true;

    /**
     * 搜索结果推荐
     */
    @Data
    public static class Recommend {

        /** 类别 */
        private String type;

        /** 意图推荐模板 */
        private String intentionTemplate;

    }

    /**
     * 获取搜索结果推荐配置
     */
    public Recommend getRecommend(String searchType) {
        return null == recommendMap ? null : recommendMap.get(searchType);
    }

    /**
     * tab展示顺序
     */
    @Data
    public static class TabSort {

        /**
         * 类别
         */
        private String type;

        /**
         * 名称
         */
        private String name;

        /**
         * 英文名称
         */
        private String nameEn;

        /**
         * 默认排序
         */
        private Integer defaultSort;

        /**
         * 命中排序
         */
        private Integer matchSort;

        /**
         * 关键字集
         */
        private String keywords;

        /**
         * 获取tab的实际排序值
         * 1、命中关键字，返回命中排序值；
         * 2、未命中关键字，返回默认排序值；
         */
        public Integer getRealSort(String dialogue) {
            if (CharSequenceUtil.isNotBlank(dialogue) && CharSequenceUtil.isNotBlank(keywords)){
                String[] keywordArr = keywords.split("、");
                for(String keyword : keywordArr){
                    if(dialogue.contains(keyword)){
                        // 命中关键字，返回命中排序值
                        return matchSort;
                    }
                }
            }
            // 未命中关键字，返回默认排序值
            return defaultSort;
        }

    }

    /**
     * 获取tab排序对象
     */
    public TabSort getTabSort(String searchType) {
        return null == tabSortMap ? null : tabSortMap.get(searchType);
    }

    /**
     * 获取tab排序对象
     */
    public TabSort getTabSort(Class<?> searchParamClass) {
        if(null != tabSortMap){
            SearchTypeEnum searchTypeEnum = SearchTypeEnum.getByKey(searchParamClass);
            if(null != searchTypeEnum){
                return tabSortMap.get(searchTypeEnum.getSearchType());
            } else {
                log.warn("搜索类别枚举-SearchTypeEnum为空！");
            }
        } else {
            log.warn("搜索结果配置-search-result-properties为空！");
        }
        return null;
    }

}
