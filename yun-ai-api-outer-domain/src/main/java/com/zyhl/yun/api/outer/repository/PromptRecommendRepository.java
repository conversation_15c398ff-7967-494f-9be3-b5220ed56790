package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.vo.PromptRecommendVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-17 10:17
 * @description 调取db查询提示词列表数据
 **/
public interface PromptRecommendRepository {

    /**
     * 调用db查询提示词数据
     * @return 提示词数据集合
     */
    List<PromptRecommendVO> listPromptRecommendList(String businessType);
}

