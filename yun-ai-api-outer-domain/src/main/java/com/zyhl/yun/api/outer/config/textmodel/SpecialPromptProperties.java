package com.zyhl.yun.api.outer.config.textmodel;

import cn.hutool.core.util.ObjectUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/23 16:32
 * @Desc 知识库特殊prompt配置，针对账号维度
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "special-prompt")
public class SpecialPromptProperties {

    /**
     * 专属提示词账号集合
     */
    private List<String> accountList;

    /**
     * 专属提示词
     */
    private List<PromptConfig> promptTextList = new ArrayList<>();

    /**
     * 提示词配置
     */
    @Data
    public static class PromptConfig {
        /**
         * 提示词key
         */
        private List<String> promptKeys = new ArrayList<>();
        /**
         * 模型code
         */
        private List<String> modelCodes = new ArrayList<>();
        /**
         * 提示词
         */
        private String prompt;
    }

    /**
     * 获取提示词
     *
     * @param promptKey 提示词key
     * @param modelCode 模型code
     * @return 提示词
     */
    public String getPrompt(String promptKey, String modelCode) {

        if (ObjectUtil.isEmpty(promptTextList) || ObjectUtil.isEmpty(promptKey) || ObjectUtil.isEmpty(modelCode)) {
            return "";
        }

        for (PromptConfig config : promptTextList) {
            if (config.promptKeys.contains(promptKey) && config.modelCodes.contains(modelCode)) {
                return config.prompt;
            }
        }

        return "";
    }
}
