package com.zyhl.yun.api.outer.domain.req;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 智能搜索查询参数
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntelligentSearchQueryEntity {


    /**
     * 请求ID。
     * 该字段存储请求的唯一标识符。
     */
    private String requestId;

    /**
     * 用户ID。
     * 该字段存储请求的用户唯一标识符。
     */
    private String userId;

    /**
     * 搜索文本。
     * 该字段存储用户输入的搜索文本。
     */
    private String query;

    /**
     * 阈值。
     * 该字段存储用于过滤结果的阈值，若传入，则只返回评分大于该阈值的数据。
     */
    private Double threshold;


}
