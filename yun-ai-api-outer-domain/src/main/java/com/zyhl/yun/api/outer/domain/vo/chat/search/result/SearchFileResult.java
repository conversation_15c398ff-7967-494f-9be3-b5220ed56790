package com.zyhl.yun.api.outer.domain.vo.chat.search.result;


import com.zyhl.yun.api.outer.domain.valueobject.File;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 对话信息-搜索结果-文件
 * @Author: WeiJingKun
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchFileResult extends SearchCommonResult implements Serializable {

    /** 文件信息列表 */
    private List<File> fileList;

    /** 查询总数 */
    private Integer totalCount;

    /** 下一页游标 */
    private List<Object> pageAfter;

}
