package com.zyhl.yun.api.outer.enums.task;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * AI输入算法任务-扣费标识
 * @Author: WeiJingKun
 */
public enum TaskFeePaidStatusEnum {

    /**
     * 默认 不扣费（该任务不涉及扣费流程）
     */
    NO_PAYMENT(-1, "不扣费（该任务不涉及扣费流程）"),

    /**
     * 未扣费
     */
    UNPAID(0, "未扣费"),

    /**
     * 已扣费
     */
    PAID(1, "已扣费"),

    ;

    private static final Map<Integer, TaskFeePaidStatusEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(TaskFeePaidStatusEnum.class).forEach(item -> MAP.put(item.code, item));
    }

    public static TaskFeePaidStatusEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return MAP.get(code);
    }

    /**
     * 是否存在
     * @param code 编码
     * @return true-存在
     */
    public static boolean isExist(Integer code) {
        return getByCode(code) != null;
    }


    TaskFeePaidStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    
}
