package com.zyhl.yun.api.outer.vo;

import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.AddressDetail;
import com.zyhl.yun.api.outer.enums.task.TaskFeePaidStatusEnum;
import lombok.Data;

import java.util.List;

/**
 * KeyValue格式VO结构
 *
 * <AUTHOR>
 * @date 2024/4/16 10:08
 */
@Data
public class AsyncTaskResult {


    /**
     * 算法编码
     */
    private String algorithmCode;

    /**
     * SupplierType供应商类型
     */
    private String supplierType;

    /**
     * 结果类型：
     * 1—图片
     * 2—文本
     * 3—音频
     * 4—视频
     */
    private Integer resultType = 1;

    /**
     * 结果类型：
     * 图片类：
     * 1—url下载结果
     * 2—base64下载结果
     * 3—文件信息下载结果
     * 文本类：
     * 音频类：
     * 视频类：
     */
    private Integer subResultType = 1;

    /**
     * 图像下载地址
     */
    private List<String> fileUrlList;

    /**
     * 图像base64
     */
    private List<String> bass64List;

    /**
     * 图配文结果
     */
    private List<ImageCaptionInfo> textList;

    /**
     * 文件信息
     */
    private List<AsyncTaskResultFileInfo> fileInfoList;

    /**
     * 付费扣费状态
     * @see TaskFeePaidStatusEnum
     */
    private Integer feePaidStatus;


    @Data
    public static class AsyncTaskResultFileInfo {

        /**
         * 主平台文件内容ID,旧底座为contentId，新底座为fileId
         */
        private String fileId;

        /**
         * 文件的md5(源文件的md5非url的md5)
         */
        private String contentHashAlgorithm;

        /**
         * 分类， 根据文件的后缀名和mime-type对文件进行分了分类，主要分类有：
         * app：安装包 ；zip：压缩包 image：图片； doc：文档 video：视频 ；audio：音频 folder：目录 ；others：其他
         */
        private String category = "image";

        /**
         * 文本为内容，其他为url
         */
        private String content;

        /**
         * 缩略图url
         */
        private String thumbnailUrl;

        /**
         * url为必选，文件名称
         */
        private String name;

        /**
         * url为必选，文件大小
         */
        private Long size;

        /**
         * url为必选，文件后缀
         */
        private String fileExtension;

        /**
         * 如果存在就返回
         */
        public AddressDetail addressDetail;

    }

    @Data
    public static class ImageCaptionInfo {

        /**
         * 配文信息
         */
        private String text;

        /**
         * 分数
         */
        private Float score;
    }
    
}
