package com.zyhl.yun.api.outer.config;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.zyhl.yun.api.outer.constants.CommonConstant;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 意图跳链配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "link")
public class LinkProperties {

    private Map<String, String> url;


    /**
     * 获取意图跳链地址
     *
     * @param sourceChannel 渠道号
     * @param intentionEnum 意图对象
     * @return
     */
    public String getIntentionUrl(String sourceChannel, DialogueIntentionEnum intentionEnum) {
        if (ObjectUtil.isEmpty(url)) {
            return "";
        }

        // 意图结果不为妙云相机 返回跳转链接配置
        if (!DialogueIntentionEnum.CLOUD_CAMERA.equals(intentionEnum)) {
            return url.get(intentionEnum.getInstruction());
        }

        // 妙云相机按照渠道来源返回
        return isInner(sourceChannel) ? url.get(CommonConstant.CLOUD_CAMERA_WITHIN) : url.get(CommonConstant.CLOUD_CAMERA_EXTERNAL);
    }

    private SourceChannelsProperties sourceChannelsProperties;

    private boolean isInner(String sourceChannel) {
        if (sourceChannelsProperties == null) {
            sourceChannelsProperties = SpringUtil.getBean(SourceChannelsProperties.class);
        }
        return sourceChannelsProperties.isInner(sourceChannel);
    }
}
