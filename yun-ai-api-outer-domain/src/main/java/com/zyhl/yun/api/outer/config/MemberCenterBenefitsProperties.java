package com.zyhl.yun.api.outer.config;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;

/**
 * 会员中心权益项配置
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "member-center")
@Data
@Slf4j
public class MemberCenterBenefitsProperties {

    private List<BenefitEntity> benefits;


    @Data
    public static class BenefitEntity {
        /**
         * 渠道号映射业务类型
         */
        private String benefitNo;

        /**
         * 默认限制次数
         */
        private Integer limitNum;

        /**
         * 渠道号
         */
        private String channel;

        /**
         * 算法列表
         */
        private List<String> algorithmCodeList;

        /**
         * 开启状态，true-开启，false-关闭
         */
        private Boolean enabled;

        /**
         * 打开会员权益
         *
         * @return
         */
        public boolean isOpen() {
            return enabled != null && enabled;
        }
    }

    /**
     * 是否需要权益
     *
     * @param channel
     * @param algorithmCode
     * @return
     */
    public Boolean hasBenefitNo(String channel, String algorithmCode) {
        return (StringUtils.isNotBlank(getBenefitNo(channel, algorithmCode)));
    }

    /**
     * 获取权益编码
     *
     * @param channelId
     * @param algorithmCode
     * @return
     */
    public String getBenefitNo(String channelId, String algorithmCode) {
        BenefitEntity findBenefit = getBenefit(channelId, algorithmCode);
        if (null != findBenefit) {
            return findBenefit.getBenefitNo();
        }
        return null;
    }

    public BenefitEntity getBenefitNoByChannelId(String channelId) {
        List<BenefitEntity> benefitList = this.getBenefits();
        if (CollectionUtils.isEmpty(benefitList)) {
            return null;
        }
        Optional<BenefitEntity> optional = benefitList.stream()
            .filter(benefit -> channelId.equals(benefit.getChannel()))
            .findFirst();
        return optional.orElse(null);
    }

    /**
     * 权益编码是否开启
     *
     * @param channelId
     * @param algorithmCode
     * @return
     */
    public boolean benefitNoOpen(String channelId, String algorithmCode) {
        BenefitEntity findBenefit = getBenefit(channelId, algorithmCode);
        if (null != findBenefit) {
            return findBenefit.isOpen();
        }
        return false;
    }

    /**
     * 获取权益编码
     *
     * @param channelId
     * @param algorithmCode
     * @return
     */
    private BenefitEntity getBenefit(String channelId, String algorithmCode) {
        BenefitEntity findBenefit = null;
        try {
            List<BenefitEntity> benefitList = this.getBenefits();
            if (CollectionUtils.isEmpty(benefitList)) {
                return null;
            }
            if (StringUtils.isAllBlank(channelId, algorithmCode)) {
                return null;
            }
            for (BenefitEntity benefit : benefitList) {
                if (channelId.equals(benefit.getChannel()) && null != benefit.getAlgorithmCodeList()
                    && benefit.getAlgorithmCodeList().contains(algorithmCode)) {
                    findBenefit = benefit;
                    return benefit;
                }
            }
        } catch (Exception e) {
            log.error("MemberCenterBenefitsProperties getBenefitNo channel:{}, algorithmCode:{}, error:", channelId,
                algorithmCode, e);
        } finally {
            log.info("MemberCenterBenefitsProperties getBenefitNo channel:{}, algorithmCode:{}, findBenefit:{}",
                channelId, algorithmCode, JSON.toJSONString(findBenefit));
        }
        return null;
    }
}
