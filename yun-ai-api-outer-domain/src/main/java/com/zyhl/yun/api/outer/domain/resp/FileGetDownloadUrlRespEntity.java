package com.zyhl.yun.api.outer.domain.resp;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件下载地址
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FileGetDownloadUrlRespEntity {

    /**
     * 文件id
     */
    public String fileId;
    /**
     * 失败错误码
     */
    public String errCode;
    /**
     * 失败原因
     */
    public String message;
    /**
     * 下载地址
     */
    public String url;
    /**
     * 下载链接的过期时间。
     */
    public String expiration;
    /**
     * 文件大小，单位：byte
     */
    public Long size;

    /**
     * 如果错误码不为空或者url为空则表示失败
     * @return true表示失败
     */
    public Boolean isFail() {
        return ObjectUtil.isNotEmpty(errCode) || ObjectUtil.isEmpty(url);
    }
}
