package com.zyhl.yun.api.outer.enums;

import lombok.Getter;

/**
 * 对话类型
 *
 * <AUTHOR>
 */
@Getter
public enum AiRegisterTypeEnum {

    /**
     * 图片报名
     */
    IMAGE(1, "图片报名"),

    /**
     * 文档报名
     */
    DOC(2, "文档报名"),
    ;

    AiRegisterTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 名称
     */
    private final String name;

}
