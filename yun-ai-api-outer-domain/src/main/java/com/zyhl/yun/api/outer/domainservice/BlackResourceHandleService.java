package com.zyhl.yun.api.outer.domainservice;

import java.util.List;

/**
 * 全网搜黑名单缓存数据处理相关业务接口
 * 
 * <AUTHOR>
 * @date 2025-04-10
 */
public interface BlackResourceHandleService {

	/**
	 * 检查是否为敏感词
	 * 
	 * @param text   待校验字符串
	 * @param parity 校验方式：1-包含校验（需要遍历所有黑名单资源）；2-精准校验 Map.containsKey
	 *               20250411会议暂定只使用精准校验（保证性能），待后续优化
	 * @return 是否为敏感词
	 */
    boolean isSensitive(String text, Integer parity);

    /**
     * 黑名单资源过滤
     * @param sourceResourceNameList 需要过滤的资源名称列表
     * @return 过滤后的资源名称列表
     */
    List<String> blackResourceFilter(List<String> sourceResourceNameList);

    /**
     * 屏蔽资源过滤
     * @param sourceResourceNameList 需要过滤的资源名称列表
     * @return 过滤后的资源名称列表
     */
    List<String> shieldResourceFilter(List<String> sourceResourceNameList);

}
