package com.zyhl.yun.api.outer.config.textmodel;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import com.zyhl.yun.api.outer.constants.Const;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description}
 * <p>
 *
 * <AUTHOR>
 * @since 4月24 2025
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "text-model.task-chat")
public class TaskChatPromptConfig {
  private String modelCode;
  private Integer modelMaxLength;
  private List<PromptListItem> promptList;

  @Data
  public static class PromptListItem {
    private String key;
    private List<SubPromptListItem> subPromptList;
  }

  @Data
  public static class SubPromptListItem {
    private String taskModule;
    private String promptCode;
  }

  public Map<String, List<SubPromptListItem>> getPromptMap() {
    Map<String, List<SubPromptListItem>> promptMap = new HashMap<>(Const.NUM_16);
    if(CollUtil.isEmpty(promptList)){
      return promptMap;
    }
    for (PromptListItem promptListItem : promptList) {
      //doc-summary：
      promptMap.put(promptListItem.getKey(), promptListItem.getSubPromptList());
    }
    return promptMap;
  }
}
