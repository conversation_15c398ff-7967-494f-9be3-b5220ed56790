package com.zyhl.yun.api.outer.enums;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 资源类型枚举
 *
 * <AUTHOR>
 * @since 2023-06-27
 */
@Getter
@AllArgsConstructor
public enum ResourceTypeEnum {

    /**
     * 文本  默认
     */
    TEXT(0, "文本"),
    /**
     * 邮箱
     */
    MAIL(1, "邮箱"),

    /**
     * 笔记
     */
    NOTE(2, "笔记"),

    /**
     * 图片
     */
    PICTURE(3, "图片"),

    /**
     * 对话ID
     */
    DIALOGUE(4, "对话ID"),

    /**
     * 云盘文档列表
     */
    CLOUD_DISK_DOCUMENT(5, "云盘文档列表"),

    /**
     * 邮件和附件列表
     */
    MAIL_ATTACHMENT(6, "邮件和附件列表"),

    /**
     * 个人知识库文件列表
     */
    PERSONAL_KNOWLEDGE_FILE(7, "个人知识库文档ID列表"),

    /**
     * 个人知识库列表
     */
    PERSONAL_KNOWLEDGE_BASE(8, "个人知识库列表"),

    ;

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 名称
     */
    private final String name;


    private static final Map<Integer, ResourceTypeEnum> MAP = new ConcurrentHashMap<>();


    static {
        EnumSet.allOf(ResourceTypeEnum.class).forEach(item -> MAP.put(item.type, item));
    }

    public static ResourceTypeEnum getType(Integer type) {
        if (null == type) {
            return null;
        }
        return MAP.get(type);
    }

    public static boolean isExist(Integer type) {
        return getType(type) != null;
    }

    public static boolean isText(Integer type) {
        return ResourceTypeEnum.TEXT.type.equals(type);
    }

    public static boolean isNotText(Integer type) {
        return !isText(type);
    }

    public static boolean isMail(Integer type) {
        return ResourceTypeEnum.MAIL.type.equals(type);
    }

    public static boolean isNote(Integer type) {
        return ResourceTypeEnum.NOTE.type.equals(type);
    }

    public static boolean isImage(Integer type) {
        return ResourceTypeEnum.PICTURE.type.equals(type);
    }

    public static boolean isDialogueId(Integer type) {
        return ResourceTypeEnum.DIALOGUE.type.equals(type);
    }

    public static boolean isDocument(Integer type) {
        return ResourceTypeEnum.CLOUD_DISK_DOCUMENT.type.equals(type);
    }

    public static boolean isAttachment(Integer type) {
        return ResourceTypeEnum.MAIL_ATTACHMENT.type.equals(type);
    }

    public static boolean isDocumentOrAttachment(Integer type) {
        return ResourceTypeEnum.CLOUD_DISK_DOCUMENT.type.equals(type) || ResourceTypeEnum.MAIL_ATTACHMENT.type.equals(type);
    }

    public static boolean isPersonalKnowledgeFile(Integer type) {
        return ResourceTypeEnum.PERSONAL_KNOWLEDGE_FILE.type.equals(type);
    }

    public static boolean isPersonalKnowledgeBase(Integer type) {
        return ResourceTypeEnum.PERSONAL_KNOWLEDGE_BASE.type.equals(type);
    }

    /**
     * 是否可以截取字符串的类型
     *
     * @param type
     * @return
     */
    public static boolean isSubStringType(Integer type) {
        return (ResourceTypeEnum.MAIL.type.equals(type) || ResourceTypeEnum.NOTE.type.equals(type));
    }

    /**
     * 是否包含某个枚举类型
     *
     * @param typeList 列表
     * @param typeEnum 枚举类型
     * @return true-包含，false-不包含
     */
    public static boolean contains(List<Integer> typeList, ResourceTypeEnum typeEnum) {
        if (ObjectUtil.isEmpty(typeList)) {
            return false;
        }

        return typeList.stream().anyMatch(type -> typeEnum.getType().equals(type));
    }

    public static boolean containsKnowledgeFile(List<Integer> typeList) {
        return contains(typeList, PERSONAL_KNOWLEDGE_FILE);
    }

    public static boolean containsKnowledgeBase(List<Integer> typeList) {
        return contains(typeList, PERSONAL_KNOWLEDGE_BASE);
    }

}
