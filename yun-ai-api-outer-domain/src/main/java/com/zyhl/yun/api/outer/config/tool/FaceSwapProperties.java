package com.zyhl.yun.api.outer.config.tool;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 授权报名相关配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "tool.face-swap")
public class FaceSwapProperties {

    /**
     * 模板配置列表
     */
    private List<FaceSwapTemplate> list;

    /**
     * AI照相馆模板信息
     */
    @Data
    public static class FaceSwapTemplate {
        /**
         * 模板编号：style-poseId
         */
        private String templateNo;

        /**
         * 模板图片高度
         */
        private Integer height;

        /**
         * 模板图片宽度
         */
        private Integer width;

        /**
         * 人脸锚点信息
         */
        private List<FacePointInfo> faceList;
    }


}
