package com.zyhl.yun.api.outer.domain.vo.chat.search.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 对话信息-搜索结果-圈子-列表数据
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchGroup implements Serializable {

    /** 圈子id */
    private String groupId;

    /** 圈子图片url */
    private String icon;

    /** 圈子名称 注意：命中的正文关键字后台会使用keywordsTag提醒（见返回样例），端侧做高亮显示后去除前后keywordsTag标签呈现给用户 */
    private String name;

    /** 圈主账号所有者账号名，当前为手机号 */
    private String managerAccount;

    /** 圈主账号所有者手机号UserId */
    private String managerUserId;

    /** 话题id */
    private String topicId;

    /** 话题名称 */
    private String topicName;

    /** 当前用户是否在圈子中：是 true 否 false */
    private String accountExisting;

    /** 背景图片url */
    private String backgroudUrl;

    /** 圈子类型 */
    private String type;

    /** 圈子默认权限：全权限=1，分享权限=2，查看=3,定制=4 */
    private String permissionType;

    /** 成员数量 */
    private String memberNum;

    /** 圈子号(新增字段 此字段高亮是全匹配由前端做 ) */
    private String groupNo;

    /** 推荐文案 */
    private String recommendedText;

    /** 推荐状态 0 不推荐 1推荐 */
    private String openType;

    /** 审核状态 */
    private Integer verifyStatus;

    /** 审核结果 */
    private Integer verifyResult;

    /** 是否动态命中：0否，1是 */
    private Integer isDynamic;

    /** dynamicInfo对象 */
    private DynamicInfo dynamicInfo;

}
