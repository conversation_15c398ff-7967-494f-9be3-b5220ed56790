package com.zyhl.yun.api.outer.domain.vo;

import java.io.Serializable;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户模型配置查询 v2
 * <AUTHOR>
 * @Date 2025/2/22 10:34
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChatConfigV2VO implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;


	private List<ChatConfigVO> modelList;
	
	private Boolean networkSearchStatus;

	private Integer aipptSupplier;

}
