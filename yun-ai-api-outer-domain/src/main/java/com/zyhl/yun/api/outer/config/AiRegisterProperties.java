package com.zyhl.yun.api.outer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 授权报名相关配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ai-register")
public class AiRegisterProperties {

    /**
     * 文档检索厂商类型
     *
     * @see com.zyhl.hcy.yun.ai.common.base.enums.SupplierTypeEnum
     */
    private Integer docFactoryType = 0;

    /**
     * 文档检索算法组
     */
    private Integer docAlgorithmGroupCode = 2;

    /**
     * 文档向量化厂商类型
     */
    private Integer docVectorFactoryType = 0;
    /**
     * 文档向量化算法组
     */
    private Integer docVectorAlgorithmGroupCode = 3;
}
