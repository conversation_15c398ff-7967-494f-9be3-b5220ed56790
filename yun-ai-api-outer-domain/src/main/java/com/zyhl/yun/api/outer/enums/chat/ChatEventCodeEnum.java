package com.zyhl.yun.api.outer.enums.chat;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流式返回结果事件状态码
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ChatEventCodeEnum {

    /**
     * 已为你自动打开联网搜索开关
     */
    OPEN_NETWORK_SEARCH("openNetworkSearch", "已为你自动打开联网搜索开关"),
    /**
     * 已为你切换到深度研究模式
     */
    SWITCHING_MODEL("switchingModel", "已为你切换到深度研究模式"),
    /**
     * 提炼并总结回复
     */
    SWITCHING_MODEL_NEED_TIME("switchingModelNeedTime", "提炼并总结回复"),
    ;

    private final String code;
    private final String desc;
}
