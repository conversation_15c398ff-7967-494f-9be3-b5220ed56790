package com.zyhl.yun.api.outer.domain.valueobject;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 邮件信息类
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MailInfo {
    /**
     * 邮件ID
     */
    private String mailId;

    /**
     * 收件人列表
     */
    private List<String> recipientList;

    /**
     * 收件邮箱列表
     */
    private List<String> emailAddressList;

    /**
     * 邮件标题
     */
    private String title;

    /**
     * 邮件正文
     */
    private String content;

    /**
     * 发件人
     */
    private String sender;

    /**
     * 云盘文件ID列表
     */
    private List<String> fileIdList;

    /**
     * 邮箱附件名列表
     */
    private List<String> attachNameList;

    /**
     * 文件下载 URL列表
     */
    private List<String> fileUrlList;

    /**
     * 邮件sid
     */
    private String sid;

    /**
     * 邮箱krmey
     */
    private String rmkey;

    public MailInfo(String mailId) {
        this.mailId = mailId;
    }
}