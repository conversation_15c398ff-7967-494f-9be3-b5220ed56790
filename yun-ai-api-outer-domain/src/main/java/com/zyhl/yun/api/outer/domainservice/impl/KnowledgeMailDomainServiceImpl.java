package com.zyhl.yun.api.outer.domainservice.impl;

import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.resp.MailDetailPartResult;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.vo.RagMailDetailVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveFileDownloadBatchVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveFileVO;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.domain.entity.knowledge.AlgorithmRagTextContentEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.PersonalKnowledgeMailPreviewInfoEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileResEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeInviteEntity;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domainservice.KnowledgeMailDomainService;
import com.zyhl.yun.api.outer.enums.ModelTypeEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileAuditStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeStatusEnum;
import com.zyhl.yun.api.outer.external.UserDriveExternalService;
import com.zyhl.yun.api.outer.repository.AlgorithmRagTextContentRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileResRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeInviteRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeRepository;
import com.zyhl.yun.api.outer.util.HtmlResourceProcessorUtils;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 *
 * 知识库邮件业务接口实现类  提供邮件预览信息的核心业务逻辑。
 * <AUTHOR>
 * @date 2025/4/17 18:07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KnowledgeMailDomainServiceImpl implements KnowledgeMailDomainService {

    private final UserKnowledgeFileRepository userKnowledgeFileRepository;
    private final UserKnowledgeFileResRepository userKnowledgeFileResRepository;
    private final AlgorithmRagTextContentRepository algorithmRagTextContentRepository;
    private final UserDriveExternalService userDriveExternalService;
    private final UserKnowledgeInviteRepository userKnowledgeInviteRepository;
    private final UserKnowledgeRepository userKnowledgeRepository;

    @Value("${audit.not-pass.thumbnail-url}")
    private String auditNotPassThumbnailUrl;


    /**
     * 获取邮件预览信息核心业务逻辑
     *
     * @param baseId     知识库ID（领域聚合根标识）
     * @param resourceId 资源ID（聚合内实体标识）
     * @return 邮件预览聚合根实体
     */
    @Override
    public PersonalKnowledgeMailPreviewInfoEntity getMailPreview(String baseId, String resourceId, String model) {
        //查询个人知识库文件资源表，验证基础资源存在性
        UserKnowledgeFileEntity fileEntity = userKnowledgeFileRepository.getOne(baseId, resourceId,ResourceTypeEnum.MAIL.getType());
        if (ObjectUtil.isNull(fileEntity)) {
            log.warn("未找到对应的知识库文件资源，baseId: {}, resourceId: {}", baseId, resourceId);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_NOT_FOUND);
        }
        //公共知识库 才判断文档送审状态，提示错误码
        if (fileEntity.getBaseId() != null) {
            UserKnowledgeEntity userKnowledge = userKnowledgeRepository.selectById(fileEntity.getBaseId());
            if (userKnowledge != null
                    && KnowledgeStatusEnum.OPEN.getStatus().equals(userKnowledge.getOpenLevel())
                    && FileAuditStatusEnum.isNotPass(fileEntity.getAuditStatus())) {
                throw new YunAiBusinessException(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
            }
        }
        //判断用户是否有权限预览
        String userId = RequestContextHolder.getUserId();
        if (!userId.equals(fileEntity.getUserId())) {
            List<UserKnowledgeInviteEntity> userKnowledgeInviteEntities = userKnowledgeInviteRepository.get(Long.valueOf(baseId), userId);
            if (CollUtil.isEmpty(userKnowledgeInviteEntities)) {
                throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_NO_PERMISSION);
            }
        }


        //构建HBase行键（领域知识：rowKey生成规则为 userId_fileId_resourceType）
        String rowKey = buildRowKey(fileEntity.getUserId(), resourceId, String.valueOf(ResourceTypeEnum.MAIL.getType()));

        //查询邮件正文内容
        AlgorithmRagTextContentEntity contentEntity = algorithmRagTextContentRepository.getByRowKey(rowKey);
        if (Objects.isNull(contentEntity)) {
            log.warn("未找到对应的邮件正文内容，rowKey: {}", rowKey);
            return null;
        }

        //获取正文数据(包含收件人发件人标题等邮件信息)
        RagMailDetailVO ragMailDetailVO = parseJsonToMailPreviewInfoEntity(contentEntity.getContent());
        if (ragMailDetailVO == null) {
            log.error("解析邮件预览信息失败");
            return null;
        }

        //预览正文数据(只有html的正文邮件信息)
        String htmlNewContent = Optional.ofNullable(parseJsonToMailPreviewInfoEntity(contentEntity.getNewContent()))
                .map(RagMailDetailVO::getHtml)
                .flatMap(html -> Optional.ofNullable(html.getContent()))
                .orElse(null);

        //提取附件ID列表并查询附件资源
        List<UserKnowledgeFileResEntity> fileResList = userKnowledgeFileResRepository.getResourceByFileId(
                fileEntity.getUserId(),
                Long.valueOf(baseId),
                resourceId,
                KnowledgeResourceTypeEnum.MAIL.getCode()
        );

        List<String> fileIds = extractAttachmentFileIds(fileResList);

        //获取独立空间文件信息
        List<OwnerDriveFileVO> fileInfos = fetchFileInfos(fileEntity.getUserId(), fileIds);

        //构建完整预览实体
        return buildMailPreviewEntity(fileEntity.getUserId(), ragMailDetailVO, htmlNewContent, fileInfos, baseId, resourceId, model);
    }

    /**
     * 构建HBase行键
     *
     * @param userId 用户ID
     * @param fileId 文件ID
     * @param type   资源类型
     * @return 行键字符串
     */
    private String buildRowKey(String userId, String fileId, String type) {
        return String.format("%s_%s_%s", userId, fileId, type);
    }

    /**
     * 将 JSON 正文字符串解析为 RagMailDetailVO 对象
     *
     * @param jsonStr JSON 字符串
     * @return 解析后的 RagMailDetailVO 对象
     */
    private RagMailDetailVO parseJsonToMailPreviewInfoEntity(String jsonStr) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(jsonStr, RagMailDetailVO.class);
        } catch (Exception e) {
            log.error("解析邮件预览信息失败", e);
            return null;
        }
    }

    /**
     * 提取附件文件ID列表
     *
     * @param entityList 文件资源实体列表
     * @return 文件ID列表
     */
    private List<String> extractAttachmentFileIds(List<UserKnowledgeFileResEntity> entityList) {
        return Optional.ofNullable(entityList)
                .orElse(Collections.emptyList())
                .stream()
                .map(entity -> String.valueOf(entity.getFileId()))
                .collect(Collectors.toList());
    }

    /**
     * 获取独立空间文件信息
     *
     * @param userId  用户ID
     * @param fileIds 文件ID列表
     * @return 文件信息列表
     */
    private List<OwnerDriveFileVO> fetchFileInfos(String userId, List<String> fileIds) {
        if (CollUtil.isEmpty(fileIds)) {
            return Collections.emptyList();
        }
        return userDriveExternalService.getBatchFileInfo(userId, fileIds);
    }

    /**
     * 构建邮件预览实体
     *
     * @param detailVO   邮件详情对象
     * @param fileInfos  文件信息列表
     * @param baseId     知识库ID
     * @param resourceId 资源ID
     * @return 邮件预览实体
     */
    private PersonalKnowledgeMailPreviewInfoEntity buildMailPreviewEntity(
            String userId,
            RagMailDetailVO detailVO,
            String htmlNewContent,
            List<OwnerDriveFileVO> fileInfos,
            String baseId,
            String resourceId,
            String model
    ) {
        MailDetailPartResult textContent = detailVO.getText();
        MailDetailPartResult htmlContent = detailVO.getHtml();
        //用抽取出来的枚举
        if (ModelTypeEnum.TEXT.getValue().equalsIgnoreCase(model)) {
            htmlContent = null;
        } else if (ModelTypeEnum.HTML.getValue().equalsIgnoreCase(model) && CharSequenceUtil.isNotBlank(htmlNewContent)) {
            // 如果 htmlContent 不为空，则清空 textContent
            textContent = null;
        }
        if (CharSequenceUtil.isNotBlank(htmlNewContent)) {
            //处理htmlContent里的src值
            htmlContent.setContent(processHtmlContent(userId, htmlNewContent));
        }
        return PersonalKnowledgeMailPreviewInfoEntity.builder()
                .resourceId(resourceId)
                .baseId(baseId)
                .recipientList(splitAddresses(detailVO.getTo()))
                .carbonCopyList(splitAddresses(detailVO.getCc()))
                .title(detailVO.getSubject())
                .senderList(splitAddresses(detailVO.getAccount()))
                .sendDate(detailVO.getSendDate())
                .textContent(textContent)
                .htmlContent(htmlContent)
                .content(detailVO.getTxtContent())
                .attachmentList(buildAttachmentList(fileInfos))
                .build();
    }



    /**
     * 处理html正文图片，音视频等多媒体的src地址
     * @param htmlContent html格式字符串
     * @return 替换后的html字符串
     */
    public String processHtmlContent(String userId , String htmlContent) {
        // 1. 提取所有fileId
        List<String> allFileIds = HtmlResourceProcessorUtils.extractFileIds(htmlContent);
        if (CollUtil.isEmpty(allFileIds)) {
            return htmlContent;
        }

        // 2. 查询文件状态
        Map<String, Boolean> fileStatusMap = checkFileApprovalStatus(userId, allFileIds);

        // 3. 分离已审核和未审核的 fileId，并避免空指针
        List<String> approvedFileIds = Optional.ofNullable(fileStatusMap).orElse(Collections.emptyMap()).entrySet().stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .filter(key -> !CharSequenceUtil.isBlank(key))
                .collect(Collectors.toList());

        // 4. 获取已审核文件的下载URL
        Map<String, String> approvedUrls = CollUtil.isEmpty(approvedFileIds)
            ? Collections.emptyMap()
            : convertToUrlMap(userDriveExternalService.getBatchFilesDownloadUrl(userId, approvedFileIds));

        // 5. 替换HTML中的URL
        return HtmlResourceProcessorUtils.replaceResourceUrls(
            htmlContent,
            approvedUrls,
            auditNotPassThumbnailUrl
        );
    }

    /**
     * 检查文件审核状态
     */
    private Map<String, Boolean> checkFileApprovalStatus(String userId, List<String> fileIds) {
        if (CollUtil.isEmpty(fileIds)) {
            return Collections.emptyMap();
        }

        // 查询数据库获取审核状态列表
        List<UserKnowledgeFileResEntity> statusList = userKnowledgeFileResRepository.getResourceByFileIds(userId, fileIds);

        // 构建 fileId -> 是否审核通过 的 Map（使用 Stream 过滤 + 收集）
        Map<String, Boolean> approvalMap = Optional.ofNullable(statusList).orElse(Collections.emptyList()).stream()
                .filter(entity -> !CharSequenceUtil.isBlank(entity.getFileId())
                        && Objects.equals(entity.getAuditStatus(), 2))
                .collect(Collectors.toMap(
                        UserKnowledgeFileResEntity::getFileId,
                        entity -> true,
                        // 合并函数：保留第一个出现的值
                        (existing, replacement) -> existing
                ));

        // 构建最终结果 Map，保持原始 fileIds 顺序（可选）
        Map<String, Boolean> result = fileIds.stream()
                .filter(fileId -> !CharSequenceUtil.isBlank(fileId))
                .collect(Collectors.toMap(
                        fileId -> fileId,
                        fileId -> approvalMap.getOrDefault(fileId, false),
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));

        return Collections.unmodifiableMap(result);
    }

    private Map<String, String> convertToUrlMap(OwnerDriveFileDownloadBatchVO downloadInfo) {
        Map<String, String> urlMap = new HashMap<>(Const.NUM_16);
        if (downloadInfo != null && downloadInfo.getItems() != null) {
            for (OwnerDriveFileDownloadBatchVO.OwnerDriveDownloadUrlInfo info : downloadInfo.getItems()) {
                if (info != null && info.getFileId() != null && info.getUrl() != null) {
                    urlMap.put(info.getFileId(), info.getUrl());
                }
            }
        }
        return urlMap;
    }

    /**
     * 拆分地址字符串为列表
     *
     * @param addresses 地址字符串
     * @return 地址列表
     */
    private List<String> splitAddresses(String addresses) {
        return StrUtil.split(addresses, ",");
    }

    /**
     * 构建附件列表
     *
     * @param fileInfos 文件信息列表
     * @return 文件对象列表
     */
    private List<File> buildAttachmentList(List<OwnerDriveFileVO> fileInfos) {
        if (CollUtil.isEmpty(fileInfos)) {
            return Collections.emptyList();
        }
        return fileInfos.stream()
                .filter(Objects::nonNull)
                .map(File::new)
                .collect(Collectors.toList());
    }
}
