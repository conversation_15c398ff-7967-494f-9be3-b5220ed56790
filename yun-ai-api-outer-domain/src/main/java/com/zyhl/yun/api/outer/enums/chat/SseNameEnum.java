package com.zyhl.yun.api.outer.enums.chat;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述：流式输出的名称枚举
 *
 * <AUTHOR> zhumaoxian  2025/4/9 20:36
 */
@Getter
@AllArgsConstructor
public enum SseNameEnum {

    SECOND_SSE_NAME("AI-chat-second-sse", "二次流式输出默认名称", "【二次流式对话】"),
    
    SSE_NAME("AI-chat-sse", "流式输出默认名称", "【通用对话】"),

    AGENT_NAME("AI-chat-sse-agent", "智能体对话", "【智能体对话】"),

    SPEEDREAD_NAME("AI-chat-sse-speedread", "快速阅读对话", "【快速阅读对话】"),

    TASK_SSE("AI-chat-sse-task", "任务对话", "【任务对话】"),

    SIMPLE_SSE("AI-chat-sse-simple", "普通对话", "【普通对话】"),

    SIMPLE_SSE_MAIL("AI-chat-sse-simple-mail", "普通对话的邮件总结对话", "【普通对话】【邮件总结】"),

    VISION_SSE("AI-chat-sse-vision", "视觉大模型流式对话", "【视觉大模型对话】"),

    DOC_SSE("AI-chat-sse-doc", "文档资源对话", "【文档对话】"),

    CONTINUE_TRANS("AI-chat-sse-continue-trans", "断点续传", "断点续传"),

    KNOWLEDGE("AI-chat-sse-kn", "知识库对话", "【RAG重要节点日志】"),
    KNOWLEDGE_NO_VECTOR("AI-chat-sse-kn-no-vector", "知识库对话-向量化返回空", "【RAG重要节点日志】【文本向量化】"),
    KNOWLEDGE_NO_RECALL("AI-chat-sse-kn-no-recall", "知识库对话-召回为空", "【RAG重要节点日志】【多路召回】"),
    KNOWLEDGE_NO_RERANK("AI-chat-sse-kn-no-rerank", "知识库对话-重排为空", "【RAG重要节点日志】【算法重排】"),
    KNOWLEDGE_NO_FILE("AI-chat-sse-kn-no-file", "知识库对话-文件查询为空", "【RAG重要节点日志】【文件查询】"),
    KNOWLEDGE_RELEVANCY("AI-chat-sse-kn-relevancy", "知识库对话-重排数据被相关性过滤了部分数据", "【RAG重要节点日志】【相关性】"),
    KNOWLEDGE_NO_RECALL_NO_NETWORK("AI-chat-sse-kn-no-recall-no-network", "知识库对话-无召回无联网", "【RAG重要节点日志】【无召回无联网】"),
    KNOWLEDGE_RECALL_TIMEOUT("AI-chat-sse-recall-timeout", "知识库对话-召回超时", "【RAG重要节点日志】【召回超时】"),
    KNOWLEDGE_RERANK_TIMEOUT("AI-chat-sse-rerank-timeout", "知识库对话-重排超时", "【RAG重要节点日志】【重排超时】"),

    KNOWLEDGE_NOUN_LIBRARY("AI-chat-sse-kn-noun_library", "知识库对话-命中名词库", "【RAG重要节点日志】【命中名词库】"),

    AI_INTERNET_SEARCH("AI-chat-sse-internet-search", "AI全网搜", "【AI全网搜对话】"),

    AI_SEARCH("AI-chat-search", "AI搜索", "【AI搜索】"),
    AI_SEARCH_SSE("AI-chat-sse-search", "AI搜索-大模型对话", "【AI搜索】【大模型对话】"),

    AI_EDIT("AI-chat-sse-ai-edit", "AI编辑", "【云邮AI编辑】"),

    KNOWLEDGE_NOTE_NO_RECALL_NO_NETWORK("AI-chat-sse-note-kn-no-recall-no-network", "笔记助手-知识库对话-无召回无联网", "【RAG重要节点日志】【笔记助手-无召回无联网】"),
    KNOWLEDGE_NOTE_NOUN_LIBRARY("AI-chat-sse-note-kn-noun_library", "笔记助手-知识库对话-命中名词库", "【RAG重要节点日志】【笔记助手-命中名词库】"),
    KNOWLEDGE_NOTE("AI-chat-sse-kn", "笔记助手-知识库对话", "【RAG重要节点日志】【笔记助手】"),
    ;

    private final String code;
    private final String desc;
    private final String logName;


    public static SseNameEnum getByName(String name) {
        for (SseNameEnum value : values()) {
            if (value.getCode().equals(name)) {
                return value;
            }
        }
        return null;
    }

}
