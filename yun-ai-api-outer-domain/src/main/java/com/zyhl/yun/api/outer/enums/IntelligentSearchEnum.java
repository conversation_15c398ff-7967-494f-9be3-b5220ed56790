package com.zyhl.yun.api.outer.enums;

/**
 * <AUTHOR>
 */
public enum IntelligentSearchEnum {


    /**
     * 智能搜图缓存Key
     */
    INTELLIGENT_SEARCH_KEY("image:text:search:", "智能搜图缓存Key"),

    /**
     * 页显示数
     */
    PAGE_DISPLAY_COUNT("10", "页显示数");

    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String msg;

    private IntelligentSearchEnum(String resultCode, String resultMsg) {
        this.code = resultCode;
        this.msg = resultMsg;
    }

    public String getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }


}
