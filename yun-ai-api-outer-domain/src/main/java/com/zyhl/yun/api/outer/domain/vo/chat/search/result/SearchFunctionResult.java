package com.zyhl.yun.api.outer.domain.vo.chat.search.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 对话信息-搜索结果-功能
 * @Author: WeiJingKun
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchFunctionResult extends SearchCommonResult implements Serializable {

    /** 功能信息 */
    private List<SearchFunction> functionList;

    /** 下一页起始资源标识符，最后一页该值为空 */
    private List<Object> pageAfter;

    /** 记录总数 */
    private Integer totalCount;

}
