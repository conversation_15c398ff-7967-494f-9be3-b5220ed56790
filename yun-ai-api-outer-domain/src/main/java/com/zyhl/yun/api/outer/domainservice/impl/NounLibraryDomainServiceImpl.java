package com.zyhl.yun.api.outer.domainservice.impl;

import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.es.domain.datahelper.repository.EsNounLibraryRepository;
import com.zyhl.hcy.yun.ai.common.base.es.entity.NounLibraryEntity;
import com.zyhl.yun.api.outer.config.InterventionProperties;
import com.zyhl.yun.api.outer.domainservice.NounLibraryDomainService;
import com.zyhl.yun.api.outer.external.CmicTextService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 知识库对话名词库领域服务实现类
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Slf4j
@Service
public class NounLibraryDomainServiceImpl implements NounLibraryDomainService {


    @Resource
    private InterventionProperties interventionProperties;

    @Resource
    private CmicTextService cmicTextService;

    @Resource
    private EsNounLibraryRepository esNounLibraryRepository;

    @Override
    public List<String> getNounLibraryList(List<String> entityKeyList) {
        if (CollectionUtils.isEmpty(entityKeyList)) {
            log.info("【知识库对话】【RAG重要节点日志】【数据搜索】【名词库匹配】实体关键字列表为空 不执行匹配名词库");
            return null;
        }

        List<String> nounLibraryList = null;
        try {
            NounLibraryEntity entity = esNounLibraryRepository.getNounLibrary(entityKeyList);
            if (entity != null) {
                nounLibraryList = new ArrayList<>();
                nounLibraryList.add(entity.getAnswer());
            }

        } catch (Exception e) {
            log.error("【知识库对话】【RAG重要节点日志】【数据搜索】【名词库匹配】 es查询异常:", e);
        } finally {
            log.info("【知识库对话】【RAG重要节点日志】【数据搜索】【名词库匹配】实体关键字列表为:{},匹配结果为:{}", JsonUtil.toJson(entityKeyList), JsonUtil.toJson(nounLibraryList));
        }

        return nounLibraryList;
    }

}
