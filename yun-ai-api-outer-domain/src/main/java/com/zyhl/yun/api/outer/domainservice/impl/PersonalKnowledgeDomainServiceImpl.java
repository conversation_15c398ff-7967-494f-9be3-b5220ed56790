package com.zyhl.yun.api.outer.domainservice.impl;

import com.zyhl.hcy.yun.ai.common.base.es.domain.datahelper.repository.EsPersonalKnowledgeRepository;
import com.zyhl.hcy.yun.ai.common.base.es.entity.PersonalKnowledgeEsEntity;
import com.zyhl.yun.api.outer.domainservice.PersonalKnowledgeDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

/**
 * 个人知识库领域服务实现
 * <AUTHOR>
 */
@Slf4j
@Service
public class PersonalKnowledgeDomainServiceImpl implements PersonalKnowledgeDomainService {
    @Resource
    private EsPersonalKnowledgeRepository esPersonalKnowledgeRepository;

/*    @Override
    public List<PersonalKnowledgeEsEntity> getPersonalKnowledgeResult(String text, List<BigDecimal> feature, String userId) {

        return esPersonalKnowledgeRepository.getPersonalKnowledgeResult(text, feature, userId);
    }

    @Override
    public List<PersonalKnowledgeEsEntity> getPersonalKnowledgeResultByParseType(String text, List<BigDecimal> feature, String userId, String parseType) {

        return esPersonalKnowledgeRepository.getPersonalKnowledgeResultByParseType(text, feature, userId, parseType);
    }*/

    @Override
    public Boolean updatePersonalKnowledgeRecallCount(String userId, String id) {
        return esPersonalKnowledgeRepository.updatePersonalKnowledgeRecallCount(userId, id);
    }

    @Override
    public PersonalKnowledgeEsEntity getPersonalKnowledgeById(String userId, String id) {
        return esPersonalKnowledgeRepository.getPersonalKnowledgeById(userId, id);
    }

    @Override
    public void batchDeleteDocumentById(String userId, List<String> fileIdList, int delSize, ExecutorService knowledgeDeleteThreadPool) {
        esPersonalKnowledgeRepository.batchDeleteDocumentById(userId, fileIdList, delSize, knowledgeDeleteThreadPool);
    }

}
