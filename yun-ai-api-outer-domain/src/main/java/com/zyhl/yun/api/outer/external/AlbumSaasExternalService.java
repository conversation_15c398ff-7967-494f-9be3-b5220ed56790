package com.zyhl.yun.api.outer.external;

import com.zyhl.hcy.yun.ai.common.platform.third.client.album.resp.AddMemoryStoryResp;
import com.zyhl.yun.api.outer.domain.dto.QualityAlbumSelectionRespDTO;
import com.zyhl.yun.api.outer.domain.valueobject.AlbumInfo;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.external.AlbumSaasExternalService} <br>
 * <b> description:</b>
 * 相册接口层
 *
 * <AUTHOR>
 * @date 2025-05-21 13:56
 **/
public interface AlbumSaasExternalService {

    /**
     * 新增手工故事接口
     *
     * @param qualityAlbumSelectionRespDTO the quality album selection resp dto
     * @return {@link AddMemoryStoryResp}
     * <AUTHOR>
     * @date 2025/5/21 13:42
     */
    AddMemoryStoryResp addMemoryStory(QualityAlbumSelectionRespDTO qualityAlbumSelectionRespDTO);

    /**
     * 查询相册信息
     *
     * @param id the id
     * @return {@link AlbumInfo}
     * <AUTHOR>
     * @date 2025/5/21 13:53
     */
    AlbumInfo queryAlbumByAlbumId(String id);
}
