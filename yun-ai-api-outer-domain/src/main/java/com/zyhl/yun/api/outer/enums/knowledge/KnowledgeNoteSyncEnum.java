package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 笔记同步状态
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum KnowledgeNoteSyncEnum {

    /**
     * 正常
     */
    DEFAULT(-1, "默认"),

    /**
     * 同步中
     */
    SYNC_BEGIN(0, "同步中"),

    /**
     * 同步成功
     */
    SYNC_SUCCESS(1, "同步成功"),

    /**
     * 同步失败
     */
    SYNC_FAILED(2, "同步失败"),


    ;

    /**
     * 状态
     */
    private final Integer status;
    /**
     * 备注
     */
    private final String remark;


    public static boolean isSuccess(Integer status) {
        return SYNC_SUCCESS.status.equals(status);
    }



}
