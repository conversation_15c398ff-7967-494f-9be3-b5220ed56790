package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识库添加结果枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum KnowledgeTextTypeEnum {

    /**
     * 问答
     */
    QA("QA", "QA知识库问答"),

    /**
     * 文档分片
     */
    SPLIT("SPLIT", "文档分片"),

    /**
     * 文档送大模型生成问答
     */
    GQA("GQA","文档送大模型生成问答")

    ;

    /**
     * 编码
     */
    private final String code;
    /**
     * 描述
     */
    private final String msg;



}
