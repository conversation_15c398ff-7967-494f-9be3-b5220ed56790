package com.zyhl.yun.api.outer.enums.chat;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 个人知识库资源处理结果状态枚举
 * @Author: WeiJingKun
 */
@Getter
@AllArgsConstructor
public enum PersonalKnowledgeAiStatusEnum {
    /** 处理失败 */
    PROCESSING_FAILED(-1, "处理失败"),
    /** 处理中 */
    PROCESSING(0, "处理中"),
    /** 处理成功 */
    PROCESSING_SUCCESS(1, "处理成功");

    /**
     * code
     */
    private final Integer code;
    /**
     * 名称
     */
    private final String name;


    private static final Map<Integer, PersonalKnowledgeAiStatusEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(PersonalKnowledgeAiStatusEnum.class).forEach(item -> MAP.put(item.code, item));
    }

    public static PersonalKnowledgeAiStatusEnum getType(Integer code) {
        if (null == code) {
            return null;
        }
        return MAP.get(code);
    }
}
