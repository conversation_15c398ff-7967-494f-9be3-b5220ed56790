package com.zyhl.yun.api.outer.domainservice;

import com.zyhl.yun.api.outer.domain.dto.CatalogConfigDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserDriveConfigEntity;

/**
 * 用户独立空间配置领域服务
 *
 * <AUTHOR>
 */
public interface UserDriveConfigDomainService {

    /**
     * 创建用户独立空间，只负责创建空间业务，不负责创建目录业务
     *
     * @param userId          用户id
     * @param belongsPlatform 所属平台
     * @return 用户空间配置实体
     */
    UserDriveConfigEntity createDrive(String userId, Integer belongsPlatform);

    /**
     * 创建知识库目录配置，有配置则解析并返回，无则创建并返回
     * 只负责知识库目录业务
     *
     * @param entity 用户空间配置实体
     * @return 目录配置信息
     */
    CatalogConfigDTO.CatalogConfigInfo createKnowledgeConfig(UserDriveConfigEntity entity);

    /**
     * 创建知识库目录
     *
     * @param userId   用户id
     * @param name     目录名称
     * @param parentId 父目录id
     * @return
     */
    String createCatalog(String userId, String name, String parentId);

    /**
     * 创建知识库目录配置
     * 个人知识库2.0目录业务
     *
     * @param userId 用户id
     * @param name   知识库名称
     * @return 目录配置信息
     */
    CatalogConfigDTO.CatalogConfigInfo knowledgeConfig(String userId, String name);

}
