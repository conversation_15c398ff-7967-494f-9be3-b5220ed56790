package com.zyhl.yun.api.outer.enums;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 应用类型枚举
 * 应用类型:普通对话(chat); 智能体对话(intelligent); 快速阅读(speedread);
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum ApplicationTypeEnum {

    /**
     * 普通对话
     */
    CHAT("chat", "普通对话"),

    /**
     * 智能体对话
     */
    INTELLIGENT("intelligent", "智能体对话"),

    /**
     * 快速阅读
     */
    SPEED_READ("speedread", "快速阅读"),

    ;

    private static final Map<String, ApplicationTypeEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(ApplicationTypeEnum.class).forEach(item -> MAP.put(item.code, item));
    }

    public static ApplicationTypeEnum getByCode(String code) {
        if (null == code) {
            return null;
        }
        return MAP.get(code);
    }

    public static ApplicationTypeEnum getByCodeDefaultChat(String code) {
        if (null == code) {
            return CHAT;
        }
        return MAP.getOrDefault(code, CHAT);
    }

    /**
     * 是否存在
     *
     * @param code 编码
     * @return true-存在
     */
    public static boolean isExist(String code) {
        return getByCode(code) != null;
    }


    /**
     * 是普通对话
     *
     * @param code 编码
     * @return true-是普通对话
     */
    public static boolean isChat(String code) {
        return CHAT.code.equals(code);
    }

    public static boolean isNotChat(String code) {
        return !isChat(code);
    }

    /**
     * 智能体对话
     *
     * @param code 编码
     * @return true-是智能体对话
     */
    public static boolean isIntelligen(String code) {
        return INTELLIGENT.code.equals(code);
    }

    /**
     * 是快速阅读
     *
     * @param code 编码
     * @return true-是快速阅读
     */
    public static boolean isSpeedRead(String code) {
        return SPEED_READ.code.equals(code);
    }

    /**
     * 对应code
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

}
