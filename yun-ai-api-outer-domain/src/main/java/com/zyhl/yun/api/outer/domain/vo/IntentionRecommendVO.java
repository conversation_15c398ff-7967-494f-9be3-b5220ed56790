package com.zyhl.yun.api.outer.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 意图推荐列表VO
 *
 * <AUTHOR>
 * @date 2024/6/2 23:02
 */
@Data
@Builder
public class IntentionRecommendVO {

    /**
     * 意图指令，参考IntentionCommands意图指令，
     * 1.文本对话和搜索意图时，copy中返回推荐的用户输入语句；
     * 2.图片工具意图，返回推荐工具的引导文案；
     *
     * @see com.zyhl.yun.api.outer.enums.DialogueIntentionEnum
     */
    private String intentionCommand;

    /**
     * 子意图
     */
    private String subCommand;
    /**
     * 引导文案
     */
    private String copy;

    /**
     * 跳转URL，AI消除和智能抠图意图时返回
     */
    private String linkURL = "";

    public IntentionRecommendVO() {
	}
    
	public IntentionRecommendVO(String intentionCommand, String copy, String linkURL) {
		this.intentionCommand = intentionCommand;
		this.copy = copy;
		this.linkURL = linkURL;
	}
	
	public IntentionRecommendVO(String intentionCommand,String subCommand, String copy, String linkURL) {
		this.intentionCommand = intentionCommand;
		this.subCommand = subCommand;
		this.copy = copy;
		this.linkURL = linkURL;
	}

}
