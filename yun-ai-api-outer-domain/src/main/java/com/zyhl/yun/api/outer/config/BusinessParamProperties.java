package com.zyhl.yun.api.outer.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 业务参数配置
 *
 * <AUTHOR>
 */
@Slf4j
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "business-param")
public class BusinessParamProperties {

	/**
	 * 历史对话列表
	 */
	private ChatContentList chatContentList;

	/**
	 * 图片工具业务配置
	 */
	private ImageTool imageTool;

	/**
	 * 文本工具业务配置
	 */
	private TextTool textTool;

	/**
	 * 历史会话列表查询V2接口
	 */
	private AssistantChatV2List assistantChatV2List;

	/**
	 * 历史对话列表
	 */
	@Data
	public static class ChatContentList {

		/**
		 * 限制搜索X天内的历史记录
		 */
		private Integer queryMaxDays;

		/**
		 * 业务类型查询条件映射
		 */
		private Map<String, List<String>> businessTypeMap;

	}

	/**
	 * 图片工具配置
	 */
	@Data
	public static class ImageTool {

		/**
		 * 线程休眠时间，单位毫秒
		 */
		private Integer threadSleepTime;

		/**
		 * 线程图片工具最大轮询次数
		 */
		private Integer threadMaxPollUpdateCount;
	}

	/**
	 * 文本工具配置
	 */
	@Data
	public static class TextTool {

		/**
		 * 线程休眠时间，单位毫秒
		 */
		private Integer threadSleepTime;

		/**
		 * 线程图片工具最大轮询次数
		 */
		private Integer threadMaxPollUpdateCount;
	}

	/**
	 * 历史会话列表查询V2接口
	 */
	@Data
	public static class AssistantChatV2List {

		/**
		 * 默认会话iconUrl
		 */
		private String defaultIconUrl;

		/**
		 * 会话iconUrl映射
		 */
		private Map<String, String> iconUrlMap;

		/**
		 * 获取会话iconUrl
		 *
		 * @param iconType 图标类型
		 * @return 会话iconUrl
		 */
		public String getIconUrl(String iconType, String subIconType) {
			// 获取子图标类型对应的iconUrl
			String iconUrl = iconUrlMap.get(subIconType);
			if (CharSequenceUtil.isBlank(iconUrl)) {
				// 子类型未配置，获取父图标类型对应的iconUrl
				iconUrl = iconUrlMap.get(iconType);
				if (CharSequenceUtil.isBlank(iconUrl)) {
					log.info("未配置iconUrl使用默认：{}，iconType：{}, subIconType：{}", defaultIconUrl, iconType, subIconType);
					return defaultIconUrl;
				}
			}
			return iconUrl;
		}

	}

	/**
	 * 获取图片工具线程休眠时间
	 *
	 * @return
	 */
	public int getImageToolThreadSleepTime() {
		int defaultSleepTime = 2;
		if (null == this.getImageTool()) {
			// 无配置，默认5秒
			return defaultSleepTime;
		}
		return (null == this.getImageTool().getThreadSleepTime() ? defaultSleepTime
				: this.getImageTool().getThreadSleepTime().intValue());
	}

	/**
	 * 获取图片工具线程最大执行次数
	 *
	 * @return
	 */
	public int getImageToolThreadMaxPullUpdateCount() {
		int defaultSleepTime = 90;
		if (null == this.getImageTool()) {
			// 无配置，默认90次
			return defaultSleepTime;
		}
		return (null == this.getImageTool().getThreadMaxPollUpdateCount() ? defaultSleepTime
				: this.getImageTool().getThreadMaxPollUpdateCount().intValue());
	}

	/**
	 * 获取文本工具线程休眠时间
	 *
	 * @return
	 */
	public int getTextToolThreadSleepTime() {
		int defaultSleepTime = 2;
		if (null == this.getTextTool()) {
			// 无配置，默认5秒
			return defaultSleepTime;
		}
		return (null == this.getTextTool().getThreadSleepTime() ? defaultSleepTime
				: this.getTextTool().getThreadSleepTime().intValue());
	}

	/**
	 * 获取文本工具线程最大执行次数
	 *
	 * @return
	 */
	public int getTextToolThreadMaxPullUpdateCount() {
		int defaultSleepTime = 90;
		if (null == this.getImageTool()) {
			// 无配置，默认90次
			return defaultSleepTime;
		}
		return (null == this.getTextTool().getThreadMaxPollUpdateCount() ? defaultSleepTime
				: this.getTextTool().getThreadMaxPollUpdateCount().intValue());
	}

}
