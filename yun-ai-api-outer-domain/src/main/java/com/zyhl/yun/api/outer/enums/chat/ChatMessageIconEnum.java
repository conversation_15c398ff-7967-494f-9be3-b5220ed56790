package com.zyhl.yun.api.outer.enums.chat;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述：历史会话图标枚举
 *
 * <AUTHOR> zhumaoxian 2025/4/22 23:03
 */
@Getter
@AllArgsConstructor
public enum ChatMessageIconEnum {

	SIMPLE_DIALOGUE("100", "", "普通对话", ""),

	IMAGE_DIALOGUE("200", "", "图片对话", "AI图片："),

	COMMAND_DIALOGUE("300", "", "指令对话", ""),

	TOOL_DIALOGUE("400", "", "文本工具对话", ""),

	TOOL_DIALOGUE_AI_CODER("400", "40001", "文本工具对话-代码助手", "AI代码助手："),

	TOOL_DIALOGUE_SPEED_READ("400", "40002", "文本工具对话-快速阅读", "快速阅读："),

	TOOL_DIALOGUE_MEETING_MINUTES("400", "40003", "文本工具对话-会议纪要", "AI会议纪要："),

	TOOL_DIALOGUE_AI_PPT("400", "40004", "文本工具对话-AIPPT", "AI PPT："),

	TOOL_DIALOGUE_AI_PHOTO_SOLVE_PROBLEMS("400", "40005", "文本工具对话-AI拍照解题", ""),

	TOOL_DIALOGUE_AI_PHOTO_TRANSLATE("400", "40006", "文本工具对话-AI拍照翻译", ""),

	TOOL_DIALOGUE_AI_PHOTO_QA("400", "40007", "文本工具对话-AI拍照问答", ""),

	;

	private final String iconType;
	private final String subIconType;
	private final String desc;
	private final String messageTitle;

	public static boolean isImageDialogue(String iconType) {
		return IMAGE_DIALOGUE.getIconType().equals(iconType);
	}

	public static boolean isAiCoderDialogue(String iconType, String subIconType) {
		return TOOL_DIALOGUE_AI_CODER.getIconType().equals(iconType)
				&& TOOL_DIALOGUE_AI_CODER.getSubIconType().equals(subIconType);
	}

	public static boolean isSpeedReadDialogue(String iconType, String subIconType) {
		return TOOL_DIALOGUE_SPEED_READ.getIconType().equals(iconType)
				&& TOOL_DIALOGUE_SPEED_READ.getSubIconType().equals(subIconType);
	}

	public static boolean isMeetingMinutesDialogue(String iconType, String subIconType) {
		return TOOL_DIALOGUE_MEETING_MINUTES.getIconType().equals(iconType)
				&& TOOL_DIALOGUE_MEETING_MINUTES.getSubIconType().equals(subIconType);
	}

	public static boolean isAiPptDialogue(String iconType, String subIconType) {
		return TOOL_DIALOGUE_AI_PPT.getIconType().equals(iconType)
				&& TOOL_DIALOGUE_AI_PPT.getSubIconType().equals(subIconType);
	}

}
