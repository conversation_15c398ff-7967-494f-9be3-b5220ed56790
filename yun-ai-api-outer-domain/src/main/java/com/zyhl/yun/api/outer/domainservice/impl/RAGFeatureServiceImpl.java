package com.zyhl.yun.api.outer.domainservice.impl;

import com.zyhl.hcy.plugin.redis.HcyRedisTemplate;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.TextFeatureClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.vo.TextFeatureExtractVO;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiRegisterEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmBusinessGroupEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmConfigEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.AlgorithmKnowledgeConfigEntity;
import com.zyhl.yun.api.outer.domainservice.RAGFeatureService;
import com.zyhl.yun.api.outer.enums.AIModuleEnum;
import com.zyhl.yun.api.outer.enums.BusinessSourceEnum;
import com.zyhl.yun.api.outer.repository.AlgorithmBusinessGroupRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmConfigRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmKnowledgeConfigRepository;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;

import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * RAG向量化-ServiceImpl
 *
 * @Author: WeiJingKun
 */
@Slf4j
@Service
public class RAGFeatureServiceImpl implements RAGFeatureService {

    @Resource
    private AlgorithmKnowledgeConfigRepository knowledgeConfigRepository;

    @Resource
    private AlgorithmBusinessGroupRepository businessGroupRepository;

    @Resource
    private AlgorithmConfigRepository configRepository;

    @Resource
    private TextFeatureClient textFeatureClient;

    @Resource
    private HcyRedisTemplate<String, Object> hcyRedisTemplate;
    @Resource
    private RedisOperateRepository redisOperateRepository;

    @Override
    public TextFeatureExtractVO getTextFeatureCommonKnowledgeHandler(String baseId, String rewriteQuery, Long dialogueId) {
        return getTextFeatureCommonKnowledgeHandler(baseId, rewriteQuery, dialogueId + "");
    }

    @Override
    public TextFeatureExtractVO getTextFeatureCommonKnowledgeHandler(String baseId, String rewriteQuery, String dialogueId) {
        /** 查询缓存 */
        String cacheKey = String.format(RedisConstants.RAG_TEXT_FEATURE_COMMON_KNOWLEDGE_CACHE_KEY, baseId);
        String algorithmUrl = (String) hcyRedisTemplate.opsForValue().get(cacheKey);

        try {
            /** 缓存为null */
            if (CharSequenceUtil.isBlank(algorithmUrl)) {
                /** 根据知识库的标识，获取公共知识库配置 */
                AlgorithmKnowledgeConfigEntity knowledgeConfig = knowledgeConfigRepository.queryById(baseId);
                if (null == knowledgeConfig) {
                    log.warn("公共知识库不存在，知识库id：{}", baseId);
                    return null;
                }
                log.info("公共知识库配置：{}", knowledgeConfig);

                /** 根据算法组编码，获取算法业务组 */
                AlgorithmKnowledgeConfigEntity.ConfigDTO configDTO = knowledgeConfig.getConfigDTO();
                if (null == configDTO || null == configDTO.getAlgorithmGroupCode()) {
                    log.warn("RAGFeatureServiceImpl-getTextFeatureHandler-算法组编码为空");
                    return null;
                }
                AlgorithmBusinessGroupEntity businessGroup = businessGroupRepository.queryByAlgorithmGroupCode(configDTO.getAlgorithmGroupCode());
                if (null == businessGroup) {
                    log.warn("业务算法组不存在，算法编码：{}", configDTO.getAlgorithmGroupCode());
                    return null;
                }
                log.info("RAGFeatureServiceImpl-getTextFeatureHandler-算法业务组：{}", businessGroup);

                /** 根据算法id，获取算法配置 */
                String algorithmIds = businessGroup.getAlgorithmIds();
                if (CharSequenceUtil.isBlank(algorithmIds)) {
                    log.warn("RAGFeatureServiceImpl-getTextFeatureHandler-算法id为空");
                    return null;
                }
                Long algorithmId = Long.valueOf(algorithmIds.split(",")[0]);
                AlgorithmConfigEntity config = configRepository.queryByAlgorithmId(algorithmId);
                if (null == config) {
                    log.warn("RAGFeatureServiceImpl-getTextFeatureHandler-算法配置为空");
                    return null;
                }
                log.info("RAGFeatureServiceImpl-getTextFeatureHandler-算法配置：{}", config);

                /** 通过接口地址判断调用哪个向量化接口 */
                algorithmUrl = config.getAlgorithmUrl();
                if (CharSequenceUtil.isBlank(algorithmUrl)) {
                    log.warn("RAGFeatureServiceImpl-getTextFeatureHandler-算法接口地址为空");
                    return null;
                }
                /** 存入缓存（5分钟） */
                hcyRedisTemplate.opsForValue().set(cacheKey, algorithmUrl, 5, TimeUnit.MINUTES);
            }
            return textFeatureClient.getTextFeature(algorithmUrl, rewriteQuery, dialogueId);
        } catch (Exception e) {
            log.error("文本向量化失败，向量化地址：{}\n 异常信息：", algorithmUrl, e);
        }
        return null;
    }

    @Override
    public TextFeatureExtractVO getTextFeature(String userId, String text, Long dialogueId) {
        // 查缓存
        String featureUrl = redisOperateRepository.getFeatureUrl();
        try {
            if (CharSequenceUtil.isBlank(featureUrl)) {
                // 获取算法组编码
                final Integer businessType = BusinessSourceEnum.ASSISTANT.getCode();
                final Integer module = AIModuleEnum.AI_FILE_LIBRARY.getModule();
                AlgorithmAiRegisterEntity redisEntity = redisOperateRepository.getRegisterEntity(userId, businessType, module);

                // 业务算法组配置
                AlgorithmBusinessGroupEntity businessGroup = businessGroupRepository.queryByAlgorithmGroupCode(redisEntity.getAlgorithmGroupCode());
                if (null == businessGroup) {
                    log.warn("【文本向量化】业务算法组不存在，算法编码：{}", redisEntity.getAlgorithmGroupCode());
                    return null;
                }

                // 算法id
                if (CharSequenceUtil.isBlank(businessGroup.getAlgorithmIds())) {
                    log.warn("【文本向量化】算法id为空");
                    return null;
                }

                // 算法配置
                List<Long> idList = Arrays.stream(businessGroup.getAlgorithmIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
                List<AlgorithmConfigEntity> entityList = configRepository.getByAlgorithmIds(idList);
                if (CollUtil.isEmpty(entityList)) {
                    log.warn("【文本向量化】算法配置为空");
                    return null;
                }

                // 向量化地址
                featureUrl = entityList.get(0).getAlgorithmUrl();
                if (CharSequenceUtil.isBlank(featureUrl)) {
                    log.warn("【文本向量化】算法接口地址为空");
                    return null;
                }

                // 缓存
                redisOperateRepository.setFeatureUrl(featureUrl);
            }

            // 文本向量化
            return textFeatureClient.getTextFeature(featureUrl, text, String.valueOf(dialogueId));
        } catch (Exception e) {
            log.error("【文本向量化】文本向量化失败，向量化地址：{}； 异常信息：{}", featureUrl, e.getMessage(), e);
        }
        return null;
    }

}
