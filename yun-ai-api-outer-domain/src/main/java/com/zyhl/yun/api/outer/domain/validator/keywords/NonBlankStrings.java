package com.zyhl.yun.api.outer.domain.validator.keywords;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 非空字符串校验-注解
 * <AUTHOR>
 */
@Documented
@Constraint(validatedBy = NonBlankStringValidator.class)
@Target({ ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.PARAMETER })
@Retention(RetentionPolicy.RUNTIME)
public @interface NonBlankStrings {
    String message() default "列表中不能含有空字符串";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
