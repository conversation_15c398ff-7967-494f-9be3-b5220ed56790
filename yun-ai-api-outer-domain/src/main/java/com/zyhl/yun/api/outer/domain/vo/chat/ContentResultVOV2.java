package com.zyhl.yun.api.outer.domain.vo.chat;

import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 对话结果-VO-V2
 * @Author: WeiJ<PERSON><PERSON><PERSON>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContentResultVOV2 extends ContentResultVO implements Serializable {

    private static final long serialVersionUID = 4198771509444713110L;

    /** 大模型联网搜索结果 */
    private List<AiTextResultRespParameters.NetworkSearchInfo> networkSearchInfoList;

    /** 思维链过程 */
    private String reasoningContent;

}
