package com.zyhl.yun.api.outer.config;

import com.zyhl.yun.api.outer.util.VersionUtil;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 干扰库配置
 *
 * <AUTHOR>
 */
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "intervention")
public class InterventionProperties {

	/**
	 * 默认纯文本版本号
	 */
	public static final String DEFAULT_TEXT_VERSION = "0";
	
	/**
	 * 默认答案版本，每次升级，需要配置默认版本
	 */
	private String defaultAnswerVersion = "1";

	/**
	 * 答案匹配配置
	 */
	private List<MatchAnswer> matchAnswers;

	/**
	 * 渠道配置，不配置的渠道默认开启该功能
	 */
	private List<InterventionChannel> channelList;

	/**
	 * 符合条件的最小分数（默认1.5）
	 */
	private Double minScore = 1.5D;

	/**
	 * 符合条件的最小匹配度（默认75%）
	 */
	private String minimumShouldMatch = "75%";

	/**
	 * 判断渠道是否开启
	 * 
	 * @param channel 渠道
	 * @return 是否开启
	 */
	public boolean isOpen(String channel) {
		if (StringUtils.isBlank(channel) || CollectionUtils.isEmpty(this.getChannelList())) {
			return true;
		}
		for (InterventionChannel tempChannel : this.getChannelList()) {
			if (channel.equals(tempChannel.getChannel())) {
				return tempChannel.isOpen();
			}
		}
		// 默认开启
		return true;
	}

	public String getAnswerVersion(String channel, String clientType, String clientVersion, String h5Version) {
		if (!CollectionUtils.isEmpty(this.getMatchAnswers())) {
			for (MatchAnswer matchAnswer : this.getMatchAnswers()) {
				if (CollectionUtils.isEmpty(matchAnswer.getAnyChannels())
						&& CollectionUtils.isEmpty(matchAnswer.getAnyClientTypes())
						&& null == matchAnswer.getAnyVersion()) {
					// 3个对象列表为空，不匹配
					continue;
				}
				if (!CollectionUtils.isEmpty(matchAnswer.getAnyChannels())
						&& !matchAnswer.getAnyChannels().contains(channel)) {
					// 存在一个不符合则不匹配
					continue;
				}
				if (!CollectionUtils.isEmpty(matchAnswer.getAnyClientTypes())
						&& !matchAnswer.getAnyClientTypes().contains(clientType)) {
					// 存在一个不符合则不匹配
					continue;
				}
				if (null != matchAnswer.getAnyVersion() && !matchAnswer.matchVersion(clientVersion, h5Version)) {
					// 存在一个不符合则不匹配
					continue;
				}
				// 3个条件匹配，直接返回：执行到最后则匹配到
				return matchAnswer.getAnswerVersion();
			}
		}
		// 返回默认版本
		return this.getDefaultAnswerVersion();
	}

	/**
	 * 干扰库配置渠道开关
	 * 
	 */
	@Data
	public static class InterventionChannel {

		private String channel;

		private boolean open = true;

	}

	/**
	 * 干扰库配置：渠道-客户端-版本
	 * 
	 */
	@Data
	public static class MatchAnswer {

		private String answerVersion;

		/**
		 * 任意其中一个渠道号，3个anyXXX要同时匹配，空的情况不匹配也是true【注：3个对象列表为空，不匹配】
		 */
		private List<String> anyChannels;
		/**
		 * 任意其中一个客户端类型，3个anyXXX要同时匹配，空的情况不匹配也是true【注：3个对象列表为空，不匹配】
		 */
		private List<String> anyClientTypes;
		/**
		 * 任意版本类型，3个anyXXX要同时匹配，空的情况不匹配也是true【注：3个对象列表为空，不匹配】
		 */
		private MatchAnswerAnyVersion anyVersion;

		public boolean matchVersion(String clientVersion, String h5Version) {
			final String eqMode = "eq";
			final String ltMode = "lt";
			final String lteMode = "lte";
			final String gtMode = "gt";
			final String gteMode = "gte";
			String mode = this.getAnyVersion().getMode();
			String mclientVersion = this.getAnyVersion().getClientVersion();
			String mh5Version = this.getAnyVersion().getH5Version();
			if (StringUtils.isNotBlank(mclientVersion) && StringUtils.isNotBlank(mh5Version)) {
				// version都不为空
				switch (mode) {
				case eqMode:
					return (mclientVersion.equals(clientVersion) && mh5Version.equals(h5Version));
				case ltMode:
					return (compareVersion(clientVersion, mclientVersion) < 0
							&& compareVersion(h5Version, mh5Version) < 0);
				case lteMode:
					return (compareVersion(clientVersion, mclientVersion) <= 0
							&& compareVersion(h5Version, mh5Version) <= 0);
				case gtMode:
					return (compareVersion(clientVersion, mclientVersion) > 0
							&& compareVersion(h5Version, mh5Version) > 0);
				case gteMode:
					return (compareVersion(clientVersion, mclientVersion) >= 0
							&& compareVersion(h5Version, mh5Version) >= 0);
				default:
					break;
				}
			} else if (StringUtils.isBlank(mclientVersion) && StringUtils.isNotBlank(mh5Version)) {
				// mclientVersion为空,mh5Version不为空
				switch (mode) {
				case eqMode:
					return mh5Version.equals(h5Version);
				case ltMode:
					return (compareVersion(h5Version, mh5Version) < 0);
				case lteMode:
					return (compareVersion(h5Version, mh5Version) <= 0);
				case gtMode:
					return (compareVersion(h5Version, mh5Version) > 0);
				case gteMode:
					return (compareVersion(h5Version, mh5Version) >= 0);
				default:
					break;
				}
			} else if (StringUtils.isNotBlank(mclientVersion) && StringUtils.isBlank(mh5Version)) {
				// mclientVersion不为空,mh5Version为空
				switch (mode) {
				case eqMode:
					return (mclientVersion.equals(clientVersion));
				case ltMode:
					return (compareVersion(clientVersion, mclientVersion) < 0);
				case lteMode:
					return (compareVersion(clientVersion, mclientVersion) <= 0);
				case gtMode:
					return (compareVersion(clientVersion, mclientVersion) > 0);
				case gteMode:
					return (compareVersion(clientVersion, mclientVersion) >= 0);
				default:
					break;
				}
			}
			// 默认不匹配
			return false;
		}

		/**
		 * 匹配版本，version1和version2对比
		 */
		private int compareVersion(String version1, String version2) {
			return VersionUtil.compareVersion(version1, version2);
		}
	}

	/**
	 * 干扰库配置：渠道-客户端-版本
	 * 
	 */
	@Data
	public static class MatchAnswerAnyVersion {

		/**
		 * 同时匹配clientVersion和h5Version，空则默认匹配【2个同时空，不匹配】
		 */
		private String clientVersion;
		/**
		 * 同时匹配clientVersion和h5Version，空则默认匹配【2个同时空，不匹配】
		 */
		private String h5Version;

		/**
		 * 模式，eq为=，lt为小于，，lte为小于等于，gt为大于，，gte为大于等于
		 */
		private String mode;

	}

}
