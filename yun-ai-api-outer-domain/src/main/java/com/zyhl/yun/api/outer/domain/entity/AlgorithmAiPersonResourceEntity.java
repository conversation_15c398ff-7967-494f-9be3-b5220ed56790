package com.zyhl.yun.api.outer.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI人物关系资源entity
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AlgorithmAiPersonResourceEntity {

	/**
	 * 人名
	 */
	private String name;

	/**
	 * 关系
	 */
	private String relationship;

	/**
	 * 资源名称
	 */
	private String resourceName;

}