package com.zyhl.yun.api.outer.domain.entity;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 图片配文实体
 * <AUTHOR>
 */
@Data
public class ImageCaptionEntity {

    /**
     *    请求 id
     */
    private String requestId;

    /**
     * 配文列表
     */
    private List<ImageCaptionInfoEntity> captionInfoList;


    @Data
    @NoArgsConstructor
    public static class ImageCaptionInfoEntity {

        /**
         * 分数
         */
        private Double score;

        /**
         * 配文信息
         */
        private String text;

    }
}
