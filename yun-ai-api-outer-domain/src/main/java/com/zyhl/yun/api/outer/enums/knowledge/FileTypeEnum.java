package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文件类型映射关系，知识库文件类型 与 个人云文件类型映射关系
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FileTypeEnum {

    /**
     * 文件
     */
    FILE(1, "file"),

    /**
     * 文件夹
     */
    FOLDER(2, "folder"),
    ;

    /**
     * 知识库文件类型
     */
    private final Integer knowledgeFileType;
    /**
     * 个人云文件类型
     */
    private final String yunDiskFileType;



    public static FileTypeEnum getByKnowledgeFileType(Integer knowledgeFileType) {
        for (FileTypeEnum value : FileTypeEnum.values()) {
            if (value.getKnowledgeFileType().equals(knowledgeFileType)) {
                return value;
            }
        }
        return null;
    }

    public static FileTypeEnum getByYunDiskFileType(String yunDiskFileType) {
        for (FileTypeEnum value : FileTypeEnum.values()) {
            if (value.getYunDiskFileType().equals(yunDiskFileType)) {
                return value;
            }
        }
        return null;
    }

    public static String getYunDiskFileType(Integer knowledgeFileType) {
        FileTypeEnum type = getByKnowledgeFileType(knowledgeFileType);
        return type == null ? "" : type.getYunDiskFileType();
    }

    public static Integer getKnowledgeFileType(String yunDiskFileType) {
        FileTypeEnum type = getByYunDiskFileType(yunDiskFileType);
        return type == null ? 0 : type.getKnowledgeFileType();
    }

    public static boolean isFile(Integer knowledgeFileType) {
        return FILE.knowledgeFileType.equals(knowledgeFileType);
    }

    public static boolean isFolder(Integer knowledgeFileType) {
        return FOLDER.knowledgeFileType.equals(knowledgeFileType);
    }

}
