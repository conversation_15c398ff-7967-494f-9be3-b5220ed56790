package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识库AI扩展范围枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AiExpansionRangeEnum {

    /**
     * 第一区间 1000以下
     */
    FIRST(AiExpansionInteractiveEnum.SEARCHING, "第一区间"),
    /**
     * 第二区间 1000-3000
     */
    SECOND(AiExpansionInteractiveEnum.MODEL_SWITCH, "第二区间"),
    /**
     * 第三区间 3000-6000
     */
    THIRD(AiExpansionInteractiveEnum.MODEL_SWITCH, "第三区间"),
    /**
     * 第四区间 6000以上
     */
    FOURTH(AiExpansionInteractiveEnum.MODEL_SWITCH, "第四区间");

    /**
     * 知识库AI扩展与前端交互枚举
     */
    private final AiExpansionInteractiveEnum codeEnum;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 判断该区间是否超过第一个区间
     *
     * @return true 表示超过；false 表示未超过
     */
    public static boolean isBeyondFirstRange(AiExpansionRangeEnum rangeEnum) {
        return !AiExpansionRangeEnum.FIRST.equals(rangeEnum);
    }

    /**
     * 判断当前区间是否为第三或第四区间
     *
     * @param rangeEnum 区间枚举
     * @return true 表示是 THIRD 或 FOURTH；false 表示是 FIRST 或 SECOND
     */
    public static boolean isThirdOrFourthRange(AiExpansionRangeEnum rangeEnum) {
        return AiExpansionRangeEnum.THIRD.equals(rangeEnum) || AiExpansionRangeEnum.FOURTH.equals(rangeEnum);
    }
}
