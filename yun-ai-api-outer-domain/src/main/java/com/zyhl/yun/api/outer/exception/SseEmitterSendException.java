package com.zyhl.yun.api.outer.exception;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.exception.BusinessException;
import lombok.Data;

/**
 * 描述：流式发送异常
 *
 * <AUTHOR> zhumaoxian  2025/4/9 22:43
 */
@Data
public class SseEmitterSendException extends BusinessException {

    private AbstractResultCode exceptionEnum;

    public SseEmitterSendException(String code, String message) {
        super(code, message);
    }

    public SseEmitterSendException(AbstractResultCode abstractResultCode) {
        super(abstractResultCode.getResultCode(), abstractResultCode.getResultMsg());
        this.exceptionEnum = abstractResultCode;
    }
}
