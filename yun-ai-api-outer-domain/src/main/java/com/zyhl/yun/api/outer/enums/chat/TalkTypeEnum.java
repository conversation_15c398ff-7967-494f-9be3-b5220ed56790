package com.zyhl.yun.api.outer.enums.chat;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.Getter;

/**
 * 对话类型
 * <AUTHOR>
 */
@Getter
public enum TalkTypeEnum {

    /**
     * 对话
     */
    DIALOG(0, "对话"),
    
    /**
     * 智囊
     */
    BRAINPOWER(1, "智囊"),
    
    /**
     * 任务对话
     */
    TASK(2, "任务对话"),
    ;


    private static final Map<Integer, TalkTypeEnum> MAP = new ConcurrentHashMap<>();


    static {
        EnumSet.allOf(TalkTypeEnum.class).forEach(item -> MAP.put(item.type, item));
    }

    public static TalkTypeEnum getType(Integer type) {
        if (null == type) {
            return null;
        }
        return MAP.get(type);
    }

    public static boolean isExist(Integer type) {
        return getType(type) != null;
    }

    /**
     * 是对话类型
     *
     * @param type 类型
     * @return true-是对话类型
     */
    public static boolean isDialogue(Integer type) {
        return DIALOG.type.equals(type);
    }

    public static boolean isNotDialogue(Integer type) {
        return !isDialogue(type);
    }

    /**
     * 是智囊类型
     *
     * @param type 类型
     * @return true-是智囊类型
     */
    public static boolean isBrainpower(Integer type) {
        return BRAINPOWER.type.equals(type);
    }
    
    /**
     * 是任务对话类型
     *
     * @param type 类型
     * @return true-是任务对话
     */
    public static boolean isTask(Integer type) {
        return TASK.type.equals(type);
    }


    TalkTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    private final Integer type;

    private final String name;

}
