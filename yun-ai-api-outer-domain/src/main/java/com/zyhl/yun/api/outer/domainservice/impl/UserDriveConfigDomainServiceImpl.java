package com.zyhl.yun.api.outer.domainservice.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.domain.dto.CatalogConfigDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserDriveConfigEntity;
import com.zyhl.yun.api.outer.domainservice.UserDriveConfigDomainService;
import com.zyhl.yun.api.outer.external.UserDriveExternalService;
import com.zyhl.yun.api.outer.repository.UserDriveConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 用户独立空间配置领域服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserDriveConfigDomainServiceImpl implements UserDriveConfigDomainService {

    @Resource
    private UserDriveConfigRepository userDriveConfigRepository;
    @Resource
    private UserDriveExternalService userDriveExternalService;


    @Override
    public UserDriveConfigEntity createDrive(String userId, Integer belongsPlatform) {
        UserDriveConfigEntity entity = userDriveConfigRepository.get(userId);
        if (entity == null) {
            // 创建独立空间并
            userDriveExternalService.getUserDriveId(userId);

            // 保存记录
            entity = new UserDriveConfigEntity();
            entity.setUserId(userId);
            entity.setPaasCode(String.valueOf(belongsPlatform));

            userDriveConfigRepository.add(entity);
        }

        return entity;
    }


    @Override
    public CatalogConfigDTO.CatalogConfigInfo createKnowledgeConfig(UserDriveConfigEntity entity) {
        CatalogConfigDTO dto = null;
        String name = "个人知识库";
        String path = "个人知识库";

        // 目录配置为空
        if (CharSequenceUtil.isEmpty(entity.getParentFileConfig())) {
            CatalogConfigDTO.CatalogConfigInfo info = new CatalogConfigDTO.CatalogConfigInfo();
            info.setName(name);
            info.setPath(path);
            info.setCatalogId(userDriveExternalService.createCatalog(entity.getUserId(), path));

            dto = new CatalogConfigDTO();
            dto.setKnowledge(info);

            entity.setParentFileConfig(JsonUtil.toJson(dto));
            userDriveConfigRepository.update(entity);

            return info;
        }

        // 目录配置
        dto = JsonUtil.parseObject(entity.getParentFileConfig(), CatalogConfigDTO.class);
        if (dto.getKnowledge() == null) {
            CatalogConfigDTO.CatalogConfigInfo info = new CatalogConfigDTO.CatalogConfigInfo();
            info.setName(name);
            info.setPath(path);
            info.setCatalogId(userDriveExternalService.createCatalog(entity.getUserId(), path));

            dto.setKnowledge(info);

            entity.setParentFileConfig(JsonUtil.toJson(dto));
            userDriveConfigRepository.update(entity);

            return info;
        }

        return dto.getKnowledge();

    }

    @Override
    public String createCatalog(String userId, String name, String parentId) {
        return userDriveExternalService.createCatalog(userId, name, parentId).getFileId();
    }

    @Override
    public CatalogConfigDTO.CatalogConfigInfo knowledgeConfig(String userId, String name) {
        UserDriveConfigEntity entity = userDriveConfigRepository.get(userId);
        // 目录要一级一级创建
        String path = "个人知识库"; 

        // 创建第一级目录
        CatalogConfigDTO.CatalogConfigInfo info = new CatalogConfigDTO.CatalogConfigInfo();
        CatalogConfigDTO dto = new CatalogConfigDTO();
        info.setName(path);
        info.setPath(path);
        info.setCatalogId(userDriveExternalService.createCatalog(userId, path));
        dto.setKnowledge(info);
        if (null != entity) {
            entity.setParentFileConfig(JsonUtil.toJson(dto));
            userDriveConfigRepository.update(entity);
        }

        //创建第二级目录
        info.setCatalogId(userDriveExternalService.createCatalog(userId, name, info.getCatalogId()).getFileId());

        return info;
    }

}
