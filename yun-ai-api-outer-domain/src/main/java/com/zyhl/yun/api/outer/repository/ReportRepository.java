package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.ReportInfoEntity;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.repository.ReportRepository} <br>
 * <b> description:</b>
 * 举报信息仓库接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-12-28 16:39
 **/
public interface ReportRepository {

    /**
     * 保存举报信息
     *
     * @param entity entity
     * @return int
     */
    Integer save(ReportInfoEntity entity);
}
