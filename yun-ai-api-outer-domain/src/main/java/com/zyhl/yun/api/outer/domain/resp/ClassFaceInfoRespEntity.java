package com.zyhl.yun.api.outer.domain.resp;

import lombok.Data;

/**
 * 人脸识别返回结果
 *
 * <AUTHOR>
 */
@Data
public class ClassFaceInfoRespEntity {

    /**
     * 所属图片ID。
     * 该字段存储人脸所属图片的唯一标识符。
     */
    private String fileId;

    /**
     * 人脸大头贴左上角点横坐标。
     * 该字段存储人脸大头贴左上角点的横坐标。
     */
    private Float x0;

    /**
     * 人脸大头贴左上角点纵坐标。
     * 该字段存储人脸大头贴左上角点的纵坐标。
     */
    private Float y0;

    /**
     * 人脸大头贴右下角点横坐标。
     * 该字段存储人脸大头贴右下角点的横坐标。
     */
    private Float x1;

    /**
     * 人脸大头贴右下角点纵坐标。
     * 该字段存储人脸大头贴右下角点的纵坐标。
     */
    private Float y1;

    /**
     * 是否正脸。
     * 该字段指示人脸是否为正脸，具体值包括：
     * 0 - 否
     * 1 - 是
     */
    private Integer isNormalFace;


}