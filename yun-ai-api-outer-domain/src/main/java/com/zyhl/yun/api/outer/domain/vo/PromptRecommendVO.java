package com.zyhl.yun.api.outer.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/7/16 14:00
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PromptRecommendVO implements Serializable {

    /**
     * 提示词编码
     */
    private String code;
    /**
     * 提示词名称
     */
    private String name;
    /**
     * 意图指令
     */
    private String intentionCommand;

    /**
     * 业务类型
     */
    @JsonIgnore
    private String businessType;
}
