package com.zyhl.yun.api.outer.domainservice;

import java.util.concurrent.Future;

import com.zyhl.yun.api.outer.domain.vo.SearchReturnTermsVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;

/**
 * 搜索返回词服务类
 * <AUTHOR>
 * @date 2024-10-15
 */
public interface SearchReturnTermsService {

	/**
	 * 异步调用大模型生成搜索返回词V1
	 * @Author: WeiJingKun
	 * @param dialogueId 对话id
	 * @param dialogue 对话内容
	 * @return 搜索返回词的Future
	 */
	Future<String> getOptimizeReturnTermsFutureV1(Long dialogueId, String dialogue);

	/**
	 * 获取搜索返回词V1（异常时返回默认值）
	 * @Author: WeiJingKun
	 * @param searchReturnTermsFutureV1 生成搜索返回词的Future
	 * @param intention 对话意图
	 * @param dialogueId 对话id
	 * @param dialogue 对话内容
	 * @return 搜索结果的标题
	 */
	String getSearchReturnTermsV1(Future<String> searchReturnTermsFutureV1, String intention, Long dialogueId, String dialogue);

	/**
	 * TODO【无实际业务引用，如有需要，请先读懂代码后再处理】
	 * 生成搜索结果的标题V2
	 * 1、根据意图识别的结果中实体的搜索关键词
	 * 2、从用户输入的文本中，根据意图实体的搜索关键词，提取最开始、最未尾位置
	 * 3、根据最开始，最未尾的位置，提取开始到结束的文本内容
	 * 4、将文本内容添加到多意图推荐提示词模板中，并将其赋值为搜索结果标题
	 * 5、例如：帮我搜索中秋节广州白云山的图片，意图识别的结果中实体的关键词为：中秋节、广州、白云山
	 * 	5.1、根据词提取最开始，最未的位置为3、12，根据位置提取到中秋节广州白云山
	 * 	5.2、根据提示词模板拼接为：为您找到云盘内“中秋节广州白云山”的图片
	 * @Author: WeiJingKun
	 * @param intention 对话意图
	 * @param dialogueId 对话id
	 * @param dialogue 对话内容
	 * @param firstIntentionInfo 第一个意图结果
	 * @return 搜索返回词的Future
	 */
	@Deprecated
	Future<SearchReturnTermsVO> getOptimizeReturnTermsFutureV2(String intention, String dialogueId, String dialogue, DialogueIntentionVO.IntentionInfo firstIntentionInfo);

	/**
	 * TODO【无实际业务引用，如有需要，请先读懂代码后再处理】
	 * 获取搜索返回词V2（异常时返回默认值）
	 * @Author: WeiJingKun
	 * @param searchReturnTermsFutureV2 生成搜索返回词的Future
	 * @param intention 对话意图
	 * @param dialogueId 对话id
	 * @param dialogue 对话内容
	 * @return 搜索结果的标题
	 */
	@Deprecated
	String getSearchReturnTermsV2(Future<SearchReturnTermsVO> searchReturnTermsFutureV2, String intention, String dialogueId, String dialogue);

}
