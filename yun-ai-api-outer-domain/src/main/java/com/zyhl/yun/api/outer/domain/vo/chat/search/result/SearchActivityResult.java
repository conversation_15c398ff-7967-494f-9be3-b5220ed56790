package com.zyhl.yun.api.outer.domain.vo.chat.search.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 对话信息-搜索结果-活动
 * @Author: WeiJingK<PERSON>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchActivityResult extends SearchCommonResult implements Serializable {

    /** 活动信息 */
    private List<SearchActivity> activityList;

    /** 下一页起始资源标识符，最后一页该值为空 */
    private List<Object> pageAfter;

    /** 记录总数 */
    private Integer totalCount;

}
