package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 解析状态枚举
 * @date 2025/4/29 11:44
 */
@Getter
@AllArgsConstructor
public enum FileAiStatusParamEnum {


    FAIL(-1, "处理失败", 2, "数据库失败状态"),

    PROCESSING(0, "处理中", 0, "数据库处理中状态"),

    SUCCESS(1, "处理成功", 1, "数据库成功状态"),

    ;

    /**
     * 前端的状态
     */
    private final Integer code;

    private final String description;

    /**
     * 数据库的状态
     */
    private final Integer aiStatus;

    private final String remark;

    /**
     * 获取所有 code 的列表
     *
     * @return 包含所有 code 的列表
     */
    public static List<Integer> getCodeList() {
        return Arrays.stream(FileAiStatusParamEnum.values())
                .map(FileAiStatusParamEnum::getCode)
                .collect(Collectors.toList());
    }

    /**
     * 是否存在此枚举code
     *
     * @param code 传入code
     * @return 是否存在
     */
    public static boolean isExist(Integer code) {
        return getCodeList().contains(code);
    }

    public static Integer getCodeByAiStatus(Integer aiStatus) {
        for (FileAiStatusParamEnum value : FileAiStatusParamEnum.values()) {
            if (value.getAiStatus().equals(aiStatus)) {
                return value.getAiStatus();
            }
        }
        return null;
    }
}
