package com.zyhl.yun.api.outer.constants;

/**
 * redis常量Constants层
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
public class RedisConstants {

    /**
     * 等待时间1秒
     */
    public static final long WAIT_TIME_1 = 1;
    /**
     * 等待时间3秒
     */
    public static final long WAIT_TIME_3 = 3;
    /**
     * 等待时间5秒
     */
    public static final long WAIT_TIME_5 = 5;
    /**
     * 等待时间10秒
     */
    public static final long WAIT_TIME_10 = 10;

    /**
     * 租期5秒
     */
    public static final long LEASE_TIME_5 = 5;
    /**
     * 租期10秒
     */
    public static final long LEASE_TIME_10 = 10;
    /**
     * 租期15秒
     */
    public static final long LEASE_TIME_15 = 15;
    /**
     * 租期20秒
     */
    public static final long LEASE_TIME_20 = 20;
    /**
     * 租期25秒
     */
    public static final long LEASE_TIME_25 = 25;
    /**
     * 租期30秒
     */
    public static final long LEASE_TIME_30 = 30;

    /**
     * 超时时间10秒
     */
    public static final long TIMEOUT_10 = 10;
    /**
     * 超时时间30秒
     */
    public static final long TIMEOUT_30 = 30;
    /**
     * 超时时间60秒
     */
    public static final long TIMEOUT_60 = 60;


    public static final String AUTHORIZATION = "Authorization";

    /**
     * 用户鉴权信息key
     */
    public static final String AUTHORIZATION_PREFIX = "ai:outer:authorization:";

    public static final Long AUTHORIZATION_TIMEOUT = 300L;

    /**
     * 有效时间
     */
    public static final Long AUTHORIZATION_TIME_OUT = 3L;

    /**
     * 时光轴收录范围列表用户id映射
     */
    public static final String AI_AUTHORIZATION_USER_ID = "album:saas:manage:authorization:userId:";

    /**
     * redis 路由策略缓存
     */
    public static final String ROUTE_KEY = "album:saas:manage:route:userId:";

    public static final Long ROUTE_KEY_TIMEOUT = 3600L;

    public static final String ROUTE_VALUE_KEY = "personal,syncDisk";

    /**
     * outer服务redis的key前缀
     */
    public static final String OUTER_PREFIX = "yun:ai:api:outer:";

    /**
     * AI报名缓存，%s为用户id、业务类型、模块
     */
    public static final String AI_REGISTER = OUTER_PREFIX + "ai:register:%s:%s:%s";

    /**
     * QPSKey 因为异步算法任务调用厂商也限流，所以这里用相同的key保证令牌桶是共用的
     */
    public static final String QPS_PREFIX = "yun:ai:center:aimanage:";

    /**
     * 会员权益缓存，两个参数：会员id、对话id
     */
    public static final String MEMBER_BENEFIT = OUTER_PREFIX + "member:benefit:%s:%s";

    /**
     * 消息sessionId缓存 key为userId+applicationId+businessType
     */
    public static final String MESSAGE_SESSION_ID = OUTER_PREFIX + "message:sessionId:%s:%s:%s";

    /**
     * 智能体-应用关联id缓存 key为applicationId
     */
    public static final String TYPE_RELATION_ID = OUTER_PREFIX + "typeRelationId:%s";

    /**
     * 智能体-应用信息缓存 key为applicationId
     */
    public static final String AGENT_CHAT_APPLICATION_ID = OUTER_PREFIX + "agentChatApplicationId:%s";

    /**
     * 数据迁移新增锁，参数：用户id
     */
    public static final String MIGRATION_LOCK = OUTER_PREFIX + "migration:lock:%s";

    /**
     * 评论对话锁，参数：对话id
     */
    public static final String COMMENT_LOCK = OUTER_PREFIX + "comment:lock:%s";

    /**
     * 停止对话标志，参数：对话id
     */
    public static final String STOP_DIALOGUE = OUTER_PREFIX + "stop:dialogue:%s";

    /**
     * 模型禁用标志，参数：模型code
     */
    public static final String MODEL_DISABLED = OUTER_PREFIX + "model:disabled:%s";


    /**
     * 提示词缓存key
     */
    public static final String CACHE_KEY_PROMPT_KEY = OUTER_PREFIX + "prompt:recommend:key";

    /**
     * 提示词缓存过期时间，预设30，避免缓存击穿，增加多10分钟进行异步更新
     */
    public static final int PROMPT_CACHE_EXPIRE_TIME = 40;

    /**
     * 提示词缓存逻辑过期剩余时间比对
     */
    public static final Long PROMPT_LOGIC_CACHE_EXPIRE_TIME = 600L;

    /**
     * 提示词缓存锁key
     */
    public static final String PROMPT_CACHE_LOCK_KEY = OUTER_PREFIX + "prompt:recommend:lock:key";

    /**
     * 知识库标签新增锁，参数：用户id
     */
    public static final String KNOWLEDGE_LABEL_LOCK = OUTER_PREFIX + "knowledge:label:lock:%s";

    /**
     * 知识库文件新增锁，参数：用户id
     */
    public static final String KNOWLEDGE_FILE_ADD_LOCK = OUTER_PREFIX + "knowledge:file:add:lock:%s";

    /**
     * 知识库文件删除锁，参数：用户id
     */
    public static final String KNOWLEDGE_FILE_DEL_LOCK = OUTER_PREFIX + "knowledge:file:del:lock:%s";

    /**
     * 知识库文件导入锁，参数：用户id
     */
    public static final String KNOWLEDGE_FILE_IMPORT_LOCK = OUTER_PREFIX + "knowledge:file:import:lock:%s";

    /**
     * 知识库文件重试锁，参数：用户id，任务id
     */
    public static final String KNOWLEDGE_FILE_RETRY_LOCK = OUTER_PREFIX + "knowledge:file:retry:lock:%s:%s";

    /**
     * 知识库文件移动锁，参数：用户id
     */
    public static final String KNOWLEDGE_FILE_MOVE_LOCK = OUTER_PREFIX + "knowledge:file:move:lock:%s";

    /**
     * 获取文本向量化（公共知识库）的cacheKey，参数：知识库的标识
     */
    public static final String RAG_TEXT_FEATURE_COMMON_KNOWLEDGE_CACHE_KEY = OUTER_PREFIX + "RAGFeatureServiceImpl:getTextFeatureCommonKnowledgeHandler:%s";

    /**
     * 用户知识库文件删除锁，参数：知识库id
     */
    public static final String USER_KNOWLEDGE_FILE_DEL_LOCK = OUTER_PREFIX + "user:knowledge:file:del:lock:%s";

    /**
     * 用户知识库可用状态缓存，参数：用户id
     */
    public static final String USER_KNOWLEDGE_EXIST_FILE_KEY = OUTER_PREFIX + "user:knowledge:exist:file:%s";

    /**
     * 公共知识库可用状态缓存，参数：公共知识库标识
     */
    public static final String COMMON_KNOWLEDGE_EXIST_FILE_KEY = OUTER_PREFIX + "common:knowledge:exist:file:%s";

    /**
     * 文本向量化的url
     */
    public static final String FEATURE_URL = OUTER_PREFIX + "feature:url";

    /**
     * 用户独立空间id，参数：用户id
     */
    public static final String USER_DRIVEID = OUTER_PREFIX + "user:driveId:%s";

    /**
     * 算法任务超时监控日志输出key，参数：任务id
     */
    public static final String EXPIRED_TASK_LOG_MONITOR = OUTER_PREFIX + "monitor:log:task:%s";

    /**
     * 历史对话列表缓存key，参数：会话id
     */
    public static final String AI_HISTORY_SESSION = OUTER_PREFIX + "ai:text:history:session:%s";

    /**
     * 缓存请求重放的url，参数：url，tid
     */
    public static final String URL_REQUEST_REPLAY = OUTER_PREFIX + "replay:%s:%s";
    /**
     * 缓存请求重放的url的锁，参数：url，tid
     */
    public static final String URL_REQUEST_REPLAY_LOCK = OUTER_PREFIX + "replay:lock:%s:%s";

    /**
     * 用户信息，参数：用户id
     */
    public static final String USER_INFO = OUTER_PREFIX + "user:info:%s";

    /**
     * 用户知识库召回次数缓存，参数：ES索引id
     */
    public static final String USER_KNOWLEDGE_RECALL_COUNT_KEY = OUTER_PREFIX + "user:knowledge:recall:count:%s";

    /**
     * 公共知识库召回次数缓存，参数：ES索引id
     */
    public static final String COMMON_KNOWLEDGE_RECALL_COUNT_KEY = OUTER_PREFIX + "common:knowledge:recall:count:%s";

    /**
     * AI全网搜——黑名单缓存key
     */
    public static final String BLACK_RESOURCE_CACHE_KEY = OUTER_PREFIX + "black:resource:key";

    /**
     * AI全网搜——黑名单缓存过期时间，预设30分钟
     */
    public static final int BLACK_RESOURCE_CACHE_EXPIRE_TIME = 60;

    /**
     * AI全网搜——黑名单缓存逻辑过期剩余时间比对
     */
    public static final Long BLACK_RESOURCE_LOGIC_CACHE_EXPIRE_TIME = 600L;

    /**
     * AI全网搜——黑名单分布式锁key
     */
    public static final String BLACK_RESOURCE_CACHE_LOCK_KEY = OUTER_PREFIX + "black:resource:lock:key";

    /**
     * 发现广场——人物关系资源缓存key
     */
    public static final String PERSON_RESOURCE_CACHE_KEY = OUTER_PREFIX + "person:resource:key";

    /**
     * 发现广场——人物关系资源过期时间，预设30分钟
     */
    public static final int PERSON_RESOURCE_CACHE_EXPIRE_TIME = 60;

    /**
     * 发现广场——人物关系资源缓存逻辑过期剩余时间比对
     */
    public static final Long PERSON_RESOURCE_LOGIC_CACHE_EXPIRE_TIME = 600L;

    /**
     * 发现广场——人物关系资源分布式锁key
     */
    public static final String PERSON_RESOURCE_CACHE_LOCK_KEY = OUTER_PREFIX + "person:resource:lock:key";

    /**
     * 断点续传缓存key，参数：对话id，分片序号
     */
    public static final String CONTINUE_TRANS_KEY = OUTER_PREFIX + "continue:trans:%d:%d";

    /**
     * 用户对话配置缓存key，参数：用户id
     */
    public static final String USER_DIALOGUE_CONFIG = OUTER_PREFIX + "user:dialogue:config:%s";
    
    /**
     * hbase对话更新锁，参数：对话id
     */
    public static final String UPDATE_HBASE_DIALOGUE_LOCK = OUTER_PREFIX + "hbase:dialogue:lock:%s";

    /**
     * 任务评价锁，参数：用户id,任务id
     */
    public static final String ALGORITHM_TASK_COMMENT = "yun:ai:api:outer:taskComment:lock:%s:%s";
}
