package com.zyhl.yun.api.outer.domain.vo.knowledge;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 个人知识库头像信息VO
 * @date 2025/4/16 17:32
 */
@Data
@NoArgsConstructor
public class PersonalKnowledgeProfilePhotoVO {

    /**
     * 头像类型
     * 1--预设
     * 2--个人云文件
     */
    private Integer type;

    /**
     * 头像ID
     * type=1时为预设的头像ID
     * type=2时为云盘文件ID
     */
    private String photoId;

    /**
     * 头像Url
     */
    private String url;
}
