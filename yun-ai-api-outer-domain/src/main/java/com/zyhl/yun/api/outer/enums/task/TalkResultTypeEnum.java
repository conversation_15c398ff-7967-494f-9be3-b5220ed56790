package com.zyhl.yun.api.outer.enums.task;

import lombok.Getter;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 任务结果类型
 *
 * <AUTHOR>
 */
@Getter
public enum TalkResultTypeEnum {

    /**
     * 图片
     */
    IMAGE(1, "图片"),

    /**
     * 文本
     */
    TEXT(2, "文本"),

    /**
     *  音频
     */
    AUDIO(3, "音频"),

    /**
     * 视频
     */
    VIDEO(4, "视频"),
    ;


    private static final Map<Integer, TalkResultTypeEnum> MAP = new ConcurrentHashMap<>();


    static {
        EnumSet.allOf(TalkResultTypeEnum.class).forEach(item -> MAP.put(item.type, item));
    }

    public static TalkResultTypeEnum getType(Integer type) {
        if (null == type) {
            return null;
        }
        return MAP.get(type);
    }

    public static boolean isExist(Integer type) {
        return getType(type) != null;
    }


    TalkResultTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 名称
     */
    private final String name;

}
