package com.zyhl.yun.api.outer.config.textmodel;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * 长文本模型配置类
 * 
 * <AUTHOR>
 * @date 2025-02-26 14:48
 */
@Configuration
@ConfigurationProperties(prefix = "text-model.long-text-chat")
@Data
public class LongTextModelConfig {
	private static final String VERSION_OF_QWEN_LONG = "qwenLong";
	private static final String VERSION_OF_USER_MODEL = "userModel";
	/**
	 * 使用长文本的版本（qwenLong：百炼qwen-long模型，userModel：按用户选择模型）
	 */
	private String version = VERSION_OF_USER_MODEL;

	/**
	 * 长文本解析按百炼qwen-long模型版本调用
	 * 
	 * @return
	 */
	public boolean isQwenLongVersion() {
		return VERSION_OF_QWEN_LONG.equals(this.getVersion());
	}

	/**
	 * 长文本解析按用户选择模型版本调用
	 * 
	 * @return
	 */
	public boolean isUserModelVersion() {
		return VERSION_OF_USER_MODEL.equals(this.getVersion());
	}
}
