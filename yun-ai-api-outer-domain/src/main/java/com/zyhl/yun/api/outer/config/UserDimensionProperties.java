package com.zyhl.yun.api.outer.config;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Objects;

/**
 * 用户维度信息配置类
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@RefreshScope
@Configuration
@ConfigurationProperties(
        prefix = "user.dimension"
)
@Data
public class UserDimensionProperties {

    private boolean enabled = true;

    /**
     * 按用户维度加入文件解析开关  true:入解析队列表  false:直接发消息
     *
     * @return 是否入解析队列
     */
    public boolean isEnabled() {
        return this.enabled;
    }

    public List<String> dimensionUserIds;

    public boolean isIsDimensionUser(String userId) {
        if (Objects.isNull(userId)) {
            return false;
        }
        boolean isDimensionUser = false;
        if (CollUtil.isNotEmpty(this.dimensionUserIds)) {
            isDimensionUser = this.dimensionUserIds.contains(userId);
        } else {
            isDimensionUser = true;
        }
        return isDimensionUser;
    }

}
