package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识库异步任务类型返回
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum KnowledgeTaskTypeResultEnum {

    /**
     * 文件移到文件夹
     */
    FILE_MOVE(2, "文件移到文件夹"),

    /**
     * 独立空间批量删除任务V2
     */
    FILE_DELETE(3, "独立空间批量删除任务V2"),
    ;

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 备注
     */
    private final String remark;

}
