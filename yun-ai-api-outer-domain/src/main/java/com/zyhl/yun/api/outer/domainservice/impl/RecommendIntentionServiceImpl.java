package com.zyhl.yun.api.outer.domainservice.impl;

import com.alibaba.fastjson.JSON;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.hcy.yun.ai.common.base.utils.FileBase64Util;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.hcy.yun.ai.common.model.api.client.vlmodel.ExternalVlModelClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.vo.TextModelBaseVo;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageVlDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelVlReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelVlContentTypeEnum;
import com.zyhl.hcy.yun.ai.common.model.api.utils.ImageProcessorUtil;
import com.zyhl.yun.api.outer.config.RecommendPromptTemplateProperties;
import com.zyhl.yun.api.outer.config.RecommendPromptTemplateProperties.TemplateConfig;
import com.zyhl.yun.api.outer.config.textmodel.VlModelConfig;
import com.zyhl.yun.api.outer.domain.req.LlmChatReqDTO;
import com.zyhl.yun.api.outer.domain.valueobject.llm.LlmChatMessage;
import com.zyhl.yun.api.outer.domain.vo.IntentionRecommendVO;
import com.zyhl.yun.api.outer.domainservice.CheckSystemDomainService;
import com.zyhl.yun.api.outer.domainservice.RecommendIntentionService;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.external.LlmChatExternalService;
import com.zyhl.yun.api.outer.util.JsonHandleUtils;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.RequestContextHolder.UserInfo;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.text.CharSequenceUtil;
import lombok.extern.slf4j.Slf4j;

import static com.zyhl.yun.api.outer.constants.Const.LEFT_BRACKET;

/**
 * 意图推荐方法接口实现类
 *
 * <AUTHOR>
 * @date 2024/7/17 16:28
 */
@Slf4j
@Service
public class RecommendIntentionServiceImpl implements RecommendIntentionService {

    @Resource
    private RecommendPromptTemplateProperties recommendPromptTemplateProperties;

    @Resource
    private LlmChatExternalService llmChatExternalService;

    @Resource
    private CheckSystemDomainService checkSystemDomainService;

    @Resource
    private ExternalVlModelClient externalVlModelClient;

    @Resource(name = "recommendIntentionThreadPool")
    private ExecutorService recommendIntentionThreadPool;

    /**
     * 异步获取意图推荐结果的未来值。
     * <p>
     * 通过此方法，可以在不阻塞当前线程的情况下，发起一个异步任务来计算基于对话和意图信息的推荐意图。 方法返回一个Future对象，通过该对象可以在后续时间点查询异步计算的结果。
     *
     * @param dialogue      用户的对话内容，用于意图识别和推荐。
     * @param intentionInfo 用户的意图信息，可能包含过去的意图历史或其他相关意图上下文。
     * @return 一个Future对象，代表异步计算的意图推荐结果。
     */
    @Override
    public Future<IntentionRecommendVO> getRecommendIntentionFuture(Long dialogueId, String dialogue,
                                                                    IntentionInfo intentionInfo) {
        if (StringUtils.isBlank(dialogue)) {
            return null;
        }
        if (null == intentionInfo) {
            return null;
        }
        String intention = intentionInfo.getIntention();
        // 获取提示词模板
        final TemplateConfig templateConfig = recommendPromptTemplateProperties.getTemplateConfig(intention);
        if (null == templateConfig) {
            log.error("CompletableFuture-supplyAsync dialogueId:{}, dialogue:{}, intention:{} 获取不到提示词模板配置", dialogueId,
                    dialogue, intention);
            return null;
        }
        final String userId = RequestContextHolder.getUserId();
        final UserInfo userInfo = RequestContextHolder.getUserInfo();
        try {
            final String tid = String.valueOf(MDC.get(LogConstants.TRACE_ID));
            final String serviceName = String.valueOf(MDC.get(LogConstants.SERVICE));
            // 这里并发异步执行获取意图推荐
            String clientType = RequestContextHolder.getClientType();
            return CompletableFuture.supplyAsync(() -> {
                RequestContextHolder.setUserInfo(userInfo);
                RequestContextHolder.setHeaderParams(new RequestContextHolder.HeaderParams().setClientType(clientType));
                LogCommonUtils.initLogMDC(tid, serviceName);
                TextModelBaseVo llmChatResult = null;
                IntentionRecommendVO vo = null;
                try {
                    if (StringUtils.isNotBlank(templateConfig.getCopy())) {
                        // 固定语句推荐（存在该配置，不执行大模型推荐）
                        log.info("固定语句推荐（存在该配置，不执行大模型推荐） userId:{}, dialogueId:{},", userId, dialogueId);
                        String url = recommendPromptTemplateProperties.getLinkUrlByIntention(templateConfig);
                        vo = new IntentionRecommendVO(intention, templateConfig.getCopy(), url);
                        return vo;
                    }
                    String userMsg = String.format(templateConfig.getTemplate(), dialogue)
                            + recommendPromptTemplateProperties.getDefaultAppendPrompt();
                    // 构建大模型消息请求
                    List<LlmChatMessage> messages = new ArrayList<>();
                    messages.add(new LlmChatMessage(TextModelRoleEnum.USER.getName(), userMsg));
                    LlmChatReqDTO chatDTO = new LlmChatReqDTO(userId,
                            recommendPromptTemplateProperties.getIntentionTemplate().getModelCode(), messages);
                    llmChatResult = llmChatExternalService.chatNormal(chatDTO);
                    if (!(null != llmChatResult && llmChatResult.isSuccess())) {
                        log.error("CompletableFuture-supplyAsync dialogueId:{}, dialogue:{}, intention:{} 获取不到大模型结果",
                                dialogueId, dialogue, intention);
                        return null;
                    }

                    // 对大模型返回的文字进行审核
                    try {
                        checkSystemDomainService.checkLocalAndPlatformException(dialogueId, chatDTO.getUserId(), llmChatResult.getText());
                    } catch (Exception e) {
                        log.error(
                                "CompletableFuture-supplyAsync dialogueId:{}, dialogue:{}, intention:{}, text:{} 送审失败 error:",
                                dialogueId, dialogue, intention, llmChatResult.getText(), e);
                        return null;
                    }

                    // 解析推荐文本并构建意图推荐对象
                    String resultStr = JsonHandleUtils.formatJsonStr(llmChatResult.getText());
                    List<String> list = JSON.parseArray(resultStr, String.class);
                    if (!CollectionUtils.isEmpty(list)) {
                        String url = recommendPromptTemplateProperties.getLinkUrlByIntention(templateConfig);
                        vo = new IntentionRecommendVO(intention, formatStr(list.get(0)), url);
                    }
                } catch (Exception e) {
                    log.error("CompletableFuture-supplyAsync dialogueId:{}, dialogue:{}, intention:{} error:",
                            dialogueId, dialogue, intention, e);
                } finally {
                    log.info(
                            "CompletableFuture-supplyAsync dialogueId:{}, dialogue:{}, intention:{} llmChatResult:{}, vo:{}",
                            dialogue, intention, JsonUtil.toJson(llmChatResult), JsonUtil.toJson(vo));
                }
                return vo;
            }, recommendIntentionThreadPool);
        } catch (Exception e) {
            log.error("llmChatExternalService-chatNormal dialogueId:{}, dialogue:{}, intention:{} error:", dialogueId,
                    dialogue, intention, e);
            return null;
        }
    }

    /**
     * 异步获取意图推荐结果的未来值。统一模板处理
     * <p>
     * 通过此方法，可以在不阻塞当前线程的情况下，发起一个异步任务来计算基于对话和意图信息的推荐意图。 方法返回一个Future对象，通过该对象可以在后续时间点查询异步计算的结果。
     *
     * @param dialogue          用户的对话内容，用于意图识别和推荐。
     * @param intentionInfoList 用户的多意图信息，可能包含过去的意图历史或其他相关意图上下文。
     * @return 一个Future对象，代表异步计算的意图推荐结果。
     */
    @Override
    public Future<Object> getUnifiedRecommendIntentionFuture(Long dialogueId, String dialogue, String intentionMain,
                                                             List<IntentionInfo> intentionInfoList) {
        if (StringUtils.isBlank(dialogue)) {
            return null;
        }
        if (CollectionUtils.isEmpty(intentionInfoList)) {
            //【包含主意图，需要判断大于1】
            log.warn("dialogueId:{}, dialogue:{}, 非多意图，无需执行多意图推荐", dialogueId, dialogue);
            return null;
        }
        if (!recommendPromptTemplateProperties.isIntentionUnifiedTemplate()) {
            log.warn("非统一提示词模板 dialogueId:{}", dialogueId);
            return null;
        }
        final String userId = RequestContextHolder.getUserId();
        try {
            final String tid = String.valueOf(MDC.get(LogConstants.TRACE_ID));
            final String serviceName = String.valueOf(MDC.get(LogConstants.SERVICE));
            // 这里并发异步执行获取意图推荐
            String clientType = RequestContextHolder.getClientType();
            return CompletableFuture.supplyAsync(() -> {
                RequestContextHolder.setHeaderParams(new RequestContextHolder.HeaderParams().setClientType(clientType));
                LogCommonUtils.initLogMDC(tid, serviceName);
                TextModelBaseVo llmChatResult = null;
                List<IntentionRecommendVO> vos = new ArrayList<>();
                StringBuffer intentString = new StringBuffer();
                int intentSize = 0;
                try {
                    for (IntentionInfo intentionInfo : intentionInfoList) {
                        String intention = intentionInfo.getIntention();
                        if (DialogueIntentionEnum.isTextToolIntention(intention)) {
                            //多意图推荐，过滤文本工具意图
                            continue;
                        }
                        TemplateConfig templateConfig = recommendPromptTemplateProperties.getTemplateConfig(intention);
                        if (null != templateConfig && StringUtils.isNotBlank(templateConfig.getCopy())) {
                            // 固定语句推荐（存在该配置，不执行大模型推荐）
                            log.info("固定语句推荐（存在该配置，不执行大模型推荐） userId:{}, dialogueId:{},", userId, dialogueId);
                            String url = recommendPromptTemplateProperties.getLinkUrlByIntention(templateConfig);
                            vos.add(new IntentionRecommendVO(intention, templateConfig.getCopy(), url));
                        } else {
                            intentSize++;
                            intentString.append(DialogueIntentionEnum.getByCode(intention).getRecommendName()).append("、");
                            String url = null;
                            if (null != templateConfig) {
                                url = recommendPromptTemplateProperties.getLinkUrlByIntention(templateConfig);
                            }
                            // 先加入到vos，推荐后，再设置copy
                            vos.add(new IntentionRecommendVO(intention, null, url));
                        }
                    }
                    if (intentSize > 0) {
                        intentString.setLength(intentString.length() - 1);
                        String unifiedTemplate = recommendPromptTemplateProperties.getIntentionTemplate()
                                .getUnifiedTemplate();
                        unifiedTemplate = unifiedTemplate
                                .replaceAll("\\{main_intent\\}",
                                        DialogueIntentionEnum.getByCode(intentionMain).getRecommendName())
                                .replaceAll("\\{suggest_intent\\}", intentString.toString());
                        String userMsg = String.format(unifiedTemplate, dialogue)
                                + recommendPromptTemplateProperties.getDefaultAppendPrompt();
                        // 构建大模型消息请求
                        List<LlmChatMessage> messages = new ArrayList<>();
                        messages.add(new LlmChatMessage(TextModelRoleEnum.USER.getName(), userMsg));
                        LlmChatReqDTO chatDTO = new LlmChatReqDTO(userId,
                                recommendPromptTemplateProperties.getIntentionTemplate().getModelCode(), messages);
                        llmChatResult = llmChatExternalService.chatNormal(chatDTO);
                        if (!(null != llmChatResult && llmChatResult.isSuccess())) {
                            log.error("CompletableFuture-supplyAsync dialogueId:{}, dialogue:{}, intentString:{} 获取不到大模型结果",
                                    dialogueId, dialogue, intentString.toString());
                            return null;
                        }

                        // 对大模型返回的文字进行审核
                        try {
                            checkSystemDomainService.checkLocalAndPlatform(dialogueId, chatDTO.getUserId(), llmChatResult.getText());
                        } catch (Exception e) {
                            log.error(
                                    "CompletableFuture-supplyAsync dialogueId:{}, dialogue:{}, intentString:{}, text:{} 送审失败 error:",
                                    dialogueId, dialogue, intentString.toString(), llmChatResult.getText(), e);
                            return null;
                        }
                        // 解析推荐文本并构建意图推荐对象
                        String resultStr = JsonHandleUtils.formatJsonStr(llmChatResult.getText());
                        List<String> list = formatList(resultStr);
                        if (!CollectionUtils.isEmpty(list) && list.size() >= intentSize) {
                            // 处理多意图推荐返回，intentSize>=list.size()
                            for (IntentionRecommendVO vo : vos) {
                                if (StringUtils.isNotBlank(vo.getCopy())) {
                                    // 存在固定文案，直接跳过
                                    continue;
                                }
                                // 按推荐返回顺序，接收推荐文案
                                vo.setCopy(listGetCopy(list, vo));
                            }
                        }
                    } else {
                        log.warn("dialogueId:{}, dialogue:{}, 多意图只设置固定文案，无需执行多意图推荐调用大模型", dialogueId, dialogue);
                    }

                    // 去除空copy和主意图，返回新列表
                    vos = vos.stream().filter(vo -> (StringUtils.isNotBlank(vo.getCopy())
                            && !intentionMain.equals(vo.getIntentionCommand()))).collect(Collectors.toList());
                    return vos;
                } catch (Exception e) {
                    log.error("CompletableFuture-supplyAsync dialogueId:{}, dialogue:{}, intentString:{} error:",
                            dialogueId, dialogue, intentString.toString(), e);
                } finally {
                    log.info(
                            "CompletableFuture-supplyAsync dialogueId:{}, dialogue:{}, intentString:{} llmChatResult:{}, vos:{}",
                            dialogue, intentString.toString(), JsonUtil.toJson(llmChatResult), JsonUtil.toJson(vos));
                }
                return null;
            }, recommendIntentionThreadPool);
        } catch (Exception e) {
            log.error("llmChatExternalService-chatNormal dialogueId:{}, dialogue:{}, intentionInfoList:{} error:",
                    dialogueId, dialogue, JsonUtil.toJson(intentionInfoList), e);
            return null;
        }
    }

    private String listGetCopy(List<String> list, IntentionRecommendVO vo) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        try {
            for (String str : list) {
                //返回为 搜图片：xxx，搜视频：yyy这种
                String recommendName = DialogueIntentionEnum.getByCode(vo.getIntentionCommand()).getRecommendName();
                if (str.startsWith(recommendName)) {
                    return str.substring(recommendName.length() + 1);
                }
            }
        } catch (Exception e) {
            log.error("listGetCopy vo:{}, list:{}, error:", JsonUtil.toJson(vo), JsonUtil.toJson(list), e);
        }
        return null;
    }

    /**
     * 格式化文本，返回的数据有可能是数组，需要格式化一下
     *
     * @param text
     * @return String
     */
    private String formatStr(String text) {
        List<String> list = formatList(text);
        if (!CollectionUtils.isEmpty(list)) {
            return list.get(0);
        }
        return StringUtils.EMPTY;
    }

    /**
     * 格式化文本，返回的数据有可能是数组，需要格式化一下
     *
     * @param text
     * @return List
     */
    private List<String> formatList(String text) {
        if (CharSequenceUtil.isEmpty(text)) {
            return null;
        }
        if (text.indexOf(LEFT_BRACKET) == 0) {
            try {
                return JSON.parseArray(text, String.class);
            } catch (Exception e) {
                log.error("字符串格式化失败:{}, error:", text, e);
            }
        } else {
            return Collections.singletonList(text);
        }
        return null;
    }

    @Override
    public Future<List<IntentionRecommendVO>> getTextToolAiPhotoVisionFuture(VlModelConfig.BusinessModelConfig vlmBusinessModelConfig,
                                                                             ImageProcessorUtil.ImageOutput imageOutput, TextModelVlReqDTO reqDTO) {
        Map<String, String> logMap = LogCommonUtils.getCopyOfContextMap();
        UserInfo userInfo = RequestContextHolder.getUserInfo();
        return CompletableFuture.supplyAsync(() -> {
            RequestContextHolder.setUserInfo(userInfo);
            LogCommonUtils.initLogMDC(logMap);
            List<TextModelMessageVlDTO> messageVlDtoList = new ArrayList<>();
            List<TextModelMessageVlDTO.VlContent> vlContentList = new ArrayList<>();
            String systemPrompt = vlmBusinessModelConfig.getSystemPrompt();

            // 1系统提示词
            if (StringUtils.isNotEmpty(systemPrompt)) {
                TextModelMessageVlDTO.VlContent vlContent = TextModelMessageVlDTO.VlContent.builder()
                        .type(TextModelVlContentTypeEnum.TEXT.getType())
                        .text(systemPrompt)
                        .build();
                vlContentList.add(vlContent);
            }
            String imageBase64 = FileBase64Util.fileToBase64(imageOutput.getDesFile());

            if (TextModelEnum.isZiyanVl(vlmBusinessModelConfig.getModelCode())) {
                // 自研用图片base64
                vlContentList.add(TextModelMessageVlDTO.VlContent.builder().type(TextModelVlContentTypeEnum.IMAGE_BASE64.getType())
                        .imageBase64(imageBase64).build());
            } else if (TextModelEnum.isHuoShanVl(vlmBusinessModelConfig.getModelCode())) {
                // 火山图片url类型
                TextModelMessageVlDTO.VlContent content = TextModelMessageVlDTO.VlContent.builder().type(TextModelVlContentTypeEnum.VOLCANO_IMAGE_URL.getType())
                        .build();
                content.setImageUrlObject("data:" + imageOutput.getContentType() + ";base64," + imageBase64);
                vlContentList.add(content);
            } else {
                // 默认的图片url
                TextModelMessageVlDTO.VlContent content = TextModelMessageVlDTO.VlContent.builder().type(TextModelVlContentTypeEnum.VOLCANO_IMAGE_URL.getType())
                        .build();
                content.setImageUrl("data:" + imageOutput.getContentType() + ";base64," + imageBase64);
                vlContentList.add(content);
            }

            vlContentList.add(TextModelMessageVlDTO.VlContent.builder()
                    .type(TextModelVlContentTypeEnum.TEXT.getType())
                    .text(vlmBusinessModelConfig.getUserPrompt())
                    .build());
            messageVlDtoList.add(new TextModelMessageVlDTO(TextModelRoleEnum.USER.getName(), vlContentList));
            reqDTO.setModelValue(vlmBusinessModelConfig.getModelCode());
            reqDTO.setMessageVlDtoList(messageVlDtoList);
            // 结果文本
            TextModelBaseVo textModelBaseVo = null;
            StopWatch stopWatch = StopWatch.create("视觉大模型调用计时器");
            stopWatch.start("对话id:" + reqDTO.getTaskId() + "视觉大模型调用");
            try {
                textModelBaseVo = externalVlModelClient.completions(reqDTO);
                if (Objects.isNull(textModelBaseVo) || StringUtils.isEmpty(textModelBaseVo.getText())) {
                    log.info("==> 视觉大模型调用发生获取失败，对话id:{}, getTextToolAiPhotoVisionFuture: textModelBaseVo 或其text 字段为空", reqDTO.getTaskId());
                    return null;
                }
            } catch (Exception ex) {
                log.error("==> 视觉大模型调用发生异常，对话id:{}, 原始异常信息：", reqDTO.getTaskId(), ex);
                return null;
            } finally {
                stopWatch.stop();
                log.info("视觉大模型调用计时器, 对话id:{},响应内容：{}, 耗时毫秒数:{}", reqDTO.getTaskId(), JsonUtil.toJson(textModelBaseVo),
                        stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
            }

            stopWatch = StopWatch.create("视觉大模型输出送审计时器");
            stopWatch.start("对话id:" + reqDTO.getTaskId() + "视觉大模型输出送审");
            try {
                checkSystemDomainService.checkLocalAndPlatformException(Long.parseLong(reqDTO.getTaskId()), reqDTO.getUserId(), textModelBaseVo.getText());
            } catch (Exception e) {
                log.error("==> 视觉大模型输出送审异常, 对话id:{} | e:", reqDTO.getTaskId(), e);
                return null;
            }

            String text = textModelBaseVo.getText();
            //text 必须符合list<String> 格式，比如"[\"虚数单位i是啥？\",\"复数求模咋算？\",\"还有其他复数运算吗？\"]"，然后转换后添加到list, 若转换失败则catch 异常
            try {
                List<String> parsedList = JSON.parseArray(text, String.class);
                if (!CollectionUtils.isEmpty(parsedList)) {
                    return parsedList.stream()
                            .map(item -> new IntentionRecommendVO(DialogueIntentionEnum.TEXT_TOOL.getCode(),
                                    DialogueIntentionSubEnum.AI_PHOTO_RECOGNITION_QA.getCode(), item, null))
                            .collect(Collectors.toList());
                }
            } catch (Exception e) {
                log.warn("==> 大模型返回文本转换为List<String>失败，对话id:{} | ，推荐为空，异常信息:", reqDTO.getTaskId(), e);
            }
            return Collections.emptyList();
        }, recommendIntentionThreadPool);
    }

}
