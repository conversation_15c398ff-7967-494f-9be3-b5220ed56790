package com.zyhl.yun.api.outer.domain.vo;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/1/6 16:34
 */
@Data
@Builder
public class ChatCommentGetResult implements Serializable {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 会话id
     */
    private String sessionId;

    /**
     * 对话id(任务id)
     *
     */
    private String dialogueId;


    /**
     * @see com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum
     */
    private String modelType;


    /**
     * 是否喜欢 0:不喜欢，1:喜欢
     * @see com.zyhl.yun.api.outer.enums.LikeEnum
     */
    private Integer likeComment;


    private String defaultComment;


    private String customComment;

}
