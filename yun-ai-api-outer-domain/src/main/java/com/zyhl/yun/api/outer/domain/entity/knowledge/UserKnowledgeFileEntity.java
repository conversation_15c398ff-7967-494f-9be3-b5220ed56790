package com.zyhl.yun.api.outer.domain.entity.knowledge;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.File;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.FileSortTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.OutAuditStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.PersonalKnowledgeAiStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileCategoryEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileProcessStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 个人知识库文件表
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class UserKnowledgeFileEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 知识库ID
     */
    private  Long baseId;

    /**
     * 文件的父目录ID
     */
    private String parentFileId;


    /**
     * 用户id
     */
    private String userId;

    /**
     * 文件id（独立空间文件id）
     */
    private String fileId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 空间 ID
     */
    private String driveId;

    /**
     * 旧文件id（个人云文件id）
     */
    private String oldFileId;

    /**
     * 文件归属，个人云 owner_id= user_id
     */
    private String ownerId;

    /**
     * 业务类型：
     * -1 - 未知类型
     * 1-personal 个人云
     * 2-group 圈子
     * 3-shareGroup 共享群
     * 4-family 家庭云
     * 10 -mount 挂载盘
     * 6-note 笔记
     * 7-cardpackage 卡包
     * 8-system 系统空间
     * 9-partner 合作空间
     * 5-activity 活动空间 照片直播
     *
     * @see com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum
     */
    private Integer ownerType;

    /**
     * paas平台编码
     */
    private String paasCode;

    /**
     * 文件哈希名
     */
    private String hashName;

    /**
     * 文件哈希值
     */
    private String hashValue;

    /**
     * 文件类型:1-文件，2-目录
     */
    private Integer fileType;

    /**
     * 内容类型
     */
    private Integer contentType;

    /**
     * 文件/目录分类
     * 文件/目录分类,见字典定义
     * 1 图片
     * 2 音频
     * 3 视频
     * 4 文档
     * 5 应用
     * 6 压缩文件
     * 0 其他
     * 100 普通目录
     *
     * @see FileCategoryEnum
     */
    private Integer category;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 文件后缀
     */
    private String extension;

    /**
     * 文件修改时间
     */
    private Date fileUpdatedAt;

    /**
     * 文件创建时间
     */
    private Date fileCreatedAt;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 移入回收站时间
     */
    private Date trashedTime;

    /**
     * 删除标识，0--正常；1--已删除；2--删除中；3--保险箱
     *
     * @see com.zyhl.yun.api.outer.enums.knowledge.KnowledgeStatusEnum
     */
    private Integer delFlag;

    /**
     * 算法结果状态
     * 默认 0 未处理
     * 1 成功
     * 2 失败
     *
     * @see FileProcessStatusEnum
     */
    private Integer aiStatus;

    /**
     * 算法结果码
     * 0000 成功
     * 其他则错误码
     */
    private String resultCode;

    /**
     * 算法结果
     */
    private String resultMsg;

    /**
     * 审核状态
     * 默认 0 未送审
     * 状态码：2通过，3未通过
     */
    private Integer auditStatus;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 审核结果，json格式
     */
    private String auditResult;

    /**
     * 算法组编码
     */
    private String algorithmGroupCode;

    /**
     * 来源文件资源类型
     *
     * @see com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum
     */
    private Integer fromResourceType = 0;

    /**
     * 排序优先级
     */
    private Integer sortType;

    /**
     * 来源资源信息
     * from_resource_type=3 则存储的是网址
     */
    private String fromResource;

    /**
     * 父目录路径
     */
    private String parentFilePath;

    /**
     * 附件信息
     * json格式
     * 邮件附件
     * 笔记附件
     */
    private String attachment;

    /**
     * 算法完成步骤
     * 保存文档解析各个节点最终状态（parseType不能重复）
     * [{
     * "parseType":"文档解析类型", // SPLIT
     * "status": 1 成功 2失败
     * "code": 算法结果码
     * }]
     * 文档解析类型:
     * SPLIT 文档切片（默认）
     * QA 问答对解析
     * SPLIT_GQA 切片假设性问题
     * GQA 假设性问题
     * GSPLIT 语义切块
     * SUMMARY 文档总结
     * SUMMARY.SPLIT 分块文档总结
     * SUMMARY.GSPLIT 语义分块文档总结
     */
    private String resultStepLogs;

    /**
     * 当 from_resource_type = 5
     * 修改版本号
     */
    private String revision;
    /**
     * 当 from_resource_type = 5
     * -1 （默认） 0 同步中，1 同步成功，2 同步失败
     */
    private Integer syncStatus;
    /**
     * 当 from_resource_type = 5
     *
     * 同步信息错误描述
     */
    private String syncErrorMsg;
    /**
     * 同步错误码 当 from_resource_type = 5
     */
    private String syncErrorCode;
    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 任务类型;任务类型
     * 1.图片元数据分析
     * 2.人脸聚类
     * 3.相似度聚类
     * 4.文档正文索引
     * 5.文档向量化索引(个人知识库) 默认
     */
    private Integer metaTaskType = 5;

    /**
     * 0 云盘个人云（默认）
     * 1 邮件
     * 2 笔记
     */
    private Integer resourceType;

    private String rowKey;

    public UserKnowledgeFileEntity(File file, String userId, Integer aiStatus, String resultMsg, String resultCode) {
        this.userId = userId;
        this.fileId = "";
        this.fileName = file.getName();
        this.oldFileId = file.getFileId();
        this.ownerType = OwnerTypeEnum.PERSONAL.getOwnerValue();
        this.ownerId = userId;
        this.paasCode = String.valueOf(RequestContextHolder.getBelongsPlatform());
        this.hashName = file.getContentHashAlgorithm();
        this.hashValue = file.getContentHash();
        this.fileType = FileTypeEnum.getKnowledgeFileType(file.getType());
        // this.contentType = "";
        this.category = FileCategoryEnum.getKnowledgeCategory(file.getCategory());
        this.fileSize = file.getSize();
        this.extension = file.getFileExtension();
        this.fileUpdatedAt = CharSequenceUtil.isEmpty(file.getUpdatedAt()) ? null : DateUtil.parse(file.getUpdatedAt(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
        this.fileCreatedAt = CharSequenceUtil.isEmpty(file.getCreatedAt()) ? null : DateUtil.parse(file.getCreatedAt(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
        this.trashedTime = CharSequenceUtil.isEmpty(file.getTrashedAt()) ? null : DateUtil.parse(file.getTrashedAt(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
        if (Objects.nonNull(aiStatus)) {
            // 超出大小限制和不支持格式的文件, 入个人知识库时, 算法状态为失败, fileId为个人云fileId拼接时间戳
            this.aiStatus = aiStatus;
            this.fileId = file.getFileId() + "_" + DateUtil.current();
            this.resultMsg = resultMsg;
            this.resultCode = resultCode;
        }
    }

    /**
     * 文件
     * 文件传个人云文件，目录传知识库创建的目录
     * @param file
     * @param userId
     * @param baseId
     * @param parenFileId
     */
    public UserKnowledgeFileEntity(File file, String userId, Long baseId, String parenFileId) {
        this.userId = userId;
        // 文件
        this.fileId = "";
        this.baseId = baseId;
        this.parentFileId = parenFileId;
        this.fileName = file.getName();
        this.oldFileId = file.getFileId();
        this.ownerType = OwnerTypeEnum.PERSONAL.getOwnerValue();
        this.ownerId = userId;
        this.paasCode = String.valueOf(RequestContextHolder.getBelongsPlatform());
        this.hashName = file.getContentHashAlgorithm();
        this.hashValue = file.getContentHash();
        // 文件或者目录，文件1，目录2
        this.fileType = FileTypeEnum.getKnowledgeFileType(file.getType());
        // this.contentType = "";
        this.category = FileCategoryEnum.getKnowledgeCategory(file.getCategory());
        this.fileSize = file.getSize();
        this.extension = file.getFileExtension();
        this.fileUpdatedAt = CharSequenceUtil.isEmpty(file.getUpdatedAt()) ? null : DateUtil.parse(file.getUpdatedAt(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
        this.fileCreatedAt = CharSequenceUtil.isEmpty(file.getCreatedAt()) ? null : DateUtil.parse(file.getCreatedAt(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
        this.trashedTime = CharSequenceUtil.isEmpty(file.getTrashedAt()) ? null : DateUtil.parse(file.getTrashedAt(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
    }

    /**
     * 目录构建
     * @param file
     * @param userId
     * @param baseId
     * @param parenFileId
     * @param dirId
     * @param name
     */
    public UserKnowledgeFileEntity(File file, String userId, Long baseId, String parenFileId, String parentFilePath, String dirId, String name) {
        this.userId = userId;
        // 目录
        this.fileId = dirId;
        this.baseId = baseId;
        this.parentFileId = parenFileId;
        this.parentFilePath = parentFilePath;
        this.fileName = name;
        this.oldFileId = file.getFileId();
        this.ownerType = OwnerTypeEnum.PERSONAL.getOwnerValue();
        this.ownerId = userId;
        this.paasCode = String.valueOf(RequestContextHolder.getBelongsPlatform());
        this.fileType = FileTypeEnum.FOLDER.getKnowledgeFileType();
        this.category = FileCategoryEnum.getKnowledgeCategory(file.getCategory());
        this.resultCode = ResultCodeEnum.SUCCESS.getResultCode();
        this.resultMsg = "添加到知识库成功";
        // 目录构建默认不用ai审核和送审，都是成功
        this.aiStatus = PersonalKnowledgeAiStatusEnum.PROCESSING_SUCCESS.getCode();
        this.sortType = FileSortTypeEnum.FILE.getCode();

        this.auditStatus = OutAuditStatusEnum.SUCCESS.getCode();
        this.fileUpdatedAt = CharSequenceUtil.isEmpty(file.getUpdatedAt()) ? null : DateUtil.parse(file.getUpdatedAt(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
        this.fileCreatedAt = CharSequenceUtil.isEmpty(file.getCreatedAt()) ? null : DateUtil.parse(file.getCreatedAt(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
        this.trashedTime = CharSequenceUtil.isEmpty(file.getTrashedAt()) ? null : DateUtil.parse(file.getTrashedAt(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
    }

    public UserKnowledgeFileEntity(com.zyhl.yun.api.outer.domain.valueobject.File file, Integer fromResourceType, String userId) {
        Date date = new Date();

        this.userId = userId;
        this.fileId = file.getFileId() + "_" + System.currentTimeMillis();
        ;
        this.fileName = file.getName();
        this.oldFileId = file.getFileId();
        this.ownerType = OwnerTypeEnum.AI.getOwnerValue();
        this.ownerId = userId;
        this.paasCode = String.valueOf(RequestContextHolder.getBelongsPlatform());
        this.hashName = StrUtil.nullToEmpty(file.getContentHashAlgorithm());
        this.hashValue = StrUtil.nullToEmpty(file.getContentHash());
        this.fileType = FileTypeEnum.FILE.getKnowledgeFileType();
        // this.contentType = "";
        this.category = FileCategoryEnum.getKnowledgeCategory(file.getCategory());
        this.extension = Objects.requireNonNull(KnowledgeResourceTypeEnum.getByCode(fromResourceType)).getExt();
        this.fileUpdatedAt = date;
        this.fileCreatedAt = date;
        this.trashedTime = date;
        this.fromResourceType = fromResourceType;
    }


    public UserKnowledgeFileEntity(String userId, String title, String url, String htmlJson) {
        Date date = new Date();

        this.userId = userId;
        this.fileId = DigestUtil.md5Hex(url) + "_" + System.currentTimeMillis();
        this.fileName = title;
        this.ownerId = userId;
        this.ownerType = OwnerTypeEnum.AI.getOwnerValue();
        this.paasCode = String.valueOf(RequestContextHolder.getBelongsPlatform());
        this.hashName = "";
        this.hashValue = "";
        this.fileType = FileTypeEnum.FILE.getKnowledgeFileType();
        this.category = 0;
        this.extension = KnowledgeResourceTypeEnum.HTML.getExt();
        this.fileUpdatedAt = date;
        this.fileCreatedAt = date;
        this.trashedTime = date;
        this.fromResourceType = KnowledgeResourceTypeEnum.HTML.getCode();
        this.fromResource = htmlJson;
    }

    /**
     * 从fromResource字段解析过来的html信息
     */
    private HtmlInfo htmlInfo;

    /**
     * 字段fromResource的信息
     */
    @Data
    public static class HtmlInfo {
        /**
         * 编号
         */
        private Integer index;
        /**
         * 网页主题
         */
        private String title;
        /**
         * 网页url
         */
        private String url;
        /**
         * 来源网站
         */
        private String siteName;
        /**
         * 来源网站图标
         */
        private String icon;
    }
}
