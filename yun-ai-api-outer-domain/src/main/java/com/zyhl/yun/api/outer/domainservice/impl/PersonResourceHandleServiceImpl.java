package com.zyhl.yun.api.outer.domainservice.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiPersonResourceEntity;
import com.zyhl.yun.api.outer.domainservice.PersonResourceHandleService;
import com.zyhl.yun.api.outer.repository.AlgorithmAiPersonResourceRepository;
import com.zyhl.yun.api.outer.util.SimpleRedisLock;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 人物关系缓存数据处理相关业务逻辑
 * <AUTHOR>
 */
@Slf4j
@Service
public class PersonResourceHandleServiceImpl implements PersonResourceHandleService {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private AlgorithmAiPersonResourceRepository personResourceRepository;

    @Resource
    private SimpleRedisLock simpleRedisLock;

    @Override
    public List<String> getResourceList(String name, String type) {
        List<String> resultList = null;
        if (StringUtils.isBlank(name)) {
            return Collections.emptyList();
        }

        RMap<String, Map<String, List<String>>> sensitiveMap = redissonClient.getMap(RedisConstants.PERSON_RESOURCE_CACHE_KEY);
        long cacheTtl = redissonClient.getBucket(RedisConstants.PERSON_RESOURCE_CACHE_KEY).remainTimeToLive();

        // 缓存为空/过期时间为空/剩余过期时间不足，则初始化缓存
        if (sensitiveMap.isEmpty() || ObjectUtil.equal(cacheTtl, null) || cacheTtl < RedisConstants.PERSON_RESOURCE_LOGIC_CACHE_EXPIRE_TIME) {
            return listAndHandlePromptRecommendList(name, type);
        }

        // 获取视频名集合
        Map<String, List<String>> resourceMap = sensitiveMap.get(name);
        if (resourceMap == null || resourceMap.isEmpty()) {
            return Collections.emptyList();
        } else if(StringUtils.isNotBlank(type)) {
            // type不为空时按类型获取
            resultList = resourceMap.get(type);
        } else {
            // 获取所有视频名
            resultList = resourceMap.values().stream()
                    .flatMap(List::stream)
                    .distinct()
                    .collect(Collectors.toList());
        }


        return resultList;
    }

    /**
     * 从DB获取人物关系数据，判断是否注入缓存
     * @return 提示词列表
     */
    private List<String> listAndHandlePromptRecommendList(String name, String type) {

        List<String> resultList = null;

        // get from DB
        List<AlgorithmAiPersonResourceEntity> dataList = personResourceRepository.getAllDate();

        if (dataList == null || dataList.isEmpty()) {
            return Collections.emptyList();
        }

        // 根据map结构分类，结构：{ 人名 : { 类型 : 视频名集合 } }
        Map<String, Map<String, List<String>>> map = dataList.stream()
                .filter(e -> e.getName() != null)
                .collect(Collectors.groupingBy(
                        AlgorithmAiPersonResourceEntity::getName,
                        Collectors.groupingBy(
                                AlgorithmAiPersonResourceEntity::getRelationship,
                                Collectors.mapping(
                                        AlgorithmAiPersonResourceEntity::getResourceName,
                                        Collectors.toList()
                                )
                        )
                ));

        // 获取视频名集合
        Map<String, List<String>> resourceMap = map.get(name);
        if (resourceMap == null || resourceMap.isEmpty()) {
            log.info("缓存中人物关系数据为空，返回结果为空。");
            resultList = Collections.emptyList();
        } else if(StringUtils.isNotBlank(type)) {
            // type不为空时按类型获取
            resultList = resourceMap.get(type);
        } else {
            // 获取所有视频名
            resultList = resourceMap.values().stream()
                    .flatMap(List::stream)
                    .distinct()
                    .collect(Collectors.toList());
        }

        try {
            if (simpleRedisLock.tryLock(RedisConstants.PERSON_RESOURCE_CACHE_LOCK_KEY,
                    RedisConstants.PERSON_RESOURCE_CACHE_KEY, 300, TimeUnit.SECONDS)) {
                // injection
                promptInfoInjection(map);
                simpleRedisLock.unlock(RedisConstants.PERSON_RESOURCE_CACHE_LOCK_KEY, RedisConstants.PERSON_RESOURCE_CACHE_KEY);
            } else {
                log.info("缓存中人物关系数据为空，且当前线程抢夺锁失败，返回结果为空，等待缓存注入数据线程注入成功后再尝试获取操作。");
            }
        } catch (YunAiBusinessException e) {
            log.error("人物关系缓存业务锁操作发生异常，请注意上下文业务日志是否存在问题。", e);
        }

        return resultList;
    }

    /**
     * 人物关系数据注入缓存
     */
    private void promptInfoInjection(Map<String, Map<String, List<String>>> map) {
        RMap<String, Map<String, List<String>>> sensitiveMap = redissonClient.getMap(RedisConstants.PERSON_RESOURCE_CACHE_KEY);
        sensitiveMap.putAll(map);
        sensitiveMap.expire(RedisConstants.PERSON_RESOURCE_CACHE_EXPIRE_TIME, TimeUnit.MINUTES);
    }
}