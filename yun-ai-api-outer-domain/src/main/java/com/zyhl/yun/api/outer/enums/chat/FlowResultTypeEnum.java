package com.zyhl.yun.api.outer.enums.chat;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流式结果类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FlowResultTypeEnum {

	/**
	 * 大模型文本回答：
	 * 使用outContent、reasioningConent、personalKnowledgeFileList和networkSearchInfoList；
	 */
	TEXT_MODEL(1, "大模型文本回答"),

	/**
	 * 富文本回答：使用outContent；
	 */
	RICH_TEXT(2, "富文本回答"),

	/**
	 * 云盘检索结果回答：使用searchInfoList；
	 */
	SEARCH(3, "云盘检索结果回答"),

	/**
	 * 异步图片任务回答：使用fileList或者outContent；
	 */
	ASYNC(4, "异步图片任务回答"),

	/**
	 * 邮件结果：使用mailInfo
	 */
	MAIL(5, "邮件结果"),

	/**
	 * 盘外文件结果：使用fileUrlList
	 */
	URL(6, "盘外文件结果"),

	/**
	 * 部分工具结果：使用aiFunctionResult
	 */
	TOOL_RESULT(7, "部分AI工具结果"),

	/**
	 * 8--文档文件结果:使用fileList
	 */
	DOCUMENT(8, "文档文件结果"),
	
	/**
	 * 9--相册结果：使用albumList
	 */
	ALBUM(9, "相册结果"),
	
	/**
	 * 10--思考过程结果：使用title和outContent
	 */
	REASONING_RESULT(10, "思考过程结果"),
	;

	private final Integer type;
	private final String name;

}
