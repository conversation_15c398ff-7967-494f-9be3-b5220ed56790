package com.zyhl.yun.api.outer.domain.valueobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 笔记信息类
 * 
 * <AUTHOR>
 * @date 2025-04-15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NoteInfo {
    /**
     * 笔记ID
     */
    private String noteId;

    /**
     * 笔记所属笔记本ID
     */
    private String noteBookId;

    /**
     * 笔记所属笔记本名称
     */
    private String noteBook;

    /**
     * 笔记标题
     */
    private String title;

    /**
     * 笔记类型
     * 0代表普通笔记，4代表录音笔记，1,2,3是其他笔记
     */
    private int noteType;

    public NoteInfo(String noteId) {
        this.noteId = noteId;
    }
    
    public NoteInfo(String noteId, String title) {
        this.noteId = noteId;
        this.title = title;
    }

}