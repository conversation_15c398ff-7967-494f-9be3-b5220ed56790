package com.zyhl.yun.api.outer.enums;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import lombok.AllArgsConstructor;

/**
 * 错误码枚举类
 * 详见地址：<a href="https://docs.qq.com/sheet/DR2RIdUtvbFVmSUVm?u=61aadd3d26964882a1dadb5af22e5f95&tab=BB08J2">算法中心-状态码</a>
 *
 * <AUTHOR>
 * @description
 * @date 2023/4/20 上午11:27
 */
@AllArgsConstructor
public enum ResultCodeEnum implements AbstractResultCode {

    /**
     *  通用成功响应
     */
    SUCCESS("0000", "请求成功"),

    /**
     * 通用错误响应
     */

    UNKNOWN_ERROR("9999", "未知错误"),

    //通用规范(01)通用功能(00)协议错误(00)
    ERROR_PARAMS("01000001", "参数错误"),
    ERROR_NOT_AUTH("01000002", "鉴权失败"),
    ERROR_TOKEN_EXPIRE("01000003", "令牌失效"),
    ERROR_FORBIDDEN("01000004", "权限不足"),
    ERROR_NOT_FOUND("01000005", "资源不存在"),
    ERROR_LARGE_ENTITY("01000006", "请求消息体过大"),
    ERROR_LIMITATION("01000007", "请求过多"),
    ERROR_SIGNATURE("01000009", "签名错误"),
    ERROR_CALL_EXCEPTION("01000010", "系统调用异常"),
    ERROR_TIMEOUT("01000011", "服务调用超时"),
    ERROR_SENSITIVE("01000012", "请求的IP不在白名单范围内"),
    REQUEST_TYPE_ERROR("01000013", "请求方式不支持"),
    // 这个应该是加密失败
    ERROR_NON_TARGET_USERS("01000014", "非目标用户"),
    ENCRYPT_ERROR("01000014", "加密失败"),
    DECRYPT_ERROR("01000015", "解密失败"),
    TIMEOUT_ERROR("01000016", "重连超时"),


    //通用规范(01)通用功能(00)服务错误(01)
    ERROR_SERVER_INTERNAL("01000101", "服务器响应异常"),
    ERROR_SERVER_UNAVAILABLE("01000102", "系统服务不可用"),
    ERROR_SERVER_NOT_READY("01000103", "功能未开放"),

    ERROR_AI_MODEL("10030340", "AI模型异常"),


    //算法中心(10)通用错误(00)通用错误(00)
    FILE_URL_DOWN_FAIL("10000001", "文件URL地址下载失败"),
    FILE_BASE64_DATA_ERROR("10000002", "文件base64数据错误"),
    FILE_ID_INFO_ERROR("10000003", "fileId信息错误，无法下载对应文件"),
    FILE_CONTENT_INVALID("10000004", "文件内容不符合规范"),
    FILE_SIZE_LARGE("10000006", "文件或图片上传过大"),
    MEMBER_BENEFIT_NUM("10000007", "该权益可消费数量不足"),
    ERROR_FILE_EXT("10000009", "文档格式错误，目前支持的文档格式为："),
    NOT_TARGET_USER("10000012", "非目标用户"),
    // 看文档：https://docs.qq.com/sheet/DR2RIdUtvbFVmSUVm?u=61aadd3d26964882a1dadb5af22e5f95&tab=BB08J2
    IMAGE_BENEFIT_NOT_ENOUGH("10000013", "该图片权益可消费数量不足"),
    ERROR_NOT_DEDUCTED_BENEFIT("10000014", "用户权益未扣减"),
    ERROR_FILE_FORMAT_SUPPORTED("10000019", "文件格式不支持"),
    
    SENSITIVE_WORDS_ERROR("10022012", "内容可能涉及敏感信息，建议更换其他内容再试"),


    //算法中心(10)通用错误(00)服务错误(01)
    DOWNSTREAM_SERVICES_EXCEPTION("10000101", "(下游)服务器响应异常"),
    DOWNSTREAM_SERVER_PROCESSING_FAILED("10000102", "(下游)服务器处理失败"),
    MIDDLEWARE_RESPONDS_ABNORMALLY("10000111", "中间件响应异常"),


    //算法中心(10)通用错误(00)算法任务错误（02）


    //算法中心(10)内部算法错误码(01)通用错误(00)


    //算法中心(10)内部算法错误码(01)图片元数据提取错误（01）
    ERROR_LOCAL_PATH("10010104", "共享存储文件获取失败"),


    //算法中心(10)内部算法错误码(01)图片聚类错误（02）
    //算法中心(10)内部算法错误码(01)妙云相机服务：主图识别错误(030)
    //算法中心(10)内部算法错误码(01)妙云相机服务：从图匹配错误(031)
    //算法中心(10)内部算法错误码(01)妙云相机服务：训练形象错误(032)
    //算法中心(10)内部算法错误码(01)妙云相机服务：艺术照生成错误(033)
    //算法中心(10)内部算法错误码(01)自研算法服务：人像动漫化错误(041)
    //算法中心(10)内部算法错误码(01)文生图服务：文生图错误（042）
    //算法中心(10)内部算法错误码(01)意图识别错误(05)


    //算法中心(10)外部错误码(03)音频(01)
    //算法中心(10)外部错误码(03)文本(02)


    //算法中心(10)外部错误码(03)AI助手(03)
    CONTENT_EXCEEDS_LIMIT("10030301", "文本输入内容已超最大字符限制"),
    SAVE_CHAT_MESSAGE_FAIL("10030302", "保存会话信息失败"),
    SAVE_CHAT_CONTENT_FAIL("10030303", "保存会话内容失败"),
    TASK_RECORD_NOT_FOUND("10030304", "找不到任务记录信息"),
    AI_TASK_NOT_FOUND("10030305", "找不到算法任务信息"),
    ERROR_TALK_CODE("10030306", "对话类型错误"),
    ERROR_INVALID_MODEL_TYPE("10030307", "输入了无效的会话模型"),
    ERROR_DIALOGUE_ID_DUPLICATE_COMMENT("10030308", "此对话被重复评价"),
    TASK_FAIL("10030309", "任务失败"),
    ERROR_INVALID_DIALOGUE_ID("10030310", "无效的对话ID"),
    ERROR_INVALID_USERID("10030311", "用户ID不能为空"),
    ERROR_INTENTION_CODE("10030312", "意图编码输入错误"),
    ERROR_RESOURCE_CODE("10030313", "资源类型错误"),
    ERROR_RESOURCEID_CODE("10030314", "资源ID不能为空"),
    ERROR_NACOS_MAX_LENGTH("10030315", "大模型及最大输入会话长度nacos配置错误"),
    ERROR_NACOS_CONFIG("10030316", "NACOS配置错误"),
    ERROR_USER_ID("10030317", "userId和用户域token解析出来的userId不一致"),
    SESSION_INFO_NOT_FOUND("10030318", "会话信息不存在"),
//    SENSITIVE_ERROR("10030319", "敏感词校验未通过"), // 请使用错误码：SENSITIVE_WORDS_ERROR
    ERROR_TIME_RANGE_NOT_ALLOW_EQUAL("10030320", "timeRangeList列表中的开始时间和结束时间不能相等"),
    ERROR_INVALID_APP_CHANNEL("10030321", "sourceChannel不能为空"),
    ERROR_INVALID_BUSINESS_TYPE("10030322", "sourceChannel没有匹配到对应的业务类型"),
    ERROR_APPLICATION_ID_INVALID("10030323", "应用id参数无效"),
    ERROR_APPLICATION_TYPE_INVALID("10030324", "应用类型参数无效"),
    ERROR_YUN_DISK_EXISTING("10030325", "图片已存云盘"),
    ERROR_TRANSFER_FAILED("10030326", "图片转存个人云盘失败"),
    ERROR_IMAGE_EXPIRED("10030327", "图片已过期"),
    ERROR_RESOURCE_ID_INVALID_CODE("10030328", "资源ID参数无效"),
    ERROR_CONVERSATION_ID_INVALID_CODE("10030329", "不支持此对话ID发起二次对话"),

    //算法中心(10)外部错误码(03)知识库(04)
    FILE_ALREADY_EXIST("10030401", "文件已在知识库中"),
    LABEL_NAME_EXIST("10030402", "标签名称已存在"),
    LABEL_NUM_LIMIT("10030403", "最多支持创建50个标签"),
    FILE_REPEAT_UPLOAD("10030404", "文件已上传请勿重复"),
    FILE_UPLOADING("10030405", "文件正在上传中"),
    KNOWLEDGE_CONTENT_EMPTY("10030406", "知识库检索内容为空"),
    LACK_OF_SPACE("10030407", "空间不足"),
    HTML_ULR_FORMAT_ERROR("10030407", "html格式错误"),
    HTML_URL_DOMAIN_ERROR("10030408", "html域名不支持"),
    KNOWLEDGE_ID_NOT_PROVIDE("10030409", "知识库ID不能为空"),
    KNOWLEDGE_ID_NOT_EXIST("10030410", "知识库ID不存在"),
    KNOWLEDGE_OUT_NUMBER("10030411", "知识库数量超过阈值"),
    KNOWLEDGE_NAME_OUT_NUMBER("10030412", "知识库名称字符超过阈值"),
    MAIL_IS_NULL("10030412", "导入邮箱参数为空"),
    KNOWLEDGE_DESCRIPTION_OUT_NUMBER("10030413", "知识库描述字符超过阈值"),
    MAIL_ID_IS_NULL("10030413", "导入邮箱ID为空"),
    KNOWLEDGE_USERS_CANNOT_INVITE_THEMSELVES("10030414", "用户不能加入自己的分享的知识库"),
    HTML_URL_IS_NULL("10030414", "htmlUrl为空"),
    KNOWLEDGE_NOT_EXIST("10030414", "知识库不存在"),
    NOTE_ID_IS_NULL("10030415", "导入笔记ID为空"),
    PARENT_FILE_ID_IS_NULL("10030415", "导入目录ID无效"),
    PERSONAL_KNOWLEDGE_DELETED("10030416", "该知识库已被删除，不支持访问"),
    SHARE_KNOWLEDGE_DELETED("10030417", "该知识库已被创建者删除，不支持访问"),
    SHARE_KNOWLEDGE_PRIVATE("10030418", "该知识库已被创建者设为私密，不支持访问"),
    KNOWLEDGE_NO_PERMISSION("10030419", "没有该文件权限"),
    KNOWLEDGE_FILE_PARENT_NOT_EXIST("10030420", "父文件夹不存在"),
    KNOWLEDGE_FILE_NOT_EXIST("10030421", "文件不存在"),
    KNOWLEDGE_FILE_INVALID_NAME("10030422", "文件名非法"),
    KNOWLEDGE_FILE_NAME_EMPTY("10030423", "文件名不能为空"),
    KNOWLEDGE_FILE_MOVE_RESOURCE_FOLDER_ERROR("10030424", "父目录不能移动到子目录中"),
    KNOWLEDGE_FILE_MOVE_RESOURCE_ID_ERROR("10030425", "移动的资源ID列表存在异常数据"),
    KNOWLEDGE_PHOTO_URL_ERROR("10030426", "知识库头像信息不符合规范"),
    KNOWLEDGE_MOVE_TASK_CREATE_ERROR("10030427", "知识库移动任务创建失败"),
    NOTE_SYNC_EXISTS("10030428", "笔记已同步"),
    NOTE_INFO_NOT_EXISTS("10030429", "笔记查询数据为空"),
    NOTE_SIZE_ERROR("10030430", "笔记超过一条记录"),
    NOTE_TYPE_ERROR("10030431", "笔记类型错误，只支持同步普通笔记"),

    //算法中心(10)外部错误码(03)智能搜索(05)


    // 算法中心(10)平台错误(02)通用业务错误(00)
    MQ_SEND_EXCEPTION("10020017", "MQ消息发送异常"),


    // 算法中心(10)平台错误(02)AI小程序错误(20)
    AI_FAILURE_DEFAULT("10022000", "AI系统开小差啦，请稍后重试"),
    QUEUE_ADD_EXIST_FAILURE("10022002", "您当前还有任务正在处理中"),
    ACCREDIT_HANDLE_ING("10022005", "报名（授权）正在处理中，请稍后"),
    REQUEST_TOO_FREQUENTLY("10022007", "请求过于频繁，请稍后重试"),
    ACCREDIT_CANCEL_HANDLE_ING("10022020", "取消授权正在处理中，请稍后"),


    // 其他（不在错误码文档上的）
    DOWNLOAD_TO_NFS_ERROR("10090006", "下载文件到NFS失败"),
    PERSON_SAAS_ERROR("10090007", "个人云返回的下载url,解析文件类型错误"),
    UN_SUPPORT_FILETYPE("10090012", "不支持的文件格式"),
    KNOWLEDGE_RESOURCE_CHECK_ERROR("10090013", "知识库资源校验失败"),

    KNOWLEDGE_UPLOAD_RETRY_ERROR("10090014", "知识库上传文件重试失败"),

    KNOWLEDGE_UPLOAD_RETRY_STATUS_ERROR("10090015", "任务不存在或状态不是失败"),

    KNOWLEDGE_UPLOAD_IMPORT_ERROR("10090016", "知识库上传文件导入失败"),

    KNOWLEDGE_DELETE_FILE_NUM_LIMIT("10030420", "删除知识库文件数量超过阈值"),

    ;

    /**
     * 错误码
     */
    private String resultCode;
    /**
     * 错误信息
     */
    private String resultMsg;

    @Override
    public String getResultCode() {
        return resultCode;
    }

    @Override
    public String getResultMsg() {
        return resultMsg;
    }
}
