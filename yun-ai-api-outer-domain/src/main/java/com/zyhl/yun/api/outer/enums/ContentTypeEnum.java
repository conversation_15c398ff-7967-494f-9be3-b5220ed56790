package com.zyhl.yun.api.outer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum ContentTypeEnum {

    TEXT("TEXT", "TEXT"),
    RICHTEXT("RICHTEXT", "RICHTEXT"),
    SEPARATE("SEPARATE", "SEPARATE"),
    HTML("HTML", "HTML"),
    ;

    /**
     * 笔记内容类型
     */
    private final String type;

    /**
     * 备注
     */
    private final String desc;
}
