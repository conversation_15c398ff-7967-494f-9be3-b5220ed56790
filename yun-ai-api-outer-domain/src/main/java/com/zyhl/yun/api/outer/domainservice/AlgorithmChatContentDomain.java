package com.zyhl.yun.api.outer.domainservice;

import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.chat.ContentVO;

/**
 * <AUTHOR>
 */
public interface AlgorithmChatContentDomain {

    /**
     * 判断并构造映射后的错误码
     *
     * @param vo 参数
     * @Author: WeiJingKun
     */
    void judgeAndCreateResultCode(ContentVO vo);

    /**
     * 判断并构造映射后的错误码
     *
     * @param vo 参数
     * @Author: WeiJingKun
     */
    void judgeAndCreateResultCodeV2(ContentVO vo);

    /**
     * 对话结果推荐处理
     *
     * @param vo 参数
     * @return 对话结果推荐
     */
    DialogueRecommendVO dialogueRecommendHandle(ContentVO vo);

    /**
     * 对话结果推荐-中部-处理
     *
     * @param vo 参数
     * @return 对话结果推荐
     */
    DialogueRecommendVO dialogueMiddleRecommendHandle(ContentVO vo);

}
