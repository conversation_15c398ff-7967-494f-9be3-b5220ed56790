package com.zyhl.yun.api.outer.enums;

import com.google.common.base.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 聊天大模型网络搜索状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ChatNetworkSearchStatusEnum {

	/**
	 * 关闭
	 */
	CLOSE(0, "关闭"),

	/**
	 * 打开
	 */
	OPEN(1, "打开"),

	;

	/**
	 * 编码
	 */
	private final Integer code;
	/**
	 * 描述
	 */
	private final String desc;

	/**
	 * 是否打开
	 * 
	 * @param paramCode
	 * @return
	 */
	public static Boolean isOpen(Integer paramCode) {
		return Objects.equal(OPEN.getCode(), paramCode);
	}

}
