package com.zyhl.yun.api.outer.domain.vo;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 工具推荐VO
 * 
 * @Author: liuxuewen
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ToolRecommendVO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 * 意图指令，枚举值参考IntentionCommands
	 * 
	 */

	private String command;

	/**
	 * 
	 * 子意图，目前与036工具调用的子意图编码一致
	 * 
	 */

	private String subCommand;

	/**
	 * 
	 * 工具名称
	 * 
	 */

	private String toolName;

	/**
	 * 
	 * 使用方式
	 * 
	 * 1--返回提示词，通过与大模型结合使用；
	 * 
	 * 2--跳转到对应的工具页面
	 * 
	 */

	private int useType;

	/**
	 * 
	 * 提示词
	 * 
	 */

	private String prompt;

	/**
	 * 
	 * 跳转URL
	 * 
	 */

	private String linkURL;

	/**
	 * 
	 * 提示文案
	 * 
	 */

	private String promptCopy;

	/**
	 * 
	 * 按钮文案
	 * 
	 */

	private String buttonCopy;

}
