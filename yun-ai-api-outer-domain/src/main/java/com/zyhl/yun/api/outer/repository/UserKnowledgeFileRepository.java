package com.zyhl.yun.api.outer.repository;

import com.github.pagehelper.PageInfo;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileStatisticsEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeResourceListReqEntity;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeCountVO;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeStatusEnum;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * 个人知识库文件
 *
 * <AUTHOR>
 */
public interface UserKnowledgeFileRepository {

    /**
     * 插入文件
     *
     * @param entity 实体对象
     * @return 数量
     */
    int add(@NotNull UserKnowledgeFileEntity entity);

    /**
     * 批量插入文件
     *
     * @param entityList 文件实体集合
     * @return int
     */
    boolean batchAdd(@NotNull List<UserKnowledgeFileEntity> entityList);

    /**
     * 更新文件
     *
     * @param entity 实体对象
     * @return 数量
     */
    int update(@NotNull UserKnowledgeFileEntity entity);

    /**
     * 逻辑删除文件
     *
     * @param id id
     * @return 数量
     */
    int delete(@NotNull Long id);

    /**
     * 根据id批量删除（逻辑删除）
     *
     * @param ids id集合
     * @return 数量
     */
    int deleteByIds(@NotNull List<Long> ids);

    /**
     * 根据文件id删除（逻辑删除）
     *
     * @param userId  用户id
     * @param fileIds 文件id集合
     * @param status  状态
     */
    void deleteByFileIds(@NotNull String userId, @NotNull List<String> fileIds, KnowledgeStatusEnum status);

    /**
     * 分批根据文件id删除（逻辑删除）
     *
     * @param userId  用户id
     * @param fileIds 文件id集合
     * @param status  状态
     */
    void batchDeleteByFileIds(@NotNull String userId, @NotNull List<String> fileIds, KnowledgeStatusEnum status);

    /**
     * 根据id查询
     *
     * @param id id
     * @return 实体对象
     */
    UserKnowledgeFileEntity selectById(@NotNull Long id);

    /**
     * 根据用户id查询
     *
     * @param userId 用户id
     * @return 实体对象集合
     */
    List<UserKnowledgeFileEntity> selectByUserId(@NotNull String userId);

    /**
     * 根据知识库id查询
     *
     * @param baseId 知识库id
     * @return 实体对象集合
     */
    List<UserKnowledgeFileEntity> selectByBaseId(@NotNull String baseId);

    /**
     * 根据文件id查询
     *
     * @param userId 用户id
     * @param fileId 文件id
     * @return 实体对象
     */
    UserKnowledgeFileEntity selectByFileId(String userId, @NotNull String fileId);

    /**
     * 查询文件列表
     *
     * @param userId  用户id
     * @param fileIds 文件id集合
     * @return 实体对象集合
     */
    List<UserKnowledgeFileEntity> selectByFileIds(@NotNull String userId, @NotNull List<String> fileIds);

    /**
     * 根据旧文件id查询
     *
     * @param userId  用户id
     * @param fileIds 文件id集合
     * @return 实体对象集合
     */
    List<UserKnowledgeFileEntity> selectByOldFileIds(@NotNull String userId, @NotNull List<String> fileIds);

    /**
     * 根据旧文件id查询
     *
     * @param userId
     * @param urlList
     * @return
     */
    List<UserKnowledgeFileEntity> selectHtmlResource(@NotNull String userId, @NotNull List<String> urlList);

    /**
     * 查询文件数量
     *
     * @param userId 用户id
     * @return 数量
     */
    int count(String userId);

    /**
     * 查询文件数量
     *
     * @param userId
     * @param status
     * @return
     */
    int count(String userId, Integer status);

    /**
     * 统计有效的文件数量
     *
     * @param userId 用户id
     * @return 数量
     */
    int countCanUse(String userId);

    /**
     * 分页列表
     *
     * @param userId   用户id
     * @param pageInfo 分页信息
     * @param labelId  标签id
     * @return 分页数据
     */
    PageInfo<UserKnowledgeFileEntity> list(String userId, PageInfoDTO pageInfo, Long labelId);

    /**
     * 分页列表
     *
     * @param userId   用户id
     * @param pageInfo 分页信息
     * @param status   状态
     * @param orderBy  排序
     * @return 分页数据
     */
    PageInfo<UserKnowledgeFileEntity> list180(String userId, PageInfoDTO pageInfo, Integer status, Integer orderBy);

    /**
     * 获取创建者知识库分页列表
     */
    PageInfo<UserKnowledgeFileEntity> getCreatorList(UserKnowledgeResourceListReqEntity dto, String userId);

    /**
     * 获取分页列表（非创建者）
     */
    PageInfo<UserKnowledgeFileEntity> getList(UserKnowledgeResourceListReqEntity dto, String userId);
    /**
     * 批量插入文件
     *
     * @param entityList 文件实体集合
     */
    void saveBatch(List<UserKnowledgeFileEntity> entityList);

    PersonalKnowledgeCountVO countByUserId(String userId, String countTime);

    List<UserKnowledgeFileStatisticsEntity> findStatisticsByBaseId(List<Long> baseIds);

    List<UserKnowledgeFileStatisticsEntity> findStatisticsByBaseIdForInfo(List<Long> baseIds);

    List<UserKnowledgeFileEntity> batchGet(String userId, List<String> resourceIdList);

    List<UserKnowledgeFileStatisticsEntity> findStatisticsByUserIdAndBaseIds(String userId, List<Long> baseIdList);

    /**
     * 根据fileId和baseId查个人知识库文件信息
     *
     * @param baseId
     * @param resourceId
     * @return
     */
    UserKnowledgeFileEntity getOne(String baseId, String resourceId);

    /**
     * 根据fileId和baseId和来源类型查个人知识库文件信息
     *
     * @param baseId
     * @param resourceId
     * @return
     */
    UserKnowledgeFileEntity getOne(String baseId, String resourceId, Integer fromResourceType);

    UserKnowledgeFileEntity getFileByBaseIdAndFileId(String userId, String baseId, String resourceId);

    Set<String> findRepeatImportPersonalCloudFile(String userId, String baseId, List<String> fileIdList, String parentFileId);


    /**
     * 根据旧文件id查询
     *
     * @param userId
     * @param urlList
     * @return
     */
    List<UserKnowledgeFileEntity> selectHtmlResource(@NotNull String userId, @NotNull List<String> urlList, Long baseId);

    UserKnowledgeFileEntity selectByUserIdAndFileId(String userId, String fileId);

    List<UserKnowledgeFileEntity> selectChildrenByParentId(String userId, String parentFileId);

    List<UserKnowledgeFileEntity> selectByOldFileIds(Long baseId, String userId, List<String> fileIds);

    /**
     * 根据文件id查询
     *
     * @param fileId
     * @return
     */
    UserKnowledgeFileEntity getByFileId(String fileId);

    /**
     * 根据知识库id+资源类型查询
     * @param baseId 知识库iD
     * @param resourceTypeList 资源类型
     * @return 资源文件
     */
    List<UserKnowledgeFileEntity> selectByBaseIdAndResourceType(@NotNull String baseId, List<Integer> resourceTypeList);

    /**
     * 查询是否有笔记同步记录
     * @param fileId
     * @param baseId
     * @return
     */
    UserKnowledgeFileEntity hasNoteSync(String fileId, Long baseId);
}
