package com.zyhl.yun.api.outer.domain.vo.chat.search.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 对话信息-搜索结果-圈子-接口结果-dynamicInfo对象
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DynamicInfo implements Serializable {

    /** 动态内容 */
    private String content;

    /** 别名 */
    private String alias;

    /** 动态id */
    private String dynamicId;

}
