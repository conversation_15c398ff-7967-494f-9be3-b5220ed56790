package com.zyhl.yun.api.outer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户评价枚举类
 *
 * <AUTHOR>
 * @Date 2024年02月28日 15:12
 */

@AllArgsConstructor
@Getter
public enum LikeEnum {

    /**
     *  赞
     */
    LIKE(1, "like", "赞", "喜欢"),

    /**
     * 踩
     */

    DISLIKE(0, "dislike", "踩", "不喜欢"),

    /**
     * 取消
     */

    CANCEL_COMMENT(-1, "cancelComment", "取消赞踩", "取消赞踩");

    /**
     * 模块
     */
    private final Integer module;
    /**
     * 编码
     */
    private final String code;
    /**
     * 名称
     */
    private final String name;
    /**
     * 目录名称
     */
    private final String catalogName;


    /**
     * 判断是否存在
     *
     * @param value 值
     * @return true-存在
     */
    public static boolean isExist(Integer value) {
        for (LikeEnum like : LikeEnum.values()) {
            if (like.module.equals(value)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是取消评论
     *
     * @param value 值
     * @return true-是
     */
    public static boolean isCancel(Integer value) {
        return CANCEL_COMMENT.module.equals(value);
    }
}
