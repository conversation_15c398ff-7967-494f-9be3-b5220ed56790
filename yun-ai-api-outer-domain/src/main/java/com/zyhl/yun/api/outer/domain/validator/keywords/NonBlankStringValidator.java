package com.zyhl.yun.api.outer.domain.validator.keywords;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;

/**
 * 列表中不能含有空字符串
 * <AUTHOR>
 */
public class NonBlankStringValidator implements ConstraintValidator<NonBlankStrings, List<String>> {

    @Override
    public void initialize(NonBlankStrings constraintAnnotation) {
    }

    @Override
    public boolean isValid(List<String> value, ConstraintValidatorContext context) {
        if (value == null) {
            // @NotEmpty should handle null case
            return true;
        }
        return value.stream().noneMatch(String::isEmpty);
    }
}
