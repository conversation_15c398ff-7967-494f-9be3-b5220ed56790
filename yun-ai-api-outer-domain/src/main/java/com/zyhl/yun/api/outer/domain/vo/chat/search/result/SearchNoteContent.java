package com.zyhl.yun.api.outer.domain.vo.chat.search.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 笔记内容搜索结果
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchNoteContent implements Serializable {

    /**
     * 笔记ID
     */
    private String noteId;
    /**
     * 所属分段Id
     */
    private String paragraphId;
    /**
     * 命中关键字的前10个字+后n个字。
     * 注意：命中的正文关键字后台会使用keywordsTag提醒（见返回样例），端侧做高亮显示后去除前后keywordsTag标签呈现给用户。
     */
    private String summary;

}
