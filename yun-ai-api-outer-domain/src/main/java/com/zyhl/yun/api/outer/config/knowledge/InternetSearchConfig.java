package com.zyhl.yun.api.outer.config.knowledge;

import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelConfigDTO;
import lombok.Data;

/**
 * 描述：网络搜索配置
 *
 * <AUTHOR> zhumaoxian  2025/6/11 12:04
 */
@Data
public class InternetSearchConfig {

    /**
     * 字数限制
     */
    private int queryLimit = 10;

    /**
     * 关键词数量
     */
    private int keywordCount = 4;

    /**
     * 联网搜索超时时间（ms）
     */
    private long networkTimeOut = 30;

    /**
     * 返回结果数量
     */
    private int resultTopk = 5;

    /**
     * 提取关键词配置
     */
    private ExtractKeywordConfig extractKeywordConfig = new ExtractKeywordConfig();


    /**
     * 联网搜索提取关键词配置
     */
    @Data
    public static class ExtractKeywordConfig {
        /**
         * 开启问题重写功能：true-开启，false-关闭（默认）
         */
        private boolean enabled = false;

        /**
         * 模型编码，需要输入已经支持的大模型编码
         */
        private String modelCode = "qwen";

        /**
         * 大模型系统提示词（role:system）
         */
        private String systemPrompt = "";

        /**
         * 大模型输入内容（role:user），占位符：
         * {query}对话内容
         * 不填则直接传对话内容给大模型
         */
        private String userPrompt = "";

        /**
         * 请求大模型的参数配置
         */
        private TextModelConfigDTO textModelConfig = new TextModelConfigDTO();
    }

}
