package com.zyhl.yun.api.outer.vo;

import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;
import lombok.Data;

import java.util.List;

/**
 * 算法任务结果VO 历史记录列表
 * <AUTHOR>
 */
@Data
public class AlgorithmTaskListResultVO {

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 任务状态
     *
     * @see TaskStatusEnum
     */
    private Integer status;
    
    /**
     * 异步算法任务结果
     */
    private List<AsyncTaskResult> taskResult;

    /***
     * 任务输入
     */
    private AsyncTaskRequestVO taskRequest;

    /***
     * 结果编码
     */

    private String resultCode;

    /***
     * 结果信息
     */

    private String resultMsg;
}
