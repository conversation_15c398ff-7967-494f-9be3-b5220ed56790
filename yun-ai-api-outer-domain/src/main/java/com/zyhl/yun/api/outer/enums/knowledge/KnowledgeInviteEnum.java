package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 邀请知识库的状态
 *
 * <AUTHOR>
 * @date 2025/04/17
 */
@Getter
@AllArgsConstructor
public enum KnowledgeInviteEnum {

    /**
     * 停用
     */
    STOP(0, "停用"),

    /**
     * 启用
     */
    ENABLE(1, "启用"),

    ;

    /**
     * 状态
     */
    private final Integer status;

    /**
     * 备注
     */
    private final String remark;

}
