package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识库类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum KnowledgeBaseTypeEnum {

    /**
     * 个人知识库
     */
    PERSONAL(1, "个人知识库"),

    /**
     * 分享知识库
     */
    SHARE(2, "分享知识库"),

    /**
     * 公共知识库
     */
    COMMON(3, "公共知识库"),

    ;

    /**
     * code
     */
    private final Integer code;

    /**
     * 备注
     */
    private final String remark;

    public static KnowledgeBaseTypeEnum getByCode(Integer code) {
        for (KnowledgeBaseTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static boolean isExist(Integer code) {
        return getByCode(code) != null;
    }


    public static boolean isPersonal(Integer code) {
        return PERSONAL.getCode().equals(code);
    }

    public static boolean isShare(Integer code) {
        return SHARE.getCode().equals(code);
    }

    public static boolean isCommon(Integer code) {
        return COMMON.getCode().equals(code);
    }
}
