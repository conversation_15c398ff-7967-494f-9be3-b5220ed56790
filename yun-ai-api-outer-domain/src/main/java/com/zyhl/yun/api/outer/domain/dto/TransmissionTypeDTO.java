package com.zyhl.yun.api.outer.domain.dto;

import lombok.Builder;
import lombok.Data;

/**
 * <b>className:</b>
 * {@link TransmissionTypeDTO} <br>
 * <b> description:</b>
 *
 * <AUTHOR>
 * @date 2024-05-08 17:16
 **/
@Data
@Builder
public class TransmissionTypeDTO {

    /**
     * 图片存储类型：1-个人云（默认），2-EOS对象存储
     */
    private Integer imageTransmissionType;

    /**
     * 文件id，存储内容与imageTransmissionType有关
     */
    private String fileId;

    /**
     * 内容大小
     */
    private Long fileSize;

    /**
     * 文件后缀名
     */
    private String fileSuffix;
}
