package com.zyhl.yun.api.outer.domain.validator.likecomment;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023年03月17日 14:39
 */

public class LikeCommentValidator implements ConstraintValidator<LikeCommentNotNull, Integer> {


    /**
     * 踩
     */
    private static final Integer DISLIKE = 0;

    /**
     * 赞
     */
    private static final Integer LIKE = 1;

    /**
     * 取消赞踩
     */
    private static final Integer CANCEL_COMMENT = -1;

    private static final Set<Integer> VALID_COMMENT = new HashSet<>(
        Arrays.asList(DISLIKE, LIKE, CANCEL_COMMENT));

    @Override
    public void initialize(LikeCommentNotNull constraintAnnotation) {
    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        if (value == null) {
            return false;
        }

        return VALID_COMMENT.contains(value);
    }

}
