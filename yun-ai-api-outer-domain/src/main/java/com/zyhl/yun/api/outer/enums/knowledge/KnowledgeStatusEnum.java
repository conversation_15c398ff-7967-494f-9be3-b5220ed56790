package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识库状态
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum KnowledgeStatusEnum {

    /**
     * 正常
     */
    NORMAL(0, "正常"),

    /**
     * 已删除
     */
    DELETED(1, "已删除"),

    /**
     * 删除中
     */
    DELETING(2, "删除中"),

    /**
     * 保险箱
     */
    SAFEBOX(3, "保险箱"),

    /**
     * 公开
     */
    OPEN(1, "公开"),

    /**
     * 私密
     */
    PRIVATE(0, "私密"),

    ;

    /**
     * 状态
     */
    private final Integer status;
    /**
     * 备注
     */
    private final String remark;

    public static boolean isNormal(Integer status) {
        return NORMAL.status.equals(status);
    }

    public static boolean isDeleted(Integer status) {
        return DELETED.status.equals(status);
    }

    public static boolean isPrivate(Integer status) {
        return PRIVATE.status.equals(status);
    }

}
