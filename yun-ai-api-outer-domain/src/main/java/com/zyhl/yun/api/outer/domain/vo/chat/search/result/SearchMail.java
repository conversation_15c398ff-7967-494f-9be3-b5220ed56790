package com.zyhl.yun.api.outer.domain.vo.chat.search.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 对话信息-搜索结果-邮件-列表数据
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchMail implements Serializable {

    /** 邮件所在文件夹的id */
    private String fid;

    /** 邮件ID */
    private String mailId;

    /** 重要邮件识别标签列表 */
    private List<String> importantLabel;

    /** 发件人 */
    private String from;

    /** 收件人 */
    private String to;

    /** 邮件主题 */
    private String subject;

    /** 收信时间，单位秒 */
    private Integer receiveDate;

    /**
     * 1:未读
     * 0:已读
     */
    private Integer read;

    /**
     * 邮件摘要
     */
    private String summary;

}
