package com.zyhl.yun.api.outer.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-04-16 16:50:23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ImportNoteInfoVO implements Serializable {
    /**
     * 笔记id
     */
    private String noteId;

    /**
     * 笔记所属笔记本id
     */
    private String noteBookId;

    /**
     * 笔记所属笔记本名称
     */
    private String noteBook;

    /**
     * 笔记标题
     */
    private String title;

    /**
     * 笔记类型，来自笔记对象中的expands
     * 0或者空：普通类型
     * 1：语音类型
     */
    private Integer noteType;

    /**
     * 笔记版本号
     */
    private String revision;

}