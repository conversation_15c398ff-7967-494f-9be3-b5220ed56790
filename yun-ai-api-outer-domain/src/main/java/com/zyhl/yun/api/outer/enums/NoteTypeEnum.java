package com.zyhl.yun.api.outer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum NoteTypeEnum {

    // 普通笔记
    SIMPLE_NOTE(0, "普通笔记"),
    // 录音笔记
    AUDIO_NOTE(1, "录音笔记"),
    // 语音笔记
    SEPARATION_NOTE(2, "语音笔记"),
    // AI听记笔记
    MAGIC_NOTE(3, "AI听记笔记"),
    // 智能录音笔记
    SMART_NOTE(4, "智能录音笔记"),

    ;

    /**
     * 笔记类型
     */
    private final Integer type;

    /**
     * 备注
     */
    private final String desc;
}
