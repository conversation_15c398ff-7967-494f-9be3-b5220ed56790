package com.zyhl.yun.api.outer.domain.entity;

import com.zyhl.yun.api.outer.domain.valueobject.Member;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 回忆相册生成过滤实体
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ResemblanceEntity {

    private String  classId;

    List<String> members;

    List<Member> resemblanceFile;
}
