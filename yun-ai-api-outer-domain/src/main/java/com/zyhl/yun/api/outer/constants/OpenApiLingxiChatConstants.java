package com.zyhl.yun.api.outer.constants;

/**
 * openapi lingxi 对话相关常量
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface OpenApiLingxiChatConstants {

	/**
	 * 模板id-简易导航卡片
	 */
	public static final String TEMPLATE_ID_OF_CARD_SIMPLE = "CmdcMneu";

	/**
	 * 模板id-简易导航追问卡片
	 */
	public static final String TEMPLATE_ID_OF_CARD_REPLY = "CmdcMneuReply";

	/**
	 * 大模型类型
	 */
	public static final String TYPE_OF_TEXT_MODEL = "1";
	/**
	 * 卡片类型
	 */
	public static final String TYPE_OF_CARD = "3";

	/**
	 * 属性-主意图
	 */
	public static final String ATTR_OF_COMMAND = "command";
	/**
	 * 属性-子意图
	 */
	public static final String ATTR_OF_SUB_COMMAND = "subCommand";
	/**
	 * 属性-对话
	 */
	public static final String ATTR_OF_DIALOGUE_ID = "dialogueId";

	/**
	 * 属性-获取最后的录音笔记id（配合ppt大纲使用）
	 */
	public static final String ATTR_OF_GET_LAST_VOICE_NOTE_ID = "lastVoiceNoteId";
	/**
	 * 灵犀对话标识
	 */
	public static final String LINGXI_CHAT_DATA_FLAG = "lingxiChatEndDataFlag";
	/**
	 * 灵犀对话标识
	 */
	public static final String LINGXI_CHAT_DATA_FLAG_JSON = "\\\"" + OpenApiLingxiChatConstants.LINGXI_CHAT_DATA_FLAG
			+ "\\\":";
	/**
	 * 灵犀对话结束
	 */
	public static final String LINGXI_END_CHAT_DONE = "[DONE]";

}
