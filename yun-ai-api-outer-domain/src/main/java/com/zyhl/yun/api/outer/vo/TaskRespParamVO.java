package com.zyhl.yun.api.outer.vo;

import com.zyhl.yun.api.outer.domain.dto.TransmissionTypeDTO;
import com.zyhl.yun.api.outer.enums.ImageTransmissionTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 响应参数
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TaskRespParamVO {

    /***
     * 资源类型：资源类型;0-无，1 邮件， 2 笔记， 3 图片
     */
    private Integer outResourceType;
    /***
     *  资源id
     */
    private String outResourceId;
    /***
     * 文本
     */
    private String outContent;
    /***
     * 评分
     */
    private Float score;
    /***
     * 图片存储类型：1-个人云（默认），2-EOS对象存储
     */
    private Integer imageTransmissionType;

    /**
     * 用户id
     */
    private String userId;

    public TaskRespParamVO imageResp(TransmissionTypeDTO transmissionTypeDTO) {
        this.outResourceType = 3;
        this.outResourceId = transmissionTypeDTO.getFileId();
        this.imageTransmissionType = transmissionTypeDTO.getImageTransmissionType();
        return this;
    }

    public TaskRespParamVO textResp(String outContent) {
        this.outResourceType = 0;
        this.outContent = outContent;
        return this;
    }

    public TaskRespParamVO textResp(String outContent, Float score) {
        this.outResourceType = 0;
        this.outContent = outContent;
        this.score = score;
        return this;
    }

    public boolean isEosImageResp() {
        return null != this.imageTransmissionType &&
            ImageTransmissionTypeEnum.EOS.getCode() == this.imageTransmissionType;
    }

    public boolean isYunDiskImageResp() {
        return null != this.imageTransmissionType &&
            ImageTransmissionTypeEnum.YUN_DISK.getCode() == this.imageTransmissionType;
    }
}
