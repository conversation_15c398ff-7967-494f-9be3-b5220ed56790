package com.zyhl.yun.api.outer.enums.chat;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述：对话流式输出状态
 *
 * <AUTHOR> zhumaoxian  2025/4/12 12:28
 */
@Getter
@AllArgsConstructor
public enum ChatAddFlowStatusEnum {

    PROCESSING("processing", "正在处理"),
    STOP("stop", "结束，整个对话的结束标识"),
    ERROR("error", "失败，整个对话的结束标识");

    private final String status;
    private final String desc;
    
	public static boolean isProcessing(String finishReason) {
		return PROCESSING.getStatus().equals(finishReason);
	}
    
	public static boolean isStop(String finishReason) {
		return STOP.getStatus().equals(finishReason);
	}

}
