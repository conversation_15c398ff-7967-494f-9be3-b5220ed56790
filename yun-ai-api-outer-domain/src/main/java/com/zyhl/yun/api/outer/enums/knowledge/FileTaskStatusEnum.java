package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文件转存任务状态
 * 0 未处理、1处理中、2 已完成、3处理失败 4.已过期
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FileTaskStatusEnum {

    /**
     * 未处理
     */
    UNPROCESSED(0, "未处理"),

    /**
     *  处理中
     */
    PROCESSING(1, "处理中"),

    /**
     * 已完成
     */
    FINISH(2, "已完成"),

    /**
     * 处理失败
     */
    FAIL(3, "处理失败"),

    /**
     * 已过期
     */
    OVERDUE(4, "已过期"),

    ;

    /**
     * 状态
     */
    private final Integer status;
    /**
     * 备注
     */
    private final String remark;



    public static FileTaskStatusEnum getByStatus(Integer status) {
        for (FileTaskStatusEnum value : FileTaskStatusEnum.values()) {
            if (value.getStatus().equals(status)) {
                return value;
            }
        }
        return null;
    }

    public static boolean isUnProcessed(Integer status) {
        return UNPROCESSED.status.equals(status);
    }

    public static boolean isProcessing(Integer status) {
        return PROCESSING.status.equals(status);
    }

    public static boolean isFinish(Integer status) {
        return FINISH.status.equals(status);
    }

    public static boolean isFail(Integer status) {
        return FAIL.status.equals(status);
    }

    public static boolean isOverdue(Integer status) {
        return OVERDUE.status.equals(status);
    }

}
