package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.ChatCommentEntity;
import com.zyhl.yun.api.outer.domain.vo.ChatCommentGetResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2024年02月28日 15:51
 */

public interface AlgorithmChatCommentRepository {

    /**
     * 保存用户评价结果
     * @Author: WeiJing<PERSON>un
     *
     * @param chatCommentEntity 需要保存的数据
     * @return true:保存成功，false:保存失败
     */
    boolean save(ChatCommentEntity chatCommentEntity);

    /**
     * 查询用户评价list
     * @Author: WeiJingKun
     *
     * @param entity 查询条件
     * @return 用户评价list
     */
    List<ChatCommentGetResult> get(ChatCommentEntity entity);

    /**
     * 根据对话id列表查询
     *
     * @param userId 用户id
     * @param dialogueIdList 对话id列表
     * @return 对话id，用户评价
     */
    Map<Long, ChatCommentGetResult> queryMapByDialogueIdList(String userId, List<Long> dialogueIdList);

    /**
     * 根据对话id查询
     *
     * @param userId 用户id
     * @param dialogueId 对话id
     * @return 用户评价
     */
    ChatCommentEntity getByDialogueId(String userId, Long dialogueId);
}
