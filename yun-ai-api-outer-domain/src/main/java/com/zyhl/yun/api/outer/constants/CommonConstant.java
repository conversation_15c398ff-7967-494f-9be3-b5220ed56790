package com.zyhl.yun.api.outer.constants;

import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;

/**
 * <p>
 * 公共常用量
 * </p>
 *
 * <AUTHOR>
 */
public class CommonConstant {

    public static final String REQUEST_ID_KEY = "requestId";

    public static final String USER_TOKEN_CACHE_KEY = "user:%s:token";

    public static final String USER_TASK_REMIND_CACHE_KEY = "user:%s:module:%s:taskId:%s:status:%s";

    public static final String QUEUE_NAME_CACHE_KEY = "new:ai:queue:module:%s";

    public static final String TASK_QUEUE_NAME_CACHE_KEY = "ai:image:task:queue:module:%s";

    public static final String TASK_FAILED_TIMES_CACHE_KEY = "ai:task:failed:times:%s";

    public static final String USER_QUEUE_LOCK_CACHE_KEY = "ai:queue:lock:%s:%s";

    public static final String USER_TASK_PROCESSING_CACHE_KEY = "new:ai:user:%s:task:%s";

    public static final String IMAGE_RULE_CACHE_KEY = "ai:image:rule:%s";

    public static final String IMAGE_URL_CACHE_KEY = "ai:image:content:url:%s";

    public static final String IMAGE_DOWNLOAD_URL_CACHE_KEY = "ai:image:content:download:url:%s";

    public static final String FILE_UPLOAD_RESULT_CACHE_KEY = "ai:image:file:upload:result:%s:%s:%s";

    public static final String USER_ACCREDIT_LOCK_CACHE_KEY = "hcy:ai:applet:accredit:lock:%s";
    public static final String USER_ACCREDIT_CANCEL_LOCK_CACHE_KEY = "hcy:ai:applet:accredit:cancel:lock:%s";

    public static final String IMAGE_RULE_WIDTH_HEIGHT_LENGTH_TEMPLATE = "请选择大小低于%sM，分辨率低于%s×%s的图片";

    public static final String IMAGE_RULE_SUFFIX_TEMPLATE = "请选择%s格式的图片进行处理";

    /**
     * 删除module 1和4 的cron表达式
     */
    public static final String DELETE_MODULE_1AND4_CRON_CACHE_KEY = "ai:delete:module:1and4";

    /**
     * 删除module 除了1和4 的cron表达式
     */
    public static final String DELETE_MODULE_OTHER_CRON_CACHE_KEY = "ai:delete:module:other";

    /**
     * 删除module 1和4 的 分布式锁key
     */
    public static final String DELETE_MODULE_1AND4_LOCK_CACHE_KEY = "ai:delete:module:1and4:lock";

    /**
     * 删除module 除了1和4 的 分布式锁key
     */
    public static final String DELETE_MODULE_OTHER_LOCK_CACHE_KEY = "ai:delete:module:other:lock";

    /**
     * 删除module 一次数量大小
     */
    public static final String DELETE_MODULE_SIZE_CACHE_KEY = "ai:delete:module:size";

    /**
     * 图片压缩默认大小5M 5242880
     */
    public static final long IMAGE_COMPRESS_DEFAULT_SIZE = 5242880L;

    /**
     * 图片压缩名称后缀
     */
    public static final String IMAGE_COMPRESS_NAME_SUFFIX = "_thumbnail";

    public static final String USER_TASK_REMIND_CACHE_KEY_STATUS = "last:user:%s:module:%s:taskId:%s:status:%s";

    /**
     * 获取AI弹窗协议配置的cacheKey
     */
    public static final String AI_POP_UP_PROTOCOL_QUERY_CACHE_KEY = "ai:pop:up:protocol:query:%s:%s";

    /**
     * 漫画风字段类型前缀
     */
    public static final String AI_ANIMATION_DICTIONARY_PREFIX = "ANIMATION_";

    /**
     * AI编辑 缓存baiLianAccessTokenKey
     */
    public static final String AI_EDIT_BAI_LIAN_ACCESS_TOKEN_KEY = "yun:ai:edit:baiLianAccessTokenKey";

    /**
     * AI编辑 缓存历史会话记录 Key前缀
     */
    public static final String AI_EDIT_HISTORY_LIST = "yun:ai:edit:historyList:";
    /**
     * AI举报 Key前缀
     */
    public static final String AI_REPORT_LOCK = "yun:ai:report:lock:%s:%s";

    /**
     * 妙云相机-端外
     */
    public static final String CLOUD_CAMERA_EXTERNAL = "cloud-camera-external";

    /**
     * 妙云相机-端内
     */
    public static final String CLOUD_CAMERA_WITHIN = "cloud-camera-within";

    /**
     * AI写真-端外
     */
    public static final String AI_PHOTOGRAPHY_EXTERNAL = "ai-photography-external";

    /**
     * AI写真-端内
     */
    public static final String AI_PHOTOGRAPHY_WITHIN = "ai-photography-within";

    /**
     * 最后一次任务缓存key
     */
    public static final String LAST_TASK_CACHE_KEY = "yun:ai:api:last:task:%s";


    /**
     * AI授权开关 启用
     */
    public static final String AI_AUTHORIZE_OPEN= "enable";

    /**
     * AI授权开关 禁用
     */
    public static final String AI_AUTHORIZE_CLOSE = "disable";


    public static final String ROW_KEY_PREFIX = "rowKey_";

    /** 搜索知识库资源（没有知识库） */
    public static final String SEARCH_KNOWLEDGE_BASE_RESOURCE_NO_KNOWLEDGE = DialogueIntentionEnum.SEARCH_KNOWLEDGE_BASE_RESOURCE.getInstruction()+"-no-knowledge";

    /**
     * 个人云时间转换
     * 格式化日期为ISO 8601标准格式：yyyy-MM-dd'T'HH:mm:ss.SSSXXX
     */
    public static final String PERSONAL_DATE_TIME_PATTERN = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX";

    /**
     * 扫描文件解析队列表加入文件解析-【最大】消息发送数量控制
     */
    public static final String SCAN_USER_KNOWLEDGE_QUEUE_FILE_ADD_PARSE_JOB_MSG_MAX_SEND_NUM = "yun:ai:job:scanUserKnowledgeQueueFileAddParseJob:msgMaxSendNum";


    /**
     * 年-月-日 时间格式
     */
    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    /**
     * 年 时间格式 2025-06-12 格式化后 2025
     */
    public static final String DATE_FORMAT_YEAR = "yyyy";

    /**
     * 年 时间格式 2025-06-12 格式化后 202506
     */
    public static final String DATE_FORMAT_YEAR_MONTH = "yyyyMM";
}
