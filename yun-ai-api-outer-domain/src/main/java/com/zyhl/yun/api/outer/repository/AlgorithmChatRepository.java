package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatMessageEntity;

/**
 * <AUTHOR>
 * @version 2024年03月20日 11:33
 */

public interface AlgorithmChatRepository {

    /**
     * 会话信息保存接口
     *
     * @param messageEntity 会话信息entity
     * @param contentEntity 会话内容entity
     * @return Boolean
     */
    Boolean saveChatInfo(AlgorithmChatMessageEntity messageEntity, AlgorithmChatContentEntity contentEntity);
}
