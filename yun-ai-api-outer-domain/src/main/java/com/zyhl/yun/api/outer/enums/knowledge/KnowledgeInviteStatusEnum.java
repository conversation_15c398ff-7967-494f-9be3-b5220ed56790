package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Knowledge Invite Status Enum
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-04-17 14:58:53
 */
@Getter
@AllArgsConstructor
public enum KnowledgeInviteStatusEnum {

    /**
     * 停用
     */
    DISABLE(0, "停用"),

    /**
     * 启用
     */
    ENABLE(1, "启用"),
    ;

    /**
     * 状态
     */
    private final Integer status;
    /**
     * 备注
     */
    private final String remark;
}
