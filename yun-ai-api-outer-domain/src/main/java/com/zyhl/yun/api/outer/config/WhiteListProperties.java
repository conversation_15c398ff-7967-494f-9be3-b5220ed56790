package com.zyhl.yun.api.outer.config;

import java.util.ArrayList;
import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;

/**
 * 白名单配置
 *
 * @Author: WeiJingKun
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "white-list")
public class WhiteListProperties {

    /**
     * 邮件搜索白名单配置
     */
    private MailSearch mailSearch;

    /**
     * 大模型对话VIP用户，第一版需要百炼大模型配置apiKeyVip，否则会报错
     */
    private List<String> textModelVipUser = new ArrayList<>();

    /**
     * 文本模型编码默认白名单用户
     */
    private List<String> textModelCodeWhiteUser = new ArrayList<>();

    /**
     * 全网搜推荐默认白名单用户
     */
    private List<String> allNetworkSearchWhiteUser = new ArrayList<>();

    /**
     * 送审白名单用户
     */
    private List<String> auditWhiteUser = new ArrayList<>();

    /**
     * 知识库-总结、建议、发言使用的白名单，空数组表示全开
     */
    private List<String> knowledgeSummaryWhiteUser = new ArrayList<>();

    /**
     * aippt白名单配置
     */
    private AipptWhite aipptWhite;

    /**
     * 邮件搜索白名单配置
     */
    @Data
    public static class MailSearch {

        /**
         * 总开关
         */
        private Boolean enable;

        /**
         * 邮箱后缀
         */
        private String suffix;

        /**
         * 非白名单用户提示
         */
        private String prompt;

        /**
         * 白名单列表
         */
        private List<String> whiteList;

        /**
         * 鉴权：开关打开时，所有人能用；关闭时，白名单能用
         */
        public boolean canUse(String userId) {
            if (null != enable && enable) {
                // 开关打开时，所有人能用
                return true;
            } else {
                // 开关关闭时，白名单能用
                return CollUtil.isNotEmpty(whiteList) && whiteList.contains(userId);
            }
        }

    }

    /**
     * aippt白名单配置
     */
    @Data
    public static class AipptWhite {

        /**
         * 总开关
         */
        private Boolean enable;

        /**
         * 白名单列表（手机号）
         */
        private List<String> whiteList;

        /**
         * 支持的渠道列表（助手编码）
         */
        private List<String> supportedChannels;

        /**
         * 鉴权：开关打开时，所有人能用；关闭时，白名单能用
         */
        public boolean canUse(String phone) {
            if (null != enable && enable) {
                // 开关打开时，所有人能用
                return true;
            } else {
                // 开关关闭时，白名单能用
                return CollUtil.isNotEmpty(whiteList) && whiteList.contains(phone);
            }
        }

        /**
         * 检查渠道是否支持aippt功能
         * @param assistantCode 助手编码
         * @return true-支持，false-不支持
         */
        public boolean isSupportedChannel(String assistantCode) {
            // 如果没有配置支持的渠道列表，默认只支持yunmail
            if (CollUtil.isEmpty(supportedChannels)) {
                return "yunmail".equals(assistantCode);
            }
            return supportedChannels.contains(assistantCode);
        }

    }

}
