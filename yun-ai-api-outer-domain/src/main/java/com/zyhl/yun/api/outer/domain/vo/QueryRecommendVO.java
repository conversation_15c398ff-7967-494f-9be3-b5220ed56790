package com.zyhl.yun.api.outer.domain.vo;


import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.zyhl.yun.api.outer.constants.Const;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 问题推荐列表VO
 *
 * @Author: WeiJingKun
 */
@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryRecommendVO {

    /**
     * 问题类型
     */
    private Integer type = 1;

    /**
     * 推荐问题
     */
    private String query;

    public QueryRecommendVO(String query) {
        this.query = formatStr(query);
    }

    /**
     * 格式化文本，返回的数据有可能是数组，需要格式化一下
     *
     * @param text
     * @return
     */
    private String formatStr(String text) {
        if (CharSequenceUtil.isEmpty(text)) {
            return "";
        }
        if (text.indexOf(Const.LEFT_BRACKET) == 0) {
            try {
                List<String> list = JSON.parseArray(text, String.class);
                return list.get(0);
            } catch (Exception e) {
                log.error("字符串格式化失败：{}，错误信息：{}", text, e.getMessage());
            }
        }
        return text;
    }
}
