package com.zyhl.yun.api.outer.domain.vo;

import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.PartInfo;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.UploadFormInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description}
 * <p>
 *
 * <AUTHOR>
 * @since 4月14 2025
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileCreateVO {

  /**
   * 父目录id
   */
  public String parentFileId;

  /**
   * 文件id
   */
  public String fileId;

  /**
   * 文件类型，枚举file/folder
   */
  public String type;

  /**
   * 文件名
   */
  public String fileName;

  /**
   * 是否已经秒传
   */
  public Boolean rapidUpload;

  /**
   * 上传id
   */
  public String uploadId;

  /**
   * 分段信息
   */
  public PartInfo[] partInfos;

  /**
   * 文件是否已存在，仅当renameMode=refuse或auto_rename时且存在同名文件时返回。
   */
  public Boolean exist;

  /**
   * 表单上传信息 注：仅表单上传方式返回
   */
  public UploadFormInfo formInfo;

  /**
   * 知识库Id
   */
  private String baseId;
}
