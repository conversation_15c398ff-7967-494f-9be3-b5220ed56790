package com.zyhl.yun.api.outer.domain.entity.ocr;

import com.zyhl.yun.api.outer.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 卡证识别结果
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CertificateOcrEntity extends BaseEntity {
    /**
     * 图片是否已裁剪
     * 0——未裁剪
     * 1——已裁剪
     */
    private int cropState;
    /**
     * 卡证二级类别
     * 1——身份证
     * 2——社保卡
     * 3——驾驶证
     * 4——行驶证
     * 5——护照
     * 6——港澳通行证
     * 7——居住证
     * 8——学生证
     * 9——房产证
     * 10——户口本
     * 11——银行卡
     */
    private int cardType;
}
