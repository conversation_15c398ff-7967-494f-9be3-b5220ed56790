package com.zyhl.yun.api.outer.enums.task;

/**
 * AI输入算法任务-付费类型
 * 
 * @Author: liu<PERSON><PERSON>wen
 */
public enum TaskFeeTypeEnum {

	/**
	 * 不需要付费
	 */
	NO(0, "不需要付费"),

	/**
	 * 需要付费
	 */
	YES(1, "需要付费"),
	;


	TaskFeeTypeEnum(Integer code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	/**
	 * 编码
	 */
	private Integer code;
	/**
	 * 描述
	 */
	private String desc;

	public Integer getCode() {
		return code;
	}

	public String getDesc() {
		return desc;
	}

}
