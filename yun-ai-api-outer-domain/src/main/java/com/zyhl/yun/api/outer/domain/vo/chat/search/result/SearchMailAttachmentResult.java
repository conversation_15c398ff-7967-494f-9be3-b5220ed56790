package com.zyhl.yun.api.outer.domain.vo.chat.search.result;

import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 对话信息-搜索结果-邮件附件
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchMailAttachmentResult extends SearchCommonResult implements Serializable {

    /**
     * 邮件附件搜索结果列表
     */
    private List<SearchMailAttachment> searchMailAttachmentList;
    /**
     * 总记录数
     */
    private Integer totalCount;

}
