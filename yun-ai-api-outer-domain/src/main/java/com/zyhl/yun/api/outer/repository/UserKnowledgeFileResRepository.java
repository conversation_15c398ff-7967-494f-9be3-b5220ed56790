package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileResEntity;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * className:UserKnowledgeFileResRepository
 *
 * <AUTHOR>
 * @date 2025/02/13
 */
public interface UserKnowledgeFileResRepository {

    /**
     * 查询文件列表
     *
     * @param userId  用户id
     * @param fromFileIds 文件id集合
     * @return 实体对象集合
     */
    List<UserKnowledgeFileResEntity> selectByFileIds(@NotNull String userId, @NotNull List<String> fromFileIds);

    /**
     * 查询文件列表
     *
     * @param userId  用户id
     * @param fileIds 文件id集合
     * @return 实体对象集合
     */
    List<UserKnowledgeFileResEntity> getResourceByFileIds(@NotNull String userId, @NotNull List<String> fileIds);

    /**
     * 查询文件列表
     *
     * @param userId      用户id
     * @param baseId 知识库id
     * @param fromFileIds 文件id集合
     * @param fromResourceType 来源文件资源类型；0 云盘个人云（默认） 1 邮件  2 笔记
     * @return 实体对象集合
     */
    List<UserKnowledgeFileResEntity> selectByFromFileIds(@NotNull String userId, @NotNull Long baseId,@NotNull List<String> fromFileIds,@NotNull Integer fromResourceType);

     /**
     * 查询文件列表
     *
     * @param userId      用户id
     * @param baseId 知识库id
     * @param fromFileId 文件id
     * @param fromResourceType 来源文件资源类型；0 云盘个人云（默认） 1 邮件  2 笔记
     * @return 实体对象集合
     */
    List<UserKnowledgeFileResEntity> selectByFromFileId(@NotNull String userId, @NotNull Long baseId,@NotNull String fromFileId,@NotNull Integer fromResourceType);

    /**
     * 查询文件列表
     *
     * @param userId           用户id
     * @param baseId           知识库id
     * @param fileId      文件id
     * @param fromResourceType 来源文件资源类型；0 云盘个人云（默认） 1 邮件  2 笔记
     * @return 实体对象集合
     */
     List<UserKnowledgeFileResEntity> getResourceByFileId(@NotNull String userId, @NotNull Long baseId, @NotNull String fileId, @NotNull Integer fromResourceType);


    /**
     * 批量查询文件列表
     *
     * @param userId  用户id
     * @param fromFileIds 文件id集合
     * @return 实体对象集合
     */
    List<UserKnowledgeFileResEntity> batchSelectByFileIds(@NotNull String userId, @NotNull List<String> fromFileIds);

    /**
     * 更新
     *
     * @param entity 参数
     * @return int
     */
    int update(@NotNull UserKnowledgeFileResEntity entity);

    /**
     * 查询笔记文件
     * @param userId
     * @param fileId
     * @return
     */
    UserKnowledgeFileResEntity selectNoteByFileId(String userId, String fileId);
}
