package com.zyhl.yun.api.outer.domain.vo.knowledge;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.dashscope.utils.JsonUtils;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.enums.knowledge.FileCategoryEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 个人知识库文件
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class PersonalKnowledgeFileVO {
    /**
     * 文件id
     */
    private String fileId;
    /**
     * 名称
     */
    private String name;
    /**
     * 类型，枚举值file/folder
     */
    private String type;
    /**
     * 分类， 根据文件的后缀名和mime-type对文件进行分了分类，主要分类有：app：安装包 ；zip：压缩包 image：图片； doc：文档 video：视频 ；audio：音频 folder：目录 ；others：其他
     */
    private String category;
    /**
     * 创建时间，RFC 3339，2019-08-20T06:51:27.292+08:00
     */
    private String createdAt;
    /**
     * 更新时间，RFC 3339，2019-08-20T06:51:27.292+08:00
     */
    private String updatedAt;
    /**
     * 大小，单位：字节
     */
    private Long size;
    /**
     * 文件扩展名，一般是后缀名 注：不区分大小写
     */
    private String fileExtension;
    /**
     * 文件内容 hash 值，长度64位，需要根据 contentHashAlgorithm 指定的算法
     */
    private String contentHash;
    /**
     * 文件内容hash算法名（摘要算法名称），当前仅支持sha1或者sha256，不区分大小写内容hash算法名，当前hash算法是sha1或sha256； 旧底座是md5
     */
    private String contentHashAlgorithm;
    /**
     * 处理结果 -1--算法处理失败 0--算法处理中 1--算法处理成功
     */
    private Integer aiStatus;
    /**
     * 结果描述，失败时返回
     */
    private String aiDescription;

    /**
     * 源文件类型
     *
     * @see KnowledgeResourceTypeEnum
     */
    private Integer originFileType;

    /**
     * 源文件ID
     * 当originFileType=0时，为个人云文件ID；
     * 当originFileType=1时，为个邮件ID；
     * 当originFileType=2时，为云盘笔记ID；
     * 当originFileType=3时，为网页信息的；
     */
    private String originFileId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 网页链接信息
     */
    private UserKnowledgeFileEntity.HtmlInfo htmlInfo;

    public PersonalKnowledgeFileVO(UserKnowledgeFileEntity entity) {
        this.fileId = entity.getFileId();
        this.name = entity.getFileName();
        this.type = FileTypeEnum.getYunDiskFileType(entity.getFileType());
        this.category = FileCategoryEnum.getYunDiskCategory(entity.getCategory());
        this.createdAt = DateUtil.format(entity.getCreateTime(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
        this.updatedAt = DateUtil.format(entity.getUpdateTime(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
        this.size = entity.getFileSize();
        this.fileExtension = entity.getExtension();
        this.contentHash = entity.getHashValue();
        this.contentHashAlgorithm = entity.getHashName();
        this.originFileType = entity.getFromResourceType();
        this.originFileId = entity.getOldFileId();
        this.userId = entity.getUserId();
        this.auditStatus = entity.getAuditStatus();

        if (KnowledgeResourceTypeEnum.isHtml(entity.getFromResourceType())) {
            htmlInfo = JsonUtils.fromJson(entity.getFromResource(), UserKnowledgeFileEntity.HtmlInfo.class);
            if (ObjectUtil.isEmpty(this.name) && Objects.nonNull(htmlInfo)) {
                this.name = htmlInfo.getTitle();
            }
        }
    }


    public void failed(String reason) {
        this.aiStatus = -1;
        if (CharSequenceUtil.isNotBlank(reason)) {
            this.aiDescription = reason;
        } else {
            this.aiDescription = "哎呀，解析过程出了点小意外，请稍后再试试看";
        }
    }

    public void sensitive() {
        this.aiStatus = -1;
        this.aiDescription = "该文件内容包含敏感信息，请选择其它文件重新上传";
    }

    public void processing() {
        this.aiStatus = 0;
        this.aiDescription = "该文件正在全力解析中，请耐心等待";
    }

    public void succeed() {
        this.aiStatus = 1;
        this.aiDescription = "该文件已解析完成，快来与AI助手对话试试";
    }
}
