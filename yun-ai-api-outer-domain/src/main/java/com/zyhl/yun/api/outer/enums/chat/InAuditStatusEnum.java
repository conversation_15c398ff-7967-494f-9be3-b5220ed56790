package com.zyhl.yun.api.outer.enums.chat;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * AI会话内容-输入内容审批结果状态
 * @Author: shixiaokang
 */
public enum InAuditStatusEnum {

    /**
     * 审核失败
     */
    FAIL(-1, "失败"),
    /**
     * 审核通过
     */
    SUCCESS(2, "成功"),

    ;

    private static final Map<Integer, InAuditStatusEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(InAuditStatusEnum.class).forEach(item -> MAP.put(item.code, item));
    }

    public static InAuditStatusEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return MAP.get(code);
    }

    /**
     * 是否存在
     * @param code 编码
     * @return true-存在
     */
    public static boolean isExist(Integer code) {
        return getByCode(code) != null;
    }

    InAuditStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 编码
     */
    private Integer code;

    /**
     * 描述
     */
    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    
}
