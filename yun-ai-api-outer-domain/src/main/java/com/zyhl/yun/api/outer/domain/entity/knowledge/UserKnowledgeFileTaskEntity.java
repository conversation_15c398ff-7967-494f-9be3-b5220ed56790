package com.zyhl.yun.api.outer.domain.entity.knowledge;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 个人知识库文件转存任务表
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UserKnowledgeFileTaskEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 知识库ID
     */
    private Long baseId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 文件归属，个人云 owner_id= user_id
     */
    private String ownerId;

    /**
     * 业务类型：
     * -1 - 未知类型
     * 1-personal 个人云
     * 2-group 圈子
     * 3-shareGroup 共享群
     * 4-family 家庭云
     * 5-activity 活动空间 照片直播
     * 6-note 笔记
     * 7-cardpackage 卡包
     * 8-system 系统空间
     * 9-partner 合作空间
     * 10 -mount 挂载盘
     *
     * @see com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum
     */
    private Integer ownerType;


    /**
     * 任务状态;任务状态，0 未处理、1处理中、2 已完成、3处理失败 4.已过期
     */
    private Integer taskStatus;

    /**
     * TRANSFER 转存平台转存任务
     * DELETE 独立空间批量删除任务
     *
     * @see com.zyhl.yun.api.outer.enums.knowledge.KnowledgeTaskTypeEnum
     */
    private String taskType;

    /**
     * 第三方平台的任务ID
     */
    private String thirdTaskId;

    /**
     * json格式 端侧个人知识库批量上传文档接口请求参数
     */
    private String taskRequest;

    /**
     * json格式,端侧根据这个结果显示上传文件状态
     * [{"fielId":"","result":"","description":""} ]
     */
    private String taskResponse;

    /**
     * 请求时间; MQ消息中的操作时间
     */
    private String eventTime;

    /**
     * 当次任务执行开始时间
     */
    private Date startTime;

    /**
     * 当次任务执行结束时间
     */
    private Date finishTime;

    /**
     * 任务查询次数
     */
    private Integer executeCount;

    /**
     * 失败重试任务数量
     */
    private Integer retryTaskCount;

    /**
     * 上一次的失败任务ID
     */
    private String preThirdTaskId;

    /**
     * 过期时间：NULL--代表任务无超时限制
     */
    private Date expireTime;

    /**
     * 文件id 逗号分隔
     */
    private String fileIds;

    /**
     * 上传id 逗号分隔
     */
    private String uploadIds;

    /**
     * 转存文件数
     */
    private Integer fileNum;

    /**
     * 成功文件数;处理成功文件数
     */
    private Integer successNum;

    /**
     * 失败文件数量
     */
    private Integer failNum;


    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误描述
     */
    private String errorMessage;
}
