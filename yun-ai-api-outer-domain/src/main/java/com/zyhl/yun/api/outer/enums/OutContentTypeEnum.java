package com.zyhl.yun.api.outer.enums;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 输出文本类型枚举
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
public enum OutContentTypeEnum {

    /**
     * 文本
     */
    TEXT(1, "普通文本"),

    /**
     * 富文本
     */
    RICH_TEXT(2, "富文本"),

    /**
     * JSONObject
     */
    JSON_OBJECT(3, "JSONObject"),
    ;

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 名称
     */
    private final String name;


    private static final Map<Integer, OutContentTypeEnum> MAP = new ConcurrentHashMap<>();


    static {
        EnumSet.allOf(OutContentTypeEnum.class).forEach(item -> MAP.put(item.type, item));
    }

    public static OutContentTypeEnum getType(Integer type) {
        if (null == type) {
            return null;
        }
        return MAP.get(type);
    }

    public static boolean isExist(Integer type) {
        return getType(type) != null;
    }

    OutContentTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}
