package com.zyhl.yun.api.outer.enums;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description}
 * <p>
 *
 * <AUTHOR>
 * @since 4月23 2025
 */
//@Getter
//public enum TaskStatusEnum {
//  //旧：任务状态，1 待处理、2 处理中、3 任务完成、4 任务失败 5.已过期
//  //新：-1--处理失败 0--处理中 1--处理成功
//  /**
//   * 处理失败
//   */
//  FAILURE(-1, "处理失败", 4),
//  /**
//   * 处理中
//   */
//  PROCESSING(0, "处理中", 2),
//  /**
//   * 处理成功
//   */
//  SUCCESS(1, "处理成功", 3),
//
//  ;
//
//  private final Integer code;
//
//  private final String message;
//
//  private final Integer oldCode;
//
//  TaskStatusEnum(Integer code, String message, Integer oldCode) {
//    this.code = code;
//    this.message = message;
//    this.oldCode = oldCode;
//  }
//
//  /**
//   * 通过msg获取code
//   *
//   * @param msg 描述
//   * @return
//   */
//  public static Integer getCodeByMsg(String msg) {
//    if (msg == null) {
//      return null;
//    }
//    TaskStatusEnum enumList = getByMsg(msg);
//    if (enumList == null) {
//      return null;
//    }
//    return enumList.getCode();
//  }
//
//  /**
//   * 通过code获取msg
//   *
//   * @param code 描述
//   * @return
//   */
//  public static String getMsgByCode(Integer code) {
//    if (code == null) {
//      return null;
//    }
//    TaskStatusEnum enumList = getByCode(code);
//    if (enumList == null) {
//      return null;
//    }
//    return enumList.getMessage();
//  }
//
//  /**
//   * 通过枚举code获得枚举
//   * values() 方法将枚举转变为数组
//   *
//   * @return TaskStatusEnum
//   */
//  public static TaskStatusEnum getByCode(Integer code) {
//    for (TaskStatusEnum enumList : values()) {
//      if (enumList.getCode()
//          .equals(code)) {
//        return enumList;
//      }
//    }
//    return null;
//  }
//
//  /**
//   * 通过枚举code获得枚举
//   * values() 方法将枚举转变为数组
//   *
//   * @return TaskStatusEnum
//   */
//  public static TaskStatusEnum getByMsg(String msg) {
//    for (TaskStatusEnum enumList : values()) {
//      if (enumList.getMessage()
//          .equals(msg)) {
//        return enumList;
//      }
//    }
//    return null;
//  }
//
//  public static Integer getCodeByOldCode(Integer oldCode) {
//    if (oldCode == null || oldCode == 0) {
//      return null;
//    }
//    for (TaskStatusEnum enumList : values()) {
//      if (oldCode.equals(enumList.getOldCode())) {
//        return enumList.getCode();
//      }
//    }
//    return null;
//  }
//
//  public static Integer getOldCodeByCode(Integer code) {
//    if (code == null) {
//      return null;
//    }
//    for (TaskStatusEnum enumList : values()) {
//      if (code.equals(enumList.getCode())) {
//        return enumList.getOldCode();
//      }
//    }
//    return null;
//  }
//
//}
