package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.ApplicationTypeListEntity;
import com.zyhl.yun.api.outer.domain.vo.ChatApplicationType;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2024年04月16日 15:51
 */
public interface ChatApplicationTypeRepository {

    /**
     * 查询对话应用类型信息list
     *
     * @param entity 查询条件
     * @return 对话应用类型信息list
     * @Author: WeiJingKun
     */
    List<ChatApplicationType> typeList(ApplicationTypeListEntity entity);

    /**
     * 获取对话应用类型信息，转map，key是id
     *
     * @return 应用id，对话应用类型信息
     * @Author: WeiJingKun
     */
    Map<String, ChatApplicationType> listToMapKeyIsId();

    /**
     * 根据应用id获取对话应用类型信息
     *
     * @param applicationId 应用id
     * @return 对话应用类型信息
     * @Author: WeiJing<PERSON>un
     */
    ChatApplicationType getByApplicationId(String applicationId);

    /**
     * 根据应用id获取对话应用类型信息
     *
     * @param idList
     * @return
     */
    List<ChatApplicationType> getByAppList(List<String> idList);
}
