package com.zyhl.yun.api.outer.domain.vo;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2024/4/15 16:34
 */
@Data
@Builder
public class ChatApplicationType implements Serializable {

    private static final long serialVersionUID = -3271077795545816229L;

    /**
     * 应用id
     */
    private String applicationId;

    /**
     * 应用名称,即标题
     */
    private String applicationName;

    /**
     * 应用类型，英文简称。
     * @see com.zyhl.yun.api.outer.enums.ApplicationTypeEnum
     */
    private String applicationType;

    /**
     * 应用头像url
     */
    private String avatarUrl;

    /**
     * 应用开场白
     */
    private String openingLine;

    /**
     * 引导文案
     */
    private String guidText;

    /**
     * tab标签名称，多个以英文逗号分隔。
     */
    private String tabLabel;

    /**
     * tab标签英文名称，多个以英文逗号分隔。国际化需要。
     */
    private String tabLabelEn;


    /**
     * 排序值，值越小，优先级越高。
     */
    private Integer sort;

    /**
     * 关联的会话ID
     */
    private String sessionId;

    /**
     * 应用关联id,supplier_type=阿里，指通义星尘的角色id；supplier_type=自研，指模型名称（例如deepseek r1 7b 或 deepseek r1 671b）
     */
    private String typeRelationId;
    
    /**
     * 厂商编码，用于路由不同厂商的智能体 0 自研 2 阿里
     */
    private String supplierType;

}
