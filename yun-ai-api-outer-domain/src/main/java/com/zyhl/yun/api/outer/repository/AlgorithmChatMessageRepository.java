package com.zyhl.yun.api.outer.repository;

import com.github.pagehelper.PageInfo;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatMessageEntity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2024/2/29 14:15
 */
public interface AlgorithmChatMessageRepository {

    /**
     * 获取会话信息分页数据
     * @Author: WeiJingKun
     *
     * @param entity 会话数据
     * @param offset 分页偏移量
     * @param pageSize 分页数
     * @param needTotalCount 是否需要总数
     * @return 会话分页数据
     */
    PageInfo<AlgorithmChatMessageEntity> chatList(AlgorithmChatMessageEntity entity, int offset, int pageSize, int needTotalCount);

    /**
     * 根据会话id、用户id查询会话信息
     *
     * @param sessionId 会话id
     * @param userId 用户id
     * @return 会话信息entity
     */
    AlgorithmChatMessageEntity queryBySessionId(String sessionId, String userId);

    /**
     * 根据会话id列表d、用户id查询会话信息
     *
     * @param entity 会话信息查询条件
     * @return 会话信息entity
     */
    List<AlgorithmChatMessageEntity> queryBySessionIdList(AlgorithmChatMessageEntity entity);

    /**
     * 根据id列表删除会话信息
     * @Author: WeiJingKun
     *
     * @param entity 会话信息
     * @return true:删除成功，false:删除失败
     */
    boolean deleteByIds(AlgorithmChatMessageEntity entity);


    /**
     * 保存会话信息
     * @param entity 会话信息
     * @return true:保存成功，false:保存失败
     */
    Boolean saveMessage(AlgorithmChatMessageEntity entity);


    /**
     * 更新会话信息
     * @param entity 会话信息
     * @return true:更新成功，false:更新失败
     */
    Boolean updateMessage(AlgorithmChatMessageEntity entity);

    /**
     * 根据业务类型查询会话id
     * @param userId 用户id
     * @param businessType 业务类型
     * @return 应用id，会话信息
     */
    Map<String, AlgorithmChatMessageEntity> findSessionIdByBusinessType(String userId, String businessType);

	/**
	 * 查找是否存在正常的sessionId+businessType
	 * 
	 * @param userId       用户id
	 * @param sessionId    sessionId
	 * @param businessType 业务类型
	 * @return
	 */
	boolean existNormalBySessionIdAndBusinessType(String userId, Long sessionId, String businessType);

    /**
     * 根据业务类型查询会话信息
     * @param userId 用户id
     * @param applicationId 应用id
     * @param businessType 应用类型
     * @return 会话信息Entity
     */
    AlgorithmChatMessageEntity selectOneByApplicationId(String userId, String applicationId, String businessType);

}
