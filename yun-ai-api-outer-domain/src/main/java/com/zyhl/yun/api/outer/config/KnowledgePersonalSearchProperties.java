package com.zyhl.yun.api.outer.config;

import com.zyhl.hcy.yun.ai.common.base.enums.ParseTypeEnum;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;
import java.util.List;

/**
 * className: KnowledgePersonalSearchProperties
 * description: 个人知识库-多路召回检索配置
 *
 * <AUTHOR>
 * @date 2025/2/13
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "knowledge.personal.rag.search")
public class KnowledgePersonalSearchProperties {

    /**
     * 多路检索召回策略数组
     */
    private Type type;

    /**
     * 知识库检索类型（只针对文档切片SPLIT）
     */
    private Split split;

    /**
     * smalltobig策略合并阈值（只针对文档切片SPLIT）
     */
    private SmallToBig smallToBig;


    @Data
    public static class Type {

        /**
         * 策略类型集合
         * 默认：SPLIT
         * @see com.zyhl.hcy.yun.ai.common.base.enums.ParseTypeEnum
         */
        private List<String> list = Collections.singletonList("SPLIT");
    }

    @Data
    public static class Split {

        /**
         * 类型枚举
         * 0：固定切片长度 (默认)
         * 1：small to big策略
         */
        private Integer type = 0;
    }

    @Data
    public static class SmallToBig {

        /**
         * 具体阈值
         * 大于等于阈值，则合并取大切片的内容
         */
        private Float mergeSize = 0.5F;
    }

    /**
     * 根据配置的多路检索召回策略数组，判断当前召回策略是否开启
     * @param parseTypeList 切片类型配置集合
     * @param parseTypeEnum 切片类型枚举
     * @return 是否开启
     */
    public static boolean checkStrategyIsOpen(List<String> parseTypeList, ParseTypeEnum parseTypeEnum) {
        return parseTypeList.contains(parseTypeEnum.getCode());
    }
}
