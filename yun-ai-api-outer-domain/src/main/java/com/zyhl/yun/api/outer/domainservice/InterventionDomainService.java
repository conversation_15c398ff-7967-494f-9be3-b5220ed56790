package com.zyhl.yun.api.outer.domainservice;

import com.zyhl.yun.api.outer.vo.InterventionVO;

/**
 * 干预库领域服务类
 *
 * <AUTHOR>
 */
public interface InterventionDomainService {

    /**
     * 获取答案 1、问题向量化 2、es搜索 3、判断评分阈值
     *
     * @param channel       渠道号
     * @param clientType    客户端类型
     * @param clientVersion 客户端版本
     * @param h5Version     h5版本
     * @param question      问题
     * @return 不为空在表示存在答案，为空表示不存在答案
     */
    InterventionVO getIntervention(String channel, String clientType, String clientVersion, String h5Version, String question);

}
