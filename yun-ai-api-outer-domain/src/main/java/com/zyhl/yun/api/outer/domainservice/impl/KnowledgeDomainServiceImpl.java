package com.zyhl.yun.api.outer.domainservice.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.dialogue.RecallConfig;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.dialogue.RerankConfig;
import com.zyhl.yun.api.outer.config.KnowledgeDialogueProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.config.VipKnowledgeCommonProperties;
import com.zyhl.yun.api.outer.domain.dto.KnowledgeDialogueConfigDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.KnowledgeBusinessEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.KnowledgeDialogueConfigEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.domainservice.KnowledgeDomainService;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeDialogueConfigTypeEnum;
import com.zyhl.yun.api.outer.repository.*;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.VersionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 知识库领域服务实现，包括个人知识库和公共知识库
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class KnowledgeDomainServiceImpl implements KnowledgeDomainService {

    @Resource
    private UserKnowledgeFileRepository userKnowledgeFileRepository;
    @Resource
    private KnowledgeFileRepository knowledgeFileRepository;
    @Resource
    private KnowledgeBusinessRepository knowledgeBusinessRepository;
    @Resource
    private RedisOperateRepository redisOperateRepository;
    @Resource
    private KnowledgeDialogueProperties knowledgeDialogueProperties;
    @Resource
    protected SourceChannelsProperties sourceChannelsProperties;
    @Resource
    private VipKnowledgeCommonProperties vipKnowledgeCommonProperties;
    @Resource
    private KnowledgeDialogueConfigRepository knowledgeDialogueConfigRepository;
    @Resource
    private UserKnowledgeRepository userKnowledgeRepository;

    @Override
    public boolean personalEnable(String userId) {
        // 先查缓存
        Boolean existFile = redisOperateRepository.getUserKnowledgeExistFile(userId);
        if (existFile == null) {
            // 查数据库，是否有数据
            existFile = userKnowledgeFileRepository.count(userId) > 0;
            // 缓存
            redisOperateRepository.setUserKnowledgeExistFile(userId, existFile);
        }

        // 存在文件 并且 打开个人知识库对话开关
        return existFile && knowledgeDialogueProperties.getPersonalSwitch();
    }

    @Override
    public List<UserKnowledgeEntity> personalEnable2(String userId) {
        // 查询选中的知识库
        return userKnowledgeRepository.getSelectedBase(userId);
    }

    @Override
    public boolean commonEnable(String sourceChannel) {
        String baseId = knowledgeDialogueProperties.getKnowledgeBaseId();

        // 查绑定关系
        SourceChannelsProperties.SourceChannel channel = sourceChannelsProperties.getByChannel(sourceChannel);
        KnowledgeBusinessEntity entity = knowledgeBusinessRepository.selectOne(baseId, channel.getCode());
        if (entity == null) {
            // 不存在绑定关系
            return false;
        }

        // 查缓存
        Boolean existFile = redisOperateRepository.getCommonKnowledgeExistFile(baseId);
        if (existFile == null) {
            // 查数据库，知识库是否有数据
            existFile = knowledgeFileRepository.count(baseId) > 0;
            // 缓存
            redisOperateRepository.setCommonKnowledgeExistFile(baseId, existFile);
        }

        // 知识库没有文件
        if (!existFile) {
            return false;
        }

        // 白名单 或者 打开开关
        return knowledgeDialogueProperties.getWhiteList().contains(RequestContextHolder.getPhoneNumber())
                || entity.isOpen();
    }

    @Override
    public boolean vipCommonEnable() {
        // VIP白名单 并且 打开开关
        return vipKnowledgeCommonProperties.getWhiteList().contains(RequestContextHolder.getPhoneNumber())
                && vipKnowledgeCommonProperties.getVipCommonSwitch();
    }

    @Override
    public KnowledgeDialogueConfigDTO getUserDialogueConfig(String userId) {
        String dialogueConfig = redisOperateRepository.getDialogueConfig(userId);
        if (ObjectUtil.isNotEmpty(dialogueConfig)) {
            return JsonUtil.parseObject(dialogueConfig, KnowledgeDialogueConfigDTO.class);
        }

        KnowledgeDialogueConfigDTO dto = new KnowledgeDialogueConfigDTO();

        // 缓存为空，查询数据库
        List<KnowledgeDialogueConfigEntity> list = knowledgeDialogueConfigRepository.selectByUserId(userId);
        for (KnowledgeDialogueConfigEntity entity : list) {
            if (KnowledgeDialogueConfigTypeEnum.isRecall(entity.getType())) {
                dto.setRecallConfig(JsonUtil.parseObject(entity.getConfigJson(), RecallConfig.class));
            } else if (KnowledgeDialogueConfigTypeEnum.isRerank(entity.getType())) {
                dto.setRerankConfig(JsonUtil.parseObject(entity.getConfigJson(), RerankConfig.class));
            }
        }

        // 保存缓存
        redisOperateRepository.setDialogueConfig(userId, JsonUtil.toJson(dto));

        return dto;
    }
}
