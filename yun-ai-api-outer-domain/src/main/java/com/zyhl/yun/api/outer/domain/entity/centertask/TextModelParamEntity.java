package com.zyhl.yun.api.outer.domain.entity.centertask;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 创建ai算法任务-文本模型类-请求参数
 *
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TextModelParamEntity implements Serializable {

    /**
     * userId_对话id
     * 用于查询hbase数据
     */
    private String rowkey;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 对话id
     */
    private String dialogueId;

    /**
     * 会话id，上下文唯一标识
     */
    private String sessionId;

    /**
     * 指令，比如：总结概要
     */
    private String commands;

    /**
     * 智能体关联id，2024-04-16
     */
    private String typeRelationId;
}
