package com.zyhl.yun.api.outer.util;

import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import cn.hutool.core.text.CharSequenceUtil;

/**
 * 意图识别工具类
 *
 * <AUTHOR>
 */
public class IntentionUtils {
    /**
     * 意图算法编码列表
     */
    private static final Set<String> INTENTIONS_REQUIRING_EMPTY_RESOURCE_ID = new HashSet<>(Arrays.asList(
            DialogueIntentionEnum.AI_HEAD_SCULPTURE.getCode(),
            DialogueIntentionEnum.PICTURE_COMIC_STYLE.getCode(),
            DialogueIntentionEnum.OLD_PHOTOS_REPAIR.getCode(),
            DialogueIntentionEnum.PICTURE_GENERATE_TEXT.getCode(),
            DialogueIntentionEnum.INTELLIGENT_CUTOUT.getCode(),
            DialogueIntentionEnum.AI_EXPANSION_MAP.getCode(),
            DialogueIntentionEnum.IMAGE_QUALITY_RESTORATION.getCode(),
            DialogueIntentionEnum.AI_PHOTO_EDIT.getCode(),
            DialogueIntentionEnum.SMART_FAKE_CHECK.getCode())
    );

    /**
     * 返回链接的条件判断
     *
     * @param intention  意图结果
     * @param resourceId 文件id
     * @return 布尔值 true表示同步返回跳转链接
     */
    public static boolean returnLinkCondition(String intention, String resourceId) {
        // AI工具相关意图并且没传资源id (妙云相机、AI消除、智能抠图不管是否传资源id都直接返回跳转链接) 或者 创建笔记意图
        return isIntentionWithEmptyResourceId(intention, resourceId) || DialogueIntentionEnum.isJumpLinkIntention(intention)
                || DialogueIntentionEnum.CREATE_NOTE.getCode().equals(intention)
                || DialogueIntentionEnum.CREATE_VOICE_NOTE.getCode().equals(intention)
                || DialogueIntentionEnum.KNOWLEDGE_ENTRANCE.getCode().equals(intention);
    }

    /**
     * 说明是跳链
     *
     * @param intention  意图
     * @param resourceId 资源ID
     * @return 返回结果
     */
    private static boolean isIntentionWithEmptyResourceId(String intention, String resourceId) {
        return INTENTIONS_REQUIRING_EMPTY_RESOURCE_ID.contains(intention) && CharSequenceUtil.isEmpty(resourceId);
    }

    /**
     * 返回链接的条件判断V2
     *
     * @param intention  意图结果
     * @param resourceId 文件id
     * @return 布尔值 true表示同步返回跳转链接
     */
    public static boolean returnLinkConditionV2(String intention, String resourceId) {
        // AI工具相关意图并且没传资源id (妙云相机、AI消除、智能抠图不管是否传资源id都直接返回跳转链接) 或者 创建笔记意图
        return isIntentionWithEmptyResourceIdV2(intention, resourceId) || DialogueIntentionEnum.isJumpLinkIntentionV2(intention)
                || DialogueIntentionEnum.CREATE_NOTE.getCode().equals(intention)
                || DialogueIntentionEnum.CREATE_VOICE_NOTE.getCode().equals(intention)
                || DialogueIntentionEnum.KNOWLEDGE_ENTRANCE.getCode().equals(intention);
    }

    /**
     * 说明是跳链
     *
     * @param intention  意图
     * @param resourceId 资源ID
     * @return 返回结果
     */
    private static boolean isIntentionWithEmptyResourceIdV2(String intention, String resourceId) {
        return INTENTIONS_REQUIRING_EMPTY_RESOURCE_IDV2.contains(intention) && CharSequenceUtil.isEmpty(resourceId);
    }

    /**
     * 意图算法编码列表
     */
    private static final Set<String> INTENTIONS_REQUIRING_EMPTY_RESOURCE_IDV2 =
            new HashSet<>(Arrays.asList(
                    DialogueIntentionEnum.AI_HEAD_SCULPTURE.getCode(),
                    DialogueIntentionEnum.PICTURE_COMIC_STYLE.getCode(),
                    DialogueIntentionEnum.OLD_PHOTOS_REPAIR.getCode(),
                    DialogueIntentionEnum.PICTURE_GENERATE_TEXT.getCode(),
                    DialogueIntentionEnum.INTELLIGENT_CUTOUT.getCode(),
                    DialogueIntentionEnum.AI_EXPANSION_MAP.getCode(),
                    DialogueIntentionEnum.IMAGE_QUALITY_RESTORATION.getCode(),
                    DialogueIntentionEnum.AI_PHOTO_EDIT.getCode(),
                    DialogueIntentionEnum.SMART_FAKE_CHECK.getCode())
            );
}
