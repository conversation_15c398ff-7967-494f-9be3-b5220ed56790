package com.zyhl.yun.api.outer.domain.vo.chat.search.param;

import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 对话信息-搜索参数-邮件附件列表
 * 
 * <AUTHOR>
 * @date 2025-04-17
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchMailAttachmentParam extends SearchCommonParam implements Serializable {


    /**
     * 附件名称
     */
    private String attachName;

    /**
     * 已读未读 0 已读，1 未读
     */
    private Integer read;
    /**
     * 附件后缀数组，docx,pdf....， or 关系
     */
    private List<String> suffixList;
    /**
     * 发件人数组，  or 关系
     */
    private List<String> fromList;
    /**
     * 开始时间，结束时间。格式：yyyyMMddHHmmss
     */
    private MillisecondTimeRange timeRange;


}
