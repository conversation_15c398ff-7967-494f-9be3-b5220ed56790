package com.zyhl.yun.api.outer.domainservice.impl;

import cn.hutool.core.collection.CollUtil;
import com.zyhl.yun.api.outer.domain.aggregate.PictureFileAggregate;
import com.zyhl.yun.api.outer.domain.entity.ResemblanceEntity;
import com.zyhl.yun.api.outer.domainservice.ResemblanceService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 回忆相册生成过滤重复服务实现
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
@Service
@Slf4j
public class ResemblanceServiceImpl implements ResemblanceService {

    @Override
    public List<ResemblanceEntity> imageCluster(List<PictureFileAggregate> imageClusterInputEntities, double similarityThreshold) {
        if (CollUtil.isEmpty(imageClusterInputEntities)) {
            return null;
        }
        // 获取有效的数据，并跳过 null 的特征向量
        Map<String, double[]> collect = imageClusterInputEntities.stream()
        		// 过滤掉 null 的特征向量
                .filter(entity -> entity.getFeature() != null)  
                .collect(Collectors.toMap(
                        PictureFileAggregate::getFileId,
                        entity -> convertFloatToDouble(entity.getFeature())
                ));
        // 获取特征向量为 null 的数据，并打印它们的 fileId
        if (collect.size() != imageClusterInputEntities.size()) {
            List<String> nullFeatureFileIds = imageClusterInputEntities.stream()
                    .filter(entity -> entity.getFeature() == null)
                    .map(PictureFileAggregate::getFileId)
                    .collect(Collectors.toList());
            log.warn("向量聚类任务-文件缺失向量数据,缺失向量数据文件id:{}", nullFeatureFileIds);
        }
        List<Set<String>> groups = groupSimilarImages(collect, similarityThreshold);
        return resultAssembly(groups);
    }

    private List<ResemblanceEntity> resultAssembly(List<Set<String>> groups) {
        List<ResemblanceEntity> imageClusters = new ArrayList<>();
        for (int i = 0; i < groups.size(); i++) {
            Set<String> group = groups.get(i);
            ResemblanceEntity imageCluster = new ResemblanceEntity();
            // 设置 classId，格式为 class_00, class_01, ...
            String classId = String.format("class_%02d", i);
            imageCluster.setClassId(classId);
            // 将 Set<String> 转换为 List<String>
            List<String> memberList = new ArrayList<>(group);
            imageCluster.setMembers(memberList);
            imageClusters.add(imageCluster);
        }
        return imageClusters;
    }

    private static double[] convertFloatToDouble(List<Float> floatArray) {
        double[] doubleArray = new double[floatArray.size()];
        for (int i = 0; i < floatArray.size(); i++) {
            BigDecimal b = new BigDecimal(String.valueOf(floatArray.get(i)));
            double doubleValue = b.doubleValue();
            doubleArray[i] = doubleValue;
        }
        return doubleArray;
    }

    // 按余弦相似度分组并合并相似组
    private static List<Set<String>> groupSimilarImages(Map<String, double[]> vectorStorage, double threshold) {
        // 获取所有图片的文件id
        List<String> imageKeys = new ArrayList<>(vectorStorage.keySet());
        int fileCount = imageKeys.size();
        // 创建一个标记数组，用于标记已经访问过的图片
        boolean[] visited = new boolean[fileCount];
        // 创建一个存储分组的列表
        List<Set<String>> groups = Collections.synchronizedList(new ArrayList<>());
        // 使用并行流进行分组和合并
        imageKeys.parallelStream().forEach(fileId -> {
            // 标记当前图片为已访问
            synchronized (visited) {
                int index = imageKeys.indexOf(fileId);  // 获取图片在列表中的索引
                if (visited[index]) {
                	return;
                }
                visited[index] = true;
            }
            // 获取当前图片的向量
            double[] vectorA = vectorStorage.get(fileId);
            // 创建一个新的分组，并添加当前图片
            Set<String> newGroup = Collections.synchronizedSet(new HashSet<>());
            newGroup.add(fileId);
            // 遍历其他图片，检查相似度并添加到分组中
            for (int comparedFiles = 0; comparedFiles < fileCount; comparedFiles++) {
                // 跳过自身
                if (imageKeys.get(comparedFiles).equals(fileId)) {
                	continue;
                }
                // 跳过已访问过的图片
                synchronized (visited) {
                    if (visited[comparedFiles]) {
                    	continue;
                    }
                }
                //未对比过的图片
                String imageB = imageKeys.get(comparedFiles);
                double[] vectorB = vectorStorage.get(imageB);
                // 计算余弦相似度
                double similarity = cosineSimilarity(vectorA, vectorB);
                // 如果相似度大于阈值，则将图片添加到新分组中
                if (similarity >= threshold) {
                    synchronized (visited) {
                        // 如果图片未访问过，则将其添加到新分组中并标记为已访问
                        if (!visited[comparedFiles]) {
                            newGroup.add(imageB);
                            visited[comparedFiles] = true;
                        }
                    }
                }
            }
            // 如果新分组不为空，则添加到结果列表中
            if (!newGroup.isEmpty()) {
                // 在添加新分组之前，检查是否需要合并已有的分组
                synchronized (groups) {
                    boolean merged = false;
                    for (Set<String> group : groups) {
                        for (String imageInGroup : group) {
                            if (cosineSimilarity(vectorStorage.get(imageInGroup), vectorA) >= threshold) {
                                // 合并新分组到已有分组
                                group.addAll(newGroup);
                                merged = true;
                                break;
                            }
                        }
						if (merged) {
							break;
						}
                    }
                    if (!merged) {
                        groups.add(newGroup); // 如果没有合并，添加新分组
                    }
                }
            }
        });
        return groups;
    }


    // 计算两个向量的余弦相似度
    public static double cosineSimilarity(double[] vectorA, double[] vectorB) {
        double dotProduct = 0.0;
        double normA = 0.0;
        double normB = 0.0;
        for (int i = 0; i < vectorA.length; i++) {
            dotProduct += vectorA[i] * vectorB[i];
            normA += Math.pow(vectorA[i], 2);
            normB += Math.pow(vectorB[i], 2);
        }
        return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }
}
