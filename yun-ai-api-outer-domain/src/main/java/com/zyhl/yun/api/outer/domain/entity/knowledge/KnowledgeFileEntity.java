package com.zyhl.yun.api.outer.domain.entity.knowledge;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 公共知识库文件资源表
 * <AUTHOR>
 */
@Data
@Builder
public class KnowledgeFileEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 知识库的标识
     * common 公共知识库
     * customer 客服
     */
    private String baseId;

    /**
     * 文件内容md5
     */
    private String fileId;

    /**
     * 文件存储类型
     * eos-eos对象存储
     */
    private String storeType;

    /**
     * 存储的key
     * 如果是eos，则是eos的key
     */
    private String storeKey;

    /**
     * 文件哈希名
     */
    private String hashName;

    /**
     * 文件哈希值
     */
    private String hashValue;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 文件后缀
     */
    private String extension;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人id
     */
    private String createdBy;

    /**
     * 更新人id
     */
    private String updatedBy;

    /**
     * 业务算法组编码
     * 4-文档向量化算法组（公共知识库）
     */
    private int algorithmGroupCode;

    /**
     * 算法id
     */
    private String algorithmIds;

    /**
     * 算法结果状态
     * 默认 0 未处理
     * 1 成功
     * 2 失败
     */
    private Integer status;

    /**
     * 算法结果码
     * 0000 成功
     * 其他则错误码
     */
    private String resultCode;

    /**
     * 算法结果
     * 记录错误信息；成功的结果保存到hbase;文本和图片向量不写入这里
     */
    private String resultMsg;

    /**
     * 执行次数
     */
    private Integer executeCount;

    /**
     * 删除标识，0-正常，1-已删除
     */
    private Integer delFlag;
}
