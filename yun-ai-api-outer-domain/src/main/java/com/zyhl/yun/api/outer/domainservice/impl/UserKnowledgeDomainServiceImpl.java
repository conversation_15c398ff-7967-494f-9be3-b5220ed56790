package com.zyhl.yun.api.outer.domainservice.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.properties.OwnerDriveProperties;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.RecallQueryDTO;
import com.zyhl.yun.api.outer.config.KnowledgePersonalProperties;
import com.zyhl.yun.api.outer.constants.KnowledgeConstants;
import com.zyhl.yun.api.outer.domain.dto.CatalogConfigDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.*;
import com.zyhl.yun.api.outer.domain.valueobject.PersonalKnowledgeBase;
import com.zyhl.yun.api.outer.domainservice.UserDriveConfigDomainService;
import com.zyhl.yun.api.outer.domainservice.UserKnowledgeDomainService;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.*;
import com.zyhl.yun.api.outer.repository.*;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 用户知识库服务实现类
 *
 * <AUTHOR> zhumaoxian  2025/4/18 11:14
 */
@Slf4j
@Service
public class UserKnowledgeDomainServiceImpl implements UserKnowledgeDomainService {

    @Resource
    private UserKnowledgeRepository userKnowledgeRepository;

    @Resource
    private UidGenerator uidGenerator;

    @Resource
    private OwnerDriveProperties ownerDriveProperties;

    @Resource
    private UserDriveConfigDomainService userDriveConfigDomainService;

    @Resource
    private KnowledgePersonalProperties knowledgePersonalProperties;
    @Resource
    private UserKnowledgeFileRepository userKnowledgeFileRepository;
    @Resource
    private AlgorithmKnowledgeConfigRepository algorithmKnowledgeConfigRepository;
    @Resource
    private KnowledgeBusinessRepository knowledgeBusinessRepository;

    @Resource
    private UserKnowledgeInviteRepository userKnowledgeInviteRepository;

    /**
     * 创建默认用户知识库
     *
     * @return 默认用户知识库实体
     */
    @Override
    public UserKnowledgeEntity createDefaultKnowledge() {
        String userId = RequestContextHolder.getUserId();
        List<UserKnowledgeEntity> entityList = userKnowledgeRepository.selectByUserId(userId);
        if (CollectionUtils.isEmpty(entityList)) {
            try {
                UserKnowledgeEntity entity = buildDefaultKnowledgeEntity(userId);
                userKnowledgeRepository.add(entity);
                return entity;
            } catch (Exception e) {
                log.error("【创建默认用户知识库】异常,userId:{} ｜ e:", userId, e);
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_SERVER_INTERNAL);
            }
        }

        return entityList.get(0);
    }

    @Override
    public List<PersonalKnowledgeBase> getUserKnowledgeListByIdList(String userId,List<Long> baseIdList) {
        List<UserKnowledgeEntity> userKnowledgeList = userKnowledgeRepository.selectByIds(baseIdList);
        if (CollectionUtils.isEmpty(userKnowledgeList)) {
            return Collections.emptyList();
        }
        List<PersonalKnowledgeBase> knowledgeBaseList = new ArrayList<>();
        // 知识库邀请列表
        List<UserKnowledgeInviteEntity> inviteEntityList = userKnowledgeInviteRepository.getListByUserId(baseIdList, userId);
        // 数据处理
        for (UserKnowledgeEntity entity : userKnowledgeList) {
            PersonalKnowledgeBase knowledgeBase = new PersonalKnowledgeBase(entity);
            /** baseType处理 */
            // 如果baseId存在于邀请列表中，设置baseType为分享知识库
            if (inviteEntityList.stream().anyMatch(item -> item.getKnowledgeId().equals(entity.getId()))) {
                knowledgeBase.setBaseType(KnowledgeBaseTypeEnum.SHARE.getCode());
            } else {
                knowledgeBase.setBaseType(KnowledgeBaseTypeEnum.PERSONAL.getCode());
            }
            knowledgeBaseList.add(knowledgeBase);
        }
        return knowledgeBaseList;
    }

    @Override
    public Long getUserKnowledgeIdByUserIdBizType(String userId, Integer bizType) {
        UserKnowledgeEntity userKnowledgeEntity = userKnowledgeRepository.selectNoteSync(userId, bizType);
        if (ObjectUtil.isEmpty(userKnowledgeEntity)) {
            return null;
        }
        return userKnowledgeEntity.getId();
    }

    @Override
    public List<RecallQueryDTO.KnowledgeGroup> fileList(List<String> fileIdList) {
        List<RecallQueryDTO.KnowledgeGroup> knowledgeGroupList = new ArrayList<>();
        // todo 添加baseId过滤
        List<UserKnowledgeFileEntity> entityList = userKnowledgeFileRepository.selectByFileIds(null, fileIdList);
        for (UserKnowledgeFileEntity entity : entityList) {
            RecallQueryDTO.KnowledgeGroup group = new RecallQueryDTO.KnowledgeGroup();
            group.setFileId(entity.getFileId());
            group.setUserId(entity.getUserId());
            group.setBaseId(Objects.isNull(entity.getBaseId()) ? null : String.valueOf(entity.getBaseId()));
            group.setKnowledgeBase(KnowledgeBaseEnum.PERSONAL.getCode());
            knowledgeGroupList.add(group);
        }

        return knowledgeGroupList;
    }

    @Override
    public List<RecallQueryDTO.KnowledgeGroup> baseList(List<PersonalKnowledgeBase> baseList, String businessCode) {
        List<RecallQueryDTO.KnowledgeGroup> knowledgeGroupList = new ArrayList<>();

        List<Long> personalBaseIdList = new ArrayList<>();
        List<String> commonBaseIdList = new ArrayList<>();
        for (PersonalKnowledgeBase base : baseList) {
            if (KnowledgeBaseTypeEnum.isPersonal(base.getBaseType())) {
                personalBaseIdList.add(Long.valueOf(base.getBaseId()));
            } else if (KnowledgeBaseTypeEnum.isShare(base.getBaseType())) {
                personalBaseIdList.add(Long.valueOf(base.getBaseId()));
            } else if (KnowledgeBaseTypeEnum.isCommon(base.getBaseType())) {
                commonBaseIdList.add(base.getBaseId());
            }
        }

        if (ObjectUtil.isNotEmpty(personalBaseIdList)) {
            List<UserKnowledgeEntity> entityList = userKnowledgeRepository.selectByIds(personalBaseIdList);
            for (UserKnowledgeEntity entity : entityList) {
                RecallQueryDTO.KnowledgeGroup group = new RecallQueryDTO.KnowledgeGroup();
                group.setBaseId(String.valueOf(entity.getId()));
                group.setUserId(entity.getUserId());
                group.setKnowledgeBase(KnowledgeBaseEnum.PERSONAL.getCode());

                knowledgeGroupList.add(group);
            }
        }
        if (ObjectUtil.isNotEmpty(commonBaseIdList)) {
            List<AlgorithmKnowledgeConfigEntity> entityList = algorithmKnowledgeConfigRepository.selectByIds(commonBaseIdList);
            for (AlgorithmKnowledgeConfigEntity entity : entityList) {
                if (KnowledgeStatusEnum.isDeleted(entity.getDelFlag())
                        || KnowledgeEffectEnum.isUnEffect(entity.getIsEffect())) {
                    // 删除或者不生效
                    continue;
                }

                KnowledgeBusinessEntity businessEntity = knowledgeBusinessRepository.selectOne(entity.getId(), businessCode);
                if (businessEntity == null || !businessEntity.isOpen()) {
                    // 不存在绑定关系或者未打开
                    continue;
                }

                RecallQueryDTO.KnowledgeGroup group = new RecallQueryDTO.KnowledgeGroup();
                group.setBaseId(entity.getId());
                group.setKnowledgeBase(KnowledgeBaseEnum.COMMON.getCode());

                knowledgeGroupList.add(group);
            }
        }

        return knowledgeGroupList;
    }

    @Override
    public UserKnowledgeEntity createNoteSyncKnowledge() {
        String userId = RequestContextHolder.getUserId();
        UserKnowledgeEntity entity;
        try {
            entity = buildDefaultKnowledgeEntity(userId);
            entity.setBizType(BizTypeEnum.NOTE_SYNC.getCode());
            entity.setOpenLevel(KnowledgeOpenLevelEnum.PRIVATE.getLevel());
            entity.setDelFlag(KnowledgeStatusEnum.NORMAL.getStatus());
            entity.setName("隐藏笔记知识库");
            userKnowledgeRepository.add(entity);
            return entity;
        } catch (Exception e) {
            log.error("【创建笔记默认用户知识库】异常,userId:{} ｜ e:", userId, e);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_SERVER_INTERNAL);
        }
    }

    /**
     * 构建默认用户知识库实体
     *
     * @param userId 用户ID
     * @return 构建完成的用户知识库实体
     */
    private UserKnowledgeEntity buildDefaultKnowledgeEntity(String userId) {
        UserKnowledgeEntity entity = new UserKnowledgeEntity();
        entity.setId(uidGenerator.getUID());
        entity.setUserId(userId);
        entity.setOwnerId(ownerDriveProperties.getDefaultOwnerid());

        // 设置知识库目录ID
        setFolderId(entity, userId);

        // 设置头像信息
        setProfilePhoto(entity);

        // 设置其他默认属性
        setDefaultAttributes(entity, userId);

        log.info("【创建默认用户知识库】 entity:{}", JsonUtil.toJson(entity));
        return entity;
    }

    /**
     * 设置知识库目录ID
     *
     * @param entity 用户知识库实体
     * @param userId 用户ID
     */
    private void setFolderId(UserKnowledgeEntity entity, String userId) {
        UserDriveConfigEntity configEntity = userDriveConfigDomainService.createDrive(userId, RequestContextHolder.getBelongsPlatform());
        CatalogConfigDTO.CatalogConfigInfo configInfo = userDriveConfigDomainService.createKnowledgeConfig(configEntity);
        String catalogId = userDriveConfigDomainService.createCatalog(userId, KnowledgeConstants.DEFAULT_KNOWLEDGE_NAME, configInfo.getCatalogId());
        entity.setFolderId(catalogId);
    }

    /**
     * 设置头像信息
     *
     * @param entity 用户知识库实体
     */
    private void setProfilePhoto(UserKnowledgeEntity entity) {
        List<KnowledgePersonalProperties.ProfilePhoto> profilePhotoList = knowledgePersonalProperties.getProfilePhotoList();
        if (!CollectionUtils.isEmpty(profilePhotoList)) {
            entity.setProfilePhoto(JsonUtil.toJson(profilePhotoList.get(0)));
        }
    }

    /**
     * 设置默认属性
     *
     * @param entity 用户知识库实体
     * @param userId 用户ID
     */
    private void setDefaultAttributes(UserKnowledgeEntity entity, String userId) {
        entity.setName(KnowledgeConstants.DEFAULT_KNOWLEDGE_NAME);
        entity.setDescription(null);

        entity.setOwnerType(OwnerTypeEnum.AI.getOwnerValue());
        entity.setDelFlag(KnowledgeStatusEnum.NORMAL.getStatus());
        entity.setSelected(KnowledgeSelectedEnum.NOT_SELECTED.getStatus());

        Date nowDate = new Date();
        entity.setCreatedBy(userId);
        entity.setUpdatedBy(userId);
        entity.setCreateTime(nowDate);
        entity.setUpdateTime(nowDate);
    }
}
