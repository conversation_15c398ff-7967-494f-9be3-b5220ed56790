package com.zyhl.yun.api.outer.config.note;

import com.zyhl.hcy.yun.ai.common.rag.dto.validate.dialogue.RecallConfig;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.dialogue.RerankConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * 笔记助手搜索配置
 *
 * <AUTHOR>
 */
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "note.search")
public class NoteSearchProperties {

    // 查询文档解析是否完成--最大重试次数
    private Integer maxRetryCount = 10;
    // 查询文档解析是否完成--重试间隔（毫秒）
    private Integer retryIntervalMillis = 1000;

    /** 过滤关键字 */
    private List<String> excludeKeywords = new ArrayList<>();

    /**
     * 是否高亮显示
     */
    private boolean highlight =  true;
    /**
     * 高亮显示前标记
     */
    private String preTags;

    /**
     * 高亮显示后标记
     */
    private String postTags;

    /**
     * 多路召回配置(笔记正文搜索)
     */
    private RecallConfig recallConfig = new RecallConfig();

    /**
     * 【向量】算法重排配置
     */
    private RerankConfig vectorRerankConfig = new RerankConfig();

    /**
     * 【关键字】算法重排配置
     */
    private RerankConfig keywordRerankConfig = new RerankConfig();
}
