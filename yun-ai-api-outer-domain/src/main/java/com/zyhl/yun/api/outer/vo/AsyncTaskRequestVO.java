package com.zyhl.yun.api.outer.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 算法任务输入
 * <AUTHOR>
 */
@Data
public class AsyncTaskRequestVO {

    /**
     * 传送类型
     * 1—url传送
     * 2—base64传送
     * 3—fileId
     */
    private Integer sendType;

    /**
     * 风格
     */
    private String style;

    /**
     * base64
     */
    private String base64;

    /**
     * 文件id
     */
    private String fileId;

    /**
     * 文件Url
     */
    private String fileUrl;

    /**
     * 图片扩展名
     */
    private String imageExt;

    /**
     * 参考SupplierTypeEnum供应商类型
     */
    private String supplierType;

    /**
     * 渠道id
     */
    private String channelId;

    /**
     * 请求时间
     */
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date requestTime;
}
