package com.zyhl.yun.api.outer.domain.entity.knowledge;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * className:UserKnowledgeFileResEntity
 *
 * <AUTHOR>
 * @date 2025/02/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserKnowledgeFileResEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 来源文件id
     */
    private String fromFileId;

    /**
     * 文件id
     */
    private String fileId;

    /**
     * 来源文件资源类型
     * 0 云盘个人云（默认）
     * 1 邮件
     * 2 笔记
     */
    private Integer fromResourceType;

    /**
     * 资源文件md5
     */
    private String resMd5;

    /**
     * 资源类型
     * 1 图片
     * 2 音频
     * 3 视频
     * 4 文档
     * 5 应用
     * 6 压缩文件
     * 0 其他
     * 100 普通目录
     */
    private Integer resType;

    /**
     * 文件归属
     */
    private String ownerId;

    /**
     * 业务类型
     * -1 - 未知类型
     * 12 - 邮箱
     * 11-ai 知识库
     * 1-personal 个人云
     * 2-group 圈子
     * 3-shareGroup 共享群
     * 4-family 家庭云
     * 10 -mount 挂载盘
     * 6-note 笔记
     * 7-cardpackage 卡包
     * 8-system 系统空间
     * 9-partner 合作空间
     * 5-activity 活动空间 照片直播
     */
    private Integer ownerType;

    /**
     * paas平台编码
     */
    private String paasCode;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 文件后缀
     */
    private String extension;

    /**
     * 文件修改时间
     */
    private Date fileUpdatedAt;

    /**
     * 文件创建时间
     */
    private Date fileCreatedAt;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标识
     */
    private Integer delFlag;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     *
     * 审核结果
     */
    private String auditResult;

}