package com.zyhl.yun.api.outer.config.textmodel;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 快速阅读配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "text-model.speedread-chat")
public class SpeedReadProperties {

    /**
     * 模型编码
     */
    private String modelCode = "blian";

    /**
     * 模型最大长度限制
     */
    private Integer modelMaxLength = 50000;

    /**
     * 用户问题提示词
     */
    private String userPrompt = "\n用户的问题：";
} 