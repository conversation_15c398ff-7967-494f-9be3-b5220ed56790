package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识库枚举类
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum KnowledgeBaseEnum {

    /**
     * 人知识库
     */
    PERSONAL("personal", "个人知识库", "个人知识库"),

    /**
     *
     */
    COMMON("common", "公共知识库", "公共知识库"),

    /**
     * 所有知识库
     */
    KNOWLEDGE("knowledge", "知识库", "所有知识库，包含个人和公共"),
    ;

    /**
     * 编码
     */
    private final String code;
    /**
     * 名称
     */
    private final String name;
    /**
     * 备注
     */
    private final String remark;



    public static KnowledgeBaseEnum getByCode(String code) {
        for (KnowledgeBaseEnum e : KnowledgeBaseEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }

    /**
     * 知识库类型数量
     *
     * @return 数量
     */
    public static int baseCount() {
        return KnowledgeBaseEnum.values().length - 1;
    }

    public static boolean isPersonal(String code) {
        return PERSONAL.code.equals(code);
    }

    public static boolean isCommon(String code) {
        return COMMON.code.equals(code);
    }

    public static boolean isKnowledge(String code) {
        return KNOWLEDGE.code.equals(code);
    }
}
