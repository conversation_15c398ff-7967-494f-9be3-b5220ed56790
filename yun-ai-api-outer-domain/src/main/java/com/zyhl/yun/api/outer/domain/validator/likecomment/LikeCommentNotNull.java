package com.zyhl.yun.api.outer.domain.validator.likecomment;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2023年03月17日 14:39
 */

@Documented
@Constraint(validatedBy = LikeCommentValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface LikeCommentNotNull {

    String message() default "likeComment不能为空且取值范围为-1, 0, 1；1:赞， 0:踩, -1:取消赞踩";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
