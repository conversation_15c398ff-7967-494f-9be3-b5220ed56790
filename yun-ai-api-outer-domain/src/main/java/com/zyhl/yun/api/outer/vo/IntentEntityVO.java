package com.zyhl.yun.api.outer.vo;

import lombok.Data;

import java.util.List;

/**
 * 实体识别结果VO
 *
 * <AUTHOR>
 * @date 2024/4/16 10:01
 */
@Data
public class IntentEntityVO {

    /**
     * 时间列表，输出是某个时间，或者开始时间与结束时间
     */
    private List<String> timeList;

    /**
     * 地点列表
     */
    private List<String> placeList;

    /**
     * 事物标签列表，key与metadataList的key值一致，Value的类型为List<String>
     */
    private List<KeyValueVO> labelList;

    /**
     * 实体列表，Value的类型为Value的类型为List<String>
     */
    private List<KeyValueVO> metaDataList;

    /**
     * 图片标签列表
     */
    private List<String> imageNameList;

    /**
     * 文件后缀名称列表
     */
    private List<String> suffixList;

    /**
     * 发件人列表，意图为“028”重要邮件时返回
     */
    private List<String> senderList;

    /**
     * 邮箱列表，意图为“028”重要邮件时返回
     */
    private List<String> emailAddressList;

    /**
     * 状态列表，不同意图返回的结果不一致，需要根据意图结果来处理。
     * 028重要邮件：全部、未读、已读
     */
    private List<String> statusList;

    /**
     * 类别列表，不同意图返回的结果不一致，需要根据意图结果来处理。
     * 028重要邮件：重要邮件
     */
    private List<String> typeList;

    /**
     * 标题列表
     * 032发邮件
     */
    private List<String> titleList;

    /**
     * 内容列表
     * 032发邮件
     */
    private List<String> contentList;

    /**
     * 收件人列表
     * 032发邮件
     */
    private List<String> recipientList;

    /**
     * 邮件附件列表，028邮件
     */
    private List<String> attachmentList;

    /**
     * 人名列表，key的类型为String，Value的类型为List<String>
     * 样例：personList = [{"key": "主演", "value": ["刘德华"]}]
     */
    private List<KeyValueVO> personList;

}
