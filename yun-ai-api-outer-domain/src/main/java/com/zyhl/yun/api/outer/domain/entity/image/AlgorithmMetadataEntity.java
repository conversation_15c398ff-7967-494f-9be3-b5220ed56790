package com.zyhl.yun.api.outer.domain.entity.image;

import com.zyhl.hcy.yun.ai.common.base.hbase.infrastructure.persistence.aspect.HbaseColumn;
import com.zyhl.hcy.yun.ai.common.base.hbase.infrastructure.persistence.aspect.HbaseTable;
import com.zyhl.hcy.yun.ai.common.base.hbase.infrastructure.persistence.po.metadata.FaceInfoPO;
import com.zyhl.hcy.yun.ai.common.base.hbase.infrastructure.persistence.po.metadata.ImageQualityPO;
import com.zyhl.hcy.yun.ai.common.base.hbase.infrastructure.persistence.po.metadata.ThingLabelPO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 *
 *
 * @author: zjj
 * @Description:
 * @Date: 2024年3月28日15:39:10
 */

@Builder
@Data
@HbaseTable(tableName = "algorithm_metadata")
@AllArgsConstructor
@NoArgsConstructor
public class AlgorithmMetadataEntity {

    /**
     * 主键id
     */
    @HbaseColumn(family = "img")
    private String rowKey;

    /**
     * 文件id
     */
    @HbaseColumn(family = "img")
    private String fileId;

    /**
     * 图片特征值
     */
    @HbaseColumn(family = "img")
    private String imageFeature;

    /**
     * 图片质量总体评分
     */
    @HbaseColumn(family = "img")
    private ImageQualityPO imgQuality;

    /**
     * 人物信息
     */
    @HbaseColumn(family = "lst")
    private List<FaceInfoPO> faceList;

    /**
     * 事物标签数据
     */
    @HbaseColumn(family = "lst")
    private List<ThingLabelPO> thingList;
}
