package com.zyhl.yun.api.outer.domain.entity.knowledge;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.alibaba.dashscope.utils.JsonUtils;
import com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.File;
import com.zyhl.yun.api.outer.enums.knowledge.FileCategoryEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;

/**
 * 个人知识库上传
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserKnowledgeUploadEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 知识库id
     */
    private Long baseId;

    /**
     * 目标父目录ID
     */
    private String targetParentFileId;

    /**
     * 目标父目录路径
     */
    private String targetParentFilePath;

    /**
     * 来源父目录上传ID
     */
    private Integer parentId;

    /**
     * 来源父目录上传ID
     */
    private String parentFileId;

    /**
     * 来源父目录上传ID
     */
    private String fileId;

    /**
     * 对应新的目标ID
     */
    private String targetFileId;

    /**
     * 0 云盘个人云（默认）, 1 邮件, 2 笔记, 3 网址, 4 微信文件, 5 本地文件
     */
    private Integer resourceType;

    /**
     * from_resource_type=3 则存储的是网址信息，json格式 {}, from_resource_type=2
     */
    private String resource;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件归属，个人云 owner_id= user_id
     */
    private String ownerId;

    /**
     * 业务类型：
     * -1 - 未知类型
     * 1-personal 个人云
     * 2-group 圈子
     * 3-shareGroup 共享群
     * 4-family 家庭云
     * 5-activity 活动空间 照片直播
     * 6-note 笔记
     * 7-cardpackage 卡包
     * 8-system 系统空间
     * 9-partner 合作空间
     * 10 -mount 挂载盘
     *
     * @see com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum
     */
    private Integer ownerType;

    /**
     * paas平台编码
     */
    private String paasCode;

    /**
     * 文件哈希名
     */
    private String hashName;

    /**
     * 文件哈希值
     */
    private String hashValue;

    /**
     * 文件类型:1-文件，2-目录
     */
    private Integer fileType;

    /**
     * 文件/目录分类
     * 文件/目录分类,见字典定义
     * 1 图片
     * 2 音频
     * 3 视频
     * 4 文档
     * 5 应用
     * 6 压缩文件
     * 0 其他
     * 100 普通目录
     *
     * @see FileCategoryEnum
     */
    private Integer category;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 文件后缀
     */
    private String extension;

    /**
     * 文件修改时间
     */
    private Date fileUpdatedAt;

    /**
     * 文件创建时间
     */
    private Date fileCreatedAt;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标识，0--正常；1--已删除；2--删除中；3--保险箱
     *
     * @see com.zyhl.yun.api.outer.enums.knowledge.KnowledgeStatusEnum
     */
    private Integer delFlag;

    /**
     * 默认 0 未处理, 1 处理中（云盘文件导入使用）, 2 成功（文件夹下面没有文件也是成功状
     */
    private Integer uploadStatus;

    /**
     * 算法结果码
     * 0000 成功
     * 其他则错误码
     */
    private String resultCode;

    /**
     * 算法结果
     */
    private String resultMsg;

    public UserKnowledgeUploadEntity(String userId, Long baseId, Integer resourceType, String targetParentFileId, String targetParentFilePath) {
        this.baseId = baseId;
        this.userId = userId;
        this.resourceType = resourceType;
        // 文件保存到独立空间的父目录id
        this.targetParentFileId = targetParentFileId;
        this.targetParentFilePath = targetParentFilePath;
    }

    /**
     * 保存上传结果
     * @param
     * @return
     */
    public UserKnowledgeUploadEntity buildUploadResult(Integer uploadStatus, String resultCode, String resultMsg) {
        this.resultCode = resultCode;
        this.resultMsg = resultMsg;
        this.uploadStatus = uploadStatus;
        return this;
    }

    /**
     * 保存个人云文件信息
     * @param file
     * @return
     */
    public UserKnowledgeUploadEntity buildPersonFile(File file) {
        // 个人云的文件ID
        this.fileId = file.getFileId();
        // 个人云文件的父目录ID
        this.parentFileId = file.getParentFileId();
        this.fileName = file.getName();
        this.ownerType = OwnerTypeEnum.PERSONAL.getOwnerValue();
        this.ownerId = userId;
        this.paasCode = String.valueOf(RequestContextHolder.getBelongsPlatform());
        this.hashName = file.getContentHashAlgorithm();
        this.hashValue = file.getContentHash();
        // 文件或者目录，文件1，目录2
        this.fileType = FileTypeEnum.getKnowledgeFileType(file.getType());
        this.category = FileCategoryEnum.getKnowledgeCategory(file.getCategory());
        this.fileSize = file.getSize();
        this.extension = file.getFileExtension();
        this.fileUpdatedAt = CharSequenceUtil.isEmpty(file.getUpdatedAt()) ? null : DateUtil.parse(file.getUpdatedAt(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
        this.fileCreatedAt = CharSequenceUtil.isEmpty(file.getCreatedAt()) ? null : DateUtil.parse(file.getCreatedAt(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
        return this;
    }

    public UserKnowledgeUploadEntity record(Integer uploadStatus,String resultCode,String resultMsg ) {
        this.uploadStatus = uploadStatus;
        this.resultCode = resultCode;
        this.resultMsg = resultMsg;
        return this;
    }

    public UserKnowledgeUploadEntity buildHtml(Long baseId, String userId, String ownerId,String resource,String targetParentFileId) {
        this.baseId = baseId;
        this.userId = StrUtil.isNotBlank(userId)?userId:RequestContextHolder.getUserId();
        this.ownerId = ownerId;
        this.ownerType = OwnerTypeEnum.AI.getOwnerValue();
        this.resourceType = KnowledgeResourceTypeEnum.HTML.getCode();
        this.resource =resource;
        this.paasCode = String.valueOf(RequestContextHolder.getBelongsPlatform());
        this.fileType = FileTypeEnum.FILE.getKnowledgeFileType();
        this.createTime = new Date();
        this.updateTime = new Date();
        this.delFlag = 0;
        this.targetParentFileId = targetParentFileId;
        this.targetParentFilePath =targetParentFileId;
        return this;
    }
}
