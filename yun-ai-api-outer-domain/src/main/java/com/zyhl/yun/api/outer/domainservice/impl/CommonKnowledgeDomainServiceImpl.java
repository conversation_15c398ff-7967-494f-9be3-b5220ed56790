package com.zyhl.yun.api.outer.domainservice.impl;

import com.zyhl.hcy.yun.ai.common.base.es.domain.datahelper.repository.EsCommonKnowledgeRepository;
import com.zyhl.hcy.yun.ai.common.base.es.entity.CommonKnowledgeEsEntity;
import com.zyhl.yun.api.outer.domainservice.CommonKnowledgeDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 公共知识库领域服务实现
 * <AUTHOR>
 */
@Slf4j
@Service
public class CommonKnowledgeDomainServiceImpl implements CommonKnowledgeDomainService {

    @Resource
    private EsCommonKnowledgeRepository esCommonKnowledgeRepository;


/*    @Override
    public List<CommonKnowledgeEsEntity> getTextSplitCommonKnowledgeResult(String baseId, String text, List<BigDecimal> feature) {
        return esCommonKnowledgeRepository.getTextSplitCommonKnowledgeResult(baseId, text, feature);
    }

    @Override
    public List<CommonKnowledgeEsEntity> getTextSplitCommonKnowledgeResultByParseType(String baseId, String text, List<BigDecimal> feature, String parseType) {
        return esCommonKnowledgeRepository.getCommonKnowledgeResultByParseType(baseId, text, feature, parseType);
    }

    @Override
    public List<CommonKnowledgeEsEntity> getQaCommonKnowledgeResult(String baseId, String text, List<BigDecimal> feature) {
        return esCommonKnowledgeRepository.getQaCommonKnowledgeResult(baseId, text, feature);
    }*/

    @Override
    public Boolean updateCommonKnowledgeRecallCount(CommonKnowledgeEsEntity entity) {
        return esCommonKnowledgeRepository.updateCommonKnowledgeRecallCount(entity);
    }

    @Override
    public CommonKnowledgeEsEntity getCommonKnowledgeById(CommonKnowledgeEsEntity entity) {
        return esCommonKnowledgeRepository.getCommonKnowledgeById(entity);
    }

}
