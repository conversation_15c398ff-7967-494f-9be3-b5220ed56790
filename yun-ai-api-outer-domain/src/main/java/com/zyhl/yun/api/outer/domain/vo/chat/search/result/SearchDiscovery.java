package com.zyhl.yun.api.outer.domain.vo.chat.search.result;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 对话信息-搜索结果-发现广场-列表数据
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchDiscovery implements Serializable {

    /**
     * 内容id
     */
    private Long id;

    /**
     * 内容封面图
     */
    private String contentImageUrl;

    /**
     * 内容标题
     */
    private String contentTitle;

    /**
     * 内容简介
     */
    private String contentDesc;

    /**
     * 端内链接
     */
    private String contentUrl;

    /**
     * 浏览量
     */
    private Integer viewCount;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
