package com.zyhl.yun.api.outer.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 干预库返回结果对象
 * @author: shixiaokang
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InterventionVO {

    /**
     * 知识库es id
     */
    private String id;

    /***
     * 知识库问题
     */
    private String question;

    /***
     * 知识库答案
     */
    private String answer;

    /***
     * 知识库问题类型
     */
    private Integer type;
    
    /***
     * 知识库问题答案版本号
     */
    private String version;

}
