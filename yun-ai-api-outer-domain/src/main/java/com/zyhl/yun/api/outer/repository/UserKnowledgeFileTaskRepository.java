package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileTaskEntity;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeTaskTypeEnum;

import java.util.List;
import java.util.Set;

/**
 * 个人知识库文件转存任务
 *
 * <AUTHOR>
 */
public interface UserKnowledgeFileTaskRepository {

    /**
     * 添加
     *
     * @param entity 实体对象
     */
    void add(UserKnowledgeFileTaskEntity entity);

    /**
     * 更新
     *
     * @param entity 实体对象
     */
    void update(UserKnowledgeFileTaskEntity entity);

    /**
     * 根据id查询
     *
     * @param id id
     * @return 实体对象
     */
    UserKnowledgeFileTaskEntity getById(Long id);

    /**
     * 根据id删除
     *
     * @param id id
     */
    void deleteById(Long id);

    /**
     * 根据状态查询
     *
     * @param userId     用户id
     * @param statusList 状态集合
     * @param type       任务类型
     * @return 实体对象集合
     */
    List<UserKnowledgeFileTaskEntity> getByStatus(String userId, List<Integer> statusList, KnowledgeTaskTypeEnum type);
}
