package com.zyhl.yun.api.outer.domain.vo.aippt;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AiPPT获取Code响应VO
 *
 * <AUTHOR> Assistant
 * @date 2025/1/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AipptCodeGetResponseVO {

    /**
     * apikey
     */
    private String apikey;

    /**
     * 认证code
     */
    private String code;

    /**
     * code有效期，例如86400
     */
    private String timeExpire;
}
