package com.zyhl.yun.api.outer.vo;

import java.io.Serializable;

import lombok.Builder;
import lombok.Data;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.vo.AIToolConfigVO} <br>
 * <b> description:</b>
 * AI工具对应的参数配置VO
 *
 * <AUTHOR>
 * @date 2024-11-28 09:39
 **/
@Data
@Builder
public class AIToolConfigVO implements Serializable {

    private static final long serialVersionUID = -3227104237100909099L;

    /**
     * 文件存储Id,默认是当前用户Id,大屏AI写真场景,可能后台配置了运营用户
     */
    private String targetUserId;

    /**
     * 文件的存储目录Id
     */
    private String catalogId;

    /**
     * 文件存储的文件名称
     */
    private String catalogName;

    /**
     * 渠道接入时间，格式： 2024-11-16 10:10:10
     */
    private String startTime;

    /**
     * 渠道失效时间,格式 2024-11-16 10:10:10
     */
    private String endTime;

    /**
     * 是否支持打印功能,目前只有大屏AI写真场景支持
     */
    private Boolean canPrint;

    /**
     * 其他参数,json格式
     */
    private String ext;
}
