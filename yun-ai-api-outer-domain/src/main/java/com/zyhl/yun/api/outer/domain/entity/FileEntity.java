package com.zyhl.yun.api.outer.domain.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件信息
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileEntity {
    /**
     * 文件ID。
     * 该字段唯一标识每个文件或目录。
     */
    private String fileId;

    /**
     * 父目录ID。
     * 该字段存储父目录的文件ID，用于构建文件层次结构。
     */
    private String parentFileId;

    /**
     * 名称。
     * 该字段存储文件或目录的名称。
     */
    private String name;

    /**
     * 描述。
     * 该字段存储文件或目录的描述信息。
     */
    private String description;

    /**
     * 类型。
     * 该字段存储文件或目录的类型，枚举值包括 "file" 和 "folder"。
     */
    private String type;

    /**
     * 文件扩展名。
     * 该字段存储文件的扩展名，通常是文件名的后缀。
     */
    private String fileExtension;

    /**
     * 分类。
     * 该字段根据文件的后缀名和MIME类型对文件进行分类，主要分类包括 "app"（安装包）、"zip"（压缩包）、"image"（图片）、"doc"（文档）、"video"（视频）、"audio"（音频）、"folder"（目录）、"others"（其他）。
     */
    private String category;

    /**
     * 创建时间。
     * 该字段存储文件或目录的创建时间，格式为 RFC 3339，例如 "2019-08-20T06:51:27.292Z"。
     */
    private String createdAt;

    /**
     * 更新时间。
     * 该字段存储文件或目录的最后更新时间，格式为 RFC 3339，例如 "2019-08-20T06:51:27.292Z"。
     */
    private String updatedAt;

    /**
     * 放入回收站时间。
     * 该字段存储文件或目录被放入回收站的时间，格式为 RFC 3339。
     */
    private String trashedAt;

    /**
     * 文件本地创建时间。
     * 该字段存储文件在上传时刻的本地创建时间，格式为 RFC 3339。
     */
    private String localCreatedAt;

    /**
     * 文件本地修改时间。
     * 该字段存储文件在上传时刻的本地更新时间，格式为 RFC 3339。
     */
    private String localUpdatedAt;

    /**
     * 是否收藏。
     * 该字段指示文件或目录是否被用户收藏。
     */
    private Boolean starred;

    /**
     * 大小。
     * 该字段存储文件的大小，单位为字节。
     */
    private Long size;

    /**
     * 用户自定义标签。
     * 该字段存储用户自定义的标签数组，在文件复制时不复制。
     */
    private TagEntity[] userTags;

    /**
     * AI标签。
     * 该字段存储AI生成的标签数组，在文件复制时复制。
     */
    private String[] labels;

    /**
     * 缩略图地址。
     * 该字段存储文件的缩略图地址数组。
     */
    private ThumbnailInfoEntity[] thumbnailUrls;

    /**
     * 处罚状态。
     * 该字段存储文件或目录的处罚状态，可选值为 0（未处罚）和 1（被冻结）。
     */
    private Integer punishMode;

    /**
     * 内容哈希。
     * 该字段存储文件内容的哈希值。
     */
    private String contentHash;

    /**
     * 内容哈希算法名。
     * 该字段存储计算内容哈希所使用的算法名称，当前支持的算法包括 "sha1" 和 "sha256"。
     */
    private String contentHashAlgorithm;

    /**
     * 版本ID。
     * 该字段存储文件的版本ID，当相同文件ID的文件被覆盖上传时，会产生新的版本ID。
     */
    private String revisionId;

    /**
     * 是否系统目录。
     * 该字段指示文件或目录是否为系统目录，默认值为 false。
     */
    private Boolean systemDir;

    /**
     * 媒体元数据信息。
     * 该字段存储媒体文件的元数据信息，如果存在则返回。
     */
    private MediaMetaInfoEntity mediaMetaInfo;

    /**
     * 地址详情。
     * 该字段存储文件或目录的地址详情信息，如果存在则返回。
     */
    private AddressDetailEntity addressDetail;

}
