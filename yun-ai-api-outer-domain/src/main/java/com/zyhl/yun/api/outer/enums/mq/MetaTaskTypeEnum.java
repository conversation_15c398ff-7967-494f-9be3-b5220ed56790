package com.zyhl.yun.api.outer.enums.mq;


import java.util.Objects;

/**
 * 元数据任务类型
 *
 * <AUTHOR>
 */
public enum MetaTaskTypeEnum {


    /**
     * 图片元数据分析
     */
    IMAGE(1, "图片元数据"),

    /**
     * 人脸聚
     */
    HUMAN_FACE(2, "人脸聚类"),

    /**
     * 相似度聚
     */
    SIMILARITY(3, "相似度聚类"),

    /**
     * 文档向量类
     */
    DOC_INDEX(4, "文档正文索引"),

    /**
     * 文档向量化（个人知识库）
     */
    DOC_PERSONAL_KNOWLEDGE_VECTOR(5, "文档向量化（个人知识库）"),

    /**
     * 文档向
     */
    DOC_COMMON_KNOWLEDGE_VECTOR(6, "文档向量化（公共知识库）");

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 描述
     */
    private final String msg;

    private MetaTaskTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    public Integer getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }

    public static String getMsg(Integer code) {
        for (MetaTaskTypeEnum resultEnum : MetaTaskTypeEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.getMsg();
            }
        }
        return null;
    }

    public static MetaTaskTypeEnum getTaskTypeEnum(Integer code) {
        for (MetaTaskTypeEnum resultEnum : MetaTaskTypeEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum;
            }
        }
        return IMAGE;
    }

}
