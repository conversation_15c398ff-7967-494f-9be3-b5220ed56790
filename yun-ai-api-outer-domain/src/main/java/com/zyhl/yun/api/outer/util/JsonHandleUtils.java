package com.zyhl.yun.api.outer.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.zyhl.yun.api.outer.constants.Const.SINGLE_QUOTATION_MARK;

/**
 * json字符串处理工具类
 *
 * <AUTHOR>
 * @date 2024/7/18 15:44
 */
public class JsonHandleUtils {

    private static final Pattern JSON_PATTERN = Pattern.compile("(?i)`*json`*");

    /**
     * 格式化json字符串
     *
     * @param jsonStr json字符串
     * @return 返回格式化后的json字符串
     */
    public static String formatJsonStr(String jsonStr) {
    	if(null == jsonStr) {
    		return jsonStr;
    	}
    	jsonStr = jsonStr.replace("```", "");

        // 使用Matcher对象查找并替换所有匹配项
        Matcher matcher = JSON_PATTERN.matcher(jsonStr);
        String cleanJson = matcher.replaceAll("");

        // 由于JSON字符串可能被包含在额外的引号中，这里也移除它们
        if (cleanJson.startsWith(SINGLE_QUOTATION_MARK) && cleanJson.endsWith(SINGLE_QUOTATION_MARK)) {
            cleanJson = cleanJson.substring(1, cleanJson.length() - 1);
        }

        return cleanJson;
    }

}
