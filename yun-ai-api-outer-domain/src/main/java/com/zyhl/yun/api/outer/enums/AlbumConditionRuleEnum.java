package com.zyhl.yun.api.outer.enums;

import lombok.Getter;

/**
 * 相册生成条件规则
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Getter
public enum AlbumConditionRuleEnum {

	/**
	 * 全部
	 */
	ALL(0, "全部"),
	/**
	 * 每年
	 */
	YEAR(1, "每年"),
	/**
	 * 每季度
	 */
	SEASON(2, "每季度"),

	/**
	 * 每年
	 */
	MONTH(3, "每月"),

	/**
	 * 每周
	 */
	WEEK(4, "每周"),;

	AlbumConditionRuleEnum(Integer type, String name) {
		this.type = type;
		this.name = name;
	}

	/**
	 * 类型
	 */
	private final Integer type;
	/**
	 * 名称
	 */
	private final String name;

	public static AlbumConditionRuleEnum getByName(String name) {
		for (AlbumConditionRuleEnum value : values()) {
			if (value.getName().equals(name)) {
				return value;
			}
		}
		return null;
	}

	public static boolean isExist(String name) {
		return getByName(name) != null;
	}

}
