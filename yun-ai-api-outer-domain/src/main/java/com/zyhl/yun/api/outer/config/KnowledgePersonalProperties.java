package com.zyhl.yun.api.outer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 个人知识库配置
 *
 * <AUTHOR>
 */
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "knowledge.personal")
public class KnowledgePersonalProperties {

    /**
     * 文件大小限制，默认100M
     */
    private Long size = 104857600L;

    /**
     * 文件后缀
     */
    private List<String> extList = new ArrayList<>();


    /**
     * 图片大小限制（B）默认50M
     */
    private Long imageSize = 51200000L;

    /**
     * 标签个数
     */
    private Integer labelNum = 50;

    /**
     * 标签名字数
     */
    private Integer labelNameLen = 6;

    /**
     * 上传文件数量限制
     */
    private Integer uploadNumLimit = 50;

    /**
     * 批量删除时每批的数量
     */
    private Integer fileDelete = 100;

    /**
     * 批量删除时每批的数量
     */
    private Integer fileResDelete = 100;

    /**
     * ES批量删除时每批的数量
     */
    private Integer esFileChunkDelete = 100;

    /**
     * 批量删除hbase时每批的数量
     */
    private Integer hbaseDelete = 200;

    /**
     * 转存任务过期时长（秒）
     */
    private Integer transferExpireTime;
    /**
     * 删除任务过期时长（秒）
     */
    private Integer deleteExpireTime;

    /**
     * # 删除任务查询间隔时间（毫秒）
     */
    private Long deleteTaskQuerySleep;
    /**
     * 删除任务查询次数（每次10秒钟）
     */
    private Integer deleteTaskQueryTimes;

    /**
     * 解析失败原因（错误码对应的描述）
     */
    private Map<String, String> parseFailedReason;

    /**
     * 个人知识库2.0名称最大长度
     */
    private Integer nameLength = 25;

    /**
     * 个人知识库2.0描述最大长度
     */
    private Integer descriptionLength = 150;

    /**
     * 个人知识库2.0知识库名称和描述送审开关
     */
    private Boolean checkContentSwitch;

    /**
     * 个人知识库2.0创建知识库最大数量
     */
    private Integer createMaxNum = 10;

    /**
     * 个人知识库头像信息列表
     */
    private List<ProfilePhoto> profilePhotoList;

    /**
     * 个人知识库html域名白名单
     */
    private List<String> htmlUrlLegalHosts;

    /**
     * 统计时间偏移量（分钟）
     */
    private Integer countTimeOffset = 240;
    /**
     * 前端请求一次知识库文件最大删除数量
     */
    private Integer maxFileDeleteSize = 500;

    /**
     * 个人知识库文件名称最大长度 默认40
     */
    private Integer fileNameLength = 40;



    // ---------------------------------- //

    public Date getTransferExpireDate() {
        return transferExpireTime == null ? null : new Date(System.currentTimeMillis() + transferExpireTime * 1000L);
    }

    public Date getDeleteExpireDate() {
        return deleteExpireTime == null ? null : new Date(System.currentTimeMillis() + deleteExpireTime * 1000L);
    }

    /**
     * 知识库头像信息
     */
    @Data
    public static class ProfilePhoto{
        /**
         * 头像类型
         * 1--预设
         * 2--个人云文件
         */
        private Integer type;

        /**
         * 头像ID
         * type=1时为预设的头像ID
         * type=2时为云盘文件ID
         */
        private String photoId;

        /**
         * 头像url
         */
        private String url;
    }
}
