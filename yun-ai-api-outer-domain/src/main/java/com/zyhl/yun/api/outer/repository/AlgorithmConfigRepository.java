package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.AlgorithmConfigEntity;

import java.util.List;

/**
 * 算法配置表-Repository
 * @Author: WeiJing<PERSON>un
 */
public interface AlgorithmConfigRepository {

    /**
     * 根据算法id，获取算法配置
     * @Author: Wei<PERSON><PERSON><PERSON><PERSON>
     *
     * @param algorithmId 算法id
     * @return 算法配置
     */
    AlgorithmConfigEntity queryByAlgorithmId(Long algorithmId);

    /**
     * 根据算法id集合，获取算法配置
     * @Author: WeiJingKun
     *
     * @param algorithmIds 算法id集合
     * @return 算法配置list
     */
    List<AlgorithmConfigEntity> getByAlgorithmIds(List<Long> algorithmIds);
}
