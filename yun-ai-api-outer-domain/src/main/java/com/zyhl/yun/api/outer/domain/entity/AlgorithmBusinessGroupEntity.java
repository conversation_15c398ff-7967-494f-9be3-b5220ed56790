package com.zyhl.yun.api.outer.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 算法业务组表-Entity
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AlgorithmBusinessGroupEntity {
    /**
     * 算法组编码
     * 1 华为 - 图片元数据提取任务算法组 （目前只支持），文档全文检索
     * 2 自研-文档全文检索算法组
     * 3 自研- 文档向量化算法组（个人知识库）
     * 4 自研- 文档向量化算法组（公共知识库）
     * 5 自研 - 视频AI算法组
     */
    private Integer algorithmGroupCode;

    /**
     * 算法id列表，逗号分隔
     * 1 图片元数据算法id（1）、图片向量算法id（4）
     * 2 文档全文检索id（99990001）
     * 3 文档向量化算法id（5）
     * 4 文档向量化算法id（6）
     * 5 视频提取音频（7） 音频提取正文（8）
     */
    private String algorithmIds;

    /**
     * 任务类型
     * 1.图片元数据分析
     * 2.人脸聚类
     * 3.相似度聚类
     * 4.文档全文检索
     * 5.文档向量化（个人知识库）
     * 6.文档向量化（公共知识库）
     * 7.视频提取正文
     */
    private Integer taskType;

    /**
     * 文件/目录分类,见字典定义
     * 1 图片
     * 2 音频
     * 3 视频
     * 4 文档
     * 5 应用
     * 6 压缩文件
     * 0 其他
     * 100 普通目录
     * 1000 笔记
     * 1001 邮箱
     */
    private Integer category;

    /**
     * 回调配置
     */
    private String callback;

}
