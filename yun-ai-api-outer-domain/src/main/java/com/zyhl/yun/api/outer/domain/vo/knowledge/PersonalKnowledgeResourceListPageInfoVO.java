package com.zyhl.yun.api.outer.domain.vo.knowledge;

import com.github.pagehelper.PageInfo;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.vo.common.BasePageInfoVO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 查询个人知识库资源列表VO
 * @date 2025/4/16 20:41
 */

@Data
@NoArgsConstructor
public class PersonalKnowledgeResourceListPageInfoVO extends BasePageInfoVO {

    /**
     * 知识库文件列表
     */
    private List<PersonalKnowledgeResource> list;

    public PersonalKnowledgeResourceListPageInfoVO setData(PageInfoDTO pageDTO, PageInfo<?> pageVO, List<PersonalKnowledgeResource> fileList) {
        this.list = fileList;

        if (pageDTO.isNeedTotal()) {
            // 需要总数才有分页
            this.totalCount = pageVO.getTotal();

            // 判断是否有下一页
            int nextPageCursor = Integer.parseInt(pageDTO.getPageCursor()) + pageDTO.getPageSize();
            if (nextPageCursor < pageVO.getTotal()) {
                this.nextPageCursor = String.valueOf(nextPageCursor);
            }
        }

        return this;
    }
}
