package com.zyhl.yun.api.outer.external;

import com.zyhl.hcy.yun.ai.common.model.api.client.centertask.vo.CenterTaskCreateVO;
import com.zyhl.yun.api.outer.domain.entity.centertask.CenterTaskCreateEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.ImageParamEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.TextModelParamEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.TextParamEntity;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import java.util.List;
import org.springframework.validation.annotation.Validated;

/**
 * 中心任务-ExternalService
 * @Author: WeiJingKun
 */
public interface CenterTaskExternalService {

    /**
     * 创建ai算法任务【图片类】
     *
     * @param dialogueIntentionEnum 枚举参数
     * @param createEntity 创建参数
     * @param imageParamEntity 图片参数
     * @return 创建结果
     */
    CenterTaskCreateVO createImageTask(DialogueIntentionEnum dialogueIntentionEnum, @Validated CenterTaskCreateEntity createEntity, @Validated ImageParamEntity imageParamEntity);

    /**
     * 创建ai算法任务【文本类】，例如：文生图
     *
     * @param dialogueIntentionEnum 枚举参数
     * @param createEntity 创建参数
     * @param textParamEntity 文本参数
     * @return 创建结果
     */
    CenterTaskCreateVO createTextTask(DialogueIntentionEnum dialogueIntentionEnum, @Validated CenterTaskCreateEntity createEntity, TextParamEntity textParamEntity);

    /**
     * 创建ai算法任务【文本模型类】
     *
     * @param dialogueIntentionEnum 枚举参数
     * @param createEntity 创建参数
     * @param textModelParamEntity 文本参数
     * @return 创建结果
     */
    CenterTaskCreateVO createTextModelTask(DialogueIntentionEnum dialogueIntentionEnum, @Validated CenterTaskCreateEntity createEntity, TextModelParamEntity textModelParamEntity);

    /**
     * 构建任务rowkey
     *
     * @param userId 用户id
     * @param dialogueId 对话id
     * @return 响应结果
     */
    String createTaskRowkey(String userId, String dialogueId);

    /**
     * 构建任务rowKey，用list返回
     *
     * @param userId         用户id
     * @param dialogueIdList 对话id
     * @return 响应结果
     */
    List<String> createTaskRowkeyToList(String userId, List<String> dialogueIdList);

}
