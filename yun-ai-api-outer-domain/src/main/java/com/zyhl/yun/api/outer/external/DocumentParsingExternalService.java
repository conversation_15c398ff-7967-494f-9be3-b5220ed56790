package com.zyhl.yun.api.outer.external;

import com.zyhl.yun.api.outer.domain.vo.DocumentParsingResultVO;

import java.util.List;

/**
 * 文档解析接口
 *
 * <AUTHOR>
 */
public interface DocumentParsingExternalService {

	/**
     * 文档解析（解析完删除本地文件）
     *
     * @param localpathList 共享地址集合
     * @return 文档解析结果
     */
    public DocumentParsingResultVO parsingAfterDelete(List<String> localpathList, Integer maxLength);

    /**
     * 文档解析
     *
     * @param localpathList 共享地址集合
     * @return 文档解析结果
     */
    public DocumentParsingResultVO parsing(List<String> localpathList, Integer maxLength);

}
