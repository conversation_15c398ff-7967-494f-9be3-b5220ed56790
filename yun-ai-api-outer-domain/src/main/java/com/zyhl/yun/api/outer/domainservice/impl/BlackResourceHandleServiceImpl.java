package com.zyhl.yun.api.outer.domainservice.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiBlackResourceEntity;
import com.zyhl.yun.api.outer.domainservice.BlackResourceHandleService;
import com.zyhl.yun.api.outer.repository.BlackResourceRepository;
import com.zyhl.yun.api.outer.util.SimpleRedisLock;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 黑名单缓存数据处理相关业务逻辑
 * <AUTHOR>
 */
@Slf4j
@Service
public class BlackResourceHandleServiceImpl implements BlackResourceHandleService {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private BlackResourceRepository blackResourceRepository;

    @Resource
    private SimpleRedisLock simpleRedisLock;

    @Override
    public boolean isSensitive(String text, Integer parity) {
        boolean isSensitive;
        if (ObjectUtil.isEmpty(text)) {
            return true;
        }
        RMap<String, AlgorithmAiBlackResourceEntity> sensitiveMap = redissonClient.getMap(RedisConstants.BLACK_RESOURCE_CACHE_KEY);
        long cacheTtl = redissonClient.getBucket(RedisConstants.BLACK_RESOURCE_CACHE_KEY).remainTimeToLive();

        // 缓存为空/过期时间为空/剩余过期时间不足，则初始化缓存
        if (sensitiveMap.isEmpty() || ObjectUtil.equal(cacheTtl, null) || cacheTtl < RedisConstants.BLACK_RESOURCE_LOGIC_CACHE_EXPIRE_TIME) {
            return listAndHandlePromptRecommendList(text, parity);
        }

        if (ObjectUtil.equal(parity, 1)) {
            isSensitive = sensitiveMap.keySet().stream().anyMatch(text::contains);
        } else {
            isSensitive = sensitiveMap.containsKey(text);
        }


        return isSensitive;
    }

    /**
     * 从DB获取黑名单数据，判断是否注入缓存
     * @return 提示词列表
     */
    private boolean listAndHandlePromptRecommendList(String text, Integer parity) {

        boolean isSensitive = false;

        // get from DB
        List<AlgorithmAiBlackResourceEntity> dataList = blackResourceRepository.getAllDate();

        if (dataList == null || dataList.isEmpty()) {
            return false;
        }

        // 将dataList转换为map
        Map<String, AlgorithmAiBlackResourceEntity> map = dataList
                .stream()
                .collect(Collectors.toMap(AlgorithmAiBlackResourceEntity::getResourceName, item -> item));

        if (ObjectUtil.equal(parity, 1)) {
            isSensitive = map.keySet().stream().anyMatch(text::contains);
        } else {
            isSensitive = map.containsKey(text);
        }

        try {
            if (simpleRedisLock.tryLock(RedisConstants.BLACK_RESOURCE_CACHE_LOCK_KEY,
                    RedisConstants.BLACK_RESOURCE_CACHE_KEY, 300, TimeUnit.SECONDS)) {
                // injection
                promptInfoInjection(map);
                simpleRedisLock.unlock(RedisConstants.BLACK_RESOURCE_CACHE_LOCK_KEY, RedisConstants.BLACK_RESOURCE_CACHE_KEY);
            } else {
                log.info("缓存中黑名单数据为空，且当前线程抢夺锁失败，返回结果为空，等待缓存注入数据线程注入成功后再尝试获取操作。");
            }
        } catch (YunAiBusinessException e) {
            log.error("黑名单缓存业务锁操作发生异常，请注意上下文业务日志是否存在问题。", e);
        }

        return isSensitive;
    }

    /**
     * 黑名单数据注入缓存
     */
    private void promptInfoInjection(Map<String, AlgorithmAiBlackResourceEntity> map) {
        RMap<String, AlgorithmAiBlackResourceEntity> sensitiveMap = redissonClient.getMap(RedisConstants.BLACK_RESOURCE_CACHE_KEY);
        sensitiveMap.putAll(map);
        sensitiveMap.expire(RedisConstants.BLACK_RESOURCE_CACHE_EXPIRE_TIME, TimeUnit.MINUTES);
    }

    @Override
    public List<String> blackResourceFilter(List<String> sourceResourceNameList) {
        // 查询【黑名单资源】名称列表
        List<String> resourceNameList = blackResourceRepository.getResourceNameList(sourceResourceNameList,
                AlgorithmAiBlackResourceEntity.ShieldedTypeEnum.blackList());
        // 剔除【黑名单资源】
        if(CollUtil.isNotEmpty(sourceResourceNameList) && CollUtil.isNotEmpty(resourceNameList)){
            sourceResourceNameList.removeAll(resourceNameList);
        }
        return sourceResourceNameList;
    }

    @Override
    public List<String> shieldResourceFilter(List<String> sourceResourceNameList) {
        // 查询【屏蔽资源】名称列表
        List<String> resourceNameList = blackResourceRepository.getResourceNameList(sourceResourceNameList,
                AlgorithmAiBlackResourceEntity.ShieldedTypeEnum.shieldResourceList());
        // 剔除【屏蔽资源】
        if(CollUtil.isNotEmpty(sourceResourceNameList) && CollUtil.isNotEmpty(resourceNameList)){
            sourceResourceNameList.removeAll(resourceNameList);
        }
        return sourceResourceNameList;
    }

}