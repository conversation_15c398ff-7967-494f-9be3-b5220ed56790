package com.zyhl.yun.api.outer.strategy.chat.searchresult;

import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchCommonParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchResult;

/**
 * 搜索策略
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 */
public interface SearchStrategy {

    /**
     * 执行搜索策略
     * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
     *
     * @param searchResult 搜索结果
     * @param searchSubParam  搜索子参数
     * @param searchCommonParam  搜索参数-公共
     */
    void performSearch(SearchResult searchResult, Object searchSubParam, SearchCommonParam searchCommonParam);

}
