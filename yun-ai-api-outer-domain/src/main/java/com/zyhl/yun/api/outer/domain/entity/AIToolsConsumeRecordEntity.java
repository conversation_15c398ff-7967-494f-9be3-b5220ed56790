package com.zyhl.yun.api.outer.domain.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.domain.entity.AIToolsConsumeRecordEntity} <br>
 * <b> description:</b>
 * AI工具核销记录实体
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-08-16 18:22
 **/
@Data
public class AIToolsConsumeRecordEntity implements Serializable {
    private static final long serialVersionUID = -8126030539132252911L;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 核销日期(20240306)
     */
    private String consumeDate;

    /**
     * 渠道id
     */
    private String channelId;

    /**
     * 模型
     */
    private Integer module;

    /**
     * 限制总次数
     */
    private Integer limitTotal;

    /**
     * 使用总次数
     */
    private Integer useTimes;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
