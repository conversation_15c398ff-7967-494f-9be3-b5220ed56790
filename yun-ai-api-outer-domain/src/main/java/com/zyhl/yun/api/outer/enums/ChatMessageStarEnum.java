package com.zyhl.yun.api.outer.enums;

import com.google.common.base.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 聊天大模型网络搜索状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ChatMessageStarEnum {

	/**
	 * 关闭
	 */
	YES(1, "启用星标"),

	/**
	 * 未启用星标
	 */
	NO(0, "未启用星标"),

	;

	/**
	 * 编码
	 */
	private final Integer code;
	/**
	 * 描述
	 */
	private final String desc;

	/**
	 * 是否星标
	 * 
	 * @param paramCode
	 * @return
	 */
	public static Boolean isStar(Integer paramCode) {
		return Objects.equal(YES.getCode(), paramCode);
	}

}
