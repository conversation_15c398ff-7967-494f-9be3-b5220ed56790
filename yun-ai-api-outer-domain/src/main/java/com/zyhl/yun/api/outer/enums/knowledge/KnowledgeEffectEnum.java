package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识库是否生效
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum KnowledgeEffectEnum {

    /**
     * 正常
     */
    UN_EFFECT(0, "不生效"),

    /**
     * 已删除
     */
    HAS_EFFECT(1, "已生效"),

    ;

    /**
     * 状态
     */
    private final Integer status;
    /**
     * 备注
     */
    private final String remark;


    public static boolean isHasEffect(Integer status) {
        return HAS_EFFECT.status.equals(status);
    }

    public static boolean isUnEffect(Integer status) {
        return UN_EFFECT.status.equals(status);
    }
}
