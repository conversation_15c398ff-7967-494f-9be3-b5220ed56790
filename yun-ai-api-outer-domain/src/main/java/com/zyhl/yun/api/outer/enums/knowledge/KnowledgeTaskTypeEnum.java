package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识库任务类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum KnowledgeTaskTypeEnum {

    /**
     * 转存平台转存任务
     */
    TRANSFER("TRANSFER", "转存平台转存任务"),

    /**
     * 独立空间批量删除任务
     */
    DELETE("DELETE", "独立空间批量删除任务"),

    /**
     * 独立空间批量删除文档对应的资源
     */
    DELETE_RES("DELETE_RES", "独立空间批量删除文档对应的资源删除任务"),

    /**
     * 独立空间批量删除任务V2
     */
    FILE_DELETE("FILE_DELETE", "独立空间批量删除任务V2"),

    /**
     * 独立空间移动文件夹任务
     */
    FILE_MOVE("FILE_MOVE", "独立空间移动文件夹任务"),

    /**
     * 文件同步
     */
    FILE_SYNC("FILE_SYNC", "文件同步"),
    ;

    /**
     * 编码
     */
    private final String code;
    /**
     * 备注
     */
    private final String remark;



    public static KnowledgeTaskTypeEnum getByCode(String code) {
        for (KnowledgeTaskTypeEnum e : KnowledgeTaskTypeEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }


    public static boolean isTransfer(String code) {
        return TRANSFER.code.equals(code);
    }

    public static boolean isDelete(String code) {
        return DELETE.code.equals(code);
    }

    public static boolean isDeleteRes(String code) {
        return DELETE_RES.code.equals(code);
    }

}
