package com.zyhl.yun.api.outer.domainservice;

import com.zyhl.hcy.yun.ai.common.rag.dto.validate.RecallQueryDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.domain.valueobject.PersonalKnowledgeBase;

import java.util.List;

/**
 * 用户知识库领域服务接口
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface UserKnowledgeDomainService {

    /**
     * 创建默认知识库
     */
    UserKnowledgeEntity createDefaultKnowledge();

    /**
     * 根据用户id和知识库id集合，获取知识库列表
     * @Author: WeiJingKun
     *
     * @param userId 用户id
     * @param baseIdList 知识库id集合
     * @return 知识库列表
     */
    List<PersonalKnowledgeBase> getUserKnowledgeListByIdList(String userId, List<Long> baseIdList);

    /**
     * 根据用户id和业务类型获取知识库id
     *
     * @param userId  用户id
     * @param bizType 业务类型
     * @return 知识库列表
     */
    Long getUserKnowledgeIdByUserIdBizType(String userId, Integer bizType);

    /**
     * 文件列表
     *
     * @param fileIdList 文件id集合
     * @return 集合
     */
    List<RecallQueryDTO.KnowledgeGroup> fileList(List<String> fileIdList);

    /**
     * 文件列表
     *
     * @param baseList 知识库集合
     * @return 集合
     */
    List<RecallQueryDTO.KnowledgeGroup> baseList(List<PersonalKnowledgeBase> baseList, String businessCode);


    /**
     * 创建笔记同步知识库
     * @return 知识库实体
     */
    UserKnowledgeEntity createNoteSyncKnowledge();

}
