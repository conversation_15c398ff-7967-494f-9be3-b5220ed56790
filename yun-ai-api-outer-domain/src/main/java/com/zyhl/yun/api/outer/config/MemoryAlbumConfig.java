package com.zyhl.yun.api.outer.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/21 09:43
 */
@Data
@Configuration
@ConfigurationProperties(
        prefix = "memory.config"
)
public class MemoryAlbumConfig {
	
	/**
	 * 最小生成文件数
	 */
    private int albumFileMinNum = 2;

    /**
	 * 最大生成文件数
	 */
    private int albumFileMaxNum = 35;

    private List<String> albumNames;

    private static List<String> defaultAlbumNames;

    private double similarityThreshold = 0.99;

    //是否处理无向量的图片
    private Boolean handleNonEmbedding = false;

    /**
     * 相册音乐id
     */
    private List<String> albumNameMusicIds;

    /**
     * 默认相册音乐id
     */
    private static List<String> defaultAlbumNameMusicIds;

    static {

        /**
         * 照片是时光的落款
         * 时光里的影集
         * 定格在照片里的故事
         * 写给过去的影像日记
         * 那些值得铭记的瞬间
         * 触摸回忆的质感
         * 藏在日常里的小圆满
         * 收集生活中的闪耀瞬间
         * 那些被镜头留住的故事
         * 那些温柔的记忆碎片
         */
        defaultAlbumNames = new ArrayList<>();

        defaultAlbumNames.add("照片是时光的落款");
        defaultAlbumNames.add("时光里的影集");
        defaultAlbumNames.add("定格在照片里的故事");
        defaultAlbumNames.add("写给过去的影像日记");
        defaultAlbumNames.add("那些值得铭记的瞬间");
        defaultAlbumNames.add("触摸回忆的质感");
        defaultAlbumNames.add("藏在日常里的小圆满");
        defaultAlbumNames.add("收集生活中的闪耀瞬间");
        defaultAlbumNames.add("那些被镜头留住的故事");
        defaultAlbumNames.add("那些温柔的记忆碎片");
    }

    static {

        /**
         * 008 春日漫游
         * 009 快乐的一天
         * 011 Pleasant Understanding
         * 012 旅途
         */
        defaultAlbumNameMusicIds = new ArrayList<>();

        defaultAlbumNameMusicIds.add("48");
        defaultAlbumNameMusicIds.add("49");
        defaultAlbumNameMusicIds.add("51");
        defaultAlbumNameMusicIds.add("52");
    }

    /****
     * 获取随机的相册名称
     * @return
     */
    public String getRandomAlbumName() {
        List<String> combinedList = new ArrayList<>();
        combinedList.addAll(defaultAlbumNames);
        if (CollUtil.isNotEmpty(albumNames)) {
            combinedList.addAll(albumNames);
        }

        // 随机选择一个元素
        Random random = new Random();
        String randomElement = combinedList.get(random.nextInt(combinedList.size()));
        return randomElement;
    }

    /**
     * 获取随机音乐id
     *
     * @return
     */
    public String getRandomAlbumMusicId() {

        List<String> musicIdList = new ArrayList<>();
        if (CollUtil.isNotEmpty(albumNameMusicIds)) {
            musicIdList.addAll(albumNameMusicIds);
        } else {
            musicIdList.addAll(defaultAlbumNameMusicIds);
        }

        //随机选一个音乐id给到要生成的相册
        return RandomUtil.randomEle(musicIdList);
    }
}
