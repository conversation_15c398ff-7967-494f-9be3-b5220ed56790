package com.zyhl.yun.api.outer.pool;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.thread.NamedThreadFactory;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.Optional;
import java.util.concurrent.*;

/**
 * 配置个人云Sass全局业务工作线程池
 *
 * <AUTHOR>
 * 2023/5/24
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties("thread-pool.global")
public class GlobalThreadPool {

    private static final int DEFAULT_CORE_SIZE = Runtime.getRuntime().availableProcessors() * 2;

    private static final int DEFAULT_MAX_SIZE = Runtime.getRuntime().availableProcessors() * 4;

    private static final int LONG_UPLOAD_FILE_CORE_SIZE = 1;

    private static final int LONG_UPLOAD_FILE_MAX_SIZE = Runtime.getRuntime().availableProcessors() * 4;

    private static final long DEFAULT_KEEP_ALIVE_TIME = 1;

    private static final TimeUnit DEFAULT_KEEP_ALIVE_TIME_UNIT = TimeUnit.MINUTES;

    private static final int DEFAULT_QUEUE_SIZE = 100000;

    private static final String DEFAULT_THREAD_NAME_PREFIX = "hcy-saas-business-";

    private static final RejectedExecutionHandler DEFAULT_REJECT_HANDLER =
            new ThreadPoolExecutor.AbortPolicy();

    /**
     * 核心线程数
     */
    private Integer coreSize;
    /**
     * 最大线程数
     */
    private Integer maxSize;
    /**
     * 保活时间
     */
    private Long keepAliveTime;
    /**
     * 保活时间单位
     */
    private TimeUnit keepAliveTimeUnit;
    /**
     * 任务队列长度
     */
    private Integer queueSize;
    /**
     * 线程名称前缀
     */
    private String threadNamePrefix;
    /**
     * 拒绝策略实现类，必须提供类的全限定名称，同时拥有默认的构造器
     */
    private String rejectedClass;

    @Bean
    @Primary
    public ExecutorService businessThreadPool(GlobalThreadPool config) {
        String threadNamePrefix = Optional.ofNullable(config.getThreadNamePrefix()).orElse(DEFAULT_THREAD_NAME_PREFIX);
        int coreSize = Optional.ofNullable(config.getCoreSize()).orElse(DEFAULT_CORE_SIZE);
        int maxSize = Optional.ofNullable(config.getMaxSize()).orElse(DEFAULT_MAX_SIZE);
        long keepAliveTime = Optional.ofNullable(config.getKeepAliveTime()).orElse(DEFAULT_KEEP_ALIVE_TIME);
        TimeUnit timeUnit = Optional.ofNullable(config.getKeepAliveTimeUnit()).orElse(DEFAULT_KEEP_ALIVE_TIME_UNIT);
        int queueSize = Optional.ofNullable(config.getQueueSize()).orElse(DEFAULT_QUEUE_SIZE);
        RejectedExecutionHandler rejectHandler = DEFAULT_REJECT_HANDLER;
        if (CharSequenceUtil.isNotBlank(config.getRejectedClass())) {
            try {
                Class<?> rejectClass = Class.forName(config.getRejectedClass());
                Object instance = rejectClass.newInstance();
                if (instance instanceof RejectedExecutionHandler) {
                    rejectHandler = ((RejectedExecutionHandler) instance);
                } else {
                    log.error("类[{}]加载没有实现RejectedExecutionHandler, 无法设置为拒绝策略", config.getRejectedClass());
                }
            } catch (ClassNotFoundException e) {
                log.error("类[{}]加载失败", config.getRejectedClass(), e);
            } catch (InstantiationException | IllegalAccessException e) {
                log.error("类[{}]对象实例化失败", config.getRejectedClass(), e);
            }
        }
        log.info("Global Thread Pool init starting");
        BusinessThreadPoolExecutor executor = new BusinessThreadPoolExecutor(coreSize, maxSize, keepAliveTime, timeUnit,
                new ArrayBlockingQueue<>(queueSize), new NamedThreadFactory(threadNamePrefix, false),
                rejectHandler);
        log.info("Global Thread Pool init end");
        return executor;
    }

    /**
     * 第三方平台调用使用线程池
     *
     * @param config
     * @return
     */
    @Bean
    public ExecutorService platformThreadPool(GlobalThreadPool config) {
        return commonThreadPool("PLATFORM_THREAD_POOL", "platformThreadPool", config);
    }

    /**
     * 送审调用使用线程池
     *
     * @param config 线程池配置
     * @return ExecutorService
     */
    @Bean
    public ExecutorService checkSystemThreadPool(GlobalThreadPool config) {
        return commonThreadPool("CHECK_SYSTEM_THREAD_POOL", "checkSystemThreadPool", config);
    }

    /**
     * 会话对话-相关线程池
     * @Author: WeiJingKun
     * @param config 全局业务工作线程池配置
     * @return java.util.concurrent.ExecutorService
     */
    @Bean
    public ExecutorService algorithmChatThreadPool(GlobalThreadPool config) {
        return commonThreadPool("ALGORITHM_CHAT_THREAD_POOL", "algorithmChatThreadPool", config);
    }

    /**
     * 搜索-相关线程池
     *
     * @Author: WeiJingKun
     * @param config 全局业务工作线程池配置
     * @return java.util.concurrent.ExecutorService
     */
    @Bean
    public ExecutorService searchThreadPool(GlobalThreadPool config) {
        return commonThreadPool("SEARCH_THREAD_POOL", "searchThreadPool", config);
    }

    @Bean("getFileContentThreadPool")
    public ExecutorService getFileContentThreadPool() {
        String threadNamePrefix = "FILE_CONTENT_THREAD_POOL";
        int coreSize = Optional.ofNullable(getCoreSize()).orElse(DEFAULT_CORE_SIZE);
        int maxSize = Optional.ofNullable(getMaxSize()).orElse(DEFAULT_MAX_SIZE);
        long keepAliveTime = Optional.ofNullable(getKeepAliveTime()).orElse(DEFAULT_KEEP_ALIVE_TIME);
        TimeUnit timeUnit = Optional.ofNullable(getKeepAliveTimeUnit()).orElse(DEFAULT_KEEP_ALIVE_TIME_UNIT);
        int queueSize = Optional.ofNullable(getQueueSize()).orElse(DEFAULT_QUEUE_SIZE);
        RejectedExecutionHandler rejectHandler = DEFAULT_REJECT_HANDLER;
        if (CharSequenceUtil.isNotBlank(getRejectedClass())) {
            try {
                Class<?> rejectClass = Class.forName(getRejectedClass());
                Object instance = rejectClass.newInstance();
                if (instance instanceof RejectedExecutionHandler) {
                    rejectHandler = ((RejectedExecutionHandler) instance);
                } else {
                    log.error("类[{}]加载没有实现RejectedExecutionHandler, 无法设置为拒绝策略", getRejectedClass());
                }
            } catch (ClassNotFoundException e) {
                log.error("类[{}]加载失败", getRejectedClass(), e);
            } catch (InstantiationException | IllegalAccessException e) {
                log.error("类[{}]对象实例化失败", getRejectedClass(), e);
            }
        }
        log.info("getFileContent Thread Pool init starting");
        BusinessThreadPoolExecutor executor = new BusinessThreadPoolExecutor(coreSize, maxSize, keepAliveTime, timeUnit,
                new ArrayBlockingQueue<>(queueSize), new NamedThreadFactory(threadNamePrefix, false),
                rejectHandler);
        log.info("getFileContent Thread Pool init end");
        return executor;
    }

    @Bean("mailAttachThreadPool")
    public ExecutorService mailAttachThreadPool() {
        String threadNamePrefix = "MAIL_ATTACH_THREAD_POOL";
        int coreSize = Optional.ofNullable(getCoreSize()).orElse(DEFAULT_CORE_SIZE);
        int maxSize = Optional.ofNullable(getMaxSize()).orElse(DEFAULT_MAX_SIZE);
        long keepAliveTime = Optional.ofNullable(getKeepAliveTime()).orElse(DEFAULT_KEEP_ALIVE_TIME);
        TimeUnit timeUnit = Optional.ofNullable(getKeepAliveTimeUnit()).orElse(DEFAULT_KEEP_ALIVE_TIME_UNIT);
        int queueSize = Optional.ofNullable(getQueueSize()).orElse(DEFAULT_QUEUE_SIZE);
        RejectedExecutionHandler rejectHandler = DEFAULT_REJECT_HANDLER;
        if (CharSequenceUtil.isNotBlank(getRejectedClass())) {
            try {
                Class<?> rejectClass = Class.forName(getRejectedClass());
                Object instance = rejectClass.newInstance();
                if (instance instanceof RejectedExecutionHandler) {
                    rejectHandler = ((RejectedExecutionHandler) instance);
                } else {
                    log.error("类[{}]加载没有实现RejectedExecutionHandler, 无法设置为拒绝策略", getRejectedClass());
                }
            } catch (ClassNotFoundException e) {
                log.error("类[{}]加载失败", getRejectedClass(), e);
            } catch (InstantiationException | IllegalAccessException e) {
                log.error("类[{}]对象实例化失败", getRejectedClass(), e);
            }
        }
        log.info("mailAttachThreadPool Thread Pool init starting");
        BusinessThreadPoolExecutor executor = new BusinessThreadPoolExecutor(coreSize, maxSize, keepAliveTime, timeUnit,
                new ArrayBlockingQueue<>(queueSize), new NamedThreadFactory(threadNamePrefix, false),
                rejectHandler);
        log.info("mailAttachThreadPool Thread Pool init end");
        return executor;
    }

    @Bean("longUploadFileThreadPool")
    public ExecutorService longUploadFileThreadPool() {
        String threadNamePrefix = "LONG_UPLOAD_FILE_THREAD_POOL";
        int coreSize = Optional.ofNullable(getCoreSize()).orElse(LONG_UPLOAD_FILE_CORE_SIZE);
        int maxSize = Optional.ofNullable(getMaxSize()).orElse(LONG_UPLOAD_FILE_MAX_SIZE);
        long keepAliveTime = Optional.ofNullable(getKeepAliveTime()).orElse(DEFAULT_KEEP_ALIVE_TIME);
        TimeUnit timeUnit = Optional.ofNullable(getKeepAliveTimeUnit()).orElse(DEFAULT_KEEP_ALIVE_TIME_UNIT);
        int queueSize = Optional.ofNullable(getQueueSize()).orElse(DEFAULT_QUEUE_SIZE);
        RejectedExecutionHandler rejectHandler = DEFAULT_REJECT_HANDLER;
        if (CharSequenceUtil.isNotBlank(getRejectedClass())) {
            try {
                Class<?> rejectClass = Class.forName(getRejectedClass());
                Object instance = rejectClass.newInstance();
                if (instance instanceof RejectedExecutionHandler) {
                    rejectHandler = ((RejectedExecutionHandler) instance);
                } else {
                    log.error("类[{}]加载没有实现RejectedExecutionHandler, 无法设置为拒绝策略", getRejectedClass());
                }
            } catch (ClassNotFoundException e) {
                log.error("类[{}]加载失败", getRejectedClass(), e);
            } catch (InstantiationException | IllegalAccessException e) {
                log.error("类[{}]对象实例化失败", getRejectedClass(), e);
            }
        }
        log.info("longUploadFileThreadPool Thread Pool init starting");
        BusinessThreadPoolExecutor executor = new BusinessThreadPoolExecutor(coreSize, maxSize, keepAliveTime, timeUnit,
                new ArrayBlockingQueue<>(queueSize), new NamedThreadFactory(threadNamePrefix, false),
                rejectHandler);
        log.info("longUploadFileThreadPool Thread Pool init end");
        return executor;
    }


    @Bean("knowledgeQueryThreadPool")
    public ExecutorService knowledgeQueryThreadPool() {
        String threadNamePrefix = "LONG_UPLOAD_FILE_THREAD_POOL";
        int coreSize = Optional.ofNullable(getCoreSize()).orElse(LONG_UPLOAD_FILE_CORE_SIZE);
        int maxSize = Optional.ofNullable(getMaxSize()).orElse(LONG_UPLOAD_FILE_MAX_SIZE);
        long keepAliveTime = Optional.ofNullable(getKeepAliveTime()).orElse(DEFAULT_KEEP_ALIVE_TIME);
        TimeUnit timeUnit = Optional.ofNullable(getKeepAliveTimeUnit()).orElse(DEFAULT_KEEP_ALIVE_TIME_UNIT);
        int queueSize = Optional.ofNullable(getQueueSize()).orElse(DEFAULT_QUEUE_SIZE);
        RejectedExecutionHandler rejectHandler = DEFAULT_REJECT_HANDLER;
        if (CharSequenceUtil.isNotBlank(getRejectedClass())) {
            try {
                Class<?> rejectClass = Class.forName(getRejectedClass());
                Object instance = rejectClass.newInstance();
                if (instance instanceof RejectedExecutionHandler) {
                    rejectHandler = ((RejectedExecutionHandler) instance);
                } else {
                    log.error("类[{}]加载没有实现RejectedExecutionHandler, 无法设置为拒绝策略", getRejectedClass());
                }
            } catch (ClassNotFoundException e) {
                log.error("类[{}]加载失败", getRejectedClass(), e);
            } catch (InstantiationException | IllegalAccessException e) {
                log.error("类[{}]对象实例化失败", getRejectedClass(), e);
            }
        }
        log.info("knowledgeQueryThreadPool Thread Pool init starting");
        BusinessThreadPoolExecutor executor = new BusinessThreadPoolExecutor(coreSize, maxSize, keepAliveTime, timeUnit,
                new ArrayBlockingQueue<>(queueSize), new NamedThreadFactory(threadNamePrefix, false),
                rejectHandler);
        log.info("knowledgeQueryThreadPool Thread Pool init end");
        return executor;
    }

    /**
     * 推荐下一句相关线程池
     *
     * @param config 全局业务工作线程池配置
     * @return java.util.concurrent.ExecutorService
     */
    @Bean
    public ExecutorService recommendQueryThreadPool(GlobalThreadPool config) {
        return commonThreadPool("RECOMEND_QUERY_THREAD_POOL", "recommendQueryThreadPool", config);
    }

    /**
     * 推荐多意图相关线程池
     *
     * @param config 全局业务工作线程池配置
     * @return java.util.concurrent.ExecutorService
     */
    @Bean
    public ExecutorService recommendIntentionThreadPool(GlobalThreadPool config) {
        return commonThreadPool("RECOMEND_INTENTION_THREAD_POOL", "recommendIntentionThreadPool", config);
    }

    /**
     * 搜索返回提示语句相关线程池
     *
     * @param config 全局业务工作线程池配置
     * @return java.util.concurrent.ExecutorService
     */
    @Bean
    public ExecutorService searchReturnTermsThreadPool(GlobalThreadPool config) {
        return commonThreadPool("SEARCH_RETURN_TERMS_THREAD_POOL", "searchReturnTermsThreadPool", config);
    }

    /**
     * 默认线程池配置
     * @param config 全局业务工作线程池配置
     * @return java.util.concurrent.ExecutorService
     */
    private ExecutorService commonThreadPool(String threadNamePrefix, String methodName, GlobalThreadPool config) {
        int coreSize = Optional.ofNullable(config.getCoreSize()).orElse(DEFAULT_CORE_SIZE);
        int maxSize = Optional.ofNullable(config.getMaxSize()).orElse(DEFAULT_MAX_SIZE);
        long keepAliveTime = Optional.ofNullable(config.getKeepAliveTime()).orElse(DEFAULT_KEEP_ALIVE_TIME);
        TimeUnit timeUnit = Optional.ofNullable(config.getKeepAliveTimeUnit()).orElse(DEFAULT_KEEP_ALIVE_TIME_UNIT);
        int queueSize = Optional.ofNullable(config.getQueueSize()).orElse(DEFAULT_QUEUE_SIZE);
        RejectedExecutionHandler rejectHandler = DEFAULT_REJECT_HANDLER;
        if (CharSequenceUtil.isNotBlank(config.getRejectedClass())) {
            try {
                Class<?> rejectClass = Class.forName(config.getRejectedClass());
                Object instance = rejectClass.newInstance();
                if (instance instanceof RejectedExecutionHandler) {
                    rejectHandler = ((RejectedExecutionHandler) instance);
                } else {
                    log.error("类[{}]加载没有实现RejectedExecutionHandler, 无法设置为拒绝策略", config.getRejectedClass());
                }
            } catch (ClassNotFoundException e) {
                log.error("类[{}]加载失败", config.getRejectedClass(), e);
            } catch (InstantiationException | IllegalAccessException e) {
                log.error("类[{}]对象实例化失败", config.getRejectedClass(), e);
            }
        }
        log.info("Global Thread Pool init " + methodName + " coreSize:{} | maxSize:{} | starting", coreSize, maxSize);
        BusinessThreadPoolExecutor executor = new BusinessThreadPoolExecutor(coreSize, maxSize, keepAliveTime, timeUnit,
                new ArrayBlockingQueue<>(queueSize), new NamedThreadFactory(threadNamePrefix, false),
                rejectHandler);
        log.info("Global Thread Pool init " + methodName + " end");
        return executor;
    }

    /**
     * 异步监控超时任务处理线程池
     *
     * @param config 配置数据
     * @return java.util.concurrent.ExecutorService
     * @Author: Derrick
     */
    @Bean
    public ExecutorService monitorLogTaskThreadPool(GlobalThreadPool config) {
        return commonThreadPool("MONITOR_LOG_TASK_THREAD_POOL", "monitorLogTaskThreadPool", config);
    }

    /**
     * 意图识别-相关线程池
     * @Author: WeiJingKun
     * @param config 全局业务工作线程池配置
     * @return java.util.concurrent.ExecutorService
     */
    @Bean
    public ExecutorService dialogueIntentionThreadPool(GlobalThreadPool config) {
        return commonThreadPool("DIALOGUE_INTENTION_THREAD_POOL", "dialogueIntentionThreadPool", config);
    }

    /**
     * 异步更新知识库召回次数处理线程池
     *
     * @param config 全局业务工作线程池配置
     * @return java.util.concurrent.ExecutorService
     * @Author: zangmeng
     */
    @Bean
    public ExecutorService knowledgeRecallCountThreadPool(GlobalThreadPool config) {
        return commonThreadPool("KNOWLEDGE_RECALL_COUNT_THREAD_POOL", "knowledgeRecallCountThreadPool", config);
    }

    /**
     * 异步 知识库批量删除处理线程池
     *
     * @param config 全局业务工作线程池配置
     * @return java.util.concurrent.ExecutorService
     * @Author: zangmeng
     */
    @Bean
    public ExecutorService knowledgeDeleteThreadPool(GlobalThreadPool config) {
        return commonThreadPool("KNOWLEDGE_DELETE_THREAD_POOL", "knowledgeDeleteThreadPool", config);
    }

    /**
     * 搜索小站资源-相关线程池
     * @Author: WeiJingKun
     * @param config 全局业务工作线程池配置
     * @return java.util.concurrent.ExecutorService
     */
    @Bean
    public ExecutorService searchPantaThreadPool(GlobalThreadPool config) {
        return commonThreadPool("SEARCH_PANTA_THREAD_POOL", "searchPantaThreadPool", config);
    }

    /**
     * 搜索小站资源-小站、厂商混合并行搜索
     * @param config 全局业务工作线程池配置
     * @return java.util.concurrent.ExecutorService
     */
    @Bean
    public ExecutorService multiSearchThreadPool(GlobalThreadPool config) {
        return commonThreadPool("MULTI_SEARCH_THREAD_POOL", "multiSearchThreadPool", config);
    }

    /**
     * 搜索小站资源-小站、厂商混合并行搜索，超时监控池
     * @param config 全局业务工作线程池配置
     * @return java.util.concurrent.ExecutorService
     */
    @Bean
    public ScheduledExecutorService multiSearchSchedulePool(GlobalThreadPool config) {
        return new ScheduledThreadPoolExecutor(1);
    }

    /**
     * 多路重排-相关线程池
     * @Author: WeiJingKun
     * @param config 全局业务工作线程池配置
     * @return java.util.concurrent.ExecutorService
     */
    @Bean
    public ExecutorService multiRouteRerankThreadPool(GlobalThreadPool config) {
        return commonThreadPool("MULTI_ROUTE_RERANK_THREAD_POOL", "multiRouteRerankThreadPool", config);
    }

    /**
     * 生成回忆相册-相关线程池
     * @Author: liuxuewen
     * @param config 全局业务工作线程池配置
     * @return java.util.concurrent.ExecutorService
     */
    @Bean
    public ExecutorService memoryAlbumThreadPool(GlobalThreadPool config) {
        return commonThreadPool("MEMORY_ALBUM_THREAD_POOL", "memoryAlbumThreadPool", config);
    }
}
