package com.zyhl.yun.api.outer.domainservice.impl;

import com.zyhl.hcy.yun.ai.common.base.eos.ObjectStoreClient;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.domain.entity.ObjectKeysEntity;
import com.zyhl.yun.api.outer.domain.entity.ReportInfoEntity;
import com.zyhl.yun.api.outer.domain.entity.UploadPicEntity;
import com.zyhl.yun.api.outer.domainservice.EosBusinessService;
import com.zyhl.yun.api.outer.repository.ReportRepository;
import com.zyhl.yun.api.outer.vo.UploadPicVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.zyhl.yun.api.outer.enums.ResultCodeEnum.ERROR_CALL_EXCEPTION;

/**
 * EOS业务
 *
 * @author: wujianwei
 */
@Slf4j
@Service
public class EosBusinessServiceImpl implements EosBusinessService {

    @Resource
    private ObjectStoreClient eosObjectStoreClient;

    @Resource
    private ReportRepository reportRepository;

    @Override
    public UploadPicVO uplaodAndGetUrl(UploadPicEntity entity) {
        Boolean falg = eosObjectStoreClient.uploadObjectSimple(entity.getObjectKey(), entity.getInputStream(), entity.getMetadata());
        if (!falg) {
            throw new YunAiBusinessException(ERROR_CALL_EXCEPTION);
        }
        String url = eosObjectStoreClient.generateSignedAttachment(entity.getObjectKey(), "", 24 * 60 * 60);
        if (StringUtils.isBlank(url)) {
            throw new YunAiBusinessException(ERROR_CALL_EXCEPTION);
        }
        UploadPicVO vo = new UploadPicVO();
        vo.setUrl(url);
        vo.setObjectKey(entity.getObjectKey());
        return vo;
    }

    @Override
    public Integer cancel(ObjectKeysEntity entity) {
        int i = 0;
        for (String key : entity.getObjectKeys()) {
            if (eosObjectStoreClient.deleteObject(key)) {
                i++;
            }
        }
        return i;
    }

    @Override
    public Integer submit(ReportInfoEntity entity) {
        return reportRepository.save(entity);
    }

}
