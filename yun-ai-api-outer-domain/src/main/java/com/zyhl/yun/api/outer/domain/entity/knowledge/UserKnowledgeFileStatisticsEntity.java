package com.zyhl.yun.api.outer.domain.entity.knowledge;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 个人知识库文件统计entity
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class UserKnowledgeFileStatisticsEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 知识库id
     */
    private Long baseId;

    /**
     * 资源总数量
     */
    private Integer totalCount;

    /**
     * 已处理数量
     */
    private Integer processed;

    /**
     * 未处理数量
     */
    private Integer untreated;
}
