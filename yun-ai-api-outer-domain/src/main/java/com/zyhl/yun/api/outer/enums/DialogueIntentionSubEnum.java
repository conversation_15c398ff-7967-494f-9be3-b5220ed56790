package com.zyhl.yun.api.outer.enums;

import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import cn.hutool.core.collection.CollUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * {@code @projectName} yun-ai-api-outer
 * <p>
 * {@code @description} 子意图枚举类
 * <p>
 *
 * <AUTHOR>
 * @since 4月16 2025
 */
@Getter
@AllArgsConstructor
public enum DialogueIntentionSubEnum {
    /**
     * AI生成PPT功能
     */
    AI_GENERATE_PPT("036001", "ai-generate-ppt", "AI生成PPT"),

    /**
     * AI生成会议纪要功能
     */
    AI_GENERATE_MEETING_MINUTES("036002", "ai-generate-meeting-minutes", "AI生成会议纪要"),

    /**
     * AI编程意图功能
     */
    AI_PROGRAMMING_INTENTION("036003", "ai-programming-intention", "AI编程意图"),

    /**
     * AI识图拍照解题功能
     */
    AI_PHOTO_RECOGNITION_SOLVE_PROBLEMS("036004", "ai-photo-recognition-solve-problems", "AI识图-拍照解题"),

    /**
     * AI识图拍照翻译功能
     */
    AI_PHOTO_RECOGNITION_TRANSLATE("036005", "ai-photo-recognition-translate", "AI识图-拍照翻译"),

    /**
     * AI识图拍照问答功能
     */
    AI_PHOTO_RECOGNITION_QA("036006", "ai-photo-recognition-qa", "AI识图-拍照问答"),

    /**
     * 图书快速阅读功能
     */
    SPEED_READ("036007", "speed-read", "图书快速阅读"),

    /**
     * 生成回忆相册
     */
    AI_MEMORY_ALBUM("036018", "ai-memory-album", "生成回忆相册"),

    /**
     * 会议通知邮件
     */
    AI_MEETING_MAIL("036019", "ai-meeting-mail", "会议通知"),

    /**
     * 爆款文案功能
     */
    HIT_COPYWRITING("036008", "hit-copywriting", "爆款文案"),

    /**
     * 创意标题功能
     */
    CREATIVE_TITLE("036009", "creative-title", "创意标题"),

    /**
     * 内容润色功能
     */
    CONTENT_POLISHING("036010", "content-polishing", "内容润色"),

    /**
     * 总结概括功能
     */
    SUMMARIZE("036011", "summarize", "总结概括"),

    /**
     * 写工作计划功能
     */
    WRITE_WORK_PLAN("036012", "write-work-plan", "写工作计划"),

    /**
     * 语法校对功能
     */
    GRAMMAR_CHECK("036013", "grammar-check", "语法校对"),

    /**
     * 职业规划功能
     */
    CAREER_PLANNING("036014", "career-planning", "职业规划"),

    /**
     * 广告创意功能
     */
    ADVERTISING_CREATIVE("036015", "advertising-creative", "广告创意"),

    /**
     * 营销方案功能
     */
    MARKETING_PLAN("036016", "marketing-plan", "营销方案"),

    /**
     * 商品好评功能
     */
    PRODUCT_POSITIVE_REVIEWS("036017", "product-positive-reviews", "商品好评"),

    /**
     * 邮件编辑
     */
    MAIL_EDIT("036020", "mail-edit", "邮件编辑");


    /**
     * 编号
     */
    private final String code;
    /**
     * 指令
     */
    private final String instruction;
    /**
     * 名称
     */
    private final String name;

    /**
     * 拍照识图子意图
     *
     * @param subIntention
     * @return
     */
    public static boolean isAiPhotoSubIntentions(String subIntention) {
        return AI_PHOTO_RECOGNITION_SOLVE_PROBLEMS.getCode().equals(subIntention)
                || AI_PHOTO_RECOGNITION_TRANSLATE.getCode().equals(subIntention)
                || AI_PHOTO_RECOGNITION_QA.getCode().equals(subIntention);
    }

    /**
     * 识别意图-AI编程执行意图
     *
     * @param mainIntention
     * @param argskey       指定key
     * @return
     */
    public static boolean isAiCoderExecute(IntentionInfo mainIntention, Object argskey) {
        return null != mainIntention && isAiCoderExecute(mainIntention.getIntention(), mainIntention.getSubIntention())
                && CollUtil.isNotEmpty(mainIntention.getArgumentMap())
                && mainIntention.getArgumentMap().containsKey(argskey);
    }

    /**
     * 输入意图-AI编程执行意图
     *
     * @param command
     * @param subCommand
     * @return
     */
    public static boolean isAiCoderExecute(String command, String subCommand) {
        return DialogueIntentionEnum.TEXT_TOOL.getCode().equals(command)
                && DialogueIntentionSubEnum.AI_PROGRAMMING_INTENTION.getCode().equals(subCommand);
    }

    /**
     * 识别意图-AI会议纪要执行意图
     *
     * @param mainIntention
     * @return
     */
    public static boolean isAiMeetingMinutesExecute(IntentionInfo mainIntention) {
        return null != mainIntention
                && isAiMeetingMinutesExecute(mainIntention.getIntention(), mainIntention.getSubIntention())
                && CollUtil.isNotEmpty(mainIntention.getArgumentMap());
    }

    /**
     * 识别意图-AI会议纪要执行意图，带文档
     *
     * @param mainIntention
     * @param isReqResourceDocSse 文档大模型对话
     * @return
     */
    public static boolean isAiMeetingMinutesExecute(IntentionInfo mainIntention, boolean isReqResourceDocSse) {
        return null != mainIntention
                && isAiMeetingMinutesExecute(mainIntention.getIntention(), mainIntention.getSubIntention())
                && isReqResourceDocSse;
    }

    /**
     * 输入意图-AI会议纪要执行意图
     *
     * @param command
     * @param subCommand
     * @return
     */
    public static boolean isAiMeetingMinutesExecute(String command, String subCommand) {
        return DialogueIntentionEnum.TEXT_TOOL.getCode().equals(command)
                && DialogueIntentionSubEnum.AI_GENERATE_MEETING_MINUTES.getCode().equals(subCommand);
    }

    /**
     * AI拍照识图-3个子意图，并且是图片大模型对话
     *
     * @param mainIntention
     * @param reqResourceImageSse
     * @return
     */
    public static boolean isAiPhotoVisionSubs(IntentionInfo mainIntention, boolean reqResourceImageSse) {
        return null != mainIntention && DialogueIntentionEnum.TEXT_TOOL.getCode().equals(mainIntention.getIntention())
                && reqResourceImageSse
                && (DialogueIntentionSubEnum.isAiPhotoSubIntentions(mainIntention.getSubIntention()));
    }

    /**
     * 可执行的文本工具子意图，增加话，的需要控制||后面追加
     *
     * @param mainIntention
     * @return
     */
    public static boolean allowTextToolExecute(IntentionInfo mainIntention) {
        if (null != mainIntention) {
            return DialogueIntentionEnum.isTextToolIntention(mainIntention.getIntention())
                    && allowTextToolExecute(mainIntention.getSubIntention());
        }
        return false;
    }

    /**
     * 可执行的文本工具子意图，增加话，的需要控制||后面追加（当前AI PPT，AI编程，AI生成会议纪要，AI拍照视图，AI生成回忆相册）【此处新增工具，需要加】
     *
     * @param subIntentionCode
     * @return
     */
    public static boolean allowTextToolExecute(String subIntentionCode) {
        return (AI_GENERATE_PPT.getCode().equals(subIntentionCode)
                || AI_GENERATE_MEETING_MINUTES.getCode().equals(subIntentionCode)
                || AI_PROGRAMMING_INTENTION.getCode().equals(subIntentionCode)
                || AI_PHOTO_RECOGNITION_SOLVE_PROBLEMS.getCode().equals(subIntentionCode)
                || AI_PHOTO_RECOGNITION_TRANSLATE.getCode().equals(subIntentionCode)
                || AI_PHOTO_RECOGNITION_QA.getCode().equals(subIntentionCode)
                || AI_MEMORY_ALBUM.getCode().equals(subIntentionCode));
    }

    public static boolean isAiPpt(String subIntentionCode) {
        return AI_GENERATE_PPT.getCode().equals(subIntentionCode);
    }

    public static boolean isAiCoder(String subIntentionCode) {
        return AI_PROGRAMMING_INTENTION.getCode().equals(subIntentionCode);
    }

    public static boolean isAiMeetingMinutes(String subIntentionCode) {
        return AI_GENERATE_MEETING_MINUTES.getCode().equals(subIntentionCode);
    }

    public static boolean isAiSpeedRead(String subIntentionCode) {
        return SPEED_READ.getCode().equals(subIntentionCode);
    }

    public static boolean isAiPhotoSolveProblems(String subIntentionCode) {
        return AI_PHOTO_RECOGNITION_SOLVE_PROBLEMS.getCode().equals(subIntentionCode);
    }

    public static boolean isAiPhotoTranslate(String subIntentionCode) {
        return AI_PHOTO_RECOGNITION_TRANSLATE.getCode().equals(subIntentionCode);
    }

    public static boolean isAiPhotoQa(String subIntentionCode) {
        return AI_PHOTO_RECOGNITION_QA.getCode().equals(subIntentionCode);
    }

    public static boolean isMemoryAlbum(String subIntentionCode) {
        return AI_MEMORY_ALBUM.getCode().equals(subIntentionCode);
    }

    public static boolean isMeetingMail(String subIntentionCode) {
        return AI_MEETING_MAIL.getCode().equals(subIntentionCode);
    }

    public static boolean isMailEdit(String subIntentionCode) {
        return MAIL_EDIT.getCode().equals(subIntentionCode);
    }

    /**
     * AI创新工具图标使用【此处新增工具，需要加】
     *
     * @param subIntentionCode
     * @return
     */
    public static boolean isNewAiToolIcon(String subIntentionCode) {
        return isAiPpt(subIntentionCode) || isAiCoder(subIntentionCode) || isAiMeetingMinutes(subIntentionCode)
                || isAiSpeedRead(subIntentionCode) || isAiPhotoSolveProblems(subIntentionCode)
                || isAiPhotoTranslate(subIntentionCode) || isAiPhotoQa(subIntentionCode)
                || isMemoryAlbum(subIntentionCode);
    }

}
