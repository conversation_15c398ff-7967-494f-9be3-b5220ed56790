package com.zyhl.yun.api.outer.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * className:StringUtil
 * description:
 *
 * <AUTHOR>
 * @date 2024/10/24
 */
public final class CustomStringUtil {

    private CustomStringUtil() {
        throw new AssertionError("Utility class should not be instantiated!!!");
    }

    /**
     * 去除es脚本注入相关字符
     *
     * @return 字符串
     */
    public static String getEsText(String text) {
        return text.replaceAll("\n", "").replaceAll("\"", "");
    }

    public static String getNewFileNameByNow(String fileName) {
        if (StrUtil.isEmpty(fileName)) {
            return "";
        }
        //获取文件名 去除后缀名
        String suffix = FileUtil.getSuffix(fileName);
        String name = FileUtil.getName(fileName);
        // 获取当前日期和时间
        LocalDateTime now = LocalDateTime.now();

        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
        return name + "_" + now.format(formatter) + suffix;
    }

}
