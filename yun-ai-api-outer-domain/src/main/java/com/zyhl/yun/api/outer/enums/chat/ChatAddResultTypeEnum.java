package com.zyhl.yun.api.outer.enums.chat;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ChatAddResultTypeEnum {

    /**
     * 同步返回
     */
    SYNCHRONIZATION(1, "同步"),

    /**
     * 异步返回
     */
    ASYNCHRONOUS(2, "异步"),

    /**
     * 流式返回
     */
    FLOW_TYPE(3, "流式返回"),

    /**
     * 富文本返回
     */
    RICH_TEXT_TYPE(4, "富文本返回");

    ;

    private final Integer type;
    private final String name;



    private static final Map<Integer, ChatAddResultTypeEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(ChatAddResultTypeEnum.class).forEach(item -> MAP.put(item.type, item));
    }

    public static ChatAddResultTypeEnum getType(Integer type) {
        if (null == type) {
            return null;
        }
        return MAP.get(type);
    }

    public static boolean isExist(Integer type) {
        return getType(type) != null;
    }


    public static boolean isSynchronous(Integer type) {
        return SYNCHRONIZATION.type.equals(type);
    }

    public static boolean isAsynchronous(Integer type) {
        return ASYNCHRONOUS.type.equals(type);
    }

    public static boolean isFlowType(Integer type) {
        return FLOW_TYPE.type.equals(type);
    }

}
