package com.zyhl.yun.api.outer.util;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

/**
 * Md5工具
 * @Author: WeiJingKun
 */
@Slf4j
public class Md5Utils {

    public static final String SECRET_KEY = "market_ai_invite";

    /**
     * 加密
     * @param phone
     * @return
     * @throws Exception
     */
    public static String encrypt(String phone) throws Exception {
        SecretKey secretKey = new SecretKeySpec(SECRET_KEY.getBytes(), "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] encryptedPhone = cipher.doFinal(phone.getBytes());
        return Base64.getEncoder().encodeToString(encryptedPhone);
    }

    /**
     * 解密
     * @param encryPhone
     * @return
     */
    public static String decrypt(String encryPhone) {
        try {
            SecretKey secretKey = new SecretKeySpec(SECRET_KEY.getBytes(), "AES");
            // 创建Cipher实例，使用AES算法进行解密
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decryptedPhoneNumber = cipher.doFinal(Base64.getDecoder().decode(encryPhone));
            return new String(decryptedPhoneNumber);
        } catch (Exception e) {
            log.error("instance error=", e);
        }
        return null;
    }
}
