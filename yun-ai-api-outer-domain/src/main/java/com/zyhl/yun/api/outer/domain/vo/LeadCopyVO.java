package com.zyhl.yun.api.outer.domain.vo;

import com.zyhl.yun.api.outer.config.LeadCopyProperties;
import com.zyhl.yun.api.outer.config.LeadCopyProperties.LeadCopyPrompt;
import com.zyhl.yun.api.outer.enums.AcceptLanguageEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import org.apache.commons.lang3.StringUtils;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 会话输入同步结果-引导文案对象VO
 *
 * <AUTHOR>
 * @date 2024/6/2 22:41
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LeadCopyVO {

    /**
     * 场景类型
     *
     * @see com.zyhl.yun.api.outer.enums.chat.search.LeadCopyTypeEnum
     */
    private Integer type;

    /**
     * 提示文案
     */
    private String promptCopy;

    /**
     * 按钮文案
     */
    private String buttonCopy;

    /**
     * 跳转URL
     */
    private String linkURL;

    /**
     * 跳转名称
     */
    private String linkName;

    /**
     * 图片URL
     */
    private String imageURL;

    /**
     * 图标URL
     */
    private String iconURL;

    /**
     * 提示词编码列表
     */
    private List<LeadCopyPrompt> promptCodeList;

    /**
     * 输入框提示文案
     */
    private String inputBoxCopy;

    /**
     * 意图编码，当type=5时返回
     */
    private String intentionCommand;

    public static LeadCopyVO getLeadCopyVo(LeadCopyProperties.Copy copyConfig, DialogueIntentionEnum intentionEnum) {
        if (copyConfig == null) {
            return null;
        }
        //跳转名称【文本工具意图，采用配置文件配置文本工具名称，不存在则取意图名称】
        String linkName = copyConfig.getLinkName();
        if (StringUtils.isEmpty(linkName)) {
            linkName = intentionEnum.getName();
        }
        LeadCopyVO vo = LeadCopyVO.builder()
                .buttonCopy(copyConfig.getButtonCopy())
                .promptCopy(copyConfig.getPromptCopy())
                .type(copyConfig.getType())
                .linkName(linkName)
                .linkURL(copyConfig.getLinkURL())
                .imageURL(copyConfig.getImageURL())
                .iconURL(copyConfig.getIconURL())
                .promptCodeList(copyConfig.getPromptCodeList())
                .inputBoxCopy(copyConfig.getInputBoxCopy())
                .build();
        // 根据请求头判断是否需要转成英文
        if (AcceptLanguageEnum.EN_US.getCode().equalsIgnoreCase(RequestContextHolder.getAcceptLanguage())) {
            if (StringUtils.isNotBlank(copyConfig.getButtonCopyEn())) {
                vo.setButtonCopy(copyConfig.getButtonCopyEn());
            }
            if (StringUtils.isNotBlank(copyConfig.getPromptCopyEn())) {
                vo.setPromptCopy(copyConfig.getPromptCopyEn());
            }
        }
        return vo;
    }

}
