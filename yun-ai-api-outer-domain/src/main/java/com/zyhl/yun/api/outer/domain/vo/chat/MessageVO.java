package com.zyhl.yun.api.outer.domain.vo.chat;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zyhl.yun.api.outer.domain.vo.ChatApplicationType;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

/**
 * <AUTHOR>
 * @data 2024/3/4 10:09
 */
@Data
public class MessageVO {

    /** 会话ID */
    private String sessionId;

    /** 会话标题 */
    private String title;

    /** 会话时间 */
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date createTime;

    /** 会话时间 */
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date updateTime;
    
    /**
     * 是否星标
     */
    private Boolean enableStar;
    
    /** 对话应用类型 */
    private ChatApplicationType applicationInfo;

}
