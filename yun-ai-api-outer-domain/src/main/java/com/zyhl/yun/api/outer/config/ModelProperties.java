package com.zyhl.yun.api.outer.config;

import static com.zyhl.yun.api.outer.constants.Const.NUM_16;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.UserTypeEnum;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.Data;

/**
 * 文本模型配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "model")
public class ModelProperties {

    /**
     * 【云邮】大模型配置，必须要有配置，否则系统异常
     */
    private List<ModelLimitBusinessConfig> limitYunMailList = new ArrayList<>();

    /**
     * 【小天】大模型配置，必须要有配置，否则系统异常
     */
    private List<ModelLimitBusinessConfig> limitXiaoTianList = new ArrayList<>();

    /**
     * 【云手机】大模型配置，必须要有配置，否则系统异常
     */
    private List<ModelLimitBusinessConfig> limitCloudPhoneList = new ArrayList<>();

    /**
     * 【笔记助手】大模型配置，必须要有配置，否则系统异常
     */
    private List<ModelLimitBusinessConfig> limitNoteList = new ArrayList<>();


    /**
     * 【5g消息】大模型配置，必须要有配置，否则系统异常
     */
    private List<ModelLimitBusinessConfig> limitMessage5gList = new ArrayList<>();

    /**
     * 智能体输入限制，空则不限制
     */
    private Map<String, ModelLimitConfig> limitAgent = new HashMap<>();

    /**
     * 小天助手使用的模型，默认百炼
     */
    private String xiaoTianUse = "blian";

    /**
     * 智能调度配置
     */
    private List<IntelligentScheduleConfig> intelligentSchedules;

    /**
     * 发送大模型的历史对话轮数
     */
    private int historyDialogueCount = 10;

    /**
     * 查询会员中心信息开关(默认模型配置使用)
     */
    private Boolean queryModelMemberCenterOpen = true;

    // ------------------------------输入限制配置--------------------------- //

    /**
     * 转map，兼容原来的写法
     *
     * @return map
     */
    public Map<String, Object> limitMap(String businessType) {
        Map<String, Object> map = new HashMap<>(NUM_16);
        // 这里只有【云邮助手】使用，如果有改动，则接口需要添加渠道入参
        Map<String, ModelLimitConfig> limit = getLimitByAssistantEnum(AssistantEnum.YUN_MAIL, businessType);
        if (!ObjectUtil.isEmpty(limit)) {
            for (String key : limit.keySet()) {
                ModelLimitConfig config = limit.get(key);
                if (!config.isSessionSet()) {
                    continue;
                }
                Map<String, Object> m = new HashMap<>(NUM_16);
                m.put("length", config.length);
                m.put("QPS", config.QPS);
                map.put(key, m);
            }
        }
        return map;
    }

    /**
     * 获取输入字数限制
     *
     * @param assistantEnum 助手枚举
     * @param modelCode     模型编码
     * @return 限制字数
     */
    public Integer getLengthLimit(AssistantEnum assistantEnum, String businessType, String modelCode) {
        Map<String, ModelLimitConfig> limit = getLimitByAssistantEnum(assistantEnum, businessType);
        ModelLimitConfig config = limit.get(modelCode);
        return config != null ? config.getLength() : null;
    }

    /**
     * 会话设置的数据：true-返回给前端，false-不返回给前端
     *
     * @param assistantEnum 助手枚举
     * @param modelCode     模型编码
     * @param sessionSet    会话设置，true只判断会话设置，false判断所有
     * @return true-存在，false-不存在
     */
    public boolean isExist(AssistantEnum assistantEnum, String businessType, String modelCode, boolean sessionSet) {
        Map<String, ModelLimitConfig> modelLimitConfig = getLimitByAssistantEnum(assistantEnum, businessType);
        ModelLimitConfig config = modelLimitConfig.get(modelCode);
        if (config == null) {
            return false;
        }
        return sessionSet ? config.isSessionSet() : true;
    }

    /**
     * 检查并获取用户可用模型
     *
     * @param assistantEnum 助手枚举
     * @param modelCode     模型编码
     * @param sessionSet    会话设置，true只判断会话设置，false判断所有
     * @param userTypeEnum  用户类型枚举
     * @return 默认模型
     */
    public String checkAndGetCanUseModel(AssistantEnum assistantEnum, String businessType, String modelCode, boolean sessionSet, UserTypeEnum userTypeEnum) {
        // nacos没有配置，则返回null
        Map<String, ModelLimitConfig> limit = getLimitByAssistantEnum(assistantEnum, businessType);
        if (ObjectUtil.isEmpty(limit)) {
            return null;
        }

        // 模型编码为【空】，返回nacos配置的默认模型
        if (CharSequenceUtil.isBlank(modelCode)) {
            Map<String, String> assistantMap = getDefModelByAssistantType(assistantEnum, businessType);
            if (ObjectUtil.isNotNull(assistantMap)) {
                modelCode = assistantMap.get(null == userTypeEnum ? UserTypeEnum.DEF_MODEL.getCode() : userTypeEnum.getCode());
            }
        }

        // 模型编码为【不空】
        if (CharSequenceUtil.isNotBlank(modelCode)) {
            // 判断是否为可用模型
            if (isExist(assistantEnum, businessType, modelCode, sessionSet)) {
                // 可用，直接返回
                return modelCode;
            } else {
                // 不可用，返回null
                return null;
            }
        } else {
            // 入参modelCode，为空 && nacos【无】默认配置，则返回null
            return null;
        }

    }

    /**
     * 获取助手大业务的用户类型的默认模型
     *
     * @param assistantEnum
     * @param userTypeEnum
     * @return
     */
	public String getModelByUserType(AssistantEnum assistantEnum, String businessType, UserTypeEnum userTypeEnum) {
        Map<String, String> assistantMap = getDefModelByAssistantType(assistantEnum, businessType);
        if(CollUtil.isNotEmpty(assistantMap)) {
        	return assistantMap.get(userTypeEnum.getCode());
        }
		return null;
	}

	/**
	 * 获取助手默认用户类型模型列表
	 * @param assistantEnum
	 * @param businessType
	 * @return
	 */
    private Map<String, String> getDefModelByAssistantType(AssistantEnum assistantEnum, String businessType) {
        if (null != assistantEnum) {
            switch (assistantEnum) {
                /** 云邮助手 */
                case YUN_MAIL:
                    return getDefModelsByBusinessTypeAndList(limitYunMailList, businessType);
                /** 小天助手 */
                case XIAO_TIAN:
                    return getDefModelsByBusinessTypeAndList(limitXiaoTianList, businessType);
                /** 云手机助手 */
                case CLOUD_PHONE:
                    return getDefModelsByBusinessTypeAndList(limitCloudPhoneList, businessType);
                /** 笔记助手 */
                case NOTE:
                    return getDefModelsByBusinessTypeAndList(limitNoteList, businessType);
                default:
                    break;
            }
        }
        return Collections.emptyMap();
    }

    /**
     * 获取最大历史长度（区分助手类型）
     *
     * @param assistantEnum 助手枚举
     * @param modelCode     模型编码
     * @return 字数限制
     */
    public Integer getMaxLength(AssistantEnum assistantEnum, String businessType, String modelCode) {
        Map<String, ModelLimitConfig> limit = getLimitByAssistantEnum(assistantEnum, businessType);
        ModelLimitConfig config = limit.get(modelCode);
        return config != null ? config.getHistoryMaxLength() : null;
    }

    /**
     * 智能体模型获取最大历史长度
     *
     * @param modelCode 模型编码
     * @return 字数限制
     */
    public Integer getAgentMaxLength(String modelCode) {
        ModelLimitConfig config = limitAgent.get(modelCode);
        return config != null ? config.getHistoryMaxLength() : null;
    }

    /**
     * 获取大模型配置，根据助手大模型配置
     *
     * @param assistantEnum 助手枚举
     * @return 大模型配置
     */
    public ModelLimitConfig getLimitByAssistantEnumAndType(AssistantEnum assistantEnum, String businessType, String modelType) {
        Map<String, ModelLimitConfig> map = getLimitByAssistantEnum(assistantEnum, businessType);
        if (CollUtil.isEmpty(map)) {
            return null;
        }
        return map.get(modelType);
    }

    /**
     * 获取大模型配置，根据助手枚举（默认云邮助手的配置）
     *
     * @param assistantEnum 助手枚举
     * @return 大模型配置
     */
    public Map<String, ModelLimitConfig> getLimitByAssistantEnum(AssistantEnum assistantEnum, String businessType) {
        Map<String, ModelLimitConfig> modelLimitConfigMap = null;
        if (null != assistantEnum) {
            switch (assistantEnum) {
                /** 云邮助手 */
                case YUN_MAIL:
                    modelLimitConfigMap = getByBusinessTypeAndList(this.limitYunMailList, businessType);
                    break;
                /** 小天助手 */
                case XIAO_TIAN:
                    modelLimitConfigMap = getByBusinessTypeAndList(this.limitXiaoTianList, businessType);
                    break;
                /** 云手机助手 */
                case CLOUD_PHONE:
                    modelLimitConfigMap = getByBusinessTypeAndList(limitCloudPhoneList, businessType);
                    break;
                /** 笔记助手 */
                case NOTE:
                    modelLimitConfigMap = getByBusinessTypeAndList(limitNoteList, businessType);
                    break;
                /** 5g消息助手 */
                case MESSAGE_5G:
                    modelLimitConfigMap = getByBusinessTypeAndList(limitMessage5gList, businessType);
                    break;
                default:
                    break;
            }
        }
        // modelLimitConfigMap根据sort从小到大排序，null在最后
        if(CollUtil.isNotEmpty(modelLimitConfigMap)) {
        	modelLimitConfigMap = modelLimitConfigMap.entrySet().stream()
        			.sorted(Map.Entry.comparingByValue(Comparator.comparingInt(config -> config.getSort() != null ? config.getSort() : Integer.MAX_VALUE)))
        			.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldValue, newValue) -> oldValue, LinkedHashMap::new));
        }
        return modelLimitConfigMap;
    }
    
    /**
     * 支持的意图列表
     *
     * @param assistantEnum 助手枚举
     * @param businessType  业务类型
     * @return
     */
    public List<String> getSupportIntentions(AssistantEnum assistantEnum, String businessType) {
        if (null != assistantEnum) {
            switch (assistantEnum) {
                /** 云邮助手 */
                case YUN_MAIL:
                    return getSupportIntentionsByBusinessTypeAndList(limitYunMailList, businessType);
                /** 小天助手 */
                case XIAO_TIAN:
                    return getSupportIntentionsByBusinessTypeAndList(limitXiaoTianList, businessType);
                /** 云手机助手 */
                case CLOUD_PHONE:
                    return getSupportIntentionsByBusinessTypeAndList(limitCloudPhoneList, businessType);
                /** 笔记助手 */
                case NOTE:
                    return getSupportIntentionsByBusinessTypeAndList(limitNoteList, businessType);
                /** 5g消息助手 */
                case MESSAGE_5G:
                    return getSupportIntentionsByBusinessTypeAndList(limitMessage5gList, businessType);
                default:
                    break;
            }
        }
        return null;
    }

    /**
     * 支持的二级子意图列表
     * @param assistantEnum
     * @param businessType
     * @return
     */
    public List<String> getSupportSubIntentions(AssistantEnum assistantEnum, String businessType) {
        if (null != assistantEnum) {
            switch (assistantEnum) {
                /** 云邮助手 */
                case YUN_MAIL:
                    return getSupportSubIntentionsByBusinessTypeAndList(limitYunMailList, businessType);
                /** 小天助手 */
                case XIAO_TIAN:
                    return getSupportSubIntentionsByBusinessTypeAndList(limitXiaoTianList, businessType);
                /** 云手机助手 */
                case CLOUD_PHONE:
                    return getSupportSubIntentionsByBusinessTypeAndList(limitCloudPhoneList, businessType);
                /** 笔记助手 */
                case NOTE:
                    return getSupportSubIntentionsByBusinessTypeAndList(limitNoteList, businessType);
                /** 5g消息助手 */
                case MESSAGE_5G:
                    return getSupportSubIntentionsByBusinessTypeAndList(limitMessage5gList, businessType);
                default:
                    break;
            }
        }
        return null;
    }

    /**
     * 搜索限定意图列表
     *
     * @param assistantEnum 助手枚举
     * @param businessType  业务类型
     * @return
     */
    public List<String> getSearchIncludeIntentions(AssistantEnum assistantEnum, String businessType) {
        if (null != assistantEnum) {
            switch (assistantEnum) {
                /** 云邮助手 */
                case YUN_MAIL:
                    return getSearchIntentionsByBusinessTypeAndList(limitYunMailList, businessType);
                /** 小天助手 */
                case XIAO_TIAN:
                    return getSearchIntentionsByBusinessTypeAndList(limitXiaoTianList, businessType);
                /** 云手机助手 */
                case CLOUD_PHONE:
                    return getSearchIntentionsByBusinessTypeAndList(limitCloudPhoneList, businessType);
                /** 笔记助手 */
                case NOTE:
                    return getSearchIntentionsByBusinessTypeAndList(limitNoteList, businessType);
                /** 5g消息助手 */
                case MESSAGE_5G:
                    return getSearchIntentionsByBusinessTypeAndList(limitMessage5gList, businessType);
                default:
                    break;
            }
        }
        return null;
    }

    /**
     * 获取业务类型默认搜索限定意图列表
     *
     * @param limitList
     * @param businessType
     * @return
     */
    private List<String> getSearchIntentionsByBusinessTypeAndList(List<ModelLimitBusinessConfig> limitList,
                                                                  String businessType) {
        if (CollUtil.isNotEmpty(limitList)) {
            for (ModelLimitBusinessConfig limit : limitList) {
                if (CollUtil.isEmpty(limit.getBusinessTypes())
                        || (null != limit.getBusinessTypes() && limit.getBusinessTypes().contains(businessType))) {
                    return limit.getSearchIncludeIntentions();
                }
            }
        }
        return null;
    }

	/**
	 * 获取业务类型默认支持意图列表
	 * 
	 * @param limitList
	 * @param businessType
	 * @return
	 */
	private List<String> getSupportIntentionsByBusinessTypeAndList(List<ModelLimitBusinessConfig> limitList,
			String businessType) {
		if (CollUtil.isNotEmpty(limitList)) {
			for (ModelLimitBusinessConfig limit : limitList) {
				if (CollUtil.isEmpty(limit.getBusinessTypes())
						|| (null != limit.getBusinessTypes() && limit.getBusinessTypes().contains(businessType))) {
					return limit.getSupportIntentions();
				}
			}
		}
		return null;
	}

    /**
     * 获取业务类型默认支持二级子意图列表
     *
     * @param limitList
     * @param businessType
     * @return
     */
    private List<String> getSupportSubIntentionsByBusinessTypeAndList(List<ModelLimitBusinessConfig> limitList,
                                                                   String businessType) {
        if (CollUtil.isNotEmpty(limitList)) {
            for (ModelLimitBusinessConfig limit : limitList) {
                if (CollUtil.isEmpty(limit.getBusinessTypes())
                        || (null != limit.getBusinessTypes() && limit.getBusinessTypes().contains(businessType))) {
                    return limit.getSupportSubIntentions();
                }
            }
        }
        return null;
    }

	/**
	 * 获取业务类型默认模型列表
	 * 
	 * @param limitList
	 * @param businessType
	 * @return
	 */
	private Map<String, String> getDefModelsByBusinessTypeAndList(List<ModelLimitBusinessConfig> limitList,
			String businessType) {
		if (CollUtil.isNotEmpty(limitList)) {
			for (ModelLimitBusinessConfig limit : limitList) {
				if (CollUtil.isEmpty(limit.getBusinessTypes())
						|| (null != limit.getBusinessTypes() && limit.getBusinessTypes().contains(businessType))) {
					return limit.getDefModels();
				}
			}
		}
		return null;
	}

	/**
	 * 获取业务类型模型列表
	 * 
	 * @param limitList
	 * @param businessType
	 * @return
	 */
	private Map<String, ModelLimitConfig> getByBusinessTypeAndList(List<ModelLimitBusinessConfig> limitList,
			String businessType) {
		if (CollUtil.isNotEmpty(limitList)) {
			for (ModelLimitBusinessConfig limit : limitList) {
				if (CollUtil.isEmpty(limit.getBusinessTypes())
						|| (null != limit.getBusinessTypes() && limit.getBusinessTypes().contains(businessType))) {
					return limit.getModels();
				}
			}
		}
		return null;
	}

	/**
     * 模型限制业务相关配置
     */
    @Data
    public static class ModelLimitBusinessConfig {
       
        /**
         * 业务类型，空-代表全部业务类型
         */
        private List<String> businessTypes;
        /**
         * 可选模型列表
         */
        private Map<String, ModelLimitConfig> models;
        
        /**
         * 按用户类型配置默认模型
         */
        Map<String, String> defModels;
        
        /**
         * 限定搜索意图列表，空则不限定
         */
        private List<String> searchIncludeIntentions;
        
        /**
         * 支持的意图列表，空则全部支持
         */
        private List<String> supportIntentions;

        /**
         * 支持的二级子意图列表，空则全部支持
         */
        private List<String> supportSubIntentions;


    }

    /**
     * 模型限制配置
     */
    @Data
    public static class ModelLimitConfig {
        /** 模型名称 */
        private String name;
        /** 输入字数限制 */
        private Integer length;
        /** 模型qps限制 */
        private Integer QPS;
        /** 【准备弃用，后续不要再使用】默认-模型：true-默认，false-非默认 */
        private boolean defModel = false;
        /** 会话设置的数据：true-返回给前端，false-不返回给前端 */
        private boolean sessionSet = false;
        /** 历史最大字数 */
        private Integer historyMaxLength;
        /** 排序，最小在前面 */
        private Integer sort;
        /** 是否收费：true-收费，false-免费 */
        private boolean paid = true;
        /** 模型是否支持联网 默认false */
        private boolean enableNetworkSearch = false;
        /** 大模型结果的云盘保存路径 */
        private String savePath;
        /** 图标地址 */
        private String iconUrl;
        /** 是否专属模型：true-专属模型，false-非专属模型 */
        private boolean hasExclusive;
    }

    // ------------------------------智能调度配置--------------------------- //

    public List<String> getExecuteSort(String channel) {
        return getExecuteSort(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode(), channel, "");
    }

    public List<String> getExecuteSort(String code, String channel, String defaultModel) {
        List<String> result = new ArrayList<>();
        if (!CharSequenceUtil.isEmpty(defaultModel)) {
            result.add(defaultModel);
        }
        if (ObjectUtil.isEmpty(intelligentSchedules)) {
            return result;
        }

        // 读取配置：匹配意图编码和渠道号
        IntelligentScheduleConfig scheduleConfig = null;
        for (IntelligentScheduleConfig config : intelligentSchedules) {
            if (!code.equals(config.getCode())) {
                continue;
            }
            if (ObjectUtil.isEmpty(config.getChannels())) {
                // 全渠道
                scheduleConfig = config;
            } else if (config.getChannels().contains(channel)) {
                scheduleConfig = config;
            }
        }

        // 存在配置
        if (scheduleConfig != null && !ObjectUtil.isEmpty(scheduleConfig.getExecuteSort())) {
            for (String modelCode : scheduleConfig.getExecuteSort()) {
                if (!modelCode.equals(defaultModel)) {
                    result.add(modelCode);
                }
            }
        }

        // 返回结果
        return result;
    }

    /**
     * 智能调度配置类
     */
    @Data
    public static class IntelligentScheduleConfig {
        /**
         * 意图编码
         */
        private String code;
        /**
         * 渠道号，空-代表全部渠道
         */
        private List<String> channels;
        /**
         * 执行顺序
         */
        private List<String> executeSort;
    }

}
