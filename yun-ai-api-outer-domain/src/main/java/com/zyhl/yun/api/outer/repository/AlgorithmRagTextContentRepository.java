package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.knowledge.AlgorithmRagTextContentEntity;

/**
 * <AUTHOR>
 * @description 知识库资源的正文内容HBASE操作
 * @date 2025/4/17 18:07
 */
public interface AlgorithmRagTextContentRepository {



    /**
     * 根据rowKey查询知识库资源的正文内容
     *
     * @param rowKey userId_fileId_resourceType  用户id+文件id+文件来源
     * @return 实体对象
     */
    AlgorithmRagTextContentEntity getByRowKey(String rowKey);


}
