package com.zyhl.yun.api.outer.external;

import java.util.List;

import com.zyhl.hcy.yun.ai.common.platform.third.client.note.dto.NoteListReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.note.vo.NoteDetailVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.notethird.req.NoteThirdListClientReq;
import com.zyhl.hcy.yun.ai.common.platform.third.client.notethird.vo.NoteThirdDetailVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.notethird.vo.NoteThirdListVO;

import java.util.List;

/**
 * 笔记服务
 *
 * <AUTHOR>
 */
public interface NoteExternalService {

    /**
     * 获取笔记内容
     *
     * @param token  token
     * @param noteId 笔记id
     * @return 笔记内容
     */
    String noteContent(String token, String noteId);

    /**
     * 获取笔记内容（不包含标签）
     *
     * @param token  token
     * @param noteId 笔记id
     * @return 笔记纯文本内容
     */
    String getTextContent(String token, String noteId);

    /**
     * 获取笔记列表（token版本）
     * @param reqDTO 请求参数
     * @return 笔记列表
     */
    List<NoteDetailVO> getNoteList(NoteListReqDTO reqDTO);
    
    /**
     * 获取笔记标题
     *
     * @param userId 用户id
     * @param noteId 笔记id
     * @return 笔记标题
     */
    String getTitle(String userId, String noteId);

    /**
     * 获取笔记详情，内容包含标签
     *
     * @param userId 用户id
     * @param noteId 笔记id
     * @return 笔记详情
     */
    NoteThirdDetailVO getNoteDetail(String userId, String noteId);

    /**
     * 获取笔记列表
     *
     * @param reqDTO 请求参数
     * @return 笔记列表
     */
    List<NoteThirdListVO> getNoteList(NoteThirdListClientReq reqDTO);


}
