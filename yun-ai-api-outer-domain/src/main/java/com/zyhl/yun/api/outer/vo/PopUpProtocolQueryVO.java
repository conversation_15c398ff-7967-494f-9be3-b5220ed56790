package com.zyhl.yun.api.outer.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * PopUpProtocolController.query，响应参数
 * @Author: WeiJingKun
 */
@Data
public class PopUpProtocolQueryVO implements Serializable {

    /** 标题 */
    private String title;

    /** 正文文案 */
    private String text;

    /** 协议文案 */
    private String protocol;

    /** 协议跳转的url */
    private String protocolUrl;

    /** 确认按钮文案 */
    private String confirm;

    /** 取消按钮文案 */
    private String cancel;

    /** 过期时间 */
    private Date expireTime;

}
