package com.zyhl.yun.api.outer.external;

import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.RecallQueryDTO;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.dialogue.RecallConfig;
import com.zyhl.hcy.yun.ai.common.rag.vo.RecallResultVO;
import com.zyhl.hcy.yun.ai.common.rag.vo.RerankResultVO;
import com.zyhl.hcy.yun.ai.common.rag.vo.RewriteResultVO;
import com.zyhl.yun.api.outer.domain.dto.RecallDTO;
import com.zyhl.yun.api.outer.domain.dto.RerankDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述：rag外部服务接口
 *
 * <AUTHOR> zhumaoxian  2025/2/17 14:53
 */
public interface RagExternalService {

    /**
     * 问题重写
     *
     * @param reqDTO 请求参数
     * @return 重写结果
     */
    String questionRewrite(TextModelTextReqDTO reqDTO);

    /**
     * 问题重写
     *
     * @param reqDTO 请求参数
     * @return 重写结果
     */
    RewriteResultVO rewrite(TextModelTextReqDTO reqDTO);

    /**
     * 文本向量化
     *
     * @param userId     用户id
     * @param text       文本
     * @param dialogueId 对话id
     * @return 向量结果
     */
    List<BigDecimal> textFeature(String userId, String text, Long dialogueId);

    /**
     * 文本向量化
     *
     * @param text       文本
     * @param dialogueId 对话id
     * @return 向量结果
     */
    List<BigDecimal> textEmbed(String text, Long dialogueId);

    /**
     * 多路召回
     *
     * @param dto 参数
     * @return 召回结果
     */
    List<RecallResultVO> recall(RecallDTO dto);

    /**
     * 算法重排
     *
     * @param dto 重排参数
     * @return 重排结果
     */
    List<RerankResultVO> rerank(RerankDTO dto);

    /**
     * 数字峰会算法重排
     *
     * @param text       文本
     * @param recallList 召回结果
     * @return 重排结果
     */
    List<RerankResultVO> digitalSummitRerank(String text, List<RecallResultVO> recallList);

    /**
     * 对重排结果进行相关性判断
     *
     * @param reqDTO       大模型请求参数
     * @param rerankResult 重排结果
     * @return 相关性结果
     */
    List<RerankResultVO> relevancy(TextModelTextReqDTO reqDTO, List<RerankResultVO> rerankResult);


    /**
     * 关键字提取
     *
     * @param textList 关键字提取参数
     * @return 关键字提取结果
     */
    List<List<String>> keywordExtract(List<String> textList);

    List<RecallResultVO> noteContentRecall(RecallQueryDTO dto, RecallConfig config);
}
