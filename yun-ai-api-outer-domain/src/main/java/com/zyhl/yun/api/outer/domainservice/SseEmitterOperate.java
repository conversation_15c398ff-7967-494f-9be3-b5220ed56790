package com.zyhl.yun.api.outer.domainservice;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import org.apache.catalina.connector.ClientAbortException;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.logger.util.LogUtil;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.yun.api.outer.config.FlowTypeProperties;
import com.zyhl.yun.api.outer.constants.OpenApiLingxiChatConstants;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.chat.SseNameEnum;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 描述：流式处理器方法
 *
 * <AUTHOR> zhumaoxian  2025/4/9 22:08
 */
@Slf4j
@Data
public class SseEmitterOperate {

    private FlowTypeProperties flowTypeProperties;

    private SseEmitter sseEmitter;

    /**
     * 状态
     */
    private boolean complete = false;

    /**
     * 名称
     */
    private String sseName = "AI-chat-sse";

    private static final List<String> ERROR_CODE_LIST = Arrays.asList(
            AiResultCode.CODE_01000001.getCode(),
            AiResultCode.CODE_01000003.getCode(),
            AiResultCode.CODE_01000004.getCode(),
            AiResultCode.CODE_10022024.getCode()
    );

    public SseEmitterOperate() {
        init(null, SseNameEnum.SSE_NAME);
    }

    public SseEmitterOperate(SseEmitter sseEmitter) {
        init(sseEmitter, SseNameEnum.SSE_NAME);
    }

    public SseEmitterOperate(SseEmitter sseEmitter, SseNameEnum sseNameEnum) {
        init(sseEmitter, sseNameEnum);
    }

    /**
     * 初始化
     *
     * @param sseEmitter  sseEmitter
     * @param sseNameEnum sseNameEnum
     */
    private void init(SseEmitter sseEmitter, SseNameEnum sseNameEnum) {
        flowTypeProperties = SpringUtil.getBean(FlowTypeProperties.class);
        this.sseEmitter = sseEmitter;
        this.sseName = sseNameEnum.getCode();

        if (Objects.isNull(sseEmitter)) {
            this.sseEmitter = new SseEmitter(flowTypeProperties.getTimeout());
        }
    }

    /**
     * 发送成功
     *
     * @param data 发送的数据
     * @param <T>  泛型BaseResult.data
     */
    public <T> void sendSuccess(T data) {
        send(BaseResult.success(data));
    }

    /**
     * 发送信息
     *
     * @param result 信息
     */
    public void send(BaseResult<?> result) {
        try {
            send(JsonUtil.toJson(result));
        } finally {

            if (null == result) {
                return;
            }

            // 接口日志输出
            LogUtil.setInterfaceOutput(result);

            // 这批错误码不需要打印日志
            if (ERROR_CODE_LIST.contains(result.getCode())) {
                return;
            }

        }
    }
    
    /**
     * 发送信息（object）
     *
     * @param result 信息
     */
    public void send(Object result) {
        try {
            send(JsonUtil.toJson(result));
        } finally {

            if (null == result) {
                return;
            }

            // 接口日志输出
            LogUtil.setInterfaceOutput(result);

        }
    }

    /**
     * 发送信息
     *
     * @param result 发送信息
     */
	public void send(String result) {
		if (complete) {
			log.info("【流式输出】流式对象已经关闭，无法继续发送信息");
			return;
		}
		// 发送信息
		SseEmitter.SseEventBuilder event = SseEmitter.event().id(String.valueOf(System.currentTimeMillis()))
				.reconnectTime(flowTypeProperties.getReconnectTimeMillis()).name(sseName).data(result);
		try {
			sseEmitter.send(event);
		} catch (ClientAbortException e) {
			log.error("【流式输出】发送信息失败，异常信息：{}，发送信息：{}，与端侧的连接中断", e.getMessage(), result);
		} catch (Exception e) {
			log.error("【流式输出】发送信息失败，异常信息：{}，发送信息：{}", e.getMessage(), result, e);
			complete();
		} finally {
			LogCommonUtils.printlnStrLog("2.0版本会话输入SSE接口请求结果返回 result", result);
		}
	}

    /**
     * 发送信息并关闭流对象
     *
     * @param result 信息
     */
    public void sendAndComplete(BaseResult<?> result) {
        send(result);
        complete();
    }
    
    /**
     * 发送信息并关闭流对象(obj)
     *
     * @param result 信息
     */
	public void sendAndComplete(Object obj) {
		String json = JSONUtil.toJsonStr(obj);
		send(json);
		/**
		 * 特殊场景，需要追加done
		 */
		if (json.contains(OpenApiLingxiChatConstants.LINGXI_CHAT_DATA_FLAG_JSON)) {
			send(OpenApiLingxiChatConstants.LINGXI_END_CHAT_DONE);
		}
		complete();
	}

    /**
     * 发送信息并关闭流对象
     *
     * @param result 信息
     */
	public void sendAndComplete(String result) {
		send(result);
		/**
		 * 特殊场景，需要追加done
		 */
		if (result.contains(OpenApiLingxiChatConstants.LINGXI_CHAT_DATA_FLAG_JSON)) {
			send(OpenApiLingxiChatConstants.LINGXI_END_CHAT_DONE);
		}
		complete();
	}

    /**
     * 关闭流对象
     */
    public void complete() {
        if (complete) {
            return;
        }
        try {
            log.info("【流式输出】关闭流对象");
            complete = true;
            sseEmitter.complete();
        } catch (Exception e) {
            if (!(e instanceof IllegalStateException)) {
                log.error("【流式输出】流式对象关闭异常，异常信息：{}", e.getMessage(), e);
            }
        }
    }

}
