package com.zyhl.yun.api.outer.enums;

import lombok.Getter;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 业务来源枚举
 *
 * <AUTHOR>
 */
@Getter
public enum BusinessSourceEnum {
    /**
     * 图片业务来源：AI工具，包含AI助手
     */
    TOOLS(0, "AI工具"),

    /**
     * 文本业务来源：AI助手，包含AI工具
     */
    ASSISTANT(1, "AI助手"),

    /**
     * 智能相册
     */
    ALBUM(2, "智能相册"),

    /**
     * 文档检索
     */
    DOC_SEARCH(3, "文档检索"),


    AI_VIDEO_ANALYSIS(4, "视频智能分析");


    private static final Map<Integer, BusinessSourceEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(BusinessSourceEnum.class).forEach(item -> MAP.put(item.code, item));
    }

    public static BusinessSourceEnum getByCode(Integer code) {
        return code == null ? null : MAP.get(code);
    }

    /**
     * 资源类型是否存在
     *
     * @param code 编码
     * @return true-存在
     */
    public static boolean isExist(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * AI工具
     *
     * @param code 编码
     * @return true-存在
     */
    public static boolean isTools(Integer code) {
        return TOOLS.equals(getByCode(code));
    }

    /**
     * AI助手
     *
     * @param code 编码
     * @return true-存在
     */
    public static boolean isAssistant(Integer code) {
        return ASSISTANT.equals(getByCode(code));
    }

    /**
     * 智能相册
     *
     * @param code 编码
     * @return true-存在
     */
    public static boolean isAlbum(Integer code) {
        return ALBUM.equals(getByCode(code));
    }

    /**
     * 文档检索
     *
     * @param code 编码
     * @return true-是文档检索
     */
    public static boolean isDocSearch(Integer code) {
        return DOC_SEARCH.equals(getByCode(code));
    }



    BusinessSourceEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 名称
     */
    private final String name;


}
