package com.zyhl.yun.api.outer.domain.entity.centertask;

import com.zyhl.yun.api.outer.enums.task.TaskFeePaidStatusEnum;
import com.zyhl.yun.api.outer.enums.task.TaskFeeTypeEnum;
import com.zyhl.yun.api.outer.domain.dto.ImageToolSettingDTO;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 创建ai算法任务-请求参数entity
 *
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CenterTaskCreateEntity implements Serializable {

    private static final long serialVersionUID = -9033629535076193991L;
    /**
     * 任务id，自己生成
     */
    private Long id;

    /**
     * 用户id
     */
    @NotBlank(message = "用户id-缺失")
    private String userId;

    /**
     * 算法编号，详见 DialogueIntentionEnum
     */
    @NotNull(message = "算法编号-缺失")
    private List<String> algorithmCodes;

    /**
     * 厂商编号，详见 SupplierTypeEnum
     */
    private List<String> supplierTypes;

    /**
     * 来源渠道
     */
    private String sourceChannel;

    /**
     * 付费类型：0不需要付费；1需要付费；
     *
     * @see TaskFeeTypeEnum
     */
    private Integer feeType;

    /**
     * 付费扣费状态：-1：不扣费（任务不涉及扣费流程设置-1）；0：未扣费；1：已扣费；
     *
     * @see TaskFeePaidStatusEnum
     */
    private Integer feePaidStatus;
    
    /**
     * 任务个数
     */
    private Integer number;

    /**
     * 拓展字段，如漫画风风格style, 扩图的比例
     */
    private String extendField;

    /**
     * 对话内容
     */
    private String dialogue;

    /**
     * 子意图编码
     */
    private List<String> subAlgorithmCodes;

    /**
     * 对话输入的工具设置
     */
    private ImageToolSettingDTO imageToolSettingDTO;

}
