package com.zyhl.yun.api.outer.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 全网搜推荐VO
 * @Author: WeiJingKun
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AllNetworkSearchRecommendVO implements Serializable {

    private static final long serialVersionUID = 3130866717671223658L;

    /** 推荐问题 */
    private String query;

    /** 按钮文案 */
    private String buttonCopy;

    /** 是否可以查看更多， */
    private Boolean hasMore;

}
