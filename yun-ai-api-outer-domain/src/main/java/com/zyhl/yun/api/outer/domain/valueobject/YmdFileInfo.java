package com.zyhl.yun.api.outer.domain.valueobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 * 年月日文信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class YmdFileInfo {

    /**
     * 文件ID
     */
    private String fileId;

    /**
     * 父目录ID
     */
    private String parentFileId;

    /**
     * 文件名称
     * 禁止出现以下9个非法字符：'\'、'/'、':'、'*'、'?'、'"'、'<'、'>'、'|'
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 文件类型
     * file-普通文件
     * folder-目录文件
     */
    private String type;

    /**
     * 文件扩展名，一般是后缀名
     */
    private String fileExtension;

    /**
     * 文件类别
     * image	图片
     * audio	音频
     * video	视频
     * folder 	目录
     * doc	文档
     * app	安装包
     * zip	压缩包
     * others	其他
     */
    private String category;

    /**
     * 创建时间
     * RFC 3339，2019-08-20T06:51:27.292+08:00
     */
    private String createdAt;

    /**
     * 更新时间
     * RFC 3339，2019-08-20T06:51:27.292+08:00
     */
    private String updatedAt;

    /**
     * 放入回收站时间
     * RFC 3339，2019-08-20T06:51:27.292+08:00
     */
    private String trashedAt;

    /**
     * 文件本地创建时间
     * RFC 3339，2019-08-20T06:51:27.292+08:00
     */
    private String localCreatedAt;

    /**
     * 文件本地修改时间
     * RFC 3339，2019-08-20T06:51:27.292+08:00
     */
    private String localUpdatedAt;

    /**
     * 是否收藏
     */
    private Boolean starred;

    /**
     * 普通文件大小
     * 单位：字节
     */
    private Long size;

    /**
     * 自定义标签列表
     */
    private List<TagInfo> userTags;

    @Data
    public static class TagInfo {

        /**
         * 标签名称
         * 不能包含#
         */
        private String key;

        /**
         * 标签值
         * 不能包含#
         */
        private String value;
    }

    /**
     * 缩略图信息列表
     */
    private List<ThumbnailInfo> thumbnailList;

    @Data
    public static class ThumbnailInfo {

        /**
         * 缩略图样式:S 小图 M 中图 L 大图 XL 超大图
         */
        private String style;

        /**
         * 缩略图尺寸
         * 格式：宽X高（分隔符为大写字母X）
         */
        private String size;

        /**
         * 缩略图链接
         * 对应尺寸缩略图生成失败时为空
         */
        private String url;
    }

    /**
     * 内容hash
     */
    private String contentHash;

    /**
     * 内容hash算法名，当前hash算法是sha1或sha256
     */
    private String contentHashAlgorithm;

    /**
     * 文件版本ID 当相同fileId文件覆盖上传时，会生成新的revisionId
     */
    private String revisionId;

    /**
     * 媒体元信息
     */
    private MediaMetaInfo mediaMetaInfo;

    @Data
    public static class MediaMetaInfo {

        /**
         * 图像宽度
         * 单位：像素
         */
        private Long width;

        /**
         * 图像高度
         * 单位：像素
         */
        private Long height;

        /**
         * 拍摄时间
         * RFC 3339，2019-08-20T06:51:27.292+08:00
         */
        private String takenAt;

        /**
         * 时长，单位：秒
         * 值为浮点数，精度：小数点后6位
         */
        private String duration;
    }

    /**
     * 如果存在就返回
     */
    private AddressDetail addressDetail;

    @Data
    public static class AddressDetail {

        /**
         * 详细地址
         */
        private String addressline;

        /**
         * 国家
         */
        private String country;

        /**
         * 省份
         */
        private String province;

        /**
         * 城市
         */
        private String city;

        /**
         * 区/县
         */
        private String district;

        /**
         * 乡/镇
         */
        private String township;
    }

    /**
     * 文件元信息审核信息
     */
    private FileAuditInfo metadataAuditInfo;

    @Data
    public static class FileAuditInfo {

        /**
         * 文件审核状态，0代表未审核，1代表已审核
         */
        private Integer auditStatus;

        /**
         * 审核级别，10代表机审，20代表人审，30代表人审+机审，99代表申诉复审
         */
        private Integer auditLevel;

        /**
         * 审核结果，1代表正常文件，2代表内容低敏文件，3代表内容高敏文件，4代表元信息敏感文件
         */
        private Integer auditResult;
    }

    /**
     * 文件内容审核信息
     */
    private FileAuditInfo contentAuditInfo;
}
