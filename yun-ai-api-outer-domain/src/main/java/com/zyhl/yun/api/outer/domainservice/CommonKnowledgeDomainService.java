package com.zyhl.yun.api.outer.domainservice;

import com.zyhl.hcy.yun.ai.common.base.es.entity.CommonKnowledgeEsEntity;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 公共知识库领域服务类
 * <AUTHOR>
 */
public interface CommonKnowledgeDomainService {

    /**
     * 获取公共知识库文本切片结果
     * @param baseId 知识库id
     * @param text 文本
     * @param feature 文本向量化
     * @return 结果列表
     */
//    List<CommonKnowledgeEsEntity> getTextSplitCommonKnowledgeResult(String baseId, String text, List<BigDecimal> feature);

    /**
     * 通过解析类型获取公共知识库文本切片结果
     * @param baseId 知识库id
     * @param text 文本
     * @param feature 文本向量化
     * @param parseType 解析类型
     * @return 结果列表
     */
//    List<CommonKnowledgeEsEntity> getTextSplitCommonKnowledgeResultByParseType(String baseId, String text, List<BigDecimal> feature, String parseType);

    /**
     * 获取公共知识库qa结果
     * @param baseId 知识库id
     * @param text 文本
     * @param feature 文本向量化
     * @return 结果列表
     */
//    List<CommonKnowledgeEsEntity> getQaCommonKnowledgeResult(String baseId, String text, List<BigDecimal> feature);

    /**
     * 更新公共知识库召回次数
     *
     * @param entity
     * @return
     */
    Boolean updateCommonKnowledgeRecallCount(CommonKnowledgeEsEntity entity);

    /**
     * 根据id获取公共知识库
     * @param entity 实体
     * @return
     */
    CommonKnowledgeEsEntity getCommonKnowledgeById(CommonKnowledgeEsEntity entity);

}
