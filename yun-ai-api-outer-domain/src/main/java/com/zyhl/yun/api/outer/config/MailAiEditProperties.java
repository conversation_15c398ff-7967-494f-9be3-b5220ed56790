package com.zyhl.yun.api.outer.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


/**
 * 云邮AI编辑配置信息
 * 
 * <AUTHOR>
 * @date 2025-04-30
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "yun-mail-aiedit-config")
public class MailAiEditProperties {


    /**
     * 文件路径
     */
    private String filePath;

    /**
     * AI编辑文件或输入总结提示语key
     */
    private String summaryPromptKey;
    /**
     * AI编辑文件或输入处理提示语key
     */
    private String processPromptKey;




}
