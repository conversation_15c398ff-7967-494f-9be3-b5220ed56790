package com.zyhl.yun.api.outer.domain.valueobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 图片鉴伪结果
 * <AUTHOR>
 * @date 2025/4/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImageCheckResult {

    /**
     * 是否伪造参数，12代表伪造，0代表非伪造
     */
    private Integer result;

    /**
     * 伪造分值[0,1]
     * 分值越大伪造程度越高
     */
    private Integer percent;
}
