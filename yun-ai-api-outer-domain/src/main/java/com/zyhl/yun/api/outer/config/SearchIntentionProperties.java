package com.zyhl.yun.api.outer.config;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 搜索意图配置
 * @Author: WeiJingKun
 */
@Slf4j
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "search-intention-properties")
public class SearchIntentionProperties {

    /** 搜索意图转综合搜索开关：true-打开；false-关闭（默认） */
    private boolean convertComprehensiveSearch;

    /** 拼接的搜索意图前缀 */
    private String searchPrefix;

}
