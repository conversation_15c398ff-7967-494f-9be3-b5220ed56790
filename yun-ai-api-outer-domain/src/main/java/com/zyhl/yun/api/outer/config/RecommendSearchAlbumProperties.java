package com.zyhl.yun.api.outer.config;

import com.zyhl.yun.api.outer.enums.AcceptLanguageEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;

/**
 * 推荐人物相册配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "recommend-search-album-template")
public class RecommendSearchAlbumProperties {

    /**
     * 本人name配置，默认“本人”
     */
    private String myselfName = "本人";

    /**
     * 人物名称映射
     */
    private List<NameMap> nameMapList;

    /**
     * 本人
     */
    private String myselfPromptCopy;

    /**
     * 本人-英文
     */
    private String myselfPromptCopyEn;

    /**
     * 单人
     */
    private String onlyonePromptCopy;

    /**
     * 单人-英文
     */
    private String onlyonePromptCopyEn;

    /**
     * 多人
     */
    private String multiplePromptCopy;

    /**
     * 多人-英文
     */
    private String multiplePromptCopyEn;

    /**
     * 按钮文案
     */
    private String buttonCopy;

    /**
     * 按钮文案-英文
     */
    private String buttonCopyEn;

    public String getI18nMyselfPromptCopy() {
        if (AcceptLanguageEnum.EN_US.getCode().equalsIgnoreCase(RequestContextHolder.getAcceptLanguage())) {
            return this.getMyselfPromptCopyEn();
        }
        return this.getMyselfPromptCopy();
    }

    public String getI18nOnlyonePromptCopy() {
        if (AcceptLanguageEnum.EN_US.getCode().equalsIgnoreCase(RequestContextHolder.getAcceptLanguage())) {
            return this.getOnlyonePromptCopyEn();
        }
        return this.getOnlyonePromptCopy();
    }

    public String getI18nMultiplePromptCopy() {
        if (AcceptLanguageEnum.EN_US.getCode().equalsIgnoreCase(RequestContextHolder.getAcceptLanguage())) {
            return this.getMultiplePromptCopyEn();
        }
        return this.getMultiplePromptCopy();
    }

    /**
     * 获取按钮文案
     *
     * @return {@link String}
     * <AUTHOR>
     * @date 2025/6/16 15:25
     */
    public String getI18nButtonCopy() {
        if (AcceptLanguageEnum.EN_US.getCode().equalsIgnoreCase(RequestContextHolder.getAcceptLanguage())) {
            return this.getButtonCopyEn();
        }
        return this.getButtonCopy();
    }

    public String getI18nNameValue(String name) {
        if (CollUtil.isNotEmpty(this.getNameMapList())) {
            for (NameMap nameMap : this.getNameMapList()) {
                if (nameMap.getName().equals(name)) {
                    if (AcceptLanguageEnum.EN_US.getCode().equalsIgnoreCase(RequestContextHolder.getAcceptLanguage())) {
                        return nameMap.getValueEn();
                    } else {
                        return nameMap.getValue();
                    }
                }
            }
        }
        return null;
    }

    @Data
    public static class NameMap {
        /**
         * 名称
         */
        private String name;
        /**
         * 名称显示值
         */
        private String value;
        /**
         * 名称显示值-英文
         */
        private String valueEn;

    }

    public boolean hasNameMapConfig(String name) {
        if (CollUtil.isNotEmpty(this.getNameMapList())) {
            for (NameMap nameMap : this.getNameMapList()) {
                if (nameMap.getName().equals(name) && StringUtils.isNotEmpty(nameMap.getValue())
                        && StringUtils.isNotEmpty(nameMap.getValueEn())) {
                    return true;
                }
            }
        }
        return false;

    }

}
