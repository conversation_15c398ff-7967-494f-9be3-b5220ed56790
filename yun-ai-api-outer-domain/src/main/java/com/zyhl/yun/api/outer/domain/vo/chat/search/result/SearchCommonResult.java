package com.zyhl.yun.api.outer.domain.vo.chat.search.result;

import com.zyhl.yun.api.outer.config.SearchResultProperties;
import com.zyhl.yun.api.outer.domain.vo.chat.search.SearchInfo;
import com.zyhl.yun.api.outer.enums.AcceptLanguageEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Objects;

/**
 * 对话信息-搜索结果-公共
 * @Author: WeiJingKun
 */
@Data
public class SearchCommonResult implements Serializable {

    /** 搜索信息 */
    private SearchInfo searchInfo;

    /**
     * set搜索结果-公共数据
     * PS：searchResult必须为新对象，不可为调用方法的对象
     */
    public void setSearchCommonResult(SearchResultProperties.TabSort tabSort, String dialogue, Object searchParam, Object searchResult){
        boolean enUs = AcceptLanguageEnum.EN_US.getCode().equalsIgnoreCase(RequestContextHolder.getAcceptLanguage());
        String tagName = Objects.isNull(tabSort) ? null : getTagName(enUs, tabSort);
        searchInfo = SearchInfo.builder()
                .tagName(tagName)
                .searchType(null == tabSort ? null : tabSort.getType())
                .sort(null == tabSort ? null : tabSort.getRealSort(dialogue))
                .searchParam(searchParam)
                .searchResult(searchResult)
                .build();
    }

    /**
     * 获取标签名
     */
    public String getTagName(boolean enUs, SearchResultProperties.TabSort tabSort){
        return enUs && StringUtils.isNotBlank(tabSort.getNameEn()) ? tabSort.getNameEn() : tabSort.getName();
    }

}
