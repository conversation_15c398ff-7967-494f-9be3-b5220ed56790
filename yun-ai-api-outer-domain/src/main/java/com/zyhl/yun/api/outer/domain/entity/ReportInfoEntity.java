package com.zyhl.yun.api.outer.domain.entity;

import lombok.Data;

import java.util.Date;

/**
 * 举报信息
 *
 * <AUTHOR>
 */
@Data
public class ReportInfoEntity {

    /**
     * 用户ID。
     * 该字段存储报告提交者的用户ID。
     */
    private String userId;

    /**
     * 业务类型。
     * 该字段存储报告的业务类型，具体值需参考业务类型定义。
     */
    private Integer businessType;

    /**
     * 报告原因。
     * 该字段存储用户提交报告的具体原因。
     */
    private String reasons;

    /**
     * 联系信息。
     * 该字段存储用户的联系信息，以便后续沟通。
     */
    private String contactInfo;

    /**
     * 描述。
     * 该字段存储用户对报告的详细描述。
     */
    private String describe;

    /**
     * 对象键数组。
     * 该字段存储与报告相关的对象键数组。
     */
    private String[] objectKeys;

    /**
     * 创建时间。
     * 该字段存储报告的创建时间。
     */
    private Date createTime;

    /**
     * 更新时间。
     * 该字段存储报告的最后更新时间。
     */
    private Date updateTime;

    /**
     * 删除标志。
     * 该字段指示报告是否被删除，通常 0 表示未删除，1 表示已删除。
     */
    private Integer delFlag;


}
