package com.zyhl.yun.api.outer.external;

import com.zyhl.hcy.yun.ai.common.model.api.client.ali.dto.GenericSearchDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.ali.vo.GenericSearchVO;

import java.util.List;

/**
 * interfaceName: AiInternetSearchExternalService
 * description: AI全网搜-第三方业务类
 *
 * <AUTHOR>
 * @date 2025/3/18
 */
public interface AiInternetSearchExternalService {

    /**
     * 调用阿里通用搜索接口封装方法
     *
     * @param genericSearchDTO 请求参数
     * @return 搜索结果
     */
    List<GenericSearchVO> genericSearch(GenericSearchDTO genericSearchDTO);

    /**
     * 调用阿里通用搜索接口封装方法（20250616新版本）
     *
     * @param genericSearchDTO 请求参数
     * @return 搜索结果
     */
    List<GenericSearchVO> unifiedSearch(GenericSearchDTO genericSearchDTO);

    /**
     * 知识库对话的搜索联网内容
     * 联网搜索接口：https://help.aliyun.com/document_detail/2857020.html
     *
     * @param query 用户输入的内容
     * @return 联网搜索结果
     */
    List<GenericSearchVO> knowledgeSearch(String query);

    /**
     * 模型联网搜索
     * 联网搜索接口：https://help.aliyun.com/document_detail/2857020.html
     * @param query 用户输入的内容
     * @return 联网搜索结果
     */
	List<GenericSearchVO> modelNetworkSearch(String query);
}