package com.zyhl.yun.api.outer.domainservice.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.hcy.plugin.redis.HcyRedisTemplate;
import com.zyhl.yun.api.outer.constants.CommonConstant;
import com.zyhl.yun.api.outer.domain.entity.AiPopUpProtocolEntity;
import com.zyhl.yun.api.outer.domainservice.PopUpProtocolDomainService;
import com.zyhl.yun.api.outer.repository.IAiPopUpProtocolRepository;
import com.zyhl.yun.api.outer.vo.PopUpProtocolQueryVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * AI弹窗协议配置-DomainServiceImpl
 *
 * @Author: WeiJingKun
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PopUpProtocolDomainServiceImpl implements PopUpProtocolDomainService {

    @Resource
    private IAiPopUpProtocolRepository iaiPopUpProtocolRepository;

    @Resource
    private HcyRedisTemplate<String, Object> hcyRedisTemplate;

    @Resource(name = "businessThreadPool")
    ExecutorService businessThreadPool;

    @Override
    public PopUpProtocolQueryVO protocolGet(AiPopUpProtocolEntity entity) {
        /** 参数初始化 */
        Date now = new Date();

        /** 查询缓存 */
        String cacheKey = String.format(CommonConstant.AI_POP_UP_PROTOCOL_QUERY_CACHE_KEY, entity.getChannel(), entity.getType());
        PopUpProtocolQueryVO queryVO = (PopUpProtocolQueryVO) hcyRedisTemplate.opsForValue().get(cacheKey);

        /** 缓存为null */
        if (ObjectUtil.isEmpty(queryVO)) {
            /** 获取AI弹窗协议配置，并存入缓存 */
            queryVO = getPopUpProtocolQueryVO(entity, now, cacheKey);
            return queryVO;
        } else {
            /** 缓存不为null，判断是否逻辑过期 */
            Date expireTime = queryVO.getExpireTime();
            // 没有逻辑过期时间 || 逻辑过期，则查询DB更新缓存
            if (expireTime == null || now.after(expireTime)) {
                // 线程异步处理：获取AI弹窗协议配置，并存入缓存
                businessThreadPool.execute(() -> getPopUpProtocolQueryVO(entity, now, cacheKey));
            }
            // 返回暂时的过期时间
            return queryVO;
        }

    }

    /**
     * 获取AI弹窗协议配置，并存入缓存
     *
     * @Author: WeiJingKun
     */
    private PopUpProtocolQueryVO getPopUpProtocolQueryVO(AiPopUpProtocolEntity entity, Date now, String cacheKey) {
        /** 参数初始化 */
        PopUpProtocolQueryVO queryVO = new PopUpProtocolQueryVO();
        try {
            /** 获取AI弹窗协议配置 */
            AiPopUpProtocolEntity queryEntity = iaiPopUpProtocolRepository.query(entity);
            // AI弹窗协议配置-Entity -> PopUpProtocolController.get，响应参数
            BeanUtils.copyProperties(queryEntity, queryVO);
            // 设置逻辑过期时间（5分钟）
            queryVO.setExpireTime(DateUtil.offsetMinute(now, 5));

            /** 存入缓存（60分钟），设置60分钟的原因：防止设置时间短，两个时间都过期，直接打到DB（预防缓存穿透问题） */
            hcyRedisTemplate.opsForValue().set(cacheKey, queryVO, 60, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("cache error", e);
        } finally {
            log.info("\n参数：{}\n结果：{}\ncacheKey：{}", JSONUtil.toJsonStr(entity), JSONUtil.toJsonStr(queryVO), cacheKey);
        }
        return queryVO;
    }

}
