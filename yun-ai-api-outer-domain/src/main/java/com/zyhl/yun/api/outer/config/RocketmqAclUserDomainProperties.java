package com.zyhl.yun.api.outer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * mq实例配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "rocketmq.acl.user-domain")
public class RocketmqAclUserDomainProperties {

    /**
     * 访问密钥，common包使用了这个字段
     */
    private String accessKey;
    /**
     * 密钥，common包使用了这个字段
     */
    private String secretKey;
    /**
     * 访问点类型，common包使用了这个字段
     */
    private String access_point_type;
    /**
     * 服务地址
     */
    private String namesrvAddr;
    /**
     * 命名空间
     */
    private String namespace;
    /**
     * 实例名称
     */
    private String instanceName;

}
