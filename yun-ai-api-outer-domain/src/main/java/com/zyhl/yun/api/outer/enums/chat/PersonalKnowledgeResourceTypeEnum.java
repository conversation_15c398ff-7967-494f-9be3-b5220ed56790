package com.zyhl.yun.api.outer.enums.chat;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 个人知识库资源类型枚举
 * @Author: WeiJingKun
 */
@Getter
@AllArgsConstructor
public enum PersonalKnowledgeResourceTypeEnum {
    /** 文件 */
    FILE(0, "文件"),
    /** 邮件 */
    EMAIL(1, "邮件"),
    /** 笔记 */
    NOTE(2, "笔记"),
    /** 在线链接 */
    ONLINE_LINK(3, "在线链接");

    /**
     * code
     */
    private final Integer code;
    /**
     * 名称
     */
    private final String name;


    private static final Map<Integer, PersonalKnowledgeResourceTypeEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(PersonalKnowledgeResourceTypeEnum.class).forEach(item -> MAP.put(item.code, item));
    }

    public static PersonalKnowledgeResourceTypeEnum getType(Integer code) {
        if (null == code) {
            return null;
        }
        return MAP.get(code);
    }
}
