package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 个人知识库文件内的附件资源删除标识
 *
 * <AUTHOR>
 * @date 2025/02/13
 */
@Getter
@AllArgsConstructor
public enum UserKnowledgeFileResDelFlagEnum {

    /**
     * 正常
     */
    NORMAL(0, "正常"),

    /**
     * 已删除
     */
    DELETED(1, "已删除"),

    /**
     * 删除中
     */
    DELETING(2, "删除中"),

    ;

    /**
     * 状态
     */
    private final Integer delFlag;

    /**
     * 备注
     */
    private final String remark;

}
