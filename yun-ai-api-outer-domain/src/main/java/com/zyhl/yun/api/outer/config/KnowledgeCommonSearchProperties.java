package com.zyhl.yun.api.outer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

/**
 * className: KnowledgeCommonSearchProperties
 * description: 公共知识库-多路召回检索配置
 *
 * <AUTHOR>
 * @date 2025/2/13
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "knowledge.common.rag.search")
public class KnowledgeCommonSearchProperties {

    /**
     * 多路检索召回策略数组
     */
    private Type type;

    /**
     * 知识库检索类型（只针对文档切片SPLIT）
     */
    private Split split;

    /**
     * smalltobig策略合并阈值（只针对文档切片SPLIT）
     */
    private SmallToBig smallToBig;

    @Data
    public static class Type {

        /**
         * 策略类型集合
         * 默认：SPLIT,QA
         * @see com.zyhl.hcy.yun.ai.common.base.enums.ParseTypeEnum
         */
        private List<String> list = Arrays.asList("SPLIT", "QA");
    }

    @Data
    public static class Split {

        /**
         * 类型枚举
         * 0：固定切片长度 (默认)
         * 1：small to big策略
         */
        private Integer type = 0;
    }

    @Data
    public static class SmallToBig {

        /**
         * 具体阈值
         * 大于等于阈值，则合并取大切片的内容
         */
        private Float mergeSize = 0.5F;
    }
}
