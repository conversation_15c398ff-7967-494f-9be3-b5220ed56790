package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识库公开级别枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum KnowledgeOpenLevelEnum {

    /**
     * 私密
     */
    PRIVATE(0, "私密（默认）"),

    /**
     * 公开（默认）
     */
    OPEN(1, "公开"),

    ;

    /**
     * 状态
     */
    private final Integer level;

    /**
     * 描述
     */
    private final String remark;

    public static boolean isOpen(Integer level) {
        return OPEN.level.equals(level);
    }

}
