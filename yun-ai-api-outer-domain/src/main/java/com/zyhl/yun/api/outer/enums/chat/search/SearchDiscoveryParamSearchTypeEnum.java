package com.zyhl.yun.api.outer.enums.chat.search;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 对话信息-搜索参数-发现广场-搜索类型枚举
 * @Author: WeiJingKun
 */
@Getter
@AllArgsConstructor
public enum SearchDiscoveryParamSearchTypeEnum {

    /** 普通搜索 */
    REGULAR_SEARCH(1, "普通搜索"),
    /** 扩词搜索 */
    EXPAND_WORD_SEARCH(2, "扩词搜索"),

    ;

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 描述
     */
    private final String desc;

}
