package com.zyhl.yun.api.outer.domain.vo;

import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.AddressDetail;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.MediaMetaInfo;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description}
 * <p>
 *
 * <AUTHOR>
 * @since 4月14 2025
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileCompleteVO implements Serializable {

  /**
   * 文件id
   */
  public String fileId;
  /**
   * 父目录id
   */
  public String parentFileId;

  /**
   * 名称
   */
  public String name;

  /**
   * 类型，枚举值file/folder
   */
  public String type;
  /**
   * 分类， 根据文件的后缀名和mime-type对文件进行分了分类，主要分类有doc, image, audio, video
   */
  public String category;
  /**
   * 创建时间，RFC 3339，2019-08-20T06:51:27.292Z
   */
  public String createdAt;
  /**
   * 更新时间，RFC 3339，2019-08-20T06:51:27.292Z
   */
  public String updatedAt;

  /**
   * 文件本地创建时间, 本地创建时间是指文件在上传时刻的本地时间，使用rcf3339
   */
  public String localCreatedAt;
  /**
   * 文件本地修改时间，文件本地更新时间, 本地更新时间是指文件在上传时刻的本地时间，使用rcf3339
   */
  public String localUpdatedAt;
  /**
   * 大小
   */
  public Long size;

  /**
   * 文件扩展名，一般是后缀名
   */
  public String fileExtension;
  /**
   * 缩略图地址，目前仅图片、视频文件返回
   */
  private String thumbnailUrl;

  /**
   * 内容hash算法名，当前仅支持sha1或者sha256， 不区分大小写内容hash算法名
   */
  public String contentHashAlgorithm;
  /**
   * 内容hash
   */
  public String contentHash;

  /**
   * 如果存在就返回
   */
  public MediaMetaInfo mediaMetaInfo;
  /**
   * 如果存在就返回
   */
  public AddressDetail addressDetail;

  /**
   * 是否收藏
   */
  public Boolean starred;

}
