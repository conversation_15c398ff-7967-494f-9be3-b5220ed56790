package com.zyhl.yun.api.outer.domainservice.impl;

import cn.hutool.core.text.StrPool;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.HttpUtil;
import com.zyhl.yun.api.outer.config.ImageCommonProperties;
import com.zyhl.yun.api.outer.domainservice.IImageCommonService;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Optional;
import java.util.StringJoiner;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.domainservice.impl.ImageCommonServiceImpl} <br>
 * <b> description:</b>
 * 图片公共处理接口实现类
 *
 * <AUTHOR>
 * @date 2024-03-09 13:51
 **/
@Slf4j
@Service
public class ImageCommonServiceImpl implements IImageCommonService {

    public static final String IMAGE_BASE64_HEADER = "data:image/";

    public static final String VIDEO_BASE64_HEADER = "data:video/";

    @Resource
    private ImageCommonProperties imageCommonProperties;

    @Override
    public String urlToLocalPath(String url, String imageExt, String userId) {
        File localFile = new File(createPathBaseOnDate(url, imageExt, userId));
        if (localFile.exists()) {
            return localFile.getAbsolutePath();
        }
        if (StringUtils.isNotBlank(url)) {
            try {
                // 方案1：使用HttpRequest并配置SSL，忽略证书验证
                HttpRequest request = HttpRequest.get(url)
                        .setSSLSocketFactory(getSSLSocketFactory())
                        .setReadTimeout(30000)
                        .setConnectionTimeout(3000);

                try (HttpResponse response = request.execute()) {
                    if (response.isOk()) {
                        byte[] bytes = response.bodyBytes();
                        FileUtils.writeByteArrayToFile(localFile, bytes);
                        return localFile.getAbsolutePath();
                    } else {
                        log.error("urlToLocalPath download fail, status: {}, | userId:{}", response.getStatus(), userId);
                        throw new YunAiBusinessException(ResultCodeEnum.UNKNOWN_ERROR);
                    }
                }
            } catch (Exception e) {
                log.error("urlToLocalPath using HttpRequest failed, trying alternative method, | userId:{}, | e: {}", userId, e);
                // 方案2：使用自定义的HttpUtil类（备选方案）
                try {
                    byte[] bytes = HttpUtil.downLoadFromUrl(url);
                    FileUtils.writeByteArrayToFile(localFile, bytes);
                    return localFile.getAbsolutePath();
                } catch (IOException e2) {
                    log.error("urlToLocalPath alternative method also failed, | userId:{}, | e: {}", userId, e2);
                    throw new YunAiBusinessException(ResultCodeEnum.UNKNOWN_ERROR);
                }
            }
        }
        return StringUtils.EMPTY;
    }

    @Override
    public String base64ToLocalPath(String base64, String imageExt, String userId) {
        base64 = getImgContent(base64);
        File localFile = new File(createPathBaseOnDate(base64, imageExt, userId));
        if (localFile.exists()) {
            return localFile.getAbsolutePath();
        }
        if (StringUtils.isNotBlank(base64)) {
            try {
                FileUtils.writeByteArrayToFile(localFile, Base64.getDecoder().decode(base64));
            } catch (IOException e) {
                log.error("base64ToLocalPath write file fail, | userId:{}, | e: {}", userId, e);
                throw new YunAiBusinessException(ResultCodeEnum.UNKNOWN_ERROR);
            }
            return localFile.getAbsolutePath();
        }
        return StringUtils.EMPTY;
    }

    /**
     * 获取真实base64内容
     *
     * @param base64Str the base 64 string
     * @return {@link String}
     * <AUTHOR>
     * @date 2024-3-12 16:04
     */
    public String getImgContent(String base64Str) {
        if (base64Str.startsWith(IMAGE_BASE64_HEADER)) {
            return base64Str.substring(base64Str.indexOf(",") + 1);
        }
        if (base64Str.startsWith(VIDEO_BASE64_HEADER)) {
            return base64Str.substring(base64Str.indexOf(",") + 1);
        }
        return base64Str;
    }

    /**
     * 创建文件路径，同一天内相同
     *
     * @param base64OrUrl the base64 or url
     * @param imageExt    the image ext
     * @param userId      the user id
     * @return {@link String}
     * <AUTHOR>
     * @date 2024-3-9 15:40
     */
    @Override
    public String createPathBaseOnDate(String base64OrUrl, String imageExt, String userId) {
        //基础路径
        String catalog = imageCommonProperties.getSharePath() + File.separator + imageCommonProperties.getFodderCatalog();
        StringJoiner path = new StringJoiner(File.separator,
                File.separator, File.separator);
        //根据日期来分
        LocalDateTime localDateTime = LocalDateTime.now();
        String resultPath = (catalog + File.separator + userId + File.separator + Optional.of(localDateTime)
                .map(d -> {
                    path.add(String.valueOf(d.getYear()))
                            .add(String.valueOf(d.getMonthValue()))
                            .add(String.valueOf(d.getDayOfMonth()));
                    return DigestUtils.md5Hex(base64OrUrl) + StrPool.DOT + imageExt;
                })
                .map(StringUtils::upperCase)
                .map(path::add)
                .orElseThrow(() ->
                        new YunAiBusinessException(ResultCodeEnum.UNKNOWN_ERROR)).toString());
        if(File.separator.equals(String.valueOf(resultPath.charAt(resultPath.length() - 1)))) {
            //最后一位字符是分隔符，需要去掉
            resultPath = resultPath.substring(0, resultPath.length() - 1);
        }
        return resultPath;
    }

    /**
     * 创建目录路径，同一天内相同
     *
     * @param base64OrUrl the base64 or url
     * @param imageExt    the image ext
     * @param userId      the user id
     * @return {@link String}
     * <AUTHOR>
     * @date 2024-3-9 15:40
     */
    @Override
    public String createDirPathBaseOnDate(String base64OrUrl, String businessPath, String userId) {
        // 基础路径
        String catalog = imageCommonProperties.getSharePath() + File.separator
                + imageCommonProperties.getFodderCatalog();
        StringJoiner path = new StringJoiner(File.separator, File.separator, File.separator);
        // 根据日期来分
        LocalDateTime localDateTime = LocalDateTime.now();
        return (catalog + File.separator + userId + File.separator + Optional.of(localDateTime).map(d -> {
                    path.add(String.valueOf(d.getYear())).add(String.valueOf(d.getMonthValue()))
                            .add(String.valueOf(d.getDayOfMonth()));
                    return businessPath + File.separator + DigestUtils.md5Hex(base64OrUrl);
                }).map(StringUtils::upperCase).map(path::add)
                .orElseThrow(() -> new YunAiBusinessException(ResultCodeEnum.UNKNOWN_ERROR)).toString());
    }

    /**
     * 获取忽略证书验证的SSLSocketFactory
     * @return SSLSocketFactory
     */
    private SSLSocketFactory getSSLSocketFactory() {
        try {
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, getTrustManager(), new SecureRandom());
            return sslContext.getSocketFactory();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取信任所有证书的TrustManager
     * @return TrustManager数组
     */
    private TrustManager[] getTrustManager() {
        return new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public void checkServerTrusted(X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[]{};
                    }
                }
        };
    }
}
