package com.zyhl.yun.api.outer.enums.drive;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 独立空间审核结果
 * 审核结果 1代表正常文件 2代表内容低敏文件 3代表内容高敏文件 4代表元信息敏感文件
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OwnerDriveAuditResultEnum {

    /**
     * 正常文件
     */
    NORMAL_FILE(1, "正常文件"),

    /**
     * 内容低敏文件
     */
    LOW_SENSITIVE_CONTENT(2, "内容低敏文件"),

    /**
     * 内容高敏文件
     */
    HIGH_SENSITIVE_CONTENT(3, "内容高敏文件"),

    /**
     * 元信息敏感文件
     */
    METADATA_SENSITIVE(4, "元信息敏感文件");

    private final Integer code;
    private final String description;

    public static boolean isNormalFile(Integer code) {
        return NORMAL_FILE.code.equals(code);
    }
    public static boolean isNotNormalFile(Integer code) {
        return !NORMAL_FILE.code.equals(code);
    }

    public static boolean isLowSensitiveContent(Integer code) {
        return LOW_SENSITIVE_CONTENT.code.equals(code);
    }

    public static boolean isHighSensitiveContent(Integer code) {
        return HIGH_SENSITIVE_CONTENT.code.equals(code);
    }

    public static boolean isMetadataSensitive(Integer code) {
        return METADATA_SENSITIVE.code.equals(code);
    }
}

