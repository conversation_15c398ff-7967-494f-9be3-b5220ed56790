package com.zyhl.yun.api.outer.domain.dto;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.yun.api.outer.constants.RegConst;
import lombok.Data;

/**
 * 描述：用户信息
 *
 * <AUTHOR> zhumaoxian  2025/2/19 10:38
 */
@Data
public class UserInfoDTO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 所属平台，详见所属平台枚举说明
     */
    private Integer belongsPlatform;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 省份编码
     */
    private String province;

    /**
     * 用户简介信息，包括头像和昵称
     */
    private UserProfileInfoDTO userProfileInfo;

    public Integer getProvinceCode() {
        if (ObjectUtil.isEmpty(province) || !province.matches(RegConst.REG_DATA_STR)) {
            return null;
        }
        return Integer.parseInt(province);
    }

}
