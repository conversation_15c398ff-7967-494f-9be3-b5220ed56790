package com.zyhl.yun.api.outer.domainservice.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.config.ModelWhiteProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.domainservice.UserTypeDomainService;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.UserTypeEnum;
import com.zyhl.yun.api.outer.repository.AlgorithmChatConfigRepository;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 会话大语言模型设定DomainService实现类
 * @Author: WeiJingKun
 */
@Slf4j
@Service
public class ChatConfigServiceDomainServiceImpl implements ChatConfigServiceDomainService {

    @Resource
    private AlgorithmChatConfigRepository algorithmChatConfigRepository;

    @Resource
    private UserTypeDomainService userTypeDomainService;

    @Resource
    private ModelProperties modelProperties;

    @Resource
    private ModelWhiteProperties modelWhiteProperties;
    
    @Resource
    private SourceChannelsProperties sourceChannelsProperties;
    
    @Override
    public ChatConfigEntity getUserCanUseModel(String userId, String phone, AssistantEnum assistantEnum, String businessType) {
        // 获取用户数据库大模型配置
        ChatConfigEntity chatConfigEntity = algorithmChatConfigRepository.get(userId, assistantEnum.getCode());
        String modelCode = ObjectUtil.isNull(chatConfigEntity) ? null : chatConfigEntity.getModelType();
        // 获取用户类型枚举
     	UserTypeEnum userTypeEnum = null;
		if (StringUtils.isEmpty(modelCode)) {
			// 用户没设置模型，才获取用户类型枚举
			userTypeEnum = userTypeDomainService.getUserType(userId, phone,
					sourceChannelsProperties.getAllInnerBenefitNos());
		}

		//联网搜索状态
		Integer networkSearchStatus = null;
		if(null != chatConfigEntity) {
			networkSearchStatus = chatConfigEntity.getNetworkSearchStatus();
		}
        // 检查并获取用户可用模型
        String canUseModel = modelProperties.checkAndGetCanUseModel(assistantEnum, businessType, modelCode, true, userTypeEnum);
        // 模型白名单校验，针对特殊场景只设置某些账号有权限操作特定模型
        boolean modelAccessible = modelWhiteProperties.isModelAccessible(canUseModel, phone);
        log.info("检查并获取用户可用模型：{},模型白名单权限：{}，用户id：{}", canUseModel, modelAccessible, userId);
        if (CharSequenceUtil.isNotBlank(canUseModel) && modelAccessible) {
            return new ChatConfigEntity(userId, canUseModel, assistantEnum.getCode(), networkSearchStatus);
        }
		// 用户没设置模型，才获取用户类型枚举
		userTypeEnum = userTypeDomainService.getUserType(userId, phone,
				sourceChannelsProperties.getAllInnerBenefitNos());
		return new ChatConfigEntity(userId, modelProperties.getModelByUserType(assistantEnum, businessType, userTypeEnum),
				assistantEnum.getCode(), networkSearchStatus);
    }

}
