package com.zyhl.yun.api.outer.domainservice;

import com.zyhl.hcy.yun.ai.common.base.es.entity.PersonalKnowledgeEsEntity;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

/**
 * 个人知识库领域服务类
 *
 * <AUTHOR>
 */
public interface PersonalKnowledgeDomainService {

    /**
     * 获取答案 1、问题向量化 2、es搜索 3、判断评分阈值
     *
     * @param question 问题
     * @param feature  问题向量
     * @param userId   用户id
     * @return 不为空在表示存在匹配项，为空表示不存在答案
     */
//    List<PersonalKnowledgeEsEntity> getPersonalKnowledgeResult(String question, List<BigDecimal> feature, String userId);

    /**
     * 通过解析类型，获取答案 1、问题向量化 2、es搜索 3、判断评分阈值
     *
     * @param question 问题
     * @param feature  问题向量
     * @param userId   用户id
     * @param parseType 解析类型
     * @return 不为空在表示存在匹配项，为空表示不存在答案
     */
//    List<PersonalKnowledgeEsEntity> getPersonalKnowledgeResultByParseType(String question, List<BigDecimal> feature, String userId, String parseType);

    /***
     * 更新个人知识库召回次数
     *
     * @param userId 用户id
     * @param id ES id
     * @return 更新结果
     */
    Boolean updatePersonalKnowledgeRecallCount(String userId, String id);

    /**
     * 根据id获取个人知识库ES数据
     * @param userId 用户id
     * @param id ES id
     * @return
     */
    PersonalKnowledgeEsEntity getPersonalKnowledgeById(String userId, String id);

    /**
     * 物理删除，逻辑删除请用updateDocument更新is_deleted为true
     * @param userId 用户id
     * @param fileIdList 待删除的文件ID集合
     * @param delSize 删除数量 为0时取默认100
     * @param knowledgeDeleteThreadPool 线程池，参照outer服务默认配置的线程池knowledgeDeleteThreadPool
     */
    public void batchDeleteDocumentById(String userId, List<String> fileIdList, int delSize, ExecutorService knowledgeDeleteThreadPool);

}
