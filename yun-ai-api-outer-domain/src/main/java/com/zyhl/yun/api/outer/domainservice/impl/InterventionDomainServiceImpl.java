package com.zyhl.yun.api.outer.domainservice.impl;

import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.es.domain.datahelper.repository.EsInterventionRepository;
import com.zyhl.hcy.yun.ai.common.base.es.entity.EsInterventionEntity;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.vo.TextFeatureExtractVO;
import com.zyhl.yun.api.outer.config.InterventionProperties;
import com.zyhl.yun.api.outer.domainservice.InterventionDomainService;
import com.zyhl.yun.api.outer.external.CmicTextService;
import com.zyhl.yun.api.outer.util.CustomStringUtil;
import com.zyhl.yun.api.outer.vo.InterventionVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

/**
 * 干预库领域服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class InterventionDomainServiceImpl implements InterventionDomainService {


    @Resource
    private InterventionProperties interventionProperties;

    @Resource
    private CmicTextService cmicTextService;

    @Resource
    private EsInterventionRepository esInterventionRepository;

    @Override
    public InterventionVO getIntervention(String channel, String clientType, String clientVersion, String h5Version,
                                          String question) {
        Double minScoreConfig = interventionProperties.getMinScore();
        String minimumShouldMatch = interventionProperties.getMinimumShouldMatch();
        Double score = null;
        InterventionVO interventionVO = null;
        try {

            if (StringUtils.isBlank(channel) || StringUtils.isBlank(clientType) || StringUtils.isBlank(clientVersion)) {
                log.info(
                        "getIntervention 干扰库匹配答案 渠道或者客户端类型为空，不执行匹配干扰库 channel:{}, clientType:{}, clientVersion:{}, h5Version:{}",
                        channel, clientType, clientVersion, h5Version);
                return null;
            }

            if (StringUtils.isBlank(question)) {
                log.info(
                        "getIntervention 干扰库匹配答案 问题为空，不执行匹配干扰库 channel:{}, clientType:{}, clientVersion:{}, h5Version:{}",
                        channel, clientType, clientVersion, h5Version);
                return null;
            }
            //给传入问题去除字符
            question = CustomStringUtil.getEsText(question);
            // 默认开，channel渠道开关，配置开关
            if (!interventionProperties.isOpen(channel)) {
                log.info("getIntervention 干扰库匹配答案 渠道配置关闭干扰库，不执行匹配干扰库 channel:{}", channel);
                return null;
            }

            // 获取文本向量
            TextFeatureExtractVO vo = cmicTextService.getTextFeature(question);
            if (null == vo || CollectionUtils.isEmpty(vo.getFeature())) {
                log.info("getIntervention 干扰库匹配答案 获取文本向量空，不执行匹配干扰库 channel:{}, question:{}", channel, question);
                return null;
            }
            String answerVersion = getAnswerVersion(channel, clientType, clientVersion, h5Version);
            EsInterventionEntity entity = esInterventionRepository.getIntervention(channel, clientType, answerVersion, question, vo.getFeature());
            if (entity != null) {
                interventionVO = new InterventionVO();
                //记录版本号
                interventionVO.setVersion(answerVersion);
                interventionVO.setId(entity.getId());
                interventionVO.setQuestion(entity.getQuestion());
                interventionVO.setAnswer(entity.getAnswer());
                interventionVO.setType(entity.getType());
            }

        } catch (Exception e) {
            log.error("InterventionDomainService-getIntervention question:{}, channel:{} 干扰库匹配答案error:", question,
                    channel, e);
        } finally {
            log.info(
                    "InterventionDomainService-getIntervention-finally 干扰库匹配答案 question:{}, channel:{}, clientType:{}, clientVersion:{}, h5Version:{}, "
                            + "minimumShouldMatch:{}, minScoreConfig is {}, score:{}, interventionVO:{}",
                    question, channel, clientType, clientVersion, h5Version, minimumShouldMatch, minScoreConfig, score,
                    (null != interventionVO ? JsonUtil.toJson(interventionVO) : null));
        }
        return interventionVO;
    }

    private String getAnswerVersion(String channel, String clientType, String clientVersion, String h5Version) {
        return interventionProperties.getAnswerVersion(channel, clientType, clientVersion, h5Version);
    }
}
