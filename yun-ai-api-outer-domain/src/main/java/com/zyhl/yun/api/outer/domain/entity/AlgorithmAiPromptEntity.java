package com.zyhl.yun.api.outer.domain.entity;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.domain.entity.AlgorithmAiPromptEntity} <br>
 * <b> description:</b>
 * AI提示词模板实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-06-28 11:34
 **/
@Data
@Builder
public class AlgorithmAiPromptEntity implements Serializable {
    private static final long serialVersionUID = -6193374560236015589L;

    /**
     * id
     */
    private Long id;

    /**
     * 提示词编码
     */
    private String promptKey;

    /**
     * 提示词名称
     */
    private String promptName;

    /**
     * 提示词模板
     */
    private String promptTemplate;

    /**
     * 记录历史版本  默认1，更新累加+
     */
    private Integer version;

    /**
     * 用户流量调度做A/Btest, 默认1,分子是当前权重/分母是相同key的权重之和
     */
    private Integer weight;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 下限值
     */
    private Integer lowerBound;

    /**
     * 上限值
     */
    private Integer upperBound;

    /**
     * 业务类型
     */
    private String businessType;
}
