package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;

/**
 * <AUTHOR>
 * @version 2024年02月28日 15:51
 */

public interface AlgorithmChatConfigRepository {

    /**
     * 保存对话配置
     * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
     *
     * @param chatConfigEntity 对话配置
     * @return true:保存成功，false:保存失败
     */
    boolean saveOrUpdate(ChatConfigEntity chatConfigEntity);

    /**
     * 用户设置的模型
     *
     * @param userId 用户id
     * @param businessCode 业务编码
     * @return 对话配置
     */
    ChatConfigEntity get(String userId, String businessCode);
}
