package com.zyhl.yun.api.outer.config;

import com.zyhl.hcy.yun.ai.common.base.qpslimit.dto.TokenBucketDTO;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 令牌桶配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "dialogue-bucket")
public class TokenBucketProperties {

    /**
     * 文本模型配置
     */
    private Map<String, TokenBucket> textModel;

    @Data
    public static class TokenBucket {
        /**
         * 每秒qps
         */
        private Integer period;
        /**
         * 桶大小
         */
        private Integer limit;
        /**
         * 最大并发数
         */
        private Integer maxBurstSize;
        /**
         * 获取令牌的等待时间(ms)
         */
        private long timeoutMillis;
        /**
         * 取模算法基数 默认2
         */
        private Integer scale;
        /**
         * redis超时时间
         */
        private Integer expireTime;
    }

    /**
     * 转换为TokenBucketDTO
     *
     * @param modelCode 模型编码
     * @return 令牌桶参数
     */
    public TokenBucketDTO toTokenBucketDTO(String modelCode) {
        // 获取配置
        TokenBucket tokenBucket = textModel.get(modelCode);
        if (tokenBucket == null) {
            return null;
        }

        TokenBucketDTO tokenBucketDTO = new TokenBucketDTO();
        if (null != tokenBucket.getPeriod()) {
            tokenBucketDTO.setPeriod(tokenBucket.getPeriod());
        }
        if (null != tokenBucket.getScale()) {
            tokenBucketDTO.setScale(tokenBucket.getScale());
        }
        tokenBucketDTO.setMaxBurstSize(tokenBucket.getMaxBurstSize() != null ? tokenBucket.getMaxBurstSize() : tokenBucket.getLimit());
        tokenBucketDTO.setLimit(tokenBucket.getLimit());
        tokenBucketDTO.setTimeoutMillis(tokenBucket.getTimeoutMillis());
        tokenBucketDTO.setExpireTime(tokenBucket.getExpireTime());
        return tokenBucketDTO;
    }

}
