package com.zyhl.yun.api.outer.config;

import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * 送审敏感配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties("check-sensitive")
public class CheckSensitiveConfig {

    /**
     * 本地词库
     */
    private List<String> localWords;

}
