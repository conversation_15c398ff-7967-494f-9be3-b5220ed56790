package com.zyhl.yun.api.outer.util;

import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiPersonResourceEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName PersonResourceExcelParser
 * <AUTHOR>
 **/
@Slf4j
public class PersonResourceExcelParser {
    private static final String[] EXPECTED_HEADERS = {"人名", "关系", "资源名称"};

    /**
     * 解析Excel文件
     */
    public static List<AlgorithmAiPersonResourceEntity> parse(File file) throws IOException {
        List<AlgorithmAiPersonResourceEntity> validData = new ArrayList<>();

        try (Workbook workbook = WorkbookFactory.create(file)) {
            Sheet sheet = workbook.getSheetAt(0);

            // 标题行验证
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {

                log.info("缺少标题行");
                return null;
            }

            // 验证标题列
            for (int i = 0; i < EXPECTED_HEADERS.length; i++) {
                Cell cell = headerRow.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                String header = cell.getStringCellValue().trim();

                if (!header.equals(EXPECTED_HEADERS[i])) {
                    log.info("标题列错误");
                    return null;
                }
            }

            // 处理数据行
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {

                processRow(sheet.getRow(i), validData);
            }
        }
        return validData;
    }

    private static void processRow(Row row, List<AlgorithmAiPersonResourceEntity> validData) {
        if (row == null) {
            log.info("空行");
            return;
        }

        AlgorithmAiPersonResourceEntity employee = new AlgorithmAiPersonResourceEntity();

        try {
            // 主键
            Cell nameCell = row.getCell(0);
            if (null == nameCell || nameCell.getCellType() == CellType.BLANK) {
                log.info("人名不能为空");
                return;
            } else {
                employee.setName(nameCell.getStringCellValue().trim());
            }

            // 资源名称
            Cell relationshipCell = row.getCell(1);
            if (null == relationshipCell || relationshipCell.getCellType() == CellType.BLANK) {
                log.info("关系不能为空");
                return;
            } else {
                employee.setRelationship(relationshipCell.getStringCellValue().trim());
            }

            // 类型
            Cell resourceNameCell = row.getCell(2);
            if (null == resourceNameCell || resourceNameCell.getCellType() == CellType.BLANK) {
                log.info("资源名称不能为空");
                return;
            } else {
                employee.setResourceName(resourceNameCell.getStringCellValue().trim());
            }

        } catch (Exception e) {
            log.error("数据解析异常", e);
        }

        validData.add(employee);
    }
}
