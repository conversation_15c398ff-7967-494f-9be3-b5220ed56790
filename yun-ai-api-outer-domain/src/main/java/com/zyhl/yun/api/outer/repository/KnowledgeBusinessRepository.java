package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.knowledge.KnowledgeBusinessEntity;

/**
 * 知识库业务映射表
 *
 * <AUTHOR>
 */
public interface KnowledgeBusinessRepository {

    /**
     * 根据baseId查询
     *
     * @param baseId       知识库标识
     * @param businessCode 业务编码
     * @return 知识库映射实体
     */
    KnowledgeBusinessEntity selectOne(String baseId, String businessCode);

}
