package com.zyhl.yun.api.outer.domainservice;


import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.domain.entity.TemplateMatchEntity;
import com.zyhl.yun.api.outer.domain.resp.TemplateMatchRsqEntity;

/**
 * 模板匹配服务
 *
 * <AUTHOR>
 */
public interface TemplateMatchService {

    /**
     * 图片标签与影集模板匹配模型
     *
     * @param entity 模板匹配实体
     * @return 模板匹配结果
     */
    BaseResult<TemplateMatchRsqEntity> templateMatch(TemplateMatchEntity entity);
}
