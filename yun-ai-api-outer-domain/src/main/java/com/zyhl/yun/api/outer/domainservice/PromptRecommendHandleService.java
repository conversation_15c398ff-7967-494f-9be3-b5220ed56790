package com.zyhl.yun.api.outer.domainservice;

import com.zyhl.yun.api.outer.domain.vo.PromptRecommendVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/17 9:46
 * @Description prompt recommend 数据处理相关业务接口
 */
public interface PromptRecommendHandleService {

    /**
     * 根据prompt key获取对应的推荐词列表
     * @return 推荐词列表
     */
    List<PromptRecommendVO> getPromptRecommendList(String channel);
}
