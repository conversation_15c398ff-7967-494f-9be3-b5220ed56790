package com.zyhl.yun.api.outer.domain.entity.image;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.List;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.zyhl.yun.api.outer.constants.CommonConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 相册生成条件实体
 * 
 * <AUTHOR>
 * @description:
 * @date 2025-06-11 09:14
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AlbumConditionEntity {

	/**
	 * 时间范围列表
	 */
	private List<DateRange> dateRangeList;

	/**
	 * 搜索规则，空则不限制
	 * 
	 * @see com.zyhl.yun.api.outer.enums.AlbumConditionRuleEnum
	 * 
	 */
	private String searchRule;

	/**
	 * 搜索张数，空则不限制
	 */
	private Integer searchSize;

	/**
	 * 时间范围
	 */
	@Data
	@Builder
	@AllArgsConstructor
	@NoArgsConstructor
	public static class DateRange {
		/**
		 * 开始日期，格式yyyy-MM-dd，空则不限制
		 */
		private String startDate;

		/**
		 * 结束日期，格式yyyy-MM-dd，空则不限制
		 */
		private String endDate;

		/**
		 * 获取开始时间的 00:00:00
		 * 
		 * @return date 时间类型
		 */
		public LocalDateTime getFirstTime() {
			// 开始时间的过滤
			DateTime date = DateUtil.parse(this.startDate, CommonConstant.YYYY_MM_DD);

			// 将Date转换为LocalDate
			LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			// 获取当天的00:00:00时间
			return localDate.atStartOfDay();
		}

		/**
		 * 获取开始时间的 23:59:59
		 *
		 * @return date 时间类型
		 */
		public LocalDateTime getEndTime() {
			DateTime startDateTime = DateUtil.parse(this.endDate, CommonConstant.YYYY_MM_DD);
			LocalDate localDate = startDateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			return localDate.atTime(LocalTime.MAX);
		}
	}
}
