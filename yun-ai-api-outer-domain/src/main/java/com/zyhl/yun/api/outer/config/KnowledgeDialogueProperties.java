package com.zyhl.yun.api.outer.config;

import com.zyhl.hcy.yun.ai.common.rag.dto.validate.dialogue.*;
import com.zyhl.yun.api.outer.config.knowledge.InternetSearchConfig;
import com.zyhl.yun.api.outer.config.knowledge.PoliticianInfo;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 知识库对话配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "knowledge.dialogue")
public class KnowledgeDialogueProperties {

    /**
     * 指定使用的公共知识库的标识
     */
    private String knowledgeBaseId = "";
    /**
     * 公共知识库白名单列表
     */
    private List<String> whiteList = new ArrayList<>();
    /**
     * 个人知识库开关
     */
    private Boolean personalSwitch = false;

    /**
     * 开头文案
     */
    private Map<String, String> titleMap;

    /**
     * 结束语
     */
    private String ending = "当前知识库未找到相关内容，已由大模型为您生成回答";

    /**
     * 可用知识库的渠道
     */
    private List<String> enableChannelList = new ArrayList<>();

    /*
     * -------------------------------------------以下是rag2.2版本的配置方式-----------------------------------------------------
     */

    /**
     * 重写配置
     */
    private RewriteConfig rewriteConfig = new RewriteConfig();

    /**
     * 关键字提取配置
     */
    private KeywordConfig keywordConfig = new KeywordConfig();

    /**
     * 多路召回配置
     */
    private RecallConfig recallConfig = new RecallConfig();

    /**
     * 多路召回配置-总结类、建议类、发言稿
     */
    private RecallConfig recallConfigSummary = new RecallConfig();

    /**
     * 【默认】算法重排配置
     */
    private RerankConfig rerankConfig = new RerankConfig();

    /**
     * 【默认】算法重排配置-总结类、建议类、发言稿
     */
    private RerankConfig rerankConfigSummary = new RerankConfig();

    /**
     * 【向量】算法重排配置
     */
    private RerankConfig vectorRerankConfig = new RerankConfig();
    /**
     * 【向量】算法重排配置-总结类、建议类、发言稿
     */
    private RerankConfig vectorRerankConfigSummary = new RerankConfig();

    /**
     * 【全文】算法重排配置
     */
    private RerankConfig textRerankConfig = new RerankConfig();
    /**
     * 【全文】算法重排配置-总结类、建议类、发言稿
     */
    private RerankConfig textRerankConfigSummary = new RerankConfig();

    /**
     * 【关键字】算法重排配置
     */
    private RerankConfig keywordRerankConfig = new RerankConfig();
    /**
     * 【关键字】算法重排配置-总结类、建议类、发言稿
     */
    private RerankConfig keywordRerankConfigSummary = new RerankConfig();

    /**
     * 相关性配置
     */
    private RelevancyConfig relevancyConfig = new RelevancyConfig();

    /**
     * 对话配置
     */
    private DialogueConfig dialogueConfig = new DialogueConfig();


    /**
     * 数据搜索超时时间
     */
    private int searchTimeout = 10;

    /**
     * 知识库角标开关
     */
    private boolean enableMark = false;
    /**
     * 行政人物信息
     */
    private boolean politicianEnabled = true;
    private List<PoliticianInfo> politicianList = new ArrayList<>();

    /**
     * 知识库名词库开关
     */
    private boolean enableNounLibrary = false;

    /**
     * 多路重排开关
     */
    private boolean enableMultiRerank = false;

    /**
     * 联网搜索配置
     */
    private InternetSearchConfig internetSearchConfig = new InternetSearchConfig();
}
