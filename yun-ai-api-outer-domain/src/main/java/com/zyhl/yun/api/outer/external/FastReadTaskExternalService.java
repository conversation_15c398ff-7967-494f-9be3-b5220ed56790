package com.zyhl.yun.api.outer.external;

import com.zyhl.hcy.yun.ai.common.model.api.client.centertask.vo.CenterTaskCreateVO;
import com.zyhl.yun.api.outer.domain.entity.centertask.CenterTaskCreateEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.TextParamEntity;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description}
 * <p>
 *
 * <AUTHOR>
 * @since 4月15 2025
 */
public interface FastReadTaskExternalService {

  /**
   * 创建ai算法任务【文本类】
   *
   * @param createEntity
   * @param textParamEntity
   * @return
   */
  CenterTaskCreateVO createCommonTextTask(
      CenterTaskCreateEntity createEntity, TextParamEntity textParamEntity);

}
