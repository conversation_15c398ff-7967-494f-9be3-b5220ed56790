package com.zyhl.yun.api.outer.domain.vo.common;

import cn.hutool.core.collection.CollUtil;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * AC自动机结果
 * 结果int[]的值：[0]匹配的下标，[1]匹配的长度
 * @Author: WeiJingKun
 */
@Data
@Builder
public class AhoCorasickResult {

    /** 匹配的下标 */
    private Integer index;
    /** 匹配的长度 */
    private Integer length;

    /**
     * 构建AC自动机结果列表，并根据index字段正序排序
     * @Author: WeiJingKun
     * @param searchIndexList AC自动机原结果列表
     * @return AC自动机结果列表
     */
    public static List<AhoCorasickResult> createAhoCorasickResultList(List<int[]> searchIndexList) {
        List<AhoCorasickResult> ahoCorasickResultList = new ArrayList<>();
        if(CollUtil.isNotEmpty(searchIndexList)){
            for (int i = 0; i < searchIndexList.size(); i++) {
                // 结果int[]的值：[0]匹配的下标，[1]匹配的长度
                AhoCorasickResult ahoCorasickResult = AhoCorasickResult.builder().index(searchIndexList.get(i)[0]).length(searchIndexList.get(i)[1]).build();
                ahoCorasickResultList.add(ahoCorasickResult);
            }
        }
        if(CollUtil.isNotEmpty(ahoCorasickResultList)){
            // 根据index字段正序排序
            ahoCorasickResultList.sort(Comparator.comparing(AhoCorasickResult::getIndex, Comparator.nullsFirst(Comparator.naturalOrder())));
        }
        return ahoCorasickResultList;
    }

}
