package com.zyhl.yun.api.outer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 知识库文件审核URL相关配置
 *
 * <AUTHOR>
 */
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "audit.not-pass")
public class KnowledgeAuditUrlProperties {
    private String thumbnailUrl;
    // 审核不通过和谐图片
    private String imageUrl;
}
