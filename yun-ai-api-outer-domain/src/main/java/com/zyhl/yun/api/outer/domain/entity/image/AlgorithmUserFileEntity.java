package com.zyhl.yun.api.outer.domain.entity.image;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;

/**
 * <p>
 * AlgorithmUserFilePO
 * </p>
 *
 * <AUTHOR>
 * @description 用户文件资源表
 * @since 2024/4/8 15:38
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AlgorithmUserFileEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 文件id
     */
    private String fileId;


    /**
     * 文件id
     */
    private String metaRowKey;


    /**
     * 旧文件id
     */
    private String oldFileId;

    /**
     * 文件归属
     */
    private String ownerId;

    /**
     * 业务类型：
     * -1 - 未知类型
     * 1-personal 个人云
     * 2-group 圈子
     * 3-shareGroup 共享群
     * 4-family 家庭云
     * 5-mount 挂载盘
     * 6-note 笔记
     * 7-cardpackage 卡包
     * 8-system 系统空间
     * 9-partner 合作空间
     * 10-activity 活动空间 照片直播
     */
    private String ownerType;

    /**
     * paas平台编码
     */
    private String paasCode;


    /**
     * 文件所属节点
     */
    private String driveId;

    /**
     * 父目录Id
     */
    private String parentFileId;

    /**
     * 文件id全路径
     */
    private String fullFileIdPath;

    /**
     * 文件哈希算法名
     */
    private String hashName;

    /**
     * 文件哈希值
     */
    private String hashValue;


    /**
     * 文件类型:1-文件，2-目录
     */
    private String fileType;

    /**
     * 内容类型
     */
    private Integer contentType;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件/目录分类,见字典定义
     * 1 图片
     * 2 音频
     * 3 视频
     * 4 文档
     * 5 应用
     * 6 压缩文件
     * 0 其他
     * 100 普通目录
     */
    private String category;


    /**
     * 文件位置
     */
    private String filePosition;


    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 宽度
     */
    private Long width;

    /**
     * 长度
     */
    private Long height;


    /**
     * 音视频时⻓
     */
    private Double duration;


    /**
     * 文件后缀
     */
    private String extension;


    /**
     * 原文件id全路径
     */
    private String originFullFileIdPath;


    /**
     * 拍摄时间
     */
    private Date shotTime;

    /**
     * 上传时间
     */
    private Date uploadTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 移入回收站时间
     */
    private Date trashedTime;

    /**
     * 删除标识
     */
    private Integer delFlag;

    /**
     * 创建人Id
     */
    private String createdBy;

    /**
     * 更新人Id
     */
    private String updatedBy;


    /**
     * 文件创建时间
     */
    private Date fileCreatedAt;
    /**
     * 文件更新时间
     */
    private Date fileUpdatedAt;
    /**
     * 区分卡包文件还是卡包盘中的卡包文件；默认0
     * 0.其他
     * 1.代表卡包盘
     * 2.代表个人云卡包
     */
    private Integer cardPackageType;

    /**
     * 本地更新时间
     */
    private Date localUpdatedAt;

    /**
     * 本地创建时间
     */
    private Date localCreatedAt;
}
