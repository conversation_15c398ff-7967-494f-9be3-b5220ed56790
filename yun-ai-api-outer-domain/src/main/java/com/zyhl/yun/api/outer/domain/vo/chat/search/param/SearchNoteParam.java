package com.zyhl.yun.api.outer.domain.vo.chat.search.param;

import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.validator.keywords.NonBlankStrings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 对话信息-搜索参数-笔记
 * @Author: WeiJingKun
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchNoteParam extends SearchCommonParam implements Serializable {

    /** 分页信息 */
    @Builder.Default
    private PageInfoDTO pageInfo = new PageInfoDTO("", 10, 1);

    /** 搜索关键字列表（每个关键字之间是或的关系），最多传十个关键词 */
    @Size(max = 10, message = "搜索关键字列表不能超过十个")
    @NotEmpty(message = "搜索关键字列表不能为空")
    @NonBlankStrings(message = "搜索关键字不能为空")
    private List<String> keywords;

    /**
     * 搜索类型（默认值0）
     * 普通笔记：0（除废纸篓和加密柜以外的笔记）
     * 录音笔记：1（noteType = 1且archived != 2）
     * 加密柜：2（archived = 2）
     * 废纸篓：3（noteStatus=5）
     * 语音笔记：4（noteType=2 且archived != 2）
     * AI听记笔记：5（noteType=3 且archived != 2）
     */
    @Builder.Default
    private Integer searchType = 0;

    /**
     * 普通笔记类型列表，不传则查所有的普通笔记
     * 0：常规文字笔记
     * 1：录音笔记
     * 2：语音笔记
     * 3：ai听记笔记
     * 4：新录音笔记
     */
    private List<Integer> noteTypeList;

    /**
     * Mobile：手机客户端
     * PC：PC客户端
     * 139Mail：139邮箱
     * 参数为空时默认Mobile
     */
    private String searchSource;

    /**
     * 搜索时间范围列表，每个时间范围均为或的关系
     * 如果填了，TimeRange中的两个字段就是必填
     */
    @Valid
    private List<MillisecondTimeRange> timeRangeList;

    @Getter
    @AllArgsConstructor
    public enum NoteTypeEnum {
        /**
         * 常规文字笔记类型，适用于普通的文本笔记。
         */
        TYPE0(0, "常规文字笔记"),

        /**
         * 录音笔记类型，适用于通过录音设备录制的笔记。
         */
        TYPE1(1, "录音笔记"),

        /**
         * 语音笔记类型，适用于通过语音识别技术生成的笔记。
         */
        TYPE2(2, "语音笔记"),

        /**
         * AI听记笔记类型，适用于通过人工智能技术自动听记的笔记。
         */
        TYPE3(3, "ai听记笔记"),

        /**
         * 新录音笔记类型，适用于最新的录音笔记格式或功能。
         */
        TYPE4(4, "新录音笔记");

        /** 枚举值 */
        private final Integer code;
        /** 备注信息 */
        private final String remark;

    }

}
