package com.zyhl.yun.api.outer.domainservice.impl;

import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.vo.RagMailDetailVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.File;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.FileResult;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.AIFileVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.BatchFileVO;
import com.zyhl.yun.api.outer.domain.dto.ExternalResourceInfoDTO;
import com.zyhl.yun.api.outer.domainservice.ResourceInfoDomainService;
import com.zyhl.yun.api.outer.enums.knowledge.FileCategoryEnum;
import com.zyhl.yun.api.outer.external.MailExternalService;
import com.zyhl.yun.api.outer.external.NoteExternalService;
import com.zyhl.yun.api.outer.external.YunDiskExternalService;

import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 描述：资源信息领域服务
 *
 * <AUTHOR> zhumaoxian  2025/3/10 17:51
 */
@Slf4j
@Service
public class ResourceInfoDomainServiceImpl implements ResourceInfoDomainService {

    @Resource
    private NoteExternalService noteExternalService;
    @Resource
    private MailExternalService mailExternalService;
    @Resource
    private YunDiskExternalService yunDiskExternalService;

    @Override
    public ExternalResourceInfoDTO getMailInfo(String phone, String resourceId) {
        try {
            RagMailDetailVO vo = mailExternalService.getMailInfo(phone, resourceId);
            if (vo != null) {
                ExternalResourceInfoDTO dto = new ExternalResourceInfoDTO();
                dto.setName(vo.getSubject());
                return dto;
            }
        } catch (Exception e) {
            log.error("获取邮件信息异常，异常信息：{}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public ExternalResourceInfoDTO getNoteInfo(String userId, String resourceId) {
        try {
            ExternalResourceInfoDTO dto = new ExternalResourceInfoDTO();
            dto.setName(noteExternalService.getTitle(userId, resourceId));
            return dto;
        } catch (Exception e) {
            log.error("获取笔记信息异常，异常信息：{}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public ExternalResourceInfoDTO getImgInfo(String userId, Integer belongsPlatform, String resourceId) {
        try {
            List<String> resourceIdList = Collections.singletonList(resourceId);
            List<String> imageThumbnailStyleList = Collections.singletonList("Big");
            BatchFileVO vo = yunDiskExternalService.fileBatchGetByAllPlatform(userId, belongsPlatform, resourceIdList, imageThumbnailStyleList);
            if (Objects.nonNull(vo) && Objects.nonNull(vo.getBatchFileResults())) {
                FileResult fileInfo = vo.getBatchFileResults().get(0);
                if (Objects.isNull(fileInfo.getSrcFile())) {
                    return null;
                }

                File file = fileInfo.getSrcFile();
                ExternalResourceInfoDTO dto = new ExternalResourceInfoDTO();
                dto.setResourceId(resourceId);
                dto.setName(file.getName());
                dto.setThumbnailUrl(ObjectUtil.isEmpty(file.getThumbnailUrls()) ? "" : file.getThumbnailUrls().get(0).getUrl());
                dto.setCategory(file.getCategory());
                if (FileCategoryEnum.VIDEO.getYundiskCategory().equals(file.getCategory())
                        || FileCategoryEnum.AUDIO.getYundiskCategory().equals(file.getCategory())) {
                    log.info("音视频文件，重新获取源文件地址");
                    AIFileVO fileVO = yunDiskExternalService.getFileInfo(userId, resourceId);
                    if (Objects.nonNull(fileVO)) {
                        dto.setPresentUrl(fileVO.getContent());
                    }
                }
                return dto;
            }
        } catch (Exception e) {
            log.error("获取图片信息异常，异常信息：{}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public String getMailContent(String sid, String rmkey, String resourceId) {
        try {
            return mailExternalService.mailContent(sid, rmkey, resourceId);
        } catch (Exception e) {
            log.error("获取邮件信息异常，异常信息：{}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public String getNoteContent(String token, String resourceId) {
        try {
            return noteExternalService.noteContent(token, resourceId);
        } catch (Exception e) {
            log.error("获取笔记信息异常，异常信息：{}", e.getMessage(), e);
            throw e;
        }
    }
}
