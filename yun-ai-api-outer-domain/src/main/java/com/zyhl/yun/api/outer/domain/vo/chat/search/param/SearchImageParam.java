package com.zyhl.yun.api.outer.domain.vo.chat.search.param;

import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 对话信息-搜索参数-图片
 * @Author: WeiJingKun
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchImageParam extends SearchCommonParam implements Serializable {

    /** 关键字 */
    private String text;

    /** 分页信息 */
    @Builder.Default
    private PageInfoDTO pageInfo = new PageInfoDTO("", 50, 1);

    /**
     * 排序方式（不填默认1）
     * 1--按照图片拍摄时间倒序排序（如拍摄时间为空则用上传时间）
     * 2--按照相关度倒序排序
     */
    private Integer sortType;
    
	/**
	 * true--只返回华为搜图数据 不走个人云
	 * 
	 * false--走个人云
	 */
	private Boolean isOriginal;

}
