package com.zyhl.yun.api.outer.external;

import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.*;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.resp.OwnerDriveResponse;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.*;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveDirVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.enums.ThumbnailStyleEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import java.util.Map;
import java.util.List;

/**
 * 用户独立空间
 *
 * <AUTHOR>
 */
public interface UserDriveExternalService {

    /**
     * 获取用户独立空间id
     *
     * @param userId 用户id
     * @return 返回空间id
     */
    String getUserDriveId(String userId);

    /**
     * 创建目录
     *
     * @param userId  用户id
     * @param dirName 目录名
     * @return 返回目录id
     */
    String createCatalog(String userId, String dirName);

    /**
     * 指定父目录ID创建目录
     *
     * @param userId
     * @param parentFileId
     * @param dirName
     * @return
     */
    OwnerDriveDirVO createCatalog(String userId, String dirName, String parentFileId);

    /**
     * 创建目录
     *
     * @param userId       用户id
     * @param dirName      目录名
     * @param parentFileId 父目录id
     * @param renameMode   重命名模式
     * @return 创建目录结果
     */
    OwnerDriveDirVO createCatalog(String userId, String dirName, String parentFileId, String renameMode);

    /**
     * 创建文件转存任务
     *
     * @param userInfo    用户信息
     * @param dirId       目录id
     * @param fileIdsList 文件id集合
     * @return 返回任务id
     */
    String transTask(RequestContextHolder.UserInfo userInfo, String dirId, List<String> fileIdsList);

    /**
     * 获取文件下载地址
     *
     * @param userId 用户id
     * @param fileId 文件id
     * @return 返回下载地址，完整的url
     */
    String getDownloadUrl(String userId, String fileId);


    OwnerDriveFileDownloadBatchVO getBatchFilesDownloadUrl(String userId, List<String> fileIds);

    /**
     * 获取文件详情
     *
     * @param userId 用户id
     * @param fileId 文件id
     * @return 返回文件详情
     */
    OwnerDriveFileVO getFileInfo(String userId, String fileId);

    /**
     * 获取文件详情
     *
     * @param userId 用户id
     * @param fileId 文件id
     * @return 返回文件详情
     */
    OwnerDriveFileVO getFileInfo(String userId, String fileId, ThumbnailStyleEnum... styleList);

    /**
     * 删除文件
     *
     * @param userId  用户id
     * @param fileIds 文件id集合
     * @return 返回删除任务id
     */
    String deleteByFileIds(String userId, List<String> fileIds);

    /**
     * 移动文件
     *
     * @param userId         用户id
     * @param fileIds        文件id集合
     * @param toParentFileId 目的父文件夹 id，当移入根目录时，填 "/"
     * @return 返回移动任务id
     */
    String moveByFileIds(String userId, List<String> fileIds, String toParentFileId);

    /**
     * 获取删除任务结果
     *
     * @param userId 用户id
     * @param taskId 任务id
     * @return 返回删除任务结果
     */
    OwnerDriveTaskResultVO getDeleteTaskResult(String userId, String taskId);

    /**
     * 批量获取文件详情
     *
     * @param userId     用户id
     * @param fileIdList 文件id列表
     * @return 文件详情列表
     */
    List<OwnerDriveFileVO> getBatchFileInfo(String userId, List<String> fileIdList);

    /**
     * 批量获取文件详情
     *
     * @param req 文件id列表
     * @return 文件详情列表
     */
    List<OwnerDriveFileVO> getBatchFileInfo(OwnerDriveBatchFileReqDTO req);

    /*
     * 获取独立空间大小
     *
     * @param dto 参数
     * @return 返回
     */
    OwnerDriveVO getSpace(OwnerDriveReqDTO dto);

    OwnerDriveResponse updateFileInfo(OwnerDriveFileUpdateReqDTO reqDTO);

    /**
     * 检查文件是否存在
     *
     * @param userId 用户id
     * @param fileId 文件id
     * @return true存在 false不存在 （注意：这个方法只判断目录）
     */
    boolean checkFileExist(String userId, String fileId);

    /**
     * 批量获取文件全路径
     *
     * @param userId  用户id
     * @param fileIds 文件id列表
     * @return 结果map 目录id为kye为key，文件ID 拼接的全路径为value的
     */
    Map<String, String> batchFileGetPath(String userId, List<String> fileIds);
}
