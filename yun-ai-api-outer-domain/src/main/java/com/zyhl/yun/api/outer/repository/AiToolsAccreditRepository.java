package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.AiToolsAccreditEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/6 16:53
 */
public interface AiToolsAccreditRepository {

    /**
     * 查询单个记录
     *
     * @param entity 实体对象
     * @return 实体对象
     */
    AiToolsAccreditEntity selectOne(AiToolsAccreditEntity entity);


    /**
     * 插入报名（授权记录）
     *
     * @param entity 实体对象
     */
    void insert(AiToolsAccreditEntity entity);

    /**
     * 根据id更新授权记录
     * @Author: WeiJingKun
     *
     * @param entity 更新条件
     */
    void updateById(AiToolsAccreditEntity entity);

    /**
     * 查询用户报名授权记录
     *
     * @param entity 实体对象
     * @return 实体对象
     */
    List<AiToolsAccreditEntity> queryByUserId(AiToolsAccreditEntity entity);

    /**
     * 查询用户所有的报名记录
     *
     * @param userId 用户id
     * @return 实体对象集合
     */
    List<AiToolsAccreditEntity> queryByUserId(String userId);
}
