package com.zyhl.yun.api.outer.domain.vo.chat.search.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 对话信息-搜索参数-个人云资产接口分页信息-排序查询条件
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SortRange implements Serializable {

    /**
     * 排序字段
     * createdAt：上传时间
     * updatedAt：更新时间
     * takenAt：拍摄时间(仅对图片有效)
     * relevancy：相关度(仅支持降序)
     */
    @NotEmpty(message = "排序字段不能为空")
    private String orderBy;

    /**
     * 排序方向
     * true：降序
     * false：升序
     */
    @NotNull(message = "排序顺序不能为空")
    private Boolean orderDirection;

}
