package com.zyhl.yun.api.outer.enums.chat;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 对话提示词编码枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ChatPromptCodeEnum {

    RAG_NOUN_LIBRARY("noun_library", "rag_user_noun_library", "rag_system_noun_library", "知识库名词库"),

    RAG_NETWORK_MARK("network_mark_0", "rag_user_network_mark", "rag_system_network_mark", "知识库对话联网有角标"),
    RAG_NETWORK_UNMARK("network_unmark_0", "rag_user_network_unmark", "rag_system_network_unmark", "知识库对话联网无角标"),
    RAG_UNNETWORK_MARK("unnetwork_mark_0", "rag_user_unnetwork_mark", "rag_system_unnetwork_mark", "知识库对话无联网有角标"),
    RAG_UNNETWORK_UNMARK("unnetwork_unmark_0", "rag_user_unnetwork_unmark", "rag_system_unnetwork_unmark", "知识库对话无联网无角标"),

    RAG_NETWORK_MARK_1("network_mark_1", "rag_user_network_mark_1", "rag_system_network_mark_1", "知识库对话联网有角标"),
    RAG_NETWORK_UNMARK_1("network_unmark_1", "rag_user_network_unmark_1", "rag_system_network_unmark_1", "知识库对话联网无角标"),
    RAG_UNNETWORK_MARK_1("unnetwork_mark_1", "rag_user_unnetwork_mark_1", "rag_system_unnetwork_mark_1", "知识库对话无联网有角标"),
    RAG_UNNETWORK_UNMARK_1("unnetwork_unmark_1", "rag_user_unnetwork_unmark_1", "rag_system_unnetwork_unmark_1", "知识库对话无联网无角标"),

    RAG_NETWORK_MARK_2("network_mark_2", "rag_user_network_mark_2", "rag_system_network_mark_2", "知识库对话联网有角标"),
    RAG_NETWORK_UNMARK_2("network_unmark_2", "rag_user_network_unmark_2", "rag_system_network_unmark_2", "知识库对话联网无角标"),
    RAG_UNNETWORK_MARK_2("unnetwork_mark_2", "rag_user_unnetwork_mark_2", "rag_system_unnetwork_mark_2", "知识库对话无联网有角标"),
    RAG_UNNETWORK_UNMARK_2("unnetwork_unmark_2", "rag_user_unnetwork_unmark_2", "rag_system_unnetwork_unmark_2", "知识库对话无联网无角标"),

    RAG_NETWORK_MARK_3("network_mark_3", "rag_user_network_mark_3", "rag_system_network_mark_3", "知识库对话联网有角标"),
    RAG_NETWORK_UNMARK_3("network_unmark_3", "rag_user_network_unmark_3", "rag_system_network_unmark_3", "知识库对话联网无角标"),
    RAG_UNNETWORK_MARK_3("unnetwork_mark_3", "rag_user_unnetwork_mark_3", "rag_system_unnetwork_mark_3", "知识库对话无联网有角标"),
    RAG_UNNETWORK_UNMARK_3("unnetwork_unmark_3", "rag_user_unnetwork_unmark_3", "rag_system_unnetwork_unmark_3", "知识库对话无联网无角标"),

    RAG_FORCE_NETWORK_MARK("force_network_mark", "rag_user_force_network_mark", "rag_system_force_network_mark", "知识库对话强制联网有角标"),
    RAG_FORCE_NETWORK_UNMARK("force_network_unmark", "rag_user_force_network_unmark", "rag_system_force_network_unmark", "知识库对话强制联网无角标"),


    ;

    private final String code;
    private final String userPrompt;
    private final String systemPrompt;
    private final String desc;


    private static final Map<String, ChatPromptCodeEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(ChatPromptCodeEnum.class).forEach(item -> MAP.put(item.getCode(), item));
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return
     */
    public static ChatPromptCodeEnum getByCode(String code) {
        return MAP.get(code);
    }

    public static ChatPromptCodeEnum getByCode(boolean enableNetworkSearch, boolean enableMark, int type) {
        String network = enableNetworkSearch ? "network" : "unnetwork";
        String mark = enableMark ? "mark" : "unmark";
        return getByCode(network + "_" + mark + "_" + type);
    }

    public static ChatPromptCodeEnum getByCode(boolean enableMark) {
        String mark = enableMark ? "mark" : "unmark";
        return getByCode("force_network_" + mark);
    }

}
