package com.zyhl.yun.api.outer.config;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 客户端类型配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "client-type")
public class ClientTypeProperties {

    // ---------------------- 客户端类型 start ---------------------- //

    /**
     * 客户端列表
     */
    private List<ClientType> list;

    /**
     * 端内客户端
     *
     * @param clientType 客户端类型
     * @return true-端内
     */
    public boolean isInner(String clientType) {
        if (ObjectUtil.isEmpty(list) || CharSequenceUtil.isEmpty(clientType)) {
            return false;
        }

        for (ClientType item : list) {
            if (clientType.equals(item.getCode())) {
                return "inner".equals(item.getEnd());
            }
        }

        return false;
    }


    /**
     * 客户端
     */
    @Data
    public static class ClientType {
        /**
         * 客户端编号
         */
        private String code;
        /**
         * 客户端备注
         */
        private String remark;
        /**
         * 端内-inner，端外-outer
         */
        private String end = "outer";
    }

    // ---------------------- 客户端类型 end ---------------------- //
}
