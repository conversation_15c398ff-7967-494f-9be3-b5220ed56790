package com.zyhl.yun.api.outer.pool;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class ThreadPoolConfig {

    @Value("${business.thread.pool.core.size:0}")
    private Integer coreSize;

    @Value("${business.thread.pool.max.size:0}")
    private Integer maxSize;

    @Value("${business.thread.pool.queue.capacity:10000}")
    private Integer queueCapacity;

    @Value("${business.thread.pool.alive.seconds:60}")
    private Integer aliveSeconds;

    /**
     * 笔记比数
     */
    @Bean(name = "noteSyncThreadPoolExecutor")
    public ThreadPoolTaskExecutor exportMoveInfoDataThreadPoolExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // CPU核数
        int availableProcessors = Runtime.getRuntime().availableProcessors();
        // 设置核心线程数为 CPU 核心数
        if (ObjectUtil.equal(coreSize, 0)) {
            coreSize = availableProcessors;
        }
        // 设置最大线程数为 CPU 核心数 + 1
        if (ObjectUtil.equal(maxSize, 0)) {
            maxSize = availableProcessors + 1;
        }
        // 核心线程池大小
        executor.setCorePoolSize(coreSize);
        // 最大线程数
        executor.setMaxPoolSize(maxSize);
        // 队列容量
        executor.setQueueCapacity(queueCapacity);
        // 活跃时间
        executor.setKeepAliveSeconds(aliveSeconds);
        // 线程名字前缀
        executor.setThreadNamePrefix("hcy-saas-business-note-sync-");
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //MDC上下文丢失
        executor.setTaskDecorator(runnable -> {
            Map<String, String> contextMap = MDC.getCopyOfContextMap();
            return () -> {
                try {
                    if (Objects.nonNull(contextMap)) {
                        MDC.setContextMap(contextMap);
                    }
                    runnable.run();
                } finally {
                    MDC.clear();
                }
            };
        });
        // 初始化线程池
        executor.initialize();
        return executor;
    }


}
