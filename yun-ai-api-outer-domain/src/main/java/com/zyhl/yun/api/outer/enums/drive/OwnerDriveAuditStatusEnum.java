package com.zyhl.yun.api.outer.enums.drive;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 独立空间审核状态
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OwnerDriveAuditStatusEnum {

    /**
     * 未审核
     */

    UNAUDITED(0, "未审核"),

    /**
     * 已审核
     */
    AUDITED(1, "已审核"),

    ;
    /**
     * 状态
     */
    private final Integer status;
    /**
     * 描述
     */
    private final String remark;


    public static boolean isUnAudited(Integer status) {
        return UNAUDITED.status.equals(status);
    }

    public static boolean isAudited(Integer status) {
        return AUDITED.status.equals(status);
    }


}
