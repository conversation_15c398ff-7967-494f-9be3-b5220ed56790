package com.zyhl.yun.api.outer.repository;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeLabelFileEntity;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 个人知识库标签与文件的关系
 *
 * <AUTHOR>
 */
public interface UserKnowledgeLabelFileRepository {

    /**
     * 新增标签与文件关系
     *
     * @param entity 实体对象
     * @return 数量
     */
    int add(@NotNull UserKnowledgeLabelFileEntity entity);

    /**
     * 根据id更新
     *
     * @param entity 实体对象
     * @return 数量
     */
    int updateById(@NotNull UserKnowledgeLabelFileEntity entity);

    /**
     * 更新标签与文件关系
     *
     * @param userId     用户id
     * @param labelId    标签id
     * @param newLabelId 新的标签id
     * @return 数量
     */
    int updateLabelId(@NotNull String userId, @NotNull Long labelId, @NotNull Long newLabelId);

    /**
     * 根据id删除
     *
     * @param id 标签id
     * @return 数量
     */
    int deleteById(@NotNull Long id);

    /**
     * 根据文件id删除关系，物理删除
     *
     * @param userId  用户id
     * @param fileIds 文件id集合
     */
    void deleteByFileIds(@NotNull String userId, @NotNull List<String> fileIds);

    /**
     * 批量根据文件id删除关系，物理删除
     *
     * @param userId  用户id
     * @param fileIds  文件id集合
     */
    void batchDeleteByUserId(@NotNull String userId, @NotNull List<String> fileIds);

    /**
     * 根据用户id删除关系，逻辑删除
     *
     * @param userId  用户id
     * @param lableId 标签id
     * @param fileId  文件id
     */
    void deleteByUserId(@NotNull String userId, @NotNull Long lableId, @NotNull String fileId);

    /**
     * 查询一条
     *
     * @param userId  用户id
     * @param labelId 标签id
     * @param fileId  文件id
     * @return 实体对象
     */
    UserKnowledgeLabelFileEntity selectOne(@NotNull String userId, @NotNull Long labelId, @NotNull String fileId);

    /**
     * 根据用户id查询
     *
     * @param userId     用户id
     * @param labelId    标签id
     * @param oldFileIds 旧文件id集合
     * @return 实体对象集合
     */
    List<UserKnowledgeLabelFileEntity> selectByUserId(@NotNull String userId, Long labelId, List<String> oldFileIds);

    /**
     * 根据用户id查询
     *
     * @param userId  用户id
     * @param fileIds 文件id集合
     * @return 实体对象集合
     */
    List<UserKnowledgeLabelFileEntity> selectByUserId(@NotNull String userId, List<String> fileIds);

}
