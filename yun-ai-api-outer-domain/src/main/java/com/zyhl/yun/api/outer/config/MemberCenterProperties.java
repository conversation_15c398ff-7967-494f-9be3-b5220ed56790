package com.zyhl.yun.api.outer.config;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 会员中心权益项配置
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "member-center")
@Data
public class MemberCenterProperties {

    /**
     * 开启状态，true-开启，false-关闭
     */
    private Boolean enabled;

    /**
     * 渠道号映射业务类型
     */
    private String benefitNo;

    /**
     * 打开会员权益
     *
     * @return
     */
    public boolean isOpen() {
        return enabled != null && enabled;
    }

    // ---------------------- 场景权益 start ---------------------- //

    /**
     * 场景权益
     */
    private List<SceneTagBenefit> sceneTagList;

    /**
     * @param sceneTag 场景
     * @return 权益编号
     */
    public SceneTagBenefit getSceneTagBenefitNo(String sceneTag) {
        if (ObjectUtil.isEmpty(sceneTagList) || CharSequenceUtil.isEmpty(sceneTag)) {
            return null;
        }
        for (SceneTagBenefit item : sceneTagList) {
            if (sceneTag.equals(item.getSceneTag())) {
                return item;
            }
        }
        return null;
    }

    /**
     * 场景权益
     */
    @Data
    public static class SceneTagBenefit {
        // 场景标识
        private String sceneTag;
        // 权益编号
        private String benefitNo;
        // 开关：true-启用，false-停用
        private boolean enabled;
    }

    // ---------------------- 场景权益 end ---------------------- //
}
