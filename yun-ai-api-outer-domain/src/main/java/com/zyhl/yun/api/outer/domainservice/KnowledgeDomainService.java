package com.zyhl.yun.api.outer.domainservice;

import com.zyhl.yun.api.outer.domain.dto.KnowledgeDialogueConfigDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;

import java.util.List;

/**
 * 知识库领域服务
 *
 * <AUTHOR>
 */
public interface KnowledgeDomainService {
    /**
     * 个人知识库可用
     *
     * @param userId 用户id
     * @return boolean
     */
    boolean personalEnable(String userId);

    /**
     * 个人知识库可用2.0
     *
     * @param userId 用户id
     * @return boolean
     */
    List<UserKnowledgeEntity> personalEnable2(String userId);

    /**
     * 公共知识库可用
     *
     * @param sourceChannel 渠道号
     * @return boolean
     */
    boolean commonEnable(String sourceChannel);

    /**
     * VIP专属智能体-公共知识库可用
     *
     * @return boolean
     */
    boolean vipCommonEnable();

    /**
     * 获取用户知识库对话配置
     *
     * @param userId 用户id
     * @return 配置
     */
    KnowledgeDialogueConfigDTO getUserDialogueConfig(String userId);

}
