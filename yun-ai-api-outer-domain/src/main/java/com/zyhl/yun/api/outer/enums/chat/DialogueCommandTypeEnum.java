package com.zyhl.yun.api.outer.enums.chat;

import lombok.Getter;

/**
 * 对话指令类型
 *
 * <AUTHOR>
 */
@Getter
public enum DialogueCommandTypeEnum {

    /**
     * 普通命令
     */
    ORDINARY(1, "普通命令"),

    /**
     * 自动命令
     */
    AUTO(2, "自动命令"),

    ;


    public static DialogueCommandTypeEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }

        return type == 1 ? ORDINARY : type == 2 ? AUTO : null;
    }

    /**
     * 类型是否存在
     *
     * @param type 类型
     * @return true-存在
     */
    public static boolean isExist(Integer type) {
        return getByType(type) != null;
    }

    /**
     * 是普通类型
     *
     * @param type 类型
     * @return true-是普通类型
     */
    public static boolean isOrdinary(Integer type) {
        return ORDINARY.type.equals(type);
    }

    /**
     * 是自动类型
     *
     * @param type 类型
     * @return true-是自动类型
     */
    public static boolean isAuto(Integer type) {
        return AUTO.type.equals(type);
    }



    DialogueCommandTypeEnum(Integer type, String remark) {
        this.type = type;
        this.remark = remark;
    }

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 备注
     */
    private final String remark;

}
