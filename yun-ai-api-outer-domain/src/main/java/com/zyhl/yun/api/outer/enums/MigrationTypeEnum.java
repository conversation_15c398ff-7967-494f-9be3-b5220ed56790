package com.zyhl.yun.api.outer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 旧用户数据迁移类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MigrationTypeEnum {

    /**
     * 图片迁移
     */
    IMAGE(1, "报名图片迁移"),

    /**
     * 文档迁移
     */
    DOC(2, "报名文档迁移"),

    ;

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 备注
     */
    private final String remark;


    /**
     * 判断是否存在
     *
     * @param type 类型
     * @return true-存在
     */
    public static boolean isExist(Integer type) {
        if (type == null) {
            return false;
        }
        for (MigrationTypeEnum value : MigrationTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return true;
            }
        }
        return false;
    }

    public static boolean isImage(Integer type) {
        return IMAGE.getType().equals(type);
    }

    public static boolean isDoc(Integer type) {
        return DOC.getType().equals(type);
    }
}
