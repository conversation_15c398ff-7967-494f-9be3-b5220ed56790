package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识库文件处理状态
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FileProcessStatusEnum {

    /**
     * 未处理
     */
    UNPROCESSED(0, "未处理"),

    /**
     * 成功
     */
    SUCCESS(1, "成功"),

    /**
     * 失败
     */
    FAIL(2, "失败"),
    ;

    /**
     * 状态
     */
    private final Integer status;
    /**
     * 描述
     */
    private final String remark;



    public static boolean isSuccess(Integer status) {
        return SUCCESS.status.equals(status);
    }

    public static boolean isFail(Integer status) {
        return FAIL.status.equals(status);
    }

    public static boolean isUnProcessed(Integer status) {
        return UNPROCESSED.status.equals(status);
    }

}
