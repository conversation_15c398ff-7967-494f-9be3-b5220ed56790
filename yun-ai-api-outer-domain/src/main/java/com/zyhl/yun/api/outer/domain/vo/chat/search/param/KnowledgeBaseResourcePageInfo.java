package com.zyhl.yun.api.outer.domain.vo.chat.search.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;

/**
 * 对话信息-搜索参数-知识库资源搜索分页信息
 * @Author: WeiJingKun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KnowledgeBaseResourcePageInfo implements Serializable {

    private static final long serialVersionUID = 1059724201018818044L;

    /** 每页显示数据记录数，默认10条记录，最大100 */
    @Range(min = 10,max = 100,message = "分页数量最小10，最大100")
    private Integer pageSize;

    /** 分页标识,用户入参获取下一页的数据 */
    private List<Object> pageAfter;

    /** 不传默认按照更新时间倒序 */
    private List<KnowledgeBaseResourceSortRange> sortInfos;

    /** 是否返回总数(0不返回，1返回) */
    @Builder.Default
    private Integer needTotalCount = 1;

}
