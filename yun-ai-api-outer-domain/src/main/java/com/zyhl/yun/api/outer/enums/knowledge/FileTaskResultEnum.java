package com.zyhl.yun.api.outer.enums.knowledge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文件上传任务结果状态，接口的返回值
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FileTaskResultEnum {

    /**
     * 失败
     */
    FAIL(-1, "失败"),

    /**
     * 处理中
     */
    PROCESSING(0, "处理中"),

    /**
     *  部分成功
     */
    PARTIAL_SUCCESS(1, "部分成功"),

    /**
     * 成功
     */
    SUCCEED(2, "成功"),

    ;

    /**
     * 状态
     */
    private final Integer status;
    /**
     * 描述
     */
    private final String remark;



    public static FileTaskResultEnum getByStatus(Integer status) {
        for (FileTaskResultEnum value : FileTaskResultEnum.values()) {
            if (value.getStatus().equals(status)) {
                return value;
            }
        }
        return null;
    }

}
