package com.zyhl.yun.api.outer.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 内容推荐列表VO
 *
 * <AUTHOR>
 * @date 2024/6/2 23:03
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContentRecommendVO {

    /**
     * 推荐类型
     * 1--工具类运营推荐
     * 2--发现类运营推荐
     * 3--推荐圈子类运营推荐
     * 4--活动类运营推荐
     * 5--通用类运营推荐
     * 6--其他运营推荐
     */
    private Integer type;

    /**
     * 内容推荐关键字编码列表
     * AI助手推荐功能名称&编码对照表：https://evgnmv1g8h.feishu.cn/sheets/ROc4sM3bfh3dnQtDxrocVuqSnid
     * 密码：792534R#
     */
    private List<String> contentList;

}