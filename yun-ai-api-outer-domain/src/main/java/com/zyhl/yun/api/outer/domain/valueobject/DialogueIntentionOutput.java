package com.zyhl.yun.api.outer.domain.valueobject;

import java.util.List;
import java.util.Map;

import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 描述：对话意图参数
 *
 * <AUTHOR> zhumaoxian  2025/4/12 9:43
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DialogueIntentionOutput {
	
	
    public DialogueIntentionOutput(String command, String subCommand) {
    	this.command = command;
    	this.subCommand = subCommand;
    }

    public DialogueIntentionOutput(DialogueIntentionVO.IntentionInfo intentionInfo) {
    	if(null != intentionInfo) {
    		this.command = intentionInfo.getIntention();
    		this.subCommand = intentionInfo.getSubIntention();
    		this.argumentMap = intentionInfo.getArgumentMap();
    	}
	}
	/**
     * 意图指令，枚举值参考IntentionCommands
     * @see com.zyhl.yun.api.outer.enums.DialogueIntentionEnum
     */
    private String command;
    /**
     * 子意图
     */
    private String subCommand;
    /**
     * 实体信息
     * AI编程文件名key=aiCoderFileName
     */
    private Map<String, List<String>> argumentMap;
}
