package com.zyhl.yun.api.outer.enums.chat;

import java.util.EnumSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * AI会话内容-输出内容审批结果状态
 * @Author: WeiJingKun
 */
public enum OutAuditStatusEnum {

    /**
     * 审核失败
     */
    FAIL(-1, "失败"),

    /**
     * 审核成功
     */
    SUCCESS(2, "成功"),

    ;

    private static final Map<Integer, OutAuditStatusEnum> MAP = new ConcurrentHashMap<>();

    static {
        EnumSet.allOf(OutAuditStatusEnum.class).forEach(item -> MAP.put(item.code, item));
    }

    public static OutAuditStatusEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return MAP.get(code);
    }

    /**
     * 是否存在
     * @param code 编码
     * @return true-存在
     */
    public static boolean isExist(Integer code) {
        return getByCode(code) != null;
    }

    OutAuditStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 编码
     */
    private Integer code;

    /**
     * 描述
     */
    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    
}
