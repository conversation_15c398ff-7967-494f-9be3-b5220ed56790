package com.zyhl.yun.api.outer.external;

import com.zyhl.hcy.yun.ai.common.base.enums.AIModuleEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.AICatalogVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.AIFileVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.BatchFileVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.DriveVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.FileUploadVO;

import java.util.List;

/**
 * 云盘接口
 *
 * <AUTHOR>
 * @date 2024-04-10
 */
public interface YunDiskExternalService {

    /**
     * 当前用户创建云盘目录
     *
     * @param module 模块
     * @return 文件夹
     */
    AICatalogVO createDir(Integer module);

    /**
     * 为用户创建云盘目录
     *
     * @param userId          用户id
     * @param phone           手机号码
     * @param belongsPlatform 所属平台
     * @param module          模块
     * @return 文件夹
     */
    AICatalogVO createDir(String userId, String phone, Integer belongsPlatform, Integer module);

    /**
     * 上传云盘 支持多级目录上传
     *
     * @param path            上传路径
     * @param module          模块
     * @param userId          用户id
     * @param belongsPlatform 底座
     * @param fileSuffix      后缀
     * @param base64          文件base64
     * @return 返回文件ids
     */
    FileUploadVO uploadFilePath(String path, AIModuleEnum module, String userId, Integer belongsPlatform, String fileSuffix, String base64);

    FileUploadVO uploadFileToCustomPath(String path, AIModuleEnum module, String userId, Integer belongsPlatform, String fileSuffix, String base64, String fileName);

	/**
	 * 根据url地址获取base64
	 *
	 * @param url 文件地址
	 * @return base64
	 */
    String urlToBase64(String url);

	/**
	 * 获取文件信息
	 *
	 * @param userId 用户id
	 * @param fileId 文件id
	 * @return 文件信息
	 */
    AIFileVO getFileInfo(String userId, String fileId);

	/**
	 * 文件批量获取
	 *
	 * @param userId          用户id
	 * @param belongsPlatform 归属平台
	 * @param fileIds         文件id列表
	 * @return 批量文件信息
	 */
	BatchFileVO fileBatchGetByAllPlatform(String userId, Integer belongsPlatform, List<String> fileIds);

	/**
	 * 文件批量获取
	 *
	 * @param userId          用户id
	 * @param belongsPlatform 归属平台
	 * @param fileIds         文件id列表
	 * @return 批量文件信息
	 */
    BatchFileVO fileBatchGetByAllPlatform(String userId, Integer belongsPlatform, List<String> fileIds, List<String> imageThumbnailStyleList);

	/**
	 * 获取空间信息
	 *
	 * @param userId          用户id
	 * @param belongsPlatform 归属平台
	 * @return 驱动信息
	 */
    DriveVO getDrive(String userId, Integer belongsPlatform);
}
