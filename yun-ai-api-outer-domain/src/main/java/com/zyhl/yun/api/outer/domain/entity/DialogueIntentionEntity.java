package com.zyhl.yun.api.outer.domain.entity;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.domain.entity.DialogueIntentionEntity} <br>
 * <b> description:</b>
 * 对话意图请求参数实体
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-03-05 14:26
 **/
@Data
public class DialogueIntentionEntity implements Serializable {

    private static final long serialVersionUID = -3134345188965307310L;

    /**
     * 请求ID
     */
    @NotBlank(message = "请求ID不能为空")
    private String requestId;

    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 会话ID
     */
    @NotBlank(message = "会话ID不能为空")
    private String sessionId;

    /**
     * 对话ID
     */
    @NotBlank(message = "对话ID不能为空")
    private String dialogueId;

    /**
     * 本次对话内容
     */
    @Valid
    @NotNull(message = "本次对话内容不能为空")
    private DialogueInfo currentDialogue;

    /**
     * 历史对话内容
     */
    private List<DialogueInfo> historyDialogueList;

    /**
     * 语义分析跳过标识
     * 1--当意图结果为012搜图片时，跳过语义分析算法，直接返回意图结果；
     */
    private Integer skipStatus = 0;

    @Data
    public static class DialogueInfo {

        /**
         * 对话内容
         */
        @NotBlank(message = "对话内容不能为空")
        private String dialogue;

        /**
         * prompt
         */
        private String prompt;

        /**
         * 时间戳
         */
        @NotBlank(message = "时间戳不能为空")
        private String timestamp;

        /**
         * 本次对话内容的意图结果，历史对话内容填入
         */
        private String intention;

        public DialogueInfo() {
        }

        public DialogueInfo(String dialogue, String timestamp) {
            this.dialogue = dialogue;
            this.timestamp = timestamp;
        }
    }
}
