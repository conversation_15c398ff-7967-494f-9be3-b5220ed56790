package com.zyhl.yun.api.outer.domain.req;


import com.zyhl.yun.api.outer.domain.entity.FileEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 搜索分页参数
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchPageEntity {

    /**
     * 文件信息
     */
    private List<FileEntity> fileInfo;

    /**
     *  下一页起始资源标识符，最后一页该值为空
     */
    private String nextPageCursor;


}
