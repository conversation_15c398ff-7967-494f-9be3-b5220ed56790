package com.zyhl.yun.api.outer.domainservice;

import com.zyhl.yun.api.outer.domain.req.SearchEntity;
import com.zyhl.yun.api.outer.domain.req.SearchPageEntity;
import com.zyhl.yun.api.outer.domain.resp.IntelligentSearchRespEntity;

/**
 * 搜索页
 *
 * <AUTHOR>
 */
public interface SearchPageService {

    /**
     * 搜索页
     *
     * @param data   智能搜索返回数据
     * @param entity 搜索页参数
     * @return 搜索页结果
     */
    SearchPageEntity searchList(IntelligentSearchRespEntity data, SearchEntity entity);

    /**
     * 搜索页
     *
     * @param entity 搜索页参数
     * @return 搜索页结果
     */
    SearchPageEntity searchList(SearchEntity entity);
}
