package com.zyhl.yun.api.outer.domainservice;

import com.zyhl.hcy.yun.ai.common.platform.third.client.check.dto.CheckTextReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.vo.CheckResultVO;
import com.zyhl.yun.api.outer.config.CheckSystemConfig.CheckSystemTextModel;
import com.zyhl.yun.api.outer.enums.CheckTypeEnum;

/**
 * 送审服务接口
 *
 * <AUTHOR>
 * @date 2025/2/13 18:00
 */
public interface CheckSystemDomainService {

    /**
     * 大模型文本送审（支持本地送审和送审平台送审）
     *
     * @param checkSystemTextModel 送审配置
     * @param checkType            送审类型
     * @param checkTextReqDTO      送审对象
     * @return 送审结果
     */
    CheckResultVO checkSystemCheckText(CheckSystemTextModel checkSystemTextModel, CheckTypeEnum checkType, CheckTextReqDTO checkTextReqDTO);

    /**
     * 文本送审，先本地送审再平台送审
     *
     * @param dialogueId 对话id
     * @param userId     用户id
     * @param text       文本
     * @return 送审结果
     */
    CheckResultVO checkLocalAndPlatform(Long dialogueId, String userId, String text);

    /**
     * 文本送审，先本地送审再平台送审，送审失败时抛出异常
     *
     * @param dialogueId 对话id
     * @param userId     用户id
     * @param text       文本
     * @return 送审结果
     */
    CheckResultVO checkLocalAndPlatformException(Long dialogueId, String userId, String text);

    /**
     * 文本送审，先平台送审,送审平台异常，则本地送审
     *
     * @param dialogueId 对话id
     * @param userId     用户id
     * @param text       文本
     * @return 送审结果
     */
    CheckResultVO checkPlatformAndLocal(Long dialogueId, String userId, String text);
}
