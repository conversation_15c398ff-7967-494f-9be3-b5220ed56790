package com.zyhl.yun.api.outer.domain.vo.chat.search.param;

import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import lombok.*;

import java.io.Serializable;

/**
 * 对话信息-搜索参数-笔记内容
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchNoteContentParam extends SearchCommonParam implements Serializable {

    /**
     * 笔记ID
     */
    private String noteId;

    /** 分页信息 */
    @Builder.Default
    private PageInfoDTO pageInfo = new PageInfoDTO("", 10, 1);

    /**
     * 搜索内容
     */
    private String query;
}
