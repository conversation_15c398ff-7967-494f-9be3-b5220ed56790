package com.zyhl.yun.api.outer.repository;


import com.github.pagehelper.PageInfo;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeInviteEntity;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 知识库邀请表数据库操作类
 *
 * <AUTHOR>
 */
public interface UserKnowledgeInviteRepository {

	/**
	 * 根据userId获取知识邀请列表
	 *
	 * @param knowledgeId 知识库id
	 * @param userId      用户id
	 * @return 知识库邀请列表
	 */
    List<UserKnowledgeInviteEntity> getListByUserId(List<Long> knowledgeId, String userId);

    /**
     * 根据inviteUserId获取知识邀请列表
     *
     * @param knowledgeId  知识库id
     * @param inviteUserId 邀请人id
     * @param pageInfo     分页信息
     * @return
     */
    PageInfo<UserKnowledgeInviteEntity> pageByInviteUserId(List<Long> knowledgeId, String inviteUserId, PageInfoDTO pageInfo);


    /**
     * 根据userId删除知识邀请列表（逻辑删除）
     *
     * @param knowledgeId 知识库id
     * @param userId      加入人Id
     */
    boolean deleteByUserId(List<Long> knowledgeId, String userId);

    /**
     * 新增
     *
     * @param entity 参数
     * @return int
     */
    int save(@NotNull UserKnowledgeInviteEntity entity);

    /**
     * 根据知识库id和userId获取知识库记录
     *
     * @param knowledgeId 知识库id
     * @return 返回
     */
    List<UserKnowledgeInviteEntity> get(Long knowledgeId, String userId);

    List<UserKnowledgeInviteEntity> getKnowledgeListByUserId(String userId);

    /**
     * 根据知识库id全量获取分享用户
     *
     * @param knowledgeId 知识库id
     * @param condition   执行条件
     * @param isAsc       是否是 ASC 排序
     * @param columns     字段数组
     * @return
     */
    List<UserKnowledgeInviteEntity> getAllListByKnowledgeId(Long knowledgeId, boolean condition, boolean isAsc, String... columns);

    /**
     * 更新知识选中状态
     *
     * @param userId      用户id
     * @param baseIdList 知识库id集合
     */
    void updateSelected(String userId, List<String> baseIdList);

    /**
     * 查询总数
     *
     * @param knowledgeId 知识库id
     * @param userId 用户id
     * @return 数量
     */
    int count(Long knowledgeId, String userId);

    /**
     * 查询总数
     *
     * @param knowledgeId 知识库id
     * @return 数量
     */
    int countByKnowledgeId(Long knowledgeId);

    /**
     * 更新状态
     *
     * @param knowledgeId 知识库id
     */
    void updateStatus(Long knowledgeId);

}
