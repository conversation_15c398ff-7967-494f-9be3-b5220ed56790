package com.zyhl.yun.api.outer.domain.dto;

import com.zyhl.hcy.yun.ai.common.base.enums.ParseTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 个人知识库文件添加结果
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class KnowledgeEsQueryResultDTO {

    /**
     * 知识库编码_业务编码_文件ID_切片序号（唯一）
     */
    private String id;

    /**
     * 输入内容
     */
    private String text;

    /**
     * 问题
     */
    private String question;

    /**
     * 文件名称
     */
    private String fileId;

    /**
     * 解析类型
     * SPLIT 文档切片 QA 问答对解析 GQA 假设性问题 GSPLIT 语义切块 SUMMARY 文档总结
     * @see ParseTypeEnum
     */
    private String parseType;

    /**
     * 召回次数
     */
    private Integer recallCount;

    /**
     * 知识库类型
     *
     * @see com.zyhl.yun.api.outer.enums.knowledge.KnowledgeBaseEnum
     */
    private String knowledgeBase;

    public KnowledgeEsQueryResultDTO setKnowledgeBase(String knowledgeBase) {
        this.knowledgeBase = knowledgeBase;
        return this;
    }
}