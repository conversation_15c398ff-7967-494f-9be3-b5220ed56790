package com.zyhl.yun.api.outer.enums;

/**
 * 接受语言
 * <AUTHOR>
 * @date 2025/2/11 10:36
 */
public enum AcceptLanguageEnum {

    /**
     * 简体中文：zh-CN（默认）
     * 英文：en-US
     * 繁体中文：zh-HK
     */
    ZH_CN("zh-CN", "简体中文"),
    EN_US("en-US", "英文"),
    ZH_HK("zh-HK", "繁体中文"),
    ;

    AcceptLanguageEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
