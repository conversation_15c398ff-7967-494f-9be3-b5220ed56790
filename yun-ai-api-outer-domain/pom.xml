<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.zyhl.hcy</groupId>
    <artifactId>yun-ai-api-outer</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <modelVersion>4.0.0</modelVersion>
  <groupId>com.zyhl.hcy.yun-ai-api-outer</groupId>
  <artifactId>yun-ai-api-outer-domain</artifactId>
  <name>yun-ai-api-outer-domain</name>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
    </dependency>

    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct-processor</artifactId>
    </dependency>

    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct-jdk8</artifactId>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>

    <!-- PageHelper -->
    <dependency>
      <groupId>com.github.pagehelper</groupId>
      <artifactId>pagehelper-spring-boot-starter</artifactId>
    </dependency>

    <!-- Validation -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>

    <dependency>
      <groupId>com.zyhl.hcy</groupId>
      <artifactId>hcy-platform-commons</artifactId>
    </dependency>

    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
    </dependency>

    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
      <version>5.8.25</version>
    </dependency>

    <dependency>
      <groupId>com.zyhl.hcy</groupId>
      <artifactId>hcy-plugins-starter-uidgenerator</artifactId>
    </dependency>

    <dependency>
      <groupId>com.zyhl.hcy</groupId>
      <artifactId>hcy-plugins-starter-logger</artifactId>
    </dependency>

    <dependency>
      <groupId>com.zyhl.hcy</groupId>
      <artifactId>yun-ai-common-base</artifactId>
    </dependency>

    <dependency>
      <groupId>com.zyhl.hcy</groupId>
      <artifactId>yun-ai-common-model-api</artifactId>
    </dependency>

    <dependency>
      <groupId>com.zyhl.hcy</groupId>
      <artifactId>yun-ai-common-platform-third</artifactId>
    </dependency>
    <dependency>
      <groupId>com.zyhl.hcy</groupId>
      <artifactId>yun-ai-common-intention</artifactId>
      <version>${yun-ai-common.version}</version>
    </dependency>

    <dependency>
      <groupId>com.zyhl.hcy</groupId>
      <artifactId>yun-ai-common-rag</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-context</artifactId>
    </dependency>

  </dependencies>
</project>
