#修改基础镜像
FROM b66ypcgaicisp01-f8a759e8.ecis.guangzhou-2.cmecloud.cn/prod/openjdk8-arthas-skywalking:1.0.0
#镜像端口
EXPOSE 19027
#环境变量
ENV TZ=Asia/Shanghai

ENV skywalkingService=b66-ypcg-ai-sw-p01.skywalking-b66-ai-p1-e2fpohrir8.svc.cluster.local:11800

ENV applicationName=yun-ai-api-outer
ENV jvm_heap_size="-Xmx8g -Xms8g -Xss256K"
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

#将编译后的jar放置到镜像根目录
COPY yun-ai-api-outer-web/target/yun-ai-api-outer.jar /
CMD timestamp=$(date +%Y%m%d%H%M%S) \
&& mkdir -p logs/${applicationName}/$HOSTNAME/gc \
&& java \
${jvm_heap_size} \

#skywalking-agent
-javaagent:/skywalking/agent/skywalking-agent.jar \
-Dskywalking.agent.service_name=${applicationName} \
-Dskywalking.collector.backend_service=${skywalkingService} \

-Djava.security.egd=file:/dev/./urandom \
-XX:+PrintClassHistogram -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintHeapAtGC \
-Xloggc:logs/${applicationName}/$HOSTNAME/gc/${applicationName}-gc-${timestamp}.log \

-XX:+PrintGCDateStamps -XX:+HeapDumpOnOutOfMemoryError  \
-XX:HeapDumpPath=/logs/${applicationName}/$HOSTNAME/heap.hprof \
-XX:+UseConcMarkSweepGC  \
-XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m \
-XX:MaxDirectMemorySize=2g \
-XX:SurvivorRatio=6 \
-XX:+UseConcMarkSweepGC  \

-jar /yun-ai-api-outer.jar
