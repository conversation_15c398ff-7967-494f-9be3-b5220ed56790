FROM 10.19.19.84/k8s/openjdk8-arthas-skywalking-jacoco:1.0.0

#请修改为应用端口
EXPOSE 19027 19982

ENV TZ=Asia/Shanghai

#请修改应用名，需要区分环境，研发（联调）环境需要加上dev后缀，测试环境需要加上test后缀
ENV applicationName=yun-ai-api-outer-web-test
ENV jvm_heap_size="-Xmx8g -Xms8g -Xss256K"
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

#请将编译后的jar放置到镜像根目录，建议先本地编译确认具体目录
#编译后jar目录类似yun-neauth-config-web/target/yun-neauth-config-web-1.0-SNAPSHOT.jar，版本可用*代替
#放到/目录可以改jar包名，把版本去掉
ADD yun-ai-api-outer-web/target/yun-ai-api-outer.jar /yun-ai-api-outer.jar

CMD timestamp=$(date +%Y%m%d%H%M%S) \
&& mkdir -p logs/${applicationName}/$HOSTNAME/gc \
&& java \
${jvm_heap_size} \

#skywalking-agent
-javaagent:/skywalking/agent/skywalking-agent.jar \
-Dskywalking.agent.service_name=${applicationName} \
-Dskywalking.collector.backend_service=a50-skywalking-t01.skywalking-a50-ypcg-t01-nacos-01:11800 \

-javaagent:/jacoco/jacocoagent.jar=includes=*,output=tcpserver,port=19982,address=0.0.0.0,append=true\

-Djava.security.egd=file:/dev/./urandom \
-XX:+PrintClassHistogram -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintHeapAtGC \
-Xloggc:logs/${applicationName}/$HOSTNAME/gc/${applicationName}-gc-${timestamp}.log \

#新增加修改 JVM 的 DNS 缓存时间
-Dnetworkaddress.cache.ttl=60  \
-Dnetworkaddress.cache.negative.ttl=10  \

#请修改jar包名

-jar /yun-ai-api-outer.jar