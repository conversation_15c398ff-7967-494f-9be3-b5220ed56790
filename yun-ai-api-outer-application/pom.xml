<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.zyhl.hcy</groupId>
        <artifactId>yun-ai-api-outer</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.zyhl.hcy.yun-ai-api-outer</groupId>
    <artifactId>yun-ai-api-outer-application</artifactId>

    <name>yun-ai-api-outer-application</name>


    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.zyhl.hcy.yun-ai-api-outer</groupId>
            <artifactId>yun-ai-api-outer-domain</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zyhl.hcy.yun-ai-api-outer</groupId>
            <artifactId>yun-ai-api-outer-external</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.13.1</version>
        </dependency>

        <dependency>
            <groupId>com.zyhl.hcy.yun-ai-api-outer</groupId>
            <artifactId>yun-ai-api-outer-infrastructure</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zyhl.hcy</groupId>
            <artifactId>yun-ai-common-base</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zyhl.hcy</groupId>
            <artifactId>yun-ai-common-model-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zyhl.hcy</groupId>
            <artifactId>yun-ai-common-platform-third</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>33.0.0-jre</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>net.sourceforge.htmlunit</groupId>
            <artifactId>htmlunit</artifactId>
            <version>2.70.0</version>
        </dependency>
    </dependencies>

</project>
