package com.zyhl.yun.api.outer.application.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * enumName: KnowledgeTaskQuantityTypeEnum
 * description:
 *
 * <AUTHOR>
 * @date 2025/2/24
 */
@Getter
@AllArgsConstructor
public enum KnowledgeTaskQuantityTypeEnum {

	/**
	 * 单个
	 */
    SINGLE(1, "single"),

    /**
     * 批量
     */
    BATCH(2, "batch"),

    ;

    private final Integer code;

    private final String value;
}
