package com.zyhl.yun.api.outer.application.service.task;

import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;

/**
 * interfaceName: AiInternetSearchLLMService
 * description: AI全网搜调大模型处理接口
 *
 * <AUTHOR>
 * @date 2025/3/14
 */
public interface AiInternetSearchLLMService {

    /**
     * AI全网搜——调用大模型搜索并流式返回
     *
     * @param dto 请求参数DTO
     * @param resourceContent 资源内容
     * @param llmSearchEntity 大模型搜索实体
     * @return 会话输入返回结果VO
     */
    AlgorithmChatAddVO aiInternetSearchByLLM(ChatAddInnerDTO dto, String resourceContent, Object llmSearchEntity);
}