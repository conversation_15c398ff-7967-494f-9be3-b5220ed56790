package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.application.chatv2.dto.*;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.config.MailaiSearchProperties;
import com.zyhl.yun.api.outer.constants.ChatAddParamConstants;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * 参数重置
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ParamResetHandlerImpl extends AbstractChatAddV2Handler {


    @Resource
    private MailaiSearchProperties mailaiSearchProperties;


    /**
     * 当前执行顺序
     */
    private ExecuteSort thisExecuteSort = ExecuteSort.PARAM_RESET;

    @Override
    public void afterPropertiesSet() throws Exception {
    	//支持的业务初始化
    	List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        thisBusinessTypes.add(ChatBusinessTypeEnum.CLOUD_PHONE);
        thisBusinessTypes.add(ChatBusinessTypeEnum.MESSAGE_5G);
        thisBusinessTypes.add(ChatBusinessTypeEnum.NOTE);
    	this.setBusinessTypes(thisBusinessTypes);
    }
    
    /**
     * 返回当前处理器的执行顺序
     *
     * @return 执行顺序
     */
    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    /**
     * 执行具体的逻辑处理
     *
     * @param innerDTO 处理DTO
     * @return 是否执行成功
     */
    @Override
    public boolean execute(ChatAddHandleDTO innerDTO) {
        return true;
    }

    /**
     * 主要的执行入口，负责调用意图重置、上下文重置以及扩展信息重置的逻辑，并记录日志信息
     *
     * @param handleDTO 处理DTO
     * @return 是否执行成功
     */
    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        log.info("进入{}", thisExecuteSort.getDesc());
        try {
            ChatAddReqDTO reqDto = handleDTO.getReqDTO();
            DialogueInputInfoDTO dialogueInput = (reqDto == null) ? null : reqDto.getDialogueInput();

            // 如果command不为空，里面的主意图和子意图都为空，则command重置为空
            DialogueIntentionDTO command = handleDTO.getInputInfoDTO().getCommand();
            if(ObjectUtil.isNotNull(command) && CharSequenceUtil.isAllBlank(command.getCommand(), command.getSubCommand())){
                handleDTO.getInputInfoDTO().setCommand(null);
            }

            // 意图重置处理
            intentionResetHandle(dialogueInput, reqDto);

            // extInfo重置处理
            resetExtInfoHandle(dialogueInput);

        } catch (Exception e) {
            log.error("{}异常,handleDto:{},异常信息:", thisExecuteSort.getDesc(), JsonUtil.toJson(handleDTO), e);
        }

        return true;
    }

    /**
     * 重置扩展信息处理方法
     * 该方法用于将DialogueInputInfoDTO对象中的某些字段（如工具设置和版本信息）序列化后更新到扩展信息中
     *
     * @param dialogueInput 对话输入信息的DTO对象，包含需要序列化的工具设置和版本信息
     */
    private void resetExtInfoHandle(DialogueInputInfoDTO dialogueInput) {
        // 检查输入对象是否为空，为空则直接返回，不进行任何操作
        if (dialogueInput == null) {
            return;
        }

        // 获取扩展信息映射，用于存储序列化后的工具设置和版本信息
        Map<String, Object> extInfoMap = dialogueInput.getExtInfoMap();

        // 如果工具设置不为空，则将其序列化为JSON字符串，并添加到扩展信息映射中
        if (dialogueInput.getToolSetting() != null) {
            extInfoMap.put(ChatAddParamConstants.TOOL_SETTING_KEY, JsonUtil.toJson(dialogueInput.getToolSetting()));
        }

        // 如果版本信息不为空，则将其序列化为JSON字符串，并添加到扩展信息映射中
        DialogueVersionInfoDTO versionInfo = dialogueInput.getVersionInfo();
        if (versionInfo != null) {
            extInfoMap.put(ChatAddParamConstants.H5_VERSION_KEY, versionInfo.getH5Version());
            extInfoMap.put(ChatAddParamConstants.PC_VERSION_KEY, versionInfo.getPcVersion());
        }

        // 将更新后的扩展信息映射序列化为JSON字符串，并设置回对话输入信息对象中
        dialogueInput.setExtInfo(JsonUtil.toJson(extInfoMap));
    }

    /**
     * 处理对话意图的重置逻辑。根据是否启用大模型对话或知识库检索，设置对应的命令码
     *
     * @param dialogueInput 对话输入信息DTO
     * @param reqDto        助手对话请求参数
     */
    private void intentionResetHandle(DialogueInputInfoDTO dialogueInput, ChatAddReqDTO reqDto) {
        if (dialogueInput == null) {
            return;
        }
        //走云邮AI搜索,需要拼接“搜索”两个字，让意图识别提取搜索意图
//        if (dialogueInput.isEnableAiSearch()){
//            String sourceChannel = reqDto.getSourceChannel();
//            boolean isMail = SourceChannelsProperties.isMailChannel(sourceChannel);
//            if (isMail){
//                String prefix = mailaiSearchProperties.getSplicingPrefix();
//                dialogueInput.setDialogue(prefix+dialogueInput.getDialogue());
//            }
//        }

        // 智能体对话，意图指令重置为文生文意图
		if (ApplicationTypeEnum.isIntelligen(reqDto.getApplicationType())) {
			if (null != reqDto.getDialogueInput().getCommand()
					&& StringUtils.isNotEmpty(reqDto.getDialogueInput().getCommand().getCommand())) {
				log.info("智能体入参意图，无需重置为文本意图");
			} else {
				dialogueInput.setCommandCode(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());
			}
		}

		if (!ApplicationTypeEnum.isIntelligen(reqDto.getApplicationType())) {
			// 强制使用大模型对话，默认文生文
			if (dialogueInput.isEnableForceLlm()) {
				dialogueInput.setCommandCode(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());
				return;
			}

			// 强制AI全网搜对话，默认文生文
			if (dialogueInput.isEnableAllNetworkSearch()) {
				dialogueInput.setCommandCode(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());
				return;
			}

			/**
			 * 启动知识库+联网搜索回答，即enableKnowledgeAndNetworkSearch=true
			 * 默认文生文
			 */
			if (dialogueInput.isEnableKnowledgeAndNetworkSearch()) {
				dialogueInput.setCommandCode(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());
				return;
			}

			// 无dialogue只传提示词，默认文生文
			if (StringUtils.isNotEmpty(dialogueInput.getPrompt()) && StringUtils.isEmpty(dialogueInput.getDialogue())
					&& (null == dialogueInput.getCommand()
					|| StringUtils.isEmpty(dialogueInput.getCommand().getCommand()))) {
				dialogueInput.setCommandCode(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());
			}

			// 非对话应用，默认文生文
			if(!ApplicationTypeEnum.isChat(reqDto.getApplicationType())) {
				dialogueInput.setCommandCode(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());
			}
		}
        
    }

}
