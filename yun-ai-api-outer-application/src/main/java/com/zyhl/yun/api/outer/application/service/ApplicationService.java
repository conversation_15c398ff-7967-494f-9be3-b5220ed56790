package com.zyhl.yun.api.outer.application.service;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;

/**
 * 应用业务逻辑接口
 *
 * <AUTHOR>
 */
public interface ApplicationService {

    /**
     * 智能体类型获取sessionId
     * @param dto 用户会话输入DTO
     * @return sessionId
     */
    Long intelligentGetSessionId(AlgorithmChatAddDTO dto);
    
    /**
     * 智能体类型获取sessionId
     * @param dto 用户会话输入DTO
     * @return sessionId
     */
    Long intelligentGetSessionId(ChatAddReqDTO dto);
}
