package com.zyhl.yun.api.outer.application.handle.chat.impl;

import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddContentDTO;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.handle.chat.AbstractChatAddHandler;
import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;
import com.zyhl.yun.api.outer.config.LeadCopyProperties;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddResultTypeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.VersionUtil;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.IntentEntityVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 发邮件意图的处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SendMailHandlerImpl extends AbstractChatAddHandler {

    @Resource
    private LeadCopyProperties copyProperties;

    @Override
    public int order() {
        return ExecuteSort.SEND_MAIL.getSort();
    }

    @Override
    public boolean execute(ChatAddInnerDTO innerDTO) {
        // 发邮件意图
        return DialogueIntentionEnum.SEND_MAIL.getCode().equals(innerDTO.getIntentionCode());
    }

    @Override
    public boolean run(ChatAddInnerDTO innerDTO) {
        log.info("进入发邮件意图功能处理");

        AlgorithmChatAddContentDTO content = innerDTO.getContent();
        // 渠道来源为云邮助手渠道，邮箱h5Version版本 < 2.0.15版本，更新为对话意图（000），执行下一个Handler
        if (sourceChannelsProperties.isMail(content.getSourceChannel())) {
            if (VersionUtil.mailH5VersionLt2015()) {
                log.info("进入发邮件意图功能处理,渠道来源是云邮助手，邮箱h5Version版本 < 2.0.15版本：{}，【发邮件】更新为对话意图（000），执行下一个Handler", RequestContextHolder.getH5Version());
                innerDTO.setTextGenerateTextIntention();
                return true;
            }
        }

        // 渠道来源为小天助手渠道，小天h5Version版本 < 1.7.0版本，更新为对话意图（000），执行下一个Handler
        if (sourceChannelsProperties.isXiaoTian(content.getSourceChannel())) {
            log.info("进入发邮件意图功能处理,渠道来源是小天助手，暂时屏蔽发邮件意图，【发邮件】更新为对话意图（000），执行下一个Handler");
            innerDTO.setTextGenerateTextIntention();
            return true;

            // todo 效果不好小天暂时屏蔽 20250112
            /*if (VersionUtil.xtH5VersionLt170()) {
                log.info("进入发邮件意图功能处理,渠道来源是小天助手，邮箱h5Version版本 < 1.7.0版本：{}，【发邮件】更新为对话意图（000），执行下一个Handler", RequestContextHolder.getH5Version());
                innerDTO.setTextGenerateTextIntention();
                return true;
            }*/
        }

        // 处理发邮件意图信息
        handleSendMailIntentionInfo(innerDTO);

        return false;
    }

    /**
     * 处理发邮件意图信息
     *
     * @param innerDTO 对话接口内部数据传输对象
     */
    private void handleSendMailIntentionInfo(ChatAddInnerDTO innerDTO) {
        AlgorithmChatAddVO respParams = innerDTO.getRespParams();

        // 设置邮件信息
        setMailInfo(innerDTO, respParams);

        // 设置标题
        respParams.setTitle(copyProperties.getSendMailTitle());

        // 设置引导文案对象
        LeadCopyVO copyVo = LeadCopyVO.getLeadCopyVo(copyProperties.getByInstruction(DialogueIntentionEnum.SEND_MAIL.getInstruction()), DialogueIntentionEnum.SEND_MAIL);
        respParams.setLeadCopy(copyVo);

        // 保存hbase-所有对话结果
        AiTextResultRespParameters respParameters = AiTextResultRespParameters.builder()
                .resultCode(ResultCodeEnum.SUCCESS.getResultCode())
                .resultMsg(ResultCodeEnum.SUCCESS.getResultMsg())
                .title(copyProperties.getSendMailTitle())
                .leadCopy(copyVo)
                .mailInfo(innerDTO.getRespParams().getMailInfo())
                .intentionInfoList(innerDTO.getIntentionVO() == null ? null : innerDTO.getIntentionVO().getIntentionInfoList())
                .build();
        saveHbaseAllChatResult(innerDTO, respParameters);

        // 保存数据库
        addSuccess(innerDTO, OutContentTypeEnum.TEXT);

        // 返回同步结果
        respParams.setResultType(ChatAddResultTypeEnum.SYNCHRONIZATION.getType());
    }

    /**
     * 设置邮件信息
     * @param innerDTO 对话接口内部数据传输对象
     * @param respParams 对话结果VO
     */
    private void setMailInfo(ChatAddInnerDTO innerDTO, AlgorithmChatAddVO respParams) {
        // 获取邮件信息
        List<DialogueIntentionVO.IntentionInfo> intentionInfoList = innerDTO.getIntentionVO().getIntentionInfoList();
        if (CollectionUtils.isEmpty(intentionInfoList) || CollectionUtils.isEmpty(intentionInfoList.get(0).getEntityList())) {
            return;
        }

        // 实体识别结果VO
        IntentEntityVO entityVO = mergeLists(intentionInfoList.get(0).getEntityList());

        // 封装邮件信息VO
        MailInfoVO mailInfo = MailInfoVO.builder()
                .title(CollectionUtils.isEmpty(entityVO.getTitleList()) ? "" : entityVO.getTitleList().get(0))
                .recipientList(entityVO.getRecipientList())
                .emailAddressList(entityVO.getEmailAddressList())
                .build();

        // 设置邮件信息
        respParams.setMailInfo(mailInfo);
    }

    /**
     * 合并entityList
     *
     * @param entityList 实体列表
     * @return 实体识别结果VO
     */
    public static IntentEntityVO mergeLists(List<IntentEntityVO> entityList) {
        IntentEntityVO mergedEntity = new IntentEntityVO();
        mergedEntity.setRecipientList(new ArrayList<>());
        mergedEntity.setTitleList(new ArrayList<>());
        mergedEntity.setEmailAddressList(new ArrayList<>());

        entityList.forEach(entityVO -> {
            mergedEntity.getRecipientList().addAll(entityVO.getRecipientList());
            mergedEntity.getTitleList().addAll(entityVO.getTitleList());
            mergedEntity.getEmailAddressList().addAll(entityVO.getEmailAddressList());
        });

        return mergedEntity;
    }

}
