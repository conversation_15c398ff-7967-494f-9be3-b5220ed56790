package com.zyhl.yun.api.outer.application.config;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * 应用智能体lingxi配置
 *
 * <AUTHOR>
 * @date 2025-06-18 19:52
 */
@Data
@Configuration
@ConfigurationProperties("application-agent.lingxi")
public class ApplicationAgentLingxiConfig {

	/**
	 * 演示使用配置
	 */
	private LingxiDemoConfig demoConfig;

	/**
	 * 业务配置
	 */
	private LingxiBusinessConfig businessConfig = new LingxiBusinessConfig();

	/**
	 * 文本配置
	 */
	private LingxiTextConfig textConfig = new LingxiTextConfig();

	/**
	 * 演示使用配置类
	 */
	@Data
	public static class LingxiDemoConfig {

		/**
		 * 是否demo，demo则设置true，默认false
		 */
		private Boolean demo = false;

		/**
		 * 演示的Basic token
		 */
		private String basicToken;

	}

	/**
	 * 业务配置类
	 */
	@Data
	public static class LingxiBusinessConfig {

		/**
		 * 获取意图列表内容数量，默认100条
		 */
		private Integer intentionQuerySize = 100;
		/**
		 * 获取笔记列表数量，默认50条
		 */
		private Integer notePageSize = 50;
		/**
		 * 查找类型，默认4=录音笔记
		 */
		private Integer noteType = 4;
		/**
		 * 默认查找最近一条，默认false
		 */
		private Boolean defaultRecentOne = false;

		/**
		 * 渠道
		 */
		private String channel;
		/**
		 * 应用id
		 */
		private String applicationId;
		/**
		 * 邮箱平台logo
		 */
		private String mailLogo;
		/**
		 * 邮件编辑跳转地址
		 */
		private String mailEditJumpUrl;
		/**
		 * 邮件已发送跳转地址
		 */
		private String mailSendJumpUrl;
		/**
		 * 笔记录音跳转地址
		 */
		private String noteVoiceJumpUrl;
		/**
		 * AI助手跳转地址
		 */
		private String aiHelperJumpUrl;

	}

	/**
	 * 文本配置类
	 */
	@Data
	public static class LingxiTextConfig {
		/**
		 * 追尾公共desc
		 */
		String bubbleDesc = "猜您还想问：";

		/**
		 * 会议通知编辑邮件追尾标题
		 */
		String meetingEditMailTitle = "编辑邮件";
		/**
		 * 会议通知编辑邮件追尾回复内容
		 */
		String meetingEditMailReplyText = "我要编辑会议邮件，会议内容是%s";
		/**
		 * 会议通知发送邮件追尾标题
		 */
		String meetingSendMailTitle = "已确认邮件内容，发送邮件";
		/**
		 * 会议通知发送邮件追尾回复内容
		 */
		String meetingSendMailReplyText = "我已确认邮件内容，帮我发送会议邮件，会议内容是%s";

		/**
		 * ppt方案会议通知编辑邮件追尾标题
		 */
		String pptEditMailTitle = "编辑邮件";
		/**
		 * ppt方案会议通知编辑邮件追尾回复内容
		 */
		String pptEditMailReplyText = "我要编辑PPT方案会议邮件，会议内容是%s";
		/**
		 * ppt方案会议通知发送邮件追尾标题
		 */
		String pptSendMailTitle = "已确认邮件内容，发送邮件";
		/**
		 * ppt方案会议通知发送邮件追尾回复内容
		 */
		String pptSendMailReplyText = "我已确认邮件内容，帮我发送PPT方案会议邮件，会议内容是%s";

		/**
		 * 开始ppt生成-卡片颜色
		 */
		String startPptColor = "";
		/**
		 * 开始ppt生成-卡片背景
		 */
		String startPptImage = "";
		/**
		 * 开始ppt生成标题
		 */
		String startPptTitle = "PPT生成";
		/**
		 * 开始ppt生成子标题
		 */
		String startPptSubtitle = "正在飞速完成创作";
		/**
		 * 开始ppt生成按钮名称
		 */
		String startPptButtonName = "查看PPT";
		/**
		 * 开始ppt生成按钮颜色
		 */
		String startPptButtonColor = "";
		/**
		 * 开始ppt生成按钮图片
		 */
		String startPptButtonImage = "";
		/**
		 * 开始ppt生成-跳转浏览方式
		 */
		String startPptBrowser = "internal://open?url=";
		/**
		 * 开始ppt生成-自动拉端，200毫秒
		 */
		String startPptButtonAutoOpenTs = "200";

		/**
		 * 开始ppt生成-ppt成功后追尾标题
		 */
		String pptSuccTitle = "发送邮件通知";
		/**
		 * 开始ppt生成-ppt成功后追尾回复内容
		 */
		String pptSuccReplyText = "帮我将会议方案PPT作为附件，发送邮件通知，会议内容是%s";

		/**
		 * 编辑邮件文本大模型输出文本
		 */
		String editMailModelText = "点击前往编辑邮件";
		/**
		 * 编辑邮件-卡片颜色
		 */
		String editMailColor = "";
		/**
		 * 编辑邮件-卡片背景
		 */
		String editMailImage = "";
		/**
		 * 编辑邮件标题
		 */
		String editMailTitle = "139邮件";
		/**
		 * 编辑邮件子标题
		 */
		String editMailSubtitle = "点击前往编辑邮件";
		/**
		 * 编辑邮件按钮名称
		 */
		String editMailButtonName = "编辑邮件";
		/**
		 * 编辑邮件按钮颜色
		 */
		String editMailButtonColor = "";
		/**
		 * 编辑邮件按钮背景
		 */
		String editMailButtonImage = "";
		/**
		 * 查看邮件-卡片颜色
		 */
		String detailMailColor = "";
		/**
		 * 查看邮件-卡片背景
		 */
		String detailMailImage = "";
		/**
		 * 查看邮件标题
		 */
		String detailMailTitle = "139邮件";
		/**
		 * 查看邮件子标题
		 */
		String detailMailSubtitle = "点击前往查看邮件";
		/**
		 * 查看邮件按钮名称
		 */
		String detailMailButtonName = "查看详情";
		/**
		 * 查看邮件按钮颜色
		 */
		String detailMailButtonColor = "";
		/**
		 * 查看邮件按钮背景
		 */
		String detailMailButtonImage = "";

		/**
		 * 发送邮件成功
		 */
		String sendMailSuccModelText = "邮件已成功发送！";

		/**
		 * 录音笔记标题
		 */
		String voiceNoteTitle = "会议要开始了？试试会议录音";
		/**
		 * 录音笔记追尾回复内容
		 */
		String voiceNoteReplyText = "我的会议即将开始，帮我录音记录会议内容";

		/**
		 * 录音笔记卡片大模型文本
		 */
		String voiceNoteCardModelText = "为您提供移动云盘录音笔记功能：";
		/**
		 * 录音笔记卡片颜色
		 */
		String voiceNoteCardColor = "";
		/**
		 * 录音笔记卡片背景
		 */
		String voiceNoteCardImage = "";
		/**
		 * 录音笔记卡片标题
		 */
		String voiceNoteCardTitle = "会议录音";
		/**
		 * 录音笔记卡片子标题
		 */
		String voiceNoteCardSubtitle = "AI实时录音记录会议";
		/**
		 * 录音笔记卡片按钮名称
		 */
		String voiceNoteCardButtonName = "前往录音";
		/**
		 * 录音笔记卡片按钮颜色
		 */
		String voiceNoteCardButtonColor = "";
		/**
		 * 录音笔记卡片按钮背景
		 */
		String voiceNoteCardButtonImage = "";

		/**
		 * 生成ppt追尾标题
		 */
		String genPptTitle = "会议已结束？根据录音笔记生成PPT";
		
		/**
		 * 生成ppt追尾回复内容
		 */
		String genPptReplyText = "会议已结束，帮我根据录音笔记内容，一键智能生成PPT";
		
		/**
		 * 生成ppt追尾回复内容，附加会议内容
		 */
		String genPptReplyTextWithMeeting = "会议已结束，帮我根据录音笔记内容，一键智能生成PPT，会议内容是%s";

		/**
		 * 选择笔记追尾标题
		 */
		String selectNoteGenPptTitle = "选择已有笔记生成PPT";
		/**
		 * 选择笔记追尾颜色
		 */
		String selectNoteGenPptColor = "";
		/**
		 * 选择笔记追尾背景
		 */
		String selectNoteGenPptImage = "";
		
		
		String emptyNoteModelText = "暂无录音笔记内容";
		
		String notFoundNoteModelText = "会议对应的录音笔记不存在";
		
		/**
		 * 过滤词
		 */
		List<String> filterChars = null;

	}
	
	/**
	 * 移除过滤一些词
	 * 
	 * @param text 输入
	 * @return 输出
	 */
	public String removeSomeFilterChar(String text) {
		if (null == text) {
			return StringUtils.EMPTY;
		}

		List<String> filterChars = this.getTextConfig().getFilterChars();
		if (null == filterChars) {
			filterChars = new ArrayList<>();
			filterChars.add("的");
		}
		for (String filterChar : filterChars) {
			text = text.replace(filterChar, StringUtils.EMPTY);
		}
		return text;
	}
}
