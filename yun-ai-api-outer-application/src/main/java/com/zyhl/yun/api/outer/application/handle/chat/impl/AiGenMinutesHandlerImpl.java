package com.zyhl.yun.api.outer.application.handle.chat.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddContentDTO;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.handle.chat.AbstractChatAddHandler;
import com.zyhl.yun.api.outer.config.textmodel.ModelPromptProperties;
import com.zyhl.yun.api.outer.constants.CommonConstant;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.VersionUtil;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * AI会议纪要
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AiGenMinutesHandlerImpl extends AbstractChatAddHandler {

    @Resource
    private TextSseHandlerImpl textSseHandler;

    @Resource
    private ChatConfigServiceDomainService chatConfigServiceDomainService;

    @Resource
    private ModelPromptProperties modelPromptProperties;

    @Override
    public int order() {
        return ExecuteSort.AI_GEN_MINUTES.getSort();
    }

    @Override
    public boolean execute(ChatAddInnerDTO innerDTO) {
    	//1.0接口弃用，改到2.0接口
    	return false;
    	/*
        AlgorithmChatAddContentDTO content = innerDTO.getContent();
        AssistantEnum assistantEnum = content.getAssistantEnum();
        // 获取意图码，如果为空则使用commands
        String intentionCode = ObjectUtil.isEmpty(innerDTO.getIntentionCode())
                ? content.getCommands()
                : innerDTO.getIntentionCode();
        // 判断是否为"037"意图码
        boolean isAiCodeIntention = DialogueIntentionEnum.AI_CODE.getCode().equals(intentionCode);
        // 检查toolCallsList第一个元素是否为"AI编程"
        boolean hasAiProgrammingToolCall = checkmeetingMinutesToolCall(innerDTO);
        // 检查是否符合AI编程助手条件: 意图码为"036"且toolCallsList第一个元素为"AI生成会议纪要"
        if (AssistantEnum.XIAO_TIAN.equals(assistantEnum)) {
            return VersionUtil.xtH5VersionGte200(RequestContextHolder.getH5Version())
                    && isAiCodeIntention
                    && hasAiProgrammingToolCall;
        }
        return false;
        */
    }


    private boolean checkmeetingMinutesToolCall(ChatAddInnerDTO innerDTO) {

        DialogueIntentionVO intentionVO = innerDTO.getIntentionVO();
        if (intentionVO == null || intentionVO.getIntentionInfoList() == null || intentionVO.getIntentionInfoList().isEmpty()) {
            return false;
        }

        // 获取第一个IntentionInfo对象
        DialogueIntentionVO.IntentionInfo intentionInfo = intentionVO.getIntentionInfoList().get(0);
        if (intentionInfo == null || intentionInfo.getEntityList() == null || intentionInfo.getEntityList().isEmpty()) {
            return false;
        }
        // 校验子意图
        return DialogueIntentionSubEnum.AI_GENERATE_MEETING_MINUTES.getCode().equals(intentionInfo.getSubIntention());
    }

    @Override
    public boolean run(ChatAddInnerDTO innerDTO) {
        log.info("进入AI会议纪要处理");
        // 获取 prompt 字段
        String promptKey = innerDTO.getContent().getPrompt();
        // 获取用户设置的模型，没有设置则使用默认模型
        AssistantEnum assistantEnum = innerDTO.getContent().getAssistantEnum();
        String businessType = innerDTO.getContent().getBusinessType();
        ChatConfigEntity chatConfigEntity = chatConfigServiceDomainService.getUserCanUseModel(
                innerDTO.getReqParams().getUserId(),
                RequestContextHolder.getPhoneNumber(),
                assistantEnum,
                businessType);
        String modelCode = chatConfigEntity.getModelType();
        // 从ModelPromptProperties配置里面获取提示词，获取不到替换为空
        String promptTemplate = modelPromptProperties.getPrompt(promptKey, modelCode);
        if (CharSequenceUtil.isEmpty(promptTemplate)) {
            promptTemplate = promptKey;
        }
        // 设置请求参数
        innerDTO.getContent().setPrompt(promptTemplate);
        textSseHandler.run(innerDTO);
        return false;
    }
}