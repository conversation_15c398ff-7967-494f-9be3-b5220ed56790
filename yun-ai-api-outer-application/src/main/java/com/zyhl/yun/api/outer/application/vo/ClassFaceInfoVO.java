package com.zyhl.yun.api.outer.application.vo;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ClassFaceInfoVO {

    /**
     * 所属图片ID
     */
    private String fileId;

    /**
     * 人脸大头贴左上角点横坐标
     */
    private Float x0;

    /**
     * 人脸大头贴左上角点纵坐标
     */
    private Float y0;

    /**
     * 人脸大头贴右下角点横坐标
     */
    private Float x1;

    /**
     * 人脸大头贴右下角点纵坐标
     */
    private Float y1;

    /**
     * 是否正脸 0-否 1-是
     */
    private Integer isNormalFace;

}