package com.zyhl.yun.api.outer.application.chatv2.pojo;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import com.zyhl.hcy.yun.ai.common.rag.vo.RecallResultVO;
import com.zyhl.yun.api.outer.constants.RegConst;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 描述：知识库流程处理流传信息，相关知识
 *
 * <AUTHOR> zhumaoxian  2025/4/13 21:26
 */
@Slf4j
@Data
public class KnowledgeFlowInfoDataHandle {

    /**
     * 文档名称
     */
    private String fileName;
    /**
     * 文档召回的片段
     */
    private List<String> segmentList = new ArrayList<>();
    /**
     * 文档创建时间
     */
    private String createTime;
    /**
     * 文档更新时间
     */
    private String updateTime;

    /**
     * 知识库类型
     *
     * @see com.zyhl.yun.api.outer.enums.knowledge.KnowledgeBaseEnum
     */
    private String knowledgeBase;

    private final static String FILE_NAME_STR = "[文件名]";
    private final static String FILE_CONTENT_STR = "\\[文件内容\\]";
    private final static String DOC_KNOWLEDGE_STR = "文档知识";

    public KnowledgeFlowInfoDataHandle(List<RecallResultVO> list) {
        list.sort(Comparator.comparing(RecallResultVO::getIndex));
        dataHandle(list);
    }

    public void dataHandle(List<RecallResultVO> list) {
        for (RecallResultVO vo : list) {
            knowledgeBase = vo.getKnowledgeBase();

            if (ObjectUtil.isEmpty(createTime) && ObjectUtil.isNotEmpty(vo.getLocalCreatedAt()) && vo.getLocalCreatedAt() > 0) {
                createTime = DateUtil.format(new Date(vo.getLocalCreatedAt()), DatePattern.UTC_PATTERN);
            }
            if (ObjectUtil.isEmpty(updateTime) && ObjectUtil.isNotEmpty(vo.getLocalUpdatedAt()) && vo.getLocalUpdatedAt() > 0) {
                updateTime = DateUtil.format(new Date(vo.getLocalUpdatedAt()), DatePattern.UTC_PATTERN);
            }

            // [文件名]:  139邮箱运营上线计划和上线报备流程.ppt [文件内容]:  ##第9页 thanks守正出新立已达人
            String[] arr = vo.getText().split(FILE_CONTENT_STR);

            if (ObjectUtil.isEmpty(fileName) && arr.length > 1) {
                String name = arr[0];
                int startIndex = name.indexOf(FILE_NAME_STR) + FILE_NAME_STR.length() + 1;
                int endIndex = name.lastIndexOf(".");
                fileName = "《" + name.substring(startIndex, (endIndex == -1 ? name.length() : endIndex)).trim() + "》";
            }

            if (arr.length == 1) {
                segmentList.add(arr[0].replaceAll(RegConst.ADDRESS, "【链接详见" + fileName + "文档】"));
            } else {
                segmentList.add(vo.getText().substring(arr[0].length() + 7).trim().replaceAll(RegConst.ADDRESS, "【链接详见" + fileName + "文档】"));
            }
        }
    }

    /**
     * 格式化知识
     *
     * @param index 索引
     * @return String
     */
    public String format(int index, boolean enableMark) {
        StringBuilder sb = new StringBuilder("<文档>");
        if (enableMark) {
            sb.append("<文档ID>[_").append(index).append("]</文档ID>");
        }
        sb.append("<文档名称>").append(fileName).append("</文档名称>");
        sb.append("<文档片段>");
        for (String segment : segmentList) {
            sb.append("<片段>").append(segment).append("</片段>");
        }
        sb.append("</文档片段>");
        sb.append("<文档创建时间>").append(StrUtil.emptyToDefault(createTime, "无")).append("</文档创建时间>");
        sb.append("<文档更新时间>").append(StrUtil.emptyToDefault(updateTime, "无")).append("</文档更新时间>");
        sb.append("</文档>");

        return sb.toString();
    }


    public static List<String> segmentHandle(List<String> segmentList) {
        if (ObjectUtil.isEmpty(segmentList)) {
            return new ArrayList<>();
        }
        for (int i = 0; i < segmentList.size(); i++) {
            String[] arr = segmentList.get(i).split(FILE_CONTENT_STR);
            String segment = HtmlUtil.cleanHtmlTag(segmentList.get(i));
            if (arr.length > 1) {
                segment = HtmlUtil.cleanHtmlTag(segmentList.get(i).substring(arr[0].length() + 7));
            }
            segmentList.set(i, HtmlUtil.unescape(segment));
        }

        return segmentList;
    }
}
