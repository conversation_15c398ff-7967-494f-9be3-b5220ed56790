package com.zyhl.yun.api.outer.application.vo.knowledge;

import com.github.pagehelper.PageInfo;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.vo.common.BasePageInfoVO;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeResource;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeResourceListPageInfoVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 知识库导入任务列表响应
 *
 * <AUTHOR>
 */
@Data
public class KnowledgeFileTaskListVO extends BasePageInfoVO {

    /**
     * 导入任务列表
     */
    private List<PersonalKnowledgeImportTaskVO> list;

    public KnowledgeFileTaskListVO setData(PageInfoDTO pageDTO, PageInfo<?> pageVO, List<PersonalKnowledgeImportTaskVO> fileList) {
        this.list = fileList;
        if (pageDTO.isNeedTotal()) {
            // 需要总数才有分页
            this.totalCount = pageVO.getTotal();
        }
        // 判断是否有下一页
        int nextPageCursor = Integer.parseInt(pageDTO.getPageCursor()) + pageDTO.getPageSize();
        if (nextPageCursor < pageVO.getTotal()) {
            this.nextPageCursor = String.valueOf(nextPageCursor);
        }
        return this;
    }
} 