package com.zyhl.yun.api.outer.application.service.knowledge.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.enums.CommonResultCode;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileAddReqDTO;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.application.enums.KnowledgeTaskQuantityTypeEnum;
import com.zyhl.yun.api.outer.application.service.knowledge.UserKnowledgeMailAndNoteService;
import com.zyhl.yun.api.outer.application.service.mq.KnowledgeDispatchTaskMqService;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeAddResultVO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeTaskResultVO;
import com.zyhl.yun.api.outer.config.KnowledgePersonalProperties;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiRegisterEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileTaskEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeLabelFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeDispatchTaskMqEntity;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.enums.BusinessSourceEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.*;
import com.zyhl.yun.api.outer.enums.mq.MetaTaskTypeEnum;
import com.zyhl.yun.api.outer.repository.AlgorithmAiRegisterRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileTaskRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeLabelFileRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * className: UserKnowledgeMailAndNoteServiceImpl
 * description: 个人知识库 - 邮件/笔记 处理业务接口实现类
 *
 * <AUTHOR>
 * @date 2025/2/10
 */
@Slf4j
@Service
@AllArgsConstructor
public class UserKnowledgeMailAndNoteServiceImpl implements UserKnowledgeMailAndNoteService {

    private final KnowledgePersonalProperties knowledgePersonalProperties;

    private final UserKnowledgeFileTaskRepository userKnowledgeFileTaskRepository;

    private final KnowledgeDispatchTaskMqService knowledgeDispatchTaskMqService;

    private final UserKnowledgeLabelFileRepository userKnowledgeLabelFileRepository;

    private final UserKnowledgeFileRepository userKnowledgeFileRepository;

    private final AlgorithmAiRegisterRepository registerRepository;

    private final UidGenerator uidGenerator;

    /**
     * 添加到知识库成功
     */
    private static final String FILE_EXIST_OTHER_LABEL = "添加到知识库成功";

    /**
     * PAAS CODE，无具体含义，兼容个人云文档的逻辑
     */
    public static final String DEFAULT_PAAS_CODE = "0";

    @Override
    public Long add(KnowledgeFileAddReqDTO dto) {

        String userId = dto.getUserId();
        Integer resourceType = dto.getResourceType();
        List<String> fileIdList = dto.getFileList().stream().map(File::getFileId).collect(Collectors.toList());
        Map<String, String> fileNameMap = dto.getFileList().stream().collect(Collectors.toMap(File::getFileId, File::getName));
        log.info("知识库转存，请求资源id列表：{}", fileIdList);

        // 标签文件映射关系（旧文件id）
        Map<String, List<UserKnowledgeLabelFileEntity>> labelFileMap = userKnowledgeLabelFileRepository.selectByUserId(userId, null, fileIdList)
                .stream().collect(Collectors.groupingBy(UserKnowledgeLabelFileEntity::getOldFileId));

        // 已入库的邮件/笔记/网页
        Map<String, UserKnowledgeFileEntity> existMap = new HashMap<>(Const.NUM_32);
        userKnowledgeFileRepository.selectByOldFileIds(userId, fileIdList).forEach(item -> existMap.put(item.getOldFileId(), item));

        // 循环校验文件
        Long labelId = Long.valueOf(dto.getLabelId());
        List<KnowledgeAddResultVO> resultList = new ArrayList<>();
        List<String> addList = new ArrayList<>();
        List<UserKnowledgeLabelFileEntity> labelFileList = new ArrayList<>();

        if (KnowledgeResourceTypeEnum.MAIL.getCode().equals(resourceType)) {
            handleMail(fileIdList, existMap, labelFileMap, labelId, resultList, addList, labelFileList);
        }


        if (KnowledgeResourceTypeEnum.NOTE.getCode().equals(resourceType)) {
            handleNote(fileIdList, existMap, labelFileMap, labelId, resultList, addList, labelFileList);
        }

        // 任务对象（内部保存转存任务表，并发起向量化提取message）
        UserKnowledgeFileTaskEntity taskEntity = createFileTask(dto, resultList, addList, fileNameMap);

        // 保存映射关系
        labelFileList.forEach(userKnowledgeLabelFileRepository::add);

        return taskEntity.getId();
    }

    @Override
    public KnowledgeTaskResultVO add180(KnowledgeFileAddReqDTO dto) {

        String userId = dto.getUserId();
        Integer resourceType = dto.getResourceType();
        List<String> fileIdList = dto.getFileList().stream().map(File::getFileId).collect(Collectors.toList());
        Map<String, String> fileNameMap = dto.getFileList().stream().collect(Collectors.toMap(File::getFileId, File::getName));
        log.info("知识库转存 | 进入邮件转存前置处理环节，请求邮件id列表：{}", fileIdList);

        // 标签文件映射关系（旧文件id）
        Map<String, List<UserKnowledgeLabelFileEntity>> labelFileMap = userKnowledgeLabelFileRepository.selectByUserId(userId, null, fileIdList)
                .stream().collect(Collectors.groupingBy(UserKnowledgeLabelFileEntity::getOldFileId));

        // 已入库的邮件/笔记
        Map<String, UserKnowledgeFileEntity> existMap = new HashMap<>(Const.NUM_32);
        userKnowledgeFileRepository.selectByOldFileIds(userId, fileIdList).forEach(item -> existMap.put(item.getOldFileId(), item));

        // 循环校验文件
        Long labelId = Long.valueOf(dto.getLabelId());
        List<KnowledgeAddResultVO> resultList = new ArrayList<>();
        List<String> addList = new ArrayList<>();
        List<UserKnowledgeLabelFileEntity> labelFileList = new ArrayList<>();

        if (KnowledgeResourceTypeEnum.MAIL.getCode().equals(resourceType)) {
            handleMail(fileIdList, existMap, labelFileMap, labelId, resultList, addList, labelFileList);
        }


        if (KnowledgeResourceTypeEnum.NOTE.getCode().equals(resourceType)) {
            handleNote(fileIdList, existMap, labelFileMap, labelId, resultList, addList, labelFileList);
        }

        // 任务对象（内部保存转存任务表，并发起向量化提取message）
        UserKnowledgeFileTaskEntity taskEntity = createFileTask(dto, resultList, addList, fileNameMap);

        // 保存映射关系
        labelFileList.forEach(userKnowledgeLabelFileRepository::add);

        // 返回结果
        KnowledgeTaskResultVO resultVO = new KnowledgeTaskResultVO();
        resultVO.setResultList(resultList);
        if (ObjectUtil.isEmpty(addList)) {
            resultVO.setStatus(FileTaskResultEnum.FAIL.getStatus());
        } else {
            resultVO.setStatus(FileTaskResultEnum.PROCESSING.getStatus());
        }
        return resultVO;
    }

    /**
     * 处理邮箱
     * db校验
     * 1、相同邮件（fileId） 相同标签，提示文件已存在
     * 2、相同邮件（fileId） 不同标签，新旧标签同时挂载同一个邮件
     */
    private void handleMail(List<String> fileIdList, Map<String, UserKnowledgeFileEntity> existMap,
                            Map<String, List<UserKnowledgeLabelFileEntity>> labelFileMap, Long labelId,
                            List<KnowledgeAddResultVO> resultList, List<String> addList,
                            List<UserKnowledgeLabelFileEntity> labelFileList) {

        log.info("知识库转存 | 进入循环校验邮件是否重复上传环节。");
        for (String fileId : fileIdList) {

            UserKnowledgeFileEntity entity = existMap.get(fileId);
            // 判断是否存在同样fileId的文件
            if (ObjectUtil.isNotNull(entity)) {

                // 1. 已有标签labelFileMap为空，并且存在新增标签
                if (labelFileMap.isEmpty()) {
                    if (ObjectUtil.isNotNull(labelId)) {
                        // 直接插入新标签
                        labelHandleAndUpdate(fileId, labelId, null, labelFileList, resultList, entity);
                    } else {
                        // 不是新增标签，则直接提示文件已存在
                        resultList.add(new KnowledgeAddResultVO(fileId, ResultCodeEnum.FILE_ALREADY_EXIST));
                    }
                    continue;
                }

                // 2. 已有标签labelFileMap不为空
                List<UserKnowledgeLabelFileEntity> labelFileEntityList = labelFileMap.get(fileId);
                // 有已有标签匹配上的则属于重复上传
                if (labelFileEntityList.stream().anyMatch(labelInfo -> labelInfo.getLabelId().equals(labelId))) {
                    resultList.add(new KnowledgeAddResultVO(fileId, ResultCodeEnum.FILE_ALREADY_EXIST));
                } else {
                    // 无匹配上的，属于新增标签
                    labelHandleAndUpdate(fileId, labelId, labelFileEntityList.get(0), labelFileList, resultList, entity);
                }
                continue;
            }

            // 新上传
            resultList.add(new KnowledgeAddResultVO(fileId, ResultCodeEnum.FILE_UPLOADING));
            addList.add(fileId);
        }
        log.info("知识库转存 | 循环校验邮件是否重复上传环节处理结束，需要新上传的文件列表：{}，需要更新标签的文件列表：{}，total：{}", addList, labelFileList, resultList);
    }

    /**
     * 处理笔记
     * 1、相同笔记（fileId） 相同标签，覆盖上传
     * 2、相同笔记（fileId） 不同标签，新旧标签同时挂载同一个笔记
     */
    private void handleNote(List<String> fileIdList, Map<String, UserKnowledgeFileEntity> existMap,
                            Map<String, List<UserKnowledgeLabelFileEntity>> labelFileMap, Long labelId,
                            List<KnowledgeAddResultVO> resultList, List<String> addList,
                            List<UserKnowledgeLabelFileEntity> labelFileList) {
        for (String fileId : fileIdList) {
            UserKnowledgeFileEntity entity = existMap.get(fileId);
            if (ObjectUtil.isNotNull(entity)) {
                List<UserKnowledgeLabelFileEntity> labelFileEntityList = labelFileMap.get(fileId);
                if (labelFileEntityList.stream().noneMatch(labelInfo -> labelInfo.getLabelId().equals(labelId))) {
                    labelHandleAndUpdate(fileId, labelId, labelFileEntityList.get(0), labelFileList, resultList, entity);
                }
            }
            // 新上传
            resultList.add(new KnowledgeAddResultVO(fileId, ResultCodeEnum.FILE_UPLOADING));
            addList.add(fileId);
        }
    }

    /**
     * 创建文件任务
     */
    private UserKnowledgeFileTaskEntity createFileTask(KnowledgeFileAddReqDTO dto, List<KnowledgeAddResultVO> resultList,
                                                       List<String> fileIdsList, Map<String, String> fileNameMap) {

        // 任务对象
        UserKnowledgeFileTaskEntity taskEntity = new UserKnowledgeFileTaskEntity();
        taskEntity.setUserId(dto.getUserId());
        taskEntity.setOwnerId(dto.getUserId());
        taskEntity.setFileNum(fileIdsList.size());
        taskEntity.setTaskRequest(JsonUtil.toJson(dto));
        taskEntity.setTaskResponse(JsonUtil.toJson(resultList));
        taskEntity.setOwnerType(OwnerTypeEnum.AI.getOwnerValue());
        taskEntity.setFileIds(String.join(StrPool.COMMA, fileIdsList));
        taskEntity.setTaskStatus(FileTaskStatusEnum.FINISH.getStatus());
        taskEntity.setTaskType(KnowledgeTaskTypeEnum.TRANSFER.getCode());
        taskEntity.setExpireTime(knowledgePersonalProperties.getTransferExpireDate());
        // 发起转存任务
        if (CollUtil.isNotEmpty(fileIdsList)) {
            log.info("知识库转存 | 进入邮件转存向量化message发送环节，邮件id集合：{}", fileIdsList);
            // 文件转存（RAG2.2，邮件和笔记无需转存，而是发送向量化提取message）
            launchDispatchTask(taskEntity, fileIdsList, dto.getResourceType(), fileNameMap);
        }
        // 保存到转存任务表（兼容RAG1.0的逻辑，前端可以查询，下游完成后更新状态）
        userKnowledgeFileTaskRepository.add(taskEntity);
        return taskEntity;
    }

    /**
     * 组装请求体，发送mq
     */
    private void launchDispatchTask(UserKnowledgeFileTaskEntity entity, List<String> fileIdsList, Integer resourceType, Map<String, String> fileNameMap) {

        String userId = entity.getUserId();
        // 获取算法组信息
        AlgorithmAiRegisterEntity registerEntity = registerRepository.queryByUserId(userId, BusinessSourceEnum.ASSISTANT.getCode());
        // 组装
        List<UserKnowledgeDispatchTaskMqEntity> taskEntityList = new ArrayList<>();
        List<UserKnowledgeFileEntity> fileEntityList = new ArrayList<>();
        fileIdsList.forEach(fileId -> {
            // 此处避免唯一索引userId_fileId冲突，需要拼接随机码，此处为newFileId
            String transferFileId = fileId + StrPool.UNDERLINE + uidGenerator.getUID();
            // 任务组装
            UserKnowledgeDispatchTaskMqEntity taskEntity = new UserKnowledgeDispatchTaskMqEntity();
            taskEntity.setTaskType(KnowledgeTaskQuantityTypeEnum.SINGLE.getCode());
            // PaasCode，此处没有，给固定值作为参数传递，兼容db的design
            taskEntity.setPaasCode(DEFAULT_PAAS_CODE);
            taskEntity.setRefresh(true);
            taskEntity.setFileId(transferFileId);
            taskEntity.setUserId(userId);
            taskEntity.setOwnerId(userId);
            taskEntity.setResourceType(resourceType);
            taskEntity.setOwnerType(OwnerTypeEnum.AI.getOwnerValue());
            taskEntity.setMetaTaskType(MetaTaskTypeEnum.DOC_PERSONAL_KNOWLEDGE_VECTOR.getCode());
            taskEntity.setAlgorithmGroupCode(registerEntity != null ? String.valueOf(registerEntity.getAlgorithmGroupCode()) : "");
            taskEntityList.add(taskEntity);

            // 落库组装
            UserKnowledgeFileEntity fileEntity = new UserKnowledgeFileEntity();
            fileEntity.setId(uidGenerator.getUID());
            fileEntity.setUserId(userId);
            fileEntity.setFileId(transferFileId);
            fileEntity.setFileName(fileNameMap.get(fileId));
            fileEntity.setOldFileId(fileId);
            fileEntity.setCreateTime(new Date());
            fileEntity.setOwnerId(entity.getOwnerId());
            fileEntity.setFromResourceType(resourceType);
            fileEntity.setOwnerType(entity.getOwnerType());
            fileEntity.setDelFlag(KnowledgeStatusEnum.NORMAL.getStatus());
            fileEntity.setAuditStatus(FileAuditStatusEnum.UNAUDITED.getStatus());
            fileEntity.setAiStatus(FileProcessStatusEnum.UNPROCESSED.getStatus());
            fileEntity.setCreateTime(new Date());
            fileEntity.setUpdateTime(new Date());
            // 以下字段为兼容性入参，邮件/笔记不存在该数据
            fileEntity.setFileType(FileTypeEnum.FILE.getKnowledgeFileType());
            fileEntity.setPaasCode(DEFAULT_PAAS_CODE);
            fileEntity.setHashName(String.valueOf(resourceType));
            fileEntity.setHashValue(String.valueOf(resourceType));
            fileEntityList.add(fileEntity);
        });
        try {
            // send message
            knowledgeDispatchTaskMqService.sendMq(taskEntityList);
            log.info("知识库转存 | 发送邮件转存向量化message成功，消息content：{}", taskEntityList);
        } catch (Exception e) {
            log.error("个人知识库 - 邮件转存任务发送message失败。file info:{}", entity, e);
            throw new YunAiBusinessException(CommonResultCode.ERROR_CALL_EXCEPTION);
        }
        // save file
        userKnowledgeFileRepository.saveBatch(fileEntityList);
    }

    /**
     * 标签处理并更新文件的更新时间
     *
     * @param fileId          文件id
     * @param labelFileEntity 标签与文件映射的实体类
     * @param labelId         新增标签id
     * @param labelFileList   标签与文件映射关系保存实体集合
     * @param resultList      结果集合
     * @param entity          重复上传的文件实体
     */
    private void labelHandleAndUpdate(String fileId, Long labelId,
                                      UserKnowledgeLabelFileEntity labelFileEntity,
                                      List<UserKnowledgeLabelFileEntity> labelFileList,
                                      List<KnowledgeAddResultVO> resultList, UserKnowledgeFileEntity entity) {
        if (null == labelFileEntity) {
            labelFileEntity = new UserKnowledgeLabelFileEntity(entity.getUserId(), fileId);
        }
        labelFileEntity.setLabelId(labelId);
        labelFileList.add(labelFileEntity);
        resultList.add(new KnowledgeAddResultVO(fileId, ResultCodeEnum.SUCCESS.getResultCode(), FILE_EXIST_OTHER_LABEL));
        // 更新文件的更新时间
        UserKnowledgeFileEntity updateEntity = new UserKnowledgeFileEntity();
        updateEntity.setId(entity.getId());
        userKnowledgeFileRepository.update(updateEntity);
    }
}
