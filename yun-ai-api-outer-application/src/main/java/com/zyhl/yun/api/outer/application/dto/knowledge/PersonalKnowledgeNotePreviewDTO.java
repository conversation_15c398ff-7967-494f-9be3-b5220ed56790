package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import com.zyhl.yun.api.outer.application.dto.BaseDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/18 14:31
 */
@Data
public class PersonalKnowledgeNotePreviewDTO extends BaseChannelDTO implements Serializable {

    /**
     * 资源id
     */
    @NotBlank(message = "资源id不能为空")
    private String resourceId;
}
