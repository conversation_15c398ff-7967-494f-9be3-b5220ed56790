package com.zyhl.yun.api.outer.application.chatv2.hanlde.listener;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import cn.hutool.core.collection.ListUtil;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatDialogueRecommendService;
import com.zyhl.yun.api.outer.config.*;
import com.zyhl.yun.api.outer.domain.vo.AllNetworkSearchRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.vo.MultiSearchVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.FileOperationUtil;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.event.TextModelStreamEventListener;
import com.zyhl.hcy.yun.ai.common.model.api.client.vo.TextModelBaseVo;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.ApiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.dto.CheckTextReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.vo.CheckResultVO;
import com.zyhl.yun.api.outer.application.assembler.ChatFlowResultAssembler;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.event.CompleteCallbackEvent;
import com.zyhl.yun.api.outer.application.chatv2.pojo.CompleteEvent;
import com.zyhl.yun.api.outer.application.chatv2.pojo.TextModelParamInfo;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueFlowResultVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatHistoryService;
import com.zyhl.yun.api.outer.application.service.chat.DialogueRecommendService;
import com.zyhl.yun.api.outer.application.service.external.MemberCenterService;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.valueobject.HtmlInfo;
import com.zyhl.yun.api.outer.domain.valueobject.KnowledgeSearchInfo;
import com.zyhl.yun.api.outer.domainservice.CheckSystemDomainService;
import com.zyhl.yun.api.outer.domainservice.SseEmitterOperate;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.CheckTypeEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.AiTextResultVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddFlowStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.OutAuditStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.SseNameEnum;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.sse.EventSource;

/**
 * 流式对话事件监听，多实例
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
public class SseEventListener extends TextModelStreamEventListener {

    private FlowTypeProperties flowTypeProperties;
    private RedisOperateRepository redisOperateRepository;
    private AlgorithmChatHistoryService algorithmChatHistoryService;
    private DialogueRecommendService dialogueRecommendService;
    private AiTextResultRepository aiTextResultRepository;
    private MemberCenterService memberCenterService;
    private CheckSystemConfig checkSystemConfig;
    private CheckSystemDomainService checkSystemDomainService;
    private DialogueRecommendProperties dialogueRecommendProperties;
    private ChatFlowResultAssembler chatFlowResultAssembler;
    private ChatDialogueRecommendService chatDialogueRecommendService;
    /**
     * 获取主线程的ThreadLocal信息，并且异步调用前，将父线程中的请求信息绑定给子线程
     */
    private RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo;

    private AllNetworkSearchProperties allNetworkSearchProperties;
    private ChatProperties chatProperties;

    public SseEventListener(ChatAddHandleDTO handleDTO, List<TextModelMessageDTO> historyList) {
        flowTypeProperties = SpringUtil.getBean(FlowTypeProperties.class);
        algorithmChatHistoryService = SpringUtil.getBean(AlgorithmChatHistoryService.class);
        dialogueRecommendService = SpringUtil.getBean(DialogueRecommendService.class);
        aiTextResultRepository = SpringUtil.getBean(AiTextResultRepository.class);
        redisOperateRepository = SpringUtil.getBean(RedisOperateRepository.class);
        memberCenterService = SpringUtil.getBean(MemberCenterService.class);
        checkSystemConfig = SpringUtil.getBean(CheckSystemConfig.class);
        checkSystemDomainService = SpringUtil.getBean(CheckSystemDomainService.class);
        dialogueRecommendProperties = SpringUtil.getBean(DialogueRecommendProperties.class);
        chatFlowResultAssembler = SpringUtil.getBean(ChatFlowResultAssembler.class);
        chatDialogueRecommendService = SpringUtil.getBean(ChatDialogueRecommendService.class);
        allNetworkSearchProperties = SpringUtil.getBean(AllNetworkSearchProperties.class);
        chatProperties = SpringUtil.getBean(ChatProperties.class);

        init(handleDTO, historyList);
    }

    /**
     * 全量内容（送审使用全量，保存数据库使用全量）
     */
    private final StringBuilder allThinkMsg = new StringBuilder();
    private final StringBuilder allMsg = new StringBuilder();
    /**
     * 增量内容（输出使用增量）
     */
    private final StringBuilder addThinkMsg = new StringBuilder();
    private final StringBuilder addMsg = new StringBuilder();
    private String resultCode = "";
    private String resultMsg = "";
    /**
     * 大模型token标识
     */
    private Integer outputTokens = 0;
    /**
     * 结束对话标识
     */
    private boolean isFirst = false;
    private boolean isLast = false;
    private boolean hasSend = false;

    private long requestTime = RequestContextHolder.getRequestTime();
    private String userId;
    private String phone = RequestContextHolder.getPhoneNumber();
    private Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();
    private Long dialogId;
    private SseEmitterOperate sseEmitterOperate;
    private String logName = "";
    private ChatAddHandleDTO handleDTO;
    private ChatAddReqDTO reqDTO;
    private DialogueInputInfoDTO inputInfoDTO;
    private ChatAddRespVO respVO;
    private List<Future<Object>> futures;
    private TextModelParamInfo textDto;
    private DialogueIntentionVO intentionVO;
    private List<File> fileList;
    private boolean useOrderTip;
    /**
     * AI全网搜结果
     */
    private List<MultiSearchVO> aiInternetSearchResultList;
    /**
     * 附件临时文件路径
     */
    private List<String> attachmentToFilePaths;
    private CheckSystemConfig.CheckSystemTextModel checkSystemTextModel = null;

    private Map<String, String> logMap = MDC.getCopyOfContextMap();
    private int sendIndex = -1;

    // --------------------- 可在外部设置的字段 start --------------------- //
    /**
     * 父级title信息
     */
    private String parentTitle;
    /**
     * 使用的大模型编码
     */
    private String modelCode;
    /**
     * title信息
     */
    private String title;
    /**
     * 结束语
     */
    private String ending;
    /**
     * 回答声明
     */
    private String responseStatement;
    /**
     * 知识库文件
     */
    private List<KnowledgeSearchInfo> personalKnowledgeFileList;

    /**
     * 输出网络搜索信息，默认输出，不需要的设置false【智能体，知识库】
     */
    private boolean outputNetworkSearchInfo = true;
    private boolean onlySaveNetworkSearchInfo = false;
    /**
     * 网络搜索信息列表
     */
    private List<HtmlInfo> networkSearchInfoList;

    /**
     * 入库hbase结果列表-大模型之前
     */
    private List<DialogueFlowResult> beforeOutputList = new ArrayList<>();
    /**
     * 入库hbase结果列表-大模型之后
     */
    private List<DialogueFlowResult> afterOutputList = new ArrayList<>();

    /**
     * 入库前，追加outContent之前的数据
     */
    private String appendBeforeOutContent;

    /**
     * 回调需要的流式结果对象
     */
    private DialogueFlowResult callBackDialogueFlowResult;

    /**
     * 回调需要的流式结果对象原始值
     */
    private DialogueFlowResult callBackOriginalDialogueFlowResult;

    /**
     * 回调需要的流式结果标题
     */
    private String callBackTitle;
    /**
     * 是否操作db，默认需要操作
     */
    private boolean operateDatabase = true;

    public void setInternetSearchInfoList(List<AiTextResultRespParameters.NetworkSearchInfo> internetSearchInfoList) {
        if (ObjectUtil.isNotEmpty(internetSearchInfoList)) {
            networkSearchInfoList = new ArrayList<>();
            internetSearchInfoList.forEach(item -> networkSearchInfoList.add(new HtmlInfo(item)));
        }
    }

    /**
     * 完成回调事件
     */
    private CompleteCallbackEvent completeCallbackEvent;

    // --------------------- 可在外部设置的字段 end --------------------- //

    /**
     * a标签
     */
    private static final String A_TAG_START = "<a";
    private static final String A_TAG_END = "</a>";
    private boolean aTagStart = false;
    private boolean aTagEnd = false;

    /**
     * AI全网搜【查看更多】标签
     */
    private static final String AI_INTERNET_SEARCH_MORE = "<a>more</a>";
    private static final String AI_INTERNET_SEARCH_NO_MORE = "<a>no_more</a>";

    /**
     * 初始化
     *
     * @param handleDTO   对话请求参数
     * @param historyList 历史消息列表
     */
    public void init(ChatAddHandleDTO handleDTO, List<TextModelMessageDTO> historyList) {
        mainThreadLocalInfo = RequestContextHolder.getThreadLocalInfoAndBindingAttributes();
        if (null != handleDTO) {
            this.setDialogue(handleDTO.getInputInfoDTO().getDialogue());
        }
        sseEmitterOperate = handleDTO.getSseEmitterOperate();
        this.handleDTO = handleDTO;
        reqDTO = handleDTO.getReqDTO();
        inputInfoDTO = handleDTO.getInputInfoDTO();
        respVO = handleDTO.getRespVO();
        futures = handleDTO.getFutures();

        userId = handleDTO.getReqDTO().getUserId();
        dialogId = handleDTO.getDialogueId();

        textDto = new TextModelParamInfo(handleDTO, historyList);

        intentionVO = handleDTO.getIntentionVO();

        fileList = handleDTO.getRespVO().getFlowResult().getFileList();
        useOrderTip = handleDTO.isUseOrderTip();
        aiInternetSearchResultList = handleDTO.getAiInternetSearchResultList();
        // 设置handleDTO
        this.handleDTO = handleDTO;
    }


    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
        this.checkSystemTextModel = checkSystemConfig.getCheckSystemTextModel(modelCode);
        this.respVO.getFlowResult().setModelType(modelCode);
        this.responseStatement = chatProperties.getChatOutput().getStatement(handleDTO.getAssistantEnum(), handleDTO.getBusinessType(), modelCode);
    }

    /**
     * 避免多次执行beforeSend
     */
    private boolean EXE_BEFORE = false;

    public void before() {
        MDC.setContextMap(logMap);
        if (FileOperationUtil.deleteLocalFiles(attachmentToFilePaths) > 0) {
            attachmentToFilePaths = null;
        }
        if (EXE_BEFORE) {
            return;
        }
        EXE_BEFORE = true;
        // 把主线程ThreadLocal信息set到子线程
        RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);
        RequestContextHolder.setUserInfo(Long.valueOf(userId), phone, belongsPlatform);
        logName = Objects.requireNonNull(SseNameEnum.getByName(sseEmitterOperate.getSseName())).getLogName();
    }


    @Override
    public boolean onEvent(TextModelBaseVo response) {
        before();
        try {
            if (redisOperateRepository.getStopDialogue(dialogId)) {
                // 对话结束
                log.info("【流式对话】{}对话已停止，对话id：{}", logName, dialogId);
                return dialogueStop();
            }

            // 判断意图替换\\n为\n
            IntentionInfo mainIntention = DialogueIntentionVO.getMainIntention(intentionVO);
            if (null != response.getText() && null != mainIntention && DialogueIntentionEnum
                    .isSpecReplaceBr(mainIntention.getIntention(), mainIntention.getSubIntention())) {
                if (response.getText().contains("\\n")) {
                    log.info("意图出现 isSpecReplaceBr 替换 \\n为\n 对话id：{}", dialogId);
                    response.setText(response.getText().replace("\\n", "\n"));
                }
            }

            // 最后一次事件（不同模型判断不一样）
            isLast = Boolean.TRUE.equals(response.getIsLast()) || outputTokens.equals(response.getOutputTokens());
            outputTokens = response.getOutputTokens() == null ? 0 : response.getOutputTokens();

            addThinkMsg.append(CharSequenceUtil.nullToDefault(response.getReasoningContent(), StringUtils.EMPTY));
            addMsg.append(CharSequenceUtil.nullToDefault(response.getText(), StringUtils.EMPTY));
            if (!isLast && unReachedCheckSize()) {
                //未达到送审字数
                return true;
            }

            // 控制返回完整a标签
            if (aTagHandle(isLast)) {
                return true;
            }

            // 送审以及输出给端侧
            if (ObjectUtil.isNotEmpty(addThinkMsg) || ObjectUtil.isNotEmpty(addMsg)) {
                isFirst = allThinkMsg.length() == 0 && allMsg.length() == 0;
                // 执行文本内容送审
                allThinkMsg.append(addThinkMsg);
                allMsg.append(addMsg);
                if (checkSystemTextModel.isCheckPart()) {
                    if (CheckResultVO.isFail(checkText(false))) {
                        return dialogueFail(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
                    }
                }

                /** 构建全网搜【查看更多】推荐 */
                createAllNetworkSearchRecommend();

                // 分开输出
                DialogueFlowResultVO flowResultVO = new DialogueFlowResultVO(addMsg.toString(), addThinkMsg.toString(), ChatAddFlowStatusEnum.PROCESSING);

                // 发送SseEmitter消息
                respVO.setTitle("");
                if (isFirst) {
                    respVO.setTitle(parentTitle);
                    flowResultVO.setTitle(title);
                    flowResultVO.setNetworkSearchInfoList(networkSearchInfoHandle(response.getNetworkSearchInfoList()));
                }
                respVO.setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());

                if (null != this.getHandleDTO().getLingxiParamInfo()
                        && this.getHandleDTO().getLingxiParamInfo().isLingxiRespFlag()) {
                    respVO.setFlowResult(flowResultVO);
                    send(BaseResult.success(respVO),
                            OpenApiLingxiChatRespVO.getOpenApiLingxiChatResp(handleDTO, respVO));
                } else {
                    send(BaseResult.success(respVO.setFlowResult(flowResultVO)));
                }

                // 发送消息后参数处理
                addThinkMsg.setLength(0);
                addMsg.setLength(0);
                hasSend = true;
            }

            // 最后一次 更新会话任务状态
            if (isLast) {
                if (CheckSystemConfig.isAuditAll(checkSystemTextModel.getType())) {
                    // 执行文本内容送审（全量送审）
                    log.info("【流式对话】{}正常结束，全量送审一次，对话id：{}", logName, dialogId);
                    if (CheckResultVO.isFail(checkText(true))) {
                        log.error("【流式对话】{}正常结束，全量送审失败，对话id：{}", logName, dialogId);
                        return dialogueFail(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
                    }
                }

                // 对话结束
                return dialogueSuccess();
            }
        } catch (YunAiBusinessException e) {
            // 回滚权益
            memberCenterService.consumeBenefitFail(userId, phone, dialogId);

            log.error("【流式对话】{}对话异常，流式响应信息：{}", logName, JsonUtil.toJson(response), e);
            return dialogueFail(AiResultCode.getByCodeOrMsg(e.getCode(), e.getMessage()));
        } catch (Exception e) {
            // 回滚权益
            memberCenterService.consumeBenefitFail(userId, phone, dialogId);

            log.error("【流式对话】{}对话异常，流式响应信息：{}", logName, JsonUtil.toJson(response), e);
            return dialogueFail(ResultCodeEnum.ERROR_AI_MODEL);
        }

        return true;
    }

    @Override
    public void onClosed(EventSource eventSource) {
        MDC.setContextMap(logMap);
        if (FileOperationUtil.deleteLocalFiles(attachmentToFilePaths) > 0) {
            attachmentToFilePaths = null;
        }
        String logStr = "【流式对话】" + logName + "对话关闭，输出信息，";
        LogCommonUtils.printlnListLog(logStr + "allThinkMsg第{}分块：\n{}", allThinkMsg.toString());
        LogCommonUtils.printlnListLog(logStr + "allMsg第{}分块：\n{}", allMsg.toString());
        log.info(logStr + "title：{}，parentTitle：{}，modelCode：{}，ending：{}，outputNetworkSearchInfo：{}，allThinkMsg.length：{}，allMsg.length：{}"
                , title, parentTitle, modelCode, ending, outputNetworkSearchInfo, allThinkMsg.length(), allMsg.length());
        LogCommonUtils.printlnListLog(logStr + "personalKnowledgeFileList第{}分块：\n{}", JsonUtil.toJson(personalKnowledgeFileList));
        LogCommonUtils.printlnListLog(logStr + "networkSearchInfoList第{}分块：\n{}", JsonUtil.toJson(networkSearchInfoList));


        if (hasCompleteCallbackEvent()) {
            log.info("【流式对话】{}大模型对话关闭，但需要继续执行下一个handler", logName);

            // 更新hbase会话内容
            AiTextResultRespParameters result = getRespResult(ResultCodeEnum.SUCCESS.getResultCode(), ResultCodeEnum.SUCCESS.getResultMsg());
            this.getRespVO().getFlowResult().setIndex(sendIndex++);

            if (this.operateDatabase) {
                aiTextResultRepository.updateAddFlowResult(userId, dialogId, result);
            }

            // 设置完成参数
            if (fileList != null && fileList.size() > 0) {
                handleDTO.getRespVO().getFlowResult().setFileList(fileList);
            }
            CompleteEvent completeEvent = new CompleteEvent(this.handleDTO, this);
            completeEvent.setResultCode(resultCode);
            completeEvent.setResultMsg(resultMsg);
            completeEvent.setOutContent(allMsg.toString());
            completeEvent.setReasoningContent(allThinkMsg.toString());

            // 执行回调
            completeCallbackEvent.complete(completeEvent);
            return;
        }

        // 兜底结束，默认是成功
        if (!sseEmitterOperate.isComplete()) {
            dialogueSuccess();
        }
    }

    @Override
    public void onFailure(Throwable e, int code, String body) {
        MDC.setContextMap(logMap);
        if (FileOperationUtil.deleteLocalFiles(attachmentToFilePaths) > 0) {
            attachmentToFilePaths = null;
        }
        String logStr = "【流式对话】" + logName + "对话失败，输出信息，";
        LogCommonUtils.printlnListLog(logStr + "allThinkMsg第{}分块：\n{}", allThinkMsg.toString());
        LogCommonUtils.printlnListLog(logStr + "allMsg第{}分块：\n{}", allMsg.toString());
        log.info(logStr + "title：{}，code：{}，body：{}", title, code, body);
        LogCommonUtils.printlnListLog(logStr + "personalKnowledgeFileList第{}分块：\n{}", JsonUtil.toJson(personalKnowledgeFileList));
        LogCommonUtils.printlnListLog(logStr + "networkSearchInfoList第{}分块：\n{}", JsonUtil.toJson(networkSearchInfoList));

        if (sseEmitterOperate.isComplete()) {
            return;
        }
        if (ApiCommonResultCode.SENSITIVE_ERROR.getResultCode().equals(String.valueOf(code))) {
            //强制转换，统一错误码
            e = new YunAiBusinessException(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
        }
        if (e instanceof YunAiBusinessException) {
            YunAiBusinessException ex = (YunAiBusinessException) e;
            dialogueFail(AiResultCode.getByCodeOrMsg(ex.getCode(), ex.getMessage()));
        } else {
            dialogueFail(ResultCodeEnum.ERROR_AI_MODEL);
        }
    }

    /**
     * 发送推荐信息
     */
    public void sendRecommendInfo() {
        if (!ObjectUtil.isEmpty(futures)) {
            // 返回推荐信息
            dialogueRecommendService.setFuturesResult(dialogId, respVO.getRecommend(), futures);
            // 使用大模型回答时，不需要返回【大模型推荐】
            chatDialogueRecommendService.removeTextIntentionRecommend(this.respVO.getRecommend());
        }

        // 分开输出
        DialogueFlowResultVO flowResultVO = new DialogueFlowResultVO(addMsg.toString(), addThinkMsg.toString(),
                ChatAddFlowStatusEnum.STOP);
        respVO.setEnding(ending);
        respVO.setResponseStatement(responseStatement);
        respVO.setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());

        if (null != this.getHandleDTO().getLingxiParamInfo()
                && this.getHandleDTO().getLingxiParamInfo().isLingxiRespFlag()) {
            respVO.setFlowResult(flowResultVO);
            send(BaseResult.success(respVO), OpenApiLingxiChatRespVO.getOpenApiLingxiChatResp(handleDTO, respVO));
        } else {
            send(BaseResult.success(respVO.setFlowResult(flowResultVO)));
        }
    }

    /**
     * 构建全网搜【查看更多】推荐
     *
     * @Author: WeiJingKun
     */
    private void createAllNetworkSearchRecommend() {
        /** 构建全网搜【查看更多】推荐 */
        String allMsgStr = allMsg.toString();
        String addMsgStr = addMsg.toString();
        if (CharSequenceUtil.isNotBlank(allMsgStr)) {
            if (allMsgStr.contains(AI_INTERNET_SEARCH_MORE)
                    && CharSequenceUtil.isNotBlank(allNetworkSearchProperties.getSearchMoreRecommendQuery())) {
                // 构建全网搜【查看更多】推荐
                AllNetworkSearchRecommendVO allNetworkSearchRecommend = new AllNetworkSearchRecommendVO();
                allNetworkSearchRecommend.setQuery(allNetworkSearchProperties.getSearchMoreRecommendQuery());
                allNetworkSearchRecommend.setButtonCopy(allNetworkSearchProperties.getSearchMoreRecommendButtonCopy());
                allNetworkSearchRecommend.setHasMore(true);
                List<AllNetworkSearchRecommendVO> allNetworkSearchRecommendList = ListUtil.toList(allNetworkSearchRecommend);
                // 设置全网搜【查看更多】推荐
                DialogueRecommendVO recommendVO = this.respVO.getRecommend();
                if (ObjectUtil.isNull(recommendVO)) {
                    recommendVO = DialogueRecommendVO.builder().allNetworkSearchList(allNetworkSearchRecommendList).build();
                } else {
                    recommendVO.setAllNetworkSearchList(allNetworkSearchRecommendList);
                }
                this.respVO.setRecommend(recommendVO);
            }
            /** 不输出，全网搜【查看更多】推荐标签 */
//            // 全量内容
//            allMsgStr = allMsgStr.replace(AI_INTERNET_SEARCH_MORE, "");
//            allMsgStr = allMsgStr.replace(AI_INTERNET_SEARCH_NO_MORE, "");
//            allMsg.setLength(0);
//            allMsg.append(allMsgStr);
//            // 增量内容
//            addMsgStr = addMsgStr.replace(AI_INTERNET_SEARCH_MORE, "");
//            addMsgStr = addMsgStr.replace(AI_INTERNET_SEARCH_NO_MORE, "");
//            addMsg.setLength(0);
//            addMsg.append(addMsgStr);
        }
    }

    /**
     * 处理对话成功
     *
     * @return boolean
     */
    public boolean dialogueSuccess() {
        // 【AI全网搜】流式对话末尾，添加【视频搜索】注意事项
        if (this.reqDTO.getDialogueInput().isEnableAllNetworkSearch()
                && CollUtil.isNotEmpty(allNetworkSearchProperties.getVideoSearchPrecautionList())) {
            String videoSearchPrecaution = allNetworkSearchProperties.randomGetVideoSearchPrecaution();
            addMsg.append(videoSearchPrecaution);
            allMsg.append(videoSearchPrecaution);
        }

        if (hasCompleteCallbackEvent()) {
            // 最后一次输出
            DialogueFlowResultVO flowResultVO = new DialogueFlowResultVO(addMsg.toString(), addThinkMsg.toString(), ChatAddFlowStatusEnum.STOP);
            respVO.setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());

            if (null != this.getHandleDTO().getLingxiParamInfo()
                    && this.getHandleDTO().getLingxiParamInfo().isLingxiRespFlag()) {
                respVO.setFlowResult(flowResultVO);
                send(BaseResult.success(respVO),
                        OpenApiLingxiChatRespVO.getOpenApiLingxiChatResp(handleDTO, respVO));
            } else {
                send(BaseResult.success(respVO.setFlowResult(flowResultVO)));
            }

            log.info("文生文结束，执行下一个handler... dialogId:{}", this.getDialogId());
            return true;
        }

        // 最后一次输出
        sendRecommendInfo();

        return dialogueSuccess(ChatStatusEnum.CHAT_SUCCESS);
    }

    /**
     * 处理成功的情况
     *
     * @param chatStatus 对话状态
     * @return boolean
     */
    public boolean dialogueSuccess(ChatStatusEnum chatStatus) {
        // sse结束
        complete();

        if (this.operateDatabase) {
            // 更新会话状态
            algorithmChatHistoryService.updateOutResult(dialogId, OutAuditStatusEnum.SUCCESS, chatStatus, null, JsonUtil.toJson(this.respVO.getRecommend()));

            // 更新hbase会话内容
            AiTextResultRespParameters result = getRespResult(ResultCodeEnum.SUCCESS.getResultCode(), ResultCodeEnum.SUCCESS.getResultMsg());
            aiTextResultRepository.update(userId, dialogId, textDto.addAnswer(allMsg.toString()), result);
        }


        return true;
    }

    /**
     * 处理停止对话的情况，前端调停止接口修改状态，这里只需要更新输出审核状态
     *
     * @return boolean
     */
    public boolean dialogueStop() {
        // sse结束
        DialogueFlowResultVO flowResultVO = new DialogueFlowResultVO();
        flowResultVO.setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());

        if (null != this.getHandleDTO().getLingxiParamInfo()
                && this.getHandleDTO().getLingxiParamInfo().isLingxiRespFlag()) {
            respVO.setFlowResult(flowResultVO);
            send(BaseResult.success(respVO),
                    OpenApiLingxiChatRespVO.getOpenApiLingxiChatResp(handleDTO, respVO));
        } else {
            respVO.setResponseStatement(responseStatement);
            send(BaseResult.success(respVO.setFlowResult(flowResultVO)));
        }
        complete();

        if (this.operateDatabase) {
            // 更新会话状态（对话成功）
            algorithmChatHistoryService.updateOutResultStop(dialogId);

            // 更新hbase会话内容
            AiTextResultRespParameters result = getRespResult(ResultCodeEnum.SUCCESS.getResultCode(), ResultCodeEnum.SUCCESS.getResultMsg());
            aiTextResultRepository.update(userId, dialogId, textDto.addAnswer(allMsg.toString()), result);
        }

        return true;
    }

    /**
     * 处理失败的情况
     *
     * @param aiResultCode 结果
     * @return boolean
     */
    public boolean dialogueFail(AiResultCode aiResultCode) {
        return dialogueFail(aiResultCode.getCode(), aiResultCode.getMsg());
    }

    public boolean dialogueFail(AbstractResultCode aiResultCode) {
        return dialogueFail(aiResultCode.getResultCode(), aiResultCode.getResultMsg());
    }

    public boolean dialogueFail(String resultCode, String resultMsg) {
        // 发送SseEmitter消息并关闭连接
        DialogueFlowResultVO flowResultVO = new DialogueFlowResultVO(addMsg.toString(), addThinkMsg.toString(), ChatAddFlowStatusEnum.STOP);
        respVO.setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
        if (ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultCode().equals(resultCode)) {
            flowResultVO.setOutAuditStatus(OutAuditStatusEnum.FAIL.getCode());
        }

        if (null != this.getHandleDTO().getLingxiParamInfo()
                && this.getHandleDTO().getLingxiParamInfo().isLingxiRespFlag()) {
            respVO.setFlowResult(flowResultVO);
            send(BaseResult.success(respVO),
                    OpenApiLingxiChatRespVO.getOpenApiLingxiChatErrorResp(handleDTO, resultCode, resultMsg));
        } else {
            send(BaseResult.error(resultCode, resultMsg, respVO.setFlowResult(flowResultVO)));
        }

        //存在错误，去掉下一个处理handle
        if (Objects.nonNull(this.completeCallbackEvent)) {
            log.info("对话失败，移除下一个handler... dialogId:{}", this.getDialogId());
            this.completeCallbackEvent = null;
        }
        complete();

        if (this.operateDatabase) {
            // 更新会话状态（对话失败）
            AiTextResultRespParameters result = getRespResult(resultCode, resultMsg);
            algorithmChatHistoryService.updateOutResult(dialogId, OutAuditStatusEnum.FAIL, ChatStatusEnum.CHAT_FAIL, JsonUtil.toJson(result));

            // 更新hbase会话内容
            aiTextResultRepository.update(userId, dialogId, null, result);
        }

        return true;
    }

    /**
     * 获取响应结果
     *
     * @param resultCode 错误码
     * @param resultMsg  错误信息
     * @return 响应结果
     */
    private AiTextResultRespParameters getRespResult(String resultCode, String resultMsg) {
        // 保存下来，用于回调
        this.resultCode = resultCode;
        this.resultMsg = resultMsg;

        AiTextResultRespParameters result = AiTextResultRespParameters.builder()
                .version(AiTextResultVersionEnum.V2.getVersion()).resultCode(resultCode).resultMsg(resultMsg)
                .data(String.valueOf(allMsg)).build();
        result.setTitle(parentTitle);
        if (ResultCodeEnum.SUCCESS.getResultCode().equals(resultCode)) {
            result.setEnding(ending);
            result.setResponseStatement(responseStatement);
            DialogueFlowResult flowResult = new DialogueFlowResult(String.valueOf(allMsg), String.valueOf(allThinkMsg));
            flowResult.setTitle(title);
            flowResult.setPersonalKnowledgeFileList(personalKnowledgeFileList);
            flowResult.setNetworkSearchInfoList(outputNetworkSearchInfo ? networkSearchInfoList : null);
            if (null == completeCallbackEvent && CollUtil.isNotEmpty(this.getBeforeOutputList())) {
                // 追加输出之前的文案
                flowResult.setIndex(this.getBeforeOutputList().size());
                this.getBeforeOutputList().add(flowResult);
                result.setOutputList(this.getBeforeOutputList());
            } else {
                result.setOutputList(Collections.singletonList(flowResult));
            }
            result.setOutputCommandVO(this.getIntentionVO());
            result.setAiInternetSearchResultList(this.getAiInternetSearchResultList());
        }
        return result;
    }

    private <T extends ChatAddRespVO> void send(BaseResult<T> result, OpenApiLingxiChatRespVO resp) {
        sendIndex++;

        // 设置公共参数
        DialogueFlowResultVO flowResultVO = result.getData().getFlowResult();
        flowResultVO.setModelType(modelCode);
        flowResultVO.setIndex(sendIndex);

        // 先保存redis
        redisOperateRepository.setFlowResult(dialogId, sendIndex, JsonUtil.toJson(result));

        // 调用send发送消息
        if (sseEmitterOperate.isComplete()) {
            return;
        } else if (isFirst) {
            log.info("【流式对话】{}对话id：{}，从端侧请求到流式输出首token耗时：{}ms", logName, dialogId, (System.currentTimeMillis() - requestTime));
        }

        sseEmitterOperate.send(JSONUtil.toJsonStr(resp));

        if (!result.isSuccess()) {
            log.warn("【流式对话】{}对话失败，输出结果信息：{}", logName, JsonUtil.toJson(result));
        }
    }

    private <T extends ChatAddRespVO> void send(BaseResult<T> result) {
        sendIndex++;

        // 设置公共参数
        DialogueFlowResultVO flowResultVO = result.getData().getFlowResult();
        flowResultVO.setModelType(modelCode);
        flowResultVO.setIndex(sendIndex);

        // 先保存redis
        redisOperateRepository.setFlowResult(dialogId, sendIndex, JsonUtil.toJson(result));

        // 调用send发送消息
        if (sseEmitterOperate.isComplete()) {
            return;
        } else if (isFirst) {
            log.info("【流式对话】{}对话id：{}，从端侧请求到流式输出首token耗时：{}ms", logName, dialogId, (System.currentTimeMillis() - requestTime));
        }

        sseEmitterOperate.send(result);

        if (!result.isSuccess()) {
            log.warn("【流式对话】{}对话失败，输出结果信息：{}", logName, JsonUtil.toJson(result));
        }
    }

    private void complete() {

        // 存在回调事件
        if (hasCompleteCallbackEvent()) {
            return;
        }

        // 已经关闭
        if (sseEmitterOperate.isComplete()) {
            return;
        }

        // 关闭连接
        log.info("【流式对话】{}关闭流对象，对话id：{}，从端侧请求到流式输出完成耗时：{}ms", logName, dialogId, (System.currentTimeMillis() - requestTime));
        sseEmitterOperate.complete();
    }

    /**
     * 处理a标签
     *
     * @param lastEvent 是否是最后一个事件
     * @return boolean true-继续读取，false-结束读取
     */
    private boolean aTagHandle(boolean lastEvent) {
        if (lastEvent) {
            return false;
        }
        if (!aTagStart && addMsg.toString().toLowerCase().contains(A_TAG_START)) {
            // 存在a标签开始
            aTagStart = true;
        }
        if (aTagStart && addMsg.toString().toLowerCase().contains(A_TAG_END)) {
            // 存在a标签结束
            aTagEnd = true;
        }
        if (aTagStart && !aTagEnd) {
            // 存在a标签未结束，继续读取
            return true;
        }
        aTagStart = false;
        aTagEnd = false;

        return false;
    }

    /**
     * 文本审核
     *
     * @param checkAll 是否审核全部，true-审核全部，false-审核局部
     * @return CheckResultVO
     */
    private CheckResultVO checkText(Boolean checkAll) {
        CheckTypeEnum checkType;
        if (Boolean.TRUE.equals(checkAll)) {
            checkType = CheckTypeEnum.API;
        } else {
            checkType = CheckSystemConfig.isAuditLocalPart(checkSystemTextModel.getType()) ? CheckTypeEnum.LOCAL : CheckTypeEnum.API;
        }
        CheckTextReqDTO reqDTO = new CheckTextReqDTO(String.valueOf(dialogId), userId, String.valueOf(allThinkMsg) + allMsg);
        return checkSystemDomainService.checkSystemCheckText(checkSystemTextModel, checkType, reqDTO);
    }

    private List<HtmlInfo> networkSearchInfoHandle(List<TextModelBaseVo.NetworkSearchInfoVo> searchInfoList) {
        if (!outputNetworkSearchInfo) {
            return null;
        } else if (onlySaveNetworkSearchInfo) {
            return null;
        }
        if (ObjectUtil.isNotEmpty(networkSearchInfoList)) {
            return networkSearchInfoList;
        }
        if (ObjectUtil.isEmpty(searchInfoList)) {
            return null;
        }

        networkSearchInfoList = searchInfoList.stream().map(HtmlInfo::new).collect(Collectors.toList());
        return networkSearchInfoList;
    }

    /**
     * 检查输出字数是否达到送审字数
     *
     * @return true-未达到送审字数，false-达到送审字数
     */
    private boolean unReachedCheckSize() {
        String str = addThinkMsg.toString() + addMsg;
        // 过滤掉换行符再计算字数
        int length = str.replaceAll("\\n", "")
                .replaceAll(" ", "")
                .length();
        return length < checkSystemTextModel.getOutputSize();
    }

    /**
     * 是否有回调事件
     */
    private boolean hasCompleteCallbackEvent() {
        return !Objects.isNull(this.completeCallbackEvent);
    }

}
