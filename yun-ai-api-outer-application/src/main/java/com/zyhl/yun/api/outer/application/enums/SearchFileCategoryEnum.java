package com.zyhl.yun.api.outer.application.enums;

/**
 * <AUTHOR>
 * @Classname NoteTypeEnum
 * @Description 笔记类型
 * @Date 2024/3/1 13:36
 */
public enum SearchFileCategoryEnum {

    /**
     * 其他类型
     */
    OTHERS("0", "others"),

    /**
     * 图片类型
     */
    IMAGE("1", "image"),

    /**
     * 音频类型
     */
    AUDIO("2", "audio"),

    /**
     * 视频类型
     */
    VIDEO("3", "video"),

    /**
     * 文档类型
     */
    DOC("4", "doc"),

    /**
     * 应用类型
     */
    APP("5", "app"),

    /**
     * 压缩包类型
     */
    ZIP("6", "zip"),

    /**
     * 文件夹类型
     */
    FOLDER("100", "folder");



    ;

    private final String type;

    private final String name;

    SearchFileCategoryEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static String getNameByType(String type){
        for (SearchFileCategoryEnum value : values()) {
            if (value.type.equals(type)){
                return value.getName();
            }
        }
        return null;
    }
}
