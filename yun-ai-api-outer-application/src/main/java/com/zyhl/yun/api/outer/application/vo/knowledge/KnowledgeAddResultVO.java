package com.zyhl.yun.api.outer.application.vo.knowledge;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 个人知识库文件添加结果
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class KnowledgeAddResultVO {

    /**
     * 文件id
     */
    private String fileId;

    /**
     * 添加结果
     * 0000--添加到知识库成功
     * 9999--未知错误
     * 10000009--文档格式错误
     * 10000006--文件上传过大
     * 10030401--文件已在知识库中
     */
    private String result;

    /**
     * 结果描述
     */
    private String description;

    /**
     * 表algorithm_user_knowledge_file的id
     */
    private Long knowledgeFileId;

    public KnowledgeAddResultVO(String fileId, AbstractResultCode resultCode) {
        this.fileId = fileId;
        this.result = resultCode.getResultCode();
        this.description = resultCode.getResultMsg();
    }

    public KnowledgeAddResultVO(String fileId, String result, String description) {
        this.fileId = fileId;
        this.result = result;
        this.description = description;
    }


    public void setMsg(String msg) {
        if (ObjectUtil.isNotEmpty(msg)) {
            this.description = msg;
        }
    }

}