package com.zyhl.yun.api.outer.application.chatv2.vo;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum;
import com.zyhl.yun.api.outer.domain.valueobject.AiFunctionResult;
import com.zyhl.yun.api.outer.domain.valueobject.AlbumInfo;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.valueobject.HtmlInfo;
import com.zyhl.yun.api.outer.domain.valueobject.KnowledgeSearchInfo;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.domain.vo.chat.search.SearchInfo;
import com.zyhl.yun.api.outer.enums.ModelTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddFlowStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatEventCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatMiddleCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.OutAuditStatusEnum;

import cn.hutool.core.date.DatePattern;
import com.zyhl.yun.api.outer.enums.knowledge.AiExpansionInteractiveEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 对话流式结果对象
 * 
 * <AUTHOR>
 * @date 2025-04-12
 */
@Data
@NoArgsConstructor
public class DialogueFlowResultVO {

	/**
	 * 流式编码
	 */
	private int index;

	/**
	 * 结果类型
	 *
	 * @see com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum
	 */
	private Integer resultType = FlowResultTypeEnum.TEXT_MODEL.getType();

	/**
	 * 中间处理状态码，正常回答时为空，不为空时，展示同时的title
	 *
	 * @see com.zyhl.yun.api.outer.enums.chat.ChatMiddleCodeEnum
	 */
	private String middleCode;

	/**
	 * 事件码，前端遇到这个字段时，要根据对应的事件进行响应的处理
	 *
	 * @see com.zyhl.yun.api.outer.enums.chat.ChatEventCodeEnum
	 */
	private String eventCode;

	/**
	 * 每个模块流式回答的标题
	 */
	private String title;

	/**
	 * 输出文本，流式增量出 例如： 第一句：你好， 第二句：欢迎 第三句：来到中国。
	 */
	private String outContent = "";

	/**
	 * 思维链过程
	 */
	private String reasoningContent = "";

	/**
	 * 个人知识库参考文件，可选。
	 */
	private List<KnowledgeSearchInfo> personalKnowledgeFileList;

	/**
	 * 大模型联网搜索结果，可选。
	 */
	private List<HtmlInfo> networkSearchInfoList;

	/**
	 * 云盘搜索信息列表
	 */
	private List<SearchInfo> searchInfoList;

	/**
	 * 个人云文件信息列表
	 */
	private List<File> fileList;

	/**
	 * 相册信息列表
	 */
	private List<AlbumInfo> albumList;

	/**
	 * 4.8 modelType枚举类中的编码值，如果没有使用大模型，则返回空
	 */
	private String modelType;

	/**
	 * 结果返回数量（当前给异步任务使用）
	 */
	private Integer resultCount;

	/**
	 * 邮件信息
	 */
	private MailInfoVO mailInfo;

	/**
	 * 完成原因，默认stop "processing"：正在处理 "stop"：结束，当前部分的流式结束标识，表示这个部分的输出已结束
	 * "error"：异常，当前部分的流式结束标识，表示这个部分的输出已结束
	 *
	 * @see ChatAddFlowStatusEnum
	 */
	private String finishReason = ChatAddFlowStatusEnum.STOP.getStatus();

	/**
	 * 输出时间，RFC 3339格式(东八区)，注： 2019-10-12T14:20:50.52+08:00
	 */
	@JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
	private Date outputTime = new Date();

	/**
	 * 输出内容审批结果;状态码：2通过，其他失败
	 *
	 * @see OutAuditStatusEnum
	 */
	private Integer outAuditStatus = OutAuditStatusEnum.SUCCESS.getCode();

	/**
	 * 错误码，该部分处理失败时返回
	 */
	private String errorCode = ResultCodeEnum.SUCCESS.getResultCode();

	/**
	 * 错误信息，该部分处理失败时返回
	 */
	private String errorMessage = ResultCodeEnum.SUCCESS.getResultMsg();

	/**
	 * AI工具结果
	 */
	private AiFunctionResult aiFunctionResult;

	public DialogueFlowResultVO(String outContent, String reasoningContent,
			ChatAddFlowStatusEnum chatAddFlowStatusEnum) {
		this.outContent = outContent;
		this.reasoningContent = reasoningContent;
		if (null != chatAddFlowStatusEnum) {
			this.finishReason = chatAddFlowStatusEnum.getStatus();
		}
	}

	public DialogueFlowResultVO(ChatAddFlowStatusEnum chatAddFlowStatusEnum) {
		if (null != chatAddFlowStatusEnum) {
			this.finishReason = chatAddFlowStatusEnum.getStatus();
		}
	}

	public DialogueFlowResultVO(int index, FlowResultTypeEnum resultTypeEnum, ChatMiddleCodeEnum middleCodeEnum) {
		this.index = index;
		if (null != resultTypeEnum) {
			this.resultType = resultTypeEnum.getType();
		}
		this.finishReason = ChatAddFlowStatusEnum.PROCESSING.getStatus();
		this.middleCode = middleCodeEnum.getCode();
		this.title = middleCodeEnum.getDesc();
	}

	public DialogueFlowResultVO(ChatMiddleCodeEnum middleCodeEnum, String modelType) {
		this.finishReason = ChatAddFlowStatusEnum.PROCESSING.getStatus();
		this.middleCode = middleCodeEnum.getCode();
		this.title = middleCodeEnum.getDesc();
		this.modelType = modelType;
	}

	public DialogueFlowResultVO(AiExpansionInteractiveEnum interactionEnum, String modelType) {
		this.finishReason = ChatAddFlowStatusEnum.PROCESSING.getStatus();
		if (null == interactionEnum) {
			return;
		}

		if (TextModelEnum.BLIAN_QWEN3_235B.getCode().equals(modelType)) {
			interactionEnum = AiExpansionInteractiveEnum.MODEL_SELECTED;
		}

		this.modelType = TextModelEnum.BLIAN_QWEN3_235B.getCode();
		if (null != interactionEnum.getTitle()) {
			this.title = interactionEnum.getTitle();
		}

		if (null != interactionEnum.getEventCode()) {
			this.eventCode = interactionEnum.getEventCode();
		}

		if (null != interactionEnum.getOutContent()) {
			this.outContent = interactionEnum.getOutContent();
		}

		if (null != interactionEnum.getResultType()) {
			this.resultType = interactionEnum.getResultType();
		}
	}

	public DialogueFlowResultVO(AiExpansionInteractiveEnum interactionEnum) {
		this.finishReason = ChatAddFlowStatusEnum.PROCESSING.getStatus();
		if (null == interactionEnum) {
			return;
		}

		if (null != interactionEnum.getTitle()) {
			this.title = interactionEnum.getTitle();
		}

		if (null != interactionEnum.getEventCode()) {
			this.eventCode = interactionEnum.getEventCode();
		}

		if (null != interactionEnum.getOutContent()) {
			this.outContent = interactionEnum.getOutContent();
		}

		if (null != interactionEnum.getResultType()) {
			this.resultType = interactionEnum.getResultType();
		}
	}

	public DialogueFlowResultVO(int index, FlowResultTypeEnum resultTypeEnum, String title, String outContent) {
		if (null != resultTypeEnum) {
			this.resultType = resultTypeEnum.getType();
		}
		this.index = index;
		this.title = title;
		this.outContent = outContent;
		this.finishReason = ChatAddFlowStatusEnum.STOP.getStatus();
	}

	public DialogueFlowResultVO(int index, FlowResultTypeEnum resultTypeEnum, String title, String outContent,
			String errorCode, String errorMessage) {
		if (null != resultTypeEnum) {
			this.resultType = resultTypeEnum.getType();
		}
		this.index = index;
		this.title = title;
		this.outContent = outContent;
		this.reasoningContent = null;
		this.errorCode = errorCode;
		this.errorMessage = errorMessage;
		this.finishReason = ChatAddFlowStatusEnum.STOP.getStatus();
	}

	public DialogueFlowResultVO(ChatEventCodeEnum entCodeEnum, String modelType) {
		this.finishReason = ChatAddFlowStatusEnum.PROCESSING.getStatus();
		this.eventCode = entCodeEnum.getCode();
		this.title = entCodeEnum.getDesc();
		this.modelType = modelType;
	}

	public DialogueFlowResultVO(List<KnowledgeSearchInfo> personalKnowledgeFileList, String modelType) {
		this.finishReason = ChatAddFlowStatusEnum.PROCESSING.getStatus();
		this.personalKnowledgeFileList = personalKnowledgeFileList;
		this.modelType = modelType;
	}

	public DialogueFlowResultVO(ChatAddFlowStatusEnum chatAddFlowStatusEnum, int index) {
		if (null != chatAddFlowStatusEnum) {
			this.finishReason = chatAddFlowStatusEnum.getStatus();
		}
		this.index = index;
	}

	public DialogueFlowResultVO(List<HtmlInfo> networkSearchInfoList, String modelType, Integer resultType) {
		this.finishReason = ChatAddFlowStatusEnum.PROCESSING.getStatus();
		this.networkSearchInfoList = networkSearchInfoList;
		this.modelType = modelType;
		this.resultType = resultType;
	}
}