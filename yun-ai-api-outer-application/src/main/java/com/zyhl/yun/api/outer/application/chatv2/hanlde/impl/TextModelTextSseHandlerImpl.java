package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.service.BenefitService;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatAddCheckService;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatCommonService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.dto.ContinueTextSseDTO;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.config.MailAiPromptProperties;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.config.textmodel.CloudPhonePromptConfig;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.domainservice.QpsLimitService;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.SseNameEnum;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 文本流式对话
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TextModelTextSseHandlerImpl extends AbstractChatAddV2Handler {

    /**
     * 当前执行顺序
     */
    private final ExecuteSort thisExecuteSort = ExecuteSort.TEXT_MODE_TEXT_SSE;

    @Resource
    private BenefitService benefitService;
    @Resource
    private DataSaveService dataSaveService;
    @Resource
    private ChatConfigServiceDomainService chatConfigServiceDomainService;
    @Resource
    private QpsLimitService qpslimitService;
    @Resource
    private ModelProperties modelProperties;
    @Resource
    private AlgorithmChatContentRepository algorithmChatContentRepository;
    @Resource
    private TextModelExternalService textModelExternalService;
    @Resource
    private AiTextResultRepository aiTextResultRepository;
    @Resource
    private ChatAddCheckService chatAddCheckService;
    @Resource
    private ChatCommonService chatCommonService;
    @Resource
    private MailAiPromptProperties mailAiPromptProperties;
    @Resource
    private CloudPhonePromptConfig cloudPhonePromptConfig;

    @Override
    public void afterPropertiesSet() throws Exception {
        //支持的业务初始化
        List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        thisBusinessTypes.add(ChatBusinessTypeEnum.CLOUD_PHONE);
        thisBusinessTypes.add(ChatBusinessTypeEnum.MESSAGE_5G);
        thisBusinessTypes.add(ChatBusinessTypeEnum.NOTE);
        this.setBusinessTypes(thisBusinessTypes);
    }

    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public boolean execute(ChatAddHandleDTO handleDTO) {
        return DialogueIntentionEnum.isTextIntention(handleDTO.getIntentionCode());
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        log.info("进入{}", thisExecuteSort.getDesc());
        DialogueInputInfoDTO inputInfoDTO = handleDTO.getInputInfoDTO();

        // 扣减权益
        benefitService.consumeBenefit(handleDTO.getReqDTO(), RequestContextHolder.getPhoneNumber(), handleDTO.getDialogueId());

        // 保存到hbase
        dataSaveService.saveTextResult(handleDTO, "", "");

        // 保存数据库
        dataSaveService.add(handleDTO, ChatStatusEnum.CHAT_IN);

        // 历史对话
        List<TextModelMessageDTO> historyList = aiTextResultRepository.getHistoryList(handleDTO.getReqDTO().getUserId(), handleDTO.getSessionId());

        // 监听器
        SseEventListener event = new SseEventListener(handleDTO, historyList);
        event.getSseEmitterOperate().setSseName(SseNameEnum.SIMPLE_SSE.getCode());

        // AI搜索，回复指定模型
        ContinueTextSseDTO continueTextSseDTO = handleDTO.getContinueTextSseDTO();
        if (continueTextSseDTO.isContinueTextSseHandler()) {
            log.info("【普通文本对话】AI搜索，继续执行大模型回答DTO:{}", JSONUtil.toJsonStr(continueTextSseDTO));
            // 强制联网搜
            event.getTextDto().setEnableForceNetworkSearch(continueTextSseDTO.getEnableForceNetworkSearch());
            event.getSseEmitterOperate().setSseName(SseNameEnum.AI_SEARCH_SSE.getCode());
            event.setModelCode(continueTextSseDTO.getModelCode());
            event.setEnding(handleDTO.getEnding());
            simpleDialogue(event, handleDTO);
            return false;
        }

        //邮件AI总结、回复指定模型
        MailAiPromptProperties.MailAiPrompt promptProperties = mailAiPromptProperties.getByPromptKeyAndChannel(inputInfoDTO.getPrompt(), handleDTO.getReqDTO().getSourceChannel());
        if (ObjectUtil.isNotEmpty(promptProperties) && promptProperties.isEnable()) {
            event.getSseEmitterOperate().setSseName(SseNameEnum.SIMPLE_SSE_MAIL.getCode());
            log.info("【普通文本对话】邮件AI总结、回复功能，配置promptProperties:{}", JSONUtil.toJsonStr(promptProperties));
            event.setModelCode(promptProperties.getModelCode());
            simpleDialogue(event, handleDTO);
            return false;
        }

        // 获取用户设置的模型，没有设置则使用默认模型
        ChatConfigEntity chatConfigEntity = chatConfigServiceDomainService.getUserCanUseModel(handleDTO.getReqDTO().getUserId(),
                RequestContextHolder.getPhoneNumber(), handleDTO.getAssistantEnum(), handleDTO.getBusinessType());

        // 普通对话
        event.setModelCode(chatConfigEntity.getModelType());
        event.setEnding(handleDTO.getEnding());
        simpleDialogue(event, handleDTO);

        return false;
    }

    /**
     * 普通对话，指定模型
     *
     * @param event 流式监听事件
     */
    public void simpleDialogue(SseEventListener event, ChatAddHandleDTO handleDTO) {

        // qps限制
        if (!qpslimitService.modelQpsLimit(event.getModelCode())) {
            log.info("【普通文本对话】请求过多，qps限流，model:{}", event.getModelCode());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_LIMITATION);
        }

        // 输入字数判断
        Integer lengthLimit = modelProperties.getLengthLimit(handleDTO.getAssistantEnum(), handleDTO.getBusinessType(), event.getModelCode());
        if (lengthLimit != null && handleDTO.getInputInfoDTO().getDialogue().length() > lengthLimit) {
            throw new YunAiBusinessException(ResultCodeEnum.CONTENT_EXCEEDS_LIMIT);
        }

        // 从配置获取提示词
        String newPrompt = chatCommonService.getPromptByConfig(event);
        // 从db获取提示词
        String finalPrompt = chatAddCheckService.getDialoguePrompt(newPrompt,handleDTO.getReqDTO().getSourceChannel());
        // 追加重新生成提示词
        finalPrompt = chatAddCheckService.getAppendEnableRegenerate(handleDTO.getInputInfoDTO(), finalPrompt);
        // 重新设置最后匹配的提示词
        event.getTextDto().setPrompt(finalPrompt);

        // 文本模型
        Integer maxLength = modelProperties.getMaxLength(handleDTO.getAssistantEnum(), handleDTO.getBusinessType(), event.getModelCode());
        TextModelTextReqDTO reqDTO = event.getTextDto().toTextReqDTO(maxLength);
        boolean cloudPhoneChannel = SourceChannelsProperties.isCloudPhoneChannel(handleDTO.getReqDTO().getSourceChannel());
        if (Boolean.TRUE.equals(cloudPhoneChannel)) {
            reqDTO.setDefaultSystemRole(cloudPhonePromptConfig.getSystemPrompt());
        }
        //设置角标启用按界面版本控制
        reqDTO.setEnableNetworkSearchCitation(true);
        log.info("【普通文本对话】指定文本模型对话 model:{}, reqDTO:{}", event.getModelCode(), JSONUtil.toJsonStr(reqDTO));
        textModelExternalService.streamDialogue(event.getModelCode(), reqDTO, event);

        // 更新模型编码
        algorithmChatContentRepository.updateModelCode(event.getDialogId(), event.getModelCode());
    }


}
