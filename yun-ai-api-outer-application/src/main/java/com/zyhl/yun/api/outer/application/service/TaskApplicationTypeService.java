package com.zyhl.yun.api.outer.application.service;

import java.util.List;

import com.zyhl.yun.api.outer.application.chatv2.pojo.TaskPromptOfChatInfo;
import com.zyhl.yun.api.outer.application.dto.ReadTaskDTO;
import com.zyhl.yun.api.outer.application.dto.ReadTaskReqDTO;
import com.zyhl.yun.api.outer.application.dto.SpeedReadResultVO;
import com.zyhl.yun.api.outer.application.vo.SpeedReadTaskVO;
import com.zyhl.yun.api.outer.domain.vo.common.PageInfoVO;

/**
 * 快速阅读服务接口
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
public interface TaskApplicationTypeService {

	/**
	 * 创建快速阅读任务
	 * 
	 * @param dto 入参
	 * @return 响应对象
	 */
	SpeedReadTaskVO createReadTask(ReadTaskDTO dto);

	/**
	 * 快速阅读列表
	 * 
	 * @param dto 入参
	 * @return 响应对象列表
	 */
	PageInfoVO<SpeedReadTaskVO> readTaskList(ReadTaskReqDTO dto);

	/**
	 * 快速阅读删除
	 * 
	 * @param dto 入参
	 * @return 是否删除
	 */
	Boolean readTaskDelete(ReadTaskDTO dto);

	/**
	 * 快速阅读获取
	 * 
	 * @param dto 入参
	 * @return 响应对象
	 */
	SpeedReadResultVO readTaskGet(ReadTaskDTO dto);

	/**
	 * 获取全文数据（userId+taskId）
	 * 
	 * @param dto 入参
	 * @return 获取全文数据
	 */
	String getSpeedReadFullText(ReadTaskDTO dto);

	/**
	 * 更新hbase结果数据
	 * 
	 * @param userId                用户id
	 * @param taskId                任务id
	 * @param taskPromptOfChatInfos 任务提示信息
	 */
	void updateSpeedReadHbaseResult(String userId, String taskId, List<TaskPromptOfChatInfo> taskPromptOfChatInfos);
}
