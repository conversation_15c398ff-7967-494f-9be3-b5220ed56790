package com.zyhl.yun.api.outer.application.chatv2.service;

import com.zyhl.yun.api.outer.application.chatv2.dto.AlgorithmChatV2ListDTO;
import com.zyhl.yun.api.outer.application.chatv2.vo.MessageResultV2VO;
import com.zyhl.yun.api.outer.domain.vo.common.PageInfoVO;

/**
 * 会话-service
 * @Author: WeiJingKun
 */
public interface AlgorithmChatV2MessageService {

    /**
     * 历史会话列表查询
     *
     * @param dto 请求参数
     * @return 分页结果
     */
    PageInfoVO<MessageResultV2VO> chatList(AlgorithmChatV2ListDTO dto);

}
