package com.zyhl.yun.api.outer.application.service.impl;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.hbase.domain.datahelper.repository.HbaseRepository;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeHtmlPreviewReqDTO;
import com.zyhl.yun.api.outer.application.service.KnowledgeFilePreviewService;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeInviteEntity;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmRagTextContentPO;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeInviteRepository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 知识库预览服务实现
 * 
 * <AUTHOR>
 * @date 2025-04-19
 */
@Service
@Slf4j
public class KnowledgeFilePreviewServiceImpl implements KnowledgeFilePreviewService {

    @Resource
    private HbaseRepository hbaseRepository;

    @Resource
    private UserKnowledgeFileRepository userKnowledgeFileRepository;

    @Resource
    private UserKnowledgeInviteRepository userKnowledgeInviteRepository;



    @Override
    public String htmlPreview(KnowledgeHtmlPreviewReqDTO dto) {
        String resourceId = dto.getResourceId();
        String baseId = dto.getBaseId();
        String userId = dto.getUserId();
        // 1. 查询个人知识库文件资源表，验证基础资源存在性
        UserKnowledgeFileEntity userKnowledgeFileEntity = userKnowledgeFileRepository.getOne(baseId, resourceId,KnowledgeResourceTypeEnum.HTML.getCode());
        if (ObjectUtil.isNull(userKnowledgeFileEntity)) {
            log.warn("未找到对应的知识库文件资源，baseId: {}, resourceId: {}", baseId, resourceId);
            return null;
        }
        if (!userId.equals(userKnowledgeFileEntity.getUserId())){
            List<UserKnowledgeInviteEntity> userKnowledgeInviteEntities = userKnowledgeInviteRepository.get(Long.valueOf(baseId), userId);
            if (CollUtil.isEmpty(userKnowledgeInviteEntities)){
                throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_NO_PERMISSION);
            }
        }
        String rowkey = userKnowledgeFileEntity.getUserId() + "_" + userKnowledgeFileEntity.getFileId()+ "_" + KnowledgeResourceTypeEnum.HTML.getCode();
        List<AlgorithmRagTextContentPO> algorithmRagTextContentPOS = hbaseRepository.selectList(Arrays.asList(rowkey), AlgorithmRagTextContentPO.class);
        if (CollUtil.isNotEmpty(algorithmRagTextContentPOS)){
            //获取hbase里面的html内容
            return algorithmRagTextContentPOS.get(0).getContent();
        }else {
            log.warn("hbase没获取到html内容，baseId: {}, resourceId: {}", baseId, resourceId);
        }
        return null;
    }
}
