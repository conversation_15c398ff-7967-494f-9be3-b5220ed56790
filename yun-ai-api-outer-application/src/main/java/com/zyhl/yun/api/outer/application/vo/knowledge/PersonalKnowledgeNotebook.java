package com.zyhl.yun.api.outer.application.vo.knowledge;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/18 11:47
 */
@Data
public class PersonalKnowledgeNotebook implements Serializable {

    /**
     * 笔记本ID
     */
    private String tagId;
    /**
     * 笔记本名称
     */
    private String text;
    /**
     * 笔记本类型, [0-普通笔记本, 1-默认笔记本]，默认值 0 [普通笔记本]
     */
    private Integer isDefault;
    /**
     * 排序下标
     */
    private Integer orderIndex;

}
