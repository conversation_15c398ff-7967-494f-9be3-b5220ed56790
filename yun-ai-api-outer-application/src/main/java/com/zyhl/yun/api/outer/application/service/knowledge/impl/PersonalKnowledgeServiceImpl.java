package com.zyhl.yun.api.outer.application.service.knowledge.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.enums.NumberEnum;
import com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum;
import com.zyhl.hcy.yun.ai.common.base.enums.RenameModeEnum;
import com.zyhl.hcy.yun.ai.common.base.es.domain.datahelper.repository.EsPersonalKnowledgeRepository;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.hbase.domain.datahelper.repository.HbaseRepository;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.dto.CheckTextReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.enums.CheckSystemAuditTypeEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.vo.CheckResultVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.vo.RagMailDetailVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.notethird.vo.NoteContentVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.notethird.vo.NoteExpandVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.notethird.vo.NoteThirdListVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.enums.OwnerDriveTaskStatusEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.properties.OwnerDriveProperties;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDriveFileUpdateReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDriveReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveTaskResultVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yunuser.YunUserClient;
import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.PesonalKnowledgeConvertor;
import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.*;
import com.zyhl.yun.api.outer.application.service.knowledge.PersonalKnowledgeService;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeBase;
import com.zyhl.yun.api.outer.config.FileCheckConfig;
import com.zyhl.yun.api.outer.config.KnowledgePersonalProperties;
import com.zyhl.yun.api.outer.domain.dto.CatalogConfigDTO;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.*;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeCountVO;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeProfilePhoto;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeProfilePhotoVO;
import com.zyhl.yun.api.outer.domainservice.CheckSystemDomainService;
import com.zyhl.yun.api.outer.domainservice.PersonalKnowledgeDomainService;
import com.zyhl.yun.api.outer.domainservice.UserDriveConfigDomainService;
import com.zyhl.yun.api.outer.enums.ContentTypeEnum;
import com.zyhl.yun.api.outer.enums.NoteTypeEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.*;
import com.zyhl.yun.api.outer.external.CheckSystemExternalService;
import com.zyhl.yun.api.outer.external.UserDriveExternalService;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmRagTextContentPO;
import com.zyhl.yun.api.outer.repository.*;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 个人知识库业务实现
 *
 * <AUTHOR>
 * @date 2025/04/14
 */
@Slf4j
@Service
public class PersonalKnowledgeServiceImpl implements PersonalKnowledgeService {

    @Resource
    private UserKnowledgeRepository userKnowledgeRepository;

    @Resource
    private PesonalKnowledgeConvertor convertor;

    @Resource
    private UserKnowledgeInviteRepository userKnowledgeInviteRepository;

    @Resource
    private UserKnowledgeFileRepository userKnowledgeFileRepository;

    @Resource
    private UserKnowledgeFileTaskRepository userKnowledgeFileTaskRepository;

    @Resource
    private UserKnowledgeFileResRepository userKnowledgeFileResRepository;

    @Resource
    private UidGenerator uidGenerator;

    @Resource
    private UserDriveConfigDomainService userDriveConfigDomainService;

    @Resource
    private PesonalKnowledgeConvertor pesonalKnowledgeConvertor;

    @Resource
    private OwnerDriveProperties ownerDriveProperties;

    @Resource
    private HbaseRepository hbaseRepository;

    @Resource
    private KnowledgePersonalProperties knowledgePersonalProperties;

    @Resource
    private PersonalKnowledgeDomainService personalKnowledgeDomainService;

    @Resource
    private UserDriveExternalService userDriveExternalService;

    @Resource(name = "businessThreadPool")
    private ExecutorService businessThreadPool;
    @Resource(name = "knowledgeDeleteThreadPool")
    private ExecutorService knowledgeDeleteThreadPool;

    private static final String CONNECT_SPLIT = "_";

    @Resource
    private CheckSystemExternalService checkSystemExternalService;

    @Resource
    private CheckSystemDomainService checkSystemDomainService;

    @Resource
    private FileCheckConfig fileCheckConfig;

    @Resource
    private YunUserClient yunUserClient;

    @Resource
    private EsPersonalKnowledgeRepository esPersonalKnowledgeRepository;

    @Override
    public KnowledgeBase add(PersonalKnowledgeAddReqDTO dto) {
        // 当创建的知识库属于唯一有用的时候，需要去创建独立空间，查询知识库表，没有记录则去创建独立空间
        String userId = dto.getUserId();
        int count = userKnowledgeRepository.countByUserId(userId);
        if (count >= knowledgePersonalProperties.getCreateMaxNum()) {
            log.info("个人知识库已有数量：{}", count);
            String msg = String.format("知识库数量超过上限，最多创建%d个知识库", knowledgePersonalProperties.getCreateMaxNum());
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_OUT_NUMBER.getResultCode(), msg);
        }

        // 知识库名称和描述送审
        checkContentName(dto.getName());
        if (ObjectUtil.isNotEmpty(dto.getDescription())) {
            checkContentDescription(dto.getDescription());
        }

        if (count < NumberEnum.ONE.getValue()) {
            Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();
            // 创建独立空间并保存目录
            userDriveConfigDomainService.createDrive(userId, belongsPlatform);
        }
        log.info("创建个人知识库入参：{}", JsonUtil.toJson(dto));
        UserKnowledgeEntity entity = assembleBySave(dto);
        userKnowledgeRepository.add(entity);
        KnowledgeBase knowledgeBase = pesonalKnowledgeConvertor.toKnowledgeBase(entity);
        // 初次创建的知识库资源数为0
        knowledgeBase.setTotalCount(0);
        knowledgeBase.setParsedCount(0);
        knowledgeBase.setUnparsedCount(0);
        // 初次创建知识库时将类型置为个人知识库
        knowledgeBase.setBaseType(KnowledgeBaseTypeEnum.PERSONAL.getCode());
        knowledgeBase.setOpenLevel(KnowledgeStatusEnum.PRIVATE.getStatus());
        return knowledgeBase;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public KnowledgeBase update(PersonalKnowledgeUpdateReqDTO dto) {
        // 先根据知识库ID判断该知识库是否存在且是否属于该用户
        String userId = dto.getUserId();
        UserKnowledgeEntity entity = userKnowledgeRepository.selectByIdAndUserId(Long.valueOf(dto.getBaseId()), userId);
        if (null == entity) {
            log.info("update-知识库ID不存在，baseId:{}", dto.getBaseId());
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_ID_NOT_EXIST.getResultCode(),
                    "知识库ID不存在");
        }
        log.info("更改个人知识库入参：{}", JsonUtil.toJson(dto));

        // 知识库名称和描述送审
        if (ObjectUtil.isNotEmpty(dto.getName())) {
            checkContentName(dto.getName());
        }
        if (ObjectUtil.isNotEmpty(dto.getDescription())) {
            checkContentDescription(dto.getDescription());
        }

        // 更新知识库和更新独立空间目录
        userKnowledgeRepository.update(assembleByUpdate(dto, entity));

        // 将知识库设为私密后，已加入的成员将自动被移出
        if (KnowledgeStatusEnum.PRIVATE.getStatus().equals(dto.getOpenLevel())) {
            userKnowledgeInviteRepository.updateStatus(entity.getId());
        }

        KnowledgeBase knowledgeBase = pesonalKnowledgeConvertor.toKnowledgeBase(entity);
        // 文件统计
        List<UserKnowledgeFileStatisticsEntity> statisticsList = userKnowledgeFileRepository.findStatisticsByBaseId(Collections.singletonList(entity.getId()));
        if (statisticsList != null && !statisticsList.isEmpty()) {
            knowledgeBase.setTotalCount(statisticsList.get(0).getTotalCount());
            knowledgeBase.setParsedCount(statisticsList.get(0).getProcessed());
            knowledgeBase.setUnparsedCount(statisticsList.get(0).getUntreated());
        } else {
            knowledgeBase.setTotalCount(0);
            knowledgeBase.setParsedCount(0);
            knowledgeBase.setUnparsedCount(0);
        }
        // 确定该知识库的类型
        if (userKnowledgeInviteRepository.countByKnowledgeId(entity.getId()) > NumberEnum.ZERO.getValue()) {
            knowledgeBase.setBaseType(KnowledgeBaseTypeEnum.SHARE.getCode());
        } else if (userKnowledgeRepository.count(entity.getId(), userId) > NumberEnum.ZERO.getValue()) {
            knowledgeBase.setBaseType(KnowledgeBaseTypeEnum.PERSONAL.getCode());
        }
        // 知识库改为公开，邮件、笔记正文送审、邮件、笔记、个人云附件送审
        asyncFileCheck(dto, entity);
        return knowledgeBase;
    }

    public CompletableFuture<Void> asyncFileCheck(PersonalKnowledgeUpdateReqDTO dto, UserKnowledgeEntity entity) {
        return CompletableFuture.runAsync(() -> {
            try {
                fileCheck(dto, entity);
            } catch (Exception e) {
                log.error("个人知识库正文、文件送审异常", e);
            }
        });
    }

    /**
     * 正文、文件送审
     *
     * @param dto    请求参数
     * @param entity 知识库实体
     */
    public void fileCheck(PersonalKnowledgeUpdateReqDTO dto, UserKnowledgeEntity entity) {
        if (KnowledgeStatusEnum.PRIVATE.getStatus().equals(dto.getOpenLevel())) {
            return;
        }
        // 查询笔记、邮件、个人云文件
        List<Integer> resourceTypeList = Arrays.asList(KnowledgeResourceTypeEnum.MAIL.getCode(), KnowledgeResourceTypeEnum.NOTE.getCode(), KnowledgeResourceTypeEnum.PERSONAL_FILE.getCode());
        List<UserKnowledgeFileEntity> files = userKnowledgeFileRepository.selectByBaseIdAndResourceType(String.valueOf(entity.getId()), resourceTypeList);
        if (CollectionUtils.isEmpty(files)) {
            log.warn("知识库下无笔记、邮件、个人云文件,baseId:{}", entity.getId());
            return;
        }
        // 送审开关
        Boolean enabled = fileCheckConfig.isEnabled();
        log.info("知识库改为公开，baseId:{}，fileCheckConfig:{}，size:{}", entity.getId(), fileCheckConfig, files.size());
        for (UserKnowledgeFileEntity fileEntity : files) {
            // 个人云文件异步送审
            if (KnowledgeResourceTypeEnum.PERSONAL_FILE.getCode().equals(fileEntity.getFromResourceType())
                    && FileCategoryEnum.IMAGE.getKnowledgeCategory().equals(fileEntity.getCategory())) {
                personalFileCheck(enabled, fileEntity);
            } else if (KnowledgeResourceTypeEnum.NOTE.getCode().equals(fileEntity.getFromResourceType())
                    || KnowledgeResourceTypeEnum.MAIL.getCode().equals(fileEntity.getFromResourceType())) {
                /*// 邮件、笔记，正文先同步送审，后异步送审附件
                String content = getContent(fileEntity);
                // 正文送审
                if(Boolean.FALSE.equals(contentCheck(content, fileEntity))){
                    // 正文送审失败更新file表
                    userKnowledgeFileRepository.update(fileEntity);
                    // 遍历es的分片,修改送审状态
                    esPersonalKnowledgeRepository.batchUpdateAuditStatus(fileEntity.getUserId(), fileEntity.getFileId(),String.valueOf(fileEntity.getBaseId()), FileAuditStatusEnum.NOT_PASS.getStatus(), 100);
                    continue;
                }
                // 正文送审成功更新file表
                userKnowledgeFileRepository.update(fileEntity);*/
                // 查询附件资源表
                List<UserKnowledgeFileResEntity> fileResList = userKnowledgeFileResRepository.selectByFileIds(fileEntity.getUserId(), Arrays.asList(fileEntity.getFileId()));
                if (CollectionUtils.isEmpty(fileResList)) {
                    continue;
                }
                // 附件异步送审
                fileResCheck(enabled, fileResList);
            } else {
                log.info("知识库下非笔记、邮件、图片,fileId:{}, category:{}", fileEntity.getFileId(), fileEntity.getCategory());
            }
        }
    }

    private void fileResCheck(Boolean enabled, List<UserKnowledgeFileResEntity> fileResList) {
        for (UserKnowledgeFileResEntity resEntity : fileResList) {
            if (FileAuditStatusEnum.PASS.getStatus().equals(resEntity.getAuditStatus()) || FileAuditStatusEnum.NOT_PASS.getStatus().equals(resEntity.getAuditStatus())) {
                log.info("个人云附件已送审,fileId:{},auditStatus:{}", resEntity.getFileId(), resEntity.getAuditStatus());
                continue;
            }
            if (Boolean.FALSE.equals(enabled)) {
                checkSystemExternalService.checkFile(resEntity.getUserId(), resEntity.getFileId(), resEntity.getExtension(), CheckSystemAuditTypeEnum.USER_KNOWLEDGE_FILE_RES_TYPE.getType());
                resEntity.setAuditStatus(1);
            } else {
                resEntity.setAuditStatus(2);
                resEntity.setAuditTime(new Date());
            }
            log.info("附件送审,fileId:{},auditStatus:{}", resEntity.getFileId(), resEntity.getAuditStatus());
            userKnowledgeFileResRepository.update(resEntity);
        }
    }

    private String getContent(UserKnowledgeFileEntity fileEntity) {
        String content = "";
        if (KnowledgeResourceTypeEnum.MAIL.getCode().equals(fileEntity.getFromResourceType())) {
            // 获取邮件正文
            String rowKey = fileEntity.getUserId() + StrPool.UNDERLINE + fileEntity.getFileId()
                    + StrPool.UNDERLINE + KnowledgeResourceTypeEnum.MAIL.getCode();
            content = getMailContent(rowKey);
        } else if (KnowledgeResourceTypeEnum.NOTE.getCode().equals(fileEntity.getFromResourceType())) {
            // 获取笔记正文
            String rowKey = fileEntity.getUserId() + StrPool.UNDERLINE + fileEntity.getFileId()
                    + StrPool.UNDERLINE + KnowledgeResourceTypeEnum.NOTE.getCode();
            content = getNoteContent(rowKey);
        }
        return content;
    }

    private void personalFileCheck(Boolean enabled, UserKnowledgeFileEntity fileEntity) {
        // 文件夹不送审
        if (FileTypeEnum.FOLDER.getKnowledgeFileType().equals(fileEntity.getFileType())) {
            return;
        }
        if (FileAuditStatusEnum.PASS.getStatus().equals(fileEntity.getAuditStatus()) || FileAuditStatusEnum.NOT_PASS.getStatus().equals(fileEntity.getAuditStatus())) {
            log.info("个人云文件已送审,fileId:{},auditStatus:{}", fileEntity.getFileId(), fileEntity.getAuditStatus());
            return;
        }
        if (Boolean.FALSE.equals(enabled)) {
            checkSystemExternalService.checkFile(fileEntity.getUserId(), fileEntity.getFileId(), fileEntity.getExtension(), CheckSystemAuditTypeEnum.PUBLIC_KNOWLEDGE_FILE_RES_TYPE.getType());
            fileEntity.setAuditStatus(1);
        } else {
            fileEntity.setAuditStatus(2);
            fileEntity.setAuditResult("送审关闭");
            fileEntity.setAuditTime(new Date());
        }
        log.info("个人云文件送审，fileEntity:{}", fileEntity);
        // 送审更新file表
        userKnowledgeFileRepository.update(fileEntity);
    }

    /**
     * 正文送审
     *
     * @param content 正文
     */
    private Boolean contentCheck(String content, UserKnowledgeFileEntity fileEntity) {
        boolean enabled = fileCheckConfig.isEnabled();
        log.info("batchImport note fileCheckConfig:{}", fileCheckConfig);
        if (enabled) {
            // 开关打开
            // 内容为空
            if (CharSequenceUtil.isEmpty(content)) {
                fileEntity.setAuditStatus(KnowledgeUploadStatusEnum.FAIL.getStatus());
                fileEntity.setAuditResult("文件文本解析结果为空");
                return Boolean.FALSE;
            }
            // 标题涉敏校验
            long uid = uidGenerator.getUID();
            CheckResultVO resultVo = checkSystemDomainService.checkPlatformAndLocal(uid, fileEntity.getUserId(), content);
            boolean fail = CheckResultVO.isFail(resultVo);
            String msg = resultVo.getDetail() != null ? resultVo.getDetail().getDetail() : "";
            if (fail) {
                fileEntity.setAuditStatus(KnowledgeUploadStatusEnum.FAIL.getStatus());
                fileEntity.setAuditResult(msg);
                return Boolean.FALSE;
            } else {
                fileEntity.setAuditStatus(KnowledgeUploadStatusEnum.SUCCESS.getStatus());
                fileEntity.setAuditResult(msg);
                return Boolean.TRUE;
            }
        } else {
            // 开关关闭，设置送审状态为已完成
            fileEntity.setAuditStatus(KnowledgeUploadStatusEnum.SUCCESS.getStatus());
            fileEntity.setAuditResult("关闭已送审");
            return Boolean.TRUE;
        }
    }

    private String getNoteContent(String rowKey) {
        List<AlgorithmRagTextContentPO> resultList = hbaseRepository.selectList(Arrays.asList(rowKey), AlgorithmRagTextContentPO.class);
        if (CollectionUtils.isEmpty(resultList)) {
            return "";
        }
        AlgorithmRagTextContentPO contentPo = resultList.get(0);
        String noteContent = contentPo.getContent();
        if (StringUtils.isBlank(noteContent)) {
            return "";
        }
        NoteThirdListVO vo = JsonUtil.parseObject(noteContent, NoteThirdListVO.class);
        if (vo == null || CollectionUtils.isEmpty(vo.getContents())) {
            return "";
        }
        NoteExpandVO expand = vo.getExpand();
        Integer noteType;
        if (expand == null || expand.getNoteType() == null) {
            noteType = 0;
        } else {
            noteType = expand.getNoteType();
        }
        List<NoteContentVO> contents = vo.getContents();
        for (NoteContentVO content : contents) {
            if (NoteTypeEnum.SIMPLE_NOTE.getType().equals(noteType) || NoteTypeEnum.AUDIO_NOTE.getType().equals(noteType)) {
                if (ContentTypeEnum.RICHTEXT.getType().equals(content.getType()) || ContentTypeEnum.TEXT.getType().equals(content.getType())) {
                    return content.getData();
                }
            } else if (NoteTypeEnum.SEPARATION_NOTE.getType().equals(noteType) || NoteTypeEnum.MAGIC_NOTE.getType().equals(noteType)
                    || NoteTypeEnum.SMART_NOTE.getType().equals(noteType)) {
                if (ContentTypeEnum.SEPARATE.getType().equals(content.getType())) {
                    return content.getData();
                }
            }
        }
        return "";
    }


    /**
     * 获取正文
     *
     * @param rowKey HBASE的key
     * @return 正文
     */
    private String getMailContent(String rowKey) {
        List<AlgorithmRagTextContentPO> resultList = hbaseRepository.selectList(Arrays.asList(rowKey), AlgorithmRagTextContentPO.class);
        if (CollectionUtils.isEmpty(resultList)) {
            return "";
        }
        AlgorithmRagTextContentPO contentPo = resultList.get(0);
        String content = contentPo.getContent();
        if (StringUtils.isBlank(content)) {
            return "";
        }
        RagMailDetailVO vo = JsonUtil.parseObject(content, RagMailDetailVO.class);
        if (vo == null || StringUtils.isBlank(vo.getTxtContent())) {
            return "";
        }
        Document document = Jsoup.parse(vo.getTxtContent());
        return document.text().replaceAll("\\s+", " ").trim();
    }

    private UserKnowledgeEntity assembleByUpdate(PersonalKnowledgeUpdateReqDTO dto, UserKnowledgeEntity entity) {
        String name = entity.getName();
        entity.setName(dto.getName());
        // 校验数据库名称并更新独立空间目录
        if (name != null && !name.equals(dto.getName())) {
            // 上送的name和数据库里对应记录的name不一致，需要更新独立空间目录
            updateDrive(dto.getUserId(), dto.getName(), entity.getFolderId());
        }
        entity.setDescription(dto.getDescription());
        if (KnowledgeStatusEnum.OPEN.getStatus().equals(dto.getOpenLevel()) ||
                KnowledgeStatusEnum.PRIVATE.getStatus().equals(dto.getOpenLevel())) {
            entity.setOpenLevel(Integer.valueOf(dto.getOpenLevel()));
        }
        if (null != dto.getProfilePhoto()) {
            entity.setProfilePhoto(JsonUtil.toJson(dto.getProfilePhoto()));
        }
        entity.setUpdateTime(new Date());
        return entity;
    }

    private void updateDrive(String userId, String name, String folderId) {
        OwnerDriveFileUpdateReqDTO reqDTO = new OwnerDriveFileUpdateReqDTO();
        reqDTO.setUserId(userId);
        reqDTO.setFileId(folderId);
        reqDTO.setName(name);
        reqDTO.setFileRenameMode(RenameModeEnum.FORCE_RENAME.getCode());
        userDriveExternalService.updateFileInfo(reqDTO);
    }

    @Override
    public PersonalKnowledgeCountVO count(PersonalKnowledgeCountReqDTO dto) {
        List<UserKnowledgeEntity> allList = userKnowledgeRepository.getAllByUserId(dto.getUserId());
        if (ObjectUtil.isEmpty(allList)) {
            log.info("用户{}没有使用过知识库", dto.getUserId());
            return new PersonalKnowledgeCountVO(false);
        }

        // 使用过知识库
        int normalCount = 0;
        for (UserKnowledgeEntity entity : allList) {
            if (KnowledgeStatusEnum.isNormal(entity.getDelFlag())
                    && BizTypeEnum.isDefault(entity.getBizType())) {
                normalCount++;
            }
        }
        if (normalCount == 0) {
            log.info("用户{}使用过知识库，但是已经全部删除", dto.getUserId());
            return new PersonalKnowledgeCountVO(true);
        }

        // 统计数据
        String countTime = DateUtil.format(DateUtil.parse(handleCountTime(dto.getCountTime())), DatePattern.NORM_DATETIME_PATTERN);
        PersonalKnowledgeCountVO vo = userKnowledgeFileRepository.countByUserId(dto.getUserId(), countTime);
        vo.setHasUsed(true);
        vo.setBaseCount(normalCount);
        return vo;
    }

    private String handleCountTime(String countTime) {
        OffsetDateTime originalTime = OffsetDateTime.parse(countTime);
        OffsetDateTime earlierTime = originalTime.minusMinutes(knowledgePersonalProperties.getCountTimeOffset());
        DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
        return earlierTime.format(formatter);
    }

    @Override
    public List<KnowledgeBase> getInfoListByIds(KnowledgeBatchGetReqDTO dto) {
        String userId = dto.getUserId();
        if (StringUtils.isBlank(userId)) {
            userId = RequestContextHolder.getUserId();
        }
        List<String> baseIdList = dto.getBaseIdList();
        List<Long> baseIdLongList = baseIdList.stream().map(Long::valueOf).collect(Collectors.toList());

        if (baseIdLongList.size() == 1) {
            return getOneById(baseIdLongList, userId);
        }
        return getInfoListByIdList(baseIdLongList, userId);
    }

    /**
     * 入参只有一条知识库id
     */
    private List<KnowledgeBase> getOneById(List<Long> baseIds, String userId) {

        // 查询知识库信息
        UserKnowledgeEntity userKnowledgeEntity = userKnowledgeRepository.selectInfoById(baseIds.get(0));

        if (null == userKnowledgeEntity) {
            return Collections.emptyList();
        }

        Long baseId = userKnowledgeEntity.getId();

        // 知识库邀请列表
        List<UserKnowledgeInviteEntity> inviteEntityList = userKnowledgeInviteRepository.getListByUserId(baseIds, userId);

        List<KnowledgeBase> resultList = new ArrayList<>();
        KnowledgeBase knowledgeBase = pesonalKnowledgeConvertor.toKnowledgeBase(userKnowledgeEntity);

        // 当前知识库是否是邀请知识库
        if (!ObjectUtil.isEmpty(inviteEntityList)) {
            if (Objects.equals(KnowledgeStatusEnum.DELETED.getStatus(), userKnowledgeEntity.getDelFlag())) {
                // 当前知识库是邀请知识库 知识库状态为已删除 返回错误信息：该知识库已被创建者删除，不支持访问
                throw new YunAiBusinessException(ResultCodeEnum.SHARE_KNOWLEDGE_DELETED);
            }
            if (Objects.equals(KnowledgeStatusEnum.PRIVATE.getStatus(), userKnowledgeEntity.getOpenLevel())) {
                // 当前知识库是邀请知识库 知识库状态为私密 返回错误信息：该知识库已被创建者设为私密，不支持访问
                throw new YunAiBusinessException(ResultCodeEnum.SHARE_KNOWLEDGE_PRIVATE);
            }

            knowledgeBase.setBaseType(KnowledgeBaseTypeEnum.SHARE.getCode());
        } else {
            if (Objects.equals(KnowledgeStatusEnum.DELETED.getStatus(), userKnowledgeEntity.getDelFlag())) {
                // 当前知识库不是邀请知识库 知识库状态为已删除 返回错误信息：该知识库已被删除，不支持访问
                throw new YunAiBusinessException(ResultCodeEnum.PERSONAL_KNOWLEDGE_DELETED);
            }

            if (!StringUtils.equals(userId, userKnowledgeEntity.getUserId())) {
                log.info("当前知识库不属于当前用户，知识库id:{}，用户id:{}", baseId, userId);
                return Collections.emptyList();
            }

            knowledgeBase.setBaseType(KnowledgeBaseTypeEnum.PERSONAL.getCode());
        }
        // 文件统计
        List<UserKnowledgeFileStatisticsEntity> statisticsList = userKnowledgeFileRepository.findStatisticsByBaseIdForInfo(baseIds);
        setStatistics(baseId, knowledgeBase, statisticsList);
        resultList.add(knowledgeBase);
        return resultList;
    }

    /**
     * 入参为多条ids，返回多条知识库信息
     */
    private List<KnowledgeBase> getInfoListByIdList(List<Long> baseIdLongList, String userId) {
        List<UserKnowledgeEntity> list = userKnowledgeRepository.getInfoListByIds(baseIdLongList);
        if (ObjectUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        // 知识库邀请列表
        List<UserKnowledgeInviteEntity> inviteEntityList = userKnowledgeInviteRepository.getListByUserId(baseIdLongList, userId);
        // 文件统计
        List<UserKnowledgeFileStatisticsEntity> statisticsList = userKnowledgeFileRepository.findStatisticsByBaseIdForInfo(baseIdLongList);

        List<KnowledgeBase> resultList = new ArrayList<>();
        // 数据处理
        for (UserKnowledgeEntity entity : list) {
            Long baseId = Long.valueOf(entity.getId());
            KnowledgeBase base = pesonalKnowledgeConvertor.toKnowledgeBase(entity);
            // 如果baseId存在于邀请列表中，设置baseType为分享知识库
            if (inviteEntityList.stream().anyMatch(item -> item.getKnowledgeId().equals(baseId))) {
                base.setBaseType(KnowledgeBaseTypeEnum.SHARE.getCode());
            } else {
                if (!StringUtils.equals(userId, entity.getUserId())) {
                    log.info("当前知识库不属于当前用户，知识库id:{}，用户id:{}", baseId, userId);
                    continue;
                }
                base.setBaseType(KnowledgeBaseTypeEnum.PERSONAL.getCode());
            }

            setStatistics(baseId, base, statisticsList);
            resultList.add(base);
        }
        return resultList;
    }

    private void setStatistics(Long baseId, KnowledgeBase knowledgeBase, List<UserKnowledgeFileStatisticsEntity> statisticsList) {
        // 如果baseId存在于统计列表中，设置文件数量
        if (statisticsList.stream().anyMatch(item -> item.getBaseId().equals(baseId))) {
            UserKnowledgeFileStatisticsEntity statisticsEntity = statisticsList.stream()
                    .filter(item -> item.getBaseId().equals(baseId))
                    .findFirst().orElse(null);
            assert statisticsEntity != null;
            knowledgeBase.setTotalCount(Objects.equals(null, statisticsEntity.getTotalCount()) ? 0 : statisticsEntity.getTotalCount());
            knowledgeBase.setParsedCount(Objects.equals(null, statisticsEntity.getProcessed()) ? 0 : statisticsEntity.getProcessed());
            knowledgeBase.setUnparsedCount(Objects.equals(null, statisticsEntity.getUntreated()) ? 0 : statisticsEntity.getUntreated());
        } else {
            knowledgeBase.setTotalCount(0);
            knowledgeBase.setParsedCount(0);
            knowledgeBase.setUnparsedCount(0);
        }
    }

    @Override
    public List<PersonalKnowledgeProfilePhotoVO> profilePhotoList(BaseChannelDTO dto) {
        List<KnowledgePersonalProperties.ProfilePhoto>
                profilePhotoList = knowledgePersonalProperties.getProfilePhotoList();
        return pesonalKnowledgeConvertor.toPersonalKnowledgeProfilePhotoVOList(profilePhotoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(PersonalKnowledgeDeleteReqDTO dto) {
        // 根据知识库ID判断该知识库是否存在
        UserKnowledgeEntity userKnowledgeEntity = userKnowledgeRepository
                .selectByIdAndUserId(Long.valueOf(dto.getBaseId()), dto.getUserId());
        if (Objects.isNull(userKnowledgeEntity)) {
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_ID_NOT_EXIST);
        }

        // 逻辑删除 个人知识库信息
        userKnowledgeRepository.deleteByIdAndUserId(Long.valueOf(dto.getBaseId()), dto.getUserId());

        // 根据知识库id先查出来
        List<UserKnowledgeFileEntity> userKnowledgeFileEntityList
                = userKnowledgeFileRepository.selectByBaseId(dto.getBaseId());

        // 知识库文件id
        List<String> fileIdList = userKnowledgeFileEntityList
                .stream().map(UserKnowledgeFileEntity::getFileId).collect(Collectors.toList());

        // 删除数据 个人知识库文件
        userKnowledgeFileRepository.batchDeleteByFileIds(dto.getUserId(), fileIdList, KnowledgeStatusEnum.DELETING);

        // 删除来源于邮箱的hbase的关联数据
        batchDeleteHBaseData(userKnowledgeFileEntityList, dto);
        // 批量删除个人知识库文档数据
        personalKnowledgeDomainService.batchDeleteDocumentById(dto.getUserId(), fileIdList,
                knowledgePersonalProperties.getEsFileChunkDelete(), knowledgeDeleteThreadPool);

        // 生成个人知识库文件资源删除任务
        List<UserKnowledgeFileTaskEntity> taskEntityList = deleteFile(dto, fileIdList);
        // 生成个人知识库独立空间文件资源删除任务
        List<UserKnowledgeFileTaskEntity> taskEntityResList = deleteFileRes(dto, fileIdList);
        // 执行个人知识库文件资源删除任务
        deleteTask(taskEntityList);
        // 执行个人知识库独立空间文件资源删除任务
        deleteFileResTask(taskEntityResList);
    }

    private UserKnowledgeEntity assembleBySave(PersonalKnowledgeAddReqDTO dto) {
        UserKnowledgeEntity entity = new UserKnowledgeEntity();
        entity.setId(uidGenerator.getUID());
        entity.setUserId(dto.getUserId());

        // 读取配置
        entity.setOwnerId(ownerDriveProperties.getDefaultOwnerid());

        // 获取知识库目录ID
        CatalogConfigDTO.CatalogConfigInfo info = userDriveConfigDomainService.knowledgeConfig(dto.getUserId(), dto.getName());
        if (null != info) {
            entity.setFolderId(info.getCatalogId());
        }

        // 知识库，复用原来的枚举类
        entity.setOwnerType(OwnerTypeEnum.AI.getOwnerValue());
        // 直接传json
        entity.setProfilePhoto(JsonUtil.toJson(dto.getProfilePhoto()));
        entity.setName(dto.getName());
        entity.setDescription(dto.getDescription());
        if (null != dto.getOpenLevel()) {
            // 默认是0
            entity.setOpenLevel(dto.getOpenLevel());
        }
        entity.setDelFlag(KnowledgeStatusEnum.NORMAL.getStatus());
        entity.setCreatedBy(dto.getUserId());
        entity.setUpdatedBy(dto.getUserId());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        return entity;
    }

    /**
     * 个人知识库头像信息（通过photoId获取配置的url）
     */
    private PersonalKnowledgeProfilePhoto getPersonalKnowledgeProfilePhoto(PersonalKnowledgeProfilePhoto profilePhoto) {
        List<KnowledgePersonalProperties.ProfilePhoto>
                profilePhotoList = knowledgePersonalProperties.getProfilePhotoList();

        // ProfilePhoto的photoId为key, url为value
        Map<String, String> photoIdToUrlMap = profilePhotoList.stream()
                .collect(Collectors.toMap(
                        KnowledgePersonalProperties.ProfilePhoto::getPhotoId,
                        KnowledgePersonalProperties.ProfilePhoto::getUrl
                ));
        String url = photoIdToUrlMap.getOrDefault(profilePhoto.getPhotoId(), profilePhotoList.get(0).getUrl());
        profilePhoto.setUrl(url);
        log.info("个人知识库头像信息-通过photoId获取配置的url profilePhoto:{}", JsonUtil.toJson(profilePhoto));
        return profilePhoto;
    }

    private void batchDeleteHBaseData(List<UserKnowledgeFileEntity> list, PersonalKnowledgeDeleteReqDTO dto) {
        // 遍历list，挑选出from_resource_type为邮箱的数据，
        List<String> fileIdHbaseList = new ArrayList<>();
        for (UserKnowledgeFileEntity item : list) {
            if (item != null && ResourceTypeEnum.isMail(item.getFromResourceType())) {
                fileIdHbaseList.add(item.getFileId());
            }
        }
        log.info("将要删除的hbase数据，fileIdHbaseList: {}， userId: {}", fileIdHbaseList, dto.getUserId());
        if (CollUtil.isEmpty(fileIdHbaseList)) {
            return;
        }
        String userId = dto.getUserId();
        List<String> rowKeyList = new ArrayList<>();

        // 构建rowKeyList
        for (String fileId : fileIdHbaseList) {
            String rowKey = userId + CONNECT_SPLIT + fileId;
            rowKeyList.add(rowKey);
        }
        while (!rowKeyList.isEmpty()) {
            List<String> subRowKeyList = rowKeyList.subList(0, Math.min(rowKeyList.size(), knowledgePersonalProperties.getHbaseDelete()));
            // 删除hbase数据
            Boolean delFlag = hbaseRepository.delByRowKeyList(subRowKeyList, AlgorithmRagTextContentPO.class);
            log.info("分批删除中的hbase数据，subRowKeyList: {}，删除是否成功: {}， userId: {}", subRowKeyList, delFlag, userId);
            // 删除subList
            rowKeyList.removeAll(subRowKeyList);
        }
    }

    private List<UserKnowledgeFileTaskEntity> deleteFile(PersonalKnowledgeDeleteReqDTO dto, List<String> fileIds) {
        int batchSize = knowledgePersonalProperties.getFileDelete();
        List<UserKnowledgeFileTaskEntity> taskEntityList = new ArrayList<>();
        for (int i = 0; i < fileIds.size(); i += batchSize) {
            // 获取当前批次的文件 ID 子列表
            List<String> subFileIds = fileIds.subList(i, Math.min(i + batchSize, fileIds.size()));
            // 创建删除文件的任务
            UserKnowledgeFileTaskEntity taskEntity = assembleTaskEntity(dto, subFileIds, KnowledgeTaskTypeEnum.DELETE);
            userKnowledgeFileTaskRepository.add(taskEntity);
            taskEntityList.add(taskEntity);
        }
        return taskEntityList;
    }


    /**
     * 生成个人知识库文件资源删除任务
     *
     * @param dto         参数dto
     * @param fromFileIds 来源文件id集合
     */
    private List<UserKnowledgeFileTaskEntity> deleteFileRes(PersonalKnowledgeDeleteReqDTO dto, List<String> fromFileIds) {
        // 先查询表algorithm_user_knowledge_file_res，查询出删除标识为正常0的记录
        List<UserKnowledgeFileResEntity> fileResList
                = userKnowledgeFileResRepository.batchSelectByFileIds(dto.getUserId(), fromFileIds);
        log.info("删除个人知识库文件资源，userId：{}，fromFileIds：{}, fileResList.size: {}",
                dto.getUserId(), fromFileIds, fileResList.size());
        // 遍历fileResList，更新表algorithm_user_knowledge_file_res对应记录的删除标识为删除中2
        // 用于存储待删除的fileId
        List<String> fileIdResList = new ArrayList<>();
        if (CollUtil.isNotEmpty(fileResList)) {
            for (UserKnowledgeFileResEntity entity : fileResList) {
                fileIdResList.add(entity.getFileId());
                entity.setUpdateTime(new Date());
                entity.setDelFlag(UserKnowledgeFileResDelFlagEnum.DELETING.getDelFlag());
                userKnowledgeFileResRepository.update(entity);
            }
        } else {
            log.info("文件里并没有图片或者文件里的图片都没有成功上传到独立空间，userId：{}，fromFileIds：{}，fileResList.size: {}",
                    dto.getUserId(), fromFileIds, fileResList.size());
        }
        // 依次取出fileIdResList的固定数量（100）个元素，保存任务，直到fileIdResList为空
        int batchSize = knowledgePersonalProperties.getFileResDelete();
        List<UserKnowledgeFileTaskEntity> taskEntityList = new ArrayList<>();
        for (int i = 0; i < fileIdResList.size(); i += batchSize) {
            // 获取当前批次的文件 ID 子列表
            List<String> subFileIds = fileIdResList.subList(i, Math.min(i + batchSize, fileIdResList.size()));
            // 创建删除文件的任务
            UserKnowledgeFileTaskEntity taskEntity = assembleTaskEntity(dto, subFileIds, KnowledgeTaskTypeEnum.DELETE_RES);
            userKnowledgeFileTaskRepository.add(taskEntity);
            taskEntityList.add(taskEntity);
        }
        return taskEntityList;
    }

    /**
     * 组装个人知识库文件转存任务实体
     *
     * @param dto          请求入参
     * @param fileIds      文件Id集合
     * @param taskTypeEnum 知识库任务类型
     * @return 个人知识库文件转存任务
     */
    private UserKnowledgeFileTaskEntity assembleTaskEntity(PersonalKnowledgeDeleteReqDTO dto, List<String> fileIds,
                                                           KnowledgeTaskTypeEnum taskTypeEnum) {
        UserKnowledgeFileTaskEntity taskEntity = new UserKnowledgeFileTaskEntity();
        taskEntity.setUserId(dto.getUserId());
        taskEntity.setOwnerType(OwnerTypeEnum.AI.getOwnerValue());
        taskEntity.setTaskStatus(FileTaskStatusEnum.UNPROCESSED.getStatus());
        taskEntity.setTaskType(taskTypeEnum.getCode());
        taskEntity.setTaskRequest(JsonUtil.toJson(dto));
        taskEntity.setExpireTime(knowledgePersonalProperties.getDeleteExpireDate());
        taskEntity.setFileIds(String.join(",", fileIds));
        taskEntity.setFileNum(fileIds.size());
        return taskEntity;
    }

    /**
     * 删除任务
     *
     * @param taskEntityResList 删除任务实体集合
     */
    private void deleteTask(List<UserKnowledgeFileTaskEntity> taskEntityResList) {
        for (UserKnowledgeFileTaskEntity taskEntity : taskEntityResList) {
            try {
                final List<String> fileIdList = Arrays.asList(taskEntity.getFileIds().split(","));

                // 发起删除任务
                String thirdTaskId = userDriveExternalService.deleteByFileIds(taskEntity.getUserId(), fileIdList);
                taskEntity.setTaskStatus(FileTaskStatusEnum.PROCESSING.getStatus());
                taskEntity.setThirdTaskId(thirdTaskId);
                userKnowledgeFileTaskRepository.update(taskEntity);

                // 异步查询任务
                Map<String, String> logMap = MDC.getCopyOfContextMap();
                businessThreadPool.execute(() -> {
                    MDC.setContextMap(logMap);
                    for (int i = 0; i < knowledgePersonalProperties.getDeleteTaskQueryTimes(); i++) {
                        try {
                            Thread.sleep(knowledgePersonalProperties.getDeleteTaskQuerySleep());
                        } catch (InterruptedException e) {
                            log.error("线程休眠异常", e);
                            Thread.currentThread().interrupt();
                        }

                        // 查询删除任务结果，这里只管成功状态，其他状态由中心任务的定时器扫描处理
                        Date startTime = new Date();
                        OwnerDriveTaskResultVO resultVO = userDriveExternalService.getDeleteTaskResult(taskEntity.getUserId(), taskEntity.getThirdTaskId());
                        if (OwnerDriveTaskStatusEnum.isSuccess(resultVO.getTaskInfo().getStatus())) {
                            // 成功，更新状态
                            taskEntity.setStartTime(startTime);
                            taskEntity.setFinishTime(new Date());
                            taskEntity.setTaskStatus(FileTaskStatusEnum.FINISH.getStatus());
                            taskEntity.setExecuteCount(i);
                            taskEntity.setSuccessNum(taskEntity.getFileNum());
                            userKnowledgeFileTaskRepository.update(taskEntity);

                            // 更新为已删除
                            userKnowledgeFileRepository.deleteByFileIds(taskEntity.getUserId(), fileIdList, KnowledgeStatusEnum.DELETED);
                        }

                        if (!OwnerDriveTaskStatusEnum.isRunning(resultVO.getTaskInfo().getStatus())) {
                            log.info("删除任务结束，任务状态：{}", resultVO.getTaskInfo().getStatus());
                            break;
                        }
                    }
                });
            } catch (Exception e) {
                log.error("发起删除任务失败，任务id：{}，异常信息：{}", taskEntity.getId(), e.getMessage(), e);
            }
        }
    }

    /**
     * 执行删除任务
     *
     * @param taskEntityResList 删除任务实体集合
     */
    private void deleteFileResTask(List<UserKnowledgeFileTaskEntity> taskEntityResList) {
        // 遍历taskEntityResList，更新表algorithm_user_knowledge_file_task对应记录
        for (UserKnowledgeFileTaskEntity entity : taskEntityResList) {
            final List<String> fileIdList = Arrays.asList(entity.getFileIds().split(","));

            // 发起删除任务
            String thirdTaskId = "";
            try {
                thirdTaskId = userDriveExternalService.deleteByFileIds(entity.getUserId(), fileIdList);
                entity.setTaskStatus(FileTaskStatusEnum.PROCESSING.getStatus());
                log.info("删除文件资源，userId：{}，fileIds：{}, thirdTaskId: {}", entity.getUserId(), fileIdList, thirdTaskId);
            } catch (Exception e) {
                entity.setTaskStatus(FileTaskStatusEnum.FAIL.getStatus());
                log.error("删除文件资源调起接口失败，userId：{}，fileIds：{}, thirdTaskId: {}, Exception: {}", entity.getUserId(),
                        fileIdList, thirdTaskId, e.getMessage(), e);
            }
            entity.setThirdTaskId(thirdTaskId);
            userKnowledgeFileTaskRepository.update(entity);
        }
    }

    private PageInfo<UserKnowledgeEntity> list(String userId, Integer baseType, PageInfoDTO pageInfo, List<Long> shareIdList) {
        // 先查询总数，再查询分页数据
        List<UserKnowledgeEntity> allList = new ArrayList<>();

        if (ObjectUtil.isEmpty(baseType)) {
            // 查出个人知识库和所在的共享知识库
            // 查询个人知识库
            List<UserKnowledgeEntity> personalList = userKnowledgeRepository.selectByUserId(userId);
            if (personalList != null) {
                allList.addAll(personalList);
            }
            // 查询共享知识库
            List<UserKnowledgeEntity> shareList = getShareList(userId, shareIdList);
            if (shareList != null) {
                allList.addAll(shareList);
            }
        } else if (Objects.equals(baseType, KnowledgeBaseTypeEnum.PERSONAL.getCode())) {
            // 查出个人知识库
            List<UserKnowledgeEntity> personalList = userKnowledgeRepository.selectByUserId(userId);
            if (personalList != null) {
                allList.addAll(personalList);
            }
        } else if (baseType.equals(KnowledgeBaseTypeEnum.SHARE.getCode())) {
            // 查出共享知识库
            List<UserKnowledgeEntity> shareList = getShareList(userId, shareIdList);
            if (shareList != null) {
                allList.addAll(shareList);
            }
        } else if (baseType.equals(KnowledgeBaseTypeEnum.COMMON.getCode())) {
            // 查出公共知识库
        }
        // 如果列表为空，直接返回空分页结果
        if (allList.isEmpty()) {
            return new PageInfo<>(Collections.emptyList(), 0);
        }
        // 按createTime降序排序
        allList.sort(Comparator.comparing(UserKnowledgeEntity::getCreateTime).reversed());

        // 手动分页（pageCursor从0开始）
        int total = allList.size();
        int pageCursor = Integer.parseInt(pageInfo.getPageCursor());
        int pageSize = pageInfo.getPageSize();

        // 处理超出范围的pageCursor
        if (pageCursor >= total) {
            return new PageInfo<>(Collections.emptyList(), total);
        }
        int toIndex = Math.min(pageCursor + pageSize, total);
        List<UserKnowledgeEntity> pageList = allList.subList(pageCursor, toIndex);

        // 创建Page对象
        Page<UserKnowledgeEntity> page = new Page<>();
        page.setTotal(total);
        page.addAll(pageList);

        return new PageInfo<>(page);
    }

    private List<UserKnowledgeEntity> getShareList(String userId, List<Long> shareIdList) {
        // 知识库邀请列表ID
        List<UserKnowledgeInviteEntity> list = userKnowledgeInviteRepository.getKnowledgeListByUserId(userId);
        if (ObjectUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        Map<Long, UserKnowledgeInviteEntity> map = list.stream().collect(Collectors.toMap(k -> k.getKnowledgeId(), v -> v));
        shareIdList.addAll(new ArrayList<>(map.keySet()));
        //共享知识库列表
        List<UserKnowledgeEntity> shareList = userKnowledgeRepository.getInfoListByIds(shareIdList);
        //筛选出公开的知识库
        shareList = shareList.stream().filter(item -> {
                    UserKnowledgeInviteEntity inviteEntity = map.get(item.getId());
                    item.setSelected(inviteEntity.getSelected());
                    item.setCreateTime(inviteEntity.getCreateTime());
                    return item.getOpenLevel().equals(KnowledgeStatusEnum.OPEN.getStatus());
                })
                .collect(Collectors.toList());
        return shareList;
    }

    @Override
    public PersonalKnowledgeListPageInfoVO list(PersonalKnowledgeListReqDTO dto) {
        PersonalKnowledgeListPageInfoVO result = new PersonalKnowledgeListPageInfoVO();

        // 查知识库表
        String userId = dto.getUserId();
        List<Long> shareIdList = new ArrayList<>();
        PageInfo<UserKnowledgeEntity> pageInfo = list(userId, dto.getBaseType(), PageInfoDTO.getReqDTO(dto.getPageInfo()), shareIdList);
        if (ObjectUtil.isEmpty(pageInfo.getList())) {
            log.info("知识库为空，userId:{}", userId);
            return result;
        }

        List<UserKnowledgeEntity> list = pageInfo.getList();
        // 数据转换
        List<KnowledgeBase> resultList = convertor.toKnowledgeBaseList(list);
        //知识库ID列表
        List<Long> baseIdList = list.stream()
                .map(UserKnowledgeEntity::getId)
                .collect(Collectors.toList());
        // 文件统计
        List<UserKnowledgeFileStatisticsEntity> statisticsList = userKnowledgeFileRepository.findStatisticsByBaseId(baseIdList);
        // 数据处理
        for (KnowledgeBase base : resultList) {
            Long baseId = Long.valueOf(base.getBaseId());

            // 如果baseId存在于邀请列表中，设置baseType为分享知识库
            if (shareIdList.contains(baseId)) {
                base.setBaseType(KnowledgeBaseTypeEnum.SHARE.getCode());
            } else {
                base.setBaseType(KnowledgeBaseTypeEnum.PERSONAL.getCode());
            }

            // 如果baseId存在于统计列表中，设置文件数量
            Optional<UserKnowledgeFileStatisticsEntity> statisticsEntity = statisticsList.stream()
                    .filter(item -> item.getBaseId().equals(baseId))
                    .findFirst();

            base.setTotalCount(statisticsEntity.map(UserKnowledgeFileStatisticsEntity::getTotalCount).orElse(0));
            base.setParsedCount(statisticsEntity.map(UserKnowledgeFileStatisticsEntity::getProcessed).orElse(0));
            base.setUnparsedCount(statisticsEntity.map(UserKnowledgeFileStatisticsEntity::getUntreated).orElse(0));
        }
        // 获取已使用空间大小和总空间大小
        OwnerDriveVO ownerDriveVO = getOwnerDriveVO(userId);
        result.setUsedSpace(ownerDriveVO.getUsedSize());
        result.setTotalSpace(ownerDriveVO.getTotalSize());

        // 返回结果
        return result.setData(dto.getPageInfo(), pageInfo, resultList);
    }

    private OwnerDriveVO getOwnerDriveVO(String userId) {
        OwnerDriveReqDTO reqDTO = new OwnerDriveReqDTO();
        reqDTO.setUserId(userId);
        reqDTO.setClientInfo(RequestContextHolder.getClientInfo());
        reqDTO.setAppChannel(RequestContextHolder.getAppChannel());
        reqDTO.setOwnerId(ownerDriveProperties.getDefaultOwnerid());
        return userDriveExternalService.getSpace(reqDTO);
    }

    private void checkContentName(String name) {
        CheckTextReqDTO checkTextReqDTO = new CheckTextReqDTO();
        checkTextReqDTO.setContent(name);
        checkTextReqDTO.setAccount(RequestContextHolder.getUserId());
        CheckResultVO checkResultVO = checkSystemExternalService.checkTextName(
                knowledgePersonalProperties.getCheckContentSwitch(), checkTextReqDTO);
        if (CheckResultVO.isFail(checkResultVO)) {
            log.info("【知识库名称送审】PersonalKnowledgeServiceImpl-checkNameContent-个人知识库名称送审不通过：{}", name);
            throw new YunAiBusinessException(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
        }
    }

    private void checkContentDescription(String content) {
        CheckTextReqDTO checkTextReqDTO = new CheckTextReqDTO();
        checkTextReqDTO.setContent(content);
        checkTextReqDTO.setAccount(RequestContextHolder.getUserId());
        CheckResultVO checkResultVO = checkSystemExternalService.checkTextDescription(
                knowledgePersonalProperties.getCheckContentSwitch(), checkTextReqDTO);
        if (CheckResultVO.isFail(checkResultVO)) {
            log.info("PersonalKnowledgeServiceImpl-checkName-个人知识库描述送审不通过：{}", content);
            throw new YunAiBusinessException(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
        }
    }

}