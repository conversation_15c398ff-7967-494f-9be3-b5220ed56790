package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import java.util.Objects;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatMessageService;
import com.zyhl.yun.api.outer.application.util.ChatAddUtils;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatMessageEntity;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.ChatMessageStarEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatMessageIconEnum;
import com.zyhl.yun.api.outer.repository.AlgorithmChatMessageRepository;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 会话信息操作
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ChatMessageServiceImpl implements ChatMessageService {

    @Resource
    private AlgorithmChatMessageRepository algorithmChatMessageRepository;
    @Resource
    private SourceChannelsProperties channelsProperties;
    @Resource
    private UidGenerator uidGenerator;
    @Resource
    private RedisOperateRepository redisOperateRepository;

    private static final String SYMBOL = " | ";

    @Override
    public long save(ChatAddHandleDTO handleDTO) {
    	ChatAddReqDTO reqDTO = handleDTO.getReqDTO();
        Long sessionId = null;
        if (!CharSequenceUtil.isEmpty(reqDTO.getSessionId())) {
            sessionId = Long.parseLong(reqDTO.getSessionId());
        }

        // 智能体会话，需要先查询会话id
        if (sessionId == null && ApplicationTypeEnum.isIntelligen(reqDTO.getApplicationType())) {
            sessionId = getAgentSessionId(reqDTO.getUserId(), RequestContextHolder.getSourceChannel(), reqDTO.getApplicationId());
        }
        AlgorithmChatMessageEntity messageEntity;

        if(Boolean.TRUE.equals(handleDTO.getSaveMessage())) {
        	// 图书快速阅读需求增加的控制
            messageEntity = addChatMessage(handleDTO, handleDTO.getSessionId());
            return messageEntity.getId();
        }

        if (sessionId != null) {
            log.info("更新会话信息，会话id：{}", sessionId);
            messageEntity = AlgorithmChatMessageEntity.builder().id(sessionId).updateTime(DateUtil.date()).build();
            algorithmChatMessageRepository.updateMessage(messageEntity);
            log.info("更新会话信息成功，会话id：{}", sessionId);

            return sessionId;
        } else {
            // 新增会话信息
            messageEntity = addChatMessage(handleDTO, null);
        }

        return messageEntity.getId();
    }

    @Override
    public long saveAll(ChatAddHandleDTO handleDTO) {
        AlgorithmChatMessageEntity messageEntity = null;
        // 保存会话（true-新增；false-更新）
        Boolean saveMessage = handleDTO.getSaveMessage();
        Long sessionId = handleDTO.getSessionId();
        if (Boolean.TRUE.equals(saveMessage)) {
            // 新增会话信息（指定sessionId）
            messageEntity = addChatMessage(handleDTO, sessionId);
            return messageEntity.getId();
        } else {
            log.info("更新会话信息，会话id：{}", sessionId);
            messageEntity = AlgorithmChatMessageEntity.builder().id(sessionId).updateTime(DateUtil.date()).build();
            algorithmChatMessageRepository.updateMessage(messageEntity);
            log.info("更新会话信息成功，会话id：{}", sessionId);

            return sessionId;
        }
    }

    @Override
    public boolean exist(String id, String userId) {
        AlgorithmChatMessageEntity entity = algorithmChatMessageRepository.queryBySessionId(id, userId);
        return Objects.nonNull(entity);
    }

    private AlgorithmChatMessageEntity addChatMessage(ChatAddHandleDTO handleDTO, Long sessionId) {
        AlgorithmChatMessageEntity messageEntity = new AlgorithmChatMessageEntity();
        messageEntity.setId(null == sessionId ? uidGenerator.getUID() : sessionId);
        ChatAddReqDTO reqDTO = handleDTO.getReqDTO();
		messageEntity.setUserId(reqDTO .getUserId());
        messageEntity.setTitle(createTitle(reqDTO, handleDTO.getResourceName()));
        //星标设置
        messageEntity.setEnableStar(StringUtils.isNoneBlank(ChatAddUtils.getOrderTip(reqDTO.getDialogueInput().getExtInfo()))
                ? ChatMessageStarEnum.YES.getCode()
                : ChatMessageStarEnum.NO.getCode());
        messageEntity.setCreateTime(DateUtil.date());
        messageEntity.setUpdateTime(DateUtil.date());

        // 业务类型、应用类型、应用id
        messageEntity.setBusinessType(channelsProperties.getType(RequestContextHolder.getSourceChannel()));
        messageEntity.setApplicationId(CharSequenceUtil.emptyToDefault(reqDTO.getApplicationId(), "0"));
        messageEntity.setApplicationType(ApplicationTypeEnum.getByCodeDefaultChat(reqDTO.getApplicationType()).getCode());
        
        // 二级子意图
        String subIntentionCode = handleDTO.getSubIntentionCode();
		if (DialogueIntentionEnum.isTextToolIntention(handleDTO.getIntentionCode())
				&& DialogueIntentionSubEnum.isNewAiToolIcon(subIntentionCode)) {
			String newMessageTitle = messageEntity.getTitle();
			String orderTipJoin = ChatAddUtils.getOrderTip(handleDTO.getInputInfoDTO().getExtInfo());
			if (StringUtils.isNotEmpty(orderTipJoin)) {
				orderTipJoin = orderTipJoin + SYMBOL;
				if (!newMessageTitle.equals(orderTipJoin) && newMessageTitle.contains(orderTipJoin)) {
					// 文本工具指令，需要额外去除指令内容，列表会设置
					messageEntity.setTitle(newMessageTitle.substring(orderTipJoin.length() - 1));
				}
			}
			// 创新AI工具图标设置&&可执行的二级子意图
			messageEntity.setIconType(ChatMessageIconEnum.TOOL_DIALOGUE.getIconType());
			// 创新AI工具图标设置-子图标
			if (DialogueIntentionSubEnum.isAiPpt(subIntentionCode)) {
				messageEntity.setSubIconType(ChatMessageIconEnum.TOOL_DIALOGUE_AI_PPT.getSubIconType());
			}
			if (DialogueIntentionSubEnum.isAiMeetingMinutes(subIntentionCode)) {
				messageEntity.setSubIconType(ChatMessageIconEnum.TOOL_DIALOGUE_MEETING_MINUTES.getSubIconType());
			}
			if (DialogueIntentionSubEnum.isAiSpeedRead(subIntentionCode)) {
				messageEntity.setSubIconType(ChatMessageIconEnum.TOOL_DIALOGUE_SPEED_READ.getSubIconType());
			}
			if (DialogueIntentionSubEnum.isAiCoder(subIntentionCode)) {
				messageEntity.setSubIconType(ChatMessageIconEnum.TOOL_DIALOGUE_AI_CODER.getSubIconType());
			}
			if (DialogueIntentionSubEnum.isAiPhotoSolveProblems(subIntentionCode)) {
				messageEntity
						.setSubIconType(ChatMessageIconEnum.TOOL_DIALOGUE_AI_PHOTO_SOLVE_PROBLEMS.getSubIconType());
			}
			if (DialogueIntentionSubEnum.isAiPhotoTranslate(subIntentionCode)) {
				messageEntity.setSubIconType(ChatMessageIconEnum.TOOL_DIALOGUE_AI_PHOTO_TRANSLATE.getSubIconType());
			}
			if (DialogueIntentionSubEnum.isAiPhotoQa(subIntentionCode)) {
				messageEntity.setSubIconType(ChatMessageIconEnum.TOOL_DIALOGUE_AI_PHOTO_QA.getSubIconType());
			}
		}else {
        	// 普通icon
        	String orderTip = ChatAddUtils.getOrderTip(reqDTO.getDialogueInput().getExtInfo());
        	if (ObjectUtil.isNotEmpty(orderTip)) {
        		messageEntity.setIconType(ChatMessageIconEnum.COMMAND_DIALOGUE.getIconType());
			} else if (null != reqDTO.getDialogueInput().getAttachment() && ResourceTypeEnum.contains(
					reqDTO.getDialogueInput().getAttachment().getAttachmentTypeList(), ResourceTypeEnum.PICTURE)) {
        		messageEntity.setIconType(ChatMessageIconEnum.IMAGE_DIALOGUE.getIconType());
        	} else {
        		messageEntity.setIconType(ChatMessageIconEnum.SIMPLE_DIALOGUE.getIconType());
        	}
        }

        boolean success = false;
        try {
            success = algorithmChatMessageRepository.saveMessage(messageEntity);
        } finally {
            log.info("新增会话信息 success:{}, 会话id:{}", success, messageEntity.getId());
        }
        return messageEntity;
    }


    /**
     * 生成会话标题
     *
     * @param reqDTO 请求参数
     * @return 会话标题
     */
    private String createTitle(ChatAddReqDTO reqDTO, String resourceName) {
        DialogueInputInfoDTO dialogueInput = reqDTO.getDialogueInput();
        String dialogue = dialogueInput.getDialogue();
        String extInfo = dialogueInput.getExtInfo();
        String commands = null;
        if (null != dialogueInput.getCommand()) {
            commands = dialogueInput.getCommand().getCommand();
        }
        boolean dialogueExist = CharSequenceUtil.isNotEmpty(dialogue);

        DialogueIntentionEnum intentionEnum = DialogueIntentionEnum.getByCodeOrDefault(commands);

        // 意图结果
        String intentionName = intentionEnum.getName();

        // 最后兜底名称，资源名称不存在，设置意图名称
		String finalName = StringUtils.isNotEmpty(resourceName) ? resourceName : intentionName;
		
        String orderTip = ChatAddUtils.getOrderTip(extInfo);
        if (StringUtils.isEmpty(orderTip)) {
            // 对话存在返回对话，对话不存在返回意图名称（兜底）
            return dialogueExist ? dialogue : finalName;
        } else {
            // 对话存在返回指令名称+对话，对话不存在只返回指令名称
            return dialogueExist ? orderTip.concat(SYMBOL).concat(dialogue) : orderTip;
        }
    }


    private Long getAgentSessionId(String userId, String sourceChannel, String applicationId) {
        // 智能体sessionId限制 userId+applicationId+businessType唯一
        // 增加redis缓存，不存在数据库复查, userId+applicationId+businessType做key
        String businessType = channelsProperties.getType(sourceChannel);

        // 获取缓存
        String sessionId = redisOperateRepository.getMessageSessionId(userId, applicationId, businessType);
        if (!StringUtils.isEmpty(sessionId)) {
            return Long.parseLong(sessionId);
        }

        // 查询数据库
        AlgorithmChatMessageEntity messageEntity = algorithmChatMessageRepository.selectOneByApplicationId(userId, applicationId, businessType);

        // 设置缓存
        if (Objects.nonNull(messageEntity)) {
            sessionId = String.valueOf(messageEntity.getId());
            redisOperateRepository.setMessageSessionId(userId, applicationId, businessType, sessionId);
            return messageEntity.getId();
        }
        return null;
    }
}
