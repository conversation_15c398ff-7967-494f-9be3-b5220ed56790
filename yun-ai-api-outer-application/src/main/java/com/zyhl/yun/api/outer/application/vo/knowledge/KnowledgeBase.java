package com.zyhl.yun.api.outer.application.vo.knowledge;

import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeProfilePhoto;
import lombok.Data;

/**
 * 知识库
 *
 * <AUTHOR>
 * @date 2025/04/14
 */
@Data
public class KnowledgeBase {

    /**
     * 知识库Id
     */
    private String baseId;

    /**
     * 知识库类型
     * 1--个人知识库
     * 2--分享知识库
     * 3--公共知识库
     */
    private Integer baseType;

    /**
     * 知识库名称
     */
    private String name;

    /**
     * 知识库描述
     */
    private String description;

    /**
     * 是否用户选择
     */
    private boolean selected;

    /**
     * 公开/私密（1 公开，0私密，默认为0）
     */
    private Integer openLevel;

    /**
     * 头像信息
     */
    private PersonalKnowledgeProfilePhoto profilePhoto;

    /**
     * 文件已解析数量
     */
    private Integer parsedCount;

    /**
     * 资源总数量
     */
    private Integer totalCount;

    /**
     * 文件未完成数量
     */
    private Integer unparsedCount;

    /**
     * 创建时间，RFC 3339，
     * 2019-08-20T06:51:27.292+08:00
     */
    private String createdAt;

    /**
     * 更新时间，RFC 3339，
     * 2019-08-20T06:51:27.292+08:00
     */
    private String updatedAt;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

}