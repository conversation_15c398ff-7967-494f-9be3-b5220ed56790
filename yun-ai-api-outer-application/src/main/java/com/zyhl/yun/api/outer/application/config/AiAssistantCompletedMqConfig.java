package com.zyhl.yun.api.outer.application.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * AI助手对话完成 mq配置
 * <AUTHOR>
 */
@Data
@Configuration
public class AiAssistantCompletedMqConfig {

	/**
	 * AI助手对话完成任务mq 事件类型
	 */
	public static final String AI_ASSISTANT_DIALOGUE_COMPLETED_EVENT_TYPE = "algorithm.ai.assistant.dialogue.completed";

	/**
	 * AI助手对话完成任务mq 消息id前缀
	 */
	public static final String AI_ASSISTANT_DIALOGUE_COMPLETED_MESSAGE_ID_PREFIX = "aiAssistantCompleted_";

	@Value("${rocketmq.producer.ai-assistant-dialogue-completed.topic}")
	private String topic;

	@Value("${rocketmq.producer.ai-assistant-dialogue-completed.tag}")
	private String tag;

}
