package com.zyhl.yun.api.outer.application.mq.dto;

import lombok.Builder;
import lombok.Data;

/**
 * AI助手对话完成MQ队列 TOPIC_ALGORITHM_AI-ASSISTANT_DIALOGUE-COMPLETED
 *
 * <AUTHOR>
 */
@Data
@Builder
public class AiAssistantCompletedMqDTO {

	/**
	 * 会话id
	 */
	private String sessionId;

	/**
	 * 对话id
	 */
	private String dialogueId;

	/**
	 * 对话完成时间，RFC 3339，示例:2024-05-06T06:51:27.292+08:00
	 */
	private String dialogueTime;

	/**
	 * AI指令
	 */
	private String commands;

	/**
	 * 渠道Id
	 */
	private String channelId;

	/**
	 * 应用类型：chat 普通对话，intelligent智能体对话
	 */
	private String applicationType;
}
