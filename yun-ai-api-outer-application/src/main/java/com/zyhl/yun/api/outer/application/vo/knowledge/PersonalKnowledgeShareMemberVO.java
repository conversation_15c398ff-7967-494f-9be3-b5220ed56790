package com.zyhl.yun.api.outer.application.vo.knowledge;

import com.zyhl.yun.api.outer.domain.dto.UserProfileInfoDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * Personal Knowledge Share Member VO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-04-17 00:43:14
 */
@Data
public class PersonalKnowledgeShareMemberVO implements Serializable {
    /**
     * 知识库ID
     */
    private String baseId;
    /**
     * 用信息户
     */
    private UserProfileInfoDTO userProfileInfo;
    /**
     * 成员等级
     * 0--创建者
     * 10--普通成员
     */
    private String level;
    /**
     * 加入时间，创建者没有时间，成员才有加入时间
     * RFC 3339，
     * 2019-08-20T06:51:27.292+08:00
     */
    private String joinTime;

}
