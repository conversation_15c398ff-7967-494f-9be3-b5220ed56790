package com.zyhl.yun.api.outer.application.convertor.dto.dynamic;

import com.zyhl.yun.api.outer.application.vo.SearchPageVO;
import com.zyhl.yun.api.outer.domain.req.SearchPageEntity;
import org.mapstruct.Mapper;


/**
 * 智能查询接口转换
 * <AUTHOR>
 */

@Mapper(componentModel = "spring")
public interface SearchEntityConvertor {


    /**
     * entity转换 pageVO
     * @param search entity
     * @return SearchPageVO
     */
    SearchPageVO toVO(SearchPageEntity search);

}