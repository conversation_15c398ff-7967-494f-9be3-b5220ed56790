package com.zyhl.yun.api.outer.application.chatv2.dto;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.BaseDTO;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 查询对话输出接口-DTO
 * @Author: WeiJingKun
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class AssistantChatV2PollingUpdateDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 1786127397896958005L;

    /** 对话id */
    private Long dialogueId;

    /**
     * 参数校验
     * @Author: WeiJingKun
     */
    public void validate() {
        /** 检查登录的userId */
        checkTokenUserId();

        /** 对话id不能为空 */
        if (null == dialogueId) {
            log.error("对话id不能为空");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS.getResultCode(), "对话id不能为空");
        }
    }

}
