package com.zyhl.yun.api.outer.application.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 对话信息流式结果VO
 *
 * <AUTHOR>
 * @date 2024/4/9 15:23
 */
@Data
public class FlowTypeResultVO {

    /**
     * 输出文本
     */
    private String outContent;

    
    /**
     * 输出时间
     */
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date outAuditTime;

    /**
     * 输出内容审批结果;状态码：2通过，其他失败
     */
    private Integer outAuditStatus;

    public FlowTypeResultVO() {

    }

    public FlowTypeResultVO(String outContent, Integer outAuditStatus) {
        this.outContent = outContent;
        this.outAuditStatus = outAuditStatus;
        this.outAuditTime = new Date();
    }
    
}
