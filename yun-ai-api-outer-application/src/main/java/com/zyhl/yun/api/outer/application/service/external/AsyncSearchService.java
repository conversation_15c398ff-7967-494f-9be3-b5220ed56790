package com.zyhl.yun.api.outer.application.service.external;


import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchCommonParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchResult;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.KeyValueVO;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

/**
 * 智能搜索（异步保存数据）
 *
 * <AUTHOR>
 */
public interface AsyncSearchService {

    /**
     * 智能搜索意图处理
     *
     * @param params 用户输入对象
     * @return 会话输入返回结果VO
     */
    AlgorithmChatAddVO searchIntentionHandle(ChatAddInnerDTO params);

    /**
     * 获取对话意图响应VO
     * @param dto    会话输入入参DTO
     * @param result 会话输入返回结果VO
     * @return 对话意图响应VO
     */
    DialogueIntentionVO getIntentionVO(AlgorithmChatAddDTO dto, AlgorithmChatAddVO result);

    /**
     * 设置授权报名记录
     * @Author: WeiJingKun
     * @param dto          会话输入DTO
     * @param resVO        会话输入返回结果VO
     * @param intention    对话意图
     */
    void handleSetRegisterStatus(AlgorithmChatAddDTO dto, AlgorithmChatAddVO resVO, String intention);

    /**
     * 设置结果列表
     *
     * @param resultList  结果列表
     * @param keyValueVoList KeyValue格式VO列表
     */
    void setResultList(List<String> resultList, List<KeyValueVO> keyValueVoList);

    /**
     * 构建发现广场搜索参数V2-搜索类型处理
     * @Author: WeiJingKun
     *
     * @param queryTypeStrList 查询类型字符串列表
     * @return java.util.List<java.lang.Integer>
     */
    @NotNull
    List<Integer> searchDiscoveryParamV2HandleQueryTypeList(List<String> queryTypeStrList);

    /**
     * 获取搜索结果
     * @param searchParam 搜索参数
     * @param searchCommonParam 搜索通用参数
     * @return 搜索结果
     * @throws ExecutionException 执行异常
     * @throws InterruptedException 打断异常
     * @throws TimeoutException 超时异常
     */
    SearchResult getSearchResult(SearchParam searchParam, SearchCommonParam searchCommonParam) throws ExecutionException, InterruptedException, TimeoutException;

}
