package com.zyhl.yun.api.outer.application.handle.chat.impl;

import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import com.zyhl.yun.api.outer.application.dto.*;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.zyhl.hcy.commons.enums.CommonResultCode;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.StringUtil;
import com.zyhl.hcy.yun.ai.common.model.api.client.blian.vo.TextModelFileVO;
import com.zyhl.yun.api.outer.application.handle.chat.AbstractChatAddHandler;
import com.zyhl.yun.api.outer.application.service.AlgorithmAiRegisterService;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatAddCheckService;
import com.zyhl.yun.api.outer.application.service.chat.ChatApplicationTypeService;
import com.zyhl.yun.api.outer.config.textmodel.LongTextModelConfig;
import com.zyhl.yun.api.outer.constants.RegConst;
import com.zyhl.yun.api.outer.domain.vo.ChatApplicationType;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.DialogueCommandTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.TalkTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.search.ImageSortTypeEnum;
import com.zyhl.yun.api.outer.repository.AlgorithmChatMessageRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;

/**
 * 参数校验
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ParamValidHandlerImpl extends AbstractChatAddHandler {

    @Resource
    private AlgorithmAiRegisterService algorithmAiRegisterService;
    @Resource
    private AlgorithmChatMessageRepository algorithmChatMessageRepository;
    @Resource
    private ChatApplicationTypeService chatApplicationTypeService;
    @Resource
    private AlgorithmChatAddCheckService algorithmChatAddCheckService;
    @Resource
    private LongTextModelConfig longTextModelConfig;

    /**
     * 表情字符，需要加提示词：以中文回复
     */
    private static final String EMOJI_WITH_CN = "以中文回复";

    @Override
    public int order() {
        return ExecuteSort.PARAM_VALID.getSort();
    }

    @Override
    public boolean run(ChatAddInnerDTO innerDTO) {
        log.info("进入参数处理");
        AlgorithmChatAddDTO dto = innerDTO.getReqParams();

        // 强制使用大模型对话
        if(Boolean.TRUE.equals(dto.getEnableForceLlm()) || Boolean.TRUE.equals(dto.getEnableAllNetworkSearch())){
            dto.getContent().setCommands(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());
        }

        // 用户id校验
        dto.checkTokenUserId();

        // 报名校验
        if (!algorithmAiRegisterService.checkAiAssistant(dto.getUserId())) {
            log.error("用户未报名，用户id：{}", dto.getUserId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_FORBIDDEN);
        }

        // 会话id
        if (!CharSequenceUtil.isEmpty(dto.getSessionId())) {
            if (!dto.getSessionId().matches(RegConst.REG_ID_STR)) {
                log.error("会话id格式不正确，会话id：{}", dto.getSessionId());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            } else if (algorithmChatMessageRepository.queryBySessionId(dto.getSessionId(), dto.getUserId()) == null) {
                log.error("会话id不存在，会话id：{}", dto.getSessionId());
                throw new YunAiBusinessException(ResultCodeEnum.SESSION_INFO_NOT_FOUND);
            }
        }

        // 应用类型
        if (CharSequenceUtil.isEmpty(dto.getApplicationType())) {
            dto.setApplicationType(ApplicationTypeEnum.CHAT.getCode());
        } else if (!ApplicationTypeEnum.isExist(dto.getApplicationType())) {
            log.error("应用类型不存在，应用类型：{}", dto.getApplicationType());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        // 应用id
        if (ApplicationTypeEnum.isChat(dto.getApplicationType())) {
            dto.setApplicationId("");
        } else if (ApplicationTypeEnum.isIntelligen(dto.getApplicationType())) {
            if (CharSequenceUtil.isEmpty(dto.getApplicationId())) {
                log.error("应用id为空，应用id：{}", dto.getApplicationId());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }
            ChatApplicationType type = chatApplicationTypeService.getByApplicationId(dto.getApplicationId());
            if (type == null || !ApplicationTypeEnum.isIntelligen(dto.getApplicationType())) {
                log.error("应用id不存在，应用id：{}，应用类型：{}", dto.getApplicationId(), dto.getApplicationType());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }
        }

        // 接口版本
        dto.setApiVersion(CharSequenceUtil.emptyToDefault(RequestContextHolder.getApiVersion(), dto.getApiVersion()));

        // 对话内容信息
        contentValid(innerDTO);

        //有些数据重新设置过，需要再初始化一次
        innerDTO.init();

        return true;
    }

    /**
     * 内容对象校验
     *
     * @param innerDTO 请求参数
     */
    private void contentValid(ChatAddInnerDTO innerDTO) {
        AlgorithmChatAddDTO dto = innerDTO.getReqParams();
        AlgorithmChatAddContentDTO contentDTO = dto.getContent();

        // 对话内容信息
        if (Objects.isNull(contentDTO)) {
            log.error("内容参数为空");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        // 渠道来源
        String sourceChannel = contentDTO.getSourceChannel();
        if (CharSequenceUtil.isEmpty(sourceChannel)) {
            log.error("来源渠道为空，来源渠道：{}", sourceChannel);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        } else if (!sourceChannelsProperties.isExist(sourceChannel)) {
            log.error("来源渠道不存在，来源渠道：{}", sourceChannel);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        // 根据渠道，set助手类型
        String businessCode = sourceChannelsProperties.getCode(sourceChannel);
        AssistantEnum assistantEnum = AssistantEnum.getByCode(businessCode);
        if (ObjectUtil.isNull(assistantEnum)) {
            log.error("助手类型不存在，来源渠道：{}", sourceChannel);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        contentDTO.setAssistantEnum(assistantEnum);

        // 资源类型校验
        resourceTypeValid(innerDTO);

        // 指令
        if (CharSequenceUtil.isEmpty(contentDTO.getCommands())) {
            if (ApplicationTypeEnum.isIntelligen(dto.getApplicationType())) {
                contentDTO.setCommands(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());
            } else if (CharSequenceUtil.isEmpty(contentDTO.getDialogue())) {
                log.error("对话指令和对话内容不能同时为空，指令：{}，对话内容：{}", contentDTO.getCommands(), contentDTO.getDialogue());
                throw new YunAiBusinessException(CommonResultCode.ERROR_PARAMS);
            }
        } else if (!DialogueIntentionEnum.isExist(contentDTO.getCommands())) {
            log.error("对话指令不存在，指令：{}", contentDTO.getCommands());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INTENTION_CODE);

        }

        // 对话类型
        if (!TalkTypeEnum.isExist(contentDTO.getDialogueType())) {
            log.error("对话类型不存在，对话类型：{}", contentDTO.getDialogueType());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_TALK_CODE);
        }

        // 命令类型
        if (contentDTO.getCommandType() == null) {
            contentDTO.setCommandType(DialogueCommandTypeEnum.ORDINARY.getType());
        } else if (!DialogueCommandTypeEnum.isExist(contentDTO.getCommandType())) {
            log.error("命令类型不存在，命令类型：{}", contentDTO.getCommandType());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        // 语义搜图排序
        if (contentDTO.getSortInfo() != null && !ImageSortTypeEnum.isExist(contentDTO.getSortInfo().getImageSortType())) {
            log.error("图片语义搜索排序方式不存在，排序方式：{}", contentDTO.getSortInfo().getImageSortType());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        // 扩展信息
        try {
            if (CharSequenceUtil.isNotEmpty(contentDTO.getExtInfo())) {
                JSONObject jsonObject = new JSONObject(contentDTO.getExtInfo());
                RequestContextHolder.getHeaderParams().setH5Version(jsonObject.getStr("h5Version", ""));
                RequestContextHolder.getHeaderParams().setPcVersion(jsonObject.getStr("pcVersion", ""));
            }
        } catch (Exception e) {
            log.error("扩展信息解析异常，扩展信息：{}，异常信息：{}", contentDTO.getExtInfo(), e.getMessage(), e);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        // 纯表情或者纯数字并且没输入prompt
        boolean blankPrompt = CharSequenceUtil.isBlank(contentDTO.getPrompt());
        boolean isNumericOrEmoji = StringUtil.isNumeric(contentDTO.getDialogue()) || StringUtil.allEmoji(contentDTO.getDialogue());
        if (blankPrompt && isNumericOrEmoji) {
            log.info("对话内容为纯数字或纯表情并且提示词为空，默认添加提示词：{}", EMOJI_WITH_CN);
            contentDTO.setPrompt(EMOJI_WITH_CN);
        }

    }

    /**
     * 资源类型校验
     *
     * @param innerDTO 请求参数
     */
    private void resourceTypeValid(ChatAddInnerDTO innerDTO) {
        AlgorithmChatAddDTO dto = innerDTO.getReqParams();
        AlgorithmChatAddContentDTO contentDTO = dto.getContent();

        // 资源类型
        if (contentDTO.getResourceType() == null) {
            contentDTO.setResourceType(ResourceTypeEnum.TEXT.getType());
        } else if (!ResourceTypeEnum.isExist(contentDTO.getResourceType())) {
            log.error("资源类型不存在，资源类型：{}", contentDTO.getResourceType());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        // 对话资源
        if (ResourceTypeEnum.isText(contentDTO.getResourceType())) {
            // 文本类型
            contentDTO.setResourceId("");
            if (CharSequenceUtil.isEmpty(contentDTO.getDialogue())) {
                log.error("对话内容为空，对话内容：{}", contentDTO.getDialogue());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }
        } else if (ResourceTypeEnum.isMail(contentDTO.getResourceType())) {
            // 邮件类型
            if (CharSequenceUtil.isEmpty(contentDTO.getResourceId())) {
                log.error("邮件资源id为空，资源id：{}", contentDTO.getResourceId());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            } else if (CharSequenceUtil.isEmpty(contentDTO.getDialogue()) && CharSequenceUtil.isEmpty(contentDTO.getPrompt())) {
                log.error("邮件类型对话内容和提示词不能同时为空，对话内容：{}，提示词：{}", contentDTO.getDialogue(), contentDTO.getPrompt());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }
        } else if (ResourceTypeEnum.isNote(contentDTO.getResourceType())) {
            // 笔记类型
            if (CharSequenceUtil.isEmpty(contentDTO.getResourceId())) {
                log.error("笔记资源id为空，资源id：{}", contentDTO.getResourceId());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            } else if (CharSequenceUtil.isEmpty(contentDTO.getDialogue()) && CharSequenceUtil.isEmpty(contentDTO.getPrompt())) {
                log.error("笔记类型对话内容和提示词不能同时为空，对话内容：{}，提示词：{}", contentDTO.getDialogue(), contentDTO.getPrompt());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }
        } else if (ResourceTypeEnum.isImage(contentDTO.getResourceType())) {
            // 图片类型
            if (CharSequenceUtil.isEmpty(contentDTO.getResourceId())) {
                log.error("图片资源id为空，资源id：{}", contentDTO.getResourceId());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }
        } else if (ResourceTypeEnum.isDialogueId(contentDTO.getResourceType())) {
            // 对话id类型
            if (CharSequenceUtil.isEmpty(contentDTO.getResourceId())) {
                log.error("对话id类型，资源id为空，资源id：{}", contentDTO.getResourceId());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            } else if (!contentDTO.getResourceId().matches(RegConst.REG_ID_STR)) {
                log.error("对话id格式不正确，资源id：{}", contentDTO.getResourceId());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            } else if (CharSequenceUtil.isEmpty(contentDTO.getCommands())) {
                log.error("对话指令不能为空，指令：{}", contentDTO.getCommands());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }

            // 获取上次对话信息
            LastDialogueInfoDTO lastDialogueInfo = algorithmChatAddCheckService.getLastDialogueInfo(contentDTO.getResourceId(), dto.getUserId());
            if (lastDialogueInfo == null) {
                log.error("上次对话id无效，对话id：{}", contentDTO.getResourceId());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_RESOURCE_ID_INVALID_CODE);
            }

            // 设置上次对话信息
            contentDTO.setLastDialogueInfo(lastDialogueInfo);
        } else if (ResourceTypeEnum.isDocument(contentDTO.getResourceType())) {
            // 文档类型
            documentTypeValid(dto, contentDTO);
        } else if (ResourceTypeEnum.isAttachment(contentDTO.getResourceType())) {
            // 附件类型
            attachmentValid(dto, contentDTO);
        } else if (ResourceTypeEnum.isPersonalKnowledgeFile(contentDTO.getResourceType())) {
            // 个人知识库类型
            if (CharSequenceUtil.isEmpty(contentDTO.getResourceId())) {
                log.error("个人知识库资源id为空，资源id：{}", contentDTO.getResourceId());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }
            try {
                ResourceInfoDTO resourceInfo = JSON.parseObject(contentDTO.getResourceId(), ResourceInfoDTO.class);
                if (Objects.isNull(resourceInfo) || ObjectUtil.isEmpty(resourceInfo.getFileIdList())) {
                    log.error("个人知识库资源id为空，资源id：{}", contentDTO.getResourceId());
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }
                innerDTO.setResourceInfo(resourceInfo);
            } catch (Exception e) {
                log.error("个人知识库资源id格式不正确，资源id：{}", contentDTO.getResourceId());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }
        }
    }

    /**
     * 文档类型校验
     *
     * @param dto        请求参数
     * @param contentDTO 请求参数
     */
    private void documentTypeValid(AlgorithmChatAddDTO dto, AlgorithmChatAddContentDTO contentDTO) {
        if (CharSequenceUtil.isEmpty(contentDTO.getResourceId())) {
            log.error("文档资源id为空，资源id：{}", contentDTO.getResourceId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        } else if (CharSequenceUtil.isEmpty(contentDTO.getDialogue()) && CharSequenceUtil.isEmpty(contentDTO.getPrompt())) {
            log.error("文档类型对话内容和提示词不能同时为空，对话内容：{}，提示词：{}", contentDTO.getDialogue(), contentDTO.getPrompt());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        try {
            CloudDiskDocumentDTO documentDto = JSON.parseObject(contentDTO.getResourceId(), CloudDiskDocumentDTO.class);
            //按模型版本处理长文本
            if (longTextModelConfig.isQwenLongVersion()) {
                //阿里百炼千问长文本模型处理流程
                List<TextModelFileVO> fileIdList = algorithmChatAddCheckService.getFilesByCloudDiskDocument(documentDto.getFileIdList(), dto.getUserId(), false);
                if (CollUtil.isEmpty(fileIdList)) {
                    log.info("文档资源id为空，资源id：{}", contentDTO.getResourceId());
                    throw new YunAiBusinessException(ResultCodeEnum.FILE_ID_INFO_ERROR);
                }
                contentDTO.setTextModelFiles(fileIdList);
            } else if (longTextModelConfig.isUserModelVersion()) {
                //用户选择本模型处理流程
                List<String> localPathList = algorithmChatAddCheckService.getFilesByCloudDiskDocumentLocalPath(documentDto.getFileIdList(), dto.getUserId(), false);
                contentDTO.setLongTextFileLocalPathList(localPathList);
            } else {
                log.error("参数异常，文档资源校验，其他模型版本暂不支持... version:{}", longTextModelConfig.getVersion());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }
        } catch (YunAiBusinessException e) {
            log.error("文档资源校验失败，资源id：{}，失败原因：{}", contentDTO.getResourceId(), e.getMessage());
            throw e;
        } catch (JSONException e) {
            log.error("文档资源id解析失败，资源id：{}，失败原因：{}", contentDTO.getResourceId(), e.getMessage());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        } catch (Exception e) {
            log.error("文档资源id校验失败，资源id：{}，失败原因：{}", contentDTO.getResourceId(), e.getMessage());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
    }

    /**
     * 附件类型校验
     *
     * @param dto        请求参数
     * @param contentDTO 请求参数
     */
    private void attachmentValid(AlgorithmChatAddDTO dto, AlgorithmChatAddContentDTO contentDTO) {
        if (CharSequenceUtil.isEmpty(contentDTO.getResourceId())) {
            log.error("邮件附件资源id为空，资源id：{}", contentDTO.getResourceId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        } else if (CharSequenceUtil.isEmpty(contentDTO.getDialogue()) && CharSequenceUtil.isEmpty(contentDTO.getPrompt())) {
            log.error("邮件附件类型对话内容和提示词不能同时为空，对话内容：{}，提示词：{}", contentDTO.getDialogue(), contentDTO.getPrompt());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        try {
            MailAttachmentDTO attachmentDto = JSON.parseObject(contentDTO.getResourceId(), MailAttachmentDTO.class);
            //按模型版本处理长文本
            if (longTextModelConfig.isQwenLongVersion()) {
                List<TextModelFileVO> fileIdList = algorithmChatAddCheckService.getFilesByEmailAttachment(attachmentDto, dto);
                if (CollUtil.isEmpty(fileIdList)) {
                    log.info("邮件附件id为空，资源id：{}", contentDTO.getResourceId());
                    throw new YunAiBusinessException(ResultCodeEnum.FILE_ID_INFO_ERROR);
                }
                contentDTO.setTextModelFiles(fileIdList);
            } else if (longTextModelConfig.isUserModelVersion()) {
                List<String> localPathList = algorithmChatAddCheckService.getFilesByEmailAttachmentLocalPath(attachmentDto, dto);
                contentDTO.setLongTextFileLocalPathList(localPathList);
            } else {
                log.error("参数异常，邮件附件id校验，其他模型版本暂不支持... version:{}", longTextModelConfig.getVersion());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }
        } catch (YunAiBusinessException e) {
            log.error("邮件附件id校验失败，资源id：{}，失败原因：{}", contentDTO.getResourceId(), e.getMessage());
            throw e;
        } catch (JSONException e) {
            log.error("邮件附件资源id解析失败，资源id：{}，失败原因：{}", contentDTO.getResourceId(), e.getMessage(), e);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        } catch (Exception e) {
            log.error("邮件附件id校验失败，资源id：{}，失败原因：{}", contentDTO.getResourceId(), e.getMessage(), e);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
    }

}
