package com.zyhl.yun.api.outer.application.handle.chat.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.hcy.yun.ai.common.base.utils.StopWatchUtil;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.event.TextModelStreamEventListener;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveFileVO;
import com.zyhl.hcy.yun.ai.common.rag.vo.RecallResultVO;
import com.zyhl.hcy.yun.ai.common.rag.vo.RerankResultVO;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.dto.TextModelDTO;
import com.zyhl.yun.api.outer.application.handle.chat.AbstractChatAddHandler;
import com.zyhl.yun.api.outer.application.handle.chat.listener.SseDialogueEventListener;
import com.zyhl.yun.api.outer.application.service.knowledge.KnowledgeRecallUpdateService;
import com.zyhl.yun.api.outer.application.util.SseEmitterDataUtils;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeTemplateInfoVO;
import com.zyhl.yun.api.outer.config.KnowledgeDialogueProperties;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.config.knowledge.DigitalSummitProperties;
import com.zyhl.yun.api.outer.config.textmodel.ModelPromptProperties;
import com.zyhl.yun.api.outer.config.textmodel.SpecialPromptProperties;
import com.zyhl.yun.api.outer.domain.dto.RecallDTO;
import com.zyhl.yun.api.outer.domain.dto.RerankDTO;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.domainservice.KnowledgeDomainService;
import com.zyhl.yun.api.outer.domainservice.QpsLimitService;
import com.zyhl.yun.api.outer.enums.*;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeBaseEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.external.RagExternalService;
import com.zyhl.yun.api.outer.external.UserDriveExternalService;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 文本意图，知识库对话流式返回
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class KnowledgeSseHandlerImpl extends AbstractChatAddHandler {

    private static final String REPLACE_KEY_OF_HISTORY = "{history}";
    private static final String REPLACE_KEY_OF_POLITICIAN = "{politician}";
    private static final String REPLACE_KEY_OF_KNOWLEDGE = "{knowledge}";
    private static final String REPLACE_KEY_OF_QUERY = "{query}";
    private static final String RAG_SYSTEM_PROMPT = "rag_system_prompt";
    private static final String RAG_USER_PROMPT = "rag_user_prompt";

    @Resource
    private SpecialPromptProperties specialPromptProperties;
    @Resource
    private KnowledgeDialogueProperties knowledgeDialogueProperties;
    @Resource
    private ModelProperties modelProperties;
    @Resource
    private KnowledgeDomainService knowledgeDomainService;
    @Resource
    private TextModelExternalService textModelExternalService;
    @Resource
    private QpsLimitService qpslimitService;
    @Resource
    private UserDriveExternalService userDriveExternalService;
    @Resource
    private RagExternalService ragExternalService;
    @Resource
    private KnowledgeRecallUpdateService knowledgeRecallUpdateService;
    @Resource
    private ModelPromptProperties modelPromptProperties;
    @Resource
    private ChatConfigServiceDomainService chatConfigServiceDomainService;
    @Resource
    private UserKnowledgeFileRepository userKnowledgeFileRepository;
    @Resource
    private DigitalSummitProperties summitProperties;

    @Override
    public int order() {
        return ExecuteSort.KNOWLEDGE_SSE.getSort();
    }

    @Override
    public boolean execute(ChatAddInnerDTO innerDTO) {
        // 非流式对话 || 非对话类型 || 非(文本资源 || 知识库资源)|| 非文本意图 || 提示词非空 || 非(小天渠道 || 指定渠道) || 强制大模型对话
        String channel = innerDTO.getContent().getSourceChannel();
        Integer resourceType = innerDTO.getContent().getResourceType();
        if (innerDTO.getSseEmitter() == null
                || ApplicationTypeEnum.isNotChat(innerDTO.getReqParams().getApplicationType())
                || !(ResourceTypeEnum.isText(resourceType) || ResourceTypeEnum.isPersonalKnowledgeFile(resourceType))
                || !DialogueIntentionEnum.isTextIntention(innerDTO.getIntentionCode())
                || CharSequenceUtil.isNotEmpty(innerDTO.getContent().getPrompt())
                || !(sourceChannelsProperties.isXiaoTian(channel) || knowledgeDialogueProperties.getEnableChannelList().contains(channel))
                || Boolean.TRUE.equals(innerDTO.getReqParams().getEnableForceLlm())) {
            return false;
        }

        // 个人知识库 或者 公共知识库  有一个可用则执行
        if (ResourceTypeEnum.isText(resourceType)) {
            innerDTO.setCommonEnable(knowledgeDomainService.commonEnable(innerDTO.getContent().getSourceChannel()));
        }
        innerDTO.setPersonalEnable(knowledgeDomainService.personalEnable(innerDTO.getReqParams().getUserId()));

        log.info("【知识库对话】公共知识库可用：{}，个人知识库可用：{}", innerDTO.isCommonEnable(), innerDTO.isPersonalEnable());
        return innerDTO.isCommonEnable() || innerDTO.isPersonalEnable();
    }

    @Override
    public boolean run(ChatAddInnerDTO innerDTO) {
        log.info("进入知识库流式对话处理");

        String sseName = SseEmitterDataUtils.SSE_NAME_KNOWLEDGE;
        String userId = innerDTO.getReqParams().getUserId();
        List<TextModelMessageDTO> historyList = aiTextResultRepository.getHistoryList(userId, innerDTO.getReqParams().getSessionId());

        // query重写
        TextModelDTO modelDTO = new TextModelDTO(innerDTO.getReqParams(), innerDTO.getRespParams(), "", historyList, "");
        String query = ragExternalService.questionRewrite(modelDTO.toTextReqDTO(null));

        // query向量化
        List<BigDecimal> featureList = ragExternalService.textFeature(userId, query, innerDTO.getDialogueId());
        if (ObjectUtil.isEmpty(featureList)) {
            return failHandle(innerDTO);
        }

        // 数字峰会知识库处理
        boolean phoneFlag = summitProperties.getWhiteList().contains(RequestContextHolder.getPhoneNumber());
        boolean queryKeyFlag = true;
        for (String key : summitProperties.getQueryKeys()) {
            String[] splitList = key.split(",");
            List<String> newSplitList = Arrays.stream(splitList).filter(StrUtil::isNotBlank).collect(Collectors.toList());
            queryKeyFlag = queryKeyFlag && newSplitList.stream().anyMatch(query::contains);
        }

        // 用户符合白名单 并且 输入的对话包含数字峰会搜索词 并且文件id列表不为空
        if (phoneFlag && queryKeyFlag && !CollectionUtils.isEmpty(summitProperties.getFileIds())) {
            return digitalSummitHandle(innerDTO, sseName, userId, historyList, modelDTO, query, featureList);
        }

        // es搜索
        RecallDTO recallDTO = new RecallDTO(innerDTO.isCommonEnable(), innerDTO.isPersonalEnable());
        recallDTO.setText(query);
        recallDTO.setFeature(featureList);
        recallDTO.setUserId(userId);
        recallDTO.setIntentionVO(innerDTO.getIntentionVO());
        recallDTO.setFileIdList(innerDTO.getResourceInfo().getFileIdList());
        List<RecallResultVO> recallResult = ragExternalService.recall(recallDTO);
        if (CollUtil.isEmpty(recallResult)) {
            log.info("【知识库对话】【RAG重要节点日志】【多路召回】结果为空");
            return failHandle(innerDTO);
        }

        // 算法重排
        RerankDTO rerankDTO = new RerankDTO();
        rerankDTO.setText(innerDTO.getContent().getDialogue());
        rerankDTO.setRecallList(recallResult);
        List<RerankResultVO> rerankResult = ragExternalService.rerank(rerankDTO);
        if (CollUtil.isEmpty(rerankResult)) {
            log.info("【知识库对话】【RAG重要节点日志】【rag算法重排】结果为空");
            return failHandle(innerDTO);
        }

        //query和分块相关度判断（大模型+提示词）
        int rerankResultSize = rerankResult.size();
        rerankResult = ragExternalService.relevancy(modelDTO.toSingleTextReqDTO(innerDTO.getContent().getDialogue()), rerankResult);
        if (CollUtil.isEmpty(rerankResult)) {
            log.info("【知识库对话】【RAG重要节点日志】【知识库相关性】query和分块相关度判断后，结果为空");
            return failHandle(innerDTO);
        }
        if (rerankResultSize != rerankResult.size()) {
            //知识库判断命中拦截
            sseName = SseEmitterDataUtils.SSE_NAME_KNOWLEDGE_RELEVANCY;
        }

        // 异步更新ES库召回次数
        knowledgeRecallUpdateService.recallUpdate(recallResult, rerankResult);

        // 开头文案
        String knowledgeBaseCode = getHitBase(recallResult, rerankResult);
        String titleText = knowledgeDialogueProperties.getTitleMap().getOrDefault(knowledgeBaseCode, "");

        // 个人知识库文件信息
        List<File> fileList = getFileList(innerDTO.getReqParams().getUserId(), recallResult, rerankResult);
        if (!KnowledgeBaseEnum.isCommon(knowledgeBaseCode) && CollUtil.isEmpty(fileList)) {
            log.info("【知识库对话】【RAG重要节点日志】命中个人知识库，但是文件不存在或者获取文件信息失败");
            return failHandle(innerDTO);
        }

        // 扣减权益
        memberCenterService.consumeBenefit(innerDTO.getReqParams(), RequestContextHolder.getPhoneNumber(), innerDTO.getDialogueId());

        //保存
        saveTextResult(innerDTO, "", "");
        add(innerDTO, ChatStatusEnum.CHAT_IN);

        // 监听器
        SseDialogueEventListener event = new SseDialogueEventListener(innerDTO, historyList);
        event.setTitle(titleText);
        event.setFileList(fileList);
        event.setSseName(sseName);

        // 智能调度
        KnowledgeTemplateInfoVO templateInfoVO = new KnowledgeTemplateInfoVO(innerDTO.getContent().getDialogue());
        templateInfoVO.knowledgeHandle(recallResult, rerankResult, fileList);

        schedule(innerDTO, event, templateInfoVO);

        return false;
    }

    /**
     * 智能调度
     *
     * @param innerDTO     用户输入对象
     * @param event        流式监听参数
     * @param templateInfo 对话模板
     */
    private void schedule(ChatAddInnerDTO innerDTO, SseDialogueEventListener event, KnowledgeTemplateInfoVO templateInfo) {
        // 开启计时
        StopWatch stopWatch = StopWatchUtil.createStarted();
        try {
            ChatConfigEntity chatConfigEntity = chatConfigServiceDomainService.getUserCanUseModel(innerDTO.getReqParams().getUserId(), RequestContextHolder.getPhoneNumber(), innerDTO.getContent().getAssistantEnum(), innerDTO.getContent().getBusinessType());
            List<String> executeSort = new ArrayList<>();
            executeSort.add(chatConfigEntity.getModelType());
            executeSort.addAll(Arrays.asList(knowledgeDialogueProperties.getDialogueConfig().getModelCode().split(",")));

            AbstractResultCode error = null;
            boolean success = false;
            for (String code : executeSort) {
                error = null;

                // qps限制
                if (!qpslimitService.modelQpsLimit(code)) {
                    log.info("【知识库对话】【RAG重要节点日志】请求过多，qps限流，model:{}", code);
                    error = ResultCodeEnum.ERROR_LIMITATION;
                } else {

                    try {
                        TextModelTextReqDTO reqDTO = event.getTextDto().toTextReqDTO(modelProperties.getMaxLength(innerDTO.getContent().getAssistantEnum(), innerDTO.getContent().getBusinessType(), code));
                        event.setModelCode(code);
                        // 知识库调用大模型不加角标
                        reqDTO.setEnableNetworkSearchCitation(false);

                        templateInfo.historyHandle(reqDTO);
                        success = modelStreamHandle(reqDTO, code, event, templateInfo);

                        // 更新模型编码
                        algorithmChatContentRepository.updateModelCode(event.getDialogId(), code);
                        innerDTO.getRespParams().setModelType(code);
                        break;
                    } catch (YunAiBusinessException e) {
                        error = e.getExceptionEnum();
                        log.error("【知识库对话】【RAG重要节点日志】调用文本大模型异常:{}，对话id:{}", e.getMessage(), event.getDialogId(), e);
                    } catch (Exception e) {
                        log.error("【知识库对话】【RAG重要节点日志】调用文本大模型异常:{}，对话id:{}", e.getMessage(), event.getDialogId(), e);
                    }
                }

            }

            if (!success) {
                log.warn("【知识库对话】【RAG重要节点日志】对话失败，智能调度大文本模型处理失败，对话id：{}", event.getDialogId());
                if (error != null) {
                    event.dialogueFail(error);
                } else {
                    event.dialogueFail(ResultCodeEnum.ERROR_SERVER_INTERNAL);
                }
            }
        } finally {
            log.info("【知识库对话】【RAG重要节点日志】结束，耗时：{}，对话id：{}\n 最终结果：{}",
                    StopWatchUtil.logTime(stopWatch), event.getDialogId(), JsonUtil.toJson(event.getAllMsg()));
            StopWatchUtil.clearDuration();
        }

    }

    /**
     * 调大文本模型流式处理
     *
     * @param reqDTO 请求参数
     * @param code   模型编码
     * @param event  监听事件
     */
    private boolean modelStreamHandle(TextModelTextReqDTO reqDTO, String code, TextModelStreamEventListener event, KnowledgeTemplateInfoVO templateInfo) {

        String phoneNum = RequestContextHolder.getPhoneNumber();

        // 系统提示词
        String systemPrompt;
        // 用户输入内容
        String userPrompt;
        if (specialPromptProperties.getAccountList().contains(phoneNum)) {
            log.info("【知识库对话】v1知识库对话提示词沿用`指定`提示词，用户手机号：{}", phoneNum);
            systemPrompt = specialPromptProperties.getPrompt(RAG_SYSTEM_PROMPT, code);
            userPrompt = specialPromptProperties.getPrompt(RAG_USER_PROMPT, code);
        } else {
            log.info("【知识库对话】v1知识库对话提示词沿用`通用`提示词，用户手机号：{}", phoneNum);
            systemPrompt = modelPromptProperties.getPrompt(RAG_SYSTEM_PROMPT, code);
            userPrompt = modelPromptProperties.getPrompt(RAG_USER_PROMPT, code);
        }
        LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】使用的system提示词第{}个分块：\n{}", systemPrompt);
        LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】使用的user提示词第{}个分块：\n{}", userPrompt);

        // 行政人物处理
        templateInfo.politicianHandle(knowledgeDialogueProperties, systemPrompt);

        // 系统提示词
        systemPrompt = systemPrompt.replace(REPLACE_KEY_OF_HISTORY, templateInfo.getHistory())
                .replace(REPLACE_KEY_OF_KNOWLEDGE, templateInfo.getKnowledge())
                .replace(REPLACE_KEY_OF_POLITICIAN, templateInfo.getPolitician());

        // 用户输入内容
        userPrompt = userPrompt.replace(REPLACE_KEY_OF_QUERY, templateInfo.getQuery())
                .replace(REPLACE_KEY_OF_HISTORY, templateInfo.getHistory())
                .replace(REPLACE_KEY_OF_KNOWLEDGE, templateInfo.getKnowledge())
                .replace(REPLACE_KEY_OF_POLITICIAN, templateInfo.getPolitician());

        // 新的对话信息
        TextModelMessageDTO msgDTO = new TextModelMessageDTO();
        msgDTO.setRole(TextModelRoleEnum.USER.getName());
        msgDTO.setContent(userPrompt);
        msgDTO.setCommand(systemPrompt);

        // 调大模型
        templateInfo.getHistoryList().add(msgDTO);
        TextModelTextReqDTO req = genTextModelTextReq(reqDTO, templateInfo.getHistoryList());
        log.info("【知识库对话】【RAG重要节点日志】模型编码：{}，大模型配置参数：{}", code, JsonUtil.toJson(req.getTextModelConfig()));
        LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】调大文本模型入参{}个分块：\n{}", JsonUtil.toJson(req));
        return textModelExternalService.streamDialogue(code, req, event);
    }

    /**
     * 获取新的对话信息
     *
     * @param reqDTO  模型请求
     * @param msgList 消息请求
     * @return 模型请求实体
     */
    private TextModelTextReqDTO genTextModelTextReq(TextModelTextReqDTO reqDTO, List<TextModelMessageDTO> msgList) {
        TextModelTextReqDTO req = new TextModelTextReqDTO();
        req.setTaskId(reqDTO.getTaskId());
        req.setUserId(reqDTO.getUserId());
        req.setSessionId(reqDTO.getSessionId());
        req.setMessageDtoList(msgList);
        req.setTextModelConfig(knowledgeDialogueProperties.getDialogueConfig().getTextModelConfig());
        req.setEnableForceNetworkSearch(reqDTO.getEnableForceNetworkSearch());
        req.setEnableNetworkSearchCitation(reqDTO.getEnableNetworkSearchCitation());
        return req;
    }

    /**
     * 命中知识库
     *
     * @param esResult     召回结果
     * @param rerankResult 重排结果
     * @return 开头文案
     */
    private String getHitBase(List<RecallResultVO> esResult, List<RerankResultVO> rerankResult) {
        Set<String> baseSet = new HashSet<>();
        for (RerankResultVO vo : rerankResult) {
            RecallResultVO item = esResult.get(Integer.parseInt(vo.getDocument().getSegmentId()));
            baseSet.add(item.getKnowledgeBase());
        }

        String code = "";
        if (baseSet.size() >= KnowledgeBaseEnum.baseCount()) {
            code = KnowledgeBaseEnum.KNOWLEDGE.getCode();
        } else {
            code = baseSet.toArray(new String[0])[0];
        }
        log.info("【知识库对话】命中的知识库：{}", baseSet);
        return code;
    }

    /**
     * 查询个人知识库文件信息
     *
     * @param userId       用户id
     * @param esResult     召回结果
     * @param rerankResult 重排结果
     * @return 引用文件列表
     */
    private List<File> getFileList(String userId, List<RecallResultVO> esResult, List<RerankResultVO> rerankResult) {
        List<File> list = new ArrayList<>();
        Set<String> fileIds = new HashSet<>();
        for (RerankResultVO vo : rerankResult) {
            RecallResultVO item = esResult.get(Integer.parseInt(vo.getDocument().getSegmentId()));
            if (KnowledgeBaseEnum.isPersonal(item.getKnowledgeBase())) {
                fileIds.add(item.getFileId());
            }
        }
        if (ObjectUtil.isEmpty(fileIds)) {
            return list;
        }
        try {
            // 先查数据库，获取文件类型，根据不同的类型处理
            List<UserKnowledgeFileEntity> entityList = userKnowledgeFileRepository.selectByFileIds(userId, new ArrayList<>(fileIds));
            for (UserKnowledgeFileEntity entity : entityList) {
                if (KnowledgeResourceTypeEnum.isPersonalFile(entity.getFromResourceType())) {
                    // 查询文件信息
                    try {
                        OwnerDriveFileVO fileInfo = userDriveExternalService.getFileInfo(userId, entity.getFileId());
                        list.add(new File(fileInfo));
                    } catch (Exception e) {
                        log.error("【知识库对话】查询独立空间文件详细异常，文件id：{}，异常信息：{}", entity.getFileId(), e.getMessage(), e);
                    }
                } else {
                    File file = new File();
                    file.setFileId(entity.getFileId());
                    file.setName(entity.getFileName());
                    list.add(file);
                }
            }
        } catch (Exception e) {
            log.error("【知识库对话】查询独立空间文件详细异常，文件id：{}，异常信息：{}", fileIds, e.getMessage(), e);
        }

        return list;
    }

    /**
     * 知识库对话失败处理
     *
     * @param innerDTO 请求参数
     * @return Boolean
     */
    private boolean failHandle(ChatAddInnerDTO innerDTO) {
        if (ResourceTypeEnum.isText(innerDTO.getContent().getResourceType())) {
            return true;
        }

        //保存
        AiTextResultRespParameters resp = new AiTextResultRespParameters(ResultCodeEnum.KNOWLEDGE_CONTENT_EMPTY);
        saveHbaseAllChatResult(innerDTO, resp);
        addSuccess(innerDTO, OutContentTypeEnum.TEXT);

        // 返回给前端
        try {
            BaseResult result = BaseResult.error(ResultCodeEnum.KNOWLEDGE_CONTENT_EMPTY, innerDTO.getRespParams());
            SseEmitterDataUtils.sendMsg(innerDTO.getSseEmitter(), result, new AtomicBoolean(false), false);
            SseEmitterDataUtils.complete(innerDTO.getSseEmitter(), new AtomicBoolean(false));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return false;
    }


    /**
     * 数字峰会对话处理
     */
    private boolean digitalSummitHandle(ChatAddInnerDTO innerDTO, String sseName, String userId, List<TextModelMessageDTO> historyList,
                                        TextModelDTO modelDTO, String query, List<BigDecimal> featureList) {
        log.info("【知识库对话】【RAG重要节点日志】【数字峰会】处理开始 userId:{} | query:{}", userId, query);

        // es搜索
        List<String> fileIds = summitProperties.getFileIds();
        RecallDTO recallDTO = new RecallDTO(innerDTO.isCommonEnable(), innerDTO.isPersonalEnable());
        recallDTO.setText(query);
        recallDTO.setFeature(featureList);
        recallDTO.setUserId(userId);
        recallDTO.setIntentionVO(innerDTO.getIntentionVO());
        recallDTO.setFileIdList(fileIds);
        List<RecallResultVO> recallResult = ragExternalService.recall(recallDTO);
        if (CollUtil.isEmpty(recallResult)) {
            log.info("【知识库对话】【RAG重要节点日志】【数字峰会】【多路召回】结果为空");
            return failHandle(innerDTO);
        }

        // 算法重排
        List<RerankResultVO> rerankResult = ragExternalService.digitalSummitRerank(innerDTO.getContent().getDialogue(), recallResult);
        if (CollUtil.isEmpty(rerankResult)) {
            log.info("【知识库对话】【RAG重要节点日志】【数字峰会】【rag算法重排】结果为空");
            return failHandle(innerDTO);
        }

        // 异步更新ES库召回次数
        knowledgeRecallUpdateService.recallUpdate(recallResult, rerankResult);

        // 开头文案
        String knowledgeBaseCode = getHitBase(recallResult, rerankResult);
        String titleText = knowledgeDialogueProperties.getTitleMap().getOrDefault(knowledgeBaseCode, "");

        // 个人知识库文件信息
        List<File> fileList = getDigitalSummitFileList(innerDTO.getReqParams().getUserId());
        if (!KnowledgeBaseEnum.isCommon(knowledgeBaseCode) && CollUtil.isEmpty(fileList)) {
            log.info("【知识库对话】【RAG重要节点日志】【数字峰会】命中个人知识库，但是文件不存在或者获取文件信息失败");
            return failHandle(innerDTO);
        }

        // 扣减权益
        memberCenterService.consumeBenefit(innerDTO.getReqParams(), RequestContextHolder.getPhoneNumber(), innerDTO.getDialogueId());

        //保存
        saveTextResult(innerDTO, "", "");
        add(innerDTO, ChatStatusEnum.CHAT_IN);

        // 监听器
        SseDialogueEventListener event = new SseDialogueEventListener(innerDTO, historyList);
        event.setTitle(titleText);
        event.setFileList(fileList);
        event.setSseName(sseName);

        // 智能调度
        KnowledgeTemplateInfoVO templateInfoVO = new KnowledgeTemplateInfoVO(innerDTO.getContent().getDialogue());
        templateInfoVO.knowledgeHandle(recallResult, rerankResult, fileList);

        digitalSummitSchedule(innerDTO, event, templateInfoVO);

        return false;
    }

    /**
     * 查询【数字峰会】个人知识库文件信息
     *
     * @param userId       用户id
     * @return 引用文件列表
     */
    private List<File> getDigitalSummitFileList(String userId) {
        List<File> list = new ArrayList<>();
        List<String> fileIds = summitProperties.getFileIds();
        if (ObjectUtil.isEmpty(fileIds)) {
            return list;
        }
        try {
            // 先查数据库，获取文件类型，根据不同的类型处理
            List<UserKnowledgeFileEntity> entityList = userKnowledgeFileRepository.selectByFileIds(userId, fileIds);
            for (UserKnowledgeFileEntity entity : entityList) {
                if (KnowledgeResourceTypeEnum.isPersonalFile(entity.getFromResourceType())) {
                    // 查询文件信息
                    try {
                        OwnerDriveFileVO fileInfo = userDriveExternalService.getFileInfo(userId, entity.getFileId());
                        list.add(new File(fileInfo));
                    } catch (Exception e) {
                        log.error("【知识库对话】【数字峰会】查询独立空间文件详细异常，文件id：{}，异常信息：{}", entity.getFileId(), e.getMessage(), e);
                    }
                } else {
                    File file = new File();
                    file.setFileId(entity.getFileId());
                    file.setName(entity.getFileName());
                    list.add(file);
                }
            }
        } catch (Exception e) {
            log.error("【知识库对话】【数字峰会】查询独立空间文件详细异常，文件id：{}，异常信息：{}", fileIds, e.getMessage(), e);
        }

        return list;
    }

    /**
     * 调大文本模型流式处理
     *
     * @param reqDTO 请求参数
     * @param code   模型编码
     * @param event  监听事件
     */
    private boolean digitalSummitModelStreamHandle(TextModelTextReqDTO reqDTO, String code, TextModelStreamEventListener event, KnowledgeTemplateInfoVO templateInfo) {
        // 系统提示词
        String systemPrompt = modelPromptProperties.getPrompt(RAG_SYSTEM_PROMPT, code);
        // 用户输入内容
        String userPrompt = summitProperties.getUserPrompt();
        LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】【数字峰会】使用的system提示词第{}个分块：\n{}", systemPrompt);
        LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】【数字峰会】使用的user提示词第{}个分块：\n{}", userPrompt);

        // 行政人物处理
        templateInfo.politicianHandle(knowledgeDialogueProperties, systemPrompt);

        // 系统提示词
        systemPrompt = systemPrompt.replace(REPLACE_KEY_OF_HISTORY, templateInfo.getHistory())
                .replace(REPLACE_KEY_OF_KNOWLEDGE, templateInfo.getKnowledge())
                .replace(REPLACE_KEY_OF_POLITICIAN, templateInfo.getPolitician());

        // 用户输入内容
        userPrompt = userPrompt.replace(REPLACE_KEY_OF_QUERY, templateInfo.getQuery())
                .replace(REPLACE_KEY_OF_HISTORY, templateInfo.getHistory())
                .replace(REPLACE_KEY_OF_KNOWLEDGE, templateInfo.getKnowledge())
                .replace(REPLACE_KEY_OF_POLITICIAN, templateInfo.getPolitician());

        // 新的对话信息
        TextModelMessageDTO msgDTO = new TextModelMessageDTO();
        msgDTO.setRole(TextModelRoleEnum.USER.getName());
        msgDTO.setContent(userPrompt);
        msgDTO.setCommand(systemPrompt);

        // 调大模型
        templateInfo.getHistoryList().add(msgDTO);
        TextModelTextReqDTO req = genTextModelTextReq(reqDTO, templateInfo.getHistoryList());
        log.info("【知识库对话】【RAG重要节点日志】【数字峰会】模型编码：{}，大模型配置参数：{}", code, JsonUtil.toJson(req.getTextModelConfig()));
        LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】【数字峰会】调大文本模型入参{}个分块：\n{}", JsonUtil.toJson(req));
        return textModelExternalService.streamDialogue(code, req, event);
    }

    /**
     * 智能调度
     *
     * @param innerDTO     用户输入对象
     * @param event        流式监听参数
     * @param templateInfo 对话模板
     */
    private void digitalSummitSchedule(ChatAddInnerDTO innerDTO, SseDialogueEventListener event, KnowledgeTemplateInfoVO templateInfo) {
        // 开启计时
        StopWatch stopWatch = StopWatchUtil.createStarted();
        try {
            ChatConfigEntity chatConfigEntity = chatConfigServiceDomainService.getUserCanUseModel(innerDTO.getReqParams().getUserId(), RequestContextHolder.getPhoneNumber(), innerDTO.getContent().getAssistantEnum(), innerDTO.getContent().getBusinessType());
            List<String> executeSort = new ArrayList<>();
            executeSort.add(chatConfigEntity.getModelType());
            executeSort.addAll(Arrays.asList(knowledgeDialogueProperties.getDialogueConfig().getModelCode().split(",")));

            AbstractResultCode error = null;
            boolean success = false;
            for (String code : executeSort) {
                error = null;

                // qps限制
                if (!qpslimitService.modelQpsLimit(code)) {
                    log.info("【知识库对话】【RAG重要节点日志】【数字峰会】请求过多，qps限流，model:{}", code);
                    error = ResultCodeEnum.ERROR_LIMITATION;
                } else {

                    try {
                        TextModelTextReqDTO reqDTO = event.getTextDto().toTextReqDTO(modelProperties.getMaxLength(innerDTO.getContent().getAssistantEnum(), innerDTO.getContent().getBusinessType(), code));
                        event.setModelCode(code);
                        // 知识库调用大模型不加角标
                        reqDTO.setEnableNetworkSearchCitation(false);

                        templateInfo.historyHandle(reqDTO);
                        success = digitalSummitModelStreamHandle(reqDTO, code, event, templateInfo);

                        // 更新模型编码
                        algorithmChatContentRepository.updateModelCode(event.getDialogId(), code);
                        innerDTO.getRespParams().setModelType(code);
                        break;
                    } catch (YunAiBusinessException e) {
                        error = e.getExceptionEnum();
                        log.error("【知识库对话】【RAG重要节点日志】【数字峰会】调用文本大模型异常:{}，对话id:{}", e.getMessage(), event.getDialogId(), e);
                    } catch (Exception e) {
                        log.error("【知识库对话】【RAG重要节点日志】【数字峰会】调用文本大模型异常:{}，对话id:{}", e.getMessage(), event.getDialogId(), e);
                    }
                }

            }

            if (!success) {
                log.warn("【知识库对话】【RAG重要节点日志】【数字峰会】对话失败，智能调度大文本模型处理失败，对话id：{}", event.getDialogId());
                if (error != null) {
                    event.dialogueFail(error);
                } else {
                    event.dialogueFail(ResultCodeEnum.ERROR_SERVER_INTERNAL);
                }
            }
        } finally {
            log.info("【知识库对话】【RAG重要节点日志】【数字峰会】结束，耗时：{}，对话id：{}\n 最终结果：{}",
                    StopWatchUtil.logTime(stopWatch), event.getDialogId(), JsonUtil.toJson(event.getAllMsg()));
            StopWatchUtil.clearDuration();
        }
    }
}
