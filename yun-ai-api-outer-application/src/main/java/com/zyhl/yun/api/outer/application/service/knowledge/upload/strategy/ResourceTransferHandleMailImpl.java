package com.zyhl.yun.api.outer.application.service.knowledge.upload.strategy;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.vo.CheckResultVO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileCheckReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.ResourceTransferReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.upload.AbstractResourceTransferHandle;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeFileImportVO;
import com.zyhl.yun.api.outer.domain.entity.PersonalKnowledgeImportTaskEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeUploadEntity;
import com.zyhl.yun.api.outer.domain.vo.ImportMailInfoVO;
import com.zyhl.yun.api.outer.domain.vo.ImportNoteInfoVO;
import com.zyhl.yun.api.outer.domain.vo.PersonalKnowledgeImportCheckResult;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileCategoryEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeUploadStatusEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述：邮件资源文件处理
 *
 * <AUTHOR> zhumaoxian  2025/3/21 17:19
 */
@Slf4j
@Component
public class ResourceTransferHandleMailImpl extends AbstractResourceTransferHandle {

    @Override
    public void afterPropertiesSet() throws Exception {
        register(KnowledgeResourceTypeEnum.MAIL.getCode(), this);
    }

    @Override
    public KnowledgeFileImportVO trans(ResourceTransferReqDTO dto) {
        // 参数校验
        checkMailReq(dto);

        // 获取邮件列表的id集合
        List<String> idList = dto.getMailList().stream()
                .map(ImportMailInfoVO::getMailId).collect(Collectors.toList());

        // 查询未成功的导入任务
        List<PersonalKnowledgeImportTaskEntity> taskEntities = userKnowledgeUploadRepository
                .findNotSuccessByFileIds(dto.getUserId(), String.valueOf(dto.getBaseId()), dto.getResourceType(), idList);

        // 查询已入库的邮件
        List<UserKnowledgeFileEntity> fileEntities
                = userKnowledgeFileRepository.selectByOldFileIds(dto.getBaseId(), dto.getUserId(), idList);
        List<ImportMailInfoVO> existMailInfos = findDuplicateMails(fileEntities, dto.getMailList());

        // 记录还未成功的邮箱资源 和 已经入库的
        Map<String, PersonalKnowledgeImportTaskEntity> existMap = taskEntities.stream()
                .collect(Collectors.toMap(PersonalKnowledgeImportTaskEntity::getFileId, entity -> entity));

        // 存放需要导入的任务
        List<UserKnowledgeUploadEntity> needImportList = new ArrayList<>();
        // 存放返回结果
        List<PersonalKnowledgeImportCheckResult> checkResult = new ArrayList<>();
        UserKnowledgeFileEntity fileEntity = null;
        if (ObjectUtil.isNotEmpty(dto.getParentFileId())) {
            fileEntity = userKnowledgeFileRepository.selectByFileId(dto.getUserId(), dto.getParentFileId());
        }
        for (ImportMailInfoVO mailInfoVO : dto.getMailList()) {
            PersonalKnowledgeImportTaskEntity entity = existMap.get(mailInfoVO.getMailId());

            // 处理上传过程中或者失败的
            if (ObjectUtil.isNotEmpty(entity)) {
                PersonalKnowledgeImportCheckResult importCheckResult = new PersonalKnowledgeImportCheckResult();
                importCheckResult.setResourceType(dto.getResourceType());
                importCheckResult.setCheckResult(1);
                importCheckResult.setFailMessage("文件已经上传");
                importCheckResult.setMailList(mailInfoVO);
                checkResult.add(importCheckResult);
                continue;
            }

            // 处理已经存在知识库中的
            if (existMailInfos.size()> 0 && existMailInfos.contains(mailInfoVO)){
                PersonalKnowledgeImportCheckResult importCheckResult = new PersonalKnowledgeImportCheckResult();
                importCheckResult.setResourceType(dto.getResourceType());
                importCheckResult.setCheckResult(1);
                importCheckResult.setFailMessage("文件已存在知识库");
                importCheckResult.setMailList(mailInfoVO);
                checkResult.add(importCheckResult);
                continue;
            }

            UserKnowledgeUploadEntity insertEntity = UserKnowledgeUploadEntity.builder()
                    .id(uidGenerator.getUID())
                    .baseId(dto.getBaseId())
                    .userId(dto.getUserId())
                    .fileId(mailInfoVO.getMailId())
                    .fileName(mailInfoVO.getTitle())
                    .fileType(FileTypeEnum.FILE.getKnowledgeFileType())
                    .resourceType(dto.getResourceType())
                    .resource(JSON.toJSONString(mailInfoVO))
                    .ownerId(dto.getUserId())
                    .ownerType(OwnerTypeEnum.MAIL.getOwnerValue())
                    .paasCode(String.valueOf(RequestContextHolder.getBelongsPlatform()))
                    .uploadStatus(KnowledgeUploadStatusEnum.NOT_PROCESSED.getStatus())
                    .extension(KnowledgeResourceTypeEnum.MAIL.getExt())
                    .category(FileCategoryEnum.DOC.getKnowledgeCategory())
                    .targetFileId(dto.getParentFileId())
                    .targetParentFilePath(dto.getParentFilePath())
                    .fileUpdatedAt(new Date())
                    .fileCreatedAt(new Date())
                    .build();
            if(Objects.nonNull(fileEntity)){
                insertEntity.setTargetParentFileId(fileEntity.getFileId());
                insertEntity.setTargetParentFilePath(fileEntity.getParentFilePath() + "/" + fileEntity.getFileId());
            }
            // 标题送审
            if(Boolean.FALSE.equals(fileCheck(mailInfoVO, dto.getUserId()))){
                PersonalKnowledgeImportCheckResult importCheckResult = new PersonalKnowledgeImportCheckResult();
                importCheckResult.setResourceType(dto.getResourceType());
                importCheckResult.setCheckResult(Integer.valueOf(ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultCode()));
                importCheckResult.setFailMessage(ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultMsg());
                importCheckResult.setMailList(mailInfoVO);
                checkResult.add(importCheckResult);
                // 送审不通过，修改upload表状态
                insertEntity.setUploadStatus(KnowledgeUploadStatusEnum.FAIL.getStatus());
                insertEntity.setResultCode(ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultCode());
                insertEntity.setResultMsg(ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultMsg());
            }
            needImportList.add(insertEntity);
        }

        KnowledgeFileImportVO result = new KnowledgeFileImportVO();
        result.setErrorList(checkResult);
        // 插入数据库
        userKnowledgeUploadRepository.batchAdd(needImportList);
        // 发送MQ
        sendCategoryTaskMq(needImportList);
        return result;
    }

    /**
     * 邮件标题送审
     * @param mailInfoVO 上传列表
     */
    private Boolean fileCheck(ImportMailInfoVO mailInfoVO, String userId) {
        boolean enabled = fileCheckConfig.isEnabled();
        log.info("batchImport mail fileCheckConfig:{}",fileCheckConfig);
        if(Boolean.FALSE.equals(enabled)){
            // 开关打开
            String fileName = mailInfoVO.getTitle();
            long uid = uidGenerator.getUID();
            CheckResultVO resultVo = checkSystemDomainService.checkPlatformAndLocal(uid, userId, fileName);
            return !CheckResultVO.isFail(resultVo);
        }
        return Boolean.TRUE;
    }

    @Override
    public void check(KnowledgeFileCheckReqDTO dto) {
        //暂不需要校验
    }

    private void checkMailReq(ResourceTransferReqDTO dto) {
        if (CollectionUtils.isEmpty(dto.getMailList())) {
            throw new YunAiBusinessException(ResultCodeEnum.MAIL_IS_NULL);
        }
        for (ImportMailInfoVO mail : dto.getMailList()) {
            if (mail.getMailId() == null) {
                throw new YunAiBusinessException(ResultCodeEnum.MAIL_ID_IS_NULL);
            }
        }
    }

    public List<ImportMailInfoVO> findDuplicateMails(List<UserKnowledgeFileEntity> fileEntities, List<ImportMailInfoVO> mailList) {
        // 提取fileEntities中的fileId到Set中以便快速查找
        Set<String> existingFileIds = fileEntities.stream()
                .map(UserKnowledgeFileEntity::getOldFileId)
                .collect(Collectors.toSet());

        // 过滤出mailList中mailId存在于existingFileIds的ImportMailInfoVO对象
        return mailList.stream()
                .filter(mail -> existingFileIds.contains(mail.getMailId()))
                .collect(Collectors.toList());
    }
}
