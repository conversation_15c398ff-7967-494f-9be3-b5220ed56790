package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeProfilePhoto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import java.io.Serializable;

/**
 * 创建个人知识库请求参数
 * @date 2025/04/14
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class PersonalKnowledgeAddReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 知识库名称
     */
    private String name;

    /**
     * 知识库描述
     */
    private String description;

    /**
     * 公开/私密（1 公开，0私密）
     */
    private Integer openLevel;

    /**
     * 头像信息
     */
    private PersonalKnowledgeProfilePhoto profilePhoto;

}