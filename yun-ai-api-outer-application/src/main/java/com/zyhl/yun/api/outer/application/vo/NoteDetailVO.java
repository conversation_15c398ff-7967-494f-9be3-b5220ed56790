package com.zyhl.yun.api.outer.application.vo;

import com.zyhl.yun.api.outer.application.dto.NoteDetailReqDTO;
import com.zyhl.yun.api.outer.external.client.resp.NoteDetailResponse;
import com.zyhl.yun.api.outer.external.client.resp.market.NoteContentsResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Classname NoteDetailVo
 * @Description 笔记文本内容VO
 * @Date 2024/3/1 9:42
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NoteDetailVO {

    /**
     * 笔记ID
     */
    private String noteId;

    /**
     * 笔记文本内容
     */
    private String txtContent;

    /**
     * 笔记标题
     */
    private String title;

    /**
     * 用户输入内容
     */
    private String inputContent;

    /**
     * 格式化NoteDetailVO
     * @param reqDTO
     * @param response
     * @param noteRes
     * @return
     */
    public static NoteDetailVO convert(NoteDetailReqDTO reqDTO, NoteDetailResponse response, NoteContentsResult noteRes) {
        NoteDetailVO vo = new NoteDetailVO();
        vo.setNoteId(noteRes.getNoteId());
        vo.setTitle(response.getTitle());
        vo.setTxtContent(noteRes.getTxtcontent());
        vo.setInputContent(reqDTO.getInputContent());
        return vo;
    }

}
