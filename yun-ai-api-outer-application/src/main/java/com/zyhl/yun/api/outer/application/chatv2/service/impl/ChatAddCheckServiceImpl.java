package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.YunDiskClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.FileGetContentReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.AIFileVO;
import com.zyhl.hcy.yun.ai.common.platform.third.dto.GetUserInfoByPhoneNumberRespDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.pojo.LocalFileInfo;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatAddCheckService;
import com.zyhl.yun.api.outer.application.service.chat.impl.AlgorithmChatAddCheckServiceImpl;
import com.zyhl.yun.api.outer.application.service.external.MailAttachService;
import com.zyhl.yun.api.outer.application.util.RegexUtils;
import com.zyhl.yun.api.outer.application.vo.MailAttachVO;
import com.zyhl.yun.api.outer.config.TextFileModeProperties;
import com.zyhl.yun.api.outer.config.textmodel.AppendPromptConfig;
import com.zyhl.yun.api.outer.domain.valueobject.MailInfo;
import com.zyhl.yun.api.outer.domainservice.IImageCommonService;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.repository.AlgorithmAiPromptRepository;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/6/4 13:13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatAddCheckServiceImpl implements ChatAddCheckService {

    @Resource
    private final MailAttachService mailAttachService;
    @Resource
    private YunDiskClient yunDiskClient;
    @Resource
    private IImageCommonService imageCommonService;
    @Resource
    private TextFileModeProperties textFileModeProperties;
    @Resource
    private AlgorithmAiPromptRepository algorithmAiPromptRepository;
    @Resource
    private AlgorithmChatAddCheckServiceImpl algorithmChatAddCheckServiceImpl;
    @Resource
    private AppendPromptConfig appendPromptConfig;

    @Value("${dialogue.prompt-length:100}")
    private Integer promptLength;

    @Resource(name = "longUploadFileThreadPool")
    private ExecutorService longUploadFileThreadPool;


    /**
     * 根据云盘文件id列表获取文件共享存储路径列表
     *
     * @param fileIdList     云盘文件id列表
     * @param userId         用户id
     * @param fileListFilter 文件列表是否过滤 true是 false否
     * @return 上传文件列表vo
     */
    @Override
    public List<LocalFileInfo> getFilesByCloudDiskDocumentLocalPath(List<String> fileIdList, String userId,
                                                                    boolean fileListFilter) {
        if (!fileListFilter) {
            // 校验资源id参数不能为空
            if (fileIdList == null || CollectionUtils.isEmpty(fileIdList)) {
                return null;
            }

            fileIdList = getFilterList(fileIdList);
            if (CollUtil.isEmpty(fileIdList)) {
                return null;
            }
        }

        // 大模型上传qps限制，不允许超过3个
        if (fileIdList.size() > textFileModeProperties.getFileNum()) {
            log.error("根据云盘文档列表获取上传文件LocalPath列表,文件数量过多 | dto:{}", JsonUtil.toJson(fileIdList));
            throw new YunAiBusinessException(AiResultCode.CODE_10000018.getCode(), AiResultCode.CODE_10000018.getMsg());
        }

        List<AIFileVO> fileVOList = getFileList(fileIdList, userId);

        // 校验文件下载地址
        log.info("根据云盘文档列表获取上传文件LocalPath列表 获取文件列表信息 fileVOList:{}", JsonUtil.toJson(fileIdList));
        if (CollUtil.isEmpty(fileVOList)) {
            return null;
        }
        List<LocalFileInfo> localPathFile = new ArrayList<>();
        for (AIFileVO fileVo : fileVOList) {
            String localPath = null;
            try {
                localPath = imageCommonService.urlToLocalPath(fileVo.getContent(), fileVo.getFileSuffix(), userId);
                if (StringUtils.isNotBlank(localPath)) {
                    localPathFile.add(LocalFileInfo.builder().localPath(localPath).ext(fileVo.getFileSuffix())
                            .name(fileVo.getFileName()).build());
                }
            } catch (Exception e) {
                log.error("文件下载失败 userId:{}, getContentId:{}, getContent:{} error:", userId, fileVo.getContentId(),
                        fileVo.getContent(), e);
            }
        }
        if (CollUtil.isEmpty(localPathFile)) {
            throw new YunAiBusinessException(AiResultCode.CODE_10000017.getCode(), AiResultCode.CODE_10000017.getMsg());
        }
        return localPathFile;
    }

    private List<AIFileVO> getFileList(List<String> fileIdList, String userId) {
        // 获取文件
        List<AIFileVO> fileVOList = new ArrayList<>();
        List<Future<AIFileVO>> fileFutures = new ArrayList<>();
        for (String fileId : fileIdList) {
            Future<AIFileVO> future = this.longUploadFileThreadPool.submit(() -> {
                try {
                    // 获取文件信息
                    AIFileVO fileVo = getFileContent(fileId, userId);

                    // 校验文件大小
                    if (fileVo.getFileSize() >= textFileModeProperties.getFileSize()) {
                        log.error("根据云盘文档列表获取上传文件列表 文件格式过大 fileVo:{}", JsonUtil.toJson(fileVo));
                        throw new YunAiBusinessException(AiResultCode.CODE_10000006.getCode(),
                                AiResultCode.CODE_10000006.getMsg());
                    }

                    // 文件后缀校验
                    String fileSuffix = StringUtils.isBlank(fileVo.getFileSuffix()) ? null : fileVo.getFileSuffix().toLowerCase();
                    if (null == fileSuffix || !textFileModeProperties.getFileSuffixList().contains(fileSuffix)) {
                        log.error("根据云盘文档列表获取上传文件列表 不支持的文件格式 fileVo:{}", JsonUtil.toJson(fileVo));
                        throw new YunAiBusinessException(AiResultCode.CODE_10000019.getCode(), AiResultCode.CODE_10000019.getMsg());
                    }

                    return fileVo;
                } catch (Exception e) {
                    log.error("longUploadFileThreadPool 根据云盘文档列表获取上传文件列表 fileId:{}, error:", fileId, e);
                    throw e;
                }
            });
            fileFutures.add(future);
        }

        // 获取文件线程结果
        for (Future<AIFileVO> fileFuture : fileFutures) {
            try {
                AIFileVO vo = fileFuture.get();
                if (null != vo) {
                    fileVOList.add(vo);
                }
            } catch (Exception e) {
                log.error("根据云盘文档列表获取上传文件列表 处理文件异常 error:", e);
                if (e instanceof YunAiBusinessException) {
                    throw (YunAiBusinessException) e;
                }
            }
        }
        return fileVOList;
    }

    /**
     * 获取文件信息
     *
     * @param fileId 文件id
     * @param userId 用户id
     * @return 文件信息对象AIFileVO
     */
    private AIFileVO getFileContent(String fileId, String userId) {
        try {
            FileGetContentReqDTO fileGetContentReqDTO = new FileGetContentReqDTO();
            fileGetContentReqDTO.setFileId(fileId);

            GetUserInfoByPhoneNumberRespDTO userInfo = new GetUserInfoByPhoneNumberRespDTO();
            userInfo.setUserDomainId(Long.valueOf(userId));
            fileGetContentReqDTO.setUserInfo(userInfo);

            //获取原图
            fileGetContentReqDTO.setIsOriginal(true);
            return yunDiskClient.getFileContent(fileGetContentReqDTO);

        } catch (Exception e) {
            log.error("根据云盘文档列表获取上传文件列表 获取云盘文件信息失败 fileId:{} | userId:{} | e:", fileId, userId, e);
            throw new YunAiBusinessException(ResultCodeEnum.FILE_ID_INFO_ERROR);
        }
    }

    /**
     * 根据邮件附件列表获取文件共享存储路径列表
     *
     * @param mailList     邮箱附件列表dto
     * @param reqDTO       会话输入dto
     * @param resourceName
     * @return 上传文件列表vo
     */
    @Override
    public List<String> getFilesByEmailAttachmentLocalPath(List<MailInfo> mailList, ChatAddReqDTO reqDTO, String resourceName) {
        MailInfo mailInfo = mailList.get(0);
        // 过滤去重和去除前后空格
        List<String> attachNameList = getFilterList(mailInfo.getAttachNameList());
        List<String> fileIdList = getFilterList(mailInfo.getFileIdList());
        List<String> fileUrlList = getFilterList(mailInfo.getFileUrlList());
        // 大模型上传qps限制，不允许超过3个
        if (attachNameList.size() + fileIdList.size() + fileUrlList.size() > textFileModeProperties.getFileNum()) {
            log.error("根据邮件文档列表获取上传文件LocalPath列表,文件数量过多 | dto:{}", JsonUtil.toJson(mailInfo));
            throw new YunAiBusinessException(AiResultCode.CODE_10000018.getCode(), AiResultCode.CODE_10000018.getMsg());
        }
        //根据attachName获取fileUrl来获取resourceName
        if (Boolean.FALSE.equals(CollectionUtils.isEmpty(mailList.get(0).getAttachNameList()))) {
            resourceName = mailList.get(0).getAttachNameList().get(0);
        }

        List<String> localPathList = new ArrayList<>();
        // 获取邮箱附件列表
        if (!CollUtil.isEmpty(attachNameList)) {
            String sid = mailInfo.getSid();
            String rmKey = mailInfo.getRmkey();
            Map<String, Object> map = reqDTO.getDialogueInput().getExtInfoMap();
            sid = StringUtils.isNotEmpty(sid) ? sid : (String) map.getOrDefault("sid", "");
            rmKey = StringUtils.isNotEmpty(rmKey) ? rmKey : (RegexUtils.extractAfterEqualSymbolValue((String) map.getOrDefault("rmkey", "")));
            List<MailAttachVO> attachList = mailAttachService.getMailFiles(reqDTO.getUserId(), mailInfo.getMailId(),
                    sid, rmKey, attachNameList);
            log.info("根据邮件附件列表获取上传文件LocalPath列表 获取邮箱附件列表 attachmentDto:{} | attachList:{}",
                    JsonUtil.toJson(mailInfo), JsonUtil.toJson(attachList));
            if (CollUtil.isNotEmpty(attachList)) {
                // 遍历处理
                for (MailAttachVO mailAttachVO : attachList) {
                    if (StringUtils.isNotBlank(mailAttachVO.getLocalFilePath())) {
                        localPathList.add(mailAttachVO.getLocalFilePath());
                    }
                }
            }
        }
        // 获取云盘url列表
        if (Boolean.FALSE.equals(CollUtil.isEmpty(fileUrlList))) {
            for (String url : fileUrlList) {
                try {
                    String path = imageCommonService.createDirPathBaseOnDate(url, "mailFileUrl", reqDTO.getUserId());
                    HttpResponse response = HttpUtil.createGet(url).execute();
                    // 检查响应是否成功
                    if (response.isOk()) {
                        // 从响应头中获取文件名
                        String disposition = response.header("Content-Disposition");
                        String fileName = extractFileName(disposition);

                        if (StringUtils.isEmpty(fileName)) {
                            int index = url.lastIndexOf(StrUtil.DOT);
                            if (index != -1) {
                                fileName = RandomUtil.randomInt(10000) + url.substring(index);
                            } else {
                                // 如果没有找到 .，可以返回一个默认的.pdf后缀
                                fileName = RandomUtil.randomInt(10000) + ".pdf";
                            }
                        }
                        // 创建目标文件
                        File file = new File(path + File.separator + fileName);
                        // 下载文件并保存到本地
                        InputStream inputStream = response.bodyStream();
                        byte[] bytes = IOUtils.toByteArray(inputStream);
                        FileUtils.writeByteArrayToFile(file, bytes);
                        localPathList.add(file.getAbsolutePath());
                        if (StringUtils.isEmpty(resourceName)) {
                            resourceName = fileName;
                        }
                    } else {
                        log.error("下载失败，状态码：" + response.getStatus());
                    }
                } catch (IOException e) {
                    log.error("下载文件失败 | e:", e);
                }
            }
        }

        if (CollUtil.isEmpty(localPathList)) {
            throw new YunAiBusinessException(AiResultCode.CODE_10000017.getCode(), AiResultCode.CODE_10000017.getMsg());
        }
        return localPathList;
    }

    @Override
    public String getDialoguePrompt(String prompt, String channel) {
        return algorithmChatAddCheckServiceImpl.getDialoguePrompt(prompt, channel);
    }

    /**
     * 过滤去重和去除前后空格处理
     *
     * @param list 字符串列表
     * @return 过滤去重和去除前后空格后的字符串列表
     */
    private List<String> getFilterList(List<String> list) {
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        return list.stream()
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public String getAppendEnableRegenerate(DialogueInputInfoDTO inputInfo, String finalPrompt) {
        if (null != inputInfo && inputInfo.isEnableRegenerate()) {
            if (StringUtils.isNotEmpty(finalPrompt)) {
                return finalPrompt + "（" + appendPromptConfig.getRegeneratePrompt() + "）";
            } else {
                return appendPromptConfig.getRegeneratePrompt();
            }
        }
        return finalPrompt;
    }

    /**
     * 从Content-Disposition中提取文件名
     *
     * @param disposition the Content-Disposition
     * @return {@link String}
     * <AUTHOR>
     * @date 2025-07-15 10:53
     */
    private static String extractFileName(String disposition) {
        if (StrUtil.isEmpty(disposition)) {
            return StrUtil.EMPTY;
        }

        String fileName = StrUtil.EMPTY;
        int startIndex = disposition.indexOf("filename*=UTF-8''") + "filename*=UTF-8''".length();
        if (startIndex < 0) {
            return StrUtil.EMPTY;
        }

        int endIndex = disposition.length();
        for (int i = startIndex; i < disposition.length(); i++) {
            if (disposition.charAt(i) == ';') {
                endIndex = i;
                break;
            }
        }
        String encodedFileName = disposition.substring(startIndex, endIndex);
        try {
            fileName = URLDecoder.decode(encodedFileName, StandardCharsets.UTF_8.name());
        } catch (Exception e) {
            log.error("文件名解码失败 | encodedFileName:{}", encodedFileName);
        }
        return fileName;
    }
}
