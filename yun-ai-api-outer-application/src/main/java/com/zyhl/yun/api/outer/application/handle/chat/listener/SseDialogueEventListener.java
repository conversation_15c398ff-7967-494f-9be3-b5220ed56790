package com.zyhl.yun.api.outer.application.handle.chat.listener;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.logger.util.LogUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.NumberUtil;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.event.TextModelStreamEventListener;
import com.zyhl.hcy.yun.ai.common.model.api.client.vo.TextModelBaseVo;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.ApiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.model.api.utils.TextModelUtil;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.dto.CheckTextReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.vo.CheckResultVO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.dto.TextModelDTO;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatHistoryService;
import com.zyhl.yun.api.outer.application.service.chat.DialogueRecommendService;
import com.zyhl.yun.api.outer.application.service.external.MemberCenterService;
import com.zyhl.yun.api.outer.application.util.SseEmitterDataUtils;
import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;
import com.zyhl.yun.api.outer.application.vo.FlowTypeResultV2VO;
import com.zyhl.yun.api.outer.application.vo.FlowTypeResultVO;
import com.zyhl.yun.api.outer.config.CheckSystemConfig;
import com.zyhl.yun.api.outer.config.FlowTypeProperties;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domainservice.CheckSystemDomainService;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.CheckTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ApiVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddResultTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.OutAuditStatusEnum;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.sse.EventSource;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.Future;

/**
 * 流式对话事件监听，多实例
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
public class SseDialogueEventListener extends TextModelStreamEventListener {

    private FlowTypeProperties flowTypeProperties;
    private RedisOperateRepository redisOperateRepository;
    private AlgorithmChatHistoryService algorithmChatHistoryService;
    private DialogueRecommendService dialogueRecommendService;
    private AiTextResultRepository aiTextResultRepository;
    private MemberCenterService memberCenterService;
    private CheckSystemConfig checkSystemConfig;
    private CheckSystemDomainService checkSystemDomainService;

    public SseDialogueEventListener(ChatAddInnerDTO innerDTO, List<TextModelMessageDTO> historyList) {
        flowTypeProperties = SpringUtil.getBean(FlowTypeProperties.class);
        algorithmChatHistoryService = SpringUtil.getBean(AlgorithmChatHistoryService.class);
        dialogueRecommendService = SpringUtil.getBean(DialogueRecommendService.class);
        aiTextResultRepository = SpringUtil.getBean(AiTextResultRepository.class);
        redisOperateRepository = SpringUtil.getBean(RedisOperateRepository.class);
        memberCenterService = SpringUtil.getBean(MemberCenterService.class);
        checkSystemConfig = SpringUtil.getBean(CheckSystemConfig.class);
        checkSystemDomainService = SpringUtil.getBean(CheckSystemDomainService.class);

        init(innerDTO, historyList);
    }

    /**
     * 全量内容（送审使用全量，保存数据库使用全量）
     */
    private final StringBuilder allThinkMsg = new StringBuilder();
    private final StringBuilder allMsg = new StringBuilder();
    /**
     * 增量内容（输出使用增量）
     */
    private final StringBuilder addThinkMsg = new StringBuilder();
    private final StringBuilder addMsg = new StringBuilder();
    /**
     * 大模型token标识
     */
    private Integer outputTokens = 0;
    /**
     * 结束对话标识
     */
    private boolean finishFlag = false;
    private boolean isFirst = false;
    private boolean hasSend = false;

    private long requestTime = RequestContextHolder.getRequestTime();
    private String sseName = "AI-chat-sse";
    private String userId;
    private String phone = RequestContextHolder.getPhoneNumber();
    private Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();
    private Long dialogId;
    private AlgorithmChatAddDTO reqParams;
    private SseEmitter sseEmitter = null;
    private AlgorithmChatAddVO resVO = null;
    private List<Future<Object>> futures;

    private TextModelDTO textDto;

    private String modelCode;
    private CheckSystemConfig.CheckSystemTextModel checkSystemTextModel = null;

    /**
     * 思考内容和正式回答使用一个字段输出
     */
    private boolean oneFiledOutput = ApiVersionEnum.isMergeThinkContentVersion(RequestContextHolder.getApiVersion());

    private Map<String, String> logMap = MDC.getCopyOfContextMap();

    /**
     * title信息
     */
    private String title;
    /**
     * 知识库文件
     */
    private List<File> fileList;

    /**
     * 输出网络搜索信息
     */
    private boolean outputNetworkSearchInfo = false;
    /**
     * 网络搜索信息列表
     */
    private List<AiTextResultRespParameters.NetworkSearchInfo> networkSearchInfoList;

    /**
     * a标签
     */
    private static final String A_TAG_START = "<a";
    private static final String A_TAG_END = "</a>";
    private boolean aTagStart = false;
    private boolean aTagEnd = false;

    /**
     * 初始化
     *
     * @param innerDTO    对话请求参数
     * @param historyList 历史消息列表
     */
    public void init(ChatAddInnerDTO innerDTO, List<TextModelMessageDTO> historyList) {
        reqParams = innerDTO.getReqParams();
        sseEmitter = innerDTO.getSseEmitter();
        resVO = innerDTO.getRespParams();
        futures = innerDTO.getFutures();

        userId = innerDTO.getReqParams().getUserId();
        dialogId = innerDTO.getDialogueId();

        textDto = new TextModelDTO(innerDTO.getReqParams(), resVO, "", historyList, "");

        if (resVO != null) {
            resVO.setResultType(ChatAddResultTypeEnum.FLOW_TYPE.getType());
        }
    }


    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
        this.checkSystemTextModel = checkSystemConfig.getCheckSystemTextModel(modelCode);
        this.resVO.setModelType(modelCode);
    }


    @Override
    public boolean onEvent(TextModelBaseVo response) {
    	//设置用户信息
    	RequestContextHolder.setUserInfo(NumberUtil.getLongValue(userId), phone, belongsPlatform);
        MDC.setContextMap(logMap);
        try {
            // 停止对话
            if (finishFlag) {
                log.info("【流式对话】对话已结束，对话id：{}", dialogId);
                return false;
            }
            if (redisOperateRepository.getStopDialogue(dialogId)) {
                // 对话结束
                log.info("【流式对话】对话已停止，对话id：{}", dialogId);
                return dialogueStop();
            }

            // 最后一次事件（不同模型判断不一样）
            boolean lastEvent = Boolean.TRUE.equals(response.getIsLast()) || outputTokens.equals(response.getOutputTokens());
            outputTokens = response.getOutputTokens() == null ? 0 : response.getOutputTokens();

            addThinkMsg.append(CharSequenceUtil.nullToDefault(response.getReasoningContent(), StringUtils.EMPTY));
            addMsg.append(CharSequenceUtil.nullToDefault(response.getText(), StringUtils.EMPTY));
            if (!lastEvent && unReachedCheckSize()) {
                //未达到送审字数
                return true;
            }

            // 控制返回完整a标签
            if (aTagHandle(lastEvent)) {
                return true;
            }

            // 送审以及输出给端侧
            if (ObjectUtil.isNotEmpty(addThinkMsg) || ObjectUtil.isNotEmpty(addMsg)) {
                isFirst = allThinkMsg.length() == 0 && allMsg.length() == 0;
                // 执行文本内容送审
                allThinkMsg.append(addThinkMsg);
                allMsg.append(addMsg);
                if (checkSystemTextModel.isCheckPart()) {
                    if (CheckResultVO.isFail(checkText(false))) {
                        return dialogueFail(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
                    }
                }

                // 判断接口版本
                if (oneFiledOutput) {
                    // 思考内容和正式回答合并输出
                    resVO.setFlowResult(new FlowTypeResultVO(thinkText() + addMsg, OutAuditStatusEnum.SUCCESS.getCode()));
                } else {
                    // 分开输出
                    resVO.setFlowResult(new FlowTypeResultV2VO(this.getIndexAndIncrement(), addThinkMsg.toString(), addMsg.toString(), OutAuditStatusEnum.SUCCESS.getCode()));
                }

                // 发送SseEmitter消息
                resVO.setTitle("");
                resVO.setPersonalKnowledgeFileList(null);
                resVO.setNetworkSearchInfoList(null);
                if (isFirst) {
                    resVO.setTitle(title);
                    resVO.setPersonalKnowledgeFileList(fileList);
                    resVO.setNetworkSearchInfoList(networkSearchInfoHandle(response.getNetworkSearchInfoList()));
                }
                send(BaseResult.success(resVO));

                // 发送消息后参数处理
                addThinkMsg.setLength(0);
                addMsg.setLength(0);
                hasSend = true;
            }

            // 最后一次 更新会话任务状态
            if (lastEvent) {
                if (CheckSystemConfig.isAuditAll(checkSystemTextModel.getType())) {
                    // 执行文本内容送审（全量送审）
                    log.info("【流式对话】正常结束，全量送审一次，对话id：{}", dialogId);
                    if (CheckResultVO.isFail(checkText(true))) {
                        log.error("【流式对话】正常结束，全量送审失败，对话id：{}", dialogId);
                        return dialogueFail(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
                    }
                }

                // 对话结束
                return dialogueSuccess();
            }
        } catch (YunAiBusinessException e) {
            // 回滚权益
            memberCenterService.consumeBenefitFail(userId, phone, dialogId);

            log.error("【流式对话】对话异常，流式响应信息：{}", JsonUtil.toJson(response), e);
            return dialogueFail(AiResultCode.getByCodeOrMsg(e.getCode(), e.getMessage()));
        } catch (ClientAbortException e) {
            // 回滚权益
            memberCenterService.consumeBenefitFail(userId, phone, dialogId);

            if (CheckSystemConfig.isAuditAll(checkSystemTextModel.getType())) {
                // 执行文本内容送审（全量送审）
                log.info("【流式对话】对话中断，全量送审一次，对话id：{}", dialogId);
                if (CheckResultVO.isFail(checkText(true))) {
                    log.error("【流式对话】对话中断，全量送审失败，对话id：{}", dialogId);
                    return dialogueFail(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
                }
            }

            // 对话终止情况 （一般情况下是前端杀进程会出现）
            log.info("【流式对话】对话中断，对话id：{}，输出信息：{}", dialogId, allMsg);
            return dialogueSuccess(ChatStatusEnum.CHAT_END);
        } catch (Exception e) {
            // 回滚权益
            memberCenterService.consumeBenefitFail(userId, phone, dialogId);

            log.error("【流式对话】对话异常，流式响应信息：{}", JsonUtil.toJson(response), e);
            return dialogueFail(AiResultCode.CODE_10000101);
        }

        return true;
    }

    @Override
    public void onClosed(EventSource eventSource) {
        MDC.setContextMap(logMap);
        log.info("【流式对话】【RAG重要节点日志】对话关闭，输出信息，allThinkMsg：{}", allThinkMsg);
        log.info("【流式对话】【RAG重要节点日志】对话关闭，输出信息，allMsg：{}\ntitle：{}\nfileList：{}", allMsg, title, JsonUtil.toJson(fileList));

        complete();
    }

    @Override
    public void onFailure(Throwable e, int code, String body) {
        MDC.setContextMap(logMap);
        log.error("【流式对话】对话失败，code：{}，body：{}，allThinkMsg:{}，allMsg：{}，title：{}，fileList：{}", code, body, allThinkMsg, allMsg, title, JsonUtil.toJson(fileList), e);

        if (finishFlag) {
            return;
        }
        if (ApiCommonResultCode.SENSITIVE_ERROR.getResultCode().equals(String.valueOf(code))) {
            //强制转换，统一错误码
            e = new YunAiBusinessException(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
        }
        if (e instanceof YunAiBusinessException) {
            YunAiBusinessException ex = (YunAiBusinessException) e;
            dialogueFail(AiResultCode.getByCodeOrMsg(ex.getCode(), ex.getMessage()));
        } else {
            dialogueFail(ResultCodeEnum.ERROR_SERVER_INTERNAL);
        }
    }


    /**
     * 发送推荐信息
     */
    public void sendRecommendInfo() {
        if (!ObjectUtil.isEmpty(futures)) {
            // 返回推荐信息
            dialogueRecommendService.setFuturesResult(dialogId, resVO.getRecommend(), futures);
            try {
                if (oneFiledOutput) {
                    resVO.setFlowResult(new FlowTypeResultVO("", OutAuditStatusEnum.SUCCESS.getCode()));
                } else {
                    resVO.setFlowResult(new FlowTypeResultV2VO(this.getIndex(), "", "", OutAuditStatusEnum.SUCCESS.getCode()));
                }
                send(BaseResult.success(resVO));
            } catch (Exception e) {
                log.warn("【流式对话】发送推荐信息异常：{}", e.getMessage());
            }
        }
    }

    /**
     * 处理对话成功
     *
     * @return boolean
     */
    public boolean dialogueSuccess() {
        sendRecommendInfo();

        return dialogueSuccess(ChatStatusEnum.CHAT_SUCCESS);
    }

    /**
     * 处理成功的情况
     *
     * @param chatStatus 对话状态
     * @return boolean
     */
    public boolean dialogueSuccess(ChatStatusEnum chatStatus) {
        // sse结束
        complete();

        // 更新会话状态
        algorithmChatHistoryService.updateOutResult(dialogId, OutAuditStatusEnum.SUCCESS, chatStatus, null);

        // 更新hbase会话内容
        AiTextResultRespParameters result = getRespResult(ResultCodeEnum.SUCCESS.getResultCode(), ResultCodeEnum.SUCCESS.getResultMsg());
        aiTextResultRepository.update(userId, dialogId, textDto.addAnswer(allMsg.toString()), result);

        return true;
    }

    /**
     * 处理停止对话的情况，前端调停止接口修改状态，这里只需要更新输出审核状态
     *
     * @return boolean
     */
    public boolean dialogueStop() {
        // sse结束
        complete();

        // 更新会话状态（对话成功）
        algorithmChatHistoryService.updateOutResultStop(dialogId);

        // 更新hbase会话内容
        AiTextResultRespParameters result = getRespResult(ResultCodeEnum.SUCCESS.getResultCode(), ResultCodeEnum.SUCCESS.getResultMsg());
        aiTextResultRepository.update(userId, dialogId, textDto.addAnswer(allMsg.toString()), result);

        return true;
    }

    /**
     * 处理失败的情况
     *
     * @param aiResultCode 结果
     * @return boolean
     */
    public boolean dialogueFail(AiResultCode aiResultCode) {
        return dialogueFail(aiResultCode.getCode(), aiResultCode.getMsg());
    }

    public boolean dialogueFail(AbstractResultCode aiResultCode) {
        return dialogueFail(aiResultCode.getResultCode(), aiResultCode.getResultMsg());
    }

    public boolean dialogueFail(String resultCode, String resultMsg) {
        // 发送SseEmitter消息并关闭连接
        try {
            send(BaseResult.error(resultCode, resultMsg));
        } catch (Exception e) {
            log.error("流式对话发送失败结果异常，异常信息：{}", e.getMessage(), e);
        }
        complete();

        // 更新会话状态（对话失败）
        AiTextResultRespParameters result = getRespResult(resultCode, resultMsg);
        algorithmChatHistoryService.updateOutResult(dialogId, OutAuditStatusEnum.FAIL, ChatStatusEnum.CHAT_FAIL, JsonUtil.toJson(result));

        // 更新hbase会话内容
        aiTextResultRepository.update(userId, dialogId, null, result);

        return true;
    }

    /**
     * 获取响应结果
     *
     * @param resultCode 错误码
     * @param resultMsg  错误信息
     * @return 响应结果
     */
    private AiTextResultRespParameters getRespResult(String resultCode, String resultMsg) {
        AiTextResultRespParameters result = new AiTextResultRespParameters(resultCode, resultMsg);
        if (ResultCodeEnum.SUCCESS.getResultCode().equals(resultCode)) {
            result.setData(String.valueOf(allMsg));
            result.setTitle(title);
            result.setPersonalKnowledgeFileList(fileList);
            result.setNetworkSearchInfoList(networkSearchInfoList);
            result.setReasoningContent(String.valueOf(allThinkMsg));
        }
        return result;
    }

    private void send(BaseResult<?> result) throws IOException {
        // 调用send发送消息
        if (finishFlag) {
            return;
        } else if (isFirst) {
            log.info("【流式对话】对话id：{}，从端侧请求到流式输出首token耗时：{}ms", dialogId, (System.currentTimeMillis() - requestTime));
        }
        sseEmitter.send(SseEmitter.event()
                .id(String.valueOf(System.currentTimeMillis()))
                .reconnectTime(flowTypeProperties.getReconnectTimeMillis())
                .name(sseName)
                .data(JsonUtil.toJson(result))
        );

        if (Objects.nonNull(result) && !result.isSuccess()) {
            log.warn("【流式对话】对话失败，输出结果信息：{}", JsonUtil.toJson(result));
        }
    }

    private void complete() {
        if (finishFlag) {
            return;
        }
        finishFlag = true;
        // 关闭连接
        try {
            log.info("【流式对话】关闭流对象，对话id：{}，从端侧请求到流式输出完成耗时：{}ms", dialogId, (System.currentTimeMillis() - requestTime));
            sseEmitter.complete();
        } catch (Exception e) {
            if (!(e instanceof IllegalStateException)) {
                log.error("【流式对话】流式对象关闭异常，异常信息：{}", e.getMessage(), e);
            }
        }
    }

    /**
     * 处理a标签
     *
     * @param lastEvent 是否是最后一个事件
     * @return boolean true-继续读取，false-结束读取
     */
    private boolean aTagHandle(boolean lastEvent) {
        if (lastEvent) {
            return false;
        }
        if (!aTagStart && addMsg.toString().toLowerCase().contains(A_TAG_START)) {
            // 存在a标签开始
            aTagStart = true;
        }
        if (aTagStart && addMsg.toString().toLowerCase().contains(A_TAG_END)) {
            // 存在a标签结束
            aTagEnd = true;
        }
        if (aTagStart && !aTagEnd) {
            // 存在a标签未结束，继续读取
            return true;
        }
        aTagStart = false;
        aTagEnd = false;

        return false;
    }

    /**
     * 文本审核
     *
     * @param checkAll 是否审核全部，true-审核全部，false-审核局部
     * @return CheckResultVO
     */
    private CheckResultVO checkText(Boolean checkAll) {
        CheckTypeEnum checkType;
        if (Boolean.TRUE.equals(checkAll)) {
            checkType = CheckTypeEnum.API;
        } else {
            checkType = CheckSystemConfig.isAuditLocalPart(checkSystemTextModel.getType()) ? CheckTypeEnum.LOCAL : CheckTypeEnum.API;
        }
        CheckTextReqDTO reqDTO = new CheckTextReqDTO(String.valueOf(dialogId), userId, String.valueOf(allThinkMsg) + allMsg);
        return checkSystemDomainService.checkSystemCheckText(checkSystemTextModel, checkType, reqDTO);
    }

    /**
     * 合并同一个字段输出时思考内容需要加<think></think>
     *
     * @return String
     */
    private String thinkText() {
        String thinkText = addThinkMsg.toString();
        if (ObjectUtil.isNotEmpty(thinkText)) {
            if (!Boolean.TRUE.equals(this.getStartThinkContent())) {
                this.setStartThinkContent(true);
                // 开始思维链
                thinkText = TextModelUtil.TAG_START_THINK + thinkText;
            }
        }
        if (ObjectUtil.isNotEmpty(addMsg) && Boolean.TRUE.equals(this.getStartThinkContent()) && !Boolean.TRUE.equals(this.getStartContent())) {
            this.setStartContent(true);
            // 结束思维链
            thinkText += TextModelUtil.TAG_END_THINK;
        }
        return thinkText;
    }

    private List<AiTextResultRespParameters.NetworkSearchInfo> networkSearchInfoHandle(List<TextModelBaseVo.NetworkSearchInfoVo> searchInfoList) {
        if (!outputNetworkSearchInfo) {
            return null;
        }
        if (ObjectUtil.isNotEmpty(networkSearchInfoList)) {
            return networkSearchInfoList;
        }
        if (ObjectUtil.isEmpty(searchInfoList)) {
            resVO.setNetworkSearchInfoList(Collections.emptyList());
            return null;
        }
        List<AiTextResultRespParameters.NetworkSearchInfo> networkSearchInfos = new ArrayList<>();
        for (TextModelBaseVo.NetworkSearchInfoVo searchInfo : searchInfoList) {
            networkSearchInfos.add(AiTextResultRespParameters.NetworkSearchInfo.builder()
                    .index(searchInfo.getIndex())
                    .icon(searchInfo.getIcon())
                    .siteName(searchInfo.getSiteName())
                    .title(searchInfo.getTitle())
                    .url(searchInfo.getUrl())
                    .build());
        }

        return networkSearchInfos;
    }

    /**
     * 检查输出字数是否达到送审字数
     *
     * @return true-未达到送审字数，false-达到送审字数
     */
    private boolean unReachedCheckSize() {
        String str = addThinkMsg.toString() + addMsg;
        // 过滤掉换行符再计算字数
        int length = str.replaceAll("\\n", "")
                .replaceAll(" ", "")
                .length();
        return length < checkSystemTextModel.getOutputSize();
    }
}
