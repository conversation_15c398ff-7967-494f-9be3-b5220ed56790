package com.zyhl.yun.api.outer.application.chatv2.hanlde.event.completeimpl;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.event.AbstractCompleteCallbackEvent;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.impl.AsyncImageToolHandlerImpl;
import com.zyhl.yun.api.outer.application.chatv2.pojo.CompleteEvent;
import com.zyhl.yun.api.outer.enums.AiResultCode;

import lombok.extern.slf4j.Slf4j;

/**
 * 下一个执行异步图片工具处理器事件
 * 
 * <AUTHOR>
 * @date 2025-04-25
 */
@Slf4j
@Component
public class NextAsynImageToolCallbackEvent extends AbstractCompleteCallbackEvent {

	@Resource
	private AsyncImageToolHandlerImpl asyncImageToolHandlerImpl;

	@Override
	public void complete(CompleteEvent data) {
		try {
			// 继续执行handler
			log.info("继续执行handler 润色后的，图片工具 dialogueId:{}", data.getHandleDTO().getDialogueId());
			asyncImageToolHandlerImpl.runHandlerContinue(data.getHandleDTO(), data.getEventListener());
		} catch (YunAiBusinessException e) {
			data.getEventListener().dialogueFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			data.getEventListener().dialogueFail(AiResultCode.CODE_9999);
		}
	}

}
