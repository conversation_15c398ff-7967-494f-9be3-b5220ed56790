package com.zyhl.yun.api.outer.application.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotEmpty;

/**
 * 二次对话流式接口入参DTO
 *
 * <AUTHOR>
 * @date 2024/12/16 11:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class SecondStreamChatAddDTO extends BaseDTO {

    /**
     * 对话id
     */
    @NotEmpty(message = "对话ID不能为空")
    private String dialogueId;
}
