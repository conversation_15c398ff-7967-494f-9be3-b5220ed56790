package com.zyhl.yun.api.outer.application.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * AI助手任务权益扣费请求入参req
 *
 * <AUTHOR>
 * @Date 2024/06/13 15:00
 */
@Data
public class AIAssistantPayReqDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 用户id，如果有token就不需要传
	 */
	private String userId;

	/**
	 * 对话id
	 */
	@NotNull(message = "对话id不可为空")
	private String dialogueId;
}
