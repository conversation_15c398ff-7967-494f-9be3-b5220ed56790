package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.enums.knowledge.FileTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import java.util.List;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 查询个人知识库资源列表入参
 * @date 2025/4/16 20:07
 */
@Data
@Slf4j
@EqualsAndHashCode(callSuper = true)
public class PersonalKnowledgeResourceListReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 知识库id
     */
    private String baseId;

    /**
     * 父资源 ID，目前仅支持传目录类型的云盘文件资源 ID
     */
    private String parentResoureId;

    /**
     * 资源类型
     * 0--个人云文件
     * 1--邮件
     * 2--笔记
     * 3--在线链接
     */
    private Integer resourceType;

    /**
     * 解析状态
     * -1--处理失败
     * 0--处理中
     * 1--处理成功
     */
    private Integer aiStatus;

    /**
     * 1--按导入时间
     * 2--按资源名称
     * 3--按资源类型
     * 4--按资源大小
     */
    private Integer sortType;

    /**
     * 1--升序
     * 2--降序
     */
    private Integer sortDirection;

    /**
     * 分页信息
     */
    private PageInfoDTO pageInfo;

    /**
     * 类型，枚举值file/folder，当且仅当resourceType=0时有效
     * @see FileTypeEnum
     */
    private String type;

    /**
     * 文件类型列表
     * 0--综合
     * 1 图片
     * 2 音频
     * 3 视频
     * 4 文档
     * 5 应用
     * 6 压缩文件
     * 0 其他
     * 100 普通目录
     */
    private List<Integer> categoryList;
}
