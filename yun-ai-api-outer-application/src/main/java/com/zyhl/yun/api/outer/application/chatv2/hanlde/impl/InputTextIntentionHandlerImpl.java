package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.collection.CollUtil;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.service.chat.DialogueIntentionService;
import com.zyhl.yun.api.outer.application.util.IntentionConvertUtils;
import com.zyhl.yun.api.outer.config.MailaiSearchProperties;
import com.zyhl.yun.api.outer.config.SearchParamProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.IntentEntityVO;
import org.springframework.stereotype.Component;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueIntentionDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.constants.FutureConstants;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 输入内容意图识别
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class InputTextIntentionHandlerImpl extends AbstractChatAddV2Handler {

    @Resource
    private DialogueIntentionService dialogueIntentionService;

    @Resource
    private IntentionConvertUtils intentionConvertUtils;

    @Resource
    private MailaiSearchProperties mailaiSearchProperties;

    /**
     * 当前执行顺序
     */
    private ExecuteSort thisExecuteSort = ExecuteSort.INPUT_INTENTION;

    @Override
    public void afterPropertiesSet() throws Exception {
    	//支持的业务初始化
    	List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        thisBusinessTypes.add(ChatBusinessTypeEnum.CLOUD_PHONE);
        thisBusinessTypes.add(ChatBusinessTypeEnum.MESSAGE_5G);
        thisBusinessTypes.add(ChatBusinessTypeEnum.NOTE);
    	this.setBusinessTypes(thisBusinessTypes);
    }
    
    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public boolean execute(ChatAddHandleDTO handleDTO) {

        DialogueIntentionDTO command = handleDTO.getInputInfoDTO().getCommand();

        // 意图指令为空 并且 对话内容不为空
        return CharSequenceUtil.isEmpty(command == null ? null : command.getCommand())
                && CharSequenceUtil.isNotEmpty(handleDTO.getInputInfoDTO().getDialogue())
                && Boolean.FALSE.equals(handleDTO.getInputInfoDTO().isEnableAllNetworkSearch());
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        DialogueInputInfoDTO inputInfoDTO = handleDTO.getInputInfoDTO();
        boolean enableAiEdite = inputInfoDTO.isEnableAiEdite();
        if (enableAiEdite) {
            // 邮箱AI搜索||邮箱AI编辑的不进入这个handler处理，直接返回，去执行下一个handler
            return true;
        }
        log.info("进入{}", thisExecuteSort.getDesc());
        // 获取意图识别预处理future的结果
        handleDTO.setIntentionVO(getDialogueIntention(handleDTO));
        
		// 有图片并且是搜索意图需要强制转换为文本意图
		if (handleDTO.isReqResourceImageSse()
				&& DialogueIntentionEnum.isSearchTypeIntentionForAll(handleDTO.getIntentionCode())) {
			log.info("有图片并且是搜索意图需要强制转换为文本意图 originalIntentionCode:{}", handleDTO.getIntentionCode());
			handleDTO.setTextGenerateTextIntention();
		}

        return true;
    }

    /**
     * 获取意图识别预处理future的结果
     *
     * @param handleDTO 用户输入对象
     */
    private DialogueIntentionVO getDialogueIntention(ChatAddHandleDTO handleDTO) {
        DialogueIntentionVO intention = null;
        try {
            /** 获取意图识别预处理future */
            Future<DialogueIntentionVO> future = handleDTO.getDialogueIntentionFuture();
            if (ObjectUtil.isNotNull(future)) {
                // 等到任务都完成，并设置超时60s
                intention = future.get(FutureConstants.SIXTY_SECONDS, TimeUnit.SECONDS);
                // 处理邮箱渠道的意图转换
                handleMailChannelIntentionConvert(intention, handleDTO);

            }
        } catch (Exception e) {
            log.error("InputIntentionHandlerImpl-getDialogueIntention，future.get()，异常", e);
        }
        
        // 设置输出意图
        handleDTO.getRespVO().setOutputCommandVO(intention);
        
        return intention;
    }



    /**
     * 处理前端传入了意图，但是没有意图识别实体的情况
     * 意图识别获取实体，但把意图替换成前端传入的意图
     *
     * @param handleDTO 用户输入对象
     */
    public DialogueIntentionVO createInputIntention(ChatAddHandleDTO handleDTO) {
        DialogueIntentionDTO command = handleDTO.getInputInfoDTO().getCommand();
        // 前端传入了意图，并且意图是枚举里的
        if(ObjectUtil.isNotNull(command) && DialogueIntentionEnum.isExist(command.getCommand())){
            // 意图识别
            DialogueIntentionVO dialogueIntention = dialogueIntentionService.getIntentionVOV2(handleDTO);
            List<DialogueIntentionVO.IntentionInfo> intentionInfoList = dialogueIntention.getIntentionInfoList();
            if(CollUtil.isNotEmpty(intentionInfoList)){
                DialogueIntentionVO.IntentionInfo intentionInfo = intentionInfoList.get(0);
                List<IntentEntityVO> entityList = intentionInfo.getEntityList();
                List<DialogueIntentionVO.IntentionInfo> subIntentions = intentionInfo.getSubIntentions();
                // 主意图实体为空且子意图不为空，则把第一个子意图的实体赋值给主意图
                if(CollUtil.isEmpty(entityList) && CollUtil.isNotEmpty(subIntentions)){
                    intentionInfo.setEntityList(subIntentions.get(0).getEntityList());
                }
                intentionInfo.setIntention(command.getCommand());
                handleDTO.setIntentionVO(dialogueIntention);
            }
        }
        return handleDTO.getIntentionVO();
    }

    /**
     * 处理邮箱渠道的意图转换
     *
     * @param intention 意图识别结果
     * @param handleDTO 处理对象
     */
    private void handleMailChannelIntentionConvert(DialogueIntentionVO intention, ChatAddHandleDTO handleDTO) {
        //如果是邮箱渠道确实chat的要进行意图转换
        ChatAddReqDTO reqDto = handleDTO.getReqDTO();
        if (intention != null && reqDto != null) {
            String applicationType = reqDto.getApplicationType();
            String channel = reqDto.getSourceChannel();
            if (SourceChannelsProperties.isMailChannel(channel) && ApplicationTypeEnum.CHAT.getCode().equals(applicationType)) {
                boolean isTransferIntention = mailaiSearchProperties.isTransferIntention();
                if (isTransferIntention) {
                    //云邮意图转换
                    String businessType = handleDTO.getBusinessType();
                    DialogueInputInfoDTO dialogueInput = reqDto.getDialogueInput();
                    if (dialogueInput != null) {
                        String userInput = dialogueInput.getDialogue();
                        boolean enableAiSearch = dialogueInput.isEnableAiSearch();
                        intentionConvertUtils.convertIntention(intention, channel, businessType, enableAiSearch, userInput);
                    }
                }
            }
        }
    }

}
