package com.zyhl.yun.api.outer.application.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Classname NoteDetailDTO
 * @Description 邮件详情接口入参dto
 * @Date 2024/2/29 9:46
 */
@Data
public class MailDetailReqDTO {

    /**
     * 用户登录SessionID
     */
    @NotEmpty(message = "用户登录SessionID不能为空")
    private String sid;

    /**
     * RMKEY
     */
    @NotEmpty(message = "RMKEY不能为空")
    private String rmKey;

    /**
     * 邮件ID
     */
    @NotEmpty(message = "邮件ID不能为空")
    private String mid;

    /**
     * 用户输入的内容
     */
    private String inputContent;

}
