package com.zyhl.yun.api.outer.application.chatv2.service.impl.openapi;

import java.net.URLEncoder;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.zyhl.yun.api.outer.application.chatv2.dto.AssistantChatV2PollingUpdateDTO;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatContentService;
import com.zyhl.yun.api.outer.application.chatv2.service.IntelligentCommonService;
import com.zyhl.yun.api.outer.application.chatv2.service.openapi.OpenapiLingxiCommonService;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueResultV2VO;
import com.zyhl.yun.api.outer.application.config.ApplicationAgentLingxiConfig;
import com.zyhl.yun.api.outer.application.config.ApplicationAgentLingxiConfig.LingxiBusinessConfig;
import com.zyhl.yun.api.outer.constants.ReplaceConstants;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.external.service.UserEtnService;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 智能体对话-公共-服务实现
 *
 * <AUTHOR>
 * @date 2025-07-04 08:56
 **/
@Slf4j
@Service
public class OpenapiLingxiCommonServiceImpl implements OpenapiLingxiCommonService {

	@Resource
	private IntelligentCommonService intelligentCommonService;

	@Resource
	private ChatContentService chatContentService;

	@Resource
	private UserEtnService userEtnService;

	@Resource
	private ApplicationAgentLingxiConfig applicationAgentLingxiConfig;

	@Override
	public DialogueResultV2VO contentPollingUpdate(AssistantChatV2PollingUpdateDTO pollingUpdate) {
		return intelligentCommonService.contentPollingUpdate(pollingUpdate);
	}

	@Override
	public String getDialogueIdByIntention(String userId, Long sessionId, IntentionInfo mainIntention) {
		LingxiBusinessConfig businessConfig = applicationAgentLingxiConfig.getBusinessConfig();
		String lastDialogueId = chatContentService.getDialogueIdByIntention(userId, sessionId, mainIntention,
				businessConfig.getIntentionQuerySize());
		log.info("根据意图获取对话id  mainIntention:{}, lastDialogueId:{}", JSONUtil.toJsonStr(mainIntention), lastDialogueId);
		return lastDialogueId;
	}

	@Override
	public AlgorithmChatContentEntity getLastIntentionDialogue(String userId, Long sessionId, String dialogueId) {
		return intelligentCommonService.getLastIntentionDialogue(userId, sessionId, dialogueId);
	}

	/**
	 * 通过对话信息获取邮件信息
	 * 
	 * @param lastMailDialogueInfo 上一次发邮件对话信息
	 * @return
	 */
	@Override
	public MailInfoVO getMailInfoResult(AlgorithmChatContentEntity lastMailDialogueInfo) {
		return intelligentCommonService.getMailInfoResult(lastMailDialogueInfo);
	}

	@Override
	public String getSendMailJumpUrl(String token) {
		LingxiBusinessConfig businessConfig = applicationAgentLingxiConfig.getBusinessConfig();
		if (null == businessConfig || null == businessConfig.getMailSendJumpUrl()) {
			return null;
		}
		return businessConfig.getMailSendJumpUrl().replace(ReplaceConstants.REPLACE_TOKEN,
				userEtnService.querySpecToken(token));
	}

	@Override
	public String getMailEditJumpUrl(String token, String dialogueId) {
		LingxiBusinessConfig businessConfig = applicationAgentLingxiConfig.getBusinessConfig();
		if (null == businessConfig || null == businessConfig.getMailEditJumpUrl()) {
			return null;
		}
		String paramCardButtonUrl = businessConfig.getMailEditJumpUrl().replace(ReplaceConstants.REPLACE_TOKEN,
				userEtnService.querySpecToken(token));
		return paramCardButtonUrl.replace(ReplaceConstants.REPLACE_DIALOGUE_ID, dialogueId);
	}

	@Override
	public String getAiHelperJumpUrl(String token) {
		LingxiBusinessConfig businessConfig = applicationAgentLingxiConfig.getBusinessConfig();
		if (null == businessConfig || null == businessConfig.getAiHelperJumpUrl()) {
			return null;
		}
		return businessConfig.getAiHelperJumpUrl().replace(ReplaceConstants.REPLACE_TOKEN,
				userEtnService.querySpecToken(token));
	}

	@SuppressWarnings("deprecation")
	@Override
	public String getNoteVoiceJumpUrl(String token, String noteTitle) {
		LingxiBusinessConfig businessConfig = applicationAgentLingxiConfig.getBusinessConfig();
		if (null == businessConfig || null == businessConfig.getNoteVoiceJumpUrl()) {
			return null;
		}
		String url = businessConfig.getNoteVoiceJumpUrl().replace(ReplaceConstants.REPLACE_TOKEN,
				userEtnService.querySpecToken(token));
		if (StringUtils.isNotEmpty(noteTitle)) {
			url = url + "&noteTitle=" + URLEncoder.encode(noteTitle);
		}
		return url;
	}
}
