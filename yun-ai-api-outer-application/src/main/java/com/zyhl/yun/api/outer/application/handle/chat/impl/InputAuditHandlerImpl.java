package com.zyhl.yun.api.outer.application.handle.chat.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.handle.chat.AbstractChatAddHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 用户输入内容送审
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class InputAuditHandlerImpl extends AbstractChatAddHandler {

    @Override
    public int order() {
        return ExecuteSort.INPUT_AUDIT.getSort();
    }

    @Override
    public boolean execute(ChatAddInnerDTO innerDTO) {
        // 不为空才执行
        return CharSequenceUtil.isNotEmpty(innerDTO.getContent().getDialogue());
    }

    @Override
    public boolean run(ChatAddInnerDTO innerDTO) {
        log.info("进入输入内容送审处理");
        //会话输入内容送审
        checkSystemDomainService.checkLocalAndPlatformException(innerDTO.getDialogueId(), innerDTO.getReqParams().getUserId(), innerDTO.getContent().getDialogue());

        return true;
    }

}
