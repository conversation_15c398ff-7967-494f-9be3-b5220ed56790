package com.zyhl.yun.api.outer.application.service.chat;

import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.domain.vo.IntentionRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;

/**
 * 引导文案对象接口
 *
 * <AUTHOR>
 */
public interface LeadCopyService {

    /**
     * 获取引导文案对象VO
     *
     * @param intentionVO   意图结果对象
     * @param sourceChannel 渠道来源
     * @param inContent     对话输入文本内容
     * @return 引导文案对象VO
     */
    LeadCopyVO getLeadCopyVo(DialogueIntentionVO intentionVO, String sourceChannel, String inContent);

    /**
     * 获取引导文案对象VO（输入资源ID为空）
     *
     * @param intentionVO   意图结果对象
     * @param sourceChannel 渠道来源
     * @param inResourceId  输入资源ID
     * @param inContent     对话输入文本内容
     * @return 引导文案对象VO
     */
    LeadCopyVO getLeadCopyVo(DialogueIntentionVO intentionVO, String sourceChannel, String inResourceId, String inContent);

    /**
     * 旧版本setIntentionList迁移到LeadCopy，setIntentionListLeadCopy
     *
     * @param params 聊天参数
     */
    void leadCopyType5(ChatAddInnerDTO params);

    /**
     * 获取引导文案对象VO
     *
     * @param vo 意图推荐对象
     * @return 引导对象
     */
    LeadCopyVO getLeadCopy(IntentionRecommendVO vo);

    /**
     * 获取引导文案对象VO（类型7）
     * @Author: WeiJingKun
     *
     * @param instruction 意图指令
     * @param promptCopy 提示文案
     * @return 引导对象
     */
    LeadCopyVO getLeadCopy7(String instruction, String promptCopy);

}
