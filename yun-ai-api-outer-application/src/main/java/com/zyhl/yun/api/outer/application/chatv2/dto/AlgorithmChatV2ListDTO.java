package com.zyhl.yun.api.outer.application.chatv2.dto;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.BaseDTO;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatMessageSortTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 历史会话列表查询接口-DTO
 * @Author: WeiJingKun
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class AlgorithmChatV2ListDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 2430353062658341350L;

    /**
     * 渠道来源
     * 详见，配置文件：source-channels
     */
    @NotBlank(message = "渠道来源不能为空")
    private String sourceChannel;

    /**
     * 应用类型
     * @see ApplicationTypeEnum
     */
    private String applicationType;

    /** 分页信息 */
    private PageInfoDTO pageInfo;

    /**
     * 排序方式（不填默认1）
     * 1--按照会话【创建时间】倒序排序
     * 2--按照会话【更新时间】倒序排序
     */
    private Integer sortType;

    /**
     * 参数校验
     * @Author: WeiJingKun
     */
    public void validate() {
        /** 检查登录的userId */
        checkTokenUserId();

        /** applicationType存在，则判断是否有效 */
        if(CharSequenceUtil.isNotBlank(applicationType)){
            if(!ApplicationTypeEnum.isExist(applicationType)){
                log.error("应用类型参数无效");
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_APPLICATION_TYPE_INVALID);
            }
        }

        /** 分页信息校验 */
        if(null != pageInfo){
            pageInfo.validate();
        }

        /** 默认 1-【创建时间】倒序排序 */
        if(sortType == null){
            sortType = ChatMessageSortTypeEnum.CREATE_TIME_DESC.getCode();
        }

        // 排序方式校验
        if (!ChatMessageSortTypeEnum.isExist(sortType)) {
            log.error("排序方式参数无效");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
    }

}
