package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.dashscope.utils.JsonUtils;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.enums.ParseTypeEnum;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.hcy.yun.ai.common.base.utils.StopWatchUtil;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.vo.CheckResultVO;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.RecallQueryDTO;
import com.zyhl.hcy.yun.ai.common.rag.enums.KnowledgeRerankTypeEnum;
import com.zyhl.hcy.yun.ai.common.rag.enums.RecallVersionEnum;
import com.zyhl.hcy.yun.ai.common.rag.vo.RecallResultVO;
import com.zyhl.hcy.yun.ai.common.rag.vo.RerankResultVO;
import com.zyhl.hcy.yun.ai.common.rag.vo.RewriteResultVO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueAttachmentDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.KnowledgeSearchDTO;
import com.zyhl.yun.api.outer.application.chatv2.pojo.KnowledgeFlowInfo;
import com.zyhl.yun.api.outer.application.chatv2.pojo.KnowledgeFlowInfoDataHandle;
import com.zyhl.yun.api.outer.application.chatv2.pojo.TextModelParamInfo;
import com.zyhl.yun.api.outer.application.chatv2.service.NoteKnowledgeSearchService;
import com.zyhl.yun.api.outer.application.chatv2.service.knowledge.strategy.InternetSearchStrategy;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueFlowResultVO;
import com.zyhl.yun.api.outer.application.service.knowledge.KnowledgeRecallUpdateService;
import com.zyhl.yun.api.outer.config.KnowledgeDialogueProperties;
import com.zyhl.yun.api.outer.config.NoteKnowledgeDialogueProperties;
import com.zyhl.yun.api.outer.config.VipKnowledgeCommonProperties;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.domain.dto.KnowledgeDialogueConfigDTO;
import com.zyhl.yun.api.outer.domain.dto.RecallDTO;
import com.zyhl.yun.api.outer.domain.dto.RerankDTO;
import com.zyhl.yun.api.outer.domain.entity.DialogueIntentionEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.AlgorithmKnowledgeConfigEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.KnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.valueobject.KnowledgeSearchInfo;
import com.zyhl.yun.api.outer.domain.valueobject.NoteInfo;
import com.zyhl.yun.api.outer.domain.valueobject.PersonalKnowledgeBase;
import com.zyhl.yun.api.outer.domain.valueobject.PersonalKnowledgeResource;
import com.zyhl.yun.api.outer.domainservice.KnowledgeDomainService;
import com.zyhl.yun.api.outer.domainservice.NounLibraryDomainService;
import com.zyhl.yun.api.outer.domainservice.UserKnowledgeDomainService;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatMiddleCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.SseNameEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileAuditStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeBaseEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeOpenLevelEnum;
import com.zyhl.yun.api.outer.external.*;
import com.zyhl.yun.api.outer.repository.AlgorithmKnowledgeConfigRepository;
import com.zyhl.yun.api.outer.repository.KnowledgeFileRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 描述：知识库对话相关数据
 *
 * <AUTHOR> yinxin  2025/6/23 15:57
 */
@Slf4j
@Service
public class NoteKnowledgeSearchServiceImpl implements NoteKnowledgeSearchService {

    @Resource
    private RagExternalService ragExternalService;
    @Resource
    private NoteRagExternalService noteRagExternalService;
    @Resource
    private KnowledgeRecallUpdateService knowledgeRecallUpdateService;
    @Resource
    private UserKnowledgeDomainService userKnowledgeDomainService;
    @Resource
    private UserKnowledgeFileRepository userKnowledgeFileRepository;
    @Resource
    private AlgorithmKnowledgeConfigRepository knowledgeConfigRepository;
    @Resource
    private KnowledgeFileRepository knowledgeFileRepository;
    @Resource
    private UserKnowledgeRepository userKnowledgeRepository;
    @Resource
    private AiInternetSearchExternalService aiInternetSearchExternalService;
    @Resource
    private KnowledgeDialogueProperties knowledgeDialogueProperties;
    @Resource
    private NoteKnowledgeDialogueProperties noteKnowledgeDialogueProperties;
    @Resource
    private VipKnowledgeCommonProperties vipKnowledgeCommonProperties;
    @Resource
    private KnowledgeDomainService knowledgeDomainService;
    @Resource
    private NounLibraryDomainService nounLibraryService;
    @Resource
    private UidGenerator uidGenerator;
    @Resource
    private DialogueIntentionExternalService dialogueIntentionExternalService;
    @Resource
    private InternetSearchStrategy internetSearchStrategy;
    @Resource
    private CheckSystemExternalService checkSystemExternalService;

    @Resource(name = "multiRouteRerankThreadPool")
    private ExecutorService multiRouteRerankThreadPool;


    // 创建线程池
    private final static int corePoolSize = 5;
    private final static int maxPoolSize = 50;
    private final static long keepAliveTime = 600;
    private final static int queueSize = 10000;
    private final static ThreadPoolExecutor pool = new ThreadPoolExecutor(corePoolSize, maxPoolSize, keepAliveTime, TimeUnit.SECONDS, new ArrayBlockingQueue<>(queueSize));

    @Override
    public KnowledgeFlowInfo setKnowledgeFlowInfo(ChatAddHandleDTO handleDTO) {
        // 个人知识库 或者 公共知识库  有一个可用则执行
        KnowledgeFlowInfo knowledgeFlowInfo = handleDTO.getKnowledgeFlowInfo();
        DialogueAttachmentDTO attachment  = handleDTO.getInputInfoDTO().getAttachment();

        List<Integer> typeList = attachment.getAttachmentTypeList();
        if (ResourceTypeEnum.containsKnowledgeFile(typeList)) {
            knowledgeFlowInfo.setPersonalEnable(true);
        } else if (ResourceTypeEnum.containsKnowledgeBase(typeList)) {
            knowledgeFlowInfo.setPersonalEnable(true);
        } else if (!CollectionUtils.isEmpty(attachment.getNoteList())) {
            knowledgeFlowInfo.setPersonalEnable(true);
        } else {
            if (ObjectUtil.isNotEmpty(typeList)
                    && !ResourceTypeEnum.isText(typeList.get(0))) {
                return null;
            }
            List<UserKnowledgeEntity> list = knowledgeDomainService.personalEnable2(handleDTO.getReqDTO().getUserId());
            if (ObjectUtil.isEmpty(list)) {
                return null;
            }
            knowledgeFlowInfo.setSelectedKnowledgeList(list);
            knowledgeFlowInfo.setPersonalEnable(ObjectUtil.isNotEmpty(list));
        }

        knowledgeFlowInfo.setCommonEnable(knowledgeDomainService.commonEnable(handleDTO.getReqDTO().getSourceChannel()));
        knowledgeFlowInfo.setVipCommonEnable(knowledgeDomainService.vipCommonEnable());
        return knowledgeFlowInfo;
    }

    @Override
    public void knowledgeSearch(ChatAddHandleDTO handleDTO, List<TextModelMessageDTO> historyList) {
        Map<String, String> logMap = MDC.getCopyOfContextMap();

        // 获取主线程的ThreadLocal信息，并且异步调用前，将父线程中的请求信息绑定给子线程
        RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo = RequestContextHolder.getThreadLocalInfoAndBindingAttributes();

        // 多线程对象，不能把handleDTO直接传入线程中，防止数据污染
        KnowledgeSearchDTO dto = new KnowledgeSearchDTO(handleDTO);

        CountDownLatch countDownLatch = new CountDownLatch(2);
        pool.submit(() -> {
            long startTime = System.currentTimeMillis();
            try {
                MDC.setContextMap(logMap);
                // 把主线程ThreadLocal信息set到子线程
                RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);
                // 向前端发送搜索中
                DialogueFlowResultVO flowResultVO = new DialogueFlowResultVO(ChatMiddleCodeEnum.KNOWLEDGE_BASE_SEARCHING, dto.getModelType());
                flowResultVO.setModelType(dto.getModelType());
                dto.getSseEmitterOperate().sendSuccess(dto.getRespVO().processingFlowResult(flowResultVO));
                // 知识库搜索
                knowledgeRecall(dto, handleDTO.getReqDTO(), handleDTO.getRespVO(), historyList);
                // 笔记知识库搜索
                noteKnowledgeRecall(dto, handleDTO.getReqDTO(), handleDTO.getRespVO(), historyList);
            } catch (Exception e) {
                log.error("【知识库对话】【RAG重要节点日志】【数据搜索】知识库数据召回异常，异常信息：{}", e.getMessage(), e);
            } finally {
                countDownLatch.countDown();
                MDC.put(LogConstants.DURATION, String.valueOf(System.currentTimeMillis() - startTime));
                log.info("【知识库对话】【RAG重要节点日志】【数据搜索】知识库数据召回结束，耗时：{}ms", System.currentTimeMillis() - startTime);
                MDC.remove(LogConstants.DURATION);
            }
        });

        /*pool.submit(() -> {
            long startTime = System.currentTimeMillis();
            try {
                MDC.setContextMap(logMap);
                // 把主线程ThreadLocal信息set到子线程
                RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);
                // 向前端发送搜索中
                DialogueFlowResultVO flowResultVO = new DialogueFlowResultVO(ChatMiddleCodeEnum.KNOWLEDGE_BASE_SEARCHING, dto.getModelType());
                flowResultVO.setModelType(dto.getModelType());
                dto.getSseEmitterOperate().sendSuccess(dto.getRespVO().processingFlowResult(flowResultVO));
                // 笔记知识库搜索
                noteKnowledgeRecall(dto, handleDTO.getReqDTO(), handleDTO.getRespVO(), historyList);
            } catch (Exception e) {
                log.error("【笔记助手-知识库对话】【RAG重要节点日志】【数据搜索】知识库数据召回异常，异常信息：{}", e.getMessage(), e);
            } finally {
                countDownLatch.countDown();
                MDC.put(LogConstants.DURATION, String.valueOf(System.currentTimeMillis() - startTime));
                log.info("【笔记助手-知识库对话】【RAG重要节点日志】【数据搜索】知识库数据召回结束，耗时：{}ms", System.currentTimeMillis() - startTime);
                MDC.remove(LogConstants.DURATION);
            }
        });*/
        pool.submit(() -> {
            long startTime = System.currentTimeMillis();
            try {
                MDC.setContextMap(logMap);
                // 把主线程ThreadLocal信息set到子线程
                RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);
                // 网络搜索
                dto.setNetworkList(internetSearchStrategy.internetSearch(handleDTO));
            } catch (Exception e) {
                log.error("【知识库对话】【RAG重要节点日志】【数据搜索】网络搜索异常，异常信息：{}", e.getMessage(), e);
            } finally {
                countDownLatch.countDown();
                MDC.put(LogConstants.DURATION, String.valueOf(System.currentTimeMillis() - startTime));
                log.info("【知识库对话】【RAG重要节点日志】【数据搜索】网络搜索结束，耗时：{}ms", System.currentTimeMillis() - startTime);
                MDC.remove(LogConstants.DURATION);
            }
        });

        try {
            boolean notimeout = countDownLatch.await(knowledgeDialogueProperties.getSearchTimeout(), TimeUnit.SECONDS);
            if (!notimeout) {
                dto.setSseName(SseNameEnum.KNOWLEDGE_RECALL_TIMEOUT.getCode());
                log.info("【知识库对话】【RAG重要节点日志】【数据搜索】知识库相关搜索超时，超时时间：{}s", knowledgeDialogueProperties.getSearchTimeout());
            }
        } catch (Exception e) {
            log.error("【知识库对话】【RAG重要节点日志】【数据搜索】线程等待异常", e);
        }

        // 个人知识库文件列表为空，查询名词库结果
        handleNounLibrary(handleDTO, dto);

        // 搜索结束，数据复制
        handleDTO.getKnowledgeFlowInfo().setQuery(handleDTO.getInputInfoDTO().getDialogue());
        handleDTO.getKnowledgeFlowInfo().getKnowledgeList().addAll(dto.getKnowledgeList());
        handleDTO.getKnowledgeFlowInfo().setPersonalFileList(dto.getPersonalFileList());
        handleDTO.getKnowledgeFlowInfo().setCommonFileList(dto.getCommonFileList());
        handleDTO.getKnowledgeFlowInfo().getNetworkList().addAll(dto.getNetworkList());
        handleDTO.getSseEmitterOperate().setSseName(dto.getSseName());
        handleDTO.getKnowledgeFlowInfo().setRewrite(dto.getRewrite());
    }

    private void noteKnowledgeRecall(KnowledgeSearchDTO dto, ChatAddReqDTO reqDTO, ChatAddRespVO respVO, List<TextModelMessageDTO> historyList) {
        // query重写
        ChatAddHandleDTO addDTO = new ChatAddHandleDTO(reqDTO, null);
        addDTO.setRespVO(respVO);
        TextModelParamInfo modelDTO = new TextModelParamInfo(addDTO, historyList);
        RewriteResultVO rewrite = noteRagExternalService.rewrite(modelDTO.toTextReqDTO(null));
        dto.setRewrite(rewrite);

        // query向量化
        List<BigDecimal> featureList = noteRagExternalService.textEmbed(rewrite.getQuery(), dto.getDialogueId());
        if (ObjectUtil.isEmpty(featureList)) {
            log.info("【笔记助手-知识库对话】【RAG重要节点日志】【文本向量化】结果为空");
            return;
        }

        // 资源信息列表
        List<RecallQueryDTO.KnowledgeGroup> resourceInfoList = getNoteResourceInfoList(dto);
        if (resourceInfoList.isEmpty()) {
            log.info("【笔记助手-知识库对话】【RAG重要节点日志】【数据搜索】传入的笔记noteList为空或不符合要求");
            return;
        }

        // 数据库配置
        KnowledgeDialogueConfigDTO dialogueConfig = knowledgeDomainService.getUserDialogueConfig(dto.getUserId());

        // es搜索（多路召回）
        RecallDTO recallDTO = new RecallDTO(dto.isCommonEnable(), dto.isPersonalEnable(), dto.isVipCommonEnable());
        recallDTO.setText(rewrite.getQuery());
        recallDTO.setFeature(featureList);
        recallDTO.setUserId(dto.getUserId());
        recallDTO.setIntentionVO(dto.getIntentionVO());
        recallDTO.setKnowledgeGroupList(resourceInfoList);
        recallDTO.setVersion(RecallVersionEnum.V2.getVersion());
        recallDTO.setQueryType(rewrite.getQueryType());
        recallDTO.setConfig(dialogueConfig.getRecallConfig());
        //获取问题关键字 1、输入原文获取关键字、2重写后内容获取关键字 合并去重得到结果
        List<String> keywordList = noteKeywordExtract(addDTO, rewrite.getQuery());
        if (CollUtil.isNotEmpty(keywordList)) {
            recallDTO.setTextKeywordList(keywordList);
        }
        List<RecallResultVO> recallResult = noteRagExternalService.recall(recallDTO);
        if (ObjectUtil.isEmpty(recallResult)) {
            log.info("【笔记助手-知识库对话】【RAG重要节点日志】【多路召回】结果为空");
            return;
        }

        List<RerankResultVO> rerankResult = new ArrayList<>();
        if (noteKnowledgeDialogueProperties.isEnableMultiRerank()) {
            // 多路重排
            rerankResult = noteMultiRouteRerank(dto, dialogueConfig, recallResult, rewrite);
            if (CollUtil.isEmpty(rerankResult)) {
                log.info("【笔记助手-知识库对话】【RAG重要节点日志】【rag算法重排】【多路】结果为空");
                dto.setSseName(SseNameEnum.KNOWLEDGE_NO_RERANK.getCode());
                return;
            }
        } else {
            // 单路算法重排
            RerankDTO rerankDTO = new RerankDTO();
            rerankDTO.setText(dto.getQuery());
            rerankDTO.setRecallList(recallResult);
            rerankDTO.setQueryType(rewrite.getQueryType());
            //个性化重排配置
            rerankDTO.setConfig(dialogueConfig.getRerankConfig());
            rerankResult = noteRagExternalService.rerank(rerankDTO);
            if (CollUtil.isEmpty(rerankResult)) {
                log.info("【笔记助手-知识库对话】【RAG重要节点日志】【rag算法重排】【单路】结果为空");
                dto.setSseName(SseNameEnum.KNOWLEDGE_NO_RERANK.getCode());
                return;
            }
        }

        // query和分块相关度判断（大模型+提示词）
        rerankResult = noteRagExternalService.relevancy(modelDTO.toSingleTextReqDTO(dto.getQuery()), rerankResult);
        if (ObjectUtil.isEmpty(rerankResult)) {
            log.info("【笔记助手-知识库对话】【RAG重要节点日志】【知识库相关性】query和分块相关度判断后，结果为空");
            return;
        }

        // 异步更新ES库召回次数
        knowledgeRecallUpdateService.recallUpdate(recallResult, rerankResult);

        // 文件过滤，过滤掉已经被删除了的文件并且合并普通知识库召回的分片
        noteFileFilter(dto, recallResult, rerankResult);
        if (ObjectUtil.isEmpty(dto.getKnowledgeList())) {
            log.info("【笔记助手-知识库对话】【RAG重要节点日志】命中个人知识库，但是文件不存在或者获取文件信息失败");
            return;
        }

        dto.setSseName(SseNameEnum.KNOWLEDGE_NOTE.getCode());
    }

    private void knowledgeRecall(KnowledgeSearchDTO dto, ChatAddReqDTO reqDTO, ChatAddRespVO respVO, List<TextModelMessageDTO> historyList) {

        // query重写
        ChatAddHandleDTO addDTO = new ChatAddHandleDTO(reqDTO, null);
        addDTO.setRespVO(respVO);
        TextModelParamInfo modelDTO = new TextModelParamInfo(addDTO, historyList);
        RewriteResultVO rewrite = ragExternalService.rewrite(modelDTO.toTextReqDTO(null));
        dto.setRewrite(rewrite);

        // query向量化
        List<BigDecimal> featureList = ragExternalService.textEmbed(rewrite.getQuery(), dto.getDialogueId());
        if (ObjectUtil.isEmpty(featureList)) {
            log.info("【知识库对话】【RAG重要节点日志】【文本向量化】结果为空");
            dto.setSseName(SseNameEnum.KNOWLEDGE_NO_VECTOR.getCode());
            return;
        }

        // 资源信息列表
        List<RecallQueryDTO.KnowledgeGroup> resourceInfoList = getReousrceInfoList(dto);
        //知识库文件id列表
        List<String> fileIdList = getFileIdByAttachment(dto);

        // 数据库配置
        KnowledgeDialogueConfigDTO dialogueConfig = knowledgeDomainService.getUserDialogueConfig(dto.getUserId());

        // es搜索（多路召回）
        RecallDTO recallDTO = new RecallDTO(dto.isCommonEnable(), dto.isPersonalEnable(), dto.isVipCommonEnable());
        recallDTO.setText(rewrite.getQuery());
        recallDTO.setFeature(featureList);
        recallDTO.setUserId(dto.getUserId());
        recallDTO.setIntentionVO(dto.getIntentionVO());
        recallDTO.setFileIdList(fileIdList);
        recallDTO.setKnowledgeGroupList(resourceInfoList);
        recallDTO.setVersion(RecallVersionEnum.V2.getVersion());
        recallDTO.setQueryType(rewrite.getQueryType());
        recallDTO.setConfig(dialogueConfig.getRecallConfig());
        //获取问题关键字 1、输入原文获取关键字、2重写后内容获取关键字 合并去重得到结果
        List<String> keywordList = keywordExtract(addDTO, rewrite.getQuery());
        if (CollUtil.isNotEmpty(keywordList)) {
            recallDTO.setTextKeywordList(keywordList);
        }
        List<RecallResultVO> recallResult = ragExternalService.recall(recallDTO);
        if (ObjectUtil.isEmpty(recallResult)) {
            log.info("【知识库对话】【RAG重要节点日志】【多路召回】结果为空");
            dto.setSseName(SseNameEnum.KNOWLEDGE_NO_RECALL.getCode());
            return;
        }

        List<RerankResultVO> rerankResult = new ArrayList<>();
        if (knowledgeDialogueProperties.isEnableMultiRerank()) {
            // 多路重排
            rerankResult = multiRouteRerank(dto, dialogueConfig, recallResult, rewrite);
            if (CollUtil.isEmpty(rerankResult)) {
                log.info("【知识库对话】【RAG重要节点日志】【rag算法重排】【多路】结果为空");
                dto.setSseName(SseNameEnum.KNOWLEDGE_NO_RERANK.getCode());
                return;
            }
        } else {
            // 单路算法重排
            RerankDTO rerankDTO = new RerankDTO();
            rerankDTO.setText(dto.getQuery());
            rerankDTO.setRecallList(recallResult);
            rerankDTO.setQueryType(rewrite.getQueryType());
            //个性化重排配置
            rerankDTO.setConfig(dialogueConfig.getRerankConfig());
            rerankResult = ragExternalService.rerank(rerankDTO);
            if (CollUtil.isEmpty(rerankResult)) {
                log.info("【知识库对话】【RAG重要节点日志】【rag算法重排】【单路】结果为空");
                dto.setSseName(SseNameEnum.KNOWLEDGE_NO_RERANK.getCode());
                return;
            }
        }

        // query和分块相关度判断（大模型+提示词）
        int rerankResultSize = rerankResult.size();
        rerankResult = ragExternalService.relevancy(modelDTO.toSingleTextReqDTO(dto.getQuery()), rerankResult);
        if (rerankResultSize != rerankResult.size()) {
            //知识库判断命中拦截
            dto.setSseName(SseNameEnum.KNOWLEDGE_RELEVANCY.getCode());
        }
        if (ObjectUtil.isEmpty(rerankResult)) {
            log.info("【知识库对话】【RAG重要节点日志】【知识库相关性】query和分块相关度判断后，结果为空");
            return;
        }

        // 异步更新ES库召回次数
        knowledgeRecallUpdateService.recallUpdate(recallResult, rerankResult);

        // 文件过滤，过滤掉已经被删除了的文件
        fileFilter(dto, recallResult, rerankResult);
        if (ObjectUtil.isEmpty(dto.getKnowledgeList())) {
            log.info("【知识库对话】【RAG重要节点日志】命中个人知识库，但是文件不存在或者获取文件信息失败");
            dto.setSseName(SseNameEnum.KNOWLEDGE_NO_FILE.getCode());
            return;
        }

        dto.setSseName(SseNameEnum.KNOWLEDGE.getCode());
    }

    private List<String> noteKeywordExtract(ChatAddHandleDTO addDTO, String query) {
        Set<String> resultSet = new HashSet<>();

        // 去除重复内容
        Set<String> textSet = new LinkedHashSet<>();
        textSet.add(query);
        textSet.add(addDTO.getInputInfoDTO().getDialogue());
        List<String> textList = new ArrayList<>(textSet);

        // 关键字提取
        List<List<String>> resultList = noteRagExternalService.keywordExtract(textList);

        // 合并所有关键字并去重
        Optional.ofNullable(resultList)
                .ifPresent(lists -> lists.stream()
                        .filter(Objects::nonNull)
                        .forEach(resultSet::addAll));

        return new ArrayList<>(resultSet);
    }

    private List<String> keywordExtract(ChatAddHandleDTO addDTO, String query) {
        Set<String> resultSet = new HashSet<>();

        // 去除重复内容
        Set<String> textSet = new LinkedHashSet<>();
        textSet.add(query);
        textSet.add(addDTO.getInputInfoDTO().getDialogue());
        List<String> textList = new ArrayList<>(textSet);

        // 关键字提取
        List<List<String>> resultList = ragExternalService.keywordExtract(textList);

        // 合并所有关键字并去重
        Optional.ofNullable(resultList)
                .ifPresent(lists -> lists.stream()
                        .filter(Objects::nonNull)
                        .forEach(resultSet::addAll));

        return new ArrayList<>(resultSet);
    }

    private List<String> getFileIdByAttachment(KnowledgeSearchDTO dto) {
        List<String> fileIdList = new ArrayList<>();
        // 附件资源
        DialogueAttachmentDTO attachmentDTO = dto.getAttachmentDTO();

        // 个人知识库
        if (ResourceTypeEnum.containsKnowledgeFile(attachmentDTO.getAttachmentTypeList())) {
            // 知识库文件类型
            fileIdList = attachmentDTO.getKnowledgeFileList()
                    .stream().map(PersonalKnowledgeResource::getResourceId)
                    .collect(Collectors.toList());
        }
        return fileIdList;
    }

    private List<RecallQueryDTO.KnowledgeGroup> getReousrceInfoList(KnowledgeSearchDTO dto) {
        List<RecallQueryDTO.KnowledgeGroup> resourceInfoList = new ArrayList<>();
        // 附件资源
        DialogueAttachmentDTO attachmentDTO = dto.getAttachmentDTO();

        // 个人知识库
        if (ResourceTypeEnum.containsKnowledgeFile(attachmentDTO.getAttachmentTypeList())) {
            // 知识库文件类型
            List<String> fileIdList = attachmentDTO.getKnowledgeFileList()
                    .stream().map(PersonalKnowledgeResource::getResourceId)
                    .collect(Collectors.toList());
            resourceInfoList.addAll(userKnowledgeDomainService.fileList(fileIdList));
        }
        if (ResourceTypeEnum.containsKnowledgeBase(attachmentDTO.getAttachmentTypeList())) {
            // 知识库类型
            List<PersonalKnowledgeBase> baseList = attachmentDTO.getKnowledgeBaseList();
            resourceInfoList.addAll(userKnowledgeDomainService.baseList(baseList, dto.getAssistantEnum().getCode()));
        }
        if (ObjectUtil.isNotEmpty(dto.getSelectedKnowledgeList())) {
            // 普通对话类型
            dto.getSelectedKnowledgeList().forEach(entity -> {
                RecallQueryDTO.KnowledgeGroup group = new RecallQueryDTO.KnowledgeGroup();
                group.setBaseId(String.valueOf(entity.getId()));
                group.setUserId(entity.getUserId());
                group.setKnowledgeBase(KnowledgeBaseEnum.PERSONAL.getCode());
                resourceInfoList.add(group);
            });
        }

        // 公共知识库
        if (dto.isCommonEnable()) {
            RecallQueryDTO.KnowledgeGroup group = new RecallQueryDTO.KnowledgeGroup();
            group.setBaseId(knowledgeDialogueProperties.getKnowledgeBaseId());
            group.setKnowledgeBase(KnowledgeBaseEnum.COMMON.getCode());
            resourceInfoList.add(group);
        }

        // VIP专属智能体知识库
        if (dto.isVipCommonEnable()) {
            RecallQueryDTO.KnowledgeGroup group = new RecallQueryDTO.KnowledgeGroup();
            group.setBaseId(vipKnowledgeCommonProperties.getKnowledgeBaseId());
            group.setKnowledgeBase(KnowledgeBaseEnum.COMMON.getCode());
            resourceInfoList.add(group);
        }
        return resourceInfoList;
    }

    private List<RecallQueryDTO.KnowledgeGroup> getNoteResourceInfoList(KnowledgeSearchDTO dto) {
        List<RecallQueryDTO.KnowledgeGroup> resourceInfoList = new ArrayList<>();
        String userId = dto.getUserId();

        // 传入的笔记id
        List<String> fileIdList = dto.getAttachmentDTO().getNoteList()
                .stream().map(NoteInfo::getNoteId)
                .collect(Collectors.toList());

        List<RecallQueryDTO.KnowledgeGroup> noteList = userKnowledgeDomainService.fileList(fileIdList);
        if (!CollectionUtils.isEmpty(noteList) && userId.equals(noteList.get(0).getUserId())) {
            resourceInfoList.addAll(noteList);
        }

        return resourceInfoList;
    }

    private List<RerankResultVO> noteMultiRouteRerank(KnowledgeSearchDTO dto, KnowledgeDialogueConfigDTO dialogueConfig, List<RecallResultVO> recallResult, RewriteResultVO rewrite) {
        /**
         * 将召回结果分为3大类：
         * 1、向量结果（切片向量化检索）
         * 2、全文结果（全文检索字段：text）
         * 3、关键字结果（全文检索字段：keyword）
         */
        Set<String> textSet = new HashSet<>();
        // 向量结果
        List<String> scalarFlag = Arrays.asList(ParseTypeEnum.TEXT.getCode(), ParseTypeEnum.KEYWORD.getCode());
        List<RecallResultVO> vectorRecallResult = recallResult.stream()
                .filter(recallResultVO -> !scalarFlag.contains(recallResultVO.getRecallParseType()))
                // 过滤：仅保留 text不重复的元素（add方法返回false表示text已经存在）
                .filter(item -> textSet.add(item.getText())).collect(Collectors.toList());
        // 全文结果
        textSet.clear();
        List<RecallResultVO> textRecallResult = recallResult.stream()
                .filter(recallResultVO -> ParseTypeEnum.TEXT.getCode().equals(recallResultVO.getRecallParseType()))
                // 过滤：仅保留 text不重复的元素（add方法返回false表示text已经存在）
                .filter(item -> textSet.add(item.getText())).collect(Collectors.toList());
        // 关键字结果
        textSet.clear();
        List<RecallResultVO> keywordRecallResult = recallResult.stream()
                .filter(recallResultVO -> ParseTypeEnum.KEYWORD.getCode().equals(recallResultVO.getRecallParseType()))
                // 过滤：仅保留 text不重复的元素（add方法返回false表示text已经存在）
                .filter(item -> textSet.add(item.getText())).collect(Collectors.toList());

        /** 多路重排 */
        // 开启计时
        StopWatch allStopWatch = StopWatchUtil.createStarted();
        List<RerankResultVO> rerankResult = new CopyOnWriteArrayList<>();
        Map<String, String> logMap = MDC.getCopyOfContextMap();
        // 获取主线程的ThreadLocal信息，并且异步调用前，将父线程中的请求信息绑定给子线程
        RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo = RequestContextHolder.getThreadLocalInfoAndBindingAttributes();
        // 3路
        CountDownLatch countDownLatch = new CountDownLatch(3);
        // 【向量】算法重排
        multiRouteRerankThreadPool.submit(() -> {
            // 开启计时
            StopWatch stopWatch = StopWatchUtil.createStarted();
            List<RerankResultVO> vectorRerankResult = new ArrayList();
            try {
                MDC.setContextMap(logMap);
                // 把主线程ThreadLocal信息set到子线程
                RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);

                RerankDTO rerankDTO = new RerankDTO();
                rerankDTO.setText(dto.getQuery());
                rerankDTO.setRecallList(vectorRecallResult);
                rerankDTO.setQueryType(rewrite.getQueryType());
                //个性化重排配置
                rerankDTO.setConfig(dialogueConfig.getRerankConfig());
                rerankDTO.setRerankType(KnowledgeRerankTypeEnum.VECTOR.getType());
                vectorRerankResult = noteRagExternalService.rerank(rerankDTO);
                if (CollUtil.isEmpty(vectorRerankResult)) {
                    log.info("【笔记助手-知识库对话】【RAG重要节点日志】【rag算法重排】【向量】结果为空");
                } else {
                    rerankResult.addAll(vectorRerankResult);
                }
            } catch (Exception e) {
                log.error("【笔记助手-知识库对话】【RAG重要节点日志】【rag算法重排】【向量】，异常：{}", e.getMessage(), e);
            } finally {
                countDownLatch.countDown();
                log.info("【笔记助手-知识库对话】【RAG重要节点日志】【rag算法重排】【向量】，耗时：{}，结果数：{}", StopWatchUtil.logTime(stopWatch), vectorRerankResult.size());
                StopWatchUtil.clearDuration();
            }
        });
        // 【全文】算法重排
        multiRouteRerankThreadPool.submit(() -> {
            // 开启计时
            StopWatch stopWatch = StopWatchUtil.createStarted();
            List<RerankResultVO> textRerankResult = new ArrayList();
            try {
                MDC.setContextMap(logMap);
                // 把主线程ThreadLocal信息set到子线程
                RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);

                RerankDTO rerankDTO = new RerankDTO();
                rerankDTO.setText(dto.getQuery());
                rerankDTO.setRecallList(textRecallResult);
                rerankDTO.setQueryType(rewrite.getQueryType());
                //个性化重排配置配置
                rerankDTO.setConfig(dialogueConfig.getRerankConfig());
                rerankDTO.setRerankType(KnowledgeRerankTypeEnum.TEXT.getType());
                textRerankResult = noteRagExternalService.rerank(rerankDTO);
                if (CollUtil.isEmpty(textRerankResult)) {
                    log.info("【笔记助手-知识库对话】【RAG重要节点日志】【rag算法重排】【全文】结果为空");
                } else {
                    rerankResult.addAll(textRerankResult);
                }
            } catch (Exception e) {
                log.error("【笔记助手-知识库对话】【RAG重要节点日志】【rag算法重排】【全文】，异常：{}", e.getMessage(), e);
            } finally {
                countDownLatch.countDown();
                log.info("【笔记助手-知识库对话】【RAG重要节点日志】【rag算法重排】【全文】，耗时：{}，结果数：{}", StopWatchUtil.logTime(stopWatch), textRerankResult.size());
                StopWatchUtil.clearDuration();
            }
        });
        // 【关键字】算法重排
        multiRouteRerankThreadPool.submit(() -> {
            // 开启计时
            StopWatch stopWatch = StopWatchUtil.createStarted();
            List<RerankResultVO> keywordRerankResult = new ArrayList();
            try {
                MDC.setContextMap(logMap);
                // 把主线程ThreadLocal信息set到子线程
                RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);

                RerankDTO rerankDTO = new RerankDTO();
                rerankDTO.setText(dto.getQuery());
                rerankDTO.setRecallList(keywordRecallResult);
                rerankDTO.setQueryType(rewrite.getQueryType());
                //个性化重排配置配置
                rerankDTO.setConfig(dialogueConfig.getRerankConfig());
                rerankDTO.setRerankType(KnowledgeRerankTypeEnum.KEYWORD.getType());
                keywordRerankResult = noteRagExternalService.rerank(rerankDTO);
                if (CollUtil.isEmpty(keywordRerankResult)) {
                    log.info("【笔记助手-知识库对话】【RAG重要节点日志】【rag算法重排】【关键字】结果为空");
                } else {
                    rerankResult.addAll(keywordRerankResult);
                }
            } catch (Exception e) {
                log.error("【笔记助手-知识库对话】【RAG重要节点日志】【rag算法重排】【关键字】，异常：{}", e.getMessage(), e);
            } finally {
                countDownLatch.countDown();
                log.info("【笔记助手-知识库对话】【RAG重要节点日志】【rag算法重排】【关键字】，耗时：{}，结果数：{}", StopWatchUtil.logTime(stopWatch), keywordRerankResult.size());
                StopWatchUtil.clearDuration();
            }
        });

        try {
            boolean noTimeout = countDownLatch.await(noteKnowledgeDialogueProperties.getSearchTimeout(), TimeUnit.SECONDS);
            if (!noTimeout) {
                dto.setSseName(SseNameEnum.KNOWLEDGE_RERANK_TIMEOUT.getCode());
                log.info("【笔记助手-知识库对话】【RAG重要节点日志】【rag算法重排】超时，超时时间：{}s", noteKnowledgeDialogueProperties.getSearchTimeout());
            }
        } catch (Exception e) {
            log.error("【笔记助手-知识库对话】【RAG重要节点日志】【rag算法重排】线程等待异常", e);
        }

        // 过滤：仅保留 text不重复的元素（add方法返回false表示text已经存在）
        textSet.clear();
        List<RerankResultVO> distincRrerankResult = rerankResult.stream().filter(item -> textSet.add(item.getDocument().getText())).collect(Collectors.toList());
        log.info("【笔记助手-知识库对话】【RAG重要节点日志】【rag算法重排】结束，总结果数：{}，总耗时：{}，去重前结果数：{}",
                distincRrerankResult.size(), StopWatchUtil.logTime(allStopWatch), rerankResult.size());

        LogCommonUtils.printlnStrLog("【笔记助手-知识库对话】【RAG重要节点日志】【rag算法重排】合并去重后结果", JsonUtil.toJson(distincRrerankResult));
        return distincRrerankResult;
    }

    private List<RerankResultVO> multiRouteRerank(KnowledgeSearchDTO dto, KnowledgeDialogueConfigDTO dialogueConfig, List<RecallResultVO> recallResult, RewriteResultVO rewrite) {
        /**
         * 将召回结果分为3大类：
         * 1、向量结果（切片向量化检索）
         * 2、全文结果（全文检索字段：text）
         * 3、关键字结果（全文检索字段：keyword）
         */
        Set<String> textSet = new HashSet<>();
        // 向量结果
        List<String> scalarFlag = Arrays.asList(ParseTypeEnum.TEXT.getCode(), ParseTypeEnum.KEYWORD.getCode());
        List<RecallResultVO> vectorRecallResult = recallResult.stream()
                .filter(recallResultVO -> !scalarFlag.contains(recallResultVO.getRecallParseType()))
                // 过滤：仅保留 text不重复的元素（add方法返回false表示text已经存在）
                .filter(item -> textSet.add(item.getText())).collect(Collectors.toList());
        // 全文结果
        textSet.clear();
        List<RecallResultVO> textRecallResult = recallResult.stream()
                .filter(recallResultVO -> ParseTypeEnum.TEXT.getCode().equals(recallResultVO.getRecallParseType()))
                // 过滤：仅保留 text不重复的元素（add方法返回false表示text已经存在）
                .filter(item -> textSet.add(item.getText())).collect(Collectors.toList());
        // 关键字结果
        textSet.clear();
        List<RecallResultVO> keywordRecallResult = recallResult.stream()
                .filter(recallResultVO -> ParseTypeEnum.KEYWORD.getCode().equals(recallResultVO.getRecallParseType()))
                // 过滤：仅保留 text不重复的元素（add方法返回false表示text已经存在）
                .filter(item -> textSet.add(item.getText())).collect(Collectors.toList());

        /** 多路重排 */
        // 开启计时
        StopWatch allStopWatch = StopWatchUtil.createStarted();
        List<RerankResultVO> rerankResult = new CopyOnWriteArrayList<>();
        Map<String, String> logMap = MDC.getCopyOfContextMap();
        // 获取主线程的ThreadLocal信息，并且异步调用前，将父线程中的请求信息绑定给子线程
        RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo = RequestContextHolder.getThreadLocalInfoAndBindingAttributes();
        // 3路
        CountDownLatch countDownLatch = new CountDownLatch(3);
        // 【向量】算法重排
        multiRouteRerankThreadPool.submit(() -> {
            // 开启计时
            StopWatch stopWatch = StopWatchUtil.createStarted();
            List<RerankResultVO> vectorRerankResult = new ArrayList();
            try {
                MDC.setContextMap(logMap);
                // 把主线程ThreadLocal信息set到子线程
                RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);

                RerankDTO rerankDTO = new RerankDTO();
                rerankDTO.setText(dto.getQuery());
                rerankDTO.setRecallList(vectorRecallResult);
                rerankDTO.setQueryType(rewrite.getQueryType());
                //个性化重排配置
                rerankDTO.setConfig(dialogueConfig.getRerankConfig());
                rerankDTO.setRerankType(KnowledgeRerankTypeEnum.VECTOR.getType());
                vectorRerankResult = ragExternalService.rerank(rerankDTO);
                if (CollUtil.isEmpty(vectorRerankResult)) {
                    log.info("【知识库对话】【RAG重要节点日志】【rag算法重排】【向量】结果为空");
                } else {
                    rerankResult.addAll(vectorRerankResult);
                }
            } catch (Exception e) {
                log.error("【知识库对话】【RAG重要节点日志】【rag算法重排】【向量】，异常：{}", e.getMessage(), e);
            } finally {
                countDownLatch.countDown();
                log.info("【知识库对话】【RAG重要节点日志】【rag算法重排】【向量】，耗时：{}，结果数：{}", StopWatchUtil.logTime(stopWatch), vectorRerankResult.size());
                StopWatchUtil.clearDuration();
            }
        });
        // 【全文】算法重排
        multiRouteRerankThreadPool.submit(() -> {
            // 开启计时
            StopWatch stopWatch = StopWatchUtil.createStarted();
            List<RerankResultVO> textRerankResult = new ArrayList();
            try {
                MDC.setContextMap(logMap);
                // 把主线程ThreadLocal信息set到子线程
                RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);

                RerankDTO rerankDTO = new RerankDTO();
                rerankDTO.setText(dto.getQuery());
                rerankDTO.setRecallList(textRecallResult);
                rerankDTO.setQueryType(rewrite.getQueryType());
                //个性化重排配置配置
                rerankDTO.setConfig(dialogueConfig.getRerankConfig());
                rerankDTO.setRerankType(KnowledgeRerankTypeEnum.TEXT.getType());
                textRerankResult = ragExternalService.rerank(rerankDTO);
                if (CollUtil.isEmpty(textRerankResult)) {
                    log.info("【知识库对话】【RAG重要节点日志】【rag算法重排】【全文】结果为空");
                } else {
                    rerankResult.addAll(textRerankResult);
                }
            } catch (Exception e) {
                log.error("【知识库对话】【RAG重要节点日志】【rag算法重排】【全文】，异常：{}", e.getMessage(), e);
            } finally {
                countDownLatch.countDown();
                log.info("【知识库对话】【RAG重要节点日志】【rag算法重排】【全文】，耗时：{}，结果数：{}", StopWatchUtil.logTime(stopWatch), textRerankResult.size());
                StopWatchUtil.clearDuration();
            }
        });
        // 【关键字】算法重排
        multiRouteRerankThreadPool.submit(() -> {
            // 开启计时
            StopWatch stopWatch = StopWatchUtil.createStarted();
            List<RerankResultVO> keywordRerankResult = new ArrayList();
            try {
                MDC.setContextMap(logMap);
                // 把主线程ThreadLocal信息set到子线程
                RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);

                RerankDTO rerankDTO = new RerankDTO();
                rerankDTO.setText(dto.getQuery());
                rerankDTO.setRecallList(keywordRecallResult);
                rerankDTO.setQueryType(rewrite.getQueryType());
                //个性化重排配置配置
                rerankDTO.setConfig(dialogueConfig.getRerankConfig());
                rerankDTO.setRerankType(KnowledgeRerankTypeEnum.KEYWORD.getType());
                keywordRerankResult = ragExternalService.rerank(rerankDTO);
                if (CollUtil.isEmpty(keywordRerankResult)) {
                    log.info("【知识库对话】【RAG重要节点日志】【rag算法重排】【关键字】结果为空");
                } else {
                    rerankResult.addAll(keywordRerankResult);
                }
            } catch (Exception e) {
                log.error("【知识库对话】【RAG重要节点日志】【rag算法重排】【关键字】，异常：{}", e.getMessage(), e);
            } finally {
                countDownLatch.countDown();
                log.info("【知识库对话】【RAG重要节点日志】【rag算法重排】【关键字】，耗时：{}，结果数：{}", StopWatchUtil.logTime(stopWatch), keywordRerankResult.size());
                StopWatchUtil.clearDuration();
            }
        });

        try {
            boolean noTimeout = countDownLatch.await(knowledgeDialogueProperties.getSearchTimeout(), TimeUnit.SECONDS);
            if (!noTimeout) {
                dto.setSseName(SseNameEnum.KNOWLEDGE_RERANK_TIMEOUT.getCode());
                log.info("【知识库对话】【RAG重要节点日志】【rag算法重排】超时，超时时间：{}s", knowledgeDialogueProperties.getSearchTimeout());
            }
        } catch (Exception e) {
            log.error("【知识库对话】【RAG重要节点日志】【rag算法重排】线程等待异常", e);
        }

        // 过滤：仅保留 text不重复的元素（add方法返回false表示text已经存在）
        textSet.clear();
        List<RerankResultVO> distincRrerankResult = rerankResult.stream().filter(item -> textSet.add(item.getDocument().getText())).collect(Collectors.toList());
        log.info("【知识库对话】【RAG重要节点日志】【rag算法重排】结束，总结果数：{}，总耗时：{}，去重前结果数：{}",
                distincRrerankResult.size(), StopWatchUtil.logTime(allStopWatch), rerankResult.size());

        LogCommonUtils.printlnStrLog("【知识库对话】【RAG重要节点日志】【rag算法重排】合并去重后结果", JsonUtil.toJson(distincRrerankResult));
        return distincRrerankResult;
    }

    private void noteFileFilter(KnowledgeSearchDTO dto, List<RecallResultVO> recallResult, List<RerankResultVO> rerankResult) {
        // 文件id和分片的集合（要保持重排的顺序）
        Map<String, List<RecallResultVO>> personalFileMap = new LinkedHashMap<>(Const.NUM_16);
        for (RerankResultVO vo : rerankResult) {
            RerankResultVO.DocumentDataVO dataVO = vo.getDocument();
            RecallResultVO recallVO = recallResult.get(Integer.parseInt(dataVO.getSegmentId()));
            // 个人知识库
            List<RecallResultVO> shardingList = personalFileMap.get(vo.getDocument().getFileId());
            if (ObjectUtil.isEmpty(shardingList)) {
                shardingList = new ArrayList<>();
                personalFileMap.put(vo.getDocument().getFileId(), shardingList);
            }
            shardingList.add(recallVO);
        }

        // 召回结果分组
        Map<String, List<RecallResultVO>> recallGroupMap = new HashMap<>(Const.NUM_16);
        recallGroupMap.putAll(personalFileMap);

        // 个人知识库文件处理
        List<KnowledgeSearchInfo> personalFileList = CollectionUtils.isEmpty(dto.getPersonalFileList()) ? new ArrayList<>() : dto.getPersonalFileList();
        List<KnowledgeFlowInfoDataHandle> knowledgeList = dto.getKnowledgeList();
        log.info("【笔记助手-知识库对话】【RAG重要节点日志】【召回的文件过滤】原先知识库personalFileList的大小:{} | knowledgeList的大小:{}", personalFileList.size(), knowledgeList.size());
        if (ObjectUtil.isNotEmpty(personalFileMap)) {
            List<KnowledgeSearchInfo> list = new ArrayList<>();
            // 查下文件列表
            List<String> fileIds = new ArrayList<>(personalFileMap.keySet());
            List<UserKnowledgeFileEntity> entityFileList = userKnowledgeFileRepository.selectByFileIds(null, fileIds);
            Map<String, UserKnowledgeFileEntity> fileMap = entityFileList.stream().collect(Collectors.toMap(UserKnowledgeFileEntity::getFileId, Function.identity()));

            // 查下知识库
            Set<Long> baseIdSet = entityFileList.stream().map(UserKnowledgeFileEntity::getBaseId).collect(Collectors.toSet());
            Map<Long, UserKnowledgeEntity> baseMap = userKnowledgeRepository.selectByIds(new ArrayList<>(baseIdSet))
                    .stream().collect(Collectors.toMap(UserKnowledgeEntity::getId, Function.identity()));

            // 将所有未送审的分片内容聚合放一起同步送审,送审不通过将所有分片id过滤
            contentAuditAndFilter(personalFileMap, dto.getDialogueId(), dto.getUserId());

            // 数据处理，为了保持有序性使用personalFileMap
            for (String fileId : personalFileMap.keySet()) {
                try {
                    UserKnowledgeFileEntity fileEntity = fileMap.get(fileId);
                    if (ObjectUtil.isEmpty(fileEntity)) {
                        continue;
                    }

                    UserKnowledgeEntity knowledge = baseMap.get(fileEntity.getBaseId());
                    // 分块对应的知识库文件送审不通过并且是公开知识库，需要过滤掉文件和分块
                    if (FileAuditStatusEnum.isNotPass(fileEntity.getAuditStatus()) && knowledge != null
                            && KnowledgeOpenLevelEnum.isOpen(knowledge.getOpenLevel())) {
                        log.info("【笔记助手-知识库对话】【RAG重要节点日志】【召回的文件过滤】分块对应的知识库文件送审不通过并且是公开知识库，过滤掉文件和分块 fileEntity:{}", JsonUtil.toJson(fileEntity));
                        continue;
                    }

                    // 分片处理
                    List<String> sortList = personalFileMap.get(fileId).stream()
                            .sorted(Comparator.comparingInt(vo -> vo.getIndex() == null ? 0 : vo.getIndex()))
                            .map(RecallResultVO::getText)
                            .collect(Collectors.toList());
                    List<String> shardingList = KnowledgeFlowInfoDataHandle.segmentHandle(sortList);
                    if (CollectionUtils.isEmpty(shardingList)) {
                        log.info("【笔记助手-知识库对话】【RAG重要节点日志】【召回的文件过滤】过滤后分块为空，整个文件不送给大模型 fileId:{}", fileId);
                        continue;
                    }

                    // 笔记知识库不需要知识库名称
                    list.add(new KnowledgeSearchInfo(fileEntity, shardingList, null));
                    knowledgeList.add(new KnowledgeFlowInfoDataHandle(recallGroupMap.get(fileId)));
                } catch (Exception e) {
                    log.error("【笔记助手-知识库对话】【RAG重要节点日志】【召回的文件过滤】数据处理异常 fileId:{}", fileId, e);
                }
            }

            personalFileList.addAll(list);
        }

        // 相关知识，送给大模型的数据 去重
        dto.setKnowledgeList(knowledgeList.stream().distinct().collect(Collectors.toList()));

        // 个人知识库文件列表 去重
        dto.setPersonalFileList(personalFileList.stream().distinct().collect(Collectors.toList()));

        LogCommonUtils.printlnListLog("【笔记助手-知识库对话】【RAG重要节点日志】【相关知识】【过滤后】个人知识库的相关知识第{}个分块：\n{}", JsonUtil.toJson(dto.getPersonalFileList()));
    }

    /**
     * 召回的文件过滤
     *
     * @param dto          流转信息
     * @param rerankResult 重排结果
     */
    private void fileFilter(KnowledgeSearchDTO dto, List<RecallResultVO> recallResult, List<RerankResultVO> rerankResult) {

        // 文件id和分片的集合（要保持重排的顺序）
        Map<String, List<RecallResultVO>> personalFileMap = new LinkedHashMap<>(Const.NUM_16);
        Map<String, List<RecallResultVO>> commonFileMap = new LinkedHashMap<>(Const.NUM_16);
        for (RerankResultVO vo : rerankResult) {
            RerankResultVO.DocumentDataVO dataVO = vo.getDocument();
            RecallResultVO recallVO = recallResult.get(Integer.parseInt(dataVO.getSegmentId()));
            if (KnowledgeBaseEnum.isPersonal(recallVO.getKnowledgeBase())) {
                // 个人知识库
                List<RecallResultVO> shardingList = personalFileMap.get(vo.getDocument().getFileId());
                if (ObjectUtil.isEmpty(shardingList)) {
                    shardingList = new ArrayList<>();
                    personalFileMap.put(vo.getDocument().getFileId(), shardingList);
                }
                shardingList.add(recallVO);
            } else if (KnowledgeBaseEnum.isCommon(recallVO.getKnowledgeBase())) {
                // 公共知识库
                List<RecallResultVO> shardingList = commonFileMap.get(vo.getDocument().getFileId());
                if (ObjectUtil.isEmpty(shardingList)) {
                    shardingList = new ArrayList<>();
                    commonFileMap.put(vo.getDocument().getFileId(), shardingList);
                }
                shardingList.add(recallVO);
            }
        }

        // 召回结果分组
        Map<String, List<RecallResultVO>> recallGroupMap = new HashMap<>(Const.NUM_16);
        recallGroupMap.putAll(personalFileMap);
        recallGroupMap.putAll(commonFileMap);

        // 个人知识库文件处理
        if (ObjectUtil.isNotEmpty(personalFileMap)) {
            List<KnowledgeSearchInfo> list = new ArrayList<>();
            // 查下文件列表
            List<String> fileIds = new ArrayList<>(personalFileMap.keySet());
            List<UserKnowledgeFileEntity> entityFileList = userKnowledgeFileRepository.selectByFileIds(null, fileIds);
            Map<String, UserKnowledgeFileEntity> fileMap = entityFileList.stream().collect(Collectors.toMap(UserKnowledgeFileEntity::getFileId, Function.identity()));

            // 查下知识库
            Set<Long> baseIdSet = entityFileList.stream().map(UserKnowledgeFileEntity::getBaseId).collect(Collectors.toSet());
            Map<Long, UserKnowledgeEntity> baseMap = userKnowledgeRepository.selectByIds(new ArrayList<>(baseIdSet))
                    .stream().collect(Collectors.toMap(UserKnowledgeEntity::getId, Function.identity()));

            // 将所有未送审的分片内容聚合放一起同步送审,送审不通过将所有分片id过滤
            contentAuditAndFilter(personalFileMap, dto.getDialogueId(), dto.getUserId());

            // 数据处理，为了保持有序性使用personalFileMap
            for (String fileId : personalFileMap.keySet()) {
                try {
                    UserKnowledgeFileEntity fileEntity = fileMap.get(fileId);
                    if (ObjectUtil.isEmpty(fileEntity)) {
                        continue;
                    }

                    UserKnowledgeEntity knowledge = baseMap.get(fileEntity.getBaseId());
                    // 分块对应的知识库文件送审不通过并且是公开知识库，需要过滤掉文件和分块
                    if (FileAuditStatusEnum.isNotPass(fileEntity.getAuditStatus()) && knowledge != null
                            && KnowledgeOpenLevelEnum.isOpen(knowledge.getOpenLevel())) {
                        log.info("【知识库对话】【RAG重要节点日志】【召回的文件过滤】分块对应的知识库文件送审不通过并且是公开知识库，过滤掉文件和分块 fileEntity:{}", JsonUtil.toJson(fileEntity));
                        continue;
                    }

                    // 分片处理
                    List<String> sortList = personalFileMap.get(fileId).stream()
                            .sorted(Comparator.comparingInt(vo -> vo.getIndex() == null ? 0 : vo.getIndex()))
                            .map(RecallResultVO::getText)
                            .collect(Collectors.toList());
                    List<String> shardingList = KnowledgeFlowInfoDataHandle.segmentHandle(sortList);
                    if (CollectionUtils.isEmpty(shardingList)) {
                        log.info("【知识库对话】【RAG重要节点日志】【召回的文件过滤】过滤后分块为空，整个文件不送给大模型 fileId:{}", fileId);
                        continue;
                    }

                    list.add(new KnowledgeSearchInfo(fileEntity, shardingList, baseMap.get(fileEntity.getBaseId())));
                    dto.getKnowledgeList().add(new KnowledgeFlowInfoDataHandle(recallGroupMap.get(fileId)));
                } catch (Exception e) {
                    log.error("【知识库对话】【RAG重要节点日志】【召回的文件过滤】数据处理异常 fileId:{}", fileId, e);
                }
            }

            dto.setPersonalFileList(list);
        }


        // 公共知识库文件处理
        if (ObjectUtil.isNotEmpty(commonFileMap)) {
            List<KnowledgeSearchInfo> list = new ArrayList<>();
            // 查下文件列表
            List<String> fileIds = new ArrayList<>(commonFileMap.keySet());
            List<KnowledgeFileEntity> entityFileList = knowledgeFileRepository.selectByFileIds(null, fileIds);
            Map<String, KnowledgeFileEntity> fileMap = entityFileList.stream().collect(Collectors.toMap(KnowledgeFileEntity::getFileId, Function.identity()));

            // 查下知识库
            Set<String> baseIdSet = entityFileList.stream().map(KnowledgeFileEntity::getBaseId).collect(Collectors.toSet());
            Map<String, AlgorithmKnowledgeConfigEntity> baseMap = knowledgeConfigRepository.selectByIds(new ArrayList<>(baseIdSet))
                    .stream().collect(Collectors.toMap(AlgorithmKnowledgeConfigEntity::getId, Function.identity()));

            // 数据处理，为了保持有序性使用commonFileMap
            for (String fileId : commonFileMap.keySet()) {
                KnowledgeFileEntity fileEntity = fileMap.get(fileId);
                if (ObjectUtil.isEmpty(fileEntity)) {
                    continue;
                }

                // 分片处理
                List<String> sortList = commonFileMap.get(fileId).stream()
                        .sorted(Comparator.comparingInt(vo -> vo.getIndex() == null ? 0 : vo.getIndex()))
                        .map(RecallResultVO::getText)
                        .collect(Collectors.toList());
                List<String> shardingList = KnowledgeFlowInfoDataHandle.segmentHandle(sortList);
                list.add(new KnowledgeSearchInfo(fileEntity, shardingList, baseMap.get(fileEntity.getBaseId())));
                dto.getKnowledgeList().add(new KnowledgeFlowInfoDataHandle(recallGroupMap.get(fileId)));
            }

            dto.setCommonFileList(list);
        }

        LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】【相关知识】【过滤后】个人知识库的相关知识第{}个分块：\n{}", JsonUtil.toJson(dto.getPersonalFileList()));
        LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】【相关知识】【过滤后】公共知识库的相关知识第{}个分块：\n{}", JsonUtil.toJson(dto.getCommonFileList()));
    }

    /**
     * 将所有未送审的分片内容聚合放一起同步送审,送审不通过将所有分片id过滤
     *
     * @param personalFileMap 文件id和分片的集合
     * @param dialogueId      对话id
     * @param userId          用户id
     */
    private void contentAuditAndFilter(Map<String, List<RecallResultVO>> personalFileMap, Long dialogueId, String userId) {
        List<String> auditIdList = new ArrayList<>();
        List<String> auditContentList = new ArrayList<>();
        for (String fileId : personalFileMap.keySet()) {
            List<RecallResultVO> recallVoList = personalFileMap.get(fileId);
            if (CollectionUtils.isEmpty(recallVoList)) {
                continue;
            }

            // 收集待送审的内容与ID
            List<String> untreatedList = new ArrayList<>();
            for (RecallResultVO vo : recallVoList) {
                if (FileAuditStatusEnum.isUntreated(vo.getAuditStatus())) {
                    auditContentList.add(vo.getText());
                    auditIdList.add(vo.getId());
                }
            }
            if (CollectionUtils.isEmpty(untreatedList)) {
                continue;
            }

            // 送审的内容列表
            auditContentList.addAll(KnowledgeFlowInfoDataHandle.segmentHandle(untreatedList));
        }

        if (CollectionUtils.isEmpty(auditContentList) || CollectionUtils.isEmpty(auditIdList)) {
            log.info("【知识库对话】【RAG重要节点日志】【召回的文件送审过滤】无召回切片内容或切片id需要送审 personalFileMap size:{}", personalFileMap.size());
            return;
        }

        try {
            // 分片送审
            String auditContent = String.join(" ", auditContentList);
            CheckResultVO checkResultVO = checkSystemExternalService.checkText(dialogueId, userId, auditContent);
            log.info("【知识库对话】【RAG重要节点日志】【召回的文件送审过滤】将未送审的切片内容合并进行送审 auditIdList:{} ｜ 送审结果:{}",
                    JsonUtil.toJson(auditIdList), JsonUtil.toJson(checkResultVO));
            if (Boolean.TRUE.equals(checkResultVO.getSuccess())) {
                return;
            }

            log.info("【知识库对话】【RAG重要节点日志】【召回的文件送审过滤】切片内容审核未通过，将对应切片过滤 auditIdList:{}",
                    JsonUtil.toJson(auditIdList));
            // 送审不通过将所有送审分片id过滤
            personalFileMap.forEach((key, value) -> {
                value.removeIf(recallResultVO -> auditIdList.contains(recallResultVO.getId()));
            });

        } catch (Exception e) {
            log.error("【知识库对话】【RAG重要节点日志】【召回的文件送审过滤】送审过程中发生异常，dialogueId:{} | auditIdList:{} | e:",
                    dialogueId, JsonUtil.toJson(auditIdList), e);
            personalFileMap.forEach((key, value) -> {
                value.removeIf(recallResultVO -> auditIdList.contains(recallResultVO.getId()));
            });
        }
    }

    /**
     * 知识库名词库处理
     *
     * @param handleDTO 对话接口责任链处理数据传输对象
     * @param dto       搜索对象
     */
    private void handleNounLibrary(ChatAddHandleDTO handleDTO, KnowledgeSearchDTO dto) {
        if (!CollUtil.isEmpty(dto.getPersonalFileList())) {
            return;
        }

        List<String> nounLibraryList = new ArrayList<>();
        try {
            DialogueIntentionVO intentionVO = getIntentionVO(handleDTO, dto);
            log.info("【知识库对话】【RAG重要节点日志】【数据搜索】【名词库处理】开始intentionVO:{}", JsonUtil.toJson(intentionVO));
            //标签（取意图MetaDataList）
            List<String> entityKeyList = new ArrayList<>();
            if (Objects.nonNull(intentionVO) && ObjectUtil.isNotEmpty(intentionVO.getIntentionInfoList())) {
                for (DialogueIntentionVO.IntentionInfo info : intentionVO.getIntentionInfoList()) {
                    if (ObjectUtil.isNotEmpty(info.getEntityList()) && ObjectUtil.isNotEmpty(info.getEntityList().get(0).getMetaDataList())) {
                        info.getEntityList().get(0).getMetaDataList().forEach(item -> entityKeyList.addAll(item.getValue()));
                    }
                }
            }

            // 名词库匹配
            nounLibraryList = nounLibraryService.getNounLibraryList(entityKeyList);
            if (CollUtil.isEmpty(nounLibraryList)) {
                log.info("【知识库对话】【RAG重要节点日志】【数据搜索】【名词库处理】个人知识库文件列表为空，查询名词库结果为空 entityKeyList:{}", JsonUtils.toJson(entityKeyList));
                return;
            }

            handleDTO.getKnowledgeFlowInfo().setNounLibraryList(nounLibraryList);
        } catch (Exception e) {
            log.error("【知识库对话】【RAG重要节点日志】【数据搜索】【名词库处理】个人知识库文件列表为空，查询名词库结果异常", e);
        } finally {
            log.info("【知识库对话】【RAG重要节点日志】【数据搜索】【名词库处理】个人知识库文件列表为空，查询名词库结果:{}", JsonUtil.toJson(nounLibraryList));
        }
    }

    public DialogueIntentionVO getIntentionVO(ChatAddHandleDTO handleDTO, KnowledgeSearchDTO searchDto) {
        DialogueIntentionVO intentionVO = searchDto.getIntentionVO();
        DialogueIntentionVO.IntentionInfo intentionInfo = Objects.isNull(intentionVO) || CollUtil.isEmpty(intentionVO.getIntentionInfoList()) ? null : intentionVO.getIntentionInfoList().get(0);
        if (Objects.isNull(intentionInfo) || CollUtil.isEmpty(intentionInfo.getEntityList())) {
            log.info("【知识库对话】【RAG重要节点日志】【数据搜索】【名词库处理】intentionVO为空,手动调用一次意图识别接口");
            ChatAddReqDTO dto = handleDTO.getReqDTO();

            DialogueIntentionEntity.DialogueInfo currentDialogueInfo = new DialogueIntentionEntity.DialogueInfo();
            currentDialogueInfo.setTimestamp(dto.getDialogueInput().getInputTime());
            String prompt = dto.getDialogueInput().getPrompt();
            if (StrUtil.isNotEmpty(prompt)) {
                currentDialogueInfo.setDialogue(prompt.concat(dto.getDialogueInput().getDialogue()));
            } else {
                currentDialogueInfo.setDialogue(dto.getDialogueInput().getDialogue());
            }

            DialogueIntentionEntity entity = new DialogueIntentionEntity();
            String requestId = String.valueOf(uidGenerator.getUID());
            entity.setRequestId(requestId);
            entity.setSessionId(requestId);
            entity.setDialogueId(requestId);
            entity.setUserId(dto.getUserId());
            entity.setCurrentDialogue(currentDialogueInfo);

            return dialogueIntentionExternalService.dialogueIntentionFuc(handleDTO.getReqDTO().getSourceChannel(), entity);
        }

        return intentionVO;
    }
}
