package com.zyhl.yun.api.outer.application.dto;

import com.zyhl.hcy.plugin.logger.annotation.LogIgnoreElement;
import lombok.Data;

import javax.annotation.PostConstruct;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class CardInfoDTO {
    /**
     * 用户id。没传userId时，从token中获取。
     */
    private String userId;

    /**
     * 文件id
     */
    private String fileId;

    /**
     * 传送类型
     */
    @NotNull(message = "传送类型不可为空")
    private Integer sendType;

    /**
     * 1——url传送
     * 2——base64传送
     */
    private String fileUrl;

    /**
     * 图片的base64字符串
     */
    @LogIgnoreElement
    private String base64;

    /**
     * 图片是否已裁剪
     * 0-——未裁剪
     * 1——已裁剪
     * 如果传入该字段，算法无需先图片裁剪，响应时间可以缩短
     */
    private Integer cropState;

    /**
     * 卡证二级类别
     * 0——其他证件
     * 1——身份证
     * 2——社保卡
     * 3——驾驶证
     * 4——行驶证
     * 5——户口本
     * 6——护照
     * 7——港澳通行证
     * 8——居住证
     * 9——学生证
     * 10——银行卡
     * 11——房产证
     * 如果传入该字段，算法无需先进行分类识别，响应时间将大大缩短
     */
    private Integer cardType;

    @PostConstruct
    private void init() {
        if (cropState == null) {
            // 将null转换为默认值0
            cropState = 0;
        }
    }
}
