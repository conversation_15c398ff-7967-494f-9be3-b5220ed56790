package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueIntentionDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 文本工具意图（执行意图）-其他子意图兜底
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TextToolOthersHandlerImpl extends AbstractChatAddV2Handler {

	/**
	 * 当前执行顺序
	 */
	private ExecuteSort thisExecuteSort = ExecuteSort.TEXT_TOOL_OTHERS;

	@Resource
	private TextModelDocSseHandlerImpl textModelDocSseHandlerImpl;

	@Override
	public void afterPropertiesSet() throws Exception {
		// 支持的业务初始化
		List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        this.setBusinessTypes(thisBusinessTypes);
	}

	@Override
	public int order() {
		return thisExecuteSort.getSort();
	}

	@Override
	public boolean execute(ChatAddHandleDTO handleDTO) {

		if (StringUtils.isEmpty(handleDTO.getReqDTO().getDialogueInput().getDialogue())) {
			// 对话内容空，不执行
			return false;
		}

		DialogueIntentionDTO command = handleDTO.getReqDTO().getDialogueInput().getCommand();
		if (null != command && DialogueIntentionEnum.isTextToolIntention(command.getCommand())) {
			// 判断入参是文本工具意图
			return true;
		}

		IntentionInfo mainIntention = null;
		if (null != handleDTO.getIntentionVO()
				&& CollUtil.isNotEmpty(handleDTO.getIntentionVO().getIntentionInfoList())) {
			mainIntention = DialogueIntentionVO.getMainIntention(handleDTO.getIntentionVO());
			if (null != mainIntention && DialogueIntentionEnum.isTextToolIntention(mainIntention.getIntention())
					&& CollUtil.isNotEmpty(mainIntention.getArgumentMap())) {
				// 判断意图识别是文本工具意图（主意图+参数不为空才是执行意图
				return true;
			}
		}
		if (null != mainIntention && DialogueIntentionEnum.isTextToolIntention(mainIntention.getIntention())) {
			// 意图识别为不执行的文本工具意图，则设置为文本意图
			handleDTO.setTextGenerateTextIntention();
		}
		return false;
	}

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		log.info("进入{}", thisExecuteSort.getDesc());

		if (handleDTO.isReqResourceDocSse()) {
			// 存在文档，调用文档大模型流式对话
			textModelDocSseHandlerImpl.run(handleDTO);
			return false;
		}

		// 其他情况，暂无具体流程，兜底暂时设置为文本意图
		handleDTO.setTextGenerateTextIntention();

		// 继续下一个流程
		return true;
	}

}
