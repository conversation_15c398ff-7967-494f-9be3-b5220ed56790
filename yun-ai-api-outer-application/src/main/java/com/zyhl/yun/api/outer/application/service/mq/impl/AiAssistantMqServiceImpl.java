package com.zyhl.yun.api.outer.application.service.mq.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.chinamobile.tuxedo.sdk.api.Message;
import com.chinamobile.tuxedo.sdk.api.Producer;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.enums.BusinessOwnerTypeEnum;
import com.zyhl.hcy.yun.ai.common.base.rocketmq.enums.MessageVersionEnum;
import com.zyhl.hcy.yun.ai.common.base.rocketmq.message.MessageDTO;
import com.zyhl.hcy.yun.ai.common.base.utils.MessageUtil;
import com.zyhl.hcy.yun.ai.common.base.utils.StopWatchUtil;
import com.zyhl.yun.api.outer.application.config.AiAssistantCompletedMqConfig;
import com.zyhl.yun.api.outer.application.mq.dto.AiAssistantCompletedMqDTO;
import com.zyhl.yun.api.outer.application.service.mq.AiAssistantMqService;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 助手mq发送服务
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AiAssistantMqServiceImpl implements AiAssistantMqService {

	@Resource
	private UidGenerator uidGenerator;

	@Resource
	private AiAssistantCompletedMqConfig aiAssistantCompletedConfig;

	@Resource(name = "aiAssistantCompletedProducer")
	private Producer aiAssistantCompletedProducer;

	@Override
	public boolean sendDialogueCompletedMq(AlgorithmChatContentEntity contentEntity) {
		StopWatch stopWatch = StopWatchUtil.createStarted();
		boolean success = false;
		String jsonStr = null;
		String sessionId = String.valueOf(contentEntity.getSessionId());
		String dialogueId = String.valueOf(contentEntity.getId());
		if (!ChatStatusEnum.isChatSuccess(contentEntity.getChatStatus())) {
			log.info("sessionId:{}, dialogueId:{}, sendAiAssistantCompletedMq chatStatus不成功，不发送mq。", sessionId,
					dialogueId);
			return true;
		}
		try {
			// 对话完成时间设置为输出送审时间
			Date dialogueTime = contentEntity.getOutAuditTime();
			if (null == dialogueTime) {
				// 如果时间空，设置当前时间
				dialogueTime = DateUtil.date();
			}
			String dialogueTimeUtc = DateUtil.format(dialogueTime, DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
			// 组装MQ数据
			AiAssistantCompletedMqDTO dto = AiAssistantCompletedMqDTO.builder().sessionId(String.valueOf(sessionId))
					.dialogueId(String.valueOf(dialogueId)).dialogueTime(dialogueTimeUtc)
					.commands(contentEntity.getToolsCommand()).channelId(contentEntity.getSourceChannel())
					.applicationType(contentEntity.getApplicationType()).build();
			// 业务类型编码=PERSONAL，业务归属用户id=用户id
			MessageDTO<AiAssistantCompletedMqDTO> baseMqDTO = new MessageDTO<>(
					(AiAssistantCompletedMqConfig.AI_ASSISTANT_DIALOGUE_COMPLETED_MESSAGE_ID_PREFIX
							+ uidGenerator.getUID()),
					AiAssistantCompletedMqConfig.AI_ASSISTANT_DIALOGUE_COMPLETED_EVENT_TYPE,
					BusinessOwnerTypeEnum.PERSONAL.getType(), contentEntity.getUserId(), dto,
					MessageVersionEnum.AI_ASSISTANT_DIALOGUE_COMPLATED_VERSION_V1.getVersion());
			jsonStr = JSON.toJSONString(baseMqDTO);
			Message msg = MessageUtil.createMessage();
			msg.setBody(jsonStr.getBytes());
			msg.setTopic(aiAssistantCompletedConfig.getTopic());
			msg.setTag(aiAssistantCompletedConfig.getTag());
			aiAssistantCompletedProducer.send(msg);
			log.info("sessionId:{}, dialogueId:{}, sendAiAssistantCompletedMq completed msg:{}", sessionId, dialogueId,
					JSON.toJSONString(msg));
			success = true;
		} catch (Exception e) {
			log.error("sessionId:{}, dialogueId:{}, sendAiAssistantCompletedMq 助手对话完成任务消息发送失败:{}, error:", sessionId,
					dialogueId, jsonStr, e);
		} finally {
			log.info(
					"sessionId:{}, dialogueId:{}, sendAiAssistantCompletedMq 助手对话完成任务消息发送:{}, success:{} time taken:{}",
					sessionId, dialogueId, jsonStr, success, StopWatchUtil.logTotalTimeMs(stopWatch));
			StopWatchUtil.clearDuration();
		}
		return success;
	}

}
