package com.zyhl.yun.api.outer.application.service.task.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.CaptureEntityConvertor;
import com.zyhl.yun.api.outer.application.dto.CaptureCaptionDTO;
import com.zyhl.yun.api.outer.application.service.task.ImageCaptionService;
import com.zyhl.yun.api.outer.application.vo.ImageCaptionVO;
import com.zyhl.yun.api.outer.domain.entity.ImageCaptionEntity;
import com.zyhl.yun.api.outer.domain.entity.ImageCaptureCaptionEntity;
import com.zyhl.yun.api.outer.domain.req.FileGetDownloadUrlReqEntity;
import com.zyhl.yun.api.outer.domain.resp.FileGetDownloadUrlRespEntity;
import com.zyhl.yun.api.outer.domainservice.CaptionService;
import com.zyhl.yun.api.outer.domainservice.FileExternalService;
import com.zyhl.yun.api.outer.domainservice.NfsFileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;

import static com.zyhl.hcy.yun.ai.common.base.enums.YunAiCommonResultCode.DOWNSTREAM_SERVICES_EXCEPTION;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ImageCaptionServiceImpl implements ImageCaptionService {


    @Resource
    private CaptionService captionService;

    @Resource
    private CaptureEntityConvertor convertor;

    @Resource
    private FileExternalService fileExternalService;

    @Resource
    private NfsFileService nfsFileService;

    @Value("${nfs.path}")
    private String filePath;
    @Value("${nfs.aiPath}")
    private String aiPath;

    /**
     * 图配文
     * @param dto
     * @return
     */
    @Override
    public ImageCaptionVO getImageCaption(CaptureCaptionDTO dto) {
        //根据文件id调用个人云接口下载文件
        FileGetDownloadUrlReqEntity reqEntity = new FileGetDownloadUrlReqEntity();
        reqEntity.setUserId(dto.getUserId());
        reqEntity.setFileId(dto.getFileId());
        FileGetDownloadUrlRespEntity downloadUrlResp = fileExternalService.fileGetDownloadUrl(reqEntity);
        if (ObjectUtil.isEmpty(downloadUrlResp)|| StringUtils.isEmpty(downloadUrlResp.getUrl())){
            log.info("个人云SaaS返回文件信息为空，用户ID：{},文件id{}",dto.getUserId(),dto.getFileId());
            throw new YunAiBusinessException(DOWNSTREAM_SERVICES_EXCEPTION);
        }
        //上传至共享存储
        File file = nfsFileService.getFile(downloadUrlResp.getUrl(),dto.getRequestId());
        //共享存储 路径映射
        if (ObjectUtil.isEmpty(file)|| StringUtils.isEmpty(file.getAbsolutePath())){
            log.info("上传至共享存储异常，用户ID：{},文件id{}",dto.getUserId(),dto.getFileId());
            throw new YunAiBusinessException(DOWNSTREAM_SERVICES_EXCEPTION);
        }
        String replace = file.getAbsolutePath().replace(filePath, aiPath);
        log.info("共享存储路径:{},fileAbsolutePath路径:{}",replace,file.getAbsolutePath());
        //调用图配文接口
        ImageCaptureCaptionEntity caption = new ImageCaptureCaptionEntity();
        caption.setSendType(4);
        caption.setRequestId(dto.getRequestId());
        caption.setLocalPath(replace);
        ImageCaptionEntity entity = captionService.imageCaption(caption);
        if (ObjectUtil.isEmpty(entity)){
            log.info("调用图配文接口返回空,requestId{}",dto.getRequestId());
            return null;
        }
        return convertor.toInfoVO(entity);
    }
}
