package com.zyhl.yun.api.outer.application.dto;


import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.enums.BaseResultCodeEnum;
import com.zyhl.yun.api.outer.application.util.RegexUtils;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.constants.RegConst;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * 停止对话接口参数
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class AlgorithmChatStopDTO extends BaseDTO {
    /**
     * 渠道来源
     */
    private String sourceChannel;
    /**
     * 会话Id
     */
    private String sessionId;
    /**
     * 当前对话Id
     */
    private String dialogueId;

    /**
     * 参数校验
     *
     * @param properties 渠道配置
     * @return 错误码
     */
    public AbstractResultCode check(SourceChannelsProperties properties) {
        if (CharSequenceUtil.isEmpty(sourceChannel)) {
            log.info("渠道来源为空");
            return BaseResultCodeEnum.ERROR_PARAMS;
        } else if (!properties.isExist(sourceChannel)) {
            log.info("渠道不存在：{}", sourceChannel);
            return BaseResultCodeEnum.ERROR_PARAMS;
        } else if (CharSequenceUtil.isEmpty(sessionId)) {
            log.info("会话id为空");
            return BaseResultCodeEnum.ERROR_PARAMS;
        } else if (CharSequenceUtil.isEmpty(dialogueId)) {
            log.info("对话id为空");
            return BaseResultCodeEnum.ERROR_PARAMS;
        } else if (!sessionId.matches(RegConst.REG_DATA_STR)) {
            log.info("会话id不是纯数字：{}", sessionId);
            return BaseResultCodeEnum.ERROR_PARAMS;
        } else if (!dialogueId.matches(RegConst.REG_DATA_STR)) {
            log.info("对话id不是纯数字：{}", dialogueId);
            return BaseResultCodeEnum.ERROR_PARAMS;
        }

        return checkUserId();
    }

}
