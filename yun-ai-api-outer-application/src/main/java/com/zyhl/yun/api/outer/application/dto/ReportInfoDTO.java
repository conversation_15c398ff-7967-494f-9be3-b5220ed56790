package com.zyhl.yun.api.outer.application.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 */
@Data
public class ReportInfoDTO {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 举报业务
     */
    @NotNull(message = "举报业务不能为空")
    private Integer businessType;

    /**
     * 举报原因
     */
    @NotNull(message = "举报原因不能为空")
    private Integer[] reasons;

    /**
     *  联系方式
     */
    @NotNull(message = "联系方式不能为空")
    @Size(min = 1,max = 100, message = "联系方式长度不能超过100位")
    private String contact_info;

    /**
     * 举报原因描述
     */
    @Size(min = 0,max = 200, message = "原因长度不能超过200位")
    private String describe;

    /**
     * 举报对象
     */
    private String[] objectKeys;


}
