package com.zyhl.yun.api.outer.application.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/3 15:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class SecondStreamChatAddV2DTO extends BaseDTO implements Serializable {

    /**
     * 对话id，必填
     */
    private String dialogueId;

}
