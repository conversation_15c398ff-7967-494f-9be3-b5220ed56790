package com.zyhl.yun.api.outer.application.chatv2.service;

import com.zyhl.yun.api.outer.application.chatv2.dto.SecondStreamChatAddV2InnerDTO;

/**
 * 二次对话-智能体相关业务-接口实现类
 *
 * <AUTHOR>
 * @date 2025/6/4 13:40
 */
public interface SecondChatAddV2IntelligentService {

    /**
     * 执行会议发邮件结果
     *
     * @param innerDTO
     */
    public void runMeetingMailResult(SecondStreamChatAddV2InnerDTO innerDTO);

    /**
     * 执行aippt结果
     *
     * @param innerDTO
     */
    public void runAiPptResult(SecondStreamChatAddV2InnerDTO innerDTO);

    /**
     * 执行会议发邮件结果和aippt结果
     *
     * @param innerDTO the inner dto
     * <AUTHOR>
     * @date 2025/6/11 15:56
     */
    void runSendMailAndAiPPtResult(SecondStreamChatAddV2InnerDTO innerDTO);
}
