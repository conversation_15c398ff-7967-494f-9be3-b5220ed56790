package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueAttachmentDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatDialogueSearchService;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueFlowResultVO;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.config.note.NoteSearchProperties;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddFlowStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatMiddleCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileProcessStatusEnum;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 笔记助手搜索相关
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SearchForNoteHandlerImpl extends AbstractChatAddV2Handler {

    /**
     * 当前执行顺序
     */
    private ExecuteSort thisExecuteSort = ExecuteSort.SEARCH_FOR_NOTE;

    @Resource
    private ChatDialogueSearchService chatDialogueSearchService;
    @Resource
    private UserKnowledgeFileRepository userKnowledgeFileRepository;
    @Resource
    private NoteSearchProperties noteSearchProperties;

    @Override
    public void afterPropertiesSet() throws Exception {
        //支持的业务初始化
        List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.NOTE);
        this.setBusinessTypes(thisBusinessTypes);
    }

    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public boolean execute(ChatAddHandleDTO handleDTO) {
        // 非笔记渠道，不做后续任何处理
        return DialogueIntentionEnum.SEARCH_NOTE_KNOWLEDGE_BASE.getCode().equals(handleDTO.getIntentionCode());
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        log.info("进入{}", thisExecuteSort.getDesc());
        DialogueInputInfoDTO inputInfoDTO = handleDTO.getInputInfoDTO();
        handleDTO.getRespVO().getFlowResult().setResultType(FlowResultTypeEnum.SEARCH.getType());

        if (ObjectUtil.isEmpty(inputInfoDTO)) {
            log.error("输入信息为空，输入信息：{}", inputInfoDTO);
            handleDTO.getRespVO().setSessionId("");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        DialogueAttachmentDTO attachment = inputInfoDTO.getAttachment();
        // 笔记类型
        if (ObjectUtil.isEmpty(attachment.getNoteList())) {
            log.error("笔记资源为空，资源：{}", attachment.getNoteList());
            handleDTO.getRespVO().setSessionId("");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        String noteId = attachment.getNoteList().get(0).getNoteId();
        if (CharSequenceUtil.isEmpty(noteId)) {
            log.error("笔记资源id为空，资源id：{}", noteId);
            handleDTO.getRespVO().setSessionId("");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        boolean checkResult = checkDocumentProcessStatus(noteId, noteSearchProperties.getMaxRetryCount(), noteSearchProperties.getRetryIntervalMillis(), handleDTO);
        if(!checkResult) {
            DialogueFlowResultVO flowResultVO = new DialogueFlowResultVO(ChatMiddleCodeEnum.SEARCH_NULL, null);
            // 结束标识
            handleDTO.getRespVO().setSessionId("");
            flowResultVO.setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
            flowResultVO.setResultType(FlowResultTypeEnum.SEARCH.getType());
            handleDTO.getRespVO().setFlowResult(flowResultVO).setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
            handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(handleDTO.getRespVO()));
            return false;
        }

        chatDialogueSearchService.searchForNoteHandler(handleDTO);

        // 结束标识
        handleDTO.getRespVO().getFlowResult().setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
        // 返回搜索结果
        handleDTO.getRespVO().getFlowResult().setResultType(FlowResultTypeEnum.SEARCH.getType());
        handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(handleDTO.getRespVO()));

        return false;
    }

    /**
     * 检查文档解析状态（轮询机制）
     * @param noteId 笔记ID
     * @param maxRetryCount 最大轮询次数（可配置）
     * @param retryIntervalMillis 轮询间隔时间（毫秒，可配置）
     */
    private boolean checkDocumentProcessStatus(String noteId, int maxRetryCount, long retryIntervalMillis, ChatAddHandleDTO handleDTO) {
        String userId = handleDTO.getReqDTO().getUserId();
        int retryCount = 0;
        while (retryCount < maxRetryCount) {
            UserKnowledgeFileEntity entity = userKnowledgeFileRepository.selectByFileId(
                    userId,
                    noteId
            );

            // 1. 检查资源是否存在
            if (ObjectUtil.isEmpty(entity)) {
                log.warn("笔记资源不存在，资源id：{}", noteId);
                return false;
            }

            // 2. 检查是否已完成解析
            if (FileProcessStatusEnum.isSuccess(entity.getAiStatus())) {
                log.info("文档解析完成，笔记ID：{}", noteId);
                // 完成则退出方法，继续下一步
                return true; 
            }

            // 3. 未完成时等待并重试
            retryCount++;
            if (retryCount < maxRetryCount) {
                log.info("文档解析未完成，开始第 {} 次重试（共 {} 次），笔记ID：{}",
                        retryCount, maxRetryCount, noteId);
                try {
                	// 等待间隔
                    Thread.sleep(retryIntervalMillis); 
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("轮询检查被中断，笔记ID：{}", noteId, e);
                }
            }
        }

        // 4. 超过最大重试次数仍未完成
        log.warn("文档解析未在预期时间内完成，笔记ID：{}，最大重试次数：{}", noteId, maxRetryCount);
        return false;
    }
}
