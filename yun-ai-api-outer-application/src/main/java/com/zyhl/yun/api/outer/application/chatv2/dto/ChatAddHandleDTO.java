package com.zyhl.yun.api.outer.application.chatv2.dto;

import java.util.List;
import java.util.concurrent.Future;

import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.application.chatv2.pojo.KnowledgeFlowInfo;
import com.zyhl.yun.api.outer.application.chatv2.pojo.OpenApiLingxiParamInfo;
import com.zyhl.yun.api.outer.application.chatv2.pojo.TaskPromptOfChatInfo;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatTidbSaveDTO;
import com.zyhl.yun.api.outer.application.dto.ContinueTextSseDTO;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.config.textmodel.VlModelConfig;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueIntentionOutput;
import com.zyhl.yun.api.outer.domain.vo.AllNetworkSearchRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchImageResult;
import com.zyhl.yun.api.outer.domainservice.SseEmitterOperate;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.AiTextResultVersionEnum;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;
import com.zyhl.yun.api.outer.vo.InterventionVO;
import com.zyhl.yun.api.outer.vo.MultiSearchVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 对话接口责任链处理数据传输对象，
 * <p>
 * 包含： 1、请求参数，数据不可改 2、生成响应参数的方法 3、中间状态数据
 *
 * <AUTHOR>
 */
@Slf4j
@Data
public class ChatAddHandleDTO {

    /**
     * 流式结果的索引
     */
    private Integer flowResultIndex = 0;

    /**
     * 请求参数，里面的数据不可改
     */
    private ChatAddReqDTO reqDTO;
    /**
     * 请求参数，里面的数据不可改
     */
    private DialogueInputInfoDTO inputInfoDTO;


    /**
     * 流式对象操作类
     */
    private SseEmitterOperate sseEmitterOperate;

    /**
     * 会话id，默认为入参的会话id，如果没有需要生成一个
     */
    private Long sessionId;
    /**
     * 对话id，需要新生成一个
     */
    private Long dialogueId;
    /**
     * 智能体关联id
     */
    private String typeRelationId;
    /**
     * 助手类型枚举
     */
    private AssistantEnum assistantEnum;
    /**
     * 渠道业务类型
     */
    private String businessType;

    /**
     * 意图编码
     */
    private String intentionCode;

    /**
     * 二级子意图编码
     */
    private String subIntentionCode;

    /**
     * 意图结果对象
     */
    private DialogueIntentionVO intentionVO;

    /**
     * 响应结果
     */
    private ChatAddRespVO respVO = new ChatAddRespVO();

    /**
     * 并发线程：多意图、推荐提问语句
     */
    private List<Future<Object>> futures;

    /**
     * 干预库对象
     */
    private InterventionVO interventionVO;

    /**
     * 资源内容（邮件，笔记，图片ocr等等内容，用于大模型对话）
     */
    private String resourceContent;

    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 资源后缀
     */
    private String resourceExt;

    /**
     * hbase的resp数据对象
     */
    private AiTextResultRespParameters hbaseResp;

    /**
     * tidb需要保存的数据对象
     */
    private AlgorithmChatTidbSaveDTO algorithmChatTidbSaveDTO;

    /**
     * 知识库流程信息
     */
    private KnowledgeFlowInfo knowledgeFlowInfo = new KnowledgeFlowInfo();

    /**
     * 保存会话（true-新增；false-更新）
     */
    private Boolean saveMessage;


    /**
     * 意图识别的异步处理future
     */
    private Future<DialogueIntentionVO> dialogueIntentionFuture;

    /**
     * 搜索返回词的异步处理future
     */
    private Future<String> searchReturnTermsFuture;

    /**
     * 全网搜推荐异步处理future
     */
    private Future<List<AllNetworkSearchRecommendVO>> allNetworkSearchRecommendFuture;

    /**
     * 搜索相关对象
     */
    private SearchParam searchParam;

    /**
     * 结果返回数量（当前给异步任务使用）
     */
    private Integer resultCount;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 当前任务模块
     */
    private String currentTaskModule;

    /**
     * 任务提示词和对象信息结果
     */
    private List<TaskPromptOfChatInfo> taskPromptOfChatInfos;

    /**
     * 视觉大模型-业务模型配置
     */
    private VlModelConfig.BusinessModelConfig vlmBusinessModelConfig;


    /**
     * 外部资源id
     */
    private String outResourceId;
    /**
     * 继续执行大模型回答DTO
     */
    private ContinueTextSseDTO continueTextSseDTO = new ContinueTextSseDTO();

    /**
     * 个人知识库有效的文件数量
     */
    private int personalKnowledgeFileCount;

    /**
     * 结束语
     */
    private String ending;

    /**
     * 是否使用orderTip获取提示词
     */
    private boolean useOrderTip;

    /**
     * 临时存语义搜图结果
     */
    private SearchImageResult tmpSearchImageResult;
    
    /**
     * 辅助字段-灵犀响应参数结果
     */
    private OpenApiLingxiParamInfo lingxiParamInfo;

    /**
     * AI全网搜结果
     */
    private List<MultiSearchVO> aiInternetSearchResultList;

    /**
     * 图片本地路径
     */
    private List<String> imageLocalPaths;

    public ChatAddHandleDTO(ChatAddReqDTO reqDTO, SseEmitterOperate sseEmitterOperate) {
        this.reqDTO = reqDTO;
        this.inputInfoDTO = reqDTO.getDialogueInput();
        this.sseEmitterOperate = sseEmitterOperate;
    }

    public void init() {
        if (!CharSequenceUtil.isBlank(reqDTO.getSessionId())) {
            setSessionId(Long.valueOf(reqDTO.getSessionId()));
        }

        SourceChannelsProperties.SourceChannel sourceChannel = SpringUtil.getBean(SourceChannelsProperties.class)
                .getByChannel(reqDTO.getSourceChannel());

        this.assistantEnum = AssistantEnum.getByCode(sourceChannel.getCode());
        this.businessType = sourceChannel.getBusinessType();

        // 意图相关赋值
        if (null != inputInfoDTO.getCommand()) {
            this.intentionCode = StrUtil.emptyIfNull(inputInfoDTO.getCommand().getCommand());
            this.subIntentionCode = StrUtil.emptyIfNull(inputInfoDTO.getCommand().getSubCommand());
        }
        this.intentionVO = DialogueIntentionVO.newMainIntention(this.intentionCode, this.subIntentionCode);
        this.respVO.setOutputCommandCode(intentionCode, subIntentionCode);

        this.genResourceName();
    }

    // --------------------- set --------------------- //

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
        this.respVO.setSessionId(String.valueOf(sessionId));
    }

    public void setDialogueId(Long dialogueId) {
        this.dialogueId = dialogueId;
        this.respVO.setDialogueId(String.valueOf(dialogueId));
    }

    public void setIntentionCode(String intentionCode) {
        this.intentionCode = intentionCode;
        this.respVO.setOutputCommandCode(intentionCode);
    }

    public void setInterventionVO(InterventionVO interventionVO) {
        this.interventionVO = interventionVO;
    }

    /**
     * 设置意图识别结果
     *
     * @param intentionVO 意图识别结果
     */
    public void setIntentionVO(DialogueIntentionVO intentionVO) {
        if (intentionVO == null) {
            return;
        }
        this.intentionVO = intentionVO;
        IntentionInfo mainIntentionInfo = intentionVO.getIntentionInfoList().get(0);
        this.intentionCode = mainIntentionInfo.getIntention();
        this.subIntentionCode = intentionVO.getIntentionInfoList().get(0).getSubIntention();
        this.respVO.setOutputCommandCode(intentionCode, this.subIntentionCode);
        this.respVO.getOutputCommand().setArgumentMap(mainIntentionInfo.getArgumentMap());
    }

    /**
     * 设置意图结果为文生文意图
     */
    public void setTextGenerateTextIntention() {
        log.info("手动设置意图结果为文生文意图");
        this.intentionCode = DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode();
        this.subIntentionCode = null;
        if (null == this.intentionVO.getIntentionInfoList() || this.intentionVO.getIntentionInfoList().size() == 0) {
            this.setIntentionVO(DialogueIntentionVO.newMainIntention(intentionCode));
            this.respVO.setOutputCommandCode(intentionCode);
        } else {
            this.intentionVO.getIntentionInfoList().get(0).setIntention(intentionCode);
            this.intentionVO.getIntentionInfoList().get(0).setSubIntention(null);
            this.respVO.setOutputCommandCode(intentionCode);
        }
    }

    /**
     * 生成资源名称
     */
    private void genResourceName() {
        if (!this.isReqResourceDocSse()) {
            return;
        }
        Integer resourceType = this.getReqDTO().getDialogueInput().getAttachment().getAttachmentTypeList().get(0);
        if (ResourceTypeEnum.isMail(resourceType) || ResourceTypeEnum.isAttachment(resourceType)) {
            this.setResourceName(this.getReqDTO().getDialogueInput().getAttachment().getMailList().get(0).getTitle());
        }
        if (ResourceTypeEnum.isNote(resourceType)) {
            this.setResourceName(this.getReqDTO().getDialogueInput().getAttachment().getNoteList().get(0).getTitle());
        }
        if (ResourceTypeEnum.isDocument(resourceType)) {
            this.setResourceName(this.getReqDTO().getDialogueInput().getAttachment().getFileList().get(0).getName());
        }
    }

    // --------------------- set --------------------- //

    /**
     * 获取hbase 存储对象
     *
     * @param taskId          任务id
     * @param resourceContent 附件内容
     * @param text2TextData   文生文结果
     * @return hbase对象
     */
	public AiTextResultEntity getAiTextResultEntity(String taskId, String resourceContent, String text2TextData) {
		// 响应结果
		AiTextResultRespParameters resp = AiTextResultRespParameters.builder()
				.version(AiTextResultVersionEnum.V2.getVersion()).build();
		resp.setResult(ResultCodeEnum.SUCCESS);
		resp.setData(text2TextData);
		resp.setParam(searchParam);
		resp.setLeadCopy(respVO.getLeadCopy());
        resp.setIntentionVO(intentionVO);
        resp.setAiInternetSearchResultList(aiInternetSearchResultList);
		intentionVO = this.getIntentionVO();
		if (null != intentionVO) {
			//预入库意图
			IntentionInfo mainIntention = DialogueIntentionVO.getMainIntention(intentionVO);
			resp.setOutputCommand(new DialogueIntentionOutput(mainIntention.getIntention(),
					mainIntention.getSubIntention(), mainIntention.getArgumentMap()));
		}

        // 保存hbase的对象
		AiTextResultEntity entity = new AiTextResultEntity();
		entity.setUserId(reqDTO.getUserId());
		entity.setDialogueId(dialogueId);
		entity.setTaskId(taskId);
		entity.setReqParameters(JSONUtil.toJsonStr(inputInfoDTO));
		entity.setAttachment(resourceContent);
		entity.setRespParameters(JsonUtil.toJson(resp));

        return entity;
    }

    /**
     * 是否文档对话类型（只包含文档）
     *
     * @return
     */
    public boolean isReqResourceOnlyDocumentSse() {
        DialogueAttachmentDTO attachment = this.getReqDTO().getDialogueInput().getAttachment();
        if (null != attachment && CollUtil.isNotEmpty(attachment.getAttachmentTypeList())) {
            for (Integer type : attachment.getAttachmentTypeList()) {
                if (ResourceTypeEnum.isDocument(type)) {
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * 是否文档对话类型（只包含笔记）
     *
     * @return
     */
    public boolean isReqResourceOnlyNoteSse() {
        DialogueAttachmentDTO attachment = this.getReqDTO().getDialogueInput().getAttachment();
        if (null != attachment && CollUtil.isNotEmpty(attachment.getAttachmentTypeList())) {
            for (Integer type : attachment.getAttachmentTypeList()) {
                if (ResourceTypeEnum.isNote(type)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 是否文档对话类型（包含：邮件/笔记/文档/附件）
     *
     * @return
     */
    public boolean isReqResourceDocSse() {
        DialogueAttachmentDTO attachment = this.getReqDTO().getDialogueInput().getAttachment();
        if (null != attachment && CollUtil.isNotEmpty(attachment.getAttachmentTypeList())) {
            for (Integer type : attachment.getAttachmentTypeList()) {
                if (ResourceTypeEnum.isMail(type) || ResourceTypeEnum.isNote(type) || ResourceTypeEnum.isDocument(type)
                        || ResourceTypeEnum.isAttachment(type)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 是否图片对话类型（包含：图片）
     *
     * @return
     */
    public boolean isReqResourceImageSse() {
        DialogueAttachmentDTO attachment = this.getReqDTO().getDialogueInput().getAttachment();
        if (null != attachment && CollUtil.isNotEmpty(attachment.getAttachmentTypeList())) {
            for (Integer type : attachment.getAttachmentTypeList()) {
                if (ResourceTypeEnum.isImage(type)) {
                    return true;
                }
            }
        }
        return false;
    }

}
