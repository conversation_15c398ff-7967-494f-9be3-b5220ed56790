package com.zyhl.yun.api.outer.application.service.knowledge.upload;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.OwnerDriveClient;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileCheckReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.ResourceTransferReqDTO;
import com.zyhl.yun.api.outer.application.service.mq.KnowledgeTransCategoryTaskMqService;
import com.zyhl.yun.api.outer.application.service.mq.KnowledgeTransTaskMqService;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeAddResultVO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeFileImportVO;
import com.zyhl.yun.api.outer.config.FileCheckConfig;
import com.zyhl.yun.api.outer.config.KnowledgePersonalProperties;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileTaskEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeUploadEntity;
import com.zyhl.yun.api.outer.domainservice.CheckSystemDomainService;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileCategoryEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileTaskStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeTaskTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeUploadStatusEnum;
import com.zyhl.yun.api.outer.external.UserDriveExternalService;
import com.zyhl.yun.api.outer.external.YunDiskExternalService;
import com.zyhl.yun.api.outer.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.InitializingBean;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 描述：文件转存策略处理
 *
 * <AUTHOR> lgy  2025/4/16
 */
@Slf4j
public abstract class AbstractResourceTransferHandle implements InitializingBean {

    /**
     * 存策略实现
     */
    private static final Map<Integer, AbstractResourceTransferHandle> map = new ConcurrentHashMap<>();

    public static AbstractResourceTransferHandle getByCode(Integer code) {
        return map.get(code);
    }

    protected void register(Integer code, AbstractResourceTransferHandle handle) {
        map.put(code, handle);
    }

    /**
     * -------------------------------以上是策略工厂-----------------------------------
     */

    @Resource
    protected UserKnowledgeFileRepository userKnowledgeFileRepository;
    @Resource
    protected AlgorithmUserKnowledgeUploadRepository userKnowledgeUploadRepository;
    @Resource
    protected UserKnowledgeFileTaskRepository userKnowledgeFileTaskRepository;
    @Resource
    protected KnowledgePersonalProperties knowledgePersonalProperties;
    @Resource
    protected UserDriveExternalService userDriveExternalService;
    @Resource
    protected YunDiskExternalService yunDiskExternalService;
    @Resource
    protected OwnerDriveClient ownerClient;
    @Resource
    protected KnowledgeTransTaskMqService knowledgeTransTaskMQService;
    @Resource
    protected KnowledgeTransCategoryTaskMqService knowledgeTransCategoryTaskMqService;
    @Resource
    protected RedisOperateRepository redisOperateRepository;
    @Resource
    protected UserKnowledgeRepository userKnowledgeRepository;

    @Resource
    protected UidGenerator uidGenerator;
    /**
     * 文件过大
     */
    private static final String ERROR_TOO_LARGE = "文件上传过大";
    /**
     * 文档格式错误
     */
    private static final String ERROR_EXT = "文档格式错误";

    /**
     * 添加到知识库成功
     */
    protected final String FILE_EXIST_OTHER_LABEL = "添加到知识库成功";

    @Resource
    protected CheckSystemDomainService checkSystemDomainService;

    @Resource
    protected FileCheckConfig fileCheckConfig;


    /**
     * 创建文件转存任务
     */
    protected UserKnowledgeFileTaskEntity saveTransTask(ResourceTransferReqDTO dto, List<String> fileIdsList, List<Long> uploadIds, String thirdTaskId) {
        // 任务对象
        UserKnowledgeFileTaskEntity taskEntity = new UserKnowledgeFileTaskEntity();
        taskEntity.setUserId(dto.getUserId());
        taskEntity.setBaseId(dto.getBaseId());
        taskEntity.setTaskStatus(FileTaskStatusEnum.FINISH.getStatus());
        taskEntity.setTaskType(KnowledgeTaskTypeEnum.TRANSFER.getCode());
        taskEntity.setExpireTime(knowledgePersonalProperties.getTransferExpireDate());
        taskEntity.setUploadIds(uploadIds.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(",")));
        taskEntity.setTaskStatus(FileTaskStatusEnum.PROCESSING.getStatus());
        taskEntity.setThirdTaskId(thirdTaskId);
        taskEntity.setFileIds(String.join(",", fileIdsList));
        taskEntity.setFileNum(fileIdsList.size());
        if (!KnowledgeResourceTypeEnum.isPersonalFile(dto.getResourceType())) {
            taskEntity.setSuccessNum(fileIdsList.size());
        }
        // 保存
        userKnowledgeFileTaskRepository.add(taskEntity);
        return taskEntity;
    }


    /**
     * 知识库文件校验
     *
     * @param fileSize      文件大小
     * @param fileExtension 文件拓展
     * @return
     */
    protected KnowledgeAddResultVO validFile(String fileId, Long fileSize, String fileExtension, String yunDiskCategory) {
        // 校验文件
        String ext = ObjectUtil.isEmpty(fileExtension) ? "" : fileExtension.toLowerCase();
        if (ObjectUtil.isEmpty(ext) || !knowledgePersonalProperties.getExtList().contains(ext)) {
            log.info("【知识库导入】不支持的文件类型，fileId：{}，文件格式：{}", fileId, ext);
            return new KnowledgeAddResultVO(fileId, ResultCodeEnum.ERROR_FILE_FORMAT_SUPPORTED.getResultCode(), ERROR_EXT);
        }

        Long size = fileSize == null ? 0L : fileSize;
        if (FileCategoryEnum.IMAGE.getKnowledgeCategory().equals(FileCategoryEnum.getKnowledgeCategory(yunDiskCategory))) {
            if (knowledgePersonalProperties.getImageSize().compareTo(size) < 0) {
                // 图片大小是否支持
                log.info("【知识库导入】图片过大，fileId：{}，图片大小：{}，限制大小：{}", fileId, fileSize, knowledgePersonalProperties.getImageSize());
                return new KnowledgeAddResultVO(fileId, ResultCodeEnum.FILE_SIZE_LARGE.getResultCode(), ERROR_TOO_LARGE);
            }
        } else if ( knowledgePersonalProperties.getSize().compareTo(size) < 0) {
            log.info("【知识库导入】文件过大，fileId：{}，文件大小：{}，限制大小：{}", fileId, fileSize, knowledgePersonalProperties.getSize());
            return new KnowledgeAddResultVO(fileId, ResultCodeEnum.FILE_SIZE_LARGE.getResultCode(), ERROR_TOO_LARGE);
        }

        return null;
    }

    /**
     * 文件发送任务MQ消息
     *
     * @param taskEntity 转存表信息
     * @return
     */
    protected void sendFileTaskMq(UserKnowledgeFileTaskEntity taskEntity, Long baseId, Long taskId) {
        if (ObjectUtil.isEmpty(taskEntity) || StringUtils.isEmpty(taskEntity.getThirdTaskId())) {
            log.error("知识库任务参数为空");
            return;
        }
        try {
            Map<String, Object> m = new HashMap<>(3);
            m.put("baseId", baseId);
            m.put("taskId", taskId);
            knowledgeTransTaskMQService.sendMqV3(taskEntity, m);
        } catch (Exception e) {
            // 有异常，定时器兜底
            log.error("向mq发送消息失败，异常信息：{}", e.getMessage());
        }
    }

    protected void sendCategoryTaskMq(List<UserKnowledgeUploadEntity> categoryUserKnowledge) {
        for (UserKnowledgeUploadEntity entity : categoryUserKnowledge) {
            try {
                // 插入数据库
                if(!KnowledgeUploadStatusEnum.FAIL.getStatus().equals(entity.getUploadStatus())){
                    knowledgeTransCategoryTaskMqService.sendMq(entity);
                }
            } catch (Exception e) {
                // 有异常，定时器兜底
                log.error("【个人知识库批量导入资源】【mq发送】向mq发送消息失败，异常信息：{}", e.getMessage());
            }
        }
    }

    protected void sendNoteTaskMq(List<UserKnowledgeUploadEntity> categoryUserKnowledge) {
        for (UserKnowledgeUploadEntity entity : categoryUserKnowledge) {
            try {
                // 插入数据库
                userKnowledgeUploadRepository.add(entity);
                if(!KnowledgeUploadStatusEnum.FAIL.getStatus().equals(entity.getUploadStatus())){
                    knowledgeTransCategoryTaskMqService.sendMq(entity);
                }
            } catch (Exception e) {
                // 有异常，定时器兜底
                log.error("【个人知识库批量导入资源】【mq发送】向mq发送消息失败，异常信息：{}", e.getMessage());
            }
        }
    }


    /**
     * 添加知识库文件资源
     *
     * @param dto 请求参数
     * @return 添加结果
     * <AUTHOR>
     * @date 2025/3/21 17:19
     */
    public abstract KnowledgeFileImportVO trans(ResourceTransferReqDTO dto);

    /**
     * 添加知识库文件资源
     *
     * @param dto 请求参数
     * @return 添加结果
     * <AUTHOR>
     * @date 2025/3/21 17:19
     */
    public abstract void check(KnowledgeFileCheckReqDTO dto);

}
