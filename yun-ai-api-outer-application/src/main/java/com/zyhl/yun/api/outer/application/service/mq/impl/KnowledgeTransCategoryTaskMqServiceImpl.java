package com.zyhl.yun.api.outer.application.service.mq.impl;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.chinamobile.tuxedo.sdk.api.Message;
import com.chinamobile.tuxedo.sdk.api.Producer;
import com.chinamobile.tuxedo.sdk.api.SendResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.rocketmq.message.MessageDTO;
import com.zyhl.hcy.yun.ai.common.base.utils.MessageUtil;
import com.zyhl.yun.api.outer.application.service.mq.KnowledgeTransCategoryTaskMqService;
import com.zyhl.yun.api.outer.application.service.mq.KnowledgeTransTaskMqService;
import com.zyhl.yun.api.outer.config.RocketmqProducerProperties;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileTaskEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeUploadEntity;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 知识库转存任务状态查询消息服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class KnowledgeTransCategoryTaskMqServiceImpl implements KnowledgeTransCategoryTaskMqService {

    @Resource
    private RocketmqProducerProperties rocketmqProducerProperties;

    @Resource(name = "knowledgeCategoryVectorProducer")
    private Producer knowledgeCategoryVectorProducer;

    /**
     * 文档向量 事件类型
     */
    private static final String DOC_VECTOR_DISPATCH_EVENT_TYPE = "algorithm.local.knowledge.store.delay";

    /**
     * 消息id前缀
     */
    private static final String MESSAGE_ID_PREFIX = "personalKnowledgeTransCategoryTask_";


    @Override
    public void sendMq(UserKnowledgeUploadEntity uploadEntity) {
        if (ObjectUtil.isEmpty(uploadEntity)) {
            log.error("上传任务参数为空");
            return;
        }

        // 生成消息
        Message msg = createMessage(uploadEntity);

        // 发送消息
        SpringUtil.getBean(KnowledgeTransCategoryTaskMqServiceImpl.class).send(msg, uploadEntity.getUserId());
    }

    /**
     * 创建消息
     *
     * @return
     */
    private Message createMessage(UserKnowledgeUploadEntity uploadEntity) {
        // 参数
        Map<String, Object> params = new HashMap<>(Const.NUM_16);
        // resourceType为0时，个人云的目录ID
        params.put("fileId", uploadEntity.getFileId());
        // resourceType为0时，独立空间的目录
        params.put("targetFileId", uploadEntity.getTargetFileId());
        // 这个独立空间目录下文件路径，即这个目录所在路径+"/"+自身id
        if (StringUtils.isNotBlank(uploadEntity.getTargetParentFilePath()) && StringUtils.isNotBlank(uploadEntity.getTargetFileId())) {
            params.put("targetFilePath", uploadEntity.getTargetParentFilePath() + StrPool.C_SLASH + uploadEntity.getTargetFileId());
        }
        params.put("uploadId", uploadEntity.getId());
        params.put("baseId", uploadEntity.getBaseId());
        params.put("resourceType", uploadEntity.getResourceType());
        params.put("fileLevel", 1);
        params.put("fileType", uploadEntity.getFileType());
        params.put("resource", uploadEntity.getResource());

        // 消息实体
        MessageDTO<Map<String, Object>> dto = new MessageDTO<>(MESSAGE_ID_PREFIX + UUID.randomUUID(), params);
        dto.setEventType(DOC_VECTOR_DISPATCH_EVENT_TYPE);
        dto.setUserId(uploadEntity.getUserId());
        dto.setOwnerId(uploadEntity.getOwnerId());
        dto.setOwnerType(String.valueOf(uploadEntity.getOwnerType()));

        String jsonStr = JSONUtil.toJsonStr(dto);

        // 创建消息
        Message msg = MessageUtil.createMessage();
        msg.setBody(jsonStr.getBytes());
        msg.setTopic(rocketmqProducerProperties.getPersonalKnowledgeCategoryTransTask().getTopic());
        msg.setTag(rocketmqProducerProperties.getPersonalKnowledgeCategoryTransTask().getTag());
        log.info("发送知识库目录转发任务状态查询消息:{}", jsonStr);
        return msg;
    }

    @Retryable
    protected void send(Message msg, String userId) {
        SendResult sendResult;
        try {
            sendResult = knowledgeCategoryVectorProducer.send(msg);
            log.info("发送知识库目录转发任务消息成功，用户id：{}，发送结果：{}", userId, sendResult);
        } catch (Exception e) {
            log.error("发送知识库目录转发任务消息失败，用户id：{}", userId, e);
            throw new YunAiBusinessException(ResultCodeEnum.MQ_SEND_EXCEPTION);
        }
    }


}
