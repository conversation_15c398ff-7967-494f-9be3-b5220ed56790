package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.config.LeadCopyProperties;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.AiTextResultVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.IntentEntityVO;

import lombok.extern.slf4j.Slf4j;

/**
 * 发邮件意图处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SpecialSendMailHandlerImpl extends AbstractChatAddV2Handler {

	/**
	 * 当前执行顺序
	 */
	private ExecuteSort thisExecuteSort = ExecuteSort.SPECIAL_SEND_MAIL;

	@Resource
	private LeadCopyProperties copyProperties;
	@Resource
	private DataSaveService dataSaveService;

	@Override
	public void afterPropertiesSet() throws Exception {
    	//支持的业务初始化
    	List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        this.setBusinessTypes(thisBusinessTypes);
    }
    
	@Override
	public int order() {
		return thisExecuteSort.getSort();
	}

	@Override
	public boolean execute(ChatAddHandleDTO handleDTO) {
		return DialogueIntentionEnum.SEND_MAIL.getCode().equals(handleDTO.getIntentionCode());
	}

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		log.info("进入{}", thisExecuteSort.getDesc());

		if (AssistantEnum.isXiaoTian(RequestContextHolder.getAssistantEnum())) {
			// todo 效果不好小天暂时屏蔽 20250112
			log.info("进入发邮件意图功能处理,渠道来源是小天助手，暂时屏蔽发邮件意图，【发邮件】更新为对话意图（000），执行下一个Handler");
			handleDTO.setTextGenerateTextIntention();
			return true;

		}
		
		ChatAddRespVO respVO = handleDTO.getRespVO();

		// 设置邮件信息
		setMailInfo(handleDTO, respVO);

		// 设置标题
		respVO.setTitle(copyProperties.getSendMailTitle());

		// 设置引导文案对象
		LeadCopyVO copyVo = LeadCopyVO.getLeadCopyVo(
				copyProperties.getByInstruction(DialogueIntentionEnum.SEND_MAIL.getInstruction()),
				DialogueIntentionEnum.SEND_MAIL);
		respVO.setLeadCopy(copyVo);

		// 保存hbase-所有对话结果
		AiTextResultRespParameters respParameters = AiTextResultRespParameters.builder()
				.version(AiTextResultVersionEnum.V2.getVersion()).resultCode(ResultCodeEnum.SUCCESS.getResultCode())
				.resultMsg(ResultCodeEnum.SUCCESS.getResultMsg()).title(copyProperties.getSendMailTitle())
				.leadCopy(copyVo).mailInfo(respVO.getFlowResult().getMailInfo())
				.intentionInfoList(
						handleDTO.getIntentionVO() == null ? null : handleDTO.getIntentionVO().getIntentionInfoList())
				.build();
		respParameters.setOutputCommandVO(handleDTO.getIntentionVO());
		
		// 保存hbase
		dataSaveService.saveHbaseAllChatResult(handleDTO, respParameters);

		// 保存数据库
		dataSaveService.addSuccess(handleDTO, OutContentTypeEnum.TEXT);

		// 返回异步结果
		handleDTO.getRespVO().getFlowResult().setResultType(FlowResultTypeEnum.MAIL.getType());

		// 流式输出
		handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(handleDTO.getRespVO()));

		return false;
	}

	/**
	 * 设置邮件信息
	 * 
	 * @param handleDTO 对话接口内部数据传输对象
	 * @param reqDTO    对话请求VO
	 */
	private void setMailInfo(ChatAddHandleDTO handleDTO, ChatAddRespVO respVO) {
		// 获取邮件信息
		List<DialogueIntentionVO.IntentionInfo> intentionInfoList = handleDTO.getIntentionVO().getIntentionInfoList();
		if (CollectionUtils.isEmpty(intentionInfoList)
				|| CollectionUtils.isEmpty(intentionInfoList.get(0).getEntityList())) {
			return;
		}

		// 实体识别结果VO
		IntentEntityVO entityVO = mergeLists(intentionInfoList.get(0).getEntityList());

		// 封装邮件信息VO
		MailInfoVO mailInfo = MailInfoVO.builder()
				.title(CollectionUtils.isEmpty(entityVO.getTitleList()) ? "" : entityVO.getTitleList().get(0))
				.recipientList(entityVO.getRecipientList()).emailAddressList(entityVO.getEmailAddressList()).build();

		// 设置邮件信息
		respVO.getFlowResult().setMailInfo(mailInfo);
	}

	/**
	 * 合并entityList
	 *
	 * @param entityList 实体列表
	 * @return 实体识别结果VO
	 */
	public static IntentEntityVO mergeLists(List<IntentEntityVO> entityList) {
		IntentEntityVO mergedEntity = new IntentEntityVO();
		mergedEntity.setRecipientList(new ArrayList<>());
		mergedEntity.setTitleList(new ArrayList<>());
		mergedEntity.setEmailAddressList(new ArrayList<>());

		entityList.forEach(entityVO -> {
			mergedEntity.getRecipientList().addAll(entityVO.getRecipientList());
			mergedEntity.getTitleList().addAll(entityVO.getTitleList());
			mergedEntity.getEmailAddressList().addAll(entityVO.getEmailAddressList());
		});

		return mergedEntity;
	}

}
