package com.zyhl.yun.api.outer.application.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Classname YunDiskReqDTO
 * @Description 云盘文件详情接口入参dto
 * @Date 2024/3/19 14:46
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class YunDiskReqDTO {

	/**
	 * 文件id
	 */
	@NotEmpty(message = "文件id")
	private String fileId;
	
	/**
	 * UserFileThumbnailStyleEnum
	 * 
	 * 图片缩略图处理样式 可空：可选值Small，Middle，Big，Large，默认Small
	 */
	private String imageThumbnailStyle;

	public YunDiskReqDTO(String fileId) {
		this.fileId = fileId;
	}

}
