package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

/**
 * 批量获取个人知识库信息请求参数
 * 
 * <AUTHOR>
 * @date 2025-04-15
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class KnowledgeBatchGetReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 知识库id列表
     */
    private List<String> baseIdList;
}
