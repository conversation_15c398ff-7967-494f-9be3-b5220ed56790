package com.zyhl.yun.api.outer.application.chatv2.pojo;

import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO.OpenApiLingxiChatSession;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 灵犀响应参数结果
 *
 * <AUTHOR>
 * @date 2025-06-27 10:15
 */
@Slf4j
@Data
public class OpenApiLingxiParamInfo {
	/**
	 * 是否灵犀响应结果，用于大模型输出使用
	 */
	private boolean lingxiRespFlag = false;

	private String reqId;

	private String deviceId;

	private String model;

	/**
	 * 上一个session信息，用于失败时候响应回去
	 */
	private OpenApiLingxiChatSession lastChatSession;
}
