package com.zyhl.yun.api.outer.application.service.task.impl;

import cn.hutool.core.collection.ConcurrentHashSet;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.yun.ai.common.base.enums.YunAiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.TaskCommentDTO;
import com.zyhl.yun.api.outer.application.dto.TaskCommentQueryDTO;
import com.zyhl.yun.api.outer.application.dto.TaskCommentRetDTO;
import com.zyhl.yun.api.outer.application.service.task.AlgorithmTaskCommentService;
import com.zyhl.yun.api.outer.application.vo.TaskCommentInfoVO;
import com.zyhl.yun.api.outer.config.task.TaskCommentModuleConfig;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.domain.entity.TaskAiAbilityEntity;
import com.zyhl.yun.api.outer.domain.entity.TaskCommentEntity;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.LikeEnum;
import com.zyhl.yun.api.outer.repository.AlgorithmTaskCommentRepository;
import com.zyhl.yun.api.outer.repository.TaskAiAbilityRepository;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 任务评论服务实现类
 * @date 2025/4/21 16:33
 */
@Slf4j
@Service
public class AlgorithmTaskCommentServiceImpl implements AlgorithmTaskCommentService {

    @Resource
    private AlgorithmTaskCommentRepository algorithmTaskCommentRepository;

    @Resource
    private TaskAiAbilityRepository taskAiAbilityRepository;

    @Resource
    private TaskCommentModuleConfig taskCommentModuleConfig;

    @Resource
    private RedissonClient redissonClient;

    @Override
    public void addTaskComment(TaskCommentDTO dto) {
        log.info("add task comment|param={}", dto);
        if (dto.getCommentList() == null || dto.getCommentList().isEmpty()) {
            log.warn("add task comment|评论列表不能为空");
            throw new YunAiBusinessException(AiResultCode.CODE_01000001.getCode(), AiResultCode.CODE_01000001.getMsg());
        }

        String taskId = dto.getTaskId();
        Long taskIdLong = Long.valueOf(taskId);
        String userId = dto.getUserId();
        RLock lock = null;
        boolean acquired = false;

        try {
            // 获取redis锁
            String lockKey = String.format(RedisConstants.ALGORITHM_TASK_COMMENT, userId, taskId);
            lock = redissonClient.getLock(lockKey);
            try {
                // 尝试获取锁，如果获取失败则抛出异常
                acquired = lock.tryLock(RedisConstants.WAIT_TIME_10, RedisConstants.LEASE_TIME_30, TimeUnit.SECONDS);
                if (!acquired) {
                    log.warn("add task comment|acquire lock failed|userId={}|taskId={}", userId, taskId);
                    throw new YunAiBusinessException(AiResultCode.CODE_01000007.getCode(), AiResultCode.CODE_01000007.getMsg());
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("add task comment|acquire lock interrupted|taskId={}|errMsg={}", taskId, e.getMessage(), e);
                throw new YunAiBusinessException(AiResultCode.CODE_9999.getCode(), AiResultCode.CODE_9999.getMsg());
            }

            // 校验用户ID
            AbstractResultCode validUserIdRet = dto.checkUserId();
            if (Objects.nonNull(validUserIdRet)) {
                log.warn("add task comment|userId not match, userId={}", userId);
                throw new YunAiBusinessException(AiResultCode.CODE_01000001.getCode(), AiResultCode.CODE_01000001.getMsg());
            }

            // 校验模块编码
            Set<String> modules = taskCommentModuleConfig.getModuleSet();
            if (CollectionUtils.isEmpty(modules)) {
                log.warn("add task comment|moduleSet was empty");
                throw new YunAiBusinessException(AiResultCode.CODE_01000001.getCode(), AiResultCode.CODE_01000001.getMsg());
            }

            List<TaskCommentInfoVO> commentList = dto.getCommentList();
            Set<String> taskComments = new ConcurrentHashSet<>();

            // 根据 taskId，判断任务是否存在，不存在则不处理
            TaskAiAbilityEntity taskEntity = taskAiAbilityRepository.getTaskEntity(Long.valueOf(taskId));
            if (taskEntity == null) {
                log.warn("add task comment|task not exist, taskId={}", taskId);
                throw new YunAiBusinessException(YunAiCommonResultCode.ERROR_NOT_FOUND);
            }

            // 判断 sourceChannel 是否符合预期值，不符合则不处理
            if (!taskEntity.getSourceChannel().equals(dto.getSourceChannel())) {
                log.warn("add task comment|sourceChannel not match, sourceChannel={}", dto.getSourceChannel());
                throw new YunAiBusinessException(AiResultCode.CODE_01000001.getCode(), AiResultCode.CODE_01000001.getMsg());
            }

            for (TaskCommentInfoVO taskCommentInfoVO : commentList) {
                // 判断 module 是否符合预期值，不符合则不处理
                String module = taskCommentInfoVO.getModule();
                String innerTaskId = taskCommentInfoVO.getTaskId();
                String userIdTaskIdModule = String.format("%s:%s:%s", userId, innerTaskId, module);

                // 判断 taskId 是否符合预期值，不符合则不处理
                if (!innerTaskId.equals(taskId)) {
                    log.warn("add task comment|taskId not match, taskId={}|expectTaskId={}", innerTaskId, taskId);
                    throw new YunAiBusinessException(AiResultCode.CODE_01000001.getCode(), AiResultCode.CODE_01000001.getMsg());
                }
                // 判断 userIdTaskIdModule 是否重复，重复则不处理
                if (!CollectionUtils.isEmpty(taskComments) && taskComments.contains(userIdTaskIdModule)) {
                    log.warn("add task comment|userIdTaskIdModule has been processed, userId={}|taskId={}|module={}",
                            userId, innerTaskId, module);
                    throw new YunAiBusinessException(AiResultCode.CODE_01000001.getCode(), AiResultCode.CODE_01000001.getMsg());
                }
                // 判断 module 是否符合预期值，不符合则不处理
                if (!modules.contains(module)) {
                    log.warn("add task comment|module not support, module={}", module);
                    throw new YunAiBusinessException(AiResultCode.CODE_01000001.getCode(), AiResultCode.CODE_01000001.getMsg());
                }
                // 判断 likeComment 是否符合预期值，不符合则不处理
                if (!LikeEnum.isExist(taskCommentInfoVO.getLikeComment())) {
                    log.warn("add task comment|likeComment not match, likeComment={}", taskCommentInfoVO.getLikeComment());
                    throw new YunAiBusinessException(AiResultCode.CODE_01000001.getCode(), AiResultCode.CODE_01000001.getMsg());
                }
                taskComments.add(userIdTaskIdModule);
            }

            List<TaskCommentEntity> taskCommentEntities = commentList.stream()
                    .map(comment -> TaskCommentEntity.builder()
                            .userId(userId)
                            .taskId(taskIdLong)
                            .module(comment.getModule())
                            .likeComment(comment.getLikeComment())
                            .defaultComment(comment.getDefaultComment())
                            .customComment(comment.getCustomComment())
                            .build())
                    .collect(Collectors.toList());
            // 批量保存评论信息
            algorithmTaskCommentRepository.bulkSave(taskCommentEntities);
        } finally {
            // redis解锁
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public TaskCommentRetDTO getTaskComment(TaskCommentQueryDTO dto) {
        log.info("get task comment|param={}", dto);

        // 校验用户ID
        String userId = dto.getUserId();
        AbstractResultCode validUserIdRet = dto.checkUserId();
        if (Objects.nonNull(validUserIdRet)) {
            log.warn("add task comment|userId not match, userId={}", userId);
            throw new YunAiBusinessException(AiResultCode.CODE_01000001.getCode(), AiResultCode.CODE_01000001.getMsg());
        }

        // 根据 taskId，判断任务是否存在，不存在则不处理
        String taskId = dto.getTaskId();
        TaskAiAbilityEntity taskEntity = taskAiAbilityRepository.getTaskEntity(Long.valueOf(taskId));
        if (taskEntity == null) {
            log.warn("add task comment|task not exist, taskId={}", taskId);
            throw new YunAiBusinessException(YunAiCommonResultCode.ERROR_NOT_FOUND);
        }

        // 判断 sourceChannel 是否符合预期值，不符合则不处理
        if (!taskEntity.getSourceChannel().equals(dto.getSourceChannel())) {
            log.warn("add task comment|sourceChannel not match, sourceChannel={}", dto.getSourceChannel());
            throw new YunAiBusinessException(AiResultCode.CODE_01000001.getCode(), AiResultCode.CODE_01000001.getMsg());
        }

        TaskCommentEntity entity = TaskCommentEntity.builder()
                .taskId(Long.valueOf(taskId))
                .userId(userId)
                .build();
        List<TaskCommentEntity> taskComment = algorithmTaskCommentRepository.getTaskComment(entity);
        if (CollectionUtils.isEmpty(taskComment)) {
            return new TaskCommentRetDTO(Collections.emptyList());
        }

        List<TaskCommentInfoVO> commentList = new ArrayList<>();
        for (TaskCommentEntity taskCommentEntity : taskComment) {
            TaskCommentInfoVO taskCommentInfoVO = new TaskCommentInfoVO();
            taskCommentInfoVO.setTaskId(String.valueOf(taskCommentEntity.getTaskId()));
            taskCommentInfoVO.setModule(taskCommentEntity.getModule());
            taskCommentInfoVO.setLikeComment(taskCommentEntity.getLikeComment());
            taskCommentInfoVO.setDefaultComment(taskCommentEntity.getDefaultComment());
            taskCommentInfoVO.setCustomComment(taskCommentEntity.getCustomComment());
            commentList.add(taskCommentInfoVO);
        }
        return new TaskCommentRetDTO(commentList);
    }

}