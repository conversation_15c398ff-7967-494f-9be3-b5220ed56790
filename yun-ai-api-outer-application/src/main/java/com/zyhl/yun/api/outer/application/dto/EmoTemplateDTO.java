package com.zyhl.yun.api.outer.application.dto;

import lombok.Data;

/**
 * className:EmoTemplateDTO description:
 *
 * <AUTHOR>
 * @date 2024/10/18
 */
@Data
public class EmoTemplateDTO {

    /**
     * 模板id 给前端进行回显，选中对应模版
     */
    private Integer templateId;

    /**
     * 模板url 存CDN,前端从广告平台获取到配置
     */
    private String templateUrl;

    /**
     * 判断人/猫狗图片 0-人，1-猫狗
     */
    private Integer animalFlag;

    /**
     * 模板类型 0-静态模板，1-动态模板
     */
    private Integer mediaMode;

    /**
     * 贴纸的URL
     */
    private String stickerUrl;

    /**
     * 客户端设备信息
     */
    private String clientInfo;

    /**
     * 仅仅添加贴纸
     */
    private Boolean onlyStickFlag;
}
