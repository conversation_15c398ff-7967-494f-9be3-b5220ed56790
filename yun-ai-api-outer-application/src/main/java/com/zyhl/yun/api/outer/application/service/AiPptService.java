package com.zyhl.yun.api.outer.application.service;

import com.zyhl.yun.api.outer.application.dto.AiPptContentSwitchReqDTO;
import com.zyhl.yun.api.outer.application.dto.AiPptContentSwitchRespDTO;
import com.zyhl.yun.api.outer.application.dto.AiPptFileSaveReqDTO;
import com.zyhl.yun.api.outer.application.dto.AiPptResultInformReqDTO;
import com.zyhl.yun.api.outer.application.dto.AiPptResultInformRespDTO;
import com.zyhl.yun.api.outer.domain.dto.aippt.AipptCodeGetRequestDTO;
import com.zyhl.yun.api.outer.domain.vo.aippt.AipptCodeGetResponseVO;

/**
 * AIPPT服务接口
 *
 * <AUTHOR> Assistant
 */
public interface AiPptService {

    /**
     * AIPPT结果通知事件
     *
     * @param reqDTO 请求参数
     * @return PPT对话ID响应
     */
    AiPptResultInformRespDTO resultInform(AiPptResultInformReqDTO reqDTO);

    /**
     * AIPPT文件保存
     *
     * @param reqDTO 请求参数
     */
    void fileSave(AiPptFileSaveReqDTO reqDTO);

    /**
     * 获取鉴权code
     *
     * @param requestDTO 请求参数
     * @return 鉴权code响应
     */
    AipptCodeGetResponseVO getCode(AipptCodeGetRequestDTO requestDTO);

    /**
     * PPT内容格式转换
     *
     * @param reqDTO 请求参数
     * @return 转换后的内容响应
     */
    AiPptContentSwitchRespDTO contentSwitch(AiPptContentSwitchReqDTO reqDTO);
}