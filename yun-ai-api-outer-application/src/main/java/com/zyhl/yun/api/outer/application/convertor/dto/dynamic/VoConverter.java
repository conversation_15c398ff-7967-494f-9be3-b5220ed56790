package com.zyhl.yun.api.outer.application.convertor.dto.dynamic;

import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.vo.chat.ShareDialogueResultVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;

/**
 * 转换成vo
 *
 * @Author: zhumaoxian
 */
@Mapper(componentModel = "spring", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface VoConverter {

    /**
     * entity to vo
     *
     * @param entity entity
     * @return ShareDialogueResultVO
     */
    @Mapping(source = "id", target = "dialogueId")
    @Mapping(source = "toolsCommand", target = "commands")
    ShareDialogueResultVO toShareDialogueResultVO(AlgorithmChatContentEntity entity);

}
