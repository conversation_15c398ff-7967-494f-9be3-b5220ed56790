package com.zyhl.yun.api.outer.application.service.external.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import com.zyhl.hcy.plugin.neauth.util.Md5Util;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.service.external.UserAuthService;
import com.zyhl.yun.api.outer.domain.dto.UserInfoDTO;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.external.client.resp.UserDomainRspDTO;
import com.zyhl.yun.api.outer.external.service.UserEtnService;
import com.zyhl.yun.outer.datahelper.util.constant.CacheConfigConstants;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/11 下午10:07
 */
@Service
@Slf4j
public class UserAuthServiceImpl implements UserAuthService {

	/**
	 * 用户token key
	 */
    private static final String KEY_OF_AUTH_TOKEN = "authToken";
    /**
     * 用户账号
     */
    private static final String KEY_OF_MOBILE = "account";
    /**
     * basic token 前缀
     */
    private static final String KEY_OF_BASIC = "Basic ";

    
	@Resource
    private UserEtnService userEtnService;

    @Override
    public Boolean validateToken(String userId, String token) {

        UserDomainRspDTO result = userEtnService.queryUserIdByToken(token);
        return Objects.equals(userId,result);
    }

    @Override
    @Cacheable(value = CacheConfigConstants.AI_PRE_USER, key = "'token:' + #token",
            unless = "#result == null",
            cacheManager = CacheConfigConstants.REDIS_CACHE)
    public UserDomainRspDTO validateTokenOnly(String token) {
        UserDomainRspDTO userDomainRspDTO = userEtnService.queryUserIdByToken(token);
        if (Objects.isNull(userDomainRspDTO)) {
            log.error("token:{} 无效", token);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_NOT_AUTH);
        }
        return userDomainRspDTO;
    }

    @Cacheable(value = CacheConfigConstants.AI_PRE_USER, key = "'userId:' + #userId",
            unless = "#result == null",
            cacheManager = CacheConfigConstants.REDIS_CACHE)
    @Override
    public UserInfoDTO validateUserId(String userId) {
        if(ObjectUtil.isEmpty(userId)){
            return null;
        }
        UserInfoDTO userInfoDTO = userEtnService.getUserInfoData(Long.parseLong(userId));
        return userInfoDTO;
    }

	@Override
	public UserDomainRspDTO validateStToken(String stToken, String sourceId, Long ts, String sign) throws Exception {

		log.info("validateStToken stToken:{}, sourceId:{}, ts:{}, sign:{}", stToken, sourceId, ts, sign);

		if (null == ts || StringUtils.isAnyBlank(stToken, sourceId, sign)) {
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}
		String realSign = Md5Util.getMd5(stToken + sourceId + ts);
		// 校验签名
		if (!(null != sign && sign.equalsIgnoreCase(realSign.toLowerCase()))) {
			log.error("校验签名失败，入参 sign:{}, realSign:{}", sign, realSign);
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_SIGNATURE);
		}
		// 获取basic token
		String basicTokenResult = userEtnService.loginByStToken(stToken, sourceId);
		if (StringUtils.isEmpty(basicTokenResult)) {
			log.error("loginByStToken basicToken 为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_TOKEN_EXPIRE);
		}
		
		log.info("loginByStToken succ result basicTokenResult:{}", basicTokenResult);
		JSONObject tokenObj = JSONUtil.parseObj(basicTokenResult);
		String authToken = tokenObj.getStr(KEY_OF_AUTH_TOKEN);
		String mobile = tokenObj.getStr(KEY_OF_MOBILE);
		if (StringUtils.isEmpty(authToken) || StringUtils.isEmpty(mobile)) {
			log.error("loginByStToken authToken or mobile 为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_TOKEN_EXPIRE);
		}
		String mobilePrefix = "mobile:%s:";
		// 转换basic token
		String basicToken = KEY_OF_BASIC + Base64.encode(String.format(mobilePrefix, mobile) + authToken);
		// 获取用户信息
		UserDomainRspDTO userInfo = userEtnService.queryUserIdByToken(basicToken);
		if (null == userInfo) {
			log.error("queryUserIdByToken userInfo 为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_TOKEN_EXPIRE);
		}
		// 设置basic token
		userInfo.setBasicToken(basicToken);
		return userInfo;

	}
}
