package com.zyhl.yun.api.outer.application.chatv2.service;

import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;

/**
 * 对话公共服务接口
 * 
 * <AUTHOR>
 */
public interface ChatCommonService {

	String getPromptByConfig(SseEventListener event);

	/**
	 * 获取提示词(优先取配置，再取db)
	 * @param inputPrompt
	 * @param modelCode
	 * @return
	 */
	String getPromptByConfigAndDB(String inputPrompt, String modelCode,String channel);
}
