package com.zyhl.yun.api.outer.application.service.knowledge.impl;

import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.PesonalKnowledgeConvertor;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeMailPreviewReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.KnowledgeMailPreviewService;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeMailPreviewInfoResultVO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.PersonalKnowledgeMailPreviewInfoEntity;
import com.zyhl.yun.api.outer.domainservice.impl.KnowledgeMailDomainServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 邮箱预览服务
 * @date 2025/4/17 15:58
 */
@Slf4j
@Service
@AllArgsConstructor
public class KnowledgeMailPreviewServiceImpl implements KnowledgeMailPreviewService {

    private final KnowledgeMailDomainServiceImpl knowledgeMailDomainService;

    private final PesonalKnowledgeConvertor convertor;


    /**
     * 获取邮箱预览信息
     *
     * @param dto
     * @return
     */
    public KnowledgeMailPreviewInfoResultVO previewMailResource(KnowledgeMailPreviewReqDTO dto) {
        // 获取邮件预览信息
        PersonalKnowledgeMailPreviewInfoEntity entity  = knowledgeMailDomainService.getMailPreview(dto.getBaseId(), dto.getResourceId(),dto.getModel().getValue());
        return convertor.toResultVO(entity);
    }


}