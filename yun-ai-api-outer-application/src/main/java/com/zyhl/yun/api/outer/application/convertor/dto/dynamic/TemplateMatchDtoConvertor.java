package com.zyhl.yun.api.outer.application.convertor.dto.dynamic;


import com.zyhl.yun.api.outer.application.dto.TemplateMatchDTO;
import com.zyhl.yun.api.outer.application.vo.TemplateMatchVO;
import com.zyhl.yun.api.outer.domain.entity.TemplateMatchEntity;
import com.zyhl.yun.api.outer.domain.resp.TemplateMatchRsqEntity;
import org.mapstruct.Mapper;

/**
 * 模板匹配结果Convertor
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface TemplateMatchDtoConvertor {

    /**
     * dto 转 entity
     * @param dto dto
     * @return entity
     */
    TemplateMatchEntity toEntity(TemplateMatchDTO dto);

    /**
     * entity 转 vo
     * @param data entity
     * @return TemplateMatchVO
     */
    TemplateMatchVO toVO(TemplateMatchRsqEntity data);
}
