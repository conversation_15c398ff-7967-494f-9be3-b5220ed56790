package com.zyhl.yun.api.outer.application.assembler;

import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;

import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.FileResp;
import com.zyhl.yun.api.outer.domain.valueobject.File;

/**
 * 对话文件-类转换器
 * 
 * @Author: liux<PERSON>wen
 */
@Mapper(componentModel = "spring", injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public abstract class ChatFileAssembler {

	public abstract File getFileVo(FileResp vo);

}
