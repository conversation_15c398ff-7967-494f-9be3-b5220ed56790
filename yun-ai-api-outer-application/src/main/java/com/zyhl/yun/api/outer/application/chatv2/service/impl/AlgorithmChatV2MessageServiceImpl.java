package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.zyhl.hcy.yun.ai.common.base.annotation.MethodExecutionTimeLog;
import com.zyhl.yun.api.outer.application.assembler.ChatV2MessageAssembler;
import com.zyhl.yun.api.outer.application.chatv2.dto.AlgorithmChatV2ListDTO;
import com.zyhl.yun.api.outer.application.chatv2.service.AlgorithmChatV2MessageService;
import com.zyhl.yun.api.outer.application.chatv2.vo.MessageResultV2VO;
import com.zyhl.yun.api.outer.config.BusinessParamProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatMessageEntity;
import com.zyhl.yun.api.outer.domain.vo.*;
import com.zyhl.yun.api.outer.domain.vo.common.PageInfoVO;
import com.zyhl.yun.api.outer.enums.*;
import com.zyhl.yun.api.outer.enums.chat.ChatMessageIconEnum;
import com.zyhl.yun.api.outer.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.zyhl.yun.api.outer.constants.Const.NUM_16;

/**
 * 会话-serviceImpl
 *
 * @Author: WeiJingKun
 */
@Slf4j
@Service
public class AlgorithmChatV2MessageServiceImpl implements AlgorithmChatV2MessageService {

    @Resource
    private AlgorithmChatMessageRepository algorithmChatMessageRepository;

    @Resource
    private ChatApplicationTypeRepository chatApplicationTypeRepository;

    @Resource
    private ChatV2MessageAssembler messageAssembler;

    @Resource
    private SourceChannelsProperties sourceChannelsProperties;
    @Resource
    private BusinessParamProperties businessParamProperties;

    @Override
    @MethodExecutionTimeLog(value = "历史会话列表查询-serviceImpl", printParam = true, printResult = true)
    public PageInfoVO<MessageResultV2VO> chatList(AlgorithmChatV2ListDTO dto) {
        /** 参数初始化 */
        String userId = dto.getUserId();
        // 应用类型处理，没传值，则默认chat-普通对话
        String applicationType = dto.getApplicationType();
        String sourceChannel = dto.getSourceChannel();
        applicationType = CharSequenceUtil.isBlank(applicationType) ? ApplicationTypeEnum.CHAT.getCode() : applicationType;
        // 获取业务类型
        String businessType = sourceChannelsProperties.getTypeNullThrowException(sourceChannel);
        PageInfoDTO page = PageInfoDTO.getReqDTO(dto.getPageInfo());

        /** 获取会话信息 */
        PageInfo<AlgorithmChatMessageEntity> pageInfo = algorithmChatMessageRepository.chatList(
                AlgorithmChatMessageEntity.builder()
                        .userId(userId)
                        .businessType(businessType)
                        .sourceChannel(sourceChannel)
                        .applicationType(applicationType)
                        .sortType(dto.getSortType())
                        .build(),
                Integer.parseInt(page.getPageCursor()),
                page.getPageSize(),
                page.getNeedTotalCount()
        );
        List<AlgorithmChatMessageEntity> messageEntityList = pageInfo.getList();
        log.info("历史会话列表查询-serviceImpl，分页：{}，结果：{}", JSON.toJSON(page), JSON.toJSON(messageEntityList));

        // 智能体类型，则需要获取对话应用类型信息map，key是应用id
        Map<String, ChatApplicationType> chatApplicationTypeMap = new HashMap<>(NUM_16);
        if (ApplicationTypeEnum.INTELLIGENT.getCode().equals(applicationType)) {
            chatApplicationTypeMap = chatApplicationTypeRepository.listToMapKeyIsId();
        }

        /** 封装返回结果 */
        PageInfoVO<MessageResultV2VO> result = PageInfoVO.getRespDTO(page, pageInfo);
        if (CollUtil.isEmpty(messageEntityList)) {
            return result;
        }
        List<MessageResultV2VO> messageVOList = new ArrayList<>();
		for (AlgorithmChatMessageEntity messageEntity : messageEntityList) {
			MessageResultV2VO messageVO = messageAssembler.toMessageResultV2VO(messageEntity);
			messageVO.setSessionId(String.valueOf(messageEntity.getId()));
			String iconType = messageEntity.getIconType();
			String subIconType = messageEntity.getSubIconType();
			messageVO.setIconUrl(businessParamProperties.getAssistantChatV2List().getIconUrl(iconType, subIconType));

			String title = messageEntity.getTitle();
			// 标题追加
			if (ChatMessageIconEnum.isImageDialogue(iconType)) {
				messageVO.setTitle(ChatMessageIconEnum.IMAGE_DIALOGUE.getMessageTitle() + title);
			}
			if (ChatMessageIconEnum.isAiCoderDialogue(iconType, subIconType)) {
				messageVO.setTitle(ChatMessageIconEnum.TOOL_DIALOGUE_AI_CODER.getMessageTitle() + title);
			}
			if (ChatMessageIconEnum.isSpeedReadDialogue(iconType, subIconType)) {
				messageVO.setTitle(ChatMessageIconEnum.TOOL_DIALOGUE_SPEED_READ.getMessageTitle() + title);
			}
			if (ChatMessageIconEnum.isMeetingMinutesDialogue(iconType, subIconType)) {
				messageVO.setTitle(ChatMessageIconEnum.TOOL_DIALOGUE_MEETING_MINUTES.getMessageTitle() + title);
			}
			if (ChatMessageIconEnum.isAiPptDialogue(iconType, subIconType)) {
				messageVO.setTitle(ChatMessageIconEnum.TOOL_DIALOGUE_AI_PPT.getMessageTitle() + title);
			}

			// 智能体类型，则需要获取智能体信息
			if (ApplicationTypeEnum.INTELLIGENT.getCode().equals(applicationType)) {
				String applicationId = messageEntity.getApplicationId();
				if (CharSequenceUtil.isNotBlank(applicationId)) {
					messageVO.setApplicationInfo(chatApplicationTypeMap.get(applicationId));
				}
			}
			messageVOList.add(messageVO);
		}
        result.setList(messageVOList);
        return result;
    }

}
