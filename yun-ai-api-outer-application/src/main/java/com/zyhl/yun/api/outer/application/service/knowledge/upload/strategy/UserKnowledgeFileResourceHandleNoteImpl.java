package com.zyhl.yun.api.outer.application.service.knowledge.upload.strategy;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileAddReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileBatchImportReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.upload.AbstractUserKnowledgeFileResourceHandle;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeAddResultVO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeTaskResultVO;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.domain.entity.PersonalKnowledgeImportTaskEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.vo.ImportNoteInfoVO;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileTaskResultEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 描述：笔记资源文件处理
 *
 * <AUTHOR> zhumaoxian  2025/3/21 17:19
 */
@Slf4j
@Component
public class UserKnowledgeFileResourceHandleNoteImpl extends AbstractUserKnowledgeFileResourceHandle {
    @Override
    public void afterPropertiesSet() throws Exception {
        register(KnowledgeResourceTypeEnum.NOTE.getCode(), this);
    }

    @Override
    public KnowledgeTaskResultVO addV2(KnowledgeFileAddReqDTO dto) {

        // 已入库的笔记
        Map<String, UserKnowledgeFileEntity> existMap = new HashMap<>(Const.NUM_32);
        List<String> idList = dto.getFileList().stream().map(File::getFileId).collect(Collectors.toList());
        userKnowledgeFileRepository.selectByOldFileIds(dto.getUserId(), idList).forEach(item -> existMap.put(item.getOldFileId(), item));

        // 循环校验文件
        List<KnowledgeAddResultVO> resultList = new ArrayList<>();
        List<String> addUrlList = new ArrayList<>();
        List<UserKnowledgeFileEntity> addList = new ArrayList<>();
        for (File file : dto.getFileList()) {
            // db校验
            UserKnowledgeFileEntity entity = existMap.get(file.getFileId());
            if (entity != null) {
                log.info("笔记资源已上传，fileId：{}，ai_status:{}", file.getFileId(), entity.getAiStatus());
                resultList.add(new KnowledgeAddResultVO(file.getFileId(), ResultCodeEnum.FILE_REPEAT_UPLOAD));
                continue;
            }

            // 校验通过
            resultList.add(new KnowledgeAddResultVO(file.getFileId(), ResultCodeEnum.SUCCESS.getResultCode(), FILE_EXIST_OTHER_LABEL));

            addUrlList.add(file.getFileId());
            addList.add(new UserKnowledgeFileEntity(file, dto.getResourceType(), dto.getUserId()));
        }
        // todo 先入上传表，表新建  高兴
        // todo 发送mq  高兴

        // 创建转存任务
        createTransTask(dto, resultList, addUrlList, null);

        if (ObjectUtil.isNotEmpty(addList)) {
            // 批量入库
            userKnowledgeFileRepository.batchAdd(addList);

            // 发送mq
            knowledgeDispatchTaskMqService.sendTaskMq(addList);
        }

        // 返回结果
        KnowledgeTaskResultVO resultVO = new KnowledgeTaskResultVO();
        resultVO.setResultList(resultList);
        if (ObjectUtil.isEmpty(addList)) {
            resultVO.setStatus(FileTaskResultEnum.FAIL.getStatus());
        } else {
            resultVO.setStatus(FileTaskResultEnum.PROCESSING.getStatus());
        }
        return resultVO;
    }
}
