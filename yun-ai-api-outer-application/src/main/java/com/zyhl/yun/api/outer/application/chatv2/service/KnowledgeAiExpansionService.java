package com.zyhl.yun.api.outer.application.chatv2.service;

import com.zyhl.hcy.yun.ai.common.rag.vo.RewriteResultVO;
import com.zyhl.yun.api.outer.application.chatv2.dto.KnowledgeSearchDTO;
import com.zyhl.yun.api.outer.application.chatv2.pojo.KnowledgeFlowInfo;

/**
 * 知识库AI扩写服务
 *
 * <AUTHOR>
 */
public interface KnowledgeAiExpansionService {

    /**
     * 获取知识库AI扩写大纲信息
     *
     * @param knowledgeFlowInfo 知识库流程处理流传信息
     * @param enableWhiteVip    是否启用白名单
     */
    void setExpansionOutline(KnowledgeFlowInfo knowledgeFlowInfo, Boolean enableWhiteVip);

    /**
     * 获取知识库AI扩写信息
     *
     * @param dto     搜索参数
     * @param rewrite 扩写信息
     */
    void setAiExpansionInfo(KnowledgeSearchDTO dto, RewriteResultVO rewrite);
}
