package com.zyhl.yun.api.outer.application.convertor.dto.dynamic;

import com.zyhl.yun.api.outer.application.dto.ApplicationTypeListDTO;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.domain.entity.ApplicationTypeListEntity;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import javax.annotation.Resource;

/**
 * ApplicationTypeListDTO转Entity类
 *
 * <AUTHOR>
 * @version 2024年04月16日 16:10
 */

@Mapper(componentModel = "spring", injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public abstract class BaseApplicationTypeListConvertor {


    @Resource
    protected SourceChannelsProperties sourceChannelsProperties;

    /**
     * dto转entity
     * @param dto dto
     * @return entity
     */
    @Mapping(target = "businessType", expression = "java(sourceChannelsProperties.getType(dto.getSourceChannel()))")
    public abstract ApplicationTypeListEntity toEntity(ApplicationTypeListDTO dto);

}
