package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.FileOperationUtil;
import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.dto.CreateOutlineDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.AssistantChatV2PollingUpdateDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueIntentionDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueTextToolSettingDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueToolSettingDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.pojo.KnowledgeFlowInfo;
import com.zyhl.yun.api.outer.application.chatv2.service.AlgorithmChatV2ContentService;
import com.zyhl.yun.api.outer.application.chatv2.service.BenefitService;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatAddCheckService;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatAddFileService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.service.KnowledgeSearchService;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueResultV2VO;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.application.config.ChatTextToolOpenConfig;
import com.zyhl.yun.api.outer.application.dto.ChatConfigGetDTO;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.application.service.chat.ChatConfigService;
import com.zyhl.yun.api.outer.config.ImageCommonProperties;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.valueobject.KnowledgeSearchInfo;
import com.zyhl.yun.api.outer.domain.vo.ChatConfigVO;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.domainservice.QpsLimitService;
import com.zyhl.yun.api.outer.domainservice.ResourceInfoDomainService;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.AiTextResultVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.SseNameEnum;
import com.zyhl.yun.api.outer.external.DocumentParsingExternalService;
import com.zyhl.yun.api.outer.external.YunDiskExternalService;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * AI PPT
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TextToolAiPptOutlineHandlerImpl extends AbstractChatAddV2Handler {

    /**
     * 当前执行顺序
     */
    private final ExecuteSort thisExecuteSort = ExecuteSort.TEXT_TOOL_AI_PPT_OUT_LINE;
    /**
     * AIPPT意图
     */
    private static final String THIS_MAIN_INTENTION = DialogueIntentionEnum.TEXT_TOOL.getCode();
    private static final String THIS_SUB_INTENTION = DialogueIntentionSubEnum.AI_GENERATE_PPT.getCode();

    /**
     * 模型编码
     */
    private static final String MODEL_ALI_AI_PPT = "ali-ai-ppt";

    private static final String FILE_SUFFIX = "txt";

    @Resource
    private BenefitService benefitService;

    @Resource
    private DataSaveService dataSaveService;

    @Resource
    private QpsLimitService qpslimitService;

    @Resource
    private ChatConfigServiceDomainService chatConfigServiceDomainService;

    @Resource
    private ChatConfigService chatConfigService;

    @Resource
    private AlgorithmChatContentRepository algorithmChatContentRepository;

    @Resource
    private TextModelExternalService textModelExternalService;

    @Resource
    private ResourceInfoDomainService resourceInfoDomainService;

    @Resource
    private ChatAddCheckService chatAddCheckService;

    @Resource
    private DocumentParsingExternalService documentParsingExternalService;

    @Resource
    private YunDiskExternalService yunDiskExternalService;

    @Resource
    private AiTextResultRepository aiTextResultRepository;

    @Resource
    private KnowledgeSearchService knowledgeSearchService;

    @Resource
    private ChatAddFileService chatAddFileService;

    @Resource
    private ImageCommonProperties imageCommonProperties;

    @Resource
    private ChatTextToolOpenConfig chatTextToolOpenConfig;

    @Resource
    private ChatTextToolBusinessConfig chatTextToolBusinessConfig;

    @Resource
    private UidGenerator uidGenerator;

    @Resource
    private AlgorithmChatV2ContentService contentService;

    @Override
    public void afterPropertiesSet() throws Exception {
        //支持的业务初始化
        List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        this.setBusinessTypes(thisBusinessTypes);
    }

    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }


    @Override
    public boolean execute(ChatAddHandleDTO handleDTO) {
        Optional<DialogueInputInfoDTO> optional = Optional.ofNullable(handleDTO).map(ChatAddHandleDTO::getReqDTO)
                .map(ChatAddReqDTO::getDialogueInput);

        Optional<DialogueIntentionDTO> commandOption = optional.map(DialogueInputInfoDTO::getCommand);
        Optional<String> mainCommandOption = commandOption.map(DialogueIntentionDTO::getCommand);
        Optional<String> subCommandOption = commandOption.map(DialogueIntentionDTO::getSubCommand);
        // 当传了指令意图和子意图，则判断是不是ai ppt意图
        boolean command = commandOption.isPresent() && mainCommandOption.isPresent() && subCommandOption.isPresent()
                && DialogueIntentionEnum.isTextToolIntention(mainCommandOption.get())
                && THIS_SUB_INTENTION.equals(subCommandOption.get());

        // 意图识别意图和子意图，则判断是不是ai ppt意图
        boolean isInputIntention = false;
        IntentionInfo mainIntention = DialogueIntentionVO.getMainIntention(Objects.requireNonNull(handleDTO).getIntentionVO());
        if (null != mainIntention) {
            isInputIntention = DialogueIntentionEnum.isTextToolIntention(mainIntention.getIntention())
                    && THIS_SUB_INTENTION.equals(mainIntention.getSubIntention());
        }

        // 判断是不是ai ppt生成，如果是执行其他下一个
        Optional<Boolean> pptOptional = optional.map(DialogueInputInfoDTO::getToolSetting)
                .map(DialogueToolSettingDTO::getTextToolSetting).map(DialogueTextToolSettingDTO::getEnablePptMake);
        if ((command || isInputIntention) && (!pptOptional.isPresent() || Boolean.FALSE.equals(pptOptional.get()))) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        log.info("进入{}", thisExecuteSort.getDesc());

        if (!chatTextToolOpenConfig.getAiPptOpenByChannel(RequestContextHolder.getSourceChannel())) {
            log.info("isAiPptOpen=false，不执行aippt意图，aippt大纲生成转换为文本意图");
            handleDTO.setTextGenerateTextIntention();
            return true;
        }

        Map<String, List<String>> argumentMap;

        // 判断对话内容是不是空&&文档是否空（包含：图片/邮件/笔记/文档/附件）
        if (StringUtils.isEmpty(handleDTO.getInputInfoDTO().getDialogue()) && handleDTO.isReqResourceDocSse()
                && handleDTO.isReqResourceImageSse()) {
            log.info("生成ppt大纲来源为空，对话内容/图片/邮件/笔记/文档/附件 都为空");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        DialogueIntentionVO.IntentionInfo mainIntention = DialogueIntentionVO.getMainIntention(handleDTO.getIntentionVO());
        mainIntention.setIntention(THIS_MAIN_INTENTION);
        mainIntention.setSubIntention(THIS_SUB_INTENTION);
        argumentMap = mainIntention.getArgumentMap();

        // 设置输出意图及参数信息
        handleDTO.getRespVO().setOutputCommandVO(mainIntention);
        handleDTO.getRespVO().getOutputCommand().setArgumentMap(argumentMap);

        return runAliPPTOutline(handleDTO);
    }

    /**
     * 执行ai ppt 大纲
     *
     * @param handleDTO the  handle dto
     * @return {@link Boolean}
     * <AUTHOR>
     * @date 2025-4-30 12:08
     */
    private Boolean runAliPPTOutline(ChatAddHandleDTO handleDTO) {
        List<File> attachmentToFiles = Collections.emptyList();
        String userId = RequestContextHolder.getUserId();
        try {
            // 扣减权益
            benefitService.consumeBenefit(handleDTO.getReqDTO(), RequestContextHolder.getPhoneNumber(), handleDTO.getDialogueId());

            AiTextResultRespParameters respParameters = AiTextResultRespParameters.builder()
                    .version(AiTextResultVersionEnum.V2.getVersion()).resultCode(ResultCodeEnum.SUCCESS.getResultCode())
                    .resultMsg(ResultCodeEnum.SUCCESS.getResultMsg())
                    .build();
            respParameters.setOutputCommandVO(handleDTO.getIntentionVO());

            // 保存到hbase
            dataSaveService.saveHbaseAllChatResult(handleDTO, respParameters);

            // 保存数据库
            dataSaveService.add(handleDTO, ChatStatusEnum.CHAT_IN);

            // 监听器
            SseEventListener event = new SseEventListener(handleDTO, null);
            event.getSseEmitterOperate().setSseName(SseNameEnum.SIMPLE_SSE.getCode());
            // 获取用户设置的模型，没有设置则使用默认模型
            ChatConfigEntity chatConfigEntity = chatConfigServiceDomainService.getUserCanUseModel(event.getUserId(),
                    event.getPhone(), handleDTO.getAssistantEnum(), handleDTO.getBusinessType());
            event.setModelCode(chatConfigEntity.getModelType());

            // qps限制
            if (!qpslimitService.modelQpsLimit(MODEL_ALI_AI_PPT)) {
                log.info("【普通文本对话】请求过多，qps限流，model:{}", event.getModelCode());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_LIMITATION);
            }

            // 前端不传参设置设置默认值
            if (null == handleDTO.getInputInfoDTO().getToolSetting()) {
                handleDTO.getInputInfoDTO().setToolSetting(new DialogueToolSettingDTO());
            }
            if (null == handleDTO.getInputInfoDTO().getToolSetting().getTextToolSetting()) {
                handleDTO.getInputInfoDTO().getToolSetting().setTextToolSetting(DialogueTextToolSettingDTO.getDefault());
            }

            Optional<DialogueTextToolSettingDTO> dialogueTextToolSettingDTO = Optional.of(handleDTO)
                    .map(ChatAddHandleDTO::getReqDTO)
                    .map(ChatAddReqDTO::getDialogueInput).map(DialogueInputInfoDTO::getToolSetting)
                    .map(DialogueToolSettingDTO::getTextToolSetting);
            CreateOutlineDTO createOutlineDTO = new CreateOutlineDTO();
            createOutlineDTO.setRequestId(Long.toString(handleDTO.getDialogueId()));
            createOutlineDTO.setContent(new String[]{handleDTO.getReqDTO().getDialogueInput().getDialogue()});
            dialogueTextToolSettingDTO.map(DialogueTextToolSettingDTO::getPage)
                    .ifPresent(pageType -> createOutlineDTO.setPage(Integer.parseInt(pageType)));
            dialogueTextToolSettingDTO.map(DialogueTextToolSettingDTO::getScene)
                    .ifPresent(scene -> createOutlineDTO.setScene(Integer.parseInt(scene)));
            dialogueTextToolSettingDTO.map(DialogueTextToolSettingDTO::getLanguage)
                    .ifPresent(createOutlineDTO::setLanguage);
            dialogueTextToolSettingDTO.map(DialogueTextToolSettingDTO::getGroup)
                    .ifPresent(group -> createOutlineDTO.setGroup(Integer.parseInt(group)));

            attachmentToFiles = chatAddFileService.getAttachmentToFile(handleDTO);
            if (!CollectionUtils.isEmpty(attachmentToFiles)) {
                File[] array = attachmentToFiles.stream().filter(Objects::nonNull).toArray(File[]::new);
                createOutlineDTO.setFiles(array);
                // 设置临时文件路径
                event.setAttachmentToFilePaths(
                        attachmentToFiles.stream().distinct().map(File::getAbsolutePath).collect(Collectors.toList()));
            }

            // 设置用户token
            createOutlineDTO.setToken(RequestContextHolder.getToken());

            Boolean historyToOutLine = fromHistoryToOutLine(handleDTO, createOutlineDTO);

            // 个人知识库搜索
            if (Boolean.FALSE.equals(historyToOutLine) && chatTextToolBusinessConfig.getAiPptGenerate().isSearchKnowledgeEnable()
                    && StringUtils.isNoneEmpty(handleDTO.getInputInfoDTO().getDialogue())) {
                try {
                    KnowledgeFlowInfo knowledgeFlowInfo = knowledgeSearchService.setKnowledgeFlowInfo(handleDTO);
                    if (null != knowledgeFlowInfo) {
                        log.info("aippt大纲生成，搜索知识库， dialogueId:{}", handleDTO.getDialogueId());
                        // 历史记录查询
                        List<TextModelMessageDTO> historyList = aiTextResultRepository.getHistoryList(userId,
                                handleDTO.getReqDTO().getSessionId());
                        // 查询用户配置的模型
                        ChatConfigGetDTO dto = new ChatConfigGetDTO();
                        dto.setUserId(RequestContextHolder.getUserId());
                        dto.setBusinessType(RequestContextHolder.getBusinessType());
                        dto.setSourceChannel(RequestContextHolder.getSourceChannel());
                        dto.setAssistantEnum(RequestContextHolder.getAssistantEnum());
                        ChatConfigVO vo = chatConfigService.get(dto).stream().filter(item -> item.getDefaultMode() == 1)
                                .findFirst().get();
                        handleDTO.getKnowledgeFlowInfo().setChatConfigVO(vo);
                        // 召回知识库信息，保存在KnowledgeFlowInfo
                        List<KnowledgeSearchInfo> personKnowledgeList = knowledgeSearchService
                                .knowledgeCallData(handleDTO, historyList);
                        if (CollUtil.isNotEmpty(personKnowledgeList)) {
                            createOutlineDTO.setKnowledge(JSONUtil.toJsonStr(personKnowledgeList));
                        }
                    } else {
                        log.info("aippt大纲生成，不搜索知识库， dialogueId:{}", handleDTO.getDialogueId());
                    }
                } catch (Exception e) {
                    log.error("aippt大纲生成，知识库搜索异常 dialogueId:{} error:", handleDTO.getDialogueId(), e);
                }
            } else {
                log.info("aippt大纲生成，不搜索知识库，【关闭功能或者对话内容为空】 dialogueId:{}", handleDTO.getDialogueId());
            }

            // 调用阿里ppt大纲
            textModelExternalService.streamAiPptOutlineDialogue(createOutlineDTO, event);
            // 更新模型编码
            algorithmChatContentRepository.updateModelCode(event.getDialogId(), event.getModelCode());

            return Boolean.FALSE;
        } catch (Exception e) {
            log.error("streamAiPptOutlineDialogue error:", e);
            if (!CollectionUtils.isEmpty(attachmentToFiles)) {
                //删除临时文件
                List<String> attachmentToFilePaths = attachmentToFiles.stream().distinct().map(File::getAbsolutePath)
                        .collect(Collectors.toList());
                FileOperationUtil.deleteLocalFiles(attachmentToFilePaths);
            }
            throw e;
        }
    }

    /**
     * content 写 file
     *
     * @param file    the file
     * @param content the content
     * <AUTHOR>
     * @date 2025-4-30 12:07
     */
    private void writeFile(File file, String content) throws IOException {
        FileUtils.writeByteArrayToFile(file, content.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 从历史会话中获取内容
     *
     * @param handleDTO        the handle dto
     * @param createOutlineDTO the create outline dto
     * @return {@link Boolean}
     * <AUTHOR>
     * @date 2025/6/17 14:17
     */
    private Boolean fromHistoryToOutLine(ChatAddHandleDTO handleDTO,
                                         CreateOutlineDTO createOutlineDTO) {
        try {
            DialogueIntentionVO intentionVO = handleDTO.getIntentionVO();
            IntentionInfo mainIntention = DialogueIntentionVO.getMainIntention(intentionVO);
            Map<String, List<String>> argumentMap = mainIntention.getArgumentMap();
            Boolean isHistory = CollUtil.isNotEmpty(argumentMap)
                    && argumentMap.containsKey(ChatTextToolBusinessConfig.ARGUMENT_AI_PPT_HISTORY_SEARCH)
                    && argumentMap.get(ChatTextToolBusinessConfig.ARGUMENT_AI_PPT_HISTORY_SEARCH)
                    .contains(ChatTextToolBusinessConfig.ARGUMENT_AI_PPT_HISTORY_SEARCH_VALUE);
            if (Boolean.FALSE.equals(isHistory)) {
                log.info("==> isHistory is false");
                return Boolean.FALSE;
            }
            Boolean hasKeywords = CollUtil.isNotEmpty(argumentMap)
                    && argumentMap.containsKey(ChatTextToolBusinessConfig.ARGUMENT_AI_MEMORY_ALBUM_KEYWORD)
                    && CollUtil.isNotEmpty(argumentMap.get(ChatTextToolBusinessConfig.ARGUMENT_AI_MEMORY_ALBUM_KEYWORD));
            if (Boolean.FALSE.equals(hasKeywords)) {
                log.info("==> hasKeywords is false");
                return Boolean.FALSE;
            }
            List<AlgorithmChatContentEntity> contentEntityList = algorithmChatContentRepository.getContentListBySessionId(handleDTO.getSessionId(),
                    chatTextToolBusinessConfig.getAiPptGenerate().getMaxHistoryCount(), RequestContextHolder.getUserId());
            if (CollUtil.isEmpty(contentEntityList)) {
                log.warn("==> 获取历史会话内容失败, 此次对话可能在这次会话中的第一条");
                return Boolean.FALSE;
            }

            log.info("==> 获取历史会话内容成功, contentEntityList:{}", JSONUtil.toJsonStr(contentEntityList));
            List<String> keywordList = ListUtil.toList(argumentMap.get(ChatTextToolBusinessConfig.ARGUMENT_AI_MEMORY_ALBUM_KEYWORD));
            Optional<AlgorithmChatContentEntity> optional = contentEntityList.stream()
                    .filter(Objects::nonNull)
                    .filter(entity -> StringUtils.isNotEmpty(entity.getInContent()))
                    .filter(entity -> !entity.getId().equals(handleDTO.getDialogueId()))
                    .filter(entity -> keywordList.stream().anyMatch(entity.getInContent()::contains))
                    .sorted(Comparator.comparing(AlgorithmChatContentEntity::getCreateTime).reversed())
                    .limit(1).findFirst();
            if (Boolean.FALSE.equals(optional.isPresent())) {
                log.info("==> 未能根据关键词匹配到历史记录");
                return Boolean.FALSE;
            }
            AlgorithmChatContentEntity entity = optional.get();
            //当前对话的用户query +  历史记录的对话结果
            AssistantChatV2PollingUpdateDTO pollingUpdate = new AssistantChatV2PollingUpdateDTO();
            pollingUpdate.setDialogueId(entity.getId());
            pollingUpdate.setUserId(RequestContextHolder.getUserId());
            DialogueResultV2VO dialogResult = contentService.pollingUpdate(pollingUpdate);
            List<DialogueFlowResult> outputList = dialogResult.getOutputList();
            if (CollUtil.isEmpty(outputList)) {
                log.info("==> 获取第一条对话的返回信息失败.");
                return Boolean.FALSE;
            }
            Optional<DialogueFlowResult> dialogueOptional = outputList.stream().filter(dialogueFlowResult ->
                    FlowResultTypeEnum.TEXT_MODEL.getType().equals(dialogueFlowResult.getResultType())).findFirst();
            if (Boolean.FALSE.equals(dialogueOptional.isPresent())) {
                log.info("==> 获取第一条对话的返回大模型文本回答失败.");
                return Boolean.FALSE;
            }
            DialogueFlowResult dialogueFlowResult = dialogueOptional.get();
            String[] originalContent = createOutlineDTO.getContent();
            List<String> contentList = Objects.isNull(originalContent)
                    ? new ArrayList<>() : Arrays.stream(originalContent).collect(Collectors.toCollection(ArrayList::new));
            if (contentList.isEmpty()) {
                createOutlineDTO.setContent(new String[]{dialogueFlowResult.getOutContent()});
            } else {
                String content = contentList.get(0) + StringUtils.SPACE + dialogueFlowResult.getOutContent();
                createOutlineDTO.setContent(new String[]{content});
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("==> fromHistoryToOutLine fail, | e:", e);
            return Boolean.FALSE;
        }
    }
}
