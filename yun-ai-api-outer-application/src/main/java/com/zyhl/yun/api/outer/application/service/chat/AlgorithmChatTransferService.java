package com.zyhl.yun.api.outer.application.service.chat;

import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.FileUploadVO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmTransferDTO;

/**
 * 对话结果转储服务类
 *
 * <AUTHOR>
 */
public interface AlgorithmChatTransferService {
    /**
     * 对话结果转存
     * @param dto 转换dto
     * @return 文件上传信息
     */
    FileUploadVO transfer(AlgorithmTransferDTO dto);
}
