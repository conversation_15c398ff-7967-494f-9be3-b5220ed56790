package com.zyhl.yun.api.outer.application.convertor.dto.dynamic;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zyhl.hcy.yun.ai.common.base.enums.YunAiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeBase;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeMailPreviewInfoResultVO;
import com.zyhl.yun.api.outer.application.vo.knowledge.PersonalKnowledgeMailPreviewInfoVO;
import com.zyhl.yun.api.outer.config.KnowledgePersonalProperties;
import com.zyhl.yun.api.outer.domain.entity.knowledge.PersonalKnowledgeMailPreviewInfoEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.domain.vo.knowledge.KnowledgeBaseVO;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeProfilePhoto;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeProfilePhotoVO;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;

/**
 * className:PesonalKnowledgeConvertor
 *
 * <AUTHOR>
 * @date 2025/04/15
 */
@Mapper(componentModel = "spring")
public interface PesonalKnowledgeConvertor {

    ObjectMapper objectMapper = new ObjectMapper();

    /**
     * entity转换为vo
     *
     * @param entity 参数
     * @return vo
     */
    @Mapping(source = "profilePhoto", target = "profilePhoto", qualifiedByName = "parsePersonalKnowledgeProfilePhoto")
    @Mapping(source = "id", target = "baseId", qualifiedByName = "convertId")
    @Mapping(source = "selected", target = "selected", qualifiedByName = "convertSelected")
    @Mapping(source = "createdBy", target = "createBy", qualifiedByName = "convertCreatedBy")
    @Mapping(source = "updatedBy", target = "updateBy", qualifiedByName = "convertUpdatedBy")
    @Mapping(source = "createTime", target = "createdAt", qualifiedByName = "convertCreateTime")
    @Mapping(source = "updateTime", target = "updatedAt", qualifiedByName = "convertUpdateTime")
    KnowledgeBase toKnowledgeBase(UserKnowledgeEntity entity);

    /**
     * 转换更新者
     * 
     * @param updatedBy 创建者
     * @return 更新者
     */
    @Named("convertUpdatedBy")
    default String convertUpdatedBy(String updatedBy) {
        if (ObjectUtil.isEmpty(updatedBy)) {
            return null;
        }
        return updatedBy;
    }

    /**
     * 转换创建者
     * 
     * @param createdBy 创建者
     * @return 创建者
     */
    @Named("convertCreatedBy")
    default String convertCreatedBy(String createdBy) {
        if (ObjectUtil.isEmpty(createdBy)) {
            return null;
        }
        return createdBy;
    }

    /**
     * 转换更新时间
     * 
     * @param updateTime 更新时间
     * @return 更新时间
     */
    @Named("convertUpdateTime")
    default String convertUpdateTime(Date updateTime) {
        if (ObjectUtil.isEmpty(updateTime)) {
            return null;
        }
        // 返回将时间格式转换为字符串
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        return sdf.format(updateTime);
    }

    /**
     * 转换创建时间
     * 
     * @param updateTime 创建时间
     * @return 创建时间
     */
    @Named("convertCreateTime")
    default String convertCreateTime(Date createTime) {
        if (ObjectUtil.isEmpty(createTime)) {
            return null;
        }
        // 返回将时间格式转换为字符串
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        return sdf.format(createTime);
    }

    /**
     * Long转换为String
     * @param id 入参id
     * @return 字符串id
     */
    @Named("convertId")
    default String convertId(Long id) {
        if (ObjectUtil.isEmpty(id)) {
            return null;
        }
        return String.valueOf(id);
    }

    /**
     * String转换为PersonalKnowledgeProfilePhoto
     *
     * @param data 参数
     * @return PatternConfig
     */
    @Named("parsePersonalKnowledgeProfilePhoto")
    default PersonalKnowledgeProfilePhoto parsePersonalKnowledgeProfilePhoto(String data) {
        if (ObjectUtil.isEmpty(data)) {
            return null;
        }
        try {
            return JSONUtil.toBean(data, PersonalKnowledgeProfilePhoto.class);
        } catch (Exception e) {
            throw new YunAiBusinessException(
                    YunAiCommonResultCode.ERROR_SERVER_INTERNAL.getResultCode(),
                    "json to obj error ==>" + e.getMessage());
        }
    }

	/**
	 * 转换知识库列表
	 * 
	 * @param entityList 实体列表
	 * @return 知识库列表
	 */
    @Mapping(source = "id", target = "baseId", qualifiedByName = "convertId")
    @Mapping(source = "selected", target = "selected", qualifiedByName = "convertSelected")
    @Mapping(source = "profilePhoto", target = "profilePhoto", qualifiedByName = "parsePersonalKnowledgeProfilePhoto")
    @Mapping(source = "createdBy", target = "createBy", qualifiedByName = "convertCreatedBy")
    @Mapping(source = "updatedBy", target = "updateBy", qualifiedByName = "convertUpdatedBy")
    @Mapping(source = "createTime", target = "createdAt", qualifiedByName = "convertCreateTime")
    @Mapping(source = "updateTime", target = "updatedAt", qualifiedByName = "convertUpdateTime")
    List<KnowledgeBase> toKnowledgeBaseList(List<UserKnowledgeEntity> entityList);

    /**
     * 转换选项
     * @param selected 选项值
     * @return 选项值
     */
    @Named("convertSelected")
    default String convertSelected(Integer selected) {
        if (ObjectUtil.isEmpty(selected)) {
            return null;
        }

        return selected == 1 ? "true" : "false";
    }

    @Mapping(source = "profilePhoto", target = "profilePhoto", qualifiedByName = "parsePersonalKnowledgeProfilePhoto")
    @Mapping(source = "id", target = "baseId", qualifiedByName = "convertId")
    @Mapping(source = "selected", target = "selected", qualifiedByName = "convertSelected")
    @Mapping(source = "createdBy", target = "createBy", qualifiedByName = "convertCreatedBy")
    @Mapping(source = "updatedBy", target = "updateBy", qualifiedByName = "convertUpdatedBy")
    @Mapping(source = "createTime", target = "createdAt", qualifiedByName = "convertCreateTime")
    @Mapping(source = "updateTime", target = "updatedAt", qualifiedByName = "convertUpdateTime")
    KnowledgeBaseVO toKnowledgeBaseVO(UserKnowledgeEntity entity);

    List<PersonalKnowledgeProfilePhotoVO> toPersonalKnowledgeProfilePhotoVOList
            (List<KnowledgePersonalProperties.ProfilePhoto> profilePhotoList);

     PersonalKnowledgeMailPreviewInfoVO toVO(PersonalKnowledgeMailPreviewInfoEntity entity);

    /**
     * 
     * @param entity
     * @return
     */
    default KnowledgeMailPreviewInfoResultVO toResultVO(PersonalKnowledgeMailPreviewInfoEntity entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        KnowledgeMailPreviewInfoResultVO resultVO = new KnowledgeMailPreviewInfoResultVO();
        resultVO.setMail(toVO(entity));
        return resultVO;
    }
}
