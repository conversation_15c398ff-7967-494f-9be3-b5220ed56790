package com.zyhl.yun.api.outer.application.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/7/30 11:25
 * @desc TOPIC_LOCAL_ALGORITHM_AUTHORIZE AI授权报名MQ配置
 */
@Data
@Configuration
public class TopicLocalAlgorithmAuthorizeMqConfig {

	/**
	 * AI授权报名MQ 事件类型
	 */
	public static final String TOPIC_LOCAL_ALGORITHM_AUTHORIZE_EVENT_TYPE = "algorithm.local.authorize";

	/**
	 * AI授权报名MQ 消息id前缀
	 */
	public static final String TOPIC_LOCAL_ALGORITHM_AUTHORIZE_MESSAGE_ID_PREFIX = "topicLocalAlgorithmAuthorize_";

	/**
	 * AI视频分析报名MQ 事件类型
	 */
	public static final String TOPIC_LOCAL_ALGORITHM_VIDEO_SERIES_QUERY_EVENT_TYPE = "algorithm.local.video.series.query";

	/**
	 * AI视频分析报名MQ 消息id前缀
 	 */
	public static final String TOPIC_LOCAL_ALGORITHM_VIDEO_SERIES_QUERY_MESSAGE_ID_PREFIX = "topicLocalAlgorithmVideoSeriesQuery_";

	@Value("${rocketmq.producer.topic-local-algorithm-authorize.topic}")
	private String topic;

	@Value("${rocketmq.producer.topic-local-algorithm-authorize.tag}")
	private String tag;


	@Value("${rocketmq.producer.topic-local-algorithm-video-series-query.topic:TOPIC_LOCAL_ALGORITHM_VIDEO-SERIES-QUERY}")
    private  String topicVideoSeriesQueryTopic;


	@Value("${rocketmq.producer.topic-local-algorithm-video-series-query.tag:*}")
	private String topicVideoSeriesQuerytag;

}
