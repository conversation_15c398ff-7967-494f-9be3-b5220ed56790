package com.zyhl.yun.api.outer.application.assembler;

import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;

import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueFlowResultVO;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;

/**
 * 对话流式结果-类转换器
 * 
 * @Author: liu<PERSON><PERSON>wen
 */
@Mapper(componentModel = "spring", injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public abstract class ChatFlowResultAssembler {

	public abstract DialogueFlowResult getFlowResult(DialogueFlowResultVO vo);

	public abstract DialogueFlowResultVO getFlowResultVO(DialogueFlowResult output);

}
