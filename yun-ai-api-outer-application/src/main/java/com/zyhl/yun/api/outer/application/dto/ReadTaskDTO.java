package com.zyhl.yun.api.outer.application.dto;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.File;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description}
 * <p>
 *
 * <AUTHOR>
 * @since 4月15 2025
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class ReadTaskDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 1751749849654474686L;
    /**
     * 来源渠道
     */
    @NotBlank(message = "sourceChannel不能为空")
    private String sourceChannel;
    /**
     * 个人云文档信息
     */
    private File file;
    /**
     * 任务Id
     */
    //@NotBlank(message = "taskId不能为空")
    @Pattern(regexp = "^\\d*$", message = "任务Id只能包含数字")
    private String taskId;

	public ReadTaskDTO(String userId, String taskId) {
		this.setUserId(userId);
		this.taskId = taskId;
	}
	
    /**
     * 校验用户id
     *
     * <AUTHOR>
     * @date 2025-4-20 14:36
     */
    public void validUserId() {
        String userId = RequestContextHolder.getUserId();
        if (StringUtils.isBlank(userId) && StringUtils.isBlank(this.getUserId())) {
            log.warn("==> 快速阅读任务，userId参数为空.");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_FORBIDDEN);
        } else if (StringUtils.isEmpty(this.getUserId())) {
            this.setUserId(userId);
        } else if (StringUtils.isEmpty(userId)) {
            log.info("==> 快速阅读任务，请求头未带token解析不到userId, 根据Body获取userId{}", this.getUserId());
            userId = this.getUserId();
            RequestContextHolder.setUserId(userId);
        }
    }

}
