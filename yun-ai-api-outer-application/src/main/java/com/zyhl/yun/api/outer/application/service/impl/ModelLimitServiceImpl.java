package com.zyhl.yun.api.outer.application.service.impl;

import com.zyhl.yun.api.outer.application.service.ModelLimitService;
import com.zyhl.yun.api.outer.repository.ModelLimitRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ModelLimitServiceImpl implements ModelLimitService {


    private final ModelLimitRepository modelLimitRepository;

    /**
     * 查询模型限制
     * @return 模型限制
     */
    @Override
    public Map<String, Object> queryModelLimit() {
        return modelLimitRepository.queryModelLimit();
    }

}
