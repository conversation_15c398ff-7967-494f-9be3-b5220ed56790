package com.zyhl.yun.api.outer.application.service.knowledge.impl;

import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.enums.NumberEnum;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeShareAddReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeShareGetReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.KnowledgeShareService;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeInviteEntity;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeInviteEnum;
import com.zyhl.yun.api.outer.repository.UserKnowledgeInviteRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * className:KnowledgeShareServiceImpl
 * description: 保存加入知识库实现类
 *
 * <AUTHOR>
 * @date 2025/04/12
 */
@Slf4j
@Service
public class KnowledgeShareServiceImpl implements KnowledgeShareService {

	@Resource
	private UserKnowledgeInviteRepository userKnowledgeInviteRepository;

	@Resource
	private UidGenerator uidGenerator;

	@Resource
	private UserKnowledgeRepository userKnowledgeRepository;

	@Override
	public void joinKnowledge(KnowledgeShareAddReqDTO dto) {

		Long baseId = Long.valueOf(dto.getBaseId());
		String userId = RequestContextHolder.getUserId();
		// 根据数据库id查询是否有该知识库
		UserKnowledgeEntity userKnowledgeEntity = userKnowledgeRepository.selectByKnowledgeId(baseId);
		if (null == userKnowledgeEntity) {
			log.info("joinKnowledge-知识库ID不存在，baseId:{}", baseId);
			throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_ID_NOT_EXIST.getResultCode(),
					"知识库ID不存在");
		}

		// 限制邀请人不能邀请自己加入自己的知识库
		if (userId.equals(userKnowledgeEntity.getUserId())) {
			throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_USERS_CANNOT_INVITE_THEMSELVES.getResultCode(),
					"用户不能加入自己的分享的知识库");
		}

		// 查询是否已经加入该知识库，若已加入则不新建记录
		if (userKnowledgeInviteRepository.count(baseId, userId) > NumberEnum.ZERO.getValue()) {
			return;
		}

		UserKnowledgeInviteEntity entity = assembleBySave(dto, userKnowledgeEntity.getUserId());
		userKnowledgeInviteRepository.save(entity);

	}

	@Override
	public Boolean get(KnowledgeShareGetReqDTO dto) {

		String userId = RequestContextHolder.getUserId();
		Long baseId = Long.valueOf(dto.getBaseId());
		// 先查询该知识库是否是本人创建的，是即返回true
		if (userKnowledgeRepository.count(baseId, userId) >= NumberEnum.ONE.getValue()) {
			return true;
		}
		// 查询是否已加入
		if (userKnowledgeInviteRepository.count(baseId, userId) >= NumberEnum.ONE.getValue()) {
			return true;
		}
		return false;

	}

	private UserKnowledgeInviteEntity assembleBySave(KnowledgeShareAddReqDTO dto, String inviteUserId) {
		UserKnowledgeInviteEntity entity = new UserKnowledgeInviteEntity();
		entity.setId(uidGenerator.getUID());
		entity.setUserId(RequestContextHolder.getUserId());
		if (StringUtils.isNotBlank(inviteUserId)) {
			entity.setInviteUserId(inviteUserId);
		}
		entity.setKnowledgeId(Long.valueOf(dto.getBaseId()));
		entity.setStatus(KnowledgeInviteEnum.ENABLE.getStatus());
		entity.setCreateTime(new Date());
		entity.setUpdateTime(new Date());
		return entity;
	}

}
