package com.zyhl.yun.api.outer.application.chatv2.service;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.dto.AiAssistantStatusReqDTO;
import com.zyhl.yun.api.outer.application.dto.LastDialogueInfoDTO;
import com.zyhl.yun.api.outer.application.dto.ShareBatchGetDTO;
import com.zyhl.yun.api.outer.application.vo.ChatStatusVO;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.vo.chat.ShareDialogueResultVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import java.util.List;

/**
 * 对话内容操作类
 *
 * <AUTHOR>
 */
public interface ChatContentService {

    /**
     * 新增对话内容，只是保存输入的内容，没有结果
     *
     * @param handleDTO  dto对象
     * @param chatStatus 对话状态
     * @param taskId     任务id
     * @param modelCode  模型编码
     * @return 对话内容实体
     */
    AlgorithmChatContentEntity add(ChatAddHandleDTO handleDTO, Integer chatStatus, Long taskId, String modelCode);

    /**
     * 新增成功对话，输入的内容和结果一起
     *
     * @param handleDTO      dto对象
     * @param modelCode      模型编码
     * @param outContentType 输出文本类型 OutContentTypeEnum
     * @return
     */
    AlgorithmChatContentEntity addSuccess(ChatAddHandleDTO handleDTO, String modelCode, Integer outContentType);

    /**
     * 新增失败对话，输入的内容和结果一起
     *
     * @param handleDTO      dto对象
     * @param modelCode      模型编码
     * @param outContentType 输出文本类型 OutContentTypeEnum
     * @return
     */
    AlgorithmChatContentEntity addFail(ChatAddHandleDTO handleDTO, String modelCode, Integer outContentType);
    
    /**
     * 保存tidb结果（输入和结果数据）
     *
     * @param handleDTO 用户输入对象
     * @return com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity
     * @Author: WeiJingKun
     */
    AlgorithmChatContentEntity saveAll(ChatAddHandleDTO handleDTO);

    /**
     * 获取对话状态
     *
     * @param dto 助手状态查询参数
     * @return com.zyhl.yun.api.outer.domain.vo.ChatStatusVO
     */
    ChatStatusVO getChatStatus(AiAssistantStatusReqDTO dto);

    /**
     * 批量获取对话内容
     *
     * @param dto 请求参数
     * @return vo
     */
    List<ShareDialogueResultVO> shareBatchGet(ShareBatchGetDTO dto);

	/**
	 * 获取上次对话信息
	 *
	 * @param dialogueId 对话id
	 * @param userId     用户id
	 * @return 上次对话信息DTO
	 */
	LastDialogueInfoDTO getLastDialogueInfo(String dialogueId, String userId);

	/**
	 * 更新失败对话状态
	 * @param dialogueId 对话id
	 */
	boolean updateChatFail(Long dialogueId);

	/**
	 * 获取最新一条意图对话
	 * @param condition
	 * @return
	 */
	AlgorithmChatContentEntity getLastIntentionDialogue(AlgorithmChatContentEntity condition);

	/**
	 * 获取对话id by userId+sessionId+意图信息
	 * 
	 * @param userId        用户id
	 * @param sessionId     会话id
	 * @param mainIntention 主意图信息
	 * @param querySize     查询数量
	 * @return 对话id
	 */
	String getDialogueIdByIntention(String userId, Long sessionId, IntentionInfo mainIntention, int querySize);
}
