package com.zyhl.yun.api.outer.application.chatv2.hanlde.event.completeimpl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.assembler.ChatFlowResultAssembler;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueAttachmentDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueTextToolSettingDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueToolSettingDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.event.AbstractCompleteCallbackEvent;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.pojo.CompleteEvent;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatContentService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.service.openapi.OpenapiLingxiCommonService;
import com.zyhl.yun.api.outer.application.chatv2.service.openapi.OpenapiLingxiMeetingAfterService;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters.ExtInfoParam;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueIntentionOutput;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.AiTextResultVersionEnum;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 下一个执行会议发邮件处理器事件
 */
@Slf4j
@Component
public class OutlineToPptCallbackEvent extends AbstractCompleteCallbackEvent {

	@Resource
	private DataSaveService dataSaveService;
	@Resource
	private ChatContentService chatContentService;
	@Resource
	private UidGenerator uidGenerator;
	@Lazy
	@Resource
	private OpenapiLingxiMeetingAfterService openapiLingxiMeetingAfterService;
	@Resource
	private OpenapiLingxiCommonService openapiLingxiCommonService;
	@Resource
	private ChatFlowResultAssembler chatFlowResultAssembler;
	@Resource
	private ChatTextToolBusinessConfig chatTextToolBusinessConfig;

	@Override
	public void complete(CompleteEvent data) {
		log.info("下一个执行大纲直接生成ppt处理器事件");
		try {
			SseEventListener event = data.getEventListener();
			ChatAddHandleDTO handleDTO = data.getHandleDTO();
			ChatAddRespVO respVO = handleDTO.getRespVO();
			List<DialogueFlowResult> outputList = new ArrayList<>();
			if (CollUtil.isNotEmpty(event.getBeforeOutputList())) {
				// 追加输出之前的文案
				outputList.addAll(event.getBeforeOutputList());
			}
			respVO.getFlowResult().setIndex(0);
			if (StringUtils.isNotEmpty(event.getAppendBeforeOutContent())) {
				// 不为空，是追加outContent
				respVO.getFlowResult().setOutContent(event.getAppendBeforeOutContent() + data.getOutContent());
			} else {
				respVO.getFlowResult().setOutContent(data.getOutContent());
			}
			respVO.getFlowResult().setTitle(event.getCallBackTitle());
			respVO.getFlowResult().setReasoningContent(data.getReasoningContent());
			outputList.add(chatFlowResultAssembler.getFlowResult(respVO.getFlowResult()));
			// 设置hbase
			AiTextResultRespParameters result = AiTextResultRespParameters.builder()
					.version(AiTextResultVersionEnum.V2.getVersion())
					.extInfoParam(
							ExtInfoParam.builder().dialogueFlowResult(event.getCallBackDialogueFlowResult()).build())
					.outputCommand(new DialogueIntentionOutput(respVO.getOutputCommand().getCommand(),
							respVO.getOutputCommand().getSubCommand(), respVO.getOutputCommand().getArgumentMap()))
					.outputList(outputList).build();

			// 保存hbase
			dataSaveService.saveHbaseAllChatResult(handleDTO, result);

			// 保存tidb
			dataSaveService.addSuccessAndModelCode(handleDTO, event.getModelCode(), OutContentTypeEnum.TEXT);

			// 会议通知邮件信息
			MailInfoVO mailInfo = null;
			AlgorithmChatContentEntity lastMailDialogueInfo = openapiLingxiCommonService.getLastIntentionDialogue(
					handleDTO.getReqDTO().getUserId(), handleDTO.getSessionId(),
					String.valueOf(handleDTO.getDialogueId()));
			if (null != lastMailDialogueInfo) {
				mailInfo = openapiLingxiCommonService.getMailInfoResult(lastMailDialogueInfo);
			}
			// 会议标题
			String meetingTitle = (null != mailInfo
					? chatTextToolBusinessConfig.getIntelligentMeeting().removeTitlePrefix(mailInfo.getTitle())
					: handleDTO.getInputInfoDTO().getDialogue());
			// 继续大纲生成ppt
			long lastDialogueId = handleDTO.getDialogueId().longValue();
			// 新对话id
			handleDTO.setDialogueId(uidGenerator.getUID());
			DialogueAttachmentDTO attachment = new DialogueAttachmentDTO();
			attachment.setAttachmentTypeList(Collections.singletonList(ResourceTypeEnum.DIALOGUE.getType()));
			attachment.setDialogueIdList(Collections.singletonList(String.valueOf(lastDialogueId)));
			handleDTO.getInputInfoDTO().setAttachment(attachment);
			if (StringUtils.isNotEmpty(meetingTitle)) {
				handleDTO.getInputInfoDTO().setDialogue("基于大纲和所选模板生成PPT，会议内容是" + meetingTitle);
			} else {
				handleDTO.getInputInfoDTO().setDialogue("基于大纲和所选模板生成PPT");
			}
			// 重新设置响应对象
			handleDTO.setRespVO(new ChatAddRespVO());
			DialogueToolSettingDTO toolSettingDTO = new DialogueToolSettingDTO();
			DialogueTextToolSettingDTO textToolSettingDTO = new DialogueTextToolSettingDTO();
			// ppt制作
			textToolSettingDTO.setEnablePptMake(true);
			toolSettingDTO.setTextToolSetting(textToolSettingDTO);
			handleDTO.getInputInfoDTO().setToolSetting(toolSettingDTO);
			try {
				// 需要休眠1秒，控制ppt生成顺序问题
				Thread.sleep(1000);
			} catch (Exception e) {
				log.error("休眠失败:", e);
			}
			openapiLingxiMeetingAfterService.run(handleDTO);
		} catch (YunAiBusinessException e) {
			data.getEventListener().dialogueFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			data.getEventListener().dialogueFail(AiResultCode.CODE_9999);
		}
	}

}
