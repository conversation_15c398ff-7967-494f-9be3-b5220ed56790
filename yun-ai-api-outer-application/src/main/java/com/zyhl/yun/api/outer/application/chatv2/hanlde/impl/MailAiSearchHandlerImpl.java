package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import cn.hutool.core.collection.CollUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.assembler.ChatFlowResultAssembler;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatDialogueSearchService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.dto.ContinueTextSseDTO;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.application.service.chat.DialogueIntentionService;
import com.zyhl.yun.api.outer.application.util.IntentionConvertUtils;
import com.zyhl.yun.api.outer.config.AllNetworkSearchProperties;
import com.zyhl.yun.api.outer.config.MailaiSearchProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.IntentionRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.domain.vo.chat.search.SearchInfo;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.search.LeadCopyTypeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.IntentEntityVO;
import com.zyhl.yun.api.outer.vo.KeyValueVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 邮箱界面智能搜索
 * 
 * <AUTHOR>
 * @date 2025-04-19
 *
 */
@Slf4j
@Component
public class MailAiSearchHandlerImpl extends AbstractChatAddV2Handler {

	/**
	 * 当前执行顺序
	 */
	private ExecuteSort thisExecuteSort = ExecuteSort.MAIL_AI_SEARCH;

	@Resource
	private ChatDialogueSearchService chatDialogueSearchService;

	@Resource
	private MailaiSearchProperties mailaiSearchProperties;

	@Resource
	private DialogueIntentionService intentionService;

	@Resource
	private TextModelTextSseHandlerImpl textModelTextSseHandlerImpl;

	@Resource
	private DataSaveService dataSaveService;

	@Resource
	private ChatFlowResultAssembler chatFlowResultAssembler;
	@Resource
	private AllNetworkSearchProperties allNetworkSearchProperties;

	@Resource
	private ChatConfigServiceDomainService chatConfigServiceDomainService;

	@Resource
	private IntentionConvertUtils intentionConvertUtils;


	@Override
	public void afterPropertiesSet() throws Exception {
    	//支持的业务初始化
    	List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
    	this.setBusinessTypes(thisBusinessTypes);
    }

	@Override
	public int order() {
		return thisExecuteSort.getSort();
	}

	@Override
	public boolean execute(ChatAddHandleDTO handleDTO) {
		return true;
	}

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {

		try {
			boolean enableAiSearch = handleDTO.getInputInfoDTO().isEnableAiSearch();
			String sourceChannel = handleDTO.getReqDTO().getSourceChannel();
			boolean isMail = SourceChannelsProperties.isMailChannel(sourceChannel);
			if (!enableAiSearch || !isMail) {
				// 非邮箱AI搜索的不进入这个handler处理，直接返回，去执行下一个handler
				return true;
			}

			log.info("进入{}", thisExecuteSort.getDesc());

			long t1 = System.currentTimeMillis();
			List<String> searchIntention = getConfigSearchIntention(handleDTO.getReqDTO().getSourceChannel());

			// 处理意图识别和前缀
			DialogueIntentionVO intentionVO = processIntentionAndPrefix(handleDTO);
			long t2 = System.currentTimeMillis();
			String oldIntention = JsonUtil.toJson(intentionVO);
			boolean isTransferIntention = mailaiSearchProperties.isTransferIntention();
			boolean isValid = false;
			if (isTransferIntention) {
				if (!Objects.isNull(intentionVO)) {
					String mainIntentionCode = DialogueIntentionVO.getMainIntentionCode(intentionVO);
					if (!DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode().equals(mainIntentionCode)) {
						isValid = true;
					}
				}
			} else{
				// 过滤意图和验证
				isValid = filterAndValidateIntention(intentionVO, searchIntention);

			}
			String newIntention = JsonUtil.toJson(intentionVO);
			long t3 = System.currentTimeMillis();
			log.info("MailAiSearchHandlerImpl-run,isTransferIntention={},isTransferIntention={},processIntentionAndPrefix-userTime：{}ms, filterAndValidateIntention-userTime：{}ms, oldIntention:{},newIntention:{}", isTransferIntention, isValid, t2-t1, t3 - t2, oldIntention, newIntention);
			if (!isValid) {
				// 没有提取出搜索意图，则执行文本生成文本处理
				executeTextGenerateText(handleDTO);
				log.info("MailAiSearchHandlerImpl-run-intention-isNotValid，executeTextGenerateText,userTime:{}ms", System.currentTimeMillis() - t3);
				return false;
			}

			// 执行搜索
			handleDTO.setIntentionVO(intentionVO);
			chatDialogueSearchService.mailAiSearchIntentionHandle(handleDTO);
			boolean mailAutoAllNetworkSearch = true;
			if (mailaiSearchProperties.isAutoAllNetworkSearch()) {
				mailAutoAllNetworkSearch = chatDialogueSearchService.mailAutoAllNetworkSearch(handleDTO);
			}
			long t4 = System.currentTimeMillis();
			int resultSize = 0;
			if (mailAutoAllNetworkSearch) {
				List<SearchInfo> searchInfoList = handleDTO.getRespVO().getFlowResult().getSearchInfoList();
				if (CollUtil.isEmpty(searchInfoList)) {
					// 搜索失败，则调用textModelTextSseHandlerImpl.run()进行文生文
					executeTextGenerateText(handleDTO);
				} else {
					resultSize = searchInfoList.size();
					handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(handleDTO.getRespVO()));
					// 保存搜索结果到tidb和hbase
					saveTidbAndHbaseResult(handleDTO);
				}

			}

			log.info("MailAiSearchHandlerImpl-run, resultSize:{}, search-userTime:{}ms, processResult-useTime:{}ms, saveResult-useTime:{}ms",
					resultSize , t4 - t3, System.currentTimeMillis() - t4);
		} catch (Exception e) {
			log.error("MailAiSearchHandlerImpl-run error:", e);
			throw e;
		}

		//执行该搜索后就停止了，不会再进行其他handler
		return false;
	}


	/**
	 * 处理意图识别和前缀处理
	 *
	 * @param handleDTO 用户输入对象
	 * @return 意图识别结果，如果无法识别则返回null
	 */
	private DialogueIntentionVO processIntentionAndPrefix(ChatAddHandleDTO handleDTO) {
		DialogueIntentionVO intentionVO = handleDTO.getIntentionVO();

		// 可能前面意图识别有问题，做一次意图识别重试
		if (Objects.isNull(intentionVO)) {
			intentionVO = chatDialogueSearchService.getIntentionVO(handleDTO.getReqDTO(), handleDTO.getRespVO());
			handleMailChannelIntentionConvert(intentionVO, handleDTO);
		}

//		resetDialogue(handleDTO);

		return intentionVO;
	}

	/**
	 * 处理邮箱渠道的意图转换
	 *
	 * @param intention 意图识别结果
	 * @param handleDTO 处理对象
	 */
	private void handleMailChannelIntentionConvert(DialogueIntentionVO intention, ChatAddHandleDTO handleDTO) {
		//如果是邮箱渠道确实chat的要进行意图转换
		ChatAddReqDTO reqDto = handleDTO.getReqDTO();
		if (intention != null && reqDto != null) {
			String applicationType = reqDto.getApplicationType();
			String channel = reqDto.getSourceChannel();
			if (SourceChannelsProperties.isMailChannel(channel) && ApplicationTypeEnum.CHAT.getCode().equals(applicationType)) {
				boolean isTransferIntention = mailaiSearchProperties.isTransferIntention();
				if (isTransferIntention) {
					//云邮意图转换
					String businessType = handleDTO.getBusinessType();
					DialogueInputInfoDTO dialogueInput = reqDto.getDialogueInput();
					if (dialogueInput != null) {
						String userInput = dialogueInput.getDialogue();
						boolean enableAiSearch = dialogueInput.isEnableAiSearch();
						intentionConvertUtils.convertIntention(intention, channel, businessType, enableAiSearch, userInput);
					}
				}
			}
		}
	}
	/**
	 * 获取配置的搜索意图列表
	 *
	 * @param sourceChannel 来源渠道
	 * @return 搜索意图列表
	 */
	private List<String> getConfigSearchIntention(String sourceChannel) {
		List<String> searchIntention = new ArrayList<>();
		Map<String, List<String>> searchSourceChannelIntention = mailaiSearchProperties.getSearchSourceChannelIntention();

		// 先根据sourceChannel获取特定渠道的搜索意图
		if (CollUtil.isNotEmpty(searchSourceChannelIntention) && StringUtils.isNotBlank(sourceChannel)) {
			searchIntention = searchSourceChannelIntention.get(sourceChannel);
		}

		// 如果仍然没有获取到，则使用全局搜索意图配置
		if (CollUtil.isEmpty(searchIntention)) {
			searchIntention = mailaiSearchProperties.getSearchIntention();
		}

		return searchIntention;
	}

	/**
	 * 过滤和验证意图
	 *
	 * @param intentionVO 意图识别结果
	 * @return 过滤后的意图是否有效
	 */
	private boolean filterAndValidateIntention(DialogueIntentionVO intentionVO, List<String> cofSearchIntention) {
		if (Objects.isNull(intentionVO) || CollUtil.isEmpty(intentionVO.getIntentionInfoList())) {
			return false;
		}

		// 获取搜索意图列表
		final List<String> searchIntention = cofSearchIntention;

		// 检查主意图是否为018综合搜索，如果是则过滤掉其他意图
		String mainIntentionCode = DialogueIntentionVO.getMainIntentionCode(intentionVO);
		if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode().equals(mainIntentionCode)
				&& intentionVO.getIntentionInfoList().size() > 1) {
			// 记录过滤前的意图
			String beforeFilter = JsonUtil.toJson(intentionVO.getIntentionInfoList());

			// 只保留综合搜索意图
			List<DialogueIntentionVO.IntentionInfo> comprehensiveSearchList = new ArrayList<>();
			for (DialogueIntentionVO.IntentionInfo intentionInfo : intentionVO.getIntentionInfoList()) {
				if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode().equals(intentionInfo.getIntention())) {
					comprehensiveSearchList.add(intentionInfo);
				}
			}
			intentionVO.setIntentionInfoList(comprehensiveSearchList);

			// 记录过滤后的意图
			String afterFilter = JsonUtil.toJson(intentionVO.getIntentionInfoList());
			log.info("主意图是018综合搜索，过滤掉其他意图 - 过滤前: {}, 过滤后: {}", beforeFilter, afterFilter);
		}

		// 检查意图是否都在配置列表中，如果不在则转换为综合搜索
		checkAndConvertIntentions(intentionVO, searchIntention);

		// 安全地过滤意图列表，避免对不可修改的列表调用removeIf
		List<DialogueIntentionVO.IntentionInfo> intentionInfoList = intentionVO.getIntentionInfoList();
		List<DialogueIntentionVO.IntentionInfo> filteredList = new ArrayList<>();

		for (DialogueIntentionVO.IntentionInfo intentionInfo : intentionInfoList) {
			if (searchIntention.contains(intentionInfo.getIntention())) {
				filteredList.add(intentionInfo);
			}
		}

		intentionVO.setIntentionInfoList(filteredList);

		// 如果只有一个综合搜，则移除掉非资源搜的子意图
		if (CollUtil.isNotEmpty(intentionVO.getIntentionInfoList()) && intentionVO.getIntentionInfoList().size() == 1
				&& DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode().equals(intentionService.getIntentionCode(intentionVO))) {
			List<DialogueIntentionVO.IntentionInfo> subIntentions = intentionVO.getIntentionInfoList().get(0).getSubIntentions();
			if (CollUtil.isNotEmpty(subIntentions)) {
				subIntentions.removeIf(intentionInfo -> !searchIntention.contains(intentionInfo.getIntention()));
			} else {
				// 如果是综合搜索但没有子意图，尝试转换意图
				transformIntention(intentionVO, cofSearchIntention);
			}
		}

		// 过滤后意图都没有了，或者只剩下一个意图但这个意图是综合搜且没有子意图，则返回false
		return !(CollUtil.isEmpty(intentionVO.getIntentionInfoList())
				|| (intentionVO.getIntentionInfoList().size() == 1
				&& DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode().equals(intentionService.getIntentionCode(intentionVO))
				&& CollUtil.isEmpty(intentionVO.getIntentionInfoList().get(0).getSubIntentions())));
	}

	/**
	 * 如果是综合搜索，但没有子意图，需要提取metaDataList,将其转换成多个资源搜的子意图
	 * 012-搜图片，013-搜文档，014-搜视频，015-搜音频，016-搜文件夹，017-搜笔记，028-搜邮件，搜邮件附件
	 * 下条件搜索全部资源：
	 * 1、邮件主题、正文
	 * 2、邮件附件标题
	 * 3、云盘文档标题、正文
	 * 4、云盘图片内容
	 * 5、笔记标题、内容
	 * @param intentionVO 意图识别结果
	 * @param cofSearchIntention 配置的搜索意图列表
	 */
	private void transformIntention(DialogueIntentionVO intentionVO, List<String> cofSearchIntention) {
		// 判断是否为空或者没有意图信息
		if (Objects.isNull(intentionVO) || CollUtil.isEmpty(intentionVO.getIntentionInfoList())) {
			return;
		}

		// 获取主意图
		DialogueIntentionVO.IntentionInfo mainIntention = intentionVO.getIntentionInfoList().get(0);

		// 判断是否为综合搜索意图，且没有子意图
		if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode().equals(mainIntention.getIntention())
				&& CollUtil.isEmpty(mainIntention.getSubIntentions())) {

			// 获取实体列表
			List<IntentEntityVO> entityList = mainIntention.getEntityList();
			if (CollUtil.isEmpty(entityList)) {
				return;
			}

			// 从实体列表中提取关键词
			List<String> keywords = extractKeywords(entityList);
//			if (CollUtil.isEmpty(keywords)) {
//				return;
//			}

			// 创建子意图列表
			List<DialogueIntentionVO.IntentionInfo> subIntentions = new ArrayList<>();

			// 添加综合搜索意图实体
			addSearchSubIntention(subIntentions, keywords, entityList, cofSearchIntention);


			// 设置子意图列表
			if (CollUtil.isNotEmpty(subIntentions)) {
				mainIntention.setSubIntentions(subIntentions);
			}
		}
	}

	/**
	 * 从实体列表中提取关键词
	 *
	 * @param entityList 实体列表
	 * @return 关键词列表
	 */
	private List<String> extractKeywords(List<IntentEntityVO> entityList) {
		List<String> keywords = new ArrayList<>();

		for (IntentEntityVO entity : entityList) {
			// 提取metaDataList中的关键词
			if (CollUtil.isNotEmpty(entity.getMetaDataList())) {
				for (KeyValueVO keyValue : entity.getMetaDataList()) {
					if (CollUtil.isNotEmpty(keyValue.getValue())) {
						keywords.addAll(keyValue.getValue());
					}
				}
			}
		}
		// 去重
		return keywords.stream().distinct().collect(Collectors.toList());
	}

	/**
	 * 添加搜索子意图
	 *
	 * @param subIntentions 子意图列表
	 * @param keywords 关键词列表
	 * @param entityList 实体列表
	 * @param cofSearchIntention 搜索意图列表
	 */
	private void addSearchSubIntention(List<DialogueIntentionVO.IntentionInfo> subIntentions, List<String> keywords, List<IntentEntityVO> entityList, List<String> cofSearchIntention) {
//		if (CollUtil.isEmpty(keywords)) {
//			return;
//		}

		IntentEntityVO entity = entityList.get(0);


		// 为子意图创建新的实体列表

		// 获取搜索意图列表
		final List<String> searchIntention = cofSearchIntention;
		for (String intentionCode : searchIntention) {
			List<IntentEntityVO> newEntityList = new ArrayList<>();
			DialogueIntentionVO.IntentionInfo subIntention = new DialogueIntentionVO.IntentionInfo();
			subIntention.setIntention(intentionCode);
			subIntention.setScore(0.0);
			IntentEntityVO newEntity = new IntentEntityVO();
			// 根据不同的意图类型设置相应的字段
			//012-搜图片，013-搜文档，014-搜视频，015-搜音频，016-搜文件夹，017-搜笔记，028-搜邮件，搜邮件附件
//			 下条件搜索全部资源：
//			  1、邮件主题、正文
//			  2、邮件附件标题
//			  3、云盘文档标题、正文
//			  4、云盘图片内容
//			  5、笔记标题、内容
			copyNonNullFields(entity, newEntity);
			if (!CollUtil.isEmpty(entity.getMetaDataList())){
				newEntity.setMetaDataList(entity.getMetaDataList());
			}
			if (!CollUtil.isEmpty(keywords)) {
				newEntity.setImageNameList(keywords);
				newEntity.setTitleList(keywords);
				newEntity.setAttachmentList(keywords);
			}
			if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode().equals(intentionCode) ) {
				//统合搜跳过
				continue;
			}
//
//			else if(DialogueIntentionEnum.SEARCH_IMAGE.getCode().equals(intentionCode) ) {
//				//012-搜图片--名称
//				newEntity.setImageNameList(keywords);
//
//			} else if (DialogueIntentionEnum.SEARCH_DOCUMENT.getCode().equals(intentionCode)) {
//				//013-搜文档,直接用metadata
//			}else if (DialogueIntentionEnum.SEARCH_NOTE.getCode().equals(intentionCode)) {
//				//017-搜笔记,直接用metadata
//
//			} else if (DialogueIntentionEnum.SEARCH_MAIL.getCode().equals(intentionCode)) {
//				//028-搜邮件,邮件附件
//				newEntity.setTitleList(keywords);
//				newEntity.setAttachmentList(keywords);
//
//			}
			newEntityList.add(newEntity);
			subIntention.setEntityList(newEntityList);
			// 设置实体列表到子意图
			subIntentions.add(subIntention);

		}

	}



	/**
	 * 执行文本生成文本处理
	 *
	 * @param handleDTO 用户输入对象
	 */
	private void executeTextGenerateText(ChatAddHandleDTO handleDTO) {
		//暂时没有找到相关结果，建议调整一下问题重新搜索。另根据你刚才的问题，整理了一些可能对你有用的内容如下：
		String msg = mailaiSearchProperties.getNoResultPromptCopy();
		// 获取用户设置的模型，没有设置则使用默认模型
		ChatConfigEntity chatConfigEntity = chatConfigServiceDomainService.getUserCanUseModel(handleDTO.getReqDTO().getUserId(),
				RequestContextHolder.getPhoneNumber(), handleDTO.getAssistantEnum(), handleDTO.getBusinessType());
		boolean enableForceNetworkSearch = true;
		String intentionCode = handleDTO.getIntentionCode();
		if (!intentionCode.equals(DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode())) {
			enableForceNetworkSearch = handleDTO.getInputInfoDTO().isEnableForceNetworkSearch();
		}
		// 构建引导文案
		LeadCopyVO leadCopyVO = LeadCopyVO.builder()
				// 类型6：语义实体为空、搜索条件为空或搜索结果为空时的返回，仅返回提示文案promptCopy
				.type(LeadCopyTypeEnum.TYPE6.getCode())
				// 提示文案
				.promptCopy(msg).build();
		handleDTO.getRespVO().setLeadCopy(leadCopyVO);
		handleDTO.setTextGenerateTextIntention();
		handleDTO.setContinueTextSseDTO(new ContinueTextSseDTO(chatConfigEntity.getModelType(), enableForceNetworkSearch));
		handleDTO.setUseOrderTip(true);
		removeTextIntentionRecommend(handleDTO);
		textModelTextSseHandlerImpl.run(handleDTO);
	}

	/**
	 * 使用大模型回答时，不需要返回【大模型推荐】
	 * @Author: WeiJingKun
	 *
	 * @param handleDTO 用户输入对象
	 */
	private void removeTextIntentionRecommend(ChatAddHandleDTO handleDTO) {
		DialogueIntentionVO intentionVO = handleDTO.getIntentionVO();
		DialogueRecommendVO recommendVO = handleDTO.getRespVO().getRecommend();
		if (intentionVO != null) {
			List<IntentionRecommendVO> intentionList = recommendVO.getIntentionList();
			if (CollUtil.isNotEmpty(intentionList)) {
				// 搜索结果为空，使用大模型回答时，不需要返回【大模型推荐】
				List<IntentionRecommendVO> newIntentionList = intentionList.stream()
						// 过滤：仅保留非文本意图-000的数据
						.filter(result -> !result.getIntentionCommand().equals(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode()))
						.collect(Collectors.toList());
				recommendVO.setIntentionList(newIntentionList);
			}
		}
	}

	/**
	 * 检查意图是否都在配置列表中，如果都不在则转换为综合搜索
	 *
	 * @param intentionVO 意图识别结果
	 */
	private void checkAndConvertIntentions(DialogueIntentionVO intentionVO, List<String> searchIntention) {

		List<DialogueIntentionVO.IntentionInfo> intentionInfoList = intentionVO.getIntentionInfoList();

		// 检查是否有意图在配置列表中
		boolean hasValidIntention = intentionInfoList.stream()
				.anyMatch(intentionInfo -> searchIntention.contains(intentionInfo.getIntention()));

		// 如果没有任何意图在配置列表中，才需要转换为综合搜索
		if (!hasValidIntention) {
			// 记录转换前的意图
			String originalIntentions = JsonUtil.toJson(intentionInfoList);

			// 所有意图都不在配置列表中，需要将所有意图转换为018综合搜索
			DialogueIntentionVO.IntentionInfo comprehensiveIntention = new DialogueIntentionVO.IntentionInfo();
			comprehensiveIntention.setIntention(DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode());
			comprehensiveIntention.setScore(1.0);

			// 合并所有意图的metaDataList
			List<KeyValueVO> mergedMetaDataList = new ArrayList<>();

			// 遍历所有意图，收集metaDataList
			for (DialogueIntentionVO.IntentionInfo intentionInfo : intentionInfoList) {
				if (CollUtil.isNotEmpty(intentionInfo.getEntityList())) {
					for (IntentEntityVO entity : intentionInfo.getEntityList()) {
						if (CollUtil.isNotEmpty(entity.getMetaDataList())) {
							mergedMetaDataList.addAll(entity.getMetaDataList());
						}
					}
				}
			}

			// 创建新的实体对象，设置合并后的metaDataList
			IntentEntityVO newEntity = new IntentEntityVO();
			newEntity.setMetaDataList(mergedMetaDataList);

			// 从intentionInfoList的第一个元素获取其他字段
			if (CollUtil.isNotEmpty(intentionInfoList) && CollUtil.isNotEmpty(intentionInfoList.get(0).getEntityList())) {
				try {
					IntentEntityVO firstEntity = intentionInfoList.get(0).getEntityList().get(0);
					// 复制除metaDataList以外的所有字段，添加空值检查
					copyNonNullFields(firstEntity, newEntity);
				} catch (Exception e) {
					log.error("复制意图实体字段时发生异常", e);
				}
			}

			// 设置实体列表到综合搜索意图
			List<IntentEntityVO> newEntityList = new ArrayList<>();
			newEntityList.add(newEntity);
			comprehensiveIntention.setEntityList(newEntityList);

			// 替换原有意图列表 - 使用可修改的ArrayList而不是不可修改的singletonList
			List<DialogueIntentionVO.IntentionInfo> newIntentionList = new ArrayList<>();
			newIntentionList.add(comprehensiveIntention);
			intentionVO.setIntentionInfoList(newIntentionList);

			// 打印转换后的意图列表
			String convertedIntentions = JsonUtil.toJson(intentionVO.getIntentionInfoList());

			// 将转换前后的意图实体打印在一条日志中
			log.info("意图转换 - 所有意图都不在配置列表中，转换为综合搜索意图,转换前: {},转换后: {}", originalIntentions, convertedIntentions);
		} else {
			log.info("意图检查 - 有意图在配置列表中，不需要转换");
		}
	}

	/**
	 * 复制非空字段
	 *
	 * @param source 源实体
	 * @param target 目标实体
	 */
	private void copyNonNullFields(IntentEntityVO source, IntentEntityVO target) {
		// 不复制metaDataList，因为它已经在外部设置了
		Optional.ofNullable(source.getTimeList()).ifPresent(target::setTimeList);
		Optional.ofNullable(source.getPlaceList()).ifPresent(target::setPlaceList);
		Optional.ofNullable(source.getLabelList()).ifPresent(target::setLabelList);
		Optional.ofNullable(source.getImageNameList()).ifPresent(target::setImageNameList);
		Optional.ofNullable(source.getSuffixList()).ifPresent(target::setSuffixList);
		Optional.ofNullable(source.getRecipientList()).ifPresent(target::setRecipientList);
		Optional.ofNullable(source.getSenderList()).ifPresent(target::setSenderList);
		Optional.ofNullable(source.getEmailAddressList()).ifPresent(target::setEmailAddressList);
		Optional.ofNullable(source.getStatusList()).ifPresent(target::setStatusList);
		Optional.ofNullable(source.getTypeList()).ifPresent(target::setTypeList);
		Optional.ofNullable(source.getTitleList()).ifPresent(target::setTitleList);
		Optional.ofNullable(source.getAttachmentList()).ifPresent(target::setAttachmentList);
	}

	private void saveTidbAndHbaseResult(ChatAddHandleDTO handleDTO) {
		try {
			if(null != handleDTO.getHbaseResp()) {
				handleDTO.getHbaseResp().setOutputCommandVO(handleDTO.getIntentionVO());
			}
			// 保存hbase-所有对话结果
			DialogueFlowResult flowResult =chatFlowResultAssembler.getFlowResult(handleDTO.getRespVO().getFlowResult());
			if(null != handleDTO.getHbaseResp().getOutputList()) {
				handleDTO.getHbaseResp().getOutputList().add(flowResult);
			} else {
				List<DialogueFlowResult> outputList = new ArrayList<>();
				outputList.add(flowResult);
				handleDTO.getHbaseResp().setOutputList(outputList);
			}
			dataSaveService.saveHbaseAllChatResult(handleDTO, handleDTO.getHbaseResp());

			// 保存tidb-所有对话结果
			dataSaveService.saveTidbAllChatResult(handleDTO);
		} catch (Exception e) {
			log.error("云邮智能搜索-同步保存数据，execute-异常\n dialogueId：{}", handleDTO.getDialogueId(), e);
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_SERVER_UNAVAILABLE);
		}
	}

}
