package com.zyhl.yun.api.outer.application.dto;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.enums.BaseResultCodeEnum;
import com.zyhl.yun.api.outer.enums.BusinessSourceEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 云邮AI助手1.1报名重构，报名请求参数
 *
 * <AUTHOR>
 */
@Data
@Slf4j
public class AiRegisterReqDTO extends BaseDTO implements Serializable {

    /**
     * 业务来源，必填
     *
     * @see BusinessSourceEnum
     */
    private Integer sourceBusiness;

    /**
     * 算法工具所属模块，AI助手和智能相册不需要传
     */
    private Integer module;

    /**
     * 校验参数的合法性。
     * 该方法主要进行两步校验：
     * 1. 检查业务来源是否存在于业务来源枚举中；
     * 2. 如果业务来源是图片类型，检查模块是否存在于AI模块枚举中。
     *
     * @return 如果参数校验失败，返回相应的错误参数结果码；如果校验成功，返回null。
     */
    public AbstractResultCode checkParam() {
        // 20240423会议，AI工具和AI助手合并
        if (!BusinessSourceEnum.isAssistant(sourceBusiness) && !BusinessSourceEnum.isDocSearch(sourceBusiness)
                && !BusinessSourceEnum.AI_VIDEO_ANALYSIS.getCode().equals(sourceBusiness)) {
            log.info("业务来源参数错误");
            return BaseResultCodeEnum.ERROR_PARAMS;
        }

        // 无论前端传什么都改为0
        module = 0;

        return super.checkUserId();
    }
}
