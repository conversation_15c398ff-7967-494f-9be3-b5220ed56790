package com.zyhl.yun.api.outer.application.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * 排序信息
 *
 * <AUTHOR>
 * @date 2024/5/27 16:07
 */
@Data
public class DialogueSortInfoDTO {

    /**
     * 文件搜索排序方式
     */
    private JSONObject fileSortTpe;

    /**
     * 图片搜索排序方式
     * 排序方式（不填默认1）
     * 1--按照图片拍摄时间倒序排序（如拍摄时间为空则用上传时间）
     * 2--按照相关度倒序排序
     */
    private Integer imageSortType;

    /**
     * 笔记搜索排序方式
     */
    private Integer noteSortType;

    /**
     * 相册搜索排序方式
     */
    private Integer albumSortType;

    /**
     * 功能搜索排序方式
     */
    private Integer functionSortType;

    /**
     * 活动搜索排序方式
     */
    private Integer activitySortType;

    /**
     * 发现广场搜索排序方式
     */
    private Integer squareSortType;

    /**
     * 我的圈子搜索排序方式
     */
    private Integer circleSortType;

    /**
     * 热门圈子搜索排序方式
     */
    private Integer hotCircleSortType;

}
