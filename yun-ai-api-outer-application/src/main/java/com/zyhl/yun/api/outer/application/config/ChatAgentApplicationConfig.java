package com.zyhl.yun.api.outer.application.config;


import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * 对话智能体配置（弃用）
 * <AUTHOR>
 */
@Deprecated
@Data
@Component
@ConfigurationProperties("chat.agent.application")
public class ChatAgentApplicationConfig {

	/**
	 * 智能体白名单开关
	 */
	private Boolean whiteSwitch = false;
	/**
	 * 白名单应用id列表
	 */
	private List<String> whiteApplicationIds;
    /**
     * 用户白名单列表（手机号码）
     */
    private List<String> userWhiteList;

}
