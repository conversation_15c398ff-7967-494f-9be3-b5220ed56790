package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 个人知识库资源信息移动操作请求参数
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KnowledgeFileMoveBatchReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 知识库
     */
    private String baseId;

    /**
     * 资源ID列表
     */
    private List<String> resourceIdList;

    /**
     * 目的父文件夹 id
     */
    private String toParentFileId;
}
