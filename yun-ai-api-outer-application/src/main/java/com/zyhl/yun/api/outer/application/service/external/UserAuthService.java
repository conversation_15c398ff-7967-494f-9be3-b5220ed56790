package com.zyhl.yun.api.outer.application.service.external;

import com.zyhl.yun.api.outer.domain.dto.UserInfoDTO;
import com.zyhl.yun.api.outer.external.client.resp.UserDomainRspDTO;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/11 下午10:04
 */
public interface UserAuthService {

    /**
     * 验证token
     * @param userId 用户id
     * @param token token
     * @return 校验结果
     */
    Boolean validateToken(String userId, String token);

    /**
     * 验证token
     * @param token token
     * @return 用户结果
     */
    UserDomainRspDTO validateTokenOnly(String token);

    /**
     * 根据userId查询用户信息
     * @param userId
     * @return
     */
    UserInfoDTO validateUserId(String userId);
    
    
	/**
	 * 验证统一认证token并且获取用户信息
	 * 
	 * @param stToken  统一认证token
	 * @param sourceId 中移认证sourceId
	 * @param ts       时间戳，精确到毫秒
	 * @param sign     签名: 签名章节 MD5(stToken + sourceId + ts)
	 * @return 用户信息
	 * @throws Exception 异常信息
	 */
	UserDomainRspDTO validateStToken(String stToken, String sourceId, Long ts, String sign) throws Exception;

}
