package com.zyhl.yun.api.outer.application.enums;

/**
 * 任务模块
 * 
 * <AUTHOR>
 * @date 2025-04-28
 */
public enum TaskModuleEnum {
	/**
	 * 文档总结
	 */
	DOC_SUMMARY("doc-summary", "文档总结", "图书快速阅读使用"),

	/**
	 * 文档大纲-脑图
	 */
	DOC_MIND_MAP("doc-mindMap", "文档大纲-脑图", "图书快速阅读使用"),
	
	/**
	 * 文档大纲-脑图
	 */
	DOC_FULL_OUTLINE("doc-outline", "文档大纲-全文大纲", "图书快速阅读使用");

	private final String type;

	private final String name;
	
	private final String remark;

	TaskModuleEnum(String type, String name, String remark) {
		this.type = type;
		this.name = name;
		this.remark = remark;
	}

	public String getType() {
		return type;
	}

	public String getName() {
		return name;
	}
	
	public String getRemark() {
		return remark;
	}
}
