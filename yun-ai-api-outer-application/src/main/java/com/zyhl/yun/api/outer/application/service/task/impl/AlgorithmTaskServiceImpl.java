package com.zyhl.yun.api.outer.application.service.task.impl;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.zyhl.hcy.commons.exception.BusinessException;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.redis.HcyRedisTemplate;
import com.zyhl.hcy.yun.ai.common.base.eos.ObjectStoreClient;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.FileBase64Util;
import com.zyhl.hcy.yun.ai.common.base.utils.StopWatchUtil;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.enums.BaseResultCodeEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.YunDiskClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.FileGetContentReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.AIFileVO;
import com.zyhl.hcy.yun.ai.common.platform.third.dto.GetUserInfoByPhoneNumberRespDTO;
import com.zyhl.yun.api.outer.application.config.TaskFileSuffixConfig;
import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.AiFileVoConvertor;
import com.zyhl.yun.api.outer.application.dto.AITaskResultPageReqDTO;
import com.zyhl.yun.api.outer.application.dto.AiTaskResultReqDTO;
import com.zyhl.yun.api.outer.application.dto.EmoTemplateDTO;
import com.zyhl.yun.api.outer.application.dto.ShortLinkDTO;
import com.zyhl.yun.api.outer.application.dto.TaskBusinessParamDTO;
import com.zyhl.yun.api.outer.application.service.task.AlgorithmTaskService;
import com.zyhl.yun.api.outer.config.EosFileExpireConfig;
import com.zyhl.yun.api.outer.config.MemberCenterBenefitsProperties;
import com.zyhl.yun.api.outer.constants.CommonConstant;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.domain.entity.TaskAiAbilityEntity;
import com.zyhl.yun.api.outer.domain.vo.common.PageInfoVO;
import com.zyhl.yun.api.outer.domainservice.ExpiredTaskDomainService;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ImageSuffixEnum;
import com.zyhl.yun.api.outer.enums.ImageTransmissionTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.SendTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileCategoryEnum;
import com.zyhl.yun.api.outer.enums.task.FileExpiredStatusEnum;
import com.zyhl.yun.api.outer.enums.task.TalkResultTypeEnum;
import com.zyhl.yun.api.outer.enums.task.TaskFeePaidStatusEnum;
import com.zyhl.yun.api.outer.enums.task.TaskFeeTypeEnum;
import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;
import com.zyhl.yun.api.outer.external.client.req.centertask.BusinessParam;
import com.zyhl.yun.api.outer.external.client.req.centertask.TextParam;
import com.zyhl.yun.api.outer.external.service.EOSExternalService;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.repository.TaskAiAbilityRepository;
import com.zyhl.yun.api.outer.util.DateUtil;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.AlgorithmTaskListResultVO;
import com.zyhl.yun.api.outer.vo.AlgorithmTaskResultVO;
import com.zyhl.yun.api.outer.vo.AsyncTaskRequestVO;
import com.zyhl.yun.api.outer.vo.AsyncTaskResult;
import com.zyhl.yun.api.outer.vo.TaskRespParamVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * AI算法任务接口实现类
 *
 * <AUTHOR>
 * @Date 2024/03/16 15:41
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlgorithmTaskServiceImpl implements AlgorithmTaskService {

    private final TaskAiAbilityRepository taskAiAbilityRepository;

    @Resource
    private YunDiskClient yunDiskClient;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private AiFileVoConvertor aiFileVoConvertor;

    @Resource
    private MemberCenterBenefitsProperties memberCenterBenefitsProperties;

    @Resource(name = "getFileContentThreadPool")
    private ExecutorService getFileContentThreadPool;

    private final HcyRedisTemplate<String, Object> hcyRedisTemplate;

    private static final List<Integer> AI_TASK_EXECUTE_TYPE = Arrays.asList(TaskStatusEnum.PROCESS_FAILURE.getCode(),
            TaskStatusEnum.OVERDUE.getCode(), TaskStatusEnum.PROCESS_FINISH.getCode());

    public static final String COUNTER_PREFIX = "center:task:queue:num:";

    @Resource
    private ObjectStoreClient objectStoreClient;

    @Resource
    private EOSExternalService esExternalService;

    @Resource
    private TaskFileSuffixConfig taskFileSuffixConfig;

    @Resource
    private ExpiredTaskDomainService expiredTaskDomainService;

    @Resource(name = "monitorLogTaskThreadPool")
    private ExecutorService monitorLogTaskThreadPool;

    /**
     * 获取短链的地址
     */
    @Value("${yun-short-Link.url:https://l.yun.139.com/shortUrl/generate}")
    private String shortLinkUrl;

    /**
     * AI工具-自增key后缀专属时间节点，应与center-aimanage工程timeNode保持一致
     */
    @Value("${monitor.date.timeNode:3}")
    private Integer timeNode;

    @Resource
    private EosFileExpireConfig eosFileExpireConfig;

    /**
     * 渠道ID演示专区
     */
    private static final String CHANNEL_ID = "40201";

    /**
     * 动态模板
     */
    private static final Integer MEDIA_MODE_1 = 1;

    @Resource
    private AiTextResultRepository aiTextResultRepository;

    /**
     * 查询算法任务结果信息
     *
     * @param dto 任务结果信息请求入参req
     * @return 算法任务结果VO
     */
    @Override
    public AlgorithmTaskResultVO getAiTaskResult(AiTaskResultReqDTO dto) {
        // 返回结果VO
        AlgorithmTaskResultVO resultVo = new AlgorithmTaskResultVO();
        resultVo.setTaskId(dto.getTaskId());

        // 查算法任务表
        TaskAiAbilityEntity taskEntity = taskAiAbilityRepository.getTaskEntity(Long.valueOf(dto.getTaskId()));
        if (ObjectUtils.isEmpty(taskEntity)) {
            throw new YunAiBusinessException(ResultCodeEnum.TASK_RECORD_NOT_FOUND);
        }

        // 超时任务日志监控
        this.monitorLogTaskThreadPool.execute(() -> expiredTaskDomainService.expiredTaskMonitor(taskEntity));

        String userId = RequestContextHolder.getUserId();
        if (StringUtils.isBlank(userId) && StringUtils.isBlank(dto.getUserId())) {
            log.warn("==> 异步任务查询，taskId: {}, userId参数为空.", dto.getTaskId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_FORBIDDEN);
        }
        if (StringUtils.isBlank(userId)) {
            log.info("==> 异步任务查询，taskId: {}, 请求头未带token解析不到userId, 根据Body获取userId{}",
                    dto.getTaskId(), dto.getUserId());
            userId = dto.getUserId();
            RequestContextHolder.setUserId(userId);
        }

        //数据越权
        if (StringUtils.isNotBlank(taskEntity.getUserId()) && !taskEntity.getUserId().equals(userId)) {
            ServletRequestAttributes requestAttributes = (ServletRequestAttributes) org.springframework.web.context.request.RequestContextHolder.currentRequestAttributes();
            Map<String, String> headers = ServletUtil.getHeaderMap(requestAttributes.getRequest());
            log.info("==> 异步任务查询，taskId: {}, 数据库userId{}, 参数userId{}, 请求头参数{}",
                    dto.getTaskId(), taskEntity.getUserId(), userId, JSONUtil.toJsonStr(headers));
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_FORBIDDEN);
        }
        resultVo.setStatus(taskEntity.getTaskStatus());
        AsyncTaskResult asyncTaskResult = new AsyncTaskResult();
        resultVo.setResultList(Collections.singletonList(asyncTaskResult));
        asyncTaskResult.setAlgorithmCode(taskEntity.getAlgorithmCode());
        asyncTaskResult.setSupplierType(taskEntity.getSupplierTypes());
        asyncTaskResult.setFeePaidStatus(taskEntity.getFeePaidStatus());
        addBusinessParam(resultVo, taskEntity);

        // 排队位置
        resultVo.setQueueOffset(0L);
        long offset;
        try {
            RAtomicLong queueCounter = redissonClient.getAtomicLong(COUNTER_PREFIX + asyncTaskResult.getAlgorithmCode()
                    + DateUtil.getCurrentOrPreviousDate(timeNode));
            offset = queueCounter.get() - 1;
            if (offset < 0L) {
                offset = 0L;
            }
            resultVo.setQueueOffset(offset);
        } catch (Exception e) {
            log.error("redisson getAndIncrement error:", e);
        }

        // 已执行的任务状态 任务完成、任务失败、任务已过期
        if (!AI_TASK_EXECUTE_TYPE.contains(taskEntity.getTaskStatus())) {
            return resultVo;
        } else {
            setLastTaskCache(taskEntity);
        }

        // 扣费相关处理【无关任务成功或失败】
        deductionFeeHandle(resultVo, taskEntity);

        // 状态为任务失败、任务已过期
        if (!TaskStatusEnum.PROCESS_FINISH.getCode().equals(taskEntity.getTaskStatus())) {
            // 处理返回结果
            AiResultCode aiResultCode = AiResultCode.getByCodeOrMsg(taskEntity.getResultCode(), taskEntity.getResultMsg());
            resultVo.setAiResultCode(aiResultCode);
            return resultVo;
        }

        // 状态为任务完成
        try {
            // 获取转换后的JSONObject对象
            List<TaskRespParamVO> respParam = JSON.parseArray(taskEntity.getRespParam(), TaskRespParamVO.class);
            if (CollectionUtils.isEmpty(respParam) || ObjectUtils.isEmpty(respParam.get(0))) {
                return resultVo;
            }

            // 处理任务类型为非图片类型
            if (DialogueIntentionEnum.NON_IMAGE_MODULE.contains(asyncTaskResult.getAlgorithmCode())) {
                handleTextGeneratePicture(resultVo, respParam);
                return resultVo;
            }

            // 文件过期相关处理，返回文件是否正常：true-文件正常，false-文件过期
            boolean fileExpired = fileExpiredHandle(resultVo, taskEntity);

            // 文件正常
            if (fileExpired) {
                // 获取输出资源id
                handleGeneratePicture(asyncTaskResult, respParam, taskEntity);
            }
        } catch (Exception e) {
            log.error("getAiTaskResult gJSONObject fail taskEntity:{} | e:", JsonUtil.toJson(taskEntity), e);
            if (e instanceof BusinessException) {
                throw e;
            } else {
                throw new YunAiBusinessException(BaseResultCodeEnum.DEFAULT_ERROR_CODE);
            }
        } finally {
            log.info("getAiTaskResult-resultVo：{}", JsonUtil.toJson(taskEntity));
        }

        return resultVo;
    }

    @Override
    public PageInfoVO<AlgorithmTaskListResultVO> listTaskRecord(AITaskResultPageReqDTO dto) {
        //分页查询用户任务列表 处理结果和单个任务一样
        // 参数初始化 */
        StopWatch stopWatch = StopWatchUtil.createStarted();
        try {
            PageInfoVO<AlgorithmTaskListResultVO> pageInfoVO = new PageInfoVO<>();
            PageInfoDTO page = PageInfoDTO.getReqDTO(dto.getPageInfo());
            PageInfo<TaskAiAbilityEntity> pageInfo = taskAiAbilityRepository.getTaskEntityList(
                    Integer.parseInt(page.getPageCursor()), page.getPageSize(),
                    dto.getUserId(), dto.getCommand(), dto.getChannelId(), 1);

            if (CollectionUtils.isEmpty(pageInfo.getList())) {
                return pageInfoVO;
            }
            List<AlgorithmTaskListResultVO> listResult = new ArrayList<>();
            pageInfo.getList().forEach(taskEntity -> listResult.add(extracted(dto, taskEntity))
            );
            pageInfoVO = PageInfoVO.getRespDTO(page, pageInfo);
            pageInfoVO.setList(listResult);
            return pageInfoVO;
        } catch (Exception e) {
            log.error("历史任务记录查询：", e);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_SERVER_UNAVAILABLE);
        } finally {
            log.info("历史对话列表查询-AlgorithmChatHistoryServiceImpl-contentList，并行处理结果耗时：{}", StopWatchUtil.logTime(stopWatch));
            StopWatchUtil.clearDuration();
        }
    }

    /***
     * 提取任务信息到结果
     * @param dto 请求体
     * @param taskEntity 任务信息
     * @return 结果
     */
    private AlgorithmTaskListResultVO extracted(AITaskResultPageReqDTO dto, TaskAiAbilityEntity taskEntity) {
        AlgorithmTaskListResultVO resultVo = new AlgorithmTaskListResultVO();
        resultVo.setTaskId(String.valueOf(taskEntity.getId()));
        resultVo.setStatus(taskEntity.getTaskStatus());
        AsyncTaskResult asyncTaskResult = new AsyncTaskResult();
        if (!TaskStatusEnum.PROCESS_FINISH.getCode().equals(taskEntity.getTaskStatus())) {
            AiResultCode aiResultCode = AiResultCode.getByCodeOrMsg(taskEntity.getResultCode(), taskEntity.getResultMsg());
            resultVo.setResultCode(aiResultCode.getCode());
            resultVo.setResultMsg(aiResultCode.getMsg());
        }
        asyncTaskResult.setAlgorithmCode(taskEntity.getAlgorithmCode());
        asyncTaskResult.setSupplierType(taskEntity.getSupplierTypes());
        asyncTaskResult.setFeePaidStatus(taskEntity.getFeePaidStatus());
        resultVo.setTaskResult(Collections.singletonList(asyncTaskResult));

        AsyncTaskRequestVO asyncTaskRequestVO = new AsyncTaskRequestVO();
        asyncTaskRequestVO.setRequestTime(taskEntity.getCreateTime());
        List<TaskRespParamVO> respParam = JSON.parseArray(taskEntity.getRespParam(), TaskRespParamVO.class);
        // 处理任务类型为非图片类型
        BusinessParam businessParam = JSONUtil.toBean(taskEntity.getBusinessParam(), BusinessParam.class);
        if (DialogueIntentionEnum.NON_IMAGE_MODULE.contains(asyncTaskResult.getAlgorithmCode())) {
            //文本类型参数转换
            if (businessParam.getTextParam() != null) {
                BeanUtils.copyProperties(businessParam.getTextParam(), asyncTaskRequestVO);
            }
            getAsyncTaskRequestVO(businessParam, asyncTaskRequestVO, taskEntity.getUserId());
            handleTextGeneratePictureListResult(resultVo, respParam);
        } else {
            //图片类型参数转换
            BeanUtils.copyProperties(businessParam.getImageParam(), asyncTaskRequestVO);
            getAsyncTaskRequestVO(businessParam, asyncTaskRequestVO, taskEntity.getUserId());
            // 获取输出资源id
            if (FileExpiredStatusEnum.EXPIRED.getCode().equals(taskEntity.getFileExpiredStatus())) {
                resultVo.setStatus(TaskStatusEnum.OVERDUE.getCode());
            } else {
                handleGeneratePicture(asyncTaskResult, respParam, taskEntity);
            }
        }

        asyncTaskRequestVO.setChannelId(dto.getChannelId());
        asyncTaskRequestVO.setSupplierType(taskEntity.getSupplierTypes());
        resultVo.setTaskRequest(asyncTaskRequestVO);
        return resultVo;
    }

    private void getAsyncTaskRequestVO(BusinessParam businessParam, AsyncTaskRequestVO asyncTaskRequestVO, String userId) {
        String fileId = businessParam.getImageParam().getFileId();
        if (StringUtils.isNotEmpty(fileId)) {
            asyncTaskRequestVO.setSendType(SendTypeEnum.FILE_ID.getCode());
            asyncTaskRequestVO.setFileId(fileId);
            try {
                AIFileVO aiFileVO = getFileContent(businessParam.getImageParam().getFileId(), userId);
                if (aiFileVO != null) {
                    asyncTaskRequestVO.setFileUrl(aiFileVO.getContent());
                }
            } catch (Exception e) {
                log.error("用户Id:{},文件Id:{}获取信息异常,", userId, fileId);
            }
        } else if (StringUtils.isNotEmpty(businessParam.getImageParam().getEosObjectKey())) {
            asyncTaskRequestVO.setSendType(SendTypeEnum.URL.getCode());
            String fileUrl = esExternalService.getFileUrl(businessParam.getImageParam().getEosObjectKey(), null, eosFileExpireConfig.getExpireTime());
            asyncTaskRequestVO.setFileUrl(fileUrl);
        } else if (StringUtils.isNotEmpty(businessParam.getImageParam().getLocalPath())) {
            asyncTaskRequestVO.setSendType(SendTypeEnum.BASE64.getCode());
            String base64 = FileBase64Util.fileToBase64(new File(businessParam.getImageParam().getLocalPath()));
            asyncTaskRequestVO.setBase64(base64);
        }
    }

    /**
     * 设置最后一次任务缓存
     */
    private void setLastTaskCache(TaskAiAbilityEntity taskEntity) {
        try {
            String cacheKey = String.format(CommonConstant.LAST_TASK_CACHE_KEY, taskEntity.getId());
            if (!Boolean.TRUE.equals(hcyRedisTemplate.hasKey(cacheKey))) {
                hcyRedisTemplate.opsForValue().set(cacheKey, taskEntity.getId(), 24, TimeUnit.HOURS);
            }
        } catch (Exception e) {
            log.error("set last task cache exception = ", e);
        }
    }

    /**
     * 文件过期相关处理 fileExpiredStatus为1，则返回10000205-任务结果已过期 返回文件是否正常：true-文件正常，false-文件过期
     *
     * @Author: WeiJingKun
     */
    private boolean fileExpiredHandle(AlgorithmTaskResultVO resultVo, TaskAiAbilityEntity taskEntity) {
        if (FileExpiredStatusEnum.EXPIRED.getCode().equals(taskEntity.getFileExpiredStatus())) {
            resultVo.setAiResultCode(AiResultCode.CODE_10000205);
            return false;
        }
        return true;
    }

    /**
     * 扣费相关处理【无关任务成功或失败】
     *
     * @Author: WeiJingKun
     */
    private void deductionFeeHandle(AlgorithmTaskResultVO resultVo, TaskAiAbilityEntity taskEntity) {
        /** 参数初始化 */
        Long id = taskEntity.getId();
        Integer feePaidStatus = taskEntity.getFeePaidStatus();

        /**
         * 当数据库fee_paid_status=-1【不扣费】
         * 需要查询配置，判断是否需要扣费
         * 【是】则更新fee_paid_status=0【未扣费】
         * 【否】不做任何处理
         */
        if (TaskFeePaidStatusEnum.NO_PAYMENT.getCode().equals(feePaidStatus)) {
            // 查询配置，判断是否需要扣费
            if (memberCenterBenefitsProperties.hasBenefitNo(taskEntity.getSourceChannel(),
                    taskEntity.getAlgorithmCode())) {
                log.info("任务taskId:{} 首次查询，获取配置后，判定需要设置付费类型和未扣费...", id);
                // 【是】则更新数据库fee_type=1【需要付费】，fee_paid_status=0【未扣费】
                taskAiAbilityRepository.updateFeePaidStatus(id, TaskFeeTypeEnum.YES.getCode(), TaskFeePaidStatusEnum.UNPAID.getCode(), null);

                // 接口返回，fee_paid_status=0【未扣费】
                List<AsyncTaskResult> resultList = resultVo.getResultList();
                if (CollUtil.isNotEmpty(resultList)) {
                    resultList.get(0).setFeePaidStatus(TaskFeePaidStatusEnum.UNPAID.getCode());
                }
            }
        }
    }

    /**
     * 处理图片的逻辑
     *
     * @param asyncTaskResult the async task result
     * @param respParamVOList the resp param
     * @param taskEntity      the task entity
     * <AUTHOR>
     * @date 2024-4-28 15:39
     */
    private void handleGeneratePicture(AsyncTaskResult asyncTaskResult,
                                       List<TaskRespParamVO> respParamVOList,
                                       TaskAiAbilityEntity taskEntity) {
        if (CollectionUtils.isEmpty(respParamVOList)) {
            return;
        }
        List<AsyncTaskResult.AsyncTaskResultFileInfo> fileInfoList = new ArrayList<>();
        List<String> fileUrlList = new ArrayList<>();
        String userId = RequestContextHolder.getUserId();
        boolean isShortLink = false;
        if (StringUtils.equals(DialogueIntentionEnum.AI_PHOTOGRAPHY.getCode(), taskEntity.getAlgorithmCode())
                && StringUtils.equals(CHANNEL_ID, taskEntity.getSourceChannel())) {
            isShortLink = true;
        }
        CountDownLatch costDownLatch = new CountDownLatch(respParamVOList.size());
        ConcurrentHashMap<Integer, String> fileUrlMap = new ConcurrentHashMap<>(Const.NUM_16);
        ConcurrentHashMap<Integer, AsyncTaskResult.AsyncTaskResultFileInfo> fileInfoMap = new ConcurrentHashMap<>(Const.NUM_16);
        for (int i = 0; i < respParamVOList.size(); i++) {
            TaskRespParamVO respParamVO = respParamVOList.get(i);
            int index = i;
            boolean finalIsShortLink = isShortLink;
            this.getFileContentThreadPool.execute(() -> {
                try {
                    if (respParamVO.getImageTransmissionType() == null ||
                            ImageTransmissionTypeEnum.YUN_DISK.getCode() == respParamVO.getImageTransmissionType()) {
                        //个人云
                        String targetUserId = respParamVO.getUserId();
                        AIFileVO aiFileVO = getFileContent(respParamVO.getOutResourceId(),
                                StringUtils.isNotEmpty(targetUserId) ? targetUserId : userId);
                        AsyncTaskResult.AsyncTaskResultFileInfo fileInfo =
                                aiFileVoConvertor.toAsyncTaskResultFileInfo(aiFileVO);
                        fileInfo.setFileId(respParamVO.getOutResourceId());
                        fileInfo.setContentHashAlgorithm(aiFileVO.getFingerprint());
                        fileInfo.setName(aiFileVO.getFileName());
                        fileInfo.setSize(aiFileVO.getFileSize());
                        fileInfo.setFileExtension(aiFileVO.getFileSuffix());
                        fileInfo.setContent(finalIsShortLink ? getShortLink(aiFileVO.getContent()) : aiFileVO.getContent());
                        fileInfoMap.put(index, fileInfo);
                        fileUrlMap.put(index, fileInfo.getContent());
                    } else {
                        //EOS
                        String fileSuffix = ImageSuffixEnum.getByAlgorithmCode(taskEntity.getAlgorithmCode()).getCode();
                        // 针对算法编码为AI表情包编码的特殊处理
                        if (DialogueIntentionEnum.AI_EMOTICON.getCode().equals(taskEntity.getAlgorithmCode())) {
                            TaskBusinessParamDTO paramDTO = new TaskBusinessParamDTO(taskEntity.getBusinessParam());
                            if (Objects.nonNull(paramDTO.getImageParam())) {
                                EmoTemplateDTO emoTemplateDTO = JsonUtil.parseObject(paramDTO.getImageParam().getExtendField(), EmoTemplateDTO.class);
                                boolean onlyStickFlag = Boolean.TRUE.equals(emoTemplateDTO.getOnlyStickFlag()) && CharSequenceUtil.isNotEmpty(emoTemplateDTO.getStickerUrl());
                                fileSuffix = (onlyStickFlag || MEDIA_MODE_1.equals(emoTemplateDTO.getMediaMode())) ? ImageSuffixEnum.GIF.getCode() : fileSuffix;
                                if (index == 2) {
                                    fileSuffix = (onlyStickFlag || MEDIA_MODE_1.equals(emoTemplateDTO.getMediaMode())) ? ImageSuffixEnum.MP4.getCode() : fileSuffix;
                                }
                            }
                        }
                        String fileUrl = objectStoreClient.generateSignedAttachment(respParamVO.getOutResourceId(),
                                respParamVO.getOutResourceId() + StrPool.DOT + fileSuffix, eosFileExpireConfig.getExpireTime());
                        fileUrlMap.put(index, finalIsShortLink ? getShortLink(fileUrl) : fileUrl);
                    }
                } catch (Exception ex) {
                    log.error("==> 文件下载,参数:userId[{}], fileId:[{}]执行==> 失败. | e:", userId, respParamVO, ex);
                } finally {
                    costDownLatch.countDown();
                }
            });
        }
        try {
            boolean await = costDownLatch.await(RedisConstants.TIMEOUT_60, TimeUnit.SECONDS);
            if (!await) {
                log.error("==> 文件下载,主线程等待超时, | e:");
            }
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            log.error("==> 文件下载,主线程等待异常, | e:", ex);
        }

        // 针对算法编码为AI表情包编码的特殊处理
        if (DialogueIntentionEnum.AI_EMOTICON.getCode().equals(taskEntity.getAlgorithmCode())) {
            fileInfoList.stream()
                    .filter(obj -> taskFileSuffixConfig.getVideo().contains(obj.getFileExtension()))
                    .forEach(obj -> obj.setCategory(FileCategoryEnum.VIDEO.getYundiskCategory()));
        }
        for (int i = 0; i < respParamVOList.size(); i++) {
            if (Objects.nonNull(fileInfoMap.get(i))) {
                fileInfoList.add(fileInfoMap.get(i));
            }
            if (StringUtils.isNotEmpty(fileUrlMap.get(i))) {
                fileUrlList.add(fileUrlMap.get(i));
            }
        }
        asyncTaskResult.setFileInfoList(fileInfoList);
        asyncTaskResult.setFileUrlList(fileUrlList);
    }

    /**
     * 处理任务类型为非图片类型
     *
     * @param resultVo  算法任务结果VO
     * @param respParam 返回结果JSONObject列表
     */
    private void handleTextGeneratePicture(AlgorithmTaskResultVO resultVo, List<TaskRespParamVO> respParam) {
        if (CollectionUtils.isEmpty(respParam)) {
            return;
        }
        List<AsyncTaskResult.ImageCaptionInfo> list = getImageCaptionInfoList(respParam);
        resultVo.getResultList().get(0).setTextList(list);
        resultVo.getResultList().get(0).setResultType(TalkResultTypeEnum.TEXT.getType());
    }

    private static List<AsyncTaskResult.ImageCaptionInfo> getImageCaptionInfoList(List<TaskRespParamVO> respParam) {
        List<AsyncTaskResult.ImageCaptionInfo> textList = new ArrayList<>();
        for (TaskRespParamVO taskRespParamVO : respParam) {
            if (CharSequenceUtil.isEmpty(taskRespParamVO.getOutContent())) {
                continue;
            }
            AsyncTaskResult.ImageCaptionInfo imageCaptionInfo =
                    new AsyncTaskResult.ImageCaptionInfo();
            imageCaptionInfo.setText(taskRespParamVO.getOutContent());
            Float score = Objects.nonNull(taskRespParamVO.getScore()) ?
                    Float.parseFloat(String.valueOf(taskRespParamVO.getScore())) : 1F;
            imageCaptionInfo.setScore(score);
            textList.add(imageCaptionInfo);
        }
        return textList;
    }

    private void handleTextGeneratePictureListResult(AlgorithmTaskListResultVO resultVo, List<TaskRespParamVO> respParam) {
        if (CollectionUtils.isEmpty(respParam)) {
            return;
        }
        List<AsyncTaskResult.ImageCaptionInfo> list = getImageCaptionInfoList(respParam);
        resultVo.getTaskResult().get(0).setTextList(list);
        resultVo.getTaskResult().get(0).setResultType(TalkResultTypeEnum.TEXT.getType());
    }

    /**
     * 获取文件
     *
     * @param fileId the file id
     * @param userId the user id
     * @return AIFileVO
     */
    private AIFileVO getFileContent(String fileId, String userId) {
        FileGetContentReqDTO fileGetContentReqDTO = new FileGetContentReqDTO();
        fileGetContentReqDTO.setFileId(fileId);
        GetUserInfoByPhoneNumberRespDTO userInfo = new GetUserInfoByPhoneNumberRespDTO();
        userInfo.setUserDomainId(Long.valueOf(userId));
        fileGetContentReqDTO.setUserInfo(userInfo);
        //获取原图
        fileGetContentReqDTO.setIsOriginal(true);
        return yunDiskClient.getFileContent(fileGetContentReqDTO);
    }

    /**
     * 获取短链
     *
     * @param longURL the long url
     * @return {@link String} 短链
     * <AUTHOR>
     * @date 2024-6-20 17:35
     */
    private String getShortLink(String longURL) {
        try {
            ShortLinkDTO shortLinkDTO = ShortLinkDTO.builder().longURL(longURL).configURL(longURL).expireTime(1).build();
            String post = HttpUtil.post(shortLinkUrl, JsonUtil.toJson(shortLinkDTO));
            log.info("==> 获取短链,参数:longURL[{}],执行==> 成功. | result:{}", longURL, post);
            BaseResult baseResult = JsonUtil.parseObject(post, BaseResult.class);
            String data = baseResult.getData().toString();
            log.info("==> 获取短链,参数:longURL[{}],执行==> 成功. | result:{}", longURL, data);
            return data;
        } catch (Exception ex) {
            log.error("==> 获取短链,参数:longURL[{}],执行==> 失败. | e:", longURL, ex);
        }
        return longURL;
    }

    /**
     * 添加业务参数
     *
     * @param resultVo   the result vo
     * @param taskEntity the task entity
     * <AUTHOR>
     * @date 2025/6/12 15:00
     */
    private void addBusinessParam(AlgorithmTaskResultVO resultVo, TaskAiAbilityEntity taskEntity) {
        BusinessParam businessParam = JsonUtil.parseObject(taskEntity.getBusinessParam(), BusinessParam.class);
        TextParam textParam = businessParam.getTextParam();
        if (Objects.nonNull(textParam)) {
            Map<String, Object> map = JsonUtil.toMap(JsonUtil.toJson(textParam));
            String rowKey = textParam.getRowkey();
            String textRowKey = textParam.getExtendField();
            if (StringUtils.isNotEmpty(rowKey)) {
                AiTextResultEntity aiTextResultEntity = aiTextResultRepository.getByRowKey(rowKey);
                if (Objects.nonNull(aiTextResultEntity) && StringUtils.isNotEmpty(aiTextResultEntity.getReqParameters())) {
                    map.put("prompt", aiTextResultEntity.getReqParameters());
                    if (StringUtils.isEmpty(textRowKey)) {
                        map.remove("prompt");
                        map.put("text", aiTextResultEntity.getReqParameters());
                        resultVo.setBusinessParam(JsonUtil.toJson(map));
                        return;
                    }
                }
            }

            if (StringUtils.isNotEmpty(textRowKey)) {
                AiTextResultEntity aiTextResultEntity = aiTextResultRepository.getByRowKey(textRowKey);
                if (Objects.nonNull(aiTextResultEntity) && StringUtils.isNotEmpty(aiTextResultEntity.getReqParameters())) {
                    map.put("text", aiTextResultEntity.getReqParameters());
                }
            }
            resultVo.setBusinessParam(JsonUtil.toJson(map));
        }
    }
}
