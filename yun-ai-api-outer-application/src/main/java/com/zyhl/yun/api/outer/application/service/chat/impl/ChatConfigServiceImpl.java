package com.zyhl.yun.api.outer.application.service.chat.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.enums.SupplierTypeEnum;
import com.zyhl.yun.api.outer.config.ModelWhiteProperties;
import com.zyhl.yun.api.outer.config.WhiteListProperties;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.repository.UserKnowledgeInviteRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeRepository;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddContentDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.ChatConfigDTO;
import com.zyhl.yun.api.outer.application.dto.ChatConfigGetDTO;
import com.zyhl.yun.api.outer.application.service.chat.ChatConfigService;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domain.vo.ChatConfigV2VO;
import com.zyhl.yun.api.outer.domain.vo.ChatConfigVO;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.ChatNetworkSearchStatusEnum;
import com.zyhl.yun.api.outer.repository.AlgorithmChatConfigRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 会话大语言模型设定Service实现类
 *
 * <AUTHOR>
 * @version 2024年02月28日 15:31
 */
@Service
@Slf4j
public class ChatConfigServiceImpl implements ChatConfigService {

    @Resource
    private AlgorithmChatConfigRepository algorithmChatConfigRepository;
    @Resource
    private ModelProperties modelProperties;
    @Resource
    private SourceChannelsProperties channelsProperties;
    @Resource
    private ModelWhiteProperties modelWhiteProperties;
    @Resource
    private ChatConfigServiceDomainService chatConfigServiceDomainService;
    @Resource
    private UserKnowledgeRepository userKnowledgeRepository;
    @Resource
    private UserKnowledgeInviteRepository userKnowledgeInviteRepository;
    @Resource
    private WhiteListProperties whiteListProperties;

    @Override
    public boolean config(@NotNull ChatConfigDTO dto) {
        String phoneNumber = RequestContextHolder.getPhoneNumber();
        if (!modelWhiteProperties.isModelAccessible(dto.getModelType(), phoneNumber)) {
            log.info("没有权限操作大模型，model:{},phoneNumber:{}", dto.getModelType(), phoneNumber);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        ChatConfigEntity entity = new ChatConfigEntity(dto.getUserId(), dto.getModelType(), dto.getAssistantEnum().getCode(), dto.getNetworkSearchStatus());

        // 知识库选中更新
        userKnowledgeRepository.updateSelected(dto.getUserId(), dto.getBaseIdList());
        userKnowledgeInviteRepository.updateSelected(dto.getUserId(), dto.getBaseIdList());

        return algorithmChatConfigRepository.saveOrUpdate(entity);
    }

    @Override
    public List<ChatConfigVO> get(ChatConfigGetDTO dto) {
        ChatConfigV2VO v2Vo = getConfigV2(dto);
        if (null != v2Vo) {
            return v2Vo.getModelList();
        }
        return Collections.emptyList();
    }

    @Override
    public ChatConfigV2VO getConfigV2(ChatConfigGetDTO dto) {
        String userId = dto.getUserId();
        AssistantEnum assistantEnum = dto.getAssistantEnum();
        //用户没设置的时候，默认返回true
        Boolean networkSearchStatus = true;
        //默认模型
        TextModelEnum defaultModel = null;
        // 获取需要选中的模型
        ChatConfigEntity entity = chatConfigServiceDomainService.getUserCanUseModel(userId, RequestContextHolder.getPhoneNumber(), assistantEnum, dto.getBusinessType());
        if (null != entity) {
            defaultModel = TextModelEnum.getByCode(entity.getModelType());
            Integer configNetworkSearchStatus = entity.getNetworkSearchStatus();
            if (null != configNetworkSearchStatus) {
                // 用户配置了，按用户配置的设置
                networkSearchStatus = ChatNetworkSearchStatusEnum.isOpen(configNetworkSearchStatus);
            }
        }
        // 设置用户默认模型标识
        List<ChatConfigVO> result = new ArrayList<>();
        Map<String, ModelProperties.ModelLimitConfig> limit = modelProperties.getLimitByAssistantEnum(assistantEnum, dto.getBusinessType());
        for (String code : limit.keySet()) {
            ModelProperties.ModelLimitConfig modelLimitConfig = limit.get(code);
            if (!modelLimitConfig.isSessionSet()) {
                continue;
            }
            TextModelEnum textModel = TextModelEnum.getByCode(code);
            if (null == textModel) {
                continue;
            }
            // 限制特殊场景的模型只有指定白名单账号返回
            if (!modelWhiteProperties.isModelAccessible(code, RequestContextHolder.getPhoneNumber())) {
                continue;
            }
            ChatConfigVO vo = new ChatConfigVO(textModel, modelLimitConfig);
            // 拿不到默认模型，则直接根据配置判断
            if (null == defaultModel) {
                if (modelLimitConfig.isDefModel()) {
                    vo.setDefMode();
                }
            } else {
                if (textModel.equals(defaultModel)) {
                    // 匹配上就是默认模型
                    vo.setDefMode();
                }
            }

            result.add(vo);
        }

        // 根据配置的排序值，从小到大排序
        List<ChatConfigVO> modelList = result.stream().sorted(Comparator.comparing(ChatConfigVO::getSort)).collect(Collectors.toList());

        // 设置aipptSupplier，根据配置的支持渠道列表判断
        Integer aipptSupplier = null;
        if (whiteListProperties.getAipptWhite() != null &&
            whiteListProperties.getAipptWhite().isSupportedChannel(assistantEnum.getCode())) {
            // 检查用户是否在aippt白名单中
            String phoneNumber = RequestContextHolder.getPhoneNumber();
            if (whiteListProperties.getAipptWhite().canUse(phoneNumber)) {
                aipptSupplier = SupplierTypeEnum.AIPPT.getCode();
                log.info("用户{}在aippt白名单中，渠道{}支持aippt，设置aipptSupplier为{}", phoneNumber, assistantEnum.getCode(), aipptSupplier);
            }
        }

        return new ChatConfigV2VO(modelList, networkSearchStatus, aipptSupplier);
    }

    @Override
    public ChatConfigVO getUserSetModel(AlgorithmChatAddDTO dto) {
        String userId = dto.getUserId();
        AlgorithmChatAddContentDTO content = dto.getContent();
        String sourceChannel = content.getSourceChannel();
        AssistantEnum assistantEnum = content.getAssistantEnum();

        // 智能体对话强制选择百炼，其实应该使用星尘，但是现在没有星尘的模型，使用百练是为了映射为阿里厂商
        if (ApplicationTypeEnum.isIntelligen(dto.getApplicationType()) && !CharSequenceUtil.isEmpty(dto.getApplicationId())) {
            log.info("智能体强制使用百练模型，用户id：{}", userId);
            TextModelEnum type = TextModelEnum.BLIAN;
            return new ChatConfigVO(type, modelProperties.getLengthLimit(assistantEnum, content.getBusinessType(), type.getCode())).setDefMode();
        }

        // 如果来源渠道为小天，强制选中百炼模型
        if (channelsProperties.isXiaoTian(sourceChannel)) {
            log.info("小天助手使用nacos配置的模型，用户id：{}，配置模型：{}", userId, modelProperties.getXiaoTianUse());
            TextModelEnum type = TextModelEnum.getByCode(modelProperties.getXiaoTianUse());
            return new ChatConfigVO(type, modelProperties.getLengthLimit(assistantEnum, content.getBusinessType(), type.getCode())).setDefMode();
        }

        // 获取用户选中模型
        List<ChatConfigVO> chatConfigList = get(createChatConfigGetDTO(userId, sourceChannel, assistantEnum));
        // 使用findFirst直接查找第一个defaultMode为1的对象
        return chatConfigList.stream()
                .filter(model -> model.getDefaultMode() == 1)
                .findFirst()
                .orElse(null);
    }

    /**
     * 构建ChatConfigGetDTO
     *
     * @param userId        用户id
     * @param sourceChannel 渠道来源
     * @param assistantEnum 助手枚举
     * @return ChatConfigGetDTO
     * @Author: WeiJingKun
     */
    private ChatConfigGetDTO createChatConfigGetDTO(String userId, String sourceChannel, AssistantEnum assistantEnum) {
        ChatConfigGetDTO dto = new ChatConfigGetDTO();
        dto.setUserId(userId);
        dto.setSourceChannel(sourceChannel);
        dto.setAssistantEnum(assistantEnum);
        return dto;
    }
}
