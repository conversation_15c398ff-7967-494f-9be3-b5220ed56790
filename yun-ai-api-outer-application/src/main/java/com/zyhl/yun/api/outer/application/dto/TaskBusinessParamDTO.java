package com.zyhl.yun.api.outer.application.dto;

import com.alibaba.fastjson.JSONObject;

import java.util.List;

import lombok.Data;

/**
 * algorithm_task_ai_ability.business_param 业务参数
 *
 * <AUTHOR>
 */
@Data
public class TaskBusinessParamDTO {

    /**
     * 图片类参数
     */
    private ImageParam imageParam;

    /**
     * 文本类参数
     */
    private TextParam textParam;

    /**
     * 文本模型参数
     */
    private TextModelParam textModelParam;

    /**
     * 文本模型参数
     */
    private static final String TEXT_MODEL_PARAM_KEY = "textModelParam";

    /**
     * 图片参数
     */
    private static final String IMAGE_PARAM_KEY = "imageParam";

    /**
     * 文本参数
     */
    private static final String TEXT_PARAM_KEY = "textParam";

    public TaskBusinessParamDTO(String jsonStr) {
        final JSONObject jsonObject = JSONObject.parseObject(jsonStr);
        if (jsonObject.containsKey(IMAGE_PARAM_KEY)) {
            imageParam = jsonObject.getJSONObject(IMAGE_PARAM_KEY).toJavaObject(ImageParam.class);
        }
        if (jsonObject.containsKey(TEXT_PARAM_KEY)) {
            textParam = jsonObject.getJSONObject(TEXT_PARAM_KEY).toJavaObject(TextParam.class);
        }
        if (jsonObject.containsKey(TEXT_MODEL_PARAM_KEY)) {
            textModelParam = jsonObject.getJSONObject(TEXT_MODEL_PARAM_KEY).toJavaObject(TextModelParam.class);
        }
    }

    /**
     * 图片类参数
     */
    @Data
    public static class ImageParam {

        /**
         * userId_对话id，用于查询/更新hbase数据
         */
        private String rowkey;

        /**
         * 非必须	文件fileId fileId、localPath、eosKey三选一
         */
        private String fileId;

        /**
         * 非必须	文件本地路径 fileId、localPath、eosKey三选一
         */
        private String localPath;

        /**
         * 非必须	eosKey fileId、localPath、eosKey三选一
         */
        private String eosObjectKey;

        /**
         * 非必须	图片扩展名，例如：jpg、png
         */
        private String imageExt;

        /**
         * 非必须	风格类型，有些接口需要这个参数
         */
        private String style;

        /**
         * 非必须	扩展字段，根据接口需要自行处理
         */
        private String extendField;

        /**
         * 图片存储类型：@see ImageTransmissionTypeEnum
         */
        private Integer imageTransmissionType;

        /**
         * 个人云保存路径。例如：/我的应用收藏/AI助手
         */
        private String yunPath;


    }


    /**
     * 文本类参数
     */
    @Data
    public static class TextParam {

        /**
         * 必须	userId_对话id，用于查询hbase数据
         */
        private String rowkey;

        /**
         * 必须	宽度
         */
        private String width;

        /**
         * 必须	高度
         */
        private String height;

        /**
         * 非必须	风格类型，文生图功能需要
         */
        private String style;

        /**
         * 文生图的文本
         */
        private String text;

        /**
         * 图片存储类型：@see ImageTransmissionTypeEnum
         */
        private Integer imageTransmissionType;

        /**
         * 个人云保存路径。例如：/我的应用收藏/AI助手
         */
        private String yunPath;

    }


    /**
     * 文本模型参数
     */
    @Data
    public static class TextModelParam {
        /**
         * 必须	userId_对话id，用于查询hbase数据
         */
        private String rowkey;
        /**
         * 非必须	会话id，上下文唯一标识
         */
        private String sessionId;
        /**
         * 非必须	指令，比如：总结概要
         */
        private List<String> commands;
        /**
         * 智能体关联id
         */
        private String typeRelationId;
    }
}
