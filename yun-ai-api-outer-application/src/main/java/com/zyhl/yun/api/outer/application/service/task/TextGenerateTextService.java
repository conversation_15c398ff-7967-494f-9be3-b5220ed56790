package com.zyhl.yun.api.outer.application.service.task;

import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;

/**
 * 文生文相关接口
 *
 * <AUTHOR>
 * @date 2024/4/9 17:45
 */
public interface TextGenerateTextService {

    /**
     * 流式文生文处理
     *
     * @param params          dto
     * @return 会话输入返回结果VO
     */
    AlgorithmChatAddVO flowTypeHandle(ChatAddInnerDTO params);

}
