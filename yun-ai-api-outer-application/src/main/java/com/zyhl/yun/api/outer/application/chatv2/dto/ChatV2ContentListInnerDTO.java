package com.zyhl.yun.api.outer.application.chatv2.dto;

import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueResultV2VO;
import lombok.Data;

/**
 * 历史对话列表查询接口，内部数据传输对象
 * 包含：1、请求参数，数据不可改；2、响应参数；3、中间状态数据
 *
 * @Author: WeiJ<PERSON>Kun
 */
@Data
public class ChatV2ContentListInnerDTO {

	/**
	 * 请求参数，里面的数据不可改
	 */
	private AlgorithmChatV2ContentListDTO req;

	/**
	 * 响应结果
	 */
	private DialogueResultV2VO resp = new DialogueResultV2VO();

	public ChatV2ContentListInnerDTO(AlgorithmChatV2ContentListDTO dto) {
		this.req = dto;
	}

}
