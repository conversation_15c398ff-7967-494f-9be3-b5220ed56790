package com.zyhl.yun.api.outer.application.dto;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2024/1/6 16:37
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationTypeListDTO implements Serializable {

    /**
     * 用户id
     */
    private String userId;
    /**
     * 应用id
     */
    private String applicationId;


    /**
     * 渠道号
     */
    @NotEmpty(message = "sourceChannel渠道号不能为空")
    private String sourceChannel;

    public void validate() {
        // 首先检查UserInformationContextHolder中的userId是否有值
        String contextUserId = RequestContextHolder.getUserId();
        // 如果UserInformationContextHolder中的userId有值，就使用它；否则，检查传入的userId是否有值
        if (StringUtils.hasText(contextUserId)) {
            userId = contextUserId;
        } else if (!StringUtils.hasText(userId)) {
            // 如果UserInformationContextHolder中的userId无值，且传入的userId也无值，则抛出异常
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_USERID);
        }
        // 校验渠道号
        if (!StringUtils.hasText(sourceChannel)) {
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_APP_CHANNEL);
        }
    }

}
