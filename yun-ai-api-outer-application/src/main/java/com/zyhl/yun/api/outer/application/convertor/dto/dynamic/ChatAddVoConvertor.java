package com.zyhl.yun.api.outer.application.convertor.dto.dynamic;

import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;
import com.zyhl.yun.api.outer.application.vo.ChatAddBaseVO;
import org.mapstruct.Mapper;

/**
 * 会话输入接口结果VO转换
 * <AUTHOR>
 * @date 2024/5/27 15:57
 */
@Mapper(componentModel = "spring")
public interface ChatAddVoConvertor {

    /**
     * 输入接口结果VO转换
     * @param vo vo
     * @return ChatAddBaseVO
     */
    ChatAddBaseVO chatAddBaseToVO(AlgorithmChatAddVO vo);
}
