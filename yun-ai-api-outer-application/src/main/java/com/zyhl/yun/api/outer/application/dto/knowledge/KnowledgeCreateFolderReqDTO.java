package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 描述：创建个人知识库文件夹入参
 *
 * <AUTHOR>
 */
@Data
@Slf4j
public class KnowledgeCreateFolderReqDTO extends BaseChannelDTO implements Serializable {


    /**
     * 知识库id
     */
    private String baseId;

    /**
     * 文件夹名称
     */
    private String name;

    /**
     * 目的父文件夹 id,如果是根目录则传入"/"表示根目录
     */
    private String parentFileId;

    /**
     * 同名文件处理模式，可选值如下：
     * force_rename：当发现同名文件时，云端强制重命名
     * refuse：当云端存在同名文件时，拒绝更新文件
     * 默认是force_rename
     *
     * @see com.zyhl.hcy.yun.ai.common.base.enums.RenameModeEnum
     */
    private String renameMode;

}
