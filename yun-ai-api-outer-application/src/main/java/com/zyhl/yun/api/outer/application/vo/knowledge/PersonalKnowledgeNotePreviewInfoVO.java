package com.zyhl.yun.api.outer.application.vo.knowledge;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/17 17:48
 */
@Data
public class PersonalKnowledgeNotePreviewInfoVO implements Serializable {

    /**
     * 个人知识库资源ID
     */
    private String resourceId;
    /**
     * 描述文件版本号
     */
    private String version;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 提醒时间
     */
    private String remindTime;
    /**
     * 修订版本号
     */
    private String revision;
    /**
     * 笔记名称
     */
    private String title;
    /**
     * 附件目录
     */
    private String attachmentDir;
    /**
     * 附件目录在网盘上的ID
     */
    private String attachmentDirId;
    /**
     * 网盘上的描述文件ID
     */
    private String contentId;
    /**
     * 便签状态（新建0 | 修改1 | 删除2 | 已同步3 | 临时4 | 回收5）
     */
    private Integer noteStatus;
    /**
     * 来源平台（pc | web | mobile...）
     */
    private String system;
    /**
     * 来源平台描述（如android1.6等）
     */
    private String description;
    /**
     * 最后访问时间
     */
    private String visitTime;
    /**
     * 归档
     */
    private Integer archived;
    /**
     * 笔记是否置顶 （0:不置顶 1:置顶）
     */
    private String topmost;
    /**
     * 短信发送周期（0：无提醒，1：仅一次，2：每天，3：工作日，4：每周，5：每月，6：每年）
     */
    private Integer remindType;
    /**
     * 分享状态
     */
    private String shareStatus;
    /**
     * 分享数
     */
    private String shareCount;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 附件目录ID（新底座）
     */
    private String attachmentCatalogId;
    /**
     * 缩略图所属附件ID
     */
    private String thumbnailAttachmentId;
    /**
     * 缩略图url
     */
    private String thumbUrl;
    /**
     * 内容摘要
     */
    private String summary;
    /**
     * 内容类型
     */
    private String contentType;
    /**
     * 附件信息
     */
    private List<PersonalKnowledgeNoteAttachment> attachments;
    /**
     * 笔记内容
     */
    private List<PersonalKnowledgeNoteContent> contents;
    /**
     * 笔记本列表
     */
    private PersonalKnowledgeNotebook noteBook;
    /**
     * 笔记音频
     */
    private PersonalKnowledgeNoteAudioInfo audio;
    /**
     * 笔记扩展
     */
    private PersonalKnowledgeNoteExpands expand;
    /**
     * 笔记提醒
     */
    private List<PersonalKnowledgeRemind> reminds;
    /**
     * 笔记标注
     */
    private List<PersonalKnowledgeNoteMarkInfo> marks;
    /**
     * 媒体预览信息（音视频）
     */
    private List<MediaPreviewInfo> mediaPreviewInfos;

}
