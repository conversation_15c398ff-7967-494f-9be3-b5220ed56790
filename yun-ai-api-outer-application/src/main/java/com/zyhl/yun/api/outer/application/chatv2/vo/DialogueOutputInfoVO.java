package com.zyhl.yun.api.outer.application.chatv2.vo;

import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.valueobject.HtmlInfo;
import com.zyhl.yun.api.outer.domain.valueobject.KnowledgeSearchInfo;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.domain.vo.chat.search.SearchInfo;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 对话输出内容-VO
 * @Author: WeiJingKun
 */
@Data
public class DialogueOutputInfoVO implements Serializable {

    private static final long serialVersionUID = 8555697290611346676L;

    /**
     * 结果类型
     * 1--大模型文本回答：使用outContent、reasioningConent、personalKnowledgeFileList和networkSearchInfoList；
     * 2--富文本回答：使用outContent；
     * 3--云盘检索结果回答：使用searchInfoList；
     * 4--异步图片任务回答：使用fileList或者outContent；
     * 5--邮件结果：使用AutoMailInfo
     * @see FlowResultTypeEnum
     */
    private Integer resultType;

    /**
     * 流式回答的标题
     */
    private String title;

    /**
     * 输出文本，流式增量出
     * 例如：
     * 第一句：你好，
     * 第二句：欢迎
     * 第三句：来到中国。
     */
    private String outContent;

    /**
     * 思维链过程
     */
    private String reasoningContent;

    /**
     * 个人知识库参考文件，可选。
     */
    private List<KnowledgeSearchInfo> personalKnowledgeFileList;

    /**
     * 大模型联网搜索结果，可选。
     */
    private List<HtmlInfo> networkSearchInfoList;

    /**
     * 云盘搜索信息列表
     */
    private List<SearchInfo> searchInfoList;

    /**
     * 个人云文件信息列表
     */
    private List<File> fileList;

    /**
     * 自动生成的邮件信息
     */
    private MailInfoVO mailInfo;

    /**
     * 4.8 modelType枚举类中的编码值，如果没有使用大模型，则返回空
     */
    private String modelType;

    /**
     * 完成原因
     * "processing"：正在处理
     * "stop"：结束，当前部分的流式结束标识，表示这个部分的输出已结束
     */
    private String finishReason;

    /**
     * 错误码，该部分处理失败时返回
     */
    private String errorCode;

    /**
     * 错误信息，该部分处理失败时返回
     */
    private String errorMessage;

    /**
     * TODO【使用的是哪个字段的值？】
     * 输出时间，RFC 3339格式(东八区)，注：
     * 2019-10-12T14:20:50.52+08:00
     */
    private String outputTime;

    /**
     * 输出内容审批结果;状态码：2通过，其他失败
     */
    private Integer outAuditStatus;

}
