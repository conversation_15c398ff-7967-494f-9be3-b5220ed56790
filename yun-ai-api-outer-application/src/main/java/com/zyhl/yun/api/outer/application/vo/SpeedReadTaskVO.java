package com.zyhl.yun.api.outer.application.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.File;

import java.io.Serializable;
import java.util.Date;

import cn.hutool.core.date.DatePattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description} 快速阅读任务
 * <p>
 *
 * <AUTHOR>
 * @since 4月15 2025
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpeedReadTaskVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 任务处理状态 -1--处理失败 0--处理中 1--处理成功 2--部分成功
     */
    private Integer status;
    /**
     * 文件ID
     */
    private String fileId;
    /**
     * 个人云文档信息
     */
    private File file;
    /**
     * 失败原因
     */
    private String errorMessage;

    /**
     * 创建时间，RFC 3339，2019-08-20T06:51:27.292+08:00
     */
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date createdAt;
    /**
     * 更新时间，RFC 3339，2019-08-20T06:51:27.292+08:00
     */
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date updatedAt;
}
