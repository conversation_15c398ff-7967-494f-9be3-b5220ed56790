package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 知识库标签新增请求参数
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class KnowledgeLabelAddReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 标签id
     */
    private String labelId;

    /**
     * 标签名称
     */
    private String labelName;

}
