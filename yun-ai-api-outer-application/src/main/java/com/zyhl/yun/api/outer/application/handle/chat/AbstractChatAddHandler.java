package com.zyhl.yun.api.outer.application.handle.chat;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.StopWatchUtil;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddContentDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.dto.ContinueTextSseDTO;
import com.zyhl.yun.api.outer.application.handle.chat.impl.AiInternetSearchHandlerImpl;
import com.zyhl.yun.api.outer.application.handle.chat.impl.KnowledgeSseHandlerImpl;
import com.zyhl.yun.api.outer.application.handle.chat.impl.TextSseHandlerImpl;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatContentService;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatMessageService;
import com.zyhl.yun.api.outer.application.service.chat.DialogueRecommendService;
import com.zyhl.yun.api.outer.application.service.external.AsyncSearchService;
import com.zyhl.yun.api.outer.application.service.external.MemberCenterService;
import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;
import com.zyhl.yun.api.outer.config.AllNetworkSearchProperties;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchDiscoveryResult;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchFileResult;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchResult;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.domainservice.CheckSystemDomainService;
import com.zyhl.yun.api.outer.enums.*;
import com.zyhl.yun.api.outer.enums.chat.ChatAddResultTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.search.LeadCopyTypeEnum;
import com.zyhl.yun.api.outer.external.MailExternalService;
import com.zyhl.yun.api.outer.external.NoteExternalService;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * AI对话处理器
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractChatAddHandler implements ChatAddHandler {

    @Resource
    protected AlgorithmChatMessageService algorithmChatMessageService;
    @Resource
    protected AlgorithmChatContentService algorithmChatContentService;
    @Resource
    protected MemberCenterService memberCenterService;
    @Resource
    protected DialogueRecommendService dialogueRecommendService;

    @Resource
    protected AlgorithmChatContentRepository algorithmChatContentRepository;
    @Resource
    protected AiTextResultRepository aiTextResultRepository;

    @Resource
    protected SourceChannelsProperties sourceChannelsProperties;

    @Resource
    private NoteExternalService noteExternalService;
    @Resource
    private MailExternalService mailExternalService;
    @Resource
    protected CheckSystemDomainService checkSystemDomainService;

    @Resource
    private AsyncSearchService asyncSearchService;

    @Resource
    private UidGenerator uidGenerator;

    @Resource
    @Lazy
    private AiInternetSearchHandlerImpl aiInternetSearchHandlerImpl;

    @Resource
    @Lazy
    private TextSseHandlerImpl textSseHandlerImpl;

    @Resource
    private ChatConfigServiceDomainService chatConfigServiceDomainService;

    @Resource
    private ModelProperties modelProperties;

    @Resource
    private AllNetworkSearchProperties allNetworkSearchProperties;

    /**
     * 随机器
     */
    private final Random random = new Random();

    private static final Pattern EQUAL_PATTERN = Pattern.compile("=([^=]*)$");

    /**
     * 执行顺序
     */
    @Getter
    @AllArgsConstructor
    protected enum ExecuteSort {
        /**
         * 参数校验
         */
        PARAM_VALID(10, "参数校验"),
        /**
         * 对话前预处理
         */
        CHAT_BEFORE(20, "对话前预处理"),

        /**
         * AI代码助手
         */
        CHAT_AI_CODER(25, "AI编程"),
        /**
         * AI会议纪要
         */
        AI_GEN_MINUTES(26, "AI会议纪要"),
        /**
         * 干预库匹配
         */
        INTERVENTION(30, "干预库匹配"),
        /**
         * 输入内容送审
         */
        INPUT_AUDIT(40, "输入内容送审"),

        /**
         * AI全网搜
         */
        AI_INTERNET_SEARCH(45, "AI全网搜"),

        /**
         * 输入内容意图识别
         */
        INPUT_INTENTION(50, "输入内容意图识别"),

        SEND_MAIL(55, "发邮件意图处理"),

        /**
         * 引导语类型5
         */
        LEAD_COPY_5(60, "引导语类型5"),
        /**
         * 提示词推荐
         */
        PROMPT_RECOMMEND(70, "提示词推荐"),
        /**
         * 引导语类型1-4
         */
        LEAD_COPY(80, "引导语类型1-4"),
        /**
         * 推荐
         */
        RECOMMEND(90, "多意图推荐、提问语句推荐"),
        /**
         * 搜索
         */
        SEARCH(100, "搜索"),
        /**
         * 云邮搜索
         */
        MAIL_SEARCH(105, "云邮助手搜索"),
        /**
         * 知识库流式对话
         */
        KNOWLEDGE_SSE(110, "知识库流式对话"),
        /**
         * 文本流式对话
         */
        TEXT_SSE(120, "文本流式对话"),
        /**
         * 异步处理
         */
        ASYNC(130, "异步处理：图片工具、v1文本"),

        ;

        /**
         * 排序
         */
        private final int sort;
        /**
         * 描述
         */
        private final String desc;
    }


    @Override
    public int order() {
        return 0;
    }


    @Override
    public boolean execute(ChatAddInnerDTO innerDTO) {
        return true;
    }

    /**
     * 判断是否执行【输入内容意图识别】
     * @Author: WeiJingKun
     * @param innerDTO 用户输入对象
     * @return true：执行；false：不执行
     */
    protected boolean judgeInputIntention(ChatAddInnerDTO innerDTO) {
        // 意图为空 并且 对话内容不为空
        return CharSequenceUtil.isEmpty(innerDTO.getIntentionCode())
                && CharSequenceUtil.isNotEmpty(innerDTO.getContent().getDialogue())
                && Boolean.FALSE.equals(innerDTO.getReqParams().getEnableAllNetworkSearch());
    }

    /**
     * 保存到hbase
     */
    protected AiTextResultEntity saveTextResult(ChatAddInnerDTO innerDTO, String answer, String resourceContent) {
        return saveTextResult(innerDTO, "", answer, resourceContent);
    }

    protected AiTextResultEntity saveTextResult(ChatAddInnerDTO innerDTO, String taskId, String answer, String resourceContent) {
        // 保存结果
        AiTextResultEntity entity = innerDTO.getAiTextResultEntity(taskId, resourceContent, answer);
        aiTextResultRepository.save(entity);

        return entity;
    }

    /**
     * 保存hbase-所有对话结果（入参和结果）
     *
     * @param innerDTO 用户输入对象
     * @param resp     hbase的resp数据对象
     * @Author: WeiJingKun
     */
    protected void saveHbaseAllChatResult(ChatAddInnerDTO innerDTO, AiTextResultRespParameters resp) {
        String userId = innerDTO.getReqParams().getUserId();

        // 保存结果
        AiTextResultEntity entity = new AiTextResultEntity();
        entity.setUserId(userId);
        entity.setDialogueId(innerDTO.getDialogueId());
        entity.setTaskId("");
        entity.setReqParameters(innerDTO.getContent().getDialogue());
        entity.setAttachment("");
        entity.setRespParameters(JsonUtil.toJson(resp));
        aiTextResultRepository.save(entity);

    }

    /**
     * 新增数据到数据库
     */
    protected void add(ChatAddInnerDTO innerDTO, ChatStatusEnum chatStatus) {
        add(innerDTO, chatStatus, null, "");
    }

    protected void add(ChatAddInnerDTO innerDTO, ChatStatusEnum chatStatus, Long taskId, String modelCode) {
        // 先保存会话表
        Long sessionId = algorithmChatMessageService.save(innerDTO.getReqParams());
        innerDTO.setSessionId(sessionId);

        // 再保存对话表
        algorithmChatContentService.add(innerDTO, chatStatus.getCode(), taskId, modelCode);
    }

    /**
     * 新增成功对话
     */
    protected void addSuccess(ChatAddInnerDTO innerDTO, OutContentTypeEnum outContentType) {
        addSuccessAndModelCode(innerDTO, "", outContentType);
    }

    /**
     * 新增成功对话，并且更新
     */
    protected void addSuccessAndModelCode(ChatAddInnerDTO innerDTO, String modelCode, OutContentTypeEnum outContentType) {

        // 智能鉴伪需要outContentResource=3特殊处理
        if (DialogueIntentionEnum.SMART_FAKE_CHECK.getCode().equals(innerDTO.getIntentionCode())) {
            outContentType = OutContentTypeEnum.JSON_OBJECT;
        }

        // 先保存会话表
        Long sessionId = algorithmChatMessageService.save(innerDTO.getReqParams());
        innerDTO.setSessionId(sessionId);

        // 再保存对话表
        algorithmChatContentService.addSuccess(innerDTO, modelCode, outContentType.getType());
    }

    /**
     * 保存tidb-所有对话结果（输入和结果数据）
     *
     * @param innerDTO 用户输入对象
     * @Author: WeiJingKun
     */
    protected void saveTidbAllChatResult(ChatAddInnerDTO innerDTO) {
        /**
         * 先保存会话表
         * 1、innerDTO.asyncSaveMessage： true，则新增数据
         * 2、innerDTO.asyncSaveMessage： false，则更新数据
         */
        algorithmChatMessageService.save(innerDTO);

        // 再保存对话表
        algorithmChatContentService.saveAll(innerDTO);
    }

    /**
     * 根据资源类型获取内容
     *
     * @param dto 输入对象
     * @return 返回内容
     */
    protected String resourceContent(AlgorithmChatAddDTO dto) {
        AlgorithmChatAddContentDTO contentDTO = dto.getContent();
        Integer resourceType = contentDTO.getResourceType();
        String resourceContent;
        switch (ResourceTypeEnum.getType(resourceType)) {
            case MAIL:
                try {
                    // 获取邮件内容
                    Map<String, Object> extInfoMap = contentDTO.getExtInfoMap();
                    String sid = (String) extInfoMap.getOrDefault("sid", "");

                    // 使用正则表达式匹配等号后的值
                    String rmkeyVal = (String) extInfoMap.getOrDefault("rmkey", "");
                    Matcher matcher = EQUAL_PATTERN.matcher(rmkeyVal);
                    String rmkey = matcher.find() ? matcher.group(1) : rmkeyVal;
                    if (CharSequenceUtil.isEmpty(sid) || CharSequenceUtil.isEmpty(rmkey)) {
                        log.error("邮件参数为空，sid：{}，rmkey：{}", sid, rmkey);
                        throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                    }

                    resourceContent = mailExternalService.mailContent(sid, rmkey, contentDTO.getResourceId());
                } catch (YunAiBusinessException e) {
                    AiResultCode resultCode = AiResultCode.getByCodeOrMsg(e.getCode(), e.getMessage());
                    throw new YunAiBusinessException(resultCode.getCode(), resultCode.getMsg());
                } catch (Exception e) {
                    throw new YunAiBusinessException(ResultCodeEnum.UNKNOWN_ERROR);
                }
                break;
            case NOTE:
                // 获取笔记内容
                resourceContent = noteExternalService.noteContent(RequestContextHolder.getToken(), contentDTO.getResourceId());
                break;
            default:
                // 返回默认内容，默认为空字符串
                resourceContent = CharSequenceUtil.EMPTY;
        }

        // 邮件笔记内容校验一起送审
        if (!CharSequenceUtil.isEmpty(resourceContent)) {
            log.info("{}内容送审", ResourceTypeEnum.getType(contentDTO.getResourceType()).getName());
            checkSystemDomainService.checkLocalAndPlatformException(null, dto.getUserId(), resourceContent);
        }

        return resourceContent;
    }

    /**
     * 【云邮助手】搜索意图处理
     *
     * @param innerDTO 用户输入对象
     * @Author: WeiJingKun
     */
    protected void mailSearchIntentionHandle(ChatAddInnerDTO innerDTO) {
        /** sessionId处理 */
        Long sessionId = innerDTO.getSessionId();
        if (sessionId == null) {
            // 如果没有sessionId，需要先构建需要响应的，以及对话数据保存需要的sessionId
            sessionId = uidGenerator.getUID();
            innerDTO.setSessionId(sessionId);
            innerDTO.getReqParams().setSessionId(sessionId.toString());
            // 保存会话（true-新增；false-更新）
            innerDTO.setSaveMessage(Boolean.TRUE);
        }

        /** 智能搜索意图处理 */
        asyncSearchService.searchIntentionHandle(innerDTO);

        /** 【云邮助手】判断是否自动进入全网搜流程 */
        boolean saveSearchResultFlag = mailAutoAllNetworkSearch(innerDTO);

        /** 同步保存数据 */
        if(saveSearchResultFlag){
            StopWatch stopWatch = StopWatchUtil.createStarted();
            try {
                // 保存hbase-所有对话结果
                saveHbaseAllChatResult(innerDTO, innerDTO.getHbaseResp());

                // 保存tidb-所有对话结果
                saveTidbAllChatResult(innerDTO);
            } catch (Exception e) {
                log.error("【云邮助手】搜索功能处理-同步保存数据，execute-异常\n dialogueId：{}", innerDTO.getDialogueId(), e);
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_SERVER_UNAVAILABLE);
            } finally {
                log.info("【云邮助手】搜索功能处理-同步保存数据，方法-耗时：{}", StopWatchUtil.logTime(stopWatch));
                StopWatchUtil.clearDuration();
            }
        } else {
            log.info("【云邮助手】搜索功能处理-执行全网搜");
            // 去掉引导文案
            innerDTO.getRespParams().setLeadCopy(null);
            // 去掉全网搜推荐数据
            innerDTO.getRespParams().getRecommend().setAllNetworkSearchList(null);
        }
    }

    /**
     * 【云邮助手】判断是否自动进入全网搜流程
     * @Author: WeiJingKun
     *
     * @param innerDTO 用户输入对象
     * @return true-普通对话搜索；false-AI全网搜
     */
    private boolean mailAutoAllNetworkSearch(ChatAddInnerDTO innerDTO) {
        AlgorithmChatAddVO resVO = innerDTO.getRespParams();
        SearchResult searchResult = resVO.getSearchResult();
        DialogueRecommendVO recommendVO = resVO.getRecommend();
        // 搜索无视频资源（搜视频014、发现广场搜索022）
        boolean noSearchResultFlag = false;
        if (CollUtil.isEmpty(resVO.getSearchInfoList())) {
            noSearchResultFlag = true;
        } else {
            // 个人资产搜索
            SearchFileResult searchFileResult = searchResult.getSearchFileResult();
            // 发现广场搜索
            SearchDiscoveryResult searchDiscoveryResult = searchResult.getSearchDiscoveryResult();
            // 搜索结果都为空时，noSearchResultFlag = true
            noSearchResultFlag = (null == searchFileResult || CollUtil.isEmpty(searchFileResult.getFileList()))
                    && (null == searchDiscoveryResult || CollUtil.isEmpty(searchDiscoveryResult.getDiscoveryList()));
        }
        // 自动直接进入全网搜流程 以下两种情况自动进入全网搜流程：1、联网模型+已开开关 2、非联网模型
        boolean isNetworked = isNetworked(innerDTO);
        if(isNetworked && noSearchResultFlag){
            // 更新为对话意图（000），执行AI全网搜
            innerDTO.setTextGenerateTextIntention();
            innerDTO.getReqParams().setEnableAllNetworkSearch(Boolean.TRUE);
            // 清空普通搜索产生的leadCopy
            innerDTO.getRespParams().setLeadCopy(null);
            // 执行AI全网搜的流程
            return aiInternetSearchHandlerImpl.run(innerDTO);
        }
        return true;
    }

    /**
     * 【小天助手】搜索意图处理
     *
     * @param innerDTO 用户输入对象
     * @Author: WeiJingKun
     */
    protected void searchIntentionHandle(ChatAddInnerDTO innerDTO) {
        /** sessionId处理 */
        Long sessionId = innerDTO.getSessionId();
        if (sessionId == null) {
            // 如果没有sessionId，需要先构建需要响应的，以及对话数据保存需要的sessionId
            sessionId = uidGenerator.getUID();
            innerDTO.setSessionId(sessionId);
            innerDTO.getReqParams().setSessionId(sessionId.toString());
            // 保存会话（true-新增；false-更新）
            innerDTO.setSaveMessage(Boolean.TRUE);
        }

        /** 智能搜索意图处理 */
        asyncSearchService.searchIntentionHandle(innerDTO);

        /** 【小天助手】判断是否自动进入全网搜流程 */
        boolean saveSearchResultFlag = autoAllNetworkSearch(innerDTO);

        /** 同步保存数据 */
        if(saveSearchResultFlag){
            StopWatch stopWatch = StopWatchUtil.createStarted();
            try {
                // 保存hbase-所有对话结果
                saveHbaseAllChatResult(innerDTO, innerDTO.getHbaseResp());

                // 保存tidb-所有对话结果
                saveTidbAllChatResult(innerDTO);
            } catch (Exception e) {
                log.error("【小天助手】搜索功能处理-同步保存数据，execute-异常\n dialogueId：{}", innerDTO.getDialogueId(), e);
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_SERVER_UNAVAILABLE);
            } finally {
                log.info("【小天助手】搜索功能处理-同步保存数据，方法-耗时：{}", StopWatchUtil.logTime(stopWatch));
                StopWatchUtil.clearDuration();
            }
        } else {
            log.info("【小天助手】搜索功能处理-执行全网搜");
            // 去掉引导文案
            innerDTO.getRespParams().setLeadCopy(null);
            // 去掉全网搜推荐数据
            innerDTO.getRespParams().getRecommend().setAllNetworkSearchList(null);
        }
    }

    /**
     * 【小天助手】判断是否自动进入全网搜流程
     * @Author: WeiJingKun
     *
     * @param innerDTO 用户输入对象
     * @return true-普通对话搜索；false-AI全网搜
     */
    private boolean autoAllNetworkSearch(ChatAddInnerDTO innerDTO) {
        AlgorithmChatAddVO resVO = innerDTO.getRespParams();
        /**
         * 自动直接进入全网搜流程
         * 1、搜索无资源
         * 2、联网模型+已开开关 或 非联网模型
         */
        boolean isNetworked = isNetworked(innerDTO);
        if(isNetworked && CollUtil.isEmpty(resVO.getSearchInfoList())){
            // 更新为对话意图（000），执行AI全网搜
            innerDTO.setTextGenerateTextIntention();
            innerDTO.getReqParams().setEnableAllNetworkSearch(Boolean.TRUE);
            // 清空普通搜索产生的leadCopy
            innerDTO.getRespParams().setLeadCopy(null);
            // 全网搜没结果，进行大模型联网回答
            innerDTO.setContinueTextSseDTO(new ContinueTextSseDTO(allNetworkSearchProperties.getModelCode()));
            // 执行AI全网搜的流程
            return aiInternetSearchHandlerImpl.run(innerDTO);
        }
        return true;
    }

    private boolean isNetworked(ChatAddInnerDTO innerDTO) {
        // 如果开启了开启AI搜索，直接返回true
        if (Boolean.TRUE.equals(innerDTO.getReqParams().getEnableAiSearch())) {
            return true;
        }
        String userId = innerDTO.getReqParams().getUserId();
        AssistantEnum assistantEnum = innerDTO.getContent().getAssistantEnum();
        String businessType = innerDTO.getContent().getBusinessType();
        // 获取需要选中的模型
        ChatConfigEntity entity = chatConfigServiceDomainService.getUserCanUseModel(userId, RequestContextHolder.getPhoneNumber(), assistantEnum, businessType);
        if (null != entity) {
            String modelType = entity.getModelType();
            // 先判断模型是否为空，为空直接返回False
            if (StringUtils.isBlank(modelType)) {
                log.error("【全网搜联网判断】模型为空，model 配置为空");
                return false;
            }
            // 判断是否联网模型
            Map<String, ModelProperties.ModelLimitConfig> limit = modelProperties.getLimitByAssistantEnum(assistantEnum, businessType);
            if (ObjectUtil.isEmpty(limit)) {
                log.error("【全网搜联网判断】模型配置为空，model 配置为空");
                return false;
            }
            ModelProperties.ModelLimitConfig modelLimitConfig = limit.get(modelType);
            if (null == modelLimitConfig) {
                log.error("【全网搜联网判断】模型配置为空，model 配置为空");
                return false;
            }
            if (!modelLimitConfig.isEnableNetworkSearch()) {
                log.info("【全网搜联网判断】非联网模型自动直接进入全网搜流程");
                return true;
            }
            // 判断用户是否设置联网
            Integer configNetworkSearchStatus = entity.getNetworkSearchStatus();
            if (null != configNetworkSearchStatus) {
                // 用户配置了，按用户配置的设置
                log.info("【全网搜联网判断】按用户配置的设置,是否联网配置为：{}", configNetworkSearchStatus);
                return ChatNetworkSearchStatusEnum.isOpen(configNetworkSearchStatus);
            }
        } else {
            log.info("【全网搜联网判断】用户未配置模型");
            return false;
        }
        return false;
    }

    /**
     * 文本大模型处理器
     * @param handler
     * @return boolean
     */
	public static boolean isTextModelHandler(AbstractChatAddHandler handler) {
		return (handler instanceof TextSseHandlerImpl || handler instanceof KnowledgeSseHandlerImpl);
	}

    /**
     * 【AI全网搜】处理搜索不到数据的情况
     *
     * @param innerDTO 用户输入对象
     * @param isBlack 是否是黑名单
     * @Author: WeiJingKun
     */
    protected boolean aiInternetSearchHandlerNone(ChatAddInnerDTO innerDTO, boolean isBlack) {
        // 判断是否继续执行大模型回答
        if(innerDTO.getContinueTextSseDTO().isContinueTextSseHandler()){
            return textSseHandlerImpl.run(innerDTO);
        }

        // 不进行大模型回答，直接保存数据
        String msg;
        if (isBlack) {
            msg = allNetworkSearchProperties.getBlackText();
        } else {
            // 1. random get return text
            List<String> msgList = allNetworkSearchProperties.getEmptyResultText();
            msg = msgList.get(random.nextInt(msgList.size()));
        }

        // 2. set msg
        innerDTO.getRespParams().setLeadCopy(LeadCopyVO.builder().type(LeadCopyTypeEnum.TYPE6.getCode()).promptCopy(msg).build());
        // 3. set synchronized return
        innerDTO.getRespParams().setResultType(ChatAddResultTypeEnum.SYNCHRONIZATION.getType());
        // 4. hbase
        saveTextResult(innerDTO, "", "");
        // 5. tidb
        addSuccess(innerDTO, OutContentTypeEnum.TEXT);
        return false;
    }

}
