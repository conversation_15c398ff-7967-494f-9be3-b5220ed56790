package com.zyhl.yun.api.outer.application.vo;

import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueFlowResultVO;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueIntentionOutput;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/3 16:35
 */
@Data
public class SecondStreamChatAddV2VO implements Serializable {

    /**
     * 当前对话id,异步任务必须返回（必填）
     */
    private String dialogueId;


    /**
     * 返回结果的标题（必填）
     * commands为“024”创建笔记和“027”创建语音笔记时，返回标题；
     * commands为”000“时，返回知识库开头；
     * commands为“028”邮件搜索时返回卡片标题；
     * commands为“012”-“018”、“020”-“023”和“028”搜索意图时，返回搜索结果的返回词
     */
    private String title;

    /**
     * 输出意图指令
     */
    private DialogueIntentionOutput outputCommand;

    /**
     * 必填
     * 结束原因，当为stop时，表示该次对话已结束。
     * "processing"：正在处理
     * "stop"：结束，整个对话的结束标识
     */
    private String finishReason;

    /**
     * 结束语
     */
    private String ending;

    /**
     * 引导文案
     */
    private LeadCopyVO leadCopy;

    /**
     * 流式结果
     * commands为“032”发邮件时返回，content字段会多次流式返回
     */
    private DialogueFlowResultVO flowResult;

    /**
     * 对话结果推荐，当对话信息不为空时返回。
     * 展示在对话和框的中间一层
     */
    private DialogueRecommendVO middleRecommend;

    /**
     * 对话结果推荐，当对话信息不为空时返回
     * 展示在对话框的最下面
     */
    private DialogueRecommendVO recommend;

}
