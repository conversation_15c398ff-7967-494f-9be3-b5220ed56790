package com.zyhl.yun.api.outer.application.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YunDiskContentVO {

	/**
	 * 文件id
	 */
	private String fileId;

	/**
	 * 文件名称
	 */
	private String fileName;

	/**
	 * 文件大小
	 */
	private Long fileSize;

	/**
	 * 文件扩展名，一般是后缀名
	 */
	private String fileSuffix;

	/**
	 * 缩略图url
	 */
	private String thumbnailUrl;
	
	/**
	 * 原图url
	 */
	private String url;

}
