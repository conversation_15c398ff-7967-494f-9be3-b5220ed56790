package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.security.SecureRandom;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.zyhl.yun.api.outer.application.dto.ContinueTextSseDTO;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.hcy.yun.ai.common.base.enums.PantaLabelEnum;
import com.zyhl.hcy.yun.ai.common.base.es.domain.datahelper.repository.EsPanTaResourceRepository;
import com.zyhl.hcy.yun.ai.common.base.es.dto.PanTaResourceOuterSearchV2DTO;
import com.zyhl.hcy.yun.ai.common.base.es.entity.EsPanTaResourceEntity;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.hcy.yun.ai.common.model.api.client.ali.dto.GenericSearchDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.ali.vo.GenericSearchVO;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.dto.TextNerExtractDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.event.TextModelStreamEventListener;
import com.zyhl.hcy.yun.ai.common.model.api.client.vo.TextModelBaseVo;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelSearchOptionDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.service.BenefitService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.AiInternetSearchConvertor;
import com.zyhl.yun.api.outer.application.dto.AiInternetSearchQueryDTO;
import com.zyhl.yun.api.outer.application.dto.AiInternetSearchScheduleDTO;
import com.zyhl.yun.api.outer.application.dto.AiMultiSearchDTO;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.config.AllNetworkSearchProperties;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.config.textmodel.ModelPromptProperties;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.domain.req.LlmChatReqDTO;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.llm.LlmChatMessage;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.IntentionRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.domainservice.BlackResourceHandleService;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.domainservice.QpsLimitService;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.EntityResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.SseNameEnum;
import com.zyhl.yun.api.outer.enums.chat.search.LeadCopyTypeEnum;
import com.zyhl.yun.api.outer.external.AiInternetSearchExternalService;
import com.zyhl.yun.api.outer.external.CmicTextService;
import com.zyhl.yun.api.outer.external.LlmChatExternalService;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.MultiSearchVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import java.util.stream.Stream;

/**
 * AI全网搜
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SpecialAiInternetSearchHandlerImpl extends AbstractChatAddV2Handler {

	private static final String REGEX_PUBLISH_LINK = "\\[(.*?)\\]";

	/**
	 * 当前执行顺序
	 */
	private ExecuteSort thisExecuteSort = ExecuteSort.SPECIAL_AI_INTERNET_SEARCH;

	@Resource
	private AllNetworkSearchProperties searchProperties;
	@Resource
	private EsPanTaResourceRepository pantaResourceRepository;
	@Resource
	private ModelProperties modelProperties;
	@Resource
	private TextModelExternalService textModelExternalService;
	@Resource
	private QpsLimitService qpslimitService;
	@Resource
	private ModelPromptProperties modelPromptProperties;
	@Resource
	private AllNetworkSearchProperties allNetworkSearchProperties;
	@Resource
	private CmicTextService cmicTextService;
	@Resource
	private AiInternetSearchExternalService aiInternetSearchExternalService;
	@Resource
	private AiInternetSearchConvertor convertor;
	@Resource
	private BlackResourceHandleService blackResourceHandleService;
	@Resource
	private AlgorithmChatContentRepository algorithmChatContentRepository;
	@Resource
	private BenefitService benefitService;
	@Resource
	private DataSaveService dataSaveService;
	@Resource
	private ChatConfigServiceDomainService chatConfigServiceDomainService;
	@Resource
	private TextModelTextSseHandlerImpl textModelTextSseHandlerImpl;
	@Resource
	private TextModelKnowledgeSseHandlerImpl textModelKnowledgeSseHandlerImpl;
	@Resource
	private AiTextResultRepository aiTextResultRepository;

	@Resource
	private LlmChatExternalService llmChatExternalService;

	/**
	 * 随机器
	 */
	private final SecureRandom random = new SecureRandom();
	/** 【用户输入原文】占位符 */
	private static final String REPLACE_KEY_OF_CONTENT = "{CONTENT}";
	/** 搜索【正版平台】结果提示词占位符 */
	private static final String REPLACE_KEY_OF_GENERIC_PLATFORM = "{GENERIC_PLATFORM}";
	/** 搜索【网盘链接】结果提示词占位符 */
	private static final String REPLACE_KEY_OF_CLOUD_STORAGE_LINK = "{CLOUD_STORAGE_LINK}";
	/** 搜索【在线网址】结果提示词占位符 */
	private static final String REPLACE_KEY_OF_ONLINE_URL = "{ONLINE_URL}";
	/** 【上一段对话内容】占位符 */
	private static final String REPLACE_KEY_OF_HISTORY_RESULT = "{HISTORY_RESULT}";
	/** 空数组字符串 */
	public static final String EMPTY_JSON_ARRAY = "[]";

	/** AI全网搜-大模型提示词配置key——system */
	private static final String AI_INTERNET_SEARCH_SYSTEM_PROMPT = "ai_internet_search_system_prompt";
	/** 【查看更多】AI全网搜-大模型提示词配置key——system */
	private static final String MORE_AI_INTERNET_SEARCH_SYSTEM_PROMPT = "more_ai_internet_search_system_prompt";
	/** 【查看更多】AI全网搜-大模型提示词配置key——system */
	private static final String DOMESTIC_AI_INTERNET_SEARCH_SYSTEM_PROMPT = "domestic_ai_internet_search_system_prompt";

	/**
	 * 小站资源snippet分隔符
	 */
	private static final String CHINESE_COMMA = "，";
	private static final String PANTA_TITLE = "标题：";
	private static final String PANTA_RESOURCE_TAG = "资源标签类型：";
	private static final String PANTA_DIRECTOR = "导演：";
	private static final String PANTA_CAST_NAME = "主演：";
	private static final String PANTA_OVERVIEW = "简介：";

	/** 【网盘链接】搜索类型key */
	private static final String PANTA_RESULT_KEY = "panta";
	private static final String ALI_LINK_RESULT_KEY = "ali_link";
	/** 【正版平台】搜索类型key */
	private static final String ALI_GENUINE_RESULT_KEY = "ali_genuine";
	/** 【在线网址】搜索类型key */
	private static final String ALI_ONLINE_RESULT_KEY = "ali_online";

	/**
	 * 超时线程池
	 */
	@Resource(name = "multiSearchSchedulePool")
	private ScheduledExecutorService multiSearchSchedulePool;

	/**
	 * 调用通用搜索的线程池
	 */
	@Resource(name = "multiSearchThreadPool")
	private ExecutorService multiSearchThreadPool;

	@Override
	public void afterPropertiesSet() throws Exception {
		// 支持的业务初始化
		List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
		thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
		thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
		this.setBusinessTypes(thisBusinessTypes);
	}

	@Override
	public int order() {
		return thisExecuteSort.getSort();
	}

	@Override
	public boolean execute(ChatAddHandleDTO handleDTO) {
		return Boolean.TRUE.equals(handleDTO.getReqDTO().getDialogueInput().isEnableAllNetworkSearch())
				&& Boolean.TRUE.equals(searchProperties.isExecute());
	}

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		log.info("进入{}", thisExecuteSort.getDesc());
		// 首次查询到的全网搜结果
		List<MultiSearchVO> filterResult;
		// 上一次全网搜结果
		String historyResult = null;
		// 提示词key
		String promptKey = AI_INTERNET_SEARCH_SYSTEM_PROMPT;
		/** 获取更多搜索结果 */
		if(handleDTO.getInputInfoDTO().isEnableGetMoreAllNetworkSearch()){
			// 从hbase查询结果
			List<AiTextResultEntity> hbaseResult = aiTextResultRepository.getByRowKeyList(handleDTO.getReqDTO().getUserId(),
					handleDTO.getInputInfoDTO().getAttachment().getDialogueIdList());
			LogCommonUtils.printlnStrLog("【AI全网搜】【获取更多搜索结果】【hbase查询结果】", JsonUtil.toJson(hbaseResult));
			if (CollUtil.isEmpty(hbaseResult) || CharSequenceUtil.isBlank(hbaseResult.get(0).getRespParameters())){
				// 【执行大模型回答】
				log.info("【AI全网搜】【hbase查询结果为空】【执行大模型回答】");
				return handleNone(handleDTO, true, false);
			}
			AiTextResultRespParameters respParameters = JSON.parseObject(hbaseResult.get(0).getRespParameters(), AiTextResultRespParameters.class);
			filterResult = respParameters.getAiInternetSearchResultList();
			historyResult = respParameters.getOutputList().get(0).getOutContent();
			promptKey = MORE_AI_INTERNET_SEARCH_SYSTEM_PROMPT;

			// filterResult所有voList值都为空
			if (filterResult.stream().allMatch(vo -> CollUtil.isEmpty(vo.getVoList()))) {
				// 【执行大模型回答】
				log.info("【AI全网搜】【hbase查询结果为空】【执行大模型回答】");
				return handleNone(handleDTO, true, false);
			}
		} else {
			/** 多线程并行 */
			CountDownLatch countDownLatch = new CountDownLatch(2);
			Map<String, String> logMap = MDC.getCopyOfContextMap();
			// 获取主线程的ThreadLocal信息，并且异步调用前，将父线程中的请求信息绑定给子线程
			RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo = RequestContextHolder.getThreadLocalInfoAndBindingAttributes();
			/** 异步处理小站实体抽取 */
			AtomicReference<Map<PantaLabelEnum, List<String>>> extractResultAtomic = new AtomicReference<>();
			asyncSearchEntityExtract(handleDTO, logMap, mainThreadLocalInfo, extractResultAtomic, countDownLatch);
			/** 异步判断是否国内资源 */
			AtomicReference<Boolean> isDomesticAtomic = new AtomicReference<>();
			asyncGetIsDomestic(handleDTO, logMap, mainThreadLocalInfo, isDomesticAtomic, countDownLatch);
			/** 等待所有任务完成 */
			try {
				boolean await = countDownLatch.await(searchProperties.getSearchWaitTime(), TimeUnit.SECONDS);
				if (!await) {
					log.info("【全网搜countDownLatch】超时，超时时间：{}s", searchProperties.getSearchWaitTime());
				}
			} catch (Exception e) {
				log.error("【全网搜countDownLatch】线程等待异常", e);
			}

			// true：属于国内资源
			if(isDomesticAtomic.get() != null && isDomesticAtomic.get()){
				// 【提示文案】
				log.info("【AI全网搜】【属于国内资源】【提示文案】");
				return handleNone(handleDTO, false, false);
			}

			// 1. 调用算法【搜索实体抽取】接口
			Map<PantaLabelEnum, List<String>> extractResult = extractResultAtomic.get() == null ? new HashMap<>() : extractResultAtomic.get();
			// 把extractResult中的资源链接类型取出来
			Map<PantaLabelEnum, List<String>> resourceTypeMap = extractResult.entrySet().stream()
					.filter(entry -> PantaLabelEnum.RESOURCE_TYPE.equals(entry.getKey()))
					.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
			if (CollUtil.isNotEmpty(resourceTypeMap)) {
				// 删掉extractResult中的资源链接类型
				extractResult.remove(PantaLabelEnum.RESOURCE_TYPE);
			}
			// 1.1 没有抽取到实体
			if (CollUtil.isEmpty(extractResult)) {
				// 【执行大模型回答】
				log.info("【AI全网搜】【没有抽取到实体】【执行大模型回答】");
				return handleNone(handleDTO, true, false);
			}
			// 1.2 实体重写
			if(CharSequenceUtil.isNotBlank(allNetworkSearchProperties.getSearchEntityRewriteRegex())){
				extractResult.forEach((key, valueList) -> {
					List<String> updatedList = valueList.stream()
							// 去掉空字符串（这个是必要的，否则下面会空指针）
							.filter(CharSequenceUtil::isNotBlank)
							.map(item -> item.replaceAll(allNetworkSearchProperties.getSearchEntityRewriteRegex(), ""))
							// 去掉空字符串
							.filter(CharSequenceUtil::isNotBlank)
							.collect(Collectors.toList());
					extractResult.put(key, updatedList);
				});
			}
			log.info("【AI全网搜】【实体重写】，重写后：{}", JsonUtil.toJson(extractResult));
			// 1.3 黑名单资源过滤
			extractResultFilter(extractResult, true);
			// 1.4 黑名单资源过滤后，没有实体
			if (CollUtil.isEmpty(extractResult)) {
				// 【提示文案】
				log.info("【AI全网搜】【黑名单资源过滤】过滤后，实体为：[]，【提示文案】");
				return handleNone(handleDTO, false, true);
			}
			// 1.5 屏蔽资源过滤
			extractResultFilter(extractResult, false);
			// 1.6 屏蔽资源过滤后，没有实体
			if (CollUtil.isEmpty(extractResult)) {
                // 【执行大模型回答】
				log.info("【AI全网搜】【屏蔽资源过滤】过滤后，实体为：[]，【执行大模型回答】");
				return handleNone(handleDTO, true, false);
			}

			// 2. 组装搜索参数
			List<AiMultiSearchDTO> paramList = assemblerSearchData(extractResult, resourceTypeMap);
			// 2.1 没有搜索参数
			if (CollUtil.isEmpty(paramList)) {
				// 【执行大模型回答】
				log.info("【AI全网搜】【没有搜索参数】【执行大模型回答】");
				return handleNone(handleDTO, true, false);
			}

			// 3. 多线程获取搜索结果
			List<MultiSearchVO> multiSearchResult = multiSearch(paramList);
			// 3.1 没有搜索结果
			if (CollUtil.isEmpty(multiSearchResult)) {
				// 【执行大模型回答】
				log.info("【AI全网搜】【多线程获取搜索结果】没有搜索结果【执行大模型回答】");
				return handleNone(handleDTO, true, false);
			}

			// 4. 所有搜索结果过滤
			filterResult = dataFilter(multiSearchResult);
			// 4.1 filterResult所有voList值都为空
			if (filterResult.stream().allMatch(vo -> CollUtil.isEmpty(vo.getVoList()))) {
				// 【执行大模型回答】
				log.info("【AI全网搜】【所有搜索结果过滤】过滤后，搜索结果为：[]，【执行大模型回答】");
				return handleNone(handleDTO, true, false);
			}
			// 4.2 【黑名单资源过滤】filterResult所有voList值（最终更新 entry 的 value 为过滤后的列表）
			multiSearchResultFilter(filterResult, true);
			// 4.3 【黑名单资源过滤】filterResult所有voList值，过滤后，没有搜索结果
			if (filterResult.stream().allMatch(vo -> CollUtil.isEmpty(vo.getVoList()))) {
				// 【提示文案】
				log.info("【AI全网搜】【黑名单资源过滤】过滤后，搜索结果为：[]，【提示文案】");
				return handleNone(handleDTO, false, true);
			}
			// 4.4 【屏蔽资源过滤】filterResult所有voList值（最终更新 entry 的 value 为过滤后的列表）
			multiSearchResultFilter(filterResult, false);
			// 4.5 【屏蔽资源过滤】filterResult所有voList值，过滤后，没有搜索结果
			if (filterResult.stream().allMatch(vo -> CollUtil.isEmpty(vo.getVoList()))) {
				// 【执行大模型回答】
				log.info("【AI全网搜】【屏蔽资源过滤】过滤后，搜索结果为：[]，【执行大模型回答】");
				return handleNone(handleDTO, true, false);

			}

		}

		// 5. 根据搜索结果，获取对应的query文本
		AiInternetSearchQueryDTO queryDTO = createQueryInfo(filterResult);

		// 6. 扣减权益
		benefitService.consumeBenefit(handleDTO.getReqDTO(), RequestContextHolder.getPhoneNumber(), handleDTO.getDialogueId());

		// 7. 保存对话
		handleDTO.setAiInternetSearchResultList(filterResult);
		save(handleDTO);

		// 8. 智能调度
		// 8.1 构建流式对话事件监听对象
		SseEventListener event = new SseEventListener(handleDTO, null);
		event.getSseEmitterOperate().setSseName(SseNameEnum.AI_INTERNET_SEARCH.getCode());
		// 8.2 构建智能调度参数
		AiInternetSearchScheduleDTO searchScheduleDTO = AiInternetSearchScheduleDTO.builder()
				.content(handleDTO.getReqDTO().getDialogueInput().getDialogue())
				.sourceChannel(handleDTO.getReqDTO().getSourceChannel())
				.clientType(RequestContextHolder.getClientType())
				.historyResult(historyResult)
				.promptKey(promptKey)
				.queryDTO(queryDTO)
				.build();
		// 8.3 智能调度
		schedule(handleDTO, event, searchScheduleDTO);

		return false;
	}

	/**
	 * 异步处理小站实体抽取
	 * @Author: WeiJingKun
	 *
	 * @param handleDTO 用户输入对象
	 * @param logMap 日志
	 * @param mainThreadLocalInfo 主线程的ThreadLocal信息
	 * @param extractResultAtomic 抽取的实体
	 * @param countDownLatch 上层线程计数器
	 */
	private void asyncSearchEntityExtract(ChatAddHandleDTO handleDTO, Map<String, String> logMap, RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo, AtomicReference<Map<PantaLabelEnum, List<String>>> extractResultAtomic, CountDownLatch countDownLatch) {
		multiSearchSchedulePool.submit(() -> {
			long startTime = System.currentTimeMillis();
			try {
				MDC.setContextMap(logMap);
				// 把主线程ThreadLocal信息set到子线程
				RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);
				// 调用算法【搜索实体抽取】接口
				extractResultAtomic.set(searchEntityExtract(handleDTO.getReqDTO().getDialogueInput().getDialogue()));
			} catch (Exception e) {
				log.error("【AI全网搜】【异步处理小站实体抽取】异常，异常信息：{}", e.getMessage(), e);
			} finally {
				countDownLatch.countDown();
				long duration = System.currentTimeMillis() - startTime;
				MDC.put(LogConstants.DURATION, String.valueOf(duration));
				log.info("【AI全网搜】【异步处理小站实体抽取】耗时：{}ms", duration);
				MDC.remove(LogConstants.DURATION);
			}
		});
	}

	/**
	 * 异步判断是否国内资源
	 * @Author: WeiJingKun
	 *
	 * @param handleDTO 用户输入对象
	 * @param logMap 日志
	 * @param mainThreadLocalInfo 主线程的ThreadLocal信息
	 * @param isDomesticAtomic 是否国内资源
	 * @param countDownLatch 上层线程计数器
	 */
	private void asyncGetIsDomestic(ChatAddHandleDTO handleDTO, Map<String, String> logMap, RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo, AtomicReference<Boolean> isDomesticAtomic, CountDownLatch countDownLatch) {
		multiSearchSchedulePool.submit(() -> {
			long startTime = System.currentTimeMillis();
			try {
				MDC.setContextMap(logMap);
				// 把主线程ThreadLocal信息set到子线程
				RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);
				// 调用大模型判断是否国内资源
				isDomesticAtomic.set(getIsDomestic(handleDTO.getReqDTO().getDialogueInput().getDialogue()));
			} catch (Exception e) {
				log.error("【AI全网搜】【异步判断是否国内资源】异常，异常信息：{}", e.getMessage(), e);
			} finally {
				countDownLatch.countDown();
				long duration = System.currentTimeMillis() - startTime;
				MDC.put(LogConstants.DURATION, String.valueOf(duration));
				log.info("【AI全网搜】【异步判断是否国内资源】耗时：{}ms，对话：{}，结果：{}",
						duration, handleDTO.getReqDTO().getDialogueInput().getDialogue(), isDomesticAtomic.get());
				MDC.remove(LogConstants.DURATION);
			}
		});
	}

	/**
	 * 搜索结果过滤
	 * @Author: WeiJingKun
	 *
	 * @param multiSearchResult 搜索结果
	 * @param isBlack true-黑名单资源过滤；false-屏蔽资源过滤
	 */
	private void multiSearchResultFilter(List<MultiSearchVO> multiSearchResult, boolean isBlack){
		for (MultiSearchVO result : multiSearchResult) {
			List<String> titleList = result.getVoList().stream().map(GenericSearchVO::getTitle).distinct().collect(Collectors.toList());
			List<String> filterTitleList = new ArrayList<>();
			if(isBlack){
				// 黑名单资源过滤
				filterTitleList = blackResourceHandleService.blackResourceFilter(titleList);
			} else {
				// 屏蔽资源过滤
				filterTitleList = blackResourceHandleService.shieldResourceFilter(titleList);
			}

			List<String> finalFilterTitleList = filterTitleList;
			// 获取过滤后的列表
			List<GenericSearchVO> filteredList = result.getVoList().stream()
					.filter(vo -> finalFilterTitleList.contains(vo.getTitle()))
					.collect(Collectors.toList());
			result.setVoList(filteredList);
		}
	}

	/**
	 * 抽取到的实体过滤
	 * @Author: WeiJingKun
	 *
	 * @param extractResult 抽取到的实体
	 * @param isBlack true-黑名单资源过滤；false-屏蔽资源过滤
	 */
	private void extractResultFilter(Map<PantaLabelEnum, List<String>> extractResult, boolean isBlack){
		Iterator<Map.Entry<PantaLabelEnum, List<String>>> iterator = extractResult.entrySet().iterator();
		while (iterator.hasNext()) {
			Map.Entry<PantaLabelEnum, List<String>> entry = iterator.next();
			List<String> filterList = new ArrayList<>();
			if(isBlack){
				// 黑名单资源过滤
				filterList = blackResourceHandleService.blackResourceFilter(entry.getValue());
			} else {
				// 屏蔽资源过滤
				filterList = blackResourceHandleService.shieldResourceFilter(entry.getValue());
			}
			if (CollUtil.isEmpty(filterList)) {
				iterator.remove();
			} else {
				entry.setValue(filterList);
			}
		}
	}

	/**
	 * 【AI全网搜】处理搜索不到数据的情况
	 *
	 * @param handleDTO 对话参数
	 * @param continueTextSse true-执行大模型回答
	 * @param isBlack true-黑名单过滤，false-屏蔽过滤
	 */
	private boolean handleNone(ChatAddHandleDTO handleDTO, boolean continueTextSse, boolean isBlack) {
		/** 非黑名单过滤，执行大模型回答 */
		if(continueTextSse){
			// 非执行全网搜，参数置为false
			handleDTO.getReqDTO().getDialogueInput().setEnableAllNetworkSearch(Boolean.FALSE);

			/** 执行大模型回答 */
			// 使用大模型回答时，不需要返回【大模型推荐】
			removeTextIntentionRecommend(handleDTO);
			// 创建一个【继续执行大模型回答DTO】对象，使用用户选择的模型
			ChatConfigEntity chatConfigEntity = chatConfigServiceDomainService.getUserCanUseModel(handleDTO.getReqDTO().getUserId(),
					RequestContextHolder.getPhoneNumber(), handleDTO.getAssistantEnum(), handleDTO.getBusinessType());
			handleDTO.setContinueTextSseDTO(new ContinueTextSseDTO(chatConfigEntity.getModelType(), handleDTO.getInputInfoDTO().isEnableForceNetworkSearch()));
			// 勾选知识库时，走知识库对话
			if(textModelKnowledgeSseHandlerImpl.execute(handleDTO)){
				// 进入知识库对话
				if(textModelKnowledgeSseHandlerImpl.run(handleDTO)){
					// 选择了知识库，但是未命中知识库，走普通对话
					return textModelTextSseHandlerImpl.run(handleDTO);
				}
				return false;
			} else {
				return textModelTextSseHandlerImpl.run(handleDTO);
			}
		}

		/** 不进行大模型回答，直接保存数据 */
		// 1. 构建返回的文本
		String msg;
		if (isBlack) {
			// 搜索实体/搜索结果命中黑名单返回语句
			msg = searchProperties.getBlackText();
		} else {
			// 版权提示
			msg = searchProperties.getCopyrightNotice();
		}

		// 2. 把文本设置到引导文案对象
		handleDTO.getRespVO().setLeadCopy(LeadCopyVO.builder().type(LeadCopyTypeEnum.TYPE6.getCode()).promptCopy(msg).build());
		// 3. 保存hbase
		dataSaveService.saveTextResult(handleDTO, StringUtils.EMPTY, StringUtils.EMPTY);
		// 4. 保存tidb
		dataSaveService.addSuccess(handleDTO, OutContentTypeEnum.TEXT);

		// 流式输出
		handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(handleDTO.getRespVO()));
		return false;
	}
	/**
	 * 使用大模型回答时，不需要返回【大模型推荐】
	 * @Author: WeiJingKun
	 *
	 * @param handleDTO 用户输入对象
	 */
	private void removeTextIntentionRecommend(ChatAddHandleDTO handleDTO) {
		DialogueRecommendVO recommendVO = handleDTO.getRespVO().getRecommend();
		if (ObjectUtil.isNotNull(recommendVO)) {
			List<IntentionRecommendVO> intentionList = recommendVO.getIntentionList();
			if (CollUtil.isNotEmpty(intentionList)) {
				// 使用大模型回答时，不需要返回【大模型推荐】
				List<IntentionRecommendVO> newIntentionList = intentionList.stream()
						// 过滤：仅保留非文本意图-000的数据
						.filter(result -> !result.getIntentionCommand().equals(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode()))
						.collect(Collectors.toList());
				recommendVO.setIntentionList(newIntentionList);
			}
		}
	}

	/**
	 * 根据搜索结果，构建输送大模型的query信息
	 *
	 * @param filterResult 过滤后的结果
	 * @return query text
	 */
	private AiInternetSearchQueryDTO createQueryInfo(List<MultiSearchVO> filterResult) {
		long startTime = System.currentTimeMillis();

		/** 搜索结果分类 */
		Map<String, List<GenericSearchVO>> resultMap = new HashMap<>(Const.NUM_16);
		filterResult.forEach(result -> {
			List<GenericSearchVO> voList = result.getVoList();
			if(CollUtil.isNotEmpty(voList)){
				// 以type为key，voList为value（key一样，则value合并）
				resultMap.computeIfAbsent(result.getType(), value -> new ArrayList<>()).addAll(voList);
			}
		});

		/** 构建输送大模型的query信息 */
		// 正版资源
		List<GenericSearchVO> genericPlatform = resultMap.get(ALI_GENUINE_RESULT_KEY);
		// 网盘资源
		List<GenericSearchVO> cloudStorageLinkList = new ArrayList<>();
		CollUtil.addAll(cloudStorageLinkList, resultMap.get(PANTA_RESULT_KEY));
		CollUtil.addAll(cloudStorageLinkList, resultMap.get(ALI_LINK_RESULT_KEY));
		// 在线观看
		List<GenericSearchVO> onlineUrl = resultMap.get(ALI_ONLINE_RESULT_KEY);
		// 从1开始赋值
		AtomicInteger id = new AtomicInteger(1);
		// AI全网搜来源网页
		List<AiTextResultRespParameters.NetworkSearchInfo> networkSearchInfoList = CollUtil.newArrayList();
		// 按照正版资源 -> 网盘资源 -> 在线观看顺序处理，为空则跳过
		Stream.of(genericPlatform, cloudStorageLinkList, onlineUrl).forEach(list -> {
			if (CollUtil.isNotEmpty(list)) {
				list.forEach(vo -> {
					vo.setId(id.getAndIncrement());
					// 构建AI全网搜来源网页
					networkSearchInfoList.add(AiTextResultRespParameters.NetworkSearchInfo.builder()
							.index(vo.getId()).icon(null).siteName(vo.getSnippet()).title(vo.getTitle())
							.url(Optional.ofNullable(vo.getPublishLink()).orElse(vo.getLink())).build()
					);
				});
			}
		});

		/** networkSearchInfoList根据index从小到大排序 */
		if(CollUtil.isNotEmpty(networkSearchInfoList)){
			networkSearchInfoList.sort(Comparator.comparingInt(AiTextResultRespParameters.NetworkSearchInfo::getIndex));
		}

		AiInternetSearchQueryDTO queryInfo = AiInternetSearchQueryDTO.builder()
				.genericPlatformJson(CollUtil.isEmpty(genericPlatform) ? null : JsonUtil.toJson(genericPlatform))
				.cloudStorageLinkJson(CollUtil.isEmpty(cloudStorageLinkList) ? null : JsonUtil.toJson(cloudStorageLinkList))
				.onlineUrlJson(CollUtil.isEmpty(onlineUrl) ? null : JsonUtil.toJson(onlineUrl))
				.networkSearchInfoList(networkSearchInfoList)
				.build();
		LogCommonUtils.printlnStrLog("【AI全网搜】【构建输送大模型的query信息】", JsonUtil.toJson(queryInfo));
		log.info("【AI全网搜】【构建输送大模型的query信息】耗时：{}ms", System.currentTimeMillis() - startTime);
		return queryInfo;
	}

	/**
	 * 所有搜索结果过滤
	 *
	 * @param multiSearchResult 搜索数据集合
	 * @return 过滤后的数据
	 */
	private List<MultiSearchVO> dataFilter(List<MultiSearchVO> multiSearchResult) {
		long startTime = System.currentTimeMillis();
		LogCommonUtils.printlnStrLog("【AI全网搜】【所有搜索结果过滤】【过滤前】", JsonUtil.toJson(multiSearchResult));
		Map<String, List<GenericSearchVO>> filterResult = new HashMap<>(Const.NUM_16);

		log.info("【AI全网搜】【正版平台】链接前缀，白名单配置：{}", allNetworkSearchProperties.getGenericPlatformLinkPrefix());
		log.info("【AI全网搜】【网盘链接】链接前缀，白名单配置：{}", allNetworkSearchProperties.getCloudStorageLinkPrefix());
		log.info("【AI全网搜】【在线网址】链接前缀，黑名单配置：{}", allNetworkSearchProperties.getOnlineUrlLinkPrefix());
		// 搜索数据 过滤
		multiSearchResult.forEach(result -> {
			String type = result.getType();
			List<GenericSearchVO> filterList = result.getVoList().stream()
					// 链接前缀 过滤
					.filter(vo -> {
						// 【网盘数据】链接前缀过滤
						if (PANTA_RESULT_KEY.equals(type) || ALI_LINK_RESULT_KEY.equals(type)) {
							return AllNetworkSearchProperties.whiteLinkPrefixFilter(vo.getLink(),
									allNetworkSearchProperties.getCloudStorageLinkPrefix());
						}
						// 【正版平台】链接前缀过滤
						if (ALI_GENUINE_RESULT_KEY.equals(type)) {
							return genuineLinkPrefixFilter(vo, allNetworkSearchProperties.getGenericPlatformLinkPrefix());
						}
						// 【在线网址】链接前缀过滤
						if (ALI_ONLINE_RESULT_KEY.equals(type)) {
							return AllNetworkSearchProperties.blackLinkPrefixFilter(vo.getLink(),
									allNetworkSearchProperties.getOnlineUrlLinkPrefix());
						}
						// 其他不过滤
                        return true;
                    })
					// 仅保留：主题，不包含需要检查的关键字的数据
					.filter(vo -> checkKeywords(vo, allNetworkSearchProperties.getCheckKeywords()))
					// link去重
					.filter(distinctByKeys(GenericSearchVO::getLink, null))
					// publishLink去重
					.filter(distinctByKeys(GenericSearchVO::getPublishLink, null))
					.collect(Collectors.toList());
			if(CollUtil.isNotEmpty(filterList)){
				filterResult.computeIfAbsent(type, value -> new ArrayList<>()).addAll(filterList);
			}
		});

		// 定义参数
		List<MultiSearchVO> resultList = new ArrayList<>();

		/** 搜索结果拆分 */
		// 小站 搜索结果（归属于资源类型：网盘链接）
		List<GenericSearchVO> pantaList = filterResult.get(PANTA_RESULT_KEY);
		if (CollUtil.isNotEmpty(pantaList)) {
			resultList.add(MultiSearchVO.builder().type(PANTA_RESULT_KEY).voList(pantaList.stream().limit(searchProperties.getPantaSize()).collect(Collectors.toList())).build());
		}
		// 阿里 网盘链接 搜索结果
		List<GenericSearchVO> aliList = filterResult.get(ALI_LINK_RESULT_KEY);
		if (CollUtil.isNotEmpty(aliList)) {
			resultList.add(MultiSearchVO.builder().type(ALI_LINK_RESULT_KEY).voList(aliList.stream().limit(searchProperties.getAliSize()).collect(Collectors.toList())).build());
		}
		// 阿里 正版平台 搜索结果
		List<GenericSearchVO> aliGenuineList = filterResult.get(ALI_GENUINE_RESULT_KEY);
		if (CollUtil.isNotEmpty(aliGenuineList)) {
			// 遍历aliGenuineList，把title赋值为tile+空格+hostName
			aliGenuineList.forEach(vo -> {
				String title = StringUtils.isNotBlank(vo.getTitle()) ? vo.getTitle() : StringUtils.EMPTY;
				String hostname = StringUtils.isNotBlank(vo.getHostname()) ? vo.getHostname() : StringUtils.EMPTY;
				vo.setTitle(title + " " + hostname);
			});
			resultList.add(MultiSearchVO.builder().type(ALI_GENUINE_RESULT_KEY).voList(aliGenuineList.stream().limit(searchProperties.getGenuineSize()).collect(Collectors.toList())).build());
		}
		// 阿里 在线网址 搜索结果
		List<GenericSearchVO> aliOnlineList = filterResult.get(ALI_ONLINE_RESULT_KEY);
		if (CollUtil.isNotEmpty(aliOnlineList)) {
			resultList.add(MultiSearchVO.builder().type(ALI_ONLINE_RESULT_KEY).voList(aliOnlineList.stream().limit(searchProperties.getOnlineSize()).collect(Collectors.toList())).build());
		}

		LogCommonUtils.printlnStrLog("【AI全网搜】【所有搜索结果过滤】【过滤后】", JsonUtil.toJson(resultList));
		log.info("【AI全网搜】【所有搜索结果过滤】耗时：{}ms", System.currentTimeMillis() - startTime);
		return resultList;
	}

	/**
	 * 1、判断链接是否符合业务要求范围
	 * 2、符合要求，在title添加对应的正版来源
	 *
	 * @param vo 搜索结果
	 * @param targetLinkPrefix 链接前缀实体数据
	 * @return 是否符合
	 */
	public static boolean genuineLinkPrefixFilter(GenericSearchVO vo, List<AllNetworkSearchProperties.LinkPrefix> targetLinkPrefix) {
		if(CollUtil.isEmpty(targetLinkPrefix)){
			return true;
		}
		for(AllNetworkSearchProperties.LinkPrefix linkPrefix : targetLinkPrefix){
			boolean match = linkPrefix.getLinkList().stream().anyMatch(vo.getLink()::startsWith);
			if(match){
				String title = vo.getTitle();
				String linkPrefixName = linkPrefix.getName();
				if(CharSequenceUtil.isNotBlank(title) && !title.contains(linkPrefixName)){
					vo.setTitle(vo.getTitle() + " " + linkPrefixName);
				} else {
					vo.setTitle(linkPrefixName);
				}
				return true;
			}
		}
		return false;
	}

	private static String getRandomLink(String publishLink){
		if (StringUtils.isBlank(publishLink)) {
			return null;
		}
		// 定义正则表达式：匹配中括号内的任意内容（非贪婪模式）
		Pattern pattern = Pattern.compile(REGEX_PUBLISH_LINK);
		Matcher matcher = pattern.matcher(publishLink);

		// 存储提取的链接
		List<String> links = new ArrayList<>();

		// 遍历所有匹配结果
		while (matcher.find()) {
			// 提取括号内的内容
			String url = matcher.group(1);
			links.add(url);
		}

		if (links.isEmpty()) {
			return null;
		}

		return links.get(new Random().nextInt(links.size()));
	}

	/**
	 * 判断数据是否保留
	 *
	 * @param vo 搜索数据对象
	 * @param checkKeywords 需要检查的关键字
	 * @return true：保留数据，false：不保留数据
	 */
	private boolean checkKeywords(GenericSearchVO vo, List<String> checkKeywords){
		boolean flag = true;
		if(CollUtil.isNotEmpty(checkKeywords)){
			for(String keyword : checkKeywords){
				// 主题
				if(vo.getTitle().contains(keyword)){
					flag = false;
					break;
				}
			}
		}
		return flag;
	}

	/**
	 * 辅助方法：返回一个Predicate，用于基于指定的keyExtractor对象进行去重判断。
	 *
	 * @param keyExtractor1 排重字段1
	 * @param keyExtractor2 排重字段2
	 * @return 布尔值函数执行结果
	 * @param <T> 泛型参数
	 */
	private <T> Predicate<T> distinctByKeys(Function<? super T, ?> keyExtractor1,
											Function<? super T, ?> keyExtractor2) {
		Set<String> seen = ConcurrentHashMap.newKeySet();
		return t -> {
			String compositeKey = keyExtractor1.apply(t) + StrPool.DASHED
					+ (ObjectUtil.isNull(keyExtractor2) ? "" : keyExtractor2.apply(t));
			return seen.add(compositeKey);
		};
	}

	/**
	 * 多线程获取搜索结果
	 *
	 * @param dtoList 搜索参数集合
	 * @return 搜索结果集合
	 */
	private List<MultiSearchVO> multiSearch(List<AiMultiSearchDTO> dtoList) {
		long startTime = System.currentTimeMillis();
		final String mainThread = Thread.currentThread().getName();
		log.info("【AI全网搜】开始多线程调用搜索接口，预执行次数：{}", dtoList.size());
		List<CompletableFuture<List<MultiSearchVO>>> futureList = dtoList.stream().map(dto -> {
			CompletableFuture<List<MultiSearchVO>> taskFuture = CompletableFuture.supplyAsync(() -> {
						if (dto.isPantaFlag()) {
							log.info("【AI全网搜】异步搜小站任务执行中，当前线程：{}，主线程：{}，参数：{}", Thread.currentThread().getName(), mainThread,
									dto.getMetadataMap());
							return pantaSearch(dto.getMetadataMap());
						}
						log.info("【AI全网搜】异步阿里云搜索任务执行中，当前线程：{}，主线程：{}，参数：{}", Thread.currentThread().getName(), mainThread,
								dto.getGenericSearchDTO());
						List<GenericSearchVO> voList = aiInternetSearchExternalService.unifiedSearch(dto.getGenericSearchDTO());
						return getResultList(dto, voList);
					}, multiSearchThreadPool)
					// exception
					.handle((result, ex) -> {
						if (ex != null) {
							log.error("【AI全网搜】调用 multiSearch 异常，参数dto：{}", dto, ex);
							return Collections.emptyList();
						}
						return result;
					});
			// timeout
			return withTimeout(taskFuture, searchProperties.getSearchWaitTime(), Collections.emptyList());
		}).collect(Collectors.toList());

		List<MultiSearchVO> resultList = futureList.stream().map(CompletableFuture::join).flatMap(List::stream).collect(Collectors.toList());
		log.info("【AI全网搜】多线程调用搜索接口完成，耗时：{} ms", System.currentTimeMillis() - startTime);
		return resultList;
	}

	/**
	 * 获取结果列表
	 *
	 * @param dto 搜索参数对象
	 * @param voList 搜索结果对象
	 * @return 搜索结果列表
	 */
	private List<MultiSearchVO> getResultList(AiMultiSearchDTO dto, List<GenericSearchVO> voList) {

		if (dto.getResourceType().equals(EntityResourceTypeEnum.GENUINE)){
			return CollUtil.newArrayList(MultiSearchVO.builder().type(ALI_GENUINE_RESULT_KEY).voList(voList).build());
		} else if (dto.getResourceType().equals(EntityResourceTypeEnum.ON_LINE)){
			return CollUtil.newArrayList(MultiSearchVO.builder().type(ALI_ONLINE_RESULT_KEY).voList(voList).build());
		} else {
			// 小站资源/原阿里云搜索归属于网盘搜索资源
			return CollUtil.newArrayList(MultiSearchVO.builder().type(ALI_LINK_RESULT_KEY).voList(voList).build());
		}
	}

	/**
	 * 超时控制方法
	 *
	 * @param future 执行任务
	 * @param timeout 超时时间
	 * @param defaultValue 默认值
	 * @return 返回结果
	 * @param <T> 任意类型
	 */
	private <T> CompletableFuture<T> withTimeout(CompletableFuture<T> future, long timeout, T defaultValue) {
		CompletableFuture<T> timeoutFuture = new CompletableFuture<>();
		multiSearchSchedulePool.schedule(() -> {
			if (!future.isDone()) {
				timeoutFuture.complete(defaultValue);
			}
		}, timeout, TimeUnit.SECONDS);
		return future.applyToEither(timeoutFuture, result -> {
			if (result == defaultValue) {
				log.warn("【AI全网搜】多线程查询超时，返回默认值，当前线程：{}，数据：{}", Thread.currentThread().getName(), defaultValue);
			}
			return result;
		});
	}

	/**
	 * 搜索实体抽取
	 *
	 * @param dialogue 对话内容
	 *
	 * @return 搜索实体抽取结果
	 */
	private Map<PantaLabelEnum, List<String>> searchEntityExtract(String dialogue) {
		AllNetworkSearchProperties.SearchEntityExtractDTO searchEntityExtract = searchProperties
				.getSearchEntityExtract();
		TextNerExtractDTO dto = new TextNerExtractDTO();
		dto.setText(dialogue);
		dto.setMaxTextLength(searchEntityExtract.getMaxLength());
		dto.setEnableAllEntity(searchEntityExtract.isReturnAll());
		dto.setEntityTypeList(searchEntityExtract.getEntityTypeList());
		dto.setAllEntityExample(searchEntityExtract.getAllEntityExample());
		dto.setTypeEntityExample(searchEntityExtract.getTypeEntityExample());
		dto.setRequestId(MDC.get(LogConstants.TRACE_ID));
		return cmicTextService.searchEntityExtract("【AI全网搜】", dto);
	}

	/**
	 * 组装搜索参数
	 *
	 * @param extractResult 搜索实体抽取结果
	 * @return 合并后的搜索参数
	 */
	private List<AiMultiSearchDTO> assemblerSearchData(Map<PantaLabelEnum, List<String>> extractResult, Map<PantaLabelEnum, List<String>> resourceTypeMap) {
		long startTime = System.currentTimeMillis();
		List<AiMultiSearchDTO> paramList = new ArrayList<>();

		// 获取需要执行搜索的资源类型枚举
		List<EntityResourceTypeEnum> resourceTypeEnums = new ArrayList<>();
		// 判断resourceTypeMap是否为空
		if (resourceTypeMap.isEmpty()) {
			resourceTypeEnums.add(EntityResourceTypeEnum.DISK);
			resourceTypeEnums.add(EntityResourceTypeEnum.GENUINE);
			resourceTypeEnums.add(EntityResourceTypeEnum.ON_LINE);
		} else {
			resourceTypeMap.forEach((key, valueList) -> {
				if(CollUtil.isNotEmpty(valueList)){
					valueList.forEach(value -> {
						EntityResourceTypeEnum typeEnum = EntityResourceTypeEnum.getByDesc(value);
						if (null != typeEnum) {
							resourceTypeEnums.add(typeEnum);
						}
					});
				}
			});
		}

		resourceTypeEnums.forEach(resourceTypeEnum -> {
			switch (resourceTypeEnum) {
				case DISK:
					// 1. 小站搜索参数处理
					if (searchProperties.isSearchPanta()) {
						paramList.add(AiMultiSearchDTO.builder().resourceType(resourceTypeEnum).pantaFlag(true).metadataMap(extractResult).build());
					}

					// 2. 第三方搜索参数处理 (aliyun)
					Set<String> thirdPartyKind = searchProperties.getThirdPartyKind();
					encapsulation(extractResult, resourceTypeEnum, thirdPartyKind, paramList);
					break;
				case GENUINE:
					Set<String> thirdPartyKindGenuine = searchProperties.getThirdPartyKindGenuine();
					encapsulation(extractResult, resourceTypeEnum, thirdPartyKindGenuine, paramList);
					break;
//				case ON_LINE:
//					Set<String> thirdPartyKindOnLine = searchProperties.getThirdPartyKindOnLine();
//					encapsulation(extractResult, resourceTypeEnum, thirdPartyKindOnLine, paramList);
//					break;
			}
		});

		log.info("【AI全网搜】多线程搜索实体组装耗时：{}ms，结果：{}", System.currentTimeMillis() - startTime, JsonUtil.toJson(paramList));
		return paramList;
	}

	/**
	 * 封装小站搜索参数
	 *
	 * @param extractResult 搜索实体
	 * @param resourceTypeEnum 资源类型枚举
	 * @param thirdPartyKind 第三方搜索类型
	 * @param paramList 参数列表
	 */
	private void encapsulation(Map<PantaLabelEnum, List<String>> extractResult, EntityResourceTypeEnum resourceTypeEnum, Set<String> thirdPartyKind, List<AiMultiSearchDTO> paramList) {
		// 把抽取的所有实体去重，通过空格拼接，作为前缀suffix
		Set<String> entitySet = new HashSet<>();
		extractResult.forEach((key, value) -> CollUtil.addAll(entitySet, value));
		String prefix = String.join(" ", entitySet);
		// suffix拼接接口请求后缀配置，作为请求参数
		thirdPartyKind.forEach(suffix -> {
			GenericSearchDTO searchDTO = convertor.toGenericSearchDTO(searchProperties.getAliSearchParams());
			StringBuilder temp = new StringBuilder(prefix);
			searchDTO.setQuery(String.valueOf(temp.append(suffix)));
			paramList.add(AiMultiSearchDTO.builder().resourceType(resourceTypeEnum).genericSearchDTO(searchDTO).build());
		});
	}

	/**
	 * 小站搜索方法
	 *
	 * @param extractResult 搜索实体
	 * @return 小站搜索结果
	 */
	private List<MultiSearchVO> pantaSearch(Map<PantaLabelEnum, List<String>> extractResult) {

		long startTime = System.currentTimeMillis();
		List<EsPanTaResourceEntity> pantaResultList;
		// 获取小站搜索nacos配置
		AllNetworkSearchProperties.SearchPantaParam searchConfig = searchProperties.getSearchPantaParam();
		// 通过es搜索小站资源
		PanTaResourceOuterSearchV2DTO searchDTO = new PanTaResourceOuterSearchV2DTO(extractResult,
				searchConfig.getReleaseYear(), searchConfig.getAdult(), searchConfig.getPriorityList(),
				searchConfig.getDomestic(), searchConfig.getBusinessResourceTypeList());
		pantaResultList = pantaResourceRepository.searchPanTaResourceV2(searchDTO);
		log.info("【AI全网搜】【小站搜索】【处理前】结果总条数：{}", pantaResultList.size());
		LogCommonUtils.printlnStrLog("【AI全网搜】【小站搜索】【处理前】结果", JsonUtil.toJson(pantaResultList));

		if (CollUtil.isEmpty(pantaResultList)) {
			log.info("【AI全网搜】【小站搜索】结果为空");
			return Collections.emptyList();
		}
		// 过滤
		pantaResultList = pantaResultList.stream()
				.filter(distinctByKeys(EsPanTaResourceEntity::getShareUrl, EsPanTaResourceEntity::getTitle))
				.collect(Collectors.toList());
		// 处理
		List<GenericSearchVO> resultList = CollUtil.newArrayList();
		pantaResultList.forEach(s -> {
			StringBuilder start = new StringBuilder(PANTA_TITLE);
			s.setOverview(String.valueOf(start.append(s.getTitle()).append(CHINESE_COMMA).append(PANTA_RESOURCE_TAG)
					.append(s.getResourceTag()).append(CHINESE_COMMA).append(PANTA_DIRECTOR).append(s.getDirector())
					.append(CHINESE_COMMA).append(PANTA_CAST_NAME).append(s.getCastName()).append(CHINESE_COMMA)
					.append(PANTA_OVERVIEW).append(s.getOverview())));
			s.setReleaseUrl(getRandomLink(s.getReleaseUrl()));
			resultList.add(convertor.toGenericSearchVO(s));
		});
		log.info("【AI全网搜】【小站搜索】【处理后】结果总条数：{}，耗时：{}ms", pantaResultList.size(), System.currentTimeMillis() - startTime);
		LogCommonUtils.printlnStrLog("【AI全网搜】【小站搜索】【处理后】结果", JsonUtil.toJson(pantaResultList));
		return CollUtil.newArrayList(MultiSearchVO.builder().type(PANTA_RESULT_KEY).voList(resultList).build());
	}

	/**
	 * 保存数据到数据库
	 *
	 * @param handleDTO 对话参数
	 */
	private void save(ChatAddHandleDTO handleDTO) {
        // 保存hbase
		dataSaveService.saveTextResult(handleDTO, StringUtils.EMPTY, StringUtils.EMPTY);
		// 保存tidb
		dataSaveService.add(handleDTO, ChatStatusEnum.CHAT_IN);
		log.info("【AI全网搜】保存对话数据成功");
	}

	/**
	 * 智能调度
	 *
	 * @param handleDTO             用户输入对象
	 * @param event                 流式监听参数
	 * @param searchScheduleDTO   AI全网搜——智能调度参数
	 */
	private void schedule(ChatAddHandleDTO handleDTO, SseEventListener event, AiInternetSearchScheduleDTO searchScheduleDTO) {
		List<String> executeSort = new ArrayList<>();
		executeSort.add(searchProperties.getModelCode());
		AbstractResultCode error = null;
		boolean success = false;
		for (String code : executeSort) {
			error = null;
			// qps限制
			if (!qpslimitService.modelQpsLimit(code)) {
				log.info("请求过多，qps限流，model:{}", code);
				error = ResultCodeEnum.ERROR_LIMITATION;
			} else {
				try {
					TextModelTextReqDTO reqDTO = event.getTextDto().toTextReqDTO(modelProperties.getMaxLength(
							RequestContextHolder.getAssistantEnum(), RequestContextHolder.getBusinessType(), code));
					reqDTO.setEnableNetworkSearch(true);
					event.setModelCode(code);
					// 2025-06-13 AI全网搜来源网页（优先保障）【全网搜返回联网搜索结果】
					// event.setOutputNetworkSearchInfo(false);
					event.setInternetSearchInfoList(searchScheduleDTO.getQueryDTO().getNetworkSearchInfoList());

					// 标记走【AI全网搜】，用于注意事项拼接
					handleDTO.getReqDTO().getDialogueInput().setEnableAllNetworkSearch(Boolean.TRUE);
					// 调大文本模型流式处理
					success = modelStreamHandle(reqDTO, code, event, searchScheduleDTO);
					// 更新模型编码
					algorithmChatContentRepository.updateModelCode(event.getDialogId(), code);
					break;
				} catch (YunAiBusinessException e) {
					error = e.getExceptionEnum();
					log.error("调用文本大模型异常:{}，对话id:{}", e.getMessage(), event.getDialogId(), e);
				} catch (Exception e) {
					log.error("调用文本大模型异常:{}，对话id:{}", e.getMessage(), event.getDialogId(), e);
				}
			}
		}

		if (!success) {
			log.warn("对话失败，智能调度大文本模型处理失败，对话id：{}", event.getDialogId());
			if (error != null) {
				event.dialogueFail(error);
			} else {
				event.dialogueFail(ResultCodeEnum.ERROR_SERVER_INTERNAL);
			}
		}
	}

	/**
	 * 调大文本模型流式处理
	 *
	 * @param reqDTO 请求参数
	 * @param code   模型编码
	 * @param event  监听事件
	 * @param searchScheduleDTO AI全网搜——智能调度参数
	 */
	private boolean modelStreamHandle(TextModelTextReqDTO reqDTO, String code, TextModelStreamEventListener event,
									  AiInternetSearchScheduleDTO searchScheduleDTO) {
		AiInternetSearchQueryDTO queryDTO = searchScheduleDTO.getQueryDTO();
		// 提示词
		String prompt = modelPromptProperties.getPrompt(searchScheduleDTO.getPromptKey(), code)
				// 替换【用户输入】
				.replace(REPLACE_KEY_OF_CONTENT, searchScheduleDTO.getContent())
                // 替换【正版平台】
				.replace(REPLACE_KEY_OF_GENERIC_PLATFORM, CharSequenceUtil.isBlank(queryDTO.getGenericPlatformJson()) ? EMPTY_JSON_ARRAY : queryDTO.getGenericPlatformJson())
                // 替换【网盘链接】
				.replace(REPLACE_KEY_OF_CLOUD_STORAGE_LINK, CharSequenceUtil.isBlank(queryDTO.getCloudStorageLinkJson()) ? EMPTY_JSON_ARRAY : queryDTO.getCloudStorageLinkJson())
                // 替换【在线网址】
				.replace(REPLACE_KEY_OF_ONLINE_URL, CharSequenceUtil.isBlank(queryDTO.getOnlineUrlJson()) ? EMPTY_JSON_ARRAY : queryDTO.getOnlineUrlJson())
				// 替换【上一段对话内容】
				.replace(REPLACE_KEY_OF_HISTORY_RESULT, CharSequenceUtil.isBlank(searchScheduleDTO.getHistoryResult()) ? CharSequenceUtil.EMPTY : searchScheduleDTO.getHistoryResult())
				;

		// 日志打印：完整提示词
		LogCommonUtils.printlnListLog("【AI全网搜-完整提示词】第{}个分块：\n{}", prompt);

		// 新的对话信息
		TextModelMessageDTO msgDTO = new TextModelMessageDTO();
		msgDTO.setRole(TextModelRoleEnum.USER.getName());
		msgDTO.setContent(prompt);

		return textModelExternalService.streamDialogue(code, genTextModelTextReq(reqDTO, msgDTO), event);
	}

	/**
	 * 获取新的对话信息
	 *
	 * @param reqDTO 模型请求
	 * @param msgDTO 消息请求
	 * @return 模型请求实体
	 */
	private TextModelTextReqDTO genTextModelTextReq(TextModelTextReqDTO reqDTO, TextModelMessageDTO msgDTO) {

		TextModelTextReqDTO req = new TextModelTextReqDTO();
		req.setTaskId(reqDTO.getTaskId());
		req.setUserId(reqDTO.getUserId());
		req.setSessionId(reqDTO.getSessionId());
		req.setMessageDtoList(Collections.singletonList(msgDTO));
		req.setEnableNetworkSearch(reqDTO.getEnableNetworkSearch());
		req.setEnableModelSystemRolePrompt(false);
		req.setTextModelConfig(allNetworkSearchProperties.getTextModelConfig());
		// 设置不启用思维链（某些百炼大模型生效，如qwen3时候生效）
		req.setDisabledReasoningFlag(true);
		return req;
	}

	/**
	 * 调用大模型判断是否国内资源
	 * @Author: WeiJingKun
	 *
	 * @param dialogue 用户的输入
	 * @return true：国内资源，false：国外资源
	 */
	private boolean getIsDomestic(String dialogue) {
		boolean isDomestic = false;
		// 提示词key
        String code = searchProperties.getModelCode();
		// 拼装提示词
		String modelPrompt = modelPromptProperties.getPrompt(DOMESTIC_AI_INTERNET_SEARCH_SYSTEM_PROMPT,code).replace("{query}",dialogue);
		log.info("【AI全网搜】【调用大模型判断是否国内资源】替换拼装后的提示词getIsDomestic-modelPrompt:{}", modelPrompt);
		String resultText = null;
		try {
			// 构建调起大模型请求参数
			List<LlmChatMessage> messages = new ArrayList<>();
			messages.add(new LlmChatMessage(TextModelRoleEnum.USER.getName(), modelPrompt));
			LlmChatReqDTO reqDTO = new LlmChatReqDTO(RequestContextHolder.getUserId(), code, messages);
			reqDTO.setEnableNetworkSearch(true);
			reqDTO.setDisabledReasoningFlag(true);
			TextModelSearchOptionDTO textModelSearchOptionDTO = new TextModelSearchOptionDTO();
			textModelSearchOptionDTO.setForceSearch(true);
			textModelSearchOptionDTO.setSearchStrategy("pro");
			reqDTO.setSearchOption(textModelSearchOptionDTO);
			// 调起大模型
			TextModelBaseVo result = llmChatExternalService.chatNormal(reqDTO);
			// 获取调起大模型后返回的文本生成结果
			resultText = result.getText();
			log.info("【AI全网搜】【调用大模型判断是否国内资源】返回的文本生成结果resultText:{}", resultText);
			// 调用大模型返回的文本生成结果不存在，则按原流程执行
			if(StringUtils.isNotBlank(resultText)){
				// 将resultText转为小写 判断是否包含字符串true
				isDomestic = resultText.toLowerCase().contains("true");
			}
		} catch(Exception e) {
			log.error("【AI全网搜】【调用大模型判断是否国内资源】失败 error:", e);
		}

		log.info("【AI全网搜】【调用大模型判断是否国内资源】最终获取结果isDomestic:{}", isDomestic);
		return isDomestic;
	}

}
