package com.zyhl.yun.api.outer.application.chatv2.dto;

import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueResultV2VO;
import com.zyhl.yun.api.outer.application.dto.SecondStreamChatAddV2DTO;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.domainservice.SseEmitterOperate;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 二次流式对话v2接口内部数据传输对象，包含： 1、请求参数，数据不可改 2、生成响应参数的方法 3、中间状态数据
 *
 * <AUTHOR>
 * @date 2025/6/3 16:31
 */
@Data
public class SecondStreamChatAddV2InnerDTO implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    /**
     * 输入参数
     */
    private SecondStreamChatAddV2DTO reqParams;

    /**
     * 轮询内容结果
     */
    private DialogueResultV2VO dialogResult;

    /**
     * 邮件信息
     */
    private MailInfoVO mailInfo;

    /**
     * 个人云文件信息列表
     */
    private List<File> fileList;

    /**
     * 执行的模型编码
     */
    private String modelCode;

    /**
     * 执行大模型的问句
     */
    private String queryContent;

    /**
     * 是否获取大模型输出内容
     */
    private Boolean getOutContent;

    /**
     * 大模型输出内容
     */
    private String outContent;

    /**
     * 流式对象操作类
     */
    private SseEmitterOperate sseEmitterOperate;

    public SecondStreamChatAddV2InnerDTO(SecondStreamChatAddV2DTO reqParams, SseEmitterOperate sseEmitterOperate) {
        this.reqParams = reqParams;
        this.sseEmitterOperate = sseEmitterOperate;
    }

}
