package com.zyhl.yun.api.outer.application.service.mq;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeDispatchTaskMqEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import java.util.List;

/**
 * interfaceName: KnowledgeDispatchTaskMqService
 * description: 个人知识库 - 笔记/邮件 知识库向量化提取接口类
 *
 * <AUTHOR>
 * @date 2025/2/10
 */
public interface KnowledgeDispatchTaskMqService {

    /**
     * 发送向量化消息
     *
     * @param entityList 文件实体列表
     */
    void sendMq(List<UserKnowledgeDispatchTaskMqEntity> entityList);

    /**
     * 发送向量化消息
     * 1.0
     * @param list 文件实体列表
     */
    void sendTaskMq(List<UserKnowledgeFileEntity> list);

    /**
     * 发送向量化消息
     * 2.0使用
     * @param list 文件实体列表
     */
    void sendTaskMqV2(List<UserKnowledgeFileEntity> list);
}
