package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 知识库解析文件数量统计请求类
 * 
 * <AUTHOR>
 * @date 2025-04-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class PersonalKnowledgeCountReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 新成功解析文件的统计开始时间，RFC 3339，
     * 2019-08-20T06:51:27.292+08:00
     */
    private String countTime;
}
