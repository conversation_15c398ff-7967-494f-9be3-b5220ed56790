package com.zyhl.yun.api.outer.application.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 任务评价信息
 * @date 2025/4/21 16:05
 */
@Data
public class TaskCommentInfoVO implements Serializable {

    private static final long serialVersionUID = -8055059746211517294L;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 模块编码：
     * <p>
     * "all":针对整个任务的评价
     * "doc-summary":文档总结
     * "doc-mindmap":文档脑图
     * "doc-outline":文档大纲
     */
    private String module;

    /**
     * 是否喜欢 0:不喜欢，1:喜欢
     *
     * @see com.zyhl.yun.api.outer.enums.LikeEnum
     */

    /**
     * 是否喜欢 0:不喜欢，1:喜欢
     */
    private Integer likeComment;

    /**
     * 默认评论内容
     */
    private String defaultComment;

    /**
     * 自定义评论内容
     */
    private String customComment;

}