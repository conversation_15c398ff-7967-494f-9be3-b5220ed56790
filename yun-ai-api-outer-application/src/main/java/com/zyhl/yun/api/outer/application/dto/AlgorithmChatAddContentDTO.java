package com.zyhl.yun.api.outer.application.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zyhl.hcy.yun.ai.common.model.api.client.blian.vo.TextModelFileVO;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.TalkTypeEnum;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zyhl.yun.api.outer.constants.Const.NUM_16;

/**
 * <AUTHOR>
 */
@Data
public class AlgorithmChatAddContentDTO {

    /**
     * 渠道来源
     */
    private String sourceChannel;

    /** 助手类型(非前端入参) */
    private AssistantEnum assistantEnum;

    /** 助手业务类型(非前端入参) */
    private String businessType;
    
    /**
     * 资源类型
     *
     * @see ResourceTypeEnum
     */
    private Integer resourceType;

    /**
     * 对话内容
     */
    private String dialogue;

    /**
     * 对话类型
     *
     * @see TalkTypeEnum
     */
    private Integer dialogueType;

    /**
     * 端测传入的意图
     *
     * @see com.zyhl.yun.api.outer.enums.DialogueIntentionEnum
     */
    private String commands;

    /**
     * 意图说明 例如概括总结、内容润色等
     */
    private String prompt;

    /**
     * 时间戳
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date timestamp;

    /**
     * 笔记id/邮件id/图片文件id
     */
    private String resourceId;

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 非必填 业务场景标识
     */
    private String sceneTag;

    /**
     * 非必填 命令类型
     *
     * @see com.zyhl.yun.api.outer.enums.chat.DialogueCommandTypeEnum
     */
    private Integer commandType = 1;

    /**
     * 排序信息
     */
    private DialogueSortInfoDTO sortInfo;

    /**
     * 上次对话信息（非前端入参）
     */
    private LastDialogueInfoDTO lastDialogueInfo;

    /**
     * 文本大模型上传文件列表（非前端入参）
     */
    private List<TextModelFileVO> textModelFiles;
    
    /**
     * 长文本文件的本地共享存储路径（非前端入参）
     */
    private List<String> longTextFileLocalPathList;
    
    public Map<String, Object> getExtInfoMap() {
        if (this.extInfo == null || this.extInfo.isEmpty()) {
            // 或者返回一个空的Map
            return new HashMap<>(NUM_16);
        }

        // 可以考虑作为一个静态实例或者通过依赖注入
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.readValue(this.extInfo, new TypeReference<Map<String, Object>>() {
            });
        } catch (JsonProcessingException e) {
            return new HashMap<>(NUM_16);
        }
    }

}
