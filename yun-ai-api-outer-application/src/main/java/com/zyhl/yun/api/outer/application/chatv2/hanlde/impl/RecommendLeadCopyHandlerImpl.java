package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueAttachmentDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueIntentionDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.application.service.chat.LeadCopyV2Service;
import com.zyhl.yun.api.outer.config.LeadCopyProperties;
import com.zyhl.yun.api.outer.config.LinkProperties;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.enums.AcceptLanguageEnum;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.util.IntentionUtils;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 引导语类型1-4（引导语类型5已经删除）
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RecommendLeadCopyHandlerImpl extends AbstractChatAddV2Handler {

    /**
     * 当前执行顺序
     */
    private ExecuteSort thisExecuteSort = ExecuteSort.RECOMMEND_LEAD_COPY;

    @Resource
    private LeadCopyV2Service leadCopyService;
    @Resource
    private LinkProperties linkProperties;
    @Resource
    private LeadCopyProperties copyProperties;
    @Resource
    private DataSaveService dataSaveService;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 支持的业务初始化
        List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        this.setBusinessTypes(thisBusinessTypes);
    }

    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public boolean execute(ChatAddHandleDTO handleDTO) {
        DialogueInputInfoDTO inputInfoDTO = handleDTO.getInputInfoDTO();

        // 只有普通对话才执行 并且 非强制大模型对话
        if (!ApplicationTypeEnum.isChat(handleDTO.getReqDTO().getApplicationType())
                && !inputInfoDTO.isEnableForceLlm()) {
            return false;
        }

        // AI工具相关意图并且没传资源id (妙云相机、AI消除、智能抠图不管是否传资源id都直接返回跳转链接)
        DialogueAttachmentDTO attachment = inputInfoDTO.getAttachment();
        String fileId = CollectionUtils.isEmpty(attachment.getFileList()) ? null
                : attachment.getFileList().get(0).getFileId();
        String intentionCode = handleDTO.getIntentionCode();
        boolean returnLinkCondition = IntentionUtils.returnLinkConditionV2(intentionCode, fileId);

        // 文本工具意图
        boolean textToolIntentionCondition = DialogueIntentionEnum.isTextToolIntention(intentionCode);

        return (returnLinkCondition || textToolIntentionCondition);
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        log.info("进入{}", thisExecuteSort.getDesc());

        // 处理同步返回的信息
        String intentionCode = handleDTO.getIntentionCode();
        DialogueIntentionEnum intentionEnum = DialogueIntentionEnum.getByCodeOrDefault(intentionCode);

        // 聊天添加响应对象
        ChatAddRespVO respVO = handleDTO.getRespVO();
        // 设置引导文案对象
        DialogueIntentionVO intentionVO = handleDTO.getIntentionVO();
        if (Objects.isNull(intentionVO)) {
            intentionVO = DialogueIntentionVO.newMainIntention(intentionCode);
        }
        IntentionInfo mainIntention = DialogueIntentionVO.getMainIntention(intentionVO);

        // 设置引导文案对象
        LeadCopyVO leadCopyVO = leadCopyService.getLeadCopyVo(intentionVO, RequestContextHolder.getSourceChannel(),
                handleDTO.getInputInfoDTO().getDialogue());
        if (DialogueIntentionEnum.isTextToolIntention(intentionCode) && null == leadCopyVO) {

            DialogueIntentionDTO command = handleDTO.getInputInfoDTO().getCommand();
            if (null == command) {
                command = new DialogueIntentionDTO();
            }

            // 入参意图or识别意图-AI编程执行意图
            if (DialogueIntentionSubEnum.isAiCoderExecute(mainIntention,
                    ChatTextToolBusinessConfig.ARGUMENT_AI_CODER_FILE_NAME)
                    || DialogueIntentionSubEnum.isAiCoderExecute(command.getCommand(), command.getSubCommand())) {
                return true;
            }

            // 入参意图or识别意图-AI会议纪要执行意图（或者非执行意图的文档大模型对话）
            if (DialogueIntentionSubEnum.isAiMeetingMinutesExecute(mainIntention)
                    || DialogueIntentionSubEnum.isAiMeetingMinutesExecute(mainIntention,
                    handleDTO.isReqResourceDocSse())
                    || DialogueIntentionSubEnum.isAiMeetingMinutesExecute(command.getCommand(),
                    command.getSubCommand())) {
                return true;
            }

            // AI拍照识图意图-3个子意图的图片大模型对话
            if (DialogueIntentionSubEnum.isAiPhotoVisionSubs(mainIntention, handleDTO.isReqResourceImageSse())) {
                return true;
            }

            // AIPPT对话
            if (DialogueIntentionSubEnum.isAiPpt(mainIntention.getSubIntention())) {
                return true;
            }

            // AI生成回忆相册
            if (DialogueIntentionSubEnum.isMemoryAlbum(mainIntention.getSubIntention())) {
                return true;
            }

            // ！！注意：文本工具意图，但是实体未匹配到leadCopyVO，重置为文本意图，继续执行handle
            handleDTO.setTextGenerateTextIntention();
            return true;
        }

        // 再次校验，是否需要推荐(返回true，继续下一个流程) start
        if (DialogueIntentionEnum.isTextToolIntention(intentionCode) && null != leadCopyVO) {

            // AI会议纪要意图-非执行意图的文档大模型对话
            if (DialogueIntentionSubEnum.isAiMeetingMinutesExecute(mainIntention, handleDTO.isReqResourceDocSse())) {
                return true;
            }

            // AI拍照识图意图-3个子意图的图片大模型对话
            if (DialogueIntentionSubEnum.isAiPhotoVisionSubs(mainIntention, handleDTO.isReqResourceImageSse())) {
                return true;
            }
            
            //拍照问答，并且没传图片，需要推荐
			if (DialogueIntentionSubEnum.isAiPhotoQa(mainIntention.getSubIntention())
					&& !handleDTO.isReqResourceImageSse()) {
				log.info("识别文本工具拍照问答子意图，并且没传图片，需要推荐");
			} else {
				if (DialogueIntentionSubEnum.allowTextToolExecute(mainIntention)
						&& DialogueIntentionSubEnum.isAiPhotoSubIntentions(mainIntention.getSubIntention())
						&& CollUtil.isNotEmpty(mainIntention.getArgumentMap())) {
					log.info("识别文本工具拍照识图子意图，需要直接执行");
					return true;
				}
				
				if (!DialogueIntentionSubEnum.allowTextToolExecute(mainIntention) && handleDTO.isReqResourceDocSse()) {
					if (DialogueIntentionSubEnum.isAiSpeedRead(mainIntention.getSubIntention())) {
						log.info("图书快速阅读意图，传了文档，也走LeadCopy推荐卡片");
					} else {
						// 不可执行的文本工具意图，但是传了文档，不走推荐LeadCopy
						log.info("不可执行的文本工具意图，但是传了文档，不走推荐LeadCopy");
						return true;
					}
				}
			}

        }
        // 再次校验，是否需要推荐(返回true，继续下一个流程) end

        respVO.setFlowResult(null);
        respVO.setOutputCommandVO(intentionVO.getIntentionInfoList().get(0));
        respVO.setLeadCopy(leadCopyVO);

        boolean enUs = AcceptLanguageEnum.EN_US.getCode().equalsIgnoreCase(RequestContextHolder.getAcceptLanguage());
        // 创建笔记意图
        if (DialogueIntentionEnum.CREATE_NOTE.equals(intentionEnum)) {
            respVO.setTitle(enUs ? copyProperties.getCreateNoteTitleEn() : copyProperties.getCreateNoteTitle());
        }

        // 创建语音笔记意图
        if (DialogueIntentionEnum.CREATE_VOICE_NOTE.equals(intentionEnum)) {
            respVO.setTitle(
                    enUs ? copyProperties.getCreateVoiceNoteTitleEn() : copyProperties.getCreateVoiceNoteTitle());
        }

        // 保存leadCopy到hbase【LeadCopy，type=1、2、3、4】
        dataSaveService.saveTextResult(handleDTO, "", "");

        // 保存tidb
        dataSaveService.addSuccess(handleDTO, OutContentTypeEnum.TEXT);

        // 流式响应
        handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(respVO));

        return false;
    }
}
