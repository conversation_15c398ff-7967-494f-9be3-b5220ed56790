package com.zyhl.yun.api.outer.application.service.external.impl;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.hcy.yun.ai.common.base.enums.PantaLabelEnum;
import com.zyhl.hcy.yun.ai.common.base.es.dto.PanTaResourceOuterSearchV2DTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.dto.TextNerExtractDTO;
import com.zyhl.yun.api.outer.domainservice.BlackResourceHandleService;
import com.zyhl.yun.api.outer.enums.BlackResourceParityEnum;
import com.zyhl.yun.api.outer.external.CmicTextService;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.annotation.MethodExecutionTimeLog;
import com.zyhl.hcy.yun.ai.common.base.es.domain.datahelper.repository.EsPanTaResourceRepository;
import com.zyhl.hcy.yun.ai.common.base.es.entity.EsPanTaResourceEntity;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.StopWatchUtil;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.enums.UserBelongsPlatformEnum;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddContentDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatTidbSaveDTO;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.service.AlgorithmAiRegisterService;
import com.zyhl.yun.api.outer.application.service.chat.DialogueIntentionService;
import com.zyhl.yun.api.outer.application.service.chat.DialogueRecommendService;
import com.zyhl.yun.api.outer.application.service.external.AsyncSearchService;
import com.zyhl.yun.api.outer.application.service.external.SearchImageAlbumListService;
import com.zyhl.yun.api.outer.application.util.TimeRangeUtils;
import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;
import com.zyhl.yun.api.outer.application.vo.ContentExtInfoVO;
import com.zyhl.yun.api.outer.config.*;
import com.zyhl.yun.api.outer.constants.FutureConstants;
import com.zyhl.yun.api.outer.domain.entity.DialogueIntentionEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.vo.*;
import com.zyhl.yun.api.outer.domain.vo.chat.search.*;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.*;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.*;
import com.zyhl.yun.api.outer.domainservice.SearchReturnTermsService;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddResultTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.OutAuditStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.search.*;
import com.zyhl.yun.api.outer.external.service.UserEtnService;
import com.zyhl.yun.api.outer.strategy.chat.searchresult.SearchStrategy;
import com.zyhl.yun.api.outer.strategy.chat.searchresult.SearchStrategyEnum;
import com.zyhl.yun.api.outer.util.EntityListUtils;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.VersionUtil;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;
import com.zyhl.yun.api.outer.vo.IntentEntityVO;
import com.zyhl.yun.api.outer.vo.KeyValueVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;

import java.util.concurrent.*;

/**
 * 智能搜索（异步保存数据）
 * <AUTHOR>
 */
@Service
@Slf4j
@RefreshScope
@RequiredArgsConstructor
public class AsyncSearchServiceImpl implements AsyncSearchService {

    private final DialogueIntentionService intentionService;

    private final SearchPromptProperties promptProperties;

    private final AlgorithmAiRegisterService aiRegisterService;

    private final DialogueRecommendService recommendService;

    private final SearchSuffixProperties searchSuffixProperties;

    private final WhiteListProperties whiteListProperties;

    private final SearchReturnTermsService searchReturnTermsService;

    private final SearchResultProperties searchResultProperties;

    private final SearchParamProperties searchParamProperties;

    private final SourceChannelsProperties sourceChannelsProperties;

    private final AllNetworkSearchProperties allNetworkSearchProperties;

    private final UserEtnService userEtnService;
    
    private final SearchImageAlbumListService searchImageAlbumListService;

    private final BlackResourceHandleService blackResourceHandleService;

    private final CmicTextService cmicTextService;

    @Resource
    private EsPanTaResourceRepository esPanTaResourceRepository;

    @Resource(name = "searchThreadPool")
    private ExecutorService searchThreadPool;

    @Resource(name = "searchPantaThreadPool")
    private ExecutorService searchPantaThreadPool;

    @Override
    public AlgorithmChatAddVO searchIntentionHandle(ChatAddInnerDTO params) {
        long start = System.currentTimeMillis();
        /** 参数初始化 */
        AlgorithmChatAddDTO dto = params.getReqParams();
        AlgorithmChatAddVO resVO = params.getRespParams();
        DialogueIntentionVO intentionVO = params.getIntentionVO();
        // 对话id
        String dialogueId = resVO.getDialogueId();

        try {
            // 可能前端传入了意图字段，intentionVO为空
            if (Objects.isNull(intentionVO)) {
                intentionVO = getIntentionVO(dto, resVO);
                log.info("ASyncSearchServiceImpl-searchIntentionHandle，意图识别-耗时：{}ms", System.currentTimeMillis() - start);
            }

            // 获取意图结果code
            String intention = intentionService.getIntentionCode(intentionVO);

            // 初始化会话输入返回结果VO
            resVO.setCommands(intention);
            resVO.setResultType(ChatAddResultTypeEnum.SYNCHRONIZATION.getType());
            // 设置授权报名记录
            start = System.currentTimeMillis();
            handleSetRegisterStatus(dto, resVO, intention);
            log.info("ASyncSearchServiceImpl-searchIntentionHandle，设置授权报名记录-耗时：{}ms", System.currentTimeMillis() - start);

            /** 构建搜索参数 */
            start = System.currentTimeMillis();
			SearchParam searchParam = createSearchParam(intention, dto, intentionVO);
            log.info("ASyncSearchServiceImpl-searchIntentionHandle，构建搜索参数-耗时：{}ms", System.currentTimeMillis() - start);

            /** 异步处理全网搜推荐 */
            asyncAllNetworkSearchRecommend(searchParam, intention, params);

            /** 构建公共搜索参数 */
            SearchCommonParam searchCommonParam = new SearchCommonParam();
            searchCommonParam.setDialogue(dto.getContent().getDialogue());

            /** 获取搜索结果 */
            SearchResult searchResult = getSearchResult(searchParam, searchCommonParam);
			/** 实时搜索人物关系相册推荐 */
			ContentExtInfoVO extInfo = searchImageAlbumListService.searchImageSetAlbumList(
					dto.getContent().getSourceChannel(), VersionUtil.getVersionMap(), resVO.getLeadCopy(),
					resVO.getRecommend(), searchResult);
			if (null != extInfo) {
				// 设置扩展内容
				resVO.setLeadCopy(extInfo.getLeadCopy());
				resVO.setRecommend(extInfo.getRecommend());
			}
            /** 处理成功消息 */
            start = System.currentTimeMillis();
            AlgorithmChatAddVO algorithmChatAddVO = handleSuccessMsg(intention, dto, resVO, searchParam, searchResult, params, intentionVO);
            log.info("ASyncSearchServiceImpl-searchIntentionHandle，处理成功消息-耗时：{}ms", System.currentTimeMillis() - start);
            return algorithmChatAddVO;
        } catch (YunAiBusinessException e) {
            log.info("searchIntentionHandle dto:{} | resVO:{} | YunAiBusinessException error:", JsonUtil.toJson(dto),
                    JsonUtil.toJson(resVO), e);
            // 特定错误码处理
            start = System.currentTimeMillis();
            if (AiResultCode.CODE_10022024.getCode().equals(e.getCode())) {
                // 语义实体为空、搜索条件为空或搜索结果为空时的返回，返回【引导文案-LeadCopy，type=6】
                updateFailForException10022024(resVO, AiResultCode.CODE_10022024.getCode(), e.getMessage(), dialogueId, params);
            } else {
                // 处理失败消息
                handleFailMsg(AiResultCode.getByCodeOrMsg(e.getCode(), e.getMessage()), dialogueId, params);
            }
            log.info("ASyncSearchServiceImpl-searchIntentionHandle，特定错误码处理-耗时：{}ms", System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.error("searchIntentionHandle dto:{} | resVO:{} | Exception error:", JsonUtil.toJson(dto),
                    JsonUtil.toJson(resVO), e);
            // 处理失败消息
            start = System.currentTimeMillis();
            handleFailMsg(AiResultCode.CODE_10000101, dialogueId, params);
            log.info("ASyncSearchServiceImpl-searchIntentionHandle，处理失败消息-耗时：{}ms", System.currentTimeMillis() - start);
        }

        return resVO;
    }

    @Override
    public void handleSetRegisterStatus(AlgorithmChatAddDTO dto, AlgorithmChatAddVO resVO, String intention) {
        // 获取用户底座信息
        Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();
        // 老底座不需要设置
        if (UserBelongsPlatformEnum.OSE.getBelongsPlatform().equals(belongsPlatform)) {
            return;
        }

        // 意图012和018才需要设置智能相册报名记录
        if (DialogueIntentionEnum.existImageIntention(intention)) {
            // 新底座设置智能相册报名记录
            Integer aiAlbumStatus = aiRegisterService.checkAlbum(dto.getUserId()) ? 1 : 0;
            // todo 单词拼错了，前端改完后弃用 20240730【没确定前端改好不要动它】
            resVO.setAiAblumStatus(aiAlbumStatus);
            resVO.setAiAlbumStatus(aiAlbumStatus);
        }

        // 意图013和018才需要设置文档检索报名记录
        if (DialogueIntentionEnum.existDocumentIntention(intention)) {
            resVO.setAiDocStatus(aiRegisterService.checkDocSearch(dto.getUserId()) ? 1 : 0);
        }
    }

    /**
     * 校验语义结果，数据不可用抛出搜索引导文案业务异常
     *
     * @param entityList 语义结果列表
     */
    private void handleEntityList(AlgorithmChatAddContentDTO contentDTO, DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList) {
        String message = null;
        if(sourceChannelsProperties.isMail(contentDTO.getSourceChannel())){
            // 获取云邮助手引导语
            message = String.valueOf(promptProperties.getMailGuideRandomString(intentionEnum.getInstruction()));
        } else {
            // 获取引导文案
            message = String.valueOf(promptProperties.getGuideRandomString(intentionEnum.getInstruction()));
        }

        // 校验实体结果列表
        EntityListUtils.checkEntityList(entityList, message, intentionEnum);
    }

    /**
     * 处理成功消息
     *
     * @param intention    对话意图
     * @param dto          会话输入DTO
     * @param resVO        会话输入返回结果VO
     * @param searchParam  搜索条件对象
     * @param searchResult 搜索结果对象
     * @param params       用户输入对象
     * @param intentionVO  对话意图响应VO
     * @return 会话输入返回结果VO
     */
    @MethodExecutionTimeLog("智能搜索意图处理-handleSuccessMsg")
    private AlgorithmChatAddVO handleSuccessMsg(String intention, AlgorithmChatAddDTO dto, AlgorithmChatAddVO resVO,
                                                SearchParam searchParam, SearchResult searchResult, ChatAddInnerDTO params,
                                                DialogueIntentionVO intentionVO) {
        // 对话id
        String dialogueId = resVO.getDialogueId();
        Long dialogueIdLong = Long.parseLong(dialogueId);
        String dialogue = dto.getContent().getDialogue();
        String title = null;
        DialogueIntentionEnum intentionEnum = DialogueIntentionEnum.getByCode(intention);
        if (Objects.isNull(intentionEnum)) {
            log.error("ASyncSearchServiceImpl-handleSuccessMsg，dialogueIntentionEnum is null");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_SERVER_NOT_READY);
        }
        List<IntentionInfo> intentionInfoList = intentionVO.getIntentionInfoList();

        // 构建对话tidb保存数据
        AlgorithmChatTidbSaveDTO tidbSaveDTO = AlgorithmChatTidbSaveDTO.builder()
                .dialogueId(dialogueIdLong)
                .outAuditStatus(OutAuditStatusEnum.SUCCESS.getCode())
                .chatStatus(ChatStatusEnum.CHAT_SUCCESS.getCode())
                .msg(null)
                .build();
        params.setAlgorithmChatTidbSaveDTO(tidbSaveDTO);

        // 构建hbase的resp
        AiTextResultRespParameters respParameters = AiTextResultRespParameters.builder().param(searchParam).intentionInfoList(intentionInfoList).build();

        // 校验搜索结果为空时，额外将【引导文案-LeadCopy，type=6】保存到hbase
        List<SearchInfo> searchInfoList = searchResult.createSearchInfoList(intentionInfoList, searchResultProperties);
        if (CollUtil.isEmpty(searchInfoList)) {
            // 非018-综合搜索 && 邮箱搜索结果存在tips，放到【引导文案-LeadCopy，type=6】
            String tips = searchResult.checkAndGetSearchMailResultTips();
            if(!DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode().equals(intentionEnum.getCode()) && CharSequenceUtil.isNotBlank(tips)) {
                createLeadCopy6ByMessage(resVO, respParameters, tips);
                log.info("邮箱搜索结果存在tips，dialogueId:{} | searchResult:{}", dialogueId, JsonUtil.toJson(searchResult));
            } else {
                // 判断渠道，邮件渠道返回邮件引导文案，其他渠道返回普通引导文案
                boolean isMail = SourceChannelsProperties.isMailChannel(dto.getContent().getSourceChannel());
                String message = isMail ? promptProperties.getMailPromptRandomString(intentionEnum.getInstruction()) :
                        promptProperties.getPromptRandomString(intentionEnum.getInstruction());
                createLeadCopy6ByMessage(resVO, respParameters, message);
            }
            log.info("搜索结果为空，dialogueId:{} | searchResult:{}", dialogueId, JsonUtil.toJson(searchResult));
        } else {
            respParameters.setResult(ResultCodeEnum.SUCCESS);
            /** 获取搜索返回词V1（异常时返回默认值） */
            title = searchReturnTermsService.getSearchReturnTermsV1(params.getSearchReturnTermsFuture(), intention, dialogueIdLong, dialogue);
        }

        /** 把hbase的resp，放入innerDTO */
        // set搜索结果的标题（hbase）
        respParameters.setTitle(title);
        params.setHbaseResp(respParameters);

        /** 更新会话输入返回结果VO */
        resVO.setSearchParam(searchParam);
        resVO.setSearchResult(searchResult);
        resVO.setSearchInfoList(searchInfoList);
        // set搜索结果的标题
        resVO.setTitle(title);
        // set对话结果推荐
        setDialogueRecommend(intention, resVO, searchResult, params, dialogueIdLong);

        /** 获取和设置全网搜推荐 */
        getAndSetAllNetworkSearchList(params);

        return resVO;
    }

    /**
     * set对话结果推荐
     * @Author: WeiJingKun
     *
     * @param intention 对话意图
     * @param resVO 会话输入返回结果VO
     * @param searchResult 搜索结果对象
     * @param params 用户输入对象
     * @param dialogueIdLong 对话id
     * @return void
     */
    private void setDialogueRecommend(String intention, AlgorithmChatAddVO resVO, SearchResult searchResult, ChatAddInnerDTO params, Long dialogueIdLong) {
        long start = System.currentTimeMillis();
        // 获取功能搜索搜索意图推荐状态
        boolean functionRecommendFlag = getFunctionRecommendFlag(intention, params);
        // 功能搜索时，不走大模型获取推荐数据
        if(functionRecommendFlag){
            // 功能搜索有结果，用搜索结果第一个功能名(如果有多个结果，只取第一个)，通过【配置的模板】构建下一个问题推荐语句
            SearchFunctionResult searchFunctionResult = searchResult.getSearchFunctionResult();
            if(!(null == searchFunctionResult || CollUtil.isEmpty(searchFunctionResult.getFunctionList()))){
                // 构建意图推荐
                DialogueRecommendVO recommendVO = resVO.getRecommend();
                List<IntentionRecommendVO> intentionRecommendList = new ArrayList<>();
                // 获取推荐模板配置
                SearchResultProperties.Recommend recommend = searchResultProperties.getRecommend(SearchTypeEnum.FUNCTION.getSearchType());
                // format引导文案
                String copy = String.format(recommend.getIntentionTemplate(), searchFunctionResult.getFunctionList().get(0).getTitle());
                intentionRecommendList.add(new IntentionRecommendVO(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode(), copy, ""));
                if(ObjectUtil.isNull(recommendVO)) {
                    resVO.setRecommend(DialogueRecommendVO.builder().intentionList(intentionRecommendList).build());
                } else {
                    recommendVO.setIntentionList(intentionRecommendList);
                }
            }
        } else {
            recommendService.setFuturesResult(dialogueIdLong, resVO.getRecommend(), params.getFutures());
        }
        log.info("ASyncSearchServiceImpl-handleSuccessMsg，获取对话结果推荐-耗时：{}ms", System.currentTimeMillis() - start);
    }

    /**
     * 获取功能搜索搜索意图推荐状态
     * @Author: WeiJingKun
     *
     * @param intention 意图编码
     * @param params 用户输入对象
     * @return boolean
     */
    private boolean getFunctionRecommendFlag(String intention, ChatAddInnerDTO params) {
        boolean functionRecommendFlag = false;
        /**
         * 条件：
         * 1、功能搜索
         * 2、多意图数量  >= 2
         * 3、第二个意图为【000-文生文意图】
         */
        List<IntentionInfo> intentionInfoList = params.getIntentionVO().getIntentionInfoList();
        if(DialogueIntentionEnum.SEARCH_FUNCTION.getCode().equals(intention)
                && intentionInfoList.size() >= 2
                && DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode().equals(intentionInfoList.get(1).getIntention())){
            functionRecommendFlag = true;
        }
        return functionRecommendFlag;
    }

    /**
     * 根据提示文案创建【引导文案-LeadCopy，type=6】
     * @Author: WeiJingKun
     *
     * @param resVO 会话输入返回结果VO
     * @param respParameters
     * @param message
     * @return void
     */
    private static void createLeadCopy6ByMessage(AlgorithmChatAddVO resVO, AiTextResultRespParameters respParameters, String message) {
        long start = System.currentTimeMillis();
        respParameters.setResultCode(AiResultCode.CODE_10022024.getCode());
        respParameters.setResultMsg(message);
        if(null == resVO.getLeadCopy()) {
        	//未设置leadcopy才执行
        	/** 将抛异常处理改为返回【引导文案-LeadCopy，type=6】 */
        	// 构建引导文案
        	LeadCopyVO leadCopyVO = LeadCopyVO.builder()
        			// 类型6：语义实体为空、搜索条件为空或搜索结果为空时的返回，仅返回提示文案promptCopy
        			.type(LeadCopyTypeEnum.TYPE6.getCode())
        			// 提示文案
        			.promptCopy(message)
        			.build();
        	// hbase数据存入leadCopyVO
        	respParameters.setLeadCopy(leadCopyVO);
        	// 会话输入返回结果VO，set引导文案
        	resVO.setLeadCopy(leadCopyVO);
        }
        log.info("ASyncSearchServiceImpl-createLeadCopy6ByMessage，根据提示文案创建【引导文案-LeadCopy，type=6】-耗时：{}ms", System.currentTimeMillis() - start);
    }

    /**
     * 处理失败消息
     *
     * @param resultCode AI结果code对象
     * @param dialogueId 对话id
     * @param params     用户输入对象
     */
    private void handleFailMsg(AiResultCode resultCode, String dialogueId, ChatAddInnerDTO params) {
        updateFailThrowException(resultCode.getCode(), resultCode.getMsg(), dialogueId, params);
    }

    /**
     * 更新失败状态并抛出异常
     *
     * @param code       错误码code
     * @param msg        错误码信息
     * @param dialogueId 对话id
     * @param params     用户输入对象
     */
    private void updateFailThrowException(String code, String msg, String dialogueId, ChatAddInnerDTO params) {
        // 构建对话tidb保存数据
        AlgorithmChatTidbSaveDTO tidbSaveDTO = AlgorithmChatTidbSaveDTO.builder()
                .dialogueId(Long.parseLong(dialogueId))
                .outAuditStatus(OutAuditStatusEnum.FAIL.getCode())
                .chatStatus(ChatStatusEnum.CHAT_FAIL.getCode())
                .msg(msg)
                .build();
        params.setAlgorithmChatTidbSaveDTO(tidbSaveDTO);

        // 构建hbase的resp
        AiTextResultRespParameters respParameters = AiTextResultRespParameters.builder()
                .resultCode(code)
                .resultMsg(msg)
                .build();
        // 把hbase的resp，放入innerDTO
        params.setHbaseResp(respParameters);

        // 抛出转换后的异常
        throw new YunAiBusinessException(code, msg);
    }

    /**
     * 语义实体为空、搜索条件为空或搜索结果为空时的返回
     * 将抛异常处理改为返回【引导文案-LeadCopy，type=6】
     *
     * @param resVO      会话输入返回结果VO
     * @param code       错误码code
     * @param msg        错误码信息
     * @param dialogueId 对话id
     * @param params     用户输入对象
     */
    private void updateFailForException10022024(AlgorithmChatAddVO resVO, String code, String msg, String dialogueId, ChatAddInnerDTO params) {
        if (AiResultCode.CODE_10022024.getCode().equals(code)) {
            // 构建对话tidb保存数据
            AlgorithmChatTidbSaveDTO tidbSaveDTO = AlgorithmChatTidbSaveDTO.builder()
                    .dialogueId(Long.parseLong(dialogueId))
                    .outAuditStatus(OutAuditStatusEnum.SUCCESS.getCode())
                    .chatStatus(ChatStatusEnum.CHAT_SUCCESS.getCode())
                    .msg(msg)
                    .build();
            params.setAlgorithmChatTidbSaveDTO(tidbSaveDTO);

            /** 更新hbase结果 */
            // 构建引导文案
            LeadCopyVO leadCopyVO = LeadCopyVO.builder()
                    // 类型6：语义实体为空、搜索条件为空或搜索结果为空时的返回，仅返回提示文案promptCopy
                    .type(LeadCopyTypeEnum.TYPE6.getCode())
                    // 提示文案
                    .promptCopy(msg)
                    .build();
            // 构建hbase的resp
            AiTextResultRespParameters respParameters = AiTextResultRespParameters.builder()
                    .resultCode(code)
                    .resultMsg(msg)
                    .leadCopy(leadCopyVO)
                    .build();
            // 把hbase的resp，放入innerDTO
            params.setHbaseResp(respParameters);

            /** 会话输入返回结果VO，set引导文案 */
            resVO.setLeadCopy(leadCopyVO);
        }
    }

    /**
     * 获取搜索结果
     *
     * @Author: WeiJingKun
     */
    @MethodExecutionTimeLog("获取搜索结果-serviceImpl")
    @Override
    public SearchResult getSearchResult(SearchParam searchParam, SearchCommonParam searchCommonParam) throws ExecutionException, InterruptedException, TimeoutException {
        log.info("获取搜索结果-serviceImpl enter searchParam:{}", JSONUtil.toJsonStr(searchParam));
    	/** 参数初始化 */
        SearchResult searchResult = SearchResult.builder().build();
        if (null == searchParam) {
            return searchResult;
        }

        // 获取主线程的ThreadLocal信息，并且异步调用前，将父线程中的请求信息绑定给子线程
        RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo = RequestContextHolder.getThreadLocalInfoAndBindingAttributes();

        /** 添加搜索处理任务 */
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        List<Object> needSearchSubParamList = searchParam.createNeedSearchSubParamList();
        if(CollUtil.isNotEmpty(needSearchSubParamList)){
            needSearchSubParamList.forEach(needSearchSubParam -> {
                futures.add(asyncSearch(searchResult, needSearchSubParam, searchCommonParam, mainThreadLocalInfo));
            });
        }

        /** 过滤null，获取异步线程CompletableFuture集合 */
        List<CompletableFuture<Void>> completableFutureList = futures.stream().filter(Objects::nonNull).collect(Collectors.toList());

        /** 等到所有任务都完成，并设置超时 */
        if (CollUtil.isNotEmpty(completableFutureList)) {
            for (CompletableFuture<Void> future : completableFutureList) {
                try {
                    future.get(FutureConstants.SIXTY_SECONDS, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    log.error("ASyncSearchServiceImpl-getSearchResult，future.get()，异常", e);
                }
            }
        }

        return searchResult;
    }

    /**
     * 使用策略工厂模式，【异步】获取搜索接口结果
     *
     * @param searchResult   搜索结果
     * @param searchSubParam 搜索子参数
     * @param searchCommonParam 搜索公共参数
     * @param mainThreadLocalInfo 主线程ThreadLocal信息
     * @return java.util.concurrent.CompletableFuture<java.lang.Void>
     * @Author: WeiJingKun
     */
    private CompletableFuture<Void> asyncSearch(SearchResult searchResult, Object searchSubParam, SearchCommonParam searchCommonParam,
                                                RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo) {
        if (ObjectUtil.isNotNull(searchSubParam)) {
            return CompletableFuture.runAsync(() -> {
                /**
                 * 获取搜索策略（新增搜索类型，需要在SearchStrategyEnum添加）
                 * @see com.zyhl.yun.api.outer.strategy.chat.searchresult.SearchStrategyEnum
                 */
                SearchStrategy strategy = SearchStrategyEnum.getSearchStrategyByKey(searchSubParam.getClass());
                if (ObjectUtil.isNull(strategy)) {
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_SERVER_NOT_READY);
                }
                // 把主线程ThreadLocal信息set到子线程
                RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);
                // 执行搜索策略
                strategy.performSearch(searchResult, searchSubParam, searchCommonParam);
            }, searchThreadPool);
        }
        return null;
    }

    /**
     * 构建搜索参数
     * @Author: WeiJingKun
     * @param intention    意图
     * @param dto          会话输入入参DTO
     * @param intentionVO  对话意图响应VO
     * @return 搜索参数
     */
    private SearchParam createSearchParam(String intention, AlgorithmChatAddDTO dto, DialogueIntentionVO intentionVO) {
        DialogueIntentionEnum intentionEnum = DialogueIntentionEnum.getByCode(intention);
        if (Objects.isNull(intentionEnum)) {
            log.error("SearchServiceImpl-createSearchParam，dialogueIntentionEnum is null");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_SERVER_NOT_READY);
        }
        /** 参数初始化 */
        AlgorithmChatAddContentDTO contentDTO = dto.getContent();
        String userId = dto.getUserId();
        Integer provinceCode = userEtnService.getUserInfoData(Long.parseLong(userId)).getProvinceCode();
        IntentionInfo firstIntentionInfo = intentionVO.getIntentionInfoList().get(0);
        List<IntentionInfo> subIntentions = firstIntentionInfo.getSubIntentions();
        List<IntentEntityVO> entityList = firstIntentionInfo.getEntityList();
        /** 构建搜索参数 */
        SearchParam searchParam = SearchParam.builder().build();
        switch (intentionEnum) {
            /** 搜图片 */
            case SEARCH_IMAGE:
                handleImageCreateParam(searchParam, contentDTO, intentionEnum, entityList, userId);
                break;
            /** 搜文档 */
            case SEARCH_DOCUMENT:
                createSearchFileParam(searchParam, contentDTO, intentionEnum, entityList, false);
                break;
            /** 搜视频 */
            case SEARCH_VIDEO:
                createSearchFileParam(searchParam, contentDTO, intentionEnum, entityList, false);
                break;
            /** 搜音频 */
            case SEARCH_AUDIO:
                createSearchFileParam(searchParam, contentDTO, intentionEnum, entityList, false);
                break;
            /** 搜文件夹 */
            case SEARCH_FOLDER:
                createSearchFileParam(searchParam, contentDTO, intentionEnum, entityList, false);
                break;
            /** 搜笔记 */
            case SEARCH_NOTE:
                createSearchNoteParam(searchParam, contentDTO, intentionEnum, entityList);
                break;
            /** 综合搜索 */
            case COMPREHENSIVE_SEARCH:
                createAllSearchParam(searchParam, contentDTO, intentionEnum, subIntentions, entityList, provinceCode);
                break;
            /** 活动搜索 */
            case SEARCH_ACTIVITY:
                createSearchActivityParam(searchParam, contentDTO, intentionEnum, entityList, provinceCode);
                break;
            /** 功能搜索 */
            case SEARCH_FUNCTION:
                createSearchFunctionParam(searchParam, contentDTO, intentionEnum, entityList, provinceCode);
                break;
            /** 发现广场搜素 */
            case SEARCH_DISCOVERY:
                handleDiscoveryParamCreate(searchParam, contentDTO, intentionEnum, entityList);
                break;
            /** 圈子搜索 */
            case SEARCH_GROUP:
                createSearchGroupParam(searchParam, contentDTO, intentionEnum, entityList);
                break;
            /** 邮件搜索 */
            case SEARCH_MAIL:
                createSearchMailParam(searchParam, contentDTO, intentionEnum, entityList);
                break;
            default:
                break;
        }

        return searchParam;
    }

    /**
     * 处理搜索图片意图创建参数
     */
    private void handleImageCreateParam(SearchParam searchParam, AlgorithmChatAddContentDTO contentDTO,
                                        DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList, String userId) {
        /** 判断用户所属底座 */
        Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();

        // 【老底座】使用个人资产搜索接口搜图
        if (UserBelongsPlatformEnum.OSE.getBelongsPlatform().equals(belongsPlatform)) {
            createSearchFileParam(searchParam, contentDTO, intentionEnum, entityList, false);
            return;
        }

        // 【新底座】查询智能相册报名记录
        boolean albumFlag = aiRegisterService.checkAlbum(userId);
        if (albumFlag) {
            // 调用语义搜图【最新版本只要是新底座已报名就直接调用语义搜图，不需要判断是否存在图片标签 20240913】
            Integer imageSortType = (contentDTO.getSortInfo() != null) ? contentDTO.getSortInfo().getImageSortType() : null;
            createSearchImageParam(searchParam, contentDTO.getDialogue(), imageSortType);
            return;
        }

        // 未报名 使用个人资产搜索接口
        createSearchFileParam(searchParam, contentDTO, intentionEnum, entityList, false);
    }

    /**
     * 构建所有搜索接口的请求参数
     *
     * @Author: WeiJingKun
     */
    private void createAllSearchParam(SearchParam searchParam, AlgorithmChatAddContentDTO contentDTO,
                                      DialogueIntentionEnum intentionEnum, List<IntentionInfo> subIntentions,
                                      List<IntentEntityVO> entityList, Integer provinceCode) {
        String userId = RequestContextHolder.getUserId();
        /** 判断用户所属底座 */
        Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();
        // 【新底座】查询智能相册报名记录
        boolean albumFlag = aiRegisterService.checkAlbum(userId);
        // 综合搜索：需要判断子意图存在情况，按子意图来搜索，无子意图则搜索全部【2024-09-20修改】
        if (allowSearchByType(DialogueIntentionEnum.SEARCH_NOTE, subIntentions)) {
            try {
                /** 构建笔记搜索接口参数 */
                createSearchNoteParam(searchParam, contentDTO, intentionEnum,
                        getEntityListByType(DialogueIntentionEnum.SEARCH_NOTE, subIntentions, entityList));
            } catch (YunAiBusinessException e) {
                log.warn("综合搜索意图下存在语义实体为空的情况,不应该影响【笔记搜索】接口，直接捕获这个异常 createSearchNoteParam entityList:{}, subIntentions:{} ｜ error:",
                        JsonUtil.toJson(entityList), JsonUtil.toJson(subIntentions), e);
            }
        }

        // 文档搜索意图列表（4个搜索【老底座或者未报名5个搜索】都是执行一个方法）
        List<DialogueIntentionEnum> fileIntentionEnums = getSearchPersonFileEnums(albumFlag, userId, belongsPlatform);
        boolean findSearchFileParam = false;
        for (DialogueIntentionEnum fileIntentionEnum : fileIntentionEnums) {
            // 综合搜索：判断是否为允许搜索的类型
            boolean allowSearch = DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode().equals(intentionEnum.getCode()) && allowSearchByType(fileIntentionEnum, subIntentions);
            if (intentionEnum.getCode().equals(fileIntentionEnum.getCode()) || allowSearch) {
                try {
                    /** 构建文件搜索接口参数 */
                    // 子意图包含只有一个搜xxx指定意图，按fileIntentionEnum入参
                    DialogueIntentionEnum searchIntentionEnum = hasSearchFileOnlyOne(fileIntentionEnums, subIntentions)
                            ? fileIntentionEnum
                            : intentionEnum;
                    createSearchFileParam(searchParam, contentDTO, searchIntentionEnum,
                            getEntityListByType(fileIntentionEnum, subIntentions, entityList), false);
                    // 是否找到搜索文件参数
                    findSearchFileParam = (null != searchParam.getSearchFileParam());
                } catch (YunAiBusinessException e) {
                    log.warn("综合搜索意图下存在语义实体为空的情况,不应该影响【文档搜索】接口，直接捕获这个异常 createSearchFileParam entityList:{}, subIntentions:{} ｜ error:",
                            JsonUtil.toJson(entityList), JsonUtil.toJson(subIntentions), e);
                }
            }
            if (findSearchFileParam) {
                // 找到搜索文件参数则退出
                break;
            }
        }

        if (allowSearchByType(DialogueIntentionEnum.SEARCH_ACTIVITY, subIntentions)) {
            try {
                /** 构建活动搜索接口参数 */
                createSearchActivityParam(searchParam, contentDTO, intentionEnum,
                        getEntityListByType(DialogueIntentionEnum.SEARCH_ACTIVITY, subIntentions, entityList),
                        provinceCode);
            } catch (YunAiBusinessException e) {
                log.warn("综合搜索意图下存在语义实体为空的情况,不应该影响【活动搜索】接口，直接捕获这个异常 createSearchActivityParam entityList:{}, subIntentions:{} ｜ error:",
                        JsonUtil.toJson(entityList), JsonUtil.toJson(subIntentions), e);
            }
        }

        if (allowSearchByType(DialogueIntentionEnum.SEARCH_FUNCTION, subIntentions)) {
            try {
                /** 构建功能搜索接口参数 */
                createSearchFunctionParam(searchParam, contentDTO, intentionEnum,
                        getEntityListByType(DialogueIntentionEnum.SEARCH_FUNCTION, subIntentions, entityList),
                        provinceCode);
            } catch (YunAiBusinessException e) {
                log.warn("综合搜索意图下存在语义实体为空的情况,不应该影响【功能搜索】接口，直接捕获这个异常 createSearchFunctionParam entityList:{}, subIntentions:{} ｜ error:",
                        JsonUtil.toJson(entityList), JsonUtil.toJson(subIntentions), e);
            }
        }

        if (allowSearchByType(DialogueIntentionEnum.SEARCH_GROUP, subIntentions)) {
            try {
                /** 构建圈子搜索接口参数 */
                createSearchGroupParam(searchParam, contentDTO, intentionEnum,
                        getEntityListByType(DialogueIntentionEnum.SEARCH_GROUP, subIntentions, entityList));
            } catch (YunAiBusinessException e) {
                log.warn("综合搜索意图下存在语义实体为空的情况,不应该影响【圈子搜索】接口，直接捕获这个异常 createSearchGroupParam entityList:{}, subIntentions:{} ｜ error:",
                        JsonUtil.toJson(entityList), JsonUtil.toJson(subIntentions), e);
            }
        }

        if (allowSearchByType(DialogueIntentionEnum.SEARCH_MAIL, subIntentions)) {
            try {
                /** 构建邮件搜索接口参数 */
                createSearchMailParam(searchParam, contentDTO, intentionEnum,
                        getEntityListByType(DialogueIntentionEnum.SEARCH_MAIL, subIntentions, entityList));
            } catch (YunAiBusinessException e) {
                log.warn("综合搜索意图下存在语义实体为空的情况,不应该影响【邮件搜索】接口，直接捕获这个异常 createSearchMailParam entityList:{}, subIntentions:{} ｜ error:",
                        JsonUtil.toJson(entityList), JsonUtil.toJson(subIntentions), e);
            }
        }

        /** 构建图片搜索接口参数， 非老底座并且智能相册报名*/
        if (allowSearchByType(DialogueIntentionEnum.SEARCH_IMAGE, subIntentions)) {
            if (!UserBelongsPlatformEnum.OSE.getBelongsPlatform().equals(belongsPlatform) && albumFlag) {
                // 【新底座】语义搜图
                Integer imageSortType = (contentDTO.getSortInfo() != null) ? contentDTO.getSortInfo().getImageSortType()
                        : null;
                try {
                    /** 构建图片搜索接口参数 */
                    createSearchImageParam(searchParam, contentDTO.getDialogue(), imageSortType);
                } catch (YunAiBusinessException e) {
                    log.warn("综合搜索意图下存在语义实体为空的情况,不应该影响【图片搜索】接口，直接捕获这个异常 createSearchImageParam entityList:{}, subIntentions:{} ｜ error:",
                            JsonUtil.toJson(entityList), JsonUtil.toJson(subIntentions), e);
                }
            }
        }

        if (allowSearchByType(DialogueIntentionEnum.SEARCH_DISCOVERY, subIntentions)) {
            try {
                /** 构建发现广场搜索参数 */
                handleDiscoveryParamCreate(searchParam, contentDTO, intentionEnum,
                        getEntityListByType(DialogueIntentionEnum.SEARCH_DISCOVERY, subIntentions, entityList));
            } catch (YunAiBusinessException e) {
                log.warn("综合搜索意图下存在语义实体为空的情况,不应该影响【发现广场搜索】接口，直接捕获这个异常 handleDiscoveryParamCreate entityList:{}, subIntentions:{} ｜ error:",
                        JsonUtil.toJson(entityList), JsonUtil.toJson(subIntentions), e);
            }
        }

    }
	
	/**
	 * 获取个人云搜索枚举列表（老底座或者未报名需要追加搜image）
	 * 
	 * @param userId
	 * @param belongsPlatform
	 * @return
	 */
	private List<DialogueIntentionEnum> getSearchPersonFileEnums(boolean albumFlag, String userId, Integer belongsPlatform) {
		log.info("getSearchPersonFileEnums userId:{}, albumFlag:{}, belongsPlatform:{}", userId, albumFlag, belongsPlatform);
		if (UserBelongsPlatformEnum.OSE.getBelongsPlatform().equals(belongsPlatform) || !albumFlag) {
			return DialogueIntentionEnum.searchPersonOseFileEnums();
		}
		return DialogueIntentionEnum.searchPersonFileEnums();
	}

	/**
	 * 个人云搜索只包含单个搜索XXX的
	 */
	private boolean hasSearchFileOnlyOne(List<DialogueIntentionEnum> fileIntentionEnums,
			List<IntentionInfo> subIntentions) {
		if (CollUtil.isEmpty(subIntentions)) {
			return false;
		}
		int total = 0;
		for (IntentionInfo subIntention : subIntentions) {
			// 个人云搜索包含，后面需要增加个人云搜索类型，枚举则需要添加到DialogueIntentionEnum.searchPersonFiles()方法内
			if (fileIntentionEnums.contains(DialogueIntentionEnum.getByCode(subIntention.getIntention()))) {
				total++;
			}
		}
        // 只存在一个搜索xxx
        return total == 1;
    }

	/**
     * 构建笔记搜索接口参数
     * @Author: WeiJingKun
     */
    private void createSearchNoteParam(SearchParam searchParam, AlgorithmChatAddContentDTO contentDTO,
                                       DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList) {
        // 校验语义结果，数据不可用抛出搜索引导文案业务异常
        handleEntityList(contentDTO, intentionEnum, entityList);

        // 根据【语义实体】处理
        SearchNoteParam searchNoteParam = SearchNoteParam.builder().build();
        List<String> keywords = new ArrayList<>();
        List<MillisecondTimeRange> timeRangeList = new ArrayList<>();
        for (IntentEntityVO intentEntityVO : entityList) {
            // 设置关键字
            setResultList(keywords, intentEntityVO.getMetaDataList());

            // 设置时间
            setTimeRangeList(timeRangeList, intentEntityVO);
        }

        // 笔记要求关键字必填
        if (CollectionUtils.isEmpty(keywords)) {
            // 综合搜索意图特殊处理
            if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.equals(intentionEnum)) {
                searchParam.setSearchNoteParam(null);
                return;
            }

            // 【笔记搜索】获取引导语，并抛出异常
            promptProperties.getGuideRandomStringAndThrowException(intentionEnum.getInstruction());
        }

        // 获取旧版本默认noteTypeList：0、1、2、3
        List<Integer> defalutNoteTypeList = ListUtil.toList(SearchNoteParam.NoteTypeEnum.TYPE0.getCode(), SearchNoteParam.NoteTypeEnum.TYPE1.getCode(), SearchNoteParam.NoteTypeEnum.TYPE2.getCode(), SearchNoteParam.NoteTypeEnum.TYPE3.getCode());
        // 客户端版本 < 小天11.3版本
        if(VersionUtil.xtClientLt113()){
            searchNoteParam.setNoteTypeList(defalutNoteTypeList);
        } else {
            // 新版本端：noteTypeList传0、1、2、3、4
            defalutNoteTypeList.add(SearchNoteParam.NoteTypeEnum.TYPE4.getCode());
            searchNoteParam.setNoteTypeList(defalutNoteTypeList);
        }

        /** 关键字列表过滤 */
        keywords = filterCommonKeywords("笔记搜索", keywords);

        /** set关键字 */
        keywords = keywords.stream().filter(StringUtils::isNotBlank).distinct().limit(10).collect(Collectors.toList());
        searchNoteParam.setKeywords(keywords);

        /** set搜索时间范围列表 */
        timeRangeList = timeRangeList.stream().distinct().collect(Collectors.toList());
        searchNoteParam.setTimeRangeList(timeRangeList);

        /** set笔记搜索接口参数 */
        searchParam.setSearchNoteParam(searchNoteParam);
    }

    /**
     * 构建个人资产搜索接口参数
     * @Author: WeiJingKun
     */
    private void createSearchFileParam(SearchParam searchParam, AlgorithmChatAddContentDTO contentDTO,
                                       DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList, boolean imageNamePriority) {
        // 校验语义结果，数据不可用抛出搜索引导文案业务异常
        handleEntityList(contentDTO, intentionEnum, entityList);

        // 根据【语义实体】处理
        SearchFileParam searchFileParam = SearchFileParam.builder().build();
        /** set类别 */
        SearchFileParamTypeEnum searchFileParamTypeEnum = SearchFileParamTypeEnum.getByDialogueIntentionEnum(intentionEnum);
        if (null != searchFileParamTypeEnum) {
            searchFileParam.setType(searchFileParamTypeEnum.getCode());
        } else {
            // 默认：综合(全部)
            searchFileParam.setType(SearchFileParamTypeEnum.COMPREHENSIVE.getCode());
            log.warn("SearchServiceImpl-createSearchFileParam，searchFileParamTypeEnum is null");
        }

        List<String> nameList = new ArrayList<>();
        List<SecondTimeRange> fileTimeRangeList = new ArrayList<>();
        List<String> thingList = new ArrayList<>();
        List<String> suffixList = new ArrayList<>();
        for (IntentEntityVO intentEntityVO : entityList) {
            // 设置名称
            setResultList(nameList, intentEntityVO.getMetaDataList());

            // 处理搜索图片意图
            handleSearchImage(intentionEnum, imageNamePriority, nameList, thingList, intentEntityVO);

            // 设置时间
            setSecondTimeRangeList(fileTimeRangeList, intentEntityVO);

            /**
             * 1、h5Version >= 小天1.3.0版本
             * 2、文档后缀映射
             * 3、设置后缀名
             */
            if(VersionUtil.xtH5VersionGte130(null)){
                suffixList.addAll(searchSuffixProperties.mapingSuffixList(intentEntityVO.getSuffixList()));
                // 文档搜索默认每页50条
                searchFileParam.getPageInfo().setPageSize(50);
            }
        }

        /** set文件名称集合-最大支持10条 */
        nameList = filterCommonKeywords("个人资产搜索", nameList);
        searchFileParam.setNameList(nameList.stream().filter(StringUtils::isNotBlank).distinct().limit(10).collect(Collectors.toList()));

        /** set事物标签集合-最大支持10条 */
        thingList = thingList.stream().filter(StringUtils::isNotBlank).distinct().limit(10).collect(Collectors.toList());
        searchFileParam.setThingList(thingList);

        /** set搜索时间范围列表 */
        fileTimeRangeList = fileTimeRangeList.stream().distinct().collect(Collectors.toList());
        searchFileParam.setTimeList(fileTimeRangeList);

        /** set地理标签集合-最大支持10条 */
        searchFileParam.setAddressList(getAddressList(entityList));

        /** set后缀名集合-最大支持10条 */
        if(CollUtil.isNotEmpty(suffixList)){
            suffixList = suffixList.stream().filter(StringUtils::isNotBlank).distinct().limit(10).collect(Collectors.toList());
            searchFileParam.setSuffixList(suffixList);
        }

        /** set个人资产搜索接口参数 */
        searchParam.setSearchFileParam(searchFileParam);
    }

    /**
     * 公共关键字过滤
     * @Author: WeiJingKun
     *
     * @param keywords 关键字列表
     * @return 关键字列表
     */
    private List<String> filterCommonKeywords(String logPrefix, List<String> keywords) {
        // 复制一份keywords
        List<String> tempKeywords = new ArrayList<>(keywords);
        log.info("{}，关键字列表过滤前：{}", logPrefix, JSON.toJSONString(tempKeywords));
        // 公共关键字过滤
        tempKeywords = searchParamProperties.filterCommonKeywords(tempKeywords);
        log.info("{}，关键字列表过滤后：{}", logPrefix, JSON.toJSONString(tempKeywords));
        if(CollUtil.isEmpty(tempKeywords)){
            log.info("{}，关键字列表过滤后，没有关键字，使用过滤前的：{}", logPrefix, JSON.toJSONString(keywords));
            tempKeywords = keywords;
        }
        return tempKeywords;
    }

    /**
     * 获取地理标签集合
     */
    private List<String> getAddressList(List<IntentEntityVO> entityList) {
        return entityList.stream()
                .filter(intentEntityVO -> !CollectionUtils.isEmpty(intentEntityVO.getPlaceList()))
                .flatMap(intentEntityVO -> intentEntityVO.getPlaceList().stream())
                .distinct()
                .filter(StringUtils::isNotBlank)
                .limit(10)
                .collect(Collectors.toList());
    }

    /**
     * 处理搜索图片意图
     *
     * @param intentionEnum     意图枚举
     * @param imageNamePriority 是否图片名称标签优先
     * @param nameList          名称标签列表
     * @param thingList         事物标签列表
     * @param intentEntityVO    语义识别结果VO
     */
    private void handleSearchImage(DialogueIntentionEnum intentionEnum, boolean imageNamePriority, List<String> nameList,
                                   List<String> thingList, IntentEntityVO intentEntityVO) {
        // 非搜索图片意图不需要处理
        if (!DialogueIntentionEnum.SEARCH_IMAGE.equals(intentionEnum)) {
            return;
        }

        // 图片名称标签搜索优先 加入图片标签列表
        if (imageNamePriority) {
            nameList.clear();
            nameList.addAll(intentEntityVO.getImageNameList());
            return;
        }

        // 事物标签列表存在数据，设置事务标签
        if (!CollectionUtils.isEmpty(intentEntityVO.getLabelList())) {
            setResultList(thingList, intentEntityVO.getLabelList());
            // 事物标签优先级最高,清空nameList
            nameList.clear();
            return;
        }

        // 事物标签不存在数据，图片名称标签列表存在数据，加入图片名称标签列表
        if (!CollectionUtils.isEmpty(intentEntityVO.getImageNameList())) {
            nameList.addAll(intentEntityVO.getImageNameList());
        }
    }

    /**
     * 构建语义搜图接口参数
     *
     * @Author: WeiJingKun
     */
    private void createSearchImageParam(SearchParam searchParam, String dialogue, Integer sortType) {
        SearchImageParam searchImageParam = SearchImageParam.builder().build();

        /** set关键字 */
        searchImageParam.setText(dialogue);

        /** set排序方式 */
        searchImageParam.setSortType(sortType != null ? sortType : ImageSortTypeEnum.CORRELATION_SORT.getCode());

        /** set语义搜图接口参数 */
        searchParam.setSearchImageParam(searchImageParam);
    }

    /**
     * 获取对话意图响应VO
     *
     * @param dto    会话输入入参DTO
     * @param result 会话输入返回结果VO
     * @return 对话意图响应VO
     */
    @Override
    public DialogueIntentionVO getIntentionVO(AlgorithmChatAddDTO dto, AlgorithmChatAddVO result) {
        DialogueIntentionEntity.DialogueInfo currentDialogueInfo = new DialogueIntentionEntity.DialogueInfo();
        String prompt = dto.getContent().getPrompt();
        if (CharSequenceUtil.isNotEmpty(prompt)) {
            currentDialogueInfo.setDialogue(prompt.concat(dto.getContent().getDialogue()));
        } else {
            currentDialogueInfo.setDialogue(dto.getContent().getDialogue());
        }

        currentDialogueInfo.setTimestamp(DateUtil.formatTime(dto.getContent().getTimestamp()));
		return intentionService.getDialogueIntentionVO(dto.getContent().getSourceChannel(), result.getSessionId(),
				result.getDialogueId(), dto.getUserId(), currentDialogueInfo, dto.getEnableAiSearch());
    }

    /**
     * 设置时间范围列表（秒），长度14位
     *
     * @param secondTimeRangeList 时间范围列表（秒）
     * @param intentEntityVO    实体识别结果VO
     */
    private void setSecondTimeRangeList(List<SecondTimeRange> secondTimeRangeList, IntentEntityVO intentEntityVO) {
        List<String> timeList = intentEntityVO.getTimeList();
        if (CollectionUtils.isEmpty(timeList)) {
            return;
        }
        List<MillisecondTimeRange> timeRangeList = TimeRangeUtils.convertTimeToRanges(timeList);
        timeRangeList.forEach(timeRange -> {
            SecondTimeRange secondTimeRange = SecondTimeRange.builder()
                    .startAt(CharSequenceUtil.sub(timeRange.getStartAt(), 0, 14))
                    .endAt(CharSequenceUtil.sub(timeRange.getEndAt(), 0, 14))
                    .build();
            secondTimeRangeList.add(secondTimeRange);
        });
    }

    /**
     * 设置时间范围列表
     *
     * @param timeRangeList  时间范围列表
     * @param intentEntityVO 实体识别结果VO
     */
    private void setTimeRangeList(List<MillisecondTimeRange> timeRangeList, IntentEntityVO intentEntityVO) {
        List<String> timeList = intentEntityVO.getTimeList();
        if (CollectionUtils.isEmpty(timeList)) {
            return;
        }

        timeRangeList.addAll(TimeRangeUtils.convertTimeToRanges(timeList));
    }

    @Override
    public void setResultList(List<String> resultList, List<KeyValueVO> keyValueVoList) {
        if (CollectionUtils.isEmpty(keyValueVoList)) {
            return;
        }

        // 处理resultList
        keyValueVoList.stream()
                .map(KeyValueVO::getValue)
                .filter(Objects::nonNull)
                .forEach(resultList::addAll);
    }

    /**
     * 构建活动搜索参数
     * <AUTHOR>
     * @date 2024-6-3 11:11
     */
    private void createSearchActivityParam(SearchParam searchParam, AlgorithmChatAddContentDTO contentDTO,
                                           DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList, Integer provinceCode) {
        // 校验语义结果，数据不可用抛出搜索引导文案业务异常
        handleEntityList(contentDTO, intentionEnum, entityList);

        // 根据【语义实体】处理
        SearchActivityParam searchActivityParam = SearchActivityParam.builder().build();
        List<String> keywords = new ArrayList<>();
        for (IntentEntityVO intentEntityVO : entityList) {
            setResultList(keywords, intentEntityVO.getMetaDataList());
        }

        keywords = keywords.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(keywords)) {
            // 综合搜索意图特殊处理
            if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.equals(intentionEnum)) {
                searchParam.setSearchActivityParam(null);
                return;
            }

            // 【活动搜索】获取引导语，并抛出异常
            promptProperties.getGuideRandomStringAndThrowException(intentionEnum.getInstruction());
        }

        //活动搜索接口不支持数组提供关键字
        searchActivityParam.setKeywords(keywords.get(0));
        searchActivityParam.setProvinceCode(provinceCode);

        /** set活动搜索接口参数 */
        searchParam.setSearchActivityParam(searchActivityParam);
    }

    /**
     * 构建功能搜索参数
     * <AUTHOR>
     * @date 2024-6-3 11:11
     */
    private void createSearchFunctionParam(SearchParam searchParam, AlgorithmChatAddContentDTO contentDTO,
                                           DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList, Integer provinceCode) {
        // 校验语义结果，数据不可用抛出搜索引导文案业务异常
        handleEntityList(contentDTO, intentionEnum, entityList);

        // 根据【语义实体】处理
        SearchFunctionParam searchFunctionParam = SearchFunctionParam.builder().build();
        List<String> keywords = new ArrayList<>();
        for (IntentEntityVO intentEntityVO : entityList) {
            setResultList(keywords, intentEntityVO.getMetaDataList());
        }

        keywords = keywords.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(keywords)) {
            // 综合搜索意图特殊处理
            if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.equals(intentionEnum)) {
                searchParam.setSearchFunctionParam(null);
                return;
            }

            // 【功能搜索】获取引导语，并抛出异常
            promptProperties.getGuideRandomStringAndThrowException(intentionEnum.getInstruction());
        }

        //功能搜索接口不支持数组提供关键字
        searchFunctionParam.setKeywords(keywords.get(0));
        searchFunctionParam.setProvinceCode(provinceCode);

        /** set功能搜索接口参数 */
        searchParam.setSearchFunctionParam(searchFunctionParam);
    }

    /**
     * 处理发现广场搜索参数创建
     * @Author: WeiJingKun
     */
    private void handleDiscoveryParamCreate(SearchParam searchParam, AlgorithmChatAddContentDTO contentDTO,
                                            DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList) {
        // h5Version < 小天1.5.1版本，创建发现广场搜索V1接口参数
        if(VersionUtil.xtH5VersionLt151(null)){
            log.info("handleDiscoveryParamCreate，h5Version < 小天1.5.1版本，创建发现广场搜索V1接口参数");
            createSearchDiscoveryParam(searchParam, contentDTO, entityList);
        } else {
            // h5Version >= 小天1.5.1版本，创建发现广场搜索V2接口参数
            log.info("handleDiscoveryParamCreate，h5Version >= 小天1.5.1版本，创建发现广场搜索V2接口参数");
            createSearchDiscoveryParamV2(searchParam, contentDTO, intentionEnum, entityList);
        }
    }

    /**
     * 构建发现广场搜索参数V2
     * @Author: WeiJingKun
     */
    private void createSearchDiscoveryParamV2(SearchParam searchParam, AlgorithmChatAddContentDTO contentDTO, DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList) {
        // 校验语义结果，数据不可用抛出搜索引导文案业务异常
        handleEntityList(contentDTO, intentionEnum, entityList);

        /** 根据【语义实体】处理 */
        SearchDiscoveryParam searchDiscoveryParam = SearchDiscoveryParam.builder().build();
        List<String> keywords = new ArrayList<>();
        List<String> queryTypeStrList = new ArrayList<>();
        for (IntentEntityVO intentEntityVO : entityList) {
            // 关键字列表，添加实体数据
            setResultList(keywords, intentEntityVO.getMetaDataList());
            // 关键字列表，添加地点数据
            keywords.addAll(intentEntityVO.getPlaceList());
            // 搜索类型，添加内容类型数据
            setResultList(queryTypeStrList, intentEntityVO.getLabelList());
        }

        /** 搜索类型处理 */
        List<Integer> queryTypeList = searchDiscoveryParamV2HandleQueryTypeList(queryTypeStrList);

        /** 关键字列表过滤 */
        keywords = searchDiscoveryParamV2FilterKeywords(keywords, queryTypeList);

        /** 去空，去重，判断必填参数 */
        keywords = keywords.stream().filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(keywords)) {
            // 综合搜索意图特殊处理
            if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.equals(intentionEnum)) {
                searchParam.setSearchDiscoveryParam(null);
                return;
            }

            // 【发现广场搜索】获取引导语，并抛出异常
            promptProperties.getGuideRandomStringAndThrowException(intentionEnum.getInstruction());
        }

        /** set搜索类型 */
        searchDiscoveryParam.setQueryTypes(queryTypeList);

        /** set搜索关键字列表 */
        searchDiscoveryParam.setKeywords(keywords);

        /** set发现广场搜索接口参数 */
        searchParam.setSearchDiscoveryParam(searchDiscoveryParam);
    }

    /**
     * 构建发现广场搜索参数V2-关键字列表过滤
     * @Author: WeiJingKun
     *
     * @param keywords 关键字列表
     * @param queryTypeList 搜索类型列表
     * @return 关键字列表
     */
    private List<String> searchDiscoveryParamV2FilterKeywords(List<String> keywords, List<Integer> queryTypeList) {
        log.info("searchDiscoveryParamV2FilterKeywords，关键字列表过滤前：{}", JSON.toJSONString(keywords));
        // 公共关键字过滤
        keywords = searchParamProperties.filterCommonKeywords(keywords);

        List<SearchParamProperties.ParamFilter> queryTypesFilterList = searchParamProperties.getParamFilterList(SearchTypeEnum.DISCOVERY.getSearchType(),  SearchParamProperties.ParamFilter.QUERY_TYPES);
        if(CollUtil.isNotEmpty(queryTypesFilterList)){
            for(Integer queryType : queryTypeList) {
                for(SearchParamProperties.ParamFilter queryTypesFilter : queryTypesFilterList) {
                    // 查全部或类型匹配，则过滤关键字
                    if(SearchDiscoveryParamQueryTypeEnum.ALL.getCode().equals(queryType) || queryTypesFilter.getType().equals(queryType.toString())){
                        keywords = queryTypesFilter.filterKeywords(keywords);
                    }
                }
            }
        }
        log.info("searchDiscoveryParamV2FilterKeywords，关键字列表过滤后：{}", JSON.toJSONString(keywords));
        return keywords;
    }

    @Override
    @NotNull
    public List<Integer> searchDiscoveryParamV2HandleQueryTypeList(List<String> queryTypeStrList) {
        // 搜索类型转换成数字
        List<Integer> queryTypeList = new ArrayList<>();
        queryTypeStrList = queryTypeStrList.stream().filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(queryTypeStrList)) {
            for (String queryTypeStr : queryTypeStrList) {
                SearchDiscoveryParamQueryTypeEnum queryTypeEnum = SearchDiscoveryParamQueryTypeEnum.getByKeyReturnDefault(queryTypeStr);
                if (ObjectUtil.isNotNull(queryTypeEnum)) {
                    queryTypeList.add(queryTypeEnum.getCode());
                }
            }
        }
        // 搜索类型去重
        queryTypeList = queryTypeList.stream().filter(ObjUtil::isNotNull).distinct().collect(Collectors.toList());
        // 没有值，则默认全部
        if (CollUtil.isEmpty(queryTypeList)) {
            queryTypeList.add(SearchDiscoveryParamQueryTypeEnum.ALL.getCode());
        }
        return queryTypeList;
    }

    /**
     * 构建发现广场搜索参数
     * @Author: WeiJingKun
     */
    private void createSearchDiscoveryParam(SearchParam searchParam, AlgorithmChatAddContentDTO contentDTO, List<IntentEntityVO> entityList) {
        // 根据【语义实体】处理
        SearchDiscoveryParam searchDiscoveryParam = SearchDiscoveryParam.builder().build();
        List<String> keywords = new ArrayList<>();
        for (IntentEntityVO intentEntityVO : entityList) {
            setResultList(keywords, intentEntityVO.getMetaDataList());
            // 关键字列表，添加地点数据
            keywords.addAll(intentEntityVO.getPlaceList());
        }

        keywords = keywords.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(keywords)) {
            // 意图识别不到关键字，把用户的输入作为搜索关键字
            keywords.add(contentDTO.getDialogue());
        }

        /** set搜索关键字 */
        searchDiscoveryParam.setKeyword(CollUtil.join(keywords, ""));

        /** set发现广场搜索接口参数 */
        searchParam.setSearchDiscoveryParam(searchDiscoveryParam);
    }

    /**
     * 构建圈子搜索参数（我的圈子 + 热门圈子）
     * @Author: WeiJingKun
     */
    private void createSearchGroupParam(SearchParam searchParam, AlgorithmChatAddContentDTO contentDTO,
                                        DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList) {
        // h5Version < 小天1.4.0版本，不进行圈子搜索
        if(VersionUtil.xtH5VersionLt140()){
            log.info("createSearchGroupParam，h5Version < 小天1.4.0版本，不进行圈子搜索");
            return;
        }
        // 构建我的圈子搜索参数
        createSearchMyGroupParam(searchParam, contentDTO, intentionEnum, entityList);
        // 构建热门圈子搜索参数
        createSearchRecommendGroupParam(searchParam, contentDTO, intentionEnum, entityList);
    }

    /**
     * 构建我的圈子搜索参数
     * @Author: WeiJingKun
     */
    private void createSearchMyGroupParam(SearchParam searchParam, AlgorithmChatAddContentDTO contentDTO,
                                          DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList) {
        // 校验语义结果，数据不可用抛出搜索引导文案业务异常
        handleEntityList(contentDTO, intentionEnum, entityList);

        // 根据【语义实体】处理
        SearchMyGroupParam searchMyGroupParam = SearchMyGroupParam.builder().build();
        List<String> keywords = new ArrayList<>();
        for (IntentEntityVO intentEntityVO : entityList) {
            setResultList(keywords, intentEntityVO.getMetaDataList());
        }

        keywords = keywords.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(keywords)) {
            // 综合搜索意图特殊处理
            if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.equals(intentionEnum)) {
                searchParam.setSearchMyGroupParam(null);
                return;
            }

            // 【圈子搜索】获取引导语，并抛出异常
            promptProperties.getGuideRandomStringAndThrowException(intentionEnum.getInstruction());
        }

        /** 关键字列表过滤 */
        keywords = filterCommonKeywords("圈子搜索", keywords);

        /** set搜索关键字 */
        searchMyGroupParam.setKeyword(CollUtil.join(keywords, ""));

        /** set我的圈子搜索接口参数 */
        searchParam.setSearchMyGroupParam(searchMyGroupParam);
    }

    /**
     * 构建热门圈子搜索参数
     * @Author: WeiJingKun
     */
    private void createSearchRecommendGroupParam(SearchParam searchParam, AlgorithmChatAddContentDTO contentDTO,
                                                 DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList) {
        // 校验语义结果，数据不可用抛出搜索引导文案业务异常
        handleEntityList(contentDTO, intentionEnum, entityList);

        // 根据【语义实体】处理
        SearchRecommendGroupParam searchRecommendGroupParam = SearchRecommendGroupParam.builder().build();
        List<String> keywords = new ArrayList<>();
        for (IntentEntityVO intentEntityVO : entityList) {
            setResultList(keywords, intentEntityVO.getMetaDataList());
        }

        keywords = keywords.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(keywords)) {
            // 综合搜索意图特殊处理
            if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.equals(intentionEnum)) {
                searchParam.setSearchRecommendGroupParam(null);
                return;
            }

            // 【圈子搜索】获取引导语，并抛出异常
            promptProperties.getGuideRandomStringAndThrowException(intentionEnum.getInstruction());
        }

        /** 关键字列表过滤 */
        keywords = filterCommonKeywords("热门圈子搜索", keywords);

        /** set搜索关键字 */
        searchRecommendGroupParam.setKeyword(CollUtil.join(keywords, ""));

        /** set热门圈子搜索接口参数 */
        searchParam.setSearchRecommendGroupParam(searchRecommendGroupParam);
    }

    /**
     * 构建邮件搜索参数
     * @Author: WeiJingKun
     */
    private void createSearchMailParam(SearchParam searchParam, AlgorithmChatAddContentDTO contentDTO,
                                       DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList) {
        // 校验语义结果，数据不可用抛出搜索引导文案业务异常
        handleEntityList(contentDTO, intentionEnum, entityList);

        // 根据【语义实体】处理
        SearchMailParam searchMailParam = SearchMailParam.builder().build();

        // 初始化请求参数
        List<String> fromList = new ArrayList<>();
        Integer read = null;
        List<MillisecondTimeRange> receivedDateList = new ArrayList<>();
        Integer mailType = null;

        // 遍历语义实体，构建请求参数
        for (IntentEntityVO intentEntityVO : entityList) {
            /**
             * 指定发件人列表 支持多个（发件人名称和发件人地址）搜索
             */
            CollUtil.addAll(fromList, intentEntityVO.getSenderList());
            CollUtil.addAll(fromList, intentEntityVO.getEmailAddressList());

            // 状态包含：0-不限 或 (1-未读和2-已读)，则设置为：0-不限
            List<String> statusList = intentEntityVO.getStatusList();
            if (CollUtil.isNotEmpty(statusList)) {
                if(SearchMailParam.ReadEnum.hasAllReadStatus(statusList)){
                    read = SearchMailParam.ReadEnum.ALL.getCode();
                } else {
                    read = SearchMailParam.getReadEnumByRemark(statusList.get(0));
                }
            }

            // 时间区间
//            setSecondTimeRangeList(receivedDateList, intentEntityVO);
            List<String> timeList = intentEntityVO.getTimeList();
            if (!CollectionUtils.isEmpty(timeList)) {
                List<MillisecondTimeRange> timeRangeList = TimeRangeUtils.convertTimeToRanges(timeList);
                CollUtil.addAll(receivedDateList, timeRangeList);
            }

            // 邮件类型
            List<String> typeList = intentEntityVO.getTypeList();
            if (CollUtil.isNotEmpty(typeList)) {
                mailType = SearchMailParam.getMailTypeEnumByRemark(typeList.get(0));
            }
        }

        // 检查是否可以使用邮件搜索（true-可以搜索，false-不可以搜索）
        if (null == mailType) {
            //默认重要邮件
            mailType = SearchMailParam.MailTypeEnum.IMPORTANT.getCode();
        }
        if(!checkCanSearchMail(contentDTO, mailType)){
            return;
        }

        /**
         * set-指定发件人列表 支持多个（发件人名称和发件人地址）搜索
         */
        searchMailParam.setFromList(fromList);

        /** set-读状态 */
        searchMailParam.setRead(read);

        /**
         * 搜索收信时间范围列表，每个时间范围均为或的关系，如果填了，TimeRange中的两个字段就是必填 邮箱目前只支持传1个
         */
        if (CollUtil.isNotEmpty(receivedDateList)) {
            searchMailParam.setReceivedDateList(CollUtil.toList(receivedDateList.get(0)));
        }

        /** set邮件类型：1-重要（默认）；0-普通 */
        searchMailParam.setMailType(mailType);

        /** set邮件搜索接口参数 */
        searchParam.setSearchMailParam(searchMailParam);
    }

    /**
     * 检查是否可以使用邮件搜索
     * @Author: WeiJingKun
     *
     * @return boolean true-可以搜索，false-不可以搜索
     */
    private boolean checkCanSearchMail(AlgorithmChatAddContentDTO contentDTO, Integer mailType){
        /** h5Version判断 */
        // h5Version < 小天1.4.0版本，不进行邮件搜索
        if (VersionUtil.xtH5VersionLt140() && sourceChannelsProperties.isXiaoTian(contentDTO.getSourceChannel())) {
            log.info("createSearchMailParam-checkCanSearchMail，h5Version < 小天1.4.0版本，不进行邮件搜索");
            return false;
        }

        /** 邮件类型判断 */
        SearchMailParam.MailTypeEnum mailTypeEnum = SearchMailParam.MailTypeEnum.getByCode(mailType);
        if(ObjectUtil.isNotNull(mailTypeEnum)){
            switch (mailTypeEnum) {
                /** 普通邮件 */
                case GEN:
                    // 小天渠道，目前不支持【普通邮件】搜索
                    if (sourceChannelsProperties.isXiaoTian(contentDTO.getSourceChannel())) {
                        log.info("createSearchMailParam-checkCanSearchMail，小天渠道，目前不支持【普通邮件】搜索");
                        return false;
                    }
                    break;
                /** 重要邮件 */
                case IMPORTANT:
                    // 【重要邮件】搜索 && 不在邮件搜索白名单，给提示语，放到LeadCopy=6，返回前端
                    if (!whiteListProperties.getMailSearch().canUse(RequestContextHolder.getUserId())) {
                        String prompt = whiteListProperties.getMailSearch().getPrompt();
                        log.error("createSearchMailParam-checkCanSearchMail，【重要邮件】搜索 && 不在邮件搜索白名单，提示词：{}", prompt);
                        throw new YunAiBusinessException(AiResultCode.CODE_10022024.getCode(), prompt);
                    }
                    break;
                default:
                    break;
            }
        }

        return true;
    }

    /**
	 * 获取搜索实体，先取子意图实体，兜底返回主意图实体列表
	 * @param intentionEnum 意图枚举
	 * @param subIntentions 子意图列表
	 * @param entityList 实体识别结果列表
	 * @return 实体识别结果列表
	 */
	private List<IntentEntityVO> getEntityListByType(DialogueIntentionEnum intentionEnum,
			List<IntentionInfo> subIntentions, List<IntentEntityVO> entityList) {
		if (CollUtil.isNotEmpty(subIntentions)) {
			for (IntentionInfo subIntention : subIntentions) {
				if (intentionEnum.getCode().equals(subIntention.getIntention())) {
					// 匹配意图，允许搜索
					return subIntention.getEntityList();
				}
			}
		}
		// 兜底返回主意图实体列表
		return entityList;
	}

	/**
	 * allow 执行搜索（有自意图执行自意图的特定搜索，无子意图则允许搜索intentionEnum）
     * @param intentionEnum 意图枚举
     * @param subIntentions 子意图列表
	 * @return
	 */
	private boolean allowSearchByType(DialogueIntentionEnum intentionEnum, List<IntentionInfo> subIntentions) {
		if (CollUtil.isEmpty(subIntentions)) {
			// 子意图列表为空，允许搜索
			return true;
		}
		for (IntentionInfo subIntention : subIntentions) {
			if (intentionEnum.getCode().equals(subIntention.getIntention())) {
				// 匹配意图，允许搜索
				return true;
			}
		}
		return false;
	}

    /**
     * 获取和设置全网搜推荐
     * @Author: WeiJingKun
     * @param params 用户输入对象
     */
    private void getAndSetAllNetworkSearchList(ChatAddInnerDTO params) {
        List<AllNetworkSearchRecommendVO> allNetworkSearchRecommendList = null;
        try {
            Future<List<AllNetworkSearchRecommendVO>> future = params.getAllNetworkSearchRecommendFuture();
            /** 获取和设置全网搜推荐 */
            if(null != future){
                // 获取全网搜推荐列表
                allNetworkSearchRecommendList = future.get(FutureConstants.SIXTY_SECONDS, TimeUnit.SECONDS);
                if(CollUtil.isNotEmpty(allNetworkSearchRecommendList)){
                    // 设置到 对话结果推荐对象
                    AlgorithmChatAddVO resVO = params.getRespParams();
                    DialogueRecommendVO recommendVO = resVO.getRecommend();
                    if(ObjectUtil.isNull(recommendVO)) {
                        resVO.setRecommend(DialogueRecommendVO.builder().allNetworkSearchList(allNetworkSearchRecommendList).build());
                    } else {
                        recommendVO.setAllNetworkSearchList(allNetworkSearchRecommendList);
                    }
                }
            } else {
                log.info("【获取和设置全网搜推荐】future为空");
            }
        } catch (Exception e) {
            log.error("【获取和设置全网搜推荐】异常", e);
        } finally {
            log.info("【获取和设置全网搜推荐】结果：{}", JsonUtil.toJson(allNetworkSearchRecommendList));
        }
    }

    /**
     * 异步处理全网搜推荐
     * @Author: WeiJingKun
     * @param searchParam 搜索参数
     * @param intention 意图编码
     * @param params 用户输入对象
     */
    private void asyncAllNetworkSearchRecommend(SearchParam searchParam, String intention, ChatAddInnerDTO params) {
        // 判断是否执行【异步处理全网搜推荐】
        boolean judgeSearchPanta = judgeSearchPanta(searchParam, intention, params);
        log.info("【异步处理全网搜推荐】是否执行：{}", judgeSearchPanta);
        if(!judgeSearchPanta){
            return;
        }

        try {
            // 获取主线程的ThreadLocal信息，并且异步调用前，将父线程中的请求信息绑定给子线程
            RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo = RequestContextHolder.getThreadLocalInfoAndBindingAttributes();

            /** 异步处理全网搜推荐 */
            Future<List<AllNetworkSearchRecommendVO>> future = CompletableFuture.supplyAsync(() -> {
                StopWatch stopWatch = StopWatchUtil.createStarted();
                PanTaResourceOuterSearchV2DTO searchDTO = null;
                List<EsPanTaResourceEntity> pantaResourceList = null;
                List<AllNetworkSearchRecommendVO> allNetworkSearchRecommendList = new ArrayList<>();
                try {
                    // 把主线程ThreadLocal信息set到子线程
                    RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);

                    // 搜索实体抽取
                    Map<PantaLabelEnum, List<String>> extractResult = searchEntityExtract(params.getReqParams().getContent().getDialogue());

                    // 黑名单过滤
                    Iterator<Map.Entry<PantaLabelEnum, List<String>>> iterator = extractResult.entrySet().iterator();
                    while (iterator.hasNext()) {
                        Map.Entry<PantaLabelEnum, List<String>> entry = iterator.next();
                        List<String> filtered = entry.getValue().stream()
                                .filter(s -> !blackResourceHandleService.isSensitive(s, BlackResourceParityEnum.PRECISE.getType()))
                                .collect(Collectors.toList());
                        if (CollUtil.isEmpty(filtered)) {
                            iterator.remove();
                        } else {
                            entry.setValue(filtered);
                        }
                    }
                    if(CollUtil.isEmpty(extractResult)){
                        log.info("【异步处理全网搜推荐】metaDataList为空");
                        return allNetworkSearchRecommendList;
                    }

                    // 构建 小站资源搜索条件
                    searchDTO = createPanTaResourceOuterSearchV2DTO(extractResult);

                    // 搜索小站资源
                    pantaResourceList = esPanTaResourceRepository.searchPanTaResourceV2(searchDTO);

                    // 构建 全网搜推荐列表
                    if(CollUtil.isNotEmpty(pantaResourceList)){
                        AllNetworkSearchRecommendVO allNetworkSearchRecommend = new AllNetworkSearchRecommendVO();
                        // 遍历extractResult，获取有value有值的数据，并取第一个
//                        for (Map.Entry<PantaLabelEnum, List<String>> entry : extractResult.entrySet()) {
//                            if (CollUtil.isNotEmpty(entry.getValue())) {
//                                allNetworkSearchRecommend.setQuery(entry.getValue().get(0)+allNetworkSearchProperties.getSuffix());
//                                break;
//                            }
//                        }
                        allNetworkSearchRecommend.setQuery(allNetworkSearchProperties.getSuffix());
                        allNetworkSearchRecommend.setButtonCopy(allNetworkSearchProperties.getButtonCopy());
                        allNetworkSearchRecommendList.add(allNetworkSearchRecommend);
                    }
                } catch (Exception e) {
                    log.error("【异步处理全网搜推荐】异常", e);
                } finally {
                    log.info("【异步处理全网搜推荐】耗时：{}\n 全网搜推荐列表：{}\n 入参：{}\n 小站资源结果：{}",
                            StopWatchUtil.logTime(stopWatch), JsonUtil.toJson(allNetworkSearchRecommendList), JsonUtil.toJson(searchDTO), JsonUtil.toJson(pantaResourceList));
                    StopWatchUtil.clearDuration();
                }
                return allNetworkSearchRecommendList;
            }, searchPantaThreadPool);
            // set全网搜推荐异步处理future
            params.setAllNetworkSearchRecommendFuture(future);
        } catch (Exception e) {
            log.error("【异步处理全网搜推荐】线程池异常", e);
        }
    }

    /**
     * 构建小站资源搜索条件V2
     * @Author: WeiJingKun
     *
     * @param extractResult 实体元数据列表
     * @return 小站资源搜索条件
     */
    @NotNull
    private PanTaResourceOuterSearchV2DTO createPanTaResourceOuterSearchV2DTO(Map<PantaLabelEnum, List<String>> extractResult) {
        AllNetworkSearchProperties.SearchPantaParam searchPantaParam = allNetworkSearchProperties.getSearchPantaParam();
        PanTaResourceOuterSearchV2DTO searchDTO = new PanTaResourceOuterSearchV2DTO();
        searchDTO.setMetaDataMap(extractResult);
        searchDTO.setReleaseYear(searchPantaParam.getReleaseYear());
        searchDTO.setIsAdult(searchPantaParam.getAdult());
        searchDTO.setIsDomestic(searchPantaParam.getDomestic());
        searchDTO.setPriority(searchPantaParam.getPriorityList());
        searchDTO.setSearchResourceTagRange(searchPantaParam.getBusinessResourceTypeList());
        return searchDTO;
    }

    /**
     * 搜索实体抽取
     *
     * @param dialogue 对话内容
     *
     * @return 搜索实体抽取结果
     */
    private Map<PantaLabelEnum, List<String>> searchEntityExtract(String dialogue) {
        AllNetworkSearchProperties.SearchEntityExtractDTO searchEntityExtract = allNetworkSearchProperties.getSearchEntityExtract();
        TextNerExtractDTO dto = new TextNerExtractDTO();
        dto.setText(dialogue);
        dto.setMaxTextLength(searchEntityExtract.getMaxLength());
        dto.setEnableAllEntity(searchEntityExtract.isReturnAll());
        dto.setEntityTypeList(searchEntityExtract.getEntityTypeList());
        dto.setAllEntityExample(searchEntityExtract.getAllEntityExample());
        dto.setTypeEntityExample(searchEntityExtract.getTypeEntityExample());
        dto.setRequestId(MDC.get(LogConstants.TRACE_ID));
        return cmicTextService.searchEntityExtract("【AI全网搜】", dto);
    }

    /**
     * 判断是否执行【小站资源搜索】
     * @Author: WeiJingKun
     * @param searchParam 搜索参数
     * @param intention 意图编码
     * @param params 用户输入对象
     * @return true：执行；false：不执行
     */
    private boolean judgeSearchPanta(SearchParam searchParam, String intention, ChatAddInnerDTO params) {
        AlgorithmChatAddDTO dto = params.getReqParams();
        AlgorithmChatAddContentDTO contentDTO = dto.getContent();

        // 小天渠道，才支持AI全网搜
        if (!sourceChannelsProperties.isXiaoTian(contentDTO.getSourceChannel())) {
            log.info("【异步处理全网搜推荐】非AI全网搜渠道：{}", contentDTO.getSourceChannel());
            return false;
        }

        // 是否搜小站：true-是，false-否
        if(!allNetworkSearchProperties.isSearchPanta()){
            log.info("【异步处理全网搜推荐】未开启搜小站");
            return false;
        }

        // 白名单校验
        List<String> allNetworkSearchWhiteUser = whiteListProperties.getAllNetworkSearchWhiteUser();
        if (CollUtil.isNotEmpty(allNetworkSearchWhiteUser) && !allNetworkSearchWhiteUser.contains(RequestContextHolder.getPhoneNumber())) {
            log.info("【异步处理全网搜推荐】非白名单用户");
            return false;
        }

        // 搜小站的意图
        boolean intentionFlag = false;
        DialogueIntentionEnum intentionEnum = DialogueIntentionEnum.getByCode(intention);
        switch (intentionEnum) {
            /** 搜视频 */
            case SEARCH_VIDEO:
                intentionFlag = ObjectUtil.isNotNull(searchParam.getSearchFileParam()) && allNetworkSearchProperties.judgeSearchPanta(intention, null);
                break;
            /** 发现广场搜素 */
            case SEARCH_DISCOVERY:
                SearchDiscoveryParam searchDiscoveryParam = searchParam.getSearchDiscoveryParam();
                // 搜索类型
                List<Integer> queryTypes = searchDiscoveryParam.getQueryTypes();
                // queryTypes，去空，转List<String>
                List<String> queryTypeStrList = queryTypes.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(queryTypeStrList)){
                    intentionFlag = ObjectUtil.isNotNull(searchDiscoveryParam) && allNetworkSearchProperties.judgeSearchPanta(intention, queryTypeStrList);
                }
                break;
            /** 综合搜索 */
            case COMPREHENSIVE_SEARCH:
                intentionFlag = true;
            default:
                break;
        }
        if(!intentionFlag){
            log.info("【异步处理全网搜推荐】非搜小站的意图");
        }
        return intentionFlag;
    }

}
