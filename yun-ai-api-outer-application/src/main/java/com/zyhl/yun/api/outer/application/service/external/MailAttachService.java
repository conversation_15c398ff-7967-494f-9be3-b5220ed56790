package com.zyhl.yun.api.outer.application.service.external;

import com.zyhl.yun.api.outer.application.vo.MailAttachVO;

import java.util.List;

/**
 * 邮箱附件service
 *
 * <AUTHOR>
 */
public interface MailAttachService {

	/**
	 * 获取邮箱附件
	 * @param userId 用户id
	 * @param mid mid
	 * @param sid sid
	 * @param rmKey rmKey
	 * @param fileNames 下载附件名称集合
	 * @return 邮箱附件集合
	 */
	List<MailAttachVO> getMailFiles(String userId, String mid, String sid, String rmKey, List<String> fileNames);

}
