package com.zyhl.yun.api.outer.application.chatv2.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zyhl.yun.api.outer.domain.vo.ChatApplicationType;
import lombok.Data;

import java.util.Date;

/**
 * 会话结果-VO
 * @Author: WeiJingKun
 */
@Data
public class MessageResultV2VO {

    /** 会话ID */
    private String sessionId;

    /** 会话标题 */
    private String title;

    /** 会话时间 */
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date createTime;

    /** 会话时间 */
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date updateTime;
    
    /** 是否星标 */
    private Boolean enableStar;

    /** 会话iconUrl */
    private String iconUrl;
    
    /** 对话应用类型 */
    private ChatApplicationType applicationInfo;

}
