package com.zyhl.yun.api.outer.application.service.chat.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.zyhl.hcy.yun.ai.common.base.annotation.MethodExecutionTimeLog;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.StopWatchUtil;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.enums.BaseResultCodeEnum;
import com.zyhl.hcy.yun.ai.common.model.api.utils.TextModelUtil;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.YunDiskClient;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatContentListDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatDeleteDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatListDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatPollingUpdateDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatStopDTO;
import com.zyhl.yun.api.outer.application.dto.YunDiskReqDTO;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatHistoryService;
import com.zyhl.yun.api.outer.application.service.chat.LeadCopyService;
import com.zyhl.yun.api.outer.application.service.external.AsyncSearchService;
import com.zyhl.yun.api.outer.application.service.external.MemberCenterService;
import com.zyhl.yun.api.outer.application.service.external.SearchImageAlbumListService;
import com.zyhl.yun.api.outer.application.service.external.YunDiskService;
import com.zyhl.yun.api.outer.application.service.mq.AiAssistantMqService;
import com.zyhl.yun.api.outer.application.util.SseEmitterDataUtils;
import com.zyhl.yun.api.outer.application.vo.ContentExtInfoVO;
import com.zyhl.yun.api.outer.application.vo.SmartFakeCheckVO;
import com.zyhl.yun.api.outer.application.vo.YunDiskContentVO;
import com.zyhl.yun.api.outer.config.EosFileExpireConfig;
import com.zyhl.yun.api.outer.config.SearchResultProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.constants.RegConst;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatMessageEntity;
import com.zyhl.yun.api.outer.domain.entity.TaskAiAbilityEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.vo.ChatApplicationType;
import com.zyhl.yun.api.outer.domain.vo.ContextRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.IntentionRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.domain.vo.chat.*;
import com.zyhl.yun.api.outer.domain.vo.chat.search.SearchInfo;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchCommonParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchResult;
import com.zyhl.yun.api.outer.domain.vo.common.PageInfoVO;
import com.zyhl.yun.api.outer.domainservice.AlgorithmChatContentDomain;
import com.zyhl.yun.api.outer.domainservice.SearchReturnTermsService;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.ChatMessageStarEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ImageSuffixEnum;
import com.zyhl.yun.api.outer.enums.ImageTransmissionTypeEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ApiVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatContentSortTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatMessageSortTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.EditResourceEnum;
import com.zyhl.yun.api.outer.enums.chat.OutAuditStatusEnum;
import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;
import com.zyhl.yun.api.outer.external.service.EOSExternalService;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatMessageRepository;
import com.zyhl.yun.api.outer.repository.ChatApplicationTypeRepository;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;
import com.zyhl.yun.api.outer.repository.TaskAiAbilityRepository;
import com.zyhl.yun.api.outer.repository.assembler.ChatContentAssembler;
import com.zyhl.yun.api.outer.repository.assembler.ChatMessageAssembler;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.RequestContextHolder.HeaderParams;
import com.zyhl.yun.api.outer.util.VersionUtil;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.TaskRespParamVO;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zyhl.yun.api.outer.constants.Const.NUM_16;

/**
 * <AUTHOR>
 * @data 2024/3/1 15:53
 */
@Slf4j
@Service
public class AlgorithmChatHistoryServiceImpl implements AlgorithmChatHistoryService {

    @Resource
    private AlgorithmChatMessageRepository algorithmChatMessageRepository;

    @Resource
    private AlgorithmChatContentRepository algorithmChatContentRepository;

    @Resource
    private ChatApplicationTypeRepository chatApplicationTypeRepository;

    @Resource
    private RedisOperateRepository redisOperateRepository;

    @Resource
    private ChatMessageAssembler messageAssembler;

    @Resource
    private ChatContentAssembler contentAssembler;

    @Resource
    private AsyncSearchService asyncSearchService;

    @Resource
    private MemberCenterService memberCenterService;

    @Resource
    private SourceChannelsProperties sourceChannelsProperties;

    @Resource
    private AiAssistantMqService aiAssistantMqService;

    @Resource(name = "algorithmChatThreadPool")
    private ExecutorService algorithmChatThreadPool;

    @Resource
    private TaskAiAbilityRepository taskAiAbilityRepository;

    @Resource
    private LeadCopyService leadCopyService;

    @Resource
    private EOSExternalService eosExternalService;

    @Resource
    private YunDiskService yunDiskService;

    @Resource
    private AlgorithmChatContentDomain algorithmChatContentDomain;

    @Resource
    private SearchReturnTermsService searchReturnTermsService;
  
    @Resource
    private SearchImageAlbumListService searchImageAlbumListService;
    
    @Resource
    private EosFileExpireConfig eosFileExpireConfig;

    @Resource
    private SearchResultProperties searchResultProperties;

    @Override
    @MethodExecutionTimeLog("历史会话列表查询-serviceImpl")
    public PageInfoVO<MessageVO> chatList(AlgorithmChatListDTO dto) {
        /** 参数初始化 */
        String userId = dto.getUserId();
        // 应用类型处理，没传值，则默认chat-普通对话
        String applicationType = dto.getApplicationType();
        String sourceChannel = dto.getSourceChannel();
        applicationType = CharSequenceUtil.isBlank(applicationType) ? ApplicationTypeEnum.CHAT.getCode() : applicationType;
        // 获取业务类型
        String businessType = sourceChannelsProperties.getTypeNullThrowException(sourceChannel);
        PageInfoDTO page = PageInfoDTO.getReqDTO(dto.getPageInfo());

        /** 获取会话信息 */
        PageInfo<AlgorithmChatMessageEntity> pageInfo = algorithmChatMessageRepository.chatList(
                AlgorithmChatMessageEntity.builder()
                        .userId(userId)
                        .businessType(businessType)
                        .sourceChannel(sourceChannel)
                        .applicationType(applicationType)
                        .sortType(dto.getSortType())
                        .build(),
                Integer.parseInt(page.getPageCursor()),
                page.getPageSize(),
                page.getNeedTotalCount()
        );
        List<AlgorithmChatMessageEntity> messageEntityList = pageInfo.getList();
        log.info("历史会话列表查询-AlgorithmChatHistoryServiceImpl-chatList-chatList，userId：{}，条件：{}，分页：{}，结果：{}",
                userId, JSON.toJSON(dto), JSON.toJSON(page), JSON.toJSON(messageEntityList));

        // 智能体类型，则需要获取对话应用类型信息map，key是应用id
        Map<String, ChatApplicationType> chatApplicationTypeMap = new HashMap<>(NUM_16);
        if (ApplicationTypeEnum.INTELLIGENT.getCode().equals(applicationType)) {
            chatApplicationTypeMap = chatApplicationTypeRepository.listToMapKeyIsId();
        }

        /** 封装返回结果 */
        PageInfoVO<MessageVO> result = PageInfoVO.getRespDTO(page, pageInfo);
        if (CollUtil.isEmpty(messageEntityList)) {
            return result;
        }
        List<MessageVO> messageVOList = new ArrayList<>();
        for (AlgorithmChatMessageEntity messageEntity : messageEntityList) {
            MessageVO messageVO = messageAssembler.toMessageVO(messageEntity);
            messageVO.setSessionId(String.valueOf(messageEntity.getId()));
            // 智能体类型，则需要获取智能体信息
            if (ApplicationTypeEnum.INTELLIGENT.getCode().equals(applicationType)) {
                String applicationId = messageEntity.getApplicationId();
                if (CharSequenceUtil.isNotBlank(applicationId)) {
                    messageVO.setApplicationInfo(chatApplicationTypeMap.get(applicationId));
                }
            }
			messageVO.setEnableStar(ChatMessageStarEnum.isStar(messageEntity.getEnableStar()));
            messageVOList.add(messageVO);
        }
        result.setList(messageVOList);
        log.info("历史会话列表查询-AlgorithmChatHistoryServiceImpl-chatList，方法返回：{}", JSON.toJSON(result));
        return result;
    }

    @Override
    @MethodExecutionTimeLog("历史对话列表查询-serviceImpl")
    public PageInfoVO contentList(AlgorithmChatContentListDTO dto) {
        /** 参数初始化 */
        StopWatch stopWatch = null;
        String userId = dto.getUserId();
        Long sessionId = dto.getSessionId();
        String applicationType = dto.getApplicationType();
        String sourceChannel = dto.getSourceChannel();
        // 获取业务类型
        String businessType = sourceChannelsProperties.getTypeNullThrowException(sourceChannel);
        PageInfoDTO page = PageInfoDTO.getReqDTO(dto.getPageInfo());

        /** 获取对话信息 */
        PageInfo<AlgorithmChatContentEntity> pageInfo = algorithmChatContentRepository.contentList(
                AlgorithmChatContentEntity.builder()
                        .userId(userId)
                        .sessionId(dto.getSessionId())
                        .businessType(businessType)
                        .sourceChannel(sourceChannel)
                        .applicationType(applicationType)
                        .sortType(dto.getSortType())
                        .build(),
                Integer.parseInt(page.getPageCursor()),
                page.getPageSize(),
                page.getNeedTotalCount()
        );
        
        // 判断起始分页，sessionId有传参，并且对话内容空，删除message数据
		if (PageInfoDTO.isStartPage(page) && null != dto.getSessionId()
				&& (null == pageInfo || (null != pageInfo && CollUtil.isEmpty(pageInfo.getList())))) {
			// 查找是否存在正常的sessionId+businessType，存在才删除
			if (algorithmChatMessageRepository.existNormalBySessionIdAndBusinessType(userId, sessionId, businessType)) {
				/** 删除会话 */
				AlgorithmChatMessageEntity messageEntity = AlgorithmChatMessageEntity.builder().userId(userId)
						.sessionIdList(Collections.singletonList(sessionId)).businessType(businessType).build();
				algorithmChatMessageRepository.deleteByIds(messageEntity);
				log.info("删除message数据 userId:{}, sessionId:{}", userId, sessionId);
			}
		}

        List<AlgorithmChatContentEntity> contentEntityList = pageInfo.getList();
        log.info("历史对话列表查询-AlgorithmChatHistoryServiceImpl-contentList-contentList，userId：{}，条件：{}，分页：{}，结果：{}",
                userId, JSON.toJSON(dto), JSON.toJSON(page), JSON.toJSON(contentEntityList));

        /** 封装返回结果 */
        PageInfoVO result = PageInfoVO.getRespDTO(page, pageInfo);
        if (CollUtil.isEmpty(contentEntityList)) {
            return result;
        }
        try {
            CopyOnWriteArrayList<ContentVO> contentVOList = new CopyOnWriteArrayList<>();

            // 会话id不为null或智能体类型，则需要获取对话应用类型信息map，key是应用id
            Map<String, ChatApplicationType> chatApplicationTypeMap;
            if (null != sessionId || ApplicationTypeEnum.INTELLIGENT.getCode().equals(applicationType)) {
                chatApplicationTypeMap = chatApplicationTypeRepository.listToMapKeyIsId();
            } else {
                chatApplicationTypeMap = new HashMap<>(NUM_16);
            }

            /** 并行处理数据 */
            stopWatch = StopWatchUtil.createStarted();
            // 获取主线程的ThreadLocal信息，并且异步调用前，将父线程中的请求信息绑定给子线程
            RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo = RequestContextHolder.getThreadLocalInfoAndBindingAttributes();

            // 并行处理数据，等到所有任务都完成，并设置超时
            CompletableFuture.allOf(contentEntityList.stream().map(contentEntity -> CompletableFuture.runAsync(() -> {
                // 把主线程ThreadLocal信息set到子线程
                RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);
                // 获取对话内容VO，add到列表中
                contentVOList.add(getContentVO(contentEntity, userId, sessionId, applicationType, chatApplicationTypeMap));
            }, algorithmChatThreadPool)).toArray(CompletableFuture[]::new)).get(60, TimeUnit.SECONDS);

            // 根据版本判断并转换list响应的VO类型
            checkAndConvertList(contentVOList, result, dto.getSortType());
        } catch (Exception e) {
            log.error("历史对话列表查询-AlgorithmChatHistoryServiceImpl-contentList，并行处理数据异常：", e);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_SERVER_UNAVAILABLE);
        } finally {
            assert stopWatch != null;
            log.info("历史对话列表查询-AlgorithmChatHistoryServiceImpl-contentList，并行处理结果耗时：{}", StopWatchUtil.logTime(stopWatch));
            StopWatchUtil.clearDuration();
        }

        return result;
    }

    /**
     * 根据接口版本号，判断并转换list响应的VO类型
     * @Author: WeiJingKun
     *
     * @param contentVOList 对话内容VO集合
     * @param result 分页结果
     */
    private void checkAndConvertList(CopyOnWriteArrayList<ContentVO> contentVOList, PageInfoVO result, Integer sortType) {
        if (CollUtil.isNotEmpty(contentVOList)) {
            // 根据创建时间倒序排序
            List<ContentVO> sortList = contentVOList.stream().sorted(Comparator.comparing(ContentVO::getCreateTime).reversed()).collect(Collectors.toList());
            if(ChatContentSortTypeEnum.CREATE_TIME_ASC.getCode().equals(sortType)) {
            	//创建时间升序
            	sortList = contentVOList.stream().sorted(Comparator.comparing(ContentVO::getCreateTime)).collect(Collectors.toList());
            }
            // 根据接口版本号，判断并转换list响应的VO类型
            ApiVersionEnum apiVersion = ApiVersionEnum.getByVersion(RequestContextHolder.getApiVersion());
            apiVersion = null == apiVersion ? ApiVersionEnum.V1 : apiVersion;
            switch (apiVersion) {
                // 转成ContentResultVO
                case V1:
                	//兼容新的思维链数据渲染到V1
                	mergeNewReasoningContent(sortList);
                    result.setList(contentAssembler.toContentResultVoList(sortList));
                    break;
                // 转成ContentResultVOV2
                case V2:
                	//兼容旧的思维链数据渲染到V2
                	splitOldReasoningContent(sortList);
                    result.setList(contentAssembler.toContentResultVoV2List(sortList));
                    break;
                default:
                	//TODO 兼容前端乱传版本号（前端改造后需要去掉）
                	mergeNewReasoningContent(sortList);
                    result.setList(contentAssembler.toContentResultVoList(sortList));
                    break;
            }
        } else {
            result.setList(new ArrayList<>());
        }
    }

	/**
	 * 兼容旧的思维链数据渲染到V2
	 * 
	 * @param contentList
	 */
	private void splitOldReasoningContent(List<ContentVO> contentList) {
		for (ContentVO content : contentList) {
			SseEmitterDataUtils.splitOldReasoningContent(content);
		}
	}

	/**
	 * 兼容新的思维链数据渲染到V1
	 * 
	 * @param contentList
	 */
	private void mergeNewReasoningContent(List<ContentVO> contentList) {
		for (ContentVO content : contentList) {
			SseEmitterDataUtils.mergeNewReasoningContent(content);
		}
	}

	/**
     * 获取对话内容VO
     *
     * @param contentEntity          对话内容实体
     * @param userId                 用户id
     * @param sessionId              对话id
     * @param applicationType        应用类型
     * @param chatApplicationTypeMap 对话应用类型map
     * @return com.zyhl.yun.api.outer.domain.vo.algorithmChat.ContentVO
     * @Author: WeiJingKun
     */
    @MethodExecutionTimeLog("获取对话内容VO-serviceImpl")
    private ContentVO getContentVO(AlgorithmChatContentEntity contentEntity, String userId, Long sessionId, String applicationType, Map<String, ChatApplicationType> chatApplicationTypeMap) {
        /** 初始化参数 */
        Long taskId = contentEntity.getTaskId();
        Integer taskStatus = contentEntity.getTaskStatus();
        String dialogueId = String.valueOf(contentEntity.getId());

        /** 构建获取对话内容VO公共数据 */
        ContentVO contentVO = createCommonVO(contentEntity);

        /**
         * 构建异步任务【处理中】状态
         * 任务id不为空（异步任务），并且任务在【处理中】
         * 处理不轮巡更新审核状态，马上查询历史记录的情况
         */
        if ((null != taskId) && TaskStatusEnum.IN_PROCESS.getCode().equals(taskStatus)) {
            contentVO.setResultCode(ResultCodeEnum.QUEUE_ADD_EXIST_FAILURE.getResultCode());
            contentVO.setResultMsg(ResultCodeEnum.QUEUE_ADD_EXIST_FAILURE.getResultMsg());
            /** 异步调用，轮巡方法，尝试补偿 */
            algorithmChatThreadPool.execute(() -> {
                if (CharSequenceUtil.isNotBlank(dialogueId)) {
                    AlgorithmChatPollingUpdateDTO algorithmChatPollingUpdateDTO = new AlgorithmChatPollingUpdateDTO();
                    algorithmChatPollingUpdateDTO.setUserId(userId);
                    algorithmChatPollingUpdateDTO.setDialogueId(Long.valueOf(dialogueId));
                    chatPollingUpdate(algorithmChatPollingUpdateDTO);
                }
            });
        }

        /** 会话id不为null或智能体类型，则需要获取智能体信息 */
        if (null != sessionId || ApplicationTypeEnum.INTELLIGENT.getCode().equals(applicationType)) {
            String applicationId = contentEntity.getApplicationId();
            if (CharSequenceUtil.isNotBlank(applicationId)) {
                contentVO.setApplicationInfo(chatApplicationTypeMap.get(applicationId));
            }
        }

        /** 返回构造输入资源信息 */
        handleInResourceIdAndType(contentVO);

        /** 设置输入输出资源id和type */
        setResourceIdAndType(contentVO, contentEntity, EditResourceEnum.OUT.getCode());

        return contentVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @MethodExecutionTimeLog("历史会话删除-serviceImpl")
    public void chatDelete(AlgorithmChatDeleteDTO dto) {
        log.info("历史会话删除-AlgorithmChatHistoryServiceImpl-chatDelete，dto：{}", JSON.toJSON(dto));
        /** 参数初始化 */
        String userId = dto.getUserId();
        List<Long> sessionIdList = dto.getSessionIdList();
        // 获取业务类型
        String businessType = sourceChannelsProperties.getTypeNullThrowException(dto.getSourceChannel());

        /** 删除会话 */
        AlgorithmChatMessageEntity messageEntity = AlgorithmChatMessageEntity.builder()
                .userId(userId)
                .sessionIdList(sessionIdList)
                .businessType(businessType)
                .build();
        boolean update = algorithmChatMessageRepository.deleteByIds(messageEntity);

        if (update) {
            /** 删除对话 */
            AlgorithmChatContentEntity contentEntity = AlgorithmChatContentEntity.builder()
                    .userId(userId)
                    .sessionIdList(sessionIdList)
                    .businessType(businessType)
                    .build();
            algorithmChatContentRepository.deleteBySessionIds(contentEntity);
        }

        // 根据会话id列表、用户id，查询会话信息
        List<AlgorithmChatMessageEntity> messageEntityList = algorithmChatMessageRepository.queryBySessionIdList(messageEntity);
        if (CollUtil.isEmpty(messageEntityList)) {
            return;
        }

        // 删除消息sessionId
        for (AlgorithmChatMessageEntity entity : messageEntityList) {
            redisOperateRepository.delMessageSessionId(userId, entity.getApplicationId(), entity.getBusinessType());
        }
    }

    /**
     * 获取对话结果
     *
     * @Author: WeiJingKun
     */
    @MethodExecutionTimeLog("获取对话结果-serviceImpl")
    private ContentVO chatPollingUpdate(AlgorithmChatPollingUpdateDTO dto) {
        Long dialogueId = dto.getDialogueId();
        /** 获取并更新对话数据 */
        AlgorithmChatContentEntity entity = algorithmChatContentRepository
                .pollingUpdate(AlgorithmChatContentEntity.builder().userId(dto.getUserId()).id(dialogueId).build());
        if (null == entity) {
            return null;
        }
        // 设置对话状态，完成的发送AI助手对话完成mq
        setChatStatusAndSendMq(entity);

        // 创建公共VO
        ContentVO vo = createCommonVO(entity);

        // 针对异步文本会话，如果处理失败，需要调会员中心回滚接口将用户预扣的次数权益冲正回去
        rollbackBenefitTimes(dialogueId, vo);

        // 设置输入输出资源id和type
        setResourceIdAndType(vo, entity, EditResourceEnum.OUT.getCode());

        return vo;
    }

    @Override
    public ContentResultVO chatPollingUpdateV1(AlgorithmChatPollingUpdateDTO dto) {
        // 获取对话结果
        ContentVO contentVO = chatPollingUpdate(dto);
        if (null == contentVO) {
            log.warn("轮巡查结果V1-AlgorithmChatHistoryServiceImpl-chatPollingUpdateV1，contentVO is null");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_DIALOGUE_ID);
        }
        //V1轮询需要合并新对话的思维链
        SseEmitterDataUtils.mergeNewReasoningContent(contentVO);

        return contentAssembler.toContentResultVo(contentVO);
    }

    @Override
    public PollingUpdateV2VO chatPollingUpdateV2(AlgorithmChatPollingUpdateDTO dto) {
        // 获取对话结果
        ContentVO contentVO = chatPollingUpdate(dto);
        if (null == contentVO) {
            log.warn("轮巡查结果V2-AlgorithmChatHistoryServiceImpl-chatPollingUpdateV2，contentVO is null");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_DIALOGUE_ID);
        }
        //V2轮询需要合并新对话的思维链
        SseEmitterDataUtils.mergeNewReasoningContent(contentVO);
        
        // 获取对话结果推荐
        DialogueRecommendVO recommendVO = DialogueRecommendVO.builder().build();
        DialogueRecommendVO middleRecommendVO = DialogueRecommendVO.builder().build();
        String recommendInfo = contentVO.getRecommendInfo();
        String middleRecommendInfo = contentVO.getMiddleRecommendInfo();
        if (CharSequenceUtil.isNotBlank(recommendInfo)) {
            try {
                recommendVO = JSON.parseObject(recommendInfo, DialogueRecommendVO.class);
            } catch (JSONException e) {
                log.error("轮巡查结果V2-AlgorithmChatHistoryServiceImpl-chatPollingUpdateV2，recommendInfo JSON解析异常，outContent：{}", recommendInfo, e);
            } catch (Exception e) {
                log.error("轮巡查结果V2-AlgorithmChatHistoryServiceImpl-chatPollingUpdateV2，recommendInfo 异常，outContent：{}", recommendInfo, e);
            }
        }
        
        if (CharSequenceUtil.isNotBlank(middleRecommendInfo)) {
            try {
                middleRecommendVO = JSON.parseObject(middleRecommendInfo, DialogueRecommendVO.class);
            } catch (JSONException e) {
                log.error("轮巡查结果V2-AlgorithmChatHistoryServiceImpl-chatPollingUpdateV2，middleRecommendInfo JSON解析异常，outContent：{}", recommendInfo, e);
            } catch (Exception e) {
                log.error("轮巡查结果V2-AlgorithmChatHistoryServiceImpl-chatPollingUpdateV2，middleRecommendInfo 异常，outContent：{}", recommendInfo, e);
            }
        }

        return PollingUpdateV2VO.builder()
                .result(contentAssembler.toContentResultVo(contentVO))
                .recommend(recommendVO)
                .build();
    }
    
    @Override
    public PollingUpdateV3VO chatPollingUpdateV3(AlgorithmChatPollingUpdateDTO dto) {
        // 获取对话结果
        ContentVO contentVO = chatPollingUpdate(dto);
        if (null == contentVO) {
            log.warn("轮巡查结果V3-AlgorithmChatHistoryServiceImpl-chatPollingUpdateV3，contentVO is null");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_DIALOGUE_ID);
        }

        // 获取对话结果推荐
        DialogueRecommendVO recommendVO = DialogueRecommendVO.builder().build();
        String recommendInfo = contentVO.getRecommendInfo();
        if (CharSequenceUtil.isNotBlank(recommendInfo)) {
            try {
                recommendVO = JSON.parseObject(recommendInfo, DialogueRecommendVO.class);
            } catch (JSONException e) {
                log.error("轮巡查结果V3-AlgorithmChatHistoryServiceImpl-chatPollingUpdateV3，JSON解析异常，outContent：{}", recommendInfo, e);
            } catch (Exception e) {
                log.error("轮巡查结果V3-AlgorithmChatHistoryServiceImpl-chatPollingUpdateV3，异常，outContent：{}", recommendInfo, e);
            }
        }

        return PollingUpdateV3VO.builder()
                .result(contentAssembler.toContentResultV2Vo(contentVO))
                .recommend(recommendVO)
                .build();
    }

    @Override
    public void chatStop(AlgorithmChatStopDTO dto) {
        final AlgorithmChatContentEntity entity = algorithmChatContentRepository.getById(Long.parseLong(dto.getDialogueId()));
        if (entity == null) {
            log.info("对话不存在，对话id：{}", dto.getDialogueId());
            throw new YunAiBusinessException(BaseResultCodeEnum.ERROR_PARAMS);
        }
        if (!entity.getSessionId().equals(Long.valueOf(dto.getSessionId()))) {
            log.info("对话id和会话id不匹配，对话id：{}", dto.getDialogueId());
            throw new YunAiBusinessException(BaseResultCodeEnum.ERROR_PARAMS);
        }
        if (!dto.getUserId().equals(entity.getUserId())) {
            log.info("对话id与用户id不匹配，对话id：{}", dto.getDialogueId());
            throw new YunAiBusinessException(BaseResultCodeEnum.ERROR_PARAMS);
        }
        if (!dto.getSourceChannel().equals(entity.getSourceChannel())) {
            log.info("对话id与来源渠道不匹配，对话id：{}", dto.getDialogueId());
            throw new YunAiBusinessException(BaseResultCodeEnum.ERROR_PARAMS);
        }
        if (!ChatStatusEnum.isChatIn(entity.getChatStatus())) {
            log.info("对话状态不是对话中，对话状态：{}，对话id：{}", entity.getChatStatus(), entity.getId());
            return;
        }

        // 对话停止
        log.info("进行对话停止，对话id：{}", entity.getId());
        boolean result = algorithmChatContentRepository.updateChatStopById(entity.getId());
        log.info("对话停止成功：{}，对话id：{}", result, entity.getId());

        // 设置redis停止状态
        redisOperateRepository.setStopDialogue(entity.getId());
    }

    /**
     * 针对异步文本会话，如果处理失败，需要调会员中心回滚接口将用户预扣的次数权益冲正回去
     *
     * @Author: WeiJingKun
     */
    private void rollbackBenefitTimes(Long dialogueId, ContentVO vo) {
        // 初始化参数
        String taskId = vo.getTaskId();
        String toolsCommand = vo.getToolsCommand();
        Integer taskStatus = vo.getTaskStatus();
        String userId = vo.getUserId();

        // 【任务id不为空（异步任务），且文本意图】
        boolean hasTextIntentionTask = CharSequenceUtil.isNotBlank(taskId) && DialogueIntentionEnum.isTextIntention(toolsCommand);
        // 【任务状态不为null，且任务失败】
        boolean isTaskFail = taskStatus != null && TaskStatusEnum.isTaskFail(taskStatus);
        if(hasTextIntentionTask && isTaskFail){
            // 会员权益消费结果，消费失败（回滚权益）
            memberCenterService.consumeBenefitFail(userId, RequestContextHolder.getPhoneNumber(), dialogueId);
        }
    }

    /**
     * 构建获取对话内容VO公共数据
     *
     * @Author: WeiJingKun
     */
    @MethodExecutionTimeLog("构建获取对话内容VO公共数据-serviceImpl")
    private ContentVO createCommonVO(AlgorithmChatContentEntity entity) {
        ContentVO vo = contentAssembler.toContentVo(entity);
        vo.setDialogueId(String.valueOf(entity.getId()));
        vo.setSessionId(String.valueOf(entity.getSessionId()));

        // 判断并构造映射后的错误码
        algorithmChatContentDomain.judgeAndCreateResultCode(vo);

        // 输出内容outContent处理
        outContentHandle(vo);

        // 对话结果推荐处理
        DialogueRecommendVO recommendVO = algorithmChatContentDomain.dialogueRecommendHandle(vo);

        // 引导文案处理
        leadCopyHandle(vo, recommendVO);

        // 发邮件信息处理
        sendMailIntention(vo);

        return vo;
    }

    /**
     * 引导文案处理
     *
     * @Author: WeiJingKun
     */
    private void leadCopyHandle(ContentVO vo, DialogueRecommendVO recommendVO) {
        /** 初始化参数 */
        String toolsCommand = vo.getToolsCommand();
        String sourceChannel = vo.getSourceChannel();
        String inResourceId = vo.getInResourceId();
        String extInfo = vo.getExtInfo();
        String hbaseRespParameters = vo.getHbaseRespParameters();

        /** 引导文案处理 */
        // 根据工具指令、渠道来源，获取引导文案对象VO，处理extInfo，兼容历史版本
        LeadCopyVO leadCopyVo = getLeadCopyVO(vo, toolsCommand, sourceChannel, inResourceId, extInfo);

        // leadCopyVo（上面没有得到数据）、输出内容（hbase的resp），不为空
        if (ObjectUtil.isNull(leadCopyVo) && CharSequenceUtil.isNotBlank(hbaseRespParameters)) {
            try {
                /** 把hbase获取的outContent转：引导文案 */
                AiTextResultRespParameters respParameters = JSON.parseObject(hbaseRespParameters, AiTextResultRespParameters.class);
                leadCopyVo = respParameters.getLeadCopy();
                // 如果hbase有引导文案，则把对应的结果码和结果信息放入响应vo
                vo.setResultCode(respParameters.getResultCode());
                vo.setResultMsg(respParameters.getResultMsg());
            } catch (JSONException e) {
                // 图配文的结果为非规定AiTextResultRespParameters实体结构，不做处理
                log.warn("引导文案处理-AlgorithmChatHistoryServiceImpl-leadCopyHandle-2，JSON解析失败，hbaseRespParameters：{}，leadCopyVo：{}", hbaseRespParameters, leadCopyVo);
            } catch (Exception e) {
                log.error("引导文案处理-AlgorithmChatHistoryServiceImpl-leadCopyHandle-2，异常，hbaseRespParameters：{}，leadCopyVo：{}", hbaseRespParameters, leadCopyVo, e);
            }
        }

        // leadCopyVo（上面没有得到数据）、对话结果推荐，不为空
        if (ObjectUtil.isNull(leadCopyVo) && ObjectUtil.isNotNull(recommendVO)) {
            List<ContextRecommendVO> contextList = recommendVO.getContextList();
            /**
             * 上下文推荐列表为null（PS：这里只判断null），则为【旧】推荐数据
             */
            if (null == contextList) {
                // 需要将intentionList的功能迁移到leadCopy(type=5)
                List<IntentionRecommendVO> intentionList = recommendVO.getIntentionList();
                if (CollUtil.isNotEmpty(intentionList)) {
                    leadCopyVo = leadCopyService.getLeadCopy(intentionList.get(0));
                }
            }
        }

        // set引导文案
        vo.setLeadCopy(leadCopyVo);
    }

    /**
     * 根据工具指令、渠道来源，获取引导文案对象VO，处理extInfo，兼容历史版本
     * @Author: WeiJingKun
     */
    private LeadCopyVO getLeadCopyVO(ContentVO vo, String toolsCommand, String sourceChannel, String inResourceId, String extInfo) {
        LeadCopyVO leadCopyVo = vo.getLeadCopy();
        // 工具指令、渠道来源，不为空
        if (CharSequenceUtil.isAllNotBlank(toolsCommand, sourceChannel)) {
        	if(null == leadCopyVo) {
        		// 获取引导文案对象VO（输入资源ID为空）
        		leadCopyVo = leadCopyService.getLeadCopyVo(
                        DialogueIntentionVO.newMainIntention(toolsCommand), sourceChannel, inResourceId, vo.getInContent());
        	}

            // 处理extInfo  兼容历史版本 小天1.0 AI助手有用到做业务逻辑
            if (ObjectUtil.isNotNull(leadCopyVo)) {
                try {
                    if (CharSequenceUtil.isBlank(extInfo)) {
                        vo.setExtInfo(JSON.toJSONString(leadCopyVo));
                    } else {
                        JSONObject leadCopyVoJson = JSON.parseObject(JSON.toJSONString(leadCopyVo));
                        JSONObject extInfoJson = JSON.parseObject(extInfo);
                        extInfoJson.putAll(leadCopyVoJson.getInnerMap());
                        vo.setExtInfo(JSON.toJSONString(extInfoJson));
                    }
                } catch (JSONException e) {
                    log.error("引导文案处理-AlgorithmChatHistoryServiceImpl-leadCopyHandle-1，JSON解析异常，leadCopyVo：{}，extInfo：{}", leadCopyVo, extInfo, e);
                } catch (Exception e) {
                    log.error("引导文案处理-AlgorithmChatHistoryServiceImpl-leadCopyHandle-1，异常，leadCopyVo：{}，extInfo：{}", leadCopyVo, extInfo, e);
                }
            }
        }
        return leadCopyVo;
    }

    /**
     * 输出内容outContent处理
     *
     * @Author: WeiJingKun
     */
    private void outContentHandle(ContentVO vo) {
        // 初始化参数
        String hbaseRespParameters = vo.getHbaseRespParameters();
        String toolsCommand = vo.getToolsCommand();
        String taskId = vo.getTaskId();

        // 工具指令 和 输出内容（hbase的resp），不为空
        if (CharSequenceUtil.isNotBlank(toolsCommand)) {
            if (CharSequenceUtil.isNotBlank(hbaseRespParameters)) {
                // 【文生文】的输出内容outContent处理
                textIntention(vo, toolsCommand, hbaseRespParameters, taskId);

                // 【图片配文】的输出内容outContent处理
                pictureGenerateTextIntention(vo, toolsCommand, hbaseRespParameters);

                // 【搜索意图】的输出内容outContent处理
                searchIntention(vo, toolsCommand, hbaseRespParameters);

            }
            // 【图片智能鉴伪意图】的输出内容outContent处理
            smartFakeCheckIntention(vo, toolsCommand);
        }

    }

    /**
     * 【搜索意图】的输出内容outContent处理
     *
     * @Author: WeiJingKun
     */
    @MethodExecutionTimeLog("【搜索意图】的输出内容outContent处理-serviceImpl")
    private void searchIntention(ContentVO vo, String toolsCommand, String hbaseRespParameters) {
        // 是否为搜索意图
        if (DialogueIntentionEnum.isSearchIntention(toolsCommand)) {
            try {
                String inContent = vo.getInContent();
                String dialogueId = vo.getDialogueId();

                /** 把hbase获取的outContent转：搜索条件结果 */
                AiTextResultRespParameters respParameters = JSON.parseObject(hbaseRespParameters, AiTextResultRespParameters.class);
                SearchParam searchParam = respParameters.getParam();
                String resultCode = respParameters.getResultCode();
                String resultMsg = respParameters.getResultMsg();

                /** 获取搜索结果前，构建公共搜索参数 */
                SearchCommonParam searchCommonParam = createSearchCommonParam(vo, inContent);

                /** 获取搜索结果 */
                SearchResult searchResult = asyncSearchService.getSearchResult(searchParam, searchCommonParam);
				/** 实时搜索人物关系相册推荐 */
				ContentExtInfoVO extInfo = searchImageAlbumListService.searchImageSetAlbumList(vo.getSourceChannel(),
						VersionUtil.getVersionMap(vo.getExtInfo()), vo.getLeadCopy(), vo.getRecommend(), searchResult);
                if(null != extInfo) {
                	//设置扩展内容
                	vo.setLeadCopy(extInfo.getLeadCopy());
                	vo.setRecommend(extInfo.getRecommend());
                }
                List<SearchInfo> searchInfoList = searchResult.createSearchInfoList(respParameters.getIntentionInfoList(), searchResultProperties);

                /** 搜索成功，set搜索结果的标题 */
                searchIntentionSetTitle(vo, toolsCommand, searchInfoList, respParameters, dialogueId, inContent);

                vo.setSearchParam(searchParam);
                vo.setSearchResult(searchResult);
                vo.setSearchInfoList(searchInfoList);
                // 不涉及任务表，所以要构造数据
                vo.setResultCode(resultCode);
                vo.setResultMsg(resultMsg);
                if (ResultCodeEnum.SUCCESS.getResultCode().equals(resultCode)) {
                    vo.setTaskStatus(TaskStatusEnum.PROCESS_FINISH.getCode());
                } else {
                    vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
                }
            } catch (JSONException e) {
                vo.setResultCode(ResultCodeEnum.UNKNOWN_ERROR.getResultCode());
                vo.setResultMsg(hbaseRespParameters);
                vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
                log.error("【搜索意图】的输出内容outContent处理-AlgorithmChatHistoryServiceImpl-outContentHandle-searchIntention，JSON解析异常，hbaseRespParameters：{}", hbaseRespParameters, e);
            } catch (Exception e) {
                vo.setResultCode(ResultCodeEnum.UNKNOWN_ERROR.getResultCode());
                vo.setResultMsg(hbaseRespParameters);
                vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
                log.error("【搜索意图】的输出内容outContent处理-AlgorithmChatHistoryServiceImpl-outContentHandle-searchIntention，异常，hbaseRespParameters：{}", hbaseRespParameters, e);
            }
        }
    }

    /**
     * 搜索成功，set搜索结果的标题
     * @Author: WeiJingKun
     *
     * @param vo 输出内容vo
     * @param toolsCommand 工具指令
     * @param searchInfoList 搜索结果
     * @param respParameters hbase的resp
     * @param dialogueId 对话id
     * @param inContent 对话内容
     */
    private void searchIntentionSetTitle(ContentVO vo, String toolsCommand, List<SearchInfo> searchInfoList, AiTextResultRespParameters respParameters, String dialogueId, String inContent) {
        if(CollUtil.isNotEmpty(searchInfoList)){
            String title = respParameters.getTitle();
            /** set搜索结果的标题 */
            if(CharSequenceUtil.isNotBlank(title)){
                vo.setTitle(title);
            } else {
                Long dialogueIdLong = Long.valueOf(dialogueId);
                List<DialogueIntentionVO.IntentionInfo> intentionInfoList = respParameters.getIntentionInfoList();
                Future<String> searchReturnTermsFutureV1 = null;
                if(CollUtil.isNotEmpty(intentionInfoList)){
                    // 异步调用大模型生成搜索返回词V1
                    searchReturnTermsFutureV1 = searchReturnTermsService.getOptimizeReturnTermsFutureV1(dialogueIdLong, inContent);
                }
                // 获取搜索返回词V1（异常时返回默认值）
                vo.setTitle(searchReturnTermsService.getSearchReturnTermsV1(searchReturnTermsFutureV1, toolsCommand, dialogueIdLong, inContent));
            }
        }
    }

    /**
     * 获取搜索结果前，构建公共搜索参数
     * @Author: WeiJingKun
     *
     * @param vo 输出内容vo
     * @param inContent 对话内容
     * @return 公共搜索参数
     */
    private SearchCommonParam createSearchCommonParam(ContentVO vo, String inContent) {
        SearchCommonParam searchCommonParam = new SearchCommonParam();
        /** 处理：对话内容 */
        searchCommonParam.setDialogue(inContent);
        /** 处理：h5版本 */
        String extInfo = vo.getExtInfo();
        try {
            if (CharSequenceUtil.isNotBlank(extInfo)) {
                JSONObject extInfoJson = JSON.parseObject(extInfo);
                String h5Version = extInfoJson.getString("h5Version");
                if (CharSequenceUtil.isNotBlank(h5Version)) {
                    HeaderParams headerParams = RequestContextHolder.getHeaderParams();
                    if (null == headerParams) {
                        headerParams = new HeaderParams();
                    }
                    headerParams.setH5Version(h5Version);
                    // 获取extInfo中的h5Version放入线程
                    RequestContextHolder.setHeaderParams(headerParams);
                    // 公共参数添加h5Version
                    searchCommonParam.setH5Version(h5Version);
                }
            }
        } catch(JSONException e){
            log.error("【搜索意图】获取搜索结果前，处理h5版本-AlgorithmChatHistoryServiceImpl-setH5VersionToRequestContextHolder，JSON解析异常，extInfo：{}", extInfo, e);
        } catch(Exception e){
            log.error("【搜索意图】获取搜索结果前，处理h5版本-AlgorithmChatHistoryServiceImpl-setH5VersionToRequestContextHolder，异常，extInfo：{}", extInfo, e);
        }
        return searchCommonParam;
    }

    /**
     * 【图片配文意图】的输出内容outContent处理
     *
     * @Author: WeiJingKun
     */
    private static void pictureGenerateTextIntention (ContentVO vo, String toolsCommand, String hbaseRespParameters)
    {
        // 是否为图片配文意图
        if (DialogueIntentionEnum.PICTURE_GENERATE_TEXT.getCode().equals(toolsCommand)) {
            try {
                // hbaseRespParameters不是[开头，则不处理
                if (!hbaseRespParameters.startsWith(StrPool.BRACKET_START)) {
                	pictureGenerateTextIntentionByDB(vo, toolsCommand);
                    return;
                }
                List<String> outContentList = JSON.parseArray(hbaseRespParameters, String.class);
                // 列表转文本并且换行
                vo.setOutContent(CollUtil.join(outContentList, "\n"));
            } catch (JSONException e) {
                vo.setResultCode(ResultCodeEnum.UNKNOWN_ERROR.getResultCode());
                vo.setResultMsg(hbaseRespParameters);
                vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
                log.error("【图片配文意图】的输出内容outContent处理-AlgorithmChatHistoryServiceImpl-outContentHandle-pictureGenerateTextIntention，JSON解析异常，hbaseRespParameters：{}", hbaseRespParameters, e);
            } catch (Exception e) {
                vo.setResultCode(ResultCodeEnum.UNKNOWN_ERROR.getResultCode());
                vo.setResultMsg(hbaseRespParameters);
                vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
                log.error("【图片配文意图】的输出内容outContent处理-AlgorithmChatHistoryServiceImpl-outContentHandle-pictureGenerateTextIntention，异常，hbaseRespParameters：{}", hbaseRespParameters, e);
            }
        }
    }
    
    /**
     * 【图片配文意图】的输出内容outContent处理
     *
     * @Author: WeiJingKun
     */
    private static void pictureGenerateTextIntentionByDB (ContentVO vo, String toolsCommand)
    {
        // 是否为图片配文意图
        if (DialogueIntentionEnum.PICTURE_GENERATE_TEXT.getCode().equals(toolsCommand)) {
        	String respParam = null;
        	try {
        		respParam = vo.getRespParam();
                // respParam空，则不处理
                if (StringUtils.isEmpty(respParam)) {
                    return;
                }
                JSONArray outContentArray = JSON.parseArray(vo.getRespParam());
                if(CollUtil.isEmpty(outContentArray)) {
                    return;
                }
                List<String> outContentList = new ArrayList<>();
                for(Object outContent : outContentArray) {
                	if(outContent instanceof JSONObject) {
                		Object value = ((JSONObject)outContent).get("outContent");
                		if(value instanceof String && StringUtils.isNotEmpty(String.valueOf(value))) {
                			outContentList.add(String.valueOf(value));
                		}
                	}
                }
                // 列表转文本并且换行
                vo.setOutContent(CollUtil.join(outContentList, "\n"));
            } catch (JSONException e) {
                vo.setResultCode(ResultCodeEnum.UNKNOWN_ERROR.getResultCode());
                vo.setResultMsg(ResultCodeEnum.UNKNOWN_ERROR.getResultMsg());
                vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
                log.error("【图片配文意图】的输出内容outContent处理-AlgorithmChatHistoryServiceImpl-outContentHandle-pictureGenerateTextIntention，JSON解析异常，respParam：{}", respParam, e);
            } catch (Exception e) {
                vo.setResultCode(ResultCodeEnum.UNKNOWN_ERROR.getResultCode());
                vo.setResultMsg(ResultCodeEnum.UNKNOWN_ERROR.getResultMsg());
                vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
                log.error("【图片配文意图】的输出内容outContent处理-AlgorithmChatHistoryServiceImpl-outContentHandle-pictureGenerateTextIntention，异常，respParam：{}", respParam, e);
            }
        }
    }

    /**
     * 【文生文意图】的输出内容outContent处理
     *
     * @Author: WeiJingKun
     */
    private static void textIntention(ContentVO vo, String toolsCommand, String hbaseRespParameters, String taskId)
    {
        // 是否为文本意图
        if (DialogueIntentionEnum.isTextIntention(toolsCommand)) {
            // 响应的文本 //n转/n
            vo.setOutContent(hbaseRespParameters.replace("//n", "/n"));

            /** 任务id为空，即V2版本，流式同步结果 */
            if (CharSequenceUtil.isBlank(taskId)) {
                try {
                    /** 把hbase获取的outContent转：文生文-流式同步结果 */
                    AiTextResultRespParameters aiTextResultRespParameters = JSON.parseObject(hbaseRespParameters, AiTextResultRespParameters.class);
                    String resultCode = aiTextResultRespParameters.getResultCode();

                    // set文生文-流式同步结果
                    vo.setOutContent(aiTextResultRespParameters.getData());
                    // set文生文-大模型联网搜索结果
                    vo.setNetworkSearchInfoList(aiTextResultRespParameters.getNetworkSearchInfoList());
                    // set文生文-思维链过程
                    vo.setReasoningContent(aiTextResultRespParameters.getReasoningContent());

                    // 不涉及任务表，所以要构造数据
                    vo.setResultCode(resultCode);
                    vo.setResultMsg(aiTextResultRespParameters.getResultMsg());
                    vo.setTitle(aiTextResultRespParameters.getTitle());
                    vo.setPersonalKnowledgeFileList(aiTextResultRespParameters.getPersonalKnowledgeFileList());
                    if (ResultCodeEnum.SUCCESS.getResultCode().equals(resultCode)) {
                        vo.setTaskStatus(TaskStatusEnum.PROCESS_FINISH.getCode());
                    } else {
                        vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
                    }
                } catch (JSONException e) {
                    vo.setResultCode(ResultCodeEnum.UNKNOWN_ERROR.getResultCode());
                    vo.setResultMsg(hbaseRespParameters);
                    vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
                    log.error("【文生文意图】的输出内容outContent处理-AlgorithmChatHistoryServiceImpl-outContentHandle-textIntention，JSON解析异常，hbaseRespParameters：{}", hbaseRespParameters, e);
                } catch (Exception e) {
                    vo.setResultCode(ResultCodeEnum.UNKNOWN_ERROR.getResultCode());
                    vo.setResultMsg(hbaseRespParameters);
                    vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
                    log.error("【文生文意图】的输出内容outContent处理-AlgorithmChatHistoryServiceImpl-outContentHandle-textIntention，异常，hbaseRespParameters：{}", hbaseRespParameters, e);
                }
            }
        }
    }

    @Override
    public void updateOutResult (Long dialogueId, Integer outAuditStatus, Integer chatStatus, String msg){
        algorithmChatContentRepository.updateOutResult(dialogueId, outAuditStatus, chatStatus, msg, null);
        AlgorithmChatContentEntity entity = algorithmChatContentRepository.getById(dialogueId);
        //发送AI助手对话完成mq
        aiAssistantMqService.sendDialogueCompletedMq(entity);
    }

    @Override
    public void updateOutResult(Long dialogueId, Integer outAuditStatus, Integer chatStatus, String msg, String recommendInfo){
        algorithmChatContentRepository.updateOutResult(dialogueId, outAuditStatus, chatStatus, msg, recommendInfo);
        AlgorithmChatContentEntity entity = algorithmChatContentRepository.getById(dialogueId);
        //发送AI助手对话完成mq
        aiAssistantMqService.sendDialogueCompletedMq(entity);
    }

    @Override
    public void updateOutResult (Long dialogueId, OutAuditStatusEnum outAuditStatus, ChatStatusEnum
    chatStatus, String msg){
        updateOutResult(dialogueId, outAuditStatus.getCode(), chatStatus.getCode(), msg);
    }

    @Override
    public void updateOutResult(Long dialogueId, OutAuditStatusEnum outAuditStatus,
                                ChatStatusEnum chatStatus, String msg, String recommendInfo){
        updateOutResult(dialogueId, outAuditStatus.getCode(), chatStatus.getCode(), msg, recommendInfo);
    }

    @Override
    public void updateOutResultStop (Long dialogueId){
        algorithmChatContentRepository.updateOutResultStop(dialogueId);
        AlgorithmChatContentEntity entity = algorithmChatContentRepository.getById(dialogueId);
        //发送AI助手对话完成mq
        aiAssistantMqService.sendDialogueCompletedMq(entity);
    }

    /**
     * 设置对话状态，完成的发送AI助手对话完成mq
     *
     * @param entity 对话内容实体
     */
    private void setChatStatusAndSendMq(AlgorithmChatContentEntity entity){
        Integer taskStatus = entity.getTaskStatus();
        Integer chatStatus = entity.getChatStatus();

        if (null != taskStatus && ChatStatusEnum.isChatIn(chatStatus)) {
            log.info("开始设置对话状态，完成的发送AI助手对话完成mq id:{}", entity.getId());
            //非1-待处理；2-处理中状态，需要处理对话完成设置
            if (!TaskStatusEnum.isProcessing(taskStatus)) {
                if (TaskStatusEnum.PROCESS_FINISH.getCode().intValue() == taskStatus) {
                    //任务完成，设置对话完成
                    entity.setChatStatus(ChatStatusEnum.CHAT_SUCCESS.getCode());
                } else if (TaskStatusEnum.OVERDUE.getCode().intValue() == taskStatus) {
                    //任务过期，设置对话过期
                    entity.setChatStatus(ChatStatusEnum.CHAT_EXPIRE.getCode());
                } else {
                    //其他的，设置对话失败（加状态需要再做处理！！！）
                    entity.setChatStatus(ChatStatusEnum.CHAT_FAIL.getCode());
                }
                if (algorithmChatContentRepository.updateChatStatusForInStatus(entity)) {
                    //发送AI助手对话完成mq
                    aiAssistantMqService.sendDialogueCompletedMq(entity);
                } else {
                    log.warn("更新对话 updateChatStatusForInStatus fail... id:{}, chatStatus:{}",
                            entity.getId(), chatStatus);
                }
            }
        }
    }

    /**
     * 设置对话内容vo out 输出资源信息 outResourceId outResourceType
     *
     * @param contentVO     对话内容vo 修改输出则是本次对话信息 修改输入则是传入上一次对话信息
     * @param contentEntity 对话内容实体 修改输出则是本次对话信息 修改输入则是传入上一次对话信息
     * @param ioType        io编辑类型
     */
    @MethodExecutionTimeLog("设置输入输出资源id和type-serviceImpl")
    private void setResourceIdAndType(ContentVO contentVO, AlgorithmChatContentEntity contentEntity, Integer ioType){
        // 输出资源信息；（云盘文件ID/下载URL）不为空
        if (CharSequenceUtil.isNotEmpty(contentVO.getOutResourceId())
                // && 检查资源id是否正常能查到数据
                && checkOutResourceId(contentVO, ioType)) {
            return;
        }
        String eosUrl = handleEosUrl(contentEntity);
        if (CharSequenceUtil.isEmpty(eosUrl)) {
            //根据对话内容信息判断如果是图片资源id 资源输出类型默认设置为个人云文件id,outResourceId为文件id
            if (Objects.equals(EditResourceEnum.OUT.getCode(), ioType)) {
                contentVO.setOutResourceType(ImageTransmissionTypeEnum.YUN_DISK.getCode());
            }
            return;
        }
        //设置资源eos类型
        if (Objects.equals(EditResourceEnum.IN.getCode(), ioType)) {
            //是EOS类型：不修改resourceType，还是4，inResourceId=上一次对话id的eos url
            contentVO.setInResourceId(eosUrl);
        } else if (Objects.equals(EditResourceEnum.OUT.getCode(), ioType)) {
            contentVO.setOutResourceId(eosUrl);
            contentVO.setOutResourceType(ImageTransmissionTypeEnum.EOS.getCode());
        }
    }

    /**
     * 检查资源id是否正常能查到数据
     * @Author: WeiJingKun
     *
     * @param contentVO
     * @param ioType
     * @return boolean true-正常
     */
    private boolean checkOutResourceId(ContentVO contentVO, Integer ioType) {
        //资源id存在 判断原有id是否存在，不存在则使用资源id查询eos链接
        if (EditResourceEnum.OUT.getCode().equals(ioType)) {
            contentVO.setOutResourceType(ImageTransmissionTypeEnum.YUN_DISK.getCode());
        }
        YunDiskReqDTO yunDiskReqDTO = new YunDiskReqDTO();
        yunDiskReqDTO.setFileId(contentVO.getOutResourceId());
        try {
            YunDiskContentVO yunDiskContentVO = yunDiskService.getYunDiskContent(yunDiskReqDTO);
            if (yunDiskContentVO != null) {
                if (EditResourceEnum.IN.getCode().equals(ioType)) {
                    contentVO.setResourceType(ResourceTypeEnum.PICTURE.getType());
                    contentVO.setInResourceId(contentVO.getOutResourceId());
                }
                return true;
            }
        } catch (Exception e) {
            List<String> errorCodeList = Arrays.asList(YunDiskClient.FILE_NOT_FOUND_CODE_OSE,
                    YunDiskClient.FILE_NOT_FOUND_CODE_NEW,
                    YunDiskClient.FILE_DIR_NOT_FOUND_CODE_NEW
            );
            //云盘相关错误走eos，其他异常都返回云盘id与云盘存储类型，
            if (!(e instanceof YunAiBusinessException) ||
                    !errorCodeList.contains(((YunAiBusinessException) e).getCode())) {
                //获取不到则直接返回文件id与类型
                log.info("info-获取个人云文件错误,对话id:{},用户id:{},文件id:{}", contentVO.getDialogueId(),
                        contentVO.getUserId(), contentVO.getOutResourceId(), e);
                if (EditResourceEnum.IN.getCode().equals(ioType)) {
                    contentVO.setResourceType(ResourceTypeEnum.PICTURE.getType());
                    contentVO.setInResourceId(contentVO.getOutResourceId());
                }
                return true;
            }
            log.warn("info-获取个人云文件警告,对话id:{},用户id:{},文件id:{}", contentVO.getDialogueId(),
                    contentVO.getUserId(), contentVO.getOutResourceId(), e);
        }
        return false;
    }

    /***
     * 会话历史列表返回构造输入资源信息 resourceType,inResourceId,outResourceType,outResourceId
     * @param contentVO 历史会话信息
     * contentList接口修改
     * 1，resourceType=4（ResourceTypeEnum.DIALOGUE.getType()）的情况，inResourceId就是上一次对话id；
     * 2，查询到这个对话响应输出是否云盘类型；
     * ①是云盘类型：修改resourceType=3（ResourceTypeEnum.PICTURE.getType()），inResourceId=上一次对话id的outResourceId
     * ②否，但是做了转存，设置云盘类型：修改resourceType=3（ResourceTypeEnum.PICTURE.getType()），inResourceId=上一次对话id的outResourceId
     * ③否，是EOS类型：不修改resourceType，还是4，inResourceId=上一次对话id的eos url
     */
    @MethodExecutionTimeLog("会话历史列表返回构造输入资源信息-serviceImpl")
    private void handleInResourceIdAndType (ContentVO contentVO){
        try {
            //判断本次对话类型为对话，inResourceId为上一次对话id
            if (contentVO.getResourceType().equals(ResourceTypeEnum.DIALOGUE.getType())) {
                //不存在则按云盘eos相关逻辑处理返回
                AlgorithmChatContentEntity lastContentEntity = algorithmChatContentRepository.getById(Long.valueOf(contentVO.getInResourceId()));
                if (lastContentEntity == null) {
                    //如果对话内容找不到，设置输入资源id为空
                    contentVO.setInResourceId("");
                    return;
                }
                TaskAiAbilityEntity taskEntity = taskAiAbilityRepository.getTaskEntity(lastContentEntity.getTaskId());
                //查询任务表的相应参数
                if (null == taskEntity || CharSequenceUtil.isEmpty(taskEntity.getRespParam())) {
                    log.info("任务不存在,dialogueId:{},taskId:{}", lastContentEntity.getId(), lastContentEntity.getTaskId());
                    contentVO.setInResourceId("");
                    return;
                }
                Optional<TaskRespParamVO> optional = JSONUtil.toList(taskEntity.getRespParam(), TaskRespParamVO.class)
                        .stream().findFirst();
                if (!optional.isPresent()) {
                    log.info("TaskRespParamVO 转换不存在,dialogueId:{},taskId:{}", lastContentEntity.getId(), lastContentEntity.getTaskId());
                    contentVO.setInResourceId("");
                    return;
                }
                TaskRespParamVO taskRespParamVO = optional.get();
                //根据对话结果来判断图片类型taskRespParamVO
                log.info("对话内容结果对象:{}", JSONUtil.toJsonStr(taskRespParamVO));
                if (taskRespParamVO.isYunDiskImageResp()) {
                    //查询到这个对话响应输出是否云盘类型 ①是云盘类型：修改resourceType=3 图片，inResourceId=上一次对话id的outResourceId
                    contentVO.setInResourceId(lastContentEntity.getOutResourceId());
                    contentVO.setResourceType(ResourceTypeEnum.PICTURE.getType());
                } else {
                    ContentVO lastContentVO = new ContentVO();
                    lastContentVO.setOutResourceId(lastContentEntity.getOutResourceId());
                    lastContentVO.setUserId(lastContentEntity.getUserId());
                    lastContentVO.setDialogueId(String.valueOf(lastContentEntity.getId()));
                    lastContentEntity.setRespParam(taskEntity.getRespParam());
                    //执行输入资源信息设置 resourceType inResourceId
                    setResourceIdAndType(lastContentVO, lastContentEntity, EditResourceEnum.IN.getCode());
                    //填充返回结果
                    if (null != lastContentVO.getResourceType()) {
                        contentVO.setResourceType(lastContentVO.getResourceType());
                    }
                    contentVO.setInResourceId(lastContentVO.getInResourceId());
                }
            }
        } catch (Exception e) {
            log.error("设置输出资源类型异常,contentVO:{}", contentVO, e);
        }
    }

    /***
     * 处理eosUrl
     * @param contentEntity 对话内容实体
     */
    private String handleEosUrl (AlgorithmChatContentEntity contentEntity){
        //不存在则按云盘eos相关逻辑处理返回
        if (CharSequenceUtil.isEmpty(contentEntity.getRespParam())) {
            return null;
        }
        Optional<TaskRespParamVO> optional = JSONUtil.toList(contentEntity.getRespParam(), TaskRespParamVO.class)
                .stream().findFirst();
        if (!optional.isPresent()) {
            return null;
        }
        TaskRespParamVO taskRespParamVO = optional.get();
        //根据对话结果来判断图片类型taskRespParamVO
        log.info("对话内容结果对象:{}", JSONUtil.toJsonStr(taskRespParamVO));
        if (!taskRespParamVO.getOutResourceType().equals(ResourceTypeEnum.PICTURE.getType())) {
            return null;
        }
        if (taskRespParamVO.isEosImageResp()) {
            //获取eos连接，暂定失效时间24h,输出资源为eos图片时 将outResourceId设置为图片url
            TaskAiAbilityEntity taskEntity = taskAiAbilityRepository.getTaskEntity(contentEntity.getTaskId());
            if (null == taskEntity) {
                log.info("任务不存在,taskId:{}", contentEntity.getTaskId());
                throw new YunAiBusinessException(ResultCodeEnum.TASK_RECORD_NOT_FOUND);
            }
            String fileSuffix = ImageSuffixEnum.getByAlgorithmCode(taskEntity.getAlgorithmCode()).getCode();
            String eosUrl = eosExternalService.getFileUrl(taskRespParamVO.getOutResourceId(),
                    taskRespParamVO.getOutResourceId() + StrPool.DOT + fileSuffix,
                    eosFileExpireConfig.getExpireTime());
            log.info("对话id:{},获取到eosUrl:{}", contentEntity.getId(), eosUrl);
            return eosUrl;
        }
        return null;
    }

    /**
     * 发邮件信息处理
     *
     * @param vo 对话内容VO
     */
    private static void sendMailIntention(ContentVO vo) {
        // 不是发邮件意图，直接返回
        String toolsCommand = vo.getToolsCommand();
        if (!DialogueIntentionEnum.isSendMail(toolsCommand)) {
            return;
        }

        String hbaseResp = vo.getHbaseRespParameters();
        try {
            // 把hbase获取的outContent转：发邮件意图结果
            AiTextResultRespParameters respParameters = JSON.parseObject(hbaseResp, AiTextResultRespParameters.class);
            String resultCode = respParameters.getResultCode();

            // 不涉及任务表，所以要构造数据
            vo.setResultCode(resultCode);
            vo.setResultMsg(respParameters.getResultMsg());

            // 邮件信息
            vo.setMailInfo(respParameters.getMailInfo());
            vo.setTitle(respParameters.getTitle());
            if (ResultCodeEnum.SUCCESS.getResultCode().equals(resultCode)) {
                vo.setTaskStatus(TaskStatusEnum.PROCESS_FINISH.getCode());
            } else {
                vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
            }
        } catch (JSONException e) {
            vo.setResultCode(ResultCodeEnum.UNKNOWN_ERROR.getResultCode());
            vo.setResultMsg(hbaseResp);
            vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
            log.error("【发邮件意图】的输出内容处理-AlgorithmChatHistoryServiceImpl-outContentHandle-sendMailIntention，JSON解析异常，hbaseResp：{}", hbaseResp, e);
        } catch (Exception e) {
            vo.setResultCode(ResultCodeEnum.UNKNOWN_ERROR.getResultCode());
            vo.setResultMsg(hbaseResp);
            vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
            log.error("【发邮件意图】的输出内容处理-AlgorithmChatHistoryServiceImpl-outContentHandle-sendMailIntention，异常，hbaseResp：{}", hbaseResp, e);
        }
    }

    /**
     * 【图片智能鉴伪意图】的输出内容处理
     */
    private static void smartFakeCheckIntention(ContentVO vo, String toolsCommand) {
        if (DialogueIntentionEnum.SMART_FAKE_CHECK.getCode().equals(toolsCommand)) {
            String respParam = vo.getRespParam();
            if (CharSequenceUtil.isNotEmpty(respParam)) {
                try {
                    // mapping
                    List<TaskRespParamVO> respParamList = JSON.parseArray(respParam, TaskRespParamVO.class);
                    // 图片智能鉴伪初版仅有一个结果,因此只提取一个值,构造函数已封装
                    vo.setOutContent(JSONUtil.toJsonStr(new SmartFakeCheckVO().initialVersionCreate(respParamList)));
                } catch (Exception e) {
                    log.warn("smartFakeCheckIntention | 图片智能鉴伪outContent处理异常", e);
                }
            }
        }
    }
}
