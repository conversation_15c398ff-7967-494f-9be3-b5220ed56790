package com.zyhl.yun.api.outer.application.service.task;

import com.zyhl.yun.api.outer.application.dto.TaskCommentDTO;
import com.zyhl.yun.api.outer.application.dto.TaskCommentQueryDTO;
import com.zyhl.yun.api.outer.application.dto.TaskCommentRetDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 任务评论服务接口
 * @date 2025/4/21 16:31
 */
public interface AlgorithmTaskCommentService {

    void addTaskComment(TaskCommentDTO dto);

    TaskCommentRetDTO getTaskComment(TaskCommentQueryDTO dto);
}