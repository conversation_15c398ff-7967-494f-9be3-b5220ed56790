package com.zyhl.yun.api.outer.application.vo.knowledge;

import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileBatchImportReqDTO;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.vo.ImportHtmlInfoVO;
import com.zyhl.yun.api.outer.domain.vo.ImportMailInfoVO;
import com.zyhl.yun.api.outer.domain.vo.ImportNoteInfoVO;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 个人知识库导入任务
 *
 * <AUTHOR>
 */
@Data
public class PersonalKnowledgeImportTaskVO implements Serializable {
    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 知识库ID
     */
    private String baseId;

    /**
     * 资源类型描述
     * 0--个人云文件
     * 1--邮件
     * 2--笔记
     * 3--在线链接
     */
    private Integer resourceType;

    /**
     * 任务状态描述
     * -1--处理失败
     * 0--处理中
     * 1--处理成功
     */
    private Integer status;

    /**
     * 个人云文件
     */
    private File file;

    /**
     * 邮件信息
     */
    private ImportMailInfoVO mail;

    /**
     * 笔记信息
     */
    private ImportNoteInfoVO note;

    /**
     * 网页链接信息
     */
    private ImportHtmlInfoVO htmlInfo;

    /**
     * 创建时间，RFC 3339 格式
     * 例如：2019-08-20T06:51:27.292+08:00
     */
    private String createdAt;

    /**
     * 更新时间，RFC 3339 格式
     * 例如：2019-08-20T06:51:27.292+08:00
     */
    private String updatedAt;

    /**
     * 错误描述，失败时返回
     */
    private String errorMessage;

    /**
     * 进度，0-100
     */
    private Integer  progress;

    private String errorCode;
} 