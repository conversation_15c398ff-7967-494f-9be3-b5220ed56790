package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import com.google.common.base.Objects;

import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.utils.NumberUtil;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatContentService;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatMessageService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.service.external.MemberCenterService;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.AiTextResultVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 描述：数据保存服务
 *
 * <AUTHOR> zhumaoxian  2025/4/13 9:44
 */
@Slf4j
@Service
public class DataSaveServiceImpl implements DataSaveService {


    @Resource
    protected ChatMessageService chatMessageService;
    @Resource
    protected ChatContentService chatContentService;
    @Resource
    protected MemberCenterService memberCenterService;
    @Resource
    protected AlgorithmChatContentRepository algorithmChatContentRepository;
    @Resource
    protected AiTextResultRepository aiTextResultRepository;
    @Resource
    protected SourceChannelsProperties sourceChannelsProperties;
    @Resource
    private RedissonClient redissonClient;

    /**
     * 保存到hbase
     */
    @Override
    public AiTextResultEntity saveTextResult(ChatAddHandleDTO handleDTO, String answer, String resourceContent) {
        return saveTextResult(handleDTO, "", answer, resourceContent);
    }

    @Override
    public AiTextResultEntity saveTextResult(ChatAddHandleDTO handleDTO, String taskId, String answer, String resourceContent) {
        // 保存结果
        AiTextResultEntity entity = handleDTO.getAiTextResultEntity(taskId, resourceContent, answer);
        aiTextResultRepository.save(entity);

        return entity;
    }

    @Override
    public void updateResult(String userId, String dialogueId, DialogueFlowResult dialogueFlowResult) {
        final RLock lock = redissonClient.getLock(String.format(RedisConstants.UPDATE_HBASE_DIALOGUE_LOCK, dialogueId));
        try {
            AiTextResultEntity aiTextResult = aiTextResultRepository.getByRowKey(userId, NumberUtil.getLongValue(dialogueId));
            if (null != aiTextResult) {
                AiTextResultRespParameters respBean = JSONUtil.toBean(aiTextResult.getRespParameters(),
                        AiTextResultRespParameters.class);
                if (null != respBean) {
                    List<DialogueFlowResult> newOuts = new ArrayList<>();
                    if (CollUtil.isNotEmpty(respBean.getOutputList())) {
                        Optional<DialogueFlowResult> optional = respBean.getOutputList().stream().filter(out
                                -> Objects.equal(out.getIndex(), dialogueFlowResult.getIndex())).findFirst();
                        if (optional.isPresent()) {
                            //修改旧的对象
                            respBean.getOutputList().removeIf(out
                                    -> Objects.equal(out.getIndex(), dialogueFlowResult.getIndex()));
                        }
                        // 添加新的对象
                        newOuts.addAll(respBean.getOutputList());
                        newOuts.add(dialogueFlowResult);
                    }
                    respBean.setOutputList(newOuts);
                    String respParameters = JSONUtil.toJsonStr(respBean);
                    aiTextResultRepository.updateRespParameters(userId, NumberUtil.getLongValue(dialogueId), respParameters);
                }
            }
        } catch (Exception e) {
            log.error("【更新hbase】获取锁异常：{}", e.getMessage(), e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    /**
     * 保存hbase-所有对话结果（入参和结果）
     *
     * @param handleDTO 用户输入对象
     * @param resp      hbase的resp数据对象
     * @Author: WeiJingKun
     */
    @Override
    public void saveHbaseAllChatResult(ChatAddHandleDTO handleDTO, AiTextResultRespParameters resp) {
        String userId = handleDTO.getReqDTO().getUserId();
        if (null == resp.getVersion()) {
            //版本号空，默认v2
            resp.setVersion(AiTextResultVersionEnum.V2.getVersion());
        }
        // 保存结果
        AiTextResultEntity entity = new AiTextResultEntity();
        entity.setUserId(userId);
        entity.setDialogueId(handleDTO.getDialogueId());
        entity.setTaskId("");
        entity.setReqParameters(JSONUtil.toJsonStr(handleDTO.getInputInfoDTO()));
        entity.setAttachment("");
        entity.setRespParameters(JsonUtil.toJson(resp));
        aiTextResultRepository.save(entity);

    }

    /**
     * 新增数据到数据库
     */
    @Override
    public void add(ChatAddHandleDTO handleDTO, ChatStatusEnum chatStatus) {
        add(handleDTO, chatStatus, null, "");
    }

    @Override
    public void add(ChatAddHandleDTO handleDTO, ChatStatusEnum chatStatus, Long taskId, String modelCode) {
        // 先保存会话表
        Long sessionId = chatMessageService.save(handleDTO);
        handleDTO.setSessionId(sessionId);

        // 再保存对话表
        chatContentService.add(handleDTO, chatStatus.getCode(), taskId, modelCode);
    }

    /**
     * 新增成功对话
     */
    @Override
    public void addSuccess(ChatAddHandleDTO handleDTO, OutContentTypeEnum outContentType) {
        addSuccessAndModelCode(handleDTO, "", outContentType);
    }

    /**
     * 新增成功对话，并且更新
     */
    @Override
    public void addSuccessAndModelCode(ChatAddHandleDTO handleDTO, String modelCode, OutContentTypeEnum outContentType) {

        // 智能鉴伪需要outContentResource=3特殊处理
        if (DialogueIntentionEnum.SMART_FAKE_CHECK.getCode().equals(handleDTO.getIntentionCode())) {
            outContentType = OutContentTypeEnum.JSON_OBJECT;
        }

        // 先保存会话表
        Long sessionId = chatMessageService.save(handleDTO);
        handleDTO.setSessionId(sessionId);

        // 再保存对话表
        chatContentService.addSuccess(handleDTO, modelCode, outContentType.getType());
    }
    
    /**
     * 新增失败对话，并且更新
     */
    @Override
    public void addFailAndModelCode(ChatAddHandleDTO handleDTO, String modelCode, OutContentTypeEnum outContentType) {

        // 智能鉴伪需要outContentResource=3特殊处理
        if (DialogueIntentionEnum.SMART_FAKE_CHECK.getCode().equals(handleDTO.getIntentionCode())) {
            outContentType = OutContentTypeEnum.JSON_OBJECT;
        }

        // 先保存会话表
        Long sessionId = chatMessageService.save(handleDTO);
        handleDTO.setSessionId(sessionId);

        // 再保存对话表
        chatContentService.addFail(handleDTO, modelCode, outContentType.getType());
    }

    @Override
    public void saveTidbAllChatResult(ChatAddHandleDTO handleDTO) {
        /**
         * 先保存会话表 1、handleDTO.asyncSaveMessage： true，则新增数据 2、handleDTO.asyncSaveMessage：
         * false，则更新数据
         */
        chatMessageService.saveAll(handleDTO);

        // 再保存对话表
        chatContentService.saveAll(handleDTO);
    }

    @Override
    public AiTextResultEntity getHbaseResult(String userId, String dialogueId) {
        return aiTextResultRepository.getByRowKey(userId, dialogueId);
    }

	@Override
	public void updateChatFail(Long dialogueId) {
		chatContentService.updateChatFail(dialogueId);
	}

}
