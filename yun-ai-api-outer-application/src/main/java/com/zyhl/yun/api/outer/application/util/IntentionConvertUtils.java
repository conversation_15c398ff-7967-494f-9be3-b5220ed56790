package com.zyhl.yun.api.outer.application.util;

import cn.hutool.core.collection.CollUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.config.MailaiSearchProperties;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;
import com.zyhl.yun.api.outer.vo.IntentEntityVO;
import com.zyhl.yun.api.outer.vo.KeyValueVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 意图转换工具类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class IntentionConvertUtils {


    @Resource
    private MailaiSearchProperties mailaiSearchProperties;

    /**
     * 转换意图
     * 1、如果意图识别是018-综合搜索意图，则移除其他意图，需要将实体转换成本渠道要展示的搜索意图作为综合搜索的子意图，另外子意图需要过滤掉038、012、018、999
     * 2、如果意图是999-泛化意图，其他意图丢弃，当enableAiSearch=true，先转成018综合搜索意图，再进行步骤1的转换，当enableAiSearch=flase将意图转换成000-普通对话意图
     * 3、如果意图识别到有本渠道要展示的搜索意图，则过滤掉其他意图，只留下本渠道要展示的搜索意图，如果剩下的意图有多个，还需要过滤去除038-知识库意图,如果还有多个意图，需要把这些意图作为018综合搜索的子意图
     * 4、如果意图识别没有本渠道要展示的搜索意图，需要分情况处理
     * 4-1如果意图中包含有搜索意图，过滤掉非搜索意图,将意图转换成综合搜索（由于可能有多个意图，只取第一个搜索意图进行转换，其他意图丢弃），再进行步骤1的转换
     * 4-2如果意图中不包含搜索意图,且意图都是本渠道不支持的，如果是enableAiSearch=false,则取第一个意图将意图转换成000-普通对话意图，其他意图丢弃，如果是enableAiSearch=true，则转换为018综合搜索，并拿用户输入作为子意图实体
     * 4-3如果意图中不包含搜索意图,且意图中有本渠道支持的，如果是enableAiSearch=false，则过滤掉不支持的意图，只剩下支持的意图，如果是enableAiSearch=true，则转换为018综合搜索，并拿用户输入作为子意图实体
     *
     * @param intentionVO     意图对象
     * @param sourceChannel   渠道
     * @param businessType    业务类型
     * @param enableAiSearch  是否开启AI搜索
     * @param userInput       用户输入内容
     * @return 转换后的意图对象
     */
    public DialogueIntentionVO convertIntention(DialogueIntentionVO intentionVO, String sourceChannel,
                                               String businessType, Boolean enableAiSearch, String userInput) {
        if (intentionVO == null || CollUtil.isEmpty(intentionVO.getIntentionInfoList())) {
            return intentionVO;
        }

        log.info("意图转换开始，原始意图：{}", JsonUtil.toJson(intentionVO));

        // 过滤掉metaDataList中value为空或空字符串的元素
        filterEmptyMetaDataValues(intentionVO);

        // 获取配置的搜索意图列表
        List<String> searchIntention = getConfigSearchIntention(sourceChannel);

        // 获取所有意图编码
        List<String> intentionCodes = intentionVO.getIntentionInfoList().stream()
                .map(IntentionInfo::getIntention)
                .collect(Collectors.toList());

        // 获取所有二级子意图编码
        List<String> subIntentionCodes = intentionVO.getIntentionInfoList().stream()
                .map(IntentionInfo::getSubIntention)
                .collect(Collectors.toList());

        // 获取综合搜索意图编码
        String comprehensiveSearchIntention = DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode();
        // 获取泛化意图编码
        String otherIntention = DialogueIntentionEnum.OTHER.getCode();
        // 获取知识库搜索意图编码
        String knowledgeSearchIntention = DialogueIntentionEnum.SEARCH_KNOWLEDGE_BASE_RESOURCE.getCode();
        // 获取普通对话意图编码
        String textGenerateTextIntention = DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode();

        // 获取主意图编码
        String mainIntentionCode = DialogueIntentionVO.getMainIntentionCode(intentionVO);

        // 0.处理工具意图的子意图支持情况
        DialogueIntentionVO textToolResult = handleTextToolIntention(intentionVO, mainIntentionCode, subIntentionCodes,
                businessType, enableAiSearch, searchIntention, comprehensiveSearchIntention, userInput);
        if (textToolResult != null) {
            return textToolResult;
        }

        // 1. 如果意图识别是018-综合搜索意图，则移除其他意图，需要将实体转换成本渠道要展示的搜索意图作为综合搜索的子意图，另外子意图需要过滤掉038、012、018、999
        if (comprehensiveSearchIntention.equals(mainIntentionCode)) {
            // 过滤掉非综合搜索意图
            filterToSingleIntention(intentionVO, comprehensiveSearchIntention);
            // 综合搜索意图，创建子意图
            transformIntention(intentionVO, searchIntention);
            log.info("意图是综合搜索意图，创建子意图并过滤后：{}", JsonUtil.toJson(intentionVO));
            return intentionVO;
        }

        // 2. 如果意图是999-泛化意图，其他意图丢弃，当enableAiSearch=true，先转成018综合搜索意图，再进行步骤1的转换，当enableAiSearch=false将意图转换成000-普通对话意图
        if (otherIntention.equals(mainIntentionCode)) {
            // 过滤掉非泛化意图
            filterToSingleIntention(intentionVO, otherIntention);

            if (Boolean.TRUE.equals(enableAiSearch)) {
                // 转换为综合搜索意图（内部会创建子意图）
                convertToComprehensiveSearch(intentionVO, searchIntention, comprehensiveSearchIntention);
                log.info("意图是泛化意图，enableAiSearch=true，转换为综合搜索意图并创建子意图后：{}", JsonUtil.toJson(intentionVO));
            } else {
                // 转换为普通对话意图
                convertToTextGenerateText(intentionVO);
                log.info("意图是泛化意图，enableAiSearch=false，转换为普通对话意图后：{}", JsonUtil.toJson(intentionVO));
            }
            return intentionVO;
        }

        // 3. 如果意图识别到有本渠道要展示的搜索意图，则过滤掉其他意图，只留下本渠道要展示的搜索意图，如果剩下的意图有多个，还需要过滤去除038-知识库意图,如果剩下意图还有多个，需要转换为综合搜索意图：将每个搜索意图都作为综合搜索的子意图
        boolean hasChannelSearchIntention = intentionCodes.stream().anyMatch(searchIntention::contains);
        if (hasChannelSearchIntention) {
            // 过滤掉非本渠道要展示的搜索意图
            filterNonChannelSearchIntentions(intentionVO, searchIntention);

            // 如果剩下的意图有多个，还需要过滤去除038-知识库意图
            if (intentionVO.getIntentionInfoList().size() > 1) {
                filterKnowledgeSearchIntention(intentionVO, knowledgeSearchIntention);
            }
            //如果剩下意图还有多个，需要转换为综合搜索意图：将每个搜索意图都作为综合搜索的子意图
            if (intentionVO.getIntentionInfoList().size() > 1) {
                convertMultipleSearchToComprehensive(intentionVO, comprehensiveSearchIntention);
            }

            log.info("意图中有本渠道要展示的搜索意图，过滤后：{}", JsonUtil.toJson(intentionVO));
            return intentionVO;
        }

        // 4. 如果意图识别没有本渠道要展示的搜索意图，需要分情况处理
        // 获取所有搜索意图列表
        List<String> allSearchIntentions = getAllSearchIntentions();
        boolean hasAnySearchIntention = intentionCodes.stream().anyMatch(allSearchIntentions::contains);

        // 4.1 如果意图中包含有搜索意图，过滤掉非搜索意图,将意图转换成综合搜索（由于可能有多个意图，只取第一个搜索意图进行转换，其他意图丢弃），再进行步骤1的转换
        if (hasAnySearchIntention) {
            // 过滤掉非搜索意图
            filterNonSearchIntentions(intentionVO, allSearchIntentions);
            // 只保留第一个搜索意图
            keepOnlyFirstIntention(intentionVO);
            // 转换为综合搜索意图（内部会创建子意图）
            convertToComprehensiveSearch(intentionVO, searchIntention, comprehensiveSearchIntention);
            log.info("意图中包含搜索意图但非本渠道展示的搜索意图，转换为综合搜索意图并创建子意图后：{}", JsonUtil.toJson(intentionVO));
            return intentionVO;
        }

        // 判断意图是否都是本渠道不支持的
        boolean allIntentionsNotSupported = true;
        for (String intentionCode : intentionCodes) {
            if (!DialogueIntentionEnum.isMailNotSupportIntention(businessType, intentionCode)) {
                allIntentionsNotSupported = false;
                break;
            }
        }


        // 4.2 如果意图中不包含搜索意图,且意图都是本渠道不支持的，如果是enableAiSearch=false,则取第一个意图将意图转换成000-普通对话意图，其他意图丢弃，如果是enableAiSearch=true，则转换为018综合搜索，并拿用户输入作为子意图实体
        if (allIntentionsNotSupported) {
            // 只保留第一个意图
            keepOnlyFirstIntention(intentionVO);

            if (Boolean.FALSE.equals(enableAiSearch)) {
                // 转换为普通对话意图
                convertToTextGenerateText(intentionVO);
                log.info("意图中不包含搜索意图且都是本渠道不支持的，enableAiSearch=false，转换为普通对话意图后：{}", JsonUtil.toJson(intentionVO));
            } else {
                // 转换为综合搜索意图，并拿用户输入作为子意图实体
                convertToComprehensiveSearchWithUserInput(intentionVO, searchIntention, comprehensiveSearchIntention, userInput);
                log.info("意图中不包含搜索意图且都是本渠道不支持的，enableAiSearch=true，转换为综合搜索意图并拿用户输入作为子意图实体后：{}", JsonUtil.toJson(intentionVO));
            }
            return intentionVO;
        }

        // 4.3 如果意图中不包含搜索意图,且意图中有本渠道支持的，如果是enableAiSearch=false，则过滤掉不支持的意图，只剩下支持的意图，如果是enableAiSearch=true，则转换为018综合搜索，并拿用户输入作为子意图实体
        if (Boolean.FALSE.equals(enableAiSearch)) {
            // 过滤掉不支持的意图，只剩下支持的意图
            filterNotSupportedIntentions(intentionVO, businessType);
            log.info("意图中不包含搜索意图且有本渠道支持的，enableAiSearch=false，过滤掉不支持的意图后：{}", JsonUtil.toJson(intentionVO));
        } else {
            // 转换为综合搜索意图，并拿用户输入作为子意图实体
            keepOnlyFirstIntention(intentionVO);
            convertToComprehensiveSearchWithUserInput(intentionVO, searchIntention, comprehensiveSearchIntention, userInput);
            log.info("意图中不包含搜索意图且有本渠道支持的，enableAiSearch=true，转换为综合搜索意图并拿用户输入作为子意图实体后：{}", JsonUtil.toJson(intentionVO));
        }

        return intentionVO;
    }

    /**
     * 转换意图（重载方法，保持向后兼容性）
     *
     * @param intentionVO     意图对象
     * @param sourceChannel   渠道
     * @param businessType    业务类型
     * @param enableAiSearch  是否开启AI搜索
     * @return 转换后的意图对象
     */
    public DialogueIntentionVO convertIntention(DialogueIntentionVO intentionVO, String sourceChannel,
                                               String businessType, Boolean enableAiSearch) {
        return convertIntention(intentionVO, sourceChannel, businessType, enableAiSearch, "");
    }

    /**
     * 获取配置的搜索意图列表
     *
     * @param sourceChannel 来源渠道
     * @return 搜索意图列表
     */
    private List<String> getConfigSearchIntention(String sourceChannel) {
        List<String> searchIntention = new ArrayList<>();
        Map<String, List<String>> searchSourceChannelIntention = mailaiSearchProperties.getSearchSourceChannelIntention();

        // 先根据sourceChannel获取特定渠道的搜索意图
        if (CollUtil.isNotEmpty(searchSourceChannelIntention) && StringUtils.isNotBlank(sourceChannel)) {
            searchIntention = searchSourceChannelIntention.get(sourceChannel);
        }

        // 如果仍然没有获取到，则使用全局搜索意图配置
        if (CollUtil.isEmpty(searchIntention)) {
            searchIntention = mailaiSearchProperties.getSearchIntention();
        }

        return searchIntention;
    }

    /**
     * 获取所有搜索意图列表（包括盘内搜索意图、站内搜索意图、知识库搜索意图、综合搜索意图）
     */
    private List<String> getAllSearchIntentions() {
        // 直接从配置文件中读取所有搜索意图列表
        List<String> allSearchIntentions = mailaiSearchProperties.getAllSearchIntentions();
        log.info("从配置文件中未读取到所有搜索意图列表，使用原来的方式获取，结果：{}", allSearchIntentions);
        return allSearchIntentions;
    }





    /**
     * 过滤掉非本渠道要展示的搜索意图
     */
    private void filterNonChannelSearchIntentions(DialogueIntentionVO intentionVO, List<String> channelSearchIntentions) {
        if (CollUtil.isEmpty(intentionVO.getIntentionInfoList()) || CollUtil.isEmpty(channelSearchIntentions)) {
            return;
        }

        List<IntentionInfo> filteredList = intentionVO.getIntentionInfoList().stream()
                .filter(info -> channelSearchIntentions.contains(info.getIntention()))
                .collect(Collectors.toList());

        intentionVO.setIntentionInfoList(filteredList);
    }

    /**
     * 过滤掉知识库搜索意图
     */
    private void filterKnowledgeSearchIntention(DialogueIntentionVO intentionVO, String knowledgeSearchIntention) {
        if (CollUtil.isEmpty(intentionVO.getIntentionInfoList()) || StringUtils.isBlank(knowledgeSearchIntention)) {
            return;
        }

        List<IntentionInfo> filteredList = intentionVO.getIntentionInfoList().stream()
                .filter(info -> !knowledgeSearchIntention.equals(info.getIntention()))
                .collect(Collectors.toList());

        intentionVO.setIntentionInfoList(filteredList);
    }

    /**
     * 过滤掉非搜索意图
     */
    private void filterNonSearchIntentions(DialogueIntentionVO intentionVO, List<String> searchIntentions) {
        if (CollUtil.isEmpty(intentionVO.getIntentionInfoList()) || CollUtil.isEmpty(searchIntentions)) {
            return;
        }

        List<IntentionInfo> filteredList = intentionVO.getIntentionInfoList().stream()
                .filter(info -> searchIntentions.contains(info.getIntention()))
                .collect(Collectors.toList());

        intentionVO.setIntentionInfoList(filteredList);
    }

    /**
     * 只保留第一个意图
     */
    private void keepOnlyFirstIntention(DialogueIntentionVO intentionVO) {
        if (CollUtil.isEmpty(intentionVO.getIntentionInfoList()) || intentionVO.getIntentionInfoList().size() <= 1) {
            return;
        }

        IntentionInfo firstIntention = intentionVO.getIntentionInfoList().get(0);
        List<IntentionInfo> newList = new ArrayList<>();
        newList.add(firstIntention);

        intentionVO.setIntentionInfoList(newList);
    }



    /**
     * 过滤到只剩下指定意图
     */
    private void filterToSingleIntention(DialogueIntentionVO intentionVO, String intentionCode) {
        if (CollUtil.isEmpty(intentionVO.getIntentionInfoList()) || StringUtils.isBlank(intentionCode)) {
            return;
        }

        List<IntentionInfo> filteredList = intentionVO.getIntentionInfoList().stream()
                .filter(info -> intentionCode.equals(info.getIntention()))
                .collect(Collectors.toList());
        intentionVO.setIntentionInfoList(filteredList);
    }



    /**
     * 将多个搜索意图转换为综合搜索意图，将每个搜索意图都作为综合搜索的子意图
     *
     * @param intentionVO 意图对象
     * @param comprehensiveSearchIntention 综合搜索意图编码
     */
    private void convertMultipleSearchToComprehensive(DialogueIntentionVO intentionVO, String comprehensiveSearchIntention) {
        if (intentionVO == null || CollUtil.isEmpty(intentionVO.getIntentionInfoList()) || intentionVO.getIntentionInfoList().size() <= 1) {
            return;
        }

        // 记录转换前的意图
        String originalIntentions = JsonUtil.toJson(intentionVO.getIntentionInfoList());

        // 获取当前所有意图作为子意图
        List<IntentionInfo> currentIntentions = new ArrayList<>(intentionVO.getIntentionInfoList());

        // 创建综合搜索意图
        IntentionInfo comprehensiveIntention = new IntentionInfo();
        comprehensiveIntention.setIntention(comprehensiveSearchIntention);
        comprehensiveIntention.setScore(1.0);

        // 合并所有意图的metaDataList
        List<KeyValueVO> mergedMetaDataList = new ArrayList<>();
        for (IntentionInfo intention : currentIntentions) {
            if (CollUtil.isNotEmpty(intention.getEntityList())) {
                for (IntentEntityVO entity : intention.getEntityList()) {
                    if (CollUtil.isNotEmpty(entity.getMetaDataList())) {
                        mergedMetaDataList.addAll(entity.getMetaDataList());
                    }
                }
            }
        }

        // 创建新的实体对象，设置合并后的metaDataList
        IntentEntityVO newEntity = new IntentEntityVO();
        newEntity.setMetaDataList(mergedMetaDataList);

        // 从第一个意图的第一个实体获取其他字段
        if (CollUtil.isNotEmpty(currentIntentions) && CollUtil.isNotEmpty(currentIntentions.get(0).getEntityList())) {
            try {
                IntentEntityVO firstEntity = currentIntentions.get(0).getEntityList().get(0);
                // 复制除metaDataList以外的所有字段
                copyNonNullFields(firstEntity, newEntity);
                // 重新设置合并后的metaDataList
                newEntity.setMetaDataList(mergedMetaDataList);
            } catch (Exception e) {
                log.error("复制意图实体字段时发生异常", e);
            }
        }

        // 设置实体列表到综合搜索意图
        List<IntentEntityVO> newEntityList = new ArrayList<>();
        newEntityList.add(newEntity);
        comprehensiveIntention.setEntityList(newEntityList);

        // 将原有的搜索意图转换为子意图
        List<IntentionInfo> subIntentions = new ArrayList<>();
        for (IntentionInfo intention : currentIntentions) {
            IntentionInfo subIntention = new IntentionInfo();
            subIntention.setIntention(intention.getIntention());
            subIntention.setScore(intention.getScore() != null ? intention.getScore() : 0.0);
            subIntention.setEntityList(intention.getEntityList());
            subIntentions.add(subIntention);
        }

        // 设置子意图列表
        comprehensiveIntention.setSubIntentions(subIntentions);

        // 替换原有意图列表
        List<IntentionInfo> newIntentionList = new ArrayList<>();
        newIntentionList.add(comprehensiveIntention);
        intentionVO.setIntentionInfoList(newIntentionList);

        log.info("多个搜索意图转换为综合搜索意图 - 转换前: {}, 转换后: {}", originalIntentions, JsonUtil.toJson(intentionVO.getIntentionInfoList()));
    }

    /**
     * 转换为综合搜索意图，并拿用户输入作为子意图实体
     *
     * @param intentionVO 意图对象
     * @param searchIntention 搜索意图列表
     * @param comprehensiveSearchIntention 综合搜索意图编码
     * @param userInput 用户输入内容
     */
    private void convertToComprehensiveSearchWithUserInput(DialogueIntentionVO intentionVO, List<String> searchIntention, String comprehensiveSearchIntention, String userInput) {
        if (intentionVO == null || CollUtil.isEmpty(intentionVO.getIntentionInfoList())) {
            return;
        }

        // 记录转换前的意图
        String originalIntentions = JsonUtil.toJson(intentionVO.getIntentionInfoList());

        // 创建综合搜索意图
        IntentionInfo comprehensiveIntention = new IntentionInfo();
        comprehensiveIntention.setIntention(comprehensiveSearchIntention);
        comprehensiveIntention.setScore(1.0);

        // 创建新的实体对象，使用用户输入作为关键词
        IntentEntityVO newEntity = createEntityWithUserInput(userInput);

        // 设置实体列表到综合搜索意图
        List<IntentEntityVO> newEntityList = new ArrayList<>();
        newEntityList.add(newEntity);
        comprehensiveIntention.setEntityList(newEntityList);

        // 替换原有意图列表
        List<IntentionInfo> newIntentionList = new ArrayList<>();
        newIntentionList.add(comprehensiveIntention);
        intentionVO.setIntentionInfoList(newIntentionList);

        // 转换完成后，创建子意图，使用用户输入作为关键词
        transformIntentionWithUserInput(intentionVO, searchIntention, userInput);

        log.info("意图转换为综合搜索并拿用户输入作为子意图实体 - 转换前: {}, 转换后: {}", originalIntentions, JsonUtil.toJson(intentionVO.getIntentionInfoList()));
    }

    /**
     * 转换为综合搜索意图
     *
     * @param intentionVO 意图对象
     * @param searchIntention 搜索意图列表
     * @param comprehensiveSearchIntention 综合搜索意图编码
     */
    private void convertToComprehensiveSearch(DialogueIntentionVO intentionVO, List<String> searchIntention, String comprehensiveSearchIntention) {
        if (intentionVO == null || CollUtil.isEmpty(intentionVO.getIntentionInfoList())) {
            return;
        }

        // 记录转换前的意图
        String originalIntentions = JsonUtil.toJson(intentionVO.getIntentionInfoList());

        // 创建综合搜索意图
        IntentionInfo comprehensiveIntention = new IntentionInfo();
        comprehensiveIntention.setIntention(comprehensiveSearchIntention);
        comprehensiveIntention.setScore(1.0);

        // 创建新的实体对象，设置合并后的metaDataList
        IntentEntityVO newEntity = new IntentEntityVO();

        // 从intentionInfoList的第一个元素获取其他字段
        if (CollUtil.isNotEmpty(intentionVO.getIntentionInfoList()) && CollUtil.isNotEmpty(intentionVO.getIntentionInfoList().get(0).getEntityList())) {
            try {
                IntentEntityVO firstEntity = intentionVO.getIntentionInfoList().get(0).getEntityList().get(0);
                // 复制除metaDataList以外的所有字段
                copyNonNullFields(firstEntity, newEntity);
            } catch (Exception e) {
                log.error("复制意图实体字段时发生异常", e);
            }
        }

        // 设置实体列表到综合搜索意图
        List<IntentEntityVO> newEntityList = new ArrayList<>();
        newEntityList.add(newEntity);
        comprehensiveIntention.setEntityList(newEntityList);

        // 替换原有意图列表
        List<IntentionInfo> newIntentionList = new ArrayList<>();
        newIntentionList.add(comprehensiveIntention);
        intentionVO.setIntentionInfoList(newIntentionList);

        // 转换完成后，创建子意图
        transformIntention(intentionVO, searchIntention);

        log.info("意图转换为综合搜索 - 转换前: {}, 转换后: {}", originalIntentions, JsonUtil.toJson(intentionVO.getIntentionInfoList()));
    }




    /**
     * 从实体列表中提取关键词
     */
    private List<String> extractKeywords(List<IntentEntityVO> entityList) {
        if (CollUtil.isEmpty(entityList)) {
            return new ArrayList<>();
        }

        List<String> keywords = new ArrayList<>();

        for (IntentEntityVO entity : entityList) {
            // 提取元数据
            if (!CollUtil.isEmpty(entity.getMetaDataList())) {
                for (KeyValueVO keyValue : entity.getMetaDataList()) {
                    if (!CollUtil.isEmpty(keyValue.getValue())) {
                        keywords.addAll(keyValue.getValue());
                    }
                }
            }
        }

        return keywords.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 如果是综合搜索，但没有子意图，需要提取metaDataList,将其转换成多个资源搜的子意图
     * 012-搜图片，013-搜文档，014-搜视频，015-搜音频，016-搜文件夹，017-搜笔记，028-搜邮件，搜邮件附件
     * 下条件搜索全部资源：
     * 1、邮件主题、正文
     * 2、邮件附件标题
     * 3、云盘文档标题、正文
     * 4、云盘图片内容
     * 5、笔记标题、内容
     */
    private void transformIntention(DialogueIntentionVO intentionVO, List<String> searchIntention) {
        // 判断是否为空或者没有意图信息
        if (Objects.isNull(intentionVO) || CollUtil.isEmpty(intentionVO.getIntentionInfoList())) {
            return;
        }

        // 获取主意图
        IntentionInfo mainIntention = intentionVO.getIntentionInfoList().get(0);

        // 判断是否为综合搜索意图
        if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode().equals(mainIntention.getIntention())) {
            // 获取实体列表
            List<IntentEntityVO> entityList = mainIntention.getEntityList();
            if (CollUtil.isEmpty(entityList)) {
                return;
            }

            // 从实体列表中提取关键词
            List<String> keywords = extractKeywords(entityList);

            // 创建子意图列表
            List<IntentionInfo> subIntentions = new ArrayList<>();

            // 添加搜索子意图
            addSearchSubIntention(subIntentions, keywords, entityList, searchIntention);

            // 过滤掉不需要的子意图（过滤掉038、012、018、999）
            if (CollUtil.isNotEmpty(subIntentions)) {
                List<String> filterIntentions = mailaiSearchProperties.getExcludeComprehensiveSearchIntentions();

                subIntentions = subIntentions.stream()
                        .filter(info -> !filterIntentions.contains(info.getIntention()))
                        .collect(Collectors.toList());
            }

            // 设置子意图列表
            mainIntention.setSubIntentions(subIntentions);
        }
    }



    /**
     * 添加搜索子意图
     */
    private void addSearchSubIntention(List<IntentionInfo> subIntentions, List<String> keywords, List<IntentEntityVO> entityList, List<String> searchIntention) {
        if (CollUtil.isEmpty(entityList)) {
            return;
        }

        IntentEntityVO entity = entityList.get(0);

        // 为每个搜索意图创建子意图
        for (String intentionCode : searchIntention) {
            // 创建子意图
            IntentionInfo subIntention = new IntentionInfo();
            subIntention.setIntention(intentionCode);
            subIntention.setScore(0.0);

            // 创建新的实体
            IntentEntityVO newEntity = new IntentEntityVO();
            copyNonNullFields(entity, newEntity);

            // 设置关键词
            if (!CollUtil.isEmpty(keywords)) {
                newEntity.setImageNameList(newEntity.getImageNameList()!= null ? newEntity.getImageNameList() : keywords);
                newEntity.setTitleList(newEntity.getTitleList()!= null ? newEntity.getTitleList() : keywords);
                newEntity.setAttachmentList(newEntity.getAttachmentList()!= null ? newEntity.getAttachmentList() : keywords);
            }

            // 添加实体到子意图
            List<IntentEntityVO> newEntityList = new ArrayList<>();
            newEntityList.add(newEntity);
            subIntention.setEntityList(newEntityList);

            // 添加子意图
            subIntentions.add(subIntention);
        }
    }

    /**
     * 转换为文生文意图
     */
    private void convertToTextGenerateText(DialogueIntentionVO intentionVO) {
        IntentionInfo textGenerateTextIntention = new IntentionInfo();
        textGenerateTextIntention.setIntention(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());
        textGenerateTextIntention.setScore(1.0);

        // 保留原有实体信息
        if (CollUtil.isNotEmpty(intentionVO.getIntentionInfoList()) &&
            CollUtil.isNotEmpty(intentionVO.getIntentionInfoList().get(0).getEntityList())) {
            textGenerateTextIntention.setEntityList(intentionVO.getIntentionInfoList().get(0).getEntityList());
        }

        List<IntentionInfo> newIntentionList = new ArrayList<>();
        newIntentionList.add(textGenerateTextIntention);
        intentionVO.setIntentionInfoList(newIntentionList);
    }

    /**
     * 过滤掉不支持的意图
     */
    private void filterNotSupportedIntentions(DialogueIntentionVO intentionVO, String businessType) {
        if (CollUtil.isEmpty(intentionVO.getIntentionInfoList()) || StringUtils.isBlank(businessType)) {
            return;
        }

        List<IntentionInfo> filteredList = intentionVO.getIntentionInfoList().stream()
                .filter(info -> !DialogueIntentionEnum.isMailNotSupportIntention(businessType, info.getIntention()))
                .collect(Collectors.toList());
        intentionVO.setIntentionInfoList(filteredList);
    }

    /**
     * 过滤掉不支持的子意图
     */
    private void filterNotSupportedSubIntentions(DialogueIntentionVO intentionVO, String businessType) {
        if (CollUtil.isEmpty(intentionVO.getIntentionInfoList()) || StringUtils.isBlank(businessType)) {
            return;
        }

        for (IntentionInfo intentionInfo : intentionVO.getIntentionInfoList()) {
            // 如果是工具意图，检查子意图是否支持
            if (DialogueIntentionEnum.TEXT_TOOL.getCode().equals(intentionInfo.getIntention())) {
                String subIntentionCode = intentionInfo.getSubIntention();
                if (StringUtils.isNotBlank(subIntentionCode) &&
                    DialogueIntentionEnum.isMailNotSupportSubIntention(businessType, subIntentionCode)) {
                    // 如果子意图不支持，清空子意图
                    intentionInfo.setSubIntention(null);
                }
            }
        }

        // 过滤掉没有子意图的工具意图
        List<IntentionInfo> filteredList = intentionVO.getIntentionInfoList().stream()
                .filter(info -> {
                    if (DialogueIntentionEnum.TEXT_TOOL.getCode().equals(info.getIntention())) {
                        return StringUtils.isNotBlank(info.getSubIntention());
                    }
                    return true;
                })
                .collect(Collectors.toList());
        intentionVO.setIntentionInfoList(filteredList);
    }

    /**
     * 复制非空字段
     */
    private void copyNonNullFields(IntentEntityVO source, IntentEntityVO target) {
        if (source == null || target == null) {
            return;
        }

        Optional.ofNullable(source.getTimeList()).ifPresent(target::setTimeList);
        Optional.ofNullable(source.getPlaceList()).ifPresent(target::setPlaceList);
        Optional.ofNullable(source.getLabelList()).ifPresent(target::setLabelList);
        Optional.ofNullable(source.getImageNameList()).ifPresent(target::setImageNameList);
        Optional.ofNullable(source.getSuffixList()).ifPresent(target::setSuffixList);
        Optional.ofNullable(source.getRecipientList()).ifPresent(target::setRecipientList);
        Optional.ofNullable(source.getSenderList()).ifPresent(target::setSenderList);
        Optional.ofNullable(source.getTitleList()).ifPresent(target::setTitleList);
        Optional.ofNullable(source.getAttachmentList()).ifPresent(target::setAttachmentList);
        Optional.ofNullable(source.getContentList()).ifPresent(target::setContentList);
        Optional.ofNullable(source.getEmailAddressList()).ifPresent(target::setEmailAddressList);
        Optional.ofNullable(source.getStatusList()).ifPresent(target::setStatusList);
        Optional.ofNullable(source.getTypeList()).ifPresent(target::setTypeList);
        Optional.ofNullable(source.getMetaDataList()).ifPresent(target::setMetaDataList);
    }

    /**
     * 使用用户输入创建实体对象
     *
     * @param userInput 用户输入内容
     * @return 实体对象
     */
    private IntentEntityVO createEntityWithUserInput(String userInput) {
        IntentEntityVO entity = new IntentEntityVO();

        if (StringUtils.isNotBlank(userInput)) {
            List<String> userInputList = Collections.singletonList(userInput);

            // 创建metaDataList，将用户输入作为关键词
            List<KeyValueVO> metaDataList = new ArrayList<>();
            KeyValueVO keyValue = new KeyValueVO();
            keyValue.setKey("1");
            keyValue.setValue(userInputList);
            metaDataList.add(keyValue);

            entity.setMetaDataList(metaDataList);
            entity.setTitleList(userInputList);
            entity.setContentList(userInputList);
            entity.setImageNameList(userInputList);
            entity.setAttachmentList(userInputList);
        }

        return entity;
    }

    /**
     * 使用用户输入转换意图，创建子意图
     *
     * @param intentionVO 意图对象
     * @param searchIntention 搜索意图列表
     * @param userInput 用户输入内容
     */
    private void transformIntentionWithUserInput(DialogueIntentionVO intentionVO, List<String> searchIntention, String userInput) {
        // 判断是否为空或者没有意图信息
        if (Objects.isNull(intentionVO) || CollUtil.isEmpty(intentionVO.getIntentionInfoList())) {
            return;
        }

        // 获取主意图
        IntentionInfo mainIntention = intentionVO.getIntentionInfoList().get(0);

        // 判断是否为综合搜索意图
        if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode().equals(mainIntention.getIntention())) {
            // 创建子意图列表
            List<IntentionInfo> subIntentions = new ArrayList<>();

            // 使用用户输入作为关键词添加搜索子意图
            addSearchSubIntentionWithUserInput(subIntentions, userInput, searchIntention);

            // 过滤掉不需要的子意图（过滤掉038、012、018、999）
            if (CollUtil.isNotEmpty(subIntentions)) {
                List<String> filterIntentions = mailaiSearchProperties.getExcludeComprehensiveSearchIntentions();

                subIntentions = subIntentions.stream()
                        .filter(info -> !filterIntentions.contains(info.getIntention()))
                        .collect(Collectors.toList());
            }

            // 设置子意图列表
            mainIntention.setSubIntentions(subIntentions);
        }
    }

    /**
     * 使用用户输入添加搜索子意图
     *
     * @param subIntentions 子意图列表
     * @param userInput 用户输入内容
     * @param searchIntention 搜索意图列表
     */
    private void addSearchSubIntentionWithUserInput(List<IntentionInfo> subIntentions, String userInput, List<String> searchIntention) {
        if (StringUtils.isBlank(userInput) || CollUtil.isEmpty(searchIntention)) {
            return;
        }

        List<String> userInputList = Collections.singletonList(userInput);

        // 为每个搜索意图创建子意图
        for (String intentionCode : searchIntention) {
            // 创建子意图
            IntentionInfo subIntention = new IntentionInfo();
            subIntention.setIntention(intentionCode);
            subIntention.setScore(0.0);

            // 创建新的实体，使用用户输入作为关键词
            IntentEntityVO newEntity = createEntityWithUserInput(userInput);

            // 添加实体到子意图
            List<IntentEntityVO> newEntityList = new ArrayList<>();
            newEntityList.add(newEntity);
            subIntention.setEntityList(newEntityList);

            // 添加子意图
            subIntentions.add(subIntention);
        }
    }

    /**
     * 过滤掉metaDataList中value为空或空字符串的元素
     *
     * @param intentionVO 意图对象
     */
    private void filterEmptyMetaDataValues(DialogueIntentionVO intentionVO) {
        if (intentionVO == null || CollUtil.isEmpty(intentionVO.getIntentionInfoList())) {
            return;
        }

        for (IntentionInfo intentionInfo : intentionVO.getIntentionInfoList()) {
            if (CollUtil.isEmpty(intentionInfo.getEntityList())) {
                continue;
            }

            for (IntentEntityVO entity : intentionInfo.getEntityList()) {
                if (CollUtil.isEmpty(entity.getMetaDataList())) {
                    continue;
                }

                // 过滤metaDataList中的空值元素
                List<KeyValueVO> filteredMetaDataList = entity.getMetaDataList().stream()
                        .filter(keyValue -> keyValue != null && CollUtil.isNotEmpty(keyValue.getValue()))
                        .map(keyValue -> {
                            // 过滤value列表中的空字符串
                            List<String> filteredValues = keyValue.getValue().stream()
                                    .filter(StringUtils::isNotBlank)
                                    .collect(Collectors.toList());

                            // 如果过滤后的value列表不为空，则保留该KeyValueVO
                            if (CollUtil.isNotEmpty(filteredValues)) {
                                KeyValueVO filteredKeyValue = new KeyValueVO();
                                filteredKeyValue.setKey(keyValue.getKey());
                                filteredKeyValue.setValue(filteredValues);
                                return filteredKeyValue;
                            }
                            return null;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                // 更新实体的metaDataList
                entity.setMetaDataList(filteredMetaDataList);
            }
        }

        log.info("过滤metaDataList空值后的意图：{}", JsonUtil.toJson(intentionVO));
    }

    /**
     * 处理工具意图的子意图支持情况
     * 如果主意图是036-工具意图，则判断二级子意图是否是邮箱支持的，如果有部分支持，则过滤掉不支持的，
     * 如果二级子意图都是邮箱不支持，如果是enableAiSearch=false，则将意图转换成000-普通对话意图，
     * 如果是enableAiSearch=true，则转换为018综合搜索，并拿用户输入作为子意图实体
     *
     * @param intentionVO 意图对象
     * @param mainIntentionCode 主意图编码
     * @param subIntentionCodes 子意图编码列表
     * @param businessType 业务类型
     * @param enableAiSearch 是否开启AI搜索
     * @param searchIntention 搜索意图列表
     * @param comprehensiveSearchIntention 综合搜索意图编码
     * @param userInput 用户输入内容
     * @return 处理后的意图对象，如果不需要处理则返回null
     */
    private DialogueIntentionVO handleTextToolIntention(DialogueIntentionVO intentionVO, String mainIntentionCode,
                                                       List<String> subIntentionCodes, String businessType,
                                                       Boolean enableAiSearch, List<String> searchIntention,
                                                       String comprehensiveSearchIntention, String userInput) {
        // 如果主意图不是工具意图，直接返回null
        if (!DialogueIntentionEnum.TEXT_TOOL.getCode().equals(mainIntentionCode)) {
            return null;
        }

        // 如果没有子意图，直接返回null
        if (CollUtil.isEmpty(subIntentionCodes)) {
            return null;
        }

        // 统计支持和不支持的子意图
        List<String> supportedSubIntentions = new ArrayList<>();
        List<String> notSupportedSubIntentions = new ArrayList<>();

        for (String subIntentionCode : subIntentionCodes) {
            if (DialogueIntentionEnum.isMailNotSupportSubIntention(businessType, subIntentionCode)) {
                notSupportedSubIntentions.add(subIntentionCode);
            } else {
                supportedSubIntentions.add(subIntentionCode);
            }
        }

        // 如果有部分支持，则过滤掉不支持的
        if (CollUtil.isNotEmpty(supportedSubIntentions)) {
            // 过滤掉不支持的子意图，只保留支持的
            filterNotSupportedSubIntentions(intentionVO, businessType);
            log.info("工具意图有部分子意图支持，过滤掉不支持的子意图后：{}", JsonUtil.toJson(intentionVO));
            return intentionVO;
        }

        // 如果二级子意图都是邮箱不支持的
        if (CollUtil.isNotEmpty(notSupportedSubIntentions) && CollUtil.isEmpty(supportedSubIntentions)) {
            // 只保留第一个意图
            keepOnlyFirstIntention(intentionVO);

            if (Boolean.FALSE.equals(enableAiSearch)) {
                // 转换为普通对话意图
                convertToTextGenerateText(intentionVO);
                log.info("工具意图的二级子意图都不支持，enableAiSearch=false，转换为普通对话意图后：{}", JsonUtil.toJson(intentionVO));
            } else {
                // 转换为综合搜索意图，并拿用户输入作为子意图实体
                convertToComprehensiveSearchWithUserInput(intentionVO, searchIntention, comprehensiveSearchIntention, userInput);
                log.info("工具意图的二级子意图都不支持，enableAiSearch=true，转换为综合搜索意图并拿用户输入作为子意图实体后：{}", JsonUtil.toJson(intentionVO));
            }
            return intentionVO;
        }

        // 如果没有匹配的情况，返回null表示不需要特殊处理
        return null;
    }
}
