package com.zyhl.yun.api.outer.application.chatv2.service.impl.openapi;

import java.util.Collections;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.service.openapi.OpenapiLingxiCommonService;
import com.zyhl.yun.api.outer.application.chatv2.service.openapi.OpenapiLingxiMailEditService;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO.OpenApiLingxiCardLink;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO.OpenApiLingxiCardReplyMessage;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO.OpenApiLingxiCardReplyMessage.CardReplyItem;
import com.zyhl.yun.api.outer.application.config.ApplicationAgentLingxiConfig;
import com.zyhl.yun.api.outer.external.service.UserEtnService;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.application.chatv2.service.impl.openapi.OpenapiLingxiMailEditServiceImpl}
 * <br>
 * <b> description:</b> 能体对话-邮件编辑-服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-25 11:42
 **/
@Slf4j
@Service
public class OpenapiLingxiMailEditServiceImpl implements OpenapiLingxiMailEditService {

	@Resource
	private OpenapiLingxiCommonService openapiLingxiCommonService;
	@Resource
	private UserEtnService userEtnService;
	@Resource
	private ApplicationAgentLingxiConfig applicationAgentLingxiConfig;

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		IntentionInfo mainIntention = DialogueIntentionVO.getMainIntention(handleDTO.getIntentionVO());
		String editMailDialogueId = openapiLingxiCommonService
				.getDialogueIdByIntention(handleDTO.getReqDTO().getUserId(), handleDTO.getSessionId(), mainIntention);
		if (StringUtils.isEmpty(editMailDialogueId)) {
			log.warn("编辑邮件对话id为空，继续对话-文生文");
			return true;
		}
		log.info("编辑邮件对话id:{}", editMailDialogueId);

		// 卡片跳转地址
		String paramCardButtonUrl = openapiLingxiCommonService.getMailEditJumpUrl(RequestContextHolder.getToken(),
				editMailDialogueId);

		// 前面无大大模型输出，先发一个大模型回复
		String editMailModelText = applicationAgentLingxiConfig.getTextConfig().getEditMailModelText();
		handleDTO.getSseEmitterOperate()
				.send(OpenApiLingxiChatRespVO.getOpenApiLingxiChatTextModelResp(handleDTO, editMailModelText));

		// 结束之前，追加2个消息（卡片【编辑邮件】、追尾【会议要开始了？试试会议录音】）
		String bubbleDesc = applicationAgentLingxiConfig.getTextConfig().getBubbleDesc();
		String editMailColor = applicationAgentLingxiConfig.getTextConfig().getEditMailColor();
		String editMailImage = applicationAgentLingxiConfig.getTextConfig().getEditMailImage();
		String editMailTitle = applicationAgentLingxiConfig.getTextConfig().getEditMailTitle();
		String editMailSubtitle = applicationAgentLingxiConfig.getTextConfig().getEditMailSubtitle();
		String editMailButtonName = applicationAgentLingxiConfig.getTextConfig().getEditMailButtonName();
		String editMailButtonColor = applicationAgentLingxiConfig.getTextConfig().getEditMailButtonColor();
		String editMailButtonImage = applicationAgentLingxiConfig.getTextConfig().getEditMailButtonImage();
		String voiceNoteTitle = applicationAgentLingxiConfig.getTextConfig().getVoiceNoteTitle();
		String voiceNoteReplyText = applicationAgentLingxiConfig.getTextConfig().getVoiceNoteReplyText();

		OpenApiLingxiChatRespVO.WebLink webLink = new OpenApiLingxiChatRespVO.WebLink(paramCardButtonUrl);
		OpenApiLingxiCardReplyMessage replyMessage = new OpenApiLingxiCardReplyMessage(
				Collections
						.singletonList(new CardReplyItem(editMailColor, editMailImage, editMailTitle, editMailSubtitle,
								new OpenApiLingxiCardLink(webLink, editMailButtonColor, editMailButtonImage,
										editMailButtonName))),
				bubbleDesc, Collections.singletonList(new OpenApiLingxiCardLink(voiceNoteTitle,
						new OpenApiLingxiCardLink.ReplyLink(voiceNoteReplyText))));
		handleDTO.getSseEmitterOperate()
				.sendAndComplete(OpenApiLingxiChatRespVO.getOpenApiLingxiChatReplyResp(handleDTO, replyMessage));

		return Boolean.FALSE;
	}

}
