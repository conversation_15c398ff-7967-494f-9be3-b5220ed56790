package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import com.zyhl.hcy.yun.ai.common.base.enums.YunAiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.client.centertask.vo.CenterTaskCreateVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.enums.UserFileThumbnailStyleEnum;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueImageToolSettingDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatContentService;
import com.zyhl.yun.api.outer.application.chatv2.service.ImageTaskService;
import com.zyhl.yun.api.outer.application.chatv2.service.YunDiskV2Service;
import com.zyhl.yun.api.outer.application.dto.LastDialogueInfoDTO;
import com.zyhl.yun.api.outer.application.dto.YunDiskReqDTO;
import com.zyhl.yun.api.outer.config.IntentionCompanyProperties;
import com.zyhl.yun.api.outer.domain.dto.ImageToolSettingDTO;
import com.zyhl.yun.api.outer.domain.entity.centertask.CenterTaskCreateEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.ImageParamEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.TextParamEntity;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domainservice.BenefitNoDomainService;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ImageParamTypeEnum;
import com.zyhl.yun.api.outer.enums.ImageTransmissionTypeEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.task.TaskFeePaidStatusEnum;
import com.zyhl.yun.api.outer.enums.task.TaskFeeTypeEnum;
import com.zyhl.yun.api.outer.external.CenterTaskExternalService;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 对话任务管理器（2.0对话接口）
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ImageTaskServiceImpl implements ImageTaskService {

    private final CenterTaskExternalService centerTaskExternalService;

    private final IntentionCompanyProperties intentionCompanyProperties;

    private final BenefitNoDomainService benefitNoDomainService;

    private final ChatContentService chatContentService;

    private final YunDiskV2Service yunDiskV2Service;

    /**
     * 调创建任务接口生成任务
     *
     * @param handleDTO 输入处理DTO
     * @param intention 意图
     * @param taskId    任务id
     */
    @Override
    public CenterTaskCreateVO createImageTask(ChatAddHandleDTO handleDTO, String intention, Long taskId) throws YunAiBusinessException {
        CenterTaskCreateEntity centerTaskCreateEntity = new CenterTaskCreateEntity();
        centerTaskCreateEntity.setId(taskId);
        centerTaskCreateEntity.setUserId(RequestContextHolder.getUserId());
        centerTaskCreateEntity.setAlgorithmCodes(Collections.singletonList(intention));
        centerTaskCreateEntity.setSourceChannel(RequestContextHolder.getSourceChannel());
        centerTaskCreateEntity.setFeeType(TaskFeeTypeEnum.YES.getCode());
        centerTaskCreateEntity.setFeePaidStatus(TaskFeePaidStatusEnum.PAID.getCode());

        DialogueIntentionEnum dialogueIntentionEnum = DialogueIntentionEnum.getByCodeOrDefault(intention);

        if (isPictureGeneration(intention)) {
            // 文生图
            return textToPicture(handleDTO, centerTaskCreateEntity, dialogueIntentionEnum);
        } else if (isImageRelatedGeneration(intention)) {
            // 图片工具
            return imageTool(handleDTO, centerTaskCreateEntity, dialogueIntentionEnum);
        } else {
            log.error("2.0对话接口-异步不支持文生文");
            throw new YunAiBusinessException(YunAiCommonResultCode.ERROR_PARAMS);
        }

    }

    /**
     * 文生图
     *
     * @param handleDTO              输入处理DTO
     * @param centerTaskCreateEntity 任务创建实体
     * @param dialogueIntentionEnum  对话意图枚举
     * @return 任务创建对象
     */
    private CenterTaskCreateVO textToPicture(ChatAddHandleDTO handleDTO, CenterTaskCreateEntity centerTaskCreateEntity,
                                             DialogueIntentionEnum dialogueIntentionEnum) {
        Long dialogueId = handleDTO.getDialogueId();
        ChatAddReqDTO dto = handleDTO.getReqDTO();

        intentionCompanyProperties.setDeployProperties(dialogueIntentionEnum, centerTaskCreateEntity);
        handleDTO.setResultCount(centerTaskCreateEntity.getNumber());

        TextParamEntity textParamEntity = new TextParamEntity();
        textParamEntity.setUserId(dto.getUserId());
        textParamEntity.setRowkey(AiTextResultRepository.createRowKey(dto.getUserId(), dialogueId));
        textParamEntity.setDialogueId(String.valueOf(dialogueId));
        // 设置图片存储类型
        textParamEntity.setImageTransmissionType(ImageTransmissionTypeEnum.YUN_DISK.getCode());
        return centerTaskExternalService.createTextTask(dialogueIntentionEnum, centerTaskCreateEntity, textParamEntity);
    }

    /**
     * 图片工具
     *
     * @param handleDTO              输入处理DTO
     * @param centerTaskCreateEntity 会话创建实体
     * @param dialogueIntentionEnum  对话意图枚举
     * @return 任务创建结果
     */
    private CenterTaskCreateVO imageTool(ChatAddHandleDTO handleDTO, CenterTaskCreateEntity centerTaskCreateEntity,
                                         DialogueIntentionEnum dialogueIntentionEnum) {
        Long dialogueId = handleDTO.getDialogueId();
        ChatAddReqDTO dto = handleDTO.getReqDTO();
        String userId = dto.getUserId();

        // 根据不同的对话意图进行配置处理
        intentionCompanyProperties.setDeployProperties(dialogueIntentionEnum, centerTaskCreateEntity);
        handleDTO.setResultCount(centerTaskCreateEntity.getNumber());
        Integer resourceType = null;
        if (ObjectUtil.isNotEmpty(dto.getDialogueInput().getAttachment())) {
            resourceType = dto.getDialogueInput().getAttachment().getAttachmentTypeList().get(0);
        }

        ImageParamEntity imageEntity = new ImageParamEntity();

        if (ResourceTypeEnum.isDialogueId(resourceType)) {
            List<LastDialogueInfoDTO> lastDialogueInfoList = getLastDialogueInfoList(dto, userId, resourceType);
            if (CollUtil.isEmpty(lastDialogueInfoList)) {
                log.error("2.0对话接口-图片工具创建，上一次对话id为空或id异常");
                throw new YunAiBusinessException(YunAiCommonResultCode.ERROR_PARAMS);
            }
            // 存在上次对话信息 取对应的内容
            LastDialogueInfoDTO lastInfo = lastDialogueInfoList.get(0);
            imageEntity.setImageParamType(lastInfo.getImageParamType());
            // 有本地共享存储，则需要使用
            imageEntity.setLocalPath(lastInfo.getLocalPath());
            imageEntity.setFileUrl(lastInfo.getFileUrl());
            imageEntity.setFileId(lastInfo.getFileId());
            imageEntity.setImageExt(lastInfo.getImageExt());
        } else if (ResourceTypeEnum.isImage(resourceType)) {
            List<File> files = dto.getDialogueInput().getAttachment().getFileList();
            if (CollUtil.isEmpty(files)) {
                log.error("2.0对话接口-图片工具创建，资源文件id为空");
                throw new YunAiBusinessException(YunAiCommonResultCode.ERROR_PARAMS);
            }
            imageEntity.setFileId(files.get(0).getFileId());
            imageEntity.setImageParamType(ImageParamTypeEnum.FILE_ID.getCode());
            try {
                //新增的图片后缀获取，怕影响前面的功能先try catch
                YunDiskReqDTO fileDto = new YunDiskReqDTO(imageEntity.getFileId());
                fileDto.setImageThumbnailStyle(UserFileThumbnailStyleEnum.BIG.getStyle());
                File yunDiskContent = yunDiskV2Service.getYunDiskContent(fileDto);
                Optional.ofNullable(yunDiskContent).ifPresent(file -> imageEntity.setImageExt(file.getFileExtension()));
            } catch (Exception e) {
                log.error("2.0对话接口-图片工具创建，获取图片后缀异常", e);
            }
        } else {
            log.error("2.0对话接口-图片工具创建，资源类型错误 resourceType:{}", resourceType);
            throw new YunAiBusinessException(YunAiCommonResultCode.ERROR_PARAMS);
        }
        imageEntity.setRowkey(AiTextResultRepository.createRowKey(dto.getUserId(), dialogueId));

        // 设置图片存储类型
        imageEntity.setImageTransmissionType(ImageTransmissionTypeEnum.YUN_DISK.getCode());

        //该版本代码已经下线，前面的处理器直接走推荐处理
        if (DialogueIntentionEnum.LIVE_PHOTOS.equals(dialogueIntentionEnum)) {
            // 活照片不管版本号，默认云盘存储（前端播放问题暂未解决，2024-06-07临时设置）
            imageEntity.setImageTransmissionType(ImageTransmissionTypeEnum.YUN_DISK.getCode());
            // 活照片默认全身模式 style=1
            imageEntity.setStyle("1");
        }

        Optional<DialogueImageToolSettingDTO> imageToolSettingOptional = Optional.of(handleDTO)
                .map(ChatAddHandleDTO::getInputInfoDTO)
                .map(com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO::getToolSetting)
                .map(com.zyhl.yun.api.outer.application.chatv2.dto.DialogueToolSettingDTO::getImageToolSetting);
        if (imageToolSettingOptional.isPresent()) {
            DialogueImageToolSettingDTO imageToolSetting = imageToolSettingOptional.get();
            ImageToolSettingDTO imageToolSettingDTO = new ImageToolSettingDTO();
            imageToolSettingDTO.setStyle(imageToolSetting.getStyle());
            imageToolSettingDTO.setRatio(imageToolSetting.getRatio());
            imageToolSettingDTO.setSupplierType(imageToolSetting.getSupplierType());
            imageToolSettingDTO.setUrl(imageToolSetting.getUrl());
            centerTaskCreateEntity.setImageToolSettingDTO(imageToolSettingDTO);
        }
        Optional<String> optional = Optional.of(handleDTO)
                .map(ChatAddHandleDTO::getInputInfoDTO)
                .map(DialogueInputInfoDTO::getDialogue);
        optional.ifPresent(centerTaskCreateEntity::setDialogue);
        return centerTaskExternalService.createImageTask(dialogueIntentionEnum, centerTaskCreateEntity, imageEntity);
    }

    private List<LastDialogueInfoDTO> getLastDialogueInfoList(ChatAddReqDTO dto, String userId, Integer resourceType) {
        List<LastDialogueInfoDTO> lastDialogueInfoList = null;
        if (ResourceTypeEnum.isDialogueId(resourceType)) {
            List<String> lastDialogueIds = dto.getDialogueInput().getAttachment().getDialogueIdList();
            if (CollUtil.isNotEmpty(lastDialogueIds)) {
                lastDialogueInfoList = new ArrayList<>();
                for (String lastDialogueId : lastDialogueIds) {
                    LastDialogueInfoDTO dialogueInfo = chatContentService.getLastDialogueInfo(lastDialogueId, userId);
                    if (null != dialogueInfo) {
                        lastDialogueInfoList.add(dialogueInfo);
                    }
                }
            }
        }
        return lastDialogueInfoList;
    }

    private boolean isPictureGeneration(String intention) {
        return DialogueIntentionEnum.TEXT_GENERATE_PICTURE.getCode().equals(intention);
    }

    private boolean isImageRelatedGeneration(String intention) {
        return DialogueIntentionEnum.PICTURE_GENERATE_TEXT.getCode().equals(intention)
                || DialogueIntentionEnum.AI_HEAD_SCULPTURE.getCode().equals(intention)
                || DialogueIntentionEnum.PICTURE_COMIC_STYLE.getCode().equals(intention)
                || DialogueIntentionEnum.OLD_PHOTOS_REPAIR.getCode().equals(intention)
                || DialogueIntentionEnum.AI_EXPANSION_MAP.getCode().equals(intention)
                || DialogueIntentionEnum.INTELLIGENT_CUTOUT.getCode().equals(intention)
                || DialogueIntentionEnum.AI_PHOTO_EDIT.getCode().equals(intention)
                || DialogueIntentionEnum.IMAGE_QUALITY_RESTORATION.getCode().equals(intention)
                || DialogueIntentionEnum.SMART_FAKE_CHECK.getCode().equals(intention);
    }

}
