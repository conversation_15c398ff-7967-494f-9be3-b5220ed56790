package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import com.google.common.base.Objects;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.hcy.yun.ai.common.model.api.client.centertask.vo.CenterTaskCreateVO;
import com.zyhl.yun.api.outer.application.assembler.ChatFlowResultAssembler;
import com.zyhl.yun.api.outer.application.chatv2.dto.AssistantChatV2PollingUpdateDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.service.AlgorithmChatV2ContentService;
import com.zyhl.yun.api.outer.application.chatv2.service.BenefitService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.service.impl.ImageTaskServiceImpl;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueFlowResultVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueResultV2VO;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.config.BusinessParamProperties;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.AiTextResultVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddFlowStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.OutAuditStatusEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.RequestContextHolder.UserInfo;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 异步处理：图片工具
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AsyncImageToolHandlerImpl extends AbstractChatAddV2Handler {

    /**
     * 当前执行顺序
     */
    private final ExecuteSort thisExecuteSort = ExecuteSort.ASYNC_IMAGE_TOOL;

    @Resource
    private DataSaveService dataSaveService;
    @Resource
    private AlgorithmChatV2ContentService contentService;
    @Resource
    private ImageTaskServiceImpl imageTaskServiceImpl;
    @Resource
    private ChatConfigServiceDomainService chatConfigServiceDomainService;
    @Resource
    private BusinessParamProperties businessParamProperties;
    @Resource
    private UidGenerator uidGenerator;
    @Resource
    private ChatFlowResultAssembler chatFlowResultAssembler;
    @Resource
    private BenefitService benefitService;

    /**
     * 创建线程池
     */
    private final static int CORE_POOL_SIZE = 100;
    private final static int MAX_POOL_SIZE = 300;
    private final static long KEEP_ALIVE_TIME = 60;
    private final static int QUEUE_SIZE = 100000;
    private final static ThreadPoolExecutor POOL = new ThreadPoolExecutor(CORE_POOL_SIZE, MAX_POOL_SIZE, KEEP_ALIVE_TIME, TimeUnit.SECONDS, new ArrayBlockingQueue<>(QUEUE_SIZE));

    @Override
    public void afterPropertiesSet() throws Exception {
        // 支持的业务初始化
        List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        thisBusinessTypes.add(ChatBusinessTypeEnum.CLOUD_PHONE);
        this.setBusinessTypes(thisBusinessTypes);
    }

    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        log.info("进入{}", thisExecuteSort.getDesc());
        ChatAddReqDTO dto = handleDTO.getReqDTO();

        // 获取用户设置的模型，没有设置则使用默认模型
        ChatConfigEntity chatConfigEntity = chatConfigServiceDomainService.getUserCanUseModel(
                RequestContextHolder.getUserId(), RequestContextHolder.getPhoneNumber(),
                RequestContextHolder.getAssistantEnum(), RequestContextHolder.getBusinessType());

        // 先生成任务id，保存hbase（否则消费的时候可能查不到数据）
        Long taskId = uidGenerator.getUID();
        Integer thisFlowResultIndex = handleDTO.getFlowResultIndex();

        // 扣减权益
        benefitService.consumeBenefitImage(handleDTO.getReqDTO(), RequestContextHolder.getPhoneNumber(), handleDTO.getDialogueId(), handleDTO.getIntentionCode());

        DialogueFlowResultVO flowResult = handleDTO.getRespVO().getFlowResult();
        flowResult.setIndex(thisFlowResultIndex);
        flowResult.setResultType(FlowResultTypeEnum.ASYNC.getType());
        flowResult.setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());
        flowResult.setResultCount(null == handleDTO.getResultCount() ? 0 : handleDTO.getResultCount());
        flowResult.setOutAuditStatus(OutAuditStatusEnum.FAIL.getCode());
        handleDTO.getRespVO().setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());

        // 保存hbase-所有对话结果
        AiTextResultRespParameters respParameters = AiTextResultRespParameters.builder()
                .version(AiTextResultVersionEnum.V2.getVersion()).resultCode(ResultCodeEnum.SUCCESS.getResultCode())
                .outputList(Collections.singletonList(chatFlowResultAssembler.getFlowResult(flowResult))).build();
        respParameters.setOutputCommandVO(handleDTO.getIntentionVO());

        // 保存hbase
        dataSaveService.saveHbaseAllChatResult(handleDTO, respParameters);

        // 生成任务
        CenterTaskCreateVO taskVO;
        try {
            taskVO = imageTaskServiceImpl.createImageTask(handleDTO, handleDTO.getIntentionCode(), taskId);
            if (taskVO == null || CharSequenceUtil.isEmpty(taskVO.getTaskId())) {
                log.error("用户{}会话-创建任务表失败, dialogueId: {}, sessionId: {}", dto.getUserId(), handleDTO.getDialogueId(), handleDTO.getSessionId());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_CALL_EXCEPTION);
            }
        } catch (Exception e) {
            // 权益回滚
            benefitService.consumeBenefitFail(handleDTO.getReqDTO().getUserId(), RequestContextHolder.getPhoneNumber(), handleDTO.getDialogueId());
            throw e;
        }

        // 保存数据库
        dataSaveService.add(handleDTO, ChatStatusEnum.CHAT_IN, taskId, chatConfigEntity.getModelType());

        //imageTaskServiceImpl.createImageTask(handleDTO, handleDTO.getIntentionCode(), taskId);上面设置了数量，这里重新设置
        flowResult.setResultCount(null == handleDTO.getResultCount() ? 0 : handleDTO.getResultCount());
        dataSaveService.updateResult(handleDTO.getReqDTO().getUserId(), Long.toString(handleDTO.getDialogueId()), chatFlowResultAssembler.getFlowResult(flowResult));

        // 启动线程轮询
        runThread(handleDTO);

        return false;
    }

    /**
     * 启动多线程轮询
     *
     * @param handleDTO
     */
    public void runThread(ChatAddHandleDTO handleDTO) {
        final Map<String, String> logMap = LogCommonUtils.getCopyOfContextMap();
        final UserInfo userInfo = RequestContextHolder.getUserInfo();

        // 返回异步结果
        handleDTO.getSseEmitterOperate().send(BaseResult.success(handleDTO.getRespVO()));
        POOL.execute(() -> {
            RequestContextHolder.setUserInfo(userInfo);
            LogCommonUtils.initLogMDC(logMap);
            Integer thisFlowResultIndex = handleDTO.getFlowResultIndex();
            Long dialogueId = handleDTO.getDialogueId();
            DialogueResultV2VO result = null;
            int pollingUpdateCount = 0;
            while (true) {
                boolean breakWhile = false;
                pollingUpdateCount++;
                //默认进行中
                String finishReason = ChatAddFlowStatusEnum.PROCESSING.getStatus();
                try {
                    int maxThreadMaxPullUpdateCount = businessParamProperties.getImageToolThreadMaxPullUpdateCount();
                    if (pollingUpdateCount > maxThreadMaxPullUpdateCount) {
                        finishReason = ChatAddFlowStatusEnum.STOP.getStatus();
                        breakWhile = Boolean.TRUE;
                    }
                    TimeUnit.SECONDS.sleep(businessParamProperties.getImageToolThreadSleepTime());
                    AssistantChatV2PollingUpdateDTO dto = new AssistantChatV2PollingUpdateDTO();
                    dto.setDialogueId(dialogueId);
                    result = contentService.pollingUpdate(dto);
                    log.info(
                            "对话循环结果 dialogueId:{}, status:{}, pollingUpdateCount:{}, maxThreadMaxPullUpdateCount:{}",
                            dialogueId, (null != result ? result.getChatStatus() : null), pollingUpdateCount,
                            maxThreadMaxPullUpdateCount);
                    if (null != result) {
                        DialogueFlowResult dialogueFlowResult = getOutput(result.getOutputList(), thisFlowResultIndex);
                        if (Objects.equal(result.getChatStatus(), ChatStatusEnum.CHAT_SUCCESS.getCode())
                                && java.util.Objects.nonNull(dialogueFlowResult)
                                && Objects.equal(OutAuditStatusEnum.SUCCESS.getCode(), dialogueFlowResult.getOutAuditStatus())) {
                            log.info("对话成功，退出循环 dialogueId:{} chatStatus:{}", dialogueId, result.getChatStatus());
                            breakWhile = Boolean.TRUE;
                            finishReason = ChatAddFlowStatusEnum.STOP.getStatus();
                        }
                        if (Objects.equal(result.getChatStatus(), ChatStatusEnum.CHAT_FAIL.getCode())) {
                            log.info("对话失败，退出循环 dialogueId:{} chatStatus:{}", dialogueId, result.getChatStatus());
                            breakWhile = Boolean.TRUE;
                            finishReason = ChatAddFlowStatusEnum.ERROR.getStatus();
                        }
                    }
                } catch (Exception e) {
                    log.info("对话循环 第{}次 dialogueId:{} error:", pollingUpdateCount, dialogueId, e);
                }
                log.info("对话循环输出 dialogueId:{}, pollingUpdateCount:{}, maxThreadMaxPullUpdateCount:{}", dialogueId,
                        pollingUpdateCount, businessParamProperties.getImageToolThreadMaxPullUpdateCount());
                handleDTO.getRespVO().setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());
                handleDTO.getRespVO().getFlowResult().setFinishReason(finishReason);
                handleDTO.getRespVO().getFlowResult().setIndex(thisFlowResultIndex);
                if (null != result) {
                    DialogueFlowResult dialogueFlowResult = getOutput(result.getOutputList(), thisFlowResultIndex);
                    // 存在则设置result
                    handleDTO.getRespVO().getFlowResult().setOutAuditStatus(Optional.ofNullable(dialogueFlowResult).map(DialogueFlowResult::getOutAuditStatus).orElse(null));
                    if (ChatAddFlowStatusEnum.ERROR.getStatus().equals(handleDTO.getRespVO().getFlowResult().getFinishReason())) {
                        handleDTO.getRespVO().getFlowResult().setErrorCode(Optional.ofNullable(dialogueFlowResult).map(DialogueFlowResult::getErrorCode).orElse(null));
                        handleDTO.getRespVO().getFlowResult().setErrorMessage(Optional.ofNullable(dialogueFlowResult).map(DialogueFlowResult::getErrorMessage).orElse(null));
                    }
                    if (null != dialogueFlowResult) {
                        handleDTO.getRespVO().getFlowResult().setResultType(FlowResultTypeEnum.ASYNC.getType());
                        handleDTO.getRespVO().getFlowResult().setFileList(dialogueFlowResult.getFileList());
                        handleDTO.getRespVO().getFlowResult().setOutContent(dialogueFlowResult.getOutContent());
                    }
                }
                if (breakWhile) {
                    handleDTO.getRespVO().setFinishReason(finishReason);
                    handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(handleDTO.getRespVO()));
                    break;
                }
                if (CollUtil.isEmpty(handleDTO.getRespVO().getFlowResult().getFileList())) {
                    //没结果不输出
                    continue;
                }
                handleDTO.getSseEmitterOperate().send(BaseResult.success(handleDTO.getRespVO()));
            }
        });
    }

    private DialogueFlowResult getOutput(List<DialogueFlowResult> outputList, Integer thisFlowResultIndex) {
        if (CollUtil.isNotEmpty(outputList)) {
            Optional<DialogueFlowResult> optional = outputList.stream().filter((dialogueFlowResult ->
                    Objects.equal(thisFlowResultIndex, dialogueFlowResult.getIndex()))).findFirst();
            if (optional.isPresent()) {
                return optional.get();
            }
        }
        return null;
    }

    /**
     * 执行异步图片工具方法
     *
     * @param handleDTO the handle dto
     * @param event     the event
     */
    @Override
    public void runHandlerContinue(ChatAddHandleDTO handleDTO, SseEventListener event) {
        ChatAddReqDTO dto = handleDTO.getReqDTO();
        // 生成任务
        CenterTaskCreateVO taskVO;
        try {
            taskVO = imageTaskServiceImpl.createImageTask(handleDTO, handleDTO.getIntentionCode(), handleDTO.getTaskId());
            if (taskVO == null || CharSequenceUtil.isEmpty(taskVO.getTaskId())) {
                log.error("用户{}会话-创建任务表失败, dialogueId: {}, sessionId: {}", dto.getUserId(), handleDTO.getDialogueId(), handleDTO.getSessionId());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_CALL_EXCEPTION);
            }
        } catch (Exception e) {
            // 权益回滚
            benefitService.consumeBenefitFail(handleDTO.getReqDTO().getUserId(), RequestContextHolder.getPhoneNumber(), handleDTO.getDialogueId());
            throw e;
        }
        DialogueFlowResultVO flowResult = new DialogueFlowResultVO();
        flowResult.setIndex(event.getRespVO().getFlowResult().getIndex() + 1);
        flowResult.setResultType(FlowResultTypeEnum.ASYNC.getType());
        flowResult.setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());
        flowResult.setResultCount(null == handleDTO.getResultCount() ? 0 : handleDTO.getResultCount());
        flowResult.setOutAuditStatus(OutAuditStatusEnum.FAIL.getCode());
        handleDTO.getRespVO().setFlowResult(flowResult);
        handleDTO.getRespVO().setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());
        // 更新hbase-异步任务的模块
        dataSaveService.updateResult(handleDTO.getReqDTO().getUserId(), Long.toString(handleDTO.getDialogueId()), chatFlowResultAssembler.getFlowResult(flowResult));
        log.info("更新hbase后执行预处理图片工具 flowResult:{}", JSONUtil.toJsonStr(flowResult));

        // 发送给前端开启异步任务
        handleDTO.setFlowResultIndex(event.getRespVO().getFlowResult().getIndex());
        this.runThread(handleDTO);
    }

}
