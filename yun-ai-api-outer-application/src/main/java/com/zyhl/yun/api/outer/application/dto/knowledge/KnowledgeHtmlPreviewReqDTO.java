package com.zyhl.yun.api.outer.application.dto.knowledge;

import javax.validation.constraints.NotBlank;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 知识库html预览请求类
 * 
 * <AUTHOR>
 * @date 2025-04-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class KnowledgeHtmlPreviewReqDTO extends BaseChannelDTO {
    
    /**
     * 资源ID
     */
    @NotBlank
    private String resourceId;

    /**
     * 知识库id
     */
    @NotBlank
    private String baseId;
}
