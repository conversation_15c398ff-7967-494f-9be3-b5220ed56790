package com.zyhl.yun.api.outer.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 对话tidb保存数据-DTO
 * @Author: WeiJingKun
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlgorithmChatTidbSaveDTO {

    /** 对话id */
    private Long dialogueId;

    /** 输出审核状态 */
    private Integer outAuditStatus;

    /** 对话状态 */
    private Integer chatStatus;

    /** 对话消息（存入对话表的outContent） */
    private String msg;


}
