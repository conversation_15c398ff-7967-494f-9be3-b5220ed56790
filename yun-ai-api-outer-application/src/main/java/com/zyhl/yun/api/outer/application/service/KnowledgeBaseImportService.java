package com.zyhl.yun.api.outer.application.service;

import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileBatchImportReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileCheckReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileTaskListReqDTO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeCheckResultVO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeFileImportVO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeFileTaskListVO;
import com.zyhl.yun.api.outer.application.vo.knowledge.PersonalKnowledgeImportTaskVO;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeResourceListPageInfoVO;

import java.util.List;

/**
 * 知识库导入服务接口
 *
 * <AUTHOR>
 */
public interface KnowledgeBaseImportService {

    /**
     * 批量导入知识库文件
     *
     * @param reqDTO 批量导入请求DTO
     * @return 任务ID列表
     */
    KnowledgeFileImportVO batchImport(KnowledgeFileBatchImportReqDTO reqDTO);

    /**
     * 批量删除知识库文件任务
     *
     * @param userId 用户ID
     * @param taskIds 任务ID列表
     * @return 是否删除成功
     */
    boolean batchDelete(String userId, List<String> taskIds);


    /**
     * 查询用户自定义知识库导入任务列表
     *
     * @param dto 请求参数
     * @return 带分页信息的任务列表
     */
    KnowledgeFileTaskListVO listTasks(KnowledgeFileTaskListReqDTO dto);

    /**
     * 重试知识库导入任务
     *
     * @param userId 用户ID
     * @param taskId 任务ID
     * @return 重试后的任务信息
     */
    PersonalKnowledgeImportTaskVO retryTask(String userId, String taskId);

    void checkResource(KnowledgeFileCheckReqDTO dto);
} 