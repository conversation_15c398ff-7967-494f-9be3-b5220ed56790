package com.zyhl.yun.api.outer.application.chatv2.service;

import com.zyhl.yun.api.outer.application.chatv2.dto.AlgorithmChatV2ContentListDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.AssistantChatV2PollingUpdateDTO;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueResultV2VO;
import com.zyhl.yun.api.outer.domain.vo.common.PageInfoVO;

/**
 * 对话-service
 * @Author: WeiJingKun
 */
public interface AlgorithmChatV2ContentService {

    /**
     * 历史对话列表查询
     *
     * @param dto 请求参数
     * @return 分页结果
     */
    PageInfoVO<DialogueResultV2VO> contentList(AlgorithmChatV2ContentListDTO dto);

    /**
     * 查询对话输出
     * 根据对话id查询AI助手异步结果信息，并更新对话表输出内容。
     *
     * @param dto 请求参数
     * @return 对话结果
     */
    DialogueResultV2VO pollingUpdate(AssistantChatV2PollingUpdateDTO dto);

}
