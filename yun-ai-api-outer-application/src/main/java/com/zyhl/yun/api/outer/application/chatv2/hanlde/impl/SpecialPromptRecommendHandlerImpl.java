package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatDialogueRecommendService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.PromptRecommendVO;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 提示词推荐
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SpecialPromptRecommendHandlerImpl extends AbstractChatAddV2Handler {

	/**
	 * 当前执行顺序
	 */
	private ExecuteSort thisExecuteSort = ExecuteSort.SPECIAL_PROMPT_RECOMMEND;

	@Resource
	private DataSaveService dataSaveService;
	@Resource
	private ChatDialogueRecommendService chatDialogueRecommendService;

	@Override
	public void afterPropertiesSet() throws Exception {
    	//支持的业务初始化
    	List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        thisBusinessTypes.add(ChatBusinessTypeEnum.CLOUD_PHONE);
        this.setBusinessTypes(thisBusinessTypes);
    }
    
	@Override
	public int order() {
		return thisExecuteSort.getSort();
	}

	@Override
	public boolean execute(ChatAddHandleDTO handleDTO) {
		// 对话类型 并且 （小天渠道||云手机） 并且 非强制大模型对话
		return ApplicationTypeEnum.isChat(handleDTO.getReqDTO().getApplicationType())
				&& (AssistantEnum.isXiaoTian(RequestContextHolder.getAssistantEnum())
						|| AssistantEnum.isCloudPhone(RequestContextHolder.getAssistantEnum()))
				&& !Boolean.TRUE.equals(handleDTO.getReqDTO().getDialogueInput().isEnableForceLlm());
	}

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		log.info("进入{}", thisExecuteSort.getDesc());

		// 获取推荐提问语句
		List<PromptRecommendVO> promptRecommendList = chatDialogueRecommendService.getPromptVOList(handleDTO);
		if (ObjectUtil.isEmpty(promptRecommendList)) {
			return true;
		}

		handleDTO.setTextGenerateTextIntention();
		
		// 推荐信息
		handleDTO.getRespVO().setRecommend(DialogueRecommendVO.builder().promptList(promptRecommendList).build());
		
		handleDTO.getRespVO().setFlowResult(null);
		
		// 保存hbase
		dataSaveService.saveTextResult(handleDTO, StringUtils.EMPTY, StringUtils.EMPTY);

		// 保存tidb
		dataSaveService.addSuccess(handleDTO, OutContentTypeEnum.TEXT);

		// 输出
        handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(handleDTO.getRespVO()));
        
		return false;
	}

}
