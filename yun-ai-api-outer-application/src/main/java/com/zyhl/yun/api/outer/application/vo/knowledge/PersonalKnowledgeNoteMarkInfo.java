package com.zyhl.yun.api.outer.application.vo.knowledge;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/18 14:23
 */
@Data
public class PersonalKnowledgeNoteMarkInfo implements Serializable {
    /**
     * 标注ID
     */
    private String id;
    /**
     * 标注时间
     */
    private Long time;
    /**
     * 标注内容
     */
    private String content;
    /**
     * 附件id
     */
    private String attachmentId;
    /**
     * 标注标题
     */
    private String title;

    /**
     * 缩略图的真实URL地址(当附件为图片时有值)
     */
    private String thumbnailUrl;
}
