package com.zyhl.yun.api.outer.application.dto;

import com.zyhl.yun.api.outer.application.vo.TaskCommentInfoVO;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 任务评论信息DTO
 * @date 2025/4/22 14:42
 */
@Data
@AllArgsConstructor
public class TaskCommentRetDTO implements Serializable {

    private static final long serialVersionUID = 4746295535469097815L;

    private List<TaskCommentInfoVO> commentList;

}