package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatDialogueRecommendService;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import lombok.extern.slf4j.Slf4j;

/**
 * 推荐信息
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RecommendInfoHandlerImpl extends AbstractChatAddV2Handler {

	/**
	 * 当前执行顺序
	 */
	private ExecuteSort thisExecuteSort = ExecuteSort.RECOMMEND_INFO;

	@Resource
	protected ChatDialogueRecommendService recommendService;

	@Resource
	protected SourceChannelsProperties sourceChannelsProperties;

	@Override
	public void afterPropertiesSet() throws Exception {
		// 支持的业务初始化
		List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        this.setBusinessTypes(thisBusinessTypes);
	}

	@Override
	public int order() {
		return thisExecuteSort.getSort();
	}

	@Override
	public boolean execute(ChatAddHandleDTO handleDTO) {
		// 对话类型 并且 小天渠道 并且 非强制大模型对话
		return ApplicationTypeEnum.isChat(handleDTO.getReqDTO().getApplicationType())
				&& sourceChannelsProperties.isXiaoTian(RequestContextHolder.getSourceChannel())
				&& !handleDTO.getInputInfoDTO().isEnableForceLlm();
	}

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		log.info("进入{}", thisExecuteSort.getDesc());

		// 设置对话结果推荐对象
		handleDTO.getRespVO().setRecommend(recommendService.getDialogueRecommendVO(handleDTO));
		return true;
	}

}
