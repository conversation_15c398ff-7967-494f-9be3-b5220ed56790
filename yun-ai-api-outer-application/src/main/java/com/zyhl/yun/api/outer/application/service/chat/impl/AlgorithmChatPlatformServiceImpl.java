package com.zyhl.yun.api.outer.application.service.chat.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.model.api.client.qwen.ExternalQwenClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.vo.TextModelBaseVo;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatPlatformAddDTO;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatPlatformService;
import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;
import com.zyhl.yun.api.outer.enums.chat.ChatAddResultTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AlgorithmChatPlatformServiceImpl implements AlgorithmChatPlatformService {

    @Resource
    private UidGenerator uidGenerator;

    @Resource
    private ExternalQwenClient externalQwenClient;

    @Resource
    private ExecutorService platformThreadPool;

    @Override
    public AlgorithmChatAddVO submitAlgorithmChatText(String model, AlgorithmChatPlatformAddDTO dto) {
        AlgorithmChatAddVO vo = new AlgorithmChatAddVO();
        boolean success = false;
        String sessionId = dto.getSessionId();
        Long dialogueId = null;
        TextModelTextReqDTO reqDTO = new TextModelTextReqDTO();
        // TODO 这里是否需要 设置是否强制联网搜索，需要，则接口需要增加入参
        try {
            dialogueId = uidGenerator.getUID();
            if (CharSequenceUtil.isBlank(sessionId)) {
                // sessionId空，手动设置
                sessionId = String.valueOf(uidGenerator.getUID());
                dto.setSessionId(sessionId);
            }
            vo.setSessionId(dto.getSessionId());
            vo.setResultType(ChatAddResultTypeEnum.ASYNCHRONOUS.getType());
            vo.setDialogueId(String.valueOf(dialogueId));
            reqDTO.setSessionId(sessionId);
            // 对话id设置为taskId
            reqDTO.setTaskId(String.valueOf(dialogueId));
            // 设置对话请求
            TextModelMessageDTO textModelMessageDTO = new TextModelMessageDTO();
            textModelMessageDTO.setRole(TextModelRoleEnum.USER.getName());
            textModelMessageDTO.setContent(dto.getContent().getDialogue());
            reqDTO.setMessageDtoList(Collections.singletonList(textModelMessageDTO));
            // 异步直接返回-多线程执行
            platformThreadPool.execute(() -> {
                TextModelBaseVo resp = null;
                try {
                    resp = externalQwenClient.completions(model, reqDTO);
                } catch (Exception e) {
                    log.error(
                            "第三方平台请求-submitAlgorithmChatText 异步请求 sessionId:{} | reqDTO:{} | 第三方平台请求异步自研文本大模型执行异常error",
                            dto.getSessionId(), JSONUtil.toJsonStr(reqDTO), e);
                } finally {
                    log.info(
                            "第三方平台请求-submitAlgorithmChatText 异步请求 sessionId:{} | reqDTO:{} | resp:{} | 第三方平台请求异步自研文本大模型",
                            dto.getSessionId(), JSONUtil.toJsonStr(reqDTO), JSONUtil.toJsonStr(resp));
                }
            });
            // 线程发送完成，设置成功
            success = true;
        } catch (Exception e) {
            log.error(
                    "第三方平台请求-submitAlgorithmChatText sessionId:{} | userId:{} | dto:{} | reqDTO:{} | 第三方平台请求自研文本大模型执行异常error:",
                    sessionId, dto.getUserId(), JSONUtil.toJsonStr(dto), JSONUtil.toJsonStr(reqDTO), e);
        } finally {
            log.info(
                    "第三方平台请求-submitAlgorithmChatText sessionId:{} | userId:{} | dto:{} | reqDTO:{} | resp:{} | 第三方平台请求自研文本大模型success:{}",
                    sessionId, dto.getUserId(), JSONUtil.toJsonStr(dto), JSONUtil.toJsonStr(reqDTO),
                    JSONUtil.toJsonStr(vo), success);
        }
        return vo;
    }
}
