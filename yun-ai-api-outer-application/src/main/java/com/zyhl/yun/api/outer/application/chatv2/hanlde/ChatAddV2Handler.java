package com.zyhl.yun.api.outer.application.chatv2.hanlde;

import java.util.List;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;

/**
 * AI对话处理器
 *
 * <AUTHOR>
 */
public interface ChatAddV2Handler {

    /**
     * 处理器执行顺序序号
     *
     * @return 执行顺序
     */
    int order();
    
    /**
     * 业务流程列表
     * @return
     */
    public List<ChatBusinessTypeEnum> getBusinessTypes();
    
    /**
     * 处理器是否执行
     *
     * @param handleDTO 请求dto
     * @return false-不执行
     */
    boolean execute(ChatAddHandleDTO handleDTO);

    /**
     * 运行处理器
     *
     * @param handleDTO 内部请求dto
     * @return true-继续执行下一个，false-终止执行
     */
    boolean run(ChatAddHandleDTO handleDTO);

}
