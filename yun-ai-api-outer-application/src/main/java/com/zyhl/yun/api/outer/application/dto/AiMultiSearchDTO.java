package com.zyhl.yun.api.outer.application.dto;

import com.zyhl.hcy.yun.ai.common.base.enums.PantaLabelEnum;
import com.zyhl.hcy.yun.ai.common.model.api.client.ali.dto.GenericSearchDTO;
import com.zyhl.yun.api.outer.enums.EntityResourceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * className: AiMultiSearchDTO
 * description: AI全网搜——混合搜索参数DTO
 *
 * <AUTHOR>
 * @date 2025/3/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AiMultiSearchDTO {

    private EntityResourceTypeEnum resourceType;

    /**
     * 是否为搜小站标识，默认为false
     */
    private boolean pantaFlag = false;

    /**
     * 阿里云标准搜索接口参数DTO
     */
    private GenericSearchDTO genericSearchDTO;

    /**
     * 搜小站专用搜索实体抽取参数
     */
    private Map<PantaLabelEnum, List<String>> metadataMap;
}