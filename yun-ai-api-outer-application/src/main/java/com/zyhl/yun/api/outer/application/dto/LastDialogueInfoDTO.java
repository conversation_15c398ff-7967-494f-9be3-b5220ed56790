package com.zyhl.yun.api.outer.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 上次对话信息DTO
 *
 * <AUTHOR>
 * @date 2024/6/4 11:42
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LastDialogueInfoDTO {

    /**
     * 图片传输类型 ImageParamTypeEnum
     * 1、图片url地址
     * 2、图片base64数据，不带data:image/jpeg;base64头
     * 3、图片文件fileId
     */
    private Integer imageParamType;

    /**
     * 图片文件fileId 图片传输类型为3必传
     */
    private String fileId;

    /**
     * 图片url地址 图片传输类型为1必传
     */
    private String fileUrl;
    
    /**
     * 图片文件共享存储
     */
    private String localPath;

    /**
     * 图片扩展名，例如：jpg、png等 图片传输类型为1必传
     */
    private String imageExt;
}
