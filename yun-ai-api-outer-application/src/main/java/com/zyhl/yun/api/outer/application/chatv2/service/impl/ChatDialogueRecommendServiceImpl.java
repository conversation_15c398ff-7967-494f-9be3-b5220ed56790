package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import com.zyhl.hcy.yun.ai.common.base.enums.YunAiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelVlReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.utils.ImageProcessorUtil;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.AIFileVO;
import com.zyhl.yun.api.outer.application.assembler.RecommendToolAssembler;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueAttachmentDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueIntentionDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.impl.TextModelVisionSseHandlerImpl;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatDialogueRecommendService;
import com.zyhl.yun.api.outer.config.DialogueRecommendProperties;
import com.zyhl.yun.api.outer.config.RecommendPromptTemplateProperties;
import com.zyhl.yun.api.outer.config.textmodel.VlModelConfig;
import com.zyhl.yun.api.outer.constants.FutureConstants;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.vo.ContentRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.ContextRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.IntentionRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.PromptRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.QueryRecommendListVO;
import com.zyhl.yun.api.outer.domain.vo.QueryRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.ToolRecommendVO;
import com.zyhl.yun.api.outer.domainservice.PromptRecommendHandleService;
import com.zyhl.yun.api.outer.domainservice.RecommendIntentionService;
import com.zyhl.yun.api.outer.domainservice.RecommendQueryService;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.external.YunDiskExternalService;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.VersionUtil;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 对话结果推荐接口实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatDialogueRecommendServiceImpl implements ChatDialogueRecommendService {

    /**
     * 推荐最大size
     */
    private static final int RECOMMEND_MAX_SIZE = 3;

    private final DialogueRecommendProperties recommendProperties;

    private final PromptRecommendHandleService promptRecommendHandleService;

    private final RecommendIntentionService recommendIntentionService;

    private final RecommendQueryService recommendQueryService;

    private final RecommendPromptTemplateProperties recommendPromptTemplateProperties;

    private final YunDiskExternalService yunDiskExternalService;

    private final TextModelVisionSseHandlerImpl textModelVisionSseHandlerImpl;

    private final VlModelConfig vlModelConfig;

    @Resource
    private RecommendToolAssembler recommendToolAssembler;

    @SuppressWarnings("unchecked")
    @Override
    public DialogueRecommendVO getDialogueRecommendVO(ChatAddHandleDTO handleDTO) {
        if (ApplicationTypeEnum.isIntelligen(handleDTO.getReqDTO().getApplicationType())) {
            log.info("【智能体对话】不进行推荐，直接返回空");
            return DialogueRecommendVO.builder().build();
        }

        String intention = handleDTO.getIntentionCode();
        DialogueInputInfoDTO contentDTO = handleDTO.getReqDTO().getDialogueInput();
        DialogueIntentionVO intentionVO = handleDTO.getIntentionVO();

        // size判断，优先级：（优先）多意图推荐->内容推荐->问题推荐
        List<Future<Object>> futures = new ArrayList<>();

        //拍照解题和拍照翻译的推荐
        boolean textToolAiPhotoVision = DialogueIntentionSubEnum.AI_PHOTO_RECOGNITION_SOLVE_PROBLEMS.getCode().equals(handleDTO.getSubIntentionCode()) ||
                DialogueIntentionSubEnum.AI_PHOTO_RECOGNITION_TRANSLATE.getCode().equals(handleDTO.getSubIntentionCode());
        Optional<List<File>> fileListOptional =
                Optional.of(handleDTO)
                        .map(ChatAddHandleDTO::getInputInfoDTO)
                        .map(DialogueInputInfoDTO::getAttachment)
                        .map(DialogueAttachmentDTO::getFileList);
        if (VersionUtil.xtH5VersionGte210() && textToolAiPhotoVision
                && fileListOptional.isPresent() && !CollectionUtils.isEmpty(fileListOptional.get())) {
            Future<Object> future = getTextToolAiPhotoVisionFuture(handleDTO);
            handleDTO.setFutures(Collections.singletonList(future));
            log.info("【拍照解题和拍照翻译】进行多意图推荐");
            return DialogueRecommendVO.builder().build();
        }

        List<ContentRecommendVO> contentRecommendList = getContentRecommendVOList(intentionVO);
        int contentRecommendListSize = (CollectionUtils.isEmpty(contentRecommendList) ? 0
                : contentRecommendList.size());

        DialogueRecommendVO dialogueRecommendVO = DialogueRecommendVO.builder().contentList(contentRecommendList)
                .contextList(getContextRecommendVOList()).build();

        boolean isSseTextIntention = DialogueIntentionEnum.isTextIntention(intention);
        boolean isSseTextToolIntention = DialogueIntentionEnum.isTextToolIntention(intention);
        boolean isSearchIntention = DialogueIntentionEnum.isSearchIntentionOrOther(intention);

        // 存在意图，只有文生文或文本工具或者搜索才有多意图推荐
        if (isSseTextIntention || isSseTextToolIntention || isSearchIntention) {
            // 多意图处理
            if (intentionVO != null) {
                if (recommendPromptTemplateProperties.isIntentionUnifiedTemplate()) {
                    // 统一模板处理多意图推荐
                    Future<Object> future = getIntentionListUnifiedRecommendVOFuture(handleDTO.getDialogueId(),
                            contentDTO.getDialogue(), intention, intentionVO.getIntentionInfoList());
                    if (null != future) {
                        futures.add(future);
                    }
                } else {
                    // 独立模板处理多意图推荐
                    for (IntentionInfo intentionInfo : intentionVO.getIntentionInfoList()) {
                        if (intentionInfo.getIntention().equals(intention)) {
                            // 主意图不需要处理
                            continue;
                        }
                        // 多意图追加推荐并发
                        Future<Object> future = getIntentionListRecommendVOFuture(handleDTO.getDialogueId(),
                                contentDTO.getDialogue(), intentionInfo);
                        if (null != future) {
                            futures.add(future);
                        }
                    }
                }
            }

            // 多意图+内容推荐不够RECOMMEND_MAX_SIZE，需要追加问题推荐并发
            if ((futures.size() + contentRecommendListSize) < RECOMMEND_MAX_SIZE
                    && (isSseTextIntention || isSseTextToolIntention)) {
                // 只有文本意图才有问题推荐
                Future<Object> future = getQueryRecommendVOListFuture(handleDTO.getDialogueId(),
                        contentDTO.getDialogue());
                if (null != future) {
                    futures.add(future);
                }
            }

            if (isSseTextIntention || isSseTextToolIntention || isSearchIntention) {
                handleDTO.setFutures(futures);
                // 【文生文流式】或【搜索意图】获取到结果后，调用setFuturesResult方法
                log.info("【文生文流式】或【搜索意图】获取到最后结果后，返回前端之前调用setFuturesResult方法, dialogueId:{}", handleDTO.getDialogueId());
            } else {
                log.info("非文生文流式获取到结果时候，实时get futures, dialogueId:{}", handleDTO.getDialogueId());
                setFuturesResult(handleDTO.getDialogueId(), dialogueRecommendVO, futures);
            }
        }

        return dialogueRecommendVO;
    }

    @Override
    public DialogueRecommendVO getMiddleRecommendVo(ChatAddHandleDTO handleDTO) {
        DialogueRecommendVO dialogueRecommendVO = DialogueRecommendVO.builder().build();
        String intention = handleDTO.getIntentionCode();
        String subIntention = handleDTO.getSubIntentionCode();
        if (DialogueIntentionEnum.isTextToolIntention(intention)
                && DialogueIntentionSubEnum.isAiSpeedRead(subIntention)) {
            // 图书快速阅读，增加引流工具推荐
            dialogueRecommendVO.setToolList(getSpeedReadMiddleRecommendListVo(handleDTO));
        }
        return dialogueRecommendVO;
    }

    @Override
    public List<ToolRecommendVO> getToolRecommendListVo(ChatAddHandleDTO handleDTO) {
        String intention = handleDTO.getIntentionCode();
        if (DialogueIntentionEnum.isTextToolIntention(intention) || DialogueIntentionEnum.isTextIntention(intention)) {
            // 图书快速阅读，增加文本工具推荐
            return recommendToolAssembler.toToolRecommendListVO(recommendProperties.getTextToolRecommendList());
        }
        return null;
    }

    @Override
    public List<ToolRecommendVO> getToolRecommendListVo(ChatAddHandleDTO handleDTO, String filterPrompt) {
        String intention = handleDTO.getIntentionCode();
        if (DialogueIntentionEnum.isTextToolIntention(intention) || DialogueIntentionEnum.isTextIntention(intention)) {
            // 图书快速阅读，增加文本工具推荐
            return recommendToolAssembler.toToolRecommendListVO(recommendProperties.getTextToolRecommendList(filterPrompt));
        }
        return null;
    }

    @Override
    public List<ToolRecommendVO> getSpeedReadMiddleRecommendListVo(ChatAddHandleDTO handleDTO) {
        String intention = handleDTO.getIntentionCode();
        String subIntention = handleDTO.getSubIntentionCode();
        if (DialogueIntentionEnum.isTextIntention(intention) || (DialogueIntentionEnum.isTextToolIntention(intention)
                && DialogueIntentionSubEnum.isAiSpeedRead(subIntention))) {
            // 图书快速阅读，增加中部工具推荐
            return recommendToolAssembler.toToolRecommendListVO(recommendProperties.getSpeedReadMiddleRecommendList());
        }
        return null;
    }

    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    public void setFuturesResult(Long dialogueId, DialogueRecommendVO dialogueRecommendVO,
                                 List<Future<Object>> futures) {
        long start = System.currentTimeMillis();

        if (CollectionUtils.isEmpty(futures)) {
            log.info("DialogueRecommendServiceImpl-setFuturesResult futures is empty");
            return;
        }
        List<IntentionRecommendVO> intentionList = new ArrayList<>();
        List<QueryRecommendVO> queryList = new ArrayList<>();
        try {
            for (Future<Object> future : futures) {
                // 根据类型设置intentionList还是queryList
                try {
                    // 等待5秒，未获取结果后取消任务
                    Object object = future.get(FutureConstants.FIVE_SECONDS, TimeUnit.SECONDS);
                    if (null == object) {
                        log.warn("setFuturesResult future.get() dialogueId:{}, object  is null", dialogueId);
                        continue;
                    }
                    if (object instanceof List) {
                        List<IntentionRecommendVO> intentionRecommends = (List) object;
                        // 意图集合-多个
                        intentionList.addAll(intentionRecommends);
                    } else if (object instanceof IntentionRecommendVO) {
                        // 意图集合-单个
                        intentionList.add((IntentionRecommendVO) object);
                    } else if (object instanceof QueryRecommendListVO) {
                        // 问题集合
                        List<QueryRecommendVO> querys = ((QueryRecommendListVO) object).getQueryRecommends();
                        if (!CollectionUtils.isEmpty(querys)) {
                            queryList.addAll(querys);
                        }
                    } else {
                        log.warn("setFuturesResult future.get() dialogueId:{}, object getClass:{} not to set",
                                dialogueId, object.getClass());
                    }
                } catch (Exception e) {
                    log.error("setFuturesResult future.get() dialogueId:{}, error:", dialogueId, e);
                }
            }
        } finally {

            // 清空futures
            futures = null;

            dialogueRecommendVO.setIntentionList(intentionList);
            dialogueRecommendVO.setQueryList(queryList);

            log.info("DialogueRecommendServiceImpl-setFuturesResult，获取对话结果推荐-耗时：{}ms",
                    System.currentTimeMillis() - start);
        }
    }

    @SuppressWarnings("rawtypes")
    private Future getQueryRecommendVOListFuture(Long dialogueId, String dialogue) {
        return recommendQueryService.getRecommendQueryFuture(dialogueId, dialogue);
    }

    @SuppressWarnings("rawtypes")
    private Future getIntentionListRecommendVOFuture(Long dialogueId, String dialogue, IntentionInfo intentionInfo) {
        return recommendIntentionService.getRecommendIntentionFuture(dialogueId, dialogue, intentionInfo);
    }

    private Future<Object> getIntentionListUnifiedRecommendVOFuture(Long dialogueId, String dialogue, String intention,
                                                                    List<IntentionInfo> intentionInfoList) {
        List<IntentionInfo> recommendIntentionInfoList = new ArrayList<>();
        for (IntentionInfo intentionInfo : intentionInfoList) {
            if (intentionInfo.getIntention().equals(intention)) {
                // 主意图不需要处理
                continue;
            }
            recommendIntentionInfoList.add(intentionInfo);
        }
        return recommendIntentionService.getUnifiedRecommendIntentionFuture(dialogueId, dialogue, intention,
                recommendIntentionInfoList);
    }

    /**
     * 获取上下文推荐列表
     *
     * @return 上下文推荐列表
     */
    private List<ContextRecommendVO> getContextRecommendVOList() {
        // TODO 目前没有上下文推荐需求
        return new ArrayList<>();
    }

    /**
     * 获取内容推荐列表
     *
     * @param intentionVO 意图识别结果VO
     * @return 内容推荐列表
     */
    private List<ContentRecommendVO> getContentRecommendVOList(DialogueIntentionVO intentionVO) {
        if (ObjectUtil.isEmpty(intentionVO)) {
            return null;
        }

        List<ContentRecommendVO> recommendVOList = new ArrayList<>();
        /** 参数初始化 */
        // 【意图识别结果】内容推荐关键字列表
        List<DialogueIntentionVO.ContentRecommend> contentRecommendList = intentionVO.getContentRecommendList();
        if (CollUtil.isEmpty(contentRecommendList)) {
            log.info("【意图识别结果】内容推荐关键字列表-为空-DialogueRecommendServiceImpl-getContentRecommendVOList");
            return null;
        }
        // 【配置】每个类型推荐的数量
        Integer recommendQuantity = recommendProperties.getContentRecommendQuantity();
        // 【配置】keyword分隔符
        String split = recommendProperties.getContentRecommendSplit();
        // 【配置】对话内容推荐配置列表
        List<DialogueRecommendProperties.ContentRecommend> contentRecommendPropertiesList = recommendProperties
                .getContentList();

        /** 遍历-【意图识别结果】内容推荐关键字列表，处理数据 */
        // 把配置映射到的数据，添加到需要处理的内容推荐关键字列表
        List<DialogueIntentionVO.ContentRecommend> contentRecommendHandleList = new ArrayList<>();
        contentRecommendList.forEach(contentRecommend -> {
            // 【意图识别结果】内容推荐关键字
            String name = contentRecommend.getName();
            // 遍历-【配置】对话内容推荐配置列表
            for (DialogueRecommendProperties.ContentRecommend contentRecommendProperties : contentRecommendPropertiesList) {
                Integer contentRecommendType = contentRecommendProperties.getType();
                List<DialogueRecommendProperties.Keyword> keywordPropertiesList = contentRecommendProperties
                        .getKeywordList();
                boolean isBreak = false;
                // 遍历-【配置】内容推荐关键字列表
                for (DialogueRecommendProperties.Keyword keywordProperties : keywordPropertiesList) {
                    String keyword = keywordProperties.getKeyword();
                    Integer priority = keywordProperties.getPriority();
                    if (CharSequenceUtil.isEmpty(keyword)) {
                        continue;
                    }
                    List<String> keywordList = ListUtil.toList(keyword.split(split));
                    if (CollUtil.isNotEmpty(keywordList)) {
                        // 【意图识别结果】关键字在配置中，则添加进内容推荐map
                        if (keywordList.contains(name)) {
                            // set推荐类型
                            contentRecommend.setType(contentRecommendType);
                            // set优先级
                            contentRecommend.setPriority(priority);
                            // 添加到需要处理的内容推荐关键字列表
                            contentRecommendHandleList.add(contentRecommend);
                            isBreak = true;
                            // 退出循环-keywordPropertiesList
                            break;
                        }
                    }
                }
                if (isBreak) {
                    // 退出循环-contentRecommendPropertiesList
                    break;
                }
            }
        });

        /** 构建内容推荐map */
        if (CollUtil.isEmpty(contentRecommendHandleList)) {
            log.info("需要处理的内容推荐关键字列表-为空-DialogueRecommendServiceImpl-getContentRecommendVOList");
            return null;
        }
        // 需要处理的内容推荐关键字map【有序：使用LinkedHashMap】
        Map<Integer, List<DialogueIntentionVO.ContentRecommend>> recommendMap = new LinkedHashMap<>();
        // 先排序：优先级（从小到大，越小越优先），推荐类别（从小到大）
        // 后遍历
        contentRecommendHandleList.stream()
                .sorted(Comparator.comparing(DialogueIntentionVO.ContentRecommend::getPriority)
                        .thenComparing(DialogueIntentionVO.ContentRecommend::getType))
                .forEach(contentRecommend -> {
                    Integer contentRecommendType = contentRecommend.getType();
                    // 添加进需要处理的内容推荐关键字map
                    List<DialogueIntentionVO.ContentRecommend> contentRecommendListTemp = recommendMap
                            .get(contentRecommendType);
                    if (CollUtil.isEmpty(contentRecommendListTemp)) {
                        contentRecommendListTemp = new ArrayList<>();
                    }
                    // 控制每个类型推荐的数量【可配置】
                    if (contentRecommendListTemp.size() < recommendQuantity) {
                        contentRecommendListTemp.add(contentRecommend);
                        recommendMap.put(contentRecommendType, contentRecommendListTemp);
                    }
                });

        /** 构建内容推荐list */
        if (MapUtil.isEmpty(recommendMap)) {
            log.info("需要处理的内容推荐关键字map-为空-DialogueRecommendServiceImpl-getContentRecommendVOList");
            return null;
        }
        recommendMap.forEach((contentRecommendType, contentRecommendListValue) -> {
            // 获取内容推荐关键字的编码列表
            List<String> contentList = contentRecommendListValue.stream()
                    .map(DialogueIntentionVO.ContentRecommend::getCode).collect(Collectors.toList());
            // 构建内容推荐
            ContentRecommendVO contentRecommendVO = ContentRecommendVO.builder().type(contentRecommendType)
                    .contentList(contentList).build();
            // 添加到列表
            recommendVOList.add(contentRecommendVO);
        });
        return recommendVOList;
    }

    @Override
    public List<PromptRecommendVO> getPromptVOList(ChatAddHandleDTO handleDTO) {

        String intention = handleDTO.getIntentionCode();
        DialogueInputInfoDTO contentDTO = handleDTO.getReqDTO().getDialogueInput();
        DialogueIntentionDTO command = handleDTO.getReqDTO().getDialogueInput().getCommand();
        if (StringUtils.isEmpty(handleDTO.getReqDTO().getDialogueInput().getDialogue())
                && StringUtils.isEmpty(handleDTO.getInputInfoDTO().getPrompt())
                && (null == command || StringUtils.isEmpty(command.getCommand()))
                && handleDTO.isReqResourceOnlyDocumentSse()) {
            // 单文档-AI图书快速阅读不走提示词推荐
            return null;
        }

        if (null == contentDTO.getAttachment() || CollUtil.isEmpty(contentDTO.getAttachment().getAttachmentTypeList())) {
            return null;
        }

        Integer resourceType = contentDTO.getAttachment().getAttachmentTypeList().get(0);
        boolean isMailOrNoteOrDocument = ResourceTypeEnum.isMail(resourceType) || ResourceTypeEnum.isNote(resourceType)
                || ResourceTypeEnum.isDocument(resourceType) || ResourceTypeEnum.isAttachment(resourceType);
        // 获取推荐信息转换后返回
        if (isMailOrNoteOrDocument) {
            // 000和999和036不推荐（没匹配到准确意图）
            if (DialogueIntentionEnum.isTextIntention(intention) || DialogueIntentionEnum.isTextToolIntention(intention)) {
                return null;
            }
            return listPromptRecommends(handleDTO.getReqDTO().getSourceChannel());
        }
        return null;
    }

    /**
     * 从 Cache/DB 获取提示词
     *
     * @return 提示词指令推荐列表
     */
    private List<PromptRecommendVO> listPromptRecommends(String channel) {

        // 调取方法，随机获取3个提示词
        return promptRecommendHandleService.getPromptRecommendList(channel);
    }

    @Override
    public void removeTextIntentionRecommend(DialogueRecommendVO recommendVO) {
        if (ObjectUtil.isNotNull(recommendVO)) {
            List<IntentionRecommendVO> intentionList = recommendVO.getIntentionList();
            if (CollUtil.isNotEmpty(intentionList)) {
                // 搜索结果为空，使用大模型回答时，不需要返回【大模型推荐】
                List<IntentionRecommendVO> newIntentionList = intentionList.stream()
                        // 过滤：仅保留非文本意图-000的数据
                        .filter(result -> !result.getIntentionCommand().equals(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode()))
                        .collect(Collectors.toList());
                recommendVO.setIntentionList(newIntentionList);
            }
        }
    }

    /**
     * 获取文本工具-图片视觉识别推荐
     *
     * @param handleDTO the handle dto
     * @return {@link Future}
     * <AUTHOR>
     * @date 2025/6/18 12:02
     */
    private Future getTextToolAiPhotoVisionFuture(ChatAddHandleDTO handleDTO) {
        try {
            if (CollectionUtils.isEmpty(handleDTO.getImageLocalPaths())) {
                AIFileVO fileVo = yunDiskExternalService.getFileInfo(RequestContextHolder.getUserId(),
                        handleDTO.getInputInfoDTO().getAttachment().getFileList().get(0).getFileId());
                if (!(null != fileVo && StringUtils.isNotEmpty(fileVo.getContent()))) {
                    log.info("获取图片资源内容为空");
                    throw new YunAiBusinessException(YunAiCommonResultCode.FILE_UPLOAD_FAILED);
                }

                if (DialogueIntentionSubEnum.AI_PHOTO_RECOGNITION_SOLVE_PROBLEMS.getCode()
                        .equals(handleDTO.getSubIntentionCode())) {
                    // 1. 拍照解题
                    handleDTO.setVlmBusinessModelConfig(vlModelConfig.getAiPhotoSolveRecommendConfig());
                }
                if (DialogueIntentionSubEnum.AI_PHOTO_RECOGNITION_TRANSLATE.getCode().equals(handleDTO.getSubIntentionCode())) {
                    // 2. 拍照翻译
                    handleDTO.setVlmBusinessModelConfig(vlModelConfig.getAiPhotoTransRecommendConfig());
                }

                ImageProcessorUtil.ImageOutput imageOutput = textModelVisionSseHandlerImpl.urlToLocalPathAndProcess(
                        handleDTO.getVlmBusinessModelConfig(), fileVo.getContent(), fileVo.getFileSuffix());

                handleDTO.setImageLocalPaths(Collections.singletonList(imageOutput.getDesFile().getAbsolutePath()));
                TextModelVlReqDTO textModelVlReqDTO = new TextModelVlReqDTO();
                textModelVlReqDTO.setTaskId(String.valueOf(handleDTO.getDialogueId()));
                textModelVlReqDTO.setUserId(RequestContextHolder.getUserId());
                textModelVlReqDTO.setSessionId(String.valueOf(handleDTO.getSessionId()));
                return recommendIntentionService.getTextToolAiPhotoVisionFuture(handleDTO.getVlmBusinessModelConfig(),
                        imageOutput, textModelVlReqDTO);
            }
        } catch (Exception e) {
            log.error("获取文本工具-图片视觉识别推荐-异常", e);
            return null;
        }
        return null;
    }


}
