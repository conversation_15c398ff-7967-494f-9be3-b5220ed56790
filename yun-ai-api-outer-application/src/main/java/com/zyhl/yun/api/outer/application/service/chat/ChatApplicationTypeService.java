package com.zyhl.yun.api.outer.application.service.chat;

import com.zyhl.yun.api.outer.application.dto.ApplicationTypeListDTO;
import com.zyhl.yun.api.outer.domain.vo.ChatApplicationType;
import com.zyhl.yun.api.outer.domain.vo.TabChatApplicationType;

import java.util.List;

/**
 * 对话应用类型信息列表Service类
 *
 * <AUTHOR>
 * @version 2024年02月28日 15:30
 */

public interface ChatApplicationTypeService {

    /**
     * 查询对话应用类型信息list
     *
     * @param dto 查询条件
     * @return 对话应用类型信息list
     * @Author: WeiJingKun
     */
    List<TabChatApplicationType> typeList(ApplicationTypeListDTO dto);

    /**
     * 根据应用id获取对话应用类型信息
     *
     * @param applicationId 应用类型
     * @return 对话应用类型信息
     */
    ChatApplicationType getByApplicationId(String applicationId);

    /**
     * 根据应用id+应用类型，获取对话应用类型信息，优先读取redis缓存
     *
     * @param applicationId   应用类型
     * @param applicationType 应用类型
     * @return 对话应用类型信息
     */
    ChatApplicationType getChatApplicationTypeCache(String applicationId, String applicationType);

    /**
     * 获取应用关联id
     *
     * @param applicationId   应用id
     * @param applicationType 应用类型
     * @return 应用关联id
     */
    String getTypeRelationId(String applicationId, String applicationType);

    /**
     * 获取业务类型
     *
     * @param applicationType 应用类型
     * @param sourceChannel   来源渠道
     * @return 业务类型
     */
    String getBusinessType(String applicationType, String sourceChannel);

    /**
     * 根据应用id获取对话应用类型信息
     *
     * @param idList id集合
     * @return 对话应用类型信息
     */
    List<ChatApplicationType> getByAppList(List<String> idList);
}
