package com.zyhl.yun.api.outer.application.service.knowledge.upload;

import cn.hutool.core.collection.CollUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileAddReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileBatchImportReqDTO;
import com.zyhl.yun.api.outer.application.service.mq.KnowledgeDispatchTaskMqService;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeAddResultVO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeTaskResultVO;
import com.zyhl.yun.api.outer.config.KnowledgePersonalProperties;
import com.zyhl.yun.api.outer.domain.dto.CatalogConfigDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileTaskEntity;
import com.zyhl.yun.api.outer.enums.knowledge.FileTaskStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeTaskTypeEnum;
import com.zyhl.yun.api.outer.external.UserDriveExternalService;
import com.zyhl.yun.api.outer.repository.AlgorithmUserKnowledgeUploadRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileTaskRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 描述：知识库文件资源处理抽象类
 *
 * <AUTHOR> zhumaoxian  2025/3/21 17:19
 */
@Slf4j
public abstract class AbstractUserKnowledgeFileResourceHandle implements InitializingBean {

    /**
     * 存策略实现
     */
    private static final Map<Integer, AbstractUserKnowledgeFileResourceHandle> HANDLE_MAP = new ConcurrentHashMap<>();

    public static AbstractUserKnowledgeFileResourceHandle getByCode(Integer code) {
        return HANDLE_MAP.get(code);
    }

    protected void register(Integer code, AbstractUserKnowledgeFileResourceHandle handle) {
        HANDLE_MAP.put(code, handle);
    }

    /**
     * -------------------------------以上是策略工厂-----------------------------------
     */

    @Resource
    protected UserKnowledgeFileRepository userKnowledgeFileRepository;
    @Resource
    protected UserKnowledgeFileTaskRepository userKnowledgeFileTaskRepository;
    @Resource
    private KnowledgePersonalProperties knowledgePersonalProperties;
    @Resource
    private UserDriveExternalService userDriveExternalService;
    @Resource
    protected KnowledgeDispatchTaskMqService knowledgeDispatchTaskMqService;

    @Resource
    protected AlgorithmUserKnowledgeUploadRepository algorithmUserKnowledgeUploadRepository;

    @Resource
    protected UidGenerator uidGenerator;

    /**
     * 添加到知识库成功
     */
    protected final String FILE_EXIST_OTHER_LABEL = "添加到知识库成功";


    /**
     * 创建文件任务
     */
    protected void createTransTask(KnowledgeFileAddReqDTO dto, List<KnowledgeAddResultVO> resultList, List<String> fileIdsList, CatalogConfigDTO.CatalogConfigInfo configInfo) {
        // 任务对象
        UserKnowledgeFileTaskEntity taskEntity = new UserKnowledgeFileTaskEntity();
        taskEntity.setUserId(dto.getUserId());
        taskEntity.setTaskStatus(FileTaskStatusEnum.FINISH.getStatus());
        taskEntity.setTaskType(KnowledgeTaskTypeEnum.TRANSFER.getCode());
        taskEntity.setTaskRequest(JsonUtil.toJson(dto));
        taskEntity.setTaskResponse(JsonUtil.toJson(resultList));
        taskEntity.setExpireTime(knowledgePersonalProperties.getTransferExpireDate());

        // 发起转存任务
        if (CollUtil.isNotEmpty(fileIdsList)) {
            if (Objects.nonNull(configInfo)) {
                // 文件转存
                String thirdTaskId = userDriveExternalService.transTask(RequestContextHolder.getUserInfo(), configInfo.getCatalogId(), fileIdsList);
                taskEntity.setTaskStatus(FileTaskStatusEnum.PROCESSING.getStatus());
                taskEntity.setThirdTaskId(thirdTaskId);
            }
            taskEntity.setFileIds(String.join(",", fileIdsList));
            taskEntity.setFileNum(fileIdsList.size());
            if (!KnowledgeResourceTypeEnum.isPersonalFile(dto.getResourceType())) {
                taskEntity.setSuccessNum(fileIdsList.size());
            }
        }

        // 保存
        userKnowledgeFileTaskRepository.add(taskEntity);

    }


    /**
     * 添加知识库文件资源
     *
     * @param dto 请求参数
     * @return 添加结果
     * <AUTHOR>
     * @date 2025/3/21 17:19
     */
    public abstract KnowledgeTaskResultVO addV2(KnowledgeFileAddReqDTO dto);

}
