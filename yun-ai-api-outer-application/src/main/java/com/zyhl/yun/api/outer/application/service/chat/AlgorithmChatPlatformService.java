package com.zyhl.yun.api.outer.application.service.chat;

import com.zyhl.yun.api.outer.application.dto.AlgorithmChatPlatformAddDTO;
import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;

/**
 * <AUTHOR>
 */
public interface AlgorithmChatPlatformService {

	/**
	 * 第三方平台请求-会话信息提交接口-文生文
	 * 
	 * @param model 模型id
	 * @param dto
	 * @return
	 */
	AlgorithmChatAddVO submitAlgorithmChatText(String model, AlgorithmChatPlatformAddDTO dto);

}
