package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.github.pagehelper.PageInfo;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeBase;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.vo.common.BasePageInfoVO;
import lombok.Data;

import java.util.List;

/**
 * 知识库列表分页信息
 *
 * <AUTHOR>
 * @date 2025/04/16
 */
@Data
public class PersonalKnowledgeListPageInfoVO extends BasePageInfoVO {

    /**
     * 知识库文件列表
     */
    private List<KnowledgeBase> baseList;

    /**
     * 已使用空间大小，第一页返回
     */
    private Long usedSpace;

    /**
     * 总空间大小，第一页返回
     */
    private Long totalSpace;

    public PersonalKnowledgeListPageInfoVO setData(PageInfoDTO pageDTO, PageInfo<?> pageVO, List<KnowledgeBase> baseList) {
        this.baseList = baseList;

        if (pageDTO.isNeedTotal()) {
            // 需要总数才有分页
            this.totalCount = pageVO.getTotal();

            // 判断是否有下一页
            int nextPageCursor = Integer.parseInt(pageDTO.getPageCursor()) + pageDTO.getPageSize();
            if (nextPageCursor < pageVO.getTotal()) {
                this.nextPageCursor = String.valueOf(nextPageCursor);
            }
        }

        return this;
    }
}
