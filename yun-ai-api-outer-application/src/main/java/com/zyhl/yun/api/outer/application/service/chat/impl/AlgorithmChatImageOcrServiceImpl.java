package com.zyhl.yun.api.outer.application.service.chat.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.AIFileVO;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatImageOcrService;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.external.CmicOcrService;
import com.zyhl.yun.api.outer.external.YunDiskExternalService;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 会话输入图片ocr校验类
 *
 * <AUTHOR>
 * @data 2025/3/6 20:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlgorithmChatImageOcrServiceImpl implements AlgorithmChatImageOcrService {

	private final YunDiskExternalService yunDiskExternalService;
	private final CmicOcrService cmicOcrService;

	@Override
	public boolean chatTextIntentionToImageOcr(ChatAddInnerDTO innerDTO) {
		String fileId = innerDTO.getContent().getResourceId();
		if (StringUtils.isEmpty(fileId)) {
			return false;
		}

		String userId = RequestContextHolder.getUserId();
		// 1获取文件地址
		String fileUrl = null;
		AIFileVO file = null;
		try {
			file = yunDiskExternalService.getFileInfo(userId, fileId);
			if (null != file && StringUtils.isNotEmpty(file.getContent())) {
				fileUrl = file.getContent();
			}
		} catch (Exception e) {
			log.error("chatTextIntentionToImageOcr yunDiskExternalService.getFileInfo userId{}, fileId:{}, error:",
					userId, fileId, e);
		}
		String text = null;
		// 2调用ocr接口
		try {
			text = cmicOcrService.getImageOcr(fileUrl);
		} catch (Exception e) {
			log.error("chatTextIntentionToImageOcr cmicOcrService.getImageOcr userId{}, fileId:{}, error:", userId,
					fileId, e);
			throw new YunAiBusinessException(AiResultCode.CODE_10030330.getCode(), AiResultCode.CODE_10030330.getMsg());
		}

		// 3判断是否存在文本
		// 3.1判断是否存在文本-存在
		if (StringUtils.isNotEmpty(text)) {
			log.info("chatTextIntentionToImageOcr success userId{}, fileId:{}, text:{}", userId, fileId, text);
			// 设置图片ocr内容
			innerDTO.setResourceContent(text);
			return true;
		}
		// 3.2判断是否存在文本-不存在，返回false
		return false;
	}

}
