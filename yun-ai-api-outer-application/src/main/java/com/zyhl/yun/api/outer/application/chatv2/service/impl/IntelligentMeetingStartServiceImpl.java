package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import java.net.URLEncoder;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.service.IntelligentCommonService;
import com.zyhl.yun.api.outer.application.chatv2.service.IntelligentMeetingStartService;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.config.LeadCopyV2Properties;
import com.zyhl.yun.api.outer.config.LeadCopyV2Properties.Copy;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyV2VO;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 智能体对话-智能会议开始-服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-03 15:00
 */
@Slf4j
@Service
public class IntelligentMeetingStartServiceImpl implements IntelligentMeetingStartService {

	/**
	 * 笔记标题参数
	 */
	private static final String PARAM_OF_NOTE_TITLE = "noteTitle";

	@Resource
	private DataSaveService dataSaveService;
	@Resource
	private IntelligentCommonService intelligentCommonService;
	@Resource
	private ChatTextToolBusinessConfig chatTextToolBusinessConfig;
	@Resource
	private LeadCopyV2Properties leadCopyV2Properties;

	@SuppressWarnings("deprecation")
	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {

		IntentionInfo mainIntention = DialogueIntentionVO.getMainIntention(handleDTO.getIntentionVO());
		// 聊天添加响应对象
		ChatAddRespVO respVO = handleDTO.getRespVO();

		// 获取意图枚举
		DialogueIntentionEnum intentionEnum = DialogueIntentionEnum.getByCode(mainIntention.getIntention());

		// 获取copy->特殊配置instruction + "-intelligent"
		Copy copy = leadCopyV2Properties.getByInstruction(intentionEnum.getInstruction() + "-intelligent");

		if (null == copy) {
			log.info("lead copy找不到配置");
			return true;
		}
		/**
		 * 笔记标题
		 */
		String noteTitle = null;
		// 会议通知邮件信息
		MailInfoVO mailInfo = null;
		AlgorithmChatContentEntity lastMailDialogueInfo = intelligentCommonService.getLastIntentionDialogue(
				handleDTO.getReqDTO().getUserId(), handleDTO.getSessionId(), String.valueOf(handleDTO.getDialogueId()));
		if (null != lastMailDialogueInfo) {
			mailInfo = intelligentCommonService.getMailInfoResult(lastMailDialogueInfo);
		}
		if (null != mailInfo) {
			noteTitle = chatTextToolBusinessConfig.getIntelligentMeeting().removeTitlePrefix(mailInfo.getTitle());
			log.info("查找到笔记标题 noteTitle:{}", noteTitle);
		}

		// copy结果
		Copy copyResult = new Copy();
		BeanUtil.copyProperties(copy, copyResult);
		if (StringUtils.isNotEmpty(noteTitle) && copyResult.getLinkURL().contains(PARAM_OF_NOTE_TITLE)) {
			copyResult.setLinkURL(copyResult.getLinkURL() + URLEncoder.encode(noteTitle));
		}

		// 设置lead copy
		respVO.setLeadCopy(LeadCopyV2VO.getLeadCopyVo(copyResult, intentionEnum));

		// 保存leadCopy到hbase【LeadCopy，type=1、2、3、4】
		dataSaveService.saveTextResult(handleDTO, "", "");

		// 保存tidb
		dataSaveService.addSuccess(handleDTO, OutContentTypeEnum.TEXT);

		// 流式响应
		handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(respVO));

		return false;
	}

}
