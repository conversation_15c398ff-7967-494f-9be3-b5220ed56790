package com.zyhl.yun.api.outer.application.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * <p>
 * 阿里AI-PPT模板入参
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class PptTemplateDTO extends BaseDTO {

    /**
     * 来源渠道
     */
    @NotEmpty(message = "sourceChannel不能为空")
    private String sourceChannel;
}
