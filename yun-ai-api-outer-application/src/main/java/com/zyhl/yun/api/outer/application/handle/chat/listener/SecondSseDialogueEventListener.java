package com.zyhl.yun.api.outer.application.handle.chat.listener;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.hcy.plugin.logger.util.LogUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.event.TextModelStreamEventListener;
import com.zyhl.hcy.yun.ai.common.model.api.client.vo.TextModelBaseVo;
import com.zyhl.hcy.yun.ai.common.model.api.enums.ApiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.vo.CheckResultVO;
import com.zyhl.yun.api.outer.application.dto.SecondStreamChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.SecondStreamChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.dto.TextModelDTO;
import com.zyhl.yun.api.outer.application.vo.SecondStreamChatAddVO;
import com.zyhl.yun.api.outer.config.SecondStreamChatAddProperties;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.domainservice.CheckSystemDomainService;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.external.CheckSystemExternalService;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.sse.EventSource;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.MDC;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.Future;

/**
 * 二次流式对话事件监听，多实例
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
public class SecondSseDialogueEventListener extends TextModelStreamEventListener {

    private SecondStreamChatAddProperties flowTypeProperties;
    private RedisOperateRepository redisOperateRepository;
    private CheckSystemDomainService checkSystemDomainService;
    private AiTextResultRepository aiTextResultRepository;

    public SecondSseDialogueEventListener(SecondStreamChatAddInnerDTO innerDTO) {
        flowTypeProperties = SpringUtil.getBean(SecondStreamChatAddProperties.class);
        checkSystemDomainService = SpringUtil.getBean(CheckSystemDomainService.class);
        aiTextResultRepository = SpringUtil.getBean(AiTextResultRepository.class);
        redisOperateRepository = SpringUtil.getBean(RedisOperateRepository.class);

        init(innerDTO);
    }

    /**
     * 全量内容（送审使用全量，保存数据库使用全量）
     */
    private final StringBuilder allMsg = new StringBuilder();
    /**
     * 增量内容（输出使用增量）
     */
    private final StringBuilder addMsg = new StringBuilder();
    /**
     * 大模型token标识
     */
    private Integer outputTokens = 0;
    /**
     * 结束对话标识
     */
    private boolean finishFlag = false;


    private String userId;
    private String phone = RequestContextHolder.getPhoneNumber();
    private Long dialogId;
    private SecondStreamChatAddDTO reqParams;
    private AiTextResultRespParameters respParameters;
    private SseEmitter sseEmitter = null;
    private SecondStreamChatAddVO resVO = null;
    private List<Future<Object>> futures;

    private TextModelDTO textDto;
    /**
     * a标签
     */
    private static final String A_TAG_START = "<a";
    private static final String A_TAG_END = "</a>";
    private boolean aTagStart = false;
    private boolean aTagEnd = false;

    /**
     * 初始化
     *
     * @param innerDTO 对话请求参数
     */
    public void init(SecondStreamChatAddInnerDTO innerDTO) {
        reqParams = innerDTO.getReqParams();
        respParameters = innerDTO.getRespParameters();
        sseEmitter = innerDTO.getSseEmitter();

        // 先初始化为提取内容为空
        if (respParameters.getMailInfo() == null) {
            respParameters.setMailInfo(new MailInfoVO());
        } else {
            respParameters.getMailInfo().setContent(null);
        }

        resVO = SecondStreamChatAddVO.builder()
                .dialogueId(reqParams.getDialogueId())
                .commands(innerDTO.getIntentionCode())
                .mailInfo(respParameters.getMailInfo())
                .build();

        userId = reqParams.getUserId();
        dialogId = Long.valueOf(reqParams.getDialogueId());

        textDto = new TextModelDTO(innerDTO, innerDTO.getContent(), "");
    }

    @Override
    public boolean onEvent(TextModelBaseVo response) {
        MDC.put(LogConstants.ACCOUNT, phone);
        try {
            // 停止对话
            if (finishFlag) {
                log.info("【二次流式对话】对话已结束，对话id：{}", dialogId);
                return true;
            }
            if (redisOperateRepository.getStopDialogue(dialogId)) {
                // 对话结束
                log.info("【二次流式对话】对话已停止，对话id：{}", dialogId);
                return dialogueStop();
            }

            // 最后一次事件（不同模型判断不一样）
            boolean lastEvent = Boolean.TRUE.equals(response.getIsLast()) || outputTokens.equals(response.getOutputTokens());
            outputTokens = response.getOutputTokens() == null ? 0 : response.getOutputTokens();

            addMsg.append(CharSequenceUtil.nullToDefault(response.getText(), StringUtils.EMPTY));
            if (!lastEvent && (addMsg.length() < flowTypeProperties.getAuditSize()) && !aTagStart) {
                //未达到送审字数
                return true;
            }

            // 控制返回完整a标签
            if (addMsg.toString().toLowerCase().contains(A_TAG_START)) {
                // 存在a标签开始
                aTagStart = true;
            }
            if (aTagStart && addMsg.toString().toLowerCase().contains(A_TAG_END)) {
                // 存在a标签结束
                aTagEnd = true;
            }
            if (!lastEvent && aTagStart && !aTagEnd) {
                // 存在a标签未结束，继续读取
                return true;
            }
            aTagStart = false;
            aTagEnd = false;

            if (CharSequenceUtil.isNotEmpty(addMsg)) {
                // 执行文本内容送审
                allMsg.append(addMsg);
                CheckResultVO checkResult = checkSystemDomainService.checkLocalAndPlatform(dialogId, userId, String.valueOf(allMsg));
                if (checkResult == null || !checkResult.getSuccess()) {
                    return dialogueFail(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
                }

                // 没有提取到邮件内容不输出
                String addMsgStr = addMsg.toString();
                if (!addMsgStr.contains(flowTypeProperties.getDefaultEmailContent())) {
                    resVO.getMailInfo().setContent(addMsg.toString());
                }

                // 发送SseEmitter消息
                send(BaseResult.success(resVO));

                // 发送消息后参数处理
                addMsg.setLength(0);
            }

            // 最后一次 更新会话任务状态
            if (lastEvent) {
                // 对话结束
                return dialogueSuccess();
            }
        } catch (YunAiBusinessException e) {

            log.error("【二次流式对话】对话异常，流式响应信息：{}", JsonUtil.toJson(response), e);
            return dialogueFail(AiResultCode.getByCodeOrMsg(e.getCode(), e.getMessage()));
        } catch (ClientAbortException e) {

            // 对话终止情况 （一般情况下是前端杀进程会出现）
            log.info("【二次流式对话】对话中断，对话id：{}，输出信息：{}", dialogId, allMsg);
            return dialogueSuccess();
        } catch (Exception e) {

            log.error("【二次流式对话】对话异常，流式响应信息：{}", JsonUtil.toJson(response), e);
            return dialogueFail(AiResultCode.CODE_10000101);
        }

        return true;
    }

    @Override
    public void onClosed(EventSource eventSource) {
        MDC.put(LogConstants.ACCOUNT, phone);
        log.info("【二次流式对话】对话关闭，输出信息，\nallMsg：{}", allMsg);

        complete();
    }

    @Override
    public void onFailure(Throwable e, int code, String body) {
        MDC.put(LogConstants.ACCOUNT, phone);
        log.error("【二次流式对话】对话失败，code：{}，body：{}，allMsg：{}", code, body, allMsg, e);

        if (finishFlag) {
            return;
        }
        if(ApiCommonResultCode.SENSITIVE_ERROR.getResultCode().equals(String.valueOf(code))) {
        	//强制转换，统一错误码
        	e = new YunAiBusinessException(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
        }
        if (e instanceof YunAiBusinessException) {
            YunAiBusinessException ex = (YunAiBusinessException) e;
            dialogueFail(AiResultCode.getByCodeOrMsg(ex.getCode(), ex.getMessage()));
        } else {
            dialogueFail(ResultCodeEnum.ERROR_SERVER_INTERNAL);
        }
    }

    /**
     * 处理成功的情况
     *
     * @return boolean
     */
    public boolean dialogueSuccess() {
        // sse结束
        complete();

        if (respParameters.getMailInfo() == null) {
            return true;
        }

        // 更新hbase会话内容
        respParameters.getMailInfo().setContent(String.valueOf(allMsg));
        aiTextResultRepository.update(userId, dialogId, null, respParameters);

        return true;
    }

    /**
     * 处理停止对话的情况，前端调停止接口修改状态，这里只需要更新输出审核状态
     *
     * @return boolean
     */
    public boolean dialogueStop() {
        // sse结束
        complete();

        // 更新hbase会话内容
        respParameters.getMailInfo().setContent(String.valueOf(allMsg));
        aiTextResultRepository.update(userId, dialogId, null, respParameters);

        return true;
    }

    /**
     * 处理失败的情况
     *
     * @param aiResultCode 结果
     * @return boolean
     */
    public boolean dialogueFail(AiResultCode aiResultCode) {
        return dialogueFail(aiResultCode.getCode(), aiResultCode.getMsg());
    }

    public boolean dialogueFail(AbstractResultCode aiResultCode) {
        return dialogueFail(aiResultCode.getResultCode(), aiResultCode.getResultMsg());
    }

    public boolean dialogueFail(String resultCode, String resultMsg) {
        // 发送SseEmitter消息并关闭连接
        try {
            send(BaseResult.error(resultCode, resultMsg));
        } catch (Exception e) {
            log.error("二次流式对话发送失败结果异常，异常信息：{}", e.getMessage(), e);
        }
        complete();

        // 更新hbase会话内容
        respParameters.getMailInfo().setContent(String.valueOf(allMsg));
        aiTextResultRepository.update(userId, dialogId, null, respParameters);

        return true;
    }

    private void send(BaseResult<?> result) throws IOException {
        // 调用send发送消息
        if (finishFlag) {
            return;
        }
        sseEmitter.send(SseEmitter.event()
                .id(String.valueOf(System.currentTimeMillis()))
                .reconnectTime(flowTypeProperties.getReconnectTimeMillis())
                .name("AI-second-chat-sse")
                .data(JsonUtil.toJson(result))
        );

        //接口日志输出
        LogUtil.setInterfaceOutput(result);
    }

    private void complete() {
        if (finishFlag) {
            return;
        }
        finishFlag = true;
        // 关闭连接
        try {
            log.info("【二次流式对话】关闭流对象，对话id：{}", dialogId);
            sseEmitter.complete();
        } catch (Exception e) {
            if (!(e instanceof IllegalStateException)) {
                log.error("流式对象关闭异常，异常信息：{}", e.getMessage(), e);
            }
        }
    }
}
