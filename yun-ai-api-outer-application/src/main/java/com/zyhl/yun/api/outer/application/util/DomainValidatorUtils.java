package com.zyhl.yun.api.outer.application.util;

import com.google.common.net.InternetDomainName;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.net.URL;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 域名检测工具类
 * @date 2025/5/20 10:55
 */
public class DomainValidatorUtils {

    /**
     * 校验域名是否在白名单中（包含子域名）
     *
     * @param url       域名地址
     * @param whiteDomains 白名单域名列表
     * @return 是否校验通过
     */
    public static boolean isAllowed(String url, List<String> whiteDomains) {
        if (StringUtils.isBlank(url) || CollectionUtils.isEmpty(whiteDomains)) {
        	return false;
        }
        try {
            // 从 url 中获取域名对象
            InternetDomainName domain = InternetDomainName.from(new URL(url).getHost());
            // 获取顶级私有域名
            String topPrivateDomain = domain.topPrivateDomain().toString();
            return whiteDomains.contains(domain.toString()) || whiteDomains.contains(topPrivateDomain);
        } catch (Exception e) {
            return false;
        }
    }

}