package com.zyhl.yun.api.outer.application.dto;

import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import java.io.Serializable;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description}
 * <p>
 *
 * <AUTHOR>
 * @since 4月15 2025
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class ReadTaskReqDTO  extends BaseDTO implements Serializable {
  /**
   * 来源渠道
   */
  @NotEmpty(message = "sourceChannel不能为空")
  private String sourceChannel;

  /**
   * 分页信息
   */
  private PageInfoDTO pageInfo;
}
