package com.zyhl.yun.api.outer.application.service.mq.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.chinamobile.tuxedo.sdk.api.Message;
import com.chinamobile.tuxedo.sdk.api.Producer;
import com.chinamobile.tuxedo.sdk.api.SendResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.logger.annotation.LogAnnotation;
import com.zyhl.hcy.plugin.logger.enums.LogType;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.rocketmq.message.MessageDTO;
import com.zyhl.hcy.yun.ai.common.base.utils.MessageUtil;
import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.PojoConverter;
import com.zyhl.yun.api.outer.application.enums.KnowledgeTaskQuantityTypeEnum;
import com.zyhl.yun.api.outer.application.service.mq.KnowledgeDispatchTaskMqService;
import com.zyhl.yun.api.outer.config.RocketmqProducerProperties;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiRegisterEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeDispatchTaskMqEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.enums.BusinessSourceEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.mq.MetaTaskTypeEnum;
import com.zyhl.yun.api.outer.repository.AlgorithmAiRegisterRepository;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * className: KnowledgeDispatchTaskMqServiceImpl
 * description: 个人知识库 - 笔记/邮件 知识库向量化提取接口实现类
 *
 * <AUTHOR>
 * @date 2025/2/10
 */
@Slf4j
@Service
public class KnowledgeDispatchTaskMqServiceImpl implements KnowledgeDispatchTaskMqService {

    @Resource
    private RocketmqProducerProperties rocketmqProducerProperties;

    @Resource
    private PojoConverter pojoConverter;

    @Resource(name = "knowledgeVectorDispatchProducer")
    private Producer knowledgeVectorDispatchProducer;

    @Resource
    private AlgorithmAiRegisterRepository registerRepository;

    @Override
    @LogAnnotation(logType = LogType.MQ)
    public void sendMq(List<UserKnowledgeDispatchTaskMqEntity> entityList) {

        if (CollectionUtils.isEmpty(entityList)) {
            log.info("发送个人知识库文档向量化消息 个人知识库文件表为空，不需要发送");
            return;
        }

        MessageDTO<String> baseMqDto = new MessageDTO<>(JSON.toJSONString(entityList));
        String jsonStr = JSON.toJSONString(baseMqDto);

        Message msg = MessageUtil.createMessage();
        msg.setBody(jsonStr.getBytes());
        msg.setTopic(rocketmqProducerProperties.getPersonalKnowledgeDispatchTask().getTopic());
        msg.setTag(rocketmqProducerProperties.getPersonalKnowledgeDispatchTask().getTag());

        long startTime = System.currentTimeMillis();
        try {
            SendResult sendResult = knowledgeVectorDispatchProducer.send(msg);
            log.info("知识库资源向量化任务mq发送成功，消息体：{}，耗时：{}，发送结果：{}", JsonUtil.toJson(entityList), System.currentTimeMillis() - startTime, JsonUtil.toJson(sendResult));
        } catch (Exception e) {
            log.error("知识库资源向量化任务mq发送失败，消息体：{}，耗时：{}，异常信息：{}", JsonUtil.toJson(entityList), System.currentTimeMillis() - startTime, e.getMessage(), e);
            throw new YunAiBusinessException(ResultCodeEnum.MQ_SEND_EXCEPTION);
        }
    }


    @Override
    public void sendTaskMq(List<UserKnowledgeFileEntity> list) {
        AlgorithmAiRegisterEntity registerEntity = registerRepository.queryByUserId(list.get(0).getUserId(), BusinessSourceEnum.ASSISTANT.getCode());

        // 组装
        List<UserKnowledgeDispatchTaskMqEntity> taskEntityList = new ArrayList<>();
        list.forEach(item -> {
            // 任务组装
            UserKnowledgeDispatchTaskMqEntity taskEntity = new UserKnowledgeDispatchTaskMqEntity();
            taskEntity.setTaskType(KnowledgeTaskQuantityTypeEnum.SINGLE.getCode());
            // PaasCode，此处没有，给固定值作为参数传递，兼容db的design
            taskEntity.setPaasCode(StrUtil.emptyToDefault(item.getPaasCode(), "0"));
            taskEntity.setRefresh(true);
            if (item.getFromResourceType().equals(KnowledgeResourceTypeEnum.NOTE_SYNC.getCode())){
                taskEntity.setId(String.valueOf(item.getId()));
            }
            taskEntity.setFileId(item.getFileId());
            taskEntity.setUserId(item.getUserId());
            taskEntity.setOwnerId(item.getOwnerId());
            taskEntity.setResourceType(item.getFromResourceType());
            taskEntity.setOwnerType(item.getOwnerType());
            taskEntity.setMetaTaskType(MetaTaskTypeEnum.DOC_PERSONAL_KNOWLEDGE_VECTOR.getCode());
            taskEntity.setAlgorithmGroupCode(registerEntity != null ? String.valueOf(registerEntity.getAlgorithmGroupCode()) : "");
            taskEntityList.add(taskEntity);
        });

        // 发送消息
        sendMq(taskEntityList);
    }

    @Override
    public void sendTaskMqV2(List<UserKnowledgeFileEntity> list) {
        AlgorithmAiRegisterEntity registerEntity = registerRepository.queryByUserId(list.get(0).getUserId(), BusinessSourceEnum.ASSISTANT.getCode());
        List<UserKnowledgeDispatchTaskMqEntity> taskEntityList = pojoConverter.toUserKnowledgeDispatchTaskMqEntityList(list);
        taskEntityList.forEach(taskEntity -> {
            taskEntity.setTaskType(KnowledgeTaskQuantityTypeEnum.SINGLE.getCode());
            taskEntity.setRefresh(true);
            taskEntity.setMetaTaskType(MetaTaskTypeEnum.DOC_PERSONAL_KNOWLEDGE_VECTOR.getCode());
            taskEntity.setAlgorithmGroupCode(registerEntity != null ? String.valueOf(registerEntity.getAlgorithmGroupCode()) : "");
        });
        // 发送消息
        sendMq(taskEntityList);
    }

}
