package com.zyhl.yun.api.outer.application.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * AIPPT结果通知事件接口请求DTO
 *
 * <AUTHOR> Assistant
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AiPptResultInformReqDTO extends BaseDTO {

    /**
     * 渠道来源，参考ChannelId 枚举值
     */
    @NotEmpty(message = "sourceChannel不能为空")
    private String sourceChannel;

    /**
     * 用户Id，默认从token获取，第三方平台调用时必填
     */
    private String userId;

    /**
     * 大纲对话ID
     */
    @NotEmpty(message = "contentDialogueId不能为空")
    private String contentDialogueId;

    /**
     * 任务ID
     */
    @NotEmpty(message = "taskId不能为空")
    private String taskId;

    /**
     * 厂商作品ID
     */
    @NotEmpty(message = "designId不能为空")
    private String designId;

    /**
     * PPT作品名称
     */
    @NotEmpty(message = "pptName不能为空")
    private String pptName;

    /**
     * PPT大纲（可选）
     */
    private String content;
}