package com.zyhl.yun.api.outer.application.chatv2.service;

import java.util.List;
import java.util.concurrent.Future;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.PromptRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.ToolRecommendVO;

/**
 * 对话结果推荐接口
 *
 * <AUTHOR>
 */
public interface ChatDialogueRecommendService {

	/**
	 * 获取对话结果推荐对象
	 *
	 * @param handleDTO 接口内部传输参数
	 * @return 对话结果推荐对象
	 */
	DialogueRecommendVO getDialogueRecommendVO(ChatAddHandleDTO handleDTO);
	
	/**
	 * 获取中部推荐对象
	 * @param handleDTO
	 * @return
	 */
	DialogueRecommendVO getMiddleRecommendVo(ChatAddHandleDTO handleDTO);

	/**
	 * 获取文本工具推荐列表
	 * @param handleDTO
	 * @return
	 */
	public List<ToolRecommendVO> getToolRecommendListVo(ChatAddHandleDTO handleDTO);
	
	/**
	 * 
	 * 获取文本工具推荐列表（需要过滤提示词）
	 * 
	 * @param handleDTO
	 * @param filterPrompt 过滤提示词
	 * @return
	 */
	List<ToolRecommendVO> getToolRecommendListVo(ChatAddHandleDTO handleDTO, String filterPrompt);
	
	/**
	 * 快速阅读中部推荐列表
	 * @param handleDTO
	 * @return
	 */
	List<ToolRecommendVO> getSpeedReadMiddleRecommendListVo(ChatAddHandleDTO handleDTO);
	
	/**
	 * 设置意图推荐并发结果集
	 *
	 * @param dialogueId          对话id
	 * @param dialogueRecommendVO 对话推荐
	 * @param futures             异步线程列表
	 */
	void setFuturesResult(Long dialogueId, DialogueRecommendVO dialogueRecommendVO, List<Future<Object>> futures);

	/**
	 * 获取提示词指令推荐列表
	 *
	 * @param handleDTO 接口内部传输参数
	 * @return 意图推荐
	 */
	List<PromptRecommendVO> getPromptVOList(ChatAddHandleDTO handleDTO);

	/**
	 * 使用大模型回答时，不需要返回【大模型推荐】
	 * @Author: WeiJingKun
	 *
	 * @param recommendVO 对话结果推荐对象VO
	 */
	void removeTextIntentionRecommend(DialogueRecommendVO recommendVO);

}
