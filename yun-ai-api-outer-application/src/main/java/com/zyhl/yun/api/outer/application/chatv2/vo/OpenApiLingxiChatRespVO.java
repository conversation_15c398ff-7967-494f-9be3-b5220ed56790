package com.zyhl.yun.api.outer.application.chatv2.vo;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.constants.OpenApiLingxiChatConstants;
import com.zyhl.yun.api.outer.domain.valueobject.AiFunctionResult;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueIntentionOutput;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.util.UrlEncoderUtil;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * openapi lingxi 响应结果
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Slf4j
@Data
public class OpenApiLingxiChatRespVO {
	private String reqId;
	private String model;
	private OpenApiLingxiChatSession session;
	private List<OpenApiLingxiChatChoice> choices;

	@Data
	public static class OpenApiLingxiChatSession {
		private String sessionId;
		private String attributes; // 注意：这里是字符串形式的JSON，不是解析后的对象
	}

	@Data
	public static class OpenApiLingxiChatChoice {
		private OpenApiLingxiChatDelta delta;
	}

	@Data
	public static class OpenApiLingxiChatDelta {
		@JsonProperty("reasoning_content")
		private OpenApiLingxiChatReasoningContent reasoningContent;
		private OpenApiLingxiChatContent content;
		private List<OpenApiLingxiChatGenContent> recommendPrompts;
	}

	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	public static class OpenApiLingxiChatReasoningContent {
		private String text;
	}

	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	public static class OpenApiLingxiChatContent {
		private String type;
		private OpenApiLingxiChatData data;
	}

	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	public static class OpenApiLingxiChatGenContent {
		private String text;
	}

	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	public static class OpenApiLingxiChatData {
		/**
		 * 大模型使用
		 */
		private String text;

		/**
		 * 模板使用-模板id
		 */
		private String templateId;
		/**
		 * 模板使用-模板json数据
		 */
		private String content;

		public OpenApiLingxiChatData(String text) {
			this.text = text;
		}

		public OpenApiLingxiChatData(String templateId, String content) {
			this.templateId = templateId;
			this.content = content;
		}

	}

	/**
	 * 通用响应
	 * 
	 * @param handleDTO     handle入参
	 * @param chatAddRespVO 对话响应入参
	 * @return 响应信息
	 */
	public static OpenApiLingxiChatRespVO getOpenApiLingxiChatResp(ChatAddHandleDTO handleDTO,
			ChatAddRespVO chatAddRespVO) {
		OpenApiLingxiChatRespVO vo = initResp(handleDTO);
		OpenApiLingxiChatChoice choice = new OpenApiLingxiChatChoice();
		OpenApiLingxiChatDelta delta = new OpenApiLingxiChatDelta();
		if (Objects.equals(FlowResultTypeEnum.REASONING_RESULT.getType(),
				chatAddRespVO.getFlowResult().getResultType())) {
			// 思维结果
			String title = chatAddRespVO.getFlowResult().getTitle();
			String thisReasoningContent = (null != title ? ("**" + title + "**\n") : "")
					+ chatAddRespVO.getFlowResult().getOutContent() + "\n";
			delta.setReasoningContent(new OpenApiLingxiChatReasoningContent(thisReasoningContent));
		} else if (Objects.equals(FlowResultTypeEnum.TEXT_MODEL.getType(),
				chatAddRespVO.getFlowResult().getResultType())) {
			// 大模型结果
			String title = chatAddRespVO.getFlowResult().getTitle();
			String content = (null != title ? ("**" + title + "**\n") : "")
					+ chatAddRespVO.getFlowResult().getOutContent();
			OpenApiLingxiChatContent thisContent = new OpenApiLingxiChatContent();
			// 类型1
			thisContent.setType(OpenApiLingxiChatConstants.TYPE_OF_TEXT_MODEL);
			thisContent.setData(new OpenApiLingxiChatData(content));
			delta.setContent(thisContent);
		} else if (Objects.equals(FlowResultTypeEnum.MAIL.getType(), chatAddRespVO.getFlowResult().getResultType())) {
			// 邮件结果，设置为大模型结果，空字符
			String content = StringUtils.EMPTY;
			OpenApiLingxiChatContent thisContent = new OpenApiLingxiChatContent();
			// 类型1
			thisContent.setType(OpenApiLingxiChatConstants.TYPE_OF_TEXT_MODEL);
			thisContent.setData(new OpenApiLingxiChatData(content));
			delta.setContent(thisContent);
		} else if (Objects.equals(FlowResultTypeEnum.TOOL_RESULT.getType(),
				chatAddRespVO.getFlowResult().getResultType())) {
			IntentionInfo mainIntention = DialogueIntentionVO.getMainIntention(handleDTO.getIntentionVO());
			AiFunctionResult aiFunctionResult = chatAddRespVO.getFlowResult().getAiFunctionResult();
			if (null != aiFunctionResult && DialogueIntentionEnum.isTextToolIntention(mainIntention.getIntention())
					&& DialogueIntentionSubEnum.isAiPpt(mainIntention.getSubIntention())) {
				// tool结果，设置为大模型结果，空字符
				String cardJson = JSONUtil.toJsonStr(new OpenApiLingxiCardMessage("正在飞速完成创作", "您可以点击查看进度", "查看PPT",
						aiFunctionResult.getPreviewUrl()));
				OpenApiLingxiChatData cardChatData = new OpenApiLingxiChatData(
						OpenApiLingxiChatConstants.TEMPLATE_ID_OF_CARD_SIMPLE, cardJson);
				return OpenApiLingxiChatRespVO.getOpenApiLingxiChatResp(handleDTO,
						OpenApiLingxiChatConstants.TYPE_OF_CARD, cardChatData);
			} else {
				// 其他场景，暂时返回空数据
				String content = StringUtils.EMPTY;
				OpenApiLingxiChatContent thisContent = new OpenApiLingxiChatContent();
				// 类型1
				thisContent.setType(OpenApiLingxiChatConstants.TYPE_OF_TEXT_MODEL);
				thisContent.setData(new OpenApiLingxiChatData(content));
				delta.setContent(thisContent);
			}
		}
		choice.setDelta(delta);
		vo.setChoices(Collections.singletonList(choice));
		return vo;
	}

	/**
	 * 追尾响应
	 * 
	 * @param handleDTO               handle入参
	 * @param dialogueIntentionOutput 意图
	 * @param recommendPrompts        追尾
	 * @return 响应信息
	 */
	public static OpenApiLingxiChatRespVO getOpenApiLingxiChatResp(ChatAddHandleDTO handleDTO,
			DialogueIntentionOutput dialogueIntentionOutput, List<OpenApiLingxiChatGenContent> recommendPrompts) {
		return getOpenApiLingxiChatResp(handleDTO, dialogueIntentionOutput, recommendPrompts, null);
	}

	/**
	 * 追尾响应
	 * 
	 * @param handleDTO               handle入参
	 * @param dialogueIntentionOutput 意图
	 * @param recommendPrompts        追尾
	 * @param appendAttrMap           属性
	 * @return 响应信息
	 */
	public static OpenApiLingxiChatRespVO getOpenApiLingxiChatResp(ChatAddHandleDTO handleDTO,
			DialogueIntentionOutput dialogueIntentionOutput, List<OpenApiLingxiChatGenContent> recommendPrompts,
			Map<String, String> appendAttrMap) {
		OpenApiLingxiChatRespVO vo = initResp(handleDTO, dialogueIntentionOutput, appendAttrMap);
		OpenApiLingxiChatChoice choice = new OpenApiLingxiChatChoice();
		OpenApiLingxiChatDelta delta = new OpenApiLingxiChatDelta();
		delta.setRecommendPrompts(recommendPrompts);
		choice.setDelta(delta);
		vo.setChoices(Collections.singletonList(choice));
		return vo;
	}

	/**
	 * 普通响应
	 * 
	 * @param handleDTO handle入参
	 * @param type      类型
	 * @param chatData  对话入参
	 * @return 响应信息
	 */
	public static OpenApiLingxiChatRespVO getOpenApiLingxiChatResp(ChatAddHandleDTO handleDTO, String type,
			OpenApiLingxiChatData chatData) {
		OpenApiLingxiChatRespVO vo = initResp(handleDTO, null, null);
		OpenApiLingxiChatChoice choice = new OpenApiLingxiChatChoice();
		OpenApiLingxiChatDelta delta = new OpenApiLingxiChatDelta();
		delta.setContent(new OpenApiLingxiChatContent(type, chatData));
		choice.setDelta(delta);
		vo.setChoices(Collections.singletonList(choice));
		return vo;
	}

	/**
	 * 简易导航追问卡片响应
	 * 
	 * @param handleDTO     handle入参
	 * @param cardMenuReply 卡片入参
	 * @return 响应信息
	 */
	public static OpenApiLingxiChatRespVO getOpenApiLingxiChatReplyResp(ChatAddHandleDTO handleDTO,
			OpenApiLingxiCardReplyMessage cardMenuReplyMessage) {
		OpenApiLingxiChatData chatData = new OpenApiLingxiChatData(OpenApiLingxiChatConstants.TEMPLATE_ID_OF_CARD_REPLY,
				JSONUtil.toJsonStr(cardMenuReplyMessage));
		OpenApiLingxiChatRespVO vo = initResp(handleDTO, null, null);
		OpenApiLingxiChatChoice choice = new OpenApiLingxiChatChoice();
		OpenApiLingxiChatDelta delta = new OpenApiLingxiChatDelta();
		delta.setContent(new OpenApiLingxiChatContent(OpenApiLingxiChatConstants.TYPE_OF_CARD, chatData));
		choice.setDelta(delta);
		vo.setChoices(Collections.singletonList(choice));
		return vo;
	}

	/**
	 * 简易导航追问卡片响应
	 * 
	 * @param handleDTO     handle入参
	 * @param cardMenuReply 卡片入参
	 * @return 响应信息
	 */
	public static OpenApiLingxiChatRespVO getOpenApiLingxiChatReplyErrorResp(ChatAddHandleDTO handleDTO,
			OpenApiLingxiCardReplyMessage cardMenuReplyMessage) {
		OpenApiLingxiChatData chatData = new OpenApiLingxiChatData(OpenApiLingxiChatConstants.TEMPLATE_ID_OF_CARD_REPLY,
				JSONUtil.toJsonStr(cardMenuReplyMessage));
		OpenApiLingxiChatRespVO vo = initErrorResp(handleDTO, null, null);
		OpenApiLingxiChatChoice choice = new OpenApiLingxiChatChoice();
		OpenApiLingxiChatDelta delta = new OpenApiLingxiChatDelta();
		delta.setContent(new OpenApiLingxiChatContent(OpenApiLingxiChatConstants.TYPE_OF_CARD, chatData));
		choice.setDelta(delta);
		vo.setChoices(Collections.singletonList(choice));
		return vo;
	}

	/**
	 * 大模型响应
	 * 
	 * @param handleDTO     handle入参
	 * @param textModelResp 大模型入参
	 * @return 响应信息
	 */
	public static OpenApiLingxiChatRespVO getOpenApiLingxiChatTextModelResp(ChatAddHandleDTO handleDTO,
			String textModelResp) {
		OpenApiLingxiChatRespVO vo = initResp(handleDTO);
		OpenApiLingxiChatChoice choice = new OpenApiLingxiChatChoice();
		OpenApiLingxiChatDelta delta = new OpenApiLingxiChatDelta();
		OpenApiLingxiChatContent thisContent = new OpenApiLingxiChatContent();
		// 类型1
		thisContent.setType(OpenApiLingxiChatConstants.TYPE_OF_TEXT_MODEL);
		thisContent.setData(new OpenApiLingxiChatData(textModelResp));
		delta.setContent(thisContent);
		choice.setDelta(delta);
		vo.setChoices(Collections.singletonList(choice));
		return vo;
	}

	/**
	 * 大模型响应（错误相关，响应之前的对话id）
	 * 
	 * @param handleDTO     handle入参
	 * @param textModelResp 大模型入参
	 * @return 响应信息
	 */
	public static OpenApiLingxiChatRespVO getOpenApiLingxiChatTextModelErrorResp(ChatAddHandleDTO handleDTO,
			String textModelResp) {
		OpenApiLingxiChatRespVO vo = initErrorResp(handleDTO, null, null);
		OpenApiLingxiChatChoice choice = new OpenApiLingxiChatChoice();
		OpenApiLingxiChatDelta delta = new OpenApiLingxiChatDelta();
		OpenApiLingxiChatContent thisContent = new OpenApiLingxiChatContent();
		// 类型1
		thisContent.setType(OpenApiLingxiChatConstants.TYPE_OF_TEXT_MODEL);
		thisContent.setData(new OpenApiLingxiChatData(textModelResp));
		delta.setContent(thisContent);
		choice.setDelta(delta);
		vo.setChoices(Collections.singletonList(choice));
		return vo;
	}

	/**
	 * 错误响应（错误相关，响应之前的对话id）
	 * 
	 * @param handleDTO handle入参
	 * @param code      错误码
	 * @param message   错误信息
	 * @return 响应信息
	 */
	public static OpenApiLingxiChatRespVO getOpenApiLingxiChatErrorResp(ChatAddHandleDTO handleDTO, String code,
			String message) {
		OpenApiLingxiChatRespVO vo = initErrorResp(handleDTO, null, null);
		OpenApiLingxiChatChoice choice = new OpenApiLingxiChatChoice();
		OpenApiLingxiChatDelta delta = new OpenApiLingxiChatDelta();
		// 邮件结果，设置为大模型结果，空字符
		String content = code + " - " + message;
		OpenApiLingxiChatContent thisContent = new OpenApiLingxiChatContent();
		// 类型1
		thisContent.setType(OpenApiLingxiChatConstants.TYPE_OF_TEXT_MODEL);
		thisContent.setData(new OpenApiLingxiChatData(content));
		delta.setContent(thisContent);
		choice.setDelta(delta);
		vo.setChoices(Collections.singletonList(choice));
		return vo;
	}

	private static OpenApiLingxiChatRespVO initResp(ChatAddHandleDTO handleDTO) {
		return initResp(handleDTO, null, null);
	}

	private static OpenApiLingxiChatRespVO initResp(ChatAddHandleDTO handleDTO,
			DialogueIntentionOutput dialogueIntentionOutput, Map<String, String> appendAttrMap) {
		OpenApiLingxiChatRespVO vo = new OpenApiLingxiChatRespVO();
		OpenApiLingxiChatSession thisSession = new OpenApiLingxiChatSession();
		if (null != handleDTO) {
			if (null != handleDTO.getLingxiParamInfo()) {
				vo.setReqId(handleDTO.getLingxiParamInfo().getReqId());
				vo.setModel(handleDTO.getLingxiParamInfo().getModel());
			}
			thisSession.setSessionId(String.valueOf(handleDTO.getSessionId()));
			JSONObject obj = new JSONObject();
			/**
			 * 意图暂时去掉，不传入Attributes if (null != dialogueIntentionOutput) { obj =
			 * JSONUtil.parseObj(JSONUtil.toJsonStr(dialogueIntentionOutput)); } else { obj
			 * =
			 * JSONUtil.parseObj(JSONUtil.toJsonStr(handleDTO.getRespVO().getOutputCommand()));
			 * }
			 */
			obj.set(OpenApiLingxiChatConstants.ATTR_OF_DIALOGUE_ID, String.valueOf(handleDTO.getDialogueId()));
			obj.set(OpenApiLingxiChatConstants.LINGXI_CHAT_DATA_FLAG, String.valueOf(true));
			if (CollUtil.isNotEmpty(appendAttrMap)) {
				for (String key : appendAttrMap.keySet()) {
					obj.set(key, appendAttrMap.get(key));
				}
			}
			thisSession.setAttributes(obj.toString());
			vo.setSession(thisSession);
		} else {
			JSONObject obj = new JSONObject();
			obj.set(OpenApiLingxiChatConstants.LINGXI_CHAT_DATA_FLAG, String.valueOf(true));
			thisSession.setAttributes(obj.toString());
			vo.setSession(thisSession);
		}
		return vo;
	}

	private static OpenApiLingxiChatRespVO initErrorResp(ChatAddHandleDTO handleDTO,
			DialogueIntentionOutput dialogueIntentionOutput, Map<String, String> appendAttrMap) {
		OpenApiLingxiChatRespVO vo = new OpenApiLingxiChatRespVO();
		OpenApiLingxiChatSession thisSession = new OpenApiLingxiChatSession();
		if (null != handleDTO) {
			if (null != handleDTO.getLingxiParamInfo()) {
				vo.setReqId(handleDTO.getLingxiParamInfo().getReqId());
				vo.setModel(handleDTO.getLingxiParamInfo().getModel());
				vo.setSession(handleDTO.getLingxiParamInfo().getLastChatSession());
			} else {
				thisSession.setSessionId(String.valueOf(handleDTO.getSessionId()));
				JSONObject obj = new JSONObject();
				/**
				 * 意图暂时去掉，不传入Attributes if (null != dialogueIntentionOutput) { obj =
				 * JSONUtil.parseObj(JSONUtil.toJsonStr(dialogueIntentionOutput)); } else { obj
				 * =
				 * JSONUtil.parseObj(JSONUtil.toJsonStr(handleDTO.getRespVO().getOutputCommand()));
				 * }
				 */
				obj.set(OpenApiLingxiChatConstants.ATTR_OF_DIALOGUE_ID, String.valueOf(handleDTO.getDialogueId()));
				obj.set(OpenApiLingxiChatConstants.LINGXI_CHAT_DATA_FLAG, String.valueOf(true));
				if (CollUtil.isNotEmpty(appendAttrMap)) {
					for (String key : appendAttrMap.keySet()) {
						obj.set(key, appendAttrMap.get(key));
					}
				}
				thisSession.setAttributes(obj.toString());
				vo.setSession(thisSession);
			}
		} else {
			JSONObject obj = new JSONObject();
			obj.set(OpenApiLingxiChatConstants.LINGXI_CHAT_DATA_FLAG, String.valueOf(true));
			thisSession.setAttributes(obj.toString());
			vo.setSession(thisSession);
		}
		return vo;
	}

	/**
	 * 卡片模板对象
	 */
	@Data
	public static class OpenApiLingxiCardMessage {
		/**
		 * 文本描述
		 */
		private String cardMsg;
		/**
		 * 图标（base64或url）
		 */
		private String logo;
		/**
		 * 标题
		 */
		private String cardTitle;
		/**
		 * 副标题描述
		 */
		private String cardDesc;
		/**
		 * 按钮名称（不超过5个字）
		 */
		private String cardButtonName;
		/**
		 * 按钮跳转地址
		 */
		private String cardButtonUrl;

		public OpenApiLingxiCardMessage() {

		}

		public OpenApiLingxiCardMessage(String cardTitle, String cardDesc, String cardButtonName,
				String paramCardButtonUrl) {
			this.cardTitle = cardTitle;
			this.cardDesc = cardDesc;
			this.cardButtonName = cardButtonName;
			// 需要转码encode
			if (StringUtils.isNotEmpty(paramCardButtonUrl)) {
				try {
					this.cardButtonUrl = UrlEncoderUtil.encodeFullUrl(paramCardButtonUrl);
				} catch (Exception e) {
					log.error("OpenApiLingxiCardMessage URLEncoder.encode 转码失败 error:", e);
					this.cardButtonUrl = paramCardButtonUrl;
				}
			}
		}

		public OpenApiLingxiCardMessage(String cardMsg, String logo, String cardTitle, String cardDesc,
				String cardButtonName, String paramCardButtonUrl) {
			this.cardMsg = cardMsg;
			this.logo = logo;
			this.cardTitle = cardTitle;
			this.cardDesc = cardDesc;
			this.cardButtonName = cardButtonName;
			// 需要转码encode
			if (StringUtils.isNotEmpty(paramCardButtonUrl)) {
				try {
					this.cardButtonUrl = UrlEncoderUtil.encodeFullUrl(paramCardButtonUrl);
				} catch (Exception e) {
					log.error("OpenApiLingxiCardMessage URLEncoder.encode 转码失败 error:", e);
					this.cardButtonUrl = paramCardButtonUrl;
				}
			}
		}

	}

	/**
	 * 简易导航追问卡片实体
	 */
	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	public static class OpenApiLingxiCardReplyMessage {
		/**
		 * 文本内容，如"邮件已经..."
		 */
		private String text;
		/**
		 * 中间卡片列表，可以为空
		 */
		private List<CardReplyItem> list;
		/**
		 * 追问描述，如"您还可以查询..."
		 */
		private String desc;
		/**
		 * 气泡链接列表，为空则不显示desc
		 */
		private List<OpenApiLingxiCardLink> bubbles;

		/**
		 * 自动打开web/app
		 */
		private OpenApiLingxiCardAutoLink autoLink;

		public OpenApiLingxiCardReplyMessage(List<CardReplyItem> list) {
			this.list = list;
		}

		public OpenApiLingxiCardReplyMessage(List<CardReplyItem> list, String desc,
				List<OpenApiLingxiCardLink> bubbles) {
			this.list = list;
			this.desc = desc;
			this.bubbles = bubbles;
		}

		public OpenApiLingxiCardReplyMessage(List<CardReplyItem> list, String desc, List<OpenApiLingxiCardLink> bubbles,
				OpenApiLingxiCardAutoLink autoLink) {
			this.list = list;
			this.desc = desc;
			this.bubbles = bubbles;
			this.autoLink = autoLink;
		}

		public OpenApiLingxiCardReplyMessage(String desc, List<OpenApiLingxiCardLink> bubbles) {
			this.desc = desc;
			this.bubbles = bubbles;
		}

		/**
		 * 简易导航追问卡片-item实体
		 */
		@Data
		@AllArgsConstructor
		@NoArgsConstructor
		public static class CardReplyItem {
			/**
			 * 卡片背景色，默认无背景色
			 */
			private String color;
			/**
			 * 图片URL
			 */
			private String img;
			/**
			 * 标题，必填，如"139邮件"，必填
			 */
			private String title;
			/**
			 * 标题，必填，如"139邮件"，必填
			 */
			private String subtitle;
			/**
			 * 跳转地址，背景色默认蓝色
			 */
			private OpenApiLingxiCardLink link;
		}

	}

	/**
	 * 导航卡片链接
	 */
	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	public static class OpenApiLingxiCardLink {

		public OpenApiLingxiCardLink(String text, ReplyLink reply) {
			this.text = text;
			this.reply = reply;
		}

		public OpenApiLingxiCardLink(WebLink web, String color, String imgUrl, String text) {
			this.web = web;
			this.color = color;
			this.imgUrl = imgUrl;
			this.text = text;
		}

		/**
		 * web拉端扩展
		 */
		private WebLink web;
		/**
		 * App拉端扩展
		 */
		private NativeAppLink nativeApp;
		/**
		 * 颜色，"red","#66666"，默认终端行为
		 */
		private String color;
		/**
		 * 图片，按钮背景图或区域背景图
		 */
		private String imgUrl;
		/**
		 * 按钮名称
		 */
		private String text;
		/**
		 * 回复内容/追问
		 */
		private ReplyLink reply;
		/**
		 * 自动打开/拉起的毫秒数，空则不会拉起
		 */
		private String autoOpenTs;

		/**
		 * 简易导航卡片链接 replyLink
		 */
		@Data
		@AllArgsConstructor
		@NoArgsConstructor
		public static class ReplyLink {
			/**
			 * 追问的内容，必填
			 */
			private String text;
		}
	}

	/**
	 * 导航卡片自动拉端链接
	 */
	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	public static class OpenApiLingxiCardAutoLink {

		public OpenApiLingxiCardAutoLink(WebLink web, String paramAutoOpenTs) {
			this.web = web;
			this.nativeApp = null;
			if (StringUtils.isNotEmpty(paramAutoOpenTs)) {
				this.autoOpenTs = paramAutoOpenTs;
			}
		}

		public OpenApiLingxiCardAutoLink(NativeAppLink nativeApp, String paramAutoOpenTs) {
			this.nativeApp = nativeApp;
			this.web = null;
			if (StringUtils.isNotEmpty(paramAutoOpenTs)) {
				this.autoOpenTs = paramAutoOpenTs;
			}
		}

		/**
		 * web拉端扩展
		 */
		private WebLink web;
		/**
		 * App拉端扩展
		 */
		private NativeAppLink nativeApp;

		/**
		 * 自动打开/拉起web/app 只执行一次 例如: 1000 -> 1000毫秒后自动打开或拉起 ，
		 * 
		 * 默认1000毫秒
		 */
		private String autoOpenTs = "1000";

	}

	/**
	 * 简易导航卡片链接 weblink
	 */
	@Data
	@NoArgsConstructor
	public static class WebLink {
		/**
		 * 访问url，最大长度：1024 可加入前缀拉不同浏览器, 如拉端可忽略以下说明 1. 不加前缀: 厂商默认行为 2.
		 * browser://open?url=: 系统自带浏览器 3. internal://open?url=: 内置浏览器 示例:
		 * internal://open?url=http://baidu.com/aaaaaaaa ，必填
		 */
		private String url;
		/**
		 * 应用名 最大长度：256
		 */
		private String appName;

		public WebLink(String paramUrl) {
			// 需要转码encode
			if (StringUtils.isNotEmpty(paramUrl)) {
				try {
					this.url = UrlEncoderUtil.encodeFullUrl(paramUrl);
				} catch (Exception e) {
					log.error("WebLink URLEncoder.encode 转码失败 error:", e);
					this.url = paramUrl;
				}
			}
		}

		public WebLink(String paramUrl, String appName) {
			// 需要转码encode
			if (StringUtils.isNotEmpty(paramUrl)) {
				try {
					this.url = UrlEncoderUtil.encodeFullUrl(paramUrl);
				} catch (Exception e) {
					log.error("WebLink URLEncoder.encode 转码失败 error:", e);
					this.url = paramUrl;
				}
			}
			this.appName = appName;
		}

	}

	/**
	 * 简易导航卡片链接 nativeAppLink
	 */
	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	public static class NativeAppLink {
		/**
		 * deeplink地址 最大长度：1024，必填
		 */
		private String url;
		/**
		 * APP应用包名 最大长度：256，必填
		 */
		private String pkgName;
		/**
		 * 应用名 最大长度：256
		 */
		private String appName;
		/**
		 * 所需android最小API Level
		 */
		private String minAndroidApiLevel;
		/**
		 * 支持的APP的最小版本号
		 */
		private String minVersion;

		public NativeAppLink(String paramUrl, String pkgName) {
			// 需要转码encode
			if (StringUtils.isNotEmpty(paramUrl)) {
				try {
					this.url = UrlEncoderUtil.encodeFullUrl(paramUrl);
				} catch (Exception e) {
					log.error("NativeAppLink URLEncoder.encode 转码失败 error:", e);
					this.url = paramUrl;
				}
			}
			this.pkgName = pkgName;
		}

	}

}