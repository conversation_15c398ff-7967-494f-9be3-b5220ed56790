package com.zyhl.yun.api.outer.application.vo.knowledge;

import lombok.Data;

import java.util.List;

/**
 * 知识库文件操作异步任务结果
 *
 * <AUTHOR>
 */
@Data
public class KnowledgeTaskV2ResultVO {

    /**
     * 异步任务信息
     */
    private PersonalKnowledgeTaskInfo taskInfo;

    /**
     * 批量文件操作结果，返回请求文件ID列表中每个文件的处理结果
     * 注：不包含子目录/子文件。
     * 仅以下类型的任务返回：
     * 批量复制
     * 批量移动
     * 批量彻底删除
     * 批量逻辑删除（移入回收站）
     * 批量还原文件
     */
    private List<PersonalKnowledgeResourceResult> batchResourceResultList;

}
