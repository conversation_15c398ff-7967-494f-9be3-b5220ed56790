package com.zyhl.yun.api.outer.application.chatv2.dto;

import lombok.Data;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description} 对话输入的图片工具设置信息
 * <p>
 *
 * <AUTHOR>
 * @since 4月24 2025
 */
@Data
public class DialogueAiToolSettingDTO {

  /**
   *工具编码，与意图编码对应
   */
    private String code;
  /**
   * "doc-summary":文档总结
   * "doc-outline":文档大纲，快速阅读工具中，
   * 会在一次流式对话里面同时返回脑图和大纲内容
   */
  private String module;
}
