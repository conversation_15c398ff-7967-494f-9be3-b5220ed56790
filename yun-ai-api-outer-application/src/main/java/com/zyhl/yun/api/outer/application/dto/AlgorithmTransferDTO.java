package com.zyhl.yun.api.outer.application.dto;


import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.enums.BaseResultCodeEnum;
import com.zyhl.yun.api.outer.constants.RegConst;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 对话结果转存接口参数
 * <AUTHOR>
 */
@Data
@Slf4j
public class AlgorithmTransferDTO extends BaseDTO {
    /**
     * 会话Id
     */
    private String dialogueId;

    /**
     * 当前对话Id
     */
    private String path;

    /**
     * 参数校验
     */
    public AbstractResultCode check() {
        if (CharSequenceUtil.isEmpty(dialogueId)) {
            log.info("对话id为空");
            return BaseResultCodeEnum.ERROR_PARAMS;
        } else if (!dialogueId.matches(RegConst.REG_DATA_STR)) {
            log.info("对话id不是纯数字：{}", dialogueId);
            return BaseResultCodeEnum.ERROR_PARAMS;
        }

        return checkUserId();
    }

}
