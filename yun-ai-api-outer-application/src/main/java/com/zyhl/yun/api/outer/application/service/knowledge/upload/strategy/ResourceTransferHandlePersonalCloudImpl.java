package com.zyhl.yun.api.outer.application.service.knowledge.upload.strategy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.alibaba.fastjson.JSON;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.vo.CheckResultVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDriveReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveDirVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.FileResult;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.BatchFileVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.DriveVO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileCheckReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.ResourceTransferReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.upload.AbstractResourceTransferHandle;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeAddResultVO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeFileImportVO;
import com.zyhl.yun.api.outer.config.FileCheckConfig;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileTaskEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeUploadEntity;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.vo.ImportNoteInfoVO;
import com.zyhl.yun.api.outer.domain.vo.PersonalKnowledgeImportCheckResult;
import com.zyhl.yun.api.outer.domainservice.CheckSystemDomainService;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeUploadStatusEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述：个人云文件转存
 * 
 * <AUTHOR>
 * @date 2025-04-16
 */
@Slf4j
@Component
public class ResourceTransferHandlePersonalCloudImpl extends AbstractResourceTransferHandle {
    @Override
    public void afterPropertiesSet() throws Exception {
        register(KnowledgeResourceTypeEnum.PERSONAL_FILE.getCode(), this);
    }

    /**
     * 不存在错误码
     */
    private static final String FILE_NOT_EXIST_CODE = "13000010";


    /**
     * 新平台个人云文件不存在
     */
    private static final String PERSON_FILE_NOT_EXIST_CODE = "04000010";

    @Override
    public KnowledgeFileImportVO trans(ResourceTransferReqDTO dto) {
        if (dto.getRetryFlag() == null) {
            dto.setRetryFlag(Boolean.FALSE);
        }
        this.checkKnowledgeDriveSize();
        // 根据重复文件ID去重
        Map<String, File> fileMap = dto.getFileList().stream().collect(Collectors.toMap(File::getFileId, v -> v, (existing, replacement) -> existing));
        // 数据库重复导入去重
        KnowledgeFileImportVO result = new KnowledgeFileImportVO();
        List<PersonalKnowledgeImportCheckResult> checkResult = new ArrayList<>();
        List<String> fileIds = this.checkAndRemoveDuplicates(dto, fileMap, checkResult);
        result.setErrorList(checkResult);
        if (CollectionUtil.isEmpty(fileIds)) {
            return result;
        }
        // 查询个人云
        BatchFileVO batchFileVO = yunDiskExternalService.fileBatchGetByAllPlatform(dto.getUserId(), RequestContextHolder.getBelongsPlatform(), fileIds);
        Map<String, FileResult> fileResultMap = batchFileVO.getBatchFileResults().stream().collect(Collectors.toMap(k -> k.getSrcFile().getFileId(), v -> v));

        List<UserKnowledgeUploadEntity> uploadAddList = new ArrayList<>();
        List<UserKnowledgeFileEntity> fileAddList = new ArrayList<>();
        for (String fileId : fileIds) {
            // 个人云文件不不存在过滤
            FileResult fileResult = fileResultMap.get(fileId);
            KnowledgeAddResultVO vo = validPersonFile(fileId, fileResult, fileMap);
            if (vo != null) {
                uploadAddList.add(new UserKnowledgeUploadEntity(dto.getUserId(), dto.getBaseId(), dto.getResourceType(), dto.getParentFileId(), dto.getParentFilePath())
                        .buildPersonFile(fileResult.getSrcFile())
                        .buildUploadResult(KnowledgeUploadStatusEnum.FAIL.getStatus(), vo.getResult(), vo.getDescription()));
                continue;
            }
            com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.File personFile = fileResult.getSrcFile();
            if (FileTypeEnum.FILE.getYunDiskFileType().equals(personFile.getType())) {
                // 文件根据条件过滤
                KnowledgeAddResultVO knowledgeAddResultVO = validFile(fileId, personFile.getSize(), personFile.getFileExtension(), personFile.getCategory());
                if (knowledgeAddResultVO != null) {
                    uploadAddList.add(new UserKnowledgeUploadEntity(dto.getUserId(), dto.getBaseId(), dto.getResourceType(), dto.getParentFileId(), dto.getParentFilePath())
                            .buildPersonFile(fileResult.getSrcFile())
                            .buildUploadResult(KnowledgeUploadStatusEnum.FAIL.getStatus(), knowledgeAddResultVO.getResult(), knowledgeAddResultVO.getDescription()));
                    continue;
                }
                if (dto.getRetryFlag()) {
                    // 发起转存
                    String thirdTaskId = userDriveExternalService.transTask(RequestContextHolder.getUserInfo(), dto.getParentFileId(), fileIds);
                    // 保存task表
                    UserKnowledgeFileTaskEntity taskEntity = saveTransTask(dto, fileIds,
                            dto.getUploadIds(), thirdTaskId);
                    sendFileTaskMq(taskEntity, dto.getBaseId(), taskEntity.getId());
                    // 修改数据库
                    UserKnowledgeUploadEntity uploadEntity = new UserKnowledgeUploadEntity(dto.getUserId(), dto.getBaseId(), dto.getResourceType(), dto.getParentFileId(), dto.getParentFilePath())
                            .buildPersonFile(fileResult.getSrcFile())
                            .buildUploadResult(KnowledgeUploadStatusEnum.PROCESSING.getStatus(), null, null);
                    uploadEntity.setId(Long.valueOf(dto.getTaskId()));
                    userKnowledgeUploadRepository.updateRetryById(uploadEntity);
                    log.info("个人云文件转存重试成功，文件id：{}", fileId);
                    return result;
                }
                // 标题送审
                if(Boolean.FALSE.equals(fileCheck(personFile, dto.getUserId()))){
                    PersonalKnowledgeImportCheckResult check = new PersonalKnowledgeImportCheckResult();
                    check.personalFail(KnowledgeResourceTypeEnum.PERSONAL_FILE.getCode(), String.valueOf(dto.getBaseId()), fileMap.get(fileId), ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultMsg(),Integer.valueOf(ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultCode()));
                    checkResult.add(check);
                    uploadAddList.add(new UserKnowledgeUploadEntity(dto.getUserId(), dto.getBaseId(), dto.getResourceType(), dto.getParentFileId(), dto.getParentFilePath())
                            .buildPersonFile(fileResult.getSrcFile())
                            .buildUploadResult(KnowledgeUploadStatusEnum.FAIL.getStatus(), ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultCode(), ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultMsg()));
                    continue;
                }
                // 返回的处理结果
                uploadAddList.add(new UserKnowledgeUploadEntity(dto.getUserId(), dto.getBaseId(), dto.getResourceType(), dto.getParentFileId(), dto.getParentFilePath())
                        .buildPersonFile(fileResult.getSrcFile())
                        .buildUploadResult(KnowledgeUploadStatusEnum.PROCESSING.getStatus(), null, null));
            } else {
                // 创建目录
                try {
                    OwnerDriveDirVO catalog = userDriveExternalService.createCatalog(dto.getUserId(), personFile.getName(), dto.getParentFileId());
                    UserKnowledgeUploadEntity categoryEntity = new UserKnowledgeUploadEntity(dto.getUserId(), dto.getBaseId(), dto.getResourceType(), dto.getParentFileId(), dto.getParentFilePath())
                            .buildPersonFile(fileResult.getSrcFile())
                            .buildUploadResult(KnowledgeUploadStatusEnum.SUCCESS.getStatus(), ResultCodeEnum.SUCCESS.getResultCode(), null);
                    categoryEntity.setTargetFileId(catalog.getFileId());
                    uploadAddList.add(categoryEntity);
                    fileAddList.add(new UserKnowledgeFileEntity(fileResult.getSrcFile(), dto.getUserId(), dto.getBaseId(), catalog.getParentFileId(), dto.getParentFilePath(), catalog.getFileId(), catalog.getFileName()));
                } catch (YunAiBusinessException e) {
                    log.error("创建独立空间目录失败：{}", e.getMessage(), e);
                    if ("13010316".equals(e.getCode())) {
                        String msg = knowledgePersonalProperties.getParseFailedReason().get(e.getCode());
                        if (ObjectUtil.isEmpty(msg)) {
                            msg = e.getMessage();
                        }
                        throw new YunAiBusinessException(e.getCode(), msg);
                    }
                } catch (Exception e) {
                    log.error("创建独立空间目录失败：{}", e.getMessage(), e);
                }
            }
        }

        result.setErrorList(checkResult);

        List<UserKnowledgeUploadEntity> fileUserKnowledge = uploadAddList.stream().filter(item ->
                        FileTypeEnum.FILE.getKnowledgeFileType().equals(item.getFileType()) &&
                                KnowledgeUploadStatusEnum.PROCESSING.getStatus().equals(item.getUploadStatus())
                )
                .collect(Collectors.toList());
        String thirdTaskId = null;
        if (CollectionUtil.isNotEmpty(fileUserKnowledge)) {
            List<String> fileIdsList = fileUserKnowledge.stream()
                    .map(item -> item.getFileId()).collect(Collectors.toList());
            thirdTaskId = userDriveExternalService.transTask(RequestContextHolder.getUserInfo(), dto.getParentFileId(), fileIdsList);
        }

        // 存库
        UserKnowledgeFileTaskEntity taskEntity = this.updateDb(uploadAddList, fileAddList, dto, fileUserKnowledge, thirdTaskId);
        if (taskEntity != null) {
            sendFileTaskMq(taskEntity, dto.getBaseId(), taskEntity.getId());
        }

        List<UserKnowledgeUploadEntity> categoryUserKnowledge = uploadAddList.stream().filter(item ->
                        FileTypeEnum.FOLDER.getKnowledgeFileType().equals(item.getFileType()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(categoryUserKnowledge)) {
            // 目录转发到MQ处理
            sendCategoryTaskMq(categoryUserKnowledge);
        }

        return result;
    }

    private Boolean fileCheck(com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.File file, String userId) {

        boolean enabled = fileCheckConfig.isEnabled();
        log.info("batchImport cloud fileCheckConfig:{}",fileCheckConfig);
        if(Boolean.FALSE.equals(enabled)){
            // 开关打开
            String name = file.getName();
            long uid = uidGenerator.getUID();
            CheckResultVO resultVo = checkSystemDomainService.checkPlatformAndLocal(uid, userId, name);
            return !CheckResultVO.isFail(resultVo);
        }
        return Boolean.TRUE;
    }

    /**
     * 保存上传表、文件表、任务表
     *
     * @param uploadAddList     上传列表
     * @param fileAddList       文件列表
     * @param dto               dto
     * @param fileUserKnowledge 上传列表(文件)
     * @param thirdTaskId       第三方转存id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public UserKnowledgeFileTaskEntity updateDb(List<UserKnowledgeUploadEntity> uploadAddList, List<UserKnowledgeFileEntity> fileAddList, ResourceTransferReqDTO dto, List<UserKnowledgeUploadEntity> fileUserKnowledge, String thirdTaskId) {
        // 保存upload表
        userKnowledgeUploadRepository.batchAdd(uploadAddList);
        // 保存目录
        userKnowledgeFileRepository.batchAdd(fileAddList);
        // 保存转存表
        if (CollectionUtil.isNotEmpty(fileUserKnowledge) && StringUtils.isNotBlank(thirdTaskId)) {
            List<Long> uploadIds = fileUserKnowledge.stream().map(item -> item.getId()).collect(Collectors.toList());
            List<String> fileIdsList = fileUserKnowledge.stream()
                    .map(item -> item.getFileId()).collect(Collectors.toList());
            return saveTransTask(dto, fileIdsList, uploadIds, thirdTaskId);
        }
        return null;
    }

    /**
     * 个人云文件去重
     *
     * @param dto
     * @return
     */
    private List<String> checkAndRemoveDuplicates(ResourceTransferReqDTO dto, Map<String, File> fileMap, List<PersonalKnowledgeImportCheckResult> checkResult) {
        List<String> fileIds = new ArrayList<>(fileMap.keySet());
        Set<String> repeatUploadFileIdSet = userKnowledgeUploadRepository.findRepeatImportPersonalCloudFile(dto.getUserId(), String.valueOf(dto.getBaseId()), fileIds);
        Set<String> repeatFileIdSet = userKnowledgeFileRepository.findRepeatImportPersonalCloudFile(dto.getUserId(), String.valueOf(dto.getBaseId()), fileIds, dto.getParentFileId());
        if (repeatUploadFileIdSet.isEmpty() && repeatFileIdSet.isEmpty()) {
            return fileIds;
        }
        Iterator<String> iterator = fileIds.iterator();
        while (iterator.hasNext()) {
            String fileId = iterator.next();
            File file = fileMap.get(fileId);
            PersonalKnowledgeImportCheckResult check = new PersonalKnowledgeImportCheckResult();
            if (repeatUploadFileIdSet.contains(fileId)) {
                iterator.remove();
                check.personalFail(KnowledgeResourceTypeEnum.PERSONAL_FILE.getCode(), String.valueOf(dto.getBaseId()), file, FileTypeEnum.FOLDER.getYunDiskFileType().equals(file.getType()) ? "目录正在上传" : "文件正在上传");
                checkResult.add(check);
            } else if (repeatFileIdSet.contains(fileId)) {
                iterator.remove();
                check.personalFail(KnowledgeResourceTypeEnum.PERSONAL_FILE.getCode(), String.valueOf(dto.getBaseId()), file, FileTypeEnum.FOLDER.getYunDiskFileType().equals(file.getType()) ? "目录已存在" : "文件已存在");
                checkResult.add(check);
            }
        }
        return fileIds;
    }

    @Override
    public void check(KnowledgeFileCheckReqDTO dto) {
        //暂不需要校验
    }


    /**
     * 校验个人云文件
     *
     * @param fileId
     * @param fileResult
     * @return
     */
    private KnowledgeAddResultVO validPersonFile(String fileId, FileResult fileResult, Map<String, File> fileMap) {
        if (fileResult == null) {
            log.info("文件不存在，fileId：{}", fileId);
            return new KnowledgeAddResultVO(fileId, ResultCodeEnum.ERROR_NOT_FOUND);
        }

        String errCode = fileResult.getErrCode();
        if (FILE_NOT_EXIST_CODE.equals(errCode) || PERSON_FILE_NOT_EXIST_CODE.equals(errCode)) {
            log.info("文件不存在，fileId：{}", fileId);
            updateSrcFile(fileResult, fileMap.get(fileId));
            return new KnowledgeAddResultVO(fileId, ResultCodeEnum.ERROR_NOT_FOUND);
        }

        if (CharSequenceUtil.isNotEmpty(errCode) && !ResultCodeEnum.SUCCESS.getResultCode().equals(errCode)) {
            log.info("未知错误，fileId：{}，错误码：{}，错误信息：{}", fileId, errCode, fileResult.getMessage());
            updateSrcFile(fileResult, fileMap.get(fileId));
            return new KnowledgeAddResultVO(fileId, ResultCodeEnum.UNKNOWN_ERROR);
        }

        return null;
    }

    private void updateSrcFile(FileResult fileResult, File file) {
        Optional.ofNullable(file).ifPresent(f -> {
            if (StringUtils.isNotBlank(f.getType())) {
                fileResult.getSrcFile().setType(f.getType());
            }
            if (StringUtils.isNotBlank(f.getName())) {
                fileResult.getSrcFile().setName(f.getName());
            }
        });
    }

    /**
     * 校验空间大小
     */
    private void checkKnowledgeDriveSize() {
        RequestContextHolder.UserInfo userInfo = RequestContextHolder.getUserInfo();
        OwnerDriveVO vo = ownerClient.getOwnerDrive(OwnerDriveReqDTO.builder()
                .userId(userInfo.getUserId())
                .clientInfo(RequestContextHolder.getClientInfo())
                .appChannel(RequestContextHolder.getAppChannel())
                .build());
        if (vo.getTotalSize().compareTo(vo.getUsedSize()) <= 0) {
            log.info("获取用户独立空间ID，空间id：{}，状态：{}，总空间：{}，已使用空间：{}", vo.getDriveId(), vo.getStatus(), vo.getTotalSize(), vo.getUsedSize());
            throw new YunAiBusinessException(ResultCodeEnum.LACK_OF_SPACE);
        }
    }
}
