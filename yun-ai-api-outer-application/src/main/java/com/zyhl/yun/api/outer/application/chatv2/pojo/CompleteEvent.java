package com.zyhl.yun.api.outer.application.chatv2.pojo;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;

import lombok.Data;

/**
 * 描述：完成事件对象
 *
 * <AUTHOR> zhumaoxian
 * @date 2025/4/25 0:05
 */
@Data
public class CompleteEvent {

	public CompleteEvent(ChatAddHandleDTO handleDTO, SseEventListener eventListener) {
		this.handleDTO = handleDTO;
		this.eventListener = eventListener;
	}

	private ChatAddHandleDTO handleDTO;

	private SseEventListener eventListener;

	/**
	 * 结果码
	 */
	private String resultCode;
	/**
	 * 结果信息
	 */
	private String resultMsg;

	/**
	 * 大模型输出内容
	 */
	private String outContent = "";

	/**
	 * 思维链过程
	 */
	private String reasoningContent = "";

}
