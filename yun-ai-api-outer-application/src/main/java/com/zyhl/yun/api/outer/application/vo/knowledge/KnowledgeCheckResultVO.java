package com.zyhl.yun.api.outer.application.vo.knowledge;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 知识库上传任务结果
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class KnowledgeCheckResultVO {

    /**
     * 任务状态：
     * -1--失败
     * 0--运行中
     * 1--部分成功
     * 2--Succeed成功
     * （若状态是0运行中，可稍后再轮询任务，直到状态非0）
     */
    private Boolean checkResult;

    /**
     * 文档上传结果
     */
    private String  failMessage;

    public void checkFail( String failMessage){
        this.checkResult = false;
        this.failMessage = failMessage;
    }

    public void checkSuccess(){
        this.checkResult = true;
    }


}
