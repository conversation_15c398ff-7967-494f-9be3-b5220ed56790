package com.zyhl.yun.api.outer.application.enums;

/**
 * <AUTHOR>
 * @Classname NoteTypeEnum
 * @Description 笔记类型
 * @Date 2024/3/1 13:36
 */
public enum NoteTypeEnum {

    /**
     * 笔记类型
     */
    DEFAULT("DEFAULT", "默认"),

    /**
     * 文本
     */
    TEXT("TEXT", "文本"),

    /**
     * 图片
     */
    IMAGE("IMAGE", "图片"),

    /**
     * 富文本
     */
    RICHTEXT("RICHTEXT", "富文本"),
    ;

    private final String type;

    private final String message;

    NoteTypeEnum(String type, String message) {
        this.type = type;
        this.message = message;
    }

    public String getType() {
        return type;
    }

    public String getMessage() {
        return message;
    }
}
