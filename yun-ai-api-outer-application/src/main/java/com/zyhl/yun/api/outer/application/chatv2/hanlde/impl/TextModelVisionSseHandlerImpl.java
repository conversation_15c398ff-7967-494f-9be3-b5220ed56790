package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import com.zyhl.hcy.yun.ai.common.base.enums.YunAiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.FileBase64Util;
import com.zyhl.hcy.yun.ai.common.base.utils.FileOperationUtil;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmicimage.CmicImageClient;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelConfigDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageVlDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageVlDTO.VlContent;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelVlReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.FileSuffixContentTypeEnum;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelVlContentTypeEnum;
import com.zyhl.hcy.yun.ai.common.model.api.utils.ImageProcessorUtil.ImageInput;
import com.zyhl.hcy.yun.ai.common.model.api.utils.ImageProcessorUtil.ImageOutput;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.AIFileVO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueAttachmentDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueTextToolSettingDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueToolSettingDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.service.BenefitService;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatAddCheckService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.config.textmodel.ModelPromptProperties;
import com.zyhl.yun.api.outer.config.textmodel.VlModelConfig;
import com.zyhl.yun.api.outer.config.textmodel.VlModelConfig.BusinessModelConfig;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.domainservice.IImageCommonService;
import com.zyhl.yun.api.outer.domainservice.QpsLimitService;
import com.zyhl.yun.api.outer.domainservice.ResourceInfoDomainService;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.SseNameEnum;
import com.zyhl.yun.api.outer.external.DocumentParsingExternalService;
import com.zyhl.yun.api.outer.external.YunDiskExternalService;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 视觉大模型流式对话
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TextModelVisionSseHandlerImpl extends AbstractChatAddV2Handler {

    /**
     * 当前执行顺序
     */
    private final ExecuteSort thisExecuteSort = ExecuteSort.TEXT_MODE_VISION_SSE;

    private static final String REPLACE_KEY_OF_QUERY = "{query}";
    private static final String REPLACE_KEY_OF_QUERY_WRAPPER = "{queryWrapper}";
    private static final String REPLACE_KEY_OF_LANGUAGE = "{language}";
    private static final String REPLACE_KEY_OF_LANGUAGE_PROMPT = "翻译目标语言: ";

    @Resource
    private BenefitService benefitService;
    @Resource
    private DataSaveService dataSaveService;
    @Resource
    private ChatAddCheckService chatAddCheckService;
    @Resource
    private ChatConfigServiceDomainService chatConfigServiceDomainService;
    @Resource
    private DocumentParsingExternalService documentParsingExternalService;
    @Resource
    private QpsLimitService qpslimitService;
    @Resource
    private ModelPromptProperties modelPromptProperties;
    @Resource
    private ModelProperties modelProperties;
    @Resource
    private AlgorithmChatContentRepository algorithmChatContentRepository;
    @Resource
    private TextModelExternalService textModelExternalService;
    @Resource
    private ResourceInfoDomainService resourceInfoDomainService;
    @Resource
    private YunDiskExternalService yunDiskExternalService;
    @Resource
    private IImageCommonService imageCommonService;
    @Resource
    private CmicImageClient cmicImageClient;
    @Resource
    private VlModelConfig vlModelConfig;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 支持的业务初始化
        List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        thisBusinessTypes.add(ChatBusinessTypeEnum.CLOUD_PHONE);
		thisBusinessTypes.add(ChatBusinessTypeEnum.MESSAGE_5G);
		this.setBusinessTypes(thisBusinessTypes);
	}

    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public boolean execute(ChatAddHandleDTO handleDTO) {

        // 图片工具不处理
        if (DialogueIntentionEnum.isAiToolIntention(handleDTO.getIntentionCode())) {
            return false;
        }

        // 没dialogue||没prompt不处理
        if (StringUtils.isAllEmpty(handleDTO.getReqDTO().getDialogueInput().getDialogue(),
                handleDTO.getReqDTO().getDialogueInput().getPrompt())) {
            return false;
        }

        // 非图片资源不执行
        DialogueAttachmentDTO attachmentDTO = handleDTO.getInputInfoDTO().getAttachment();
        if (ObjectUtil.isEmpty(attachmentDTO) || ObjectUtil.isEmpty(attachmentDTO.getAttachmentTypeList())) {
            return false;
        }

        for (Integer type : attachmentDTO.getAttachmentTypeList()) {
            if (ResourceTypeEnum.isImage(type)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        log.info("进入{}", thisExecuteSort.getDesc());

        if (!DialogueIntentionEnum.isTextToolIntention(handleDTO.getIntentionCode())) {
            // 非文本工具，强制转换为文本意图
            handleDTO.setTextGenerateTextIntention();
        }
        Optional<List<com.zyhl.yun.api.outer.domain.valueobject.File>> fileListOptional =
                Optional.of(handleDTO)
                        .map(ChatAddHandleDTO::getInputInfoDTO)
                        .map(DialogueInputInfoDTO::getAttachment)
                        .map(DialogueAttachmentDTO::getFileList);
        if (!fileListOptional.isPresent() || CollectionUtils.isEmpty(fileListOptional.get())) {
            return Boolean.TRUE;
        }

        AIFileVO fileVo = null;
        if (CollectionUtils.isEmpty(handleDTO.getImageLocalPaths())) {
            fileVo = yunDiskExternalService.getFileInfo(RequestContextHolder.getUserId(),
                    handleDTO.getInputInfoDTO().getAttachment().getFileList().get(0).getFileId());
            if (!(null != fileVo && StringUtils.isNotEmpty(fileVo.getContent()))) {
                log.info("获取图片资源内容为空");
                throw new YunAiBusinessException(YunAiCommonResultCode.FILE_UPLOAD_FAILED);
            }
        }

        if (null == handleDTO.getVlmBusinessModelConfig()) {
            BusinessModelConfig defaultConfig = vlModelConfig.getDefaultDialogueImageConfig();
            handleDTO.setVlmBusinessModelConfig(defaultConfig);
        }

        // 扣减权益
        benefitService.consumeBenefit(handleDTO.getReqDTO(), RequestContextHolder.getPhoneNumber(),
                handleDTO.getDialogueId());

        // 保存到hbase
        dataSaveService.saveTextResult(handleDTO, "", "");

        // 保存数据库
        dataSaveService.add(handleDTO, ChatStatusEnum.CHAT_IN);

        // 监听器
        SseEventListener event = new SseEventListener(handleDTO, null);
        event.getSseEmitterOperate().setSseName(SseNameEnum.VISION_SSE.getCode());

        // 视觉大模型
        vlmDialogue(event, handleDTO, fileVo);

        return false;
    }

    /**
     * 视觉大模型对话
     *
     * @param dialogueId
     * @param content
     * @param params
     * @param event
     */
    private void vlmDialogue(SseEventListener event, ChatAddHandleDTO handleDTO, AIFileVO fileVo) {
        ImageOutput imageOutput = null;
        try {
            // 获取用户设置的模型，没有设置则使用默认模型
            ChatConfigEntity chatConfigEntity = chatConfigServiceDomainService.getUserCanUseModel(event.getUserId(),
                    event.getPhone(), handleDTO.getAssistantEnum(), handleDTO.getBusinessType());
            event.setModelCode(chatConfigEntity.getModelType());
            BusinessModelConfig dialogueAndImageConfig = handleDTO.getVlmBusinessModelConfig();
            String dialogueAndImageModelCode = dialogueAndImageConfig.getModelCode();
            Long dialogueId = handleDTO.getDialogueId();

            // 令牌桶限流
            if (!qpslimitService.modelQpsLimit(dialogueAndImageModelCode)) {
                log.info("【视觉大模型对话】请求过多，qps限流，模型编码：{}，对话id：{}", dialogueAndImageModelCode, dialogueId);
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_LIMITATION);
            }
            log.info("【视觉大模型对话】获取的dialogueAndImageConfig：{}，对话id：{}", JSONUtil.toJsonStr(dialogueAndImageConfig),
                    dialogueId);

            // 获取图片结果：url下载文件和处理压缩转换
            File desFile;
            String contentType;

            if (CollectionUtils.isEmpty(handleDTO.getImageLocalPaths())) {
                imageOutput = urlToLocalPathAndProcess(dialogueAndImageConfig, fileVo.getContent(), fileVo.getFileSuffix());
                desFile = imageOutput.getDesFile();
                contentType = imageOutput.getContentType();
            } else {
                desFile = FileUtils.getFile(handleDTO.getImageLocalPaths().get(0));
                contentType = FileSuffixContentTypeEnum.getEnumBySuffix(FilenameUtils
                        .getExtension(handleDTO.getImageLocalPaths().get(0))).getContentType();
            }

            String userContentText = handleDTO.getInputInfoDTO().getDialogue();
            if (StringUtils.isEmpty(userContentText)) {
                // 空对话，使用prompt
                userContentText = handleDTO.getInputInfoDTO().getPrompt();
            }
            String systemPrompt = dialogueAndImageConfig.getSystemPrompt();
            String userPrompt = dialogueAndImageConfig.getUserPrompt();
            log.info("vlmDialogue execute dialogueId:{}, dialogueAndImageModelCode:{}", dialogueId,
                    dialogueAndImageModelCode);
            // 视觉模型对象入参
            TextModelVlReqDTO reqDTO = toSimpleVlReqDTO(RequestContextHolder.getUserId(),
                    String.valueOf(handleDTO.getSessionId()), String.valueOf(dialogueId));
            List<TextModelMessageVlDTO> allMsg = reqDTO.getMessageVlDtoList();
            if (CollUtil.isNotEmpty(allMsg)) {
                // （公共方法，默认追加当前对话）最后一条需要去除
                allMsg = allMsg.subList(0, allMsg.size() - 1);
            } else {
                // 不存在，直接new list
                allMsg = new ArrayList<>();
            }

            // 对话内容追加 start
            List<VlContent> vlContent = new ArrayList<>();

            // 1系统提示词
            if (StringUtils.isNotEmpty(systemPrompt)) {
                vlContent.add(
                        VlContent.builder().type(TextModelVlContentTypeEnum.TEXT.getType()).text(systemPrompt).build());
            }

            // 2图片
            String imageBase64 = FileBase64Util.fileToBase64(desFile);
            if (TextModelEnum.isZiyanVl(dialogueAndImageModelCode)) {
                // 自研用图片base64
                vlContent.add(VlContent.builder().type(TextModelVlContentTypeEnum.IMAGE_BASE64.getType())
                        .imageBase64(imageBase64).build());
            } else if (TextModelEnum.isHuoShanVl(dialogueAndImageModelCode)) {
                // 火山图片url类型
                VlContent content = VlContent.builder().type(TextModelVlContentTypeEnum.VOLCANO_IMAGE_URL.getType())
                        .build();
                content.setImageUrlObject("data:" + contentType + ";base64," + imageBase64);
                vlContent.add(content);
            } else {
                // 默认的图片url
                VlContent content = VlContent.builder().type(TextModelVlContentTypeEnum.VOLCANO_IMAGE_URL.getType())
                        .build();
                content.setImageUrl("data:" + contentType + ";base64," + imageBase64);
                vlContent.add(content);
            }

            // 3用户提示词
            String finalDialogue = StringUtils.EMPTY;
            if (StringUtils.isNotEmpty(userPrompt)) {
                if (StringUtils.isEmpty(userContentText)) {
                    userContentText = StringUtils.EMPTY;
                }
                // 直接替换query
                finalDialogue = userPrompt.replace(REPLACE_KEY_OF_QUERY, userContentText);
                // 替换并且增加{}包裹，dialogue空直接把包裹也去掉
                finalDialogue = queryWrapper(finalDialogue, userContentText);
            }
            Optional<String> languageOptional = Optional.of(handleDTO)
                    .map(ChatAddHandleDTO::getReqDTO)
                    .map(ChatAddReqDTO::getDialogueInput).map(DialogueInputInfoDTO::getToolSetting)
                    .map(DialogueToolSettingDTO::getTextToolSetting)
                    .map(DialogueTextToolSettingDTO::getLanguage);
            if (DialogueIntentionSubEnum.AI_PHOTO_RECOGNITION_TRANSLATE.getCode().equals(handleDTO.getSubIntentionCode())) {
                if (languageOptional.isPresent()) {
                    finalDialogue = finalDialogue.replace(REPLACE_KEY_OF_LANGUAGE,
                            REPLACE_KEY_OF_LANGUAGE_PROMPT + languageOptional.get());
                } else {
                    finalDialogue = finalDialogue.replace(REPLACE_KEY_OF_LANGUAGE, StringUtils.EMPTY);
                }
            }
            log.info("【视觉大模型对话】 finalDialogue:{}", finalDialogue);
            vlContent.add(
                    VlContent.builder().type(TextModelVlContentTypeEnum.TEXT.getType()).text(finalDialogue).build());

            allMsg.add(new TextModelMessageVlDTO(TextModelRoleEnum.USER.getName(), vlContent));
            // 对话内容追加 end

            // 设置vl消息体
            reqDTO.setMessageVlDtoList(allMsg);
            // 设置模型参数
            reqDTO.setTextModelConfig(new TextModelConfigDTO(dialogueAndImageConfig.getMaxTokens(),
                    dialogueAndImageConfig.getTemperature(), null, dialogueAndImageConfig.getTopP(),
                    dialogueAndImageConfig.getTopK(), dialogueAndImageConfig.getFrequencyPenalty(), null));
            textModelExternalService.streamVlDialogue(dialogueAndImageModelCode, reqDTO, event);
            // 更新模型编码
            algorithmChatContentRepository.updateModelCode(event.getDialogId(), event.getModelCode());
        } finally {
            if (!CollectionUtils.isEmpty(handleDTO.getImageLocalPaths())) {
                //删除临时文件
                FileOperationUtil.deleteLocalFiles(handleDTO.getImageLocalPaths());
            }
            deleteLocalFile(imageOutput);
        }
    }

    /**
     * 替换并且增加{}包裹，dialogue空直接把包裹{}也去掉
     *
     * @param userContentText
     * @param dialogue
     * @return
     */
    private String queryWrapper(String userContentText, String dialogue) {
        boolean hasDialogue = StringUtils.isNotEmpty(userContentText);
        String newContent = userContentText;
        while (true) {
            if (!newContent.contains(REPLACE_KEY_OF_QUERY_WRAPPER)) {
                break;
            }
            if (hasDialogue) {
                newContent = newContent.replace(REPLACE_KEY_OF_QUERY_WRAPPER, "{" + dialogue + "}");
            } else {
                newContent = newContent.replace(REPLACE_KEY_OF_QUERY_WRAPPER, "");
            }
        }
        return newContent;
    }

    /**
     * 视觉大模型请求类生成
     *
     * @param userId
     * @param sessionId
     * @param dialogueId
     * @return
     */
    private TextModelVlReqDTO toSimpleVlReqDTO(String userId, String sessionId, String dialogueId) {
        TextModelVlReqDTO reqDTO = new TextModelVlReqDTO();
        reqDTO.setTaskId(dialogueId);
        reqDTO.setUserId(userId);
        reqDTO.setSessionId(sessionId);
        return reqDTO;
    }

    /**
     * url下载文件和处理压缩转换
     *
     * @param url
     * @param imageExt
     * @return
     */
    public ImageOutput urlToLocalPathAndProcess(BusinessModelConfig businessModelConfig, String url, String imageExt) {
        ImageOutput imageOutput = null;
        try {
            String filePath = imageCommonService.urlToLocalPath(url, imageExt, RequestContextHolder.getUserId());
            if (StringUtils.isNotEmpty(filePath)) {
                File file = new File(filePath);
                if (null != file && !file.exists()) {
                    throw new YunAiBusinessException(YunAiCommonResultCode.FILE_UPLOAD_FAILED);
                }

                imageOutput = cmicImageClient.convertAndCompressImage(ImageInput.builder().srcFile(file)
                        .accuracy(businessModelConfig.getAccuracy()).destSize(businessModelConfig.getDestSize())
                        .destHeight(businessModelConfig.getDestHeight()).destWidth(businessModelConfig.getDestWidth())
                        .build());
            } else {
                throw new YunAiBusinessException(YunAiCommonResultCode.FILE_UPLOAD_FAILED);
            }
        } catch (Exception e) {
            log.error("httpUtilDownloadFile url:{}, imageExt:{}, error:", url, imageExt, e);
            throw new YunAiBusinessException(YunAiCommonResultCode.FILE_UPLOAD_FAILED);
        }
        return imageOutput;
    }

    /**
     * 删除本地文件
     *
     * @param imageOutput
     */
    private void deleteLocalFile(ImageOutput imageOutput) {
        String path = null;
        try {
            if (null != imageOutput && null != imageOutput.getDesFile()) {
                path = (imageOutput.getDesFile().getAbsolutePath());
                log.info("删除共享存储... path:{}", path);
                FileUtil.del(imageOutput.getDesFile());
            }
        } catch (Exception e) {
            log.error("deleteLocalFile path:{} error:", path, e);
        }

    }
}
