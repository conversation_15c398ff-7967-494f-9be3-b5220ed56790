package com.zyhl.yun.api.outer.application.service.task;

import com.zyhl.yun.api.outer.application.dto.AITaskBenefitReqDTO;
import com.zyhl.yun.api.outer.application.dto.AIToolsConsumeDTO;
import com.zyhl.yun.api.outer.vo.BenefitPayResultVO;
import com.zyhl.yun.api.outer.vo.BenefitUseTimesLimitVO;

import javax.validation.Valid;

/**
 * AI算法任务权益接口
 *
 * <AUTHOR>
 */
public interface AlgorithmTaskBenefitService {

    /**
     * 算法任务权益扣费结果信息
     *
     * @param dto
     * @return
     */
    BenefitPayResultVO benefitPay(@Valid AITaskBenefitReqDTO dto);

    /**
     * 算法任务权益核销
     *
     * @param dto the dto
     * @return {@link BenefitUseTimesLimitVO}
     * <AUTHOR>
     * @date 2024-8-16 17:25
     */
    BenefitUseTimesLimitVO algorithmTaskToolsConsume(AIToolsConsumeDTO dto);
}
