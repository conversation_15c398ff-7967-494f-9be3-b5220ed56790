package com.zyhl.yun.api.outer.application.convertor.dto.dynamic;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.zyhl.yun.api.outer.application.vo.knowledge.PersonalKnowledgeShareMemberVO;
import com.zyhl.yun.api.outer.domain.dto.UserInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeInviteEntity;
import com.zyhl.yun.api.outer.enums.UserKnowledgeInviteLevelEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.Optional;

/**
 * User Knowledge Invite Convertor
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-04-17 10:48:39
 */
@Mapper(componentModel = "spring", imports = {DateUtil.class, DatePattern.class, Optional.class, UserKnowledgeInviteLevelEnum.class})
public interface UserKnowledgeInviteConvertor {

    @Mapping(target = "baseId", source = "entity.knowledgeId")
    @Mapping(target = "userProfileInfo", source = "userInfo.userProfileInfo")
    @Mapping(target = "level", expression = "java(Optional.ofNullable(entity.getLevel()).map(UserKnowledgeInviteLevelEnum::getCode).orElseGet(UserKnowledgeInviteLevelEnum.MEMBER::getCode))")
    @Mapping(target = "joinTime", expression = "java(DateUtil.format(entity.getCreateTime(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN))")
    PersonalKnowledgeShareMemberVO toPersonalKnowledgeShareMemberVO(UserKnowledgeInviteEntity entity, UserInfoDTO userInfo);
}
