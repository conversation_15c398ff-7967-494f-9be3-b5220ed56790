package com.zyhl.yun.api.outer.application.vo.knowledge;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 个人知识库文件删除与转存结果
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class KnowledgeFileTaskResVO {

    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 资源类型
     * 0--文件
     * 1--邮件
     * 2--笔记
     * 3--在线链接
     */
    private Integer resourceType;
    /**
     * 文件特有属性 - 类型，枚举值file/folder
     */
    private String type;

    private String errorCode;
    private String message;

}