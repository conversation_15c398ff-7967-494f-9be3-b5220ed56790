package com.zyhl.yun.api.outer.application.dto;


import cn.hutool.core.collection.CollUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 历史会话删除-DTO
 * @Author: WeiJingKun
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class AlgorithmChatDeleteDTO extends BaseDTO {

    /**
     * 会话idList数量限制
     */
    private static final Integer SESSION_LIST_MAX_SIZE = 500;

    /**
     * 渠道来源
     * 详见，配置文件：source-channels
     */
    @NotBlank(message = "渠道来源不能为空")
    private String sourceChannel;

    /** 会话idList */
    private List<Long> sessionIdList;

    /**
     * 参数校验
     * @Author: WeiJingKun
     */
    public void validate() {
        /** 检查登录的userId */
        checkTokenUserId();

        /** 检查会话id数量 */
        if (CollUtil.isNotEmpty(sessionIdList) && sessionIdList.size() > SESSION_LIST_MAX_SIZE) {
            log.error("会话idList数量超过" + SESSION_LIST_MAX_SIZE);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS.getResultCode(), "会话idList数量超过500");
        }
    }

}
