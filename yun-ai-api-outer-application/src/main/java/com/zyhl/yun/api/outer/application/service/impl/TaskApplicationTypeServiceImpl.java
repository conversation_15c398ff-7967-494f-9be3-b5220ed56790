package com.zyhl.yun.api.outer.application.service.impl;

import com.github.pagehelper.PageInfo;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.hbase.domain.datahelper.repository.HbaseRepository;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.YunDiskClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.BatchFileDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.FileResult;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.BatchFileVO;
import com.zyhl.yun.api.outer.application.chatv2.pojo.TaskPromptOfChatInfo;
import com.zyhl.yun.api.outer.application.dto.BaseDTO;
import com.zyhl.yun.api.outer.application.dto.ReadTaskDTO;
import com.zyhl.yun.api.outer.application.dto.ReadTaskReqDTO;
import com.zyhl.yun.api.outer.application.dto.SpeedReadResultVO;
import com.zyhl.yun.api.outer.application.enums.TaskModuleEnum;
import com.zyhl.yun.api.outer.application.service.TaskApplicationTypeService;
import com.zyhl.yun.api.outer.application.vo.SpeedReadTaskVO;
import com.zyhl.yun.api.outer.constants.CommonConstant;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.TaskAiAbilityEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.CenterTaskCreateEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.TextParamEntity;
import com.zyhl.yun.api.outer.domain.vo.common.PageInfoVO;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.DelFlagEnum;
import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;
import com.zyhl.yun.api.outer.external.FastReadTaskExternalService;
import com.zyhl.yun.api.outer.external.client.req.centertask.BusinessParam;
import com.zyhl.yun.api.outer.external.client.req.centertask.TextParam;
import com.zyhl.yun.api.outer.persistence.po.AISpeedReadResultPO;
import com.zyhl.yun.api.outer.repository.TaskAiAbilityRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * {@code @projectName} yun-ai-api-outer
 * <p>
 * {@code @description}
 * <p>
 *
 * <AUTHOR>
 * @since 4月15 2025
 */
@Slf4j
@Service
public class TaskApplicationTypeServiceImpl implements TaskApplicationTypeService {

    @Resource
    private FastReadTaskExternalService fastReadTaskExternalService;
    @Resource
    private UidGenerator uidGenerator;
    @Resource
    private YunDiskClient yunDiskClient;
    @Resource
    private HbaseRepository hbaseRepository;
    @Resource
    private TaskAiAbilityRepository taskAiAbilityRepository;

    @Value("${speed-read.supplier-code-list:2}")
    private String supplierCode;

    @Override
    public SpeedReadTaskVO createReadTask(ReadTaskDTO dto) {
        StopWatch sw = new StopWatch("创建快速阅读任务");
        // 1. 参数校验
        this.validUserId(dto);
        String userId = dto.getUserId();
        if (Objects.isNull(dto.getFile()) || StrUtil.isBlank(dto.getFile().getFileId())) {
            log.error("文件id为空");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        String fileId = dto.getFile().getFileId();
        // 2. 创建图书处理任务
        long taskId = uidGenerator.getUID();
        String textTool = DialogueIntentionEnum.TEXT_TOOL.getCode();
        String speedRead = DialogueIntentionSubEnum.SPEED_READ.getCode();
        CenterTaskCreateEntity createEntity = CenterTaskCreateEntity.builder().id(taskId).userId(userId)
                .algorithmCodes(ListUtil.toList(textTool)).subAlgorithmCodes(ListUtil.toList(speedRead))
                .supplierTypes(ListUtil.toList(supplierCode))
                .sourceChannel(dto.getSourceChannel()).build();
        TextParamEntity textParamEntity = new TextParamEntity();
        String rowKey = getSpeedReadRowKey(userId, String.valueOf(taskId));
        textParamEntity.setRowkey(rowKey);
        textParamEntity.setUserId(userId);
        textParamEntity.setFileId(fileId);
        sw.start("创建任务");
        fastReadTaskExternalService.createCommonTextTask(createEntity, textParamEntity);
        sw.stop();
        // 4. 返回结果
        Date date = new Date();
        SpeedReadTaskVO build = SpeedReadTaskVO.builder().taskId(String.valueOf(taskId))
                .status(TaskStatusEnum.IN_PROCESS.getCode()).createdAt(date).updatedAt(date).build();
        sw.start("获取文件信息");
        BatchFileVO batchFileVO = getBatchFileVO(userId, ListUtil.toList(fileId));
        log.info("获取到的文件信息：{}", batchFileVO);
        sw.stop();
        if (Objects.nonNull(batchFileVO) && CollUtil.isNotEmpty(batchFileVO.getBatchFileResults())) {
            build.setFile(batchFileVO.getBatchFileResults().get(NumberUtils.INTEGER_ZERO).getSrcFile());
        }
        log.info("创建快速阅读任务耗时:{}ms", sw.prettyPrint(TimeUnit.MILLISECONDS));
        return build;
    }

    @Override
    public PageInfoVO<SpeedReadTaskVO> readTaskList(ReadTaskReqDTO dto) {
        this.validUserId(dto);
        String userId = dto.getUserId();
        TaskAiAbilityEntity taskAiAbilityEntity = new TaskAiAbilityEntity();
        taskAiAbilityEntity.setUserId(userId);
        taskAiAbilityEntity.setAlgorithmCode(DialogueIntentionEnum.TEXT_TOOL.getCode());
        taskAiAbilityEntity.setSubAlgorithmCode(DialogueIntentionSubEnum.SPEED_READ.getCode());
        taskAiAbilityEntity.setDelFlag(DelFlagEnum.NO.getCode());
        PageInfoDTO pageInfo = PageInfoDTO.getReqDTO(dto.getPageInfo());
        String pageCursor = pageInfo.getPageCursor();
        Integer pageSize = pageInfo.getPageSize();
        Integer needTotalCount = pageInfo.getNeedTotalCount();
        PageInfo<TaskAiAbilityEntity> pageList = taskAiAbilityRepository.getFastReadTaskList(taskAiAbilityEntity,
                Integer.valueOf(pageCursor), pageSize, needTotalCount);
        List<TaskAiAbilityEntity> taskList = pageList.getList();
        PageInfoVO<SpeedReadTaskVO> result = new PageInfoVO<>();
        result.setList(Collections.emptyList());
        if (CollUtil.isEmpty(taskList)) {
            return result;
        }
        // 旧：任务状态，1 待处理、2处理中、3 任务完成、4 任务失败 5.已过期
        // 新：-1--处理失败 0--处理中 1--处理成功
//        taskList.forEach(entity -> {
//            Integer taskStatus = entity.getTaskStatus();
//            entity.setTaskStatus(TaskStatusEnum.getCodeByOldCode(taskStatus));
//        });

        // 非空
        int nextPageCursor = Integer.parseInt(pageCursor) + pageSize;
        if (Integer.valueOf(1).equals(needTotalCount)) {
            // 有total的情况直接用total判断是否有下一页
            result.setTotalCount(pageList.getTotal());
            if (nextPageCursor < pageList.getTotal()) {
                result.setNextPageCursor(String.valueOf(nextPageCursor));
            }
        }
        result.setNextPageCursor(String.valueOf(nextPageCursor));
        List<SpeedReadTaskVO> speedReadTaskVOS = new ArrayList<>();
        List<String> fileIds = new ArrayList<>();
        taskList.forEach(x -> {
            String businessParam = x.getBusinessParam();
            BusinessParam bean = getBusinessParam(businessParam);
            String fileId = bean.getTextParam().getFileId();
            fileIds.add(fileId);
            SpeedReadTaskVO speedReadTaskVO = SpeedReadTaskVO.builder().taskId(String.valueOf(x.getId()))
                    .status(x.getTaskStatus()).fileId(fileId).createdAt(x.getCreateTime()).updatedAt(x.getUpdateTime())
                    .errorMessage(x.getResultMsg()).build();
            speedReadTaskVOS.add(speedReadTaskVO);
        });
        // 填充文件信息
        BatchFileVO batchFileVO = getBatchFileVO(userId, fileIds);
        log.info("获取到的文件信息：{}", batchFileVO);
        Map<String, FileResult> fileResultMap = batchFileVO.getBatchFileResults().stream()
                .collect(Collectors.toMap(k -> k.getSrcFile().getFileId(), Function.identity(), (v1, v2) -> v1));
        speedReadTaskVOS.forEach(x -> {
            FileResult fileResult = fileResultMap.get(x.getFileId());
            x.setFile(fileResult.getSrcFile());
        });
        result.setList(speedReadTaskVOS);
        return result;

    }

    @Override
    public Boolean readTaskDelete(ReadTaskDTO dto) {
        String taskId = dto.getTaskId();
        this.validUserId(dto);
        String userId = dto.getUserId();
        // 参数校验
        if (StrUtil.isBlank(taskId)) {
            log.error("taskId为空");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        TaskAiAbilityEntity taskAiAbilityEntity = new TaskAiAbilityEntity();
        taskAiAbilityEntity.setId(Long.valueOf(taskId));
        taskAiAbilityEntity.setUserId(userId);
        Boolean tidbDelete = taskAiAbilityRepository.readTaskDelete(taskAiAbilityEntity);
        if (!tidbDelete) {
            log.info("数据库删除异常，参数：{}", dto);
            return tidbDelete;
        }
        String rowKey = getSpeedReadRowKey(userId, taskId);
        return hbaseRepository.delByRowKeyList(ListUtil.toList(rowKey), AISpeedReadResultPO.class);
    }

    @Override
    public SpeedReadResultVO readTaskGet(ReadTaskDTO dto) {
        String taskId = dto.getTaskId();
        this.validUserId(dto);
        String userId = dto.getUserId();
        if (StrUtil.isBlank(taskId)) {
            log.error("任务id为空");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        SpeedReadResultVO vo = new SpeedReadResultVO();
        // 1、获取任务信息
        TaskAiAbilityEntity taskEntity = taskAiAbilityRepository.getTaskEntityForSpeedRead(Long.valueOf(taskId));
        if (Objects.isNull(taskEntity)) {
            log.info("任务不存在，taskId:{}", taskId);
            return vo;
        }
        // 状态为任务失败、任务已过期
        if (TaskStatusEnum.PROCESS_FAILURE.getCode().equals(taskEntity.getTaskStatus())
                || TaskStatusEnum.OVERDUE.getCode().equals(taskEntity.getTaskStatus())) {
            // 处理返回结果
            AiResultCode aiResultCode = AiResultCode.getByCodeOrMsg(taskEntity.getResultCode(), taskEntity.getResultMsg());
            vo.setAiResultCode(aiResultCode);
        }
        vo.setTaskId(taskId);
        vo.setSessionId(Long.toString(taskEntity.getId()));
        vo.setStatus(taskEntity.getTaskStatus());
        vo.setErrorMessage(taskEntity.getResultMsg());
        vo.setCreatedAt(taskEntity.getCreateTime());
        vo.setUpdatedAt(taskEntity.getUpdateTime());
        // 2、获取文件
        Optional.ofNullable(taskEntity.getBusinessParam()).map(this::getBusinessParam).map(BusinessParam::getTextParam)
                .map(TextParam::getFileId).ifPresent(fileId -> {
                    try {
                        BatchFileVO batchFileVO = getBatchFileVO(userId, ListUtil.toList(fileId));
                        log.info("获取到的文件信息：{}", batchFileVO);
                        Optional.ofNullable(batchFileVO).map(BatchFileVO::getBatchFileResults)
                                .filter(CollUtil::isNotEmpty)
                                .ifPresent(results -> vo.setFile(results.get(NumberUtils.INTEGER_ZERO).getSrcFile()));
                    } catch (Exception e) {
                        log.error("获取文件异常：", e);
                    }
                });
        // 3、获取分析内容
        List<AISpeedReadResultPO> aiSpeedReadResultList = hbaseRepository
                .selectList(ListUtil.toList(getSpeedReadRowKey(userId, taskId)), AISpeedReadResultPO.class);
        Optional.ofNullable(aiSpeedReadResultList).filter(CollUtil::isNotEmpty).ifPresent(list -> {
            AISpeedReadResultPO aiSpeedReadResult = list.get(NumberUtils.INTEGER_ZERO);
            vo.setSummary(aiSpeedReadResult.getSummary());
            vo.setOutline(aiSpeedReadResult.getOutline());
            vo.setMindMap(aiSpeedReadResult.getMindMap());
            log.info("==> 快速阅读任务id:{} ,当前全文的字数：{}", aiSpeedReadResult.getTaskId(),
                    aiSpeedReadResult.getFullText().length());
        });
        return vo;
    }

    public String getSpeedReadRowKey(String userId, String taskId) {
        // rowKey_userId_taskId
        return CommonConstant.ROW_KEY_PREFIX + userId + StrUtil.UNDERLINE + taskId;
    }

    private BusinessParam getBusinessParam(String businessParam) {
        if (StrUtil.isEmpty(businessParam)) {
            return BusinessParam.builder().textParam(new TextParam()).build();
        }
        return JSONUtil.toBean(businessParam, BusinessParam.class);
    }

    /**
     * 批量获取文件
     *
     * @param userId
     * @param fileIds
     * @return
     */
    private BatchFileVO getBatchFileVO(String userId, List<String> fileIds) {
        if (CollUtil.isEmpty(fileIds)) {
            return new BatchFileVO();
        }
        BatchFileDTO reqDTO = new BatchFileDTO();
        reqDTO.setUserId(userId);
        reqDTO.setFileIds(fileIds.stream().distinct().collect(Collectors.toList()));
        reqDTO.setBelongsPlatform(RequestContextHolder.getBelongsPlatform());
        return yunDiskClient.fileBatchGetByAllPlatform(reqDTO);
    }

    /**
     * 校验用户id
     *
     * <AUTHOR>
     * @date 2025-4-20 14:36
     */
    private <T extends BaseDTO> void validUserId(T dto) {
        String userId = RequestContextHolder.getUserId();
        if (StringUtils.isBlank(userId) && StringUtils.isBlank(dto.getUserId())) {
            log.warn("==> 快速阅读任务，userId参数为空.");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_FORBIDDEN);
        } else if (StringUtils.isEmpty(dto.getUserId())) {
            dto.setUserId(userId);
        } else if (StringUtils.isEmpty(userId)) {
            log.info("==> 快速阅读任务，请求头未带token解析不到userId, 根据Body获取userId{}", dto.getUserId());
            userId = dto.getUserId();
            RequestContextHolder.setUserId(userId);
        }
    }

    @Override
    public String getSpeedReadFullText(ReadTaskDTO dto) {
        String rowKey = getSpeedReadRowKey(dto.getUserId(), dto.getTaskId());
        List<AISpeedReadResultPO> resultList = hbaseRepository.selectList(Collections.singletonList(rowKey),
                AISpeedReadResultPO.class);
        if (CollUtil.isEmpty(resultList)) {
            log.error("获取hbase全文失败，rowKey:{}", rowKey);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        AISpeedReadResultPO speedReadResult = resultList.get(0);
        String fullText = speedReadResult.getFullText();
        if (StringUtils.isBlank(fullText)) {
            log.error("hbase全文为空，rowKey:{}", rowKey);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        return fullText;
    }

    @Override
    public void updateSpeedReadHbaseResult(String userId, String taskId, List<TaskPromptOfChatInfo> taskPromptOfChatInfos) {
        String rowKey = getSpeedReadRowKey(userId, taskId);
        List<AISpeedReadResultPO> resultList = hbaseRepository.selectList(Collections.singletonList(rowKey),
                AISpeedReadResultPO.class);
        if (CollUtil.isEmpty(resultList)) {
            log.error("获取hbase全文失败，rowKey:{}", rowKey);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        AISpeedReadResultPO speedReadResult = resultList.get(0);
        for (TaskPromptOfChatInfo taskPromptOfChatInfo : taskPromptOfChatInfos) {
            if (!(taskPromptOfChatInfo.isComplete() && StringUtils.isNotEmpty(taskPromptOfChatInfo.getChatResult()))) {
                continue;
            }
            if (TaskModuleEnum.DOC_SUMMARY.getType().equals(taskPromptOfChatInfo.getModule())) {
                speedReadResult.setSummary(taskPromptOfChatInfo.getChatResult());
            }
            if (TaskModuleEnum.DOC_MIND_MAP.getType().equals(taskPromptOfChatInfo.getModule())) {
                speedReadResult.setMindMap(taskPromptOfChatInfo.getChatResult());
            }
            if (TaskModuleEnum.DOC_FULL_OUTLINE.getType().equals(taskPromptOfChatInfo.getModule())) {
                speedReadResult.setOutline(taskPromptOfChatInfo.getChatResult());
            }
        }
        hbaseRepository.saveList(Collections.singletonList(speedReadResult), AISpeedReadResultPO.class);
    }
}
