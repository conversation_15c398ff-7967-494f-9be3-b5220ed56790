package com.zyhl.yun.api.outer.application.service.impl;

import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.AliPptClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.dto.AliPptBaseDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.vo.TemplateCategoryVO;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.application.dto.PptTemplateDTO;
import com.zyhl.yun.api.outer.application.service.PptTemplateService;
import com.zyhl.yun.api.outer.application.vo.AiPptTemplateInfoVO;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 阿里AI-PPT模板 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/23
 */
@Slf4j
@Service
public class PptTemplateServiceImpl implements PptTemplateService {

    @Resource
    private AliPptClient aliPptClient;

    @Resource
    private ChatTextToolBusinessConfig chatTextToolBusinessConfig;

    @Override
    public Map<String, List<AiPptTemplateInfoVO>> pptTemplateList(PptTemplateDTO dto) {
        Map<String, List<AiPptTemplateInfoVO>> resultMap = new LinkedHashMap<>(16);
        // 远程调用阿里获取ppt模板列表信息
        List<TemplateCategoryVO> templateCategory = aliPptClient.getTemplateCategory(new AliPptBaseDTO(RequestContextHolder.getToken()));
        //处理过滤白名单模板的方法，将templateCategory进行过滤返回
        filterTemplateWhiteCategory(templateCategory);

        if (CollUtil.isNotEmpty(templateCategory)) {
            for (TemplateCategoryVO templateCategoryVO : templateCategory) {
                String name = templateCategoryVO.getName();
                List<TemplateCategoryVO.Template> temps = templateCategoryVO.getTemps();
                if (CollUtil.isEmpty(temps)) {
                    continue;
                }
                List<AiPptTemplateInfoVO> categoryTemplates = new ArrayList<>();
                for (TemplateCategoryVO.Template template : temps) {
                    AiPptTemplateInfoVO vo = new AiPptTemplateInfoVO();
                    vo.setTemplateId(String.valueOf(template.getId()));
                    vo.setCover(template.getCover());
                    vo.setPreviewList(template.getPreview());
                    vo.setTitle(template.getTitle());
                    categoryTemplates.add(vo);
                }
                resultMap.put(name, categoryTemplates);
            }
        }
        return resultMap;
    }

    /**
     * 过滤白名单模板
     *
     * @param templateCategory the template category
     * <AUTHOR>
     * @date 2025/5/27 11:26
     */
    private void filterTemplateWhiteCategory(List<TemplateCategoryVO> templateCategory) {
        Optional<List<ChatTextToolBusinessConfig.PPTTemplateWhite>> pptTemplateWhitesOptional =
                Optional.of(chatTextToolBusinessConfig)
                        .map(ChatTextToolBusinessConfig::getAiPptGenerate)
                        .map(ChatTextToolBusinessConfig.AiPptGenerateBusiness::getTemplateWhiteList);
        if (pptTemplateWhitesOptional.isPresent() && CollUtil.isNotEmpty(pptTemplateWhitesOptional.get())) {
            List<ChatTextToolBusinessConfig.PPTTemplateWhite> pptTemplateWhites = pptTemplateWhitesOptional.get();
            String phoneNumber = RequestContextHolder.getPhoneNumber();
            //templateCategory进行过滤，当templateCategory里面的temps中的模板id , 在pptTemplateWhites中存在，
            // phoneNumber不在pptTemplateWhite的userList中存在，则返回true，进行过滤
            templateCategory.forEach(templateCategoryVO -> {
                if (CollUtil.isNotEmpty(templateCategoryVO.getTemps())) {
                    templateCategoryVO.getTemps().removeIf(
                            template -> pptTemplateWhites.stream().anyMatch(pptTemplateWhite ->
                                    pptTemplateWhite.getIds().contains(String.valueOf(template.getId())) &&
                                            !pptTemplateWhite.getUserList().contains(phoneNumber)));
                }
            });
        }
    }
}
