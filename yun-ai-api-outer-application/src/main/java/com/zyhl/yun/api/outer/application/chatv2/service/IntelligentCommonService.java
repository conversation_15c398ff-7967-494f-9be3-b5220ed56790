package com.zyhl.yun.api.outer.application.chatv2.service;

import com.zyhl.yun.api.outer.application.chatv2.dto.AssistantChatV2PollingUpdateDTO;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueResultV2VO;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;

/**
 * 智能体对话-公共-服务接口
 *
 * <AUTHOR>
 * @date 2025-07-18 16:38
 **/
public interface IntelligentCommonService {

	/**
	 * 内容轮询，异常不抛出，返回null
	 * 
	 * @param pollingUpdate 轮询参数
	 * @return 内容信息
	 */
	DialogueResultV2VO contentPollingUpdate(AssistantChatV2PollingUpdateDTO pollingUpdate);

	/**
	 * 获取当前对话id之前最新一条(会议通知邮件)意图对话
	 * 
	 * @param userId     用户id
	 * @param sessionId  会话id
	 * @param dialogueId 当前对话id
	 * @return
	 */
	public AlgorithmChatContentEntity getLastIntentionDialogue(String userId, Long sessionId, String dialogueId);

	/**
	 * 通过对话信息获取邮件信息
	 * 
	 * @param lastMailDialogueInfo 上一次发邮件对话信息
	 * @return 邮件信息
	 */
	public MailInfoVO getMailInfoResult(AlgorithmChatContentEntity lastMailDialogueInfo);

}
