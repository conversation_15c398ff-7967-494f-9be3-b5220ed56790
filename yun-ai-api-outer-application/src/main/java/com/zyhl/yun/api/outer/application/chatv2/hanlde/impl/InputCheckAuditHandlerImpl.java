package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.domainservice.CheckSystemDomainService;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 输入内容送审
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class InputCheckAuditHandlerImpl extends AbstractChatAddV2Handler {

    /**
     * 当前执行顺序
     */
    private ExecuteSort thisExecuteSort = ExecuteSort.INPUT_AUDIT;

    @Resource
    protected CheckSystemDomainService checkSystemDomainService;

    @Override
    public void afterPropertiesSet() throws Exception {
    	//支持的业务初始化
    	List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        thisBusinessTypes.add(ChatBusinessTypeEnum.NOTE);
    	this.setBusinessTypes(thisBusinessTypes);
    }
    
    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public boolean execute(ChatAddHandleDTO innerDTO) {
        return true;
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        log.info("进入{}", thisExecuteSort.getDesc());

        //会话输入内容送审
        checkSystemDomainService.checkLocalAndPlatformException(handleDTO.getDialogueId(),
                RequestContextHolder.getUserId(), handleDTO.getInputInfoDTO().getDialogue());

        return true;
    }


}
