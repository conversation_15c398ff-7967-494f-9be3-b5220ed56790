package com.zyhl.yun.api.outer.application.dto;

import com.zyhl.yun.api.outer.enums.chat.ApiVersionEnum;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * 会话输入入参DTO
 *
 * <AUTHOR>
 * @data 2024/2/29 14:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class AlgorithmChatAddDTO extends BaseDTO {

    /**
     * 表情字符，需要加提示词：以中文回复
     */
    private static final String EMOJI_WITH_CN = "以中文回复";

    /**
     * 会话ID 不传则默认新创建会话
     */
    private String sessionId;

    /**
     * 应用Id
     */
    private String applicationId;

    /**
     * 应用类型 chat普通对话 intelligent智能体对话
     */
    private String applicationType;

    /**
     * 对话内容
     */
    private AlgorithmChatAddContentDTO content;

    /**
     * 是否强制联网搜索
     */
    private Boolean enableForceNetworkSearch;

    /**
     * 是否强制大模型回答，默认值为false
     * true时不走干预库和意图识别，直接大模型对话
     */
    private Boolean enableForceLlm;

    /**
     * (非前端入参)请求头参数：api版本
     */
    private String apiVersion = ApiVersionEnum.V1.getVersion();

    /**
     * (非前端入参)任务id，自己生成
     */
    private Long taskId;

    /**
     * 是否走云盘AI全网搜
     * 默认值为false
     */
    private Boolean enableAllNetworkSearch = Boolean.FALSE;

    /**
     * 是否开启AI搜索
     * 默认值为false
     */
    private Boolean enableAiSearch = Boolean.FALSE;

}
