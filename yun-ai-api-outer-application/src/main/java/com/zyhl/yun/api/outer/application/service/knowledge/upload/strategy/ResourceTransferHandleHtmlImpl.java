package com.zyhl.yun.api.outer.application.service.knowledge.upload.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.dashscope.utils.JsonUtils;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.vo.CheckResultVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.properties.OwnerDriveProperties;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileCheckReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.ResourceTransferReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.upload.AbstractResourceTransferHandle;
import com.zyhl.yun.api.outer.application.util.DomainValidatorUtils;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeAddResultVO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeFileImportVO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeTaskResultVO;
import com.zyhl.yun.api.outer.config.KnowledgePersonalProperties;
import com.zyhl.yun.api.outer.domain.entity.PersonalKnowledgeImportTaskEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeUploadEntity;
import com.zyhl.yun.api.outer.domain.vo.ImportHtmlInfoVO;
import com.zyhl.yun.api.outer.domain.vo.ImportMailInfoVO;
import com.zyhl.yun.api.outer.domain.vo.PersonalKnowledgeImportCheckResult;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileTaskResultEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeUploadStatusEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 描述：笔记资源文件处理
 *
 * <AUTHOR> zhumaoxian  2025/3/21 17:19
 */
@Slf4j
@Component
public class ResourceTransferHandleHtmlImpl extends AbstractResourceTransferHandle {

    @Resource
    private KnowledgePersonalProperties knowledgePersonalProperties;

    @Resource
    private OwnerDriveProperties ownerDriveProperties;


    private static final Pattern  htmlPattern = Pattern.compile("^https?.*");

    @Override
    public void afterPropertiesSet() throws Exception {
        register(KnowledgeResourceTypeEnum.HTML.getCode(), this);
    }

    @Override
    public KnowledgeFileImportVO trans(ResourceTransferReqDTO dto) {
        ImportHtmlInfoVO html = dto.getHtmlInfo();
        KnowledgeFileImportVO importVO = new KnowledgeFileImportVO();
        importVO.setBaseId(String.valueOf(dto.getBaseId()));

        List<PersonalKnowledgeImportCheckResult> errorList = new ArrayList<>();
        String url = html.getUrl();
        String ownerId = StrUtil.isNotBlank(dto.getOwnerId())? dto.getOwnerId() : ownerDriveProperties.getDefaultOwnerid();
        String userId =StrUtil.isNotBlank(dto.getUserId())? dto.getUserId() : RequestContextHolder.getUserId();

        // 已入库的html
        Map<String, PersonalKnowledgeImportTaskEntity> existMap = new HashMap<>(1);
        Map<String, UserKnowledgeFileEntity> fileEntityMap = new HashMap<>(1);
        List<String> urlList = new ArrayList<>(Collections.singletonList(url));

        userKnowledgeUploadRepository.selectHtmlResource(userId,dto.getBaseId(), urlList)
                .stream().filter(item -> Objects.nonNull(item.getHtmlInfo()))
                .forEach(item -> existMap.put(item.getHtmlInfo().getUrl(), item));

        userKnowledgeFileRepository.selectHtmlResource(userId, urlList,dto.getBaseId())
                .stream().filter(item -> Objects.nonNull(item.getHtmlInfo()))
                .forEach(item -> fileEntityMap.put(item.getHtmlInfo().getUrl(), item));

        // 循环校验文件
        List<KnowledgeAddResultVO> resultList = new ArrayList<>();
        List<String> addUrlList = new ArrayList<>();
        KnowledgeTaskResultVO resultVO = new KnowledgeTaskResultVO();

        if (!htmlPattern.matcher(url).matches() || url.length() > 2048) {
            resultList.add(new KnowledgeAddResultVO(html.getUrl(), ResultCodeEnum.HTML_ULR_FORMAT_ERROR));
        } else {
            try {
                String host = new URL(url).getHost();
                if (CollUtil.isEmpty(knowledgePersonalProperties.getHtmlUrlLegalHosts())){
                    log.error("html合法域名列表为空");
                    throw new YunAiBusinessException(ResultCodeEnum.HTML_URL_DOMAIN_ERROR);
                }
                // 域名校验
                if (!DomainValidatorUtils.isAllowed(url, knowledgePersonalProperties.getHtmlUrlLegalHosts())) {
                    resultList.add(new KnowledgeAddResultVO(html.getUrl(), ResultCodeEnum.HTML_URL_DOMAIN_ERROR));
                }
            } catch (Exception e) {
                resultList.add(new KnowledgeAddResultVO(html.getUrl(), ResultCodeEnum.HTML_ULR_FORMAT_ERROR));
            }
        }
        //格式验证通过
        if (CollUtil.isEmpty(resultList)) {
            // db校验
            PersonalKnowledgeImportTaskEntity entity = existMap.get(html.getUrl());
            UserKnowledgeFileEntity userKnowledgeFileEntity = fileEntityMap.get(html.getUrl());
            if (entity != null|| userKnowledgeFileEntity != null) {
                log.info("网页资源已上传，url：{}", html.getUrl());
                //记录重复上传的记录
                UserKnowledgeUploadEntity userKnowledgeUploadEntity = new UserKnowledgeUploadEntity();
                String htmlResource = JsonUtils.toJson(html);
                userKnowledgeUploadEntity.buildHtml(dto.getBaseId(),userId,ownerId,htmlResource,dto.getParentFileId());
                userKnowledgeUploadEntity.record(KnowledgeUploadStatusEnum.SUCCESS.getStatus(),ResultCodeEnum.FILE_REPEAT_UPLOAD.getResultCode(),ResultCodeEnum.FILE_REPEAT_UPLOAD.getResultMsg());
                resultVO.setStatus(FileTaskResultEnum.FAIL.getStatus());
                userKnowledgeUploadRepository.add(userKnowledgeUploadEntity);

                //返回失败结果给前端
                PersonalKnowledgeImportCheckResult importCheckResult = new PersonalKnowledgeImportCheckResult();
                importCheckResult.recordFail(KnowledgeResourceTypeEnum.HTML.getCode(), String.valueOf(dto.getBaseId()), html, KnowledgeUploadStatusEnum.FAIL.getStatus(), ResultCodeEnum.FILE_REPEAT_UPLOAD.getResultMsg());
                errorList.add(importCheckResult);
                importVO.setErrorList(errorList);
                return importVO;
            }
        }
        UserKnowledgeUploadEntity userKnowledgeUploadEntity = new UserKnowledgeUploadEntity();
        String htmlResource = JsonUtils.toJson(html);
        userKnowledgeUploadEntity.buildHtml(dto.getBaseId(),userId,ownerId,htmlResource,dto.getParentFileId());
        //
        if (CollUtil.isEmpty(resultList)) {
            // 校验通过
            resultList.add(new KnowledgeAddResultVO(html.getUrl(), ResultCodeEnum.SUCCESS.getResultCode(), FILE_EXIST_OTHER_LABEL));

            addUrlList.add(html.getUrl());

            resultVO.setStatus(FileTaskResultEnum.PROCESSING.getStatus());
            //保存上传文件表
            userKnowledgeUploadEntity.record(KnowledgeUploadStatusEnum.NOT_PROCESSED.getStatus(),null,null);

        } else {
            //html校验不通过
            KnowledgeAddResultVO addResultVO = resultList.get(0);
            userKnowledgeUploadEntity.record(KnowledgeUploadStatusEnum.FAIL.getStatus(),addResultVO.getResult(),addResultVO.getDescription());
            resultVO.setStatus(FileTaskResultEnum.FAIL.getStatus());
        }

        if (ObjectUtil.isNotEmpty(dto.getParentFileId())) {
            UserKnowledgeFileEntity fileEntity = userKnowledgeFileRepository.selectByFileId(dto.getUserId(), dto.getParentFileId());
            if(Objects.nonNull(fileEntity)){
                userKnowledgeUploadEntity.setTargetParentFileId(fileEntity.getFileId());
                userKnowledgeUploadEntity.setTargetParentFilePath(fileEntity.getParentFilePath() + "/" + fileEntity.getFileId());
            }
        }
        // 标题送审
        if(Boolean.FALSE.equals(fileCheck(html, dto.getUserId()))){
            //返回失败结果给前端
            PersonalKnowledgeImportCheckResult importCheckResult = new PersonalKnowledgeImportCheckResult();
            importCheckResult.setResourceType(dto.getResourceType());
            importCheckResult.setCheckResult(Integer.valueOf(ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultCode()));
            importCheckResult.setFailMessage(ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultMsg());
            importCheckResult.setHtmlInfo(html);
            errorList.add(importCheckResult);
            importVO.setErrorList(errorList);
            // 送审不通过，修改upload表状态
            userKnowledgeUploadEntity.setUploadStatus(KnowledgeUploadStatusEnum.FAIL.getStatus());
            userKnowledgeUploadEntity.setResultCode(ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultCode());
            userKnowledgeUploadEntity.setResultMsg(ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultMsg());
        }
        userKnowledgeUploadRepository.add(userKnowledgeUploadEntity);
        if (userKnowledgeUploadEntity.getUploadStatus().equals(KnowledgeUploadStatusEnum.NOT_PROCESSED.getStatus())){
            //发送上传文件mq
            knowledgeTransCategoryTaskMqService.sendMq(userKnowledgeUploadEntity);
        }

        return importVO;
    }

    /**
     * 网页标题送审
     * @param html 上传列表
     */
    private Boolean fileCheck(ImportHtmlInfoVO html, String userId) {
        boolean enabled = fileCheckConfig.isEnabled();
        log.info("batchImport html fileCheckConfig:{}",fileCheckConfig);
        if(Boolean.FALSE.equals(enabled)){
            // 开关打开
            String fileName = html.getTitle();
            long uid = uidGenerator.getUID();
            CheckResultVO resultVo = checkSystemDomainService.checkPlatformAndLocal(uid, userId, fileName);
            return !CheckResultVO.isFail(resultVo);
        }
        return Boolean.TRUE;
    }

    @Override
    public void check(KnowledgeFileCheckReqDTO dto) {
        String url = dto.getHtmlUrl();
        if (StrUtil.isBlank(url)){
            throw new YunAiBusinessException(ResultCodeEnum.HTML_URL_IS_NULL);
        }
        if (!htmlPattern.matcher(url).matches() || url.length() > 2048) {
            throw new YunAiBusinessException(ResultCodeEnum.HTML_ULR_FORMAT_ERROR);
        } else {
            try {
                String host = new URL(url).getHost();
                if (StrUtil.isBlank(host)){
                    throw new YunAiBusinessException(ResultCodeEnum.HTML_ULR_FORMAT_ERROR);
                }
                if (CollUtil.isEmpty(knowledgePersonalProperties.getHtmlUrlLegalHosts())){
                    log.error("html合法域名列表为空");
                    throw new YunAiBusinessException(ResultCodeEnum.HTML_URL_DOMAIN_ERROR);
                }
                // 域名校验
                if (!DomainValidatorUtils.isAllowed(url, knowledgePersonalProperties.getHtmlUrlLegalHosts())) {
                    throw new YunAiBusinessException(ResultCodeEnum.HTML_URL_DOMAIN_ERROR);
                }
            } catch (MalformedURLException e) {
                throw new YunAiBusinessException(ResultCodeEnum.HTML_ULR_FORMAT_ERROR);
            }
        }
    }


}
