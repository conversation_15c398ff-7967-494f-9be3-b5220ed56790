package com.zyhl.yun.api.outer.application.vo;

import lombok.Data;

/**
 * 获取对话状态响应对象
 *
 * <AUTHOR>
 */
@Data
public class ChatStatusVO {

    /**
     * 对话是否存在状态 1--存在 0--不存在
     */
    private Integer existStatus = 1;

    /**
     * 对话状态，当对话存在时返回
     */
    private Integer chatStatus;

    /**
     * 点赞状态，当对话存在时返回
     */
    private Integer likeStatus;

    public ChatStatusVO notExist() {
        this.existStatus = 0;
        return this;
    }
}
