package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeProfilePhoto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 更新个人知识库请求参数
 *
 * <AUTHOR>
 * @date 2025/04/15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class PersonalKnowledgeUpdateReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 知识库ID
     */
    private String baseId;

    /**
     * 知识库名称
     */
    private String name;

    /**
     * 知识库描述
     */
    private String description;

    /**
     * 公开/私密（1 公开，0私密，默认为0）
     */
    private Integer openLevel;

    /**
     * 头像信息
     */
    private PersonalKnowledgeProfilePhoto profilePhoto;

}