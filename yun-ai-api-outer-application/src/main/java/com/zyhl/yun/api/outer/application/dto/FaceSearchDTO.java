package com.zyhl.yun.api.outer.application.dto;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
public class FaceSearchDTO {

    /**
     * 请求ID
     */
    @NotBlank(message = "请求ID不能为空")
    private String requestId;
    /**
     * 图片base64
     */
    @NotBlank(message = "图片base64不可为空")
    private String base64;
    /**
     * 文件归属：用户Id/家庭Id/群组Id/圈子Id，根据ownerType界定
     */
    @NotBlank(message = "ownerId不能为空")
    private String ownerId;

    /**
     * 业务类型
     * 1-个人云，2-圈子，3-共享群，4-家庭云
     */
    @NotNull(message = "ownerType不能为空")
    private Integer ownerType;


    public void base64SizeDetermination(Integer maxFileSize) {

        byte[] imageBytes = Base64.getDecoder().decode(base64);
        File tempFile = null;
        long fileSizeInMb;
        try {
            String tempFileName = "temp-image-" + UUID.randomUUID() + ".jpg";
            tempFile = File.createTempFile(tempFileName, "");
            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                fos.write(imageBytes);
            }
            long fileSizeInBytes = tempFile.length();
            long fileSizeInKb = fileSizeInBytes / 1024;
            fileSizeInMb = fileSizeInKb / 1024;
        } catch (IOException e) {
            throw new YunAiBusinessException(ResultCodeEnum.FILE_BASE64_DATA_ERROR);
        } finally {
            tempFile.delete();
        }
        if (fileSizeInMb > maxFileSize) {
            log.error("人脸搜图-文件超过限制-base64转图片大小:{}M", fileSizeInMb);
            throw new YunAiBusinessException(ResultCodeEnum.FILE_SIZE_LARGE);
        }
    }
}
