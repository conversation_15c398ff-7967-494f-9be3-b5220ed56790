package com.zyhl.yun.api.outer.application.chatv2.dto;

import java.util.List;
import java.util.Optional;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * PPT生成入参抽取封装
 * <AUTHOR>
 * @date 2025/4/29
 */
@Slf4j
@Builder
@Getter
@AllArgsConstructor
public class AiPptValidationResult {
    /**
     * 上一次对话ID
     */
    private String previousDialogueId;
    /**
     * 编辑文本
     */
    private String editedOutline;
    /**
     * 模板ID
     */
    private String templateId;
    
    /**
     * PPT生成必填参数校验返回
     *
     * @param handleDTO
     * @return
     */
    public static AiPptValidationResult validateRequiredParams(ChatAddHandleDTO handleDTO) {
        Optional<DialogueInputInfoDTO> dialogueInputOptional = Optional.ofNullable(handleDTO)
                .map(ChatAddHandleDTO::getReqDTO)
                .map(ChatAddReqDTO::getDialogueInput);
        Optional<DialogueTextToolSettingDTO> pptSettingOptional = dialogueInputOptional
                .map(DialogueInputInfoDTO::getToolSetting)
                .map(DialogueToolSettingDTO::getTextToolSetting);
        String editedOutline = null;
        String templateId = null;
        if (pptSettingOptional.isPresent()) {
            DialogueTextToolSettingDTO pptSetting = pptSettingOptional.get();
            templateId = pptSetting.getTemplateId();
            if (templateId == null || templateId.isEmpty()) {
                log.error("templateId 必填参数不能为 null,dialogueId:{}", handleDTO.getDialogueId());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }
            editedOutline = pptSetting.getEditedOutline();
        }
        Optional<DialogueAttachmentDTO> attachmentOptional = dialogueInputOptional.map(DialogueInputInfoDTO::getAttachment);
        String previousDialogueId = null;
        if (attachmentOptional.isPresent()) {
            DialogueAttachmentDTO attachment = attachmentOptional.get();
            List<Integer> attachmentTypeList = attachment.getAttachmentTypeList();
            List<String> dialogueIdList = attachment.getDialogueIdList();
            if (attachmentTypeList.isEmpty() || dialogueIdList.isEmpty() ||
                    !ResourceTypeEnum.DIALOGUE.getType().equals(attachmentTypeList.get(0))) {
                log.error("dialogueIdList 必填参数不能为 null,dialogueId:{}", handleDTO.getDialogueId());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }
            previousDialogueId = dialogueIdList.get(0);
        }
        return new AiPptValidationResult(previousDialogueId, editedOutline, templateId);
    }
}
