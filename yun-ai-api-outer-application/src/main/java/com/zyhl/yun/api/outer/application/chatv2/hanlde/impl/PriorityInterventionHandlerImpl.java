package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;

import javax.annotation.Resource;

import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum;
import com.zyhl.yun.api.outer.application.assembler.ChatFlowResultAssembler;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueFlowResultVO;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.application.service.chat.DialogueRecommendService;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.domainservice.InterventionDomainService;
import com.zyhl.yun.api.outer.domainservice.RecommendQueryService;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.AiTextResultVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddFlowStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.InterventionVO;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 干预库匹配
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PriorityInterventionHandlerImpl extends AbstractChatAddV2Handler {

	/**
	 * 当前执行顺序
	 */
	private ExecuteSort thisExecuteSort = ExecuteSort.PRIORITY_INTERVENTION;

	/**
	 * 输入对话内容最大长度
	 */
	private static final int DIALOGUE_MAX_LEN = 512;

	@Resource
	private InterventionDomainService interventionDomainService;
	@Resource
	private RecommendQueryService recommendQueryService;
	@Resource
	private DataSaveService dataSaveService;
	@Resource
	private DialogueRecommendService dialogueRecommendService;
	@Resource
	private ChatConfigServiceDomainService chatConfigServiceDomainService;
	@Resource
	private ChatFlowResultAssembler chatFlowResultAssembler;

	@Override
	public void afterPropertiesSet() throws Exception {
		// 支持的业务初始化
		List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
		thisBusinessTypes.add(ChatBusinessTypeEnum.NOTE);
        this.setBusinessTypes(thisBusinessTypes);
	}

	@Override
	public int order() {
		return thisExecuteSort.getSort();
	}

	@Override
	public boolean execute(ChatAddHandleDTO handleDTO) {
		ChatAddReqDTO reqDTO = handleDTO.getReqDTO();
		DialogueInputInfoDTO inputInfoDTO = handleDTO.getInputInfoDTO();
		Integer attachmentType = (inputInfoDTO.getAttachment() == null
				|| CollectionUtils.isEmpty(inputInfoDTO.getAttachment().getAttachmentTypeList())) ? 0
						: inputInfoDTO.getAttachment().getAttachmentTypeList().get(0);
		String dialogue = inputInfoDTO.getDialogue();

		/**
		 * 不走干预库： 非对话类型 || 非文本资源 || 对话内容为空 || 对话内容超长 || 有对话指令 commands || 有prompt ||
		 * 走云盘AI全网搜
		 */

		String command = (inputInfoDTO.getCommand() == null) ? null : inputInfoDTO.getCommand().getCommand();
		if (ApplicationTypeEnum.isNotChat(reqDTO.getApplicationType()) || ResourceTypeEnum.isNotText(attachmentType)
				|| CharSequenceUtil.isEmpty(dialogue) || dialogue.length() > DIALOGUE_MAX_LEN
				|| CharSequenceUtil.isNotEmpty(command) || CharSequenceUtil.isNotEmpty(inputInfoDTO.getPrompt())
				|| inputInfoDTO.isEnableAllNetworkSearch()) {
			return false;
		}

		// 意图为空 || 文本意图
		return CharSequenceUtil.isEmpty(handleDTO.getIntentionCode())
				|| DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode().equals(handleDTO.getIntentionCode());
	}

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		DialogueInputInfoDTO inputInfoDTO = handleDTO.getInputInfoDTO();
		boolean enableAiSearch = inputInfoDTO.isEnableAiSearch();
		boolean enableAiEdite = inputInfoDTO.isEnableAiEdite();
		if (enableAiEdite) {
			// 邮箱AI编辑的不进入这个handler处理，直接返回，去执行下一个handler
			return true;
		}
		//走云邮AI搜索,需要拼接“搜索”两个字，让意图识别提取搜索意图
		if (enableAiSearch){
			String sourceChannel = handleDTO.getReqDTO().getSourceChannel();
			boolean isMail = SourceChannelsProperties.isMailChannel(sourceChannel);
			if (isMail){
				// 邮箱AI搜索的不进入这个handler处理，直接返回，去执行下一个handler
				return true;
			}
		}
		log.info("进入{}", thisExecuteSort.getDesc());
		// 干预库匹配
		InterventionVO interventionVO = interventionDomainService.getIntervention(
				RequestContextHolder.getSourceChannel(), RequestContextHolder.getClientType(),
				RequestContextHolder.getClientVersion(), RequestContextHolder.getH5Version(),
				inputInfoDTO.getDialogue());

		if (interventionVO == null) {
			log.debug("干预库没有命中答案，继续执行下一个节点");
			return true;
		}

		// 获取用户设置的模型，没有设置则使用默认模型
		ChatConfigEntity chatConfigEntity = chatConfigServiceDomainService.getUserCanUseModel(
				handleDTO.getReqDTO().getUserId(), RequestContextHolder.getPhoneNumber(), handleDTO.getAssistantEnum(),
				handleDTO.getBusinessType());

		// 干预库有答案
		handleDTO.setIntentionCode(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());
		handleDTO.setInterventionVO(interventionVO);

		// 使用用户的提问通过大模型获取推荐提问语句
		Future recommendQueryFuture = recommendQueryService.getRecommendQueryFuture(handleDTO.getDialogueId(),
				inputInfoDTO.getDialogue());

		// 聊天添加响应对象
		ChatAddRespVO respVO = handleDTO.getRespVO();
		// 获取推荐提问语句
		DialogueRecommendVO dialogueRecommendVO = new DialogueRecommendVO();
		dialogueRecommendService.setFuturesResult(handleDTO.getDialogueId(), dialogueRecommendVO,
				Collections.singletonList(recommendQueryFuture));
		respVO.setRecommend(dialogueRecommendVO);

		// 对话流式结果对象
		DialogueFlowResultVO flowResult = respVO.getFlowResult();
		flowResult.setOutContent(interventionVO.getAnswer());
		flowResult.setResultType(FlowResultTypeEnum.RICH_TEXT.getType());
		flowResult.setModelType(chatConfigEntity.getModelType());
		flowResult.setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());

		AiTextResultRespParameters respParameters = AiTextResultRespParameters.builder()
				.version(AiTextResultVersionEnum.V2.getVersion()).resultCode(ResultCodeEnum.SUCCESS.getResultCode())
				.resultMsg(ResultCodeEnum.SUCCESS.getResultMsg()).build();
		respParameters.setOutputList(Collections.singletonList(chatFlowResultAssembler.getFlowResult(flowResult)));
		respParameters.setOutputCommandVO(handleDTO.getIntentionVO());
		
		// 保存hbase
		dataSaveService.saveHbaseAllChatResult(handleDTO, respParameters);

		// 保存数据库
		dataSaveService.addSuccessAndModelCode(handleDTO, TextModelEnum.INTERVENTION.getCode(),
				OutContentTypeEnum.RICH_TEXT);

		// 流式响应
		handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(respVO));
		return false;
	}

}
