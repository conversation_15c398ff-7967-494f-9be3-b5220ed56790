package com.zyhl.yun.api.outer.application.service.task.impl;

import com.alibaba.fastjson.JSONObject;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.membercenter.resp.ConsumeAvailableBenefitRsp;
import com.zyhl.yun.api.outer.application.dto.AITaskBenefitReqDTO;
import com.zyhl.yun.api.outer.application.dto.AIToolsConsumeDTO;
import com.zyhl.yun.api.outer.application.service.external.MemberCenterService;
import com.zyhl.yun.api.outer.application.service.task.AlgorithmTaskBenefitService;
import com.zyhl.yun.api.outer.config.MemberCenterBenefitsProperties;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.domain.entity.AIToolsConsumeRecordEntity;
import com.zyhl.yun.api.outer.domain.entity.TaskAiAbilityEntity;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.task.TaskFeePaidStatusEnum;
import com.zyhl.yun.api.outer.enums.task.TaskFeeTypeEnum;
import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;
import com.zyhl.yun.api.outer.repository.AIToolsConsumeRecordRepository;
import com.zyhl.yun.api.outer.repository.TaskAiAbilityRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.BenefitPayResultVO;
import com.zyhl.yun.api.outer.vo.BenefitUseTimesLimitVO;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * AI算法任务接口实现类
 *
 * <AUTHOR>
 * @Date 2024/05/16 10:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlgorithmTaskBenefitServiceImpl implements AlgorithmTaskBenefitService {

    private static final String ALGORITHM_TASK_BENEFIT = "yun:ai:api:outer:taskBenefit:lock:%s";

    private static final Integer BENEFIT_REQUEST_COUNT = 3;

    private final TaskAiAbilityRepository taskAiAbilityRepository;
    private final MemberCenterBenefitsProperties memberCenterBenefitsProperties;
    private final MemberCenterService memberCenterService;
    @Resource
    private RedissonClient redissonClient;

    private final AIToolsConsumeRecordRepository aiToolsConsumeRecordRepository;

    /**
     * 不用权益扣费的命令，因为场景是在生成后用户自己选择保存扣费
     */
    private static final Set<String> NOT_CONSUME_CODE = new HashSet<>(Arrays.asList(
        DialogueIntentionEnum.PICTURE_GENERATE_TEXT.getCode(),
        DialogueIntentionEnum.TEXT_GENERATE_PICTURE.getCode(),
        DialogueIntentionEnum.LIVE_PHOTOS.getCode(),
        DialogueIntentionEnum.INTELLIGENT_BEAUTY.getCode()
    ));

    @Override
    public BenefitPayResultVO benefitPay(@Valid AITaskBenefitReqDTO dto) {
        Long taskId = Long.valueOf(dto.getTaskId());
        log.info("AlgorithmTaskBenefitService benefitPay taskId:{}", taskId);
        RLock lock = null;
        try {
            // 获取redis锁
            String lockKey = String.format(ALGORITHM_TASK_BENEFIT, taskId);
            lock = redissonClient.getLock(lockKey);
            boolean acquire = false;
            try {
                acquire = lock.tryLock(RedisConstants.WAIT_TIME_10, RedisConstants.LEASE_TIME_30, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("AlgorithmTaskBenefitService lock.tryLock lockKey:{} error:", lockKey, e);
            } finally {
                log.info("任务权益扣费加锁，taskId={}，获取锁状态：{}", taskId, acquire);
                if (!acquire) {
                    // 获取锁失败，请求过多异常
                    throw new YunAiBusinessException(AiResultCode.CODE_01000007.getCode(), AiResultCode.CODE_01000007.getMsg());
                }
            }
            TaskAiAbilityEntity taskEntity = taskAiAbilityRepository.getTaskEntity(taskId);
            if (null == taskEntity) {
                // 空则返回权限不足
                throw new YunAiBusinessException(AiResultCode.CODE_01000004.getCode(), AiResultCode.CODE_01000004.getMsg());
            }
            String channelId = taskEntity.getSourceChannel();
            String algorithmCode = taskEntity.getAlgorithmCode();
            validTaskEntity(dto, taskEntity, channelId, algorithmCode);
            // 当前权益编号
            String benefitNo = memberCenterBenefitsProperties.getBenefitNo(channelId, algorithmCode);
            String consumeSeq = null;
            // 判断权益是否开启执行
            consumeSeq = getConsumeSeq(taskId, taskEntity, channelId, algorithmCode, benefitNo);
            if (dealConsumeSeq(taskId, taskEntity, benefitNo, consumeSeq)) {
                return BenefitPayResultVO.builder().taskId(String.valueOf(taskId)).build();
            }
            log.error("任务权益扣费consumeSeq获取失败，默认任务扣费失败 taskId={}", taskId);
            // 默认扣费失败
            throw new YunAiBusinessException(AiResultCode.CODE_10000204.getCode(), AiResultCode.CODE_10000204.getMsg());
        } finally {
            // redis解锁
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private boolean dealConsumeSeq(Long taskId, TaskAiAbilityEntity taskEntity, String benefitNo, String consumeSeq) {
        if (StringUtils.isNotBlank(consumeSeq)) {
            log.info("任务权益扣费consumeSeq获取成功，taskId={}, consumeSeq={}", taskId, consumeSeq);
            String feePaidTime = DateUtil.format(new Date(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
            JSONObject json = new JSONObject();
            json.put("benefitNo", benefitNo);
            json.put("feePaidTime", feePaidTime);
            json.put("feeConsumeSeq", consumeSeq);
            if (taskAiAbilityRepository.updateFeePaidStatus(taskId, TaskFeeTypeEnum.YES.getCode(),
                TaskFeePaidStatusEnum.PAID.getCode(), taskEntity.appendJsonExtInfo(json))) {
                // 扣费成功
                return true;
            }
        }
        return false;
    }

    private void validTaskEntity(AITaskBenefitReqDTO dto, TaskAiAbilityEntity taskEntity, String channelId, String algorithmCode) {
        if (!taskEntity.getSourceChannel().equals(dto.getChannelId())
            || !taskEntity.getUserId().equals(dto.getUserId())) {
            // 权限不足，不是当前渠道或者当前用户的任务
            throw new YunAiBusinessException(AiResultCode.CODE_01000004.getCode(), AiResultCode.CODE_01000004.getMsg());
        }
        if (TaskStatusEnum.isProcessing(taskEntity.getTaskStatus())) {
            // 任务处理中
            throw new YunAiBusinessException(AiResultCode.CODE_10000201.getCode(), AiResultCode.CODE_10000201.getMsg());
        }
        if (TaskStatusEnum.isTaskFail(taskEntity.getTaskStatus())) {
            // 任务失败
            throw new YunAiBusinessException(AiResultCode.CODE_10000202.getCode(), AiResultCode.CODE_10000202.getMsg());
        }
        if (TaskFeeTypeEnum.YES.getCode().equals(taskEntity.getFeeType())
            && TaskFeePaidStatusEnum.PAID.getCode().equals(taskEntity.getFeePaidStatus())) {
            // 任务已扣费
            throw new YunAiBusinessException(AiResultCode.CODE_10000203.getCode(), AiResultCode.CODE_10000203.getMsg());
        }
        if (!memberCenterBenefitsProperties.hasBenefitNo(channelId, algorithmCode)) {
            // 任务扣费失败,一般这个不会出现！！！
            throw new YunAiBusinessException(AiResultCode.CODE_10000204.getCode(),
                AiResultCode.CODE_10000204.getMsg() + ",原因" + TaskFeePaidStatusEnum.NO_PAYMENT.getDesc());
        }
    }

    private String getConsumeSeq(Long taskId, TaskAiAbilityEntity taskEntity, String channelId, String algorithmCode, String benefitNo) {
        String consumeSeq;
        if (memberCenterBenefitsProperties.benefitNoOpen(channelId, algorithmCode)) {
            try {
                consumeSeq = memberCenterService.consumeBenefit(taskEntity.getUserId(),
                    RequestContextHolder.getPhoneNumber(), benefitNo, String.valueOf(taskId));

            } catch (Exception e) {
                if (e instanceof YunAiBusinessException
                    && ((YunAiBusinessException) e).getCode().equals(AiResultCode.CODE_10000007.getCode())) {
                    throw new YunAiBusinessException(AiResultCode.CODE_10000007.getCode(),
                        AiResultCode.CODE_10000007.getMsg());
                }
                throw e;
            }
        } else {
            // 设置不开启，不调接口，默认TEST_当前时间戳为消费码
            log.info("设置不开启，不调接口，默认TEST_当前时间戳为消费码 taskId:{}", taskId);
            consumeSeq = String.valueOf("TEST_" + System.currentTimeMillis());
        }
        return consumeSeq;
    }

    @Override
    public BenefitUseTimesLimitVO algorithmTaskToolsConsume(AIToolsConsumeDTO dto) {
        MemberCenterBenefitsProperties.BenefitEntity benefitEntity = memberCenterBenefitsProperties.getBenefitNoByChannelId(dto.getChannelId());
        if (Objects.isNull(benefitEntity)) {
            // 权益没有配置
            throw new YunAiBusinessException(AiResultCode.CODE_10000014.getCode(), AiResultCode.CODE_10000014.getMsg());
        }

        // 获取权益次数
        BenefitUseTimesLimitVO benefitUseTimesLimitVO = new BenefitUseTimesLimitVO();
        String userId = RequestContextHolder.getUserId();
        if (StringUtils.isBlank(userId) && StringUtils.isBlank(dto.getUserId())) {
            //没有用户id
            log.error("==> token校验或第三方鉴权未获取到userId.");
            throw new YunAiBusinessException(AiResultCode.CODE_01000001.getCode(), AiResultCode.CODE_01000001.getMsg());
        }
        if (StringUtils.isBlank(userId)) {
            log.info("==> 请求头未带token解析不到userId, 根据Body获取userId{}", dto.getUserId());
            userId = dto.getUserId();
            RequestContextHolder.setUserId(userId);
        }
        ConsumeAvailableBenefitRsp.AvailableBenefitRsp availableBenefit = null;
        try {
            availableBenefit = memberCenterService.queryAvailableBenefit(userId, RequestContextHolder.getPhoneNumber(), benefitEntity.getBenefitNo());
        } catch (Exception ex) {
            log.error("==> 权益查询失败, benefitNo:{}, userId:{}, phone:{}",
                benefitEntity.getBenefitNo(), userId, RequestContextHolder.getPhoneNumber(), ex);
        }
        benefitUseTimesLimitVO.setLimit(Objects.nonNull(availableBenefit) ? Integer.parseInt(availableBenefit.getBenefitValue()) : benefitEntity.getLimitNum());

        // 核销当前渠道所有工具
        Date current = DateUtil.date();
        // 获取已经存在核销记录的
        List<AIToolsConsumeRecordEntity> consumeRecordList = getTodayConsumeList(dto.getChannelId());
        List<Integer> existModules = new ArrayList<>();
        int consumeNum = 0;
        if (!CollectionUtils.isEmpty(consumeRecordList)) {
            consumeNum += handleExistRecord(consumeRecordList, current, availableBenefit, benefitEntity);
            existModules.addAll(consumeRecordList.stream().map(AIToolsConsumeRecordEntity::getModule).collect(Collectors.toList()));
        }
        // 不存在核销记录的
        List<Integer> list = benefitEntity.getAlgorithmCodeList().stream()
            .filter(NOT_CONSUME_CODE::contains)
            .filter(code -> !existModules.contains(Integer.parseInt(code)))
            .map(Integer::parseInt)
            .collect(Collectors.toList());
        List<TaskAiAbilityEntity> countEntityList = countTaskExclude(list, dto.getChannelId(), current);
        // 处理新增记录
        if (!CollectionUtils.isEmpty(countEntityList)) {
            consumeNum += handleNotExistModule(countEntityList, current, availableBenefit, benefitEntity);
        }

        if (Objects.nonNull(availableBenefit)) {
            // 如果已核销的次数和当前核销的次数之和，大于当前AI工具核销的次数，则从新查询权益已核销次数
            if (consumeNum > 0) {
                ConsumeAvailableBenefitRsp.AvailableBenefitRsp availableBenefitRsp
                    = memberCenterService.queryAvailableBenefit(userId, RequestContextHolder.getPhoneNumber(), benefitEntity.getBenefitNo());
                if (Objects.nonNull(availableBenefitRsp)) {
                    benefitUseTimesLimitVO.setUse(Integer.parseInt(availableBenefitRsp.getBenefitUsed()));
                } else {
                    benefitUseTimesLimitVO.setUse(Integer.parseInt(availableBenefit.getBenefitUsed()) + consumeNum);
                }
            } else {
                int benefitUsed = StringUtils.isNotBlank(availableBenefit.getBenefitUsed()) ? Integer.parseInt(availableBenefit.getBenefitUsed()) : 0;
                benefitUseTimesLimitVO.setUse(benefitUsed);
            }
        } else {
            // 获取今日核销次数
            AIToolsConsumeRecordEntity condition = new AIToolsConsumeRecordEntity();
            condition.setUserId(Long.parseLong(RequestContextHolder.getUserId()));
            condition.setChannelId(dto.getChannelId());
            Integer count = aiToolsConsumeRecordRepository.getTodayConsumeTimes(condition);
            benefitUseTimesLimitVO.setUse(count);
        }
        return benefitUseTimesLimitVO;
    }

    private int handleNotExistModule(List<TaskAiAbilityEntity> countEntityList, Date current,
                                     ConsumeAvailableBenefitRsp.AvailableBenefitRsp availableBenefit,
                                     MemberCenterBenefitsProperties.BenefitEntity benefitEntity) {
        int totalConsumeNum = 0;
        for (TaskAiAbilityEntity taskCount : countEntityList) {
            AIToolsConsumeRecordEntity consumeRecord = new AIToolsConsumeRecordEntity();
            consumeRecord.setUserId(Long.parseLong(RequestContextHolder.getUserId()));
            consumeRecord.setModule(Integer.parseInt(taskCount.getAlgorithmCode()));
            consumeRecord.setChannelId(taskCount.getSourceChannel());
            consumeRecord.setUseTimes(1);
            consumeRecord.setCreateTime(current);
            consumeRecord.setLimitTotal(benefitEntity.getLimitNum());
            if (Objects.nonNull(availableBenefit)) {
                consumeRecord.setLimitTotal(Integer.parseInt(availableBenefit.getBenefitValue()));
            }
            consumeRecord.setUpdateTime(current);
            AIToolsConsumeRecordEntity save = aiToolsConsumeRecordRepository.save(consumeRecord);
            consumeToBenefit(availableBenefit, 1, save.getId(), benefitEntity);
            totalConsumeNum++;
        }
        return totalConsumeNum;
    }

    /**
     * 计算当前渠道已核销的次数
     *
     * @param list      the list
     * @param channelId the channel id
     * @param current   the current
     * @return {@link List<TaskAiAbilityEntity>}
     * <AUTHOR>
     * @date 2024-8-17 12:18
     */
    private List<TaskAiAbilityEntity> countTaskExclude(List<Integer> list, String channelId, Date current) {
        TaskAiAbilityEntity taskAiAbilityEntity = new TaskAiAbilityEntity();
        taskAiAbilityEntity.setUserId(RequestContextHolder.getUserId());
        taskAiAbilityEntity.setSourceChannel(channelId);
        taskAiAbilityEntity.setTaskStatus(TaskStatusEnum.PROCESS_FINISH.getCode());
        return taskAiAbilityRepository.countChannelUseList(taskAiAbilityEntity, list, DateUtil.beginOfDay(current), current);
    }

    /**
     * 处理已存在的记录
     *
     * @param consumeRecordList the consume record list
     * @param current           the current
     * @param availableBenefit  the available benefit
     * @param benefitEntity     the benefit entity
     * @return {@link int}
     * <AUTHOR>
     * @date 2024-8-16 18:55
     */
    private int handleExistRecord(List<AIToolsConsumeRecordEntity> consumeRecordList, Date current,
                                  ConsumeAvailableBenefitRsp.AvailableBenefitRsp availableBenefit,
                                  MemberCenterBenefitsProperties.BenefitEntity benefitEntity) {
        int totalConsumeNum = 0;
        for (AIToolsConsumeRecordEntity recordEntity : consumeRecordList) {
            //需要新增当前渠道的数据
            Integer count = countTask(recordEntity.getUpdateTime(), current, recordEntity.getChannelId(), recordEntity.getModule());
            // 大于0算一次，因为可能存在多个风格
            Integer consumeNum = count > 0 ? 1 : 0;
            if (consumeNum > 0) {
                AIToolsConsumeRecordEntity consumeRecord = new AIToolsConsumeRecordEntity();
                consumeRecord.setId(recordEntity.getId());
                consumeRecord.setUseTimes(recordEntity.getUseTimes() + consumeNum);
                consumeRecord.setLimitTotal(recordEntity.getLimitTotal());
                if (Objects.nonNull(availableBenefit)) {
                    consumeRecord.setLimitTotal(Integer.parseInt(availableBenefit.getBenefitValue()));
                }
                consumeRecord.setUpdateTime(current);
                AIToolsConsumeRecordEntity consumeRecordEntity = aiToolsConsumeRecordRepository.save(consumeRecord);
                consumeToBenefit(availableBenefit, 0, consumeRecordEntity.getId(), benefitEntity);
                totalConsumeNum += consumeNum;
            }
        }
        return totalConsumeNum;
    }

    /**
     * 统计任务数
     *
     * @param beginTime the begin time
     * @param endTime   the end time
     * @param channelId the channel id
     * @param module    the module
     * @return {@link Integer}
     * <AUTHOR>
     * @date 2024-8-16 19:01
     */
    private Integer countTask(Date beginTime, Date endTime, String channelId, Integer module) {
        TaskAiAbilityEntity taskAiAbilityEntity = new TaskAiAbilityEntity();
        taskAiAbilityEntity.setUserId(RequestContextHolder.getUserId());
        taskAiAbilityEntity.setSourceChannel(channelId);
        taskAiAbilityEntity.setTaskStatus(TaskStatusEnum.PROCESS_FINISH.getCode());
        taskAiAbilityEntity.setAlgorithmCode(String.format("%03d", module));
        Integer count = taskAiAbilityRepository.countChannelUse(taskAiAbilityEntity, beginTime, endTime);
        return count == null ? 0 : count;
    }

    /**
     * 获取今日核销记录
     *
     * @param channelId the channel id
     * @return {@link List<AIToolsConsumeRecordEntity>}
     * <AUTHOR>
     * @date 2024-8-16 18:24
     */
    private List<AIToolsConsumeRecordEntity> getTodayConsumeList(String channelId) {
        AIToolsConsumeRecordEntity entity = new AIToolsConsumeRecordEntity();
        entity.setUserId(Long.parseLong(RequestContextHolder.getUserId()));
        entity.setChannelId(channelId);
        return aiToolsConsumeRecordRepository.selectTodayListByChannelId(entity);
    }

    private Boolean consumeToBenefit(ConsumeAvailableBenefitRsp.AvailableBenefitRsp benefit,
                                     Integer requestCount, Long id, MemberCenterBenefitsProperties.BenefitEntity benefitEntity) {
        String benefitNo;
        if (Objects.isNull(benefit)) {
            benefitNo = benefitEntity.getBenefitNo();
        } else {
            benefitNo = benefit.getBenefitNo();
        }
        String consumeSeq;
        try {
            consumeSeq = memberCenterService.consumeBenefit(RequestContextHolder.getUserId(),
                RequestContextHolder.getPhoneNumber(), benefitNo, Long.toString(id));
            if (StringUtils.isNotBlank(consumeSeq)) {
                // 请求成功，请求次数置0
                requestCount = 0;
                log.info("{} benefit to consume success, the result = {}", benefitNo, consumeSeq);
                return true;
            }
        } catch (Exception e) {
            log.error("benefit to consume exception", e);
            if (requestCount >= BENEFIT_REQUEST_COUNT) {
                return false;
            }
            requestCount++;
            return consumeToBenefit(benefit, requestCount, id, benefitEntity);
        }
        return false;
    }
}
