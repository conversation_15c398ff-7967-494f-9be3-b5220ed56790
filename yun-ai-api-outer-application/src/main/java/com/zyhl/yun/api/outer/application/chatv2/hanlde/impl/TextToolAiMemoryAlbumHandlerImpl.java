package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.hcy.yun.ai.common.base.utils.NumberUtil;
import com.zyhl.hcy.yun.ai.common.model.api.client.vo.TextModelBaseVo;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.resp.AddMemoryStoryResp;
import com.zyhl.yun.api.outer.application.assembler.ChatFlowResultAssembler;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueIntentionDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.pojo.SearchImageResultInfo;
import com.zyhl.yun.api.outer.application.chatv2.pojo.SearchParamInfo;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatDialogueSearchService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueFlowResultVO;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig.AiMemoryAlbum;
import com.zyhl.yun.api.outer.application.config.ChatTextToolOpenConfig;
import com.zyhl.yun.api.outer.application.dto.ImageSelectionReqDTO;
import com.zyhl.yun.api.outer.application.dto.SearchImageReqDTO;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.application.service.AlgorithmAiRegisterService;
import com.zyhl.yun.api.outer.application.service.GenerateMemoryService;
import com.zyhl.yun.api.outer.application.service.external.SearchImageAlbumListService;
import com.zyhl.yun.api.outer.application.vo.ContentExtInfoVO;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.config.ModelProperties.ModelLimitConfig;
import com.zyhl.yun.api.outer.domain.dto.QualityAlbumSelectionRespDTO;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domain.entity.image.AlbumConditionEntity;
import com.zyhl.yun.api.outer.domain.req.LlmChatReqDTO;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.AlbumInfo;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueIntentionOutput;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.valueobject.llm.LlmChatMessage;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.domain.vo.chat.search.SearchInfo;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchImageResult;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchResult;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.enums.AlbumConditionRuleEnum;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.AiTextResultVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddFlowStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatMiddleCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.external.AlbumSaasExternalService;
import com.zyhl.yun.api.outer.external.ApiTextExternalService;
import com.zyhl.yun.api.outer.external.LlmChatExternalService;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.VersionUtil;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * AI生成回忆相册
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TextToolAiMemoryAlbumHandlerImpl extends AbstractChatAddV2Handler {

	private static final String NULL_STRING = "null";
	private static final String ALL_STRING = "全部";
	/**
     * 当前执行顺序
     */
    private ExecuteSort thisExecuteSort = ExecuteSort.TEXT_TOOL_AI_MEMORY_ALBUM;

    /**
     * AI编程代码助手意图
     */
    private static final String THIS_MAIN_INTENTION = DialogueIntentionEnum.TEXT_TOOL.getCode();
    private static final String THIS_SUB_INTENTION = DialogueIntentionSubEnum.AI_MEMORY_ALBUM.getCode();

    @Resource
    private DataSaveService dataSaveService;
    @Resource
    private ChatConfigServiceDomainService chatConfigServiceDomainService;
    @Resource
    private AlgorithmAiRegisterService aiRegisterService;
    @Resource
    private GenerateMemoryService generateMemoryService;
    @Resource
    private AlbumSaasExternalService albumSaasExternalService;
    @Resource
    private ApiTextExternalService apiTextExternalService;
    @Resource
    private ChatDialogueSearchService chatDialogueSearchService;
    @Resource
    private ChatTextToolBusinessConfig chatTextToolBusinessConfig;
    @Resource
    private ChatTextToolOpenConfig chatTextToolOpenConfig;
    @Resource
    private ModelProperties modelProperties;
    @Resource
    private ChatFlowResultAssembler chatFlowResultAssembler;
    @Resource
    private LlmChatExternalService llmChatExternalService;
    @Resource
    private SearchImageAlbumListService searchImageAlbumListService;

    /**
     * 创建线程池
     */
    private final static int CORE_POOL_SIZE = 100;
    private final static int MAX_POOL_SIZE = 300;
    private final static long KEEP_ALIVE_TIME = 60;
    private final static int QUEUE_SIZE = 100000;
    private final static ThreadPoolExecutor POOL = new ThreadPoolExecutor(CORE_POOL_SIZE, MAX_POOL_SIZE, KEEP_ALIVE_TIME, TimeUnit.SECONDS, new ArrayBlockingQueue<>(QUEUE_SIZE));

	@Override
	public void afterPropertiesSet() throws Exception {
		// 支持的业务初始化
		List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
		this.setBusinessTypes(thisBusinessTypes);
	}

    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public boolean execute(ChatAddHandleDTO handleDTO) {

        if (StringUtils.isEmpty(handleDTO.getReqDTO().getDialogueInput().getDialogue())) {
            // 对话内容空，不执行
            return false;
        }

        DialogueIntentionDTO command = handleDTO.getReqDTO().getDialogueInput().getCommand();
        if (null != command && DialogueIntentionEnum.isTextToolIntention(command.getCommand())
                && THIS_SUB_INTENTION.equals(command.getSubCommand())) {
            // 判断入参是ai生成回忆相册意图（主意图+子意图联合判断），设置为文本意图
            handleDTO.setTextGenerateTextIntention();
            return false;
        }

        if (null != handleDTO.getIntentionVO()
                && CollUtil.isNotEmpty(handleDTO.getIntentionVO().getIntentionInfoList())) {
            IntentionInfo mainIntention = DialogueIntentionVO.getMainIntention(handleDTO.getIntentionVO());
            if (null != mainIntention && DialogueIntentionEnum.isTextToolIntention(mainIntention.getIntention())
                    && THIS_SUB_INTENTION.equals(mainIntention.getSubIntention())
                    && CollUtil.isNotEmpty(mainIntention.getArgumentMap()) && mainIntention.getArgumentMap()
                    .containsKey(ChatTextToolBusinessConfig.ARGUMENT_AI_MEMORY_ALBUM_KEYWORD)) {
                // 判断意图识别是ai生成回忆相册意图（主意图+子意图联合判断+参数不为空才是执行意图，并且有key=ChatTextToolBusinessConfig.ARGUMENT_AI_MEMORY_ALBUM_KEYWORD）
                return true;
            } else {
                log.info("生成回忆相册条件不能满足，mainIntention:{}", JsonUtil.toJson(mainIntention));
            }
        }
        return false;
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        log.info("进入{}", thisExecuteSort.getDesc());

        if (!AssistantEnum.isXiaoTian(RequestContextHolder.getAssistantEnum())) {
            log.info("非小天渠道，暂不支持成回忆相册意图，设置为文本意图");
            handleDTO.setTextGenerateTextIntention();
            return true;
        }

        Map<String, List<String>> argumentMap = null;
        IntentionInfo mainIntention = null;

        mainIntention = DialogueIntentionVO.getMainIntention(handleDTO.getIntentionVO());
        if (null == mainIntention) {
            // 意图识别后主意图还是空，需要 new 对象
            mainIntention = new IntentionInfo();
        }
        argumentMap = mainIntention.getArgumentMap();
        if (CollUtil.isEmpty(argumentMap)
                || !argumentMap.containsKey(ChatTextToolBusinessConfig.ARGUMENT_AI_MEMORY_ALBUM_KEYWORD)) {
            log.info("生成回忆相册意图无提取到关键字，设置问文本意图");
            handleDTO.setTextGenerateTextIntention();
            return true;
        }
        List<String> keyword = argumentMap.get(ChatTextToolBusinessConfig.ARGUMENT_AI_MEMORY_ALBUM_KEYWORD);

        String userId = RequestContextHolder.getUserId();
        // 判断相册报名-这一个版本先直接返回文本意图
        boolean albumFlag = aiRegisterService.checkAlbum(userId);
        if (!albumFlag) {
            log.info("生成回忆相册意图用户为关闭相册报名，设置问文本意图");
            handleDTO.setTextGenerateTextIntention();
            return true;
        }

        // 获取用户设置的模型，没有设置则使用默认模型
        String modelCode = null;
        String modelName = null;
        ChatConfigEntity chatConfigEntity = chatConfigServiceDomainService.getUserCanUseModel(
                handleDTO.getReqDTO().getUserId(), RequestContextHolder.getPhoneNumber(), handleDTO.getAssistantEnum(),
                handleDTO.getBusinessType());
        if (null != chatConfigEntity) {
            String modelType = chatConfigEntity.getModelType();
            ModelLimitConfig currModel = modelProperties.getLimitByAssistantEnumAndType(
                    RequestContextHolder.getAssistantEnum(), RequestContextHolder.getBusinessType(), modelType);
            if (null != currModel) {
                modelName = currModel.getName();
            }
            modelCode = chatConfigEntity.getModelType();
        }
        if (StringUtils.isEmpty(modelName)) {
            log.info("模型名称获取失败，设置问文本意图");
            handleDTO.setTextGenerateTextIntention();
            return true;
        }

        // 意图识别后，输出前端：强制重置指定AI生成回忆相册意图
        mainIntention.setIntention(THIS_MAIN_INTENTION);
        mainIntention.setSubIntention(THIS_SUB_INTENTION);

        // 设置输出意图及参数信息
        handleDTO.getRespVO().setOutputCommandVO(mainIntention);
        handleDTO.setIntentionVO(DialogueIntentionVO.instanceMainIntention(mainIntention));

        // 获取搜索参数对象
        SearchParamInfo searchParamInfo = getSearchParam(handleDTO, keyword);

        runThread(handleDTO, mainIntention, searchParamInfo, modelCode, modelName);

        return false;
    }

    private void runThread(ChatAddHandleDTO handleDTO, IntentionInfo mainIntention, SearchParamInfo searchParamInfo,
                           String modelCode, String modelName) {
        // 日志map
        final Map<String, String> logMap = LogCommonUtils.getCopyOfContextMap();
        // 获取主线程的ThreadLocal信息，并且异步调用前，将父线程中的请求信息绑定给子线程
        RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo = RequestContextHolder
                .getThreadLocalInfoAndBindingAttributes();
        // 聊天添加响应对象
        ChatAddRespVO respVO = handleDTO.getRespVO();
        respVO.setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());

        // 入库hbase结果列表
        List<DialogueFlowResult> outputList = new ArrayList<>();
        final AiMemoryAlbum aiMemoryAlbum = chatTextToolBusinessConfig.getAiMemoryAlbum();
        // 即将进行六步操作 start

        // 1，返回已分析问题类型
        respVO.setFlowResult(new DialogueFlowResultVO(0, FlowResultTypeEnum.REASONING_RESULT,
                aiMemoryAlbum.getAnalysisQuestionTitle(), aiMemoryAlbum.getAnalysisTip()));
        respVO.getFlowResult().setModelType(modelCode);
        outputList.add(chatFlowResultAssembler.getFlowResult(respVO.getFlowResult()));
        handleDTO.getSseEmitterOperate().send(BaseResult.success(respVO));
        // 多线程执行步骤：2~6
        POOL.execute(() -> {
            try {
            	LeadCopyVO leadCopy = null;
                // 日志map
                LogCommonUtils.initLogMDC(logMap);
                // 把主线程ThreadLocal信息set到子线程
                RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);
                // 2，已调度LLM大模型
                respVO.setFlowResult(new DialogueFlowResultVO(1, FlowResultTypeEnum.REASONING_RESULT,
                        aiMemoryAlbum.getSchedulerLlmTitle(), modelName));
                outputList.add(chatFlowResultAssembler.getFlowResult(respVO.getFlowResult()));
                handleDTO.getSseEmitterOperate().send(BaseResult.success(respVO));

                // 3，已分析搜索关键词
                respVO.setFlowResult(new DialogueFlowResultVO(2, FlowResultTypeEnum.REASONING_RESULT,
                        aiMemoryAlbum.getAnalysisKeywordTitle(), searchParamInfo.getKeyWords().replace(",", "，")));
                outputList.add(chatFlowResultAssembler.getFlowResult(respVO.getFlowResult()));
                handleDTO.getSseEmitterOperate().send(BaseResult.success(respVO));
                // 4，查找云盘图片
                respVO.setFlowResult(new DialogueFlowResultVO(3, FlowResultTypeEnum.SEARCH,
                        ChatMiddleCodeEnum.MCLOUD_IMAGE_SEARCHING));
                handleDTO.getSseEmitterOperate().send(BaseResult.success(respVO));
                // 查找云盘图片搜索-接口结果
				SearchInfo searchInfo = null;
				SearchInfo searchInfoOnlyFileId = null;
				Long totalSize = 0L;
				String searchImageText = searchParamInfo.getSearchMainText();
				if (aiMemoryAlbum.isOriginalSearchQuery()) {
					// 原文搜图
					searchImageText = aiMemoryAlbum
							.searchFilterEndStrsExecute(handleDTO.getInputInfoDTO().getDialogue());
				}
				// 搜索图片接口
				SearchImageResultInfo searchImageResultInfo = searchImages(searchImageText, aiMemoryAlbum.getSearchImagePageSize());
				if (null != searchImageResultInfo) {
					searchInfo = searchImageResultInfo.getSearchInfo();
					totalSize = searchImageResultInfo.getSearchInfoTotalSize();
					searchInfoOnlyFileId = searchImageResultInfo.getSearchInfoOnlyFileId();
				}
				
				// 搜索结果
				SearchResult searchResult = new SearchResult();
				// 文件列表
				List<File> fileList = null;
				if (null != searchInfo && (searchInfo.getSearchResult() instanceof SearchImageResult)) {
					fileList = ((SearchImageResult) searchInfo.getSearchResult()).getFileList();
					SearchImageResult searchImageResult = ((SearchImageResult) searchInfo.getSearchResult());
					searchResult.setSearchImageResult(searchImageResult);
				}
				// 文件列表只有fileId的
				List<File> fileListOnlyFileId = null;
				if (null != searchInfoOnlyFileId
						&& (searchInfoOnlyFileId.getSearchResult() instanceof SearchImageResult)) {
					fileListOnlyFileId = ((SearchImageResult) searchInfoOnlyFileId.getSearchResult()).getFileList();
				}
                // 以下步骤，判断是否结束
                boolean finish = false;
				if (CollUtil.isEmpty(fileList) || CollUtil.isEmpty(fileListOnlyFileId)) {
					ContentExtInfoVO extInfo = null;
					if (VersionUtil.xtH5VersionGte211()) {
						/** 实时搜索人物关系相册推荐 */
						extInfo = searchImageAlbumListService.searchImageSetAlbumList(
								handleDTO.getReqDTO().getSourceChannel(), VersionUtil.getVersionMap(), null, null,
								searchResult);
					}
					
					if (null != extInfo && null != extInfo.getLeadCopy()) {
						// 实时搜索人物关系相册推荐-直接返回leadCopy推荐
						respVO.setFlowResult(
								new DialogueFlowResultVO(3, FlowResultTypeEnum.SEARCH, null, null, null, null));
						leadCopy = extInfo.getLeadCopy();
						respVO.setLeadCopy(leadCopy);
					} else {
						// 返回搜索空结果
						respVO.setFlowResult(new DialogueFlowResultVO(3, FlowResultTypeEnum.SEARCH,
								(Objects.equals(searchParamInfo.getSearchSize(), String.valueOf(1))
										? aiMemoryAlbum.getSearchImageOneEmptyTitle()
										: aiMemoryAlbum.getSearchImageEmptyTitle()),
								null, null, aiMemoryAlbum.getSearchImageErrorTip()));
					}
					
					// 结束不send，最后再sendAndComplete
					finish = true;
				} else {
					if (null != fileList && fileList.size() == 1) {
						// 搜索一张图片无法生成故事
						respVO.setFlowResult(new DialogueFlowResultVO(3, FlowResultTypeEnum.SEARCH,
								aiMemoryAlbum.getImageOnlyOneTitle(), null, null,
								aiMemoryAlbum.getImageOnlyOneErrorTip()));
						// 结束不send，最后再sendAndComplete
						finish = true;
					} else {
						if (null == totalSize || totalSize.longValue() == 0) {
							// 总数空或者为0，设置fileListOnlyFileId.size()
							totalSize = Long.valueOf(fileListOnlyFileId.size());
						}
						log.info("查询华为搜图结果 搜图分页的totalSize:{}, 文件列表size:{}, 文件列表只有fileId的size:{}", totalSize,
								(null != fileList ? fileList.size() : 0),
								(null != fileListOnlyFileId ? fileListOnlyFileId.size() : 0));
						SearchInfo newSearchInfo = new SearchInfo();
						newSearchInfo.setSearchType(searchInfo.getSearchType());
						newSearchInfo.setSearchParam(searchInfo.getSearchParam());
						newSearchInfo.setSearchResult(searchInfo.getSearchResult());
						respVO.setFlowResult(new DialogueFlowResultVO(3, FlowResultTypeEnum.SEARCH,
								String.format(aiMemoryAlbum.getSearchImageFindTitle(), totalSize), null));
						respVO.getFlowResult().setSearchInfoList(Collections.singletonList(newSearchInfo));
						handleDTO.getSseEmitterOperate().send(BaseResult.success(respVO));
						newSearchInfo.setSearchResult(null);
						respVO.getFlowResult().setSearchInfoList(Collections.singletonList(newSearchInfo));
					}
				}
                outputList.add(chatFlowResultAssembler.getFlowResult(respVO.getFlowResult()));

                // 精选相关图片结果
                QualityAlbumSelectionRespDTO resp = null;
				if (!finish) {
					// 5，精选相关图片
					respVO.setFlowResult(new DialogueFlowResultVO(4, FlowResultTypeEnum.REASONING_RESULT,
							ChatMiddleCodeEnum.IMAGE_SELECTING));
					handleDTO.getSseEmitterOperate().send(BaseResult.success(respVO));
					// 精选相关图片-接口结果
					resp = selectImages(RequestContextHolder.getUserId(), fileListOnlyFileId, searchParamInfo);
					if (null != resp && null != resp.getAlbumFileSize() && resp.getAlbumFileSize().intValue() == 1
							&& CollUtil.isEmpty(resp.getFileIds())) {
						// 精选一张图片无法生成故事
						respVO.setFlowResult(new DialogueFlowResultVO(4, FlowResultTypeEnum.REASONING_RESULT,
								aiMemoryAlbum.getImageOnlyOneTitle(), null, null,
								aiMemoryAlbum.getImageOnlyOneErrorTip()));
						// 结束不send，最后再sendAndComplete
						finish = true;
					} else if (null == resp || (null != resp && null != resp.getAlbumFileSize()
							&& resp.getAlbumFileSize().intValue() == 0)) {
						respVO.setFlowResult(new DialogueFlowResultVO(4, FlowResultTypeEnum.REASONING_RESULT,
								aiMemoryAlbum.getSelectImageEmptyTitle(), null, null,
								aiMemoryAlbum.getSelectImageErrorTip()));
						finish = true;
						// 结束不send，最后再sendAndComplete
					} else {
						respVO.setFlowResult(new DialogueFlowResultVO(4, FlowResultTypeEnum.REASONING_RESULT,
								aiMemoryAlbum.getSelectImageFindTitle(),
								String.format(aiMemoryAlbum.getSelectImageFindTip(),
										String.valueOf(resp.getAlbumFileSize()))));
						handleDTO.getSseEmitterOperate().send(BaseResult.success(respVO));
					}
					outputList.add(chatFlowResultAssembler.getFlowResult(respVO.getFlowResult()));
				}
                // 对话是否成功
                boolean succ = true;
                if (!finish) {
                    // 6，相册生成
                    respVO.setFlowResult(
                            new DialogueFlowResultVO(5, FlowResultTypeEnum.ALBUM, ChatMiddleCodeEnum.ALBUM_GENERATING));
                    handleDTO.getSseEmitterOperate().send(BaseResult.success(respVO));
                    // 相册生成-接口结果
                    AlbumInfo albumInfo = imagesAlbumGen(resp);
                    if (null == albumInfo) {
                        // 设置chatStatus为失败
                        respVO.setFlowResult(new DialogueFlowResultVO(5, FlowResultTypeEnum.ALBUM,
                                aiMemoryAlbum.getAlbumGenErrorTitle(), null, null,
                                aiMemoryAlbum.getAlbumGenErrorTip()));
                        // 结束不send，最后再sendAndComplete
                        finish = true;
                        succ = false;
                    } else {
                        respVO.setFlowResult(new DialogueFlowResultVO(5, FlowResultTypeEnum.ALBUM,
                                aiMemoryAlbum.getAlbumGenSuccTitle(), null));
                        respVO.getFlowResult().setAlbumList(Collections.singletonList(albumInfo));
                        // 结束不send，最后再sendAndComplete
                        finish = true;
                    }
                    outputList.add(chatFlowResultAssembler.getFlowResult(respVO.getFlowResult()));
                }

                // 结束六步操作 end

                // 设置hbase
				AiTextResultRespParameters result = AiTextResultRespParameters.builder()
						.version(AiTextResultVersionEnum.V2.getVersion())
						.outputCommand(new DialogueIntentionOutput(mainIntention)).leadCopy(leadCopy)
						.outputList(outputList).build();

                // 保存hbase
                dataSaveService.saveHbaseAllChatResult(handleDTO, result);

                // 保存tidb
                if (succ) {
                    dataSaveService.addSuccessAndModelCode(handleDTO, modelCode, OutContentTypeEnum.TEXT);
                } else {
                    dataSaveService.addFailAndModelCode(handleDTO, modelCode, OutContentTypeEnum.TEXT);
                }
                // 流式响应结束
                respVO.setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
                handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(respVO));

            } catch (Exception e) {
                YunAiBusinessException ex = new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
                log.error("AiMemoryAlbum execute dialogueId:{}, error:", handleDTO.getDialogueId(), e);
                if (e instanceof YunAiBusinessException) {
                    ex = (YunAiBusinessException) e;
                }
                handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.error(ex.getCode(), ex.getMessage()));
            }
        });
    }

	/**
	 * 搜索图片接口，先按现网搜索逻辑返回搜索列表给前端，再搜索只有文件id的搜索列表给相册
	 * 
	 * @param text               搜索文本
	 * @param onlyFileIdPageSize 只有文件id的分页数量
	 * @return 搜索结果
	 */
	private SearchImageResultInfo searchImages(String text, Integer onlyFileIdPageSize) {
		SearchInfo searchInfo = chatDialogueSearchService
				.searchImages(SearchImageReqDTO.builder().dialogue(text).build());

		Integer searchInfoTotalSize = 0;
		if (searchInfo.getSearchResult() instanceof SearchImageResult) {
			searchInfoTotalSize = ((SearchImageResult) searchInfo.getSearchResult()).getTotalCount();
			if (null == searchInfoTotalSize) {
				searchInfoTotalSize = 0;
			}
		}
		SearchInfo searchInfoOnlyFileId = chatDialogueSearchService.searchImages(
				SearchImageReqDTO.builder().isOriginal(true).dialogue(text).pageSize(onlyFileIdPageSize).build());
		return new SearchImageResultInfo(searchInfo, searchInfoTotalSize.longValue(), searchInfoOnlyFileId);
	}

	/**
	 * 精选图片
	 * 
	 * @param userId 用户id
	 * @param files 语义搜图返回文件信息结果
	 */
	private QualityAlbumSelectionRespDTO selectImages(String userId, List<File> files, SearchParamInfo searchParamInfo) {
		try {
            // SearchParamInfo转ImageSelectionReqDTO.AlbumConditionEntity
            List<AlbumConditionEntity.DateRange> dateRangeList = null;
            // 有任一时间为空则不添加到时间范围中
            if (!StringUtils.isAnyEmpty(searchParamInfo.getStartDate(), searchParamInfo.getEndDate())) {
                dateRangeList = Collections.singletonList(new AlbumConditionEntity.DateRange(searchParamInfo.getStartDate(), searchParamInfo.getEndDate()));
            }

            AlbumConditionEntity albumCondition = AlbumConditionEntity.builder()
                    .dateRangeList(dateRangeList)
                    .searchRule(searchParamInfo.getSearchRule())
                    .searchSize(searchParamInfo.getSearchSize() == null ? null : NumberUtil.getIntValue(searchParamInfo.getSearchSize()))
                    .build();
			ImageSelectionReqDTO dto = new ImageSelectionReqDTO(
					files.stream().map(File::getFileId).collect(Collectors.toList()), userId,
					OwnerTypeEnum.PERSONAL.getOwnerValue(), albumCondition);
			return generateMemoryService.generateMemoryAlbum(dto);
		} catch (Exception e) {
			log.error("selectImages error:", e);
		}
		return null;
	}

    /**
     * 相册生成
     *
     * @param resp 请求参数
     */
    private AlbumInfo imagesAlbumGen(QualityAlbumSelectionRespDTO resp) {
        try {
            AddMemoryStoryResp album = albumSaasExternalService.addMemoryStory(resp);
            if (null != album) {
                return albumSaasExternalService.queryAlbumByAlbumId(album.getId());
            }
        } catch (Exception e) {
            log.error("imagesAlbumGen error:", e);
        }
        return null;
    }

    private SearchParamInfo getSearchParam(ChatAddHandleDTO handleDTO, List<String> intentionKeyWords) {
        SearchParamInfo searchParamInfo = new SearchParamInfo();
        // 从配置中获取大模型code编号和提示词
        AiMemoryAlbum aiMemoryAlbum = chatTextToolBusinessConfig.getAiMemoryAlbum();
        String modelCode = aiMemoryAlbum.getModelCode();
        // 拼装提示词
        String modelPrompt = aiMemoryAlbum.getModelPrompt().replace("{query}", handleDTO.getInputInfoDTO().getDialogue());
        // 当天时间,格式为yyyy-MM-dd
        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = today.format(formatter);
        modelPrompt = modelPrompt.replace("{formatted_now}", formattedDate);
        log.info("替换拼装后的提示词getSearchParam-modelPrompt:{}", modelPrompt);
        String resultText = null;
        try {
            // 构建调起大模型请求参数
            List<LlmChatMessage> messages = new ArrayList<>();
            messages.add(new LlmChatMessage(TextModelRoleEnum.USER.getName(), modelPrompt));
            LlmChatReqDTO reqDTO = new LlmChatReqDTO(RequestContextHolder.getUserId(), modelCode, messages);
            // 调起大模型
            TextModelBaseVo result = llmChatExternalService.chatNormal(reqDTO);
            // 获取调起大模型后返回的文本生成结果
            resultText = result.getText();
            log.info("调起大模型后返回的文本生成结果resultText:{}", resultText);
        } catch(Exception e) {
            log.error("回忆相册提示词调用大模型失败 error:", e);
        }
        // 调用大模型返回的文本生成结果不存在，则按原流程执行
        if(StringUtils.isBlank(resultText)){
            searchParamInfo.setSearchMainText(handleDTO.getInputInfoDTO().getDialogue());
            searchParamInfo.setKeyWords(String.join(",", intentionKeyWords));
            return searchParamInfo;
        }

        // 截取大模型返回的文本生成结果，获取json字符串
        try {
        	String jsonText = resultText.substring(resultText.indexOf("{\""), resultText.lastIndexOf("\"}") + 2);
            // 解析json字符串
            searchParamInfo = JSONObject.parseObject(jsonText, SearchParamInfo.class);
            String searchMainText = searchParamInfo.getSearchMainText();
            String keyWords = searchParamInfo.getKeyWords();
            if (StringUtils.isBlank(searchMainText)) {
                searchParamInfo.setSearchMainText(handleDTO.getInputInfoDTO().getDialogue());
            } else {
                searchParamInfo.setSearchMainText(searchMainText);
            }
            if (StringUtils.isBlank(keyWords) || NULL_STRING.equals(keyWords)) {
                searchParamInfo.setKeyWords(String.join(",", intentionKeyWords));
            }
        } catch (Exception e) {
            log.error("json解析失败，这里不抛出错误:", e);
            searchParamInfo.setSearchMainText(handleDTO.getInputInfoDTO().getDialogue());
            searchParamInfo.setKeyWords(String.join(",", intentionKeyWords));
        }
        // 校验时间格式是否为yyyy-MM-dd，有一个不符合即都置空
        if (!isValidDateFormat(searchParamInfo.getStartDate()) || !isValidDateFormat(searchParamInfo.getEndDate())) {
            searchParamInfo.setStartDate(null);
            searchParamInfo.setEndDate(null);
        }
        // 校验searchSize，非数字即置为null
        String searchSize = searchParamInfo.getSearchSize();
		try {
			if (StringUtils.isBlank(searchSize) || NULL_STRING.equals(searchSize) || ALL_STRING.equals(searchSize)) {
                searchParamInfo.setSearchSize(null);
            } else {
                Integer.parseInt(searchParamInfo.getSearchSize());
            }
        } catch (Exception e) {
            log.error("searchSize解析失败，这里不抛出错误:", e);
            searchParamInfo.setSearchSize(null);
        }
        // 校验searchRule，非枚举内即置为“全部”
        if (!AlbumConditionRuleEnum.isExist(searchParamInfo.getSearchRule())) {
            searchParamInfo.setSearchRule(ALL_STRING);
        }
        log.info("最终生成的搜索参数对象searchParamInfo:{}", searchParamInfo);

        return searchParamInfo;
    }

    public boolean isValidDateFormat(String dateStr) {
        if (StringUtils.isBlank(dateStr) || NULL_STRING.equals(dateStr) || ALL_STRING.equals(dateStr)) {
            return false;
        }
        String dateFormat = "yyyy-MM-dd";
        try {
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
            // 解析验证
            formatter.parse(dateStr);
            return true;
        } catch (Exception e) {
            log.error("dateStr:{}, dateFormat:{}, 日期格式错误:", dateStr, dateFormat, e);
            return false;
        }
    }

}
