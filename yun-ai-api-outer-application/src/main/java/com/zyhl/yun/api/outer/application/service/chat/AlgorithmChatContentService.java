package com.zyhl.yun.api.outer.application.service.chat;

import java.util.List;

import com.zyhl.yun.api.outer.application.dto.AiAssistantStatusReqDTO;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.dto.ShareBatchGetDTO;
import com.zyhl.yun.api.outer.application.vo.ChatStatusVO;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.vo.chat.ShareDialogueResultVO;

/**
 * 对话内容操作类
 *
 * <AUTHOR>
 */
public interface AlgorithmChatContentService {

    /**
     * 新增对话内容，只是保存输入的内容，没有结果
     *
     * @param params     dto对象
     * @param chatStatus 对话状态
     * @param taskId     任务id
     * @param modelCode  模型编码
     * @return 对话内容实体
     */
    AlgorithmChatContentEntity add(ChatAddInnerDTO params, Integer chatStatus, Long taskId, String modelCode);

    /**
     * 新增成功对话，输入的内容和结果一起
     *
     * @param params         dto对象
     * @param modelCode      模型编码
     * @param outContentType 输出文本类型 OutContentTypeEnum
     * @return
     */
    AlgorithmChatContentEntity addSuccess(ChatAddInnerDTO params, String modelCode, Integer outContentType);

    /**
     * 保存tidb结果（输入和结果数据）
     *
     * @param params 用户输入对象
     * @return com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity
     * @Author: WeiJingKun
     */
    AlgorithmChatContentEntity saveAll(ChatAddInnerDTO params);
    
    /**
     * 获取对话状态
     *
     * @param dto 助手状态查询参数
     * @return com.zyhl.yun.api.outer.domain.vo.ChatStatusVO
     */
    ChatStatusVO getChatStatus(AiAssistantStatusReqDTO dto);

    /**
     * 批量获取对话内容
     *
     * @param dto 请求参数
     * @return vo
     */
    List<ShareDialogueResultVO> shareBatchGet(ShareBatchGetDTO dto);
}
