package com.zyhl.yun.api.outer.application.vo.knowledge;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.dashscope.utils.JsonUtils;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.hcy.yun.ai.common.rag.vo.RecallResultVO;
import com.zyhl.hcy.yun.ai.common.rag.vo.RerankResultVO;
import com.zyhl.yun.api.outer.config.KnowledgeDialogueProperties;
import com.zyhl.yun.api.outer.config.knowledge.PoliticianInfo;
import com.zyhl.yun.api.outer.constants.RegConst;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeBaseEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 个人知识库文件添加结果
 *
 * <AUTHOR>
 */
@Slf4j
@Data
public class KnowledgeTemplateInfoVO {

    /**
     * 用户输入
     */
    private String query = "";

    /**
     * 历史对话信息
     */
    private String history = "";
    private List<TextModelMessageDTO> historyList = new ArrayList();

    /**
     * 相关知识
     */
    private String knowledge = "";
//    private List<Map<String, String>> knowledgeList = new ArrayList<>();
    /**
     * 相关知识
     */
    private List<KnowledgeFlowInfoDataHandle> knowledgeList = new ArrayList<>();

    /**
     * 行政人物信息
     */
    private String politician = "";


    /**
     * 重排结果，用于兼容不同版本提示词做处理
     */
    private List<RerankResultVO> rerankResultList;
    /**
     * 历史对话信息，用于兼容不同版本提示词做处理
     */
    private TextModelTextReqDTO reqDTOMsg;


    public KnowledgeTemplateInfoVO(String query) {
        this.query = StrUtil.nullToEmpty(query);
    }

    /**
     * 获取历史内容列表文本
     *
     * @param reqDTO 模型请求
     * @return 历史内容列表文本
     */
    public void historyHandle(TextModelTextReqDTO reqDTO) {
        reqDTOMsg = reqDTO;

        // 历史对话处理
        if (reqDTO.getMessageDtoList().size() > 1) {
            historyList.addAll(reqDTO.getMessageDtoList().subList(0, reqDTO.getMessageDtoList().size() - 1));
        }
        LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】【历史对话】替换行政人物前历史对话信息第{}个分块：\n{}", JsonUtil.toJson(historyList));
    }

    private final static String FILE_NAME_STR = "[文件名]";
    private final static String FILE_CONTENT_STR = "\\[文件内容\\]";
    private final static String DOC_KNOWLEDGE_STR = "文档知识";

    /**
     * 相关知识
     *
     * @param recallResult 召回结果
     * @param rerankResult 重排结果
     */
    public void knowledgeHandle(List<RecallResultVO> recallResult, List<RerankResultVO> rerankResult, List<File> fileList) {
        rerankResultList = rerankResult;

        // 命中的文件转为按重排得分排序
        Set<String> hitSet = fileList.stream().map(File::getFileId).collect(Collectors.toSet());
        Set<String> sortSet = new LinkedHashSet<>();

        // 按文件id分组
        List<RecallResultVO> hitResult = new ArrayList<>();
        rerankResult.forEach(vo -> {
            RecallResultVO recallVO = recallResult.get(Integer.parseInt(vo.getDocument().getSegmentId()));
            hitResult.add(recallVO);
            if (hitSet.contains(vo.getDocument().getFileId()) || KnowledgeBaseEnum.isCommon(recallVO.getKnowledgeBase())) {
                sortSet.add(vo.getDocument().getFileId());
            }
        });
        Map<String, List<RecallResultVO>> map = hitResult.stream().collect(Collectors.groupingBy(RecallResultVO::getFileId));

        // 循环分组
        List<Map<String, String>> resultList = new ArrayList<>();
        for (String fileId : sortSet) {
            // 提取数据
            List<RecallResultVO> list = map.get(fileId);
            knowledgeList.add(new KnowledgeFlowInfoDataHandle(list));
        }

        LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】【相关知识】替换链接地址后替换行政人物前相关知识信息第{}个分块：\n{}", JsonUtil.toJson(knowledgeList));
    }

    /**
     * 行政人物处理
     *
     * @param config 配置信息
     */
    public void politicianHandle(KnowledgeDialogueProperties config, String systemPrompt) {
        if (ObjectUtil.isNotEmpty(systemPrompt) && !systemPrompt.contains("politician")) {
            // 旧版本提示词
            oldHistoryHandle();
            oldKnowledgeHandle();
            return;
        }

        if (!config.isPoliticianEnabled() || ObjectUtil.isEmpty(config.getPoliticianList())) {
            log.info("【知识库对话】【RAG重要节点日志】【行政人物】未启用行政人物");
            getKnowledgeText(config.isEnableMark());
            return;
        }

        // 先排序
        config.getPoliticianList().sort(Comparator.comparing(PoliticianInfo::getSort).reversed());

        // 每个替换信息按照替换字数从多到少排序
        config.getPoliticianList().forEach(item -> {
            item.getReplaceInfoList().forEach(replaceInfo -> replaceInfo.setLength(replaceInfo.getReplace().length()));
            item.getReplaceInfoList().sort(Comparator.comparing(PoliticianInfo.ReplaceInfo::getLength).reversed());
        });

        // 命中的行政人物
        Set<PoliticianInfo> hitSet = new HashSet<>();

        // 相关知识命中的行政人物
        for (PoliticianInfo item : config.getPoliticianList()) {
            for (PoliticianInfo.ReplaceInfo replaceInfo : item.getReplaceInfoList()) {
                for (KnowledgeFlowInfoDataHandle data : knowledgeList) {
                    for (int i = 0; i < data.getSegmentList().size(); i++) {
                        String segment = data.getSegmentList().get(i);
                        if (segment.contains(replaceInfo.getReplace())) {
                            hitSet.add(item);
                            data.getSegmentList().set(i, segment.replace(replaceInfo.getReplace(), replaceInfo.getHonorific()));
                        }
                    }
                }
            }
        }
        getKnowledgeText(config.isEnableMark());

        if (ObjectUtil.isNotEmpty(hitSet)) {
            // 排序
            List<PoliticianInfo> hitList = new ArrayList<>(hitSet);
            hitList.sort(Comparator.comparing(PoliticianInfo::getSort).reversed());
            log.info("【知识库对话】【RAG重要节点日志】【行政人物】命中行政人物：{}", JsonUtils.toJson(hitList));

            StringBuilder sb = new StringBuilder();
            for (int i = 1, size = hitList.size(); i <= size; i++) {
                PoliticianInfo item = hitList.get(i - 1);
                sb.append("➡").append(item.getReplaceInfoList().stream().map(PoliticianInfo.ReplaceInfo::getHonorific).distinct().collect(Collectors.joining("➡")));
            }

            // 最终结果
            politician = sb.substring(1);
            log.info("【知识库对话】【RAG重要节点日志】【行政人物】行政人物回答顺序：{}", politician);
        } else {
            log.info("【知识库对话】【RAG重要节点日志】【行政人物】未命中行政人物");
        }
    }

    /**
     * 旧版本历史对话处理
     */
    private void oldHistoryHandle() {
        // 历史对话处理
        StringBuilder sb = new StringBuilder();
        if (reqDTOMsg.getMessageDtoList().size() > 1) {
            for (int i = 0, size = reqDTOMsg.getMessageDtoList().size() - 1; i < size; i++) {
                TextModelMessageDTO msg = reqDTOMsg.getMessageDtoList().get(i);
                if (TextModelRoleEnum.ASSISTANT.getName().equals(msg.getRole())
                        || TextModelRoleEnum.USER.getName().equals(msg.getRole())) {
                    sb.append(msg.getContent()).append("\n");
                }
            }
        }
        history = sb.toString();
    }

    /**
     * 旧版本相关知识处理
     */
    private void oldKnowledgeHandle() {
        List<String> strList = new ArrayList<>();
        for (RerankResultVO item : rerankResultList) {
            strList.add(item.getDocument().getText());
        }
        knowledge = JsonUtil.toJson(strList);
    }

    /**
     * 相关知识文本
     *
     * @return 相关知识文本
     */
    public void getKnowledgeText(boolean enableMark) {
        StringBuilder sb = new StringBuilder("<检索结果>");
        for (int i = 0, size = knowledgeList.size(); i < size; i++) {
            sb.append(knowledgeList.get(i).format(i + 1, enableMark));
        }
        sb.append("</检索结果>");

        knowledge = sb.toString();
        LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】【行政人物】相关信息替换行政人物后第{}个分块：\n{}", knowledge);
    }
}