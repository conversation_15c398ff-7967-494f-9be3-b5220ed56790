package com.zyhl.yun.api.outer.application.vo;

import com.zyhl.yun.api.outer.application.dto.MailDetailReqDTO;
import com.zyhl.yun.api.outer.external.client.resp.MailDetailResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Classname NoteDetailVo
 * @Description 邮件文本内容VO
 * @Date 2024/3/1 9:42
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MailDetailVO {

    /**
     * 邮件ID
     */
    private String mid;

    /**
     * 邮件文本内容
     */
    private String txtContent;

    /**
     * 邮件标题
     */
    private String subject;

    /**
     * 用户输入内容
     */
    private String inputContent;

    /**
     * 格式化
     * @param reqDTO
     * @param response
     * @param content
     * @return
     */
    public static MailDetailVO convert(MailDetailReqDTO reqDTO, MailDetailResponse response, String content) {
        MailDetailVO vo = new MailDetailVO();
        vo.setMid(reqDTO.getMid());
        vo.setInputContent(reqDTO.getInputContent());
        vo.setSubject(response.getSubject());
        vo.setTxtContent(content);
        return vo;
    }

}
