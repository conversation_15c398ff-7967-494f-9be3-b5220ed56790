package com.zyhl.yun.api.outer.application.service.knowledge.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.hbase.domain.datahelper.repository.HbaseRepository;
import com.zyhl.hcy.yun.ai.common.platform.third.client.assemble.entity.FilePreviewItem;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.OwnerDriveClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDriveBatchFileDownloadReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDriveBatchFileReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDrivePreviewInfoReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveFileDownloadBatchVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveFileVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDrivePreviewInfoVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.enums.UserFileThumbnailStyleEnum;
import com.zyhl.yun.api.outer.application.dto.knowledge.PersonalKnowledgeNotePreviewDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.PersonalKnowledgeNoteService;
import com.zyhl.yun.api.outer.application.vo.knowledge.MediaPreviewInfo;
import com.zyhl.yun.api.outer.application.vo.knowledge.PersonalKnowledgeNoteAttachment;
import com.zyhl.yun.api.outer.application.vo.knowledge.PersonalKnowledgeNotePreviewInfoVO;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileResEntity;
import com.zyhl.yun.api.outer.enums.FileCategoryEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileAuditStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeStatusEnum;
import com.zyhl.yun.api.outer.external.UserDriveExternalService;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmRagTextContentPO;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileResRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeInviteRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/4/18 15:00
 */
@Service
@Slf4j
public class PersonalKnowledgeNoteServiceImpl implements PersonalKnowledgeNoteService {

    @Resource
    private HbaseRepository hbaseRepository;

    @Resource
    private UserKnowledgeFileRepository userKnowledgeFileRepository;

    @Resource
    private UserKnowledgeInviteRepository userKnowledgeInviteRepository;

    @Resource
    private OwnerDriveClient ownerDriveClient;

    @Resource
    private UserDriveExternalService userDriveExternalService;

    @Resource
    private UserKnowledgeFileResRepository userKnowledgeFileResRepository;

    @Resource
    private UserKnowledgeRepository userKnowledgeRepository;

    @Value("${audit.not-pass.thumbnail-url}")
    private String auditNotPassThumbnailUrl;

    /**
     * 个人知识库笔记预览
     * @param dto
     * @return
     */
    @Override
    public PersonalKnowledgeNotePreviewInfoVO preview(PersonalKnowledgeNotePreviewDTO dto) {
        UserKnowledgeFileEntity knowledgeFile = userKnowledgeFileRepository.getByFileId(dto.getResourceId());
        if(ObjectUtil.isNull(knowledgeFile)){
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_NOT_FOUND);
        }
        //公共知识库 才判断文档送审状态，提示错误码
        if (knowledgeFile.getBaseId() != null) {
            UserKnowledgeEntity userKnowledge = userKnowledgeRepository.selectById(knowledgeFile.getBaseId());
            if (userKnowledge != null
                    && KnowledgeStatusEnum.OPEN.getStatus().equals(userKnowledge.getOpenLevel())
                    && FileAuditStatusEnum.isNotPass(knowledgeFile.getAuditStatus())) {
                throw new YunAiBusinessException(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
            }
        }
        String userId = dto.getUserId();
        // 判断用户id是否一致
        if(!dto.getUserId().equals(knowledgeFile.getUserId())){
            // 用户id不一致，判断是否已加入了知识库
            int count = userKnowledgeInviteRepository.count(knowledgeFile.getBaseId(), dto.getUserId());
            if(count > 0){
                userId = knowledgeFile.getUserId();
            } else {
                throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_NO_PERMISSION);
            }
        }

        String rowKey = userId + StrPool.UNDERLINE + dto.getResourceId() + StrPool.UNDERLINE + ResourceTypeEnum.NOTE.getType();
        List<String> rowKeys = new ArrayList<>();
        rowKeys.add(rowKey);
        List<AlgorithmRagTextContentPO> textList = hbaseRepository.selectList(rowKeys, AlgorithmRagTextContentPO.class);
        if(CollUtil.isEmpty(textList)){
            return null;
        }
        AlgorithmRagTextContentPO note = textList.get(0);
        if(StringUtils.isBlank(note.getNewContent())){
            log.warn("获取笔记预览链接 newContent is blank, use content, rowKey = {}", rowKey);
        }
        String newContent = StringUtils.isNotBlank(note.getNewContent()) ? note.getNewContent() : note.getContent();
        PersonalKnowledgeNotePreviewInfoVO previewInfo = JsonUtil.parseObject(newContent, PersonalKnowledgeNotePreviewInfoVO.class);
        // 获取附件列表
        List<PersonalKnowledgeNoteAttachment> attachments = previewInfo.getAttachments();
        if(CollUtil.isNotEmpty(attachments)){
            // 从algorithm_user_knowledge_file_res获取所有附件
            List<UserKnowledgeFileResEntity> userKnowledgeFiles = userKnowledgeFileResRepository.selectByFromFileId(userId, knowledgeFile.getBaseId(), dto.getResourceId(), ResourceTypeEnum.NOTE.getType());
            if(CollUtil.isEmpty(userKnowledgeFiles)){
                previewInfo.setAttachments(null);
                return previewInfo;
            }
            Map<String, UserKnowledgeFileResEntity> fileResMap = userKnowledgeFiles.stream().collect(Collectors.toMap(UserKnowledgeFileResEntity::getFileId, item -> item));
            // 录音文件id
            List<FilePreviewItem> mediaFileList = new ArrayList<>();
            // 非录音文件id
            List<String> otherFileIds = new ArrayList<>();
            // 获取多媒体类型的附件和非多媒体文件的id
            distinguishMediaFileAndOther(attachments, mediaFileList, otherFileIds);
            // 非多媒体文件，调用独立用获取下载地址
            Map<String, String> previewUrlMap = getPreviewUrlMap(otherFileIds, userId);
            // 多媒体文件不为空，则调用独立用户获取文件预览信息
            getMediaFileInfo(mediaFileList, userId, previewInfo, previewUrlMap, fileResMap);
            // 附件文件信息
            Map<String, OwnerDriveFileVO> attachmentFileMap = new HashMap<>(Const.NUM_16);
            // 获取附件缩略图地址
            Map<String, String> attachmentThumbnailUrlMap = new HashMap<>(Const.NUM_16);
            Map<String, String> thumbnailUrlMap = getAttachmentThumbnailUrlMap(attachments, userId, attachmentFileMap);
            List<PersonalKnowledgeNoteAttachment> newAttachments = new ArrayList<>();
            for(PersonalKnowledgeNoteAttachment attachment : previewInfo.getAttachments()){
                UserKnowledgeFileResEntity fileRes = fileResMap.get(attachment.getFileId());
                // 数据库中未获取到，说明已经删除了
                if(Objects.isNull(fileRes) && StringUtils.isNotBlank(attachment.getFileId())){
                    log.warn("当前笔记附件数据库中不存在或已被删除 = {}", JsonUtil.toJson(attachment));
                    continue;
                }
                attachment.setAuditStatus(Objects.nonNull(fileRes) ? fileRes.getAuditStatus() : null);
                // 图片送审中、送审失败 要显示默认图片
                if(Objects.nonNull(fileRes) && !FileAuditStatusEnum.isPass(fileRes.getAuditStatus())){
                    attachment.setPreviewUrl(auditNotPassThumbnailUrl);
                    attachment.setThumbnailUrl(auditNotPassThumbnailUrl);
                } else {
                    attachment.setPreviewUrl(previewUrlMap.get(attachment.getFileId()));
                    attachment.setThumbnailUrl(thumbnailUrlMap.get(attachment.getFileId()));
                }
                OwnerDriveFileVO fileVo = attachmentFileMap.get(attachment.getFileId());
                if(ObjectUtil.isNotNull(attachmentFileMap.get(attachment.getFileId()))){
                    attachmentThumbnailUrlMap.put(attachment.getAttachmentId(), thumbnailUrlMap.get(attachment.getFileId()));
                    attachment.setType(fileVo.getCategory());
                    attachment.setSize(fileVo.getSize());
                    attachment.setFileExtension(fileVo.getFileExtension());
                    attachment.setParentFileId(fileVo.getParentFileId());
                    if(ObjectUtil.isNotNull(fileVo.getMediaMetaInfo())){
                        attachment.setDuration(fileVo.getMediaMetaInfo().getDuration());
                    }
                }
                newAttachments.add(attachment);
            }
            previewInfo.setAttachments(newAttachments);
            setMarkThumbnailUrl(previewInfo, attachmentThumbnailUrlMap);
        }
        if(StringUtils.isBlank(previewInfo.getResourceId())){
            previewInfo.setResourceId(dto.getResourceId());
        }
        return previewInfo;

    }

    private void setMarkThumbnailUrl(PersonalKnowledgeNotePreviewInfoVO previewInfo, Map<String, String> attachmentThumbnailUrlMap) {
        if(CollUtil.isNotEmpty(previewInfo.getMarks())){
            previewInfo.getMarks().forEach(mark -> {
                if(StringUtils.isNotBlank(mark.getAttachmentId()) && StringUtils.isNotBlank(attachmentThumbnailUrlMap.get(mark.getAttachmentId()))){
                    mark.setThumbnailUrl(attachmentThumbnailUrlMap.get(mark.getAttachmentId()));
                }
            });
        }
    }


    private Map<String, String> getAttachmentThumbnailUrlMap(List<PersonalKnowledgeNoteAttachment> attachments, String userId, Map<String, OwnerDriveFileVO> attachmentFileMap) {
        List<String> attachmentFileIds = attachments.stream().map(PersonalKnowledgeNoteAttachment::getFileId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(attachmentFileIds)){
            OwnerDriveBatchFileReqDTO req = new OwnerDriveBatchFileReqDTO();
            req.setUserId(userId);
            req.setFileIds(attachmentFileIds.toArray(new String[0]));
            req.setImageThumbnailStyleList(new String[]{UserFileThumbnailStyleEnum.LARGE.getStyle()});
            List<OwnerDriveFileVO> driveFileList = userDriveExternalService.getBatchFileInfo(req);
            if(CollUtil.isNotEmpty(driveFileList)){
                attachmentFileMap.putAll(driveFileList.stream().collect(Collectors.toMap(OwnerDriveFileVO::getFileId, driveFile -> driveFile)));
                Map<String, String> thumbnailUrlMap = new HashMap<>(Const.NUM_16);
                driveFileList.stream().filter(driveFile -> CollUtil.isNotEmpty(driveFile.getThumbnailUrls())).forEach(driveFile -> {
                    thumbnailUrlMap.put(driveFile.getFileId(), driveFile.getThumbnailUrls().get(0).getUrl());
                });
                return thumbnailUrlMap;
            }
        }
        return Collections.emptyMap();
    }

    private Map<String, String> getPreviewUrlMap(List<String> otherFileIds, String userId) {
        Map<String, String> previewUrlMap = new HashMap<>(Const.NUM_16);
        if(CollUtil.isNotEmpty(otherFileIds)){
            previewUrlMap = new HashMap<>(Const.NUM_16);
            OwnerDriveBatchFileDownloadReqDTO downloadReq = new OwnerDriveBatchFileDownloadReqDTO();
            downloadReq.setUserId(userId);
            downloadReq.setFileIds(otherFileIds.toArray(new String[0]));
            OwnerDriveFileDownloadBatchVO downloadBatch = ownerDriveClient.getBatchFilesDownload(downloadReq);
            if(ObjectUtil.isNotNull(downloadBatch) && ObjectUtil.isNotNull(downloadBatch.getItems())){
                for(OwnerDriveFileDownloadBatchVO.OwnerDriveDownloadUrlInfo downloadUrlInfo : downloadBatch.getItems()){
                    previewUrlMap.put(downloadUrlInfo.getFileId(), downloadUrlInfo.getUrl());
                }
            }
        }
        return previewUrlMap;
    }

    private void getMediaFileInfo(List<FilePreviewItem> mediaFileList, String userId, PersonalKnowledgeNotePreviewInfoVO previewInfo, Map<String, String> previewUrlMap, Map<String, UserKnowledgeFileResEntity> fileResMap) {
        if(CollUtil.isNotEmpty(mediaFileList)){
            OwnerDrivePreviewInfoReqDTO ownerFileUrlReqDto = new OwnerDrivePreviewInfoReqDTO();
            ownerFileUrlReqDto.setUserId(userId);
            ownerFileUrlReqDto.setFiles(mediaFileList);
            ownerFileUrlReqDto.setClientInfo(RequestContextHolder.getClientInfo());
            ownerFileUrlReqDto.setChannel(RequestContextHolder.getAppChannel());
            OwnerDrivePreviewInfoVO previewInfoVo = ownerDriveClient.batchGetPreviewInfo(ownerFileUrlReqDto);
            if(ObjectUtil.isNotNull(previewInfoVo) && CollUtil.isNotEmpty(previewInfoVo.getItems())){
                List<MediaPreviewInfo> mediaPreviewInfos = new ArrayList<>();
                previewInfoVo.getItems().forEach(item -> {
                    if(ObjectUtil.isNotNull(item.getPreviewInfo())){
                        previewUrlMap.put(item.getFileId(), item.getPreviewInfo().getUrl());
                        MediaPreviewInfo mediaPreviewInfo = new MediaPreviewInfo();
                        UserKnowledgeFileResEntity fileRes = fileResMap.get(item.getFileId());
                        if(Objects.nonNull(fileRes) && !FileAuditStatusEnum.isPass(fileRes.getAuditStatus())){
                            mediaPreviewInfo.setStatus("auditFail");
                            mediaPreviewInfo.setUrl(auditNotPassThumbnailUrl);
                        } else {
                            mediaPreviewInfo.setStatus(item.getPreviewInfo().getStatus());
                            mediaPreviewInfo.setUrl(item.getPreviewInfo().getUrl());
                        }
                        mediaPreviewInfos.add(mediaPreviewInfo);
                    }
                });
                previewInfo.setMediaPreviewInfos(mediaPreviewInfos);
            }
        }
    }

    private void distinguishMediaFileAndOther(List<PersonalKnowledgeNoteAttachment> attachments, List<FilePreviewItem> mediaFileList, List<String> otherFileIds) {
        attachments.forEach(attachment -> {
            if(StringUtils.isNotBlank(attachment.getFileId())){
                String fileExtension = attachment.getFileName().substring(attachment.getFileName().lastIndexOf(".") + 1);
                if(FileCategoryEnum.AUDIO.getExtensions().contains(fileExtension) || FileCategoryEnum.VIDEO.getExtensions().contains(fileExtension)){
                    FilePreviewItem filePreviewItem = new FilePreviewItem();
                    filePreviewItem.setFileId(attachment.getFileId());
                    filePreviewItem.setCategory("audio");
                    if(FileCategoryEnum.VIDEO.getExtensions().contains(fileExtension)){
                        // 视频
                        filePreviewItem.setCategory("video");
                    }
                    mediaFileList.add(filePreviewItem);
                } else {
                    otherFileIds.add(attachment.getFileId());
                }
            }
        });
    }

}
