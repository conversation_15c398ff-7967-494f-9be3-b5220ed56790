package com.zyhl.yun.api.outer.application.dto;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.application.util.SseEmitterDataUtils;
import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;
import com.zyhl.yun.api.outer.application.vo.FlowTypeResultVO;
import com.zyhl.yun.api.outer.config.InterventionProperties;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.vo.AllNetworkSearchRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddResultTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.OutAuditStatusEnum;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.InterventionVO;
import lombok.Data;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 对话接口内部数据传输对象，包含：
 * 1、请求参数，数据不可改
 * 2、生成响应参数的方法
 * 3、中间状态数据
 *
 * <AUTHOR>
 */
@Data
public class ChatAddInnerDTO {

    /**
     * 请求参数，里面的数据不可改
     */
    private AlgorithmChatAddDTO reqParams;
    /**
     * 请求参数，里面的数据不可改
     */
    private AlgorithmChatAddContentDTO content;
    /**
     * 流式对象
     */
    private SseEmitter sseEmitter;

    /**
     * 会话id，默认为入参的会话id，如果没有需要生成一个
     */
    private Long sessionId;
    /**
     * 对话id，需要新生成一个
     */
    private Long dialogueId;
    /**
     * 智能体关联id
     */
    private String typeRelationId;

    /**
     * 意图编码
     */
    private String intentionCode;
    /**
     * 意图结果对象
     */
    private DialogueIntentionVO intentionVO;

    /**
     * 响应结果
     */
    private AlgorithmChatAddVO respParams = new AlgorithmChatAddVO();

    /**
     * 并发线程：多意图、推荐提问语句
     */
    private List<Future<Object>> futures;

    /**
     * 干预库对象
     */
    private InterventionVO interventionVO;

    /**
     * 资源内容（邮件，笔记，图片ocr等等内容，用于大模型对话）
     */
    private String resourceContent;

    /**
     * hbase的resp数据对象
     */
    private AiTextResultRespParameters hbaseResp;

    /**
     * tidb需要保存的数据对象
     */
    private AlgorithmChatTidbSaveDTO algorithmChatTidbSaveDTO;

    /**
     * 公共知识库可用
     */
    private boolean commonEnable;
    /**
     * 个人知识库可用
     */
    private boolean personalEnable;

    /**
     * 保存会话（true-新增；false-更新）
     */
    private Boolean saveMessage;

    /**
     * 意图识别的异步处理future
     */
    private Future<DialogueIntentionVO> dialogueIntentionFuture;

    /**
     * 搜索返回词的异步处理future
     */
    private Future<String> searchReturnTermsFuture;

    /**
     * 全网搜推荐异步处理future
     */
    private Future<List<AllNetworkSearchRecommendVO>> allNetworkSearchRecommendFuture;

    /**
     * 资源类型对应的资源信息
     */
    private ResourceInfoDTO resourceInfo = new ResourceInfoDTO();

    /** 继续执行大模型回答DTO */
    private ContinueTextSseDTO continueTextSseDTO = new ContinueTextSseDTO();

    public ChatAddInnerDTO(AlgorithmChatAddDTO reqParams, SseEmitter sseEmitter) {
        this.reqParams = reqParams;
        this.content = reqParams.getContent();
        this.sseEmitter = sseEmitter;

        init();
    }

    public void init() {
        if (!CharSequenceUtil.isBlank(reqParams.getSessionId())) {
            setSessionId(Long.valueOf(reqParams.getSessionId()));
        }

        this.intentionCode = content != null ? content.getCommands() : "";
        this.respParams.setCommands(intentionCode);
    }

    // --------------------- set --------------------- //

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
        this.respParams.setSessionId(String.valueOf(sessionId));
    }

    public void setDialogueId(Long dialogueId) {
        this.dialogueId = dialogueId;
        this.respParams.setDialogueId(String.valueOf(dialogueId));
    }

    public void setIntentionCode(String intentionCode) {
        this.intentionCode = intentionCode;
        this.respParams.setCommands(intentionCode);
    }

    public void setInterventionVO(InterventionVO interventionVO) {
        this.interventionVO = interventionVO;

        Integer resultType = ChatAddResultTypeEnum.RICH_TEXT_TYPE.getType();
        if (InterventionProperties.DEFAULT_TEXT_VERSION.equals(interventionVO.getVersion())) {
            //干扰库纯文本版本为0，流式返回纯文本内容
            resultType = ChatAddResultTypeEnum.FLOW_TYPE.getType();
        }
        respParams.setResultType(resultType);
    }

    /**
     * 设置意图识别结果
     *
     * @param intentionVO 意图识别结果
     */
    public void setIntentionVO(DialogueIntentionVO intentionVO) {
        if (intentionVO == null) {
            return;
        }
        this.intentionVO = intentionVO;
        this.intentionCode = intentionVO.getIntentionInfoList().get(0).getIntention();
        this.respParams.setCommands(intentionCode);
    }

    /**
     * 设置意图结果为文生文意图
     */
    public void setTextGenerateTextIntention() {
        this.intentionCode = DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode();
        this.intentionVO.getIntentionInfoList().get(0).setIntention(intentionCode);
        this.respParams.setCommands(intentionCode);
    }

    // --------------------- set --------------------- //

    // --------------------- 返回结果 --------------------- //

    /**
     * 流式响应结果
     *
     * @param msg                 输出信息
     * @param resultType          结果类型
     * @param dialogueRecommendVO 推荐结果
     */
    public void sseSendAndComplete(String msg, Integer resultType, DialogueRecommendVO dialogueRecommendVO) {
        respParams.setResultType(resultType);
        respParams.setFlowResult(new FlowTypeResultVO(msg, OutAuditStatusEnum.SUCCESS.getCode()));
        respParams.setRecommend(dialogueRecommendVO);
        BaseResult<AlgorithmChatAddVO> result = BaseResult.success(respParams);
        SseEmitterDataUtils.sendMsgAndComplete(sseEmitter, result, new AtomicBoolean(false));
    }

    // --------------------- 返回结果 --------------------- //

    /**
     * 获取hbase 存储对象
     *
     * @param taskId          任务id
     * @param resourceContent 附件内容
     * @param text2TextData   文生文结果
     * @return hbase对象
     */
    public AiTextResultEntity getAiTextResultEntity(String taskId, String resourceContent, String text2TextData) {
        // 响应结果
        AiTextResultRespParameters resp = new AiTextResultRespParameters();
        resp.setResult(ResultCodeEnum.SUCCESS);
        resp.setData(text2TextData);
        resp.setParam(respParams.getSearchParam());
        resp.setLeadCopy(respParams.getLeadCopy());

        AiTextResultEntity entity = new AiTextResultEntity();
        entity.setUserId(getReqParams().getUserId());
        entity.setDialogueId(dialogueId);
        entity.setTaskId(taskId);
        entity.setReqParameters(content.getDialogue());
        entity.setAttachment(resourceContent);
        entity.setRespParameters(JsonUtil.toJson(resp));

        return entity;
    }

}
