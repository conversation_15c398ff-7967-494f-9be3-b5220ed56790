package com.zyhl.yun.api.outer.application.convertor.dto.dynamic;

import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.AIFileVO;
import com.zyhl.yun.api.outer.vo.AsyncTaskResult;
import org.mapstruct.Mapper;

/**
 * <b>className:</b>
 * {@link AiFileVoConvertor} <br>
 * <b> description:</b>
 *
 * <AUTHOR>
 * @date 2024-04-28 16:50
 **/
@Mapper(componentModel = "spring")
public interface AiFileVoConvertor {
    /**
     * 文件信息转换
     * @param fileContent 文件内容
     * @return 任务结果
     */
    AsyncTaskResult.AsyncTaskResultFileInfo toAsyncTaskResultFileInfo(AIFileVO fileContent);
}
