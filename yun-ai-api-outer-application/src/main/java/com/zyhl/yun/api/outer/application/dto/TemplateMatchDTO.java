package com.zyhl.yun.api.outer.application.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 模板匹配
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TemplateMatchDTO {

    /**
     * 用户id，非必填，用于第三方鉴权，token鉴权无需填写。
     */
    private String userId;

    /**
     * 图片标签列表
     */
    private List<ImageLabels> labelList;

}
