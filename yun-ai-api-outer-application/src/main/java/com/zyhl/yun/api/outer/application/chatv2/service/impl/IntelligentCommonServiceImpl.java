package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.hadoop.hbase.shaded.com.google.common.base.Objects;
import org.springframework.stereotype.Service;

import com.zyhl.yun.api.outer.application.chatv2.dto.AssistantChatV2PollingUpdateDTO;
import com.zyhl.yun.api.outer.application.chatv2.service.AlgorithmChatV2ContentService;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatContentService;
import com.zyhl.yun.api.outer.application.chatv2.service.IntelligentCommonService;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueResultV2VO;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 智能体对话-公共-服务实现
 *
 * <AUTHOR>
 * @date 2025-07-18 16:38
 **/
@Slf4j
@Service
public class IntelligentCommonServiceImpl implements IntelligentCommonService {

	@Resource
	private ChatContentService chatContentService;

	@Resource
	private AlgorithmChatV2ContentService algorithmChatV2ContentService;

	@Override
	public DialogueResultV2VO contentPollingUpdate(AssistantChatV2PollingUpdateDTO pollingUpdate) {
		try {
			return algorithmChatV2ContentService.pollingUpdate(pollingUpdate);
		} catch (Exception e) {
			log.warn("contentPollingUpdate error:", e);
		}
		return null;
	}

	@Override
	public AlgorithmChatContentEntity getLastIntentionDialogue(String userId, Long sessionId, String dialogueId) {
		AlgorithmChatContentEntity condition = new AlgorithmChatContentEntity();
		condition.setUserId(userId);
		condition.setSessionId(sessionId);
		condition.setToolsCommand(DialogueIntentionEnum.TEXT_TOOL.getCode());
		condition.setSubToolsCommand(DialogueIntentionSubEnum.AI_MEETING_MAIL.getCode());
		condition.setId(Long.parseLong(dialogueId));
		return chatContentService.getLastIntentionDialogue(condition);

	}

	/**
	 * 通过对话信息获取邮件信息
	 * 
	 * @param lastMailDialogueInfo 上一次发邮件对话信息
	 * @return
	 */
	@Override
	public MailInfoVO getMailInfoResult(AlgorithmChatContentEntity lastMailDialogueInfo) {
		// 获取对话内容信息
		AssistantChatV2PollingUpdateDTO pollingUpdate = new AssistantChatV2PollingUpdateDTO();
		pollingUpdate.setDialogueId(lastMailDialogueInfo.getId());
		pollingUpdate.setUserId(lastMailDialogueInfo.getUserId());
		DialogueResultV2VO dialogResult = contentPollingUpdate(pollingUpdate);
		if (null == dialogResult) {
			return null;
		}
		List<DialogueFlowResult> outputList = dialogResult.getOutputList();
		if (CollUtil.isNotEmpty(outputList)) {
			for (DialogueFlowResult output : outputList) {
				if (Objects.equal(FlowResultTypeEnum.MAIL.getType(), output.getResultType())) {
					return output.getMailInfo();
				}
			}
		}
		return null;
	}
}
