package com.zyhl.yun.api.outer.application.chatv2.service.openapi;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.application.chatv2.service.openapi.OpenapiLingxiMailEditService}
 * <br>
 * <b> description:</b>
 * 能体对话-邮件编辑-服务接口
 *
 * <AUTHOR>
 * @date 2025-06-25 11:42
 **/
public interface OpenapiLingxiMailEditService {

    /**
     * 执行
     *
     * @param handleDTO 请求参数
     * @return 返回是否成功
     */
    boolean run(ChatAddHandleDTO handleDTO);

}
