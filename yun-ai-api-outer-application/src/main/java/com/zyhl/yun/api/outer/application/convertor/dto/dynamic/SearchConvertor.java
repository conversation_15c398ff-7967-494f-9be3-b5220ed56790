package com.zyhl.yun.api.outer.application.convertor.dto.dynamic;

import com.zyhl.yun.api.outer.application.dto.FaceSearchDTO;
import com.zyhl.yun.api.outer.application.dto.SearchDTO;
import com.zyhl.yun.api.outer.application.vo.FaceSearchVO;
import com.zyhl.yun.api.outer.domain.req.FaceSearchEntity;
import com.zyhl.yun.api.outer.domain.req.SearchEntity;
import com.zyhl.yun.api.outer.domain.resp.FaceSearchRespEntity;
import org.mapstruct.Mapper;



/**
 * 智能查询接口转换
 * <AUTHOR>
 */

@Mapper(componentModel = "spring")
public interface SearchConvertor {


    /**
     * 转换dto 为 entity
     * @param dto dto
     * @return SearchEntity
     */
    SearchEntity toEntity(SearchDTO dto);

    /**
     * 转换dto 为 entity
     * @param dto dto
     * @return FaceSearchEntity
     */
    FaceSearchEntity faceSearchToEntity(FaceSearchDTO dto);

    /**
     * 转换entity 为 vo
     * @param data data
     * @return FaceSearchVO
     */
    FaceSearchVO faceSearchToVO(FaceSearchRespEntity data);

}