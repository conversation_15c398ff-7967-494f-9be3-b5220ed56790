package com.zyhl.yun.api.outer.application.convertor.dto.dynamic;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import com.zyhl.hcy.yun.ai.common.platform.third.client.album.valueObject.PersonNoSetRelationData;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.valueObject.StaticFile;
import com.zyhl.yun.api.outer.config.DialogueRecommendProperties;
import com.zyhl.yun.api.outer.domain.vo.AlbumInfoVO;
import com.zyhl.yun.api.outer.domain.vo.AlbumInfoVO.ExternalResourceInfo;
import com.zyhl.yun.api.outer.domain.vo.IntentionRecommendVO;

/**
 * 对话结果推荐信息转换类
 *
 * <AUTHOR>
 * @date 2024/6/3 02:16
 */
@Mapper(componentModel = "spring")
public interface RecommendVoConvertor {

	/**
	 * 对话结果推荐信息转换
	 * 
	 * @param intentionList intentionList
	 * @return List
	 */
    List<IntentionRecommendVO> toVOList(List<DialogueRecommendProperties.Intention> intentionList);

	/**
	 * 对话结果推荐信息转换
	 * 
	 * @param intention intention
	 * @return IntentionRecommendVO
	 */
    IntentionRecommendVO toVO(DialogueRecommendProperties.Intention intention);

	/**
	 * 转换相册
	 * 
	 * @param oersonNoSetRelationData 关系信息
	 * @return 相册信息
	 */
    @Mapping(target = "albumId", source = "id")
    @Mapping(target = "staticFile", source = "staticFile", qualifiedByName = "caseToExternalResourceInfo")
	AlbumInfoVO toAlbumInfoVo(PersonNoSetRelationData oersonNoSetRelationData);
    
	/**
	 * 转换相册
	 * 
	 * @param list 列表
	 * @return 资源信息列表
	 */
	List<AlbumInfoVO> toAlbumList(List<PersonNoSetRelationData> list);

	/**
	 * 转换为资源信息
	 * 
	 * @param staticFile 文件
	 * @return 资源信息
	 */
	@Named("caseToExternalResourceInfo")
	default ExternalResourceInfo caseToExternalResourceInfo(StaticFile staticFile) {
		if (null == staticFile) {
			return null;
		}
		ExternalResourceInfo info = new ExternalResourceInfo();
		info.setResourceId(staticFile.getStaticFileId());
		info.setThumbnailUrl(staticFile.getStaticFileUrl());
		return info;
	}
}
