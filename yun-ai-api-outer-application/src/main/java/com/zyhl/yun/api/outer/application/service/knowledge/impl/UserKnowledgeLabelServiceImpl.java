package com.zyhl.yun.api.outer.application.service.knowledge.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeLabelAddReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeLabelDeleteReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeLabelListReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeLabelSortReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.UserKnowledgeLabelService;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowLedgeLabelListVO;
import com.zyhl.yun.api.outer.config.KnowledgePersonalProperties;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeLabelEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeLabelFileEntity;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeLabelEnum;
import com.zyhl.yun.api.outer.repository.UserKnowledgeLabelFileRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeLabelRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 个人知识库标签业务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class UserKnowledgeLabelServiceImpl implements UserKnowledgeLabelService {

    private final UserKnowledgeLabelRepository userKnowledgeLabelRepository;

    private final UserKnowledgeLabelFileRepository userKnowledgeLabelFileRepository;

    private final KnowledgePersonalProperties knowledgePersonalProperties;

    @Override
    public Long save(KnowledgeLabelAddReqDTO dto) {
        if (ObjectUtil.isEmpty(dto.getLabelId())) {
            log.info("新增标签");
            // 总数校验
            List<UserKnowledgeLabelEntity> list = userKnowledgeLabelRepository.selectByUserId(dto.getUserId());
            if (list.size() >= knowledgePersonalProperties.getLabelNum()) {
                log.info("标签数量已达上限，上限数：{}，已创建数：{}", knowledgePersonalProperties.getLabelNum(), list.size());
                throw new YunAiBusinessException(ResultCodeEnum.LABEL_NUM_LIMIT);
            }

            // 校验名称重复性
            UserKnowledgeLabelEntity entity = userKnowledgeLabelRepository.selectByName(dto.getUserId(), dto.getLabelName());
            if (entity != null) {
                log.info("新增标签失败，标签名称重复");
                throw new YunAiBusinessException(ResultCodeEnum.LABEL_NAME_EXIST);
            }

            // 实体对象
            entity = new UserKnowledgeLabelEntity();
            entity.setUserId(dto.getUserId());
            entity.setLabel(dto.getLabelName());
            userKnowledgeLabelRepository.add(entity);

            return entity.getId();
        } else {
            log.info("修改标签");
            // 校验id是否存在
            UserKnowledgeLabelEntity entity = userKnowledgeLabelRepository.selectById(Long.valueOf(dto.getLabelId()));
            if (entity == null) {
                log.info("标签id不存在，labelId:{}", dto.getLabelId());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }

            // 校验名称重复性
            UserKnowledgeLabelEntity nameEntity = userKnowledgeLabelRepository.selectByName(dto.getUserId(), dto.getLabelName());
            if (nameEntity != null && !entity.getId().equals(nameEntity.getId())) {
                log.info("新增标签失败，标签名称重复");
                throw new YunAiBusinessException(ResultCodeEnum.LABEL_NAME_EXIST);
            }

            entity.setLabel(dto.getLabelName());
            userKnowledgeLabelRepository.update(entity);

            return entity.getId();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(KnowledgeLabelDeleteReqDTO dto) {
        // 查询标签
        UserKnowledgeLabelEntity entity = userKnowledgeLabelRepository.selectById(Long.valueOf(dto.getLabelId()));
        if (entity == null) {
            log.info("【知识库标签】标签id不存在，labelId:{}", dto.getLabelId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        } else if (!entity.getUserId().equals(dto.getUserId())) {
            log.info("【知识库标签】标签所属用户不一致，labelId:{}，userId:{}，登录用户id:{}", dto.getLabelId(), entity.getUserId(), dto.getUserId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        // 需求：删除标签后，文件自动移动到未分类标签下，如果其他分类下已有该文件，则删除映射关系

        // 删除标签
        userKnowledgeLabelRepository.delete(entity.getId());

        // 标签下的文件
        List<UserKnowledgeLabelFileEntity> lableFileList = userKnowledgeLabelFileRepository.selectByUserId(dto.getUserId(), entity.getId(), null);
        if (CollUtil.isNotEmpty(lableFileList)) {
            // 查询标签下的文件所有映射关系
            List<String> fileIds = lableFileList.stream().map(UserKnowledgeLabelFileEntity::getFileId).collect(Collectors.toList());
            List<UserKnowledgeLabelFileEntity> fileList = userKnowledgeLabelFileRepository.selectByUserId(dto.getUserId(), fileIds);
            Map<String, List<UserKnowledgeLabelFileEntity>> map = fileList.stream().collect(Collectors.groupingBy(UserKnowledgeLabelFileEntity::getFileId));

            for (String fileId : map.keySet()) {
                List<UserKnowledgeLabelFileEntity> list = map.get(fileId);
                if (list.size() == 1) {
                    // 改为未分类
                    UserKnowledgeLabelFileEntity labelFileEntity = list.get(0);
                    labelFileEntity.setLabelId(KnowledgeLabelEnum.UNCLASSIFIED.getId());
                    userKnowledgeLabelFileRepository.updateById(labelFileEntity);
                } else {
                    // 删除映射关系
                    userKnowledgeLabelFileRepository.deleteByUserId(dto.getUserId(), entity.getId(), fileId);
                }
            }
        }
    }

    @Override
    public List<KnowLedgeLabelListVO> list(KnowledgeLabelListReqDTO dto) {
        // 全量查询
        List<UserKnowledgeLabelEntity> list = userKnowledgeLabelRepository.selectByUserId(dto.getUserId());
        if (ObjectUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        // 数据转换
        List<KnowLedgeLabelListVO> result = new ArrayList<>();
        list.forEach(item -> result.add(new KnowLedgeLabelListVO(item)));

        // 是否返回全部标签
        if (Boolean.TRUE.equals(dto.getShowAll())) {
            result.add(0, new KnowLedgeLabelListVO(KnowledgeLabelEnum.ALL));
            result.add(new KnowLedgeLabelListVO(KnowledgeLabelEnum.UNCLASSIFIED));
        }

        return result;
    }

    @Override
    public void sort(KnowledgeLabelSortReqDTO dto) {
        List<KnowLedgeLabelListVO> list = dto.getLabelList();
        for (int i = 1, size = list.size(); i <= size; i++) {
            if (KnowledgeLabelEnum.isExist(Long.valueOf(list.get(i - 1).getLabelId()))) {
                log.info("前端不应该传这个id进来，labelId：{}", list.get(i - 1).getLabelId());
                continue;
            }

            UserKnowledgeLabelEntity entity = new UserKnowledgeLabelEntity();
            entity.setId(Long.valueOf(list.get(i - 1).getLabelId()));
            entity.setSort(size - i + 1);
            userKnowledgeLabelRepository.update(entity);
        }
    }
}
