package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeLabelEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

/**
 * 知识库文件新增请求参数
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class KnowledgeFileAddReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 标签id，默认未分类
     *
     * @see KnowledgeLabelEnum
     */
    private String labelId = String.valueOf(KnowledgeLabelEnum.UNCLASSIFIED.getId());

    /**
     * 文件信息File
     * 个人云文档：fileId，name，type，category，size，fileExtension是必填字段；（RAG1.0）
     * 邮件/笔记：fileId为邮件/笔记的唯一值id，name为笔记/邮件的标题。（RAG2.2）
     */
    private List<File> fileList;

    /**
     * 资源类型
     *
     * @see KnowledgeResourceTypeEnum
     */
    private Integer resourceType = 0;

    /**
     * html文件信息
     */
    private List<UserKnowledgeFileEntity.HtmlInfo> htmlList;


}
