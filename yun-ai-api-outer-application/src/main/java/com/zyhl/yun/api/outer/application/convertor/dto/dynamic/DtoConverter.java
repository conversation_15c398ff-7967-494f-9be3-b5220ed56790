package com.zyhl.yun.api.outer.application.convertor.dto.dynamic;

import com.zyhl.yun.api.outer.application.dto.PopUpProtocolQueryWebDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileBatchImportReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.ResourceTransferReqDTO;
import com.zyhl.yun.api.outer.domain.entity.AiPopUpProtocolEntity;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;

/**
 * dto转换类
 * 
 * <AUTHOR>
 * @Date 2023/11/15 9:25
 */
@Mapper(componentModel = "spring", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface DtoConverter {

    /**
     * PopUpProtocolController.query，接收参数 -> PopUpProtocolDomainService.query，请求参数
     * @Author: WeiJing<PERSON><PERSON>
     * @param dto dto
     * @return AIPopUpProtocolEntity
     */
    AiPopUpProtocolEntity toAiPopUpProtocolEntity(PopUpProtocolQueryWebDTO dto);


    /**
     * 转换成资源转换dto
     * @param dto 导入dto
     * @return 资源转换dto
     */
    ResourceTransferReqDTO toResourceTransferReqDTO(KnowledgeFileBatchImportReqDTO dto);
}
