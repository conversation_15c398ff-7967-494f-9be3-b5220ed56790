package com.zyhl.yun.api.outer.application.service.knowledge.impl;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.redis.HcyRedisTemplate;
import com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.hbase.domain.datahelper.repository.HbaseRepository;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.enums.OwnerDriveTaskStatusEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveTaskResultVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.FileResult;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.BatchFileVO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileAddReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileDeleteReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileListBatchReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileTaskResultReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.UserKnowledgeFileTaskService;
import com.zyhl.yun.api.outer.application.service.knowledge.UserKnowledgeMailAndNoteService;
import com.zyhl.yun.api.outer.application.service.knowledge.upload.AbstractUserKnowledgeFileResourceHandle;
import com.zyhl.yun.api.outer.application.service.mq.KnowledgeFileDeleteTaskMqService;
import com.zyhl.yun.api.outer.application.service.mq.KnowledgeTransTaskMqService;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeAddResultVO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeFileTaskResVO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeTaskResultVO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeTaskV2ResultVO;
import com.zyhl.yun.api.outer.application.vo.knowledge.PersonalKnowledgeResourceResult;
import com.zyhl.yun.api.outer.application.vo.knowledge.PersonalKnowledgeResourceV2;
import com.zyhl.yun.api.outer.application.vo.knowledge.PersonalKnowledgeTaskInfo;
import com.zyhl.yun.api.outer.config.KnowledgePersonalProperties;
import com.zyhl.yun.api.outer.config.UserDimensionProperties;
import com.zyhl.yun.api.outer.constants.CommonConstant;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.domain.dto.CatalogConfigDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserDriveConfigEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileResEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileTaskEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeLabelFileEntity;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domainservice.PersonalKnowledgeDomainService;
import com.zyhl.yun.api.outer.domainservice.UserDriveConfigDomainService;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileProcessStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileTaskResultEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileTaskStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeTaskTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeTaskTypeResultEnum;
import com.zyhl.yun.api.outer.enums.knowledge.UserKnowledgeFileResDelFlagEnum;
import com.zyhl.yun.api.outer.external.UserDriveExternalService;
import com.zyhl.yun.api.outer.external.YunDiskExternalService;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmRagTextContentPO;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileResRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileTaskRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeLabelFileRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 个人知识库文件转存任务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class UserKnowledgeFileTaskServiceImpl implements UserKnowledgeFileTaskService {

    private final UserKnowledgeFileRepository userKnowledgeFileRepository;

    private final UserKnowledgeFileTaskRepository userKnowledgeFileTaskRepository;

    private final YunDiskExternalService yunDiskExternalService;

    private final UserDriveConfigDomainService userDriveConfigDomainService;

    private final KnowledgePersonalProperties knowledgePersonalProperties;

    private final KnowledgeTransTaskMqService knowledgeTransTaskMQService;

    private final UserDriveExternalService userDriveExternalService;

    private final UserKnowledgeLabelFileRepository userKnowledgeLabelFileRepository;

    private final UserKnowledgeMailAndNoteService userKnowledgeMailAndNoteService;

    private final UserKnowledgeFileResRepository userKnowledgeFileResRepository;

    private final PersonalKnowledgeDomainService personalKnowledgeDomainService;

    private final KnowledgeFileDeleteTaskMqService knowledgeFileDeleteTaskMqService;

    @Resource(name = "businessThreadPool")
    private ExecutorService businessThreadPool;
    @Resource(name = "knowledgeDeleteThreadPool")
    private ExecutorService knowledgeDeleteThreadPool;

    @Resource
    private HbaseRepository hbaseRepository;

    private final UserDimensionProperties userDimensionProperties;
    private final HcyRedisTemplate<String, Number> hcyRedisTemplate;

    /**
     * 不存在错误码
     */
    private static final String NOT_EXIST_CODE = "04000010";

    /**
     * 添加到知识库成功
     */
    private static final String FILE_EXIST_OTHER_LABEL = "添加到知识库成功";
    /**
     * 文件不存在
     */
    private static final String ERROR_NOT_EXIST = "文件不存在";
    /**
     * 文档格式错误
     */
    private static final String ERROR_EXT = "文档格式错误";
    /**
     * 文件过大
     */
    private static final String ERROR_TOO_LARGE = "文件上传过大";

    private static final String CONNECT_SPLIT = "_";
    /**
     * 每次删除知识库文件的数量
     */
    private final static Integer BATCH_FILE_SIZE = 50;
    /**
     * 任务进度相关常量
     */
    private static final int PROGRESS_THRESHOLD_30_SECONDS = 30;
    private static final int PROGRESS_THRESHOLD_60_SECONDS = 60;
    private static final int PROGRESS_50_PERCENT = 50;
    private static final int PROGRESS_80_PERCENT = 80;
    private static final int PROGRESS_90_PERCENT = 90;

    /**
     * 上传失败的错误码集合
     */
    private static final Set<String> FAIL_CODE_SET = new HashSet<>(
            Arrays.asList(ResultCodeEnum.ERROR_NOT_FOUND.getResultCode(),
                    ResultCodeEnum.UNKNOWN_ERROR.getResultCode(),
                    ResultCodeEnum.ERROR_FILE_EXT.getResultCode(),
                    ResultCodeEnum.FILE_SIZE_LARGE.getResultCode(),
                    ResultCodeEnum.FILE_ALREADY_EXIST.getResultCode(),
                    ResultCodeEnum.FILE_REPEAT_UPLOAD.getResultCode()));

    @Override
    public Long add(KnowledgeFileAddReqDTO dto) {

        // 个人云文档
        if (KnowledgeResourceTypeEnum.isPersonalFile(dto.getResourceType())) {
            return addPersonalFile(dto);
        }

        // 邮件/笔记/网页
        return userKnowledgeMailAndNoteService.add(dto);
    }


    @Override
    public KnowledgeTaskResultVO add180(KnowledgeFileAddReqDTO dto) {
        KnowledgeTaskResultVO result = new KnowledgeTaskResultVO();
        // 个人云文档
        if (KnowledgeResourceTypeEnum.isPersonalFile(dto.getResourceType())) {
            result = addPersonalFile180(dto);
        } else if (KnowledgeResourceTypeEnum.isMail(dto.getResourceType())
                || KnowledgeResourceTypeEnum.isNote(dto.getResourceType())
                || KnowledgeResourceTypeEnum.isHtml(dto.getResourceType())) {
            result = AbstractUserKnowledgeFileResourceHandle.getByCode(dto.getResourceType()).addV2(dto);
        }

        // 转换文案
        result.getResultList().forEach(item -> item.setMsg(knowledgePersonalProperties.getParseFailedReason().get(item.getResult())));

        return result;
    }

    /**
     * 个人云文件转存
     *
     * @param dto 转存文件参数
     * @return 转存任务id
     */
    private Long addPersonalFile(KnowledgeFileAddReqDTO dto) {

        // 批量获取文件信息（个人云文件id）
        List<String> fileIds = dto.getFileList().stream().map(File::getFileId).collect(Collectors.toList());
        BatchFileVO batchFileVO = yunDiskExternalService.fileBatchGetByAllPlatform(dto.getUserId(), RequestContextHolder.getBelongsPlatform(), fileIds);
        Map<String, FileResult> fileResultMap = batchFileVO.getBatchFileResults().stream().collect(Collectors.toMap(k -> k.getSrcFile().getFileId(), v -> v));

        // 查询用户独立空间
        UserDriveConfigEntity driveConfigEntity = userDriveConfigDomainService.createDrive(dto.getUserId(), RequestContextHolder.getBelongsPlatform());
        CatalogConfigDTO.CatalogConfigInfo configInfo = userDriveConfigDomainService.createKnowledgeConfig(driveConfigEntity);

        // 已转存的个人云文件id
        Map<String, UserKnowledgeFileEntity> existMap = new HashMap<>(Const.NUM_32);
        userKnowledgeFileRepository.selectByOldFileIds(dto.getUserId(), fileIds).forEach(item -> existMap.put(item.getOldFileId(), item));

        // 待转存的个人云文件id
        Set<String> transFileIds = new HashSet<>();
        List<Integer> statusList = Arrays.asList(FileTaskStatusEnum.UNPROCESSED.getStatus(), FileTaskStatusEnum.PROCESSING.getStatus());
        userKnowledgeFileTaskRepository.getByStatus(dto.getUserId(), statusList, KnowledgeTaskTypeEnum.TRANSFER).forEach(entity -> transFileIds.addAll(Arrays.asList(entity.getFileIds().split(","))));

        // 标签文件映射关系（旧文件id）
        Map<String, List<UserKnowledgeLabelFileEntity>> labelFileMap = userKnowledgeLabelFileRepository.selectByUserId(dto.getUserId(), null, fileIds)
                .stream().collect(Collectors.groupingBy(UserKnowledgeLabelFileEntity::getOldFileId));

        // 循环校验文件
        Long labelId = Long.parseLong(dto.getLabelId());
        List<KnowledgeAddResultVO> resultList = new ArrayList<>();
        List<String> fileIdsList = new ArrayList<>();
        List<UserKnowledgeLabelFileEntity> labelFileList = new ArrayList<>();
        List<UserKnowledgeFileEntity> addFailList = new ArrayList<>();
        for (File file : dto.getFileList()) {
            // 校验文件
            FileResult vo = validFile(file, fileResultMap, resultList, addFailList, dto.getUserId());
            if (vo == null) {
                continue;
            }

            // db校验
            // 1、相同文件（文件id+hashvalue） 相同标签，提示文件已存在
            // 2、相同文件（文件id+hashvalue） 不同标签，新旧标签同时挂载同一个文件
            // 3、文件id相同，hashvalue不同，则上传新文件；上传成功删除旧文件，标签不同，则新旧标签挂载新的文件
            UserKnowledgeFileEntity entity = existMap.get(file.getFileId());
            if (entity != null) {
                // 文件hash值相同
                if (CharSequenceUtil.equals(entity.getHashValue(), vo.getSrcFile().getContentHash())) {
                    // 标签映射关系
                    List<UserKnowledgeLabelFileEntity> labelFileEntityList = labelFileMap.get(file.getFileId());
                    if (labelFileEntityList.stream().anyMatch(item -> item.getLabelId().equals(labelId))) {
                        resultList.add(new KnowledgeAddResultVO(file.getFileId(), ResultCodeEnum.FILE_ALREADY_EXIST));
                    } else {
                        UserKnowledgeLabelFileEntity labelFileEntity = labelFileEntityList.get(0);
                        labelFileEntity.setLabelId(labelId);
                        labelFileList.add(labelFileEntity);

                        resultList.add(new KnowledgeAddResultVO(file.getFileId(), ResultCodeEnum.SUCCESS.getResultCode(), FILE_EXIST_OTHER_LABEL));

                        // 更新文件的更新时间
                        UserKnowledgeFileEntity updateEntity = new UserKnowledgeFileEntity();
                        updateEntity.setId(entity.getId());
                        userKnowledgeFileRepository.update(updateEntity);
                    }
                    continue;
                }
            } else if (transFileIds.contains(file.getFileId())) {
                log.info("文件正在转存中，fileId：{}", file.getFileId());
                resultList.add(new KnowledgeAddResultVO(file.getFileId(), ResultCodeEnum.FILE_REPEAT_UPLOAD));
                continue;
            }


            // 校验通过
            resultList.add(new KnowledgeAddResultVO(file.getFileId(), ResultCodeEnum.FILE_UPLOADING));

            fileIdsList.add(file.getFileId());
        }

        // 任务对象
        UserKnowledgeFileTaskEntity taskEntity = createFileTask(dto, resultList, fileIdsList, configInfo);

        // 保存映射关系
        labelFileList.forEach(userKnowledgeLabelFileRepository::add);

        // 发送延迟mq
        try {
            knowledgeTransTaskMQService.sendMq(taskEntity, "");
        } catch (Exception e) {
            // 有异常，定时器兜底
            log.error("向mq发送消息失败，异常信息：{}", e.getMessage());
        }

        return taskEntity.getId();
    }

    /**
     * 个人云文件转存
     *
     * @param dto 转存文件参数
     * @return 转存任务id
     */
    public KnowledgeTaskResultVO addPersonalFile180(KnowledgeFileAddReqDTO dto) {

        List<KnowledgeAddResultVO> resultList = new ArrayList<>();
        List<UserKnowledgeFileEntity> addList = new ArrayList<>();
        UserKnowledgeFileTaskEntity taskEntity = addHandle(dto, resultList, addList);

        // 发送延迟mq
        try {
            // mq扩展信息
            List<Map<String, Object>> ext = new ArrayList<>();
            for (UserKnowledgeFileEntity entity : addList) {
                Map<String, Object> m = new HashMap<>(Const.NUM_16);
                m.put("id", entity.getId());
                m.put("oldFileId", entity.getOldFileId());
                ext.add(m);
            }
            knowledgeTransTaskMQService.sendMq(taskEntity, JsonUtil.toJson(ext));
        } catch (Exception e) {
            // 有异常，定时器兜底
            log.error("向mq发送消息失败，异常信息：{}", e.getMessage());
        }

        // 返回结果
        KnowledgeTaskResultVO resultVO = new KnowledgeTaskResultVO();
        resultVO.setResultList(resultList);
        if (ObjectUtil.isEmpty(addList)) {
            resultVO.setStatus(FileTaskResultEnum.FAIL.getStatus());
        } else {
            resultVO.setStatus(FileTaskResultEnum.PROCESSING.getStatus());
        }
        return resultVO;
    }


    @Transactional
    public UserKnowledgeFileTaskEntity addHandle(KnowledgeFileAddReqDTO dto, List<KnowledgeAddResultVO> resultList, List<UserKnowledgeFileEntity> addList){
// 批量获取文件信息（个人云文件id）
        List<String> fileIds = dto.getFileList().stream().map(File::getFileId).collect(Collectors.toList());
        BatchFileVO batchFileVO = yunDiskExternalService.fileBatchGetByAllPlatform(dto.getUserId(), RequestContextHolder.getBelongsPlatform(), fileIds);
        Map<String, FileResult> fileResultMap = batchFileVO.getBatchFileResults().stream().collect(Collectors.toMap(k -> k.getSrcFile().getFileId(), v -> v));

        // 查询用户独立空间
        UserDriveConfigEntity driveConfigEntity = userDriveConfigDomainService.createDrive(dto.getUserId(), RequestContextHolder.getBelongsPlatform());
        CatalogConfigDTO.CatalogConfigInfo configInfo = userDriveConfigDomainService.createKnowledgeConfig(driveConfigEntity);

        // 文件表未删除的文件
        Map<String, UserKnowledgeFileEntity> existMap = new HashMap<>(Const.NUM_32);
        userKnowledgeFileRepository.selectByOldFileIds(dto.getUserId(), fileIds).forEach(item -> existMap.put(item.getOldFileId(), item));

        // 循环校验文件
        List<String> fileIdsList = new ArrayList<>();

        // 记录超过大小限制及不支持格式的文件如个人知识库列表
        List<UserKnowledgeFileEntity> addFailList = new ArrayList<>();
        for (File file : dto.getFileList()) {
            // 校验文件
            FileResult vo = validFile(file, fileResultMap, resultList, addFailList, dto.getUserId());
            if (vo == null) {
                continue;
            }

            // db校验
            UserKnowledgeFileEntity entity = existMap.get(file.getFileId());
            if (entity != null) {
                log.info("文件已转存或正在转存中，fileId：{}，ai_status:{}", file.getFileId(), entity.getAiStatus());
                resultList.add(new KnowledgeAddResultVO(file.getFileId(), ResultCodeEnum.FILE_REPEAT_UPLOAD));
                continue;
            }

            // 校验通过
            resultList.add(new KnowledgeAddResultVO(file.getFileId(), ResultCodeEnum.FILE_UPLOADING));

            fileIdsList.add(file.getFileId());
            addList.add(new UserKnowledgeFileEntity(vo.getSrcFile(), dto.getUserId(), null, null, null));
        }

        // 将校验文件大小不通过的、格式不支持的文件, 也保存到algorithm_user_knowledge_file表中
        if (ObjectUtil.isNotEmpty(addFailList)) {
            userKnowledgeFileRepository.batchAdd(addFailList);
        }

        // 判断是否为空，不为空则保存文件表
        if (ObjectUtil.isNotEmpty(addList)) {
            // 保存到用户知识库文件表
            userKnowledgeFileRepository.batchAdd(addList);
            addList.forEach(item -> {
                for (KnowledgeAddResultVO vo : resultList) {
                    if (vo.getFileId().equals(item.getOldFileId())) {
                        vo.setKnowledgeFileId(item.getId());
                    }
                }
            });
        }


        // 任务对象
        UserKnowledgeFileTaskEntity taskEntity = createFileTask(dto, resultList, fileIdsList, configInfo);
        return taskEntity;
    }

    /**
     * 文件校验
     *
     * @param file          文件
     * @param fileResultMap 文件结果集
     * @param resultList    结果集
     * @return 文件结果
     */
    private FileResult validFile(File file, Map<String, FileResult> fileResultMap, List<KnowledgeAddResultVO> resultList, List<UserKnowledgeFileEntity> addFailList, String userId) {
        // 校验
        FileResult vo = fileResultMap.get(file.getFileId());
        if (vo == null || NOT_EXIST_CODE.equals(vo.getErrCode())) {
            log.info("文件不存在，fileId：{}", file.getFileId());
            resultList.add(new KnowledgeAddResultVO(file.getFileId(), ResultCodeEnum.ERROR_NOT_FOUND.getResultCode(), ERROR_NOT_EXIST));
            return null;
        } else if (CharSequenceUtil.isNotEmpty(vo.getErrCode()) && !ResultCodeEnum.SUCCESS.getResultCode().equals(vo.getErrCode())) {
            log.info("未知错误，fileId：{}，错误码：{}，错误信息：{}", file.getFileId(), vo.getErrCode(), vo.getMessage());
            resultList.add(new KnowledgeAddResultVO(file.getFileId(), ResultCodeEnum.UNKNOWN_ERROR));
            return null;
        } else if (CharSequenceUtil.isEmpty(vo.getSrcFile().getFileExtension()) || !knowledgePersonalProperties.getExtList().contains(vo.getSrcFile().getFileExtension().toLowerCase())) {
            log.info("不支持的文件类型，fileId：{}，文件格式：{}", file.getFileId(), vo.getSrcFile().getFileExtension());
            resultList.add(new KnowledgeAddResultVO(file.getFileId(), ResultCodeEnum.ERROR_FILE_FORMAT_SUPPORTED.getResultCode(), ERROR_EXT));
            addFailList.add(new UserKnowledgeFileEntity(vo.getSrcFile(), userId, FileProcessStatusEnum.FAIL.getStatus(), ResultCodeEnum.ERROR_FILE_FORMAT_SUPPORTED.getResultMsg(), ResultCodeEnum.ERROR_FILE_FORMAT_SUPPORTED.getResultCode()));
            return null;
        } else if (vo.getSrcFile().getSize() != null && knowledgePersonalProperties.getSize().compareTo(vo.getSrcFile().getSize()) < 0) {
            log.info("文件过大，fileId：{}，文件大小：{}，限制大小：{}", file.getFileId(), vo.getSrcFile().getSize(), knowledgePersonalProperties.getSize());
            resultList.add(new KnowledgeAddResultVO(file.getFileId(), ResultCodeEnum.FILE_SIZE_LARGE.getResultCode(), ERROR_TOO_LARGE));
            addFailList.add(new UserKnowledgeFileEntity(vo.getSrcFile(), userId, FileProcessStatusEnum.FAIL.getStatus(), ResultCodeEnum.FILE_SIZE_LARGE.getResultMsg(), ResultCodeEnum.FILE_SIZE_LARGE.getResultCode()));
            return null;
        }

        return vo;
    }

    /**
     * 创建文件任务
     */
    private UserKnowledgeFileTaskEntity createFileTask(KnowledgeFileAddReqDTO dto, List<KnowledgeAddResultVO> resultList, List<String> fileIdsList, CatalogConfigDTO.CatalogConfigInfo configInfo) {
        // 任务对象
        UserKnowledgeFileTaskEntity taskEntity = new UserKnowledgeFileTaskEntity();
        taskEntity.setUserId(dto.getUserId());
        taskEntity.setTaskStatus(FileTaskStatusEnum.FINISH.getStatus());
        taskEntity.setTaskType(KnowledgeTaskTypeEnum.TRANSFER.getCode());
        taskEntity.setTaskRequest(JsonUtil.toJson(dto));
        taskEntity.setTaskResponse(JsonUtil.toJson(resultList));
        taskEntity.setExpireTime(knowledgePersonalProperties.getTransferExpireDate());

        // 发起转存任务
        if (CollUtil.isNotEmpty(fileIdsList)) {
            // 文件转存
            String thirdTaskId = userDriveExternalService.transTask(RequestContextHolder.getUserInfo(), configInfo.getCatalogId(), fileIdsList);

            taskEntity.setTaskStatus(FileTaskStatusEnum.PROCESSING.getStatus());
            taskEntity.setThirdTaskId(thirdTaskId);
            taskEntity.setFileIds(String.join(",", fileIdsList));
            taskEntity.setFileNum(fileIdsList.size());
        }

        // 保存
        userKnowledgeFileTaskRepository.add(taskEntity);

        return taskEntity;
    }

    @Override
    public KnowledgeTaskResultVO getTaskResult(KnowledgeFileTaskResultReqDTO dto) {
        // 查询任务
        UserKnowledgeFileTaskEntity entity = userKnowledgeFileTaskRepository.getById(Long.valueOf(dto.getTaskId()));
        if (entity == null) {
            log.info("任务不存在，taskId：{}", dto.getTaskId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        // 返回结果
        KnowledgeTaskResultVO resultVO = new KnowledgeTaskResultVO();
        if (CharSequenceUtil.isNotEmpty(entity.getTaskResponse())) {
            resultVO.setResultList(JsonUtil.parseArray(entity.getTaskResponse(), new TypeReference<List<KnowledgeAddResultVO>>() {
            }));
        }

        if (FileTaskStatusEnum.isUnProcessed(entity.getTaskStatus()) || FileTaskStatusEnum.isProcessing(entity.getTaskStatus())) {
            // 任务处理中
            resultVO.setStatus(FileTaskResultEnum.PROCESSING.getStatus());
            return resultVO;
        }

        if (FileTaskStatusEnum.isFail(entity.getTaskStatus()) || FileTaskStatusEnum.isOverdue(entity.getTaskStatus())) {
            // 处理失败
            resultVO.setStatus(FileTaskResultEnum.FAIL.getStatus());
            return resultVO;
        }

        // 已完成，分为全部成功、部分成功、失败
        if (entity.getSuccessNum() == 0) {
            // 判断错误码
            List<KnowledgeAddResultVO> failList = resultVO.getResultList().stream().filter(item -> FAIL_CODE_SET.contains(item.getResult())).collect(Collectors.toList());
            if (resultVO.getResultList().size() == failList.size()) {
                // 全部失败
                resultVO.setStatus(FileTaskResultEnum.FAIL.getStatus());
            } else if (failList.isEmpty()) {
                // 全部成功
                resultVO.setStatus(FileTaskResultEnum.SUCCEED.getStatus());
            } else {
                // 部分成功
                resultVO.setStatus(FileTaskResultEnum.PARTIAL_SUCCESS.getStatus());
            }
        } else if (entity.getFileNum().equals(entity.getSuccessNum())) {
            // 处理成功
            resultVO.setStatus(FileTaskResultEnum.SUCCEED.getStatus());
        } else {
            // 部分成功
            resultVO.setStatus(FileTaskResultEnum.PARTIAL_SUCCESS.getStatus());
        }

        return resultVO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(KnowledgeFileDeleteReqDTO dto) {
        List<String> fileIds = dto.getFileList().stream().map(File::getFileId).collect(Collectors.toList());
        // 先查出来
        List<UserKnowledgeFileEntity> list = userKnowledgeFileRepository.selectByFileIds(dto.getUserId(), fileIds);
        if (CollUtil.isEmpty(list)) {
            log.info("文件不存在，文件id：{}", fileIds);
            return;
        }

        // 文件id
        List<String> fileIdList = list.stream().map(UserKnowledgeFileEntity::getFileId).collect(Collectors.toList());

        // 删除数据
        userKnowledgeFileRepository.deleteByFileIds(dto.getUserId(), fileIdList, KnowledgeStatusEnum.DELETING);
        userKnowledgeLabelFileRepository.deleteByFileIds(dto.getUserId(), fileIdList);
        // 删除来源于邮箱的hbase的关联数据
        batchDeleteHBaseData(list, dto);

        personalKnowledgeDomainService.batchDeleteDocumentById(dto.getUserId(), fileIdList,
                knowledgePersonalProperties.getEsFileChunkDelete(), knowledgeDeleteThreadPool);

        // 创建删除文件的任务
        UserKnowledgeFileTaskEntity taskEntity = new UserKnowledgeFileTaskEntity();
        taskEntity.setUserId(dto.getUserId());
        taskEntity.setOwnerType(OwnerTypeEnum.AI.getOwnerValue());
        taskEntity.setTaskStatus(FileTaskStatusEnum.UNPROCESSED.getStatus());
        taskEntity.setTaskType(KnowledgeTaskTypeEnum.DELETE.getCode());
        taskEntity.setTaskRequest(JsonUtil.toJson(dto));
        taskEntity.setExpireTime(knowledgePersonalProperties.getDeleteExpireDate());
        taskEntity.setFileIds(String.join(",", fileIdList));
        taskEntity.setFileNum(fileIdList.size());

        // 保存任务
        userKnowledgeFileTaskRepository.add(taskEntity);

        List<UserKnowledgeFileTaskEntity> taskEntityResList = deleteFileRes(dto, fileIds);
        // 发起删除任务
        deleteTask(taskEntity);
        deleteFileResTask(taskEntityResList);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(KnowledgeFileDeleteReqDTO dto) {
        List<String> fileIds = dto.getFileList().stream().map(File::getFileId).collect(Collectors.toList());
        // 递归查询所有子文件数据
        List<UserKnowledgeFileEntity> list = new ArrayList<>();
        for (String fileId:fileIds) {
            list.addAll(getFileTree(dto.getUserId(),fileId));
        }
        if (CollUtil.isEmpty(list)) {
            log.info("文件不存在，fileIds：{}", JSON.toJSONString(fileIds));
            return;
        }
        if (list.size() > knowledgePersonalProperties.getMaxFileDeleteSize()) {
            log.info("删除知识库文件数量超过阈值，最多支持{}个资源，当前数量:{}", knowledgePersonalProperties.getMaxFileDeleteSize(),dto.getFileList().size());
            String msg = String.format("删除知识库文件数量超过阈值，最多支持%d个文件", knowledgePersonalProperties.getMaxFileDeleteSize());
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_DELETE_FILE_NUM_LIMIT.getResultCode(), msg);
        }

        List<List<UserKnowledgeFileEntity>> batches = Lists.partition(list, BATCH_FILE_SIZE);

        for (List<UserKnowledgeFileEntity> batch : batches) {
            deleteFileHandle(dto, batch);
        }

        // 删除文件后，在途数减对应数量
        decrementTaskNum(list.size(), dto.getUserId());

    }

    /**
     * 删除文件后，在途数减对应数量, （用户维度）【最大】消息发送数量控制
     *
     * @param fileSize 删除的文件数量
     */
    private void decrementTaskNum(int fileSize, String userId) {
        try {
            if (userDimensionProperties.isEnabled() && userDimensionProperties.isIsDimensionUser(userId)) {
                // 解析完成后，更新文件资源表状态时，在途数-1, （用户维度）【最大】消息发送数量控制
                String maxRedisKey = CommonConstant.SCAN_USER_KNOWLEDGE_QUEUE_FILE_ADD_PARSE_JOB_MSG_MAX_SEND_NUM;
                for (int i = 0; i < fileSize; i++) {
                    Number number = hcyRedisTemplate.opsForValue().get(maxRedisKey);
                    if (Objects.nonNull(number)) {
                        long maxCount = number.longValue();
                        if (maxCount > 1) {
                            hcyRedisTemplate.opsForValue().decrement(maxRedisKey);
                            log.info("【删除文件】用户维度【最大】消息发送数量控制decrement成功, maxRedisKey:{}, maxCount:{}", maxRedisKey, maxCount);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("【删除文件】用户维度【最大】消息发送数量控制decrement失败, msg:{}", e.getMessage());
        }
    }

    @Override
    public Long batchDeleteAsync(KnowledgeFileListBatchReqDTO dto) {
        // 先查出来
        List<UserKnowledgeFileEntity> list = userKnowledgeFileRepository.selectByFileIds(dto.getUserId(), dto.getResourceIdList());
        if (CollUtil.isEmpty(list)) {
            log.info("文件不存在，文件id：{}", dto.getResourceIdList());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_NOT_FOUND.getResultCode(),ResultCodeEnum.ERROR_NOT_FOUND.getResultMsg());
        }

        // 文件id
        List<String> allFileIdList = list.stream().map(UserKnowledgeFileEntity::getFileId).collect(Collectors.toList());
        // 创建删除文件的任务
        UserKnowledgeFileTaskEntity taskEntity = new UserKnowledgeFileTaskEntity();
        taskEntity.setUserId(dto.getUserId());
        taskEntity.setOwnerType(OwnerTypeEnum.AI.getOwnerValue());
        taskEntity.setTaskStatus(FileTaskStatusEnum.PROCESSING.getStatus());
        taskEntity.setTaskType(KnowledgeTaskTypeEnum.FILE_DELETE.getCode());
        taskEntity.setTaskRequest(JsonUtil.toJson(dto));
        taskEntity.setExpireTime(knowledgePersonalProperties.getDeleteExpireDate());
        taskEntity.setFileIds(String.join(",", allFileIdList));
        taskEntity.setFileNum(allFileIdList.size());

        //（from_resource_type=0）调用独立空间的批量删除接口
        List<String> personalFileIdList = list.stream()
                .filter(entity -> entity.getFromResourceType() == 0)
                .map(UserKnowledgeFileEntity::getFileId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(personalFileIdList)) {
            try {
                // 先删除独立空间数据
                String thirdTaskId = userDriveExternalService.deleteByFileIds(dto.getUserId(), personalFileIdList);
                taskEntity.setThirdTaskId(thirdTaskId);
            } catch (Exception e) {
                log.error("删除独立空间数据失败，personalFileIdList：{}，异常信息：{}", JSON.toJSONString(personalFileIdList), e.getMessage(), e);
            }
        }
        // 数据改成删除中
        userKnowledgeFileRepository.deleteByFileIds(dto.getUserId(), allFileIdList, KnowledgeStatusEnum.DELETING);
        // 保存任务
        userKnowledgeFileTaskRepository.add(taskEntity);
        try {
            // 发送删除（夹）MQ事件
            knowledgeFileDeleteTaskMqService.sendMq(taskEntity);
            log.info("发送删除（夹）MQ事件成功，消息content：{}", taskEntity);
        } catch (Exception e) {
            log.error("发送删除（夹）MQ事件失败,消息content:{}", taskEntity, e);
        }
        return taskEntity.getId();
    }

    public static String convertToRFC3339(Date date) {
        Instant instant = date.toInstant();
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        return zonedDateTime.format(formatter);
    }

    /**
     * 将实体转换为VO
     *
     * @param entity 实体
     * @return VO
     */
    private PersonalKnowledgeTaskInfo convertToVO(UserKnowledgeFileTaskEntity entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        PersonalKnowledgeTaskInfo vo = new PersonalKnowledgeTaskInfo();
        vo.setTaskId(String.valueOf(entity.getId()));
        Integer taskType = entity.getTaskType().equals(KnowledgeTaskTypeEnum.FILE_DELETE.getCode()) ? KnowledgeTaskTypeResultEnum.FILE_DELETE.getCode() : KnowledgeTaskTypeResultEnum.FILE_MOVE.getCode();
        vo.setTaskType(taskType);
        //任务状态 与库中不符，需要+1适配
        //1：待处理
        //2：处理中
        //3：任务成功
        //4：任务失败
        //5：已过期
        //6：部分成功
        vo.setStatus(String.valueOf(entity.getTaskStatus() + 1));
        // 返回将时间格式转换为字符串
        SimpleDateFormat sdf = new SimpleDateFormat(CommonConstant.PERSONAL_DATE_TIME_PATTERN);
        vo.setCreatedAt(sdf.format(entity.getCreateTime()));
        if (entity.getStartTime() != null) {
            vo.setStartedAt(sdf.format(entity.getStartTime()));
        }
        if (entity.getFinishTime() != null) {
            vo.setFinishedAt(sdf.format(entity.getFinishTime()));
        }

         // 1.判断创建时间和当前时间大小展示进度，30s内是50%，30-60s是80%，60s以上是90%
        if (entity.getUpdateTime() != null && entity.getTaskStatus() == 1) {
            try {
                long elapsedSeconds = (System.currentTimeMillis() - entity.getUpdateTime() .getTime()) / 1000;
                int progress;

                if (elapsedSeconds <= PROGRESS_THRESHOLD_30_SECONDS) {
                    progress = PROGRESS_50_PERCENT;
                } else if (elapsedSeconds <= PROGRESS_THRESHOLD_60_SECONDS) {
                    progress = PROGRESS_80_PERCENT;
                } else {
                    progress = PROGRESS_90_PERCENT;
                }
                vo.setProgress(progress);
            } catch (Exception e) {
                log.warn("解析更新时间失败: {}", entity.getUpdateTime(), e);
            }
        }
        vo.setCode(entity.getErrorCode());
        vo.setMessage(entity.getErrorMessage());
        return vo;
    }

    @Override
    public KnowledgeTaskV2ResultVO getTask(KnowledgeFileTaskResultReqDTO dto) {
        // 查询任务
        UserKnowledgeFileTaskEntity entity = userKnowledgeFileTaskRepository.getById(Long.valueOf(dto.getTaskId()));
        if (entity == null) {
            log.info("任务不存在，taskId：{}", dto.getTaskId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        // 返回结果
        KnowledgeTaskV2ResultVO resultVO = new KnowledgeTaskV2ResultVO();
        resultVO.setTaskInfo(convertToVO(entity));
        if (CharSequenceUtil.isNotEmpty(entity.getTaskResponse())) {
            List<KnowledgeFileTaskResVO> taskResponseList = JsonUtil.parseArray(entity.getTaskResponse(), new TypeReference<List<KnowledgeFileTaskResVO>>() {});
            if (CollUtil.isEmpty(taskResponseList)) {
                return resultVO;
            }
            List<PersonalKnowledgeResourceResult> batchResourceResultList = taskResponseList.stream().map(taskResponse -> {
                PersonalKnowledgeResourceResult resourceResult = new PersonalKnowledgeResourceResult();
                resourceResult.setSrcResource(new PersonalKnowledgeResourceV2(taskResponse.getResourceId(), taskResponse.getResourceType(), taskResponse.getType())   );
                resourceResult.setRstResource(new PersonalKnowledgeResourceV2(taskResponse.getResourceId(), taskResponse.getResourceType(), taskResponse.getType()));
                resourceResult.setErrorCode(taskResponse.getErrorCode());
                resourceResult.setMessage(taskResponse.getMessage());
                return resourceResult;
            }).collect(Collectors.toList());
            resultVO.setBatchResourceResultList(batchResourceResultList);
        }
        return resultVO;
    }

    private void deleteFileHandle(KnowledgeFileDeleteReqDTO dto,  List<UserKnowledgeFileEntity> list) {
         // 文件id
         List<String> fileIdList = list.stream().map(UserKnowledgeFileEntity::getFileId).collect(Collectors.toList());
         // 删除数据
         userKnowledgeFileRepository.deleteByFileIds(dto.getUserId(), fileIdList, KnowledgeStatusEnum.DELETING);
         userKnowledgeLabelFileRepository.deleteByFileIds(dto.getUserId(), fileIdList);
         // 删除来源于邮箱的hbase的关联数据
         batchDeleteHBaseData(list, dto);

         personalKnowledgeDomainService.batchDeleteDocumentById(dto.getUserId(), fileIdList,
                 knowledgePersonalProperties.getEsFileChunkDelete(), knowledgeDeleteThreadPool);

         // 创建删除文件的任务
         UserKnowledgeFileTaskEntity taskEntity = new UserKnowledgeFileTaskEntity();
         taskEntity.setUserId(dto.getUserId());
         taskEntity.setOwnerType(OwnerTypeEnum.AI.getOwnerValue());
         taskEntity.setTaskStatus(FileTaskStatusEnum.UNPROCESSED.getStatus());
         taskEntity.setTaskType(KnowledgeTaskTypeEnum.DELETE.getCode());
         taskEntity.setTaskRequest(JsonUtil.toJson(dto));
         taskEntity.setExpireTime(knowledgePersonalProperties.getDeleteExpireDate());
         taskEntity.setFileIds(String.join(",", fileIdList));
         taskEntity.setFileNum(fileIdList.size());

         // 保存任务
         userKnowledgeFileTaskRepository.add(taskEntity);

         List<UserKnowledgeFileTaskEntity> taskEntityResList = deleteFileRes(dto, fileIdList);
         // 发起删除任务
         deleteTask(taskEntity);
         deleteFileResTask(taskEntityResList);
    }

    // 服务层递归查询方法
    private List<UserKnowledgeFileEntity> getFileTree(String userId, String fileId) {
        // 查询当前文件
        UserKnowledgeFileEntity currentFile = userKnowledgeFileRepository.selectByUserIdAndFileId(userId, fileId);
        if (currentFile == null) {
            return Collections.emptyList();
        }

        List<UserKnowledgeFileEntity> result = new ArrayList<>();
        result.add(currentFile);

        // 递归查询子文件
        if (FileTypeEnum.FOLDER.getKnowledgeFileType().equals(currentFile.getFileType())) {
            findChildren(userId, currentFile.getFileId(), result);
        }

        return result;
    }

    private void findChildren(String userId, String parentFileId, List<UserKnowledgeFileEntity> result) {
        List<UserKnowledgeFileEntity> children = userKnowledgeFileRepository.selectChildrenByParentId(userId, parentFileId);
        if (children.isEmpty()) {
            return;
        }

        result.addAll(children);
        for (UserKnowledgeFileEntity child : children) {
            if (FileTypeEnum.FOLDER.getKnowledgeFileType().equals(child.getFileType())) {
                findChildren(userId, child.getFileId(), result);
            }
        }
    }

    private void batchDeleteHBaseData(List<UserKnowledgeFileEntity> list, KnowledgeFileDeleteReqDTO dto) {
        // 遍历list，挑选出from_resource_type为邮箱的数据，
        List<String> fileIdHbaseList = new ArrayList<>();
        for (UserKnowledgeFileEntity item : list) {
            if (item != null && ResourceTypeEnum.isMail(item.getFromResourceType())) {
                fileIdHbaseList.add(item.getFileId());
            }
        }
        log.info("将要删除的hbase数据，fileIdHbaseList: {}， userId: {}", fileIdHbaseList, dto.getUserId());
        if (CollUtil.isEmpty(fileIdHbaseList)) {
            return;
        }
        String userId = dto.getUserId();
        List<String> rowKeyList = new ArrayList<>();

        // 构建rowKeyList
        for (String fileId : fileIdHbaseList) {
            String rowKey = userId + CONNECT_SPLIT + fileId;
            rowKeyList.add(rowKey);
        }
        while (!rowKeyList.isEmpty()) {
            List<String> subRowKeyList = rowKeyList.subList(0, Math.min(rowKeyList.size(), knowledgePersonalProperties.getHbaseDelete()));
            // 删除hbase数据
            Boolean delFlag = hbaseRepository.delByRowKeyList(subRowKeyList, AlgorithmRagTextContentPO.class);
            log.info("分批删除中的hbase数据，subRowKeyList: {}，删除是否成功: {}， userId: {}", subRowKeyList, delFlag, userId);
            // 删除subList
            rowKeyList.removeAll(subRowKeyList);
        }
    }

    /**
     * 生成删除任务
     *
     * @param dto         参数dto
     * @param fromFileIds 来源文件id集合
     */
    private List<UserKnowledgeFileTaskEntity> deleteFileRes(KnowledgeFileDeleteReqDTO dto, List<String> fromFileIds) {
        // 先查询表algorithm_user_knowledge_file_res，查询出删除标识为正常0的记录
        List<UserKnowledgeFileResEntity> fileResList = userKnowledgeFileResRepository.selectByFileIds(dto.getUserId(), fromFileIds);
        log.info("删除文件资源，userId：{}，fromFileIds：{}, fileResList.size: {}", dto.getUserId(), fromFileIds, fileResList.size());
        // 遍历fileResList，更新表algorithm_user_knowledge_file_res对应记录的删除标识为删除中2
        // 用于存储待删除的fileId
        List<String> fileIdResList = new ArrayList<>(); 
        if (CollUtil.isNotEmpty(fileResList)) {
            for (UserKnowledgeFileResEntity entity : fileResList) {
                fileIdResList.add(entity.getFileId());
                entity.setUpdateTime(new Date());
                entity.setDelFlag(UserKnowledgeFileResDelFlagEnum.DELETING.getDelFlag());
                userKnowledgeFileResRepository.update(entity);
            }
        } else {
            log.info("文件里并没有图片或者文件里的图片都没有成功上传到独立空间，userId：{}，fromFileIds：{}，fileResList.size: {}",
                    dto.getUserId(), fromFileIds, fileResList.size());
        }
        // 依次取出fileIdResList的固定数量（100）个元素，保存任务，直到fileIdResList为空
        List<UserKnowledgeFileTaskEntity> taskEntityList = new ArrayList<>();
        while (!fileIdResList.isEmpty()) {
            List<String> subList = fileIdResList.subList(0, Math.min(fileIdResList.size(), knowledgePersonalProperties.getFileResDelete()));
            // 保存任务
            UserKnowledgeFileTaskEntity taskEntity = new UserKnowledgeFileTaskEntity();
            taskEntity.setUserId(dto.getUserId());
            taskEntity.setOwnerType(OwnerTypeEnum.AI.getOwnerValue());
            taskEntity.setTaskStatus(FileTaskStatusEnum.UNPROCESSED.getStatus());
            taskEntity.setTaskType(KnowledgeTaskTypeEnum.DELETE_RES.getCode());
            taskEntity.setTaskRequest(JsonUtil.toJson(dto));
            taskEntity.setExpireTime(knowledgePersonalProperties.getDeleteExpireDate());
            taskEntity.setFileIds(String.join(",", subList));
            taskEntity.setFileNum(subList.size());

            userKnowledgeFileTaskRepository.add(taskEntity);
            taskEntityList.add(taskEntity);
            // 删除subList
            fileIdResList.removeAll(subList);
        }

        return taskEntityList;
    }

    /**
     * 删除任务
     *
     * @param taskEntityResList 删除任务实体集合
     */
    private void deleteFileResTask(List<UserKnowledgeFileTaskEntity> taskEntityResList) {
        // 遍历taskEntityResList，更新表algorithm_user_knowledge_file_task对应记录
        for (UserKnowledgeFileTaskEntity entity : taskEntityResList) {
            final List<String> fileIdList = Arrays.asList(entity.getFileIds().split(","));

            // 发起删除任务
            String thirdTaskId = "";
            try {
                thirdTaskId = userDriveExternalService.deleteByFileIds(entity.getUserId(), fileIdList);
                entity.setTaskStatus(FileTaskStatusEnum.PROCESSING.getStatus());
                log.info("删除文件资源，userId：{}，fileIds：{}, thirdTaskId: {}", entity.getUserId(), fileIdList, thirdTaskId);
            } catch (Exception e) {
                entity.setTaskStatus(FileTaskStatusEnum.FAIL.getStatus());
                log.error("删除文件资源调起接口失败，userId：{}，fileIds：{}, thirdTaskId: {}, Exception: {}", entity.getUserId(),
                        fileIdList, thirdTaskId, e.getMessage(), e);
            }
            entity.setThirdTaskId(thirdTaskId);
            userKnowledgeFileTaskRepository.update(entity);
        }
    }

    /**
     * 删除任务
     *
     * @param taskEntity 删除任务实体
     */
    private void deleteTask(final UserKnowledgeFileTaskEntity taskEntity) {
        try {
            final List<String> fileIdList = Arrays.asList(taskEntity.getFileIds().split(","));

            // 发起删除任务
            String thirdTaskId = userDriveExternalService.deleteByFileIds(taskEntity.getUserId(), fileIdList);
            taskEntity.setTaskStatus(FileTaskStatusEnum.PROCESSING.getStatus());
            taskEntity.setThirdTaskId(thirdTaskId);
            userKnowledgeFileTaskRepository.update(taskEntity);

            // 异步查询任务
            Map<String, String> logMap = MDC.getCopyOfContextMap();
            businessThreadPool.execute(() -> {
                MDC.setContextMap(logMap);
                for (int i = 0; i < knowledgePersonalProperties.getDeleteTaskQueryTimes(); i++) {
                    try {
                        Thread.sleep(knowledgePersonalProperties.getDeleteTaskQuerySleep());
                    } catch (InterruptedException e) {
                        log.error("线程休眠异常", e);
                        Thread.currentThread().interrupt();
                    }

                    // 查询删除任务结果，这里只管成功状态，其他状态由中心任务的定时器扫描处理
                    Date startTime = new Date();
                    OwnerDriveTaskResultVO resultVO = userDriveExternalService.getDeleteTaskResult(taskEntity.getUserId(), taskEntity.getThirdTaskId());
                    if (OwnerDriveTaskStatusEnum.isSuccess(resultVO.getTaskInfo().getStatus())) {
                        // 成功，更新状态
                        taskEntity.setStartTime(startTime);
                        taskEntity.setFinishTime(new Date());
                        taskEntity.setTaskStatus(FileTaskStatusEnum.FINISH.getStatus());
                        taskEntity.setExecuteCount(i);
                        taskEntity.setSuccessNum(taskEntity.getFileNum());
                        userKnowledgeFileTaskRepository.update(taskEntity);

                        // 更新为已删除
                        userKnowledgeFileRepository.deleteByFileIds(taskEntity.getUserId(), fileIdList, KnowledgeStatusEnum.DELETED);
                    }

                    if (!OwnerDriveTaskStatusEnum.isRunning(resultVO.getTaskInfo().getStatus())) {
                        log.info("删除任务结束，任务状态：{}", resultVO.getTaskInfo().getStatus());
                        break;
                    }
                }
            });
        } catch (Exception e) {
            log.error("发起删除任务失败，任务id：{}，异常信息：{}", taskEntity.getId(), e.getMessage(), e);
        }
    }

}
