package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.constants.HeaderConstants;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.hcy.yun.ai.common.model.api.client.vo.TextModelBaseVo;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelSearchOptionDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.context.ThirdUserInformationContextHolder;
import com.zyhl.hcy.yun.ai.common.rag.enums.RewriteQueryTypeEnum;
import com.zyhl.hcy.yun.ai.common.rag.vo.RewriteResultVO;
import com.zyhl.yun.api.outer.application.assembler.ChatFlowResultAssembler;
import com.zyhl.yun.api.outer.application.chatv2.dto.KnowledgeSearchDTO;
import com.zyhl.yun.api.outer.application.chatv2.pojo.KnowledgeFlowInfo;
import com.zyhl.yun.api.outer.application.chatv2.pojo.KnowledgeFlowInfoDataHandle;
import com.zyhl.yun.api.outer.application.chatv2.service.KnowledgeAiExpansionService;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueFlowResultVO;
import com.zyhl.yun.api.outer.config.KnowledgeDialogueProperties;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.config.WhiteListProperties;
import com.zyhl.yun.api.outer.config.knowledge.AiExpansionProperties;
import com.zyhl.yun.api.outer.config.textmodel.ModelPromptProperties;
import com.zyhl.yun.api.outer.constants.KnowledgeConstants;
import com.zyhl.yun.api.outer.domain.req.LlmChatReqDTO;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.valueobject.llm.LlmChatMessage;
import com.zyhl.yun.api.outer.enums.knowledge.AiExpansionRangeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeBaseEnum;
import com.zyhl.yun.api.outer.external.LlmChatExternalService;
import com.zyhl.yun.api.outer.external.MemberCenterExternalService;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.VersionUtil;
import com.zyhl.yun.api.outer.vo.KnowledgeAiExpansionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 描述：
 *
 * <AUTHOR>
 * @date 2025/7/5 13:13
 */
@Slf4j
@Service
public class KnowledgeAiExpansionServiceImpl implements KnowledgeAiExpansionService {

    @Resource
    private ModelPromptProperties modelPromptProperties;
    @Resource
    private LlmChatExternalService llmChatExternalService;
    @Resource
    private KnowledgeDialogueProperties knowledgeDialogueProperties;
    @Resource
    private WhiteListProperties whiteListProperties;
    @Resource
    private ModelProperties modelProperties;
    @Resource
    private SourceChannelsProperties sourceChannelsProperties;
    @Resource
    private MemberCenterExternalService memberCenterExternalService;
    @Resource
    private AiExpansionProperties aiExpansionProperties;
    @Resource
    private ChatFlowResultAssembler chatFlowResultAssembler;

    private static final String REPLACE_KEY_OF_KNOWLEDGE = "{knowledge}";
    private static final String REPLACE_KEY_OF_QUERY = "{query}";
    private static final String KNOWLEDGE_VIP_AI_EXPANSION_SYSTEM_PROMPT = "knowledge_vip_ai_expansion_system_prompt";
    private static final String KNOWLEDGE_AI_EXPANSION_SYSTEM_PROMPT = "knowledge_ai_expansion_system_prompt";


    @Override
    public void setExpansionOutline(KnowledgeFlowInfo knowledgeFlowInfo, Boolean enableWhiteVip) {
        String code = aiExpansionProperties.getModelCode();
        // 提示词key
        String promptKey = Boolean.TRUE.equals(enableWhiteVip) ? KNOWLEDGE_VIP_AI_EXPANSION_SYSTEM_PROMPT : KNOWLEDGE_AI_EXPANSION_SYSTEM_PROMPT;
        // 拼装提示词
        String modelPrompt = modelPromptProperties.getPrompt(promptKey, code);
        // 用户输入内容
        modelPrompt = modelPrompt.replace(REPLACE_KEY_OF_QUERY, knowledgeFlowInfo.getQuery())
                .replace(REPLACE_KEY_OF_KNOWLEDGE, getKnowledgeText(knowledgeFlowInfo.getKnowledgeList(), knowledgeDialogueProperties.isEnableMark()));
        LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】【AI扩写】【调用大模型获取大纲】替换拼装后的提示词 第{}个分块：\n{}", modelPrompt);
        String resultText = null;
        long start = System.currentTimeMillis();
        try {
            // 构建调起大模型请求参数
            List<LlmChatMessage> messages = new ArrayList<>();
            messages.add(new LlmChatMessage(TextModelRoleEnum.USER.getName(), modelPrompt));
            LlmChatReqDTO reqDTO = new LlmChatReqDTO(RequestContextHolder.getUserId(), code, messages);
            reqDTO.setEnableNetworkSearch(true);
            reqDTO.setDisabledReasoningFlag(true);
            TextModelSearchOptionDTO textModelSearchOptionDTO = new TextModelSearchOptionDTO();
            textModelSearchOptionDTO.setForceSearch(true);
            textModelSearchOptionDTO.setSearchStrategy("pro");
            reqDTO.setSearchOption(textModelSearchOptionDTO);
            // 调起大模型
            TextModelBaseVo result = llmChatExternalService.chatNormal(reqDTO);
            // 获取调起大模型后返回的文本生成结果
            resultText = result.getText();
            LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】【AI扩写】【调用大模型生成大纲】第{}个分块：\n{}", resultText);
        } catch (Exception e) {
            log.error("【知识库对话】【RAG重要节点日志】【AI扩写】【调用大模型生成大纲】失败 error:", e);
        } finally {
            log.info("【知识库对话】【RAG重要节点日志】【AI扩写】结束，耗时：{}ms", System.currentTimeMillis() - start);
        }

        knowledgeFlowInfo.getAiExpansion().setOutline(resultText);
    }

    @Override
    public void setAiExpansionInfo(KnowledgeSearchDTO dto, RewriteResultVO rewrite) {
        try {
            // h5Version >= 小天2.1.1版本 才支持知识库对话AI扩写
            if (!VersionUtil.xtH5VersionGte211()) {
                log.info("【知识库对话】【RAG重要节点日志】【AI扩写】当前h5Version版本不支持AI扩写");
                return;
            }

            // AI扩写开关判断
            if (!aiExpansionProperties.isEnabled()) {
                log.info("【知识库对话】【RAG重要节点日志】【AI扩写】AI扩写开关未打开");
                return;
            }

            // 问题类型判断 其他类不走AI扩写逻辑
            if (RewriteQueryTypeEnum.TYPE_0.getQueryType().equals(rewrite.getQueryType())) {
                log.info("【知识库对话】【RAG重要节点日志】【AI扩写】问题类型为其他类，不走AI扩写逻辑 rewrite:{}", JsonUtil.toJson(rewrite));
                return;
            }

            // 白名单判断 云盘会员判断
            String phone = RequestContextHolder.getPhoneNumber();
            boolean isWhiteUser = whiteListProperties.getTextModelCodeWhiteUser().contains(phone);
            Boolean isMember = Boolean.FALSE;
            try {
                isMember = memberCenterExternalService.isMember(phone, sourceChannelsProperties.getAllInnerBenefitNos());
            } catch (Exception e) {
                log.error("【知识库对话】【RAG重要节点日志】【AI扩写】判断用户云盘会员异常 userId:{} | e:", dto.getUserId(), e);
            }
            if (!Boolean.TRUE.equals(isWhiteUser) && !Boolean.TRUE.equals(isMember)) {
                log.info("【知识库对话】【RAG重要节点日志】【AI扩写】用户不是云盘会员也不在VIP白名单 userId:{} | phone:{}", dto.getUserId(), phone);
                return;
            }

            // 切片数量判断
            int chunkCount = dto.getKnowledgeList().stream().map(item -> item.getSegmentList().size()).mapToInt(item -> item).sum();
            Integer configCount = aiExpansionProperties.getChunkCount();
            if (chunkCount < configCount) {
                log.info("【知识库对话】【RAG重要节点日志】【AI扩写】用户需求切片数量不足，不进行扩写 userId:{} | phone:{} | chunkCount:{} | configCount:{}",
                        dto.getUserId(), phone, chunkCount, configCount);
                return;
            }

            // 获取用户需求字数  字数判断
            int wordCount = getUserWordCount(rewrite);
            if (!aiExpansionProperties.isExpansion(wordCount)) {
                log.info("【知识库对话】【RAG重要节点日志】【AI扩写】用户需求字数不足，不进行扩写 userId:{} | phone:{} | wordCount:{}", dto.getUserId(), phone, wordCount);
                return;
            }

            // 扩写对象信息
            KnowledgeAiExpansionVO aiExpansion = new KnowledgeAiExpansionVO();
            aiExpansion.setEnableWhiteVip(isWhiteUser);
            aiExpansion.setEnableMember(isMember);
            aiExpansion.setWordCount(wordCount);
            aiExpansion.setChunkCount(chunkCount);
            aiExpansion.setModelType(aiExpansionProperties.getModelCode());
            AiExpansionRangeEnum rangeEnum = aiExpansionProperties.getAiExpansionRange(wordCount);
            aiExpansion.setRangeEnum(rangeEnum);

            // 发送模型切换提示
            DialogueFlowResultVO flowResultVO = new DialogueFlowResultVO(rangeEnum.getCodeEnum(), dto.getModelType());
            dto.getSseEmitterOperate().sendSuccess(dto.getRespVO().processingFlowResult(flowResultVO));
            log.info("【知识库对话】【RAG重要节点日志】【AI扩写】发送模型切换提示 wordCount:{} | flowResultVO:{}", wordCount, JsonUtil.toJson(flowResultVO));

            // 设置入库hbase结果列表-大模型之前
            List<DialogueFlowResult> beforeOutputList = new ArrayList<>();
            beforeOutputList.add(chatFlowResultAssembler.getFlowResult(flowResultVO));
            aiExpansion.setBeforeOutputList(beforeOutputList);

            // 设置AI扩写信息
            dto.setAiExpansion(aiExpansion);
            LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】【过滤后】【AI扩写】获取扩写信息第{}个分块：\n{}", JsonUtil.toJson(aiExpansion));
        } catch (Exception e) {
            log.error("【知识库对话】【RAG重要节点日志】【过滤后】【AI扩写】处理异常 rewrite:{} | e:", JsonUtil.toJson(rewrite), e);
        }
    }

    /**
     * 获取用户需求字数
     *
     * @param rewrite 重写结果
     * @return 用户需求字数
     */
    private int getUserWordCount(RewriteResultVO rewrite) {
        int wordCount = 0;
        if (!CollectionUtils.isEmpty(rewrite.getExtractedInfo())) {
            int duration = 0;
            for (Map<String, Object> infoMap : rewrite.getExtractedInfo()) {
                if (infoMap.containsKey(KnowledgeConstants.REQUIRE_WORD_NUM_KEY)) {
                    wordCount = (Integer) infoMap.get(KnowledgeConstants.REQUIRE_WORD_NUM_KEY);
                }
                if (infoMap.containsKey(KnowledgeConstants.DURATION_MIN_KEY)) {
                    duration = (Integer) infoMap.get(KnowledgeConstants.DURATION_MIN_KEY);
                }
            }
            // 时长优先级最高
            if (duration > 0) {
                wordCount = duration * aiExpansionProperties.getTimeToWord();
            }
        }
        return wordCount;
    }

    /**
     * 相关知识文本
     *
     * @return 相关知识文本
     */
    private String getKnowledgeText(List<KnowledgeFlowInfoDataHandle> knowledgeList, boolean enableMark) {
        if (ObjectUtil.isEmpty(knowledgeList)) {
            return "暂无资料";
        }
        StringBuilder sb = new StringBuilder("<检索结果>");
        for (int i = 0, size = knowledgeList.size(); i < size; i++) {
            KnowledgeFlowInfoDataHandle data = knowledgeList.get(i);
            if (KnowledgeBaseEnum.isCommon(data.getKnowledgeBase())) {
                // 公共知识库无角标
                data.setFileName("内部文件");
                sb.append(data.format(i + 1, false));
            } else {
                sb.append(data.format(i + 1, enableMark));
            }
        }
        sb.append("</检索结果>");

        LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】【AI扩写】获取大纲第{}个分块：\n{}", sb.toString());
        return sb.toString();
    }
}
