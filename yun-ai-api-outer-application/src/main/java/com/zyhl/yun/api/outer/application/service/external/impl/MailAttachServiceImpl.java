package com.zyhl.yun.api.outer.application.service.external.impl;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.enums.ApiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.MailClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.dto.MailAttachReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.dto.MailDetailReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.vo.MailAttachInfoVO;
import com.zyhl.yun.api.outer.application.service.external.MailAttachService;
import com.zyhl.yun.api.outer.application.vo.MailAttachVO;
import com.zyhl.yun.api.outer.domainservice.IImageCommonService;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;

import cn.hutool.core.collection.CollUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Classname YunDiskServiceImpl
 * @Description 云盘平台接口实现
 * @Date 2024/3/19 14:40
 */
@Service
@Slf4j
public class MailAttachServiceImpl implements MailAttachService {

	@Resource
	private IImageCommonService imageCommonService;
	
	@Resource
	private MailClient mailClient;

	@Resource(name = "mailAttachThreadPool")
	private ExecutorService mailAttachThreadPool;

	private static final Long MAX_FILE_SIZE = 1024 * 1024 * 50L;

	/**
	 * 获取邮箱附件
	 * @param mid
	 * @param sid
	 * @param rmKey
	 * @param fileNames 下载附件名称集合
	 * @param path 本地保存路径
	 */
	@Override
	@SneakyThrows
	public List<MailAttachVO> getMailFiles(String userId, String mid, String sid, String rmKey, List<String> fileNames) {
		List<MailAttachVO> fileList = new ArrayList<>();
		// 根据邮箱ID获取附件列表
		List<MailAttachInfoVO> attaches = getAttachInfoList(mid,sid,rmKey);
		if(CollUtil.isEmpty(attaches)){
			log.info("获取邮箱附件列表为空,mid:{}",mid);
			return fileList;
		}
		// 过滤，获取需要下载的附件列表
		List<MailAttachInfoVO> filteredAttaches = attaches.stream()
				.filter(attach -> fileNames.contains(attach.getFileName()))
				.collect(Collectors.toList());
		if(CollUtil.isEmpty(filteredAttaches)){
			log.info("未匹配到fileName相同的附件,mid:{}",mid);
			return fileList;
		}
		CountDownLatch costDownLatch = new CountDownLatch(filteredAttaches.size());
		//共享存在目录获取（邮件类型）
		String path = imageCommonService.createDirPathBaseOnDate(mid, "mailAttach", userId);
		for (MailAttachInfoVO vo : filteredAttaches) {
			this.mailAttachThreadPool.execute(() -> {
				try {
					MailAttachReqDTO attachReqDTO = createReqDTO(vo, mid, sid, rmKey, path);
					String localPath = mailClient.getMailFileLocalPath(attachReqDTO);
					if(StringUtils.isNotBlank(localPath)){
						// 单个文件最大不超过50M
						checkFile(localPath);
						String fileName = vo.getFileName();
						String fileSuffix = extractFileSuffix(fileName);
						MailAttachVO fileVO = MailAttachVO.builder()
								.fileName(fileName)
								.fileSuffix(fileSuffix)
								.localFilePath(localPath).build();
						fileList.add(fileVO);
					}
				} catch (Exception ex) {
					log.error("==> 邮箱附件下载,执行==> 失败. | e:", ex);
					if(ex instanceof YunAiBusinessException) {
						throw ex;
					}
					throw new YunAiBusinessException(ResultCodeEnum.UNKNOWN_ERROR);
				} finally {
					costDownLatch.countDown();
				}
			});
		}
		costDownLatch.await();
		return fileList;
	}

	/**
	 * 获取邮件内容中的附件信息列表
	 * @param mid
	 * @param sid
	 * @param rmKey
	 * @return
	 */
	private List<MailAttachInfoVO> getAttachInfoList(String mid, String sid, String rmKey) {
		// 构建邮件详情请求DTO
		MailDetailReqDTO mailDetailReqDTO = new MailDetailReqDTO();
		mailDetailReqDTO.setMid(mid);
		mailDetailReqDTO.setSid(sid);
		mailDetailReqDTO.setRmKey(rmKey);
		log.warn("开始请求Mail服务");

		// 返回邮件附件列表
		return mailClient.getMailAttaches(mailDetailReqDTO);
	}

	private MailAttachReqDTO createReqDTO(MailAttachInfoVO vo, String mid, String sid, String rmKey, String path) {
		MailAttachReqDTO reqDTO = new MailAttachReqDTO();
		reqDTO.setMid(mid);
		reqDTO.setSid(sid);
		reqDTO.setRmKey(rmKey);
		reqDTO.setName(vo.getFileName());
		reqDTO.setEncoding(vo.getEncoding());
		reqDTO.setOffset(vo.getFileOffSet());
		reqDTO.setSize(vo.getFileSize());
		reqDTO.setType(vo.getType());
		reqDTO.setPath(path);
		return reqDTO;
	}

	private String extractFileSuffix(String fileName) {
		int dotIndex = fileName.lastIndexOf('.');
		return dotIndex != -1 ? fileName.substring(dotIndex + 1) : "";
	}

	private void checkFile(String localPath){
		File file = new File(localPath);

		if (file.exists() && !file.isDirectory()) {
			// 获取文件大小，单位：字节
			long fileSize = file.length();
			if (fileSize >= MAX_FILE_SIZE) {
				throw new YunAiBusinessException(ApiCommonResultCode.FILE_SIZE_LARGE);
			}
		}

	}
}
