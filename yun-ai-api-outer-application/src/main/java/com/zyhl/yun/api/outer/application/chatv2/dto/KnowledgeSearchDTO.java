package com.zyhl.yun.api.outer.application.chatv2.dto;

import com.zyhl.hcy.yun.ai.common.model.api.client.ali.vo.GenericSearchVO;
import com.zyhl.hcy.yun.ai.common.rag.vo.RewriteResultVO;
import com.zyhl.yun.api.outer.application.chatv2.pojo.KnowledgeFlowInfoDataHandle;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.domain.valueobject.KnowledgeSearchInfo;
import com.zyhl.yun.api.outer.domainservice.SseEmitterOperate;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.chat.SseNameEnum;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.KnowledgeAiExpansionVO;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 知识库搜索DTO
 * 搜索对象，避免多线程数据污染，线程执行完后把数据复制到KnowledgeFlowInfo
 *
 * <AUTHOR> shixiaokang  2025/5/12 9:41
 */
@Data
public class KnowledgeSearchDTO {
    /**
     * 公共知识库可用
     */
    @Getter
    private boolean commonEnable;
    /**
     * 个人知识库可用
     */
    @Getter
    private boolean personalEnable;
    /**
     * VIP专属智能体-公共知识库可用
     */
    @Getter
    private boolean vipCommonEnable;
    /**
     * 用户选中的知识库
     */
    @Getter
    private List<UserKnowledgeEntity> selectedKnowledgeList;
    /**
     * 用户输入
     */
    @Getter
    private String query = "";
    /**
     * 用户ID
     */
    @Getter
    private String userId;
    /**
     * 对话id，需要新生成一个
     */
    @Getter
    private Long dialogueId;

    /**
     * 流式对象操作类
     */
    @Getter
    private SseEmitterOperate sseEmitterOperate;

    /**
     * 响应结果
     */
    @Getter
    private ChatAddRespVO respVO;

    /**
     * 资源信息
     */
    @Getter
    private DialogueAttachmentDTO attachmentDTO;

    /**
     * 助手类型枚举
     */
    @Getter
    private AssistantEnum assistantEnum;

    /**
     * 意图结果对象
     */
    @Getter
    private DialogueIntentionVO intentionVO;

    /**
     * 模型类编码
     */
    @Getter
    private String modelType;

    // --------------以下是结果，要带出线程-------------- //
    /**
     * 相关知识，送给大模型的数据
     */
    @Getter
    @Setter
    private List<KnowledgeFlowInfoDataHandle> knowledgeList = new ArrayList<>();

    /**
     * 个人知识库文件列表
     */
    @Getter
    @Setter
    private List<KnowledgeSearchInfo> personalFileList;

    /**
     * 公共知识库文件列表
     */
    @Getter
    @Setter
    private List<KnowledgeSearchInfo> commonFileList;

    /**
     * 联网搜索数据
     */
    @Getter
    @Setter
    private List<GenericSearchVO> networkList = new ArrayList<>();

    /**
     * sse名称
     */
    @Getter
    @Setter
    private String sseName = SseNameEnum.KNOWLEDGE.getCode();

    /**
     * 智能重写结果
     */
    @Getter
    @Setter
    private RewriteResultVO rewrite;

    /**
     * AI扩写信息
     */
    @Getter
    @Setter
    private KnowledgeAiExpansionVO aiExpansion;

    public KnowledgeSearchDTO(ChatAddHandleDTO handleDTO) {
        this.personalEnable = handleDTO.getKnowledgeFlowInfo().isPersonalEnable();
        this.commonEnable = handleDTO.getKnowledgeFlowInfo().isCommonEnable();
        this.vipCommonEnable = handleDTO.getKnowledgeFlowInfo().isVipCommonEnable();
        this.selectedKnowledgeList = handleDTO.getKnowledgeFlowInfo().getSelectedKnowledgeList();
        this.query = handleDTO.getInputInfoDTO().getDialogue();
        this.userId = handleDTO.getReqDTO().getUserId();
        this.dialogueId = handleDTO.getDialogueId();
        this.sseEmitterOperate = new SseEmitterOperate(handleDTO.getSseEmitterOperate().getSseEmitter(), SseNameEnum.KNOWLEDGE);
        this.respVO = handleDTO.getRespVO();
        this.attachmentDTO = handleDTO.getInputInfoDTO().getAttachment();
        this.assistantEnum = handleDTO.getAssistantEnum();
        this.intentionVO = handleDTO.getIntentionVO();
        this.modelType = handleDTO.getKnowledgeFlowInfo().getChatConfigVO().getModelType();
    }
}