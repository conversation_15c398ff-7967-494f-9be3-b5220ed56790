package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.service.IntelligentMeetingAfterService;
import com.zyhl.yun.api.outer.application.chatv2.service.IntelligentMeetingBeforeService;
import com.zyhl.yun.api.outer.application.chatv2.service.IntelligentMeetingSendMailService;
import com.zyhl.yun.api.outer.application.chatv2.service.IntelligentMeetingStartService;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.application.service.chat.ChatApplicationTypeService;
import com.zyhl.yun.api.outer.application.service.chat.DialogueIntentionService;
import com.zyhl.yun.api.outer.constants.IntelligentConstants;
import com.zyhl.yun.api.outer.domain.vo.ChatApplicationType;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.SseNameEnum;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;

import lombok.extern.slf4j.Slf4j;

/**
 * 智能会议智能体对话
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SpecialIntelligentMeetingHandlerImpl extends AbstractChatAddV2Handler {

    /**
     * 当前执行顺序
     */
    private final ExecuteSort thisExecuteSort = ExecuteSort.SPECIAL_INTELLIGENT_MEETING;

    @Resource
    private ChatApplicationTypeService applicationTypeService;
    @Resource
    private DialogueIntentionService dialogueIntentionService;
    @Resource
    private IntelligentMeetingBeforeService intelligentMeetingBeforeService;
    @Resource
    private IntelligentMeetingStartService intelligentMeetingStartService;
    @Resource
    private IntelligentMeetingAfterService intelligentMeetingAfterService;
    @Resource
    private IntelligentMeetingSendMailService intelligentMeetingSendMailService;
    @Resource
    private DataSaveService dataSaveService;
    @Resource
    private AiTextResultRepository aiTextResultRepository;
    @Resource
    private ChatTextToolBusinessConfig chatTextToolBusinessConfig;
    @Resource
    private TextModelTextSseHandlerImpl textModelTextSseHandlerImpl;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 支持的业务初始化
        List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        thisBusinessTypes.add(ChatBusinessTypeEnum.CLOUD_PHONE);
        thisBusinessTypes.add(ChatBusinessTypeEnum.MESSAGE_5G);
        this.setBusinessTypes(thisBusinessTypes);
    }

    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public boolean execute(ChatAddHandleDTO handleDTO) {
        return ApplicationTypeEnum.isIntelligen(handleDTO.getReqDTO().getApplicationType());
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        log.info("进入{}", thisExecuteSort.getDesc());

        ChatAddReqDTO reqDTO = handleDTO.getReqDTO();
        ChatApplicationType chatApplicationType = applicationTypeService
                .getChatApplicationTypeCache(reqDTO.getApplicationId(), reqDTO.getApplicationType());
        if (null == chatApplicationType) {
            log.info("智能体信息不存在 applicationId:{}", reqDTO.getApplicationId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        if (!IntelligentConstants.TYPE_OF_INTELLIGENT_MEETING.equals(chatApplicationType.getTypeRelationId())) {
            // 智能体不匹配，继续执行
            return true;
        }

		// 意图非（036文本工具意图||032发邮件意图），才进行意图识别
		if (!(DialogueIntentionEnum.isTextToolIntention(handleDTO.getIntentionCode())
				|| DialogueIntentionEnum.isSendMail(handleDTO.getIntentionCode()))) {
			DialogueIntentionVO intentionVO = dialogueIntentionService.getIntentionVOV2(handleDTO);
			if (null == intentionVO) {
				log.info("意图识别失败，转换为文本意图");
				handleDTO.setTextGenerateTextIntention();
				return intelligentGenDialogue(handleDTO);
			}
			// 设置意图识别结果
			handleDTO.setIntentionVO(intentionVO);
		}

		// 1 会议通知发邮件意图：制定会议发邮件流程
		if (DialogueIntentionEnum.isTextToolIntention(handleDTO.getIntentionCode())
				&& DialogueIntentionSubEnum.isMeetingMail(handleDTO.getSubIntentionCode())) {
			if (!intelligentMeetingBeforeService.run(handleDTO)) {
				return false;
			} else {
				log.info("智能会议-前置-036_036019意图执行无效");
			}
		}
		// 2 打开录音笔记意图：LeadCopy配置及推送
		if (DialogueIntentionEnum.CREATE_VOICE_NOTE.getCode().equals(handleDTO.getIntentionCode())) {
			if (!intelligentMeetingStartService.run(handleDTO)) {
				return false;
			} else {
				log.info("智能会议-开始-027意图执行无效");
			}
		}

		// 3 AI生成ppt意图
		if (DialogueIntentionEnum.isTextToolIntention(handleDTO.getIntentionCode())
				&& DialogueIntentionSubEnum.isAiPpt(handleDTO.getSubIntentionCode())) {
			if (!intelligentMeetingAfterService.run(handleDTO)) {
				return false;
			} else {
				log.info("智能会议-后置-036_036001意图执行无效");
			}
		}
		
		// 4 发邮件意图
		if (DialogueIntentionEnum.isSendMail(handleDTO.getIntentionCode())) {
			if (!intelligentMeetingSendMailService.run(handleDTO)) {
				return false;
			} else {
				log.info("智能会议-032意图执行无效");
			}
		}

        log.info("意图识别不存在的流程，转换为文本意图");
        handleDTO.setTextGenerateTextIntention();
        return intelligentGenDialogue(handleDTO);
    }

    /**
     * 普通大模型对话
     *
     * @param handleDTO
     * @return
     */
    private boolean intelligentGenDialogue(ChatAddHandleDTO handleDTO) {
        // 保存到hbase
        dataSaveService.saveTextResult(handleDTO, "", "");

        // 保存数据库
        dataSaveService.add(handleDTO, ChatStatusEnum.CHAT_IN);

        // 历史对话
        List<TextModelMessageDTO> historyList = aiTextResultRepository.getHistoryList(handleDTO.getReqDTO().getUserId(),
                handleDTO.getSessionId());

        // 监听器
        SseEventListener event = new SseEventListener(handleDTO, historyList);

        // 普通对话模型
        event.setModelCode(chatTextToolBusinessConfig.getIntelligentMeeting().getDefaultModelCode());

        // 复用普通对话handler
        textModelTextSseHandlerImpl.simpleDialogue(event, handleDTO);
        return false;
    }

}
