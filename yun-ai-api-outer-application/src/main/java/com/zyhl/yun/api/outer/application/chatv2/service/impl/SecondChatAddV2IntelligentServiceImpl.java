package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.chatv2.dto.AssistantChatV2PollingUpdateDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.SecondStreamChatAddV2InnerDTO;
import com.zyhl.yun.api.outer.application.chatv2.service.AlgorithmChatV2ContentService;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatContentService;
import com.zyhl.yun.api.outer.application.chatv2.service.SecondChatAddV2IntelligentService;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueResultV2VO;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.config.SecondStreamIntelligentProperties;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.valueobject.AiFunctionResult;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;

import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 二次对话-智能体相关业务-接口实现类
 *
 * <AUTHOR>
 * @date 2025/6/4 13:40
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SecondChatAddV2IntelligentServiceImpl implements SecondChatAddV2IntelligentService {

    private static final String REPLACE_QUERY = "{query}";

    private final SecondStreamIntelligentProperties secondStreamIntelligentProperties;

    private final ChatContentService chatContentService;

    private final AlgorithmChatV2ContentService algorithmChatV2ContentService;

    private final ChatTextToolBusinessConfig chatTextToolBusinessConfig;


    @Override
    public void runMeetingMailResult(SecondStreamChatAddV2InnerDTO innerDTO) {
        log.info("进入执行会议发邮件结果 dialogueId:{}", innerDTO.getDialogResult().getDialogueId());
        // 找到当前outputList，最后一个并且是resultType=com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum.MAIL，就有mailInfo
        DialogueResultV2VO dialogResult = innerDTO.getDialogResult();
        List<DialogueFlowResult> outputList = dialogResult.getOutputList();
        if (CollUtil.isNotEmpty(outputList)) {
            // 获取最后一个并判断resultType是否为邮件
            DialogueFlowResult flowResult = outputList.get(outputList.size() - 1);
            if (FlowResultTypeEnum.MAIL.getType().equals(flowResult.getResultType())) {
                innerDTO.setMailInfo(flowResult.getMailInfo());
            }
        }
        if (null == innerDTO.getMailInfo()) {
            // 非发邮件意图，返回错误信息
            log.error("非发邮件意图，返回错误信息");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_DIALOGUE_ID);
        }
        if (Boolean.TRUE.equals(innerDTO.getGetOutContent())) {
            //获取上个对话的大模型输出，并设置到变量
            Optional<DialogueFlowResult> optional = outputList.stream().filter(dialogueFlowResult ->
                    FlowResultTypeEnum.TEXT_MODEL.getType().equals(dialogueFlowResult.getResultType())).findFirst();
            optional.ifPresent(dialogueFlowResult ->
                    innerDTO.setOutContent(chatTextToolBusinessConfig.getIntelligentMeeting().splitMailContent(dialogueFlowResult.getOutContent())));
        }
        SecondStreamIntelligentProperties.SecondStreamSendMailConfig meetingSendMail = secondStreamIntelligentProperties.getMeetingSendMail();
        // 获取配置的模型编码
        innerDTO.setModelCode(meetingSendMail.getModeCode());
        // 拼接提示词+用户query
        String dialogue = innerDTO.getDialogResult().getDialogueInputInfo().getDialogue();
        String template = meetingSendMail.getPromptTemplate().replace(REPLACE_QUERY, dialogue);
        innerDTO.setQueryContent(template);
    }

    @Override
    public void runAiPptResult(SecondStreamChatAddV2InnerDTO innerDTO) {
        log.info("进入执行aippt结果 dialogueId:{}", innerDTO.getDialogResult().getDialogueId());
        // 找到这个时间点之前的最近的一个对话（是会议发邮件意图）
        /**
         * 小于，innerDTO.getDialogResult().getCreateTime()
         * 同一个 innerDTO.getDialogResult().getSessionId()
         * DialogueIntentionEnum.TEXT_TOOL.getCode()
         * DialogueIntentionSubEnum.AI_MEETING_MAIL.getCode()
         * 创建时间倒序，取最新一条
         */
        AlgorithmChatContentEntity lastMailDialogueInfo = getLastIntentionDialogue(innerDTO);
        if (Objects.isNull(lastMailDialogueInfo)) {
            log.error("二次流式对话处理，最近一次邮件发送对话不存在");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_DIALOGUE_ID);
        }
        //找到这个对话id，再调用algorithmChatV2ContentService.pollingUpdate
        AssistantChatV2PollingUpdateDTO pollingReq = new AssistantChatV2PollingUpdateDTO();
        pollingReq.setDialogueId(lastMailDialogueInfo.getId());
        pollingReq.setUserId(innerDTO.getReqParams().getUserId());
        DialogueResultV2VO dialogResult = algorithmChatV2ContentService.pollingUpdate(pollingReq);
        List<DialogueFlowResult> outputList = dialogResult.getOutputList();
        // 找到当前outputList，最后一个并且是resultType=com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum.MAIL，就有mailInfo
        if (CollUtil.isNotEmpty(outputList)) {
            DialogueFlowResult flowResult = outputList.get(outputList.size() - 1);
            AiFunctionResult aiFunctionResult = null;
            if (FlowResultTypeEnum.MAIL.getType().equals(flowResult.getResultType())) {
                MailInfoVO mailInfo = new MailInfoVO();
                //需要设置收件人
                mailInfo.setRecipientList(flowResult.getMailInfo().getRecipientList());
                //获取当前对话的ppt信息
                aiFunctionResult = innerDTO.getDialogResult().getOutputList().get(0).getAiFunctionResult();
                if (null != aiFunctionResult) {
                    mailInfo.setTitle(aiFunctionResult.getTitle());
                    if (Objects.nonNull(aiFunctionResult.getFile())) {
                        innerDTO.setFileList(Collections.singletonList(aiFunctionResult.getFile()));
                    }
                }
                innerDTO.setMailInfo(mailInfo);
                SecondStreamIntelligentProperties.SecondStreamSendMailConfig pptSendMailConfig = secondStreamIntelligentProperties.getPptSendMail();
                // 获取配置的模型编码
                innerDTO.setModelCode(pptSendMailConfig.getModeCode());
                // 拼接提示词+用户query
                String dialogue = innerDTO.getDialogResult().getDialogueInputInfo().getDialogue();
                String template = pptSendMailConfig.getPromptTemplate().replace(REPLACE_QUERY, dialogue);
                innerDTO.setQueryContent(template);
            }
            if (Objects.isNull(innerDTO.getMailInfo()) || Objects.isNull(aiFunctionResult) || Objects.isNull(aiFunctionResult.getFile())) {
                // 非发邮件意图&&ppt未生成，返回错误信息
                log.error("非发邮件意图&&ppt未生成，返回错误信息");
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_DIALOGUE_ID);
            }
        }
    }

    private AlgorithmChatContentEntity getLastIntentionDialogue(SecondStreamChatAddV2InnerDTO innerDTO) {
        AlgorithmChatContentEntity condition = new AlgorithmChatContentEntity();
        condition.setUserId(innerDTO.getReqParams().getUserId());
        condition.setSessionId(Long.parseLong(innerDTO.getDialogResult().getSessionId()));
        condition.setToolsCommand(DialogueIntentionEnum.TEXT_TOOL.getCode());
        condition.setSubToolsCommand(DialogueIntentionSubEnum.AI_MEETING_MAIL.getCode());
        condition.setId(Long.parseLong(innerDTO.getReqParams().getDialogueId()));
        return chatContentService.getLastIntentionDialogue(condition);
    }

    @Override
    public void runSendMailAndAiPPtResult(SecondStreamChatAddV2InnerDTO innerDTO) {
        log.info("进入执行会议发邮件结果和aippt结果 dialogueId:{}", innerDTO.getDialogResult().getDialogueId());
        DialogueResultV2VO dialogResult = innerDTO.getDialogResult();
		if (!(null != dialogResult.getExtInfoParam() && null != dialogResult.getExtInfoParam().getDialogueFlowResult()
				&& null != dialogResult.getExtInfoParam().getDialogueFlowResult().getMailInfo()
				&& null != dialogResult.getExtInfoParam().getDialogueFlowResult().getAiFunctionResult()
				&& null != dialogResult.getExtInfoParam().getDialogueFlowResult().getAiFunctionResult().getFile())) {
            log.error("无发邮件信息或者ppt未生成，返回错误信息");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_DIALOGUE_ID);
        }
        // 获取mailInfo和附件
        innerDTO.setMailInfo(dialogResult.getExtInfoParam().getDialogueFlowResult().getMailInfo());
        innerDTO.setFileList(Collections.singletonList(dialogResult.getExtInfoParam().getDialogueFlowResult().getAiFunctionResult().getFile()));
        
        // 获取大模型结果
        List<DialogueFlowResult> outputList = dialogResult.getOutputList();
        if (CollUtil.isNotEmpty(outputList)) {
            if (Boolean.TRUE.equals(innerDTO.getGetOutContent())) {
                //获取上个对话的大模型输出，并设置到变量
                Optional<DialogueFlowResult> optional = outputList.stream().filter(dialogueFlowResult ->
                        FlowResultTypeEnum.TEXT_MODEL.getType().equals(dialogueFlowResult.getResultType())).findFirst();
                optional.ifPresent(dialogueFlowResult ->
                        innerDTO.setOutContent(chatTextToolBusinessConfig.getIntelligentMeeting().splitMailContent(dialogueFlowResult.getOutContent())));
            }
        }
    }
}
