package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 云盘规划智能体对话
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SpecialIntelligentPlanHandlerImpl extends AbstractChatAddV2Handler {

	/**
	 * 当前执行顺序
	 */
	private final ExecuteSort thisExecuteSort = ExecuteSort.SPECIAL_INTELLIGENT_PLAN;

	@Override
	public void afterPropertiesSet() throws Exception {
    	//支持的业务初始化
    	List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        this.setBusinessTypes(thisBusinessTypes);
    }
    
	@Override
	public int order() {
		return thisExecuteSort.getSort();
	}

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		log.info("进入{}", thisExecuteSort.getDesc());
		return true;
	}

	@Override
	public boolean execute(ChatAddHandleDTO innerDTO) {
		return true;
	}

}
