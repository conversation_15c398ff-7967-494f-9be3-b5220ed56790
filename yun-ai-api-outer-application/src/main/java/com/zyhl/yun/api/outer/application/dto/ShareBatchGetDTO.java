package com.zyhl.yun.api.outer.application.dto;

import lombok.Data;

import java.util.List;

/**
 * 批量获取分享对话的请求DTO
 *
 * <AUTHOR>
 */
@Data
public class ShareBatchGetDTO extends BaseChannelDTO {

    /**
     * 会话ID（与applicationType必须填一个，且不能同时填两个）
     */
    private String sessionId;

    /**
     * 应用类型，英文简称。枚举参考ApplicationType
     */
    private String applicationType;

    /**
     * 对话ID清单
     */
    private List<String> dialogueIdList;
}
