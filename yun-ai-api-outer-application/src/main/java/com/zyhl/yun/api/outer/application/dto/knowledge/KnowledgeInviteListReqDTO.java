package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * Knowledge Invite List Req DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-04-17 00:16:07
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
public class KnowledgeInviteListReqDTO extends BaseChannelDTO implements Serializable {
    /**
     * 知识库
     */
    @NotBlank(message = "知识库Id不能为空")
    @Pattern(regexp = "^\\d+$", message = "非法知识库Id")
    private String baseId;
    /**
     * 分页信息
     */
    private PageInfoDTO pageInfo;
}
