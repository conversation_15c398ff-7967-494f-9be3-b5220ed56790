package com.zyhl.yun.api.outer.application.service.chat;

import com.zyhl.hcy.yun.ai.common.model.api.client.blian.vo.TextModelFileVO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.LastDialogueInfoDTO;
import com.zyhl.yun.api.outer.application.dto.MailAttachmentDTO;

import java.util.List;

/**
 * 会话输入校验类
 *
 * <AUTHOR>
 * @data 2024/6/3 15:52
 */
public interface AlgorithmChatAddCheckService {

    /**
     * 获取上次对话信息
     *
     * @param dialogueId 对话id
     * @param userId     用户id
     * @return 上次对话信息DTO
     */
    LastDialogueInfoDTO getLastDialogueInfo(String dialogueId, String userId);

    /**
     * 根据关键字获取对话关键字提示词
     *
     * @param prompt the prompt
     * @return {@link String}
     * <AUTHOR>
     * @date 2024-6-27 17:24
     */
    String getDialoguePrompt(String prompt,String channel);

    /**
     * 根据云盘文件id列表获取文件共享存储路径列表
     *
     * @param fileIdList     云盘文件id列表
     * @param userId         用户id
     * @param fileListFilter 文件列表是否过滤 true是 false否
     * @return 上传文件列表vo
     */
    List<String> getFilesByCloudDiskDocumentLocalPath(List<String> fileIdList, String userId, boolean fileListFilter);

    /**
     * 根据云盘文件id列表获取上传文件列表
     *
     * @param fileIdList     云盘文件id列表
     * @param userId         用户id
     * @param fileListFilter 文件列表是否过滤 true是 false否
     * @return 上传文件列表vo
     */
    List<TextModelFileVO> getFilesByCloudDiskDocument(List<String> fileIdList, String userId, boolean fileListFilter);
   
    /**
     * 根据邮件附件列表获取文件共享存储路径列表
     *
     * @param attachmentDto 邮箱附件列表dto
     * @param dto           会话输入dto
     * @return 上传文件列表vo
     */
    List<String> getFilesByEmailAttachmentLocalPath(MailAttachmentDTO attachmentDto, AlgorithmChatAddDTO dto);

    /**
     * 根据邮件附件列表获取上传文件列表
     *
     * @param attachmentDto 邮箱附件列表dto
     * @param dto           会话输入dto
     * @return 上传文件列表vo
     */
    List<TextModelFileVO> getFilesByEmailAttachment(MailAttachmentDTO attachmentDto, AlgorithmChatAddDTO dto);


}
