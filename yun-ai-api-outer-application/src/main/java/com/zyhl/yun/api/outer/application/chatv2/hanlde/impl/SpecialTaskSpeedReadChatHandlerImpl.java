package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueAiToolSettingDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.event.completeimpl.NextTaskChatCallbackEvent;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.pojo.TaskPromptOfChatInfo;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatCommonService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueFlowResultVO;
import com.zyhl.yun.api.outer.application.dto.ReadTaskDTO;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.application.service.TaskApplicationTypeService;
import com.zyhl.yun.api.outer.config.textmodel.TaskChatPromptConfig;
import com.zyhl.yun.api.outer.domainservice.QpsLimitService;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddFlowStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.SseNameEnum;
import com.zyhl.yun.api.outer.enums.chat.TalkTypeEnum;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 文本大模型任务对话【applicationType=speedread && dialogueType=2】
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SpecialTaskSpeedReadChatHandlerImpl extends AbstractChatAddV2Handler {

	/**
	 * 当前执行顺序
	 */
	private final ExecuteSort thisExecuteSort = ExecuteSort.SPECIAL_TASK_SPEED_READ_CHAT;

	@Resource
	private TaskChatPromptConfig taskChatPromptConfig;
	@Resource
	private TaskApplicationTypeService taskApplicationTypeService;
	@Resource
	private QpsLimitService qpslimitService;
	@Resource
	private ChatCommonService chatCommonService;
	@Resource
	private DataSaveService dataSaveService;
	@Resource
	private TextModelExternalService textModelExternalService;
	@Resource
	private AlgorithmChatContentRepository algorithmChatContentRepository;

	@Override
	public void afterPropertiesSet() throws Exception {
		// 支持的业务初始化
		List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        this.setBusinessTypes(thisBusinessTypes);
	}

	@Override
	public int order() {
		return thisExecuteSort.getSort();
	}

	@Override
	public boolean execute(ChatAddHandleDTO handleDTO) {
		return ApplicationTypeEnum.isSpeedRead(handleDTO.getReqDTO().getApplicationType())
				&& TalkTypeEnum.isTask(handleDTO.getInputInfoDTO().getDialogueType());
	}

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		log.info("进入{}", thisExecuteSort.getDesc());

		// 1 设置文本意图
		handleDTO.setTextGenerateTextIntention();

		// 2 获取sessionId(任务id)
		String sessionId = handleDTO.getReqDTO().getSessionId();
		if (Objects.isNull(sessionId)) {
			log.error("任务id[sessionId]为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		if (null == handleDTO.getInputInfoDTO().getToolSetting()) {
			log.error("ToolSetting为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		DialogueAiToolSettingDTO setting = handleDTO.getInputInfoDTO().getToolSetting().getAiToolSetting();
		if (null == setting) {
			log.error("DialogueAiToolSettingDTO 为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}
		Map<String, List<TaskChatPromptConfig.SubPromptListItem>> promptMap = taskChatPromptConfig.getPromptMap();
		String module = setting.getModule();
		if (null == promptMap || !promptMap.containsKey(module)) {
			log.error("promptMap 不包含 module:{}", module);
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		List<TaskChatPromptConfig.SubPromptListItem> subPromptList = promptMap.get(setting.getModule());
		if (CollUtil.isEmpty(subPromptList)) {
			log.error("promptMap module:{} 不包含子提示词列表", module);
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		// 3. 获取hbase全文
		String fullText = taskApplicationTypeService
				.getSpeedReadFullText(new ReadTaskDTO(RequestContextHolder.getUserId(), sessionId));

		// 4、处理
		String modelCode = taskChatPromptConfig.getModelCode();
		long maxLength = taskChatPromptConfig.getModelMaxLength();

		List<TaskPromptOfChatInfo> taskPromptOfChatInfos = new ArrayList<>();
		subPromptList.forEach(item -> {
			String prompt = item.getPromptCode();
			String realPrompt = chatCommonService.getPromptByConfigAndDB(prompt, modelCode, handleDTO.getReqDTO().getSourceChannel());
			taskPromptOfChatInfos.add(TaskPromptOfChatInfo.builder().modelMaxLength(maxLength)
					.module(item.getTaskModule()).prompt(realPrompt).build());
		});
		handleDTO.setTaskPromptOfChatInfos(taskPromptOfChatInfos);

		// 获取第一条数据
		TaskPromptOfChatInfo taskPromptOfChatInfo = taskPromptOfChatInfos.get(0);
		handleDTO.setCurrentTaskModule(taskPromptOfChatInfo.getModule());
		fullText = taskPromptOfChatInfo.getFullTextAndPrompt(fullText);

		// 监听器
		SseEventListener event = new SseEventListener(handleDTO, null);
		event.getSseEmitterOperate().setSseName(SseNameEnum.TASK_SSE.getCode());
		// 设置模型编码
		event.setModelCode(modelCode);
		NextTaskChatCallbackEvent nextTaskChatCallbackEvent = SpringUtil.getBean(NextTaskChatCallbackEvent.class);
		event.setCompleteCallbackEvent(nextTaskChatCallbackEvent);
		event.setHandleDTO(handleDTO);

		// 将截取后的文本添加到消息列表中
		TextModelTextReqDTO reqDTO = getTextModelReqDto(fullText, event);

		// 确保禁用联网搜索
		reqDTO.setEnableForceNetworkSearch(false);
		
		// 保存到hbase
		dataSaveService.saveTextResult(handleDTO, "", "");

		// 保存数据库（message不存在，会设置新增）
		dataSaveService.add(handleDTO, ChatStatusEnum.CHAT_IN);

		// 调用模型服务
		textModelExternalService.streamDialogue(modelCode, reqDTO, event);

		// 更新模型编码
		algorithmChatContentRepository.updateModelCode(event.getDialogId(), modelCode);

		return false;
	}

	/**
	 * 执行任务对话方法
	 *
	 * @param handleDTO the handle dto
	 * @param event     the event
	 */
	@Override
	public void runHandlerContinue(ChatAddHandleDTO handleDTO, SseEventListener event) {
		TaskPromptOfChatInfo currTaskPromptOfChatInfo = null;
		List<TaskPromptOfChatInfo> taskPromptOfChatInfos = handleDTO.getTaskPromptOfChatInfos();
		for (TaskPromptOfChatInfo taskPromptOfChatInfo : taskPromptOfChatInfos) {
			if (taskPromptOfChatInfo.isComplete()) {
				continue;
			}
			currTaskPromptOfChatInfo = taskPromptOfChatInfo;
			break;
		}
		// currTaskPromptOfChatInfo空，停止了
		if (null == currTaskPromptOfChatInfo) {
			log.info("停止对话，即将更新hbase taskPromptOfChatInfos modules:{}", JSONUtil.toJsonStr(
					taskPromptOfChatInfos.stream().map(TaskPromptOfChatInfo::getModule).collect(Collectors.toList())));
			// 更新hbase
			taskApplicationTypeService.updateSpeedReadHbaseResult(event.getUserId(), String.valueOf(handleDTO.getSessionId()),
					taskPromptOfChatInfos);
			// 将截取后的文本添加到消息列表中
			// 提交完成
			handleDTO.getRespVO().setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
			handleDTO.getRespVO()
					.setFlowResult(new DialogueFlowResultVO(ChatAddFlowStatusEnum.STOP, event.getSendIndex()));
			event.getSseEmitterOperate().sendAndComplete(BaseResult.success(handleDTO.getRespVO()));
			return;
		}
		// 索引回退一个版本
		event.setSendIndex(event.getSendIndex() - 1);
		// 设置新的module
		handleDTO.setCurrentTaskModule(currTaskPromptOfChatInfo.getModule());
		// 获取hbase全文
		String fullText = taskApplicationTypeService
				.getSpeedReadFullText(new ReadTaskDTO(event.getUserId(), String.valueOf(handleDTO.getSessionId())));
		fullText = currTaskPromptOfChatInfo.getFullTextAndPrompt(fullText);

		// 将截取后的文本添加到消息列表中
		TextModelTextReqDTO reqDTO = getTextModelReqDto(fullText, event);

		// 重新设置handleDTO
		event.setHandleDTO(handleDTO);
		
		// 继续调用模型服务
		textModelExternalService.streamDialogue(event.getModelCode(), reqDTO, event);
	}

	/**
	 * 获取请求dto
	 * 
	 * @param fullText
	 * @param event
	 * @return
	 */
	private TextModelTextReqDTO getTextModelReqDto(String fullText, SseEventListener event) {
		TextModelTextReqDTO reqDTO = event.getTextDto().toSimpleTextReqDTO();
		TextModelMessageDTO messageDTO = new TextModelMessageDTO();
		messageDTO.setRole(TextModelRoleEnum.USER.getName());
		messageDTO.setContent(fullText);
		// 设置所有会话
		reqDTO.setMessageDtoList(Collections.singletonList(messageDTO));
		// 确保禁用联网搜索
		reqDTO.setEnableForceNetworkSearch(false);
		return reqDTO;
	}
}
