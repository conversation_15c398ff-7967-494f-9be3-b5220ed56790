package com.zyhl.yun.api.outer.application.service.knowledge.upload.strategy;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.dashscope.utils.JsonUtils;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileAddReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileBatchImportReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.upload.AbstractUserKnowledgeFileResourceHandle;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeAddResultVO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeTaskResultVO;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileTaskResultEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述：html资源文件处理
 *
 * <AUTHOR> zhumaoxian  2025/3/21 17:19
 */
@Slf4j
@Component
public class UserKnowledgeFileResourceHandleHtmlImpl extends AbstractUserKnowledgeFileResourceHandle {
    @Override
    public void afterPropertiesSet() throws Exception {
        register(KnowledgeResourceTypeEnum.HTML.getCode(), this);
    }

    @Override
    public KnowledgeTaskResultVO addV2(KnowledgeFileAddReqDTO dto) {

        // 已入库的邮件/笔记
        Map<String, UserKnowledgeFileEntity> existMap = new HashMap<>(Const.NUM_32);
        List<String> urlList = dto.getHtmlList().stream().map(UserKnowledgeFileEntity.HtmlInfo::getUrl).collect(Collectors.toList());
        userKnowledgeFileRepository.selectHtmlResource(dto.getUserId(), urlList)
                .stream().filter(item -> Objects.nonNull(item.getHtmlInfo()))
                .forEach(item -> existMap.put(item.getHtmlInfo().getUrl(), item));

        // 循环校验文件
        List<KnowledgeAddResultVO> resultList = new ArrayList<>();
        List<String> addUrlList = new ArrayList<>();
        List<UserKnowledgeFileEntity> addList = new ArrayList<>();
        for (UserKnowledgeFileEntity.HtmlInfo html : dto.getHtmlList()) {
            try {
                HttpURLConnection connection = (HttpURLConnection) new URL(html.getUrl()).openConnection();
                connection.setRequestMethod(ServletUtil.METHOD_HEAD);
                if (connection.getResponseCode() != HttpURLConnection.HTTP_OK) {
                    log.info("无效网址，url：{}", html.getUrl());
                    resultList.add(new KnowledgeAddResultVO(html.getUrl(), ResultCodeEnum.ERROR_NOT_FOUND));
                    continue;
                }
            } catch (Exception e) {
                log.error("无效网址，url：{}，异常信息：{}", html.getUrl(), e.getMessage());
                resultList.add(new KnowledgeAddResultVO(html.getUrl(), ResultCodeEnum.ERROR_NOT_FOUND));
                continue;
            }

            // db校验
            UserKnowledgeFileEntity entity = existMap.get(html.getUrl());
            if (entity != null) {
                log.info("网页资源已上传，url：{}，ai_status:{}", html.getUrl(), entity.getAiStatus());
                resultList.add(new KnowledgeAddResultVO(html.getUrl(), ResultCodeEnum.FILE_REPEAT_UPLOAD));
                continue;
            }

            // 校验通过
            resultList.add(new KnowledgeAddResultVO(html.getUrl(), ResultCodeEnum.SUCCESS.getResultCode(), FILE_EXIST_OTHER_LABEL));

            addUrlList.add(html.getUrl());
            addList.add(new UserKnowledgeFileEntity(dto.getUserId(), html.getTitle(), html.getUrl(), JsonUtils.toJson(html)));
        }

        // 创建转存任务
        createTransTask(dto, resultList, addUrlList, null);

        if (ObjectUtil.isNotEmpty(addList)) {
            // 批量入库
            userKnowledgeFileRepository.batchAdd(addList);

            // 发送mq
            knowledgeDispatchTaskMqService.sendTaskMq(addList);
        }

        // 返回结果
        KnowledgeTaskResultVO resultVO = new KnowledgeTaskResultVO();
        resultVO.setResultList(resultList);
        if (ObjectUtil.isEmpty(addList)) {
            resultVO.setStatus(FileTaskResultEnum.FAIL.getStatus());
        } else {
            resultVO.setStatus(FileTaskResultEnum.PROCESSING.getStatus());
        }
        return resultVO;
    }
}
