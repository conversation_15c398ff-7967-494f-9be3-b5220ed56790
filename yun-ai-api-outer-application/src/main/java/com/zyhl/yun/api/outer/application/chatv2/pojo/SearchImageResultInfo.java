package com.zyhl.yun.api.outer.application.chatv2.pojo;

import com.zyhl.yun.api.outer.domain.vo.chat.search.SearchInfo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 描述：搜索图片结果信息
 *
 * <AUTHOR> liu<PERSON><PERSON><PERSON>
 * @date 2025-07-02
 */
@Slf4j
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchImageResultInfo {
	/**
	 * 搜索云盘图片信息
	 */
	SearchInfo searchInfo = null;

	/**
	 * 搜索云盘图片总数量
	 */
	Long searchInfoTotalSize = 0L;

	/**
	 * 搜索云盘图片信息 只有文件id
	 */
	SearchInfo searchInfoOnlyFileId = null;
}
