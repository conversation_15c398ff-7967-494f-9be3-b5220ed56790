package com.zyhl.yun.api.outer.application.chatv2.service;

import com.zyhl.yun.api.outer.application.dto.YunDiskReqDTO;
import com.zyhl.yun.api.outer.domain.valueobject.File;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @Classname YunDiskV2Service
 * @Description 云盘平台接口
 * @Date 2025/4/18 15:01
 */
public interface YunDiskV2Service {

	/**
	 * 获取文件内容
	 * 
	 * @param dto（fileId 文件id）
	 * @return
	 */
	File getYunDiskContent(YunDiskReqDTO dto);

	/**
	 * 获取文件内容（缩略图类型Big）
	 *
	 * @param dto（fileId 文件id）
	 * @return 文件对象
	 */
	File getYunDiskContentAndBigThumbnailUrl(YunDiskReqDTO dto);

	/**
	 * 【并行】批量获取文件内容（缩略图类型Big）
	 * @Author: WeiJingKun
	 *
	 * @param fileIdList 文件id列表
	 * @return 文件对象map { fileId : 文件对象 }
	 */
	Map<String, File> batchGetYunDiskContentAndBigThumbnailUrl(List<String> fileIdList, ExecutorService threadPool);

}
