package com.zyhl.yun.api.outer.application.dto;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.application.dto.ShortLinkDTO} <br>
 * <b> description:</b>
 * 获取短链DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-06-20 17:43
 **/
@Data
@Builder
public class ShortLinkDTO implements Serializable {
    private static final long serialVersionUID = -6511038567520770739L;

    /**
     * 长链接 必填
     */
    private String longURL;

    /**
     * 配置链接 选填
     */
    private String configURL;

    /**
     * 配置链接过期时间（单位：天),配合configUrl使用 选填
     */
    private Integer expireTime;
}
