package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatDialogueSearchService;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.VersionUtil;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.application.chatv2.hanlde.impl.SearchForCloudPhoneHandlerImpl}
 * <br>
 * <b> description:</b>
 * 云手机搜索相关业务
 *
 * <AUTHOR>
 * @date 2025-07-14 11:47
 **/
@Slf4j
@Component
public class SearchForCloudPhoneHandlerImpl extends AbstractChatAddV2Handler {

    /**
     * 当前执行顺序
     */
    private ExecuteSort thisExecuteSort = ExecuteSort.SEARCH_FOR_CLOUD_PHONE;

    @Resource
    private ChatDialogueSearchService chatDialogueSearchService;

    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        //支持的业务初始化
        List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.CLOUD_PHONE);
        this.setBusinessTypes(thisBusinessTypes);
    }

    @Override
    public boolean execute(ChatAddHandleDTO handleDTO) {
        boolean isCloudPhone = AssistantEnum.isCloudPhone(RequestContextHolder.getAssistantEnum());
        if (Boolean.FALSE.equals((isCloudPhone))) {
            return Boolean.FALSE;
        }
        if (Boolean.FALSE.equals(VersionUtil.cloudPhoneClientVersionGte462())) {
            // 云手机【业务类型:{}】不支持的意图设置为文本意图，继续执行
            log.info("云手机【业务类型:{}】不支持的意图设置为文本意图，继续执行", RequestContextHolder.getBusinessType());
            handleDTO.setTextGenerateTextIntention();
            return Boolean.FALSE;
        }
        String currentIntentionCode = handleDTO.getIntentionCode();
        boolean isSearchIntention = DialogueIntentionEnum.isSearchTypeIntentionForAll(currentIntentionCode);
        String businessType = RequestContextHolder.getBusinessType();
        boolean isChat = ApplicationTypeEnum.isChat(handleDTO.getReqDTO().getApplicationType());
        boolean isCloudPhoneSearchIntention = isSearchIntention
                && DialogueIntentionEnum.isCloudPhoneSearchIntention(businessType, currentIntentionCode);
        if (isSearchIntention && Boolean.FALSE.equals(isCloudPhoneSearchIntention)) {
            //是搜索意图，但不是云手机支持的搜索，转换为文生文
            handleDTO.setTextGenerateTextIntention();
            return Boolean.FALSE;
        }
        return isChat && isCloudPhoneSearchIntention;
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        log.info("进入{}", thisExecuteSort.getDesc());

        // 执行搜索
        if (chatDialogueSearchService.cloudPhoneSearchIntentionHandle(handleDTO)) {

            // 返回异步结果
            handleDTO.getRespVO().getFlowResult().setResultType(FlowResultTypeEnum.SEARCH.getType());

            // 流式输出
            handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(handleDTO.getRespVO()));

        }
        return Boolean.FALSE;
    }

}
