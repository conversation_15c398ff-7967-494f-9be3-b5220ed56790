package com.zyhl.yun.api.outer.application.vo.knowledge;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/18 11:53
 */
@Data
public class PersonalKnowledgeNoteExpands implements Serializable {

    /**
     * 笔记拓展信息主键ID
     */
    private Long expandId;
    /**
     * 笔记类型，0或空代表普通笔记，4代表录音笔记，1,2,3是其他笔记
     */
    private Integer noteType;
    private String expand1;
    private String expand2;
    private String expand3;
    private String expand4;
    private Integer showSpeaker;
    /**
     * noteType:3顶部导航栏说话人，-1关闭 0缺省 1开启
     */
    private Integer magicShowSpeaker;
    /**
     * noteType:3显示译文开关，-1关闭 0缺省 1开启
     */
    private Integer showTranslation;
    /**
     * noteType:3语言选项，-1关闭 0未设置 1中文 2英文 3日语 4粤语 5中英混合
     */
    private Integer language;
    /**
     * noteType:3 翻译类型，-1关闭 0未设置 1中文 2英文 3日语
     */
    private Integer translationLanguage;
    /**
     * 精转类别，0:实时录音转文字，1:云端音频转文字，2:本地音频转文字
     */
    private Integer conversionType;
    /**
     * noteType:3 说话人模式，-1不区分 0未设置 1多人讨论 2单人记录 3双人对话
     */
    private Integer magicSpeakerMode;

}
