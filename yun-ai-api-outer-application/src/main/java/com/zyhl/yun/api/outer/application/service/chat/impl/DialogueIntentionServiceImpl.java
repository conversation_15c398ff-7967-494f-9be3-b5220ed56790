package com.zyhl.yun.api.outer.application.service.chat.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.enums.NumberEnum;
import com.zyhl.hcy.yun.ai.common.base.utils.HbaseAiTextResultUtil;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.enums.UserBelongsPlatformEnum;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatDialogueSearchService;
import com.zyhl.yun.api.outer.application.config.ChatTextToolOpenConfig;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.service.AlgorithmAiRegisterService;
import com.zyhl.yun.api.outer.application.service.chat.DialogueIntentionService;
import com.zyhl.yun.api.outer.application.service.external.AsyncSearchService;
import com.zyhl.yun.api.outer.application.strategy.intention.AbstractIntentionHandle;
import com.zyhl.yun.api.outer.application.util.IntentionConvertUtils;
import com.zyhl.yun.api.outer.config.*;
import com.zyhl.yun.api.outer.domain.dto.redis.HistoryDialogInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.domain.entity.DialogueIntentionEntity;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.enums.chat.search.SearchDiscoveryParamQueryTypeEnum;
import com.zyhl.yun.api.outer.external.CenterTaskExternalService;
import com.zyhl.yun.api.outer.external.DialogueIntentionExternalService;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.VersionUtil;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;
import com.zyhl.yun.api.outer.vo.IntentEntityVO;
import com.zyhl.yun.api.outer.vo.KeyValueVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import retrofit2.http.HEAD;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 意图识别相关接口实现类
 *
 * <AUTHOR>
 * @date 2024/4/9 17:04
 */
@Service
@Slf4j
public class DialogueIntentionServiceImpl implements DialogueIntentionService {

	@Resource
	private UidGenerator uidGenerator;
	@Resource
	private DialogueIntentionExternalService dialogueIntentionExternalService;
	@Resource
	private AlgorithmAiRegisterService aiRegisterService;
	@Resource
	private MultipleIntentionMergeProperties intentionMergeProperties;
	@Resource
	private HistoryDialogInfoRedisServiceImpl historyRedisService;
	@Resource
	private IntentionContextProperties contextConfig;
	@Resource
	private CenterTaskExternalService centerTaskExternalService;
	@Resource
	private AiTextResultRepository aiTextResultRepository;
	@Resource
	protected SourceChannelsProperties sourceChannelsProperties;

	@Resource
	private SearchIntentionProperties searchIntentionProperties;

	@Resource
	private ChatDialogueSearchService chatDialogueSearchService;

	@Resource
	private ChatTextToolOpenConfig chatTextToolOpenConfig;

	@Resource
	private AsyncSearchService asyncSearchService;

	@Resource
	private IntentionConvertUtils intentionConvertUtils;

	@Resource
	private SearchParamProperties searchParamProperties;

	@Resource
	private MailaiSearchProperties mailaiSearchProperties;

	/**
	 * 获取意图编码
	 *
	 * @param vo 对话意图响应VO
	 * @return 意图编码
	 */
	@Override
	public String getIntentionCode(DialogueIntentionVO vo) {
		return vo.getIntentionInfoList().get(0).getIntention();
	}

	@Override
	public DialogueIntentionVO getDialogueIntentionVO(String channel, String sessionId, String dialogueId,
													  String userId, DialogueIntentionEntity.DialogueInfo currentDialogue,
													  Boolean enableAiSearch) {
		try {
			DialogueIntentionEntity entity = new DialogueIntentionEntity();
			String requestId = String.valueOf(uidGenerator.getUID());
			entity.setRequestId(requestId);
			if (CharSequenceUtil.isNotEmpty(sessionId)) {
				entity.setSessionId(sessionId);
			} else {
				entity.setSessionId(requestId);
			}

			entity.setDialogueId(dialogueId);
			entity.setUserId(userId);
			// 【小天渠道】开启AI搜索，拼接的搜索意图前缀
			if(sourceChannelsProperties.isXiaoTian(channel) && Boolean.TRUE.equals(enableAiSearch)){
				currentDialogue.setDialogue(searchIntentionProperties.getSearchPrefix() + currentDialogue.getDialogue());
			}
			//用户原始输入
			String originalDialogue = currentDialogue.getDialogue();
			if (sourceChannelsProperties.isMail(channel) && Boolean.TRUE.equals(enableAiSearch)){
				List<String> list = mailaiSearchProperties.getSearchIntentionPrefix();
				if (CollUtil.isNotEmpty(list) && !list.stream().anyMatch(originalDialogue::startsWith)) {
					currentDialogue.setDialogue(mailaiSearchProperties.getSplicingPrefix() + originalDialogue);
				}

			}
			entity.setCurrentDialogue(currentDialogue);
			if (!UserBelongsPlatformEnum.OSE.getBelongsPlatform().equals(RequestContextHolder.getBelongsPlatform())
					&& aiRegisterService.checkAlbum(userId)) {
				entity.setSkipStatus(1);
			}

			// 处理历史意图上下文
			processHistoryIntentionContext(sessionId, entity);

			log.info("【意图识别】开始调用意图识别接口，请求参数：{}", JsonUtil.toJson(entity));
			DialogueIntentionVO intentionVO = dialogueIntentionExternalService.dialogueIntentionFuc(channel, entity);
			log.info("【意图识别】调用意图识别接口结束，响应结果：{}", JsonUtil.toJson(intentionVO));

			// 渠道判断
			String assistantCode = sourceChannelsProperties.getCode(channel);
			AssistantEnum assistantEnum = AssistantEnum.getByCode(assistantCode);
			AbstractIntentionHandle handle = AbstractIntentionHandle.getHandle(assistantEnum);
			if (handle != null) {
				return handle.intentionHandle(intentionVO);
			}

			String intentionCode = getIntentionCode(intentionVO);
			// 算法实际意图编码（主意图）
			intentionVO.setAlgorithmIntentionCode(intentionCode);
			if (!DialogueIntentionEnum.isExistOnAide(intentionCode)) {
				// 不在IA助手处理范围内的意图都转为文生文
				log.info("【意图识别】识别结果：{}，不在AI助手处理范围内，转换为文生文", intentionCode);
				intentionVO.getIntentionInfoList().get(0)
						.setIntention(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());
			}

            // 意图过滤
            intentionFilter(intentionVO, channel, RequestContextHolder.getPhoneNumber());

            // 意图实体过滤
            intentionEntityFilter(intentionVO, channel);

			// 云邮渠道，不处理多搜索意图合并
			if (SourceChannelsProperties.isMailChannel(channel)) {
//				boolean isTransferIntention = mailaiSearchProperties.isTransferIntention();
//				if (isTransferIntention) {
//					//云邮意图转换
//					String businessType = RequestContextHolder.getBusinessType();
//					String userInput = originalDialogue;
//					intentionConvertUtils.convertIntention(intentionVO, channel, businessType, enableAiSearch, userInput);
//				}
				return intentionVO;
			}

			// 搜索知识库资源，不处理多搜索意图合并
			if(DialogueIntentionEnum.SEARCH_KNOWLEDGE_BASE_RESOURCE.getCode().equals(getIntentionCode(intentionVO))){
				return intentionVO;
			}

//			// 处理多搜索意图合并（根据配置决定是否合并）
//			return handleMultipleSearchIntention(intentionVO, intentionCode);
			/**
			 * 2025.04.28，不再进行搜索意图合并
			 * 原型 - AI助手综合搜索优化
			 * https://codesign.qq.com/s/545488952540121
			 * 密码: A67T
			 */
			// 搜索意图转综合搜索（有开关配置）
			// return convertComprehensiveSearchIntention(intentionVO, getIntentionCode(intentionVO), enableAiSearch);

			/**
			 * 2025.05.21 new搜索意图转综合搜索
			 * 999、018或多个搜索意图，需要转综合搜索-018
			 */
			 return newConvertComprehensiveSearchIntention(intentionVO);

		} catch (Exception e) {
			log.error("【意图识别】调用意图识别接口异常，直接强制将意图识别为000文生文意图，异常信息：{} error:", e.getMessage(), e);
			// 意图识别抛出异常时，直接强制识别为000文生文意图
			DialogueIntentionVO dialogueIntentionVO = new DialogueIntentionVO();
			DialogueIntentionVO.IntentionInfo info = new DialogueIntentionVO.IntentionInfo();
			info.setIntention(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());
			dialogueIntentionVO.setIntentionInfoList(Collections.singletonList(info));

			return dialogueIntentionVO;
		}
	}

	/**
	 * 意图过滤
	 *
	 * @param intentionVO 意图VO
	 * @param sourceChannel 渠道来源
	 */
	private void intentionFilter(DialogueIntentionVO intentionVO, String sourceChannel, String phone) {
		List<IntentionInfo> realIntentionInfoList = new ArrayList<>();

		intentionVO.getIntentionInfoList().forEach(intentionInfo -> {
			String intentionCode = intentionInfo.getIntention();
			String subIntentionCode = intentionInfo.getSubIntention();

			if (DialogueIntentionEnum.isTextToolIntention(intentionCode)
					&& DialogueIntentionSubEnum.isAiPpt(subIntentionCode)
					&& !chatTextToolOpenConfig.getAiPptOpenByChannel(sourceChannel)) {
				log.info("isAiPptOpen=false，不执行aippt意图");
				return;
			}

			if (DialogueIntentionEnum.isTextToolIntention(intentionCode)
					&& DialogueIntentionSubEnum.isMemoryAlbum(subIntentionCode)
					&& !chatTextToolOpenConfig.getAiMemoryAlbumOpenByPhone(phone)) {
				log.info("isAiMemoryAlbumOpen=false，不执行ai生成回忆相册意图");
				return;
			}
			
			if (DialogueIntentionEnum.isTextToolIntention(intentionCode)
					&& DialogueIntentionSubEnum.isAiSpeedRead(subIntentionCode) && !VersionUtil.xtH5VersionGte210()) {
				log.info("图书快速阅读意图 h5Version < 2.1.0版本，不返回意图");
				return;
			}


			// 版本过滤
			// h5Version < 小天1.4.0版本，剔除：圈子搜索、创建语音笔记意图、邮件搜索
			if (VersionUtil.xtH5VersionLt140()) {
				if (DialogueIntentionEnum.SEARCH_GROUP.getCode().equals(intentionCode)) {
					log.info("h5Version < 小天1.4.0版本，不进行圈子搜索");
					return;
				}
				if (DialogueIntentionEnum.CREATE_VOICE_NOTE.getCode().equals(intentionCode)) {
					log.info("h5Version < 小天1.4.0版本，不创建语音笔记");
					return;
				}
				if (DialogueIntentionEnum.SEARCH_MAIL.getCode().equals(intentionCode)) {
					log.info("h5Version < 小天1.4.0版本，不进行邮件搜索");
					return;
				}
			}
			if (VersionUtil.xtH5VersionLt151(null)) {
				if (DialogueIntentionEnum.AI_EXPANSION_MAP.getCode().equals(intentionCode)) {
					log.info("h5Version < 小天1.5.1版本，不支持AI扩图");
					return;
				}
				if (DialogueIntentionEnum.ONE_CLICK_PUZZLE.getCode().equals(intentionCode)) {
					log.info("h5Version < 小天1.5.1版本，不支持朋友圈9图");
					return;
				}
			}
			// 小天1.7.0版本过滤
			if (VersionUtil.xtH5VersionLt170()) {
				if (DialogueIntentionEnum.SMART_FAKE_CHECK.getCode().equals(intentionCode)) {
					log.info("h5Version < 小天1.7.0版本，不支持图片智能鉴伪");
					return;
				}
				if (DialogueIntentionEnum.BABY_TIME_MACHINE.getCode().equals(intentionCode)) {
					log.info("h5Version < 小天1.7.0版本，不支持宝宝时光机");
					return;
				}
				if (DialogueIntentionEnum.APPEARANCE_PREDICTION.getCode().equals(intentionCode)) {
					log.info("h5Version < 小天1.7.0版本，不支持宝宝长相预测");
					return;
				}
				if (DialogueIntentionEnum.AI_EMOTICON.getCode().equals(intentionCode)) {
					log.info("h5Version < 小天1.7.0版本，不支持AI表情包");
					return;
				}
			}

			// 小天1.8.0版本过滤
			if (VersionUtil.xtH5VersionLt180()) {
				if (DialogueIntentionEnum.KNOWLEDGE_ENTRANCE.getCode().equals(intentionCode)) {
					log.info("h5Version < 小天1.8.0版本，不支持AI知识库推荐意图");
					return;
				}
			}

			// 小天2.0.0版本过滤
			if (VersionUtil.xtH5VersionLt200()) {
				if (DialogueIntentionEnum.TEXT_TOOL.getCode().equals(intentionCode)) {
					log.info("h5Version < 小天2.0.0版本，不支持文本工具推荐意图");
					return;
				}
				if (DialogueIntentionEnum.AI_PHOTO_EDIT.getCode().equals(intentionCode)) {
					log.info("h5Version < 小天2.0.0版本，不支持AI改图意图");
					return;
				}
				if (DialogueIntentionEnum.SEARCH_KNOWLEDGE_BASE_RESOURCE.getCode().equals(intentionCode)) {
					log.info("h5Version < 小天2.0.0版本，不支持搜索知识库资源");
					return;
				}
			}

			// 小天2.0.2版本过滤
			if (VersionUtil.xtH5VersionLt202()) {
				if (DialogueIntentionEnum.TEXT_TOOL.getCode().equals(intentionCode)
						&& DialogueIntentionSubEnum.isMemoryAlbum(subIntentionCode)) {
					log.info("h5Version < 小天2.0.2版本，不支持生成回忆相册意图");
					return;
				}
			}

			// todo 由于智能鉴伪效果不好，暂时过滤图片智能鉴伪意图 20250112
			if (DialogueIntentionEnum.SMART_FAKE_CHECK.getCode().equals(intentionCode)) {
				log.info("智能鉴伪效果不好，暂时过滤，不支持图片智能鉴伪");
				return;
			}

			// 渠道过滤
			if (!sourceChannelFilter(intentionCode, sourceChannel)) {
				log.info("渠道来源：{}，不满足意图编码：{}的渠道要求", sourceChannel, intentionCode);
				return;
			}
			realIntentionInfoList.add(intentionInfo);
		});

		// 剩余的意图列表为空，则添加文生文意图
		if (CollUtil.isEmpty(realIntentionInfoList)) {
			DialogueIntentionVO.IntentionInfo info = new DialogueIntentionVO.IntentionInfo();
			info.setIntention(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());
			realIntentionInfoList.add(info);
		}
		// set处理后的意图列表
		intentionVO.setIntentionInfoList(realIntentionInfoList);
	}

	@Override
	public DialogueIntentionVO getIntentionVO(ChatAddInnerDTO params) {
		// 意图识别参数
		String userId = params.getReqParams().getUserId();
		String sessionId = params.getSessionId() == null ? StringUtils.EMPTY : String.valueOf(params.getSessionId());
		String dialogueId = String.valueOf(params.getDialogueId());
		DialogueIntentionEntity.DialogueInfo intentionParams = new DialogueIntentionEntity.DialogueInfo(
				params.getContent().getDialogue(), DateUtil.formatTime(params.getContent().getTimestamp()));

		return getDialogueIntentionVO(params.getContent().getSourceChannel(), sessionId, dialogueId, userId,
				intentionParams, params.getReqParams().getEnableAiSearch());
	}

	@Override
	public DialogueIntentionVO getIntentionVOV2(ChatAddHandleDTO handleDTO) {
		// 意图识别参数
		String userId = RequestContextHolder.getUserId();
		String sessionId = handleDTO.getSessionId() == null ? StringUtils.EMPTY : String.valueOf(handleDTO.getSessionId());
		String dialogueId = String.valueOf(handleDTO.getDialogueId());
		DialogueInputInfoDTO inputInfoDTO = handleDTO.getInputInfoDTO();

		// 解析 RFC 3339 格式的时间字符串为 Date 对象
		Date date = DateUtil.parse(inputInfoDTO.getInputTime());
		DialogueIntentionEntity.DialogueInfo intentionParams = new DialogueIntentionEntity.DialogueInfo(
				inputInfoDTO.getDialogue(), DateUtil.formatTime(date));

		return getDialogueIntentionVO(RequestContextHolder.getSourceChannel(), sessionId, dialogueId, userId,
				intentionParams, handleDTO.getReqDTO().getDialogueInput().isEnableAiSearch());
	}

	/**
	 * 处理多搜索意图合并（根据配置决定是否合并）
	 *
	 * 算法返回多搜索意图优化：
	 *
	 * 1，出现多个搜索意图，需要按多个搜索意图各自的实体去搜索，其他不存在的搜索意图不执行；
	 *
	 * 2，出现018主意图，并且还存在其他搜索意图，去掉018意图，再执行逻辑“1”；
	 *
	 * 3，出现018主意图，没其他搜索意图，执行全部搜索意图；
	 *
	 * 注意：每次增加搜索意图，都需要处理一下多搜索意图合并（DialogueIntentionEnum.isSearchSingleIntention增加其他搜索意图）
	 *
	 * @param intentionVO   对话接口内部数据传输对象
	 * @param intentionMain 主意图编码
	 */
	@Override
	public DialogueIntentionVO handleMultipleSearchIntention(DialogueIntentionVO intentionVO, String intentionMain) {
		// 开关未打开，直接返回
		if (!intentionMergeProperties.isOpen()) {
			log.warn("多搜索意图合并开关未打开，直接返回 intentionMain:{}", intentionMain);
			return intentionVO;
		}

		// 多意图参数获取
		List<DialogueIntentionVO.IntentionInfo> intentionInfoList = intentionVO.getIntentionInfoList();
		// 意图结果为空或者只存在一个意图，直接返回
		if (CollectionUtils.isEmpty(intentionInfoList) || intentionInfoList.size() == 1) {
			log.info("多搜索意图合并开关打开，是需要执行合并多搜索意图的场景，但是只有一个意图，直接返回 intentionMain:{}", intentionMain);
			return intentionVO;
		}
		log.info("多搜索意图合并开关打开，是需要执行合并多搜索意图的场景， intentionMain:{}", intentionMain);
		// 其他意图是否有搜索意图
		boolean otherIntentionHasSearch = hasSearch(intentionInfoList.subList(1, intentionInfoList.size()));
		if (DialogueIntentionEnum.isSearchSingleIntention(intentionMain)) {
			// 1，主意图是搜索，出现多个搜索意图，需要按多个搜索意图各自的实体去搜索，其他不存在的搜索意图不执行；
			return dealMultipleSearchIntention(intentionMain, intentionVO, otherIntentionHasSearch);
		} else if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode().equals(intentionMain)) {
			if (!otherIntentionHasSearch) {
				// 3，出现018主意图，没其他搜索意图，执行全部搜索意图；
				return intentionVO;
			}
			// 2，出现018主意图，并且还存在其他搜索意图，去掉018意图，再执行逻辑“1”；(去掉018主意图后还有2个及以上意图，才处理合并)
			intentionInfoList = intentionInfoList.subList(1, intentionInfoList.size());
			if (intentionInfoList.size() > 1) {
				intentionVO.setIntentionInfoList(intentionInfoList);
				// 去掉018主意图后还有2个及以上意图，才处理合并
				return dealMultipleSearchIntention(intentionInfoList.get(0).getIntention(), intentionVO,
						hasSearch(intentionInfoList.subList(1, intentionInfoList.size())));
			}
		} else {
			// 无需处理
			log.info("主意图为非搜索意图，无需处理合并 intentionMain:{}", intentionMain);
		}
		log.info("handleMultipleSearchIntention intentionMain:{}, result:{}", intentionMain,
				JSON.toJSONString(intentionVO));
		return intentionVO;
	}

	/**
	 * 处理合并
	 *
	 * @param intentionMain           主意图
	 * @param intentionVO             对话意图响应VO
	 * @param otherIntentionHasSearch 其他意图是否有搜索意图
	 * @return 对话意图响应VO
	 */
	private DialogueIntentionVO dealMultipleSearchIntention(String intentionMain, DialogueIntentionVO intentionVO,
			boolean otherIntentionHasSearch) {
		if (otherIntentionHasSearch) {
			// 新组装的多意图列表
			List<DialogueIntentionVO.IntentionInfo> newIntentionList = new ArrayList<>();
			// 非搜索意图列表
			List<DialogueIntentionVO.IntentionInfo> notSearchIntentionList = new ArrayList<>();
			List<IntentionInfo> newSubIntentionInfoList = new ArrayList<>();
			// 出现多个搜索意图，需要按多个搜索意图各自的实体去搜索，其他不存在的搜索意图不执行；
			for (IntentionInfo subIntentionInfo : intentionVO.getIntentionInfoList()) {
				if (DialogueIntentionEnum.isSearchSingleIntention(subIntentionInfo.getIntention())) {
					newSubIntentionInfoList.add(subIntentionInfo);
				} else {
					// 非搜索意图需要追加
					notSearchIntentionList.add(subIntentionInfo);
				}
			}
			// 合并综合搜索
			IntentionInfo comprehensiveSearchIntention = new IntentionInfo();
			comprehensiveSearchIntention.setIntention(DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode());
			comprehensiveSearchIntention.setEntityList(new ArrayList<>());
			comprehensiveSearchIntention.setSubIntentions(newSubIntentionInfoList);
			// 追加主意图
			newIntentionList.add(comprehensiveSearchIntention);
			if (!CollectionUtils.isEmpty(notSearchIntentionList)) {
				// 其他非搜索意图不为空则追加
				newIntentionList.addAll(notSearchIntentionList);
			}
			// 重新赋值多意图
			intentionVO.setIntentionInfoList(newIntentionList);
		} else {
			// 无需处理
			log.info("只有一个搜索意图，无需处理合并 intentionMain:{}", intentionMain);
		}
		return intentionVO;
	}

	/**
	 * 是否存在搜索
	 *
	 * @param intentionList 意图列表
	 * @return 是否存在搜索 true是 false否
	 */
	private boolean hasSearch(List<IntentionInfo> intentionList) {
		if (!CollectionUtils.isEmpty(intentionList)) {
			for (IntentionInfo intention : intentionList) {
				if (DialogueIntentionEnum.isSearchSingleIntention(intention.getIntention())) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 处理历史意图上下文
	 *
	 * @param sessionId 会话id
	 * @param entity    对话意图请求参数实体
	 */
	private void processHistoryIntentionContext(String sessionId, DialogueIntentionEntity entity) {
		try {
			// 判断是否启用意图识别上下文配置
			if (!contextConfig.isEnable()) {
				log.info("【意图识别】未启用意图识别上下文配置，不处理历史意图上下文");
				return;
			}

			// 会话id为空，不处理历史意图上下文
			if (StringUtils.isEmpty(sessionId)) {
				return;
			}

			// 获取并过滤历史对话信息列表
			List<HistoryDialogInfoDTO> historyList = getFilteredHistoryDialogues(sessionId, entity.getUserId());
			if (CollectionUtils.isEmpty(historyList)) {
				return;
			}

			// 获取HBase结果并构建映射
			Map<String, AiTextResultEntity> hbaseMap = getHbaseResults(historyList, entity.getUserId());
			if (hbaseMap == null) {
				return;
			}

			// 构建历史对话上下文
			List<DialogueIntentionEntity.DialogueInfo> historyDialogueList = buildDialogueList(historyList, hbaseMap);

			// 添加意图历史对话上下文
			if (!CollectionUtils.isEmpty(historyDialogueList)) {
				entity.setHistoryDialogueList(historyDialogueList);
			}
		} catch (Exception e) {
			log.error("【意图识别】处理历史意图上下文异常 sessionId:{} | error:", sessionId, e);
		}
	}

	/**
	 * 获取并过滤历史对话信息列表
	 *
	 * @param sessionId 会话id
	 * @param userId    用户ID
	 * @return 过滤后的历史对话信息列表
	 */
	private List<HistoryDialogInfoDTO> getFilteredHistoryDialogues(String sessionId, String userId) {
		// 获取redis历史对话信息列表
		List<HistoryDialogInfoDTO> historyList = historyRedisService.getRedisHistoryDialogInfoList
				(sessionId, contextConfig.getIntentionContextCount(), userId);
		if (CollectionUtils.isEmpty(historyList)) {
			return Collections.emptyList();
		}

		// 过滤意图上下文不需要传入的意图
		return historyList.stream()
				.filter(historyDialogue -> contextConfig.getIntentionContextCodeList().contains(historyDialogue.getIntentionCode()))
				.collect(Collectors.toList());
	}

	/**
	 * 获取HBase结果并构建映射
	 *
	 * @param historyList 历史对话信息列表
	 * @param userId      用户ID
	 * @return HBase结果映射
	 */
	private Map<String, AiTextResultEntity> getHbaseResults(List<HistoryDialogInfoDTO> historyList, String userId) {
		// 对话id列表
		List<String> dialogIdList = historyList.stream()
				.map(HistoryDialogInfoDTO::getDialogId)
				.collect(Collectors.toList());

		// 根据rowKey列表查询任务结果
		List<AiTextResultEntity> entityList = aiTextResultRepository.getByRowKeyList(userId, dialogIdList);
		if (CollectionUtils.isEmpty(entityList)) {
			log.info("获取历史意图上下文，获取HBase结果失败 dialogIdList:{}", JsonUtil.toJson(dialogIdList));
			return null;
		}

		// 将HBase结果映射为Map
		return mapHbaseResults(entityList);
	}

	/**
	 * 将HBase结果映射为Map
	 *
	 * @param entityList HBase结果列表
	 * @return HBase结果映射
	 */
	private Map<String, AiTextResultEntity> mapHbaseResults(List<AiTextResultEntity> entityList) {
		return entityList.stream()
				.collect(Collectors.toMap(
						entity -> entity.getRowKey().substring(entity.getRowKey().indexOf('_') + 1),
						Function.identity()));
	}

	/**
	 * 构建历史对话上下文
	 *
	 * @param historyList 历史对话信息列表
	 * @param hbaseMap    HBase结果映射
	 * @return 历史对话列表
	 */
	private List<DialogueIntentionEntity.DialogueInfo> buildDialogueList(List<HistoryDialogInfoDTO> historyList,
																		 Map<String, AiTextResultEntity> hbaseMap) {
		List<DialogueIntentionEntity.DialogueInfo> historyDialogueList = new ArrayList<>();

		// 上下文内容
		StringBuilder content = new StringBuilder();

		// 倒序遍历，从后往前判断是否超过字数限制
		for (int i = historyList.size() - 1; i >= 0; i--) {
			HistoryDialogInfoDTO historyDTO = historyList.get(i);
			if (content.length() > contextConfig.getIntentionContextTokens()) {
				log.info("获取历史意图上下文，历史对话内容超过字数限制，本次对话不传入上下文 historyDTO:{}", JsonUtil.toJson(historyDTO));
				break;
			}

			if (!hbaseMap.containsKey(historyDTO.getDialogId())) {
				log.info("获取历史意图上下文，存在历史对话，但是HBase没有数据 rowKey:{}", historyDTO.getDialogId());
				continue;
			}

			// 上一次用户输入对话内容
			String lastDialogue = StringUtils.EMPTY;
			if(null != hbaseMap.get(historyDTO.getDialogId())) {
				lastDialogue = HbaseAiTextResultUtil
						.getInputDialogue(hbaseMap.get(historyDTO.getDialogId()).getReqParameters());
			}

			// 如果大于配置字数限制，截取最后部分
			if (lastDialogue.length() > contextConfig.getIntentionContextTokens()) {
				lastDialogue = lastDialogue.substring(lastDialogue.length() - contextConfig.getIntentionContextTokens());
			}

			// 构建对话对象
			DialogueIntentionEntity.DialogueInfo dialogueInfo = new DialogueIntentionEntity.DialogueInfo();
			dialogueInfo.setDialogue(lastDialogue);
			dialogueInfo.setIntention(historyDTO.getMultipleIntentionCode());
			historyDialogueList.add(dialogueInfo);

			// 拼接历史对话内容
			content.append(lastDialogue);
		}

		// 反转列表 上下文需要正序排传入
		if (!CollectionUtils.isEmpty(historyDialogueList)) {
			Collections.reverse(historyDialogueList);
		}

		return historyDialogueList;
	}

	/**
	 * 根据意图编码和渠道来源校验是否符合意图要求的渠道
	 *
	 * @param intentionCode 意图编码
	 * @param sourceChannel 渠道来源
	 * @return 是否符合，true - 符合，false - 不符合
	 */
	private boolean sourceChannelFilter(String intentionCode, String sourceChannel) {

		// 033-图片智能鉴伪是否为小天渠道
		if (DialogueIntentionEnum.SMART_FAKE_CHECK.getCode().equals(intentionCode)) {
			return sourceChannelsProperties.isXiaoTian(sourceChannel);
		}

		// 038-搜索知识库资源，需判断是否为小天渠道，邮箱渠道也支持
		if (DialogueIntentionEnum.SEARCH_KNOWLEDGE_BASE_RESOURCE.getCode().equals(intentionCode)) {
			return sourceChannelsProperties.isXiaoTian(sourceChannel) || sourceChannelsProperties.isMail(sourceChannel);
		}
		return true;
	}

	/**
	 * 搜索意图转综合搜索
	 *
	 * @param intentionVO   对话接口内部数据传输对象
	 * @param intentionMain 主意图编码
	 * @param enableAiSearch 是否开启AI搜索
	 * @return 对话意图响应VO
	 */
	private DialogueIntentionVO convertComprehensiveSearchIntention(DialogueIntentionVO intentionVO, String intentionMain, Boolean enableAiSearch) {
		// 所有搜索意图（无综合搜索）
		List<String> searchSingleIntentionList = DialogueIntentionEnum.getSearchSingleIntentionList();
		// 个人资产搜索意图编码列表
		List<String> searchFileIntentionCodeList = DialogueIntentionEnum.getSearchFileIntentionCodeList();
		// 主意图是搜索意图（有综合搜索、泛化意图） || 开启AI搜索
		boolean searchFlag = DialogueIntentionEnum.isSearchIntentionOrOther(intentionMain) || Boolean.TRUE.equals(enableAiSearch);
		/** 搜索意图转综合搜索开关：true-打开 && (主意图是搜索意图（有综合搜索） || 开启AI搜索) */
		if (searchIntentionProperties.isConvertComprehensiveSearch() && searchFlag) {
			// 新组装的多意图列表
			List<DialogueIntentionVO.IntentionInfo> newIntentionList = new ArrayList<>();
			List<DialogueIntentionVO.IntentionInfo> notSearchIntentionList = new ArrayList<>();
			List<IntentionInfo> newSubIntentionInfoList = new ArrayList<>();
			List<IntentionInfo> intentionInfoList = intentionVO.getIntentionInfoList();
			List<IntentEntityVO> entityList = intentionInfoList.get(0).getEntityList();
			// 获取所有接口返回的意图编码
			List<String> intentionCodeList = new ArrayList<>();
			// 个人资产搜索意图，在主意图出现的次数
			int searchFileIntentionCodeNum = 0;
			// 出现多个搜索意图，需要按各自的实体去搜索，其他不存在的搜索意图使用主意图的实体
			for (IntentionInfo subIntentionInfo : intentionInfoList) {
				String subIntentionCode = subIntentionInfo.getIntention();
				if (searchSingleIntentionList.contains(subIntentionCode)) {
					intentionCodeList.add(subIntentionCode);
					newSubIntentionInfoList.add(subIntentionInfo);
				}

				/** 处理个人资产搜索意图在主意图出现的次数 */
				// 单个个人资产搜索意图，次数+1
				if (searchFileIntentionCodeList.contains(subIntentionCode)) {
					searchFileIntentionCodeNum++;
				}
				// 综合搜索意图，次数+searchFileIntentionCodeList.size()
				if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode().equals(subIntentionCode)) {
					searchFileIntentionCodeNum += searchFileIntentionCodeList.size();
				}

				/** 非搜索意图 */
				if(!DialogueIntentionEnum.isSearchIntentionOrOther(subIntentionCode)){
					notSearchIntentionList.add(subIntentionInfo);
				}
			}
			// 如果主意图是【搜索图片意图】，遍历entityList把labelList赋值给metaDataList
			if (DialogueIntentionEnum.SEARCH_IMAGE.getCode().equals(intentionMain)) {
				for (IntentEntityVO intentEntityVO : entityList) {
					List<KeyValueVO> labelList = intentEntityVO.getLabelList();
					// labelList不为空 && metaDataList为空，才处理
					if (CollUtil.isNotEmpty(labelList) && CollUtil.isEmpty(intentEntityVO.getMetaDataList())) {
						intentEntityVO.setMetaDataList(labelList);
					}
				}
			}
			/**
			 * 如果主意图只有【搜索发现广场意图】，使用发现广场意图的实体进行个人云搜索时
			 * 根据发现广场意图返回的类型，填写个人云的文件类型搜索条件，发现广场里有“书籍、文档、试卷、影视、其他、视频”类型，
			 * 视频、影视、其他都可以当做视频类，则设置个人云搜索的文件类型条件为视频，除了这三个类型，其他都不设置文件类型条件。
			 */
			if (intentionCodeList.size() == 1 && DialogueIntentionEnum.SEARCH_DISCOVERY.getCode().equals(intentionMain)) {
				List<String> queryTypeStrList = new ArrayList<>();
				for (IntentEntityVO intentEntityVO : entityList) {
					// 搜索类型，添加内容类型数据
					asyncSearchService.setResultList(queryTypeStrList, intentEntityVO.getLabelList());
				}
				// 搜索类型处理
				List<Integer> queryTypeList = asyncSearchService.searchDiscoveryParamV2HandleQueryTypeList(queryTypeStrList);
				// 判断是否为【视频】相关类型
				if (SearchDiscoveryParamQueryTypeEnum.judgeSearchVideo(queryTypeList)) {
					// true，则设置个人云搜索的文件类型条件为014-搜视频
					IntentionInfo subIntentionInfo = new IntentionInfo();
					subIntentionInfo.setIntention(DialogueIntentionEnum.SEARCH_VIDEO.getCode());
					// 添加主意图的实体
					subIntentionInfo.setEntityList(entityList);
					// 非接口返回的意图
					subIntentionInfo.setInterfaceReturnIntention(false);
					newSubIntentionInfoList.add(subIntentionInfo);
					// 单个个人资产搜索意图，次数为1
					searchFileIntentionCodeNum = 1;
				}
				// 判断是否为【文档】相关类型：queryTypeList，不包含全部 && 只包含书籍、文档、试卷，其中一个或多个
				if (SearchDiscoveryParamQueryTypeEnum.judgeSearchDocument(queryTypeList)) {
					// true，则设置个人云搜索的文件类型条件为013-搜文档
					IntentionInfo subIntentionInfo = new IntentionInfo();
					subIntentionInfo.setIntention(DialogueIntentionEnum.SEARCH_DOCUMENT.getCode());
					// 添加主意图的实体
					subIntentionInfo.setEntityList(entityList);
					// 非接口返回的意图
					subIntentionInfo.setInterfaceReturnIntention(false);
					newSubIntentionInfoList.add(subIntentionInfo);
					// 单个个人资产搜索意图，次数为1
					searchFileIntentionCodeNum = 1;
				}
			}
			// 遍历searchSingleIntentionList，添加其他搜索意图
			for (String intentionCode : searchSingleIntentionList) {
				/**
				 * 个人资产搜索意图在主意图出现的次数 = 1，则【不再添加】其他个人资产搜索意图
				 *
				 * 针对个人云文件搜索，当有明确搜索意图时，在调个人云搜索接口时，需传到文件类型参数
				 * 比如“搜植物的图片”，这时候命中了搜图片意图，则只进行图片搜索
				 * 根据是否打开智能相册开关判断走个人云图片搜索还是语义搜图，但是不进行文档、视频等搜索。
				 */
				if (searchFileIntentionCodeNum == 1 && searchFileIntentionCodeList.contains(intentionCode)) {
					continue;
				}
				if (!intentionCodeList.contains(intentionCode)) {
					IntentionInfo subIntentionInfo = new IntentionInfo();
					subIntentionInfo.setIntention(intentionCode);
					// 添加主意图的实体
					subIntentionInfo.setEntityList(entityList);
					// 非接口返回的意图
					subIntentionInfo.setInterfaceReturnIntention(false);
					newSubIntentionInfoList.add(subIntentionInfo);
				}
			}

			// 合并综合搜索
			IntentionInfo comprehensiveSearchIntention = new IntentionInfo();
			comprehensiveSearchIntention.setIntention(DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode());
			comprehensiveSearchIntention.setEntityList(new ArrayList<>());
			comprehensiveSearchIntention.setSubIntentions(newSubIntentionInfoList);
			// 追加主意图
			newIntentionList.add(comprehensiveSearchIntention);
			// 追加其他意图，用于多意图推荐
			newIntentionList.addAll(notSearchIntentionList);
			// 重新赋值多意图
			intentionVO.setIntentionInfoList(newIntentionList);
		}
		return intentionVO;
	}

	/**
	 * 搜索意图转综合搜索
	 *
	 * @param intentionVO   对话意图响应VO
	 * @return 对话意图响应VO
	 */
	private DialogueIntentionVO newConvertComprehensiveSearchIntention(DialogueIntentionVO intentionVO) {
		// 主意图编码
		String intentionMain = getIntentionCode(intentionVO);
		// 【不是】搜索意图（有综合搜索意图、泛化意图）
		if(!DialogueIntentionEnum.isSearchIntentionOrOther(intentionMain)){
			// 非搜索意图，直接返回
			return intentionVO;
		}

		/** 搜索意图处理 */
		// 如果主意图是999，则转为综合搜索
		if(DialogueIntentionEnum.OTHER.getCode().equals(intentionMain)){
			intentionVO.getIntentionInfoList().get(0).setIntention(DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode());
		}

		// 所有搜索意图（无综合搜索意图）
		List<String> searchSingleIntentionList = DialogueIntentionEnum.getSearchSingleIntentionList();
		List<IntentionInfo> intentionInfoList = intentionVO.getIntentionInfoList();
		List<IntentionInfo> newSubIntentionInfoList = new ArrayList<>();
		List<DialogueIntentionVO.IntentionInfo> notSearchIntentionList = new ArrayList<>();
		for (IntentionInfo subIntentionInfo : intentionInfoList) {
			String subIntentionCode = subIntentionInfo.getIntention();
			// 搜索意图
			if (searchSingleIntentionList.contains(subIntentionCode)) {
				// 搜索意图，按各自的实体去搜索
				newSubIntentionInfoList.add(subIntentionInfo);
			}

			// 非搜索意图
			if(!DialogueIntentionEnum.isSearchIntentionOrOther(subIntentionCode)){
				notSearchIntentionList.add(subIntentionInfo);
			}
		}

		/**
		 * 需要转018-综合搜索
		 * 场景1：搜索意图数量 == 1 && 主意图是（018-综合搜索 || 999-泛化意图）
		 * 场景2：搜索意图数量 > 1
		 */
		boolean needConvert = newSubIntentionInfoList.size() == NumberEnum.ONE.getValue() && DialogueIntentionEnum.isComprehensiveIntention(intentionMain);
		if(needConvert || newSubIntentionInfoList.size() > NumberEnum.ONE.getValue()){
			// 新组装的多意图列表
			List<DialogueIntentionVO.IntentionInfo> newIntentionList = new ArrayList<>();
			// 构建综合搜索意图
			IntentionInfo comprehensiveSearchIntention = new IntentionInfo();
			comprehensiveSearchIntention.setIntention(DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode());
			comprehensiveSearchIntention.setEntityList(new ArrayList<>());
			comprehensiveSearchIntention.setSubIntentions(newSubIntentionInfoList);
			// 追加主意图
			newIntentionList.add(comprehensiveSearchIntention);
			// 追加其他意图，用于多意图推荐
			newIntentionList.addAll(notSearchIntentionList);
			// 重新赋值多意图
			intentionVO.setIntentionInfoList(newIntentionList);
		}

		return intentionVO;
	}

	/**
	 * 意图实体过滤
	 *
	 * @param intentionVO 意图VO
	 * @param sourceChannel 渠道来源
	 */
	private void intentionEntityFilter(DialogueIntentionVO intentionVO, String sourceChannel) {
		try {
			log.info("意图实体过滤【前】：{}", JsonUtil.toJson(intentionVO));
			/** 小天渠道 */
			if(sourceChannelsProperties.isXiaoTian(sourceChannel)){
				// h5Version < 小天2.0.0版本，忽略意图实体过滤
				if (VersionUtil.xtH5VersionLt200()) {
					return;
				}

				// 有意图结果才处理
				List<IntentionInfo> intentionInfoList = intentionVO.getIntentionInfoList();
				if(CollUtil.isEmpty(intentionInfoList)){
					return;
				}

				// 获取搜索分类
				String category = getCategory(intentionVO, intentionInfoList);

				// 获取当前分类需要过滤的关键字列表
				List<String> categoryKeywords = searchParamProperties.getCategoryKeywords(category);
				if(CollUtil.isEmpty(categoryKeywords)){
					return;
				}
				// 关键字的数据转小写
				List<String> keywords = categoryKeywords.stream().filter(CharSequenceUtil::isNotBlank).map(String::toLowerCase).collect(Collectors.toList());

				// 遍历所有意图信息，关键字过滤
				intentionInfoList.stream()
						.map(IntentionInfo::getEntityList)
						.filter(CollUtil::isNotEmpty)
						.flatMap(List::stream)
						// 实体处理
						.forEach(entity -> processEntity(entity, keywords));

			}
		} catch (Exception e) {
			log.error("意图实体过滤异常：{}", e.getMessage(), e);
		} finally {
			log.info("意图实体过滤【后】：{}", JsonUtil.toJson(intentionVO));
		}
	}

	/**
	 * 获取搜索分类
	 *
	 * 【影视类】文件（文件夹、视频）、发现、圈子
	 * 【书籍类】文件（文件夹、文档、书籍）、发现、圈子
	 * 【文档类】文件（文件夹、文档）、发现、圈子
	 * 【音频类】文件（音频）、发现、圈子
	 * 【笔记】笔记
	 * 【邮件】邮件
	 * 【功能类】功能
	 * 【活动类】活动
	 * 【图片】图片
	 * 【圈子】圈子
	 * @Author: WeiJingKun
	 *
	 * @param intentionVO 意图VO
	 * @param intentionInfoList 意图信息列表
	 * @return 搜索分类
	 */
	@Nullable
	private String getCategory(DialogueIntentionVO intentionVO, List<IntentionInfo> intentionInfoList) {
		String category = "";
		// 收集所有的意图编码
		List<String> intentionCodeList = intentionInfoList.stream().map(IntentionInfo::getIntention).collect(Collectors.toList());
		// 主意图编码
		String intentionMain = getIntentionCode(intentionVO);
		// 【影视类】文件（文件夹、视频）、发现（影视、视频、短剧）、圈子
		// 这里通过意图已经可以区分【影视类】：文件夹、视频、发现、圈子
		List<String> queryIntentionList = ListUtil.toList(DialogueIntentionEnum.SEARCH_FOLDER.getCode(), DialogueIntentionEnum.SEARCH_VIDEO.getCode(),
				DialogueIntentionEnum.SEARCH_DISCOVERY.getCode(), DialogueIntentionEnum.SEARCH_GROUP.getCode());
		if(CollUtil.containsAll(intentionCodeList, queryIntentionList)){
			category = SearchParamProperties.ParamFilter.CategoryEnum.FILM.getType();
		}
		// 【书籍类】文件（文件夹、文档、书籍）、发现、圈子
		// 【文档类】文件（文件夹、文档）、发现、圈子
		queryIntentionList = ListUtil.toList(DialogueIntentionEnum.SEARCH_FOLDER.getCode(), DialogueIntentionEnum.SEARCH_DOCUMENT.getCode(),
				DialogueIntentionEnum.SEARCH_DISCOVERY.getCode(), DialogueIntentionEnum.SEARCH_GROUP.getCode());
		if(CharSequenceUtil.isBlank(category) && CollUtil.containsAll(intentionCodeList, queryIntentionList)){
			// 判断是否为书籍类型
			boolean isBook = isBook(intentionInfoList);
			if(isBook){
				// 【书籍类】
				category = SearchParamProperties.ParamFilter.CategoryEnum.BOOK.getType();
			} else {
				// 【文档类】
				category = SearchParamProperties.ParamFilter.CategoryEnum.DOCUMENT.getType();
			}
		}
		// 【音频类】文件（音频）、发现、圈子
		queryIntentionList = ListUtil.toList(DialogueIntentionEnum.SEARCH_AUDIO.getCode(),
				DialogueIntentionEnum.SEARCH_DISCOVERY.getCode(), DialogueIntentionEnum.SEARCH_GROUP.getCode());
		if(CharSequenceUtil.isBlank(category) && CollUtil.containsAll(intentionCodeList, queryIntentionList)){
			category = SearchParamProperties.ParamFilter.CategoryEnum.AUDIO.getType();
		}
		// 【笔记】笔记
		if(CharSequenceUtil.isBlank(category) && DialogueIntentionEnum.SEARCH_NOTE.getCode().equals(intentionMain)){
			category = SearchParamProperties.ParamFilter.CategoryEnum.NOTE.getType();
		}
		// 【邮件】邮件
		if(CharSequenceUtil.isBlank(category) && DialogueIntentionEnum.SEARCH_MAIL.getCode().equals(intentionMain)){
			category = SearchParamProperties.ParamFilter.CategoryEnum.MAIL.getType();
		}
		// 【功能类】功能
		if(CharSequenceUtil.isBlank(category) && DialogueIntentionEnum.SEARCH_FUNCTION.getCode().equals(intentionMain)){
			category = SearchParamProperties.ParamFilter.CategoryEnum.FUNCTION.getType();
		}
		// 【活动类】活动
		if(CharSequenceUtil.isBlank(category) && DialogueIntentionEnum.SEARCH_ACTIVITY.getCode().equals(intentionMain)){
			category = SearchParamProperties.ParamFilter.CategoryEnum.ACTIVITY.getType();
		}
		// 【图片】图片
		if(CharSequenceUtil.isBlank(category) && DialogueIntentionEnum.SEARCH_IMAGE.getCode().equals(intentionMain)){
			category = SearchParamProperties.ParamFilter.CategoryEnum.IMAGE.getType();
		}
		// 【圈子】圈子
		if(CharSequenceUtil.isBlank(category) && DialogueIntentionEnum.SEARCH_GROUP.getCode().equals(intentionMain)){
			category = SearchParamProperties.ParamFilter.CategoryEnum.GROUP.getType();
		}
		return category;
	}

	/**
	 * 是否是书籍类
	 * @Author: WeiJingKun
	 *
	 * @param intentionInfoList 意图信息列表
	 * @return true-书籍类
	 */
	private boolean isBook(List<IntentionInfo> intentionInfoList) {
		boolean isBook = false;
		for (IntentionInfo intentionInfo : intentionInfoList) {
			// 如果是发现广场意图，判断是否是书籍类
			if(DialogueIntentionEnum.SEARCH_DISCOVERY.getCode().equals(intentionInfo.getIntention())){
				// 获取意图的实体
				List<IntentEntityVO> entityList = intentionInfo.getEntityList();
				if(CollUtil.isNotEmpty(entityList)){
					List<String> labelDescList = new ArrayList<>();
					for (IntentEntityVO entityVO : entityList){
						// 搜索类型，添加内容类型数据
						chatDialogueSearchService.setResultList(labelDescList, entityVO.getLabelList());
					}
					if(CollUtil.isNotEmpty(labelDescList)){
						isBook = labelDescList.contains(SearchDiscoveryParamQueryTypeEnum.BOOKS.getDesc());
					}
				}
			}
		}
		return isBook;
	}

	/**
	 * 实体处理
	 * @Author: WeiJingKun
	 *
	 * @param entity 实体识别结果VO
	 * @param keywords 关键字集
	 */
	private void processEntity(IntentEntityVO entity, List<String> keywords) {
		Optional.ofNullable(entity.getMetaDataList())
				.filter(CollUtil::isNotEmpty)
				.ifPresent(metaDataList -> {
					List<KeyValueVO> filterMetaDataList = metaDataList.stream()
							// 创建过滤后的KeyValue对象
							.map(keyValueVO -> createFilteredKeyValue(keyValueVO, keywords))
							// 仅保留：value不为空的值
							.filter(keyValueVO -> CollUtil.isNotEmpty(keyValueVO.getValue()))
							.collect(Collectors.toList());
					// 如果过滤后没有实体，则使用原始实体
					entity.setMetaDataList(CollUtil.isNotEmpty(filterMetaDataList) ? filterMetaDataList : metaDataList);
				});
	}

	/**
	 * 创建过滤后的KeyValue对象
	 * @Author: WeiJingKun
	 *
	 * @param keyValueVO 原始KeyValue对象
	 * @param keywords 关键字集
	 * @return 过滤后的KeyValue对象
	 */
	private KeyValueVO createFilteredKeyValue(KeyValueVO keyValueVO, List<String> keywords) {
		List<String> filteredValues = Optional.ofNullable(keyValueVO.getValue())
				.orElse(Collections.emptyList())
				.stream()
				.map(value -> CharSequenceUtil.isNotBlank(value) ? value.toLowerCase() : null)
				.filter(CharSequenceUtil::isNotBlank)
				// 仅保留：不需要过滤的值
				.filter(value -> !keywords.contains(value))
				.collect(Collectors.toList());
		// 创建新对象避免副作用
		return new KeyValueVO(keyValueVO.getKey(), filteredValues);
	}

}

