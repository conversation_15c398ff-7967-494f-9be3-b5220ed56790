package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import com.aliyun.tea.utils.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.service.HistoryDialogueSaveRedisService;
import com.zyhl.yun.api.outer.config.IntentionContextProperties;
import com.zyhl.yun.api.outer.domain.dto.redis.HistoryDialogInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 历史对话信息redis处理接口实现类
 *
 * <AUTHOR>
 * @date 2024/12/18 15:01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HistoryDialogueSaveRedisServiceImpl implements HistoryDialogueSaveRedisService {

    private final RedisOperateRepository redisOperateRepository;
    private final IntentionContextProperties contextConfig;
    private final AlgorithmChatContentRepository contentRepository;

    /**
     * 保存redis历史对话信息
     *
     * @param handleDTO 对话接口内部数据传输对象
     */
    @Override
    public void setRedisHistoryDialogInfo(ChatAddHandleDTO handleDTO) {
        if (handleDTO.getSessionId() == null || handleDTO.getDialogueId() == null) {
            log.info("保存redis历史对话信息 sessionId或dialogueId为空");
            return;
        }

        if (StringUtils.isEmpty(handleDTO.getIntentionCode())) {
            log.info("保存redis历史对话信息 intentionCode为空");
            return;
        }

        try {
            // 会话id
            String sessionId = String.valueOf(handleDTO.getSessionId());

            // 多意图编码
            String multipleCode = buildMultipleIntentionCode(handleDTO);

            // 保存到redis的历史对话信息DTO
            HistoryDialogInfoDTO dialogInfo = HistoryDialogInfoDTO.builder()
                    .dialogId(String.valueOf(handleDTO.getDialogueId()))
                    .intentionCode(handleDTO.getIntentionCode())
                    .multipleIntentionCode(multipleCode)
                    .build();

            // 保存到redis
            redisOperateRepository.setHistoryDialogInfo(sessionId, dialogInfo, contextConfig.getMaxCount(), contextConfig.getExpireTime());

        } catch (Exception e) {
            log.error("保存redis历史对话信息异常 innerDTO:{} | e:", JsonUtil.toJson(handleDTO), e);
        }
    }

    /**
     * 获取redis历史对话信息列表
     *
     * @param sessionId 会话id
     * @param maxLength 返回的列表长度 从后往前取
     * @param userId    用户id
     * @return 保存到redis的历史对话信息DTO列表
     */
    @Override
    public List<HistoryDialogInfoDTO> getRedisHistoryDialogInfoList(String sessionId, Integer maxLength, String userId) {
        if (sessionId == null) {
            log.info("获取redis历史对话信息列表 sessionId为空");
            return null;
        }

        // 获取历史会话中的对话信息列表
        List<HistoryDialogInfoDTO> historyList = redisOperateRepository.getHistoryDialogInfoList(sessionId);

        // session有值，在redis获取不到，从tidb获取历史对话信息列表
        if (CollectionUtils.isEmpty(historyList)) {
            log.info("获取redis历史对话信息列表 session有值，在redis获取不到，从tidb获取历史对话信息列表 sessionId:{}", sessionId);

            // 根据会话id查询对话列表并指定最大返回条数
            historyList = getHistoryDialogInfoDtoList(sessionId, userId);
            if (historyList == null) {
                log.info("获取redis历史对话信息列表 session有值，在redis获取不到，从tidb获取历史对话信息列表为空 sessionId:{}", sessionId);
                return null;
            }
        }

        // 如果 maxLength 大于等于 historyList 的长度，直接返回整个列表
        if (maxLength >= historyList.size()) {
            return historyList;
        }

        // 计算从后往前取 maxLength 条记录的起始索引
        int fromIndex = historyList.size() - maxLength;

        // 返回子列表
        return historyList.subList(fromIndex, historyList.size());
    }

    /**
     * 根据会话id查询对话列表并指定最大返回条数
     *
     * @param sessionId 会话id
     * @param userId    用户id
     * @return 保存到redis的历史对话信息DTO列表
     */
    private List<HistoryDialogInfoDTO> getHistoryDialogInfoDtoList(String sessionId, String userId) {
        // 查询tidb
        List<AlgorithmChatContentEntity> contentList = contentRepository.getContentListBySessionId
                (Long.parseLong(sessionId), contextConfig.getMaxCount(), userId);
        if (CollectionUtils.isEmpty(contentList)) {
            return null;
        }

        // 根据创建时间排序，取最近的maxCount条记录
        return contentList.stream()
                .sorted(Comparator.comparing(AlgorithmChatContentEntity::getCreateTime))
                .map(content -> HistoryDialogInfoDTO.builder()
                        .dialogId(String.valueOf(content.getId()))
                        .intentionCode(content.getToolsCommand())
                        .multipleIntentionCode(content.getToolsCommand())
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 构建多意图编码
     *
     * @param handleDTO 对话接口内部数据传输对象
     * @return 拼接后的多意图编码
     */
    private String buildMultipleIntentionCode(ChatAddHandleDTO handleDTO) {
        if (handleDTO.getIntentionVO() == null || CollectionUtils.isEmpty(handleDTO.getIntentionVO().getIntentionInfoList())) {
            return handleDTO.getIntentionCode();
        }

        // 获取去重意图编码列表
        List<DialogueIntentionVO.IntentionInfo> intentionInfoList = handleDTO.getIntentionVO().getIntentionInfoList();

        // 意图编码列表
        List<String> codeList = new ArrayList<>();
        for (DialogueIntentionVO.IntentionInfo intentionInfo : intentionInfoList) {
            // 意图为综合搜索意图，并且子意图列表有数据，拼接子意图编码
            if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode().equals(intentionInfo.getIntention())
                    && CollectionUtils.isNotEmpty(intentionInfo.getSubIntentions())) {
                // 字意图编码列表
                List<String> subCodeList = intentionInfo.getSubIntentions().stream()
                        .map(DialogueIntentionVO.IntentionInfo::getIntention)
                        .collect(Collectors.toList());
                codeList.add(String.join("+", subCodeList));
                continue;
            }

            // 直接添加意图编码
            codeList.add(intentionInfo.getIntention());
        }

        if (CollectionUtils.isEmpty(codeList)) {
            return handleDTO.getIntentionCode();
        }

        return String.join("+", codeList);
    }
}
