package com.zyhl.yun.api.outer.application.service.knowledge;

import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileInfoReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileListBatchReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileListReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileUpdateReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.PersonalKnowledgeResourceListReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.*;
import com.zyhl.yun.api.outer.domain.vo.knowledge.FileListPageInfoVO;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeFileVO;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeResource;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeResourceListPageInfoVO;

import java.util.List;

/**
 * 用户知识库文件
 *
 * <AUTHOR>
 */
public interface UserKnowledgeFileService {


    /**
     * 个人知识库文件列表
     *
     * @param dto
     * @return
     */
    FileListPageInfoVO list(KnowledgeFileListReqDTO dto);

    /**
     * 个人知识库文件列表
     *
     * @param dto
     * @return
     */
    FileListPageInfoVO list180(KnowledgeFileListReqDTO dto);

    /**
     * 文件详情信息
     *
     * @param dto
     * @return
     */
    PersonalKnowledgeFileVO info(KnowledgeFileInfoReqDTO dto);

    /**
     * 查询个人知识库资源列表
     *
     * @param dto 入参
     * @return 个人知识库资源列表
     */
    PersonalKnowledgeResourceListPageInfoVO resourceList(PersonalKnowledgeResourceListReqDTO dto);

    /**
     * 批量查询个人知识库资源
     *
     * @param dto 入参
     * @return 个人知识库资源列表
     */
    List<PersonalKnowledgeResource> batchGet(KnowledgeFileListBatchReqDTO dto);

    /**
     * 创建文件夹
     *
     * @param dto 入参
     */
    PersonalKnowledgeResource createFolder(KnowledgeCreateFolderReqDTO dto);


    /**
     * 更新个人知识库资源
     *
     * @param dto 入参
     * @return 更新结果
     */
    PersonalKnowledgeResource update(KnowledgeFileUpdateReqDTO dto);

    String getUrlAndCheckAuditStatus(KnowledgeFileInfoReqDTO req);
}
