package com.zyhl.yun.api.outer.application.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * algorithm_metadata_task的 任务类型
 *
 * <AUTHOR>
 * @description 任务类型
 * @date 2024/4/19 下午2:23
 */
@Getter
@AllArgsConstructor
public enum MetadataTaskTypeEnum {
    /**
     * 1.图片元数据分析
     */
    IMAGE_METADATA_TASK(1,"图片元数据分析"),

    /**
     * 2.人脸聚类
     */
    FACE_CLUSTERING_TASK(2,"人脸聚类"),

    /**
     * 3.相似度聚类
     */
    SIMILARITY_CLUSTERING_TASK(3,"相似度聚类"),

    /**
     * 4.文档全文检索
     */
    DOCUMENT_FULL_TEXT_SEARCH_TASK(4,"文档全文检索"),

    /**
     * 5.个人知识库文档向量
     */
    DOCUMENT_USER_KNOWLEDGE_VECTOR_TASK(5,"个人知识库文档向量"),

    /**
     * 6.公共知识库文档向量
     */
    DOCUMENT_COMMON_KNOWLEDGE_VECTOR_TASK(6,"公共知识库文档向量"),


    /**
     * 8.运营用户图片元数据提取
     */
    OPERATION_USER_IMAGE_METADATA_TASK(8,"运营用户元数据提取");

    private final Integer type;
    private final String describe;

    /**
     * 根据 type 判断是否存在对应的枚举类型
     *
     * @param type 要检查的类型
     * @return 如果存在对应的枚举类型则返回 true，否则返回 false
     */
    public static boolean existsByType(Integer type) {
        for (MetadataTaskTypeEnum value : MetadataTaskTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return true;
            }
        }
        return false;
    }

}
