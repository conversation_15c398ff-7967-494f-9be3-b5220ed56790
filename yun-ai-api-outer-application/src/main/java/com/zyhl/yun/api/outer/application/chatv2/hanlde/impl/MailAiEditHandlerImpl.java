package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.enums.YunAiYunDiskResultCode;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueAttachmentDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.event.completeimpl.MailAiEditCallbackEvent;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.pojo.LocalFileInfo;
import com.zyhl.yun.api.outer.application.chatv2.service.*;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatHistoryService;
import com.zyhl.yun.api.outer.config.MailAiEditProperties;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.vo.DocumentParsingResultVO;
import com.zyhl.yun.api.outer.enums.chat.*;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.external.DocumentParsingExternalService;
import com.zyhl.yun.api.outer.external.YunDiskExternalService;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.zyhl.hcy.yun.ai.common.base.enums.AIModuleEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.FileUploadVO;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Date;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 邮件编辑处理类
 * 
 * <AUTHOR>
 * @date 2025-04-27
 */
@Slf4j
@Component
public class MailAiEditHandlerImpl  extends AbstractChatAddV2Handler {

    /**
     * 当前执行顺序
     */
    private ExecuteSort thisExecuteSort = ExecuteSort.MAIL_AI_EDIT;

    @Resource
    private BenefitService benefitService;
    @Resource
    private DataSaveService dataSaveService;
    @Resource
    private ChatAddCheckService chatAddCheckService;
    @Resource
    private DocumentParsingExternalService documentParsingExternalService;
    @Resource
    private TextModelExternalService textModelExternalService;
    @Resource
    private MailAiEditCallbackEvent mailAiEditCallbackEvent;
    @Resource
    private ChatConfigServiceDomainService chatConfigServiceDomainService;
    @Resource
    private ModelProperties modelProperties;
    @Resource
    private MailAiEditProperties mailAiEditProperties;
    @Resource
    private YunDiskExternalService yunDiskExternalService;
    @Resource
    private AlgorithmChatContentRepository algorithmChatContentRepository;
    @Resource
    private AlgorithmChatHistoryService algorithmChatHistoryService;




    @Override
    public void afterPropertiesSet() throws Exception {
        //支持的业务初始化
        List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        this.setBusinessTypes(thisBusinessTypes);
    }

    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public boolean execute(ChatAddHandleDTO handleDTO) {
        return true;
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        // 记录开始时间和各环节耗时
        long startTime = System.currentTimeMillis();
        long checkTime = 0;
        long benefitTime = 0;
        long saveDataTime = 0;
        long resourceTime = 0;
        long promptTime = 0;
        long modelCallTime = 0;
        int resourceSize = 0;
        long currentTime = System.currentTimeMillis();
        boolean enableAiSearch = handleDTO.getInputInfoDTO().isEnableAiEdite();
        if (!enableAiSearch) {
            // 非邮箱AI编辑的不进入这个handler处理，直接返回，去执行下一个handler
            return true;
        }
        log.info("进入{}", thisExecuteSort.getDesc());
        checkTime = System.currentTimeMillis() - currentTime;
        currentTime = System.currentTimeMillis();

        // 扣减权益
        benefitService.consumeBenefit(handleDTO.getReqDTO(), RequestContextHolder.getPhoneNumber(),
                handleDTO.getDialogueId());
        benefitTime = System.currentTimeMillis() - currentTime;
        currentTime = System.currentTimeMillis();

        // 保存到hbase
        dataSaveService.saveTextResult(handleDTO, "", "");

        // 保存tidb数据库
        dataSaveService.add(handleDTO, ChatStatusEnum.CHAT_IN);
        saveDataTime = System.currentTimeMillis() - currentTime;
        currentTime = System.currentTimeMillis();

        String resourceContent = null;
        // 判断是否有附件信息，如果有附件则需要从个人云下载附件并解析
        DialogueAttachmentDTO attachmentDTO = handleDTO.getInputInfoDTO().getAttachment();
        if (attachmentDTO != null && attachmentDTO.getFileList() != null && attachmentDTO.getFileList().size() > 0) {
            // 从个人云下载附件并解析
            resourceContent = getResourceContent(handleDTO);
            if (StringUtils.isBlank(resourceContent)) {
                log.info("附件解析失败或者内容为空，流程结束，总耗时:{}ms", (System.currentTimeMillis() - startTime));
                handleDTO.getRespVO().setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
                BaseResult<?> result = BaseResult.error(AiResultCode.CODE_10000020.getCode(), AiResultCode.CODE_10000020.getMsg());
                handleDTO.getSseEmitterOperate().sendAndComplete(result);
                return false;
            }
            resourceSize = resourceContent.length();
        } else {
//                String processPromptKey = mailAiEditProperties.getProcessPromptKey();
            String summaryPromptKey = mailAiEditProperties.getSummaryPromptKey();
            String prompt = handleDTO.getInputInfoDTO().getPrompt();
            if (!summaryPromptKey.equals(prompt)) {
                log.info("编辑过程中没有上传文件信息，流程结束，总耗时:{}ms", (System.currentTimeMillis() - startTime));
                handleDTO.getRespVO().setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
                BaseResult<?> result = BaseResult.error(AiResultCode.CODE_10000020.getCode(), AiResultCode.CODE_10000020.getMsg());
                handleDTO.getSseEmitterOperate().sendAndComplete(result);
                return false;
            }
        }
        resourceTime = System.currentTimeMillis() - currentTime;
        currentTime = System.currentTimeMillis();

        // 获取用户输入的内容
        String dialogue = handleDTO.getInputInfoDTO().getDialogue();
        //根据前端出入的key获取提示词
        String promptKey = handleDTO.getInputInfoDTO().getPrompt();
        String promptTemplate = chatAddCheckService.getDialoguePrompt(promptKey,handleDTO.getReqDTO().getSourceChannel());

        // 获取用户设置的模型，没有设置则使用默认模型
        ChatConfigEntity chatConfigEntity = chatConfigServiceDomainService.getUserCanUseModel(handleDTO.getReqDTO().getUserId(),
                RequestContextHolder.getPhoneNumber(), handleDTO.getAssistantEnum(), handleDTO.getBusinessType());
        // 根据模型长度要求截取文件内容
        Integer lengthLimit = modelProperties.getLengthLimit(handleDTO.getAssistantEnum(), handleDTO.getBusinessType(), chatConfigEntity.getModelType());
        int maxLenLimit = Objects.isNull(lengthLimit) ? Integer.MAX_VALUE : (lengthLimit - promptTemplate.length() - (StringUtils.isNotBlank(dialogue) ? dialogue.length() : 0));

        if (StringUtils.isNotBlank(resourceContent) && resourceContent.length() > maxLenLimit) {
            resourceContent = resourceContent.substring(0, Math.min(maxLenLimit, resourceContent.length()));
            resourceSize = resourceContent.length();
        }
        String summaryPromptKey = mailAiEditProperties.getSummaryPromptKey();
        if (StringUtils.isNotBlank(resourceContent) && promptKey.equals(summaryPromptKey)) {
            handleDTO.setResourceContent(resourceContent);
        }
        // 拼接最终提示词
        String finalPrompt = generatePrompt(promptTemplate, resourceContent, dialogue);
        promptTime = System.currentTimeMillis() - currentTime;
        currentTime = System.currentTimeMillis();

        // 使用提示词调用大模型，模型返回内容流式返回给前端
        SseEventListener event = new SseEventListener(handleDTO, null);
        event.setCompleteCallbackEvent(mailAiEditCallbackEvent);
        event.getSseEmitterOperate().setSseName(SseNameEnum.AI_EDIT.getCode());
        event.setHandleDTO(handleDTO);
        // 普通对话
        event.setModelCode(chatConfigEntity.getModelType());
        sseDialogue(event, finalPrompt, handleDTO);
        modelCallTime = System.currentTimeMillis() - currentTime;

        // 统一打印各环节耗时
        log.info("AI编辑处理开始，dialogueId:{}, 总耗时:{}ms, 检查参数:{}ms, 扣减权益:{}ms, 保存数据:{}ms, 资源处理:{}ms, 提示词处理:{}ms, 模型调用:{}ms, 文件大小:{}字节",
                handleDTO.getDialogueId(),
                (System.currentTimeMillis() - startTime),
                checkTime,
                benefitTime,
                saveDataTime,
                resourceTime,
                promptTime,
                modelCallTime,
                resourceSize);

        //执行AI编辑后就停止了，不会再进行其他handler
        return false;
    }

    private String generatePrompt(String promptTemplate, String resourceContent, String dialogue) {
        return promptTemplate.replace("{documentContent}", StringUtils.isBlank(resourceContent) ? "NONE" : resourceContent)
                .replace("{userInput}", StringUtils.isBlank(dialogue) ? "NONE" : dialogue);
    }


    /**
     * 调用大模型进行流式对话
     * @param event
     * @param finalPrompt
     * @param handleDTO
     */
    private void sseDialogue( SseEventListener event, String finalPrompt, ChatAddHandleDTO handleDTO) {

        event.getTextDto().setPrompt(finalPrompt);
        // 新的对话信息
        TextModelMessageDTO msgDTO = new TextModelMessageDTO();
        msgDTO.setRole(TextModelRoleEnum.USER.getName());
        msgDTO.setContent(finalPrompt);

        // 不需要历史对话信息
        TextModelTextReqDTO reqDTO = event.getTextDto().toTextReqDTO(null);
        TextModelTextReqDTO req = new TextModelTextReqDTO();
        req.setTaskId(reqDTO.getTaskId());
        req.setUserId(reqDTO.getUserId());
        req.setSessionId(reqDTO.getSessionId());
        req.setMessageDtoList( Collections.singletonList(msgDTO));
        // 是否联网联网
        req.setEnableForceNetworkSearch(handleDTO.getInputInfoDTO().isEnableForceNetworkSearch());
        textModelExternalService.streamDialogue(event.getModelCode(), req, event);
        // 更新模型编码
        algorithmChatContentRepository.updateModelCode(event.getDialogId(), event.getModelCode());
    }


    /**
     * 获取个人云资源内容
     * @param handleDTO
     * @return
     */
    private String getResourceContent(ChatAddHandleDTO handleDTO) {
        long startTime = System.currentTimeMillis();
        long downloadTime = 0;
        long parseTime = 0;
        int fileSize = 0;

        DialogueAttachmentDTO attachmentDTO = handleDTO.getInputInfoDTO().getAttachment();

        String resourceName = "";

        // 下载文档 并 获取文档内容
        DocumentParsingResultVO docContent;
        // 文档资源
        File file = attachmentDTO.getFileList().get(0);
        try {
            long currentTime = System.currentTimeMillis();
            docContent = getDocContent(handleDTO);
            downloadTime = System.currentTimeMillis() - currentTime;
        } catch (YunAiBusinessException e) {
            try {
                log.info("MailAiEditHandlerImpl-getResourceContent retry, 已耗时:{}ms", (System.currentTimeMillis() - startTime));
                long currentTime = System.currentTimeMillis();
                docContent = getDocContent(handleDTO);
                downloadTime = System.currentTimeMillis() - currentTime;
            } catch (YunAiBusinessException e2) {
                handleDTO.setResourceName("AI编辑");
                log.error("MailAiEditHandlerImpl-getResourceContent error2, 总耗时:{}ms:", (System.currentTimeMillis() - startTime), e2);
                return "";
            }
        }
        if(StringUtils.isNotEmpty(file.getName())) {
            resourceName = file.getName();
        } else {
            if (StringUtils.isNotEmpty(handleDTO.getResourceName())) {
                resourceName = handleDTO.getResourceName();
            } else {
                resourceName = "AI编辑";
            }
        }
        if (ObjectUtil.isEmpty(docContent) || ObjectUtil.isEmpty(docContent.getText())) {
            // 文件内容为空
            log.info("AI编辑，下载解析文件，文件内容为空，总耗时:{}ms", (System.currentTimeMillis() - startTime));
            return "";
        }
        parseTime = System.currentTimeMillis() - startTime - downloadTime;
        fileSize = docContent.getText().length();

        log.info("AI编辑，文件下载解析完成，文件名:{}, 文件大小:{}字节, 总耗时:{}ms, 下载耗时:{}ms, 解析耗时:{}ms",
                resourceName, fileSize, (System.currentTimeMillis() - startTime), downloadTime, parseTime);

        handleDTO.setResourceName(resourceName);
        return docContent.getText();
    }

    private  DocumentParsingResultVO getDocContent(ChatAddHandleDTO handleDTO) throws YunAiBusinessException  {
        long startTime = System.currentTimeMillis();
        long getFileIdsTime = 0;
        long getLocalPathTime = 0;
        long parsingTime = 0;

        DialogueAttachmentDTO attachmentDTO = handleDTO.getInputInfoDTO().getAttachment();
        long currentTime = System.currentTimeMillis();
        List<String> fileIdList = attachmentDTO.getFileList().stream().map(File::getFileId).collect(Collectors.toList());
        getFileIdsTime = System.currentTimeMillis() - currentTime;

        currentTime = System.currentTimeMillis();
        List<LocalFileInfo> localPathPath = chatAddCheckService.getFilesByCloudDiskDocumentLocalPath(fileIdList, handleDTO.getReqDTO().getUserId(), false);
        getLocalPathTime = System.currentTimeMillis() - currentTime;

        if(CollUtil.isEmpty(localPathPath)) {
            log.error("AI编辑，获取文件本地路径失败，总耗时:{}ms", (System.currentTimeMillis() - startTime));
            throw new YunAiBusinessException(YunAiYunDiskResultCode.FILE_NOT_FOUND);
        }

        String summaryPromptKey = mailAiEditProperties.getSummaryPromptKey();
        String prompt =handleDTO.getInputInfoDTO().getPrompt();
        if (summaryPromptKey.equals(prompt)) {
            uploadFilesToAiEditDirectory(localPathPath, handleDTO);
        }

        List<String> localPathList = localPathPath.stream().map(LocalFileInfo::getLocalPath).collect(Collectors.toList());
        handleDTO.setResourceName(localPathPath.get(0).getName());
        handleDTO.setResourceExt(localPathPath.get(0).getExt());
        currentTime = System.currentTimeMillis();
        DocumentParsingResultVO result = documentParsingExternalService.parsingAfterDelete(localPathList, null);
        parsingTime = System.currentTimeMillis() - currentTime;

        log.info("AI编辑，文件解析详情，文件名:{}, 总耗时:{}ms, 获取文件ID:{}ms, 获取本地路径:{}ms, 文件解析:{}ms",
                handleDTO.getResourceName(),
                (System.currentTimeMillis() - startTime),
                getFileIdsTime,
                getLocalPathTime,
                parsingTime);

        return result;
    }

    /**
     * 将文件上传到AI文件库/AI编辑目录
     *
     * @param localPathPath 本地文件信息列表
     * @param handleDTO 处理数据传输对象
     * @return 上传结果列表
     */
    public void uploadFilesToAiEditDirectory(List<LocalFileInfo> localPathPath, ChatAddHandleDTO handleDTO) {
        long startTime = System.currentTimeMillis();
        List<FileUploadVO> uploadResults = new ArrayList<>();
        List<File> fileList = new ArrayList<>();
        String targetPath = mailAiEditProperties.getFilePath();
        String userId = handleDTO.getReqDTO().getUserId();
        Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();

        log.info("开始上传文件到AI编辑目录，文件数量:{}", localPathPath.size());

        for (LocalFileInfo fileInfo : localPathPath) {
            try {
                long fileUploadStartTime = System.currentTimeMillis();
                String localPath = fileInfo.getLocalPath();
                String originalFileName = fileInfo.getName();
                // 如果原文件名包含后缀，则去掉后缀
                if (originalFileName.contains(".")) {
                    originalFileName = originalFileName.substring(0, originalFileName.lastIndexOf("."));
                }
                String fileExt = fileInfo.getExt();

                // 生成带时间戳的文件名
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
                String timestamp = sdf.format(new Date());
                String fileName = originalFileName + "_" + timestamp;

                // 读取文件并转换为Base64
                Path path = Paths.get(localPath);
                byte[] fileBytes = Files.readAllBytes(path);
                String base64Content = Base64.getEncoder().encodeToString(fileBytes);

                // 上传文件到指定路径
                FileUploadVO uploadResult = yunDiskExternalService.uploadFileToCustomPath(
                        targetPath,
                        AIModuleEnum.MAILAI_EDIT,
                        userId,
                        belongsPlatform,
                        fileExt,
                        base64Content,
                        fileName
                );

                uploadResults.add(uploadResult);

                // 创建File对象并添加到fileList
                File file = new File();
                file.setFileId(uploadResult.getFileId());
                file.setParentFileId(uploadResult.getCategoryId());
                file.setName(fileName + "." + fileExt);
                file.setType("file");
                file.setFileExtension(fileExt);
                file.setCategory("doc");
                file.setSize((long) fileBytes.length);

                // 获取当前时间并格式化为RFC 3339格式
                OffsetDateTime now = OffsetDateTime.now();
                DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
                String rfc3339Time = now.format(formatter);

                file.setCreatedAt(rfc3339Time);
                file.setUpdatedAt(rfc3339Time);
                file.setLocalCreatedAt(rfc3339Time);
                file.setLocalUpdatedAt(rfc3339Time);

                fileList.add(file);

                log.info("文件上传成功，文件名:{}, 文件ID:{}, 耗时:{}ms",
                        fileName,
                        uploadResult.getFileId(),
                        (System.currentTimeMillis() - fileUploadStartTime));
            } catch (IOException e) {
                log.error("文件上传失败，文件名:{}, 错误信息:{}", fileInfo.getName(), e.getMessage(), e);
            }
        }

        // 设置文件列表到响应对象中
        if (!fileList.isEmpty()) {
            handleDTO.getRespVO().getFlowResult().setFileList(fileList);
            handleDTO.getRespVO().getFlowResult().setResultType(FlowResultTypeEnum.DOCUMENT.getType());
            log.info("文件信息已设置到响应对象中，文件数量:{}", fileList.size());
        }

        log.info("文件上传完成，总数量:{}, 成功数量:{}, 总耗时:{}ms",
                localPathPath.size(),
                uploadResults.size(),
                (System.currentTimeMillis() - startTime));
    }
}
