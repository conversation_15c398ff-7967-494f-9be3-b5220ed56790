package com.zyhl.yun.api.outer.application.service.chat;

import com.zyhl.yun.api.outer.application.dto.AlgorithmChatContentListDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatDeleteDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatListDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatPollingUpdateDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatStopDTO;
import com.zyhl.yun.api.outer.domain.vo.chat.ContentResultVO;
import com.zyhl.yun.api.outer.domain.vo.chat.MessageVO;
import com.zyhl.yun.api.outer.domain.vo.chat.PollingUpdateV2VO;
import com.zyhl.yun.api.outer.domain.vo.chat.PollingUpdateV3VO;
import com.zyhl.yun.api.outer.domain.vo.common.PageInfoVO;
import com.zyhl.yun.api.outer.enums.chat.ApiVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.OutAuditStatusEnum;

/**
 * 历史会话服务类
 *
 * <AUTHOR>
 * @data 2024/3/1 15:52
 */
public interface AlgorithmChatHistoryService {

    /**
     * 对话列表
     *
     * @param dto 请求参数
     * @return 分页结果
     */
    PageInfoVO<MessageVO> chatList(AlgorithmChatListDTO dto);

    /**
     * 对话内容列表
     *
     * @param dto 请求参数
     * @return 对话内容分页结果
     */
    PageInfoVO contentList(AlgorithmChatContentListDTO dto);

    /**
     * 删除对话记录
     *
     * @param dto 删除参数
     */
    void chatDelete(AlgorithmChatDeleteDTO dto);

    /**
     * 查询对话输出接口V1（轮巡查结果）
     *
     * @param dto 请求参数
     * @return 对话内容
     */
    ContentResultVO chatPollingUpdateV1(AlgorithmChatPollingUpdateDTO dto);

    /**
     * 查询对话输出接口V2（轮巡查结果）
     *
     * @param dto 请求参数
     * @return 执行结果
     */
    PollingUpdateV2VO chatPollingUpdateV2(AlgorithmChatPollingUpdateDTO dto);

    /**
     * 查询对话输出接口V3（轮巡查结果）【增加思维链和联网搜索来源信息】
     *
     * @param dto 请求参数
     * @return 执行结果
     */
	PollingUpdateV3VO chatPollingUpdateV3(AlgorithmChatPollingUpdateDTO dto);

    /**
     * 对话停止
     *
     * @param dto 入参
     */
    void chatStop(AlgorithmChatStopDTO dto);

    /**
     * 更新会话输出结果
     *
     * @param dialogueId 对话id
     * @param outAuditStatus 输出审核状态
     * @param chatStatus 对话状态
     * @param msg 输出结果
     */
    void updateOutResult(Long dialogueId, Integer outAuditStatus, Integer chatStatus, String msg);

    /**
     * 更新会话输出结果
     *
     * @param dialogueId 对话id
     * @param outAuditStatus 输出审核状态
     * @param chatStatus 对话状态
     * @param msg 输出结果
     * @param recommendInfo 对话结果推荐信息（json格式）
     */
    void updateOutResult(Long dialogueId, Integer outAuditStatus, Integer chatStatus, String msg, String recommendInfo);

    /**
     * 更新会话输出结果
     *
     * @param dialogueId 对话id
     * @param outAuditStatus 输出审核状态
     * @param chatStatus 对话状态
     * @param msg 输出结果
     */
    void updateOutResult(Long dialogueId, OutAuditStatusEnum outAuditStatus, ChatStatusEnum chatStatus, String msg);

    /**
     * 更新会话输出结果
     *
     * @param dialogueId 对话id
     * @param outAuditStatus 输出审核状态
     * @param chatStatus 对话状态
     * @param msg 输出结果
     * @param recommendInfo 对话结果推荐信息（json格式）
     */
    void updateOutResult(Long dialogueId, OutAuditStatusEnum outAuditStatus, ChatStatusEnum chatStatus, String msg,
                         String recommendInfo);

    /**
     * 停止sse流式对话更新数据
     *
     * @param dialogueId 对话id
     */
    void updateOutResultStop(Long dialogueId);

}
