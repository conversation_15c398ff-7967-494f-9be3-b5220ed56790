package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserDriveConfigEntity;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.vo.ImportHtmlInfoVO;
import com.zyhl.yun.api.outer.domain.vo.ImportMailInfoVO;
import com.zyhl.yun.api.outer.domain.vo.ImportNoteInfoVO;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeLabelEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

/**
 * 知识库文件新增请求参数
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class ResourceTransferReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 知识库Id
     */
    private Long baseId;

    /**
     * 独立空间父级目录id, 指定父目录，调用先查询父目录ID
     */
    private String parentFileId;

    /**
     * 独立空间父级目录PATH, 指定父目录，调用先查询父目录PATH
     */
    private String parentFilePath;

    /**
     * 标签id，默认未分类
     *
     * @see KnowledgeLabelEnum
     */
    private String labelId = String.valueOf(KnowledgeLabelEnum.UNCLASSIFIED.getId());

    /**
     * 文件信息File
     * 个人云文档：fileId，name，type，category，size，fileExtension是必填字段；（RAG1.0）
     * 邮件/笔记：fileId为邮件/笔记的唯一值id，name为笔记/邮件的标题。（RAG2.2）
     */
    private List<File> fileList;

    /**
     * 资源类型
     *
     * @see KnowledgeResourceTypeEnum
     */
    private Integer resourceType = 0;

    /**
     * 邮件列表
     */
    private List<ImportMailInfoVO> mailList;

    /**
     * 笔记列表
     */
    private List<ImportNoteInfoVO> noteList;

    /**
     * 网页链接信息
     */
    private ImportHtmlInfoVO htmlInfo;

    private String ownerId;

    private Boolean retryFlag;

    private List<Long> uploadIds;

    private String taskId;

}
