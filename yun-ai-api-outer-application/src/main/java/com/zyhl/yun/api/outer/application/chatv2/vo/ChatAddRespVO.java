package com.zyhl.yun.api.outer.application.chatv2.vo;

import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueIntentionDTO;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.enums.chat.ChatAddFlowStatusEnum;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import lombok.Data;

/**
 * 聊天添加响应对象
 * 
 * <AUTHOR>
 * @date 2025-04-12
 */
@Data
public class ChatAddRespVO {

    /**
     * 当前对话id
     */
    private String dialogueId;

    /**
     * 当前会话Id
     */
    private String sessionId;

    /**
     * 输出意图指令
     */
    private DialogueIntentionDTO outputCommand;

    /**
     * 返回结果的标题
     * commands为“024”创建笔记和“027”创建语音笔记时，返回标题；
     * commands为”000“时，返回知识库开头；
     * commands为“028”邮件搜索时返回卡片标题；
     * commands为“012”-“018”、“020”-“023”和“028”搜索意图时，返回搜索结果的返回词
     */
    private String title;

    /**
     * 流式结果
     */
    private DialogueFlowResultVO flowResult;

    /**
     * 结束原因，当为stop时，表示该次对话已结束。默认stop
     *
     * @see ChatAddFlowStatusEnum
     */
    private String finishReason = ChatAddFlowStatusEnum.STOP.getStatus();

    /**
     * 结束语
     */
    private String ending;

    /**
     * 回答声明
     */
    private String responseStatement;

    /**
     * 引导文案
     */
    private LeadCopyVO leadCopy;

    /**
     * 对话结果推荐，当对话信息不为空时返回。展示在对话和框的中间一层。
     */
    private DialogueRecommendVO middleRecommend = new DialogueRecommendVO();

    /**
     * 对话结果推荐，当对话信息不为空时返回
     */
    private DialogueRecommendVO recommend = new DialogueRecommendVO();
    

    /**
     * 用户智能相册授权开关状态，当且仅当用户的意图commands为“012”和“018”时才返回，
     * 0--未开启
     * 1--已开启
     * （仅新底座用户才返回）
     */
    private Integer aiAlbumStatus;

    /**
     * 用户智能文档搜索授权开关状态，当且仅当用户的意图commands为“013”和“018”时才返回，
     * 0--未开启
     * 1--已开启
     * （仅新底座用户才返回）
     */
    private Integer aiDocStatus;


    public ChatAddRespVO setFlowResult(DialogueFlowResultVO dialogueFlowResultVO) {
        this.flowResult = dialogueFlowResultVO;
        return this;
    }

    public DialogueFlowResultVO getFlowResult() {
        if (this.flowResult == null) {
            this.flowResult = new DialogueFlowResultVO();
        }

        return this.flowResult;
    }

    public ChatAddRespVO setOutputCommandCode(String intentionCode) {
        this.outputCommand = new DialogueIntentionDTO(intentionCode);
        return this;
    }

    public ChatAddRespVO setOutputCommandCode(String intentionCode, String subIntentionCode) {
        this.outputCommand = new DialogueIntentionDTO(intentionCode, subIntentionCode, null);
        return this;
    }

	public void setOutputCommandVO(DialogueIntentionVO.IntentionInfo intentionInfo) {
		this.outputCommand = new DialogueIntentionDTO(intentionInfo.getIntention(), intentionInfo.getSubIntention(),
				intentionInfo.getArgumentMap());
	}

	public ChatAddRespVO setOutputCommandVO(DialogueIntentionVO intentionVO) {
        IntentionInfo mainIntentionVO = DialogueIntentionVO.getMainIntention(intentionVO);
        if (null != mainIntentionVO) {
            this.outputCommand = new DialogueIntentionDTO(mainIntentionVO);
        }
        return this;
    }

    public ChatAddRespVO processingFlowResult(DialogueFlowResultVO flowResult) {
        this.finishReason = ChatAddFlowStatusEnum.PROCESSING.getStatus();
        this.flowResult = flowResult;
        return this;
    }
}