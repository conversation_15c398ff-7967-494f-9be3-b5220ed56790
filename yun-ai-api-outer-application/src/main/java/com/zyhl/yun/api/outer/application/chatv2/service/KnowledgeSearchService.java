package com.zyhl.yun.api.outer.application.chatv2.service;

import java.util.List;

import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.pojo.KnowledgeFlowInfo;
import com.zyhl.yun.api.outer.domain.valueobject.KnowledgeSearchInfo;

/**
 * 知识库搜索服务接口
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
public interface KnowledgeSearchService {

    /**
     * 设置知识库流式信息
     * @param handleDTO
     * @return
     */
    KnowledgeFlowInfo setKnowledgeFlowInfo(ChatAddHandleDTO handleDTO);
    
    /**
     * 知识搜索，多线程执行
     *
     * @param handleDTO
     * @param historyList
     */
    void knowledgeSearch(ChatAddHandleDTO handleDTO, List<TextModelMessageDTO> historyList);


    /**
     * 知识召回数据
     *
     * @param handleDTO
     * @param historyList
     */
    List<KnowledgeSearchInfo> knowledgeCallData(ChatAddHandleDTO handleDTO, List<TextModelMessageDTO> historyList);
}
