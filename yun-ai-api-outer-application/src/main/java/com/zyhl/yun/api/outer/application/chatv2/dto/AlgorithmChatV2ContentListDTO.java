package com.zyhl.yun.api.outer.application.chatv2.dto;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.BaseDTO;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatContentSortTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 历史对话列表查询接口-DTO
 * @Author: WeiJingKun
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class AlgorithmChatV2ContentListDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -5939748195506127109L;

    /** 会话id */
    private Long sessionId;

    /**
     * 渠道来源
     * 详见，配置文件：source-channels
     */
    @NotBlank(message = "渠道来源不能为空")
    private String sourceChannel;

    /**
     * 应用类型，英文简称。
     * @see ApplicationTypeEnum
     */
    private String applicationType;

    /** 分页信息 */
    private PageInfoDTO pageInfo;
    
    /**
     * 排序方式（不填默认1）
     * 1--按照对话创建时间倒序排序
     * 2--按照对话创建时间正序排序
     */
    private Integer sortType;

    /**
     * 对话ID清单
     * 如果输入对话ID，则返回对应的对话结果
     */
    private List<Long> dialogueIdList;

    /**
     * 参数校验
     * @Author: WeiJingKun
     */
    public void validate() {
        /** 检查登录的userId */
        checkTokenUserId();

        /** 会话ID（与applicationType必须填一个，且不能同时填两个） */
        // 会话id为null
        if(sessionId == null){
            // applicationType为空，则抛出异常
            if(CharSequenceUtil.isBlank(applicationType)){
                log.error("会话id为null，应用类型不能为空");
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS.getResultCode(), "应用类型不能为空");
            }
        } else {
            // 会话id不为null，则applicationType必须为空
            if(CharSequenceUtil.isNotBlank(applicationType)){
                log.error("会话id不为null，应用类型必须为空");
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS.getResultCode(), "应用类型必须为空");
            }
        }

        /** applicationType存在，则判断是否有效 */
        if(CharSequenceUtil.isNotBlank(applicationType)){
            if(!ApplicationTypeEnum.isExist(applicationType)){
                log.error("应用类型参数无效");
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_APPLICATION_TYPE_INVALID);
            }
        }

        /** 分页信息校验 */
        if(null != pageInfo){
            pageInfo.validate();
        }
        
        /** 默认 1-【创建时间】倒序排序 */
        if(sortType == null){
            sortType = ChatContentSortTypeEnum.CREATE_TIME_DESC.getCode();
        }
    }

}
