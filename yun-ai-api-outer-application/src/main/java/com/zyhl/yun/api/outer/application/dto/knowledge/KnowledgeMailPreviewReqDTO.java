package com.zyhl.yun.api.outer.application.dto.knowledge;


import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import com.zyhl.yun.api.outer.enums.ModelTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 知识库邮件预览请求 DTO
 * @date 2025/4/17 16:52
 */
@Data
@NoArgsConstructor
public class KnowledgeMailPreviewReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 渠道来源，参考 ChannelId 枚举值（必填）
     */
    @NotBlank(message = "渠道来源 sourceChannel 不能为空")
    private String sourceChannel;

    /**
     * 用户Id，默认从 token 获取，第三方平台调用时必填（选填）
     */
    private String userId;

    /**
     * 知识库ID（必填）
     */
    @NotBlank(message = "知识库ID baseId 不能为空")
    private String baseId;

    /**
     * 资源ID（必填）
     */
    @NotBlank(message = "资源ID resourceId 不能为空")
    private String resourceId;

    /**
     * 返回内容的模式（必填）
     */
    @NotNull(message = "返回内容模式 model 不能为空")
    private ModelTypeEnum model;


    /**
     * 设置 model 值，并进行合法性校验
     *
     * @param model 模式值
     */
    public void setModel(String model) {
        this.model = ModelTypeEnum.fromValue(model);
    }
}
