package com.zyhl.yun.api.outer.application.util;

import lombok.extern.slf4j.Slf4j;

import java.time.*;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 日期工具类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-20
 */
@Slf4j
public class DateUtils {

    /**
     * 判断是否为 Thu Jan 01 00:00:00 CST 1970
     *
     * @param date 待判断的日期
     * @return 如果是 返回 true，否则返回 false
     */
    public static boolean isEpochDate(Date date) {

        if (Objects.isNull(date)) {
            return false;
        }
        // 将输入日期转换为 UTC 时间
        Instant instant = date.toInstant();
        ZonedDateTime utcDateTime = instant.atZone(ZoneId.of("UTC"));

        ZonedDateTime epochDateTime = ZonedDateTime.of(1969, 12, 31, 16, 0, 0, 0, ZoneId.of("UTC"));
        return utcDateTime.equals(epochDateTime);
    }
}
