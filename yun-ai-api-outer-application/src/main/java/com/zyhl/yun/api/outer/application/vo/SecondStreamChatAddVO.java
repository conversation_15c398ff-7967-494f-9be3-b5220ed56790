package com.zyhl.yun.api.outer.application.vo;

import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 二次流式对话VO
 *
 * <AUTHOR>
 * @date 2024/12/16 11:31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SecondStreamChatAddVO {

    /**
     * 对话id
     */
    private String dialogueId;

    /**
     * 意图指令
     */
    private String commands;

    /**
     * 邮件信息VO
     */
    private MailInfoVO mailInfo;
}
