package com.zyhl.yun.api.outer.application.service;

import com.zyhl.yun.api.outer.domain.aggregate.PictureFileAggregate;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/20 15:04
 */
public interface MemoryAlbumFileFilterService {

    /**
     * 默认筛选规则处理
     * 根据拍摄时间进行排序,取拍摄时间最靠前的图片文件列表
     *
     * @param fileAggregateList 文件
     * @param searchSize 单个分组
     * @return 文件id列表
     */
    List<String> defaultFilterFile(List<PictureFileAggregate> fileAggregateList, Integer searchSize);

    /**
     * 按年筛选规则处理
     * 根据拍摄时间按照年份进行分组,组内根据评分进行排序
     *
     * @param fileAggregateList 文件
     * @param searchSize 单个分组取舍长度
     * @return 文件id列表
     */
    List<String> yearFilterFile(List<PictureFileAggregate> fileAggregateList, Integer searchSize);

    /**
     * 按季度筛选规则处理
     * 根据拍摄时间按照嫉妒进行分组,组内根据评分进行排序
     *
     * @param fileAggregateList 文件
     * @param searchSize 单个分组取舍长度
     * @return 文件id列表
     */
    List<String> seasonFilterFile(List<PictureFileAggregate> fileAggregateList, Integer searchSize);

    /**
     * 按月份筛选规则处理
     * 根据拍摄时间按照月份进行分组,组内根据评分进行排序
     *
     * @param fileAggregateList 文件列表
     * @param searchSize 单个分组取舍长度
     * @return 文件id列表
     */
    List<String> monthFilterFile(List<PictureFileAggregate> fileAggregateList, Integer searchSize);

    /**
     * 按周筛选规则处理
     * 根据拍摄时间按照周进行分组,组内根据评分进行排序
     *
     * @param fileAggregateList 文件列表
     * @param searchSize 单个分组取舍长度
     * @return 文件id列表
     */
    List<String> weekFilterFile(List<PictureFileAggregate> fileAggregateList, Integer searchSize);
}
