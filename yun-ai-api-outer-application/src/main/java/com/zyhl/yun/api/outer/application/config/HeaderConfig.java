package com.zyhl.yun.api.outer.application.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 请求头白名单配置
 * @author: chenkai
 */
@Component
@ConfigurationProperties(prefix = "headers-allow")
@Data
public class HeaderConfig {

    /**
     * 请求头白名单
     */
    private Map<String, Boolean> headerList = new HashMap<>();

    /**
     * 日志白名单
     */
    private List<String> logWhiteList = new ArrayList<>();

    /**
     * 是否开启日志
     */
    private Boolean enableLog = false;

    /**
     * 过滤掉yaml配置为 false 的配置key
     */
    private List<String> availableHeaders = new ArrayList<>();

    @PostConstruct
    public void init() {
        availableHeaders = headerList.entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());
    }
}
