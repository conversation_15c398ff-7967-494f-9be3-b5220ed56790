package com.zyhl.yun.api.outer.application.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.vo.CheckResultVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.OwnerDriveClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.properties.OwnerDriveProperties;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDriveFileCompleteReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDriveFileCreateReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDriveFileReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDriveReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.resp.OwnerDriveFileCreateRespDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveFileVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.DriveVO;
import com.zyhl.hcy.yun.ai.common.platform.third.enums.ThirdCommonResultCode;
import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.PojoConverter;
import com.zyhl.yun.api.outer.application.dto.FileCompleteDTO;
import com.zyhl.yun.api.outer.application.dto.FileCreateDTO;
import com.zyhl.yun.api.outer.application.dto.GetSharpUploadUrlReqDTO;
import com.zyhl.yun.api.outer.application.service.WeChatFileService;
import com.zyhl.yun.api.outer.application.service.mq.KnowledgeDispatchTaskMqService;
import com.zyhl.yun.api.outer.config.KnowledgePersonalProperties;
import com.zyhl.yun.api.outer.config.FileCheckConfig;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeUploadEntity;
import com.zyhl.yun.api.outer.domain.vo.FileCompleteVO;
import com.zyhl.yun.api.outer.domain.vo.FileCreateVO;
import com.zyhl.yun.api.outer.domain.vo.GetSharpUploadUrlReqVO;
import com.zyhl.yun.api.outer.domainservice.CheckSystemDomainService;
import com.zyhl.yun.api.outer.domainservice.UserKnowledgeDomainService;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.*;
import com.zyhl.yun.api.outer.external.YunDiskExternalService;
import com.zyhl.yun.api.outer.external.ose.enums.UserBelongsPlatformEnum;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description}
 * <p>
 *
 * <AUTHOR>
 * @since 4月17 2025
 */
@Slf4j
@Service
public class WeChatFileServiceImpl implements WeChatFileService {

    @Resource
    private UidGenerator uidGenerator;

    @Resource
    private PojoConverter pojoConverter;
    @Resource
    private OwnerDriveClient ownerDriveClient;
    @Resource
    private UserKnowledgeFileRepository userKnowledgeFileRepository;
    @Resource
    private KnowledgeDispatchTaskMqService knowledgeDispatchTaskMqService;
    @Resource
    private UserKnowledgeRepository userKnowledgeRepository;
    @Resource
    private OwnerDriveProperties ownerDriveProperties;
    @Resource
    private UserKnowledgeDomainService userKnowledgeDomainService;
    @Resource
    private YunDiskExternalService yunDiskExternalService;

  @Resource
  protected CheckSystemDomainService checkSystemDomainService;

  @Resource
  protected FileCheckConfig fileCheckConfig;

  @Resource
  protected KnowledgePersonalProperties knowledgePersonalProperties;

  @Override
  public FileCreateVO createFile(FileCreateDTO dto) {
    if (UserBelongsPlatformEnum.OSE.getBelongsPlatform().equals(RequestContextHolder.getBelongsPlatform())) {
      log.info("个人知识库暂不支持老底座用户");
      throw new YunAiBusinessException(ResultCodeEnum.NOT_TARGET_USER);
    }

        dto.checkTokenUserId();
        String baseId = dto.getBaseId();
        String userId = dto.getUserId();
        //校验空间
        checkOwnerDriveSize(userId, dto.getSize());
        UserKnowledgeEntity userKnowledgeEntity = getUserKnowledgeEntity(baseId);
        OwnerDriveFileCreateReqDTO fileCreateReqDTO = pojoConverter.toFileCreateReqDTO(dto);
        String clientInfo = RequestContextHolder.getClientInfo();

    if(StrUtil.isBlank(fileCreateReqDTO.getParentFileId())){
      fileCreateReqDTO.setParentFileId(userKnowledgeEntity.getFolderId());
    }
    log.info("【调用独立空间】，入参：{}", fileCreateReqDTO);
    OwnerDriveFileCreateRespDTO file = ownerDriveClient.uploadFileFirst(fileCreateReqDTO,
        clientInfo, dto.getSourceChannel());
    // 文件名敏感校验
    fileCheck(file.getFileName(), userId);
    //秒传逻辑
    if (file.getRapidUpload()) {
      //知识库id
      Long id = userKnowledgeEntity.getId();
      //新增文件
      OwnerDriveFileReqDTO reqDTO = new OwnerDriveFileReqDTO();
      reqDTO.setFileId(file.getFileId());
      reqDTO.setUserId(dto.getUserId());
      OwnerDriveFileVO ownerDriveFileVO = ownerDriveClient.getFile(reqDTO);
      UserKnowledgeFileEntity entity = getUserKnowledgeFileEntity(ownerDriveFileVO, dto.getUserId(), id);
      //导入到指定知识库
      userKnowledgeFileRepository.add(entity);
      log.info("秒传上传，新增文件：{}", entity);
      //发起微信文件的文档RAG解析任务
      Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();
      DriveVO drive = yunDiskExternalService.getDrive(RequestContextHolder.getUserId(),
          belongsPlatform);
      entity.setDriveId(drive.getDriveId());
      knowledgeDispatchTaskMqService.sendTaskMqV2(ListUtil.toList(entity));
    }
    FileCreateVO fileCreateVO = pojoConverter.toFileCreateVO(file);
    fileCreateVO.setBaseId(String.valueOf(userKnowledgeEntity.getId()));
    return fileCreateVO;
  }

  /**
   * 文件名敏感校验
   * @param fileName 文件名
   * @param userId 用户iD
   */
  private void fileCheck(String fileName, String userId) {

    boolean enabled = fileCheckConfig.isEnabled();
    log.info("createFile fileCheckConfig:{}",fileCheckConfig);
    if(Boolean.FALSE.equals(enabled)){
      // 开关打开
      // 标题涉敏校验
      long uid = uidGenerator.getUID();
      CheckResultVO resultVo = checkSystemDomainService.checkPlatformAndLocal(uid, userId, fileName);
      boolean fail = CheckResultVO.isFail(resultVo);
      if(fail){
        log.error("createFile送审失败,userId:{},fileName:{}", userId, fileName);
        throw new YunAiBusinessException(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
      }
    }
  }

  @Override
  public FileCompleteVO completeFile(FileCompleteDTO dto) {
    if(UserBelongsPlatformEnum.OSE.getBelongsPlatform().equals(RequestContextHolder.getBelongsPlatform())){
      log.info("个人知识库暂不支持老底座用户");
      throw new YunAiBusinessException(ResultCodeEnum.NOT_TARGET_USER);
    }
    dto.checkTokenUserId();
    String baseId = dto.getBaseId();
    UserKnowledgeEntity userKnowledgeEntity = getUserKnowledgeEntity(baseId);
    dto.setBaseId(String.valueOf(userKnowledgeEntity.getId()));
    String clientInfo = RequestContextHolder.getClientInfo();
    OwnerDriveFileCompleteReqDTO fileCompleteReqDTO = pojoConverter.toFileCompleteReqDTO(dto);

        log.info("【调用独立空间】，入参：{}", fileCompleteReqDTO);
        OwnerDriveFileVO ownerDriveFileVO = ownerDriveClient.uploadFileThird(fileCompleteReqDTO,
                clientInfo, dto.getSourceChannel());
        validFile(ownerDriveFileVO);
        UserKnowledgeFileEntity entity = getUserKnowledgeFileEntity(ownerDriveFileVO, dto.getUserId(),
                userKnowledgeEntity.getId());
        //导入到指定知识库 判断文件id 是否已经入库
        UserKnowledgeFileEntity dbFileEntity = userKnowledgeFileRepository.selectByFileId(entity.getUserId(), entity.getFileId());
        if (Objects.isNull(dbFileEntity)) {
            userKnowledgeFileRepository.add(entity);
        }

        //发起微信文件的文档RAG解析任务
        Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();
        DriveVO drive = yunDiskExternalService.getDrive(RequestContextHolder.getUserId(),
                belongsPlatform);
        entity.setDriveId(drive.getDriveId());
        knowledgeDispatchTaskMqService.sendTaskMqV2(ListUtil.toList(entity));
        return pojoConverter.toFileCompleteVO(ownerDriveFileVO);
    }

    private void validFile(OwnerDriveFileVO ownerDriveFileVO) {
        String ext = ObjectUtil.isNotEmpty(ownerDriveFileVO.getFileExtension()) ? ownerDriveFileVO.getFileExtension().toLowerCase() : "";
        if (ObjectUtil.isEmpty(ext)) {
            log.info("【知识库导入】文件格式为空，ext：{}", ext);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_FILE_FORMAT_SUPPORTED);
        } else if (!knowledgePersonalProperties.getExtList().contains(ext)) {
            log.info("【知识库导入】文件格式不支持，ext：{}", ext);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_FILE_FORMAT_SUPPORTED);
        }

        Long size = ownerDriveFileVO.getSize() == null ? 0L : ownerDriveFileVO.getSize();
        if (FileCategoryEnum.IMAGE.getKnowledgeCategory().equals(FileCategoryEnum.getKnowledgeCategory(ownerDriveFileVO.getCategory()))) {
            if (knowledgePersonalProperties.getImageSize().compareTo(size) < 0) {
                // 图片大小是否支持
                log.info("【知识库导入】图片过大，fileId：{}，图片大小：{}，限制大小：{}", ownerDriveFileVO.getFileId(), ownerDriveFileVO.getSize(), knowledgePersonalProperties.getImageSize());
                throw new YunAiBusinessException(ResultCodeEnum.FILE_SIZE_LARGE);
            }
        } else if (knowledgePersonalProperties.getSize().compareTo(size) < 0) {
            log.info("【知识库导入】文件过大，文件大小：{}，限制大小：{}", size, knowledgePersonalProperties.getSize());
            throw new YunAiBusinessException(ResultCodeEnum.FILE_SIZE_LARGE);
        }
    }

    @Override
    public GetSharpUploadUrlReqVO getUploadUrl(GetSharpUploadUrlReqDTO dto) {

        log.info("进入【获取文件上传地址】流程");
        if (CharSequenceUtil.isEmpty(dto.getUserId())) {
            dto.setUserId(RequestContextHolder.getUserId());
        }
        try {
            return pojoConverter.toGetSharpUploadUrlReqVO(
                    ownerDriveClient.getUploadUrl(pojoConverter.toOwnerDriveGetUploadUrlReqDTO(dto),
                            RequestContextHolder.getClientInfo(), dto.getSourceChannel()));
        } catch (Exception e) {
            if (e instanceof YunAiBusinessException
                    && ((YunAiBusinessException) e).getCode().equals(ThirdCommonResultCode.DOWNSTREAM_SERVICES_EXCEPTION.getResultCode())) {
                throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
            }
            log.error("【获取文件上传地址】流程执行异常：{}", e.getMessage(), e);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_CALL_EXCEPTION.getResultCode(), e.getMessage());
        }
    }

    private UserKnowledgeFileEntity getUserKnowledgeFileEntity(OwnerDriveFileVO ownerDriveFileVO,
                                                               String userId, Long baseId) {
        UserKnowledgeFileEntity userKnowledgeFileEntity = new UserKnowledgeFileEntity();
        userKnowledgeFileEntity.setId(uidGenerator.getUID());
        userKnowledgeFileEntity.setParentFileId(ownerDriveFileVO.getParentFileId());
        userKnowledgeFileEntity.setUserId(userId);
        userKnowledgeFileEntity.setFileId(ownerDriveFileVO.getFileId());
        userKnowledgeFileEntity.setBaseId(baseId);
        userKnowledgeFileEntity.setFileName(ownerDriveFileVO.getName());
        userKnowledgeFileEntity.setHashName(ownerDriveFileVO.getContentHashAlgorithm());
        userKnowledgeFileEntity.setHashValue(ownerDriveFileVO.getContentHash());
        userKnowledgeFileEntity.setFileType(FileTypeEnum.FILE.getKnowledgeFileType());
        userKnowledgeFileEntity.setFileSize(ownerDriveFileVO.getSize());
        userKnowledgeFileEntity.setExtension(ownerDriveFileVO.getFileExtension());
        userKnowledgeFileEntity.setFileUpdatedAt(getDate(ownerDriveFileVO.getUpdatedAt()));
        userKnowledgeFileEntity.setFileCreatedAt(getDate(ownerDriveFileVO.getCreatedAt()));
        userKnowledgeFileEntity.setDelFlag(KnowledgeStatusEnum.NORMAL.getStatus());
        userKnowledgeFileEntity.setAiStatus(FileProcessStatusEnum.UNPROCESSED.getStatus());
        userKnowledgeFileEntity.setFromResourceType(KnowledgeResourceTypeEnum.PERSONAL_FILE.getCode());
        userKnowledgeFileEntity.setOwnerType(OwnerTypeEnum.AI.getOwnerValue());
        userKnowledgeFileEntity.setPaasCode(String.valueOf(RequestContextHolder.getBelongsPlatform()));
        userKnowledgeFileEntity.setCategory(
                FileCategoryEnum.getKnowledgeCategory(ownerDriveFileVO.getCategory()));
        // 设置图片的排序为15
        if (FileCategoryEnum.IMAGE.getKnowledgeCategory().equals(FileCategoryEnum.getKnowledgeCategory(ownerDriveFileVO.getCategory()))) {
            userKnowledgeFileEntity.setSortType(15);
        }

        userKnowledgeFileEntity.setOwnerId(ownerDriveProperties.getDefaultOwnerid());
        //父级文件路径 独立空间的父目录路径使用   目录ID
        userKnowledgeFileEntity.setParentFilePath(ownerDriveFileVO.getParentFileId());

        UserKnowledgeFileEntity fileEntity = userKnowledgeFileRepository.selectByFileId(userId, ownerDriveFileVO.getParentFileId());
        if (Objects.nonNull(fileEntity)) {
            userKnowledgeFileEntity.setParentFilePath(fileEntity.getParentFilePath() + "/" + fileEntity.getFileId());
        }

        return userKnowledgeFileEntity;
    }

    private Date getDate(String date) {
        return CharSequenceUtil.isEmpty(date) ? null
                : DateUtil.parse(date, DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
    }

    private UserKnowledgeEntity getUserKnowledgeEntity(String baseId) {
        UserKnowledgeEntity userKnowledgeEntity = null;
        if (CharSequenceUtil.isNotBlank(baseId)) {
            userKnowledgeEntity = userKnowledgeRepository.selectById(
                    Long.valueOf(baseId));
            //1、如果为空，则抛异常
            if (Objects.isNull(userKnowledgeEntity)) {
                throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_CONTENT_EMPTY);
            }
        } else {
            //2、如果不传，则查询默认知识库，如果有则使用默认知识库，如果没有就创建默认知识库
            userKnowledgeEntity = userKnowledgeDomainService.createDefaultKnowledge();
        }
        return userKnowledgeEntity;
    }

    @Override
    public void checkOwnerDriveSize(String userId, Long fileSize) {
        OwnerDriveVO vo = ownerDriveClient.getOwnerDrive(OwnerDriveReqDTO.builder()
                .userId(userId)
                .appChannel(RequestContextHolder.getAppChannel())
                .clientInfo(RequestContextHolder.getClientInfo())
                .build());
        log.info("用户独立空间信息:{}，待上传文件大小：{}", vo, fileSize);
        Long usedSize = vo.getUsedSize();
        if (Objects.nonNull(fileSize)) {
            usedSize += fileSize;
        }
        if (vo.getTotalSize() - usedSize < 0) {
            throw new YunAiBusinessException(ResultCodeEnum.LACK_OF_SPACE);
        }
    }
}
