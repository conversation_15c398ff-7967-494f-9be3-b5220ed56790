package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 知识库文件获取任务结果请求参数
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class KnowledgeFileTaskResultReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 文档上传任务ID
     */
    private String taskId;

}
