package com.zyhl.yun.api.outer.application.dto;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.enums.BaseResultCodeEnum;
import com.zyhl.yun.api.outer.constants.RegConst;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * AI助手查询对话状态的请求参数
 *
 * <AUTHOR>
 */
@Slf4j
@Data
public class AiAssistantStatusReqDTO extends BaseDTO implements Serializable {

    /**
     * 对话id
     */
    private String dialogueId;

    /**
     * 参数校验
     *
     * @return
     */
    public AbstractResultCode checkParam() {
        if (CharSequenceUtil.isEmpty(dialogueId)) {
            log.info("对话id为空");
            return BaseResultCodeEnum.ERROR_PARAMS;
        } else if (!dialogueId.matches(RegConst.REG_DATA_STR)) {
            log.info("对话id必须是数字：{}", dialogueId);
            return BaseResultCodeEnum.ERROR_PARAMS;
        }

        return super.checkUserId();
    }
}
