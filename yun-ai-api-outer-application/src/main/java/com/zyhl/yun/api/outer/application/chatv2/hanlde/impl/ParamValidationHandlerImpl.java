package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.zyhl.hcy.commons.enums.CommonResultCode;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.StringUtil;
import com.zyhl.yun.api.outer.application.chatv2.dto.*;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.dto.DialogueSortInfoDTO;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.application.service.AlgorithmAiRegisterService;
import com.zyhl.yun.api.outer.application.service.chat.ChatApplicationTypeService;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.constants.RegConst;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.valueobject.MailInfo;
import com.zyhl.yun.api.outer.domain.valueobject.PersonalKnowledgeBase;
import com.zyhl.yun.api.outer.domain.valueobject.PersonalKnowledgeResource;
import com.zyhl.yun.api.outer.domain.vo.ChatApplicationType;
import com.zyhl.yun.api.outer.enums.*;
import com.zyhl.yun.api.outer.enums.chat.DialogueCommandTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.TalkTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.search.ImageSortTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeBaseTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.repository.AlgorithmChatMessageRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 参数校验
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ParamValidationHandlerImpl extends AbstractChatAddV2Handler {

    @Resource
    private AlgorithmAiRegisterService algorithmAiRegisterService;
    @Resource
    private AlgorithmChatMessageRepository algorithmChatMessageRepository;
    @Resource
    private ChatApplicationTypeService chatApplicationTypeService;
    @Resource
    protected SourceChannelsProperties sourceChannelsProperties;
    @Resource
    private UserKnowledgeFileRepository userKnowledgeFileRepository;

    /**
     * 当前执行顺序
     */
    private final ExecuteSort thisExecuteSort = ExecuteSort.PARAM_VALIDATION;

    /**
     * 表情字符，需要加提示词：以中文回复
     */
    private static final String EMOJI_WITH_CN = "以中文回复";

    /**
     * 获取当前执行顺序
     *
     * @return 执行顺序
     */
    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        //支持的业务初始化
        List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        thisBusinessTypes.add(ChatBusinessTypeEnum.CLOUD_PHONE);
        thisBusinessTypes.add(ChatBusinessTypeEnum.NOTE);
        thisBusinessTypes.add(ChatBusinessTypeEnum.MESSAGE_5G);
        this.setBusinessTypes(thisBusinessTypes);
    }

    /**
     * 处理器是否执行
     *
     * @param handleDTO 请求dto
     * @return false-不执行
     */
    @Override
    public boolean execute(ChatAddHandleDTO handleDTO) {
        return true;
    }

    /**
     * 参数校验的主要逻辑
     *
     * @param handleDTO 处理DTO
     * @return 校验结果，始终返回true
     */
    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        log.info("进入{}", thisExecuteSort.getDesc());
        ChatAddReqDTO dto = handleDTO.getReqDTO();

        // 用户id校验
        dto.checkTokenUserId();

        // 报名校验
        if (!algorithmAiRegisterService.checkAiAssistant(dto.getUserId())) {
            log.error("用户未报名，用户id：{}", dto.getUserId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_FORBIDDEN);
        }

        // 会话id
        if (!CharSequenceUtil.isEmpty(dto.getSessionId())) {
            if (!dto.getSessionId().matches(RegConst.REG_ID_STR)) {
                log.error("会话id格式不正确，会话id：{}", dto.getSessionId());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            } else if (algorithmChatMessageRepository.queryBySessionId(dto.getSessionId(), dto.getUserId()) == null) {
                if (ApplicationTypeEnum.isSpeedRead(dto.getApplicationType())) {
                    handleDTO.setSaveMessage(true);
                    log.info("图书快速阅读应用类型，不校验会话id不存在，将入库session表");
                } else {
                    log.error("会话id不存在，会话id：{}", dto.getSessionId());
                    throw new YunAiBusinessException(ResultCodeEnum.SESSION_INFO_NOT_FOUND);
                }
            }
        }

        // 应用类型
        if (!ApplicationTypeEnum.isExist(dto.getApplicationType())) {
            log.error("应用类型不存在，应用类型：{}", dto.getApplicationType());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        // 应用id
        if (ApplicationTypeEnum.isChat(dto.getApplicationType())) {
            dto.setApplicationId("");
        } else if (ApplicationTypeEnum.isIntelligen(dto.getApplicationType())) {
            if (CharSequenceUtil.isEmpty(dto.getApplicationId())) {
                log.error("应用id为空，应用id：{}", dto.getApplicationId());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }
            ChatApplicationType type = chatApplicationTypeService.getByApplicationId(dto.getApplicationId());
            if (type == null || !ApplicationTypeEnum.isIntelligen(dto.getApplicationType())) {
                log.error("应用id不存在，应用id：{}，应用类型：{}", dto.getApplicationId(), dto.getApplicationType());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }
        }

        // 对话内容信息
        contentValid(handleDTO);

        return true;
    }

    /**
     * 内容对象校验
     *
     * @param handleDTO 请求参数
     */
    private void contentValid(ChatAddHandleDTO handleDTO) {
        DialogueInputInfoDTO inputInfoDTO = handleDTO.getInputInfoDTO();

        // 对话内容信息
        if (Objects.isNull(inputInfoDTO)) {
            log.error("内容参数为空");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        // 输入时间
        if (CharSequenceUtil.isEmpty(inputInfoDTO.getInputTime())
                || !inputInfoDTO.getInputTime().matches(RegConst.TIME_RFC3339)) {
            log.error("输入时间格式错误：{}", inputInfoDTO.getInputTime());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        // 渠道来源
        ChatAddReqDTO reqDTO = handleDTO.getReqDTO();
        String sourceChannel = reqDTO.getSourceChannel();
        if (CharSequenceUtil.isEmpty(sourceChannel)) {
            log.error("来源渠道为空，来源渠道：{}", sourceChannel);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        } else if (!sourceChannelsProperties.isExist(sourceChannel)) {
            log.error("来源渠道不存在，来源渠道：{}", sourceChannel);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        // 根据渠道，set助手类型
        String businessCode = sourceChannelsProperties.getCode(sourceChannel);
        AssistantEnum assistantEnum = AssistantEnum.getByCode(businessCode);
        if (ObjectUtil.isNull(assistantEnum)) {
            log.error("助手类型不存在，来源渠道：{}", sourceChannel);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        handleDTO.setAssistantEnum(assistantEnum);
        if (TalkTypeEnum.isTask(inputInfoDTO.getDialogueType())) {
            log.info("任务对话类型不校验资源：{}", sourceChannel);
        } else {
            // 资源类型校验
            resourceTypeValid(inputInfoDTO);
        }

        // 指令
        DialogueIntentionDTO command = inputInfoDTO.getCommand();
        if (command == null || CharSequenceUtil.isEmpty(command.getCommand())) {
            if (CharSequenceUtil.isEmpty(inputInfoDTO.getDialogue())) {
                if (handleDTO.isReqResourceDocSse()) {
                    log.info("对话指令和对话内容能同时为空 isReqResourceDocSse");
                } else {
                    log.error("对话指令和对话内容不能同时为空，指令：{}，对话内容：{}", JsonUtil.toJson(command), inputInfoDTO.getDialogue());
                    throw new YunAiBusinessException(CommonResultCode.ERROR_PARAMS);
                }
            }
        } else if (!DialogueIntentionEnum.isExist(command.getCommand())) {
            log.error("对话指令不存在，指令：{}", command.getCommand());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INTENTION_CODE);
        }

        // 命令类型
        if (!DialogueCommandTypeEnum.isExist(inputInfoDTO.getCommandType())) {
            log.error("命令类型不存在，命令类型：{}", inputInfoDTO.getCommandType());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        // 语义搜图排序
        DialogueSortInfoDTO sortInfo = inputInfoDTO.getSortInfo();
        if (sortInfo != null && sortInfo.getImageSortType() != null && !ImageSortTypeEnum.isExist(sortInfo.getImageSortType())) {
            log.error("图片语义搜索排序方式不存在，排序方式：{}", sortInfo.getImageSortType());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        // 扩展信息
        if (CharSequenceUtil.isNotEmpty(inputInfoDTO.getExtInfo())) {
            try {
                // 尝试将 extInfo 解析为 JSON 对象
                new JSONObject(inputInfoDTO.getExtInfo());
            } catch (Exception e) {
                log.error("扩展信息解析异常，扩展信息：{}，异常信息：", inputInfoDTO.getExtInfo(), e);
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }
        }

        // 纯表情或者纯数字并且没输入prompt
        boolean blankPrompt = CharSequenceUtil.isBlank(inputInfoDTO.getPrompt());
        boolean isNumericOrEmoji = StringUtil.isNumeric(inputInfoDTO.getDialogue()) || StringUtil.allEmoji(inputInfoDTO.getDialogue());
        if (blankPrompt && isNumericOrEmoji) {
            log.info("对话内容为纯数字或纯表情并且提示词为空，默认添加提示词：{}", EMOJI_WITH_CN);
            inputInfoDTO.setPrompt(EMOJI_WITH_CN);
        }

        // 初始化
        handleDTO.init();

        // 重置上下文信息，包括业务类型、助手枚举、来源渠道以及接口版本信息
        contextResetHandle(handleDTO, reqDTO, inputInfoDTO);
    }

    /**
     * 重置上下文信息，包括业务类型、助手枚举、来源渠道以及接口版本信息
     *
     * @param handleDTO     处理DTO
     * @param reqDto        请求DTO
     * @param dialogueInput 对话输入信息DTO
     */
    private void contextResetHandle(ChatAddHandleDTO handleDTO, ChatAddReqDTO reqDto, DialogueInputInfoDTO dialogueInput) {
        // 业务信息
        RequestContextHolder.BusinessInfo businessInfo = new RequestContextHolder.BusinessInfo();
        businessInfo.setBusinessType(handleDTO.getBusinessType());
        businessInfo.setAssistantEnum(handleDTO.getAssistantEnum());
        businessInfo.setSourceChannel(reqDto.getSourceChannel());
        RequestContextHolder.setBusinessInfo(businessInfo);

        // 接口版本
        DialogueVersionInfoDTO versionInfo = dialogueInput.getVersionInfo();
        if (versionInfo != null) {
            RequestContextHolder.getHeaderParams().setH5Version(versionInfo.getH5Version());
            RequestContextHolder.getHeaderParams().setPcVersion(versionInfo.getPcVersion());
        }
    }

    /**
     * 资源类型校验
     *
     * @param inputInfo 对话输入信息DTO
     */
    private void resourceTypeValid(DialogueInputInfoDTO inputInfo) {
        DialogueAttachmentDTO attachment = inputInfo.getAttachment();
        List<Integer> typeList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(attachment)) {
            List<Integer> list = attachment.getAttachmentTypeList();
            if (ObjectUtil.isNotEmpty(list)) {
                //判断是否是ppt大纲生成，可以多个
                Optional<DialogueIntentionDTO> commandOption = Optional.of(inputInfo)
                        .map(DialogueInputInfoDTO::getCommand);
                Optional<String> mainCommandOption = commandOption.map(DialogueIntentionDTO::getCommand);
                Optional<String> subCommandOption = commandOption.map(DialogueIntentionDTO::getSubCommand);
                boolean command = commandOption.isPresent() && mainCommandOption.isPresent() && subCommandOption.isPresent()
                        && DialogueIntentionEnum.isTextToolIntention(mainCommandOption.get())
                        && DialogueIntentionSubEnum.AI_GENERATE_PPT.getCode().equals(subCommandOption.get());
                // 判断是不是ai ppt生成，如果是执行其他下一个
                Optional<Boolean> pptOptional = Optional.of(inputInfo)
                        .map(DialogueInputInfoDTO::getToolSetting)
                        .map(DialogueToolSettingDTO::getTextToolSetting)
                        .map(DialogueTextToolSettingDTO::getEnablePptMake);
                if (command && (!pptOptional.isPresent() || Boolean.FALSE.equals(pptOptional.get()))) {
                    log.info("执行ai ppt大纲生成附件类型参数，不进行个数限制");
                } else if (command) {
                    if (list.size() > 1) {
                        log.error("资源类型不能同时存在，资源类型：{}", list);
                        throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                    }
                }
                typeList.addAll(list);
            }
        }

        if (ObjectUtil.isEmpty(typeList)) {
            typeList.add(ResourceTypeEnum.TEXT.getType());
        }

        String dialogue = inputInfo.getDialogue();

        // 循环资源类型
        for (int resourceType : typeList) {
            // 资源类型
            if (!ResourceTypeEnum.isExist(resourceType)) {
                log.error("资源类型不存在，资源类型：{}", resourceType);
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }

            // 对话资源
            if (ResourceTypeEnum.isText(resourceType)) {
                // 文本类型
                if (CharSequenceUtil.isEmpty(dialogue)) {
                    log.error("对话内容为空，对话内容：{}", dialogue);
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }
            } else if (ResourceTypeEnum.isMail(resourceType)) {
                // 邮件类型
                if (ObjectUtil.isEmpty(attachment.getMailList())) {
                    log.error("邮件资源为空，资源：{}", attachment.getMailList());
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }
                String mailId = attachment.getMailList().get(0).getMailId();
                if (CharSequenceUtil.isEmpty(mailId)) {
                    log.error("邮件资源id为空，资源id：{}", mailId);
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }
            } else if (ResourceTypeEnum.isNote(resourceType)) {
                // 笔记类型
                if (ObjectUtil.isEmpty(attachment.getNoteList())) {
                    log.error("笔记资源为空，资源：{}", attachment.getNoteList());
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }
                String noteId = attachment.getNoteList().get(0).getNoteId();
                if (CharSequenceUtil.isEmpty(noteId)) {
                    log.error("笔记资源id为空，资源id：{}", noteId);
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }
            } else if (ResourceTypeEnum.isImage(resourceType)) {
                // 图片类型
                if (ObjectUtil.isEmpty(attachment.getFileList())) {
                    log.error("图片资源为空，资源：{}", attachment.getFileList());
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }
                String fileId = attachment.getFileList().get(0).getFileId();
                if (CharSequenceUtil.isEmpty(fileId)) {
                    log.error("图片资源id为空，资源id：{}", fileId);
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }
            } else if (ResourceTypeEnum.isDialogueId(resourceType)) {
                // 对话id类型
                if (ObjectUtil.isEmpty(attachment.getDialogueIdList())) {
                    log.error("对话id资源为空，资源：{}", attachment.getDialogueIdList());
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }
                String dialogueId = attachment.getDialogueIdList().get(0);
                if (CharSequenceUtil.isEmpty(dialogueId)) {
                    log.error("对话id类型，资源id为空，资源id：{}", dialogueId);
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                } else if (!dialogueId.matches(RegConst.REG_ID_STR)) {
                    log.error("对话id格式不正确，资源id：{}", dialogueId);
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                } else if (CharSequenceUtil.isEmpty(inputInfo.getCommand().getCommand())) {
                    log.error("对话指令不能为空，指令：{}", inputInfo.getCommand().getCommand());
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }
            } else if (ResourceTypeEnum.isDocument(resourceType)) {
                // 文档类型
                if (ObjectUtil.isEmpty(attachment.getFileList())) {
                    log.error("文档资源为空，资源：{}", attachment.getFileList());
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }
                String fileId = attachment.getFileList().get(0).getFileId();
                if (CharSequenceUtil.isEmpty(fileId)) {
                    log.error("文档资源id为空，资源id：{}", fileId);
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }
            } else if (ResourceTypeEnum.isAttachment(resourceType)) {
                // 附件类型
                if (ObjectUtil.isEmpty(attachment.getMailList())) {
                    log.error("邮件附件资源为空，资源：{}", attachment.getMailList());
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }
                MailInfo mailInfo = attachment.getMailList().get(0);
                if (mailInfo == null || CharSequenceUtil.isEmpty(mailInfo.getMailId())) {
                    log.error("邮件附件资源id为空，邮件附件对象：{}", JsonUtil.toJson(mailInfo));
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }
            } else if (ResourceTypeEnum.isPersonalKnowledgeFile(resourceType)) {
                // 个人知识库文件类型
                DialogueIntentionDTO command = inputInfo.getCommand();
                if (Objects.isNull(command)) {
                    command = new DialogueIntentionDTO();
                    inputInfo.setCommand(command);
                }
                // 知识库对话指定文生文
                command.setCommand(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());

                if (ObjectUtil.isEmpty(inputInfo.getDialogue())) {
                    log.error("对话内容为空，对话内容：{}", inputInfo.getDialogue());
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }
                if (CollectionUtils.isEmpty(attachment.getKnowledgeFileList())) {
                    log.error("个人知识库资源id为空，资源id：{}", attachment.getKnowledgeFileList());
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }

                List<String> fileIds = new ArrayList<>();
                for (PersonalKnowledgeResource resource : attachment.getKnowledgeFileList()) {
                    if (!KnowledgeResourceTypeEnum.isExist(resource.getResourceType())) {
                        log.error("个人知识库id格式错误，知识库id：{}", resource.getBaseId());
                        throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                    } else if (ObjectUtil.isEmpty(resource.getResourceId())) {
                        log.error("个人知识库文件id为空：{}", resource.getResourceId());
                        throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                    }
                    fileIds.add(resource.getResourceId());
                }

                // 校验文件是否存在
                List<UserKnowledgeFileEntity> list = userKnowledgeFileRepository.selectByFileIds(null, fileIds);
                if (CollectionUtils.isEmpty(list)) {
                    log.error("个人知识库文件不存在，知识库文件id：{}", JsonUtil.toJson(fileIds));
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_RESOURCE_ID_INVALID_CODE);
                }
            } else if (ResourceTypeEnum.isPersonalKnowledgeBase(resourceType)) {
                // 个人知识库类型
                DialogueIntentionDTO command = inputInfo.getCommand();
                if (Objects.isNull(command)) {
                    command = new DialogueIntentionDTO();
                    inputInfo.setCommand(command);
                }
                // 知识库对话指定文生文
                command.setCommand(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());
                if (ObjectUtil.isEmpty(inputInfo.getDialogue())) {
                    log.error("对话内容为空，对话内容：{}", inputInfo.getDialogue());
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }
                if (ObjectUtil.isEmpty(attachment.getKnowledgeBaseList())) {
                    log.error("个人知识库id为空，知识库id：{}", attachment.getKnowledgeFileList());
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }

                for (PersonalKnowledgeBase base : attachment.getKnowledgeBaseList()) {
                    if (ObjectUtil.isEmpty(base.getBaseId())) {
                        log.error("个人知识库id为空，知识库id：{}", base.getBaseId());
                        throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                    } else if (ObjectUtil.isEmpty(base.getName())) {
                        log.error("个人知识库名称为空，知识库名称：{}", base.getName());
                        throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                    } else if (ObjectUtil.isEmpty(base.getBaseType())) {
                        log.error("个人知识库类型为空，知识库类型：{}", base.getBaseType());
                        throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                    } else if (!KnowledgeBaseTypeEnum.isExist(base.getBaseType())) {
                        log.error("个人知识库类型错误，知识库类型：{}", base.getBaseType());
                        throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                    }
                }
            }
        }
    }
}
