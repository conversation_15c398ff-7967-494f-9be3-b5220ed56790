package com.zyhl.yun.api.outer.application.config;

import java.util.ArrayList;
import java.util.List;

import org.python.jline.internal.Log;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 对话文本工具意图开关配置
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties("chat.text-tool")
public class ChatTextToolOpenConfig {
	/**
	 * ai ppt开关，默认开启
	 */
	private boolean aiPptOpen = true;

	/**
	 * 必须关闭的渠道列表，aiPptOpen=true，也关闭
	 */
	private List<String> aiPptMustCloseChannels = new ArrayList<>();

	/**
	 * ai 生成回忆相册开关，默认开启
	 */
	private boolean aiMemoryAlbumOpen = true;

	/**
	 * ai 生成回忆相册（open=false）关闭条件，允许开启的白名单用户
	 */
	private List<String> aiMemoryAlbumWhiteList = new ArrayList<>();

	/**
	 * ai ppt : 获取是否开启-by渠道
	 * 
	 * @param channel
	 * @return
	 */
	public boolean getAiPptOpenByChannel(String channel) {
		if (aiPptOpen && CollUtil.isNotEmpty(getAiPptMustCloseChannels())) {
			// 开启状态，再额外拦截必须关闭的渠道列表
			for (String tempChannel : getAiPptMustCloseChannels()) {
				if (tempChannel.equals(channel)) {
					log.info("必须关闭的渠道列表，aiPptOpen=true，也关闭 channel:{}", channel);
					return false;
				}
			}
		}
		return aiPptOpen;
	}

	/**
	 * ai 生成回忆相册-获取是否开启-by phone
	 * 
	 * @param channel
	 * @return
	 */
	public boolean getAiMemoryAlbumOpenByPhone(String phone) {
		if (aiMemoryAlbumOpen) {
			return true;
		}
		if (CollUtil.isNotEmpty(getAiMemoryAlbumWhiteList())) {
			// 开启状态，再额外拦截必须关闭的渠道列表
			for (String tempPhone : getAiMemoryAlbumWhiteList()) {
				if (tempPhone.equals(phone)) {
					log.info("ai 生成回忆相册（open=false）关闭条件，允许开启的白名单用户 phone:{}", phone);
					return true;
				}
			}
		}
		return false;
	}
}
