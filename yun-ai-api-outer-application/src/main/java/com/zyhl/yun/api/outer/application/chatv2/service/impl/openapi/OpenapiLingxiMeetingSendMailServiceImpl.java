package com.zyhl.yun.api.outer.application.chatv2.service.impl.openapi;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.shaded.com.google.common.base.Objects;
import org.springframework.stereotype.Service;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.dto.MailLoginReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.dto.SendMailCustomizationReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.vo.MailLoginVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.vo.MailResultVO;
import com.zyhl.yun.api.outer.application.assembler.ChatFlowResultAssembler;
import com.zyhl.yun.api.outer.application.chatv2.dto.AssistantChatV2PollingUpdateDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.event.completeimpl.PptMailChatCallbackEvent;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.service.AlgorithmChatV2ContentService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.service.openapi.OpenapiLingxiCommonService;
import com.zyhl.yun.api.outer.application.chatv2.service.openapi.OpenapiLingxiMeetingSendMailService;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueFlowResultVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueResultV2VO;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO.OpenApiLingxiCardLink;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO.OpenApiLingxiCardReplyMessage;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO.OpenApiLingxiCardReplyMessage.CardReplyItem;
import com.zyhl.yun.api.outer.application.config.ApplicationAgentLingxiConfig;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig.IntelligentMeeting;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties.SourceChannel;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.AiFunctionResult;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueIntentionOutput;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.AiTextResultVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddFlowStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.external.MailExternalService;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.external.service.UserEtnService;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * openapi lingxi 智能体对话-智能会议发邮件-服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-18 21:00
 */
@Slf4j
@Service
public class OpenapiLingxiMeetingSendMailServiceImpl implements OpenapiLingxiMeetingSendMailService {

	@Resource
	private OpenapiLingxiCommonService openapiLingxiCommonService;

	@Resource
	private DataSaveService dataSaveService;

	@Resource
	private TextModelExternalService textModelExternalService;

	@Resource
	private AlgorithmChatV2ContentService algorithmChatV2ContentService;

	@Resource
	private MailExternalService mailExternalService;

	@Resource
	private UserEtnService userEtnService;

	@Resource
	private PptMailChatCallbackEvent pptMailChatCallbackEvent;

	@Resource
	private SourceChannelsProperties sourceChannelsProperties;

	@Resource
	private ChatTextToolBusinessConfig chatTextToolBusinessConfig;

	@Resource
	private ApplicationAgentLingxiConfig applicationAgentLingxiConfig;

	@Resource
	private ModelProperties modelProperties;

	@Resource
	private ChatFlowResultAssembler chatFlowResultAssembler;

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		DialogueIntentionVO.IntentionInfo mainIntention = DialogueIntentionVO
				.getMainIntention(handleDTO.getIntentionVO());
		String sendMailDialogueId = openapiLingxiCommonService
				.getDialogueIdByIntention(handleDTO.getReqDTO().getUserId(), handleDTO.getSessionId(), mainIntention);
		if (StringUtils.isEmpty(sendMailDialogueId)) {
			log.warn("发邮件对话id为空，继续对话-文生文");
			return true;
		}
		log.info("发邮件对话id:{}", sendMailDialogueId);

		// 获取对话内容信息
		AssistantChatV2PollingUpdateDTO pollingUpdate = new AssistantChatV2PollingUpdateDTO();
		pollingUpdate.setDialogueId(Long.parseLong(sendMailDialogueId));
		pollingUpdate.setUserId(handleDTO.getReqDTO().getUserId());
		DialogueResultV2VO dialogResult = openapiLingxiCommonService.contentPollingUpdate(pollingUpdate);
		if (null == dialogResult) {
			log.warn("上一次对话信息为空，继续对话");
			return true;
		}

		checkHandleInfo(handleDTO, dialogResult);

		// 意图判断
		DialogueIntentionOutput outputCommand = dialogResult.getOutputCommand();
		if (null == outputCommand) {
			log.error("上一次对话信息意图为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		if (DialogueIntentionEnum.isTextToolIntention(outputCommand.getCommand())
				&& DialogueIntentionSubEnum.isMeetingMail(outputCommand.getSubCommand())) {
			// 前置-会议通知发邮件
			beforeSendMail(handleDTO, mainIntention, dialogResult);
		} else if (DialogueIntentionEnum.isSendMail(outputCommand.getCommand())) {
			// 后置-ppt生成方案后发邮件
			afterPptSendMail(handleDTO, mainIntention, dialogResult);
		} else if (DialogueIntentionEnum.isTextToolIntention(outputCommand.getCommand())
				&& DialogueIntentionSubEnum.isAiPpt(outputCommand.getSubCommand())) {
			// ppt生成后生成邮件内容
			afterPptGenMailContent(handleDTO, dialogResult);
		} else {
			log.error("上一次对话信息意图错误，无法处理意图");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		return false;
	}

	/**
	 * 前置会议通知，发邮件流程
	 * 
	 * @param handleDTO     请求参数
	 * @param mainIntention 意图
	 * @param mailList      邮箱请求
	 * @param dialogResult  对话结果
	 */
	private void beforeSendMail(ChatAddHandleDTO handleDTO, DialogueIntentionVO.IntentionInfo mainIntention,
			DialogueResultV2VO dialogResult) {
		DialogueFlowResult textModelResult = null;
		MailInfoVO mailInfo = null;
		List<DialogueFlowResult> outputList = dialogResult.getOutputList();
		if (CollUtil.isNotEmpty(outputList)) {
			for (DialogueFlowResult output : outputList) {
				if (null == textModelResult
						&& Objects.equal(FlowResultTypeEnum.TEXT_MODEL.getType(), output.getResultType())) {
					textModelResult = output;
				}

				if (null == mailInfo && Objects.equal(FlowResultTypeEnum.MAIL.getType(), output.getResultType())) {
					mailInfo = output.getMailInfo();
				}
			}
		}

		if (null == textModelResult) {
			log.error("对话大模型信息为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}
		if (null == mailInfo) {
			log.error("对话发邮件信息为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		String sid = null;
		String rmkey = null;
		try {
			// 统一认证token
			String stToken = userEtnService.querySpecToken(RequestContextHolder.getToken());
			if (StringUtils.isEmpty(stToken)) {
				throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
			}
			MailLoginReqDTO mailLoginReqDTO = new MailLoginReqDTO("10176", "7", stToken,
					String.valueOf(handleDTO.getDialogueId()));
			MailLoginVO mailLoginInfo = mailExternalService.mailLogin(mailLoginReqDTO);
			if (null != mailLoginInfo) {
				sid = mailLoginInfo.getSid();
				rmkey = mailLoginInfo.getRmkey();
			}
		} catch (Exception e) {
			log.error("邮箱平台登录失败 error:", e);
		}
		if (StringUtils.isAnyBlank(sid, rmkey)) {
			failedEditMailResp(handleDTO, "获取邮箱登录信息失败", dialogResult.getDialogueId());
			return;
		}

		// 聊天添加响应对象
		ChatAddRespVO respVO = handleDTO.getRespVO();
		respVO.setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());
		List<DialogueFlowResult> saveOutputList = new ArrayList<>();
		IntelligentMeeting intelligentMeeting = chatTextToolBusinessConfig.getIntelligentMeeting();

		// 发邮件（会议通知）
		SendMailCustomizationReqDTO reqDTO = new SendMailCustomizationReqDTO();
		reqDTO.setSid(sid);
		reqDTO.setRmkey(rmkey);
		reqDTO.setRecipientList(mailInfo.getRecipientList());
		reqDTO.setTitle(mailInfo.getTitle());
		reqDTO.setContent(intelligentMeeting.splitMailContent(textModelResult.getOutContent()));

		// 发邮件执行
		MailResultVO mailResult = null;
		Exception sendMailException = null;
		try {
			mailResult = mailExternalService.sendMailCustomization(reqDTO);
		} catch (Exception e) {
			log.error("sendMailCustomization reqDTO:{} error:", JSONUtil.toJsonStr(reqDTO), e);
			sendMailException = e;
		}

		// 发邮件失败
		if (!(null != mailResult && mailResult.isSuccess())) {
			String sendMailErrorMsg = intelligentMeeting.getSendMailErrorMsg();
			// 默认下游服务异常
			String errorMsg = ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION.getResultMsg();
			if (sendMailException instanceof YunAiBusinessException) {
				String code = ((YunAiBusinessException) sendMailException).getCode();
				if (AiResultCode.isSendMailErrorCode(code)) {
					// 发邮件错误码，独立转换
					AiResultCode aiResultCode = AiResultCode.getByCodeOrMsg(code,
							((YunAiBusinessException) sendMailException).getMessage());
					errorMsg = aiResultCode.getMsg();
				}
			}
			String allErrorMsg = String.format(sendMailErrorMsg, errorMsg);
			failedEditMailResp(handleDTO, allErrorMsg, dialogResult.getDialogueId());
			return;
		}

		// 发邮件成功
		log.info("发送邮件成功 dialogueId:{}", handleDTO.getDialogueId());

		// 思考过程
		respVO.setFlowResult(new DialogueFlowResultVO(0, FlowResultTypeEnum.REASONING_RESULT,
				intelligentMeeting.getSendMailNoticeTitle(), intelligentMeeting.getSendMailNoticeTip()));
		saveOutputList.add(chatFlowResultAssembler.getFlowResult(respVO.getFlowResult()));
		handleDTO.getSseEmitterOperate().send(OpenApiLingxiChatRespVO.getOpenApiLingxiChatResp(handleDTO, respVO));

		DialogueFlowResultVO mailFlowResultVO = new DialogueFlowResultVO(1, FlowResultTypeEnum.MAIL, null, null);
		mailFlowResultVO.setMailInfo(MailInfoVO.builder().sendMailFlag(true).build());
		respVO.setFlowResult(mailFlowResultVO);
		saveOutputList.add(chatFlowResultAssembler.getFlowResult(mailFlowResultVO));
		// 设置hbase
		AiTextResultRespParameters result = AiTextResultRespParameters.builder()
				.version(AiTextResultVersionEnum.V2.getVersion())
				.outputCommand(new DialogueIntentionOutput(mainIntention)).outputList(saveOutputList).build();

		// 保存hbase
		dataSaveService.saveHbaseAllChatResult(handleDTO, result);

		// 保存tidb
		dataSaveService.addSuccessAndModelCode(handleDTO, null, OutContentTypeEnum.TEXT);

		// 流式响应结束
		respVO.setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());

		// 前面无大大模型输出，先发一个大模型回复
		String sendMailSuccModelText = applicationAgentLingxiConfig.getTextConfig().getSendMailSuccModelText();
		handleDTO.getSseEmitterOperate()
				.send(OpenApiLingxiChatRespVO.getOpenApiLingxiChatTextModelResp(handleDTO, sendMailSuccModelText));

		// 结束之前，追加2个消息（卡片【邮件已成功发送！查看详情】、追尾【会议要开始了？试试会议录音】）
		String bubbleDesc = applicationAgentLingxiConfig.getTextConfig().getBubbleDesc();
		String detailMailColor = applicationAgentLingxiConfig.getTextConfig().getDetailMailColor();
		String detailMailImage = applicationAgentLingxiConfig.getTextConfig().getDetailMailImage();
		String detailMailTitle = applicationAgentLingxiConfig.getTextConfig().getDetailMailTitle();
		String detailMailSubtitle = applicationAgentLingxiConfig.getTextConfig().getDetailMailSubtitle();
		String detailMailButtonName = applicationAgentLingxiConfig.getTextConfig().getDetailMailButtonName();
		String detailMailButtonColor = applicationAgentLingxiConfig.getTextConfig().getDetailMailButtonColor();
		String detailMailButtonImage = applicationAgentLingxiConfig.getTextConfig().getDetailMailButtonImage();
		String voiceNoteTitle = applicationAgentLingxiConfig.getTextConfig().getVoiceNoteTitle();
		String voiceNoteReplyText = applicationAgentLingxiConfig.getTextConfig().getVoiceNoteReplyText();

		OpenApiLingxiChatRespVO.WebLink webLink = new OpenApiLingxiChatRespVO.WebLink(
				openapiLingxiCommonService.getSendMailJumpUrl(RequestContextHolder.getToken()));
		OpenApiLingxiCardReplyMessage replyMessage = new OpenApiLingxiCardReplyMessage(
				Collections.singletonList(
						new CardReplyItem(detailMailColor, detailMailImage, detailMailTitle, detailMailSubtitle,
								new OpenApiLingxiCardLink(webLink, detailMailButtonColor, detailMailButtonImage,
										detailMailButtonName))),
				bubbleDesc, Collections.singletonList(new OpenApiLingxiCardLink(voiceNoteTitle,
						new OpenApiLingxiCardLink.ReplyLink(voiceNoteReplyText))));
		handleDTO.getSseEmitterOperate()
				.sendAndComplete(OpenApiLingxiChatRespVO.getOpenApiLingxiChatReplyResp(handleDTO, replyMessage));

	}

	/**
	 * ppt生成邮件内容，流式对话
	 * 
	 * @param handleDTO    请求参数
	 * @param dialogResult 对话结果
	 */
	private void afterPptGenMailContent(ChatAddHandleDTO handleDTO, DialogueResultV2VO dialogResult) {

		// 聊天添加响应对象
		ChatAddRespVO respVO = handleDTO.getRespVO();
		respVO.setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());

		AiFunctionResult aiFunctionResult = getAiFunctionResult(dialogResult);
		if (!(null != aiFunctionResult && null != aiFunctionResult.getFile())) {
			String genPptFailedModelText = "ppt未生成";
			log.error("对话tool结果文件信息为空");
			// 前面无大大模型输出，先发一个大模型回复
			handleDTO.getSseEmitterOperate().sendAndComplete(
					OpenApiLingxiChatRespVO.getOpenApiLingxiChatTextModelErrorResp(handleDTO, genPptFailedModelText));
			return;
		}

		AlgorithmChatContentEntity lastMailDialogueInfo = openapiLingxiCommonService.getLastIntentionDialogue(
				handleDTO.getReqDTO().getUserId(), handleDTO.getSessionId(), dialogResult.getDialogueId());
		if (null == lastMailDialogueInfo) {
			log.error("对话信息为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}
		MailInfoVO mailInfo = openapiLingxiCommonService.getMailInfoResult(lastMailDialogueInfo);
		if (null == mailInfo) {
			log.error("对话发邮件信息为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		IntelligentMeeting intelligentMeeting = chatTextToolBusinessConfig.getIntelligentMeeting();

		// 入库hbase结果列表-大模型之前
		List<DialogueFlowResult> beforeOutputList = new ArrayList<>();
		// 思考过程
		respVO.setFlowResult(new DialogueFlowResultVO(0, FlowResultTypeEnum.REASONING_RESULT,
				intelligentMeeting.getSendMailPptNoticeRecTitle(), intelligentMeeting.getSendMailPptNoticeRecTip()));
		beforeOutputList.add(chatFlowResultAssembler.getFlowResult(respVO.getFlowResult()));
		handleDTO.getSseEmitterOperate().send(OpenApiLingxiChatRespVO.getOpenApiLingxiChatResp(handleDTO, respVO));

		respVO.setFlowResult(new DialogueFlowResultVO(1, FlowResultTypeEnum.REASONING_RESULT,
				intelligentMeeting.getSendMailPptNoticeMailTitle(), intelligentMeeting.getSendMailPptNoticeMailTip()));
		beforeOutputList.add(chatFlowResultAssembler.getFlowResult(respVO.getFlowResult()));
		handleDTO.getSseEmitterOperate().send(OpenApiLingxiChatRespVO.getOpenApiLingxiChatResp(handleDTO, respVO));

		/**
		 * 追加文案到大模型输出之前
		 */
		String autoGenPptMailContentTitle = intelligentMeeting.getAutoGenPptMailContentTitle();
		StringBuilder appendBeforeOutContent = new StringBuilder();
		appendBeforeOutContent.append(String.format(intelligentMeeting.getRecipientTitie(),
				StringUtils.join(mailInfo.getRecipientList(), "、")));
		// 去除附件后缀为邮件标题
		String mailTitle = aiFunctionResult.getTitle();
		appendBeforeOutContent.append(String.format(intelligentMeeting.getSubjectTitie(), mailTitle));
		appendBeforeOutContent
				.append(String.format(intelligentMeeting.getAttachmentTitie(), aiFunctionResult.getFile().getName()));
		appendBeforeOutContent.append(intelligentMeeting.getContentTitie());
		DialogueFlowResultVO firstModelFlowResultVO = new DialogueFlowResultVO(2, FlowResultTypeEnum.TEXT_MODEL,
				autoGenPptMailContentTitle, appendBeforeOutContent.toString());
		firstModelFlowResultVO.setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());
		respVO.setFlowResult(firstModelFlowResultVO);
		handleDTO.getSseEmitterOperate().send(OpenApiLingxiChatRespVO.getOpenApiLingxiChatResp(handleDTO, respVO));

		/**
		 * 流式对话开始
		 */
		DialogueFlowResult callBackOriginalDialogueFlowResult = new DialogueFlowResult();
		callBackOriginalDialogueFlowResult.setMailInfo(mailInfo);
		DialogueFlowResult callBackDialogueFlowResult = new DialogueFlowResult();
		MailInfoVO newMailInfo = new MailInfoVO();
		newMailInfo.setTitle(mailTitle);
		newMailInfo.setRecipientList(mailInfo.getRecipientList());
		newMailInfo.setSender(mailInfo.getSender());
		newMailInfo.setContent(mailInfo.getContent());
		callBackDialogueFlowResult.setMailInfo(newMailInfo);
		callBackDialogueFlowResult.setAiFunctionResult(aiFunctionResult);
		callBackDialogueFlowResult.setTitle(autoGenPptMailContentTitle);
		SseEventListener event = new SseEventListener(handleDTO, null);
		// 设置输出之前的文案
		event.setBeforeOutputList(beforeOutputList);
		event.setModelCode(intelligentMeeting.getPptMailModelCode());
		event.setAppendBeforeOutContent(appendBeforeOutContent.toString());
		event.setCallBackDialogueFlowResult(callBackDialogueFlowResult);
		event.setCallBackOriginalDialogueFlowResult(callBackOriginalDialogueFlowResult);
		event.setCallBackTitle(autoGenPptMailContentTitle);
		// 文本模型
		Integer maxLength = modelProperties.getMaxLength(handleDTO.getAssistantEnum(), handleDTO.getBusinessType(),
				event.getModelCode());
		TextModelTextReqDTO reqDTO = event.getTextDto().toTextReqDTO(maxLength);
		// 设置不联网
		reqDTO.setEnableForceNetworkSearch(false);
		// 组装提示词+query
		String queryContent = chatTextToolBusinessConfig.getIntelligentMeeting().getPptMailPrompt().replace("{query}",
				dialogResult.getDialogueInputInfo().getDialogue());
		TextModelMessageDTO msgDTO = new TextModelMessageDTO(TextModelRoleEnum.USER.getName(), queryContent);
		reqDTO.setMessageDtoList(Collections.singletonList(msgDTO));
		event.setHandleDTO(handleDTO);
		// 设置当前的索引，下一次加1
		event.setSendIndex(beforeOutputList.size());
		// 设置回调处理事件
		event.setCompleteCallbackEvent(pptMailChatCallbackEvent);

		log.info("【特殊场景智能体文本对话036_036001】指定文本模型对话 model:{}, reqDTO:{}", event.getModelCode(),
				JSONUtil.toJsonStr(reqDTO));
		textModelExternalService.streamDialogue(event.getModelCode(), reqDTO, event);

	}

	/**
	 * 后置ppt文案通知，发邮件流程
	 * 
	 * @param handleDTO     请求参数
	 * @param mainIntention 意图
	 * @param mailList      邮箱请求
	 * @param dialogResult  对话结果
	 */
	private void afterPptSendMail(ChatAddHandleDTO handleDTO, IntentionInfo mainIntention,
			DialogueResultV2VO dialogResult) {
		DialogueFlowResult textModelResult = getTextModelResult(dialogResult);
		if (null == textModelResult) {
			log.error("对话大模型信息为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		if (null == dialogResult.getExtInfoParam() || null == dialogResult.getExtInfoParam().getDialogueFlowResult()) {
			log.error("上一次对话信息扩展参数为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		MailInfoVO mailInfo = dialogResult.getExtInfoParam().getDialogueFlowResult().getMailInfo();
		if (null == mailInfo) {
			log.error("对话发邮件信息为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		AiFunctionResult aiFunctionResult = dialogResult.getExtInfoParam().getDialogueFlowResult()
				.getAiFunctionResult();
		if (!(null != aiFunctionResult && null != aiFunctionResult.getFile())) {
			log.error("对话tool结果文件信息为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		String sid = null;
		String rmkey = null;
		String stToken = null;
		try {
			// 统一认证token
			stToken = userEtnService.querySpecToken(RequestContextHolder.getToken());
			if (StringUtils.isEmpty(stToken)) {
				throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
			}
			MailLoginReqDTO mailLoginReqDTO = new MailLoginReqDTO("10176", "7", stToken,
					String.valueOf(handleDTO.getDialogueId()));
			MailLoginVO mailLoginInfo = mailExternalService.mailLogin(mailLoginReqDTO);
			if (null != mailLoginInfo) {
				sid = mailLoginInfo.getSid();
				rmkey = mailLoginInfo.getRmkey();
			}
		} catch (Exception e) {
			log.error("邮箱平台登录失败 error:", e);
		}
		if (StringUtils.isAnyBlank(sid, rmkey)) {
			failedEditMailResp(handleDTO, "获取邮箱登录信息失败", dialogResult.getDialogueId());
			return;
		}

		// 聊天添加响应对象
		ChatAddRespVO respVO = handleDTO.getRespVO();
		respVO.setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());
		List<DialogueFlowResult> saveOutputList = new ArrayList<>();
		IntelligentMeeting intelligentMeeting = chatTextToolBusinessConfig.getIntelligentMeeting();

		// 思考过程
		respVO.setFlowResult(new DialogueFlowResultVO(0, FlowResultTypeEnum.REASONING_RESULT,
				intelligentMeeting.getSendMailNoticeTitle(), intelligentMeeting.getSendMailNoticeTip()));
		saveOutputList.add(chatFlowResultAssembler.getFlowResult(respVO.getFlowResult()));
		handleDTO.getSseEmitterOperate().send(OpenApiLingxiChatRespVO.getOpenApiLingxiChatResp(handleDTO, respVO));

		// 发邮件（ppt方案）

		SendMailCustomizationReqDTO.McloudFileInfo pptFile = new SendMailCustomizationReqDTO.McloudFileInfo();
		pptFile.setFileId(aiFunctionResult.getFile().getFileId());
		SendMailCustomizationReqDTO reqDTO = new SendMailCustomizationReqDTO();
		reqDTO.setSid(sid);
		reqDTO.setRmkey(rmkey);
		reqDTO.setRecipientList(mailInfo.getRecipientList());
		reqDTO.setMcloudFileList(Collections.singletonList(pptFile));
		reqDTO.setTitle(mailInfo.getTitle());
		reqDTO.setContent(intelligentMeeting.splitMailContent(textModelResult.getOutContent()));

		// 发邮件执行
		MailResultVO mailResult = null;
		Exception sendMailException = null;
		try {
			mailResult = mailExternalService.sendMailCustomization(reqDTO);
		} catch (Exception e) {
			log.error("sendMailCustomization reqDTO:{} error:", JSONUtil.toJsonStr(reqDTO), e);
			sendMailException = e;
		}

		// 发邮件失败
		if (!(null != mailResult && mailResult.isSuccess())) {
			String sendMailErrorMsg = intelligentMeeting.getSendMailErrorMsg();
			// 默认下游服务异常
			String errorMsg = ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION.getResultMsg();
			if (sendMailException instanceof YunAiBusinessException) {
				String code = ((YunAiBusinessException) sendMailException).getCode();
				if (AiResultCode.isSendMailErrorCode(code)) {
					// 发邮件错误码，独立转换
					AiResultCode aiResultCode = AiResultCode.getByCodeOrMsg(code,
							((YunAiBusinessException) sendMailException).getMessage());
					errorMsg = aiResultCode.getMsg();
				}
			}
			String allErrorMsg = String.format(sendMailErrorMsg, errorMsg);
			failedEditMailResp(handleDTO, allErrorMsg, dialogResult.getDialogueId());
			return;
		}

		// 发邮件成功
		log.info("发送邮件成功 dialogueId:{}", handleDTO.getDialogueId());

		// 思考过程
		DialogueFlowResultVO mailFlowResultVO = new DialogueFlowResultVO(0, FlowResultTypeEnum.MAIL, null, null);
		mailFlowResultVO.setMailInfo(MailInfoVO.builder().sendMailFlag(true).build());
		respVO.setFlowResult(mailFlowResultVO);
		saveOutputList.add(chatFlowResultAssembler.getFlowResult(mailFlowResultVO));
		// 设置hbase
		AiTextResultRespParameters result = AiTextResultRespParameters.builder()
				.version(AiTextResultVersionEnum.V2.getVersion())
				.outputCommand(new DialogueIntentionOutput(mainIntention)).outputList(saveOutputList).build();

		// 保存hbase
		dataSaveService.saveHbaseAllChatResult(handleDTO, result);

		// 保存tidb
		dataSaveService.addSuccessAndModelCode(handleDTO, null, OutContentTypeEnum.TEXT);

		// 流式响应结束
		respVO.setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());

		// 前面无大大模型输出，先发一个大模型回复
		String sendMailSuccModelText = applicationAgentLingxiConfig.getTextConfig().getSendMailSuccModelText();
		handleDTO.getSseEmitterOperate()
				.send(OpenApiLingxiChatRespVO.getOpenApiLingxiChatTextModelResp(handleDTO, sendMailSuccModelText));

		// 结束之前，追加1个消息（卡片【邮件已成功发送！查看详情】、追尾【待定】）
		String detailMailColor = applicationAgentLingxiConfig.getTextConfig().getDetailMailColor();
		String detailMailImage = applicationAgentLingxiConfig.getTextConfig().getDetailMailImage();
		String detailMailTitle = applicationAgentLingxiConfig.getTextConfig().getDetailMailTitle();
		String detailMailSubtitle = applicationAgentLingxiConfig.getTextConfig().getDetailMailSubtitle();
		String detailMailButtonName = applicationAgentLingxiConfig.getTextConfig().getDetailMailButtonName();
		String detailMailButtonColor = applicationAgentLingxiConfig.getTextConfig().getDetailMailButtonColor();
		String detailMailButtonImage = applicationAgentLingxiConfig.getTextConfig().getDetailMailButtonImage();

		OpenApiLingxiChatRespVO.WebLink webLink = new OpenApiLingxiChatRespVO.WebLink(
				openapiLingxiCommonService.getSendMailJumpUrl(RequestContextHolder.getToken()));
		OpenApiLingxiCardReplyMessage replyMessage = new OpenApiLingxiCardReplyMessage(
				Collections.singletonList(new CardReplyItem(detailMailColor, detailMailImage, detailMailTitle,
						detailMailSubtitle, new OpenApiLingxiCardLink(webLink, detailMailButtonColor,
								detailMailButtonImage, detailMailButtonName))));
		handleDTO.getSseEmitterOperate()
				.sendAndComplete(OpenApiLingxiChatRespVO.getOpenApiLingxiChatReplyResp(handleDTO, replyMessage));

	}

	private void checkHandleInfo(ChatAddHandleDTO handleDTO, DialogueResultV2VO dialogResult) {
		// 业务信息判断
		SourceChannel sourceChannel = sourceChannelsProperties.getByChannel(dialogResult.getSourceChannel());
		SourceChannel reqSourceChannel = sourceChannelsProperties
				.getByChannel(handleDTO.getReqDTO().getSourceChannel());
		if (!(null != sourceChannel && null != reqSourceChannel
				&& sourceChannel.getBusinessType().equals(reqSourceChannel.getBusinessType()))) {
			log.error("上一次对话信息业务信息不一致");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}
	}

	/**
	 * 获取大模型结果
	 * 
	 * @param dialogResult 对话结果
	 * @return
	 */
	private DialogueFlowResult getTextModelResult(DialogueResultV2VO dialogResult) {
		List<DialogueFlowResult> outputList = dialogResult.getOutputList();
		if (CollUtil.isNotEmpty(outputList)) {
			for (DialogueFlowResult output : outputList) {
				if (Objects.equal(FlowResultTypeEnum.TEXT_MODEL.getType(), output.getResultType())) {
					return output;
				}
			}
		}
		return null;
	}

	/**
	 * 获取tool结果
	 * 
	 * @param dialogResult 对话结果
	 * @return
	 */
	private AiFunctionResult getAiFunctionResult(DialogueResultV2VO dialogResult) {
		List<DialogueFlowResult> outputList = dialogResult.getOutputList();
		if (CollUtil.isNotEmpty(outputList)) {
			for (DialogueFlowResult output : outputList) {
				if (Objects.equal(FlowResultTypeEnum.TOOL_RESULT.getType(), output.getResultType())) {
					return output.getAiFunctionResult();
				}
			}
		}
		return null;
	}

	/**
	 * 失败响应编辑邮件
	 * 
	 * @param handleDTO
	 * @param string
	 */
	private void failedEditMailResp(ChatAddHandleDTO handleDTO, String msg, String mailDialogueId) {
		ChatAddRespVO respVO = handleDTO.getRespVO();
		IntelligentMeeting intelligentMeeting = chatTextToolBusinessConfig.getIntelligentMeeting();
		// 思考过程
		respVO.setFlowResult(new DialogueFlowResultVO(0, FlowResultTypeEnum.REASONING_RESULT,
				intelligentMeeting.getSendMailNoticeTitle(), intelligentMeeting.getSendMailNoticeTip()));
		handleDTO.getSseEmitterOperate().send(OpenApiLingxiChatRespVO.getOpenApiLingxiChatResp(handleDTO, respVO));

		// 卡片跳转地址
		String paramCardButtonUrl = openapiLingxiCommonService.getMailEditJumpUrl(RequestContextHolder.getToken(),
				mailDialogueId);

		// 前面无大大模型输出，先发一个大模型回复
		handleDTO.getSseEmitterOperate()
				.send(OpenApiLingxiChatRespVO.getOpenApiLingxiChatTextModelErrorResp(handleDTO, msg));

		// 结束之前，追加2个消息（卡片【编辑邮件】、追尾【会议要开始了？试试会议录音】）
		String bubbleDesc = applicationAgentLingxiConfig.getTextConfig().getBubbleDesc();
		String editMailColor = applicationAgentLingxiConfig.getTextConfig().getEditMailColor();
		String editMailImage = applicationAgentLingxiConfig.getTextConfig().getEditMailImage();
		String editMailTitle = applicationAgentLingxiConfig.getTextConfig().getEditMailTitle();
		String editMailSubtitle = applicationAgentLingxiConfig.getTextConfig().getEditMailSubtitle();
		String editMailButtonName = applicationAgentLingxiConfig.getTextConfig().getEditMailButtonName();
		String editMailButtonColor = applicationAgentLingxiConfig.getTextConfig().getEditMailButtonColor();
		String editMailButtonImage = applicationAgentLingxiConfig.getTextConfig().getEditMailButtonImage();
		String voiceNoteTitle = applicationAgentLingxiConfig.getTextConfig().getVoiceNoteTitle();
		String voiceNoteReplyText = applicationAgentLingxiConfig.getTextConfig().getVoiceNoteReplyText();

		OpenApiLingxiChatRespVO.WebLink webLink = new OpenApiLingxiChatRespVO.WebLink(paramCardButtonUrl);
		OpenApiLingxiCardReplyMessage replyMessage = new OpenApiLingxiCardReplyMessage(
				Collections
						.singletonList(new CardReplyItem(editMailColor, editMailImage, editMailTitle, editMailSubtitle,
								new OpenApiLingxiCardLink(webLink, editMailButtonColor, editMailButtonImage,
										editMailButtonName))),
				bubbleDesc, Collections.singletonList(new OpenApiLingxiCardLink(voiceNoteTitle,
						new OpenApiLingxiCardLink.ReplyLink(voiceNoteReplyText))));
		handleDTO.getSseEmitterOperate()
				.sendAndComplete(OpenApiLingxiChatRespVO.getOpenApiLingxiChatReplyErrorResp(handleDTO, replyMessage));
	}
}
