package com.zyhl.yun.api.outer.application.service.impl;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.service.ApplicationService;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatMessageEntity;
import com.zyhl.yun.api.outer.repository.AlgorithmChatMessageRepository;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 应用业务逻辑接口实现类
 *
 * <AUTHOR>
 * @date 2024/4/22 15:31
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ApplicationServiceImpl implements ApplicationService {

    private final AlgorithmChatMessageRepository messageRepository;

    private final RedisOperateRepository redisOperateRepository;

    private final SourceChannelsProperties channelsProperties;

	@Override
	public Long intelligentGetSessionId(AlgorithmChatAddDTO dto) {
		return intelligentGetSessionId(dto.getUserId(), dto.getContent().getSourceChannel(), dto.getApplicationId());
	}
	
	@Override
	public Long intelligentGetSessionId(ChatAddReqDTO dto) {
		return intelligentGetSessionId(dto.getUserId(), dto.getSourceChannel(), dto.getApplicationId());
	}
	
    private Long intelligentGetSessionId(String userId, String sourceChannel, String applicationId) {
        // 智能体sessionId限制 userId+applicationId+businessType唯一
        // 增加redis缓存，不存在数据库复查, userId+applicationId+businessType做key
        String businessType = channelsProperties.getType(sourceChannel);

        // 获取缓存
        String sessionId = redisOperateRepository.getMessageSessionId(userId, applicationId, businessType);
        if (!StringUtils.isEmpty(sessionId)) {
            return Long.parseLong(sessionId);
        }

        // 查询数据库
        AlgorithmChatMessageEntity messageEntity = messageRepository.selectOneByApplicationId(userId, applicationId, businessType);

        // 设置缓存
        if (Objects.nonNull(messageEntity)) {
            sessionId = String.valueOf(messageEntity.getId());
            redisOperateRepository.setMessageSessionId(userId, applicationId, businessType, sessionId);
            return messageEntity.getId();
        }
        return null;
    }

}
