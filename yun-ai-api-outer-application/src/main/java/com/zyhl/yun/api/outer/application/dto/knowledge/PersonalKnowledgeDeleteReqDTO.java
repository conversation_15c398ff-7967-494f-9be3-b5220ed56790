package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 删除个人知识库接口请求
 * @date 2025/4/15 14:29
 */

@Data
@Slf4j
@EqualsAndHashCode(callSuper = true)
public class PersonalKnowledgeDeleteReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 知识库ID
     */
    private String baseId;
}
