package com.zyhl.yun.api.outer.application.dto;

import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.ParallelHashCtx;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description}
 * <p>
 *
 * <AUTHOR>
 * @since 4月14 2025
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UploadPartInfo {
  /**
   * 段编号
   */
  private Integer partNumber;
  /**
   * 分段大小
   */
  private Long partSize;
  /**
   * 上传地址，默认有效时间时间15分钟
   */
  private String uploadUrl;

  /**
   * 上一个分段的Hash上下文，该字段只在多分片并发上传的模式下有效
   */
  private ParallelHashCtx parallelHashCtx;
}
