package com.zyhl.yun.api.outer.application.dto;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.Data;

import javax.validation.Valid;

/**
 * 保存对话参数类
 *
 * <AUTHOR>
 * @data 2024/2/29 14:32
 */
@Data
public class AlgorithmChatPlatformAddDTO {
	/**
	 * 平台信息
	 */
	private String platformInfo;
	/**
	 * 会话ID 不传则默认新创建会话
	 */
	private String sessionId;
	/**
	 * 用户id
	 */
	private String userId;

	@Valid
	private AlgorithmChatAddContentDTO content;

	public void setTokenUserId() {
		if (CharSequenceUtil.isNotEmpty(RequestContextHolder.getUserId())) {
			this.userId = RequestContextHolder.getUserId();
		}
	}
}
