package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import com.zyhl.yun.api.outer.application.dto.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 个人知识库列表请求参数
 *
 * <AUTHOR>
 * @date 2025/04/16
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KnowledgeFileCheckReqDTO extends BaseChannelDTO implements Serializable {

    @NotNull(message = "资源类型不能为空")
    @Range(min = 0, max = 3, message = "资源类型不支持" )
    private Integer resourceType;

    private String htmlUrl;

}
