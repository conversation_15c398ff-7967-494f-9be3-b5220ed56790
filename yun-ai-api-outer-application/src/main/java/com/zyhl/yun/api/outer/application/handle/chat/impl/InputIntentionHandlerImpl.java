package com.zyhl.yun.api.outer.application.handle.chat.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.handle.chat.AbstractChatAddHandler;
import com.zyhl.yun.api.outer.constants.FutureConstants;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * 意图识别
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class InputIntentionHandlerImpl extends AbstractChatAddHandler {

    @Override
    public int order() {
        return ExecuteSort.INPUT_INTENTION.getSort();
    }

    @Override
    public boolean execute(ChatAddInnerDTO innerDTO) {
        // 判断是否执行【输入内容意图识别】
        return judgeInputIntention(innerDTO);
    }

    @Override
    public boolean run(ChatAddInnerDTO innerDTO) {
        log.info("进入输入内容意图识别处理");

        // 获取意图识别预处理future的结果
        innerDTO.setIntentionVO(getDialogueIntention(innerDTO));

        return true;
    }

    /**
     * 获取意图识别预处理future的结果
     * @param innerDTO 用户输入对象
     */
    private DialogueIntentionVO getDialogueIntention(ChatAddInnerDTO innerDTO) {
        DialogueIntentionVO intention = null;
        try {
            /** 获取意图识别预处理future */
            Future<DialogueIntentionVO> future = innerDTO.getDialogueIntentionFuture();
            if (ObjectUtil.isNotNull(future)) {
                // 等到任务都完成，并设置超时60s
                intention = future.get(FutureConstants.SIXTY_SECONDS, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.error("InputIntentionHandlerImpl-getDialogueIntention，future.get()，异常", e);
        }
        return intention;
    }

}
