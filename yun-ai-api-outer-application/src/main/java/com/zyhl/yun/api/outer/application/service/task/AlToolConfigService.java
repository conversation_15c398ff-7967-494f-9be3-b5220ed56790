package com.zyhl.yun.api.outer.application.service.task;

import com.zyhl.yun.api.outer.application.dto.AIToolConfigDTO;
import com.zyhl.yun.api.outer.application.dto.tool.ToolConfigFaceSwapParamDTO;
import com.zyhl.yun.api.outer.vo.AIToolConfigVO;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.application.service.task.AlToolConfigService} <br>
 * <b> description:</b>
 * AI工具对应的参数配置Service
 *
 * <AUTHOR>
 * @date 2024-11-28 10:12
 **/
public interface AlToolConfigService {

    /**
     * AI工具配置获取接口
     *
     * @param dto the dto
     * @return {@link AIToolConfigVO}
     * <AUTHOR>
     * @date 2024-11-28 10:15
     */
    AIToolConfigVO aiToolConfigGet(AIToolConfigDTO dto);


    /**
     * 获取AI照相馆的配置信息
     *
     * @param param 请求参数
     * @return {@link AIToolConfigVO}
     */
    AIToolConfigVO getFaceSwapConfig(ToolConfigFaceSwapParamDTO param);

}
