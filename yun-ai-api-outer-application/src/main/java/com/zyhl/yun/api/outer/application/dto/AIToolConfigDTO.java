package com.zyhl.yun.api.outer.application.dto;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * <b>className:</b>
 * {@link AIToolConfigDTO} <br>
 * <b> description:</b>
 * AI工具对应的参数配置VO
 *
 * <AUTHOR>
 * @date 2024-11-28 09:39
 **/
@Data
public class AIToolConfigDTO implements Serializable {

    private static final long serialVersionUID = -2807689674866728053L;

    /**
     * 用户id，如果有token就不需要传
     */
    private String userId;

    /**
     * AI指令
     */
    @NotNull(message = "AI指令不可为空")
    private String command;

    /**
     * 渠道号
     */
    @NotNull(message = "渠道号不可为空")
    private String channelId;

    /**
     * 渠道编码，当channelId为AI写真相关渠道时,参数必填
     */
    private String channelCode;

    /**
     * json格式,不同算法格式不同
     * AI照相馆：{"style":"","poseId":""}
     */
    private String businessParam;
}
