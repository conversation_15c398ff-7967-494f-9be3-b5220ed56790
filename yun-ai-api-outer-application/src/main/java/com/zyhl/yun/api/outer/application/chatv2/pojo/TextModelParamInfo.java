package com.zyhl.yun.api.outer.application.chatv2.pojo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageVlDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageVlDTO.VlContent;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelVlReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelVlContentTypeEnum;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 调用文本大模型接口字段
 *
 * <AUTHOR>
 */
@Slf4j
@Data
public class TextModelParamInfo {
    /**
     * 用户id
     */
    private String userId;
    /**
     * 会话id
     */
    private String sessionId;
    /**
     * 对话id
     */
    private String dialogueId;
    /**
     * 用户输入问题
     */
    private String question;
    /**
     * 是否强制联网搜索
     */
    private Boolean enableForceNetworkSearch;

    /**
     * 对话请求信息（历史对话 + 当前对话）
     */
    List<TextModelMessageDTO> dialogueMessageList = new ArrayList<>();


    public TextModelParamInfo(ChatAddHandleDTO handleDTO, List<TextModelMessageDTO> historyList) {
        // 初始化信息
        this.userId = handleDTO.getReqDTO().getUserId();
        this.dialogueId = handleDTO.getRespVO().getDialogueId();
        this.sessionId = handleDTO.getRespVO().getSessionId();
        this.question = CharSequenceUtil.emptyToDefault(handleDTO.getInputInfoDTO().getDialogue(), "");
        this.enableForceNetworkSearch = handleDTO.getInputInfoDTO().isEnableForceNetworkSearch();

        // 对话信息列表
        if (ObjectUtil.isNotEmpty(historyList)) {
            this.dialogueMessageList.addAll(historyList);
        }
        TextModelMessageDTO messageDTO = new TextModelMessageDTO();
        messageDTO.setRole(TextModelRoleEnum.USER.getName());
        messageDTO.setContent(this.question);
        this.dialogueMessageList.add(messageDTO);
    }

    /**
     * 设置prompt
     *
     * @param prompt 提示词
     */
    public void setPrompt(String prompt) {
        if (ObjectUtil.isEmpty(this.dialogueMessageList) || ObjectUtil.isEmpty(prompt)) {
            return;
        }

        this.dialogueMessageList.get(this.dialogueMessageList.size() - 1).setCommand(prompt);
    }

    /**
     * 文本模型参数简单封装，无message消息
     *
     * @return TextModelTextReqDTO
     */
    public TextModelTextReqDTO toSimpleTextReqDTO() {
        TextModelTextReqDTO reqDTO = new TextModelTextReqDTO();
        reqDTO.setTaskId(dialogueId);
        reqDTO.setUserId(userId);
        reqDTO.setSessionId(sessionId);
        // 设置是否强制联网搜索
        reqDTO.setEnableForceNetworkSearch(this.enableForceNetworkSearch);
        return reqDTO;
    }

    /**
     * 文本模型参数封装
     *
     * @param maxLength 最大历史字数
     * @return 文本模型参数
     */
    public TextModelTextReqDTO toTextReqDTO(Integer maxLength) {
        TextModelTextReqDTO reqDTO = toSimpleTextReqDTO();
        reqDTO.setMessageDtoList(dialogueMessageList);

        if (dialogueMessageList.size() <= 1 || maxLength == null) {
            // 只有当前对话的问题
            return reqDTO;
        }

        // 字数超限
        int totalLength = 0;
        boolean isOverLimit = false;
        List<TextModelMessageDTO> list = new ArrayList<>();
        for (int i = dialogueMessageList.size() - 1; i >= 0; i--) {
            String thisContent = dialogueMessageList.get(i).getContent();
            if (null != thisContent) {
                // 倒序累计总字数，第一条是当前问话
                totalLength += thisContent.length();
            }
            if (totalLength > maxLength) {
                // 字数超限，判断当前是用户问题，则需要减一条
                isOverLimit = true;
                if (TextModelRoleEnum.USER.getName().equals(dialogueMessageList.get(i).getRole())) {
                    list.remove(0);
                }
                break;
            }
            list.add(0, dialogueMessageList.get(i));
        }

        if (!isOverLimit) {
            return reqDTO;
        }

        // 字数超限，不传会话id
        reqDTO.setSessionId("");
        reqDTO.setMessageDtoList(list);

        return reqDTO;
    }

    /**
     * 文本模型参数简单封装，无message消息
     *
     * @return TextModelVlReqDTO
     */
    public TextModelVlReqDTO toSimpleVlReqDTO() {
        TextModelVlReqDTO reqDTO = new TextModelVlReqDTO();
        reqDTO.setTaskId(dialogueId);
        reqDTO.setUserId(userId);
        reqDTO.setSessionId(sessionId);
        // 设置是否强制联网搜索
        reqDTO.setEnableForceNetworkSearch(this.enableForceNetworkSearch);
        return reqDTO;
    }

    /**
     * 文本模型参数封装
     *
     * @param maxLength 最大历史字数
     * @return 文本模型参数
     */
    public TextModelVlReqDTO toTextVlReqDTO(Integer maxLength) {
        TextModelVlReqDTO reqDTO = toSimpleVlReqDTO();
        if (!(null != maxLength && maxLength > 0)) {
            return reqDTO;
        }
        List<TextModelMessageVlDTO> reqVlList = new ArrayList<>();
        if (CollUtil.isNotEmpty(dialogueMessageList)) {
            for (TextModelMessageDTO req : dialogueMessageList) {
                reqVlList.add(new TextModelMessageVlDTO(req.getRole(), Collections.singletonList(VlContent.builder()
                        .type(TextModelVlContentTypeEnum.TEXT.getType()).text(req.getContent()).build())));
            }
        }
        reqDTO.setMessageVlDtoList(reqVlList);

        if (dialogueMessageList.size() <= 1) {
            // 只有当前对话的问题
            return reqDTO;
        }

        // 字数超限
        int totalLength = 0;
        boolean isOverLimit = false;
        List<TextModelMessageVlDTO> list = new ArrayList<>();
        for (int i = reqVlList.size() - 1; i >= 0; i--) {
            List<VlContent> thisContent = reqVlList.get(i).getContent();
            if (CollUtil.isNotEmpty(thisContent)) {
                // 倒序累计总字数，第一条是当前问话
                totalLength += thisContent.get(0).getText().length();
            }
            if (totalLength > maxLength) {
                // 字数超限，判断当前是用户问题，则需要减一条
                isOverLimit = true;
                if (TextModelRoleEnum.USER.getName().equals(reqVlList.get(i).getRole())) {
                    list.remove(0);
                }
                break;
            }
            list.add(0, reqVlList.get(i));
        }

        if (!isOverLimit) {
            return reqDTO;
        }

        // 字数超限，不传会话id
        reqDTO.setSessionId("");
        reqDTO.setMessageVlDtoList(list);

        return reqDTO;
    }

    /**
     * 单个文本模型参数封装
     *
     * @param query 用户输入
     * @return 文本模型参数
     */
    public TextModelTextReqDTO toSingleTextReqDTO(String query) {
        TextModelTextReqDTO reqDTO = new TextModelTextReqDTO();
        reqDTO.setTaskId(dialogueId);
        reqDTO.setUserId(userId);
        reqDTO.setSessionId(sessionId);
        TextModelMessageDTO message = new TextModelMessageDTO();
        message.setRole(TextModelRoleEnum.USER.getName());
        message.setContent(query);
        reqDTO.setMessageDtoList(Collections.singletonList(message));
        return reqDTO;
    }

    /**
     * 添加回答内容到历史对话
     *
     * @param text 回答内容
     */
    public List<TextModelMessageDTO> addAnswer(String text) {
        TextModelMessageDTO messageDTO = new TextModelMessageDTO();
        messageDTO.setRole(TextModelRoleEnum.ASSISTANT.getName());
        messageDTO.setContent(text);
        messageDTO.setCommand("");
        this.dialogueMessageList.add(messageDTO);

        return this.dialogueMessageList;
    }
}
