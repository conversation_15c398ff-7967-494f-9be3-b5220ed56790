package com.zyhl.yun.api.outer.application.service.external.impl;

import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.es.domain.datahelper.repository.EsAlbumRepository;
import com.zyhl.hcy.yun.ai.common.base.es.entity.ElasticSearchEntity;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.enums.StandardModuleTypeEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.req.QueryPersonNoSetRelationDataReq;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.resp.QueryPersonNoSetRelationResp;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.service.AlbumSaasService;
import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.RecommendVoConvertor;
import com.zyhl.yun.api.outer.application.service.external.SearchImageAlbumListService;
import com.zyhl.yun.api.outer.application.vo.ContentExtInfoVO;
import com.zyhl.yun.api.outer.config.RecommendSearchAlbumProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchResult;
import com.zyhl.yun.api.outer.enums.chat.search.LeadCopyTypeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.VersionUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 智能图片的相册列表
 *
 * <AUTHOR>
 * @description
 * @date 2025/3/19 16:37
 */
@Service
@Slf4j
public class SearchImageAlbumListServiceImpl implements SearchImageAlbumListService {

    @Resource
    private AlbumSaasService albumSaasService;
    @Resource
    private RecommendSearchAlbumProperties recommendSearchAlbumProperties;
    @Resource
    private RecommendVoConvertor recommendVoConvertor;
    @Resource
    private EsAlbumRepository esAlbumRepository;

    @Override
    public ContentExtInfoVO searchImageSetAlbumList(String sourceChannel, Map<String, String> versionMap,
                                                    LeadCopyVO leadCopyVO, DialogueRecommendVO recommendVO, SearchResult searchResult) {
        ContentExtInfoVO contentExtInfoVO = new ContentExtInfoVO(leadCopyVO, recommendVO);
        // 小天允许人物关系相册推荐
        boolean xiaotianAllow = false;
        if (null != versionMap) {
            xiaotianAllow = (SourceChannelsProperties.isXiaoTianChannel(sourceChannel)
                    && VersionUtil.xtH5VersionGte202(versionMap.get(VersionUtil.H5_VERSION)));
        }
        // TODO 云邮允许人物关系相册推荐待处理
        boolean yunmail = false;
        if (!xiaotianAllow) {
            log.info("sourceChannel:{}, versionMap:{}, 当前版本不实时搜索人物关系相册推荐（2.0.2及以上开始才支持）", sourceChannel,
                    JSONUtil.toJsonStr(versionMap));
            return contentExtInfoVO;
        }
        try {
            if (Objects.isNull(searchResult) || Objects.isNull(searchResult.getSearchImageResult())) {
                log.info("语义搜图场景，无图片结果，返回。");
                return contentExtInfoVO;
            }
            /**
             * 语义搜图场景 如果搜索不到结果，判断fileList为空&hitRelationshipNameMap不为空
             */
            Map<String, List<String>> hitRelationshipNameMap = searchResult.getSearchImageResult()
                    .getHitRelationshipNameMap();
            log.info("搜索图片fileList：{}, 关系hitRelationshipNameMap: {}",
                    searchResult.getSearchImageResult().getFileList(), hitRelationshipNameMap);
            if (CollUtil.isEmpty(searchResult.getSearchImageResult().getFileList())
                    && CollUtil.isNotEmpty(hitRelationshipNameMap)) {
                if (null == recommendVO) {
                    recommendVO = new DialogueRecommendVO();
                }
                //  2.0.10版本开始，只返回按钮和文案给前端，不需要进行调用相册未设置过关系的人物数据的接口
                Boolean isH5VersionGte210 = VersionUtil.xtH5VersionGte210(versionMap.get(VersionUtil.H5_VERSION));
                if (Boolean.FALSE.equals(isH5VersionGte210)) {
                    QueryPersonNoSetRelationDataReq dto = new QueryPersonNoSetRelationDataReq();
                    dto.setUserId(RequestContextHolder.getUserId());
                    dto.setOwnerType(StandardModuleTypeEnum.PERSONAL.getOwnerType());
                    QueryPersonNoSetRelationResp resp = albumSaasService.queryPersonNoSetRelationData(dto);
                    if (null != resp && CollUtil.isNotEmpty(resp.getList())) {
                        recommendVO.setAlbumList(recommendVoConvertor.toAlbumList(resp.getList()));
                        contentExtInfoVO.setRecommend(recommendVO);
                    }
                }

                LeadCopyVO newLeadCopy = new LeadCopyVO();
                String promptCopy = null;
                if (null != leadCopyVO) {
                    // 默认用原始的promptCopy
                    promptCopy = leadCopyVO.getPromptCopy();
                }
                // 相册有返回，需要设置引导语用LeadCopy.promptCopy
                if (Boolean.TRUE.equals(isH5VersionGte210) || CollUtil.isNotEmpty(recommendVO.getAlbumList())) {
                    Set<String> hitRelationshipNameList = hitRelationshipNameMap.keySet();
                    String onlyOneName = hitRelationshipNameList.iterator().next();
                    //查询es是否有数据

                    List<ElasticSearchEntity> searchEntityList = esAlbumRepository.serFaceTags(RequestContextHolder.getUserId(), onlyOneName);
                    if (!CollectionUtils.isEmpty(searchEntityList)) {
                        log.info("es查询结果：{}", JsonUtil.toJson(searchEntityList));
                        return contentExtInfoVO;
                    }
                    if (hitRelationshipNameList.size() == 1) {
                        if (recommendSearchAlbumProperties.getMyselfName().equals(onlyOneName)) {
                            // 单个-只有本人推荐语
                            if (StringUtils
                                    .isNotEmpty(recommendSearchAlbumProperties.getI18nMyselfPromptCopy())) {
                                promptCopy = recommendSearchAlbumProperties.getI18nMyselfPromptCopy();
                            }
                        } else {
                            // 单个-非本人推荐语
                            if (recommendSearchAlbumProperties.hasNameMapConfig(onlyOneName)) {
                                promptCopy = recommendSearchAlbumProperties.getI18nOnlyonePromptCopy().replace(
                                        "{name}", recommendSearchAlbumProperties.getI18nNameValue(onlyOneName));
                            } else {
                                // 无定义，使用多人推荐语
                                promptCopy = recommendSearchAlbumProperties.getI18nMultiplePromptCopy();
                            }
                        }
                    } else {
                        // 返回多人，使用多人推荐语
                        promptCopy = recommendSearchAlbumProperties.getI18nMultiplePromptCopy();
                    }
                    newLeadCopy.setType(LeadCopyTypeEnum.TYPE6.getCode());
                }
                if (Boolean.TRUE.equals(isH5VersionGte210)) {
                    newLeadCopy.setType(LeadCopyTypeEnum.TYPE8.getCode());
                }
                newLeadCopy.setPromptCopy(promptCopy);
                newLeadCopy.setButtonCopy(recommendSearchAlbumProperties.getI18nButtonCopy());
                contentExtInfoVO.setLeadCopy(newLeadCopy);
            }
        } catch (Exception e) {
            log.error("调用相册人物推荐 searchImageSetAlbumList error:", e);
        }
        return contentExtInfoVO;

    }

}
