package com.zyhl.yun.api.outer.application.vo;


import java.util.List;

import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters.NetworkSearchInfo;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.domain.vo.chat.search.SearchInfo;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchResult;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddResultTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlgorithmChatAddVO {

    /**
     * 对话ID
     */
    private String dialogueId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 返回类型
     *
     * @see ChatAddResultTypeEnum
     */
    private Integer resultType;

    /**
     * 返回结果文本
     */
    private String text;

    /**
     * 引导文案对象
     */
    private LeadCopyVO leadCopy;

    /**
     * 意图枚举
     *
     * @see DialogueIntentionEnum
     */
    private String commands;

    /**
     * 对话信息流式结果VO
     */
    private FlowTypeResultVO flowResult;

    /**
     * 大模型联网搜索结果
     */
    List<NetworkSearchInfo> networkSearchInfoList;
    
    /**
     * 搜索意图入参
     */
    private SearchParam searchParam;

    /**
     * 搜索意图结果
     */
    private SearchResult searchResult;

    /**
     * 用户智能相册授权开关状态，当且仅当用户的意图commands为“012”和“018”时才返回，
     * 0--未开启
     * 1--已开启
     * todo 单词拼错了，前端改完后弃用 20240730【没确定前端改好不要动它】
     */
    @Deprecated
    private Integer aiAblumStatus;

    /**
     * 用户智能相册授权开关状态，当且仅当用户的意图commands为“012”和“018”时才返回，
     * 0--未开启
     * 1--已开启
     */
    private Integer aiAlbumStatus;

    /**
     * 用户智能文档检索授权开关状态，当且仅当用户的意图commands为“013”和“018”时才返回，
     * 0--未开启
     * 1--已开启
     */
    private Integer aiDocStatus;

    /**
     * 对话结果推荐对象，当对话信息不为空时返回
     */
    private DialogueRecommendVO recommend;

    /**
     * 返回结果的标题
     */
    private String title;

    /**
     * 个人知识库参考文件，可选，仅第一次流式结果返回
     */
    private List<File> personalKnowledgeFileList;

    /** 搜索信息列表 */
    private List<SearchInfo> searchInfoList;

    /**
     * 邮件信息
     */
    private MailInfoVO mailInfo;

    /**
     * 模型类型
     * @see TextModelEnum
     * 枚举值中的code编码值
     */
    private String modelType;

}
