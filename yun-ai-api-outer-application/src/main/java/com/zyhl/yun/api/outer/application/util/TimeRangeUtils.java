package com.zyhl.yun.api.outer.application.util;

import com.zyhl.yun.api.outer.domain.vo.chat.search.param.MillisecondTimeRange;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 时间范围工具类
 *
 * <AUTHOR>
 * @date 2024/4/16 13:22
 */
public class TimeRangeUtils {

    private static final DateTimeFormatter FULL_FORMAT = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final ZoneId ZONE_ID = ZoneId.systemDefault();

    /**
     * 转换获取时间范围列表
     *
     * @param timeList 时间列表
     * @return 时间范围列表
     */
    public static List<MillisecondTimeRange> convertTimeToRanges(List<String> timeList) {
        List<MillisecondTimeRange> ranges = new ArrayList<>();
        for (String timeStr : timeList) {
            if (timeStr.contains("至")) {
                // 处理日期范围
                ranges.add(createRangeFromDateRange(timeStr));
            } else if (timeStr.matches("\\d{4}-\\d{2}")) {
                //处理月份，假设为当月的第一天到最后一天
                ranges.add(createRangeFromYearMonth(timeStr));
            } else if (timeStr.matches("\\d{4}-\\d{2}-\\d{2}")) {
                // 处理单个日期，假设为当天的0点到23点59分59秒
                ranges.add(createRangeFromSingleDate(timeStr));
            } else if (timeStr.matches("\\d{4}")) {
                // 处理年份，假设为当年的第一天到最后一天
                ranges.add(createRangeFromYear(timeStr));
            } else {
                // Handle unsupported format or log an error
            }
        }
        return ranges;
    }

    private static MillisecondTimeRange createRangeFromYear(String yearStr) {
        int year = Integer.parseInt(yearStr);
        LocalDate startDate = LocalDate.of(year, 1, 1);
        LocalDate endDate = LocalDate.of(year, 12, 31);
        LocalDateTime startDateTime = LocalDateTime.from(startDate.atStartOfDay(ZONE_ID));
        LocalDateTime endDateTime = endDate.atTime(23, 59, 59).atZone(ZONE_ID).toLocalDateTime();
        return new MillisecondTimeRange(FULL_FORMAT.format(startDateTime), FULL_FORMAT.format(endDateTime));
    }

    private static MillisecondTimeRange createRangeFromYearMonth(String yearMonthStr) {
        int year = Integer.parseInt(yearMonthStr.substring(0, 4));
        int month = Integer.parseInt(yearMonthStr.substring(5));
        LocalDate startDate = LocalDate.of(year, month, 1);
        LocalDate endDate = startDate.plusMonths(1).minusDays(1);
        LocalDateTime startDateTime = LocalDateTime.from(startDate.atStartOfDay(ZONE_ID));
        LocalDateTime endDateTime = endDate.atTime(23, 59, 59).atZone(ZONE_ID).toLocalDateTime();
        return new MillisecondTimeRange(FULL_FORMAT.format(startDateTime), FULL_FORMAT.format(endDateTime));
    }

    private static MillisecondTimeRange createRangeFromSingleDate(String dateStr) {
        LocalDate date = LocalDate.parse(dateStr, DATE_FORMAT);
        LocalDateTime startDateTime = LocalDateTime.from(date.atStartOfDay(ZONE_ID));
        LocalDateTime endDateTime = date.atTime(23, 59, 59).atZone(ZONE_ID).toLocalDateTime();
        return new MillisecondTimeRange(FULL_FORMAT.format(startDateTime), FULL_FORMAT.format(endDateTime));
    }

    private static MillisecondTimeRange createRangeFromDateRange(String dateRangeStr) {
        String[] parts = dateRangeStr.split("至");
        LocalDate startDate = LocalDate.parse(parts[0], DATE_FORMAT);
        LocalDate endDate = LocalDate.parse(parts[1], DATE_FORMAT);
        LocalDateTime startDateTime = LocalDateTime.from(startDate.atStartOfDay(ZONE_ID));
        LocalDateTime endDateTime = endDate.atTime(23, 59, 59).atZone(ZONE_ID).toLocalDateTime();
        return new MillisecondTimeRange(FULL_FORMAT.format(startDateTime), FULL_FORMAT.format(endDateTime));
    }
}
