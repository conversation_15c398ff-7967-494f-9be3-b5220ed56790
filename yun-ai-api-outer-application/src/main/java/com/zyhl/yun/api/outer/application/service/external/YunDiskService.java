package com.zyhl.yun.api.outer.application.service.external;

import com.zyhl.yun.api.outer.application.dto.YunDiskReqDTO;
import com.zyhl.yun.api.outer.application.vo.YunDiskContentVO;

/**
 * <AUTHOR>
 * @Classname YunDiskService
 * @Description 云盘平台接口
 * @Date 2024/3/19 14:40
 */
public interface YunDiskService {

	/**
	 * 获取文件内容
	 * 
	 * @param dto（fileId 文件id）
	 * @return
	 */
	YunDiskContentVO getYunDiskContent(YunDiskReqDTO dto);

}
