package com.zyhl.yun.api.outer.application.handle.chat.impl;

import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.utils.StopWatchUtil;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.handle.chat.AbstractChatAddHandler;
import com.zyhl.yun.api.outer.application.service.chat.DialogueIntentionService;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.domainservice.SearchReturnTermsService;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.TalkTypeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

/**
 * 对话前处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ChatBeforeHandlerImpl extends AbstractChatAddHandler {

    @Resource
    private UidGenerator uidGenerator;

    @Resource
    private DialogueIntentionService dialogueIntentionService;

    @Resource
    private SearchReturnTermsService searchReturnTermsService;

    @Resource
    private SourceChannelsProperties sourceChannelsProperties;

    @Resource(name = "dialogueIntentionThreadPool")
    private ExecutorService dialogueIntentionThreadPool;

    @Override
    public int order() {
        return ExecuteSort.CHAT_BEFORE.getSort();
    }

    @Override
    public boolean run(ChatAddInnerDTO innerDTO) {
        log.info("进入对话前处理");
        // 生成对话id
        long dialogueId = uidGenerator.getUID();
        innerDTO.setDialogueId(dialogueId);

        // 知识库对话，强制走知识库检索进行文生文
        if (ResourceTypeEnum.isPersonalKnowledgeFile(innerDTO.getContent().getResourceType())) {
            innerDTO.setIntentionCode(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());
        }

        /** 意图识别预处理 */
        intentionPretreatment(innerDTO);

        /** set搜索返回词的异步处理future */
        innerDTO.setSearchReturnTermsFuture(searchReturnTermsService.getOptimizeReturnTermsFutureV1(dialogueId, innerDTO.getContent().getDialogue()));
        
        // 设置业务类型
        innerDTO.getContent().setBusinessType(sourceChannelsProperties.getType(innerDTO.getContent().getSourceChannel()));
        
        return true;
    }

    /**
     * 意图识别预处理
     *
     * @param innerDTO 用户输入对象
     */
    private void intentionPretreatment(ChatAddInnerDTO innerDTO) {
        // 判断是否执行【输入内容意图识别】
        if(!judgeInputIntention(innerDTO)){
            return;
        }
        Long dialogueId = innerDTO.getDialogueId();
        try {
            // 获取主线程的ThreadLocal信息，并且异步调用前，将父线程中的请求信息绑定给子线程
            RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo = RequestContextHolder.getThreadLocalInfoAndBindingAttributes();

            /** 意图识别预处理 */
            Future<DialogueIntentionVO> dialogueIntentionFuture = CompletableFuture.supplyAsync(() -> {
                StopWatch stopWatch = StopWatchUtil.createStarted();
                DialogueIntentionVO intentionVO = null;
                try {
                    // 非对话类型直接不执行
                    if (TalkTypeEnum.isNotDialogue(innerDTO.getContent().getDialogueType())) {
                        return null;
                    }

                    // 把主线程ThreadLocal信息set到子线程
                    RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);

                    // 意图识别
                    intentionVO = dialogueIntentionService.getIntentionVO(innerDTO);

                    // 第一个意图
					String currentIntentionCode = intentionVO.getIntentionInfoList().get(0).getIntention();

					// 云邮助手不支持的意图，默认为文生文
					String sourceChannel = innerDTO.getContent().getSourceChannel();
					String businessType = innerDTO.getContent().getBusinessType();
					boolean isMail = SourceChannelsProperties.isMailChannel(sourceChannel);
					boolean isMailNotSupportIntention = DialogueIntentionEnum.isMailNotSupportIntention(businessType,
							currentIntentionCode);
					log.info(
							"intentionPretreatment sourceChannel:{}, businessType:{}, currentIntentionCode:{}, isMail:{}, isMailNotSupportIntention:{}",
							sourceChannel, businessType, currentIntentionCode, isMail, isMailNotSupportIntention);
					if (isMail && isMailNotSupportIntention) {
						// 默认为文生文
						currentIntentionCode = DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode();
						intentionVO.getIntentionInfoList().get(0).setIntention(currentIntentionCode);
					}
                } catch (Exception e) {
                    log.error("意图识别预处理，异常", e);
                } finally {
                    log.info("意图识别预处理，耗时：{}", StopWatchUtil.logTime(stopWatch));
                    StopWatchUtil.clearDuration();
                }
                return intentionVO;
            }, dialogueIntentionThreadPool);
            // set意图识别预处理future
            innerDTO.setDialogueIntentionFuture(dialogueIntentionFuture);
        } catch (Exception e) {
            log.error("意图识别预处理，线程池-异常", e);
        } finally {
            log.info("意图识别预处理，dialogueId：{}", dialogueId);
        }
    }

}
