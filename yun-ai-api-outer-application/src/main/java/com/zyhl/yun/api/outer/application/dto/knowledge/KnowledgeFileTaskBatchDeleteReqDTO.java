package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 知识库导入任务批量删除请求参数
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KnowledgeFileTaskBatchDeleteReqDTO extends BaseChannelDTO implements Serializable {


    /**
     * 导入任务ID列表
     */
    private List<String> taskIdList;
} 