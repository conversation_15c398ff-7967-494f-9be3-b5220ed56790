package com.zyhl.yun.api.outer.application.strategy;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.client.centertask.vo.CenterTaskCreateVO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddContentDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.LastDialogueInfoDTO;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatAddCheckService;
import com.zyhl.yun.api.outer.application.service.chat.ChatApplicationTypeService;
import com.zyhl.yun.api.outer.application.service.external.MemberCenterService;
import com.zyhl.yun.api.outer.config.IntentionCompanyProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.domain.entity.centertask.CenterTaskCreateEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.ImageParamEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.TextModelParamEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.TextParamEntity;
import com.zyhl.yun.api.outer.domain.vo.ChatConfigVO;
import com.zyhl.yun.api.outer.domainservice.BenefitNoDomainService;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ImageParamTypeEnum;
import com.zyhl.yun.api.outer.enums.ImageTransmissionTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.ApiVersionEnum;
import com.zyhl.yun.api.outer.external.CenterTaskExternalService;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 对话任务管理器\
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DialogueTaskManager {

    private final CenterTaskExternalService centerTaskExternalService;

    private final IntentionCompanyProperties intentionCompanyProperties;

    private final ChatApplicationTypeService applicationTypeService;

    private final MemberCenterService memberService;

    private final BenefitNoDomainService benefitNoDomainService;

    private final SourceChannelsProperties sourceChannelsProperties;

    private final AlgorithmChatAddCheckService chatAddCheckService;

    /**
     * 支持EOS存储的版本号列表
     */
    private static final List<String> EOS_VERSION_LIST = Arrays.asList(
            ApiVersionEnum.V3.getVersion(),
            ApiVersionEnum.V4.getVersion(),
            ApiVersionEnum.V6.getVersion()
    );

    /**
     * 调创建任务接口生成任务
     *
     * @param dto 会话请求参数
     * @param intention 意图
     * @param model 模型
     * @param dialogueId 对话id
     */
    public CenterTaskCreateVO initializeDialogueIntentionTask(AlgorithmChatAddDTO dto, String intention, ChatConfigVO model, Long dialogueId, String channel) throws YunAiBusinessException {

        CenterTaskCreateEntity centerTaskCreateEntity = new CenterTaskCreateEntity();
        centerTaskCreateEntity.setId(dto.getTaskId());
        centerTaskCreateEntity.setUserId(dto.getUserId());
        centerTaskCreateEntity.setAlgorithmCodes(Collections.singletonList(intention));
        centerTaskCreateEntity.setSourceChannel(dto.getContent().getSourceChannel());

        DialogueIntentionEnum dialogueIntentionEnum = DialogueIntentionEnum.getByCodeOrDefault(intention);

        if (isPictureGeneration(intention)) {
            // 文生图
            return textToPicture(dialogueId, dto, centerTaskCreateEntity, dialogueIntentionEnum);
        } else if (isImageRelatedGeneration(intention)) {
            // 图片工具
            return imageTool(dialogueId, dto, centerTaskCreateEntity, dialogueIntentionEnum);
        } else {
            // 默认是文本意图
            //查询AI提示词模板表
            String promptTemplate = chatAddCheckService.getDialoguePrompt(dto.getContent().getPrompt(), channel);
            dto.getContent().setPrompt(CharSequenceUtil.emptyToDefault(promptTemplate, ""));
            return textToText(model, dialogueId, dto, centerTaskCreateEntity, dialogueIntentionEnum);
        }

    }

    public boolean isPictureGeneration(String intention) {
        return DialogueIntentionEnum.TEXT_GENERATE_PICTURE.getCode().equals(intention);
    }

    public boolean isImageRelatedGeneration(String intention) {
        return DialogueIntentionEnum.PICTURE_GENERATE_TEXT.getCode().equals(intention) ||
                DialogueIntentionEnum.AI_HEAD_SCULPTURE.getCode().equals(intention) ||
                DialogueIntentionEnum.PICTURE_COMIC_STYLE.getCode().equals(intention) ||
                DialogueIntentionEnum.OLD_PHOTOS_REPAIR.getCode().equals(intention) ||
                DialogueIntentionEnum.LIVE_PHOTOS.getCode().equals(intention) ||
                DialogueIntentionEnum.IMAGE_QUALITY_RESTORATION.getCode().equals(intention) ||
                DialogueIntentionEnum.SMART_FAKE_CHECK.getCode().equals(intention);
    }

    private CenterTaskCreateVO textToText(ChatConfigVO model, Long dialogueId,
                                          AlgorithmChatAddDTO dto, CenterTaskCreateEntity centerTaskCreateEntity,
                                          DialogueIntentionEnum dialogueIntentionEnum) {
        // 获取应用关联id
        String typeRelationId = applicationTypeService.getTypeRelationId(dto.getApplicationId(), dto.getApplicationType());

        // 扣减权益
        memberService.consumeBenefit(dto, RequestContextHolder.getPhoneNumber(), dialogueId);

        try {
            // 设置文本大模型入参Entity
            TextModelEnum textModel = TextModelEnum.getByCode(model.getModelType());
            if (textModel != null) {
                centerTaskCreateEntity.setSupplierTypes(Collections.singletonList(String.valueOf(textModel.getSupplier())));
            }

            TextModelParamEntity textModelParamEntity = new TextModelParamEntity();
            textModelParamEntity.setUserId(dto.getUserId());
            textModelParamEntity.setSessionId(dto.getSessionId());
            textModelParamEntity.setRowkey(AiTextResultRepository.createRowKey(dto.getUserId(), dialogueId));
            textModelParamEntity.setCommands(dto.getContent().getPrompt());
            textModelParamEntity.setDialogueId(String.valueOf(dialogueId));

            // 设置通义星尘入参
            textModelParamEntity.setTypeRelationId(typeRelationId);

            // 调用算法中心创建算法任务
            return centerTaskExternalService.createTextModelTask(dialogueIntentionEnum, centerTaskCreateEntity, textModelParamEntity);

        } catch (Exception e) {
            // 回滚权益
            memberService.consumeBenefitFail(dto.getUserId(), RequestContextHolder.getPhoneNumber(), dialogueId);
            throw e;
        }
    }

    /**
     * 文生图
     *
     * @param dialogueId 对话id
     * @param dto 请求对象
     * @param centerTaskCreateEntity 任务创建实体
     * @param dialogueIntentionEnum 对话意图枚举
     * @return 任务创建对象
     */
    private CenterTaskCreateVO textToPicture(Long dialogueId, AlgorithmChatAddDTO dto, CenterTaskCreateEntity centerTaskCreateEntity, DialogueIntentionEnum dialogueIntentionEnum) {
        centerTaskCreateEntity.setSupplierTypes(intentionCompanyProperties.getSupplierTypes(dialogueIntentionEnum));

        TextParamEntity textParamEntity = new TextParamEntity();
        textParamEntity.setUserId(dto.getUserId());
        textParamEntity.setRowkey(AiTextResultRepository.createRowKey(dto.getUserId(), dialogueId));
        textParamEntity.setDialogueId(String.valueOf(dialogueId));
        // 设置图片存储类型
        textParamEntity.setImageTransmissionType(EOS_VERSION_LIST.contains(dto.getApiVersion()) ? ImageTransmissionTypeEnum.EOS.getCode() : ImageTransmissionTypeEnum.YUN_DISK.getCode());

        return centerTaskExternalService.createTextTask(dialogueIntentionEnum, centerTaskCreateEntity, textParamEntity);
    }

    /**
     * 图片工具
     *
     * @param dialogueId 对话id
     * @param dto 对话请求对象
     * @param centerTaskCreateEntity 会话创建实体
     * @param dialogueIntentionEnum 对话意图枚举
     * @return 任务创建结果
     */
    private CenterTaskCreateVO imageTool(Long dialogueId, AlgorithmChatAddDTO dto, CenterTaskCreateEntity centerTaskCreateEntity, DialogueIntentionEnum dialogueIntentionEnum) {
        // 根据不同的对话意图进行配置处理
        centerTaskCreateEntity.setSupplierTypes(intentionCompanyProperties.getSupplierTypes(dialogueIntentionEnum));

        ImageParamEntity imageEntity = new ImageParamEntity();
        // 存在上次对话信息 取对应的内容
        AlgorithmChatAddContentDTO contentDto = dto.getContent();
        if (contentDto.getLastDialogueInfo() != null) {
            LastDialogueInfoDTO lastInfo = contentDto.getLastDialogueInfo();
            imageEntity.setImageParamType(lastInfo.getImageParamType());
            //有本地共享存储，则需要使用
            imageEntity.setLocalPath(lastInfo.getLocalPath());
            imageEntity.setFileUrl(lastInfo.getFileUrl());
            imageEntity.setFileId(lastInfo.getFileId());
            imageEntity.setImageExt(lastInfo.getImageExt());
        } else {
            imageEntity.setFileId(dto.getContent().getResourceId());
            imageEntity.setImageParamType(ImageParamTypeEnum.FILE_ID.getCode());
        }
        imageEntity.setRowkey(AiTextResultRepository.createRowKey(dto.getUserId(), dialogueId));

        // 设置图片存储类型
        imageEntity.setImageTransmissionType(EOS_VERSION_LIST.contains(dto.getApiVersion()) ? ImageTransmissionTypeEnum.EOS.getCode() : ImageTransmissionTypeEnum.YUN_DISK.getCode());

        if (DialogueIntentionEnum.LIVE_PHOTOS.equals(dialogueIntentionEnum)) {
            //活照片不管版本号，默认云盘存储（前端播放问题暂未解决，2024-06-07临时设置）
            imageEntity.setImageTransmissionType(ImageTransmissionTypeEnum.YUN_DISK.getCode());
            //活照片默认全身模式 style=1
            imageEntity.setStyle("1");
            // 小天助手的活照片需要指定保存路径
            if (sourceChannelsProperties.isXiaoTian(dto.getContent().getSourceChannel())) {
                imageEntity.setYunPath(intentionCompanyProperties.getYunPath());
            }
        }

        String sourceChannel = centerTaskCreateEntity.getSourceChannel();
        String clientType = RequestContextHolder.getClientType();
        String algorithmCode = dialogueIntentionEnum.getCode();
        String benefitNoTool = benefitNoDomainService.getBenefitNoForTool(sourceChannel, clientType, algorithmCode);
        //权益编码获取到，并且没关闭开关
        Boolean toolPay = StringUtils.isNotBlank(benefitNoTool)
                && benefitNoDomainService.benefitNoForToolOpen(sourceChannel, clientType);
        log.info(
                "getBenefitNoForTool dialogueId:{} 参数 sourceChannel:{},clientType:{},algorithmCode:{} 获取 benefitNoTool:{}, toolPay:{}",
                dialogueId, sourceChannel, clientType, algorithmCode, benefitNoTool, toolPay);
        //工具是否付费
        imageEntity.setToolPay(toolPay);
        return centerTaskExternalService.createImageTask(dialogueIntentionEnum, centerTaskCreateEntity, imageEntity);
    }

}
