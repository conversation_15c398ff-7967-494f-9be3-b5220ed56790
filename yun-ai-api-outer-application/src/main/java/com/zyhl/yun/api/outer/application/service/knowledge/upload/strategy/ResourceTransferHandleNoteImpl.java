package com.zyhl.yun.api.outer.application.service.knowledge.upload.strategy;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.vo.CheckResultVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.OwnerDriveClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDriveReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveVO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileCheckReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.ResourceTransferReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.upload.AbstractResourceTransferHandle;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeFileImportVO;
import com.zyhl.yun.api.outer.config.FileCheckConfig;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.domain.entity.PersonalKnowledgeImportTaskEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeUploadEntity;
import com.zyhl.yun.api.outer.domain.vo.ImportNoteInfoVO;
import com.zyhl.yun.api.outer.domain.vo.PersonalKnowledgeImportCheckResult;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchNoteParam;
import com.zyhl.yun.api.outer.domainservice.CheckSystemDomainService;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileAuditStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeUploadStatusEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述：笔记资源文件处理
 *
 * <AUTHOR> zhumaoxian  2025/3/21 17:19
 */
@Slf4j
@Component
public class ResourceTransferHandleNoteImpl extends AbstractResourceTransferHandle {

    @Resource
    private OwnerDriveClient ownerDriveClient;

    @Override
    public void afterPropertiesSet() throws Exception {
        register(KnowledgeResourceTypeEnum.NOTE.getCode(), this);
    }

    @Override
    public KnowledgeFileImportVO trans(ResourceTransferReqDTO dto) {
        // 校验参数
        checkNoteReq(dto);
        // 校验空间
        checkKnowledgeDriveSize(dto);
        // 已入库的笔记
        Map<String, String> existUploadMap = new HashMap<>(Const.NUM_32);
        Map<String, String> existFileMap = new HashMap<>(Const.NUM_32);
        // 获取已导入数据
        checkExit(dto, existUploadMap, existFileMap);
        // 已入库的笔记
        List<UserKnowledgeUploadEntity> addList = new ArrayList<>();
        // 重复的笔记
        List<String> noteIds = new ArrayList<>();
        // 存放返回结果
        List<PersonalKnowledgeImportCheckResult> checkResult = new ArrayList<>();
        // 查询目录ID
        UserKnowledgeEntity knowledgeEntity = userKnowledgeRepository.selectById(dto.getBaseId());
        if (Objects.isNull(knowledgeEntity) || CharSequenceUtil.isBlank(knowledgeEntity.getFolderId())) {
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_ID_NOT_EXIST);
        }
        String catalogId = knowledgeEntity.getFolderId();
        UserKnowledgeFileEntity fileEntity = null;
        if (ObjectUtil.isNotEmpty(dto.getParentFileId())) {
            fileEntity = userKnowledgeFileRepository.selectByFileId(dto.getUserId(), dto.getParentFileId());
        }
        for(ImportNoteInfoVO noteInfoVO : dto.getNoteList()){
            String uploadNoteId = existUploadMap.get(noteInfoVO.getNoteId());
            String fileNoteId = existFileMap.get(noteInfoVO.getNoteId());
            if (ObjectUtil.isNotEmpty(uploadNoteId) || ObjectUtil.isNotEmpty(fileNoteId)) {
                PersonalKnowledgeImportCheckResult importCheckResult = new PersonalKnowledgeImportCheckResult();
                importCheckResult.setResourceType(dto.getResourceType());
                importCheckResult.setCheckResult(1);
                importCheckResult.setFailMessage("文件已上传");
                importCheckResult.setNoteList(noteInfoVO);
                checkResult.add(importCheckResult);
                continue;
            }
            if(noteIds.contains(noteInfoVO.getNoteId())){
                log.info("笔记重复上传，noteId：{}", noteInfoVO.getNoteId());
                continue;
            }
            noteIds.add(noteInfoVO.getNoteId());
            UserKnowledgeUploadEntity insertEntity = new UserKnowledgeUploadEntity();
            insertEntity.setId(uidGenerator.getUID());
            insertEntity.setBaseId(dto.getBaseId());
            insertEntity.setUserId(dto.getUserId());
            insertEntity.setFileId(noteInfoVO.getNoteId());
            insertEntity.setResourceType(dto.getResourceType());
            insertEntity.setResource(JSON.toJSONString(noteInfoVO));
            insertEntity.setOwnerId(dto.getUserId());
            insertEntity.setOwnerType(OwnerTypeEnum.NOTE.getOwnerValue());
            insertEntity.setUploadStatus(KnowledgeUploadStatusEnum.NOT_PROCESSED.getStatus());
            insertEntity.setPaasCode(String.valueOf(RequestContextHolder.getBelongsPlatform()));
            insertEntity.setFileType(FileTypeEnum.FILE.getKnowledgeFileType());
            insertEntity.setTargetParentFileId(catalogId);
            insertEntity.setTargetParentFilePath(catalogId);
            if(Objects.nonNull(fileEntity)){
                insertEntity.setTargetParentFileId(fileEntity.getFileId());
                insertEntity.setTargetParentFilePath(fileEntity.getParentFilePath() + "/" + fileEntity.getFileId());
            }
            // 标题送审
            if (Boolean.FALSE.equals(fileCheck(noteInfoVO, dto.getUserId()))) {
                PersonalKnowledgeImportCheckResult importCheckResult = new PersonalKnowledgeImportCheckResult();
                importCheckResult.setResourceType(dto.getResourceType());
                importCheckResult.setCheckResult(Integer.valueOf(ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultCode()));
                importCheckResult.setFailMessage(ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultMsg());
                importCheckResult.setNoteList(noteInfoVO);
                checkResult.add(importCheckResult);
                // 送审不通过，修改upload表状态
                insertEntity.setUploadStatus(KnowledgeUploadStatusEnum.FAIL.getStatus());
                insertEntity.setResultCode(ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultCode());
                insertEntity.setResultMsg(ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultMsg());
            }
            addList.add(insertEntity);
        }
        KnowledgeFileImportVO result = new KnowledgeFileImportVO();
        result.setErrorList(checkResult);
        // 发送MQ
        sendNoteTaskMq(addList);
        return result;
    }

    /**
     * 笔记标题送审
     * @param noteInfoVO 上传列表
     */
    private Boolean fileCheck(ImportNoteInfoVO noteInfoVO, String userId) {
        boolean enabled = fileCheckConfig.isEnabled();
        log.info("batchImport note fileCheckConfig:{}",fileCheckConfig);
        if(Boolean.FALSE.equals(enabled)){
            // 开关打开
            long uid = uidGenerator.getUID();
            CheckResultVO resultVo = checkSystemDomainService.checkPlatformAndLocal(uid, userId, noteInfoVO.getTitle());
            return !CheckResultVO.isFail(resultVo);
        }
        return Boolean.TRUE;
    }

    private void checkExit(ResourceTransferReqDTO dto, Map<String, String> existUploadMap, Map<String, String> existFileMap) {
        List<String> idList = dto.getNoteList().stream().map(ImportNoteInfoVO::getNoteId).collect(Collectors.toList());
        List<PersonalKnowledgeImportTaskEntity> taskEntities = userKnowledgeUploadRepository.findListByParam(dto.getUserId(),
                String.valueOf(dto.getBaseId()), dto.getResourceType(), idList, KnowledgeUploadStatusEnum.NOT_PROCESSED.getStatus());
        if(!CollectionUtils.isEmpty(taskEntities)){
            taskEntities.forEach(entity -> existUploadMap.put(entity.getFileId(), entity.getFileId()));
        }
        List<UserKnowledgeFileEntity> userKnowledgeFileEntities = userKnowledgeFileRepository.selectByOldFileIds(dto.getBaseId(),dto.getUserId(), idList);
        if(!CollectionUtils.isEmpty(userKnowledgeFileEntities)){
            userKnowledgeFileEntities.forEach(entity -> existFileMap.put(entity.getOldFileId(), entity.getOldFileId()));
        }
    }

    private void checkKnowledgeDriveSize(ResourceTransferReqDTO dto) {

        // 获取独立空间
        OwnerDriveReqDTO reqDTO = new OwnerDriveReqDTO();
        reqDTO.setUserId(dto.getUserId());
        reqDTO.setClientInfo(RequestContextHolder.getClientInfo());
        reqDTO.setAppChannel(RequestContextHolder.getAppChannel());

        OwnerDriveVO vo = ownerDriveClient.getOwnerDrive(reqDTO);
        if (vo.getTotalSize().compareTo(vo.getUsedSize()) <= 0) {
            log.info("获取用户独立空间ID，空间id：{}，状态：{}，总空间：{}，已使用空间：{}", vo.getDriveId(), vo.getStatus(), vo.getTotalSize(), vo.getUsedSize());
            throw new YunAiBusinessException(ResultCodeEnum.LACK_OF_SPACE);
        }
    }

    @Override
    public void check(KnowledgeFileCheckReqDTO dto) {
        //暂不需要校验
    }

    private void checkNoteReq(ResourceTransferReqDTO dto){

        if(dto.getBaseId() == null){
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_ID_NOT_PROVIDE);
        }
        if(dto.getUserId() == null){
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_USERID);
        }
        if(CollectionUtils.isEmpty(dto.getNoteList())){
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        List<Integer> noteTypeList = ListUtil.toList(SearchNoteParam.NoteTypeEnum.TYPE0.getCode(),
                SearchNoteParam.NoteTypeEnum.TYPE1.getCode(), SearchNoteParam.NoteTypeEnum.TYPE2.getCode(),
                SearchNoteParam.NoteTypeEnum.TYPE3.getCode(),SearchNoteParam.NoteTypeEnum.TYPE4.getCode());
        for(ImportNoteInfoVO note : dto.getNoteList()){
            if(note.getNoteId() == null){
                throw new YunAiBusinessException(ResultCodeEnum.NOTE_ID_IS_NULL);
            }
            if(!(note.getNoteType() == null || noteTypeList.contains(note.getNoteType()))){
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }
        }
    }
}
