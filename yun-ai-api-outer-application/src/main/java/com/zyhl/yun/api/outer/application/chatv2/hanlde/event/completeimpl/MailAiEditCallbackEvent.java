package com.zyhl.yun.api.outer.application.chatv2.hanlde.event.completeimpl;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.converter.MarkdownToDocxConverter;
import com.zyhl.hcy.yun.ai.common.base.converter.exception.ConversionException;
import com.zyhl.hcy.yun.ai.common.base.converter.impl.FlexmarkPoiConverter;
import com.zyhl.hcy.yun.ai.common.base.enums.AIModuleEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.FileUploadVO;
import com.zyhl.yun.api.outer.application.assembler.ChatFlowResultAssembler;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.event.AbstractCompleteCallbackEvent;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.pojo.CompleteEvent;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatHistoryService;
import com.zyhl.yun.api.outer.config.MailAiEditProperties;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatMessageEntity;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddFlowStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.OutAuditStatusEnum;
import com.zyhl.yun.api.outer.external.YunDiskExternalService;
import com.zyhl.yun.api.outer.repository.AlgorithmChatMessageRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 下一个执行AI编辑处理器事件：markdown文本转换为word--》上传个人云--》获取个人云文件信息--》返回文件信息给用户
 * 
 * <AUTHOR>
 * @date 2025-04-29
 */
@Slf4j
@Component
public class MailAiEditCallbackEvent extends AbstractCompleteCallbackEvent {

	@Resource
	private DataSaveService dataSaveService;
	@Resource
	private YunDiskExternalService yunDiskExternalService;

	@Resource
	private MailAiEditProperties mailAiEditProperties;
	@Resource
	private ChatFlowResultAssembler chatFlowResultAssembler;
	@Resource
	private AlgorithmChatHistoryService algorithmChatHistoryService;

	@Resource
	private AlgorithmChatMessageRepository algorithmChatMessageRepository;

	@Override
	public void complete(CompleteEvent data) {
		// 继续执行AI编辑后续逻辑
		log.info("继续执行handler-AI编辑 dialogueId:{}", data.getHandleDTO().getDialogueId());
		aiEditContinue(data);
	}


	/**
	 * 流式对话结束回调处理
	 */
	public void aiEditContinue(CompleteEvent data) {
		long startTime = System.currentTimeMillis();
		long prepareContentTime = 0;
		long convertToDocxTime = 0;
		long uploadFileTime = 0;
		long buildFileInfoTime = 0;
		long updateResultTime = 0;

		String resultCode = data.getResultCode();
		ChatAddHandleDTO handleDTO = data.getHandleDTO();
		SseEventListener event =  data.getEventListener();
		ChatStatusEnum chatStatus = ChatStatusEnum.CHAT_FAIL;
		if (ResultCodeEnum.SUCCESS.getResultCode().equals(resultCode)) {
			String dialogue = handleDTO.getInputInfoDTO().getDialogue();
			String prompt =handleDTO.getInputInfoDTO().getPrompt();
			String resourceContent =handleDTO.getResourceContent();

			// 获取大模型返回结果，获取markdown格式文件数据
			String allMsg = data.getOutContent();
			try {
				long currentTime = System.currentTimeMillis();
				//第一次可能是文本也可能是文件，文本原文需要生成文件，最终都要传一份文件到 个人云/AI文件库/AI编辑
				String summaryPromptKey = mailAiEditProperties.getSummaryPromptKey();
//				String processPromptKey = mailAiEditProperties.getProcessPromptKey();
				String fileContent = "";
				String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
				String fileName = "AI编辑_" + timestamp;
				String summary = "";
				if (summaryPromptKey.equals(prompt)) {
					summary = allMsg;
					// 首次会话，如果用户传了文件，则将文件内容生成word上传个人云盘，如果用户没有传文件，则将用户输入的内容生成word上传个人云盘
					if (StringUtils.isNotEmpty(resourceContent)) {
						fileContent = resourceContent;
						if (StringUtils.isNotEmpty(handleDTO.getResourceName())) {
							// 如果有原始文件名，使用原始文件名加上时间戳
							String originalName = handleDTO.getResourceName();
							// 去掉可能的扩展名
							int dotIndex = originalName.lastIndexOf('.');
							if (dotIndex > 0) {
								originalName = originalName.substring(0, dotIndex);
							}
							fileName = originalName + "_" + timestamp;
						}
					} else {
						fileContent = dialogue;
					}
				}

				if (!summaryPromptKey.equals(prompt)) {
					// 后续会话，将大模型生成的内容上传到个人云盘
					String[] segment = getSegments(allMsg);
					if (segment.length == 3) {
						summary = segment[0];
						// 去除markdown标题中的目录符号后再拼接文件名
						String cleanTitle = removeMarkdownTitleSymbols(segment[1]);
						fileName = cleanTitle + "_" + timestamp;
						fileContent = segment[2];
					}
				}
				prepareContentTime = System.currentTimeMillis() - currentTime;
				currentTime = System.currentTimeMillis();

				// 将markdown格式文件数据转word文件
				ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
				MarkdownToDocxConverter markdownToDocxConverter = new FlexmarkPoiConverter();
				byte[] docxBytes;

				// 检查fileContent是否为空
				if (StringUtils.isBlank(fileContent)) {
					log.error("文件内容为空，无法进行转换");
					handleDTO.getRespVO().setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
					BaseResult<?> result = BaseResult.error(AiResultCode.CODE_10000016.getCode(), "解析失败，无法进行转换");
					handleDTO.getSseEmitterOperate().sendAndComplete(result);
					algorithmChatHistoryService.updateOutResult(event.getDialogId(), OutAuditStatusEnum.SUCCESS, chatStatus, null);
					return;
				}

				try {
					markdownToDocxConverter.convertMarkdownToDocx(fileContent, outputStream);
					docxBytes = outputStream.toByteArray();

					if (docxBytes.length == 0) {
						log.error("转换后的Word文档为空");
						handleDTO.getRespVO().setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
						BaseResult<?> result = BaseResult.error(AiResultCode.CODE_10000016.getCode(), "转换后的Word文档为空");
						handleDTO.getSseEmitterOperate().sendAndComplete(result);
						algorithmChatHistoryService.updateOutResult(event.getDialogId(), OutAuditStatusEnum.SUCCESS, chatStatus, null);
						return;
					}
				} catch (ConversionException e) {
					log.error("Markdown转Word转换失败: {}", e.getMessage(), e);
					handleDTO.getRespVO().setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
					BaseResult<?> result = BaseResult.error(AiResultCode.CODE_10000016.getCode(), "Markdown转Word转换失败: " + e.getMessage());
					handleDTO.getSseEmitterOperate().sendAndComplete(result);
					algorithmChatHistoryService.updateOutResult(event.getDialogId(), OutAuditStatusEnum.SUCCESS, chatStatus, null);
					return;
				}

				convertToDocxTime = System.currentTimeMillis() - currentTime;

				// 将word文件上传到个人云，并获取文件信息
				String userId = handleDTO.getReqDTO().getUserId();
				if (handleDTO.getRespVO().getFlowResult().getFileList()==null || handleDTO.getRespVO().getFlowResult().getFileList().size() == 0) {
					log.info("MailAiEditCallbackEvent-run, fileList is empty");
					currentTime = System.currentTimeMillis();
					// 指定保存路径
					String path = mailAiEditProperties.getFilePath(); 
					String fileSuffix = "docx";
					String base64 = Base64.getEncoder().encodeToString(docxBytes);
					FileUploadVO fileUploadVo = yunDiskExternalService.uploadFileToCustomPath(path, AIModuleEnum.MAILAI_EDIT, userId, RequestContextHolder.getBelongsPlatform(), fileSuffix, base64, fileName);
					uploadFileTime = System.currentTimeMillis() - currentTime;
					if (fileUploadVo != null && StringUtils.isNotEmpty(fileUploadVo.getFileId())) {
						currentTime = System.currentTimeMillis();
						// 构建文件信息对象
						File file = new File();
						file.setFileId(fileUploadVo.getFileId());
						file.setParentFileId(fileUploadVo.getCategoryId());
						file.setName(fileName + "." + fileSuffix);
						file.setType("file");
						file.setFileExtension(fileSuffix);
						file.setCategory("doc");
						file.setSize((long) docxBytes.length);
						// 获取当前时间
						OffsetDateTime now = OffsetDateTime.now();
						// 定义 RFC 3339 格式
						DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
						// 格式化时间
						String rfc3339Time = now.format(formatter);
						file.setCreatedAt(rfc3339Time);
						file.setUpdatedAt(rfc3339Time);
						file.setLocalCreatedAt(rfc3339Time);
						file.setLocalUpdatedAt(rfc3339Time);
						buildFileInfoTime = System.currentTimeMillis() - currentTime;
						// 将文件信息流式返回给前端, 关闭sse
						List<File> fileList = new ArrayList<>();
						fileList.add(file);
						handleDTO.getRespVO().getFlowResult().setFileList(fileList);
					}
				}

				if (handleDTO.getRespVO().getFlowResult().getFileList()!=null && handleDTO.getRespVO().getFlowResult().getFileList().size() > 0) {
					currentTime = System.currentTimeMillis();

					handleDTO.getRespVO().getFlowResult().setOutContent(summary);
					handleDTO.getRespVO().getFlowResult().setResultType(FlowResultTypeEnum.DOCUMENT.getType());
					handleDTO.getRespVO().setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
					handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(handleDTO.getRespVO()));
					//更新base
					DialogueFlowResult flowResult =chatFlowResultAssembler.getFlowResult(handleDTO.getRespVO().getFlowResult());
					dataSaveService.updateResult(handleDTO.getReqDTO().getUserId(), String.valueOf(handleDTO.getDialogueId()), flowResult);
					chatStatus = ChatStatusEnum.CHAT_SUCCESS;

					//更新对话标题，改成文件名
					AlgorithmChatMessageEntity algorithmChatMessageEntity = AlgorithmChatMessageEntity.builder()
							.userId(userId)
							.id(handleDTO.getSessionId())
							.title("AI编辑 | "+fileName)
							.build();
					algorithmChatMessageRepository.updateMessage(algorithmChatMessageEntity);
					updateResultTime = System.currentTimeMillis() - currentTime;
				} else {
					log.error("文件上传失败，用户ID：{}, 总耗时:{}ms", userId, (System.currentTimeMillis() - startTime));
					handleDTO.getRespVO().setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
					BaseResult<?> result = BaseResult.error(AiResultCode.CODE_10000017.getCode(), "文件上传失败");
					handleDTO.getSseEmitterOperate().sendAndComplete(result);
				}
				// 统一打印各环节耗时
				log.info("AI编辑处理完成，dialogueId:{}, 总耗时:{}ms, 内容准备:{}ms, 转换Word:{}ms, 上传文件:{}ms, 构建文件信息:{}ms, 更新结果:{}ms",
						handleDTO.getDialogueId(),
						(System.currentTimeMillis() - startTime),
						prepareContentTime,
						convertToDocxTime,
						uploadFileTime,
						buildFileInfoTime,
						updateResultTime);
			} catch (Exception e) {
				log.error("处理AI编辑结果时发生错误，总耗时:{}ms：", (System.currentTimeMillis() - startTime), e);
				handleDTO.getRespVO().setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
				BaseResult<?> result = BaseResult.error(AiResultCode.CODE_10000016.getCode(), "处理AI编辑结果时发生错误");
				handleDTO.getSseEmitterOperate().sendAndComplete(result);
			}
		} else {
			log.error("AI编辑处理失败，结果码：{}, 总耗时:{}ms", resultCode, (System.currentTimeMillis() - startTime));
			handleDTO.getRespVO().setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
			BaseResult<?> result = BaseResult.error(resultCode, data.getResultMsg());
			data.getHandleDTO().getSseEmitterOperate().sendAndComplete(result);
		}
		// 更新会话状态
		algorithmChatHistoryService.updateOutResult(event.getDialogId(), OutAuditStatusEnum.SUCCESS, chatStatus, null);
	}

	/**
	 * 获取分割的段落
	 * @param input 输入内容
	 * @return 输出分段数组
	 */
	private String[] getSegments(String input) {
		// 定义正则表达式模式
		String pattern = "(?<=<!-- segment:(processing|title|content) -->\\n)[\\s\\S]*?(?=\\n\\s*<!-- segment:|$)";

		// 创建匹配器
		Matcher matcher = Pattern.compile(pattern).matcher(input);

		// 创建存储结果的数组
		String[] results = new String[3];
		int index = 0;

		// 查找匹配内容
		while (matcher.find() && index < 3) {
			results[index] = matcher.group().trim();
			index++;
		}
		return results;
	}

	/**
	 * 去除markdown标题中的目录符号（如#、##等）
	 * @param markdownTitle 包含markdown标题符号的标题文本
	 * @return 去除标题符号后的纯文本标题
	 */
	private String removeMarkdownTitleSymbols(String markdownTitle) {
		if (StringUtils.isBlank(markdownTitle)) {
			return markdownTitle;
		}
		// 匹配标题开头的#符号及其后的空格
		String pattern = "^#+\\s+";
		// 替换为空字符串
		return markdownTitle.replaceAll(pattern, "").trim();
	}

}
