package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.utils.StopWatchUtil;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.YunDiskClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.FileGetContentReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.enums.UserFileThumbnailStyleEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.AIFileVO;
import com.zyhl.hcy.yun.ai.common.platform.third.dto.GetUserInfoByPhoneNumberRespDTO;
import com.zyhl.yun.api.outer.application.assembler.ChatFileAssembler;
import com.zyhl.yun.api.outer.application.chatv2.service.YunDiskV2Service;
import com.zyhl.yun.api.outer.application.dto.YunDiskReqDTO;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.constants.FutureConstants;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Classname YunDiskV2ServiceImpl
 * @Description 云盘平台接口实现
 * @Date 2025/4/18 15:01
 */
@Service
@Slf4j
public class YunDiskV2ServiceImpl implements YunDiskV2Service {

    @Resource
    private ChatFileAssembler chatFileAssembler;
    @Resource
    private YunDiskClient yunDiskClient;

    /**
     * @param dto（fileId 文件id）
     * @return
     */
    @Override
    public File getYunDiskContent(YunDiskReqDTO dto) {
        GetUserInfoByPhoneNumberRespDTO userInfo = new GetUserInfoByPhoneNumberRespDTO();
        userInfo.setUserDomainId(Long.valueOf(RequestContextHolder.getUserId()));
        String fileId = dto.getFileId();
        String imageThumbnailStyle = dto.getImageThumbnailStyle();
        List<String> thumbnailStyleList = null;
        if (CharSequenceUtil.isNotBlank(imageThumbnailStyle)) {
            thumbnailStyleList = new ArrayList<>();
            thumbnailStyleList.add(imageThumbnailStyle);
        }
        FileGetContentReqDTO reqDTO = new FileGetContentReqDTO(fileId, true, thumbnailStyleList, userInfo);
        AIFileVO vo = yunDiskClient.getFileContent(reqDTO);
        if (null != vo) {
            File file = chatFileAssembler.getFileVo(vo.getFileResp());
            file.setFileId(vo.getContentId());
            file.setContent(vo.getContent());
            file.setThumbnailUrl(vo.getThumbnailUrl());
            return file;
        }
        return null;
    }

    @Override
    public File getYunDiskContentAndBigThumbnailUrl(YunDiskReqDTO dto) {
        try {
            dto.setImageThumbnailStyle(UserFileThumbnailStyleEnum.MIDDLE.getStyle());
            return getYunDiskContent(dto);
        } catch (Exception e) {
            log.warn("获取文件内容（缩略图类型Big）dto：{}，异常：", JsonUtil.toJson(dto), e);
        }
        return null;
    }

    @Override
    public Map<String, File> batchGetYunDiskContentAndBigThumbnailUrl(List<String> fileIdList, ExecutorService threadPool) {
        // 开启计时
        StopWatch stopWatch = StopWatchUtil.createStarted();
        Map<String, File> result = new ConcurrentHashMap<>(Const.NUM_16);
        try {
            if(CollUtil.isEmpty(fileIdList)){
                return result;
            }

            /** 添加处理任务 */
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            // 日志
            Map<String, String> mdcMap = MDC.getCopyOfContextMap();
            // 获取主线程的ThreadLocal信息，并且异步调用前，将父线程中的请求信息绑定给子线程
            RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo = RequestContextHolder.getThreadLocalInfoAndBindingAttributes();
            fileIdList.forEach(fileId -> {
                futures.add(asyncGetYunDiskContentAndBigThumbnailUrl(fileId, threadPool, mdcMap, mainThreadLocalInfo, result));
            });

            /** 过滤null，获取异步线程CompletableFuture集合 */
            List<CompletableFuture<Void>> completableFutureList = futures.stream().filter(Objects::nonNull).collect(Collectors.toList());

            /** 等到所有任务都完成，并设置超时 */
            if (CollUtil.isNotEmpty(completableFutureList)) {
                for (CompletableFuture<Void> future : completableFutureList) {
                    try {
                        future.get(FutureConstants.SIXTY_SECONDS, TimeUnit.SECONDS);
                    } catch (Exception e) {
                        log.error("批量获取文件内容（缩略图类型Big），future.get()，异常", e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("批量获取文件内容（缩略图类型Big），异常：", e);
        } finally {
            log.info("批量获取文件内容（缩略图类型Big），总耗时：{}，fileIdList：{}", StopWatchUtil.logTime(stopWatch), JSON.toJSONString(fileIdList));
            StopWatchUtil.clearDuration();
        }
        return result;
    }

    /**
     * 【异步】获取文件内容（缩略图类型Big）
     * @Author: WeiJingKun
     *
     * @param fileId 文件id
     * @param threadPool 线程池
     * @param mdcMap 日志
     * @param mainThreadLocalInfo 主线程的ThreadLocal信息
     * @param result 结果集
     * @return 异步任务
     */
    private CompletableFuture<Void> asyncGetYunDiskContentAndBigThumbnailUrl(String fileId, ExecutorService threadPool, Map<String, String> mdcMap, RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo, Map<String, File> result) {
        if (CharSequenceUtil.isNotBlank(fileId)) {
            return CompletableFuture.runAsync(() -> {
                MDC.setContextMap(mdcMap);
                // 把主线程ThreadLocal信息set到子线程
                RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);

                // 获取文件内容（缩略图类型Big）
                YunDiskReqDTO fileDto = new YunDiskReqDTO(fileId);
                File newFile = getYunDiskContentAndBigThumbnailUrl(fileDto);
                if (ObjectUtil.isNull(newFile)) {
                    newFile = new File();
                    newFile.setFileId(fileId);
                }
                result.put(fileId, newFile);
            }, threadPool);
        }
        return null;
    }

}
