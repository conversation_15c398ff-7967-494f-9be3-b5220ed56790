package com.zyhl.yun.api.outer.application.chatv2.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

/**
 * 描述：对话意图参数
 *
 * <AUTHOR> zhumaoxian  2025/4/12 9:43
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DialogueIntentionDTO {
	
	
    public DialogueIntentionDTO(String command) {
		this.command = command;
	}
    
    public DialogueIntentionDTO(IntentionInfo intentionInfo) {
    	if(null != intentionInfo) {
    		this.command = intentionInfo.getIntention();
    		this.subCommand = intentionInfo.getSubIntention();
    		this.argumentMap = intentionInfo.getArgumentMap();
    	}
	}
    
	/**
     * 意图指令，枚举值参考IntentionCommands
     * @see com.zyhl.yun.api.outer.enums.DialogueIntentionEnum
     */
    private String command;
    /**
     * 子意图
     */
    private String subCommand;
    /**
     * 实体信息
     * AI编程文件名key=aiCoderFileName
     */
    private Map<String, List<String>> argumentMap;
}
