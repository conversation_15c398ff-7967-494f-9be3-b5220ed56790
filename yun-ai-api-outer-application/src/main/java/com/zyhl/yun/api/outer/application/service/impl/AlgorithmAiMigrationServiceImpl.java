package com.zyhl.yun.api.outer.application.service.impl;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import com.zyhl.yun.api.outer.application.dto.AiMigrationAddReqDTO;
import com.zyhl.yun.api.outer.application.service.AlgorithmAiMigrationService;
import com.zyhl.yun.api.outer.enums.MigrationStatusEnum;
import com.zyhl.yun.api.outer.repository.AlgorithmAiMigrationRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * AI迁移表：小天助手1.0.1
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AlgorithmAiMigrationServiceImpl implements AlgorithmAiMigrationService {

    @Resource
    private AlgorithmAiMigrationRepository algorithmAiMigrationRepository;

    /**
     * 手机号码加密
     */
    private final String key = "3fda899ca6d94ac3";
    private final AES aes = SecureUtil.aes(key.getBytes());

    @Override
    public void add(AiMigrationAddReqDTO dto) {
        String userId = RequestContextHolder.getUserId();
        String phone = RequestContextHolder.getPhoneNumber();
        Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();
        Integer type = Integer.valueOf(dto.getType());
        log.info("【报名数据迁移】用户id：{}，手机号码：{}，所属平台：{}，设备信息：{}", userId, phone, belongsPlatform, dto.getDevice());
        if (algorithmAiMigrationRepository.getStatus(userId, type) != null) {
            log.info("【报名数据迁移】用户已报名，无需重复报名，用户id：{}", userId);
            return;
        }
        algorithmAiMigrationRepository.add(userId, aes.encryptHex(phone), belongsPlatform, dto.getDevice(), dto.getSourceChannel(), type);
    }

    @Override
    public int getStatus(String userId) {
        Integer status = algorithmAiMigrationRepository.getStatus(userId);
        log.info("【报名数据迁移】用户id：{}，数据迁移状态：{}", userId, status);
        return status == null ? MigrationStatusEnum.NO_REGISTER.getStatus() : status;
    }
}
