package com.zyhl.yun.api.outer.application.chatv2.vo;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueAttachmentDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueIntentionDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueVersionInfoDTO;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters.ExtInfoParam;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueIntentionOutput;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.valueobject.HtmlInfo;
import com.zyhl.yun.api.outer.domain.valueobject.KnowledgeSearchInfo;
import com.zyhl.yun.api.outer.domain.vo.ChatApplicationType;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.domain.vo.chat.ContentVO;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ImageTransmissionTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.AiTextResultVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddFlowStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.enums.task.TaskFeePaidStatusEnum;
import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;
import com.zyhl.yun.api.outer.vo.TaskRespParamVO;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 对话结果-VO
 *
 * @Author: WeiJingKun
 */
@Slf4j
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DialogueResultV2VO implements Serializable {

    private static final long serialVersionUID = -5303497430918399672L;

    /**
     * 【原接口的字段】 对话ID
     */
    private String dialogueId;
    /**
     * 【原接口的字段】 用户id
     */
    private String userId;
    /**
     * 【原接口的字段】 会话ID
     */
    private String sessionId;

    /**
     * 【原接口的字段】 状态
     *
     * @see com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum
     */
    private Integer status;

    /**
     * 【add接口传入的数据】字段里的inputTime使用的是哪个字段的值映射？ 【使用的createTime】 对话输入
     */
    private DialogueInputInfoDTO dialogueInputInfo;

    /**
     * 【对应之前的inAuditStatus】 输入内容审批结果;状态码：2通过，其他失败
     */
    private Integer inputAuditStatus;

    /**
     * 【新旧数据有区分】 【新：从hbase取；旧：没有则拿数据库的tools_command存入DialogueIntentionOutput的command】 输出意图指令
     */
    private DialogueIntentionOutput outputCommand;

    /**
     * 【新旧数据有区分】
     * 【新：从hbase取，轮训接口需要判断resultType=4，finishReason=ChatAddFlowStatusEnum.PROCESSING，更新任务表；旧：构建1个DialogueFlowResult数据】
     * 对话输出
     */
    private List<DialogueFlowResult> outputList;

    /**
     * 【需要整合likeComment、defaultComment、customComment到DialogueCommentVO】 对话评价
     */
    private DialogueCommentVO comment;

    /**
     * 【需要整合到DialogueFlowResult.modelType】 模型类型：枚举modelType
     *
     * @see com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum
     */
    private String modelType;

    /**
     * 【任务表的字段】 付费扣费状态： -1：不扣费（该任务不涉及扣费流程） 0：未扣费 1：已扣费
     *
     * @see TaskFeePaidStatusEnum
     */
    private Integer feePaidStatus;

    /**
     * 【和旧接口一样】 创建时间
     */
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date createTime;

    /**
     * 【和旧接口一样】 对话应用类型
     */
    private ChatApplicationType applicationInfo;

    /**
     * 【和旧接口一样】 引导文案
     */
    private LeadCopyVO leadCopy;

    /**
     * 【和旧接口一样】 对话结果推荐，当对话信息不为空时返回 如果leadCopy不为空，优先展示leadCopy
     */
    private DialogueRecommendVO recommend;


    /**
     * 对话结果推荐-中部，当对话信息不为空时返回
     */
    private DialogueRecommendVO middleRecommend;

    /**
     * 【和旧接口一样】 返回结果的标题 commands为“024”创建笔记和“027”创建语音笔记时，返回标题； commands为”000“时，返回知识库开头；
     * commands为“028”邮件搜索时返回卡片标题； commands为“012”-“018”、“020”-“023”和“028”搜索意图时，返回搜索结果的返回词
     */
    private String title;

    /**
     * 【和旧接口一样】 json格式的附加信息
     */
    private String extInfo;

    /**
     * 【不是前端使用的字段】对话状态
     *
     * @see ChatStatusEnum
     */
    @JsonIgnore
    private Integer chatStatus;
    /**
     * 【不是前端使用的字段】版本号
     *
     * @see AiTextResultVersionEnum
     */
    private String hbaseResultVersion;

    /**
     * 扩展字段
     */
    private ExtInfoParam extInfoParam;

    /**
     * 回答声明
     */
    private String responseStatement;

    /** ============以下字段为旧接口涉及的============== */

    /**
     * 【文档没有】
     * 【不需要了】
     * 对话类型;0:对话历史记录,1:智囊历史记录
     */
//    private Integer talkType;

    /**
     * 【放入dialogueInputInfo的command?】
     * 【是的】
     * 工具指令;对接意图指令
     */
//    private String toolsCommand;
    /**
     * 【放入dialogueInputInfo的dialogue】
     * 【是的】
     * 输入内容;输入文本内容
     */
//    private String  inContent;
    /**
     * 【前端用不上？？？】
     * 【按协议，不用返回】
     * 输入时间
     */
//    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
//    private Date    inAuditTime;
    /**
     * 【前端用不上？？？】
     * 【按协议，不用返回】
     * 输入内容审批结果;状态码：2通过，其他失败
     */
//    private Integer inAuditStatus;

    /**
     * 【接口协议未定义】
     * 【拼接进DialogueInputInfoDTO的DialogueAttachmentDTO的attachmentTypeList】
     * 资源类型;0-无，1 邮件， 2 笔记， 3 图片
     */
//    private Integer resourceType;
    /**
     * 【接口协议未定义】
     * 【拼接进DialogueInputInfoDTO的DialogueAttachmentDTO的对应字段】
     * 输入资源ID;（笔记/邮件/图片ID；纯文本时为空）
     */
//    private String inResourceId;

    /**
     * 【根据outResourceType，转换成DialogueFlowResult对应的类型】
     * 输出内容
     */
//    private String outContent;
    /**
     * 【转换成DialogueFlowResult的outputTime？？？】
     * 【是的。无任务使用content的更新时间，有任务使用任务表的更新时间（新任务处理中的也要）】
     * 输出时间
     */
//    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
//    private Date outAuditTime;
    /**
     * 【转换成DialogueFlowResult的outAuditStatus】
     * 输出内容审批结果;状态码：2通过，其他失败
     */
//    private Integer outAuditStatus;
    /**
     * 【转换成DialogueFlowResult的resultType】
     * 【outContentType映射到resultType，resultType添加一个eos类型，添加一个urlList字段，旧字段废弃】
     * 输出资源类型： 1--云盘文件ID 2--文件下载地址
     *
     * @see com.zyhl.yun.api.outer.enums.ImageTransmissionTypeEnum
     */
//    private Integer outResourceType;
    /**
     * 【根据outResourceType，转换成DialogueFlowResult对应的类型】
     * 输出资源信息；（云盘文件ID/下载URL）
     */
//    private String outResourceId;
    /**
     * 【转换成DialogueFlowResult的resultType】
     * 输出文本类型：1--普通文本 2--富文本
     * @see com.zyhl.yun.api.outer.enums.OutContentTypeEnum
     */
//    private Integer outContentType;

    /**
     * 【前端用不上】 渠道来源 文档写着必填字段 需要加上
     */
    private String sourceChannel;

    /**
     * 【需要整合likeComment、defaultComment、customComment到DialogueCommentVO】
     * 是否喜欢 0:不喜欢，1:喜欢
     */
//    private Integer likeComment;

    /**
     * 【需要整合likeComment、defaultComment、customComment到DialogueCommentVO】
     * 默认评论
     */
//    private String defaultComment;
    /**
     * 【需要整合likeComment、defaultComment、customComment到DialogueCommentVO】
     * 用户评论
     */
//    private String customComment;

    /**
     * 【旧接口，前端有使用，新接口没有对应的字段】 【转换成DialogueFlowResult的errorCode】 任务-错误结果码
     */
    @JsonIgnore
    private String resultCode;

    /**
     * 【旧接口，前端有使用，新接口没有对应的字段】 【转换成DialogueFlowResult的errorMessage】 任务-错误信息
     */
    @JsonIgnore
    private String resultMsg;

    /**
     * 【旧接口，前端有使用，新接口没有对应的字段】
     * 【用于判断DialogueFlowResult的finishReason】
     * 任务-状态
     *
     * @see TaskStatusEnum
     */
//    private Integer taskStatus;

    /**
     * 【移除】
     * 搜索参数
     */
//    private SearchParam searchParam;

    /**
     * 【移除】
     * 搜索结果
     */
//    private SearchResult searchResult;

    /**
     * 【转换成DialogueFlowResult的personalKnowledgeFileList】
     * 【旧数据转成DialogueFlowResult对应的personalKnowledgeFileList字段】
     * 个人知识库参考文件，可选，仅第一次流式结果返回
     */
//    private List<File> personalKnowledgeFileList;

    /**
     * 【转换成DialogueFlowResult的searchInfoList】
     * 【旧数据转成DialogueFlowResult对应的字段】
     * 搜索信息列表
     */
//    private List<SearchInfo> searchInfoList;

    /**
     * 【转换成DialogueFlowResult的mailInfo】
     * 【旧数据转成DialogueFlowResult对应的mailInfo】
     * 邮件信息
     */
//    private MailInfoVO mailInfo;

    /**
     * 【转换成DialogueFlowResult的networkSearchInfoList】
     * 【旧数据转成DialogueFlowResult对应的networkSearchInfoList】
     * 大模型联网搜索结果
     */
//    private List<AiTextResultRespParameters.NetworkSearchInfo> networkSearchInfoList;

    /**
     * 【转换成DialogueFlowResult的reasoningContent】
     * 【旧数据转成DialogueFlowResult对应的reasoningContent】
     * 思维链过程
     */
//    private String reasoningContent;

    /**
     * V1对话结果转换成V2对话结果
     *
     * @param contentVO V1对话结果-VO
     * @return V2对话结果-VO
     * @Author: WeiJingKun
     */
    public DialogueResultV2VO(ContentVO contentVO) {
        // hbase响应信息
        AiTextResultRespParameters respParameters = null;
        String hbaseRespParameters = contentVO.getHbaseRespParameters();
        if (CharSequenceUtil.isNotBlank(hbaseRespParameters)) {
            try {
                respParameters = JSON.parseObject(hbaseRespParameters, AiTextResultRespParameters.class);
            } catch (Exception e) {
                log.info("【对话结果VO】json转respParameters异常，hbaseRespParameters：{}", hbaseRespParameters, e);
            }
        }

        // 基础信息
        this.dialogueId = contentVO.getDialogueId();
        this.userId = contentVO.getUserId();
        this.sessionId = contentVO.getSessionId();
        this.status = contentVO.getStatus();
        this.chatStatus = contentVO.getChatStatus();
        this.dialogueInputInfo = createDialogueInputInfo(contentVO);
        this.inputAuditStatus = contentVO.getInAuditStatus();
        this.outputCommand = createOutputCommand(contentVO, respParameters);
        this.outputList = createOutputList(contentVO, respParameters);
        this.comment = createComment(contentVO);
        this.modelType = contentVO.getModelType();
        this.feePaidStatus = contentVO.getFeePaidStatus();
        this.createTime = contentVO.getCreateTime();
        this.applicationInfo = contentVO.getApplicationInfo();
        if(null != respParameters && null != respParameters.getLeadCopy()) {
        	// hbase存在，则使用存储的leadCopy
        	this.leadCopy = respParameters.getLeadCopy();
        }else {
        	this.leadCopy = contentVO.getLeadCopy();
        }
        this.recommend = contentVO.getRecommend();
        this.middleRecommend = contentVO.getMiddleRecommend();
        this.title = contentVO.getTitle();
        this.extInfo = contentVO.getExtInfo();
        this.hbaseResultVersion = createHbaseResultVersion(respParameters);
        //新增扩展参数
		this.extInfoParam = (null != respParameters ? respParameters.getExtInfoParam() : null);
        this.sourceChannel = contentVO.getSourceChannel();
        this.responseStatement = respParameters.getResponseStatement();
    }

    private String createHbaseResultVersion(AiTextResultRespParameters respParameters) {
        if (ObjectUtil.isNotNull(respParameters)) {
            return respParameters.getVersion();
        }
        return null;
    }

    private DialogueCommentVO createComment(ContentVO contentVO) {
        DialogueCommentVO comment = new DialogueCommentVO();
        comment.setLikeComment(contentVO.getLikeComment());
        comment.setDefaultComment(contentVO.getDefaultComment());
        comment.setCustomComment(contentVO.getCustomComment());
        return comment;
    }

    private List<DialogueFlowResult> createOutputList(ContentVO contentVO, AiTextResultRespParameters respParameters) {
        List<DialogueFlowResult> thisOutputList = new ArrayList<>();
        if (ObjectUtil.isNull(respParameters)) {
            thisOutputList.add(createOutput(contentVO));
            return thisOutputList;
        } else {
			// 有leadCopy时，返回null
			if (ObjectUtil.isNotNull(respParameters.getLeadCopy()) && !DialogueIntentionEnum
					.allowLeadCopyIntention(contentVO.getToolsCommand(), contentVO.getSubToolsCommand())) {
				return null;
			}
            List<DialogueFlowResult> outputList = respParameters.getOutputList();
            if (CollUtil.isEmpty(outputList)) {
                // outputList没有值时，只有V1版本的数据才处理
                if (null == respParameters.getVersion() || AiTextResultVersionEnum.V1.getVersion().equals(respParameters.getVersion())) {
                    thisOutputList.add(createOutput(contentVO));
                    return thisOutputList;
                }
            } else {
                // 输出类型为3-云盘检索结果回答，把搜索结果放入
                for (DialogueFlowResult output : outputList) {
                    if (FlowResultTypeEnum.SEARCH.getType().equals(output.getResultType())) {
                    	if(CollUtil.isNotEmpty(contentVO.getSearchInfoList())) {
                    		output.setSearchInfoList(contentVO.getSearchInfoList());
                    	}
                    }
                }
            }
            return outputList;
        }
        /*
        if (ObjectUtil.isNull(respParameters) || CollUtil.isEmpty(respParameters.getOutputList())) {
            if (ObjectUtil.isNotNull(respParameters) && null != respParameters.getLeadCopy()) {
                return null;
            }
            if (null == respParameters.getVersion()
                    || AiTextResultVersionEnum.V1.getVersion().equals(respParameters.getVersion())) {
                List<DialogueFlowResult> thisOutputList = new ArrayList<>();
                thisOutputList.add(createOutput(contentVO));
                return thisOutputList;
            }
        }
        List<DialogueFlowResult> outputList = respParameters.getOutputList();
        if(CollUtil.isNotEmpty(outputList)){
            // 输出类型为3-云盘检索结果回答，把搜索结果放入
            for (DialogueFlowResult output : outputList){
                if (FlowResultTypeEnum.SEARCH.getType().equals(output.getResultType())){
                    output.setSearchInfoList(contentVO.getSearchInfoList());
                }
            }
        }
        return outputList;
        */
    }

    /**
     * 结果类型（旧数据回显判断）
     * 1--大模型文本回答：使用outContent、reasioningConent、personalKnowledgeFileList和networkSearchInfoList；
     * 【意图类型：000 && 输出文本类型：1--普通文本】
     * <p>
     * 2--富文本回答：使用outContent； 【意图类型：000 && 输出文本类型：2--富文本】
     * <p>
     * 3--云盘检索结果回答：使用searchInfoList； 【searchInfoList有数据】
     * <p>
     * 4--异步图片任务回答：使用fileList或者outContent； 【001-图片配文，返回outContent】 【其他图片工具意图，返回fileList】
     * <p>
     * 5--邮件结果：使用AutoMailInfo 【mailInfo有数据】
     *
     * @param contentVO 原始数据
     * @return 对话输出内容-VO
     * @Author: WeiJingKun
     * @see FlowResultTypeEnum
     */
    private DialogueFlowResult createOutput(ContentVO contentVO) {
        String toolsCommand = contentVO.getToolsCommand();
        Integer outContentType = contentVO.getOutContentType();

        DialogueFlowResult output = new DialogueFlowResult();
        output.setTitle(contentVO.getTitle());
        output.setModelType(contentVO.getModelType());
        output.setFinishReason(TaskStatusEnum.isProcessing(contentVO.getTaskStatus()) ? ChatAddFlowStatusEnum.PROCESSING.getStatus() : ChatAddFlowStatusEnum.STOP.getStatus());
        output.setErrorCode(contentVO.getResultCode());
        output.setErrorMessage(contentVO.getResultMsg());
        // TODO【无任务使用content的更新时间，有任务使用任务表的更新时间（新任务处理中的也要）】
//        if(CharSequenceUtil.isNotBlank(contentVO.getTaskId())){
//        }
        output.setOutputTime(contentVO.getUpdateTime());
        output.setOutAuditStatus(contentVO.getOutAuditStatus());
        if (DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode().equals(toolsCommand) && FlowResultTypeEnum.TEXT_MODEL.getType().equals(outContentType)) {
            /**
             * 1--大模型文本回答：使用outContent、reasioningConent、personalKnowledgeFileList和networkSearchInfoList；
             * 【意图类型：000 && 输出文本类型：1--普通文本】
             */
            output.setResultType(FlowResultTypeEnum.TEXT_MODEL.getType());
            output.setOutContent(contentVO.getOutContent());
            output.setReasoningContent(contentVO.getReasoningContent());
            // TODO 类型转换里边的字段，需要检查
            if (null != contentVO.getPersonalKnowledgeFileList()) {
                output.setPersonalKnowledgeFileList(contentVO.getPersonalKnowledgeFileList().stream().map(KnowledgeSearchInfo::new).collect(Collectors.toList()));
            }
            if (null != contentVO.getNetworkSearchInfoList()) {
                output.setNetworkSearchInfoList(contentVO.getNetworkSearchInfoList().stream().map(HtmlInfo::new).collect(Collectors.toList()));
            }
        }
        if (DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode().equals(toolsCommand) && FlowResultTypeEnum.RICH_TEXT.getType().equals(outContentType)) {
            /**
             * 2--富文本回答：使用outContent；
             * 【意图类型：000 && 输出文本类型：2--富文本】
             */
            output.setResultType(FlowResultTypeEnum.RICH_TEXT.getType());
            output.setOutContent(contentVO.getOutContent());
        }
        if (CollUtil.isNotEmpty(contentVO.getSearchInfoList())) {
            /**
             * 3--云盘检索结果回答：使用searchInfoList；
             * 【searchInfoList有数据】
             */
            output.setResultType(FlowResultTypeEnum.SEARCH.getType());
            output.setSearchInfoList(contentVO.getSearchInfoList());
        }
        if (DialogueIntentionEnum.PICTURE_GENERATE_TEXT.getCode().equals(toolsCommand)) {
            /**
             * 4--异步图片任务回答：使用fileList或者outContent；
             * 【001-图片配文，返回outContent】
             */
            output.setResultType(FlowResultTypeEnum.ASYNC.getType());
            output.setOutContent(contentVO.getOutContent());
        }
        if (DialogueIntentionEnum.isAiToolIntention(toolsCommand) ||
                DialogueIntentionEnum.TEXT_GENERATE_PICTURE.getCode().equals(toolsCommand)) {
            if (ImageTransmissionTypeEnum.isYunDisk(contentVO.getOutResourceType())) {
                /**
                 * 4--异步图片任务回答：使用fileList或者outContent；
                 * 【其他图片工具意图 && 输出资源类型： 1--云盘文件ID，返回fileList】
                 */
                output.setResultType(FlowResultTypeEnum.ASYNC.getType());
                output.setFileList(ListUtil.toList(new File(contentVO.getOutResourceId())));

                //图配文的结果设置
                if (DialogueIntentionEnum.PICTURE_GENERATE_TEXT.getCode().equals(toolsCommand) &&
                        StringUtils.isNotEmpty(contentVO.getRespParam())) {
                    List<TaskRespParamVO> respParamVOList = JSON.parseArray(contentVO.getRespParam(),
                            TaskRespParamVO.class);
                    output.setOutContent(respParamVOList.stream().map(TaskRespParamVO::getOutContent)
                            .collect(Collectors.joining(StringUtils.LF)));
                }

            }
            if (ImageTransmissionTypeEnum.isEos(contentVO.getOutResourceType())) {
                /**
                 * 6--盘外文件结果：使用fileUrlList
                 * 【其他图片工具意图 && 输出资源类型： 2--文件下载地址，返回：fileUrlList】
                 */
                output.setResultType(FlowResultTypeEnum.URL.getType());
                output.setFileUrlList(ListUtil.toList(contentVO.getOutResourceId()));
            }
        }
        if (ObjectUtil.isNotNull(contentVO.getMailInfo())) {
            output.setResultType(FlowResultTypeEnum.MAIL.getType());
            output.setMailInfo(contentVO.getMailInfo());
        }
        return output;
    }

    private DialogueIntentionOutput createOutputCommand(ContentVO contentVO,
                                                        AiTextResultRespParameters respParameters) {
        DialogueIntentionOutput outputCommand = new DialogueIntentionOutput(contentVO.getToolsCommand(), contentVO.getSubToolsCommand());
        if (null != respParameters && null != respParameters.getOutputCommand()) {
            //意图参数从hbase拿
            outputCommand.setArgumentMap(respParameters.getOutputCommand().getArgumentMap());
        }
        return outputCommand;
    }

    private DialogueInputInfoDTO createDialogueInputInfo(ContentVO contentVO) {
        DialogueInputInfoDTO input = null;
        String hbaseReqParameters = contentVO.getHbaseReqParameters();
        if (CharSequenceUtil.isNotBlank(hbaseReqParameters)) {
            try {
                input = JSON.parseObject(hbaseReqParameters, DialogueInputInfoDTO.class);
            } catch (Exception e) {
                log.info("【对话结果VO】createDialogueInputInfo异常，hbaseReqParameters：{}", hbaseReqParameters);
            }
        }
        if (ObjectUtil.isNull(input)) {
            input = new DialogueInputInfoDTO();
            input.setDialogue(contentVO.getInContent());
            input.setPrompt(contentVO.getPrompt());
            /**
             * 【使用的createTime】
             * 对话输入时间，时间统一格式为RFC 3339格式(东八区)
             * 注：2019-10-12T14:20:50.52+08:00
             */
            input.setInputTime(DateUtil.format(contentVO.getCreateTime(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN));
            input.setCommand(new DialogueIntentionDTO(contentVO.getToolsCommand()));
            input.setCommandType(contentVO.getCommandType());
            input.setAttachment(new DialogueAttachmentDTO(contentVO.getResourceType(), contentVO.getInResourceId()));
//            input.setToolSetting(new DialogueToolSettingDTO());
//            input.setEnableForceNetworkSearch(contentVO.getEnableForceNetworkSearch());
//            input.setEnableForceLlm(contentVO.getEnableForceLlm());
//            input.setEnableAllNetworkSearch(contentVO.getEnableAllNetworkSearch());
            input.setVersionInfo(DialogueVersionInfoDTO.parse(contentVO.getExtInfo()));
            input.setExtInfo(contentVO.getExtInfo());
            input.setSceneTag(contentVO.getSceneTag());
//            input.setSortInfo(new DialogueSortInfoDTO());
        }
        return input;
    }

	/**
	 * 清理前端不使用的字段
	 */
	public void cleanField() {
		this.extInfoParam = null;
	}

}
