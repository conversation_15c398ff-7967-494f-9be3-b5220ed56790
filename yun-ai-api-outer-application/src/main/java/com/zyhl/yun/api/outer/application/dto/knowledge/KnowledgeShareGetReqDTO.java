package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * className:KnowledgeShareGetReqDTO
 * description:保存加入知识库接口入参
 * <AUTHOR>
 * @date 2025/04/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class KnowledgeShareGetReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 知识库id
     */
    private String baseId;

}
