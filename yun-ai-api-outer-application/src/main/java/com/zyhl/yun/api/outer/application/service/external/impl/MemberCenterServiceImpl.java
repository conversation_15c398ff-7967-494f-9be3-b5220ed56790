package com.zyhl.yun.api.outer.application.service.external.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.membercenter.resp.ConsumeAvailableBenefitRsp;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.service.external.MemberCenterService;
import com.zyhl.yun.api.outer.domain.dto.redis.MemberBenefitDTO;
import com.zyhl.yun.api.outer.domainservice.BenefitNoDomainService;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.DialogueCommandTypeEnum;
import com.zyhl.yun.api.outer.external.MemberCenterExternalService;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 会员中心权益操作
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MemberCenterServiceImpl implements MemberCenterService {

    @Resource
    private RedisOperateRepository redisOperateRepository;
    @Resource
    private MemberCenterExternalService memberCenterExternalService;
    @Resource
    private BenefitNoDomainService benefitNoDomainService;

    @Override
    public String consumeBenefit(String userId, String phone, String benefitNo, String refId) {
        String consumeSeq = null;
        try {
            if (!CharSequenceUtil.isEmpty(phone)) {
                consumeSeq = memberCenterExternalService.consumeBenefitByPhone(phone, benefitNo);
            } else {
                consumeSeq = memberCenterExternalService.consumeBenefit(userId, benefitNo);
            }
        } catch (Exception e) {
            log.error("refId:{}, userId:{}, phone:{}, benefitNo:{}, error:", refId, userId, phone, benefitNo, e);
            throw e;
        } finally {
            log.info("refId:{}, userId:{}, phone:{}, benefitNo:{}, consumeSeq:{}", refId, userId, phone, benefitNo,
                    consumeSeq);
        }
        return consumeSeq;
    }

    @Override
    public boolean consumeBenefit(AlgorithmChatAddDTO dto, String phone, Long dialogueId) {
        if (ApplicationTypeEnum.isIntelligen(dto.getApplicationType())) {
            log.info("【会员权益】智能体对话，不需要消费权益，用户id：{}，对话id：{}，手机号码：{}", dto.getUserId(), dialogueId, phone);
            return true;
        }
        if (DialogueCommandTypeEnum.isAuto(dto.getContent().getCommandType())) {
            log.info("【会员权益】自动命令，不需要消费权益，用户id：{}，对话id：{}，手机号码：{}", dto.getUserId(), dialogueId, phone);
            return true;
        }

        // 获取权益编号
        String benefitNo = benefitNoDomainService.getBenefitNo(dto.getContent().getSceneTag(),
                dto.getContent().getSourceChannel(), RequestContextHolder.getClientType(), phone);
        if (CharSequenceUtil.isEmpty(benefitNo)) {
            log.info("【会员权益】权益未打开，用户id：{}，对话id：{}，手机号码：{}", dto.getUserId(), dialogueId, phone);
            return true;
        }

        // 参数校验
        if (CharSequenceUtil.isEmpty(dto.getUserId()) || dialogueId == null) {
            log.info("【会员权益】用户id为空或者对话id为空，用户id：{}，对话id：{}，手机号码：{}", dto.getUserId(), dialogueId, phone);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        // 权益消费
        String consumeSeq;
        if (!CharSequenceUtil.isEmpty(phone)) {
            consumeSeq = memberCenterExternalService.consumeBenefitByPhone(phone, benefitNo);
        } else {
            consumeSeq = memberCenterExternalService.consumeBenefit(dto.getUserId(), benefitNo);
        }

        // 判断消费码
        log.info("【会员权益】用户id：{}，权益编号：{}，消费码：{}", dto.getUserId(), benefitNo, consumeSeq);
        if (CharSequenceUtil.isEmpty(consumeSeq)) {
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        }

        // 保存redis
        redisOperateRepository.setConsumeSeq(dto.getUserId(), dialogueId, benefitNo, consumeSeq);

        return true;
    }

    @Override
    public void consumeBenefitFail(String userId, String phone, Long dialogueId) {
        // 参数校验
        if (CharSequenceUtil.isEmpty(userId) || dialogueId == null) {
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        // 获取消费码
        final MemberBenefitDTO dto = redisOperateRepository.getConsumeSeq(userId, dialogueId);
        if (dto == null) {
            log.info("【会员权益】缓存权益消费码为空，用户id：{}，对话id：{}", userId, dialogueId);
            return;
        }

        // 权益回滚
        boolean result;
        if (!CharSequenceUtil.isEmpty(phone)) {
            result = memberCenterExternalService.consumeBenefitFailByPhone(phone, dto.getBenefitNo(),
                    dto.getConsumeSeq());
        } else {
            result = memberCenterExternalService.consumeBenefitFail(userId, dto.getBenefitNo(), dto.getConsumeSeq());
        }

        // 删除消费码
        if (result) {
            log.info("【会员权益】权益消费回滚成功，用户id：{}，消费码：{}", userId, dto.getConsumeSeq());
            redisOperateRepository.delConsumeSeq(userId, dialogueId);
        } else {
            log.info("【会员权益】会员权益消费回滚失败，用户id：{}，手机号码：{}，对话id：{}，消费码：{}", userId, phone, dialogueId, dto.getConsumeSeq());
        }
    }

    @Override
    public ConsumeAvailableBenefitRsp.AvailableBenefitRsp queryAvailableBenefit(String userId, String phoneNumber, String benefitNo) {
        ConsumeAvailableBenefitRsp.AvailableBenefitRsp consumeSeq = null;
        try {
            if (StringUtils.isNotBlank(phoneNumber)) {
                consumeSeq = memberCenterExternalService.queryAvailableBenefitByPhone(phoneNumber, benefitNo);
            } else {
                consumeSeq = memberCenterExternalService.queryAvailableBenefit(userId, benefitNo);
            }
        } catch (Exception e) {
            log.error("userId:{}, phone:{}, benefitNo:{}, error:", userId, phoneNumber, benefitNo, e);
            throw e;
        } finally {
            log.info("userId:{}, phone:{}, benefitNo:{}, consumeSeq:{}", userId, phoneNumber, benefitNo,
                    consumeSeq);
        }
        return consumeSeq;
    }
}
