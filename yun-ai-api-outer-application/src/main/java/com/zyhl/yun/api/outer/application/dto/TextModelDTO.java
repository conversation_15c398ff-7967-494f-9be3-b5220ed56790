package com.zyhl.yun.api.outer.application.dto;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.springframework.util.CollectionUtils;

import com.zyhl.hcy.yun.ai.common.model.api.client.blian.vo.TextModelFileVO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelFileDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageVlDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageVlDTO.VlContent;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelVlReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelVlContentTypeEnum;
import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 调用文本大模型接口字段
 *
 * <AUTHOR>
 */
@Slf4j
@Data
public class TextModelDTO {
    /**
     * 会话id
     */
    private String sessionId;
    /**
     * 对话id
     */
    private String dialogueId;
    /**
     * 提示词prompt
     */
    private String commands;

    /**
     * 文本大模型文件列表
     */
    private List<TextModelFileDTO> textModelFiles;

    /**
     * 用户输入问题和附件内容
     */
    private String content;
    /**
     * 用户输入问题
     */
    private String reqParam;
    /**
     * 历史对话内容
     */
    List<TextModelMessageDTO> reqList = new ArrayList<>();

    /**
     * 用户id
     */
    private String userId;

    /**
     * 是否强制联网搜索
     */
    private Boolean enableForceNetworkSearch;


    public TextModelDTO(AlgorithmChatAddDTO chatDto, AlgorithmChatAddVO resVo, String resourceContent, List<TextModelMessageDTO> historyList, String promptTemplate) {
        this.dialogueId = resVo.getDialogueId();
        this.sessionId = resVo.getSessionId();
        this.commands = CharSequenceUtil.emptyToDefault(promptTemplate, "");
        if(ObjectUtil.isNotEmpty(historyList)){
            this.reqList.addAll(historyList);
        }

        // 用户输入问题、附件内容
        final String reqContent = CharSequenceUtil.emptyToDefault(chatDto.getContent().getDialogue(), "");
        final String attachment = CharSequenceUtil.emptyToDefault(resourceContent, "");
        this.content = (reqContent + " " + attachment).trim();
        this.reqParam = reqContent;

        // 将本次对话放入历史数据
        TextModelMessageDTO messageDTO = new TextModelMessageDTO();
        messageDTO.setRole(TextModelRoleEnum.USER.getName());
        messageDTO.setContent(this.content);
        messageDTO.setCommand(this.commands);
        this.reqList.add(messageDTO);

        // 文本大模型文件列表
        this.textModelFiles = getTextModelFiles(chatDto.getContent().getTextModelFiles());

        this.userId = chatDto.getUserId();

        this.enableForceNetworkSearch = chatDto.getEnableForceNetworkSearch();
    }

    public TextModelDTO(SecondStreamChatAddInnerDTO dto, String content, String promptTemplate) {
        this.dialogueId = dto.getReqParams().getDialogueId();
        this.sessionId = dto.getSessionId();
        this.commands = CharSequenceUtil.emptyToDefault(promptTemplate, "");

        // 用户输入问题
        this.content = CharSequenceUtil.emptyToDefault(content, "").trim();
        this.reqParam = content;

        // 将本次对话放入历史数据
        TextModelMessageDTO messageDTO = new TextModelMessageDTO();
        messageDTO.setRole(TextModelRoleEnum.USER.getName());
        messageDTO.setContent(this.content);
        messageDTO.setCommand(this.commands);

        List<TextModelMessageDTO> historyList = new ArrayList<>();
        historyList.add(messageDTO);

        this.reqList = historyList;
        this.userId = dto.getReqParams().getUserId();
    }

    private List<TextModelFileDTO> getTextModelFiles(List<TextModelFileVO> files) {
        if (CollectionUtils.isEmpty(files)) {
            return null;
        }
        List<TextModelFileDTO> textModelFileList = new ArrayList<>();
        for (TextModelFileVO file : files) {
            // 只设置fileId
            textModelFileList.add(TextModelFileDTO.builder().id(file.getId()).build());
        }
        return textModelFileList;
    }

    /**
     * 文本模型参数简单封装，无message消息
     * @return
     */
    public TextModelTextReqDTO toSimpleTextReqDTO() {
		TextModelTextReqDTO reqDTO = new TextModelTextReqDTO();
        reqDTO.setTaskId(dialogueId);
        reqDTO.setUserId(userId);
        reqDTO.setSessionId(sessionId);
        // 设置是否强制联网搜索
        reqDTO.setEnableForceNetworkSearch(this.enableForceNetworkSearch);
		return reqDTO;
	}
    
    /**
     * 文本模型参数封装
     *
     * @param maxLength 最大历史字数
     * @return 文本模型参数
     */
	public TextModelTextReqDTO toTextReqDTO(Integer maxLength) {
		TextModelTextReqDTO reqDTO = toSimpleTextReqDTO();
		reqDTO.setMessageDtoList(reqList);
		// 设置文本大模型文件列表
		reqDTO.setTextModelFiles(this.textModelFiles);

		if (reqList.size() <= 1 || maxLength == null) {
			// 只有当前对话的问题
			return reqDTO;
		}

		// 字数超限
		int totalLength = 0;
		boolean isOverLimit = false;
		List<TextModelMessageDTO> list = new ArrayList<>();
		for (int i = reqList.size() - 1; i >= 0; i--) {
			String thisContent = reqList.get(i).getContent();
			if (null != thisContent) {
				// 倒序累计总字数，第一条是当前问话
				totalLength += thisContent.length();
			}
			if (totalLength > maxLength) {
				// 字数超限，判断当前是用户问题，则需要减一条
				isOverLimit = true;
				if (TextModelRoleEnum.USER.getName().equals(reqList.get(i).getRole())) {
					list.remove(0);
				}
				break;
			}
			list.add(0, reqList.get(i));
		}

		if (!isOverLimit) {
			return reqDTO;
		}

		// 字数超限，不传会话id
		reqDTO.setSessionId("");
		reqDTO.setMessageDtoList(list);

		return reqDTO;
	}
    
	/**
     * 文本模型参数简单封装，无message消息
     * @return
     */
    public TextModelVlReqDTO toSimpleVlReqDTO() {
    	TextModelVlReqDTO reqDTO = new TextModelVlReqDTO();
        reqDTO.setTaskId(dialogueId);
        reqDTO.setUserId(userId);
        reqDTO.setSessionId(sessionId);
        // 设置是否强制联网搜索
        reqDTO.setEnableForceNetworkSearch(this.enableForceNetworkSearch);
		return reqDTO;
	}
    
    /**
     * 文本模型参数封装
     *
     * @param maxLength 最大历史字数
     * @return 文本模型参数
     */
	public TextModelVlReqDTO toTextVlReqDTO(Integer maxLength) {
		TextModelVlReqDTO reqDTO = toSimpleVlReqDTO();
		if(!(null != maxLength && maxLength.intValue() > 0)) {
			return reqDTO;
		}
		List<TextModelMessageVlDTO> reqVlList = new ArrayList<>();
		if (CollUtil.isNotEmpty(reqList)) {
			for (TextModelMessageDTO req : reqList) {
				reqVlList.add(new TextModelMessageVlDTO(req.getRole(), Collections.singletonList(VlContent.builder()
						.type(TextModelVlContentTypeEnum.TEXT.getType()).text(req.getContent()).build())));
			}
		}
		reqDTO.setMessageVlDtoList(reqVlList);

		if (reqList.size() <= 1) {
			// 只有当前对话的问题
			return reqDTO;
		}

		// 字数超限
		int totalLength = 0;
		boolean isOverLimit = false;
		List<TextModelMessageVlDTO> list = new ArrayList<>();
		for (int i = reqVlList.size() - 1; i >= 0; i--) {
			List<VlContent> thisContent = reqVlList.get(i).getContent();
			if (CollUtil.isNotEmpty(thisContent)) {
				// 倒序累计总字数，第一条是当前问话
				totalLength += thisContent.get(0).getText().length();
			}
			if (totalLength > maxLength) {
				// 字数超限，判断当前是用户问题，则需要减一条
				isOverLimit = true;
				if (TextModelRoleEnum.USER.getName().equals(reqVlList.get(i).getRole())) {
					list.remove(0);
				}
				break;
			}
			list.add(0, reqVlList.get(i));
		}

		if (!isOverLimit) {
			return reqDTO;
		}

		// 字数超限，不传会话id
		reqDTO.setSessionId("");
		reqDTO.setMessageVlDtoList(list);

		return reqDTO;
	}

    /**
     * 单个文本模型参数封装
     *
     * @param query 用户输入
     * @return 文本模型参数
     */
    public TextModelTextReqDTO toSingleTextReqDTO(String query) {
        TextModelTextReqDTO reqDTO = new TextModelTextReqDTO();
        reqDTO.setTaskId(dialogueId);
        reqDTO.setUserId(userId);
        reqDTO.setSessionId(sessionId);
        TextModelMessageDTO message = new TextModelMessageDTO();
        message.setContent(query);
        message.setRole(TextModelRoleEnum.USER.getName());
        reqDTO.setMessageDtoList(Collections.singletonList(message));
		return reqDTO;
    }
    /**
     * 添加回答内容到历史对话
     *
     * @param text 回答内容
     */
    public List<TextModelMessageDTO> addAnswer(String text) {
        TextModelMessageDTO messageDTO = new TextModelMessageDTO();
        messageDTO.setRole(TextModelRoleEnum.ASSISTANT.getName());
        messageDTO.setContent(text);
        messageDTO.setCommand("");
        this.reqList.add(messageDTO);

        return this.reqList;
    }
}
