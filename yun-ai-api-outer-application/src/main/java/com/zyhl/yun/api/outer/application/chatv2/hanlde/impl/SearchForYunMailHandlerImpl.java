package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatDialogueSearchService;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import lombok.extern.slf4j.Slf4j;

/**
 * 云邮助手搜索相关
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SearchForYunMailHandlerImpl extends AbstractChatAddV2Handler {

	/**
	 * 当前执行顺序
	 */
	private ExecuteSort thisExecuteSort = ExecuteSort.SEARCH_FOR_YUN_MAIL;

	@Resource
	private ChatDialogueSearchService chatDialogueSearchService;

	@Override
	public void afterPropertiesSet() throws Exception {
    	//支持的业务初始化
    	List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        this.setBusinessTypes(thisBusinessTypes);
    }
    
	@Override
	public int order() {
		return thisExecuteSort.getSort();
	}

	@Override
	public boolean execute(ChatAddHandleDTO handleDTO) {
		// 非云邮渠道，不做后续任何处理
		boolean isMail = AssistantEnum.isYunMail(RequestContextHolder.getAssistantEnum());
		if(!isMail){
			return false;
		}
		/**
		 * 执行的条件： 1、应用类型 == chat（普通对话） 2、云邮助手渠道号 3、云邮助手搜索意图
		 */
		String currentIntentionCode = handleDTO.getIntentionCode();
		boolean isSearchIntention = DialogueIntentionEnum.isSearchTypeIntentionForAll(currentIntentionCode);
		String businessType = RequestContextHolder.getBusinessType();
		boolean isChat = ApplicationTypeEnum.isChat(handleDTO.getReqDTO().getApplicationType());
		boolean isMailSearchIntention = isSearchIntention
				&& DialogueIntentionEnum.isMailSearchIntention(businessType, currentIntentionCode);
		if(isSearchIntention && !isMailSearchIntention) {
			//是搜索意图，但不是云邮支持的搜索，转换为文生文
			handleDTO.setTextGenerateTextIntention();
			return false;
		}
		return isChat && isMailSearchIntention;
	}

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		log.info("进入{}", thisExecuteSort.getDesc());

		// 执行搜索
		if(chatDialogueSearchService.mailSearchIntentionHandle(handleDTO)) {

			// 返回异步结果
			handleDTO.getRespVO().getFlowResult().setResultType(FlowResultTypeEnum.SEARCH.getType());

			// 流式输出
			handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(handleDTO.getRespVO()));

		}

		return false;
	}

}
