package com.zyhl.yun.api.outer.application.convertor.dto.dynamic;


import com.zyhl.yun.api.outer.application.vo.ImageCaptionVO;
import com.zyhl.yun.api.outer.domain.entity.ImageCaptionEntity;
import org.mapstruct.Mapper;

/**
 * 抓取实体转换器
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface CaptureEntityConvertor {

    /**
     * entity转VO
     * @param entity entity
     * @return VO
     */
    ImageCaptionVO toInfoVO(ImageCaptionEntity entity);

}