package com.zyhl.yun.api.outer.application.dto;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.enums.BaseResultCodeEnum;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 会话设置查询接口-DTO
 * @Author: WeiJingKun
 */
@Data
@Slf4j
public class ChatConfigGetDTO extends BaseDTO implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 默认云邮的渠道
     */
    private static final String DEFAULT_SOURCE_CHANNEL = "10102";

    /**
     * 渠道来源
     * @see SourceChannelsProperties
     */
    private String sourceChannel;

    /**
     * 助手类型【非接口参数】
     */
    private AssistantEnum assistantEnum;


    /**
     * 助手业务类型【非接口参数】
     */
    private String businessType;
    
    /**
     * 参数校验
     * @param channelsProperties 渠道配置
     * @return 错误码枚举
     */
    public AbstractResultCode check(SourceChannelsProperties channelsProperties) {
        // 新增的接口入参，需要默认值
        if (CharSequenceUtil.isBlank(this.sourceChannel)) {
            // 默认云邮的渠道
            this.sourceChannel = DEFAULT_SOURCE_CHANNEL;
        }

        if (!channelsProperties.isExist(this.sourceChannel)) {
            log.info("渠道来源不存在：{}", this.sourceChannel);
            return BaseResultCodeEnum.ERROR_PARAMS;
        }

        // 根据渠道，获取助手类型（默认云邮）
        this.assistantEnum = channelsProperties.getAssistantEnumDefaultMail(this.sourceChannel);
        // 根据渠道，获取助手业务类型
        this.businessType = channelsProperties.getType(this.sourceChannel);
        
        return super.checkUserId();
    }

}
