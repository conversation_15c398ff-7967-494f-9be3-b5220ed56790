package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

import javax.annotation.Resource;

import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Component;

import com.zyhl.hcy.yun.ai.common.base.utils.StopWatchUtil;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.application.service.chat.DialogueIntentionService;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 线程预处理：意图识别线程预处理线程预处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class BeforeThreadFutureHandlerImpl extends AbstractChatAddV2Handler {

    /**
     * 当前执行顺序
     */
    private ExecuteSort thisExecuteSort = ExecuteSort.BEFORE_THREAD_FUTURE;

    @Resource
    private DialogueIntentionService dialogueIntentionService;

    @Resource(name = "dialogueIntentionThreadPool")
    private ExecutorService dialogueIntentionThreadPool;

    @Override
    public void afterPropertiesSet() throws Exception {
        //支持的业务初始化
        List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        thisBusinessTypes.add(ChatBusinessTypeEnum.CLOUD_PHONE);
        thisBusinessTypes.add(ChatBusinessTypeEnum.MESSAGE_5G);
        thisBusinessTypes.add(ChatBusinessTypeEnum.NOTE);
        this.setBusinessTypes(thisBusinessTypes);
    }

    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public boolean execute(ChatAddHandleDTO handleDTO) {
        return true;
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        DialogueInputInfoDTO inputInfoDTO = handleDTO.getInputInfoDTO();
        boolean enableAiEdite = inputInfoDTO.isEnableAiEdite();
        if (enableAiEdite) {
            // 邮箱AI编辑的不进入这个handler处理，直接返回，去执行下一个handler
            return true;
        }
        log.info("进入{}", thisExecuteSort.getDesc());

        /** 意图识别预处理 */
        intentionPretreatment(handleDTO);

        return true;
    }

    /**
     * 意图识别预处理
     *
     * @param handleDTO 用户输入对象
     */
    private void intentionPretreatment(ChatAddHandleDTO handleDTO) {
        // 判断是否执行【输入内容意图识别】
        if (!judgeInputIntention(handleDTO)) {
            return;
        }
        Long dialogueId = handleDTO.getDialogueId();

        try {
            // 获取主线程的ThreadLocal信息，并且异步调用前，将父线程中的请求信息绑定给子线程
            RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo = RequestContextHolder.getThreadLocalInfoAndBindingAttributes();
            RequestContextHolder.BusinessInfo businessInfo = RequestContextHolder.getBusinessInfo();

            /** 意图识别预处理 */
            Future<DialogueIntentionVO> dialogueIntentionFuture = CompletableFuture.supplyAsync(() -> {
                StopWatch stopWatch = StopWatchUtil.createStarted();
                DialogueIntentionVO intentionVO = null;
                try {
                    // 把主线程ThreadLocal信息set到子线程
                    RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);
                    RequestContextHolder.setBusinessInfo(businessInfo);

                    // 意图识别
                    intentionVO = dialogueIntentionService.getIntentionVOV2(handleDTO);

                    // 第一个意图
                    String currentIntentionCode = intentionVO.getIntentionInfoList().get(0).getIntention();

                    // 云邮助手不支持的意图，默认为文生文
                    String sourceChannel = RequestContextHolder.getSourceChannel();
                    String businessType = RequestContextHolder.getBusinessType();
                    boolean isMail = SourceChannelsProperties.isMailChannel(sourceChannel);
                    boolean isMailNotSupportIntention = DialogueIntentionEnum.isMailNotSupportIntention(businessType,
                            currentIntentionCode);
                    log.info(
                            "intentionPretreatment sourceChannel:{}, businessType:{}, currentIntentionCode:{}, isMail:{}, isMailNotSupportIntention:{}",
                            sourceChannel, businessType, currentIntentionCode, isMail, isMailNotSupportIntention);
                    if (isMail && isMailNotSupportIntention) {
                        // 默认为文生文
                        currentIntentionCode = DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode();
                        intentionVO.getIntentionInfoList().get(0).setIntention(currentIntentionCode);
                    }
                } catch (Exception e) {
                    log.error("意图识别预处理，异常", e);
                } finally {
                    log.info("意图识别预处理，耗时：{}", StopWatchUtil.logTime(stopWatch));
                    StopWatchUtil.clearDuration();
                }
                return intentionVO;
            }, dialogueIntentionThreadPool);
            // set意图识别预处理future
            handleDTO.setDialogueIntentionFuture(dialogueIntentionFuture);
        } catch (Exception e) {
            log.error("意图识别预处理，线程池-异常", e);
        } finally {
            log.info("意图识别预处理，dialogueId：{}", dialogueId);
        }
    }

    /**
     * 判断是否执行【输入内容意图识别】
     *
     * @param handleDTO 用户输入对象
     * @return true：执行；false：不执行
     */
    protected boolean judgeInputIntention(ChatAddHandleDTO handleDTO) {
        // 意图为空 并且 对话内容不为空
        return CharSequenceUtil.isEmpty(handleDTO.getIntentionCode())
                && CharSequenceUtil.isNotEmpty(handleDTO.getInputInfoDTO().getDialogue())
                && Boolean.FALSE.equals(handleDTO.getInputInfoDTO().isEnableAllNetworkSearch());
    }

}
