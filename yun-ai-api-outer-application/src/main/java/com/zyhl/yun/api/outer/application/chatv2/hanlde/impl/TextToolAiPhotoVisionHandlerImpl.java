package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueIntentionDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.config.textmodel.VlModelConfig;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * AI拍照识图
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TextToolAiPhotoVisionHandlerImpl extends AbstractChatAddV2Handler {

    /**
     * 当前执行顺序
     */
    private final ExecuteSort thisExecuteSort = ExecuteSort.TEXT_TOOL_AI_PHOTO;

    @Resource
    private TextModelVisionSseHandlerImpl textModelVisionSseHandlerImpl;
    @Resource
    private VlModelConfig vlModelConfig;

	@Override
	public void afterPropertiesSet() throws Exception {
		// 支持的业务初始化
		List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        this.setBusinessTypes(thisBusinessTypes);
	}

    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public boolean execute(ChatAddHandleDTO handleDTO) {

        if (StringUtils.isEmpty(handleDTO.getReqDTO().getDialogueInput().getDialogue())
                && !handleDTO.isReqResourceImageSse()) {
            // 对话内容空&&图片资源为空，不执行
            return false;
        }

        DialogueIntentionDTO command = handleDTO.getReqDTO().getDialogueInput().getCommand();
        if (null != command && DialogueIntentionEnum.isTextToolIntention(command.getCommand())
                && DialogueIntentionSubEnum.isAiPhotoSubIntentions(command.getSubCommand())) {
            // 判断入参是ai拍照识图意图（主意图+子意图联合判断）
            handleDTO.setSubIntentionCode(command.getSubCommand());
            return true;
        }

        if (null != handleDTO.getIntentionVO()
                && CollUtil.isNotEmpty(handleDTO.getIntentionVO().getIntentionInfoList())) {
            IntentionInfo mainIntention = DialogueIntentionVO.getMainIntention(handleDTO.getIntentionVO());
            if (null != mainIntention && DialogueIntentionEnum.isTextToolIntention(mainIntention.getIntention())
                    && DialogueIntentionSubEnum.isAiPhotoSubIntentions(mainIntention.getSubIntention())) {
                // 判断意图识别是ai拍照识图意图（主意图+子意图联合判断）
                handleDTO.setSubIntentionCode(mainIntention.getSubIntention());
                return true;
            }
        }

        return false;
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        log.info("进入{}", thisExecuteSort.getDesc());

        // 根据子意图，配置不同提示词
        if (DialogueIntentionSubEnum.AI_PHOTO_RECOGNITION_SOLVE_PROBLEMS.getCode()
                .equals(handleDTO.getSubIntentionCode())) {
            // 1. 拍照解题
            handleDTO.setVlmBusinessModelConfig(vlModelConfig.getAiPhotoSolveProblemsConfig());
        }
        if (DialogueIntentionSubEnum.AI_PHOTO_RECOGNITION_TRANSLATE.getCode().equals(handleDTO.getSubIntentionCode())) {
            // 2. 拍照翻译
            handleDTO.setVlmBusinessModelConfig(vlModelConfig.getAiPhotoTranslateConfig());
        }
        if (DialogueIntentionSubEnum.AI_PHOTO_RECOGNITION_QA.getCode().equals(handleDTO.getSubIntentionCode())) {
            // 3. 拍照问答
            handleDTO.setVlmBusinessModelConfig(vlModelConfig.getAiPhotoQaConfig());
        }

        // 设置输出意图
        handleDTO.getRespVO().setOutputCommandCode(handleDTO.getIntentionCode(), handleDTO.getSubIntentionCode());

        // 执行视觉大模型流式对话
        return textModelVisionSseHandlerImpl.run(handleDTO);

    }
}
