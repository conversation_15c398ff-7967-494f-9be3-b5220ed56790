package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 知识库导入任务列表请求参数
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KnowledgeFileTaskListReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 知识库Id
     */
    private String baseId;

    /**
     * 任务状态数组，不填默认返回-1和0的数据
     * -1--处理失败
     * 0--处理中
     * 1--处理成功
     */
    private Integer[] statusArray;

    /**
     * 分页信息
     */
    private PageInfoDTO pageInfo;
} 