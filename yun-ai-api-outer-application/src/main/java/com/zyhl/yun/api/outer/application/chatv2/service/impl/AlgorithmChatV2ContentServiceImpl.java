package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import static com.zyhl.yun.api.outer.constants.Const.NUM_16;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.zyhl.hcy.yun.ai.common.base.annotation.MethodExecutionTimeLog;
import com.zyhl.hcy.yun.ai.common.base.enums.SupplierTypeEnum;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.NumberUtil;
import com.zyhl.hcy.yun.ai.common.base.utils.StopWatchUtil;
import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.dto.PptGenProgressDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.vo.PptGenProgressVO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.PptGenStatusEnum;
import com.zyhl.hcy.yun.ai.common.model.api.enums.PptUploadStatusEnum;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.dto.AipptDesignInfoRequestDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.vo.AipptDesignInfoResponseVO;
import com.zyhl.hcy.yun.ai.common.model.api.utils.TextModelUtil;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.YunDiskClient;
import com.zyhl.yun.api.outer.application.chatv2.dto.AlgorithmChatV2ContentListDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.AssistantChatV2PollingUpdateDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueAttachmentDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.service.AlgorithmChatV2ContentService;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatDialogueSearchService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.service.YunDiskV2Service;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueResultV2VO;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.application.dto.SearchImageReqDTO;
import com.zyhl.yun.api.outer.application.dto.YunDiskReqDTO;
import com.zyhl.yun.api.outer.application.service.chat.LeadCopyV2Service;
import com.zyhl.yun.api.outer.application.service.external.MemberCenterService;
import com.zyhl.yun.api.outer.application.service.external.SearchImageAlbumListService;
import com.zyhl.yun.api.outer.application.service.mq.AiAssistantMqService;
import com.zyhl.yun.api.outer.application.vo.ContentExtInfoVO;
import com.zyhl.yun.api.outer.application.vo.SmartFakeCheckVO;
import com.zyhl.yun.api.outer.config.EosFileExpireConfig;
import com.zyhl.yun.api.outer.config.SearchResultProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.constants.RegConst;
import com.zyhl.yun.api.outer.domain.dto.ExternalResourceInfoDTO;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatMessageEntity;
import com.zyhl.yun.api.outer.domain.entity.TaskAiAbilityEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.AiFunctionResult;
import com.zyhl.yun.api.outer.domain.valueobject.AlbumInfo;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.valueobject.MailInfo;
import com.zyhl.yun.api.outer.domain.valueobject.NoteInfo;
import com.zyhl.yun.api.outer.domain.valueobject.PersonalKnowledgeBase;
import com.zyhl.yun.api.outer.domain.valueobject.PersonalKnowledgeResource;
import com.zyhl.yun.api.outer.domain.vo.ChatApplicationType;
import com.zyhl.yun.api.outer.domain.vo.ContextRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.IntentionRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.domain.vo.chat.ContentVO;
import com.zyhl.yun.api.outer.domain.vo.chat.search.SearchInfo;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchCommonParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchImageParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchResult;
import com.zyhl.yun.api.outer.domain.vo.common.PageInfoVO;
import com.zyhl.yun.api.outer.domainservice.AlgorithmChatContentDomain;
import com.zyhl.yun.api.outer.domainservice.ResourceInfoDomainService;
import com.zyhl.yun.api.outer.domainservice.SearchReturnTermsService;
import com.zyhl.yun.api.outer.domainservice.UserKnowledgeDomainService;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.enums.ImageSuffixEnum;
import com.zyhl.yun.api.outer.enums.ImageTransmissionTypeEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.AiTextResultVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddFlowStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatContentSortTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.EditResourceEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;
import com.zyhl.yun.api.outer.external.AlbumSaasExternalService;
import com.zyhl.yun.api.outer.external.service.AipptExternalService;
import com.zyhl.yun.api.outer.external.service.EOSExternalService;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatMessageRepository;
import com.zyhl.yun.api.outer.repository.ChatApplicationTypeRepository;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;
import com.zyhl.yun.api.outer.repository.TaskAiAbilityRepository;
import com.zyhl.yun.api.outer.repository.assembler.ChatContentAssembler;
import com.zyhl.yun.api.outer.repository.assembler.ChatMessageAssembler;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.RequestContextHolder.HeaderParams;
import com.zyhl.yun.api.outer.util.VersionUtil;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.TaskRespParamVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 对话-serviceImpl
 *
 * @Author: WeiJingKun
 */
@Slf4j
@Service
public class AlgorithmChatV2ContentServiceImpl implements AlgorithmChatV2ContentService {

    @Resource
    private AlgorithmChatMessageRepository algorithmChatMessageRepository;

    @Resource
    private AlgorithmChatContentRepository algorithmChatContentRepository;

    @Resource
    private ChatApplicationTypeRepository chatApplicationTypeRepository;

    @Resource
    private RedisOperateRepository redisOperateRepository;

    @Resource
    private ChatMessageAssembler messageAssembler;

    @Resource
    private ChatContentAssembler contentAssembler;

    @Resource
    private ChatDialogueSearchService chatDialogueSearchService;

    @Resource
    private MemberCenterService memberCenterService;

    @Resource
    private SourceChannelsProperties sourceChannelsProperties;

    @Resource
    private AiAssistantMqService aiAssistantMqService;

    @Resource(name = "algorithmChatThreadPool")
    private ExecutorService algorithmChatThreadPool;

    @Resource
    private TaskAiAbilityRepository taskAiAbilityRepository;

    @Resource
    private LeadCopyV2Service leadCopyService;

    @Resource
    private EOSExternalService eosExternalService;

    @Resource
    private YunDiskV2Service yunDiskV2Service;

    @Resource
    private AlgorithmChatContentDomain algorithmChatContentDomain;

    @Resource
    private SearchReturnTermsService searchReturnTermsService;

    @Resource
    private SearchImageAlbumListService searchImageAlbumListService;

    @Resource
    private EosFileExpireConfig eosFileExpireConfig;

    @Resource
    private DataSaveService dataSaveService;

    @Resource
    private ResourceInfoDomainService resourceInfoDomainService;

    @Resource
    private UserKnowledgeDomainService userKnowledgeDomainService;

    @Resource
    private TextModelExternalService textModelExternalService;

    @Resource
    private ChatTextToolBusinessConfig chatTextToolBusinessConfig;

    @Resource
    private SearchResultProperties searchResultProperties;

    @Resource
    private AlbumSaasExternalService albumSaasExternalService;

    @Resource
    private AipptExternalService aipptExternalService;

    @Override
    public DialogueResultV2VO pollingUpdate(AssistantChatV2PollingUpdateDTO dto) {
        // 获取对话结果
        ContentVO contentVO = chatPollingUpdate(dto);
        if (null == contentVO) {
            log.warn("【查询对话输出】AlgorithmChatContentServiceImpl-pollingUpdate，contentVO is null");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_DIALOGUE_ID);
        }
        // 兼容旧的思维链数据渲染到V2
        splitOldReasoningContent(contentVO);

        return createDialogueResultV2VO(contentVO);
    }

    /**
     * 获取对话结果
     *
     * @Author: WeiJingKun
     */
    @MethodExecutionTimeLog("获取对话结果-serviceImpl")
    private ContentVO chatPollingUpdate(AssistantChatV2PollingUpdateDTO dto) {
        Long dialogueId = dto.getDialogueId();
        /** 获取并更新对话数据 */
        AlgorithmChatContentEntity entity = algorithmChatContentRepository
                .pollingUpdateV2(AlgorithmChatContentEntity.builder().userId(dto.getUserId()).id(dialogueId).build());
        if (null == entity) {
            return null;
        }
        // 设置对话状态，完成的发送AI助手对话完成mq
        setChatStatusAndSendMq(entity);

        // 创建公共VO
        ContentVO vo = createCommonVO(entity);

        // 针对异步文本会话，如果处理失败，需要调会员中心回滚接口将用户预扣的次数权益冲正回去
        rollbackBenefitTimes(dialogueId, vo);

        // 设置输入输出资源id和type
        setResourceIdAndType(vo, entity, EditResourceEnum.OUT.getCode());

        return vo;
    }

    /**
     * 针对异步文本会话，如果处理失败，需要调会员中心回滚接口将用户预扣的次数权益冲正回去
     *
     * @Author: WeiJingKun
     */
    private void rollbackBenefitTimes(Long dialogueId, ContentVO vo) {
        // 初始化参数
        String taskId = vo.getTaskId();
        String toolsCommand = vo.getToolsCommand();
        Integer taskStatus = vo.getTaskStatus();
        String userId = vo.getUserId();

        // 【任务id不为空（异步任务），且文本意图】
        boolean hasTextIntentionTask = CharSequenceUtil.isNotBlank(taskId) && DialogueIntentionEnum.isTextIntention(toolsCommand);
        // 【任务状态不为null，且任务失败】
        boolean isTaskFail = taskStatus != null && TaskStatusEnum.isTaskFail(taskStatus);
        if (hasTextIntentionTask && isTaskFail) {
            // 会员权益消费结果，消费失败（回滚权益）
            memberCenterService.consumeBenefitFail(userId, RequestContextHolder.getPhoneNumber(), dialogueId);
        }
    }

    /**
     * 设置对话状态，完成的发送AI助手对话完成mq
     *
     * @param entity 对话内容实体
     */
    private void setChatStatusAndSendMq(AlgorithmChatContentEntity entity) {
        Integer taskStatus = entity.getTaskStatus();
        Integer chatStatus = entity.getChatStatus();

        if (null != taskStatus && ChatStatusEnum.isChatIn(chatStatus)) {
            log.info("开始设置对话状态，完成的发送AI助手对话完成mq id:{}", entity.getId());
            //非1-待处理；2-处理中状态，需要处理对话完成设置
            if (!TaskStatusEnum.isProcessing(taskStatus)) {
                if (TaskStatusEnum.PROCESS_FINISH.getCode().intValue() == taskStatus) {
                    //任务完成，设置对话完成
                    entity.setChatStatus(ChatStatusEnum.CHAT_SUCCESS.getCode());
                } else if (TaskStatusEnum.OVERDUE.getCode().intValue() == taskStatus) {
                    //任务过期，设置对话过期
                    entity.setChatStatus(ChatStatusEnum.CHAT_EXPIRE.getCode());
                } else {
                    //其他的，设置对话失败（加状态需要再做处理！！！）
                    entity.setChatStatus(ChatStatusEnum.CHAT_FAIL.getCode());
                }
                if (algorithmChatContentRepository.updateChatStatusForInStatus(entity)) {
                    //发送AI助手对话完成mq
                    aiAssistantMqService.sendDialogueCompletedMq(entity);
                } else {
                    log.warn("更新对话 updateChatStatusForInStatus fail... id:{}, chatStatus:{}",
                            entity.getId(), chatStatus);
                }
            }
        }
    }

    @Override
    @MethodExecutionTimeLog("历史对话列表查询-serviceImpl")
    public PageInfoVO<DialogueResultV2VO> contentList(AlgorithmChatV2ContentListDTO dto) {
        long start = System.currentTimeMillis();
        /** 参数初始化 */
        StopWatch stopWatch = null;
        String userId = dto.getUserId();
        Long sessionId = dto.getSessionId();
        String applicationType = dto.getApplicationType();
        String sourceChannel = dto.getSourceChannel();
        // 获取业务类型
        String businessType = sourceChannelsProperties.getTypeNullThrowException(sourceChannel);
        PageInfoDTO page = PageInfoDTO.getReqDTO(dto.getPageInfo());

        /** 获取对话信息 */
        PageInfo<AlgorithmChatContentEntity> pageInfo = algorithmChatContentRepository.contentList(
                AlgorithmChatContentEntity.builder()
                        .userId(userId)
                        .sessionId(dto.getSessionId())
                        .businessType(businessType)
                        .sourceChannel(sourceChannel)
                        .applicationType(applicationType)
                        .sortType(dto.getSortType())
                        .dialogueIdList(dto.getDialogueIdList())
                        .isContentListV2(true)
                        .build(),
                Integer.parseInt(page.getPageCursor()),
                page.getPageSize(),
                page.getNeedTotalCount()
        );
        log.info("【历史对话列表V2】查询数据库-耗时：{}ms", System.currentTimeMillis() - start);

        // 判断起始分页，sessionId有传参，并且dto.getDialogueIdList()空，并且对话内容空，删除message数据
        start = System.currentTimeMillis();
        if (PageInfoDTO.isStartPage(page) && null != dto.getSessionId() && CollUtil.isEmpty(dto.getDialogueIdList())
                && (null == pageInfo || (null != pageInfo && CollUtil.isEmpty(pageInfo.getList())))) {
            // 查找是否存在正常的sessionId+businessType，存在才删除
            if (algorithmChatMessageRepository.existNormalBySessionIdAndBusinessType(userId, sessionId, businessType)) {
                /** 删除会话 */
                AlgorithmChatMessageEntity messageEntity = AlgorithmChatMessageEntity.builder().userId(userId)
                        .sessionIdList(Collections.singletonList(sessionId)).businessType(businessType).build();
                algorithmChatMessageRepository.deleteByIds(messageEntity);
                log.info("删除message数据 userId:{}, sessionId:{}", userId, sessionId);
            }
        }
        log.info("【历史对话列表V2】判断起始分页，sessionId有传参，并且dto.getDialogueIdList()空，并且对话内容空，删除message数据-耗时：{}ms", System.currentTimeMillis() - start);

        List<AlgorithmChatContentEntity> contentEntityList = pageInfo.getList();
        log.info("历史对话列表查询-serviceImpl，分页：{}，结果：{}", JSON.toJSON(page), JSON.toJSON(contentEntityList));

        /** 封装返回结果 */
        PageInfoVO<DialogueResultV2VO> result = PageInfoVO.getRespDTO(page, pageInfo);
        if (CollUtil.isEmpty(contentEntityList)) {
            return result;
        }
        try {
            CopyOnWriteArrayList<DialogueResultV2VO> resultList = new CopyOnWriteArrayList<>();
            // 会话id不为null或智能体类型，则需要获取对话应用类型信息map，key是应用id
            start = System.currentTimeMillis();
            Map<String, ChatApplicationType> chatApplicationTypeMap;
            if (null != sessionId || ApplicationTypeEnum.INTELLIGENT.getCode().equals(applicationType)) {
                chatApplicationTypeMap = chatApplicationTypeRepository.listToMapKeyIsId();
            } else {
                chatApplicationTypeMap = new HashMap<>(NUM_16);
            }
            log.info("【历史对话列表V2】会话id不为null或智能体类型，则需要获取对话应用类型信息map，key是应用id-耗时：{}ms", System.currentTimeMillis() - start);

            /** 并行处理数据 */
            stopWatch = StopWatchUtil.createStarted();
            // 获取主线程的ThreadLocal信息，并且异步调用前，将父线程中的请求信息绑定给子线程
            RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo = RequestContextHolder.getThreadLocalInfoAndBindingAttributes();

            // 并行处理数据，等到所有任务都完成，并设置超时
            CompletableFuture.allOf(contentEntityList.stream().map(contentEntity -> CompletableFuture.runAsync(() -> {
                long s = System.currentTimeMillis();
                // 把主线程ThreadLocal信息set到子线程
                RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);
                // 获取对话内容VO
                ContentVO contentVO = getContentVO(contentEntity, userId, sessionId, applicationType, chatApplicationTypeMap);
                if (ObjectUtil.isNotNull(contentVO)) {
                    // 兼容旧的思维链数据渲染到V2
                    splitOldReasoningContent(contentVO);
                    // 创建对话结果
                    DialogueResultV2VO dialogueResultV2VO = createDialogueResultV2VO(contentVO);
                    // 构建对话输入附件信息
                    buildInputInfoAttachment(dialogueResultV2VO);
                    resultList.add(dialogueResultV2VO);
                }
                log.info("【历史对话列表V2】单个对话数据处理-耗时：{}ms\ncontentEntity：{}", System.currentTimeMillis() - s, JSON.toJSONString(contentEntity));
            }, algorithmChatThreadPool)).toArray(CompletableFuture[]::new)).get(60, TimeUnit.SECONDS);

            // 设置AIPPT结果的coverUrl
            setCoverUrlForAipptResults(resultList, userId);

            // 根据条件进行排序
            sortList(resultList, result, dto.getSortType());
        } catch (Exception e) {
            log.error("历史对话列表查询-AlgorithmChatHistoryServiceImpl-contentList，并行处理数据异常：", e);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_SERVER_UNAVAILABLE);
        } finally {
            assert stopWatch != null;
            log.info("历史对话列表查询-AlgorithmChatHistoryServiceImpl-contentList，并行处理结果耗时：{}", StopWatchUtil.logTime(stopWatch));
            StopWatchUtil.clearDuration();
        }

        return result;
    }


    /**
     * 根据条件进行排序
     *
     * @param resultList 对话结果-VO集合
     * @param result     分页结果
     * @Author: WeiJingKun
     */
    private void sortList(CopyOnWriteArrayList<DialogueResultV2VO> resultList, PageInfoVO<DialogueResultV2VO> result, Integer sortType) {
        List<DialogueResultV2VO> sortList = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultList)) {
            // 根据创建时间倒序排序
            sortList = resultList.stream().sorted(Comparator.comparing(DialogueResultV2VO::getCreateTime).reversed()).collect(Collectors.toList());
            if (ChatContentSortTypeEnum.CREATE_TIME_ASC.getCode().equals(sortType)) {
                //创建时间升序
                sortList = resultList.stream().sorted(Comparator.comparing(DialogueResultV2VO::getCreateTime)).collect(Collectors.toList());
            }
        }
        result.setList(sortList);
    }

    /**
     * 构建对话输入附件信息
     *
     * @param dialogueResultV2VO 对话结果
     * @Author: WeiJingKun
     */
    private void buildInputInfoAttachment(DialogueResultV2VO dialogueResultV2VO) {
        long start = System.currentTimeMillis();
        DialogueInputInfoDTO dialogueInputInfo = dialogueResultV2VO.getDialogueInputInfo();
        if (ObjectUtil.isNotNull(dialogueInputInfo)) {
            DialogueAttachmentDTO attachment = dialogueInputInfo.getAttachment();
            List<Integer> attachmentTypeList = attachment.getAttachmentTypeList();
            if (ResourceTypeEnum.contains(attachmentTypeList, ResourceTypeEnum.MAIL)) {
                List<MailInfo> mailList = attachment.getMailList();
                if (CollUtil.isNotEmpty(mailList)) {
                    for (MailInfo mail : mailList) {
                        // 获取邮件信息
                        ExternalResourceInfoDTO mailInfo = resourceInfoDomainService.getMailInfo(RequestContextHolder.getPhoneNumber(), mail.getMailId());
                        if (ObjectUtil.isNotNull(mailInfo)) {
                            mail.setTitle(mailInfo.getName());
                        }
                    }
                }
            } else if (ResourceTypeEnum.contains(attachmentTypeList, ResourceTypeEnum.NOTE)) {
                List<NoteInfo> noteList = attachment.getNoteList();
                if (CollUtil.isNotEmpty(noteList)) {
                    for (NoteInfo note : noteList) {
                        // 获取笔记信息
                        ExternalResourceInfoDTO noteInfo = resourceInfoDomainService.getNoteInfo(RequestContextHolder.getUserId(), note.getNoteId());
                        if (ObjectUtil.isNotNull(noteInfo)) {
                            note.setTitle(noteInfo.getName());
                        }
                    }
                }
            } else if (ResourceTypeEnum.contains(attachmentTypeList, ResourceTypeEnum.PICTURE)
                    || ResourceTypeEnum.contains(attachmentTypeList, ResourceTypeEnum.CLOUD_DISK_DOCUMENT)) {
                // 图片或文档
                List<File> fileList = attachment.getFileList();
                if (CollUtil.isNotEmpty(fileList)) {
                    // 获取云盘文件id列表，去空
                    List<String> fileIdList = fileList.stream().map(File::getFileId).filter(CharSequenceUtil::isNotBlank).collect(Collectors.toList());
                    // 【并行】批量获取云盘文件信息
                    Map<String, File> fileMap = yunDiskV2Service.batchGetYunDiskContentAndBigThumbnailUrl(fileIdList, algorithmChatThreadPool);
                    // 遍历替换云盘文件信息
                    List<File> resultList = new ArrayList<>();
                    for (File file : fileList) {
                        // 获取云盘文件信息
                        File newFile = fileMap.get(file.getFileId());
                        if (ObjectUtil.isNotNull(newFile)) {
                            resultList.add(newFile);
                        } else {
                            resultList.add(file);
                        }
                    }
                    attachment.setFileList(resultList);
                }
            } else if (ResourceTypeEnum.contains(attachmentTypeList, ResourceTypeEnum.MAIL_ATTACHMENT)) {
                List<MailInfo> mailList = attachment.getMailList();
                if (CollUtil.isNotEmpty(mailList)) {
                    for (MailInfo mail : mailList) {
                        // 获取邮件和附件信息
                        ExternalResourceInfoDTO mailInfo = resourceInfoDomainService.getMailInfo(RequestContextHolder.getPhoneNumber(), mail.getMailId());
                        if (ObjectUtil.isNotNull(mailInfo)) {
                            mail.setTitle(mailInfo.getName());
                        }
                    }
                }
            } else if (ResourceTypeEnum.contains(attachmentTypeList, ResourceTypeEnum.PERSONAL_KNOWLEDGE_FILE)) {
                List<PersonalKnowledgeResource> knowledgeFileList = attachment.getKnowledgeFileList();
                if (CollUtil.isNotEmpty(knowledgeFileList)) {
                    // 获取云盘文件id列表，去空
                    List<String> fileIdList = knowledgeFileList.stream().map(PersonalKnowledgeResource::getResourceId).filter(CharSequenceUtil::isNotBlank).collect(Collectors.toList());
                    // 【并行】批量获取云盘文件信息
                    Map<String, File> fileMap = yunDiskV2Service.batchGetYunDiskContentAndBigThumbnailUrl(fileIdList, algorithmChatThreadPool);
                    // 遍历替换云盘文件信息
                    List<PersonalKnowledgeResource> resultList = new ArrayList<>();
                    for (PersonalKnowledgeResource knowledgeFile : knowledgeFileList) {
                        String resourceId = knowledgeFile.getResourceId();
                        // 获取个人知识库文档信息
                        File newFile = fileMap.get(resourceId);
                        knowledgeFile = new PersonalKnowledgeResource(newFile);
                        if (ObjectUtil.isNotNull(knowledgeFile)) {
                            resultList.add(knowledgeFile);
                        } else {
                            knowledgeFile = new PersonalKnowledgeResource();
                            knowledgeFile.setResourceId(resourceId);
                            resultList.add(knowledgeFile);
                        }
                    }
                    attachment.setKnowledgeFileList(resultList);
                }
            } else if (ResourceTypeEnum.contains(attachmentTypeList, ResourceTypeEnum.PERSONAL_KNOWLEDGE_BASE)) {
                List<PersonalKnowledgeBase> knowledgeBaseList = attachment.getKnowledgeBaseList();
                if (CollUtil.isNotEmpty(knowledgeBaseList)) {
                    // 获取所有的baseId转Long列表
                    List<Long> baseIdList = knowledgeBaseList.stream().map(PersonalKnowledgeBase::getBaseId).filter(id -> id.matches(RegConst.REG_ID_STR)).map(Long::valueOf).collect(Collectors.toList());
                    // 获取个人知识库列表
                    attachment.setKnowledgeBaseList(userKnowledgeDomainService.getUserKnowledgeListByIdList(RequestContextHolder.getUserId(), baseIdList));
                }
            }
        }
        log.info("【构建对话输入附件信息】耗时：{}ms，dialogueId：{}，dialogueInputInfo：{}", System.currentTimeMillis() - start, dialogueResultV2VO.getDialogueId(), JSON.toJSONString(dialogueInputInfo));
    }

    /**
     * 创建对话结果
     *
     * @param contentVO 对话内容VO
     * @return 对话结果
     * @Author: WeiJingKun
     */
    private DialogueResultV2VO createDialogueResultV2VO(ContentVO contentVO) {
        long start = System.currentTimeMillis();
        DialogueResultV2VO resultV2VO = new DialogueResultV2VO(contentVO);

        // 【新：从hbase取，轮训接口需要判断resultType=4，finishReason=ChatAddFlowStatusEnum.PROCESSING，更新任务表；旧：构建1个DialogueFlowResult数据】
        // 【新：从hbase取，轮训接口需要判断resultType=7，finishReason=ChatAddFlowStatusEnum.PROCESSING，更新任务表；旧：构建1个DialogueFlowResult数据】
        List<DialogueFlowResult> flowResultList = resultV2VO.getOutputList();
        if (CollUtil.isNotEmpty(flowResultList)) {
            for (DialogueFlowResult flowResult : flowResultList) {
                try {
                    boolean updateHbase = false;
                    boolean updateDb = false;
                    if (ChatStatusEnum.isChatSuccess(contentVO.getChatStatus())
                            || ChatStatusEnum.isChatIn(contentVO.getChatStatus())) {
                        // 其他工具类结果集处理，对话处理中
                        boolean isAiPpt = DialogueIntentionEnum.isTextToolIntention(contentVO.getToolsCommand())
                                && DialogueIntentionSubEnum.isAiPpt(contentVO.getSubToolsCommand());
                        boolean isAiMemoryAlbum = DialogueIntentionEnum.isTextToolIntention(contentVO.getToolsCommand())
                                && DialogueIntentionSubEnum.isMemoryAlbum(contentVO.getSubToolsCommand());
                        String outResourceId = contentVO.getOutResourceId();
                        if (Objects.equals(FlowResultTypeEnum.TOOL_RESULT.getType(), flowResult.getResultType())
                                && ChatAddFlowStatusEnum.isProcessing(flowResult.getFinishReason())) {
                            if (isAiPpt) {
                                // AI生成PPT轮询
                                if (StringUtils.isEmpty(outResourceId)) {
                                    break;
                                }
                                AiFunctionResult aiFunctionResult = flowResult.getAiFunctionResult();
                                if (null == aiFunctionResult) {
                                    aiFunctionResult = new AiFunctionResult();
                                }
                                Long taskId = JSONUtil.parseObj(outResourceId).getLong("taskId");
                                PptGenProgressDTO dto = new PptGenProgressDTO(taskId);
                                dto.setToken(RequestContextHolder.getToken());
                                PptGenProgressVO pptGenProgress = textModelExternalService.getAiPptGenProgress(dto);
                                if (null == pptGenProgress) {
                                    break;
                                }
                                if (StringUtils.isEmpty(aiFunctionResult.getTitle())) {
                                    aiFunctionResult.setTitle(pptGenProgress.getTitle());
                                }
                                if (Objects.equals(PptGenStatusEnum.SUCCESS.getStatus(), pptGenProgress.getStatus())) {
                                    // 成功
                                    updateDb = true;

                                    // 判断上传状态 start
                                    if (Objects.equals(PptUploadStatusEnum.SUCCESS.getStatus(),
                                            pptGenProgress.getUploadStatus())) {
                                        // 上传成功
                                        contentVO.setChatStatus(ChatStatusEnum.CHAT_SUCCESS.getCode());
                                        flowResult.setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
                                    } else if (Objects.equals(PptUploadStatusEnum.FAILED.getStatus(),
                                            pptGenProgress.getUploadStatus())) {
                                        // 上传失败
                                        contentVO.setChatStatus(ChatStatusEnum.CHAT_FAIL.getCode());
                                        flowResult.setFinishReason(ChatAddFlowStatusEnum.ERROR.getStatus());
                                        flowResult.setErrorCode(
                                                ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION.getResultCode());
                                        flowResult.setErrorMessage(
                                                ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION.getResultMsg());
                                        resultV2VO.setResultCode(flowResult.getErrorCode());
                                        resultV2VO.setResultMsg(flowResult.getErrorMessage());
                                    } else {
                                        contentVO.setChatStatus(ChatStatusEnum.CHAT_IN.getCode());
                                    }
                                    // 判断上传状态 end

                                    cn.hutool.json.JSONObject jsonObject = JSONUtil
                                            .parseObj(JSONUtil.toJsonStr(pptGenProgress));
                                    jsonObject.set("taskId", taskId);
                                    contentVO.setOutResourceId(JSONUtil.toJsonStr(jsonObject));
                                    if (StringUtils.isNotEmpty(pptGenProgress.getPptFileId())) {
                                        YunDiskReqDTO fileDto = new YunDiskReqDTO();
                                        fileDto.setFileId(pptGenProgress.getPptFileId());
                                        // 查找云盘文件
                                        aiFunctionResult
                                                .setFile(yunDiskV2Service.getYunDiskContentAndBigThumbnailUrl(fileDto));
                                    }
                                    if (StringUtils.isNotEmpty(pptGenProgress.getCoverFileId())) {
                                        YunDiskReqDTO fileDto = new YunDiskReqDTO();
                                        fileDto.setFileId(pptGenProgress.getCoverFileId());
                                        // 查找云盘文件
                                        aiFunctionResult.setCover(
                                                yunDiskV2Service.getYunDiskContentAndBigThumbnailUrl(fileDto));
                                    }
                                } else if (Objects.equals(PptGenStatusEnum.FAILED.getStatus(),
                                        pptGenProgress.getStatus())) {
                                    // 停止
                                    updateDb = true;
                                    contentVO.setChatStatus(ChatStatusEnum.CHAT_STOP.getCode());
                                    flowResult.setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
                                }
                                if (updateDb) {
                                    Long pptId = pptGenProgress.getPptId();
                                    aiFunctionResult.setPreviewUrl(
                                            chatTextToolBusinessConfig.getAiPptGenerate().getH5Url(taskId, pptId));
                                    aiFunctionResult.setPcPreviewUrl(
                                            chatTextToolBusinessConfig.getAiPptGenerate().getPcUrl(taskId, pptId));
                                    aiFunctionResult.setProgress(pptGenProgress.getProgress());
                                    flowResult.setAiFunctionResult(aiFunctionResult);
                                    updateHbase = true;
                                }
                            }
                        } else {
                            if (isAiPpt && ChatStatusEnum.isChatSuccess(contentVO.getChatStatus())) {
                                // 重新查询个人云文件（封面id）和重新设置token地址
                                log.info("重新查询个人云文件（封面id）和重新设置token地址 dialogueId:{}, outResourceId:{}",
                                        contentVO.getDialogueId(), outResourceId);
                                cn.hutool.json.JSONObject aiPptResult = JSONUtil.parseObj(outResourceId);
                                if (null != aiPptResult) {
                                    String coverFileId = aiPptResult.getStr("coverFileId");
                                    Long taskId = JSONUtil.parseObj(outResourceId).getLong("taskId");
                                    Long pptId = JSONUtil.parseObj(outResourceId).getLong("pptId");
                                    if (StringUtils.isNotEmpty(coverFileId)) {
                                        YunDiskReqDTO fileDto = new YunDiskReqDTO();
                                        fileDto.setFileId(coverFileId);
                                        // 查找云盘文件
                                        flowResult.getAiFunctionResult().setCover(
                                                yunDiskV2Service.getYunDiskContentAndBigThumbnailUrl(fileDto));
                                        // 重新设置token地址
                                        flowResult.getAiFunctionResult().setPreviewUrl(
                                                chatTextToolBusinessConfig.getAiPptGenerate().getH5Url(taskId, pptId));
                                        flowResult.getAiFunctionResult().setPcPreviewUrl(
                                                chatTextToolBusinessConfig.getAiPptGenerate().getPcUrl(taskId, pptId));
                                    }
                                }
                            }
							if (isAiMemoryAlbum
									&& (Objects.equals(FlowResultTypeEnum.ALBUM.getType(), flowResult.getResultType())
											|| Objects.equals(FlowResultTypeEnum.SEARCH.getType(),
													flowResult.getResultType()))) {
                                //获取相册结果
                                getAlbumInfo(flowResult);
                                //获取搜索结果
                                getSearchInfo(flowResult);
                            }
                        }

                        boolean isPicToText = DialogueIntentionEnum.PICTURE_GENERATE_TEXT.getCode()
                                .equals(contentVO.getToolsCommand());
                        // 异步图片工具类结果集处理，对话处理中
                        if (Objects.equals(FlowResultTypeEnum.ASYNC.getType(), flowResult.getResultType())
                                && ChatAddFlowStatusEnum.isProcessing(flowResult.getFinishReason())) {
                            flowResult.setOutAuditStatus(contentVO.getOutAuditStatus());
                            String finishReason = ChatStatusEnum.isChatSuccess(contentVO.getChatStatus())
                                    ? ChatAddFlowStatusEnum.STOP.getStatus()
                                    : ChatAddFlowStatusEnum.PROCESSING.getStatus();
                            flowResult.setFinishReason(finishReason);
                            if (isPicToText) {
                                // 图配文参数转换
                                if (StringUtils.isNotEmpty(contentVO.getRespParam())) {
                                    List<TaskRespParamVO> respParamVOList = JSON.parseArray(contentVO.getRespParam(),
                                            TaskRespParamVO.class);
                                    flowResult.setOutContent(respParamVOList.stream().map(TaskRespParamVO::getOutContent)
                                            .collect(Collectors.joining(StringUtils.LF)));
                                }
                            } else {
                                getFileInfo(flowResult, contentVO.getOutResourceId());
                                flowResult.setErrorCode(contentVO.getResultCode());
                                flowResult.setErrorMessage(contentVO.getResultMsg());
                            }
                            updateHbase = true;
                        } else {
                            if (!isPicToText && Objects.equals(FlowResultTypeEnum.ASYNC.getType(), flowResult.getResultType())
                                    && ChatStatusEnum.isChatSuccess(contentVO.getChatStatus())) {
                                getFileInfo(flowResult, contentVO.getOutResourceId());
                            }
                        }
                    } else if (ChatStatusEnum.isChatFail(contentVO.getChatStatus())) {
                        // 对话失败
                        if (ChatAddFlowStatusEnum.isProcessing(flowResult.getFinishReason())) {
                            updateHbase = true;
                        }
                        flowResult.setFinishReason(ChatAddFlowStatusEnum.ERROR.getStatus());
                        // 错误码不为空，或者错误码为ResultCodeEnum.SUCCESS，需要转换错误码
                        if ((StringUtils.isNotEmpty(flowResult.getErrorCode())
                                && Objects.equals(FlowResultTypeEnum.TOOL_RESULT.getType(), flowResult.getResultType()))) {
                            resultV2VO.setResultCode(ResultCodeEnum.SUCCESS.getResultCode());
                            resultV2VO.setResultMsg(ResultCodeEnum.SUCCESS.getResultMsg());
                        } else {
                            // 设置通用错误码集合处理类
                            String voResultCode = contentVO.getResultCode();
                            if (ResultCodeEnum.SUCCESS.getResultCode().equals(voResultCode)) {
                                resultV2VO.setResultCode(ResultCodeEnum.SUCCESS.getResultCode());
                                resultV2VO.setResultMsg(ResultCodeEnum.SUCCESS.getResultMsg());
                            } else {
                                AiResultCode resultCode = AiResultCode.getByCodeOrMsg(voResultCode,
                                        contentVO.getResultMsg());
                                if (null != resultCode) {
                                    resultV2VO.setResultCode(resultCode.getCode());
                                    resultV2VO.setResultMsg(resultCode.getMsg());
                                } else {
                                    resultV2VO.setResultCode(contentVO.getResultCode());
                                    resultV2VO.setResultMsg(contentVO.getResultMsg());
                                }
                            }
                            flowResult.setErrorCode(resultV2VO.getResultCode());
                            flowResult.setErrorMessage(resultV2VO.getResultMsg());
                        }
                    }
                    if (updateDb) {
                        // db update（当前这条更新content记录）
                        AlgorithmChatContentEntity chatContentEntity = AlgorithmChatContentEntity.builder()
                                .id(Long.valueOf(contentVO.getDialogueId())).chatStatus(contentVO.getChatStatus())
                                .outResourceId(contentVO.getOutResourceId()).updateTime(new Date()).build();
                        algorithmChatContentRepository.updateChatStatusAndOutResource(chatContentEntity);
                    }
                    if (updateHbase) {
                        // hbase update（当前这条更新hbase ai_text_result）
                        dataSaveService.updateResult(contentVO.getUserId(), contentVO.getDialogueId(), flowResult);
                    }
                } catch (Exception ex) {
                    flowResult.setFinishReason(ChatAddFlowStatusEnum.ERROR.getStatus());
                    flowResult.setErrorCode(contentVO.getResultCode());
                    flowResult.setErrorMessage(contentVO.getResultMsg());
                    log.error("对话结果转换异常：e | ", ex);
                }
            }
        }
        resultV2VO.setChatStatus(contentVO.getChatStatus());
        log.info("【构建对话输入附件信息】耗时：{}ms，dialogueId：{}，flowResultList：{}", System.currentTimeMillis() - start, contentVO.getDialogueId(), JSON.toJSONString(flowResultList));
        return resultV2VO;
    }

	/**
     * resultType：1-大模型文本回答 兼容旧的思维链数据渲染到V2 拆分旧版本思维链数据到reasoningContent
     *
     * @param content 对话内容VO
     */
    private void splitOldReasoningContent(ContentVO content) {
        String outContent = content.getOutContent();
        if (StringUtils.isBlank(content.getReasoningContent()) && StringUtils.isNotBlank(outContent)) {
            int startThinkIndex = outContent.indexOf(TextModelUtil.TAG_START_THINK);
            int endThinkIndex = outContent.indexOf(TextModelUtil.TAG_END_THINK);
            if (-1 != startThinkIndex && -1 == endThinkIndex) {
                content.setReasoningContent(outContent);
                content.setOutContent(StringUtils.EMPTY);
            }
            if (-1 != startThinkIndex && -1 != endThinkIndex) {
                content.setReasoningContent(
                        outContent.substring(startThinkIndex + TextModelUtil.TAG_START_THINK.length(), endThinkIndex));
                content.setOutContent(outContent.replaceAll(RegConst.REG_MODEL_THINK, ""));
            }
        }
    }

    /**
     * resultType：1-大模型文本回答
     * 兼容旧的思维链数据渲染到V2
     * 拆分旧版本思维链数据到reasoningContent
     *
     * @param resultV2VO 对话结果VO
     */
//    private void splitOldReasoningContent(DialogueResultV2VO resultV2VO) {
//        List<DialogueFlowResult> outputList = resultV2VO.getOutputList();
//        for (DialogueFlowResult outputInfoVO : outputList) {
//            // resultType：1-大模型文本回答
//            if(FlowResultTypeEnum.TEXT_MODEL.getType().equals(outputInfoVO.getResultType())){
//                String outContent = outputInfoVO.getOutContent();
//                if (CharSequenceUtil.isBlank(outputInfoVO.getReasoningContent()) && CharSequenceUtil.isNotBlank(outContent)) {
//                    int startThinkIndex = outContent.indexOf(TextModelUtil.TAG_START_THINK);
//                    int endThinkIndex = outContent.indexOf(TextModelUtil.TAG_END_THINK);
//                    if (-1 != startThinkIndex && -1 == endThinkIndex) {
//                        outputInfoVO.setReasoningContent(outContent);
//                        outputInfoVO.setOutContent(StringUtils.EMPTY);
//                    }
//                    if (-1 != startThinkIndex && -1 != endThinkIndex) {
//                        outputInfoVO.setReasoningContent(
//                                outContent.substring(startThinkIndex + TextModelUtil.TAG_START_THINK.length(), endThinkIndex));
//                        outputInfoVO.setOutContent(outContent.replaceAll(RegConst.REG_MODEL_THINK, ""));
//                    }
//                }
//            }
//        }
//    }

    /**
     * 获取对话内容VO
     *
     * @param contentEntity          对话内容实体
     * @param userId                 用户id
     * @param sessionId              对话id
     * @param applicationType        应用类型
     * @param chatApplicationTypeMap 对话应用类型map
     * @return com.zyhl.yun.api.outer.domain.vo.algorithmChat.ContentVO
     * @Author: WeiJingKun
     */
    @MethodExecutionTimeLog("获取对话内容VO-serviceImpl")
    private ContentVO getContentVO(AlgorithmChatContentEntity contentEntity, String userId, Long sessionId, String applicationType, Map<String, ChatApplicationType> chatApplicationTypeMap) {
        /** 构建获取对话内容VO公共数据 */
        ContentVO contentVO = createCommonVO(contentEntity);

        /** 会话id不为null或智能体类型，则需要获取智能体信息 */
        if (null != sessionId || ApplicationTypeEnum.INTELLIGENT.getCode().equals(applicationType)) {
            String applicationId = contentEntity.getApplicationId();
            if (CharSequenceUtil.isNotBlank(applicationId)) {
                contentVO.setApplicationInfo(chatApplicationTypeMap.get(applicationId));
            }
        }

        /** 返回构造输入资源信息 */
        handleInResourceIdAndType(contentVO);

        /** 设置输入输出资源id和type */
        setResourceIdAndType(contentVO, contentEntity, EditResourceEnum.OUT.getCode());

        return contentVO;
    }

    /**
     * 构建获取对话内容VO公共数据
     *
     * @Author: WeiJingKun
     */
    @MethodExecutionTimeLog("构建获取对话内容VO公共数据-serviceImpl")
    private ContentVO createCommonVO(AlgorithmChatContentEntity entity) {
        ContentVO vo = contentAssembler.toContentVo(entity);
        vo.setDialogueId(String.valueOf(entity.getId()));
        vo.setSessionId(String.valueOf(entity.getSessionId()));

        // 查找hbase结果数据
        String hbaseReqParameters = vo.getHbaseReqParameters();
        String hbaseRespParameters = vo.getHbaseRespParameters();
        if (CharSequenceUtil.isBlank(hbaseRespParameters) && CharSequenceUtil.isBlank(hbaseReqParameters)) {
            AiTextResultEntity aiTextResult = dataSaveService.getHbaseResult(vo.getUserId(), vo.getDialogueId());
            if (null != aiTextResult) {
                vo.setHbaseRespParameters(aiTextResult.getRespParameters());
                vo.setHbaseReqParameters(aiTextResult.getReqParameters());
            }
        }

        // 判断并构造映射后的错误码
        algorithmChatContentDomain.judgeAndCreateResultCodeV2(vo);

        // 输出内容outContent处理
        outContentHandle(vo);

        // 对话结果推荐-中部-处理
        algorithmChatContentDomain.dialogueMiddleRecommendHandle(vo);
        // 对话结果推荐处理
        DialogueRecommendVO recommendVO = algorithmChatContentDomain.dialogueRecommendHandle(vo);
        // 引导文案处理
        leadCopyHandle(vo, recommendVO);

        // 发邮件信息处理
        sendMailIntention(vo);
        
		// 设置应用信息
		if (ApplicationTypeEnum.isIntelligen(entity.getApplicationType())) {
			vo.setApplicationInfo(ChatApplicationType.builder().applicationId(entity.getApplicationId())
					.applicationType(entity.getApplicationType()).build());
		}

        return vo;
    }

    /**
     * 引导文案处理
     *
     * @Author: WeiJingKun
     */
    private void leadCopyHandle(ContentVO vo, DialogueRecommendVO recommendVO) {
        /** 初始化参数 */
        String toolsCommand = vo.getToolsCommand();
        String sourceChannel = vo.getSourceChannel();
        String inResourceId = vo.getInResourceId();
        String extInfo = vo.getExtInfo();
        String hbaseRespParameters = vo.getHbaseRespParameters();

        /** 引导文案处理 */
        // 根据工具指令、渠道来源，获取引导文案对象VO，处理extInfo，兼容历史版本
        LeadCopyVO leadCopyVo = getLeadCopyVO(vo, toolsCommand, sourceChannel, inResourceId, extInfo);

        // leadCopyVo（上面没有得到数据）、输出内容（hbase的resp），不为空
        if (ObjectUtil.isNull(leadCopyVo) && CharSequenceUtil.isNotBlank(hbaseRespParameters)) {
            try {
                /** 把hbase获取的outContent转：引导文案 */
                AiTextResultRespParameters respParameters = JSON.parseObject(hbaseRespParameters, AiTextResultRespParameters.class);
                leadCopyVo = respParameters.getLeadCopy();
                if (StringUtils.isEmpty(respParameters.getVersion())) {
                    // 如果hbase有引导文案，则把对应的结果码和结果信息放入响应vo
                    vo.setResultCode(respParameters.getResultCode());
                    vo.setResultMsg(respParameters.getResultMsg());
                }
            } catch (JSONException e) {
                // 图配文的结果为非规定AiTextResultRespParameters实体结构，不做处理
                log.warn("引导文案处理-AlgorithmChatHistoryServiceImpl-leadCopyHandle-2，JSON解析失败，hbaseRespParameters：{}，leadCopyVo：{}", hbaseRespParameters, leadCopyVo);
            } catch (Exception e) {
                log.error("引导文案处理-AlgorithmChatHistoryServiceImpl-leadCopyHandle-2，异常，hbaseRespParameters：{}，leadCopyVo：{}", hbaseRespParameters, leadCopyVo, e);
            }
        }

        // leadCopyVo（上面没有得到数据）、对话结果推荐，不为空
        if (ObjectUtil.isNull(leadCopyVo) && ObjectUtil.isNotNull(recommendVO)) {
            List<ContextRecommendVO> contextList = recommendVO.getContextList();
            /**
             * 上下文推荐列表为null（PS：这里只判断null），则为【旧】推荐数据
             */
            if (null == contextList) {
                // 需要将intentionList的功能迁移到leadCopy(type=5)
                List<IntentionRecommendVO> intentionList = recommendVO.getIntentionList();
                if (CollUtil.isNotEmpty(intentionList)) {
                    leadCopyVo = leadCopyService.getLeadCopy(intentionList.get(0));
                }
            }
        }

        // set引导文案
        vo.setLeadCopy(leadCopyVo);
    }

    /**
     * 根据工具指令、渠道来源，获取引导文案对象VO，处理extInfo，兼容历史版本
     *
     * @Author: WeiJingKun
     */
    private LeadCopyVO getLeadCopyVO(ContentVO vo, String toolsCommand, String sourceChannel, String inResourceId, String extInfo) {
        LeadCopyVO leadCopyVo = vo.getLeadCopy();
        // 工具指令、渠道来源，不为空
        if (CharSequenceUtil.isAllNotBlank(toolsCommand, sourceChannel)) {
            if (null == leadCopyVo) {
                // 获取引导文案对象VO（输入资源ID为空）
                leadCopyVo = leadCopyService.getLeadCopyVo(DialogueIntentionVO.newMainIntention(toolsCommand), sourceChannel, inResourceId, vo.getInContent());
            }

            // 处理extInfo  兼容历史版本 小天1.0 AI助手有用到做业务逻辑
            if (ObjectUtil.isNotNull(leadCopyVo)) {
                try {
                    if (CharSequenceUtil.isBlank(extInfo)) {
                        vo.setExtInfo(JSON.toJSONString(leadCopyVo));
                    } else {
                        JSONObject leadCopyVoJson = JSON.parseObject(JSON.toJSONString(leadCopyVo));
                        JSONObject extInfoJson = JSON.parseObject(extInfo);
                        extInfoJson.putAll(leadCopyVoJson.getInnerMap());
                        vo.setExtInfo(JSON.toJSONString(extInfoJson));
                    }
                } catch (JSONException e) {
                    log.error("引导文案处理-AlgorithmChatHistoryServiceImpl-leadCopyHandle-1，JSON解析异常，leadCopyVo：{}，extInfo：{}", leadCopyVo, extInfo, e);
                } catch (Exception e) {
                    log.error("引导文案处理-AlgorithmChatHistoryServiceImpl-leadCopyHandle-1，异常，leadCopyVo：{}，extInfo：{}", leadCopyVo, extInfo, e);
                }
            }
        }
        return leadCopyVo;
    }

    /**
     * 输出内容outContent处理
     *
     * @Author: WeiJingKun
     */
    private void outContentHandle(ContentVO vo) {
        // 初始化参数
        String hbaseRespParameters = vo.getHbaseRespParameters();
        String toolsCommand = vo.getToolsCommand();
        String taskId = vo.getTaskId();

        // 工具指令 和 输出内容（hbase的resp），不为空
        if (CharSequenceUtil.isNotBlank(toolsCommand)) {
            if (CharSequenceUtil.isNotBlank(hbaseRespParameters)) {
                // 【文生文】的输出内容outContent处理
                textIntention(vo, toolsCommand, hbaseRespParameters, taskId);

                // 【图片配文】的输出内容outContent处理
                pictureGenerateTextIntention(vo, toolsCommand, hbaseRespParameters);

                // 【搜索意图】的输出内容outContent处理
                searchIntention(vo, toolsCommand, hbaseRespParameters);

            }
            // 【图片智能鉴伪意图】的输出内容outContent处理
            smartFakeCheckIntention(vo, toolsCommand);
        }

    }

    /**
     * 【搜索意图】的输出内容outContent处理
     *
     * @Author: WeiJingKun
     */
    @MethodExecutionTimeLog("【搜索意图】的输出内容outContent处理-serviceImpl")
    private void searchIntention(ContentVO vo, String toolsCommand, String hbaseRespParameters) {
        // 是否为搜索意图 || 搜索知识库资源
        if (DialogueIntentionEnum.isSearchIntentionOrOther(toolsCommand)
                || DialogueIntentionEnum.SEARCH_KNOWLEDGE_BASE_RESOURCE.getCode().equals(toolsCommand)) {
            try {
                String inContent = vo.getInContent();
                String dialogueId = vo.getDialogueId();

                /** 把hbase获取的outContent转：搜索条件结果 */
                AiTextResultRespParameters respParameters = JSON.parseObject(hbaseRespParameters, AiTextResultRespParameters.class);
                SearchParam searchParam = respParameters.getParam();
                String resultCode = respParameters.getResultCode();
                String resultMsg = respParameters.getResultMsg();

                /** 获取搜索结果前，构建公共搜索参数 */
                SearchCommonParam searchCommonParam = createSearchCommonParam(vo, inContent);

                /** 获取搜索结果 */
                SearchResult searchResult = chatDialogueSearchService.getSearchResult(searchParam, searchCommonParam);
                /** 实时搜索人物关系相册推荐 */
                ContentExtInfoVO extInfo = searchImageAlbumListService.searchImageSetAlbumList(vo.getSourceChannel(),
                        VersionUtil.getVersionMap(vo.getExtInfo()), vo.getLeadCopy(), vo.getRecommend(), searchResult);
                if (null != extInfo) {
                    //设置扩展内容
                    vo.setLeadCopy(extInfo.getLeadCopy());
                    vo.setRecommend(extInfo.getRecommend());
                }
                List<SearchInfo> searchInfoList = searchResult.createSearchInfoList(respParameters.getIntentionInfoList(), searchResultProperties);

                /** 搜索成功，set搜索结果的标题 */
                searchIntentionSetTitle(vo, toolsCommand, searchInfoList, respParameters, dialogueId, inContent);

                vo.setSearchParam(searchParam);
                vo.setSearchResult(searchResult);
                vo.setSearchInfoList(searchInfoList);
                // 不涉及任务表，所以要构造数据
                vo.setResultCode(resultCode);
                vo.setResultMsg(resultMsg);
                if (ResultCodeEnum.SUCCESS.getResultCode().equals(resultCode)) {
                    vo.setTaskStatus(TaskStatusEnum.PROCESS_FINISH.getCode());
                } else {
                    vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
                }
            } catch (JSONException e) {
                vo.setResultCode(ResultCodeEnum.UNKNOWN_ERROR.getResultCode());
                vo.setResultMsg(hbaseRespParameters);
                vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
                log.error("【搜索意图】的输出内容outContent处理-AlgorithmChatHistoryServiceImpl-outContentHandle-searchIntention，JSON解析异常，hbaseRespParameters：{}", hbaseRespParameters, e);
            } catch (Exception e) {
                vo.setResultCode(ResultCodeEnum.UNKNOWN_ERROR.getResultCode());
                vo.setResultMsg(hbaseRespParameters);
                vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
                log.error("【搜索意图】的输出内容outContent处理-AlgorithmChatHistoryServiceImpl-outContentHandle-searchIntention，异常，hbaseRespParameters：{}", hbaseRespParameters, e);
            }
        }
    }

    /**
     * 搜索成功，set搜索结果的标题
     *
     * @param vo             输出内容vo
     * @param toolsCommand   工具指令
     * @param searchInfoList 搜索结果
     * @param respParameters hbase的resp
     * @param dialogueId     对话id
     * @param inContent      对话内容
     * @Author: WeiJingKun
     */
    private void searchIntentionSetTitle(ContentVO vo, String toolsCommand, List<SearchInfo> searchInfoList, AiTextResultRespParameters respParameters, String dialogueId, String inContent) {
        if (CollUtil.isNotEmpty(searchInfoList)) {
            String title = respParameters.getTitle();
            /** set搜索结果的标题 */
            if (CharSequenceUtil.isNotBlank(title)) {
                vo.setTitle(title);
            } else {
                Long dialogueIdLong = Long.valueOf(dialogueId);
                List<DialogueIntentionVO.IntentionInfo> intentionInfoList = respParameters.getIntentionInfoList();
                Future<String> searchReturnTermsFutureV1 = null;
                if (CollUtil.isNotEmpty(intentionInfoList)) {
                    // 异步调用大模型生成搜索返回词V1
                    searchReturnTermsFutureV1 = searchReturnTermsService.getOptimizeReturnTermsFutureV1(dialogueIdLong, inContent);
                }
                // 获取搜索返回词V1（异常时返回默认值）
                vo.setTitle(searchReturnTermsService.getSearchReturnTermsV1(searchReturnTermsFutureV1, toolsCommand, dialogueIdLong, inContent));
            }
        }
    }

    /**
     * 获取搜索结果前，构建公共搜索参数
     *
     * @param vo        输出内容vo
     * @param inContent 对话内容
     * @return 公共搜索参数
     * @Author: WeiJingKun
     */
    private SearchCommonParam createSearchCommonParam(ContentVO vo, String inContent) {
        SearchCommonParam searchCommonParam = new SearchCommonParam();
        /** 处理：对话内容 */
        searchCommonParam.setDialogue(inContent);
        /** 处理：h5版本 */
        String extInfo = vo.getExtInfo();
        try {
            if (CharSequenceUtil.isNotBlank(extInfo)) {
                JSONObject extInfoJson = JSON.parseObject(extInfo);
                String h5Version = extInfoJson.getString("h5Version");
                if (CharSequenceUtil.isNotBlank(h5Version)) {
                    HeaderParams headerParams = RequestContextHolder.getHeaderParams();
                    if (null == headerParams) {
                        headerParams = new HeaderParams();
                    }
                    headerParams.setH5Version(h5Version);
                    // 获取extInfo中的h5Version放入线程
                    RequestContextHolder.setHeaderParams(headerParams);
                    // 公共参数添加h5Version
                    searchCommonParam.setH5Version(h5Version);
                }
            }
        } catch (JSONException e) {
            log.error("【搜索意图】获取搜索结果前，处理h5版本-AlgorithmChatHistoryServiceImpl-setH5VersionToRequestContextHolder，JSON解析异常，extInfo：{}", extInfo, e);
        } catch (Exception e) {
            log.error("【搜索意图】获取搜索结果前，处理h5版本-AlgorithmChatHistoryServiceImpl-setH5VersionToRequestContextHolder，异常，extInfo：{}", extInfo, e);
        }
        return searchCommonParam;
    }

    /**
     * 【图片配文意图】的输出内容outContent处理
     *
     * @Author: WeiJingKun
     */
    private static void pictureGenerateTextIntention(ContentVO vo, String toolsCommand, String hbaseRespParameters) {
        // 是否为图片配文意图
        if (DialogueIntentionEnum.PICTURE_GENERATE_TEXT.getCode().equals(toolsCommand)) {
            try {
                // hbaseRespParameters不是[开头，则不处理
                if (!hbaseRespParameters.startsWith(StrPool.BRACKET_START)) {
                    return;
                }
                List<String> outContentList = JSON.parseArray(hbaseRespParameters, String.class);
                // 列表转文本并且换行
                vo.setOutContent(CollUtil.join(outContentList, "\n"));
            } catch (JSONException e) {
                vo.setResultCode(ResultCodeEnum.UNKNOWN_ERROR.getResultCode());
                vo.setResultMsg(hbaseRespParameters);
                vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
                log.error("【图片配文意图】的输出内容outContent处理-AlgorithmChatHistoryServiceImpl-outContentHandle-pictureGenerateTextIntention，JSON解析异常，hbaseRespParameters：{}", hbaseRespParameters, e);
            } catch (Exception e) {
                vo.setResultCode(ResultCodeEnum.UNKNOWN_ERROR.getResultCode());
                vo.setResultMsg(hbaseRespParameters);
                vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
                log.error("【图片配文意图】的输出内容outContent处理-AlgorithmChatHistoryServiceImpl-outContentHandle-pictureGenerateTextIntention，异常，hbaseRespParameters：{}", hbaseRespParameters, e);
            }
        }
    }

    /**
     * 【文生文意图】的输出内容outContent处理
     *
     * @Author: WeiJingKun
     */
    private static void textIntention(ContentVO vo, String toolsCommand, String hbaseRespParameters, String taskId) {
        // 是否为文本意图&&文本工具意图
        if (DialogueIntentionEnum.isTextIntention(toolsCommand)
                || DialogueIntentionEnum.isTextToolIntention(toolsCommand)) {
            // 响应的文本 //n转/n
            vo.setOutContent(hbaseRespParameters.replace("//n", "/n"));

            /** 任务id为空，即V2版本，流式同步结果 */
            if (CharSequenceUtil.isBlank(taskId)) {
                try {
                    /** 把hbase获取的outContent转：文生文-流式同步结果 */
                    AiTextResultRespParameters aiTextResultRespParameters = JSON.parseObject(hbaseRespParameters, AiTextResultRespParameters.class);
                    String resultCode = aiTextResultRespParameters.getResultCode();

                    // set文生文-流式同步结果
                    vo.setOutContent(aiTextResultRespParameters.getData());
                    // set文生文-大模型联网搜索结果
                    vo.setNetworkSearchInfoList(aiTextResultRespParameters.getNetworkSearchInfoList());
                    // set文生文-思维链过程
                    vo.setReasoningContent(aiTextResultRespParameters.getReasoningContent());

                    // 不涉及任务表，所以要构造数据
                    vo.setResultCode(resultCode);
                    vo.setResultMsg(aiTextResultRespParameters.getResultMsg());
                    vo.setTitle(aiTextResultRespParameters.getTitle());
                    vo.setPersonalKnowledgeFileList(aiTextResultRespParameters.getPersonalKnowledgeFileList());
                    if (ResultCodeEnum.SUCCESS.getResultCode().equals(resultCode)) {
                        vo.setTaskStatus(TaskStatusEnum.PROCESS_FINISH.getCode());
                    } else {
                        vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
                    }
                } catch (JSONException e) {
                    vo.setResultCode(ResultCodeEnum.UNKNOWN_ERROR.getResultCode());
                    vo.setResultMsg(hbaseRespParameters);
                    vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
                    log.error("【文生文意图】的输出内容outContent处理-AlgorithmChatHistoryServiceImpl-outContentHandle-textIntention，JSON解析异常，hbaseRespParameters：{}", hbaseRespParameters, e);
                } catch (Exception e) {
                    vo.setResultCode(ResultCodeEnum.UNKNOWN_ERROR.getResultCode());
                    vo.setResultMsg(hbaseRespParameters);
                    vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
                    log.error("【文生文意图】的输出内容outContent处理-AlgorithmChatHistoryServiceImpl-outContentHandle-textIntention，异常，hbaseRespParameters：{}", hbaseRespParameters, e);
                }
            }
        }
    }

    /**
     * 发邮件信息处理
     *
     * @param vo 对话内容VO
     */
    private void sendMailIntention(ContentVO vo) {
        // 不是发邮件意图，直接返回
        String toolsCommand = vo.getToolsCommand();
        if (!DialogueIntentionEnum.isSendMail(toolsCommand)) {
            return;
        }

        String hbaseResp = vo.getHbaseRespParameters();
        try {
            // 把hbase获取的outContent转：发邮件意图结果
            AiTextResultRespParameters respParameters = JSON.parseObject(hbaseResp, AiTextResultRespParameters.class);
            String resultCode = respParameters.getResultCode();

            // 不涉及任务表，所以要构造数据
            vo.setResultCode(resultCode);
            vo.setResultMsg(respParameters.getResultMsg());

            // 邮件信息
            vo.setMailInfo(respParameters.getMailInfo());
            vo.setTitle(respParameters.getTitle());
            if (ResultCodeEnum.SUCCESS.getResultCode().equals(resultCode)) {
                vo.setTaskStatus(TaskStatusEnum.PROCESS_FINISH.getCode());
            } else {
                vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
            }
        } catch (JSONException e) {
            vo.setResultCode(ResultCodeEnum.UNKNOWN_ERROR.getResultCode());
            vo.setResultMsg(hbaseResp);
            vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
            log.error("【发邮件意图】的输出内容处理-AlgorithmChatHistoryServiceImpl-outContentHandle-sendMailIntention，JSON解析异常，hbaseResp：{}", hbaseResp, e);
        } catch (Exception e) {
            vo.setResultCode(ResultCodeEnum.UNKNOWN_ERROR.getResultCode());
            vo.setResultMsg(hbaseResp);
            vo.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
            log.error("【发邮件意图】的输出内容处理-AlgorithmChatHistoryServiceImpl-outContentHandle-sendMailIntention，异常，hbaseResp：{}", hbaseResp, e);
        }
    }

    /**
     * 【图片智能鉴伪意图】的输出内容处理
     */
    private void smartFakeCheckIntention(ContentVO vo, String toolsCommand) {
        if (DialogueIntentionEnum.SMART_FAKE_CHECK.getCode().equals(toolsCommand)) {
            String respParam = vo.getRespParam();
            if (CharSequenceUtil.isNotEmpty(respParam)) {
                try {
                    // mapping
                    List<TaskRespParamVO> respParamList = JSON.parseArray(respParam, TaskRespParamVO.class);
                    // 图片智能鉴伪初版仅有一个结果,因此只提取一个值,构造函数已封装
                    vo.setOutContent(JSONUtil.toJsonStr(new SmartFakeCheckVO().initialVersionCreate(respParamList)));
                } catch (Exception e) {
                    log.warn("smartFakeCheckIntention | 图片智能鉴伪outContent处理异常", e);
                }
            }
        }
    }

    /**
     * 会话历史列表返回构造输入资源信息 resourceType,inResourceId,outResourceType,outResourceId
     *
     * @param contentVO 历史会话信息 contentList接口修改
     *                  1，resourceType=4（ResourceTypeEnum.DIALOGUE.getType()）的情况，inResourceId就是上一次对话id；
     *                  2，查询到这个对话响应输出是否云盘类型；
     *                  ①是云盘类型：修改resourceType=3（ResourceTypeEnum.PICTURE.getType()），inResourceId=上一次对话id的outResourceId
     *                  ②否，但是做了转存，设置云盘类型：修改resourceType=3（ResourceTypeEnum.PICTURE.getType()），inResourceId=上一次对话id的outResourceId
     *                  ③否，是EOS类型：不修改resourceType，还是4，inResourceId=上一次对话id的eos url
     */
    @MethodExecutionTimeLog("会话历史列表返回构造输入资源信息-serviceImpl")
    private void handleInResourceIdAndType(ContentVO contentVO) {
        try {
            //判断本次对话类型为对话，inResourceId为上一次对话id
            if (contentVO.getResourceType().equals(ResourceTypeEnum.DIALOGUE.getType())) {
                //不存在则按云盘eos相关逻辑处理返回
                Long did = NumberUtil.getLongValue(contentVO.getInResourceId());
                if (null == did) {
                    //如果对话内容找不到，设置输入资源id为空
                    contentVO.setInResourceId("");
                    return;
                }
                AlgorithmChatContentEntity lastContentEntity = algorithmChatContentRepository.getById(did);
                if (lastContentEntity == null) {
                    //如果对话内容找不到，设置输入资源id为空
                    contentVO.setInResourceId("");
                    return;
                }
                TaskAiAbilityEntity taskEntity = taskAiAbilityRepository.getTaskEntity(lastContentEntity.getTaskId());
                //查询任务表的相应参数
                if (null == taskEntity || CharSequenceUtil.isEmpty(taskEntity.getRespParam())) {
                    log.info("任务不存在,dialogueId:{},taskId:{}", lastContentEntity.getId(), lastContentEntity.getTaskId());
                    contentVO.setInResourceId("");
                    return;
                }
                Optional<TaskRespParamVO> optional = JSONUtil.toList(taskEntity.getRespParam(), TaskRespParamVO.class)
                        .stream().findFirst();
                if (!optional.isPresent()) {
                    log.info("TaskRespParamVO 转换不存在,dialogueId:{},taskId:{}", lastContentEntity.getId(), lastContentEntity.getTaskId());
                    contentVO.setInResourceId("");
                    return;
                }
                TaskRespParamVO taskRespParamVO = optional.get();
                //根据对话结果来判断图片类型taskRespParamVO
                log.info("对话内容结果对象:{}", JSONUtil.toJsonStr(taskRespParamVO));
                if (taskRespParamVO.isYunDiskImageResp()) {
                    //查询到这个对话响应输出是否云盘类型 ①是云盘类型：修改resourceType=3 图片，inResourceId=上一次对话id的outResourceId
                    contentVO.setInResourceId(lastContentEntity.getOutResourceId());
                    contentVO.setResourceType(ResourceTypeEnum.PICTURE.getType());
                } else {
                    ContentVO lastContentVO = new ContentVO();
                    lastContentVO.setOutResourceId(lastContentEntity.getOutResourceId());
                    lastContentVO.setUserId(lastContentEntity.getUserId());
                    lastContentVO.setDialogueId(String.valueOf(lastContentEntity.getId()));
                    lastContentEntity.setRespParam(taskEntity.getRespParam());
                    //执行输入资源信息设置 resourceType inResourceId
                    setResourceIdAndType(lastContentVO, lastContentEntity, EditResourceEnum.IN.getCode());
                    //填充返回结果
                    if (null != lastContentVO.getResourceType()) {
                        contentVO.setResourceType(lastContentVO.getResourceType());
                    }
                    contentVO.setInResourceId(lastContentVO.getInResourceId());
                }
            }
        } catch (Exception e) {
            log.error("设置输出资源类型异常,contentVO:{}", contentVO, e);
        }
    }

    /**
     * 设置对话内容vo out 输出资源信息 outResourceId outResourceType
     *
     * @param contentVO     对话内容vo 修改输出则是本次对话信息 修改输入则是传入上一次对话信息
     * @param contentEntity 对话内容实体 修改输出则是本次对话信息 修改输入则是传入上一次对话信息
     * @param ioType        io编辑类型
     */
    @MethodExecutionTimeLog("设置输入输出资源id和type-serviceImpl")
    private void setResourceIdAndType(ContentVO contentVO, AlgorithmChatContentEntity contentEntity, Integer ioType) {
        AiTextResultRespParameters respParameters = null;
        String hbaseRespParameters = contentVO.getHbaseRespParameters();
        if (CharSequenceUtil.isNotBlank(hbaseRespParameters)) {
            try {
                respParameters = JSON.parseObject(hbaseRespParameters, AiTextResultRespParameters.class);
            } catch (Exception e) {
                log.info("【对话结果VO】json转respParameters异常，hbaseRespParameters：{}", hbaseRespParameters, e);
            }
        }
        if (ObjectUtil.isNotNull(respParameters) && AiTextResultVersionEnum.isV2(respParameters.getVersion())) {
            // V2版本不在这里处理输出资源相关信息
            return;
        }

        // 输出资源信息；（云盘文件ID/下载URL）不为空
        if (CharSequenceUtil.isNotEmpty(contentVO.getOutResourceId())
                // && 检查资源id是否正常能查到数据
                && checkOutResourceId(contentVO, ioType)) {
            return;
        }
        String eosUrl = handleEosUrl(contentEntity);
        if (CharSequenceUtil.isEmpty(eosUrl)) {
            //根据对话内容信息判断如果是图片资源id 资源输出类型默认设置为个人云文件id,outResourceId为文件id
            if (Objects.equals(EditResourceEnum.OUT.getCode(), ioType)) {
                contentVO.setOutResourceType(ImageTransmissionTypeEnum.YUN_DISK.getCode());
            }
            return;
        }
        //设置资源eos类型
        if (Objects.equals(EditResourceEnum.IN.getCode(), ioType)) {
            //是EOS类型：不修改resourceType，还是4，inResourceId=上一次对话id的eos url
            contentVO.setInResourceId(eosUrl);
        } else if (Objects.equals(EditResourceEnum.OUT.getCode(), ioType)) {
            contentVO.setOutResourceId(eosUrl);
            contentVO.setOutResourceType(ImageTransmissionTypeEnum.EOS.getCode());
        }
    }

    /**
     * 检查资源id是否正常能查到数据
     *
     * @param contentVO
     * @param ioType
     * @return boolean true-正常
     * @Author: WeiJingKun
     */
    private boolean checkOutResourceId(ContentVO contentVO, Integer ioType) {
        //资源id存在 判断原有id是否存在，不存在则使用资源id查询eos链接
        if (EditResourceEnum.OUT.getCode().equals(ioType)) {
            contentVO.setOutResourceType(ImageTransmissionTypeEnum.YUN_DISK.getCode());
        }
        YunDiskReqDTO yunDiskReqDTO = new YunDiskReqDTO();
        yunDiskReqDTO.setFileId(contentVO.getOutResourceId());
        try {
            File yunDiskContentVO = yunDiskV2Service.getYunDiskContentAndBigThumbnailUrl(yunDiskReqDTO);
            if (yunDiskContentVO != null) {
                if (EditResourceEnum.IN.getCode().equals(ioType)) {
                    contentVO.setResourceType(ResourceTypeEnum.PICTURE.getType());
                    contentVO.setInResourceId(contentVO.getOutResourceId());
                }
                return true;
            }
        } catch (Exception e) {
            List<String> errorCodeList = Arrays.asList(YunDiskClient.FILE_NOT_FOUND_CODE_OSE,
                    YunDiskClient.FILE_NOT_FOUND_CODE_NEW,
                    YunDiskClient.FILE_DIR_NOT_FOUND_CODE_NEW
            );
            //云盘相关错误走eos，其他异常都返回云盘id与云盘存储类型，
            if (!(e instanceof YunAiBusinessException) ||
                    !errorCodeList.contains(((YunAiBusinessException) e).getCode())) {
                //获取不到则直接返回文件id与类型
                log.info("info-获取个人云文件错误,对话id:{},用户id:{},文件id:{}", contentVO.getDialogueId(),
                        contentVO.getUserId(), contentVO.getOutResourceId(), e);
                if (EditResourceEnum.IN.getCode().equals(ioType)) {
                    contentVO.setResourceType(ResourceTypeEnum.PICTURE.getType());
                    contentVO.setInResourceId(contentVO.getOutResourceId());
                }
                return true;
            }
            log.warn("info-获取个人云文件警告,对话id:{},用户id:{},文件id:{}", contentVO.getDialogueId(),
                    contentVO.getUserId(), contentVO.getOutResourceId(), e);
        }
        return false;
    }

    /**
     * 处理eosUrl
     *
     * @param contentEntity 对话内容实体
     */
    private String handleEosUrl(AlgorithmChatContentEntity contentEntity) {
        //不存在则按云盘eos相关逻辑处理返回
        if (CharSequenceUtil.isEmpty(contentEntity.getRespParam())) {
            return null;
        }
        Optional<TaskRespParamVO> optional = JSONUtil.toList(contentEntity.getRespParam(), TaskRespParamVO.class)
                .stream().findFirst();
        if (!optional.isPresent()) {
            return null;
        }
        TaskRespParamVO taskRespParamVO = optional.get();
        //根据对话结果来判断图片类型taskRespParamVO
        log.info("对话内容结果对象:{}", JSONUtil.toJsonStr(taskRespParamVO));
        if (!taskRespParamVO.getOutResourceType().equals(ResourceTypeEnum.PICTURE.getType())) {
            return null;
        }
        if (taskRespParamVO.isEosImageResp()) {
            //获取eos连接，暂定失效时间24h,输出资源为eos图片时 将outResourceId设置为图片url
            TaskAiAbilityEntity taskEntity = taskAiAbilityRepository.getTaskEntity(contentEntity.getTaskId());
            if (null == taskEntity) {
                log.info("任务不存在,taskId:{}", contentEntity.getTaskId());
                throw new YunAiBusinessException(ResultCodeEnum.TASK_RECORD_NOT_FOUND);
            }
            String fileSuffix = ImageSuffixEnum.getByAlgorithmCode(taskEntity.getAlgorithmCode()).getCode();
            String eosUrl = eosExternalService.getFileUrl(taskRespParamVO.getOutResourceId(),
                    taskRespParamVO.getOutResourceId() + StrPool.DOT + fileSuffix,
                    eosFileExpireConfig.getExpireTime());
            log.info("对话id:{},获取到eosUrl:{}", contentEntity.getId(), eosUrl);
            return eosUrl;
        }
        return null;
    }

    /**
     * 获取文件信息
     *
     * @param flowResult the flow result
     * @param fileId     the file id
     * <AUTHOR>
     * @date 2025/5/6 17:42
     */
    private void getFileInfo(DialogueFlowResult flowResult, String fileId) {
        if (StringUtils.isEmpty(fileId)) {
            return;
        }
        List<String> fileIds = new ArrayList<>();
        // outResourceId以[]包裹的为新版数据
        if (fileId.startsWith(StrPool.BRACKET_START)) {
            List<TaskRespParamVO> respParamVOList = JSON.parseArray(fileId, TaskRespParamVO.class);
            fileIds = respParamVOList.stream()
                    .filter(TaskRespParamVO::isYunDiskImageResp)
                    .map(TaskRespParamVO::getOutResourceId).collect(Collectors.toList());
        } else {
            // 旧版本数据outResourceId即为文件id
            fileIds.add(fileId);
        }
        if (CollUtil.isEmpty(fileIds)) {
            return;
        }
        List<File> fileList = fileIds.stream().map(id -> {
            YunDiskReqDTO fileDto = new YunDiskReqDTO();
            fileDto.setFileId(id);
            // 查找云盘文件
            File fileVo = yunDiskV2Service.getYunDiskContentAndBigThumbnailUrl(fileDto);
            if (Objects.isNull(fileVo)) {
                fileVo = new File();
                fileVo.setFileId(id);
            }
            return fileVo;
        }).collect(Collectors.toList());

        // 任务成功，hbase存在进行中的（设置fileList）多图需要这里处理为多个fileVO
        flowResult.setFileList(fileList);
        if (CollectionUtils.isNotEmpty(fileList)) {
            flowResult.setFileUrlList(
                    fileList.stream().map(File::getContent).collect(Collectors.toList()));
        }
    }

    /**
     * 获取相册信息
     *
     * @param flowResult the flow result
     * <AUTHOR>
     * @date 2025/5/21 19:28
     */
    private void getAlbumInfo(DialogueFlowResult flowResult) {
        if (CollUtil.isEmpty(flowResult.getAlbumList())) {
            log.info("info-对话结果中无相册信息");
            return;
        }
        try {
            List<AlbumInfo> list = flowResult.getAlbumList().stream().map(albumInfo -> albumSaasExternalService
                    .queryAlbumByAlbumId(albumInfo.getAlbumId())).collect(Collectors.toList());
            flowResult.setAlbumList(list);
        } catch (Exception e) {
            log.error("info-获取相册信息失败", e);
        }
    }
    

	private void getSearchInfo(DialogueFlowResult flowResult) {
		if (CollUtil.isEmpty(flowResult.getSearchInfoList())) {
			log.info("info-对话结果中无搜索图片信息");
			return;
		}
		if (null == flowResult.getSearchInfoList().get(0).getSearchParam()) {
			return;
		}
		try {
			SearchImageParam searchImageParam = JSONObject.parseObject(
					JSONObject.toJSONString(flowResult.getSearchInfoList().get(0).getSearchParam()),
					SearchImageParam.class);
			SearchImageReqDTO reqDTO = new SearchImageReqDTO(searchImageParam.getText(),
					searchImageParam.getPageInfo().getPageSize(), searchImageParam.getIsOriginal());
			SearchInfo searchInfo = chatDialogueSearchService.searchImages(reqDTO);
			if (null != searchInfo) {
				flowResult.setSearchInfoList(Collections.singletonList(searchInfo));
			}
		} catch (Exception e) {
			log.error("info-获取搜索图片信息失败", e);
		}
	}

	/**
	 * 为AIPPT结果设置coverUrl
	 *
	 * @param resultList 对话结果列表
	 * @param userId 用户ID
	 */
	@MethodExecutionTimeLog("为AIPPT结果设置coverUrl")
	private void setCoverUrlForAipptResults(List<DialogueResultV2VO> resultList, String userId) {
		if (CollUtil.isEmpty(resultList)) {
			return;
		}

		long startTime = System.currentTimeMillis();
		log.info("【AIPPT封面设置】开始处理，用户ID: {}, 对话数量: {}", userId, resultList.size());

		try {
			// 收集需要查询coverUrl的designId信息
			List<AipptCoverQueryInfo> queryInfoList = new ArrayList<>();

			for (DialogueResultV2VO dialogueResult : resultList) {
				if (dialogueResult.getDialogueInputInfo() != null
					&& dialogueResult.getDialogueInputInfo().getCommand() != null
					&& DialogueIntentionSubEnum.AI_GENERATE_PPT.getCode().equals(
						dialogueResult.getDialogueInputInfo().getCommand().getSubCommand())
					&& CollUtil.isNotEmpty(dialogueResult.getOutputList())) {

					for (DialogueFlowResult flowResult : dialogueResult.getOutputList()) {
						if (flowResult.getAiFunctionResult() != null) {
							AiFunctionResult aiFunctionResult = flowResult.getAiFunctionResult();

							// 检查是否满足设置coverUrl的条件
							if (aiFunctionResult.getSupplierType() != null
								&& SupplierTypeEnum.AIPPT.getCode() == aiFunctionResult.getSupplierType().intValue()
								&& CharSequenceUtil.isNotBlank(aiFunctionResult.getDesignId())) {

								queryInfoList.add(new AipptCoverQueryInfo(
									aiFunctionResult.getDesignId(),
									aiFunctionResult
								));
							}
						}
					}
				}
			}

			if (CollUtil.isEmpty(queryInfoList)) {
				log.info("【AIPPT封面设置】无需要查询的designId");
				return;
			}

			log.info("【AIPPT封面设置】需要查询的designId数量: {}", queryInfoList.size());

			// 并行查询coverUrl
			CompletableFuture.allOf(queryInfoList.stream().map(queryInfo ->
				CompletableFuture.runAsync(() -> {
					try {
						AipptDesignInfoRequestDTO request = AipptDesignInfoRequestDTO.builder()
							.userDesignId(Long.valueOf(queryInfo.getDesignId()))
							.build();

						AipptDesignInfoResponseVO response = aipptExternalService.getDesignInfoWithRetry(
							request, userId, null);

						if (response != null && response.getCode() == 0
							&& response.getData() != null
							&& CharSequenceUtil.isNotBlank(response.getData().getCoverUrl())) {

							queryInfo.getAiFunctionResult().setCoverUrl(response.getData().getCoverUrl());
							log.info("【AIPPT封面设置】成功设置coverUrl，designId: {}, coverUrl: {}",
								queryInfo.getDesignId(), response.getData().getCoverUrl());
						} else {
							log.warn("【AIPPT封面设置】查询失败或无结果，designId: {}, response: {}",
								queryInfo.getDesignId(), response);
						}
					} catch (Exception e) {
						log.error("【AIPPT封面设置】查询异常，designId: {}", queryInfo.getDesignId(), e);
					}
				}, algorithmChatThreadPool)
			).toArray(CompletableFuture[]::new)).get(30, TimeUnit.SECONDS);

		} catch (Exception e) {
			log.error("【AIPPT封面设置】处理异常，用户ID: {}", userId, e);
		} finally {
			long costTime = System.currentTimeMillis() - startTime;
			log.info("【AIPPT封面设置】处理完毕，总耗时: {}ms", costTime);
		}
	}

	/**
	 * AIPPT封面查询信息
	 */
	private static class AipptCoverQueryInfo {
		private final String designId;
		private final AiFunctionResult aiFunctionResult;

		public AipptCoverQueryInfo(String designId, AiFunctionResult aiFunctionResult) {
			this.designId = designId;
			this.aiFunctionResult = aiFunctionResult;
		}

		public String getDesignId() {
			return designId;
		}

		public AiFunctionResult getAiFunctionResult() {
			return aiFunctionResult;
		}
	}

}
