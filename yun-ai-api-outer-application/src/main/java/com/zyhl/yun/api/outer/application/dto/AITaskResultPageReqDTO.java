package com.zyhl.yun.api.outer.application.dto;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 任务结果信息请求入参req
 *
 * <AUTHOR>
 * @Date 2024/03/16 12:00
 */
@Data
@Slf4j
public class AITaskResultPageReqDTO implements Serializable {

    private static final long serialVersionUID = -8142846648709373297L;

    /**
     * 用户id，如果有token就不需要传
     */
    private String userId;

    @NotBlank(message = "指令不能为空")
    /**
     * AI指令
     * 详见IntentionCommands枚举值
     */
    private String command;

    /**
     * 渠道号；参考枚举 ChannelId 渠道ID
     */
    private String channelId;

    /** 分页信息 */
    private PageInfoDTO pageInfo;

    /**
     * 参数校验
     * @Author: WeiJingKun
     */
    public void validate(int minPageSize) {
        /** 检查登录的userId */
        checkTokenUserId();
        /** 分页信息校验 */
        if(null != pageInfo){
            pageInfo.validate(minPageSize);
        }
        //参数校验
        checkParam();
    }
    
    /**
     * 参数校验
     * @Author: WeiJingKun
     */
    public void validate() {
        /** 检查登录的userId */
        checkTokenUserId();
        /** 分页信息校验 */
        if(null != pageInfo){
            pageInfo.validate();
        }
    }

    /**
     * 检查登录的userId
     *
     * @Author: shixiaokang
     */
    public void checkTokenUserId() {
        if (CharSequenceUtil.isNotEmpty(RequestContextHolder.getUserId())) {
            this.userId = RequestContextHolder.getUserId();
        }
        if (CharSequenceUtil.isEmpty(this.userId)) {
            log.error("用户ID不能为空");
            // 如果UserInformationContextHolder中的userId无值，且传入的userId也无值，则抛出异常
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_USERID);
        }
    }

    public void checkParam() {
        if (CharSequenceUtil.isEmpty(command)) {
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS.getResultCode(), ("工具指令不能为空"));
        }
        if (DialogueIntentionEnum.NON_AI_TOOLS_MODULE.contains(command)) {
            log.info("指令非AI工具指令:{}",command);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS.getResultCode(), ("工具指令非AI工具指令"));
        }
    }
}
