package com.zyhl.yun.api.outer.application.dto;

import com.zyhl.yun.api.outer.application.vo.TaskCommentInfoVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 任务评论DTO
 * @date 2025/4/21 15:51
 */
@Slf4j
@Data
public class TaskCommentDTO extends BaseDTO implements Serializable {

    /**
     * 任务来源渠道
     */
    @NotBlank(message = "任务来源渠道不能为空")
    private String sourceChannel;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 任务ID
     */
    @NotBlank(message = "任务ID不能为空")
    private String taskId;

    /**
     * 评论信息列表
     */
    @NotEmpty(message = "评论信息列表不能为空")
    private List<TaskCommentInfoVO> commentList;

}