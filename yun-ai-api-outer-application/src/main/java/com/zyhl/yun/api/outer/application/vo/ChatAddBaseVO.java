package com.zyhl.yun.api.outer.application.vo;

import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchResult;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddResultTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/5/27 11:29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatAddBaseVO {

    /**
     * 对话ID
     */
    private String dialogueId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 返回类型
     *
     * @see ChatAddResultTypeEnum
     */
    private Integer resultType;

    /**
     * 返回结果文本
     */
    private String text;

    /**
     * 引导文案对象
     */
    private LeadCopyVO leadCopy;

    /**
     * 意图枚举
     *
     * @see DialogueIntentionEnum
     */
    private String commands;

    /**
     * 搜索意图入参
     */
    private SearchParam searchParam;

    /**
     * 搜索意图结果
     */
    private SearchResult searchResult;

    /**
     * 用户智能相册授权开关状态，当且仅当用户的意图commands为“012”和“018”时才返回，
     * 0--未开启
     * 1--已开启
     * todo 单词拼错了，前端改完后弃用 20240730
     */
    @Deprecated
    private Integer aiAblumStatus;

    /**
     * 用户智能相册授权开关状态，当且仅当用户的意图commands为“012”和“018”时才返回，
     * 0--未开启
     * 1--已开启
     */
    private Integer aiAlbumStatus;

}
