package com.zyhl.yun.api.outer.application.util;

import cn.hutool.extra.spring.SpringUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.logger.util.LogUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.enums.ApiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.model.api.utils.TextModelUtil;
import com.zyhl.yun.api.outer.config.FlowTypeProperties;
import com.zyhl.yun.api.outer.constants.RegConst;
import com.zyhl.yun.api.outer.domain.vo.chat.ContentVO;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 流式对象data工具类
 *
 * <AUTHOR>
 * @Date 2024/4/10 14:30
 */
@Slf4j
public class SseEmitterDataUtils {

	/**
	 * sse默认名称
	 */
	public static final String SSE_EMITTER_CHAT_ADD_NAME = "chatAddV2";
	/**
	 * 知识库返回sse名称
	 */
	public static final String SSE_NAME_KNOWLEDGE = "AI-chat-sse-kn";
	/**
	 * 知识库大模型判断命中返回sse名称
	 */
	public static final String SSE_NAME_KNOWLEDGE_RELEVANCY = "AI-chat-sse-kn-relevancy";
	
    private static final List<String> ERROR_CODE_LIST = Arrays.asList(
            AiResultCode.CODE_01000001.getCode(),
            AiResultCode.CODE_01000003.getCode(),
            AiResultCode.CODE_01000004.getCode(),
            AiResultCode.CODE_10022024.getCode()
    );

    private static FlowTypeProperties flowTypeProperties;

    /**
     * 断开连接后尝试重连时间（毫秒）
     *
     * @return
     */
    private static Long getReconnectTimeMillis() {
        if (flowTypeProperties == null) {
            flowTypeProperties = SpringUtil.getBean(FlowTypeProperties.class);
        }
        return flowTypeProperties == null ? 5000L : flowTypeProperties.getReconnectTimeMillis();
    }

    /**
     * 获取流式对象Data
     *
     * @param result 公共结果
     * @return Data对象
     */
    public static String getData(BaseResult<?> result) {
        return JsonUtil.toJson(result);
    }

    /**
     * 发送默认异常SseEmitter消息并关闭连接
     *
     * @param sseEmitter SseEmitter对象
     */
    public static void sendDefaultErrorMsgAndComplete(SseEmitter sseEmitter, AtomicBoolean completeFlag) {
        // 默认异常
        BaseResult<?> errRes = BaseResult.error(ResultCodeEnum.ERROR_SERVER_INTERNAL);

        try {
            // 发送SseEmitter消息
            sendMsg(sseEmitter, errRes, completeFlag);
        } catch (IOException e) {
            log.error("SseEmitterDataUtils sendDefaultErrorMsg data:{} | error:", JsonUtil.toJson(errRes), e);
        } finally {
            // 关闭连接
            complete(sseEmitter, completeFlag);
        }
    }
    /**
     * 发送SseEmitter消息
     *
     * @param sseEmitter SseEmitter对象
     * @param result     公共结果
     */
    public static void sendMsg(SseEmitter sseEmitter, BaseResult<?> result, AtomicBoolean completeFlag) throws IOException {
    	sendMsg(sseEmitter, result, completeFlag, true);
    }

    /**
     * 发送SseEmitter消息
     *
     * @param sseEmitter SseEmitter对象
     * @param result     公共结果
     */
    public static void sendMsg(SseEmitter sseEmitter, BaseResult<?> result, AtomicBoolean completeFlag, Boolean mergeAiModelException) throws IOException {
        if (completeFlag.get()) {
            return;
        }
		if (Boolean.TRUE.equals(mergeAiModelException)
				&& !ResultCodeEnum.SUCCESS.getResultCode().equals(result.getCode())) {
        	//重写错误码
        	AiResultCode aiResultCode = AiResultCode.getByCodeOrMsg(result.getCode(), result.getMessage());
        	if(null != aiResultCode) {
        		result = BaseResult.error(aiResultCode.getCode(), aiResultCode.getMsg());
        	}
        	//AI模型异常错误码全局处理
        	YunAiBusinessException exception = caseAiModelException(result.getCode());
        	if(null != exception) {
        		result = BaseResult.error(exception.getCode(), exception.getMessage());
        	}
        }
        // 调用send发送消息
        sseEmitter.send(SseEmitter.event()
                .reconnectTime(getReconnectTimeMillis())
                .id(String.valueOf(System.currentTimeMillis()))
                .name(SSE_EMITTER_CHAT_ADD_NAME)
                .data(getData(result))
        );
        //接口日志输出
        LogUtil.setInterfaceOutput(result);
        
        // 这批错误码不需要打印日志
        if (ERROR_CODE_LIST.contains(result.getCode())) {
            return;
        }

        log.info("会话输入SSE接口请求结果返回 result:{}", JsonUtil.toJson(result));
    }

    /**
     * 发送SseEmitter消息并关闭连接（不合并大模型异常的方法）
     *
     * @param sseEmitter SseEmitter对象
     * @param result     公共结果
     */
    public static void sendMsgAndCompleteOthers(SseEmitter sseEmitter, BaseResult<?> result, AtomicBoolean completeFlag) {
        try {
            // 发送SseEmitter消息
            sendMsg(sseEmitter, result, completeFlag, false);
        } catch (Exception e) {
            log.error("SseEmitterDataUtils sendMsgAndComplete data:{} | error:", JsonUtil.toJson(result), e);
        } finally {
            // 关闭连接
            complete(sseEmitter, completeFlag);
        }
    }
    
    /**
     * 发送SseEmitter消息并关闭连接
     *
     * @param sseEmitter SseEmitter对象
     * @param result     公共结果
     */
    public static void sendMsgAndComplete(SseEmitter sseEmitter, BaseResult<?> result, AtomicBoolean completeFlag) {
        try {
            // 发送SseEmitter消息
            sendMsg(sseEmitter, result, completeFlag);
        } catch (Exception e) {
            log.error("SseEmitterDataUtils sendMsgAndComplete data:{} | error:", JsonUtil.toJson(result), e);
        } finally {
            // 关闭连接
            complete(sseEmitter, completeFlag);
        }
    }

    /**
     * 关闭SseEmitter连接
     *
     * @param sseEmitter   SseEmitter对象
     * @param completeFlag 关闭标识
     */
    public static void complete(SseEmitter sseEmitter, AtomicBoolean completeFlag) {
        if (completeFlag.get()) {
            return;
        }

        // 关闭连接
        sseEmitter.complete();
        completeFlag.set(true);
        log.info("SseEmitter complete end completeFlag:{}", completeFlag);
    }

    /**
     * 过滤大模型返回忽略文本方法
     * @param string
     * @return
     */
	public static String filterIgnoreString(String string) {
		if (null != string && string.startsWith(TextModelUtil.TAG_START_THINK)) {
			// 去除空思维链
			return string
					.replace((TextModelUtil.TAG_START_THINK + TextModelUtil.TAG_START_THINK),
							TextModelUtil.TAG_START_THINK)
					.replace(TextModelUtil.TAG_START_THINK + "\n\n" + TextModelUtil.TAG_END_THINK, "")
					.replace(TextModelUtil.TAG_START_THINK + "\n" + TextModelUtil.TAG_END_THINK, "")
					.replace((TextModelUtil.TAG_START_THINK + TextModelUtil.TAG_END_THINK), "")
					.replace(TextModelUtil.TAG_START_THINK + "\n\n", TextModelUtil.TAG_START_THINK)
					.replace(TextModelUtil.TAG_START_THINK + "\n", TextModelUtil.TAG_START_THINK);
		}
		return string;
	}
	
	/**
	 * 过滤送审过滤词
	 * @param string
	 * @return
	 */
	public static String filterIgnoreAudit(String string) {
		if (null != string) {
			return string.replace("\n", "").replace(TextModelUtil.TAG_START_THINK, "")
					.replace(TextModelUtil.TAG_END_THINK, "");
		}
		return null;
	}
	
	/**
     * 转换ai大模型系统异常
     * @param e
     * @return
     */
	public static YunAiBusinessException caseAiModelException(YunAiBusinessException e) {
		YunAiBusinessException exception = caseAiModelException(e.getCode());
		if (null != exception) {
			return exception;
		}
		return e;
	}
	
	/**
     * 转换ai大模型系统异常
     * @param e
     * @return
     */
	public static YunAiBusinessException caseAiModelException(String code) {
		if (ApiCommonResultCode.DOWNSTREAM_SERVICES_EXCEPTION.getResultCode().equals(code)
				|| ApiCommonResultCode.DOWNSTREAM_SERVER_PROCESSING_FAILED.getResultCode().equals(code)) {
			return new YunAiBusinessException(ResultCodeEnum.ERROR_AI_MODEL);
		}
		return null;
	}
	
	/**
	 * 拆分旧版本思维链数据到reasoningContent
	 * 
	 * @param content
	 */
	public static void splitOldReasoningContent(ContentVO content) {
		String outContent = content.getOutContent();
		if (StringUtils.isBlank(content.getReasoningContent()) && StringUtils.isNotBlank(outContent)) {
			int startThinkIndex = outContent.indexOf(TextModelUtil.TAG_START_THINK);
			int endThinkIndex = outContent.indexOf(TextModelUtil.TAG_END_THINK);
			if (-1 != startThinkIndex && -1 == endThinkIndex) {
				content.setReasoningContent(outContent);
				content.setOutContent(StringUtils.EMPTY);
			}
			if (-1 != startThinkIndex && -1 != endThinkIndex) {
				content.setReasoningContent(
						outContent.substring(startThinkIndex + TextModelUtil.TAG_START_THINK.length(), endThinkIndex));
				content.setOutContent(outContent.replaceAll(RegConst.REG_MODEL_THINK, ""));
			}
		}
	}

	/**
	 * 合并新版本思维链数据
	 * 
	 * @param content
	 */
	public static void mergeNewReasoningContent(ContentVO content) {
		String outContent = SseEmitterDataUtils.filterIgnoreString(content.getOutContent());

		String reasoningContent = SseEmitterDataUtils.filterIgnoreString(content.getReasoningContent());
		if (StringUtils.isNotBlank(reasoningContent) && StringUtils.isNotEmpty(outContent)) {
			// 拼接思维链，追加think标签对
			content.setOutContent(
					TextModelUtil.TAG_START_THINK + reasoningContent + TextModelUtil.TAG_END_THINK + outContent);
		}

		if (StringUtils.isNotBlank(reasoningContent) && StringUtils.isEmpty(outContent)) {
			// 拼接思维链，追加think标签，无think结束标签
			content.setOutContent(TextModelUtil.TAG_START_THINK + reasoningContent);
		}
	}
}
