package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueImageToolSettingDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueToolSettingDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.event.completeimpl.NextAsynImageToolCallbackEvent;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.service.BenefitService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.SseNameEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.application.chatv2.hanlde.impl.AsynTextToPictureHandlerImpl}
 * <br>
 * <b> description:</b> 文生图2.0版本处理handler
 *
 * <AUTHOR>
 * @date 2025-04-23 13:44
 **/
@Slf4j
@Component
public class AsynTextToPictureHandlerImpl extends AbstractChatAddV2Handler {

	/**
	 * 当前执行顺序
	 */
	private final ExecuteSort thisExecuteSort = ExecuteSort.ASYNC_TEXT_GENERATE_PICTURE;

	private static final String REPLACE_KEY_OF_QUERY = "{query}";

	@Resource
	private TextModelTextSseHandlerImpl textModelTextSseHandlerImpl;
	@Resource
	private AsyncImageToolHandlerImpl asyncImageToolHandlerImpl;
	@Resource
	private DataSaveService dataSaveService;
	@Resource
	private BenefitService benefitService;
	@Resource
	private ChatConfigServiceDomainService chatConfigServiceDomainService;
	@Resource
	private UidGenerator uidGenerator;
	@Resource
	private ChatTextToolBusinessConfig chatTextToolBusinessConfig;

	@Override
	public boolean execute(ChatAddHandleDTO handleDTO) {
		// 文生图意图
		return DialogueIntentionEnum.TEXT_GENERATE_PICTURE.getCode().equals(handleDTO.getIntentionCode());
	}

	@Override
	public void afterPropertiesSet() throws Exception {
		// 支持的业务初始化
		List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
		thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        thisBusinessTypes.add(ChatBusinessTypeEnum.CLOUD_PHONE);
        this.setBusinessTypes(thisBusinessTypes);
	}

	@Override
	public int order() {
		return thisExecuteSort.getSort();
	}

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		log.info("进入{}", thisExecuteSort.getDesc());

		Optional<Boolean> optional = Optional.of(handleDTO).map(ChatAddHandleDTO::getInputInfoDTO)
				.map(DialogueInputInfoDTO::getToolSetting).map(DialogueToolSettingDTO::getImageToolSetting)
				.map(DialogueImageToolSettingDTO::getEnableLlmDescribe);
		if (optional.isPresent() && Boolean.FALSE.equals(optional.get())) {
			log.info("文生图，不执行润色，直接执行执行异步工具handler... dialogueId:{}", handleDTO.getDialogueId());
			asyncImageToolHandlerImpl.run(handleDTO);
			return false;
		}

		log.info("文生图，执行润色开始... dialogueId:{}", handleDTO.getDialogueId());

		// 扣减权益
		benefitService.consumeBenefitImage(handleDTO.getReqDTO(), RequestContextHolder.getPhoneNumber(),
				handleDTO.getDialogueId(), handleDTO.getIntentionCode());

		ChatConfigEntity chatConfigEntity = chatConfigServiceDomainService.getUserCanUseModel(
				RequestContextHolder.getUserId(), RequestContextHolder.getPhoneNumber(),
				RequestContextHolder.getAssistantEnum(), RequestContextHolder.getBusinessType());
		Long taskId = uidGenerator.getUID();
		handleDTO.setTaskId(taskId);
		// 保存数据库
		dataSaveService.add(handleDTO, ChatStatusEnum.CHAT_IN, taskId, chatConfigEntity.getModelType());

		// 保存到hbase, 这里是流式的模块，默认index =0
		dataSaveService.saveTextResult(handleDTO, "", "");

		// 空则指定提示词
		handleDTO.getReqDTO().getDialogueInput().setPrompt(chatTextToolBusinessConfig.getTextGeneratePictureBusiness()
				.getPrompt().replace(REPLACE_KEY_OF_QUERY, handleDTO.getReqDTO().getDialogueInput().getDialogue()));

		// 大模型不联网搜索
		handleDTO.getReqDTO().getDialogueInput().setEnableForceNetworkSearch(false);

		SseEventListener event = new SseEventListener(handleDTO, null);
		NextAsynImageToolCallbackEvent nextAsynImageToolCallbackEvent = SpringUtil
				.getBean(NextAsynImageToolCallbackEvent.class);
		event.setCompleteCallbackEvent(nextAsynImageToolCallbackEvent);
		event.setHandleDTO(handleDTO);
		event.getSseEmitterOperate().setSseName(SseNameEnum.SIMPLE_SSE.getCode());
		event.setModelCode(chatConfigEntity.getModelType());

		// 进行流式输出，这里已经把hbase的 index为0进行修改了
		textModelTextSseHandlerImpl.simpleDialogue(event, handleDTO);

		return false;
	}

}
