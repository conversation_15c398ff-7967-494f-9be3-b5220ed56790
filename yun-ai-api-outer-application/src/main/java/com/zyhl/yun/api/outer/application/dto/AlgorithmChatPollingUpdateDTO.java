package com.zyhl.yun.api.outer.application.dto;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * 查询对话输出（轮巡查结果）-DTO
 * @Author: WeiJingKun
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class AlgorithmChatPollingUpdateDTO extends BaseDTO {

    /** 对话id */
    private Long dialogueId;

    /**
     * 参数校验
     * @Author: WeiJingKun
     */
    public void validate() {
        /** 检查登录的userId */
        checkTokenUserId();

        /** 对话id不能为空 */
        if (null == dialogueId) {
            log.error("对话id不能为空");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS.getResultCode(), "对话id不能为空");
        }
    }

}
