package com.zyhl.yun.api.outer.application.vo.knowledge;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/17 17:58
 */
@Data
public class PersonalKnowledgeNoteContent implements Serializable {
    /**
     * 内容（笔记类型 或 附件类型）
     */
    private String type;
    /**
     * 内容数据（笔记正文内容(含标签)、附件id）
     */
    private String data;
    /**
     * 笔记资源id
     */
    private String resourceId;
    /**
     * 图文混排时的排序字段
     */
    private Integer sortOrder;

    private String contentId;

    private String txtContent;

}
