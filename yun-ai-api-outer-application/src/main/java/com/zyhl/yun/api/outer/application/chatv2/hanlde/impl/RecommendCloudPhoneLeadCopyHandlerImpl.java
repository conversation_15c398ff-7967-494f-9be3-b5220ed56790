package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueAttachmentDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.config.LeadCopyV2Properties;
import com.zyhl.yun.api.outer.config.LeadCopyV2Properties.Copy;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyV2VO;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.VersionUtil;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

/**
 * 云手机引导语推荐
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RecommendCloudPhoneLeadCopyHandlerImpl extends AbstractChatAddV2Handler {

    /**
     * 当前执行顺序
     */
    private ExecuteSort thisExecuteSort = ExecuteSort.RECOMMEND_CLOUD_PHONE_LEAD_COPY;

    @Resource
    private LeadCopyV2Properties leadCopyV2Properties;
    @Resource
    private DataSaveService dataSaveService;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 支持的业务初始化
        List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.CLOUD_PHONE);
        this.setBusinessTypes(thisBusinessTypes);
    }

    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public boolean execute(ChatAddHandleDTO handleDTO) {
        return AssistantEnum.isCloudPhone(handleDTO.getAssistantEnum());
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        log.info("进入{}", thisExecuteSort.getDesc());

        // 聊天添加响应对象
        ChatAddRespVO respVO = handleDTO.getRespVO();

        // 获取主意图
        String intentionCode = handleDTO.getIntentionCode();
        DialogueIntentionEnum intentionEnum = DialogueIntentionEnum.getByCode(intentionCode);
        IntentionInfo mainIntention = DialogueIntentionVO.getMainIntention(handleDTO.getIntentionVO());
        if (null == intentionEnum && null != mainIntention) {
            // 意图枚举为空，重新获取
            intentionEnum = DialogueIntentionEnum.getByCode(mainIntention.getIntention());
        }
        if (null == intentionEnum) {
            // 空意图设置为文本意图，继续执行
            handleDTO.setTextGenerateTextIntention();
            return true;
        }
        Optional<List<File>> fileListOptional =
                Optional.of(handleDTO)
                        .map(ChatAddHandleDTO::getInputInfoDTO)
                        .map(DialogueInputInfoDTO::getAttachment)
                        .map(DialogueAttachmentDTO::getFileList);
        if (Boolean.TRUE.equals(fileListOptional.isPresent()) && !CollectionUtils.isEmpty(fileListOptional.get())) {
            handleDTO.setTextGenerateTextIntention();
            log.info("==> 有图片，强制设置为文本意图，继续执行.");
            return Boolean.TRUE;
        }

        // 检查意图版本号
        boolean continueFlag = checkIntentionVersion(handleDTO, intentionEnum);
        if (continueFlag) {
            return true;
        }

        // 云手机推荐意图才执行
        if (DialogueIntentionEnum.isCloudPhoneTypeIntentionForAll(intentionEnum.getCode())) {
            // 意图推荐逻辑
            Copy copy = leadCopyV2Properties.getByInstruction(intentionEnum.getInstruction());
            if (null == copy) {
                // 空设置为文本意图，继续执行
                handleDTO.setTextGenerateTextIntention();
                return true;
            }

            LeadCopyVO leadCopy = LeadCopyV2VO.getLeadCopyVo(copy, intentionEnum);

            if (StringUtils.isNotEmpty(leadCopy.getLinkURL())) {
                leadCopy.setLinkURL(leadCopy.getLinkURL().replace("{entityParams}",
                        DialogueIntentionVO.getUrlEntityParams(mainIntention)));
            }
            respVO.setLeadCopy(leadCopy);

            // 保存leadCopy到hbase【LeadCopy，type=1、2、3、4】
            dataSaveService.saveTextResult(handleDTO, "", "");

            // 保存tidb
            dataSaveService.addSuccess(handleDTO, OutContentTypeEnum.TEXT);

            // 流式响应
            handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(respVO));

            return false;
        }

        log.info("云手机推荐意图，不推荐，不修改意图，继续执行");
        return true;
    }

    /**
     * 检查意图版本号
     *
     * @param handleDTO     请求参数
     * @param intentionCode 意图编码
     * @param intentionEnum 意图枚举
     * @return
     */
    private boolean checkIntentionVersion(ChatAddHandleDTO handleDTO, DialogueIntentionEnum intentionEnum) {
        String intentionCode = intentionEnum.getCode();
        /** 意图控制 start */
        /**
         * 支持意图控制
         */
        if (DialogueIntentionEnum.isCLoudPhoneNotSupportIntention(RequestContextHolder.getBusinessType(),
                intentionCode)) {
            // 云手机【业务类型:{}】不支持的意图设置为文本意图，继续执行
            log.info("云手机【业务类型:{}】不支持的意图设置为文本意图，继续执行", RequestContextHolder.getBusinessType());
            handleDTO.setTextGenerateTextIntention();
            return true;
        }

        /**
         * 客户端版本号意图控制（主要是云手机推荐意图）
         */
        if (StringUtils.isNotEmpty(RequestContextHolder.getClientVersion())
                && DialogueIntentionEnum.isCloudPhoneTypeIntentionForAll(intentionEnum.getCode())) {
            if (VersionUtil.cloudPhoneClientVersionLt440()) {
                // 云手机不支持1.0版本之前意图设置为文本意图，继续执行
                log.info("云手机不支持1.0版本之前（<4.4.0）意图设置为文本意图，继续执行");
                handleDTO.setTextGenerateTextIntention();
                return true;
            }
            // 是否验证成功意图
            boolean validSuccess = false;
            if (VersionUtil.cloudPhoneClientVersionGte462()) {
                if (DialogueIntentionEnum.isCloudPhoneV120(intentionEnum)) {
                    validSuccess = true;
                } else {
                    // 云手机不支持1.2版本意图设置为文本意图，继续执行
                    log.info("云手机不支持1.2版本（>=4.6.3）意图设置为文本意图，继续执行");
                    handleDTO.setTextGenerateTextIntention();
                    return true;
                }
            }
            if (!validSuccess && VersionUtil.cloudPhoneClientVersionGte450()) {
                /**
                 * 1.1版本意图控制
                 */
                if (DialogueIntentionEnum.isCloudPhoneV110(intentionEnum)) {
                    validSuccess = true;
                } else {
                    // 云手机不支持1.0版本意图设置为文本意图，继续执行
                    log.info("云手机不支持1.1版本（>=4.5.0）意图设置为文本意图，继续执行");
                    handleDTO.setTextGenerateTextIntention();
                    return true;
                }
            }

            if (!validSuccess && VersionUtil.cloudPhoneClientVersionGte440()) {
                /**
                 * 1.0版本意图控制
                 */
                if (DialogueIntentionEnum.isCloudPhoneV100(intentionEnum)) {
                    validSuccess = true;
                } else {
                    // 云手机不支持1.0版本意图设置为文本意图，继续执行
                    log.info("云手机不支持1.0版本（>=4.4.0）意图设置为文本意图，继续执行");
                    handleDTO.setTextGenerateTextIntention();
                    return true;
                }
            }

            log.info("客户端版本号意图控制 clientVersion:{}, validSuccess:{} , intentionEnum:{}",
                    RequestContextHolder.getClientVersion(), validSuccess, intentionEnum);

        }
        /** 意图控制 end */
        return false;
    }
}
