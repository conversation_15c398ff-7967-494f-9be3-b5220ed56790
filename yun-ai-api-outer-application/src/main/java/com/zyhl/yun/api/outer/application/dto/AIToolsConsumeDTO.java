package com.zyhl.yun.api.outer.application.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.application.dto.AIToolsConsumeDTO} <br>
 * <b> description:</b>
 * 算法任务权益核销请求DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-08-16 17:19
 **/
@Data
public class AIToolsConsumeDTO implements Serializable {

    private static final long serialVersionUID = -7259928986543881907L;

    /**
     * 用户id，如果有token就不需要传
     */
    private String userId;

    /**
     * 渠道号
     */
    @NotNull(message = "渠道号不可为空")
    private String channelId;

}
