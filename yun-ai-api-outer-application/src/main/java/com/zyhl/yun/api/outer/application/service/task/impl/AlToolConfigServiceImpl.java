package com.zyhl.yun.api.outer.application.service.task.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.config.AIToolConfigProperties;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.YunDiskClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.FilePathCatalogReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.AICatalogVO;
import com.zyhl.hcy.yun.ai.common.platform.third.dto.GetUserInfoByPhoneNumberRespDTO;
import com.zyhl.yun.api.outer.application.dto.AIToolConfigDTO;
import com.zyhl.yun.api.outer.application.dto.tool.ToolConfigFaceSwapParamDTO;
import com.zyhl.yun.api.outer.application.service.task.AlToolConfigService;
import com.zyhl.yun.api.outer.config.tool.FaceSwapProperties;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.AIToolConfigVO;

import com.zyhl.yun.api.outer.vo.tool.ToolConfigFaceSwapExtVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Import;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.application.service.task.impl.AlToolConfigServiceImpl} <br>
 * <b> description:</b>
 * AI工具对应的参数配置Service
 *
 * <AUTHOR>
 * @date 2024-11-28 10:12
 **/
@Slf4j
@Service
@RequiredArgsConstructor
@Import(AIToolConfigProperties.class)
public class AlToolConfigServiceImpl implements AlToolConfigService {

    @Resource
    private AIToolConfigProperties aiToolConfigProperties;
    @Resource
    private FaceSwapProperties faceSwapProperties;

    @Resource
    private YunDiskClient yunDiskClient;

    @Override
    public AIToolConfigVO aiToolConfigGet(AIToolConfigDTO dto) {
        List<AIToolConfigProperties.AIToolConfig> list = aiToolConfigProperties.getList();
        if (CollectionUtils.isEmpty(list)) {
            log.warn("==> AI工具配置, 配置为空");
            return null;
        }
        Optional<AIToolConfigProperties.AIToolConfig> aiToolConfigOptional =
                list.stream().filter(item -> item.getCommand().equals(dto.getCommand())).findFirst();
        if (!aiToolConfigOptional.isPresent()) {
            log.warn("==> AI工具配置,  command:{} 配置不存在", dto.getCommand());
            return null;
        }
        AIToolConfigProperties.AIToolConfig aiToolConfig = aiToolConfigOptional.get();
        List<AIToolConfigProperties.AIToolConfigDetail> details = aiToolConfig.getDetails();
        if (CollectionUtils.isEmpty(details)) {
            log.warn("==> AI工具配置,  command:{} 配置详情为空", dto.getCommand());
            return null;
        }
        //AI写真必传channelCode
        if (StringUtils.equals(dto.getCommand(), DialogueIntentionEnum.AI_PHOTOGRAPHY.getCode())) {
            if (StringUtils.isEmpty(dto.getChannelCode())) {
                log.warn("==> AI工具配置, AI写真, channelId:{}, command:{}, channelCode不能为空",
                        dto.getChannelId(), dto.getCommand());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS.getResultCode(), "当channelId为AI写真相关渠道时,参数必填");
            } else {
                //获取channelCode是否匹配
                Optional<AIToolConfigProperties.AIToolConfigDetail> toolConfigDetailOptional = details.stream()
                        .filter(item -> item.getChannelId().equals(dto.getChannelId())
                                && item.getChannelCode().equals(dto.getChannelCode())).findFirst();
                if (!toolConfigDetailOptional.isPresent()) {
                    log.warn("==> AI工具配置, AI写真, channelId:{}, command:{}, channelCode:{}不匹配",
                            dto.getChannelId(), dto.getCommand(), dto.getChannelCode());
                    return null;
                }
                AIToolConfigProperties.AIToolConfigDetail toolConfigDetail = toolConfigDetailOptional.get();
                return getTargetUserIdCatalogId(toolConfigDetail);
            }
        } else {
            Optional<AIToolConfigProperties.AIToolConfigDetail> aiToolConfigDetail = details.stream()
                    .filter(item -> item.getChannelId().equals(dto.getChannelId())).findFirst();
            if (!aiToolConfigDetail.isPresent()) {
                log.warn("==> AI工具配置, channelId:{}, command:{} 配置不存在", dto.getChannelId(), dto.getCommand());
                return null;
            }
            AIToolConfigProperties.AIToolConfigDetail toolConfigDetail = aiToolConfigDetail.get();
            return getTargetUserIdCatalogId(toolConfigDetail);
        }
    }

    @Override
    public AIToolConfigVO getFaceSwapConfig(ToolConfigFaceSwapParamDTO param) {
        ToolConfigFaceSwapExtVO extVO = new ToolConfigFaceSwapExtVO();
        extVO.setStyle(param.getStyle());
        extVO.setPoseId(param.getPoseId());

        // 获取配置信息
        String templateNo = param.getStyle() + "-" + param.getPoseId();
        if (ObjectUtil.isNotEmpty(faceSwapProperties.getList())) {
            for (FaceSwapProperties.FaceSwapTemplate template : faceSwapProperties.getList()) {
                if (template.getTemplateNo().equals(templateNo)) {
                    extVO.setHeight(template.getHeight());
                    extVO.setWidth(template.getWidth());
                    extVO.setFaceList(template.getFaceList());
                    break;
                }
            }
        }

        // 返回结果
        return AIToolConfigVO.builder().ext(JsonUtil.toJson(extVO)).build();
    }

    /**
     * 获取运营用户的文件夹id
     *
     * @param aiToolConfig the ai tool config
     * @return {@link AIToolConfigVO}
     * <AUTHOR>
     * @date 2024-11-28 15:52
     */
    private AIToolConfigVO getTargetUserIdCatalogId(AIToolConfigProperties.AIToolConfigDetail aiToolConfig) {
        //直接判断是否过期，过期返回空
        if (Boolean.FALSE.equals(aiToolConfigProperties.expireValid(aiToolConfig))) {
            log.info("==> AI工具配置, 配置已过期, aiToolConfig:{}", JSONUtil.toJsonStr(aiToolConfig));
            return null;
        }
        //判断targetUserId是否有配置，未配置则默认为当前用户
        if (StringUtils.isEmpty(aiToolConfig.getTargetUserId())) {
            log.info("==> AI工具配置, targetUserId为空, 使用当前用户");
            aiToolConfig.setTargetUserId(RequestContextHolder.getUserId());
            //获取用户域接口
            GetUserInfoByPhoneNumberRespDTO userDomain = yunDiskClient.getUserInfo(aiToolConfig.getTargetUserId());
            if (Objects.isNull(userDomain)) {
                log.error("==> AI工具配置, 获取用户域接口失败, userId:{}", aiToolConfig.getTargetUserId());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_NACOS_CONFIG);
            }
            //设置到RequestContextHolder
            RequestContextHolder.setUserInfo(userDomain.getUserDomainId(), userDomain.getPhoneNumber(), userDomain.getBelongsPlatform());
        }
        FilePathCatalogReqDTO filePathCatalogReqDTO = FilePathCatalogReqDTO.builder()
                .userId(RequestContextHolder.getUserInfo().getUserId())
                .mobile(RequestContextHolder.getUserInfo().getPhoneNumber())
                .belongsPlatform(RequestContextHolder.getUserInfo().getBelongsPlatform())
                .path(aiToolConfig.getCatalogName())
                .build();
        AICatalogVO catalogByPath = yunDiskClient.getCatalogByPath(filePathCatalogReqDTO);

        return AIToolConfigVO.builder()
                .targetUserId(aiToolConfig.getTargetUserId())
                .catalogId(Optional.ofNullable(catalogByPath.getCatalogId()).orElse(StringUtils.EMPTY))
                .catalogName(catalogByPath.getCatalogName())
                .startTime(aiToolConfig.getStartTime())
                .endTime(aiToolConfig.getEndTime())
                .canPrint(aiToolConfig.getCanPrint())
                .ext(aiToolConfig.getExt())
                .build();
    }


}
