package com.zyhl.yun.api.outer.application.service.chat.impl;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import com.zyhl.hcy.yun.ai.common.base.annotation.MethodExecutionTimeLog;
import com.zyhl.hcy.yun.ai.common.base.enums.AIModuleEnum;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.FileBase64Util;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.FileUploadVO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmTransferDTO;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatTransferService;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.entity.TaskAiAbilityEntity;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ImageSuffixEnum;
import com.zyhl.yun.api.outer.enums.ImageTransmissionTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.task.FileExpiredStatusEnum;
import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;
import com.zyhl.yun.api.outer.external.service.EOSExternalService;
import com.zyhl.yun.api.outer.external.YunDiskExternalService;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.repository.TaskAiAbilityRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.TaskRespParamVO;

import cn.hutool.core.lang.UUID;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AlgorithmChatTransferServiceImpl implements AlgorithmChatTransferService {

    @Resource
    private AlgorithmChatContentRepository algorithmChatContentRepository;

    @Resource
    private TaskAiAbilityRepository taskAiAbilityRepository;

    @Resource
    private EOSExternalService eosExternalService;

    @Resource
    private YunDiskExternalService yunDiskExternalService;

    /**
     * 临时文件路径
     */
    private static final String TEMP_FILE_PATH = "/tmp";

    /**
     * 默认转存路径
     */
    private static final String DEFAULT_PATH = "/" + AIModuleEnum.MY_FAVER_APP.getCatalogName() + "/"
        + AIModuleEnum.AI_ASSISTANT.getCatalogName();

    @Override
    @MethodExecutionTimeLog("对话结果转储-serviceImpl")
    public FileUploadVO transfer(AlgorithmTransferDTO dto) {
        AlgorithmChatContentEntity entity = algorithmChatContentRepository.getById(Long.parseLong(dto.getDialogueId()));
        if (entity == null) {
            log.info("对话不存在,dialogueId：{}", dto.getDialogueId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        if (!dto.getUserId().equals(entity.getUserId())) {
            log.info("对话id与用户id不匹配,dialogueId：{}", dto.getDialogueId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_FORBIDDEN);
        }
        if (null == entity.getTaskId()) {
            log.info("任务ID不存在,dialogueId：{}", dto.getDialogueId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_APPLICATION_ID_INVALID);
        }
        TaskAiAbilityEntity taskEntity = taskAiAbilityRepository.getTaskEntity(entity.getTaskId());
        if (null == taskEntity) {
            log.info("任务不存在,dialogueId：{}", dto.getDialogueId());
            throw new YunAiBusinessException(ResultCodeEnum.TASK_RECORD_NOT_FOUND);
        }

        //判断权益是否付费
        if (!taskEntity.isFee()) {
            log.info("用户权益未扣费,taskId：{}", taskEntity.getId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_NOT_DEDUCTED_BENEFIT);
        }

        if (!TaskStatusEnum.PROCESS_FINISH.getCode().equals(taskEntity.getTaskStatus())) {
            log.info("任务不是已处理完成状态,taskId:{}", taskEntity.getId());
            throw new YunAiBusinessException(AiResultCode.CODE_10000201.getCode(), AiResultCode.CODE_10000201.getMsg());
        }

        // 共享存储路径为空 获取任务表结果
        // 处理任务类型为非图片类型
        if (DialogueIntentionEnum.NON_IMAGE_MODULE.contains(taskEntity.getAlgorithmCode())) {
            log.info("非图片类型任务,taskId:{}", taskEntity.getId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_RESOURCE_CODE);
        }
        // 文件已过期
        if (FileExpiredStatusEnum.EXPIRED.getCode().equals(taskEntity.getFileExpiredStatus())) {
            log.info("文件已过期,taskId:{}", taskEntity.getId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_IMAGE_EXPIRED);
        }

        // 获取转换后的JSONObject对象
        List<TaskRespParamVO> paramVOList = JSONUtil.toList(taskEntity.getRespParam(), TaskRespParamVO.class);
        if (CollectionUtils.isEmpty(paramVOList) || ObjectUtils.isEmpty(paramVOList.get(0))) {
            log.info("任务结果不存在,taskId:{}", taskEntity.getId());
            throw new YunAiBusinessException(ResultCodeEnum.SESSION_INFO_NOT_FOUND);
        }
        FileUploadVO vo = null;
        // 上传个人云盘
        for (TaskRespParamVO respParamVO : paramVOList) {
            if (null == respParamVO.getImageTransmissionType()) {
                log.info("任务结果不存在ImageTransmissionType,taskId:{}", taskEntity.getId());
                throw new YunAiBusinessException(ResultCodeEnum.SESSION_INFO_NOT_FOUND);
            }
            if (ImageTransmissionTypeEnum.EOS.getCode() != respParamVO.getImageTransmissionType()) {
                log.info("对话结果已在云盘中,dialogueId：{}", dto.getDialogueId());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_YUN_DISK_EXISTING);
            }
            String fileSuffix = ImageSuffixEnum.getByAlgorithmCode(taskEntity.getAlgorithmCode()).getCode();
            vo = uploadFileByPath(dto.getPath(), respParamVO.getOutResourceId(), fileSuffix, entity.getUserId());
            log.info("uploadFileByPath dialogueId:{} get fileId:{}", dto.getDialogueId(), vo);
            // 更新对话表字段
            entity.setOutResourceId(vo.getFileId());
            entity.setUpdateTime(new Date());
            boolean updated = algorithmChatContentRepository.updateOutResourceId(entity);
            if (!updated) {
                log.error("updateOutResourceId error dialogueId:{}", dto.getDialogueId());
                throw new YunAiBusinessException(ResultCodeEnum.TASK_FAIL);
            }
            log.info("updateOutResourceId update success dialogueId:{}", dto.getDialogueId());
        }
        return vo;
    }

    /**
     * 上传个人云盘
     *
     * @param path          文件夹路径 例如：/我的应用收藏/AI助手
     * @param outResourceId 共享存储key
     * @param fileSuffix    文件后缀
     * @param userId        用户ID
     */
    private FileUploadVO uploadFileByPath(String path, String outResourceId, String fileSuffix, String userId) {
        String base64 = downloadByEos(outResourceId);
        try {
            if (StringUtils.isBlank(path)) {
                path = DEFAULT_PATH;
            }
            FileUploadVO fileUploadVo = yunDiskExternalService.uploadFilePath(path, AIModuleEnum.AI_ASSISTANT, userId, RequestContextHolder.getBelongsPlatform(), fileSuffix, base64);
            if (null == fileUploadVo || StringUtils.isBlank(fileUploadVo.getFileId())) {
                log.info("图片转储失败,outResourceId:{}", outResourceId);
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_TRANSFER_FAILED);
            }
            log.info("转储到云盘成功,文件：{}", fileUploadVo);
            return fileUploadVo;
        } catch (Exception e) {
            log.error("transfer uploadFileByPath error outResourceId:{},error msg:", outResourceId, e);
            if (e instanceof YunAiBusinessException) {
                AiResultCode resultCode = AiResultCode.getByCodeOrMsg(((YunAiBusinessException) e).getCode(), e.getMessage());
                throw new YunAiBusinessException(resultCode.getCode(), resultCode.getMsg());
            }
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_TRANSFER_FAILED);
        }
    }

    private String downloadByEos(String outResourceId) {
        String base64 = "";
        File file = null;
        try {
            Path path = Paths.get(TEMP_FILE_PATH, UUID.randomUUID().toString(), outResourceId);
            String downloadPath = path.toString();
            Boolean uploadSuccess = eosExternalService.downloadEos(path.toString(), outResourceId);
            file = new File(downloadPath);
            if (!(uploadSuccess && null != file && file.exists())) {
                log.info("共享存储文件获取失败,outResourceId:{}", outResourceId);
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_LOCAL_PATH);
            }
            log.info("从eos下载成功,文件地址：{}", file.getPath());
            base64 =  FileBase64Util.fileToBase64(file);
        } catch (Exception e) {
            log.error("从eos下载失败,outResourceId:{}, error msg:", outResourceId, e);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_LOCAL_PATH);
        }finally {
        	try {
        		if(null != file && file.exists()) {
        			file.delete();
        			File parent = file.getParentFile();
        			if(null != parent) {
        				parent.delete();
        			}
        		}
        	}catch (Exception e) {
                log.error("从eos下载后删除文件失败,outResourceId:{}, error msg:", outResourceId, e);
			}
        }
        return base64;
    }

}