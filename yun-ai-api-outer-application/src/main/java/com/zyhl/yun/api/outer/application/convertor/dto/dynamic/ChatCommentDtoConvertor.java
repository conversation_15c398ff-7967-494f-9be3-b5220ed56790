package com.zyhl.yun.api.outer.application.convertor.dto.dynamic;

import com.zyhl.yun.api.outer.application.dto.ChatCommentDTO;
import com.zyhl.yun.api.outer.domain.entity.ChatCommentEntity;
import org.mapstruct.Mapper;

/**
 * 对话内容评价DTO转Entity类
 *
 * <AUTHOR>
 * @version 2024年02月28日 16:10
 */

@Mapper(componentModel = "spring")
public interface ChatCommentDtoConvertor {


    /**
     * 对话内容评价DTO转Entity类
     * @param chatCommentDTO 对话内容评价DTO
     * @return ChatCommentEntity
     */
    ChatCommentEntity toEntity(ChatCommentDTO chatCommentDTO);

}
