package com.zyhl.yun.api.outer.application.service.chat;

import com.zyhl.yun.api.outer.application.dto.ChatCommentDTO;
import com.zyhl.yun.api.outer.domain.vo.ChatCommentGetResult;

import java.util.List;

/**
 * 对话结果评价Service类
 *
 * <AUTHOR>
 * @version 2024年02月28日 15:30
 */

public interface ChatCommentService {

    /**
     * 添加评价
     * @param dto 评价dto
     * @return 是否成功
     */

    boolean add(ChatCommentDTO dto);

    /**
     * 获取评价列表
     * @param dto 获取评价dto
     * @return  评价列表
     */
    List<ChatCommentGetResult> get(ChatCommentDTO dto);
}
