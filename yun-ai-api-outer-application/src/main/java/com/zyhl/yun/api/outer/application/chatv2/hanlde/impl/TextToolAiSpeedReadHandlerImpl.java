package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueIntentionDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatDialogueRecommendService;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;

import lombok.extern.slf4j.Slf4j;

/**
 * AI图书快速阅读
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TextToolAiSpeedReadHandlerImpl extends AbstractChatAddV2Handler {

	/**
	 * 当前执行顺序
	 */
	private ExecuteSort thisExecuteSort = ExecuteSort.TEXT_TOOL_AI_SPEED_READ;

	@Resource
	private TextModelDocSseHandlerImpl textModelDocSseHandlerImpl;

	@Resource
	private ChatTextToolBusinessConfig chatTextToolBusinessConfig;

	@Resource
	private ChatDialogueRecommendService recommendService;

	@Override
	public void afterPropertiesSet() throws Exception {
		// 支持的业务初始化
		List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        this.setBusinessTypes(thisBusinessTypes);
	}

	@Override
	public int order() {
		return thisExecuteSort.getSort();
	}

	@Override
	public boolean execute(ChatAddHandleDTO handleDTO) {

		// 文档资源为空不执行（笔记，邮件，附件，文档）
		if (!handleDTO.isReqResourceDocSse()) {
			return false;
		}

		DialogueIntentionDTO command = handleDTO.getReqDTO().getDialogueInput().getCommand();

		// 入参图书快速阅读，进入
		if (null != command && DialogueIntentionEnum.isTextToolIntention(command.getCommand())
				&& DialogueIntentionSubEnum.isAiSpeedRead(command.getSubCommand())) {
			return true;
		}

		// 对话内容为空 提示词为空 意图指令为空
		return StringUtils.isEmpty(handleDTO.getReqDTO().getDialogueInput().getDialogue())
				&& StringUtils.isEmpty(handleDTO.getInputInfoDTO().getPrompt())
				&& (null == command || StringUtils.isEmpty(command.getCommand()));
	}

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		log.info("进入{}", thisExecuteSort.getDesc());

		// 纯文档，邮件笔记附件不处理为快速阅读意图
		if (handleDTO.isReqResourceOnlyDocumentSse()) {
			// 设置图书快速阅读意图
			handleDTO.setIntentionVO(DialogueIntentionVO.newMainIntention(DialogueIntentionEnum.TEXT_TOOL.getCode(),
					DialogueIntentionSubEnum.SPEED_READ.getCode()));
		} else {
			handleDTO.setTextGenerateTextIntention();
			handleDTO.getRespVO().getMiddleRecommend().setToolList(null);
		}

		// 追加提示词
		handleDTO.getReqDTO().getDialogueInput().setPrompt(chatTextToolBusinessConfig.getAiSpeedRead().getPrompt());

		// 文档大模型流式对话
		textModelDocSseHandlerImpl.run(handleDTO);

		return false;
	}
}
