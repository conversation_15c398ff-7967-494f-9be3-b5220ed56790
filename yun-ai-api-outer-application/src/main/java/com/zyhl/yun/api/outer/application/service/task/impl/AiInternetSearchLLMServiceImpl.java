package com.zyhl.yun.api.outer.application.service.task.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.event.TextModelStreamEventListener;
import com.zyhl.hcy.yun.ai.common.model.api.client.vo.TextModelBaseVo;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.utils.TextModelUtil;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.dto.CheckTextReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.vo.CheckResultVO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddContentDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.dto.TextModelDTO;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatAddCheckService;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatHistoryService;
import com.zyhl.yun.api.outer.application.service.chat.ChatApplicationTypeService;
import com.zyhl.yun.api.outer.application.service.external.MemberCenterService;
import com.zyhl.yun.api.outer.application.service.task.AiInternetSearchLLMService;
import com.zyhl.yun.api.outer.application.util.SseEmitterDataUtils;
import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;
import com.zyhl.yun.api.outer.application.vo.FlowTypeResultV2VO;
import com.zyhl.yun.api.outer.application.vo.FlowTypeResultVO;
import com.zyhl.yun.api.outer.config.*;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.domainservice.CheckSystemDomainService;
import com.zyhl.yun.api.outer.domainservice.QpsLimitService;
import com.zyhl.yun.api.outer.enums.*;
import com.zyhl.yun.api.outer.enums.chat.ApiVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddResultTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.OutAuditStatusEnum;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.sse.EventSource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * interfaceName: AiInternetSearchHandleService
 * description: AI全网搜调大模型处理接口实现类
 *
 * <AUTHOR>
 * @date 2025/3/14
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AiInternetSearchLLMServiceImpl implements AiInternetSearchLLMService {

    private final CheckSystemDomainService checkSystemDomainService;

    private final MemberCenterService memberService;

    private final AlgorithmChatHistoryService algorithmChatHistoryService;

    private final QpsLimitService qpslimitService;

    private final TextModelExternalService textModelExternalService;

    private final RedisOperateRepository redisOperateRepository;

    private final ChatConfigServiceDomainService chatConfigServiceDomainService;

    private final FlowTypeProperties flowTypeProperties;

    private final ModelProperties modelProperties;

    private final AiTextResultRepository aiTextResultRepository;

    private final AlgorithmChatContentRepository algorithmChatContentRepository;

    private final AlgorithmChatAddCheckService chatAddCheckService;

    private final CheckSystemConfig checkSystemConfig;

    @Override
    public AlgorithmChatAddVO aiInternetSearchByLLM(ChatAddInnerDTO innerDTO, String resourceContent, Object llmSearchEntity) {

        AlgorithmChatAddDTO dto = innerDTO.getReqParams();
        AlgorithmChatAddVO resVo = innerDTO.getRespParams();
        AlgorithmChatAddContentDTO content = dto.getContent();
        AssistantEnum assistantEnum = content.getAssistantEnum();

        // 初始化会话输入返回结果VO
        resVo.setCommands(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());
        resVo.setResultType(ChatAddResultTypeEnum.FLOW_TYPE.getType());
        //查询AI提示词模板表
        String promptTemplate = chatAddCheckService.getDialoguePrompt(content.getPrompt(), innerDTO.getContent().getSourceChannel());
        // 辅助处理的对象
        StreamEventListenerParams params = new StreamEventListenerParams(innerDTO, resourceContent, promptTemplate);

        try {
            // 流式结果监听事件，监听并处理流式返回结果
            TextModelStreamEventListener event = handleEventListener(params);

            // 获取用户设置的模型，没有设置则使用默认模型
            ChatConfigEntity chatConfigEntity = chatConfigServiceDomainService.getUserCanUseModel(
                    innerDTO.getReqParams().getUserId(), RequestContextHolder.getPhoneNumber(),
                    innerDTO.getContent().getAssistantEnum(), innerDTO.getContent().getBusinessType());
            if (null != chatConfigEntity) {
                simpleDialogue(params, event, chatConfigEntity.getModelType());
                resVo.setModelType(params.getModelCode());
                return resVo;
            }
        } catch (YunAiBusinessException e) {
            // 回滚权益
            memberService.consumeBenefitFail(dto.getUserId(), RequestContextHolder.getPhoneNumber(), Long.parseLong(resVo.getDialogueId()));
            params.handleFailMsg(ResultCodeEnum.ERROR_AI_MODEL);
            e = SseEmitterDataUtils.caseAiModelException(e);
            throw e;
        } catch (Exception e) {
            // 回滚权益
            memberService.consumeBenefitFail(dto.getUserId(), RequestContextHolder.getPhoneNumber(), Long.parseLong(resVo.getDialogueId()));

            // 发送消息
            log.error("【AI全网搜对话】TextGenerateTextServiceImpl flowTypeHandle dialogueId:{}, error:", resVo.getDialogueId(), e);
            params.handleFailMsg(ResultCodeEnum.ERROR_AI_MODEL);
        }
        resVo.setModelType(params.getModelCode());
        return resVo;
    }

    /**
     * 普通对话，指定模型
     *
     * @param params    流式监听辅助处理参数
     * @param event     流式监听事件
     * @param modelCode 模型编码
     */
    private void simpleDialogue(StreamEventListenerParams params, TextModelStreamEventListener event, String modelCode) {
        AssistantEnum assistantEnum = params.getInnerDTO().getContent().getAssistantEnum();
        params.modelCode = modelCode;

        // qps限制
        if (!qpslimitService.modelQpsLimit(params.modelCode)) {
            log.info("【AI全网搜对话】simpleDialogue 请求过多，qps限流，model:{}", params.modelCode);
            params.handleFailMsg(ResultCodeEnum.ERROR_LIMITATION);
            return;
        }

        // 大模型对话文本
        String content = params.textDto.getContent();
        // 输入字数判断
        Integer lengthLimit = modelProperties.getLengthLimit(assistantEnum, params.getInnerDTO().getContent().getBusinessType(), params.modelCode);

        //符合的资源类型，超过后，截取lengthLimit个字符串
        if (null != lengthLimit
                && ResourceTypeEnum.isSubStringType(params.getInnerDTO().getContent().getResourceType())) {
            if (StringUtils.isNotEmpty(content) && content.length() > lengthLimit) {
                log.info("【AI全网搜对话】当前符合需要截取的资源类型，执行截取， dialogueId:{}, content size:{}, lengthLimit size:{}",
                        params.getInnerDTO().getDialogueId(), content.length(), lengthLimit.intValue());
                params.textDto.setContent(content.substring(0, lengthLimit.intValue()));
            }
        }

        if (lengthLimit != null && params.textDto.getContent().length() > lengthLimit) {
            log.info("【AI全网搜对话】simpleDialogue 文本模型输入字数超限，模型：{}，输入限制为：{}，输入字数为：{}", params.modelCode, lengthLimit, params.textDto.getContent().length());
            params.handleFailMsg(ResultCodeEnum.CONTENT_EXCEEDS_LIMIT);
            return;
        }

        // 文本模型
        TextModelTextReqDTO reqDTO = params.textDto.toTextReqDTO(modelProperties.getMaxLength(
                assistantEnum, params.getInnerDTO().getContent().getBusinessType(), params.modelCode));
        log.info("【AI全网搜对话】simpleDialogue 指定文本模型对话 model:{}, reqDTO:{}", params.modelCode, JSONUtil.toJsonStr(reqDTO));
        textModelExternalService.streamDialogue(params.modelCode, reqDTO, event);

        // 更新模型编码
        algorithmChatContentRepository.updateModelCode(params.dialogId, params.modelCode);
    }

    /**
     * 监听调用厂商事件处理
     *
     * @param params 辅助处理参数
     * @return 事件监听Event
     */
    private TextModelStreamEventListener handleEventListener(StreamEventListenerParams params) {

        final Map<String, String> logMap = MDC.getCopyOfContextMap();
        /**
         * 低版本，即需要合并思维链版本
         */
        final boolean mergeThinkContentVersion = ApiVersionEnum
                .isMergeThinkContentVersion(null != params.innerDTO ? RequestContextHolder.getApiVersion() : null);

        // 监听处理事件
        return new TextModelStreamEventListener() {

            @Override
            public boolean onEvent(TextModelBaseVo response) {
                MDC.setContextMap(logMap);
                CheckSystemConfig.CheckSystemTextModel checkSystemTextModel = null;
                try {
                    checkSystemTextModel = checkSystemConfig.getCheckSystemTextModel(params.modelCode);
                    // 停止对话
                    if (params.finishFlag) {
                        log.info("【AI全网搜对话】对话已结束，结束大模型对话，对话id：{}", params.dialogId);
                        return false;
                    }
                    if (redisOperateRepository.getStopDialogue(params.dialogId)) {
                        // 对话结束
                        log.info("【AI全网搜对话】对话已停止，对话id：{}", params.dialogId);
                        params.finishFlag = true;
                        params.handleStop();
                        return false;
                    }
                    List<TextModelBaseVo.NetworkSearchInfoVo> networkSearchInfoList = null;
                    // 最后一次事件（不同模型判断不一样）
                    boolean lastEvent = Boolean.TRUE.equals(response.getIsLast()) || params.outputTokens.equals(response.getOutputTokens());
                    params.outputTokens = response.getOutputTokens() == null ? 0 : response.getOutputTokens();

                    String thinkText = CharSequenceUtil.nullToDefault(response.getReasoningContent(), StringUtils.EMPTY);
                    String text = CharSequenceUtil.nullToDefault(response.getText(), StringUtils.EMPTY);
                    params.allThinkTextMsg.append(SseEmitterDataUtils.filterIgnoreString(thinkText));
                    params.allTextMsg.append(SseEmitterDataUtils.filterIgnoreString(text));

                    // 根据版本号判断是否合并思维链内容
                    if (mergeThinkContentVersion) {
                        if (StringUtils.isNotEmpty(thinkText)) {
                            if (!Boolean.TRUE.equals(this.getStartThinkContent())) {
                                this.setStartThinkContent(true);
                                // 开始思维链
                                text = TextModelUtil.TAG_START_THINK + thinkText;
                            } else {
                                text = thinkText;
                            }
                        } else {
                            if (StringUtils.isNotEmpty(text) && Boolean.TRUE.equals(this.getStartThinkContent())
                                    && !Boolean.TRUE.equals(this.getStartContent())) {
                                this.setStartContent(true);
                                // 结束思维链
                                text = TextModelUtil.TAG_END_THINK + text;
                            }
                        }
                    }

                    //未达到送审字数

                    //根据版本号判断是否合并思维链内容，非合并思维链和展示联网搜索来源信息，才直接追加
                    if (!mergeThinkContentVersion) {
                        params.addThinkMsg.append(thinkText);
                        networkSearchInfoList = response.getNetworkSearchInfoList();
                    }
                    if (StringUtils.isNotEmpty(text)) {
                        params.addMsg.append(text);
                    }

                    // 过滤送审大模型返回忽略文本方法
                    String newMsg = SseEmitterDataUtils
                            .filterIgnoreAudit(params.addThinkMsg.toString() + params.addMsg.toString());
                    if (!lastEvent && newMsg.length() < checkSystemTextModel.getOutputSize()) {
                        //【流式对话文生文】未达输出字数阈值
                        return true;
                    }

                    if (CharSequenceUtil.isNotBlank(newMsg)) {
                        params.allMsg.append(newMsg);
                        if (CheckSystemConfig.isAuditPart(checkSystemTextModel.getType()) || CheckSystemConfig.isAuditLocalPart(checkSystemTextModel.getType())) {
                            // 执行文本内容送审（部分送审）
                            if (CheckResultVO.isFail(checkSystemCheckTextDeal(params, checkSystemTextModel))) {
                                log.error("【AI全网搜对话】（增量部分送审）生成文本送审失败，送审文本：{}，对话id：{}", params.allMsg, params.dialogId);
                                return params.handleFailMsg(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
                            }
                        }
                        if (mergeThinkContentVersion) {
                            // 发送SseEmitter消息
                            params.resVO.setFlowResult(new FlowTypeResultVO(params.addMsg.toString(),
                                    OutAuditStatusEnum.SUCCESS.getCode()));
                        } else {
                            // 发送SseEmitter消息
                            params.resVO.setFlowResult(new FlowTypeResultV2VO(this.getIndexAndIncrement(), params.addThinkMsg.toString(),
                                    params.addMsg.toString(), OutAuditStatusEnum.SUCCESS.getCode()));
                        }
                        //设置联网搜索信息来源
                        setNetworkSearchInfoList(params.resVO, networkSearchInfoList);
                        if (CollUtil.isNotEmpty(params.resVO.getNetworkSearchInfoList())) {
                            //大模型联网搜索信息来源存在则设置，需要入库到hbase
                            params.networkSearchInfoList = params.resVO.getNetworkSearchInfoList();
                        }
                        if (mergeThinkContentVersion) {
                            //低版本，不返回联网搜索来源字段
                            params.resVO.setNetworkSearchInfoList(null);
                        }
                        SseEmitterDataUtils.sendMsg(params.sseEmitter, BaseResult.success(params.resVO), params.completeFlag);

                        // 发送消息后参数处理
                        params.sendFlag = true;
                        params.addThinkMsg.setLength(0);
                        params.addMsg.setLength(0);
                    }

                    // 最后一次 更新会话任务状态
                    if (lastEvent) {

                        if (CheckSystemConfig.isAuditAll(checkSystemTextModel.getType())) {
                            // 执行文本内容送审（全量送审）
                            log.info("【AI全网搜对话】（全量送审一次）正常结束，对话id：{}", params.dialogId);
                            params.fullAuditFlag = true;

                            if (CheckResultVO.isFail(checkSystemCheckTextDeal(true, params, checkSystemTextModel))) {
                                log.error("【AI全网搜对话】（全量送审一次）正常结束，生成文本送审失败，送审文本：{}，对话id：{}", params.allMsg, params.dialogId);
                                return params.handleFailMsg(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
                            }
                        }

                        // 对话结束
                        params.finishFlag = true;
                        return params.handleSuccessMsg(mergeThinkContentVersion, this.getIndexAndIncrement());
                    }
                } catch (YunAiBusinessException e) {
                    params.finishFlag = true;
                    log.error("【AI全网搜对话】flowTypeHandle onEvent YunAiBusinessException response: {} | e:", JsonUtil.toJson(response), e);
                    return params.handleFailMsg(AiResultCode.getByCodeOrMsg(e.getCode(), e.getMessage()));
                } catch (Exception e) {
                    log.error("【AI全网搜对话】flowTypeHandle onEvent Exception response: {} | e:", JsonUtil.toJson(response), e);

                    params.finishFlag = true;
                    if (params.sendFlag) {
                        if (CheckSystemConfig.isAuditAll(checkSystemTextModel.getType())) {
                            // 执行文本内容送审（全量送审）
                            log.info("【AI全网搜对话】（全量送审一次）终止对话生成文本送审，对话id：{}", params.dialogId);
                            params.fullAuditFlag = true;

                            if (CheckResultVO.isFail(checkSystemCheckTextDeal(true, params, checkSystemTextModel))) {
                                log.error("【AI全网搜对话】（全量送审一次）终止对话生成文本送审失败，送审文本：{}，对话id：{}", params.allMsg, params.dialogId);
                                return params.handleFailMsg(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
                            }
                        }
                        // 已经发送过消息，然后抛出了非业务异常，对话终止情况 （一般情况下是前端杀进程会出现）
                        return params.handleChatEndMsg();
                    }

                    return params.handleFailMsg(AiResultCode.CODE_10000101);
                }

                return true;
            }

            private void setNetworkSearchInfoList(AlgorithmChatAddVO resVO,
                                                  List<TextModelBaseVo.NetworkSearchInfoVo> networkSearchInfoList) {
                if (CollUtil.isEmpty(networkSearchInfoList)) {
                    resVO.setNetworkSearchInfoList(Collections.emptyList());
                    return;
                }
                List<AiTextResultRespParameters.NetworkSearchInfo> networkSearchInfos = null;
                networkSearchInfos = new ArrayList<>();
                for (TextModelBaseVo.NetworkSearchInfoVo searchInfo : networkSearchInfoList) {
                    networkSearchInfos.add(AiTextResultRespParameters.NetworkSearchInfo.builder().index(searchInfo.getIndex())
                            .icon(searchInfo.getIcon()).siteName(searchInfo.getSiteName())
                            .title(searchInfo.getTitle()).url(searchInfo.getUrl()).build());
                }
                resVO.setNetworkSearchInfoList(networkSearchInfos);
            }

            /**
             * 送审处理
             */
            private CheckResultVO checkSystemCheckTextDeal(StreamEventListenerParams params,
                                                           CheckSystemConfig.CheckSystemTextModel checkSystemTextModel) {
                return checkSystemCheckTextDeal(false, params, checkSystemTextModel);
            }

            /**
             * 送审处理
             */
            private CheckResultVO checkSystemCheckTextDeal(Boolean isMustApi, StreamEventListenerParams params,
                                                           CheckSystemConfig.CheckSystemTextModel checkSystemTextModel) {
                CheckTypeEnum checkType = null;
                if (Boolean.TRUE.equals(isMustApi)) {
                    checkType = CheckTypeEnum.API;
                } else {
                    checkType = (CheckSystemConfig.isAuditLocalPart(checkSystemTextModel.getType()))
                            ? CheckTypeEnum.LOCAL
                            : CheckTypeEnum.API;
                }
                return checkSystemDomainService.checkSystemCheckText(checkSystemTextModel, checkType,
                        new CheckTextReqDTO(String.valueOf(params.dialogId), params.userId,
                                SseEmitterDataUtils.filterIgnoreAudit(String.valueOf(params.allMsg))));
            }

            @Override
            public void onClosed(EventSource eventSource) {
                MDC.setContextMap(logMap);
                log.info("【AI全网搜对话】onClosed，flowTypeHandle sessionId:{} |  dialogueId:{} | userId:{} | onClosed msgAll:{}",
                        params.resVO.getSessionId(), params.dialogId, params.userId, params.allMsg);
                if (StringUtils.isBlank(params.allMsg)) {
                    //文本大模型无回复内容，设置默认文案
                    if (mergeThinkContentVersion) {
                        // 发送SseEmitter消息
                        params.resVO.setFlowResult(new FlowTypeResultVO(flowTypeProperties.getDefaultAnswer(),
                                OutAuditStatusEnum.SUCCESS.getCode()));
                    } else {
                        // 发送SseEmitter消息
                        params.resVO.setFlowResult(
                                new FlowTypeResultV2VO(this.getIndexAndIncrement(), flowTypeProperties.getDefaultAnswer(),
                                        StringUtils.EMPTY, OutAuditStatusEnum.SUCCESS.getCode()));
                    }
                    try {
                        SseEmitterDataUtils.sendMsg(params.sseEmitter, BaseResult.success(params.resVO),
                                params.completeFlag);
                    } catch (Exception e) {
                        log.error("【AI全网搜对话】flowTypeHandle onClosed Exception | e:", e);
                    }
                } else {
                    // 没有最后一次处理标识，表示只发过一次消息就onClosed了，需要更新会话内容和hbase信息（百炼个性化定义存在这种情况）
                    if (!params.finishFlag) {
                        CheckSystemConfig.CheckSystemTextModel checkSystemTextModel = checkSystemConfig.getCheckSystemTextModel(params.modelCode);
                        if (CheckSystemConfig.isAuditAll(checkSystemTextModel.getType())) {
                            // 执行文本内容送审（全量送审）
                            log.info("【AI全网搜对话】（全量送审一次）非正常结束，对话id：{}", params.dialogId);
                            params.fullAuditFlag = true;

                            if (CheckResultVO.isFail(checkSystemCheckTextDeal(true, params, checkSystemTextModel))) {
                                log.error("【AI全网搜对话】（全量送审一次）非正常结束，生成文本送审失败，送审文本：{}，对话id：{}", params.allMsg, params.dialogId);
                                params.handleFailMsg(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
                                return;
                            }
                        }
                        params.handleSuccessMsg(mergeThinkContentVersion, this.getIndexAndIncrement());
                        return;
                    }
                }
                super.onClosed(eventSource);
                SseEmitterDataUtils.complete(params.sseEmitter, params.completeFlag);
            }

            @Override
            public void onFailure(Throwable t, int code, String body) {
                MDC.setContextMap(logMap);

                if (!params.finishFlag && !params.fullAuditFlag) {
                    CheckSystemConfig.CheckSystemTextModel checkSystemTextModel = checkSystemConfig.getCheckSystemTextModel(params.modelCode);
                    if (CheckSystemConfig.isAuditAll(checkSystemTextModel.getType())) {
                        // 执行文本内容送审（全量送审）
                        log.info("【AI全网搜对话】（全量送审一次）其他异常中断生成文本送审，对话id：{}", params.dialogId);
                        params.fullAuditFlag = true;

                        if (CheckResultVO.isFail(checkSystemCheckTextDeal(true, params, checkSystemTextModel))) {
                            log.error("【AI全网搜对话】（全量送审一次）其他异常中断生成文本送审失败，送审文本：{}，对话id：{}", params.allMsg, params.dialogId);
                            params.handleFailMsg(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
                            return;
                        }
                    }
                }

                if (params.finishFlag) {
                    log.warn("【AI全网搜对话】onFailure，对话已结束，对话id：{}", params.resVO.getDialogueId());
                    return;
                }

                log.warn("【AI全网搜对话】flowTypeHandle onFailure code:{} | body:{} | msgAll:{}", code, body, params.allMsg);
                if (t instanceof YunAiBusinessException) {
                    YunAiBusinessException te = (YunAiBusinessException) t;
                    params.handleFailMsg(AiResultCode.getByCodeOrMsg(te.getCode(), te.getMessage()));
                    return;
                }

                params.handleFailMsg(ResultCodeEnum.ERROR_AI_MODEL);
            }
        };
    }

    /**
     * 流式监听辅助处理参数
     */
    @Data
    private class StreamEventListenerParams {
        /**
         * 全量内容（送审使用全量，保存数据库使用全量）
         */
        StringBuffer allMsg = new StringBuffer();
        /**
         * 增量内容（输出使用增量）
         */
        StringBuffer addMsg = new StringBuffer();
        /**
         * 增量思维链内容（输出使用增量）
         */
        StringBuffer addThinkMsg = new StringBuffer();
        /**
         * 全量文本内容（送审使用全量，保存数据库使用全量）
         */
        StringBuffer allTextMsg = new StringBuffer();
        /**
         * 全量思维链文本内容（送审使用全量，保存数据库使用全量）
         */
        StringBuffer allThinkTextMsg = new StringBuffer();
        /**
         * 大模型联网搜索内容
         */
        private List<AiTextResultRespParameters.NetworkSearchInfo> networkSearchInfoList;

        // 大模型token标识
        Integer outputTokens = 0;
        // 发送信息标识
        boolean sendFlag = false;
        // 结束对话标识
        boolean finishFlag = false;
        // 全量送审标识
        boolean fullAuditFlag = false;
        // 流式完成标识
        AtomicBoolean completeFlag = new AtomicBoolean(false);

        private String userId;
        private Long sessionId;
        private Long dialogId;
        private String sourceChannel;
        private SseEmitter sseEmitter = null;
        private AlgorithmChatAddVO resVO = null;
        private String resourceContent = null;
        private String modelCode = "";
        private TextModelDTO textDto;
        private ChatAddInnerDTO innerDTO;


        public StreamEventListenerParams(ChatAddInnerDTO innerDTO, String resourceContent, String promptTemplate) {
            this.innerDTO = innerDTO;
            this.sseEmitter = innerDTO.getSseEmitter();
            this.resourceContent = resourceContent;
            this.resVO = innerDTO.getRespParams();

            this.userId = innerDTO.getReqParams().getUserId();
            this.sessionId = innerDTO.getSessionId();
            this.dialogId = innerDTO.getDialogueId();
            this.sourceChannel = innerDTO.getContent().getSourceChannel();

            this.textDto = new TextModelDTO(innerDTO.getReqParams(), innerDTO.getRespParams(), resourceContent, null, promptTemplate);
        }

        // 处理对话终止
        private boolean handleChatEndMsg() {
            return handleSuccessAndChatStatusMsg(ChatStatusEnum.CHAT_END.getCode());
        }

        // 处理对话成功
        private boolean handleSuccessMsg(Boolean mergeThinkContentVersion, Long index) {
            return handleSuccessAndChatStatusMsg(ChatStatusEnum.CHAT_SUCCESS.getCode());
        }

        // 处理成功的情况
        private boolean handleSuccessAndChatStatusMsg(Integer chatStatus) {
            // sse结束
            SseEmitterDataUtils.complete(sseEmitter, completeFlag);

            // 更新会话状态
            algorithmChatHistoryService.updateOutResult(dialogId, OutAuditStatusEnum.SUCCESS.getCode(), chatStatus, null);

            // 更新hbase会话内容
            AiTextResultRespParameters result = new AiTextResultRespParameters(ResultCodeEnum.SUCCESS, networkSearchInfoList, String.valueOf(allThinkTextMsg), String.valueOf(allTextMsg));
            aiTextResultRepository.update(userId, dialogId, textDto.addAnswer(allTextMsg.toString()), result);

            return true;
        }

        // 处理停止对话的情况，前端调停止接口修改状态，这里只需要更新输出审核状态
        private boolean handleStop() {
            // sse结束
            SseEmitterDataUtils.complete(sseEmitter, completeFlag);

            // 更新会话状态（对话成功）
            algorithmChatHistoryService.updateOutResultStop(dialogId);

            // 更新hbase会话内容
            AiTextResultRespParameters result = new AiTextResultRespParameters(ResultCodeEnum.SUCCESS, networkSearchInfoList, String.valueOf(allThinkTextMsg), String.valueOf(allTextMsg));
            aiTextResultRepository.update(userId, dialogId, textDto.addAnswer(allTextMsg.toString()), result);

            return true;
        }

        // 处理失败的情况
        private boolean handleFailMsg(AiResultCode aiResultCode) {
            // 发送SseEmitter消息并关闭连接
            BaseResult<?> errRes = BaseResult.error(aiResultCode.getCode(), aiResultCode.getMsg());
            SseEmitterDataUtils.sendMsgAndComplete(sseEmitter, errRes, completeFlag);

            // 更新会话状态（对话失败）
            AiTextResultRespParameters result = new AiTextResultRespParameters(aiResultCode);
            algorithmChatHistoryService.updateOutResult(dialogId, OutAuditStatusEnum.FAIL.getCode(), ChatStatusEnum.CHAT_FAIL.getCode(), JsonUtil.toJson(result));

            // 更新hbase会话内容
            aiTextResultRepository.update(userId, dialogId, null, result);

            return false;
        }

        private boolean handleFailMsg(ResultCodeEnum aiResultCode) {
            // 发送SseEmitter消息并关闭连接
            BaseResult<?> errRes = BaseResult.error(aiResultCode);
            SseEmitterDataUtils.sendMsgAndComplete(sseEmitter, errRes, completeFlag);

            // 更新会话状态（对话失败）
            AiTextResultRespParameters result = new AiTextResultRespParameters(aiResultCode);
            algorithmChatHistoryService.updateOutResult(dialogId, OutAuditStatusEnum.FAIL.getCode(), ChatStatusEnum.CHAT_FAIL.getCode(), JsonUtil.toJson(result));

            // 更新hbase会话内容
            aiTextResultRepository.update(userId, dialogId, null, result);

            return false;
        }
    }
}