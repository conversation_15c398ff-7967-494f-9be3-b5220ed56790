package com.zyhl.yun.api.outer.application.dto;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.util.SseEmitterDataUtils;
import com.zyhl.yun.api.outer.application.vo.SecondStreamChatAddVO;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import lombok.Data;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 二次流式对话接口内部数据传输对象，包含：
 * 1、请求参数，数据不可改
 * 2、生成响应参数的方法
 * 3、中间状态数据
 *
 * <AUTHOR>
 */
@Data
public class SecondStreamChatAddInnerDTO {

    /**
     * 请求参数，里面的数据不可改
     */
    private SecondStreamChatAddDTO reqParams;

    /**
     * 流式对象
     */
    private SseEmitter sseEmitter;

    /**
     * 意图编码
     */
    private String intentionCode;

    /**
     * 会话id
     */
    private String sessionId;

    /**
     * 大模型对话内容
     */
    private String content;

    /**
     * hbase结果
     */
    private AiTextResultRespParameters respParameters;

    /**
     * 响应结果
     */
    private SecondStreamChatAddVO respParams = new SecondStreamChatAddVO();

    public SecondStreamChatAddInnerDTO(SecondStreamChatAddDTO reqParams, SseEmitter sseEmitter) {
        this.reqParams = reqParams;
        this.sseEmitter = sseEmitter;
    }

    // --------------------- 返回结果 --------------------- //

    /**
     * 流式响应结果
     */
    public void sseSendAndComplete() {

        // 返回结果
        respParams = SecondStreamChatAddVO.builder()
                .commands(intentionCode)
                .dialogueId(reqParams.getDialogueId())
                .mailInfo(respParameters.getMailInfo())
                .build();

        // 封装结果对象
        BaseResult<SecondStreamChatAddVO> result = BaseResult.success(respParams);

        // 流式输出结果
        SseEmitterDataUtils.sendMsgAndComplete(sseEmitter, result, new AtomicBoolean(false));
    }

}
