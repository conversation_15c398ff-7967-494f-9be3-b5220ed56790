package com.zyhl.yun.api.outer.application.chatv2.hanlde;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;

import org.springframework.beans.factory.InitializingBean;

import java.util.ArrayList;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * AI对话V2处理器
 *
 * <AUTHOR>
 */
public abstract class AbstractChatAddV2Handler implements ChatAddV2Handler, InitializingBean {

    /**
     * 执行顺序
     */
    @Getter
    @AllArgsConstructor
    protected enum ExecuteSort {

        /**
         * 0~29层级：（特定业务优先执行流程）
         */
        CONTINUE_TRANS(10, "断点续传"),

        /**
         * 30~99层级：（参数相关）
         */
        PARAM_RESET(50, "参数重置"),
        PARAM_VALIDATION(70, "参数校验"),

        /**
         * 100~199层级：（预处理）
         */
        BEFORE_CHAT_INFO(110, "对话信息前预处理"),
        BEFORE_THREAD_FUTURE(130, "线程预处理：意图识别线程预处理"),

        /**
         * 200~299层级：（优先执行）
         */
        PRIORITY_INTERVENTION(210, "干预库匹配"),

        /**
         * 300~399层级：（输入处理）
         */
        INPUT_AUDIT(310, "输入内容送审"),
        INPUT_INTENTION(330, "输入内容意图识别"),

        MAIL_AI_SEARCH(340, "邮箱界面智能搜索"),

        /**
         * 400~499层级：（特殊业务处理）
         */
        MAIL_AI_EDIT(405, "云邮AI编辑"),
        SPECIAL_TASK_SPEED_READ_CHAT(410, "文本大模型-任务（图书快速阅读）对话"),
        SPECIAL_APP_SPEED_READ_CHAT(412, "文本大模型-应用-【图书快速阅读】对话"),
        SPECIAL_APP_INTELLIGENT_CHAT(420, "文本大模型-应用【角色智能体】对话"),
        SPECIAL_INTELLIGENT_MEETING(425, "智能会议智能体对话"),
        SPECIAL_INTELLIGENT_PLAN(430, "云盘规划智能体对话"),
        SPECIAL_AI_INTERNET_SEARCH(440, "AI全网搜"),
        SPECIAL_SEND_MAIL(450, "发邮件意图处理"),
        SPECIAL_PROMPT_RECOMMEND(460, "提示词推荐"),

        /**
         * 500~599层级：（推荐文案相关）
         */
        RECOMMEND_LEAD_COPY(510, "引导语类型1-4（引导语类型5已经删除）"),
        RECOMMEND_INFO(530, "推荐信息：多意图推荐、提问语句推荐"),
        RECOMMEND_CLOUD_PHONE_LEAD_COPY(550, "云手机引导语意图推荐"),
        RECOMMEND_MESSAGE_5G_LEAD_COPY(560, "5G消息引导语意图推荐"),

        /**
         * 600~699层级：（搜索相关）
         */
        SEARCH_KNOWLEDGE_BASE_RESOURCE(605, "搜索知识库资源"),
        SEARCH_FULL(610, "全部搜索相关"),
        SEARCH_FOR_YUN_MAIL(630, "云邮助手搜索相关"),
        SEARCH_FOR_NOTE(640, "笔记助手搜索相关"),
        SEARCH_FOR_CLOUD_PHONE(650, "云手机搜索相关"),

        /**
         * 700~799层级：（文本工具相关）
         */
        TEXT_TOOL_AI_SPEED_READ(710, "图书快速阅读"),
        TEXT_TOOL_AI_PPT_OUT_LINE(714, "AI PPT OUT LINE"),
        TEXT_TOOL_AI_PPT(715, "AI PPT"),
        TEXT_TOOL_AI_CODER(720, "AI编程"),
        TEXT_TOOL_AI_MEETING_MINUTES(725, "AI会议纪要"),
        TEXT_TOOL_AI_PHOTO(730, "AI拍照识图"),
        TEXT_TOOL_AI_MEMORY_ALBUM(735, "生成回忆相册"),
        TEXT_TOOL_OTHERS(799, "文本工具意图-兜底处理类"),

        /**
         * 800~899层级：（大模型流式相关）
         */
        TEXT_MODE_KNOWLEDGE_SSE(810, "知识库流式对话"),
        TEXT_MODE_KNOWLEDGE_SSE_NOTE(815, "笔记AI助手知识库流式对话"),
        TEXT_MODE_VISION_SSE(820, "视觉大模型流式对话"),
        TEXT_MODE_DOC_SSE(830, "文档流式对话"),
        TEXT_MODE_TEXT_SSE(850, "文本流式对话"),


        /**
         * 900~999层级：（异步处理相关）
         */
        ASYNC_TEXT_GENERATE_PICTURE(910, "异步处理：图片工具-文生图"),
        ASYNC_IMAGE_TOOL(950, "异步处理：图片工具"),

        /**
         * 1000~1100层级：（开放接口处理相关）
         */
        OPEN_API_LINGXI_INTELLIGENT_MEETING(1010, "openai lingxi 智能会议智能体对话"),
        ;

        /**
         * 排序
         */
        private final int sort;
        /**
         * 描述
         */
        private final String desc;

    }

    /**
     * handler支持的业务类型列表
     */
    private List<ChatBusinessTypeEnum> businessTypes = new ArrayList<>();

    @Override
    public int order() {
        return 0;
    }


    @Override
    public List<ChatBusinessTypeEnum> getBusinessTypes() {
        return businessTypes;
    }


    public void setBusinessTypes(List<ChatBusinessTypeEnum> businessTypes) {
        this.businessTypes = businessTypes;
    }


    @Override
    public boolean execute(ChatAddHandleDTO handleDTO) {
        return true;
    }

    /**
     * 继续执行handler
     *
     * @param handleDTO
     * @param event
     */
    public void runHandlerContinue(ChatAddHandleDTO handleDTO, SseEventListener event) {

    }

}
