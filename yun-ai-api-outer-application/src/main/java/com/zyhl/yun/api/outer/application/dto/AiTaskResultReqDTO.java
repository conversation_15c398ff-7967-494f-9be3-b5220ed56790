package com.zyhl.yun.api.outer.application.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 任务结果信息请求入参req
 *
 * <AUTHOR>
 * @Date 2024/03/16 12:00
 */
@Data
public class AiTaskResultReqDTO implements Serializable {

    private static final long serialVersionUID = -8142846648709373297L;
    /**
     * 任务id
     */
    @NotNull(message = "任务id不可为空")
    private String taskId;

    /**
     * 用户id，如果有token就不需要传
     */
    private String userId;

}
