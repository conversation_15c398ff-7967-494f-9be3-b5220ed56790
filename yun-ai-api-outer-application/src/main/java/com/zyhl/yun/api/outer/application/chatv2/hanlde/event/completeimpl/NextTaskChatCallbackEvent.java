package com.zyhl.yun.api.outer.application.chatv2.hanlde.event.completeimpl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.event.AbstractCompleteCallbackEvent;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.impl.SpecialTaskSpeedReadChatHandlerImpl;
import com.zyhl.yun.api.outer.application.chatv2.pojo.CompleteEvent;
import com.zyhl.yun.api.outer.application.chatv2.pojo.TaskPromptOfChatInfo;
import com.zyhl.yun.api.outer.enums.AiResultCode;

import lombok.extern.slf4j.Slf4j;

/**
 * 下一个执行异步图片工具处理器事件
 * 
 * <AUTHOR>
 * @date 2025-04-28
 */
@Slf4j
@Component
public class NextTaskChatCallbackEvent extends AbstractCompleteCallbackEvent {

	@Resource
	private SpecialTaskSpeedReadChatHandlerImpl specialTaskSpeedReadChatHandlerImpl;

	@Override
	public void complete(CompleteEvent data) {
		try {
			// 继续执行handler
			log.info("继续执行handler 任务对话，多提示词 dialogueId:{}", data.getHandleDTO().getDialogueId());
			ChatAddHandleDTO handleDTO = data.getHandleDTO();
			List<TaskPromptOfChatInfo> taskPromptOfChatInfos = handleDTO.getTaskPromptOfChatInfos();
			// 设置成功的提示词聊天信息
			for (TaskPromptOfChatInfo taskPromptOfChatInfo : taskPromptOfChatInfos) {
				if (!taskPromptOfChatInfo.isComplete()
						&& taskPromptOfChatInfo.getModule().equals(handleDTO.getCurrentTaskModule())) {
					taskPromptOfChatInfo.setChatResult(data.getOutContent());
					taskPromptOfChatInfo.setComplete(true);
					break;
				}
			}
			specialTaskSpeedReadChatHandlerImpl.runHandlerContinue(handleDTO, data.getEventListener());
		} catch (YunAiBusinessException e) {
			data.getEventListener().dialogueFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			data.getEventListener().dialogueFail(AiResultCode.CODE_9999);
		}
	}

}
