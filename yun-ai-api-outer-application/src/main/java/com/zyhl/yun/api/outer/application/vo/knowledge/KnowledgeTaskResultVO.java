package com.zyhl.yun.api.outer.application.vo.knowledge;

import lombok.Data;

import java.util.List;

/**
 * 知识库上传任务结果
 *
 * <AUTHOR>
 */
@Data
public class KnowledgeTaskResultVO {

    /**
     * 任务状态：
     * -1--失败
     * 0--运行中
     * 1--部分成功
     * 2--Succeed成功
     * （若状态是0运行中，可稍后再轮询任务，直到状态非0）
     */
    private Integer status;

    /**
     * 文档上传结果
     */
    private List<KnowledgeAddResultVO> resultList;

}
