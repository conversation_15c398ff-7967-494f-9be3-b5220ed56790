package com.zyhl.yun.api.outer.application.service.memory;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.hcy.yun.ai.common.base.enums.YunAiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.base.es.domain.datahelper.repository.EsAlbumRepository;
import com.zyhl.hcy.yun.ai.common.base.es.entity.ElasticSearchEntity;
import com.zyhl.hcy.yun.ai.common.base.es.entity.ImageVectorDetectionEntity;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.hbase.domain.datahelper.repository.HbaseRepository;
import com.zyhl.hcy.yun.ai.common.base.hbase.infrastructure.persistence.po.metadata.ImageQualityPO;
import com.zyhl.hcy.yun.ai.common.base.hbase.infrastructure.persistence.po.metadata.MetadataInfoPO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.YunDiskClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.BatchFileDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.FileResult;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.BatchFileVO;
import com.zyhl.yun.api.outer.application.dto.ImageSelectionReqDTO;
import com.zyhl.yun.api.outer.application.service.GenerateMemoryService;
import com.zyhl.yun.api.outer.application.service.MemoryAlbumFileFilterService;
import com.zyhl.yun.api.outer.application.util.DateUtils;
import com.zyhl.yun.api.outer.config.MemoryAlbumConfig;
import com.zyhl.yun.api.outer.config.label.ThingLabelConfig;
import com.zyhl.yun.api.outer.constants.CommentConstants;
import com.zyhl.yun.api.outer.constants.FutureConstants;
import com.zyhl.yun.api.outer.domain.aggregate.PictureFileAggregate;
import com.zyhl.yun.api.outer.domain.dto.QualityAlbumSelectionRespDTO;
import com.zyhl.yun.api.outer.domain.entity.ResemblanceEntity;
import com.zyhl.yun.api.outer.domain.entity.image.AlbumConditionEntity;
import com.zyhl.yun.api.outer.domain.entity.image.AlgorithmMetadataEntity;
import com.zyhl.yun.api.outer.domain.entity.image.AlgorithmUserFileEntity;
import com.zyhl.yun.api.outer.domain.valueobject.Member;
import com.zyhl.yun.api.outer.domainservice.ResemblanceService;
import com.zyhl.yun.api.outer.enums.AlbumConditionRuleEnum;
import com.zyhl.yun.api.outer.repository.AlgorithmUserFileRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 生成意图回忆相册服务实现类
 * @date 2025/5/20 15:06
 */
@Service
@Slf4j
public class GenerateMemoryServiceImpl implements GenerateMemoryService {

    private static final int QUALITY_SCALE_FACTOR = 1000000;

    /**
     * 批量查询个人云size，默认100
     */
    private static final int MAX_IMAGE_SIZE = 100;
    
    /**
     * 批量查询个人云，失败则重试2次
     */
	private static final int QUERY_COUNT_MAX = 2;

    @Value("${memory.album.quality.hbaseQuerySize:50}")
    private int hbaseQuerySize;

    @Resource
    private ThingLabelConfig thingLabelConfig;

    @Resource
    private MemoryAlbumConfig memoryAlbumConfig;

    @Resource
    private ResemblanceService resemblanceService;

    @Resource
    private AlgorithmUserFileRepository userFileRepository;

    @Resource
    private HbaseRepository hbaseRepository;

    @Resource
    private EsAlbumRepository esAlbumRepository;

    @Resource
    private YunDiskClient yunDiskClient;
    
    @Resource
    private MemoryAlbumFileFilterService memoryAlbumFileFilterService;

	@Resource(name = "memoryAlbumThreadPool")
	private ExecutorService memoryAlbumThreadPool;
	
    @Override
    public QualityAlbumSelectionRespDTO generateMemoryAlbum(ImageSelectionReqDTO imageSelectionReqDTO) {
        long startTime = System.currentTimeMillis();
        log.info("【意图回忆相册】开始生成意图回忆相册,请求参数:{}", JSONUtil.toJsonStr(imageSelectionReqDTO));
        String userId = imageSelectionReqDTO.getUserId();
        Integer ownerType = imageSelectionReqDTO.getOwnerType();
        int albumFileMinNum = memoryAlbumConfig.getAlbumFileMinNum();

        // 1.查询用户文件信息以及hbase数据
        List<PictureFileAggregate> fileAggregateList = queryDbFileInfoAndHbase(imageSelectionReqDTO);
        if (CollUtil.isEmpty(fileAggregateList)) {
            log.info("【意图回忆相册】用户文件信息以及hbase数据为空,userId:{},ownerType:{},相册最小生成数:{},耗时:{}ms",
                    userId, ownerType, albumFileMinNum,(System.currentTimeMillis() - startTime));
            return null;
        }

        // 2.去除无关规则
        fileAggregateList = removeIrrelevantRules(fileAggregateList);
        
        // 3.过滤正常的相册（违规的，删除的，保险箱的等等非0000都不会返回）
        fileAggregateList = filterNormalData(fileAggregateList);
        
        // 4.过滤重复图片(相似度去重)
        fileAggregateList = fileDeduplication(userId, fileAggregateList);

        if (CollUtil.isEmpty(fileAggregateList)) {
            log.info("【意图回忆相册】文件图片筛选后为空,userId:{},ownerType:{},相册最小生成数:{},耗时:{}ms",
                    userId, ownerType, albumFileMinNum,(System.currentTimeMillis() - startTime));
            return null;
        }

        // 5.过滤
        if (fileAggregateList.size() < albumFileMinNum) {
            log.info("【意图回忆相册】文件图片筛选后不满足最小生成图片数量,userId:{},ownerType:{},count:{},albumFileMinNum:{},耗时:{}ms",
                    userId, ownerType, fileAggregateList.size(), albumFileMinNum, (System.currentTimeMillis() - startTime));
            return QualityAlbumSelectionRespDTO.builder().albumFileSize(fileAggregateList.size()).build();
        }

        // 6.根据搜索条件做取舍
        AlbumConditionEntity albumCondition = imageSelectionReqDTO.getAlbumCondition();
        List<String> fileIdList = null;
        if (ObjectUtil.isEmpty(albumCondition)
                || CharSequenceUtil.isEmpty(albumCondition.getSearchRule())
                || AlbumConditionRuleEnum.ALL.getName().equals(albumCondition.getSearchRule())) {
            // 走默认规则
            fileIdList = memoryAlbumFileFilterService.defaultFilterFile(fileAggregateList, albumCondition.getSearchSize());
        }else if (AlbumConditionRuleEnum.YEAR.getName().equals(albumCondition.getSearchRule())){
            // 走按年筛选图片
            fileIdList = memoryAlbumFileFilterService.yearFilterFile(fileAggregateList, albumCondition.getSearchSize());
        }else if (AlbumConditionRuleEnum.SEASON.getName().equals(albumCondition.getSearchRule())){
            // 走按每季度筛选图片
            fileIdList = memoryAlbumFileFilterService.seasonFilterFile(fileAggregateList, albumCondition.getSearchSize());
        }else if (AlbumConditionRuleEnum.MONTH.getName().equals(albumCondition.getSearchRule())){
            // 走按每月筛选图片
            fileIdList = memoryAlbumFileFilterService.monthFilterFile(fileAggregateList, albumCondition.getSearchSize());
        }else if (AlbumConditionRuleEnum.WEEK.getName().equals(albumCondition.getSearchRule())){
            // 走按每周筛选图片
            fileIdList = memoryAlbumFileFilterService.weekFilterFile(fileAggregateList, albumCondition.getSearchSize());
        }
        if (fileIdList.size() < albumFileMinNum) {
            String conditionStr = ObjectUtil.isEmpty(albumCondition) ? "" : JSONUtil.toJsonStr(albumCondition);
            log.info("【意图回忆相册】根据筛选规则:{},筛选后文件图片根据最小生成图片数量,userId:{},ownerType:{},count:{},albumFileMinNum:{},耗时:{}ms",
                    conditionStr, userId, ownerType, fileIdList.size(), albumFileMinNum, (System.currentTimeMillis() - startTime));
            return QualityAlbumSelectionRespDTO.builder().albumFileSize(fileIdList.size()).build();
        }
        log.info("【意图回忆相册】最终生成的回忆相册文件id:{},userId:{},ownerType:{},相册最小生成数:{},耗时:{}ms",
                JSONUtil.toJsonStr(fileIdList), userId, ownerType, albumFileMinNum,(System.currentTimeMillis() - startTime));
        return QualityAlbumSelectionRespDTO.builder()
                .ownerType(ownerType)
                .userId(userId)
                .albumName(memoryAlbumConfig.getRandomAlbumName())
                .albumSecondTittle(this.getSecondTitle(fileAggregateList))
                .musicId(memoryAlbumConfig.getRandomAlbumMusicId())
                .albumCover(fileIdList.get(CommentConstants.ZERO_SEQ_NO))
                .fileIds(fileIdList)
                .albumFileSize(fileIdList.size())
                .build();
    }

	private List<PictureFileAggregate> sort(List<PictureFileAggregate> fileAggregateList) {
        fileAggregateList.forEach(x -> {
            if (Objects.isNull(x.getImageQuality())) {
                ImageQualityPO imageQualityPO = new ImageQualityPO();
                x.setImageQuality(imageQualityPO);
            }
            if (Objects.isNull(x.getImageQuality().getImgQuality())) {
                x.getImageQuality().setImgQuality((float) 0);
            }
        });
        List<PictureFileAggregate> personList = fileAggregateList.stream().filter(x -> (CollUtil.isNotEmpty(x.getFaceList()) && x.getFaceList().size() > 0))
                .sorted(Comparator.comparing(x -> x.getImageQuality().getImgQuality()))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(personList) && personList.size() == memoryAlbumConfig.getAlbumFileMinNum()) {
            return personList;
        }

        fileAggregateList.removeAll(personList);

        return personList;

    }

    /**
     * 去无关规则 去除无拍摄时间的照片 去掉文档（404）、证件照（405、108000、108001、108002...108012)和截图（403）等的图片，选择指定的标签图片
     * 去除负面情绪 去低分 去掉1970-1-1的图片
     *
     * @param fileAggregateList
     */
    private List<PictureFileAggregate> removeIrrelevantRules(List<PictureFileAggregate> fileAggregateList) {

        List<Long> inverses = thingLabelConfig.getInverses();

        List<PictureFileAggregate> needToRemove = new ArrayList<>();
        for (PictureFileAggregate aggregate : fileAggregateList) {
            if (!CollUtil.isEmpty(aggregate.getThingList())) {
                //事物标签集合存在需过滤标签则整个实体过滤
                boolean shouldRemove = aggregate.getThingList().stream()
                        .anyMatch(thingLabelPO -> inverses.contains(thingLabelPO.getThingType()));
                if (Objects.equals(Boolean.TRUE, shouldRemove)) {
                    needToRemove.add(aggregate);
                    log.info("【意图回忆相册】{} 包含需去除标签thingList:{}", aggregate.getFileId(), JSONUtil.toJsonStr(aggregate.getThingList()));
                }
            }
        }
        if (CollUtil.isNotEmpty(needToRemove)) {
            //防并发修改，故统一移除
            fileAggregateList.removeAll(needToRemove);
        }
        //去低分
        List<PictureFileAggregate> fileAggregates = fileAggregateList.stream()
                .filter(x -> x.getImageQuality() != null)
                .filter(x -> {
                    double scoreDouble = Double.parseDouble(String.valueOf(x.getImageQuality().getImgQuality()));
                    int intScore = (int) (scoreDouble * QUALITY_SCALE_FACTOR);
                    boolean filterFlag = intScore > thingLabelConfig.getThingScore();
                    if (!filterFlag){
                        log.info("【意图回忆相册】用户userId:{},文件id:{},质量评分:{}太低已被过滤", x.getUserId(), x.getFileId(), intScore);
                    }
                    return filterFlag;
                })
                // 组装质量评分字段
                .map(fileAggregate -> fileAggregate.setImgQualityScore(QUALITY_SCALE_FACTOR))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(fileAggregates)) {
            //去除无拍摄时间和拍摄时间为1970-01-01 00:00:00 的照片
            fileAggregates = fileAggregates.stream()
                    .filter(x -> Objects.nonNull(x.getShootTime()))
                    .filter(x -> !DateUtils.isEpochDate(x.getShootTime()))
                    .collect(Collectors.toList());
        }
        return fileAggregates;
    }

    /**
     * 查询数据库userFile表 符合拍摄时间的文件数据
     * 符合文件的数据查询hbase获取标签数据
     *
     * @param imageSelectionReqDTO 请求体
     * @return 待生成图片标签数据
     */
    private List<PictureFileAggregate> queryDbFileInfoAndHbase(ImageSelectionReqDTO reqDTO) {
        //1.过滤拍摄时间为空的数据
        List<String> fileIds = reqDTO.getFileIds();
        List<AlgorithmUserFileEntity> dbUserFileList =  new ArrayList<>();
        if(ObjectUtil.isEmpty(reqDTO.getAlbumCondition())
                || CollUtil.isEmpty(reqDTO.getAlbumCondition().getDateRangeList())){
            // 过滤条件为空,直接查询所有文件
            dbUserFileList = userFileRepository.selectUserFileInfoHaveMetadataRowKey(reqDTO.getUserId(), reqDTO.getOwnerType(), fileIds);
            log.info("【意图回忆相册】时间过滤条件为空,查询得到的相册文件列表:{}", JSONUtil.toJsonStr(dbUserFileList));
        }else {
            List<AlbumConditionEntity.DateRange> fiffterDateRangeList = reqDTO.getAlbumCondition().getDateRangeList();
            for (AlbumConditionEntity.DateRange dateRange : fiffterDateRangeList) {
                if (CharSequenceUtil.isEmpty(dateRange.getStartDate())
                        || CharSequenceUtil.isEmpty(dateRange.getEndDate())){
                    log.info("【意图回忆相册】userId:{},ownerType:{},过滤时间存在空值,开始时间:{},结束时间:{}",
                            reqDTO.getUserId(), reqDTO.getOwnerType(), dateRange.getStartDate(), dateRange.getEndTime());
                    continue;
                }
                LocalDateTime startDate = dateRange.getFirstTime();
                LocalDateTime endDate = dateRange.getEndTime();
                List<AlgorithmUserFileEntity> dbUserFileTemp = userFileRepository.selectHbaseUserFileByTime(reqDTO.getUserId(),
                        reqDTO.getOwnerType(), reqDTO.getFileIds(), startDate, endDate);
                if (CollUtil.isNotEmpty(dbUserFileTemp)){
                    dbUserFileList.addAll(dbUserFileTemp);
                    Set<String> fileIdList = dbUserFileTemp.stream().map(AlgorithmUserFileEntity::getFileId).collect(Collectors.toSet());
                    log.info("【意图回忆相册】userId:{},ownerType:{},开始时间:{},结束时间:{},查询数据:{}",
                            reqDTO.getUserId(), reqDTO.getOwnerType(), dateRange.getStartDate(), dateRange.getEndTime(), fileIdList);
                }
            }
        }

        Set<String> rowKeySet = dbUserFileList.stream()
                .map(AlgorithmUserFileEntity::getMetaRowKey).collect(Collectors.toSet());
        // 查询hbase数据
        List<AlgorithmMetadataEntity> metadataResultList = queryHbaseData(rowKeySet);
        if (CollUtil.isEmpty(metadataResultList)) {
            log.info("【意图回忆相册】根据rowKey查询hbase数据为空,userId:{},fileId列表:{},",
                    reqDTO.getUserId(), JSONUtil.toJsonStr(reqDTO.getFileIds()));
            return Collections.emptyList();
        }
        // 根据hbase值组装待生成图片文件数据
        return getPictureFileData(reqDTO, dbUserFileList, metadataResultList);
    }

    /**
     * 获取待生成图片文件数据
     *
     * @param reqDTO 请求参数
     * @param dbUserFileList db用户资源文件表列表
     * @param metadataResultList 文件对应hbase元数据列表
     * @return 待生成图片文件列表数据
     */
    private List<PictureFileAggregate> getPictureFileData(ImageSelectionReqDTO reqDTO, List<AlgorithmUserFileEntity> dbUserFileList, List<AlgorithmMetadataEntity> metadataResultList) {
        Map<String, AlgorithmUserFileEntity> fileEntityMap = dbUserFileList.stream().collect(Collectors.toMap(AlgorithmUserFileEntity::getFileId,
                Function.identity(), (x1, x2) -> x1));
        ArrayList<PictureFileAggregate> resultList = new ArrayList<>();
        metadataResultList.forEach(fileMatadata -> {
            if (CharSequenceUtil.isEmpty(fileMatadata.getFileId())
                    || !fileEntityMap.containsKey(fileMatadata.getFileId())) {
                log.info("【意图回忆相册】组装hbase数据转换异常,userId:{},fileId:{},rowKey:{},原因:hbase返回fileId为空或者文件id不在查询文件列表中",
                        reqDTO.getUserId(), fileMatadata.getFileId(), fileMatadata.getRowKey());
            } else {
                AlgorithmUserFileEntity userFileEntity = fileEntityMap.get(fileMatadata.getFileId());
                PictureFileAggregate fileAggregate = PictureFileAggregate.builder().fileId(fileMatadata.getFileId()).
                        userId(reqDTO.getUserId()).faceList(fileMatadata.getFaceList())
                        .thingList(fileMatadata.getThingList())
                        .imageQuality(fileMatadata.getImgQuality()).shootTime(userFileEntity.getShotTime()).build();
                resultList.add(fileAggregate);
            }
        });
        return resultList;
    }

    /**
     * 查询hbase数据
     *
     * @param rowKeySet hbase的rowKey
     * @return 图片元数据
     */
    private List<AlgorithmMetadataEntity> queryHbaseData(Set<String> rowKeySet) {
        List<List<String>> splitRowKeyList = CollUtil.split(rowKeySet, hbaseQuerySize);
        List<AlgorithmMetadataEntity> metadataResultList = new ArrayList<>();
        splitRowKeyList.forEach(rowkeyList -> {
            try {
                List<MetadataInfoPO> metadataInfoPoS = rowkeyList.stream()
                        .map(GenerateMemoryServiceImpl::applyMetadataInfoPo).collect(Collectors.toList());
                List<MetadataInfoPO> fileMetadataList = hbaseRepository.metadataList(metadataInfoPoS);
                if (CollUtil.isNotEmpty(fileMetadataList)) {
                    List<AlgorithmMetadataEntity> tempEntity = fileMetadataList.stream()
                            .map(GenerateMemoryServiceImpl::applyAlgorithmMetadataEntity)
                            .collect(Collectors.toList());
                    metadataResultList.addAll(tempEntity);
                }
            } catch (Exception e) {
                log.error("查询hbase异常,rowKey:{},异常:", JSONUtil.toJsonStr(rowkeyList), e);
            }
        });
        return metadataResultList;
    }

    /**
     * rowKey to MetadataInfoPO
     *
     * @param rowKey rowkey
     * @return MetadataInfoPO
     */
    private static MetadataInfoPO applyMetadataInfoPo(String rowKey) {
        return MetadataInfoPO.builder().rowKey(rowKey).build();
    }

    /**
     * MetadataInfoPO to AlgorithmMetadataEntity
     *
     * @param metadataInfoPO
     * @return AlgorithmMetadataEntity
     */
    private static AlgorithmMetadataEntity applyAlgorithmMetadataEntity(MetadataInfoPO metadataInfoPO) {
        return AlgorithmMetadataEntity.builder().rowKey(metadataInfoPO.getRowKey())
                .fileId(metadataInfoPO.getFileId())
                .imageFeature(metadataInfoPO.getImageFeature())
                .imgQuality(metadataInfoPO.getImageQuality())
                .thingList(metadataInfoPO.getThingList())
                .faceList(metadataInfoPO.getFaceList()).build();
    }
    
	/**
	 * 过滤正常的相册（违规的，删除的，保险箱的等等非0000都不会返回）
	 * 
	 * @param fileAggregateList 相册列表
	 * @return 相册列表
	 */
	private List<PictureFileAggregate> filterNormalData(List<PictureFileAggregate> fileAggregateList) {
		if (CollUtil.isEmpty(fileAggregateList)) {
			return fileAggregateList;
		}
		// 按页数拆分list
		List<List<PictureFileAggregate>> splitLists = ListUtil.split(fileAggregateList, MAX_IMAGE_SIZE);
		// 获取当前用户
		final String userId = RequestContextHolder.getUserId();
		// 多线程future列表
		List<Future<List<PictureFileAggregate>>> futures = new ArrayList<>();
		for (int i = 0; i < splitLists.size(); i++) {
			final int pageNum = (i + 1);
			List<PictureFileAggregate> splitList = splitLists.get(i);
			// 获取多线程future
			Future<List<PictureFileAggregate>> future = (Future<List<PictureFileAggregate>>) memoryAlbumThreadPool
					.submit(() -> {
						log.info("多线程查询第{}批文件， size:{}", pageNum, splitList.size());
						List<String> fileIds = splitList.stream().map(PictureFileAggregate::getFileId)
								.collect(Collectors.toList());
						BatchFileDTO batchGetReq = new BatchFileDTO();
						batchGetReq.setUserId(userId);
						batchGetReq.setFileIds(fileIds);
						// 调用个人云接口
						BatchFileVO batchFileVO = fileBatchGet(batchGetReq);
						if (null != batchFileVO && CollUtil.isNotEmpty(batchFileVO.getBatchFileResults())) {
							List<PictureFileAggregate> resultList = new ArrayList<>();
							for (PictureFileAggregate splitFile : splitList) {
								for (FileResult tempFile : batchFileVO.getBatchFileResults()) {
									if (null != tempFile.getSrcFile() && null != tempFile.getSrcFile().getFileId()
											&& tempFile.getSrcFile().getFileId().equals(splitFile.getFileId())) {
										if (YunAiCommonResultCode.SUCCESS.getResultCode()
												.equals(tempFile.getErrCode())) {
											// 状态正常
											resultList.add(splitFile);
										} else {
											log.warn("照片不是正常状态，splitFileId:{} errorCode:{}", splitFile.getFileId(),
													tempFile.getErrCode());
										}
										break;
									}
								}
							}
							// 返回重查后的数据
							return resultList;
						}
						// 返回原始数据
						return splitList;

					});
			if (null != future) {
				futures.add(future);
			}
		}

		List<PictureFileAggregate> resultFiles = new ArrayList<>();
		for (Future<List<PictureFileAggregate>> future : futures) {
			try {
				List<PictureFileAggregate> files = future.get(FutureConstants.FILE_QUERY_SECONDS, TimeUnit.SECONDS);
				if (CollUtil.isNotEmpty(files)) {
					resultFiles.addAll(files);
				}
			} catch (Exception e) {
				log.error("future.get error:", e);
			}
		}
		log.info("原始数量:{}, 过滤后新数量:{}", fileAggregateList.size(), resultFiles.size());
		return resultFiles;
	}

	private BatchFileVO fileBatchGet(BatchFileDTO batchGetReq) {
		// 批量查询个人云，失败则重试2次
		for (int queryCount = 0; queryCount <= QUERY_COUNT_MAX; queryCount++) {
			try {
				BatchFileVO files = yunDiskClient.fileBatchGet(batchGetReq);
				return files;
			} catch (Exception e) {
				log.info("fileBatchGet queryCount:{}, error:", (queryCount + 1), e);
			}
		}
		return null;
	}

	private List<PictureFileAggregate> fileDeduplication(String userId, List<PictureFileAggregate> fileAggregates) {
        if (CollUtil.isEmpty(fileAggregates)) {
            return null;
        }
        List<Member> highestList = new ArrayList<>();
        List<Member> allMembers = new ArrayList<>();
        List<PictureFileAggregate> esNonExistentList = new ArrayList<>();
        List<PictureFileAggregate> nonEmbeddings = new ArrayList<>();

        Map<String, ElasticSearchEntity> elasticSearchEntityMap = fetchESData(userId, fileAggregates);
        if (Objects.isNull(elasticSearchEntityMap)) {
            return fileAggregates;
        }
        fileAggregates.forEach(file -> {
            ElasticSearchEntity elasticSearchEntity = elasticSearchEntityMap.get(file.getFileId());
            if (Objects.nonNull(elasticSearchEntity)) {
                if (Objects.nonNull(elasticSearchEntity.getEmbeddings())){
                    file.setFeature(elasticSearchEntity.getEmbeddings());
                }else {
                    //es存在但是没有向量的数据
                    nonEmbeddings.add(file);
                }
            }else {
                //es不存在的数据
                esNonExistentList.add(file);
            }
        });
        log.info("userId:{}【意图回忆相册】去重处理,查询es前的图片总数:{},es不存在的图片数:{}，缺失向量的图片数:{}",userId,fileAggregates.size(),esNonExistentList.size(),nonEmbeddings.size());
        if (CollUtil.isNotEmpty(esNonExistentList)){
        fileAggregates.removeAll(esNonExistentList);
        }

        if (CollUtil.isNotEmpty(nonEmbeddings) && memoryAlbumConfig.getHandleNonEmbedding()){
            fileAggregates.removeAll(nonEmbeddings);
        }
        long start = System.currentTimeMillis();
        Map<String, PictureFileAggregate> fileAggregateMap = fileAggregates.stream().collect(Collectors.toMap(PictureFileAggregate::getFileId, t -> t));

        try {
            // 调用去重服务
            List<ResemblanceEntity> resemblanceEntities = resemblanceService.imageCluster(fileAggregates, memoryAlbumConfig.getSimilarityThreshold());
            log.info("【意图回忆相册】userId:{},图片数量：{},去重耗时：{}ms", userId, fileAggregates.size(), System.currentTimeMillis() - start);
            // 将结果转换为文件 ID 列表，取每个分组的第一张图片
            for (ResemblanceEntity resemblanceEntity : resemblanceEntities) {
                //处理每个相似度组
                List<Member> memberList = resemblanceEntity.getMembers().stream().map(fileId -> {
                    //组装文件的质量评分
                    float imgQuality = Objects.isNull(fileAggregateMap.get(fileId).getImageQuality()) ? 0.0f : fileAggregateMap.get(fileId).getImageQuality().getImgQuality();
                    return Member.builder().fileId(fileId).imageQuality(imgQuality).build();
                }).collect(Collectors.toList());

                if (CollUtil.isNotEmpty(memberList)) {
                    //选取相似组质量评分最高的一张图片
                    Member member = memberList.stream().max(Comparator.comparing(Member::getImageQuality)).get();
                    highestList.add(member);
                    allMembers.addAll(memberList);
                }
            }
            if (!CollUtil.isEmpty(allMembers)) {
                allMembers.removeAll(highestList);
                // 删除非最高分数的图片
                Set<String> contentIdSet = allMembers.stream().map(Member::getFileId).collect(Collectors.toSet());
                return fileAggregates.stream()
                        .filter(x -> !contentIdSet.contains(x.getFileId())).collect(Collectors.toList());
            }
            return fileAggregates;
        } catch (Exception e) {
            throw e;
        }
    }

    private Map<String, ElasticSearchEntity> fetchESData(String userId, List<PictureFileAggregate> fileAggregates) {
        long start = System.currentTimeMillis();
        try {
            ImageVectorDetectionEntity elasticSearchEntity = new ImageVectorDetectionEntity();
            elasticSearchEntity.setUserId(userId);
            List<String> fileIds = fileAggregates.stream().map(PictureFileAggregate::getFileId).distinct().collect(Collectors.toList());
            elasticSearchEntity.setFileIds(fileIds);
            List<ElasticSearchEntity> elasticSearchEntities = esAlbumRepository.searchByFiles(elasticSearchEntity);
            if (CollUtil.isEmpty(elasticSearchEntities)) {
                log.warn("智能体生成回忆相册[ES] 查询结果为空,userId:{},耗时：{}ms", userId, System.currentTimeMillis() - start);
                return null;
            }
            log.info("智能体生成回忆相册[ES] 查询成功，耗时：{}ms，参数数：{}，返回数：{}",
                    System.currentTimeMillis() - start, fileIds.size(), elasticSearchEntities.size());

            return elasticSearchEntities.stream()
                    .collect(Collectors.toMap(ElasticSearchEntity::getFileId, Function.identity()));
        } catch (Exception e) {
            log.error("[ES] 查询异常，错误：{}", e.getMessage(), e);
            throw new YunAiBusinessException(YunAiCommonResultCode.ES_IO_EXCEPTION);
        }
    }

    /***
     * 获取二级标题
     * @param filterAggregates
     * @return
     */
    @SuppressWarnings("DuplicatedCode")
    private String getSecondTitle(List<PictureFileAggregate> filterAggregates) {
        LocalDate earliestDate = null;
        LocalDate latestDate = null;
        String secondTitle = null;

        Date earliestTime = filterAggregates.stream().min(Comparator.comparing(PictureFileAggregate::getShootTime)).get().getShootTime();
        Date latestTime = filterAggregates.stream().max(Comparator.comparing(PictureFileAggregate::getShootTime)).get().getShootTime();

        earliestDate = earliestTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        latestDate = latestTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();


        // 如果x=a
        if (Objects.nonNull(earliestDate) && Objects.nonNull(latestDate)) {
            if (earliestDate.getYear() == latestDate.getYear()) {
                // 如果y=b
                if (earliestDate.getMonthValue() == latestDate.getMonthValue()) {
                    // 如果z=c
                    if (earliestDate.getDayOfMonth() == latestDate.getDayOfMonth()) {
                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.append(earliestDate.getYear());
                        stringBuilder.append("年");
                        stringBuilder.append(earliestDate.getMonthValue());
                        stringBuilder.append("月");
                        stringBuilder.append(earliestDate.getDayOfMonth());
                        stringBuilder.append("日");
                        secondTitle = stringBuilder.toString();
                    }
                    // z!=c
                    else {
                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.append(earliestDate.getYear());
                        stringBuilder.append("年");
                        stringBuilder.append(earliestDate.getMonthValue());
                        stringBuilder.append("月");
                        stringBuilder.append(earliestDate.getDayOfMonth());
                        stringBuilder.append("日");
                        stringBuilder.append("-");
                        stringBuilder.append(latestDate.getDayOfMonth());
                        stringBuilder.append("日");
                        secondTitle = stringBuilder.toString();
                    }

                }
                // y!=b
                else {
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.append(earliestDate.getYear());
                    stringBuilder.append("年");
                    stringBuilder.append(earliestDate.getMonthValue());
                    stringBuilder.append("月");
                    stringBuilder.append("-");
                    stringBuilder.append(latestDate.getMonthValue());
                    stringBuilder.append("月");
                    secondTitle = stringBuilder.toString();
                }

            }
            // x!=a
            else {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(earliestDate.getYear());
                stringBuilder.append("年");
                stringBuilder.append(earliestDate.getMonthValue());
                stringBuilder.append("月");
                stringBuilder.append("-");
                stringBuilder.append(latestDate.getYear());
                stringBuilder.append("年");
                stringBuilder.append(latestDate.getMonthValue());
                stringBuilder.append("月");
                secondTitle = stringBuilder.toString();
            }
        }
        return secondTitle;
    }


}
