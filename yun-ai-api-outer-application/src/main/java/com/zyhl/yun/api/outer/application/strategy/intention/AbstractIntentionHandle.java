package com.zyhl.yun.api.outer.application.strategy.intention;

import cn.hutool.extra.spring.SpringUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 描述：抽象意图处理类
 *
 * <AUTHOR> zhumaoxian  2025/6/21 9:23
 */
@Slf4j
public abstract class AbstractIntentionHandle implements InitializingBean {

    /**
     * 处理策略
     */
    protected static final Map<AssistantEnum, AbstractIntentionHandle> HANDLE_MAP = new ConcurrentHashMap<>();

    private static SourceChannelsProperties sourceChannelsProperties;

    @PostConstruct
    public void init() {
        sourceChannelsProperties = SpringUtil.getBean(SourceChannelsProperties.class);
    }

    /**
     * 获取处理策略
     *
     * @param assistantEnum
     * @return
     */
    public static AbstractIntentionHandle getHandle(AssistantEnum assistantEnum) {
        return HANDLE_MAP.get(assistantEnum);
    }

    public static DialogueIntentionVO intentionHandle(DialogueIntentionVO intentionVO, String channel) {
        String assistantCode = sourceChannelsProperties.getCode(channel);
        AssistantEnum assistantEnum = AssistantEnum.getByCode(assistantCode);
        AbstractIntentionHandle handle = HANDLE_MAP.get(assistantEnum);
        if (handle != null) {
            return handle.intentionHandle(intentionVO);
        }
        return intentionVO;
    }

    /**
     * 意图处理
     *
     * @param intentionVO 意图结果
     * @return 处理结果
     */
    public abstract DialogueIntentionVO intentionHandle(DialogueIntentionVO intentionVO);

}
