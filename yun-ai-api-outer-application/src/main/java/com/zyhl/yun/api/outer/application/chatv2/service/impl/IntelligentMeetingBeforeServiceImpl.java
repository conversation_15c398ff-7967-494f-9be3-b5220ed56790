package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import static com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig.ARGUMENT_AI_MEETING_PLACE_LIST;
import static com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig.ARGUMENT_AI_MEETING_RECIPIENT_LIST;
import static com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig.ARGUMENT_AI_MEETING_TITLE_LIST;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.yun.api.outer.application.assembler.ChatFlowResultAssembler;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.event.completeimpl.MeetingMailChatCallbackEvent;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.service.IntelligentMeetingBeforeService;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueFlowResultVO;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddFlowStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.IntentEntityVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 智能体对话-智能会议前置-服务接口
 *
 * <AUTHOR>
 * @date 2025-06-03 15:00
 */
@Slf4j
@Service
public class IntelligentMeetingBeforeServiceImpl implements IntelligentMeetingBeforeService {

	@Resource
	private ChatFlowResultAssembler chatFlowResultAssembler;

	@Resource
	private ChatTextToolBusinessConfig chatTextToolBusinessConfig;

	@Resource
	private DataSaveService dataSaveService;

	@Resource
	private TextModelExternalService textModelExternalService;

	@Resource
	private MeetingMailChatCallbackEvent meetingMailChatCallbackEvent;

	@Resource
	private ModelProperties modelProperties;

	/**
	 * 创建线程池
	 */
	private final static int corePoolSize = 20;
	private final static int maxPoolSize = 40;
	private final static long keepAliveTime = 60;
	private final static int queueSize = 100000;
	private final static ThreadPoolExecutor pool = new ThreadPoolExecutor(corePoolSize, maxPoolSize, keepAliveTime,
			TimeUnit.SECONDS, new ArrayBlockingQueue<>(queueSize));

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		DialogueIntentionVO.IntentionInfo mainIntention = DialogueIntentionVO
				.getMainIntention(handleDTO.getIntentionVO());

		StringBuilder meetingRoom = new StringBuilder();
		// 从意图中的arguments中获取邮件信息
		MailInfoVO mailInfo = extractMailInfoFromArguments(mainIntention, meetingRoom);

		// 判断是被到的邮件信息是否都有值
		if (StrUtil.isEmpty(mailInfo.getTitle()) || CollUtil.isEmpty(mailInfo.getRecipientList())) {
			// 没有title直接返回 || 没有收件人直接返回
			log.info("没有title直接返回或者没有收件人直接返回");
			return true;
		}
		runThreadMeeting(handleDTO, mainIntention, mailInfo, meetingRoom.toString());
		return false;
	}

	private MailInfoVO extractMailInfoFromArguments(DialogueIntentionVO.IntentionInfo mainIntention,
			StringBuilder meetingRoom) {
		MailInfoVO mailInfo = MailInfoVO.builder().build();
		mainIntention.getArgumentMap().forEach((k, v) -> {
			if (ARGUMENT_AI_MEETING_TITLE_LIST.equals(k)) {
				// 标题只取第一个
				if (CollUtil.isNotEmpty(v)) {
					mailInfo.setTitle(v.get(0));
				}
			}
			if (ARGUMENT_AI_MEETING_PLACE_LIST.equals(k)) {
				// 地点用逗号分隔组装
				if (CollUtil.isNotEmpty(v)) {
					meetingRoom.append(String.join(",", v));
				}
			}
			if (ARGUMENT_AI_MEETING_RECIPIENT_LIST.equals(k)) {
				// 参与人用逗号分隔组装
				if (CollUtil.isNotEmpty(v)) {
					mailInfo.setRecipientList(v);
				}
			}
		});
		return mailInfo;
	}

	private void runThreadMeeting(ChatAddHandleDTO handleDTO, DialogueIntentionVO.IntentionInfo mainIntention,
			MailInfoVO mailInfo, String meetingRoom) {
		// 日志map
		final ChatTextToolBusinessConfig.IntelligentMeeting intelligentMeeting = chatTextToolBusinessConfig
				.getIntelligentMeeting();
		final Map<String, String> logMap = LogCommonUtils.getCopyOfContextMap();
		// 获取主线程的ThreadLocal信息，并且异步调用前，将父线程中的请求信息绑定给子线程
		RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo = RequestContextHolder
				.getThreadLocalInfoAndBindingAttributes();
		// 聊天添加响应对象
		ChatAddRespVO respVO = handleDTO.getRespVO();
		respVO.setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());

		// 入库hbase结果列表-大模型之前
		List<DialogueFlowResult> beforeOutputList = new ArrayList<>();
		// 入库hbase结果列表-大模型之后
		List<DialogueFlowResult> afterOutputList = new ArrayList<>();
		// 设置会议室提取实体

		String roomName = CharSequenceUtil.isNotEmpty(meetingRoom) ? meetingRoom : intelligentMeeting.getRoomName();
		// 判断意图的地点实体 做拼接 默认使用配置项的会议室名称
		String meetingRoomTip = String.format(intelligentMeeting.getMeetingRoomTip(), roomName);
		// 即将进行六步操作 start
		// 步骤序号
		// 1，确认参会人员
		addThoughtChain(handleDTO, respVO, beforeOutputList, 0, intelligentMeeting.getMeetingMemberTitle(),
				intelligentMeeting.getMeetingMemberTip());
		// 多线程执行步骤：2~4
		pool.execute(() -> {
			try {
				// 日志map
				LogCommonUtils.initLogMDC(logMap);
				// 把主线程ThreadLocal信息set到子线程
				RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);
				// 里面有3步 如果有新增 序号需要同步修改
				meetingThoughtChain(handleDTO, respVO, beforeOutputList, intelligentMeeting, mailInfo, meetingRoomTip);

				// （第5步是大模型）6，返回邮件信息 最后一步一定要返回FlowResultTypeEnum.MAIL
				mailInfo.setTitle(intelligentMeeting.getTitlePrefix() + mailInfo.getTitle());
				DialogueFlowResultVO flowResultVO = new DialogueFlowResultVO(0, FlowResultTypeEnum.MAIL, "", "");
				flowResultVO.setMailInfo(mailInfo);
				respVO.setFlowResult(flowResultVO);
				DialogueFlowResult flowResult = chatFlowResultAssembler.getFlowResult(respVO.getFlowResult());
				afterOutputList.add(flowResult);

				/**
				 * 追加文案到大模型输出之前
				 */
				StringBuilder appendBeforeOutContent = new StringBuilder();
				appendBeforeOutContent.append(String.format(intelligentMeeting.getRecipientTitie(),
						StringUtils.join(mailInfo.getRecipientList(), "、")));
				appendBeforeOutContent.append(String.format(intelligentMeeting.getSubjectTitie(), mailInfo.getTitle()));
				appendBeforeOutContent.append(intelligentMeeting.getContentTitie());
				DialogueFlowResultVO firstModelFlowResultVO = new DialogueFlowResultVO(beforeOutputList.size(),
						FlowResultTypeEnum.TEXT_MODEL, intelligentMeeting.getAutoGenMailContentTitle(),
						appendBeforeOutContent.toString());
				firstModelFlowResultVO.setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());
				respVO.setFlowResult(firstModelFlowResultVO);
				handleDTO.getSseEmitterOperate().send(BaseResult.success(respVO));

				/**
				 * 流式对话开始
				 */
				SseEventListener event = new SseEventListener(handleDTO, null);
				event.setModelCode(chatTextToolBusinessConfig.getIntelligentMeeting().getMeetingMailModelCode());
				// 设置输出之前的文案
				event.setBeforeOutputList(beforeOutputList);
				// 设置输出之后的文案
				event.setAfterOutputList(afterOutputList);
				event.setAppendBeforeOutContent(appendBeforeOutContent.toString());
				event.setCallBackTitle(intelligentMeeting.getAutoGenMailContentTitle());
				// 文本模型
				Integer maxLength = modelProperties.getMaxLength(handleDTO.getAssistantEnum(),
						handleDTO.getBusinessType(), event.getModelCode());
				TextModelTextReqDTO reqDTO = event.getTextDto().toTextReqDTO(maxLength);
				// 设置不联网
				reqDTO.setEnableForceNetworkSearch(false);
				// 组装提示词+query
				String queryContent = chatTextToolBusinessConfig.getIntelligentMeeting().getMeetingMailPrompt()
						.replace("{query}", handleDTO.getInputInfoDTO().getDialogue());
				TextModelMessageDTO msgDTO = new TextModelMessageDTO(TextModelRoleEnum.USER.getName(), queryContent);
				reqDTO.setMessageDtoList(Collections.singletonList(msgDTO));
				event.setHandleDTO(handleDTO);
				if (CollUtil.isNotEmpty(beforeOutputList)) {
					// 设置当前的索引，下一次加1
					event.setSendIndex(beforeOutputList.size());
				}
				// 设置回调处理事件
				event.setCompleteCallbackEvent(meetingMailChatCallbackEvent);

				log.info("【特殊场景智能体文本对话036_036019】指定文本模型对话 model:{}, reqDTO:{}", event.getModelCode(),
						JSONUtil.toJsonStr(reqDTO));
				textModelExternalService.streamDialogue(event.getModelCode(), reqDTO, event);

			} catch (Exception e) {
				YunAiBusinessException ex = new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
				log.error("AiMemoryAlbum execute dialogueId:{}, error:", handleDTO.getDialogueId(), e);
				if (e instanceof YunAiBusinessException) {
					ex = (YunAiBusinessException) e;
				}
				handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.error(ex.getCode(), ex.getMessage()));
			}
		});
	}

	private void meetingThoughtChain(ChatAddHandleDTO handleDTO, ChatAddRespVO respVO,
			List<DialogueFlowResult> outputList, ChatTextToolBusinessConfig.IntelligentMeeting intelligentMeeting,
			MailInfoVO mailInfo, String meetingRoomTip) {
		// 2，准备会议室（使用替换的会议室编号）
		addThoughtChain(handleDTO, respVO, outputList, 1, intelligentMeeting.getMeetingRoomTitle(), meetingRoomTip);
		// 3，制定会议议程
		addThoughtChain(handleDTO, respVO, outputList, 2, intelligentMeeting.getMeetingAgendaTitle(),
				String.format(intelligentMeeting.getMeetingAgendaTip(), mailInfo.getTitle()));
		// 4，编写会议通知邮件
		addThoughtChain(handleDTO, respVO, outputList, 3, intelligentMeeting.getMeetingNoticeTitle(),
				intelligentMeeting.getMeetingNoticeTip());
	}

	private void addThoughtChain(ChatAddHandleDTO handleDTO, ChatAddRespVO respVO, List<DialogueFlowResult> outputList,
			int number, String title, String tip) {
		respVO.setFlowResult(new DialogueFlowResultVO(number, FlowResultTypeEnum.REASONING_RESULT, title, tip));
		outputList.add(chatFlowResultAssembler.getFlowResult(respVO.getFlowResult()));
		handleDTO.getSseEmitterOperate().send(BaseResult.success(respVO));
	}

	/**
	 * 合并entityList
	 *
	 * @param entityList 实体列表
	 * @return 实体识别结果VO
	 */
	public static IntentEntityVO mergeLists(List<IntentEntityVO> entityList) {
		IntentEntityVO mergedEntity = new IntentEntityVO();
		mergedEntity.setRecipientList(new ArrayList<>());
		mergedEntity.setTitleList(new ArrayList<>());
		mergedEntity.setEmailAddressList(new ArrayList<>());

		entityList.forEach(entityVO -> {
			mergedEntity.getRecipientList().addAll(entityVO.getRecipientList());
			mergedEntity.getTitleList().addAll(entityVO.getTitleList());
			mergedEntity.getEmailAddressList().addAll(entityVO.getEmailAddressList());
		});

		return mergedEntity;
	}

}
