package com.zyhl.yun.api.outer.application.service.chat;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.domain.entity.DialogueIntentionEntity;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;

/**
 * 意图识别相关接口
 *
 * <AUTHOR>
 * @date 2024/4/9 17:04
 */
public interface DialogueIntentionService {

	/**
	 * 获取意图编码
	 *
	 * @param vo 对话意图响应VO
	 * @return 意图编码
	 */
	String getIntentionCode(DialogueIntentionVO vo);

	/**
	 * 获取对话意图响应VO
	 *
	 * @param channel         渠道ID
	 * @param sessionId       会话ID
	 * @param dialogueId      对话ID
	 * @param userId          用户ID
	 * @param currentDialogue 当前对话
	 * @param enableAiSearch 是否开启AI搜索
	 * @return 对话意图响应VO
	 */
	DialogueIntentionVO getDialogueIntentionVO(String channel, String sessionId, String dialogueId, String userId,
			DialogueIntentionEntity.DialogueInfo currentDialogue, Boolean enableAiSearch);

	/**
	 * 用户输入的对话内容进行意图识别
	 *
	 * @param params 参数
	 * @return 对话意图对象
	 */
	DialogueIntentionVO getIntentionVO(ChatAddInnerDTO params);

	/**
	 * 用户输入的对话内容进行意图识别V2
	 *
	 * @param handleDTO 参数
	 * @return 对话意图对象
	 */
	DialogueIntentionVO getIntentionVOV2(ChatAddHandleDTO handleDTO);

	/**
	 * 处理多搜索意图合并（根据配置决定是否合并）
	 * 
	 * @param intentionVO   对话接口内部数据传输对象
	 * @param intentionMain 主意图编码
	 * @return 对话意图VO
	 */
	DialogueIntentionVO handleMultipleSearchIntention(DialogueIntentionVO intentionVO, String intentionMain);
}
