package com.zyhl.yun.api.outer.application.service.knowledge;

import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeLabelAddReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeLabelDeleteReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeLabelListReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeLabelSortReqDTO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowLedgeLabelListVO;

import java.util.List;

/**
 * 知识库标签
 *
 * <AUTHOR>
 */
public interface UserKnowledgeLabelService {

    /**
     * 新增和更新知识标签
     *
     * @param dto 新增和更新知识标签请求dto
     * @return id
     */
    Long save(KnowledgeLabelAddReqDTO dto);

    /**
     * 删除知识标签
     *
     * @param dto 入参
     */
    void delete(KnowledgeLabelDeleteReqDTO dto);

    /**
     * 获取知识标签列表
     *
     * @param dto 入参
     * @return 列表
     */
    List<KnowLedgeLabelListVO> list(KnowledgeLabelListReqDTO dto);

    /**
     * 排序知识标签
     *
     * @param dto 排序请求dto
     */
    void sort(KnowledgeLabelSortReqDTO dto);
}
