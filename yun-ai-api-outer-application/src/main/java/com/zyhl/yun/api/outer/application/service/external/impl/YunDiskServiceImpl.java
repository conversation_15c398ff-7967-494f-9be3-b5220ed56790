package com.zyhl.yun.api.outer.application.service.external.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.YunDiskClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.FileGetContentReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.AIFileVO;
import com.zyhl.hcy.yun.ai.common.platform.third.dto.GetUserInfoByPhoneNumberRespDTO;
import com.zyhl.yun.api.outer.application.dto.YunDiskReqDTO;
import com.zyhl.yun.api.outer.application.service.external.YunDiskService;
import com.zyhl.yun.api.outer.application.vo.YunDiskContentVO;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Classname YunDiskServiceImpl
 * @Description 云盘平台接口实现
 * @Date 2024/3/19 14:40
 */
@Service
@Slf4j
public class YunDiskServiceImpl implements YunDiskService {

	@Resource
	private YunDiskClient yunDiskClient;

	/**
	 * @param dto（fileId 文件id）
	 * 
	 * @return
	 */
	@Override
	public YunDiskContentVO getYunDiskContent(YunDiskReqDTO dto) {
		GetUserInfoByPhoneNumberRespDTO userInfo = new GetUserInfoByPhoneNumberRespDTO();
		userInfo.setUserDomainId(Long.valueOf(RequestContextHolder.getUserId()));
		String fileId = dto.getFileId();
		String imageThumbnailStyle = dto.getImageThumbnailStyle();
		List<String> thumbnailStyleList = null;
		if (CharSequenceUtil.isNotBlank(imageThumbnailStyle)) {
			thumbnailStyleList = new ArrayList<>();
			thumbnailStyleList.add(imageThumbnailStyle);
		}
		FileGetContentReqDTO reqDTO = new FileGetContentReqDTO(fileId, true, thumbnailStyleList, userInfo);
		AIFileVO vo = yunDiskClient.getFileContent(reqDTO);
		if (null != vo) {
			return new YunDiskContentVO(fileId, vo.getFileName(), vo.getFileSize(), vo.getFileSuffix(), vo.getThumbnailUrl(), vo.getContent());
		}
		return null;
	}
}
