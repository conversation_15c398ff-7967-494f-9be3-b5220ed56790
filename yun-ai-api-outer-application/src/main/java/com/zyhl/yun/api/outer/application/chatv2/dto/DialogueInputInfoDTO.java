package com.zyhl.yun.api.outer.application.chatv2.dto;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zyhl.yun.api.outer.application.dto.DialogueSortInfoDTO;
import com.zyhl.yun.api.outer.enums.chat.DialogueCommandTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.TalkTypeEnum;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

import static com.zyhl.yun.api.outer.constants.Const.NUM_16;

/**
 * 助手对话输入信息
 *
 * <AUTHOR> zhumaoxian  2025/4/12 9:41
 */
@Data
public class DialogueInputInfoDTO {

    /**
     * 对话内容
     */
    private String dialogue;

    /**
     * 提示词
     */
    private String prompt;

    /**
     * 对话输入时间，时间统一格式为RFC 3339格式(东八区)
     * 注：2019-10-12T14:20:50.52+08:00
     */
    private String inputTime;

    /**
     * 意图指令
     */
    private DialogueIntentionDTO command;

    /**
     * 命令类型
     * 1--普通命令（默认）
     * 2--自动命令
     *
     * @see com.zyhl.yun.api.outer.enums.chat.DialogueCommandTypeEnum
     */
    private int commandType = DialogueCommandTypeEnum.ORDINARY.getType();

    /**
     * 对话附件
     */
    private DialogueAttachmentDTO attachment = new DialogueAttachmentDTO();

    /**
     * 对话输入的工具设置
     */
    private DialogueToolSettingDTO toolSetting;


    /**
     * 是否强制大模型的联网搜索，默认值为false
     */
    private boolean enableForceNetworkSearch;

    /**
     * 是否强制大模型回答，默认值为false
     * true时不走干预库和意图识别，直接大模型对话
     */
    private boolean enableForceLlm;

    /**
     * 是否启动知识库+联网搜索回答，默认fasle
     */
    private boolean enableKnowledgeAndNetworkSearch;

    /**
     * 是否走云盘AI全网搜，默认值为false
     * 云盘定制功能
     */
    private boolean enableAllNetworkSearch;

    /**
     * 否走云邮AI搜索，默认值为false
     * 云邮助手定制功能
     */
    private boolean enableAiSearch;

    /**
     * 是否走云邮AI编辑，默认值为false
     */
    private boolean enableAiEdite;
    
    /**
     * 重新生成标识
     */
    private boolean enableRegenerate;

    /**
     * 终端的版本信息
     */
    private DialogueVersionInfoDTO versionInfo;

    /**
     * json格式
     */
    private String extInfo;

    /**
     * 业务场景标识
     */
    private String sceneTag;

    /**
     * 排序信息
     */
    private DialogueSortInfoDTO sortInfo;
    
    /**
     * 对话类型 
	 *	0--对话(默认)
	 *	1--智囊(暂时不测试这种场景)
	 *	2--任务对话
     */
    private Integer dialogueType = TalkTypeEnum.DIALOG.getType();

    /**
     * 是否获取更多的全网搜结果，打开这个开关时，需要把上一次对话的对话ID一起带过来
     */
    private boolean enableGetMoreAllNetworkSearch;

    public void setCommandCode(String command) {
        if (this.command == null) {
            this.command = new DialogueIntentionDTO(command);
            return;
        }

        this.command.setCommand(command);
    }

    public Map<String, Object> getExtInfoMap() {
        if (this.extInfo == null || this.extInfo.isEmpty()) {
            // 或者返回一个空的Map
            return new HashMap<>(NUM_16);
        }

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(this.extInfo, new TypeReference<Map<String, Object>>() {
            });
        } catch (JsonProcessingException e) {
            return new HashMap<>(NUM_16);
        }
    }
}