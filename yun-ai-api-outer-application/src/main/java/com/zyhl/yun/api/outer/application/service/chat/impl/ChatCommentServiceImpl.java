package com.zyhl.yun.api.outer.application.service.chat.impl;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.enums.BaseResultCodeEnum;
import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.ChatCommentDtoConvertor;
import com.zyhl.yun.api.outer.application.dto.ChatCommentDTO;
import com.zyhl.yun.api.outer.application.service.chat.ChatCommentService;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.entity.ChatCommentEntity;
import com.zyhl.yun.api.outer.domain.vo.ChatCommentGetResult;
import com.zyhl.yun.api.outer.repository.AlgorithmChatCommentRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 对话结果评价Service实现类
 *
 * <AUTHOR>
 * @version 2024年02月28日 15:31
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChatCommentServiceImpl implements ChatCommentService {


    private final ChatCommentDtoConvertor chatCommentDTOConvertor;

    private final AlgorithmChatCommentRepository algorithmChatCommentRepository;


    @Resource
    private AlgorithmChatContentRepository algorithmChatContentRepository;

    @Override
    public boolean add(@NotNull ChatCommentDTO dto) {
        // 先查对话并校验
        AlgorithmChatContentEntity contentEntity = algorithmChatContentRepository.getById(dto.getDialogueId());
        if (contentEntity == null) {
            log.info("【评论对话】对话不存在");
            throw new YunAiBusinessException(BaseResultCodeEnum.ERROR_PARAMS);
        }
        if (!contentEntity.getSessionId().equals(dto.getSessionId())) {
            log.info("【评论对话】对话id和会话id不匹配");
            throw new YunAiBusinessException(BaseResultCodeEnum.ERROR_PARAMS);
        }
        if (!dto.getUserId().equals(contentEntity.getUserId())) {
            log.info("【评论对话】对话id与用户id不匹配");
            throw new YunAiBusinessException(BaseResultCodeEnum.ERROR_PARAMS);
        }

        // 对象转换
        ChatCommentEntity entity = chatCommentDTOConvertor.toEntity(dto);
        entity.setModelType(contentEntity.getModelType());

        // 保存
        return algorithmChatCommentRepository.save(entity);
    }

    @Override
    public List<ChatCommentGetResult> get(@NotNull ChatCommentDTO dto) {
        ChatCommentEntity entity = chatCommentDTOConvertor.toEntity(dto);
        return algorithmChatCommentRepository.get(entity);
    }


}
