package com.zyhl.yun.api.outer.application.chatv2.service;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;

/**
 * 会话信息操作
 *
 * <AUTHOR>
 */
public interface ChatMessageService {

    /**
     * 保存会话信息：
     * 1、如果有会话id，则更新数据
     * 2、如果没有会话id，则新增数据
     *
     * @param handleDTO 用户输入对象
     * @return 会话id
     */
    long save(ChatAddHandleDTO handleDTO);

    /**
     * 保存会话信息：
     * 1、innerDTO.asyncSaveMessage： true，则新增数据
     * 2、innerDTO.asyncSaveMessage： false，则更新数据
     *
     * @param handleDTO 用户输入对象
     * @return long 会话id
     * @Author: WeiJingKun
     */
    long saveAll(ChatAddHandleDTO handleDTO);

    /**
     * 判断会话是否存在
     *
     * @param id     会话id
     * @param userId 用户id
     * @return boolean
     */
    boolean exist(String id, String userId);
}
