package com.zyhl.yun.api.outer.application.dto;


import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 基础DTO
 *
 * @Author: WeiJingKun
 */
@Data
@Slf4j
public class BaseDTO {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 检查登录的userId
     *
     * @return java.lang.String
     * @Author: WeiJingKun
     */
    public void checkTokenUserId() {
        if (CharSequenceUtil.isNotEmpty(RequestContextHolder.getUserId())) {
            this.userId = RequestContextHolder.getUserId();
        }
        if (CharSequenceUtil.isEmpty(this.userId)) {
            log.error("用户ID不能为空");
            // 如果UserInformationContextHolder中的userId无值，且传入的userId也无值，则抛出异常
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_USERID);
        }
    }

    /**
     * 校验用户id
     *
     * @return
     */
    public AbstractResultCode checkUserId() {
        if (CharSequenceUtil.isNotEmpty(RequestContextHolder.getUserId())) {
            this.userId = RequestContextHolder.getUserId();
        }
        if (CharSequenceUtil.isEmpty(this.userId)) {
            log.info("用户ID为空");
            return ResultCodeEnum.ERROR_INVALID_USERID;
        }
        return null;
    }

}
