package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueIntentionDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.application.service.chat.DialogueIntentionService;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * AI会议纪要
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TextToolAiMeetingHandlerImpl extends AbstractChatAddV2Handler {

	/**
	 * 当前执行顺序
	 */
	private final ExecuteSort thisExecuteSort = ExecuteSort.TEXT_TOOL_AI_MEETING_MINUTES;

	/**
	 * AI会议纪要意图
	 */
	private static final String THIS_MAIN_INTENTION = DialogueIntentionEnum.TEXT_TOOL.getCode();
	private static final String THIS_SUB_INTENTION = DialogueIntentionSubEnum.AI_GENERATE_MEETING_MINUTES.getCode();

	@Resource
	private DialogueIntentionService dialogueIntentionService;
	@Resource
	private TextModelDocSseHandlerImpl textModelDocSseHandlerImpl;
	@Resource
	private TextModelTextSseHandlerImpl textModelTextSseHandlerImpl;
	@Resource
	private ChatTextToolBusinessConfig chatTextToolBusinessConfig;

	@Override
	public void afterPropertiesSet() throws Exception {
		// 支持的业务初始化
		List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        this.setBusinessTypes(thisBusinessTypes);
	}

	@Override
	public int order() {
		return thisExecuteSort.getSort();
	}

	@Override
	public boolean execute(ChatAddHandleDTO handleDTO) {

		if (StringUtils.isEmpty(handleDTO.getReqDTO().getDialogueInput().getDialogue())
				&& !handleDTO.isReqResourceDocSse()) {
			// 对话内容空&&文档资源为空，不执行
			return false;
		}

		DialogueIntentionDTO command = handleDTO.getReqDTO().getDialogueInput().getCommand();
		if (null != command && DialogueIntentionEnum.isTextToolIntention(command.getCommand())
				&& THIS_SUB_INTENTION.equals(command.getSubCommand())) {
			// 判断入参是ai会议纪要意图（主意图+子意图联合判断）
			return true;
		}

		if (null != handleDTO.getIntentionVO()
				&& CollUtil.isNotEmpty(handleDTO.getIntentionVO().getIntentionInfoList())) {
			IntentionInfo mainIntention = DialogueIntentionVO.getMainIntention(handleDTO.getIntentionVO());
			// 判断意图识别是ai会议纪要意图（主意图+子意图联合判断+参数不为空才是执行意图）
			return null != mainIntention && DialogueIntentionEnum.isTextToolIntention(mainIntention.getIntention())
					&& THIS_SUB_INTENTION.equals(mainIntention.getSubIntention())
					&& CollUtil.isNotEmpty(mainIntention.getArgumentMap());
		}
		return false;
	}

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		log.info("进入{}", thisExecuteSort.getDesc());

		Map<String, List<String>> argumentMap = null;
		IntentionInfo mainIntention = null;

		mainIntention = DialogueIntentionVO.getMainIntention(handleDTO.getIntentionVO());

		if (null == mainIntention) {
			// 意图识别后主意图还是空，需要 new 对象
			mainIntention = new IntentionInfo();
		}

		// 意图识别后，输出前端：强制重置指定AI会议纪要意图
		mainIntention.setIntention(THIS_MAIN_INTENTION);
		mainIntention.setSubIntention(THIS_SUB_INTENTION);
		argumentMap = mainIntention.getArgumentMap();

		if (StringUtils.isEmpty(handleDTO.getReqDTO().getDialogueInput().getPrompt())) {
			// 空则指定提示词
			handleDTO.getReqDTO().getDialogueInput()
					.setPrompt(chatTextToolBusinessConfig.getAiMeetingMinutes().getPrompt());
		}

		// 设置输出意图及参数信息
		handleDTO.getRespVO().setOutputCommandVO(mainIntention);
		handleDTO.getRespVO().getOutputCommand().setArgumentMap(argumentMap);

		// 强制设置不需要联网
		handleDTO.getReqDTO().getDialogueInput().setEnableForceNetworkSearch(false);

		if (handleDTO.isReqResourceDocSse()) {
			// 执行文档大模型流式对话
			textModelDocSseHandlerImpl.run(handleDTO);
		} else {
			// 执行普通文本大模型流式对话
			textModelTextSseHandlerImpl.run(handleDTO);
		}
		return false;
	}

}
