package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * Knowledge Invite Exit Req DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-04-17 00:31:30
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
public class KnowledgeInviteExitReqDTO extends BaseChannelDTO implements Serializable {
    /**
     * 知识库
     */
    @NotBlank(message = "知识库Id不能为空")
    private String baseId;
}
