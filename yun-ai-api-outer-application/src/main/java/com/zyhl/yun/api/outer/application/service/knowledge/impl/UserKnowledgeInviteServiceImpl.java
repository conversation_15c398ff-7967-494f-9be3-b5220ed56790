package com.zyhl.yun.api.outer.application.service.knowledge.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.UserKnowledgeInviteConvertor;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeInviteExitReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeInviteListReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.UserKnowledgeInviteService;
import com.zyhl.yun.api.outer.application.vo.knowledge.PersonalKnowledgeShareMemberVO;
import com.zyhl.yun.api.outer.domain.dto.UserInfoDTO;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeInviteEntity;
import com.zyhl.yun.api.outer.domain.vo.common.PageInfoVO;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.UserKnowledgeInviteLevelEnum;
import com.zyhl.yun.api.outer.external.service.UserEtnService;
import com.zyhl.yun.api.outer.repository.UserKnowledgeInviteRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * User Knowledge Invite Service Implementation
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-04-17 00:44:52
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class UserKnowledgeInviteServiceImpl implements UserKnowledgeInviteService {

    private final UserKnowledgeRepository knowledgeRepository;

    private final UserKnowledgeInviteRepository knowledgeInviteRepository;

    private final UserKnowledgeInviteConvertor inviteConvertor;

    private final UserEtnService userEtnService;

    public PageInfoVO<PersonalKnowledgeShareMemberVO> list(KnowledgeInviteListReqDTO dto) {
        // 1、获取知识库
        UserKnowledgeEntity knowledge = knowledgeRepository.selectById(Long.parseLong(dto.getBaseId()));
        if (Objects.isNull(knowledge)) {
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_NOT_EXIST);
        }

        // 2、非知识库创建者，检查是否为知识库成员
        if (!dto.getUserId().equals(knowledge.getUserId())) {
            List<UserKnowledgeInviteEntity> invite = knowledgeInviteRepository.get(Long.parseLong(dto.getBaseId()), dto.getUserId());
            if (CollectionUtil.isEmpty(invite) || Objects.isNull(invite.get(0))) {
                log.warn("【查询个人知识库分享成员列表】【分享成员鉴权】【非该知识库成员，request:{}】", dto);
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_FORBIDDEN);
            }
        }

        // 3、全量查询
        List<UserKnowledgeInviteEntity> allInvites = knowledgeInviteRepository.getAllListByKnowledgeId(Long.parseLong(dto.getBaseId()), true, true, "create_time");

        // 4、手动分页
        PageInfoDTO pageInfo = dto.getPageInfo();
        int inPutCursor;
        try {
            inPutCursor = Integer.parseInt(pageInfo.getPageCursor());
        } catch (NumberFormatException e) {
            log.warn("【查询个人知识库分享成员列表】【分页】【非法分页参数，request:{}】", dto);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        // 5、获取起始页标识
        boolean startPage = PageInfoDTO.isStartPage(pageInfo);
        // 获取排除置顶创建者，操作allInvites的真实起始游标，如果非起始页，需要剔除置顶创建者， pgeCursor - 1
        int startIndex = startPage ? 0 : inPutCursor - 1;

        // 5、分页处理
        List<UserKnowledgeInviteEntity> pagedResult = null;
        if (startPage) {
            // 5.1、如果是起始页，第一个元素是知识库的创建者，成员只需要获取 pageSize() - 1 个
            pagedResult = doPage(allInvites, startIndex, pageInfo.getPageSize() - 1);
            pagedResult.add(0,
                    UserKnowledgeInviteEntity.builder()
                            .userId(knowledge.getUserId())
                            .knowledgeId(knowledge.getId())
                            .level(UserKnowledgeInviteLevelEnum.CREATOR)
                            .build()
            );
        } else if (inPutCursor < 0 || inPutCursor > allInvites.size() + 1){
            pagedResult = Collections.emptyList();
        } else {
            // 5.2、如果不是起始页，那么直接对allInvites分页
            pagedResult = doPage(allInvites, startIndex, pageInfo.getPageSize());
        }

        List<PersonalKnowledgeShareMemberVO> vos = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(pagedResult)) {
            // 6、批量获取分享成员用户头像信息
            List<UserInfoDTO> userInfos = userEtnService.getUserAvatarBatch(
                    pagedResult.stream().map(UserKnowledgeInviteEntity::getUserId)
                            .filter(StringUtils::isNoneBlank)
                            .map(Long::parseLong)
                            .collect(Collectors.toList())
            );

            // 7、根据userId分组分享成员用户信息
            Map<String, UserInfoDTO> groupByUserId = userInfos.stream()
                    .collect(Collectors.toMap(
                            userInfo -> String.valueOf(userInfo.getUserId()),
                            Function.identity(),
                            // 使用先获取到的 UserInfoDTO，忽略新的
                            (existing, replacement) -> existing
                    ));

            // 8、转换为VO
            vos = pagedResult.stream()
                    .map(entity -> inviteConvertor.toPersonalKnowledgeShareMemberVO(entity, groupByUserId.get(entity.getUserId())))
                    .collect(Collectors.toList());
        }

        // 9、封装result
        PageInfoVO<PersonalKnowledgeShareMemberVO> result = new PageInfoVO<>();
        // 获取排除置顶创建者，操作allInvites的真实下页起始游标，
        int realEndIndex = startPage ? pagedResult.size() - 1 : startIndex + pagedResult.size();
        // 响应游标 = realEndIndex + 置顶创建者数量
        result.setNextPageCursor(realEndIndex < 0 || realEndIndex >= allInvites.size() ? null : String.valueOf(realEndIndex + 1));
        result.setTotalCount(Integer.valueOf(0).equals(pageInfo.getNeedTotalCount()) ? null : Long.parseLong(String.valueOf(allInvites.size() + 1)));
        result.setList(vos);
        return result;
    }

    /**
     * 集合分页
     *
     * @param AllList    全量集合
     * @param startIndex 起始游标
     * @param pageSize   当页显示数量
     * @param <T>
     * @return 分页数据
     */
    public static <T> List<T> doPage(List<T> AllList, int startIndex, int pageSize) {
        if (CollectionUtil.isNotEmpty(AllList) && startIndex < AllList.size()) {
            int endIndex = Math.min(startIndex + pageSize, AllList.size());
            List<T> pageInfo = new ArrayList<>(endIndex + 1);
            pageInfo.addAll(AllList.subList(startIndex, endIndex));
            return pageInfo;
        }
        return new ArrayList<>();
    }

    public void exit(KnowledgeInviteExitReqDTO dto) {
        // 删除
        knowledgeInviteRepository.deleteByUserId(Collections.singletonList(Long.parseLong(dto.getBaseId())), dto.getUserId());
    }
}
