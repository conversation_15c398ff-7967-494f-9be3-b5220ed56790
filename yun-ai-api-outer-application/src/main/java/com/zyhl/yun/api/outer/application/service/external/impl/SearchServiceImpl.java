package com.zyhl.yun.api.outer.application.service.external.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.SearchConvertor;
import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.SearchEntityConvertor;
import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.TemplateMatchDtoConvertor;
import com.zyhl.yun.api.outer.application.dto.FaceSearchDTO;
import com.zyhl.yun.api.outer.application.dto.SearchDTO;
import com.zyhl.yun.api.outer.application.dto.TemplateMatchDTO;
import com.zyhl.yun.api.outer.application.service.external.SearchService;
import com.zyhl.yun.api.outer.application.vo.FaceSearchVO;
import com.zyhl.yun.api.outer.application.vo.SearchPageVO;
import com.zyhl.yun.api.outer.domain.entity.TemplateMatchEntity;
import com.zyhl.yun.api.outer.domain.req.FaceSearchEntity;
import com.zyhl.yun.api.outer.domain.req.SearchEntity;
import com.zyhl.yun.api.outer.domain.req.SearchPageEntity;
import com.zyhl.yun.api.outer.domain.resp.FaceSearchRespEntity;
import com.zyhl.yun.api.outer.domain.resp.IntelligentSearchRespEntity;
import com.zyhl.yun.api.outer.domain.resp.TemplateMatchRsqEntity;
import com.zyhl.yun.api.outer.domainservice.IntelligentSearchService;
import com.zyhl.yun.api.outer.domainservice.SearchPageService;
import com.zyhl.yun.api.outer.domainservice.TemplateMatchService;
import com.zyhl.yun.api.outer.external.service.FacialImageSearchService;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 智能搜索
 * <AUTHOR>
 */
@Service
@Slf4j
@RefreshScope
@RequiredArgsConstructor
public class SearchServiceImpl implements SearchService {

    private final SearchConvertor searchDTOConvertor;

    private final SearchEntityConvertor searchEntityConvertor;

    private final IntelligentSearchService intelligentSearchService;

    private final SearchPageService searchPageService;

    private final TemplateMatchDtoConvertor dtoConvertor;

    private final FacialImageSearchService facialImageSearchService;

    private final TemplateMatchService templateMatchService;

    @Value("${templateMatch.threshold}")
    private String threshold;

    /**
     * 智能搜索
     *
     * @param dto
     * @return
     */
    @Override
    public SearchPageVO getSearchPage(SearchDTO dto) {
        SearchEntity entity = searchDTOConvertor.toEntity(dto);

        if (ObjectUtil.isNotEmpty(entity.getPageInfo()) && StringUtils.isNotEmpty(entity.getPageInfo().getPageCursor())) {
            SearchPageEntity search = searchPageService.searchList(entity);
            return searchEntityConvertor.toVO(search);
        }

        return getPageVO(entity);
    }

    private SearchPageVO getPageVO(SearchEntity entity) {
        String userId = RequestContextHolder.getUserId();
        if (StringUtils.isEmpty(userId)) {
            log.info("用户id为空");
            return new SearchPageVO();
        }
        //用户维度 智能搜索获取数据
        BaseResult<IntelligentSearchRespEntity> result = intelligentSearchService.intelligentSearch(entity);
        if (ObjectUtil.isEmpty(result) || ObjectUtil.isEmpty(result.getData()) || CollectionUtils.isEmpty(result.getData().getPhotos())) {
            log.info("查询智能查询接口返回为空，用户id{}", userId);
            return new SearchPageVO();
        }
        //根据智能搜索获取的结果 进行分页处理
        SearchPageEntity search = searchPageService.searchList(result.getData(), entity);
        return searchEntityConvertor.toVO(search);
    }

    /**
     * 人脸搜图
     *
     * @param dto
     * @return
     */
    @Override
    public FaceSearchVO getClassFaceInfo(FaceSearchDTO dto) {

        FaceSearchEntity faceSearchEntity = searchDTOConvertor.faceSearchToEntity(dto);
        BaseResult<FaceSearchRespEntity> classFaceInfo = facialImageSearchService.getClassFaceInfo(faceSearchEntity);
        FaceSearchRespEntity data = classFaceInfo.getData();
        return searchDTOConvertor.faceSearchToVO(data);
    }

    /**
     * 图片标签与影集模板匹配模型
     *
     * @param dto
     * @return
     */
    @Override
    public BaseResult<TemplateMatchRsqEntity> templateMatch(TemplateMatchDTO dto) {
        TemplateMatchEntity entity = dtoConvertor.toEntity(dto);
        entity.setRequestId(IdUtil.simpleUUID());
        entity.setThreshold(Float.parseFloat(threshold));
        return templateMatchService.templateMatch(entity);
    }

}
