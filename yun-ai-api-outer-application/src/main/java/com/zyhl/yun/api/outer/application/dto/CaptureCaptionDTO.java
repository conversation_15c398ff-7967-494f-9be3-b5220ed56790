package com.zyhl.yun.api.outer.application.dto;


import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 */
@Data
public class CaptureCaptionDTO {


    /**
     * 用户id
     */
    @NotEmpty(message = "用户id不能为空")
    private String userId;

    /**
     * 请求 id
     */
    @NotEmpty(message = "请求id不能为空")
    private String requestId;

    /**
     * 文件id
     */
    @NotEmpty(message = "文件id不能为空")
    private String fileId;

    /**
     * 生成文案提示词
     */
    private String styleType;

}
