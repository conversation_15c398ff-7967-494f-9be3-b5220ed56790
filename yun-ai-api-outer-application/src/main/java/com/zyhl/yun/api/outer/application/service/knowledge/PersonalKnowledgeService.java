package com.zyhl.yun.api.outer.application.service.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.*;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeBase;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeCountVO;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeProfilePhotoVO;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeResourceListPageInfoVO;

import java.util.List;

/**
 * 个人知识库
 *
 * <AUTHOR>
 * @date 2025/04/14
 */
public interface PersonalKnowledgeService {

    /**
     * 创建个人知识库
     *
     * @param dto 入参
     * @return 返回
     */
    KnowledgeBase add(PersonalKnowledgeAddReqDTO dto);

    /**
     * 更新个人知识库
     *
     * @param dto 入参
     * @return 返回
     */
    KnowledgeBase update(PersonalKnowledgeUpdateReqDTO dto);

    /**
     * 删除个人知识库
     *
     * @param dto 入参
     */
    void delete(PersonalKnowledgeDeleteReqDTO dto);

    PersonalKnowledgeCountVO count(PersonalKnowledgeCountReqDTO dto);

    /**
     * 根据id列表获取个人知识库详情列表
     *
     * @param dto 入参
     * @return 个人知识库详情列表
     */
    List<KnowledgeBase> getInfoListByIds(KnowledgeBatchGetReqDTO dto);

    /**
     * 查询个人知识库头像列表
     *
     * @param dto 入参
     * @return 个人知识库头像列表
     */
    List<PersonalKnowledgeProfilePhotoVO> profilePhotoList(BaseChannelDTO dto);

    /**
     * 个人知识库列表
     *
     * @param dto 入参
     * @return 返回
     */
    PersonalKnowledgeListPageInfoVO list(PersonalKnowledgeListReqDTO dto);

}