package com.zyhl.yun.api.outer.application.chatv2.service;

import java.io.File;
import java.util.List;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;

/**
 * 对话文件服务接口
 *
 * <AUTHOR>
 */
public interface ChatAddFileService {

	/**
	 * 获取file集合
	 *
	 * @param handleDTO the handle dto
	 * @return {@link List<File>}
	 * <AUTHOR>
	 * @date 2025-4-30 12:07
	 */
	List<File> getAttachmentToFile(ChatAddHandleDTO handleDTO);

}
