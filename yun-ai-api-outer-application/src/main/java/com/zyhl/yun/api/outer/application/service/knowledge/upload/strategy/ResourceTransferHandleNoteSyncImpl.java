package com.zyhl.yun.api.outer.application.service.knowledge.upload.strategy;

import cn.hutool.json.JSONUtil;
import com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum;
import com.zyhl.hcy.yun.ai.common.base.es.domain.datahelper.repository.EsPersonalKnowledgeRepository;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.notethird.req.NoteThirdListClientReq;
import com.zyhl.hcy.yun.ai.common.platform.third.client.notethird.vo.NoteThirdListVO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileCheckReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.ResourceTransferReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.upload.AbstractResourceTransferHandle;
import com.zyhl.yun.api.outer.application.service.mq.KnowledgeDispatchTaskMqService;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeFileImportVO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.vo.ImportNoteInfoVO;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.FileSortTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.*;
import com.zyhl.yun.api.outer.external.NoteExternalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 描述：笔记任务同步
 *
 * <AUTHOR> xieys  2025/6/17 10:19
 */
@Slf4j
@Component
public class ResourceTransferHandleNoteSyncImpl extends AbstractResourceTransferHandle {

    @Resource
    private NoteExternalService noteExternalService;
    @Resource
    private EsPersonalKnowledgeRepository esPersonalKnowledgeRepository;
    @Resource
    private KnowledgeDispatchTaskMqService knowledgeDispatchTaskMqService;
    @Resource(name = "noteSyncThreadPoolExecutor")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    /**
     * PAAS CODE，无具体含义，兼容个人云文档的逻辑
     */
    public static final String DEFAULT_PAAS_CODE = "0";

    @Override
    public void afterPropertiesSet() throws Exception {
        register(KnowledgeResourceTypeEnum.NOTE_SYNC.getCode(), this);
    }

    /**
     * 笔记同步
     *
     * @param dto 请求参数
     * @return
     */
    @Override
    public KnowledgeFileImportVO trans(ResourceTransferReqDTO dto) {
        log.info("【笔记数据同步任务处理】,请求参数为：{}", JSONUtil.toJsonStr(dto));
        // 校验参数
        checkNoteReq(dto);
        String noteId = dto.getNoteList().get(0).getNoteId();
        //通过知识库ID和笔记ID查询algorithm_user_knowledge_file获取个人知识库文件
        UserKnowledgeFileEntity knowledgeFileEntity = userKnowledgeFileRepository.hasNoteSync(noteId, dto.getBaseId());
        //通过笔记ID调用该用户的笔记数据 笔记平台新协议接口5.8.25 新笔记列表V3  获取笔记的详情
        NoteThirdListClientReq reqDTO = new NoteThirdListClientReq();
        reqDTO.setUserId(String.valueOf(dto.getUserId()));
        reqDTO.setNoteIds(Collections.singletonList(noteId));
        NoteThirdListVO note;
        String noteRevision;
        try {
            //调用笔记接口查询信息  目前笔记接口异常或者查询为null会抛出异常出来,查询异常则此次同步失败
            List<NoteThirdListVO> noteList = noteExternalService.getNoteList(reqDTO);
            log.info("【笔记数据同步任务处理】fileId:{},查询到笔记接口返回：{}", noteId, JSONUtil.toJsonStr(noteList));
            if (CollectionUtils.isEmpty(noteList)) {
                throw new YunAiBusinessException(ResultCodeEnum.NOTE_INFO_NOT_EXISTS);
            }
            note = noteList.get(0);
            noteRevision = note.getRevision() == null ? "1" : note.getRevision();
            //笔记查询为空  封装知识库文件对象
            if (Objects.isNull(knowledgeFileEntity)) {
                ImportNoteInfoVO noteInfoVO = dto.getNoteList().get(0);
                UserKnowledgeFileEntity fileEntity = getKnowledgeFileEntity(dto, noteInfoVO, note);
                userKnowledgeFileRepository.add(fileEntity);
                log.info("【笔记数据同步任务处理】触发笔记文档知识库解析,fileId:{},消息param:{}", noteId, fileEntity);
                knowledgeDispatchTaskMqService.sendTaskMqV2(Collections.singletonList(fileEntity));
            } else {
                //不为空 判断版本
                String revision = knowledgeFileEntity.getRevision();
                String recordRevision = revision == null ? "1" : revision;
                if (!noteRevision.equals(recordRevision)) {
                    // 修改file表数据
                    updateKnowledgeNoteFileData(knowledgeFileEntity, noteRevision);
                    // 有变更，异步删除ES分片数据、发送解析请求mq
                    threadPoolTaskExecutor.execute(() -> {
                        dealNoteSyncTask(dto, noteId, knowledgeFileEntity, note);
                    });

                }
            }
        } catch (Exception e) {
            if (e instanceof YunAiBusinessException) {
                throw e;
            }
            log.error("【笔记数据同步任务处理】处理异常，用户id：{}，笔记id：{}", dto.getUserId(), noteId);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_CALL_EXCEPTION);
        }
        KnowledgeFileImportVO importVO = new KnowledgeFileImportVO();
        importVO.setBaseId(String.valueOf(dto.getBaseId()));
        return importVO;
    }

    private void updateKnowledgeNoteFileData(UserKnowledgeFileEntity knowledgeFileEntity, String noteRevision) {
        knowledgeFileEntity.setRevision(noteRevision);
        knowledgeFileEntity.setFileUpdatedAt(new Date());
        knowledgeFileEntity.setUpdateTime(new Date());
        knowledgeFileEntity.setAiStatus(FileProcessStatusEnum.UNPROCESSED.getStatus());
        knowledgeFileEntity.setFinishTime(null);
        knowledgeFileEntity.setResultStepLogs("");
        knowledgeFileEntity.setResultCode("");
        knowledgeFileEntity.setResultMsg("");
        knowledgeFileEntity.setAuditStatus(KnowledgeUploadStatusEnum.NOT_PROCESSED.getStatus());
        knowledgeFileEntity.setAuditTime(null);
        knowledgeFileEntity.setAuditResult("");
        knowledgeFileEntity.setDelFlag(KnowledgeStatusEnum.NORMAL.getStatus());
        userKnowledgeFileRepository.update(knowledgeFileEntity);
    }

    private void dealNoteSyncTask(ResourceTransferReqDTO dto, String noteId, UserKnowledgeFileEntity knowledgeFileEntity, NoteThirdListVO note) {
        List<String> fileIds = new ArrayList<>();
        fileIds.add(noteId);
        Long deleted = esPersonalKnowledgeRepository.batchDeleteDocument(String.valueOf(dto.getUserId()), fileIds);
        log.info("【笔记数据同步任务处理】删除ES分片数据成功,fileId:{}，用户id：{}, 删除数量：{}", noteId, dto.getUserId(), deleted);
        // 触发笔记文档知识库解析
        UserKnowledgeFileEntity fileEntity = new UserKnowledgeFileEntity();
        fileEntity.setId(knowledgeFileEntity.getId());
        fileEntity.setBaseId(dto.getBaseId());
        fileEntity.setFileId(note.getNoteId());
        fileEntity.setUserId(dto.getUserId());
        fileEntity.setOwnerId(dto.getOwnerId());
        fileEntity.setFromResourceType(KnowledgeResourceTypeEnum.NOTE_SYNC.getCode());
        fileEntity.setOwnerType(OwnerTypeEnum.AI.getOwnerValue());
        log.info("【笔记数据同步任务处理】更新笔记,开始触发笔记文档知识库解析，fileId:{},消息param:{}", noteId, fileEntity);
        knowledgeDispatchTaskMqService.sendTaskMqV2(Collections.singletonList(fileEntity));
    }

    @Override
    public void check(KnowledgeFileCheckReqDTO dto) {
        //不需要校验
    }


    private UserKnowledgeFileEntity getKnowledgeFileEntity(ResourceTransferReqDTO dto, ImportNoteInfoVO noteInfoVO, NoteThirdListVO note) {

        UserKnowledgeFileEntity fileEntity = new UserKnowledgeFileEntity();
        fileEntity.setId(uidGenerator.getUID());
        fileEntity.setBaseId(dto.getBaseId());
        fileEntity.setUserId(dto.getUserId());
        fileEntity.setFileId(note.getNoteId());
        fileEntity.setRevision(note.getRevision());
        fileEntity.setSyncStatus(1);
        fileEntity.setParentFileId(dto.getParentFileId());
        fileEntity.setParentFilePath(dto.getParentFilePath());
        fileEntity.setFileName(noteInfoVO.getTitle());
        fileEntity.setFileType(FileTypeEnum.FILE.getKnowledgeFileType());
        fileEntity.setCategory(FileCategoryEnum.OTHERS.getKnowledgeCategory());
        fileEntity.setFromResourceType(KnowledgeResourceTypeEnum.NOTE_SYNC.getCode());
        fileEntity.setFromResource(JSONUtil.toJsonStr(noteInfoVO));
        fileEntity.setAiStatus(FileProcessStatusEnum.UNPROCESSED.getStatus());
        fileEntity.setDelFlag(KnowledgeStatusEnum.NORMAL.getStatus());
        fileEntity.setAuditStatus(FileAuditStatusEnum.UNAUDITED.getStatus());
        fileEntity.setFileType(FileTypeEnum.FILE.getKnowledgeFileType());
        fileEntity.setPaasCode(DEFAULT_PAAS_CODE);
        fileEntity.setOldFileId(note.getNoteId());
        fileEntity.setOwnerId(dto.getUserId());
        fileEntity.setOwnerType(OwnerTypeEnum.AI.getOwnerValue());
        fileEntity.setFileType(FileTypeEnum.FILE.getKnowledgeFileType());
        fileEntity.setCreateTime(new Date());
        fileEntity.setExtension(KnowledgeResourceTypeEnum.NOTE.getExt());
        fileEntity.setSortType(FileSortTypeEnum.NOTE.getCode());
        return fileEntity;
    }


    private void checkNoteReq(ResourceTransferReqDTO dto) {
        if (dto.getBaseId() == null) {
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_ID_NOT_PROVIDE);
        }
        if (dto.getUserId() == null) {
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_USERID);
        }
        List<ImportNoteInfoVO> noteList = dto.getNoteList();
        if (CollectionUtils.isEmpty(noteList)) {
            throw new YunAiBusinessException(ResultCodeEnum.NOTE_ID_IS_NULL);
        }
        if (noteList.size() > 1) {
            throw new YunAiBusinessException(ResultCodeEnum.NOTE_SIZE_ERROR);
        }
    }


}
