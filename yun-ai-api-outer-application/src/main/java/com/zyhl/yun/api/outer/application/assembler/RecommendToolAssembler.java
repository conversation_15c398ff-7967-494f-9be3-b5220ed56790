package com.zyhl.yun.api.outer.application.assembler;

import java.util.List;

import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;

import com.zyhl.yun.api.outer.config.DialogueRecommendProperties.RecommendTool;
import com.zyhl.yun.api.outer.domain.vo.ToolRecommendVO;

/**
 * 推荐工具-类转换器
 * 
 * @Author: liuxuewen
 */
@Mapper(componentModel = "spring", injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public abstract class RecommendToolAssembler {

	public abstract List<ToolRecommendVO> toToolRecommendListVO(List<RecommendTool> textToolRecommendList);

}
