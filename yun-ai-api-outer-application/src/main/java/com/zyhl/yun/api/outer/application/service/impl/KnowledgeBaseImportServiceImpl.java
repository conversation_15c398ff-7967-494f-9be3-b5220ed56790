package com.zyhl.yun.api.outer.application.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.OwnerDriveClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDriveDirReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDriveReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveDirVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveVO;
import com.zyhl.yun.api.outer.application.assembler.PersonalKnowledgeImportTaskAssembler;
import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.DtoConverter;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileBatchImportReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileCheckReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileTaskListReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.ResourceTransferReqDTO;
import com.zyhl.yun.api.outer.application.service.KnowledgeBaseImportService;
import com.zyhl.yun.api.outer.application.service.knowledge.upload.AbstractResourceTransferHandle;
import com.zyhl.yun.api.outer.application.service.mq.KnowledgeTransCategoryTaskMqService;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeFileImportVO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeFileTaskListVO;
import com.zyhl.yun.api.outer.application.vo.knowledge.PersonalKnowledgeImportTaskVO;
import com.zyhl.yun.api.outer.config.KnowledgePersonalProperties;
import com.zyhl.yun.api.outer.domain.entity.PersonalKnowledgeImportTaskEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeUploadEntity;
import com.zyhl.yun.api.outer.domain.vo.ImportNoteInfoVO;
import com.zyhl.yun.api.outer.domainservice.UserKnowledgeDomainService;
import com.zyhl.yun.api.outer.enums.NoteTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.BizTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeUploadStatusEnum;
import com.zyhl.yun.api.outer.repository.AlgorithmUserKnowledgeUploadRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 知识库导入服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class KnowledgeBaseImportServiceImpl implements KnowledgeBaseImportService {

    /**
     * 任务进度相关常量
     */
    private static final int PROGRESS_THRESHOLD_30_SECONDS = 30;
    private static final int PROGRESS_THRESHOLD_60_SECONDS = 60;
    private static final int PROGRESS_50_PERCENT = 50;
    private static final int PROGRESS_80_PERCENT = 80;
    private static final int PROGRESS_90_PERCENT = 90;

    @Resource
    private AlgorithmUserKnowledgeUploadRepository algorithmUserKnowledgeUploadRepository;
    @Resource
    private UserKnowledgeRepository userKnowledgeRepository;
    @Resource
    private DtoConverter dtoConverter;

    @Resource
    protected UidGenerator uidGenerator;
    @Resource
    protected KnowledgeTransCategoryTaskMqService knowledgeTransCategoryTaskMqService;
    @Resource
    private UserKnowledgeDomainService userKnowledgeDomainService;

    @Resource
    private OwnerDriveClient ownerDriveClient;

    @Resource
    private PersonalKnowledgeImportTaskAssembler personalKnowledgeImportTaskAssembler;
    @Resource
    protected KnowledgePersonalProperties knowledgePersonalProperties;
    @Resource
    private UserKnowledgeFileRepository userKnowledgeFileRepository;

    @Override
    public KnowledgeFileImportVO batchImport(KnowledgeFileBatchImportReqDTO reqDTO) {

        ResourceTransferReqDTO resourceTransferReqDTO = dtoConverter.toResourceTransferReqDTO(reqDTO);
        UserKnowledgeEntity userKnowledgeEntity = null;
        if (KnowledgeResourceTypeEnum.isNoteSync(reqDTO.getResourceType())) {
            //目前只支持普通笔记
            ImportNoteInfoVO noteInfoVO = reqDTO.getNoteList().get(0);
            if (noteInfoVO.getNoteType() !=null && !noteInfoVO.getNoteType().equals(NoteTypeEnum.SIMPLE_NOTE.getType())) {
                log.error("笔记同步逻辑目前只支持普通笔记,当前笔记id:{}", noteInfoVO.getNoteId());
                throw new YunAiBusinessException(ResultCodeEnum.NOTE_TYPE_ERROR);
            }
            //笔记同步逻辑 区分其他导入逻辑
            userKnowledgeEntity = userKnowledgeRepository.selectNoteSync(reqDTO.getUserId(), BizTypeEnum.NOTE_SYNC.getCode());
            if (ObjectUtil.isEmpty(userKnowledgeEntity)) {
                List<ImportNoteInfoVO> noteList = reqDTO.getNoteList();
                if (CollUtil.isEmpty(noteList)) {
                    throw new YunAiBusinessException(ResultCodeEnum.NOTE_ID_IS_NULL);
                }
                //新增知识库
                userKnowledgeEntity = userKnowledgeDomainService.createNoteSyncKnowledge();
            }
            resourceTransferReqDTO.setParentFileId(userKnowledgeEntity.getFolderId());
            resourceTransferReqDTO.setParentFilePath(userKnowledgeEntity.getFolderId());
        } else {
            // 查询资源ID
            userKnowledgeEntity = ObjectUtil.isEmpty(reqDTO.getBaseId()) ?
                    userKnowledgeDomainService.createDefaultKnowledge() :
                    userKnowledgeRepository.selectById(Long.valueOf(reqDTO.getBaseId()));
            if (ObjectUtil.isEmpty(userKnowledgeEntity)) {
                throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_NOT_EXIST);
            } else if (!userKnowledgeEntity.getUserId().equals(reqDTO.getUserId())) {
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_FORBIDDEN);
            }
            UserKnowledgeFileEntity fileEntity = null;
            if (ObjectUtil.isNotEmpty(reqDTO.getParentFileId())) {
                fileEntity = userKnowledgeFileRepository.selectByFileId(reqDTO.getUserId(), reqDTO.getParentFileId());
            }
            if (Objects.nonNull(fileEntity)) {
                resourceTransferReqDTO.setParentFileId(reqDTO.getParentFileId());
                resourceTransferReqDTO.setParentFilePath(fileEntity.getParentFilePath() + "/" + reqDTO.getParentFileId());
            } else {
                resourceTransferReqDTO.setParentFileId(userKnowledgeEntity.getFolderId());
                resourceTransferReqDTO.setParentFilePath(userKnowledgeEntity.getFolderId());
            }
        }
        resourceTransferReqDTO.setBaseId(userKnowledgeEntity.getId());
        resourceTransferReqDTO.setOwnerId(userKnowledgeEntity.getOwnerId());
        KnowledgeFileImportVO vo = AbstractResourceTransferHandle.getByCode(reqDTO.getResourceType()).trans(resourceTransferReqDTO);
        vo.setBaseId(String.valueOf(userKnowledgeEntity.getId()));
        return vo;
    }

    @Override
    public boolean batchDelete(String userId, List<String> taskIds) {
        try {
            log.info("知识库导入文件任务批量删除开始: userId={}, taskCount={}", userId, taskIds.size());
            // 将String类型的taskId转换为Long类型
            List<Long> ids = taskIds.stream()
                    .filter(StringUtils::hasText)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(ids)) {
                log.warn("没有有效的任务ID可删除: userId={}", userId);
                return false;
            }
            // 执行逻辑删除并判断是否全部删除成功
            int deletedCount = algorithmUserKnowledgeUploadRepository.deleteByUserIdAndId(userId, ids);
            log.info("知识库导入文件任务批量删除总数 {} 任务id个数 {} 用户id: userId={}", deletedCount, ids.size(),
                    userId);
            // 如果删除的记录数与请求删除的记录数相同，表示全部删除成功
            return deletedCount == ids.size();
        } catch (NumberFormatException e) {
            log.error("任务ID格式无效: userId={}, error={}", userId, e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("删除知识库导入任务时出错: userId={}, error={}", userId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public KnowledgeFileTaskListVO listTasks(KnowledgeFileTaskListReqDTO dto) {
        KnowledgeFileTaskListVO result = new KnowledgeFileTaskListVO();
        String userId = dto.getUserId();
        String baseId = dto.getBaseId();
        if (StrUtil.isBlank(baseId)) {
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_ID_NOT_PROVIDE);
        }
        UserKnowledgeEntity userKnowledgeEntity = userKnowledgeRepository.selectById(Long.valueOf(baseId));
        if (ObjectUtil.isEmpty(userKnowledgeEntity) || !userKnowledgeEntity.getUserId().equals(userId)) {
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_NOT_EXIST);
        }
        try {
            // 1. 查询任务列表
            PageInfo<PersonalKnowledgeImportTaskEntity> tasks = algorithmUserKnowledgeUploadRepository.findByUserIdAndBaseId(userId, baseId, dto.getStatusArray(), dto.getPageInfo());
            // 2. 转换实体列表并设置分页信息
            List<PersonalKnowledgeImportTaskVO> taskVOList = tasks.getList().stream()
                    .map(this::convertToVO)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            // 3. 提示语转换
            if (ObjectUtil.isNotEmpty(taskVOList)) {
                taskVOList.forEach(item -> {
                    String msg = knowledgePersonalProperties.getParseFailedReason().get(item.getErrorCode());
                    if (ObjectUtil.isNotEmpty(msg)) {
                        item.setErrorMessage(msg);
                    }
                });
            }

            return result.setData(dto.getPageInfo(), tasks, taskVOList);
        } catch (Exception e) {
            log.error("查看知识库资源导入任务列表失败: userId={}, baseId={}, error={}",
                    userId, baseId, e.getMessage(), e);
            return createEmptyResult();
        }
    }

    /**
     * 将实体转换为VO
     *
     * @param entity 实体
     * @return VO
     */
    private PersonalKnowledgeImportTaskVO convertToVO(PersonalKnowledgeImportTaskEntity entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        PersonalKnowledgeImportTaskVO vo = personalKnowledgeImportTaskAssembler.toVO(entity);
        // 1.判断创建时间和当前时间大小展示进度，30s内是50%，30-60s是80%，60s以上是90%
        if (entity.getUpdatedAt() != null && entity.getStatus() == 0) {
            try {
                // 解析RFC 3339格式的时间字符串
                Date updateDate = DateUtil.parse(entity.getUpdatedAt(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
                long elapsedSeconds = (System.currentTimeMillis() - updateDate.getTime()) / 1000;
                int progress;

                if (elapsedSeconds <= PROGRESS_THRESHOLD_30_SECONDS) {
                    progress = PROGRESS_50_PERCENT;
                } else if (elapsedSeconds <= PROGRESS_THRESHOLD_60_SECONDS) {
                    progress = PROGRESS_80_PERCENT;
                } else {
                    progress = PROGRESS_90_PERCENT;
                }

                vo.setProgress(progress);
            } catch (Exception e) {
                log.warn("解析创建时间失败: {}", entity.getCreatedAt(), e);
            }
        }

        return vo;
    }

    /**
     * 创建空的结果对象
     *
     * @return 空的结果对象
     */
    private KnowledgeFileTaskListVO createEmptyResult() {
        KnowledgeFileTaskListVO resultVO = new KnowledgeFileTaskListVO();
        resultVO.setList(Collections.emptyList());
        return resultVO;
    }

    @Override
    public PersonalKnowledgeImportTaskVO retryTask(String userId, String taskId) {
        // 1. 获取任务信息,只处理状态为失败的任务
        PersonalKnowledgeImportTaskEntity taskEntity;
        taskEntity = algorithmUserKnowledgeUploadRepository.findById(Long.valueOf(taskId));
        if (ObjectUtil.isEmpty(taskEntity) || !taskEntity.getStatus().equals(KnowledgeUploadStatusEnum.REQ_FAIL.getStatus()) || taskEntity.getDelFlag().equals(KnowledgeStatusEnum.DELETED.getStatus())) {
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_UPLOAD_RETRY_STATUS_ERROR);
        }
        // 2. 验证任务所属用户
        if (!userId.equals(taskEntity.getUserId())) {
            log.error("知识库导入资源任务重试用户信息错误: userId={}, taskId={}, taskUserId={}",
                    userId, taskId, taskEntity.getUserId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_FORBIDDEN);
        }
        // 3. 更新任务状态为未处理
        // 4. 发送MQ消息重新处理任务
        try {
            UserKnowledgeUploadEntity insertEntity = createUploadEntity(taskEntity);
            if (taskEntity.getResourceType() == 0) {
                processResourceTransfer(taskEntity);
            } else {
                checkKnowledgeDriveSize(taskEntity.getUserId());
                knowledgeTransCategoryTaskMqService.sendMq(insertEntity);
            }
            log.info("知识库导入资源任务重试处理成功，发送mq: userId={}, taskId={}", userId, taskId);
        } catch (Exception e) {
            log.error("发送重试任务到MQ失败: userId={}, taskId={}, error={}", userId, taskId, e.getMessage(), e);
            throw new YunAiBusinessException(ResultCodeEnum.MQ_SEND_EXCEPTION);
        }
        // todo
        taskEntity.setUploadStatus(KnowledgeUploadStatusEnum.PROCESSING.getStatus());
        algorithmUserKnowledgeUploadRepository.updateById(taskEntity);
        // 5. 转换为VO返回
        taskEntity = algorithmUserKnowledgeUploadRepository.findById(Long.valueOf(taskId));
        return convertToVO(taskEntity);
    }

    /**
     * 创建上传实体
     *
     * @param taskEntity 任务实体
     * @return 上传实体
     */
    private UserKnowledgeUploadEntity createUploadEntity(PersonalKnowledgeImportTaskEntity taskEntity) {
        UserKnowledgeUploadEntity insertEntity = new UserKnowledgeUploadEntity();
        insertEntity.setId(Long.valueOf(taskEntity.getTaskId()));
        insertEntity.setBaseId(Long.valueOf(taskEntity.getBaseId()));
        insertEntity.setUserId(taskEntity.getUserId());
        insertEntity.setFileId(taskEntity.getFileId());
        insertEntity.setResourceType(taskEntity.getResourceType());
        insertEntity.setResource(taskEntity.getResource());
        insertEntity.setOwnerId(taskEntity.getOwnerId());
        insertEntity.setOwnerType(taskEntity.getOwnerType());
        insertEntity.setUploadStatus(KnowledgeUploadStatusEnum.NOT_PROCESSED.getStatus());
        return insertEntity;
    }


    private void checkKnowledgeDriveSize(String userId) {
        // 获取独立空间
        OwnerDriveReqDTO reqDTO = new OwnerDriveReqDTO();
        reqDTO.setUserId(userId);
        reqDTO.setClientInfo(RequestContextHolder.getClientInfo());
        reqDTO.setAppChannel(RequestContextHolder.getAppChannel());
        OwnerDriveVO vo = ownerDriveClient.getOwnerDrive(reqDTO);
        log.info("获取用户独立空间ID，空间id：{}，状态：{}，总空间：{}，已使用空间：{}", vo.getDriveId(), vo.getStatus(), vo.getTotalSize(), vo.getUsedSize());
        if (vo.getTotalSize().compareTo(vo.getUsedSize()) <= 0) {
            throw new YunAiBusinessException(ResultCodeEnum.LACK_OF_SPACE);
        }
    }

    /**
     * 处理个人云文件重试
     *
     * @param taskEntity 任务实体
     */
    private void processResourceTransfer(PersonalKnowledgeImportTaskEntity taskEntity) {
        ResourceTransferReqDTO reqDTO = new ResourceTransferReqDTO();
        reqDTO.setUserId(taskEntity.getUserId());
        reqDTO.setBaseId(Long.valueOf(taskEntity.getBaseId()));
        if (taskEntity.getFile() != null) {
            reqDTO.setParentFileId(taskEntity.getFile().getTargetParentFileId());
            reqDTO.setParentFilePath(taskEntity.getFile().getTargetParentFilePath());
            reqDTO.setFileList(Collections.singletonList(taskEntity.getFile()));
        }
        reqDTO.setRetryFlag(Boolean.TRUE);
        reqDTO.setUploadIds(Collections.singletonList(Long.valueOf(taskEntity.getTaskId())));
        reqDTO.setResourceType(taskEntity.getResourceType());
        reqDTO.setTaskId(taskEntity.getTaskId());
        AbstractResourceTransferHandle.getByCode(taskEntity.getResourceType()).trans(reqDTO);
    }

    @Override
    public void checkResource(KnowledgeFileCheckReqDTO dto) {
        AbstractResourceTransferHandle.getByCode(dto.getResourceType()).check(dto);
    }

    public OwnerDriveDirVO createCatalog(String userId, String dirName, String parentId, String fileId) {
        // 创建目录
        OwnerDriveDirReqDTO reqDTO = new OwnerDriveDirReqDTO();
        reqDTO.setUserId(userId);
        reqDTO.setName(dirName);
        reqDTO.setParentFileId(parentId);
        reqDTO.setClientInfo("1|127.0.0.1|1|10.2.2|vivo|V1938T|C942102BF43616B31A0B1805FD609DD4|02-00-00-00-00-00|android 9|1080X1920|zh||||000|");
        reqDTO.setAppChannel("10000023");
        log.info("fileId:{}, 开始创建笔记独立空间目录",fileId);
        OwnerDriveDirVO vo = ownerDriveClient.getDirectory(reqDTO);
        log.info("fileId:{}, 完成创建笔记独立空间目录，父目录id：{}，目录id：{}，目录名称：{}",fileId, vo.getParentFileId(), vo.getFileId(), vo.getFileName());
        return vo;
    }


}