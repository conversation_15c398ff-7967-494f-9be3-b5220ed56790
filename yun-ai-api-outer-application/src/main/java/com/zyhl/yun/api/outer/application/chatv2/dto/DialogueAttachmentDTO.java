package com.zyhl.yun.api.outer.application.chatv2.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.domain.valueobject.*;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 描述：对话附件信息
 *
 * <AUTHOR> zhumaoxian  2025/4/12 9:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DialogueAttachmentDTO {

    /**
     * 附件类型,可以是多个，前端传了多少个类型，这里就要填多少个。
     *
     * @see com.zyhl.yun.api.outer.enums.ResourceTypeEnum
     */
    private List<Integer> attachmentTypeList;
    /**
     * AI助手对话ID列表
     */
    private List<String> dialogueIdList;
    /**
     * 云盘个人云文件列表（图片、文档）
     */
    private List<File> fileList;
    /**
     * 邮件列表
     */
    private List<MailInfo> mailList;
    /**
     * 笔记列表
     */
    private List<NoteInfo> noteList;
    /**
     * 个人知识库文件列表
     */
    private List<PersonalKnowledgeResource> knowledgeFileList;

    /**
     * 个人知识库列表
     */
    private List<PersonalKnowledgeBase> knowledgeBaseList;

    public List<Object> resourceList() {
        List<Object> list = new ArrayList<>();
        if (CollUtil.isEmpty(attachmentTypeList)) {
            return list;
        }
        for (Integer type : attachmentTypeList) {
            if (ResourceTypeEnum.isMail(type)) {
                list.addAll(mailList);
            } else if (ResourceTypeEnum.isNote(type)) {
                list.addAll(noteList);
            } else if (ResourceTypeEnum.isImage(type)) {
                list.addAll(fileList);
            } else if (ResourceTypeEnum.isDialogueId(type)) {
                list.addAll(dialogueIdList);
            } else if (ResourceTypeEnum.isDocument(type)) {
                list.addAll(fileList);
            } else if (ResourceTypeEnum.isAttachment(type)) {
                list.addAll(mailList);
            } else if (ResourceTypeEnum.isPersonalKnowledgeFile(type)) {
                list.addAll(knowledgeFileList);
            } else if (ResourceTypeEnum.isPersonalKnowledgeBase(type)) {
                list.addAll(knowledgeBaseList);
            }
        }
        return list;
    }

    public DialogueAttachmentDTO(Integer resourceType, String inResourceId) {
        this.attachmentTypeList = new ArrayList<>();
        this.attachmentTypeList.add(resourceType);
        if (ResourceTypeEnum.isMail(resourceType)) {
            this.mailList = new ArrayList<>();
            mailList.add(new MailInfo(inResourceId));
        } else if (ResourceTypeEnum.isNote(resourceType)) {
            this.noteList = new ArrayList<>();
            noteList.add(new NoteInfo(inResourceId));
        } else if (ResourceTypeEnum.isImage(resourceType)) {
            this.fileList = new ArrayList<>();
            this.fileList.add(new File(inResourceId));
        } else if (ResourceTypeEnum.isDialogueId(resourceType)) {
            this.dialogueIdList = new ArrayList<>();
            this.dialogueIdList.add(inResourceId);
        } else if (ResourceTypeEnum.isDocument(resourceType)) {
            this.fileList = new ArrayList<>();
            this.fileList.add(new File(inResourceId));
        } else if (ResourceTypeEnum.isAttachment(resourceType)) {
            this.mailList = new ArrayList<>();
            this.mailList.add(new MailInfo(inResourceId));
        } else if (ResourceTypeEnum.isPersonalKnowledgeFile(resourceType)) {
            this.knowledgeFileList = new ArrayList<>();
            this.knowledgeFileList.add(new PersonalKnowledgeResource(inResourceId));
        } else if (ResourceTypeEnum.isPersonalKnowledgeBase(resourceType)) {
            this.knowledgeBaseList = new ArrayList<>();
            this.knowledgeBaseList.addAll(JsonUtil.parseArray(inResourceId, new TypeReference<List<PersonalKnowledgeBase>>() {
            }));
        }
    }

}
