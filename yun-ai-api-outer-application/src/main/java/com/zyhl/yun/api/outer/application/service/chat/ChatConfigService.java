package com.zyhl.yun.api.outer.application.service.chat;

import java.util.List;

import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.ChatConfigDTO;
import com.zyhl.yun.api.outer.application.dto.ChatConfigGetDTO;
import com.zyhl.yun.api.outer.domain.vo.ChatConfigV2VO;
import com.zyhl.yun.api.outer.domain.vo.ChatConfigVO;

/**
 * 会话大语言模型设定Service类
 *
 * <AUTHOR>
 * @version 2024年02月28日 15:30
 */

public interface ChatConfigService {
    /**
     * 用户设置会话大语言模型
     *
     * @param dto 入参
     * @return boolean
     */
    boolean config(ChatConfigDTO dto);

    /**
     * 获取启用的文本模型以及用户设置的模型，前端会话设置使用
     *
     * @param dto 入参
     * @return 模型列表
     */
    List<ChatConfigVO> get(ChatConfigGetDTO dto);

    /**
     * v2接口使用，获取用户设置的模型 或者 默认模型
     *
     * @param dto 对话接口入参
     * @return 聊天配置
     */
    ChatConfigV2VO getConfigV2(ChatConfigGetDTO dto);

    /**
     * 【仅限add的v1接口使用】获取用户设置的模型 或者 默认模型
     *
     * @param dto 对话接口入参
     * @return 聊天配置
     */
    ChatConfigVO getUserSetModel(AlgorithmChatAddDTO dto);


}
