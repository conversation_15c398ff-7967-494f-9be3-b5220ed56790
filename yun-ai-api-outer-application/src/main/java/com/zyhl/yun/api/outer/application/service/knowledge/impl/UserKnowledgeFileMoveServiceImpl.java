package com.zyhl.yun.api.outer.application.service.knowledge.impl;

import cn.hutool.core.collection.CollUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveFileVO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileMoveBatchReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.UserKnowledgeFileMoveService;
import com.zyhl.yun.api.outer.application.service.mq.KnowledgeFileMoveTaskMqService;
import com.zyhl.yun.api.outer.config.KnowledgePersonalProperties;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileTaskEntity;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileTaskStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeTaskTypeEnum;
import com.zyhl.yun.api.outer.external.UserDriveExternalService;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileTaskRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 个人知识库文件移动任务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class UserKnowledgeFileMoveServiceImpl implements UserKnowledgeFileMoveService {

    private final UserKnowledgeFileRepository userKnowledgeFileRepository;

    private final UserKnowledgeFileTaskRepository userKnowledgeFileTaskRepository;

    private final KnowledgePersonalProperties knowledgePersonalProperties;

    private final UserDriveExternalService userDriveExternalService;

    private final KnowledgeFileMoveTaskMqService moveTaskMqService;

    private final UserKnowledgeRepository userKnowledgeRepository;

    private static final String ROOT_PATH = "/";

    @Override
    public Long batchMoveAsync(KnowledgeFileMoveBatchReqDTO dto) {
        List<String> resourceIdList = dto.getResourceIdList();
        List<UserKnowledgeFileEntity> list = userKnowledgeFileRepository.selectByFileIds(dto.getUserId(), resourceIdList);
        if (CollUtil.isEmpty(list)) {
            log.info("【个人知识库资源批量移动接口】文件不存在，dto：{}", JsonUtil.toJson(dto));
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_FILE_NOT_EXIST);
        }

        if (list.size() != resourceIdList.size()) {
            log.info("【个人知识库资源批量移动接口】资源ID列表异常，dto：{}", JsonUtil.toJson(dto));
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_FILE_MOVE_RESOURCE_ID_ERROR);
        }

        UserKnowledgeEntity knowledge;
        try {
            knowledge = userKnowledgeRepository.selectByIdAndUserId(Long.valueOf(dto.getBaseId()), dto.getUserId());
            if (knowledge == null) {
                throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_ID_NOT_EXIST);
            }
        } catch (Exception e) {
            log.error("【个人知识库资源批量移动接口】知识库id异常，dto：{}", JsonUtil.toJson(dto), e);
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_ID_NOT_EXIST);
        }

        String path = null;
        if (!ROOT_PATH.equals(dto.getToParentFileId())) {
            try {
                OwnerDriveFileVO fileVo = userDriveExternalService.getFileInfo(dto.getUserId(), dto.getToParentFileId());
                if (fileVo == null || !FileTypeEnum.FOLDER.getYunDiskFileType().equals(fileVo.getType())) {
                    log.info("【个人知识库资源批量移动接口】父目录id不存在：{}", dto.getToParentFileId());
                    throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_FILE_PARENT_NOT_EXIST);
                }
            } catch (Exception e) {
                log.error("【个人知识库资源批量移动接口】父目录id异常：{}", JsonUtil.toJson(dto), e);
                throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_FILE_PARENT_NOT_EXIST);
            }

            Map<String, String> map = userDriveExternalService.batchFileGetPath(dto.getUserId(), Collections.singletonList(dto.getToParentFileId()));
            String idPath = map.getOrDefault(dto.getToParentFileId(), null);
            // 校验 父文件夹移动到其子文件夹情况
            path = getParentFilePath(idPath);
            if (StringUtils.isNotEmpty(idPath)) {
                for (String resourceId : resourceIdList) {
                    if (idPath.contains(resourceId)) {
                        log.info("【个人知识库资源批量移动接口】父目录不能移动到子目录中，dto：{}", JsonUtil.toJson(dto));
                        throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_FILE_MOVE_RESOURCE_FOLDER_ERROR);
                    }
                }
            }
        }

        // 创建移动文件的任务
        UserKnowledgeFileTaskEntity taskEntity = getUserKnowledgeFileTaskEntity(dto, resourceIdList);

        // 移动独立空间数据(只需要处理个人云文档文件)
        List<String> personalFileIdList = list.stream()
                .filter(entity -> entity.getFromResourceType() .equals(KnowledgeResourceTypeEnum.PERSONAL_FILE.getCode()))
                .map(UserKnowledgeFileEntity::getFileId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(personalFileIdList)) {
            try {
                // 移动独立空间数据
                String thirdTaskId = userDriveExternalService.moveByFileIds(dto.getUserId(), personalFileIdList, dto.getToParentFileId());
                taskEntity.setThirdTaskId(thirdTaskId);
            } catch (Exception e) {
                log.error("【个人知识库资源批量移动接口】移动独立空间数据失败，dto：{}，异常信息：", JsonUtil.toJson(dto), e);
                throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_MOVE_TASK_CREATE_ERROR);
            }
        }

        // 保存任务
        userKnowledgeFileTaskRepository.add(taskEntity);

        // 更新知识库文件资源表的父目录ID和父目录路径
        updateParentPath(dto, knowledge, path, list);

        try {
            // 发送移动文件夹MQ事件
            moveTaskMqService.sendMq(taskEntity);
            log.info("【个人知识库资源批量移动接口】发送移动文件夹MQ事件成功，消息content：{}", taskEntity);
        } catch (Exception e) {
            log.error("【个人知识库资源批量移动接口】发送移动文件夹MQ事件失败,消息content:{}", taskEntity, e);
        }
        return taskEntity.getId();
    }

    private UserKnowledgeFileTaskEntity getUserKnowledgeFileTaskEntity(KnowledgeFileMoveBatchReqDTO dto, List<String> resourceIdList) {
        UserKnowledgeFileTaskEntity taskEntity = new UserKnowledgeFileTaskEntity();
        taskEntity.setUserId(dto.getUserId());
        taskEntity.setBaseId(Long.valueOf(dto.getBaseId()));
        taskEntity.setOwnerType(OwnerTypeEnum.AI.getOwnerValue());
        taskEntity.setTaskStatus(FileTaskStatusEnum.PROCESSING.getStatus());
        taskEntity.setTaskType(KnowledgeTaskTypeEnum.FILE_MOVE.getCode());
        taskEntity.setTaskRequest(JsonUtil.toJson(dto));
        taskEntity.setExpireTime(knowledgePersonalProperties.getDeleteExpireDate());
        taskEntity.setFileIds(String.join(",", resourceIdList));
        taskEntity.setFileNum(resourceIdList.size());
        return taskEntity;
    }

    private void updateParentPath(KnowledgeFileMoveBatchReqDTO dto, UserKnowledgeEntity knowledge, String path,
                                  List<UserKnowledgeFileEntity> list) {
        String folderId = knowledge.getFolderId();
        for (UserKnowledgeFileEntity fileEntity : list) {
            fileEntity.setParentFileId(ROOT_PATH.equals(dto.getToParentFileId()) ? folderId : dto.getToParentFileId());
            fileEntity.setParentFilePath(StringUtils.isEmpty(path) ? folderId : path);
            userKnowledgeFileRepository.update(fileEntity);
        }
    }

    private String getParentFilePath(String idPath) {
        if (StringUtils.isEmpty(idPath)) {
            return null;
        }
        // 查找第一个 '/' 和第二个 '/' 的位置
        int firstSlashIndex = idPath.indexOf('/');
        int secondSlashIndex = idPath.indexOf('/', firstSlashIndex + 1);
        // 截取从第二个 '/' 后面开始的部分
        return idPath.substring(secondSlashIndex + 1);
    }

}
