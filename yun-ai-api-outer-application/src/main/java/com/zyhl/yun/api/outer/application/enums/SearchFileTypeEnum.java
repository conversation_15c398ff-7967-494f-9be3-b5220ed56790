package com.zyhl.yun.api.outer.application.enums;

/**
 * <AUTHOR>
 * @Classname NoteTypeEnum
 * @Description 笔记类型
 * @Date 2024/3/1 13:36
 */
public enum SearchFileTypeEnum {
    /**
     * 文件类型
     */
    FILE("1", "file"),

    /**
     * 文件夹类型
     */
    FOLDER("2", "folder"),
    ;


    private final String type;

    private final String name;

    SearchFileTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static String getNameByType(String type){
        for (SearchFileTypeEnum value : values()) {
            if (value.type.equals(type)){
                return value.getName();
            }
        }
        return null;
    }
}
