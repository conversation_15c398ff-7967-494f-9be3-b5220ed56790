package com.zyhl.yun.api.outer.application.service.chat.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddContentDTO;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.service.chat.DialogueRecommendService;
import com.zyhl.yun.api.outer.config.DialogueRecommendProperties;
import com.zyhl.yun.api.outer.config.RecommendPromptTemplateProperties;
import com.zyhl.yun.api.outer.constants.FutureConstants;
import com.zyhl.yun.api.outer.domain.vo.ContentRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.ContextRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.IntentionRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.PromptRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.QueryRecommendListVO;
import com.zyhl.yun.api.outer.domain.vo.QueryRecommendVO;
import com.zyhl.yun.api.outer.domainservice.PromptRecommendHandleService;
import com.zyhl.yun.api.outer.domainservice.RecommendIntentionService;
import com.zyhl.yun.api.outer.domainservice.RecommendQueryService;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 对话结果推荐接口实现类
 *
 * <AUTHOR>
 * @date 2024/6/3 01:16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DialogueRecommendServiceImpl implements DialogueRecommendService {

    /**
     * 推荐最大size
     */
    private static final int RECOMMEND_MAX_SIZE = 3;

    private final DialogueRecommendProperties recommendProperties;

    private final PromptRecommendHandleService promptRecommendHandleService;

    private final RecommendIntentionService recommendIntentionService;

    private final RecommendQueryService recommendQueryService;

    private final RecommendPromptTemplateProperties recommendPromptTemplateProperties;

    @SuppressWarnings("unchecked")
    @Override
    public DialogueRecommendVO getDialogueRecommendVO(ChatAddInnerDTO params) {
        if (ApplicationTypeEnum.isIntelligen(params.getReqParams().getApplicationType())) {
            log.info("【智能体对话】不进行推荐，直接返回空");
            return DialogueRecommendVO.builder().build();
        }

        String intention = params.getIntentionCode();
        AlgorithmChatAddContentDTO contentDTO = params.getContent();
        DialogueIntentionVO intentionVO = params.getIntentionVO();

        List<ContentRecommendVO> contentRecommendList = getContentRecommendVOList(intentionVO);
        int contentRecommendListSize = (CollectionUtils.isEmpty(contentRecommendList) ? 0 : contentRecommendList.size());

        DialogueRecommendVO dialogueRecommendVO = DialogueRecommendVO.builder()
                .contentList(contentRecommendList)
                .contextList(getContextRecommendVOList())
                .build();

        //size判断，优先级：（优先）多意图推荐->内容推荐->问题推荐
        List<Future<Object>> futures = new ArrayList<>();

        //存在意图，只有文生文或者搜索才有多意图推荐
        if (DialogueIntentionEnum.isTextIntention(intention) || DialogueIntentionEnum.isSearchIntentionOrOther(intention)) {
            //多意图处理
            if (intentionVO != null) {
                if (recommendPromptTemplateProperties.isIntentionUnifiedTemplate()) {
                    //统一模板处理多意图推荐
                    Future<Object> future = getIntentionListUnifiedRecommendVOFuture(params.getDialogueId(), contentDTO.getDialogue(), intention, intentionVO.getIntentionInfoList());
                    if (null != future) {
                        futures.add(future);
                    }
                } else {
                    //独立模板处理多意图推荐
                    for (IntentionInfo intentionInfo : intentionVO.getIntentionInfoList()) {
                        if (intentionInfo.getIntention().equals(intention)) {
                            //主意图不需要处理
                            continue;
                        }
                        //多意图追加推荐并发
                        Future<Object> future = getIntentionListRecommendVOFuture(params.getDialogueId(), contentDTO.getDialogue(), intentionInfo);
                        if (null != future) {
                            futures.add(future);
                        }
                    }
                }
            }

            // 多意图+内容推荐不够RECOMMEND_MAX_SIZE，需要追加问题推荐并发
            if ((futures.size() + contentRecommendListSize) < RECOMMEND_MAX_SIZE && DialogueIntentionEnum.isTextIntention(intention)) {
                // 只有文本意图才有问题推荐
                Future<Object> future = getQueryRecommendVOListFuture(params.getDialogueId(), contentDTO.getDialogue());
                if (null != future) {
                    futures.add(future);
                }
            }
            boolean isSseTextIntention = DialogueIntentionEnum.isTextIntention(intention) && null != params.getSseEmitter();
            boolean isSearchIntention = DialogueIntentionEnum.isSearchIntentionOrOther(intention);
            if (isSseTextIntention || isSearchIntention) {
                params.setFutures(futures);
                //【文生文流式】或【搜索意图】获取到结果后，调用setFuturesResult方法
                log.info("【文生文流式】或【搜索意图】获取到最后结果后，返回前端之前调用setFuturesResult方法, dialogueId:{}", params.getDialogueId());
            } else {
                log.info("非文生文流式获取到结果时候，实时get futures, dialogueId:{}", params.getDialogueId());
                setFuturesResult(params.getDialogueId(), dialogueRecommendVO, futures);
            }
        }

        return dialogueRecommendVO;
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    public void setFuturesResult(Long dialogueId, DialogueRecommendVO dialogueRecommendVO, List<Future<Object>> futures) {
        long start = System.currentTimeMillis();

        if (CollectionUtils.isEmpty(futures)) {
            log.info("DialogueRecommendServiceImpl-setFuturesResult futures is empty");
            return;
        }
        List<IntentionRecommendVO> intentionList = new ArrayList<>();
        List<QueryRecommendVO> queryList = new ArrayList<>();
        try {
            for (Future<Object> future : futures) {
                // 根据类型设置intentionList还是queryList
                try {
                    //等待5秒，未获取结果后取消任务
                    Object object = future.get(FutureConstants.FIVE_SECONDS, TimeUnit.SECONDS);
                    if (null == object) {
                        log.warn("setFuturesResult future.get() dialogueId:{}, object  is null", dialogueId);
                        continue;
                    }
                    if (object instanceof List) {
                        List<IntentionRecommendVO> intentionRecommends = (List) object;
                        //意图集合-多个
                        intentionList.addAll(intentionRecommends);
                    } else if (object instanceof IntentionRecommendVO) {
                        //意图集合-单个
                        intentionList.add((IntentionRecommendVO) object);
                    } else if (object instanceof QueryRecommendListVO) {
                        //问题集合
                        List<QueryRecommendVO> querys = ((QueryRecommendListVO) object).getQueryRecommends();
                        if (!CollectionUtils.isEmpty(querys)) {
                            queryList.addAll(querys);
                        }
                    } else {
                        log.warn("setFuturesResult future.get() dialogueId:{}, object getClass:{} not to set", dialogueId,
                                object.getClass());
                    }
                } catch (Exception e) {
                    log.error("setFuturesResult future.get() dialogueId:{}, error:", dialogueId, e);
                }
            }
        } finally {

            // 清空futures
            futures = null;

            dialogueRecommendVO.setIntentionList(intentionList);
            dialogueRecommendVO.setQueryList(queryList);

            log.info("DialogueRecommendServiceImpl-setFuturesResult，获取对话结果推荐-耗时：{}ms", System.currentTimeMillis() - start);
        }
    }

    @SuppressWarnings("rawtypes")
    private Future getQueryRecommendVOListFuture(Long dialogueId, String dialogue) {
        return recommendQueryService.getRecommendQueryFuture(dialogueId, dialogue);
    }

    @SuppressWarnings("rawtypes")
    private Future getIntentionListRecommendVOFuture(Long dialogueId, String dialogue, IntentionInfo intentionInfo) {
        return recommendIntentionService.getRecommendIntentionFuture(dialogueId, dialogue, intentionInfo);
    }

    private Future<Object> getIntentionListUnifiedRecommendVOFuture(Long dialogueId, String dialogue, String intention,
                                                                    List<IntentionInfo> intentionInfoList) {
        List<IntentionInfo> recommendIntentionInfoList = new ArrayList<>();
        for (IntentionInfo intentionInfo : intentionInfoList) {
            if (intentionInfo.getIntention().equals(intention)) {
                //主意图不需要处理
                continue;
            }
            recommendIntentionInfoList.add(intentionInfo);
        }
        return recommendIntentionService.getUnifiedRecommendIntentionFuture(dialogueId, dialogue, intention,
                recommendIntentionInfoList);
    }

    /**
     * 获取上下文推荐列表
     *
     * @return 上下文推荐列表
     */
    private List<ContextRecommendVO> getContextRecommendVOList() {
        // TODO 目前没有上下文推荐需求
        return new ArrayList<>();
    }

    /**
     * 获取内容推荐列表
     *
     * @param intentionVO 意图识别结果VO
     * @return 内容推荐列表
     */
    private List<ContentRecommendVO> getContentRecommendVOList(DialogueIntentionVO intentionVO) {
        if (ObjectUtil.isEmpty(intentionVO)) {
            return null;
        }

        List<ContentRecommendVO> recommendVOList = new ArrayList<>();
        /** 参数初始化 */
        // 【意图识别结果】内容推荐关键字列表
        List<DialogueIntentionVO.ContentRecommend> contentRecommendList = intentionVO.getContentRecommendList();
        if (CollUtil.isEmpty(contentRecommendList)) {
            log.info("【意图识别结果】内容推荐关键字列表-为空-DialogueRecommendServiceImpl-getContentRecommendVOList");
            return null;
        }
        // 【配置】每个类型推荐的数量
        Integer recommendQuantity = recommendProperties.getContentRecommendQuantity();
        // 【配置】keyword分隔符
        String split = recommendProperties.getContentRecommendSplit();
        // 【配置】对话内容推荐配置列表
        List<DialogueRecommendProperties.ContentRecommend> contentRecommendPropertiesList = recommendProperties.getContentList();

        /** 遍历-【意图识别结果】内容推荐关键字列表，处理数据 */
        // 把配置映射到的数据，添加到需要处理的内容推荐关键字列表
        List<DialogueIntentionVO.ContentRecommend> contentRecommendHandleList = new ArrayList<>();
        contentRecommendList.forEach(contentRecommend -> {
            // 【意图识别结果】内容推荐关键字
            String name = contentRecommend.getName();
            // 遍历-【配置】对话内容推荐配置列表
            for (DialogueRecommendProperties.ContentRecommend contentRecommendProperties : contentRecommendPropertiesList) {
                Integer contentRecommendType = contentRecommendProperties.getType();
                List<DialogueRecommendProperties.Keyword> keywordPropertiesList = contentRecommendProperties.getKeywordList();
                boolean isBreak = false;
                // 遍历-【配置】内容推荐关键字列表
                for (DialogueRecommendProperties.Keyword keywordProperties : keywordPropertiesList) {
                    String keyword = keywordProperties.getKeyword();
                    Integer priority = keywordProperties.getPriority();
                    if (CharSequenceUtil.isEmpty(keyword)) {
                        continue;
                    }
                    List<String> keywordList = ListUtil.toList(keyword.split(split));
                    if (CollUtil.isNotEmpty(keywordList)) {
                        // 【意图识别结果】关键字在配置中，则添加进内容推荐map
                        if (keywordList.contains(name)) {
                            // set推荐类型
                            contentRecommend.setType(contentRecommendType);
                            // set优先级
                            contentRecommend.setPriority(priority);
                            // 添加到需要处理的内容推荐关键字列表
                            contentRecommendHandleList.add(contentRecommend);
                            isBreak = true;
                            // 退出循环-keywordPropertiesList
                            break;
                        }
                    }
                }
                if (isBreak) {
                    // 退出循环-contentRecommendPropertiesList
                    break;
                }
            }
        });

        /** 构建内容推荐map */
        if (CollUtil.isEmpty(contentRecommendHandleList)) {
            log.info("需要处理的内容推荐关键字列表-为空-DialogueRecommendServiceImpl-getContentRecommendVOList");
            return null;
        }
        // 需要处理的内容推荐关键字map【有序：使用LinkedHashMap】
        Map<Integer, List<DialogueIntentionVO.ContentRecommend>> recommendVOMap = new LinkedHashMap<>();
        // 先排序：优先级（从小到大，越小越优先），推荐类别（从小到大）
        // 后遍历
        contentRecommendHandleList.stream().sorted(Comparator.comparing(DialogueIntentionVO.ContentRecommend::getPriority).thenComparing(DialogueIntentionVO.ContentRecommend::getType))
                .forEach(contentRecommend -> {
                    Integer contentRecommendType = contentRecommend.getType();
                    // 添加进需要处理的内容推荐关键字map
                    List<DialogueIntentionVO.ContentRecommend> contentRecommendListTemp = recommendVOMap.get(contentRecommendType);
                    if (CollUtil.isEmpty(contentRecommendListTemp)) {
                        contentRecommendListTemp = new ArrayList<>();
                    }
                    // 控制每个类型推荐的数量【可配置】
                    if (contentRecommendListTemp.size() < recommendQuantity) {
                        contentRecommendListTemp.add(contentRecommend);
                        recommendVOMap.put(contentRecommendType, contentRecommendListTemp);
                    }
                });

        /** 构建内容推荐list */
        if (MapUtil.isEmpty(recommendVOMap)) {
            log.info("需要处理的内容推荐关键字map-为空-DialogueRecommendServiceImpl-getContentRecommendVOList");
            return null;
        }
        recommendVOMap.forEach((contentRecommendType, contentRecommendListValue) -> {
            // 获取内容推荐关键字的编码列表
            List<String> contentList = contentRecommendListValue.stream().map(DialogueIntentionVO.ContentRecommend::getCode).collect(Collectors.toList());
            // 构建内容推荐
            ContentRecommendVO contentRecommendVO = ContentRecommendVO.builder()
                    .type(contentRecommendType)
                    .contentList(contentList)
                    .build();
            // 添加到列表
            recommendVOList.add(contentRecommendVO);
        });
        return recommendVOList;
    }

    @Override
    public List<PromptRecommendVO> getPromptVOList(ChatAddInnerDTO params) {

        String intention = params.getIntentionCode();
        AlgorithmChatAddContentDTO contentDTO = params.getContent();

        boolean isMailOrNoteOrDocument = ResourceTypeEnum.isMail(contentDTO.getResourceType())
                || ResourceTypeEnum.isNote(contentDTO.getResourceType())
                || ResourceTypeEnum.isDocument(contentDTO.getResourceType());
        // 获取推荐信息转换后返回
        if (isMailOrNoteOrDocument && CharSequenceUtil.isNotBlank(contentDTO.getResourceId())) {
            // 000和999不推荐（没匹配到准确意图）
            if (DialogueIntentionEnum.isTextIntention(intention)) {
                return null;
            }
            return listPromptRecommends(params.getContent().getSourceChannel());
        }
        return null;
    }

    /**
     * 从 Cache/DB 获取提示词
     *
     * @return 提示词指令推荐列表
     */
    private List<PromptRecommendVO> listPromptRecommends(String channel) {

        // 调取方法，随机获取3个提示词
        return promptRecommendHandleService.getPromptRecommendList(channel);
    }

}
