package com.zyhl.yun.api.outer.application.service.impl;

import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.converter.MarkdownToDocxConverter;
import com.zyhl.hcy.yun.ai.common.base.converter.exception.ConversionException;
import com.zyhl.hcy.yun.ai.common.base.converter.impl.FlexmarkPoiConverter;
import com.zyhl.hcy.yun.ai.common.base.enums.AIModuleEnum;
import com.zyhl.hcy.yun.ai.common.base.enums.YunAiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.YunDiskClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.FileGetContentReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.FileReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.FileUploadContentReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.AIFileVO;
import com.zyhl.hcy.yun.ai.common.platform.third.dto.GetUserInfoByPhoneNumberRespDTO;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.dto.AiEditUpdateReqDTO;
import com.zyhl.yun.api.outer.application.service.AiEditService;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.time.StopWatch;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.Base64;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;

/**
 * AI编辑服务实现
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class AiEditServiceImpl implements AiEditService {

    @Resource
    private DataSaveService dataSaveService;

    @Resource
    private YunDiskClient yunDiskClient;

    @Resource
    private AiTextResultRepository aiTextResultRepository;

    @Override
    public boolean updateOutContent(AiEditUpdateReqDTO reqDTO) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("AI编辑-开始更新AI编辑文件内容, 参数: {}", JsonUtil.toJson(reqDTO));
        
        final RequestContextHolder.UserInfo userInfo = RequestContextHolder.getUserInfo();
        String userId = userInfo.getUserId();
        Integer belongsPlatform = userInfo.getBelongsPlatform();

        try {
            // 1. 更新HBase中的outContent内容
            long startTime = stopWatch.getTime();
            updateHbaseOutContent(userId, reqDTO.getDialogueId(), reqDTO.getContent());
            log.info("AI编辑-更新HBase耗时: {}ms", stopWatch.getTime() - startTime);

            // 2. 获取云盘文件信息
            startTime = stopWatch.getTime();
            AIFileVO aiFileVO = getYunDiskFile(userId, reqDTO.getFileId());
            log.info("AI编辑-获取云盘文件信息耗时: {}ms", stopWatch.getTime() - startTime);

            // 3. 转换并上传文件
            startTime = stopWatch.getTime();
            uploadToYunDisk(userId, belongsPlatform, reqDTO.getContent(), aiFileVO);
            log.info("AI编辑-转换并上传文件耗时: {}ms", stopWatch.getTime() - startTime);

            stopWatch.stop();
            log.info("AI编辑-总耗时: {}ms", stopWatch.getTime(TimeUnit.MILLISECONDS));
            return true;
        } catch (Exception e) {
            stopWatch.stop();
            log.error("AI编辑-处理失败, 总耗时: {}ms, 错误: {}", stopWatch.getTime(TimeUnit.MILLISECONDS), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取云盘文件信息
     */
    private AIFileVO getYunDiskFile(String userId, String fileId) {
        GetUserInfoByPhoneNumberRespDTO userInfo = yunDiskClient.getUserInfo(userId);
        FileGetContentReqDTO fileGetContentReqDTO = new FileGetContentReqDTO();
        fileGetContentReqDTO.setFileId(fileId);
        fileGetContentReqDTO.setUserInfo(userInfo);
        
        AIFileVO aiFileVO = yunDiskClient.getFileContent(fileGetContentReqDTO);
        if (aiFileVO == null || aiFileVO.getFileResp() == null || StringUtils.isEmpty(aiFileVO.getFileResp().getParentFileId())) {
            log.error("AI编辑-未找到指定文件, fileId: {}", fileId);
            throw new YunAiBusinessException(ResultCodeEnum.FILE_ID_INFO_ERROR);
        }
        
        log.info("AI编辑-获取到原始文件信息, 文件名: {}, 父目录ID: {}", aiFileVO.getFileName(), aiFileVO.getFileResp().getParentFileId());
        return aiFileVO;
    }

    /**
     * 转换并上传文件到云盘
     */
    private void uploadToYunDisk(String userId, Integer belongsPlatform, String content, AIFileVO aiFileVO) {
        // 提取文件名前缀供上传使用
        String fileName = aiFileVO.getFileName();
        String fileNameForUpload = extractFileNameWithoutExtension(fileName);
        String parentFileId = aiFileVO.getFileResp().getParentFileId();

        // 将Markdown内容转为DOCX格式
        byte[] docxBytes = convertMarkdownToDocx(content);
        String base64Content = Base64.getEncoder().encodeToString(docxBytes);

        // 覆盖上传替换原文件
        FileUploadContentReqDTO fileUploadContentReqDTO = FileUploadContentReqDTO.builder()
                .virtualFile(FileReqDTO.builder()
                        .base64(base64Content)
                        .name(fileNameForUpload)
                        .build())
                .userId(userId)
                .catalogId(parentFileId)
                .belongsPlatform(belongsPlatform)
                .module(AIModuleEnum.MAILAI_EDIT.getModule())
                .fileId(aiFileVO.getContentId())
                .fileSuffix("docx")
                .build();

        yunDiskClient.uploadFile(fileUploadContentReqDTO);
        log.info("AI编辑-文件上传成功, fileId: {}", aiFileVO.getContentId());
    }

    /**
     * 提取文件名（不含扩展名）
     */
    private String extractFileNameWithoutExtension(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(0, lastDotIndex);
        }
        return fileName;
    }

    /**
     * 更新HBase对话结果中的outContent内容
     *
     * @param userId     用户ID
     * @param dialogueId 对话ID
     * @param newContent 新的内容
     */
    private void updateHbaseOutContent(String userId, String dialogueId, String newContent) {
        log.info("AI编辑-开始更新HBase对话结果中的outContent内容, userId: {}, dialogueId: {}", userId, dialogueId);
        try {
            // 从HBase获取当前的对话结果
            AiTextResultEntity hbaseResult = dataSaveService.getHbaseResult(userId, dialogueId);
            if (hbaseResult == null) {
                log.error("未找到HBase对话结果, userId: {}, dialogueId: {}", userId, dialogueId);
                throw new YunAiBusinessException(YunAiCommonResultCode.HBASE_ERROR, "未找到HBase对话结果");
            }

            // 解析响应参数,获取index=0的对话流式结果
            AiTextResultRespParameters respBean = JSONUtil.toBean(hbaseResult.getRespParameters(), AiTextResultRespParameters.class);
            DialogueFlowResult targetResult = null;
            if (CollUtil.isNotEmpty(respBean.getOutputList())) {
                for (DialogueFlowResult result : respBean.getOutputList()) {
                    if (Objects.equals(result.getIndex(), 0)) {
                        targetResult = result;
                        break;
                    }
                }
            }
            if (targetResult == null) {
                log.error("AI编辑-未找到index=0的对话流式结果, userId: {}, dialogueId: {}", userId, dialogueId);
                throw new YunAiBusinessException(YunAiCommonResultCode.HBASE_ERROR, "AI编辑-HBase对话中没有index=0的对话流式结果");
            }

            // 提取并替换outContent中的内容段
            String originalOutContent = targetResult.getOutContent();
            String updatedOutContent = replaceContentSegment(originalOutContent, newContent);
            targetResult.setOutContent(updatedOutContent);
            String respParameters = JSONUtil.toJsonStr(respBean);
            aiTextResultRepository.updateRespParameters(userId, Long.valueOf(dialogueId), respParameters);
            log.info("AI编辑-成功更新HBase对话结果中的outContent内容, userId: {}, dialogueId: {}", userId, dialogueId);
        } catch (Exception e) {
            log.error("AI编辑-更新HBase对话结果中的outContent内容失败: {}", e.getMessage(), e);
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR, "AI编辑-更新对话内容失败: " + e.getMessage());
        }
    }

    /**
     * 提取内容段并替换为新内容
     * 注意：<!-- segment:content -->一定是最后一个标签
     *
     * @param originalContent 原始内容
     * @param newContentBody 新的内容主体
     * @return 替换后的内容
     */
    private String replaceContentSegment(String originalContent, String newContentBody) {
        String segmentTag = "<!-- segment:content -->";
        int segmentIndex = originalContent.indexOf(segmentTag);

        if (segmentIndex == -1) {
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR, "未找到对话的内容段");
        } else {
            // 找到了内容段，保留标签及之前的内容，然后拼接新内容
            String beforeSegment = originalContent.substring(0, segmentIndex + segmentTag.length());
            return beforeSegment + "\n" + newContentBody;
        }
    }

    /**
     * 将Markdown内容转换为DOCX格式
     *
     * @param markdownContent Markdown内容
     * @return DOCX格式的字节数组
     */
    private byte[] convertMarkdownToDocx(String markdownContent) {
        log.info("AI编辑-将Markdown内容转换为DOCX格式，内容长度: {}", markdownContent.length());
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            MarkdownToDocxConverter markdownToDocxConverter = new FlexmarkPoiConverter();
            markdownToDocxConverter.convertMarkdownToDocx(markdownContent, outputStream);
            byte[] docxBytes = outputStream.toByteArray();
            return docxBytes;
        } catch (ConversionException e) {
            log.error("Markdown转Word转换失败: {}", e.getMessage(), e);
            throw new YunAiBusinessException(YunAiCommonResultCode.DOWNSTREAM_SERVICES_EXCEPTION, "Markdown转Word转换失败: " + e.getMessage());
        }
    }
}