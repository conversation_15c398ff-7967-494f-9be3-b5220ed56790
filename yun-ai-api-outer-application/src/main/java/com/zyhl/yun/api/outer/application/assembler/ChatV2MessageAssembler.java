package com.zyhl.yun.api.outer.application.assembler;

import com.zyhl.yun.api.outer.application.chatv2.vo.MessageResultV2VO;
import com.zyhl.yun.api.outer.config.BusinessParamProperties;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatMessageEntity;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import javax.annotation.Resource;

/**
 * 会话-类转换器
 * @Author: WeiJingKun
 */
@Mapper(componentModel = "spring", injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public abstract class ChatV2MessageAssembler {

    @Resource
    protected BusinessParamProperties businessParamProperties;

    /**
     * entity转vo
     * @param entity entity
     * @return vo
     */
    @Mapping(target = "enableStar", expression = "java(entity.caseToEnableStar(entity.getEnableStar()))")
    @Mapping(target = "iconUrl", expression = "java(businessParamProperties.getAssistantChatV2List().getIconUrl(entity.getIconType(), entity.getSubIconType()))")
    public abstract MessageResultV2VO toMessageResultV2VO(AlgorithmChatMessageEntity entity);

}
