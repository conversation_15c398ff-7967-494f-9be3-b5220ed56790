package com.zyhl.yun.api.outer.application.service.mq;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileTaskEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeUploadEntity;

/**
 * 知识库目录任务转发
 *
 * <AUTHOR>
 */
public interface KnowledgeTransCategoryTaskMqService {

    /**
     * 发送任务状态查询消息
     *
     * @param uploadEntity 任务
     */
    void sendMq(UserKnowledgeUploadEntity uploadEntity);

}
