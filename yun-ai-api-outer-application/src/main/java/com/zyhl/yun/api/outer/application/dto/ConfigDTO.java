package com.zyhl.yun.api.outer.application.dto;

import java.io.Serializable;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * AI助手配置信息接口参数
 *
 * <AUTHOR>
 */
@Slf4j
@Data
public class ConfigDTO extends BaseDTO implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 渠道来源
     *
     * @see com.zyhl.yun.api.outer.config.SourceChannelsProperties
     */
    private String sourceChannel;

    /**
     * 助手枚举【非接口参数】
     */
    private AssistantEnum assistantEnum;
    
    /**
     * 助手业务类型【非接口参数】
     */
    private String businessType;
    
    /**
     * 参数校验
     * @param channelsProperties 渠道配置
     * @return 错误码枚举
     */
    public AbstractResultCode check(SourceChannelsProperties channelsProperties) {
        if (!channelsProperties.isExist(sourceChannel)) {
            log.info("渠道来源不存在：{}", sourceChannel);
            return ResultCodeEnum.ERROR_PARAMS;
        }

        // 根据渠道，获取助手类型（默认云邮）
        this.assistantEnum = channelsProperties.getAssistantEnumDefaultMail(this.sourceChannel);
        // 根据渠道，获取助手业务类型
        this.businessType = channelsProperties.getType(this.sourceChannel);
        
        return super.checkUserId();
    }

}
