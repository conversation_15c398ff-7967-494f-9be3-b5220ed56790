package com.zyhl.yun.api.outer.application.service.task.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;

import javax.annotation.Resource;

import org.apache.catalina.connector.ClientAbortException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.enums.SupplierTypeEnum;
import com.zyhl.hcy.yun.ai.common.base.enums.YunAiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.FileBase64Util;
import com.zyhl.hcy.yun.ai.common.base.utils.NumberUtil;
import com.zyhl.hcy.yun.ai.common.model.api.client.blian.ExternalBlianClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.event.TextModelStreamEventListener;
import com.zyhl.hcy.yun.ai.common.model.api.client.vo.TextModelBaseVo;
import com.zyhl.hcy.yun.ai.common.model.api.client.vo.TextModelBaseVo.NetworkSearchInfoVo;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelConfigDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelFileDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelFileDeleteReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageVlDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageVlDTO.VlContent;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelVlReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.ApiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelVlContentTypeEnum;
import com.zyhl.hcy.yun.ai.common.model.api.utils.TextModelUtil;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.dto.CheckTextReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.vo.CheckResultVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.AIFileVO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddContentDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.dto.ContinueTextSseDTO;
import com.zyhl.yun.api.outer.application.dto.TextModelDTO;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatAddCheckService;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatHistoryService;
import com.zyhl.yun.api.outer.application.service.chat.ChatApplicationTypeService;
import com.zyhl.yun.api.outer.application.service.chat.DialogueRecommendService;
import com.zyhl.yun.api.outer.application.service.external.MemberCenterService;
import com.zyhl.yun.api.outer.application.service.task.TextGenerateTextService;
import com.zyhl.yun.api.outer.application.util.ChatAddUtils;
import com.zyhl.yun.api.outer.application.util.SseEmitterDataUtils;
import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;
import com.zyhl.yun.api.outer.application.vo.FlowTypeResultV2VO;
import com.zyhl.yun.api.outer.application.vo.FlowTypeResultVO;
import com.zyhl.yun.api.outer.config.CheckSystemConfig;
import com.zyhl.yun.api.outer.config.CheckSystemConfig.CheckSystemTextModel;
import com.zyhl.yun.api.outer.config.DialogueRecommendProperties;
import com.zyhl.yun.api.outer.config.FlowTypeProperties;
import com.zyhl.yun.api.outer.config.MailAiPromptProperties;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.config.WhiteListProperties;
import com.zyhl.yun.api.outer.config.textmodel.ModelPromptProperties;
import com.zyhl.yun.api.outer.config.textmodel.VlModelConfig;
import com.zyhl.yun.api.outer.config.textmodel.VlModelConfig.BusinessModelConfig;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters.NetworkSearchInfo;
import com.zyhl.yun.api.outer.domain.vo.ChatApplicationType;
import com.zyhl.yun.api.outer.domain.vo.DocumentParsingResultVO;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.domainservice.CheckSystemDomainService;
import com.zyhl.yun.api.outer.domainservice.QpsLimitService;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.CheckTypeEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ApiVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddResultTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.OutAuditStatusEnum;
import com.zyhl.yun.api.outer.external.DocumentParsingExternalService;
import com.zyhl.yun.api.outer.external.YunDiskExternalService;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.VersionUtil;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.sse.EventSource;

/**
 * 文生文相关接口实现类
 *
 * <AUTHOR>
 * @date 2024/4/9 18:50
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TextGenerateTextServiceImpl implements TextGenerateTextService {

    private final CheckSystemDomainService checkSystemDomainService;

    private final MemberCenterService memberService;

    private final AlgorithmChatHistoryService algorithmChatHistoryService;

    private final QpsLimitService qpslimitService;

    private final ChatApplicationTypeService applicationTypeService;

    private final TextModelExternalService textModelExternalService;

    private final RedisOperateRepository redisOperateRepository;

    private final ChatConfigServiceDomainService chatConfigServiceDomainService;

    private final FlowTypeProperties flowTypeProperties;

    private final ModelProperties modelProperties;

    private final AiTextResultRepository aiTextResultRepository;

    @Resource
    private ExternalBlianClient externalBlianClient;

    private final DialogueRecommendService dialogueRecommendService;

    private final AlgorithmChatContentRepository algorithmChatContentRepository;

    @Resource
    private MailAiPromptProperties mailAiPromptProperties;

    private final AlgorithmChatAddCheckService chatAddCheckService;

    private final DocumentParsingExternalService documentParsingExternalService;

    private final ModelPromptProperties modelPromptProperties;

    private final WhiteListProperties whiteListProperties;

    private final CheckSystemConfig checkSystemConfig;

    private final DialogueRecommendProperties dialogueRecommendProperties;

    private final VlModelConfig vlModelConfig;

    private final YunDiskExternalService yunDiskExternalService;


    @Override
    public AlgorithmChatAddVO flowTypeHandle(ChatAddInnerDTO innerDTO) {
        AlgorithmChatAddDTO dto = innerDTO.getReqParams();
        AlgorithmChatAddVO resVo = innerDTO.getRespParams();
        AlgorithmChatAddContentDTO content = dto.getContent();
        AssistantEnum assistantEnum = content.getAssistantEnum();
        String businessType = content.getBusinessType();
        // 用户输入dialogue
        String dialogue = content.getDialogue();
        // 资源文本
        String resourceContent = innerDTO.getResourceContent();

        // 初始化会话输入返回结果VO
        resVo.setCommands(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());
        resVo.setResultType(ChatAddResultTypeEnum.FLOW_TYPE.getType());

        // 历史对话
        List<TextModelMessageDTO> historyList = aiTextResultRepository.getHistoryList(dto.getUserId(), dto.getSessionId());
        //查询AI提示词模板表
        String promptTemplate = chatAddCheckService.getDialoguePrompt(content.getPrompt(), innerDTO.getContent().getSourceChannel());
        // 辅助处理的对象
        StreamEventListenerParams params = new StreamEventListenerParams(innerDTO, resourceContent, historyList, promptTemplate);

        try {
            // 流式结果监听事件，监听并处理流式返回结果
            TextModelStreamEventListener event = handleEventListener(params);

            // AI搜索，回复指定模型
            ContinueTextSseDTO continueTextSseDTO = innerDTO.getContinueTextSseDTO();
            if(continueTextSseDTO.isContinueTextSseHandler()){
                // 强制联网搜
                params.getTextDto().setEnableForceNetworkSearch(continueTextSseDTO.getEnableForceNetworkSearch());
                log.info("###flowTypeHandle AI搜索，继续执行大模型回答DTO:{}", JSONUtil.toJsonStr(continueTextSseDTO));
                simpleDialogue(params, event, continueTextSseDTO.getModelCode());
                resVo.setModelType(params.getModelCode());
                return resVo;
            }

            // 智能体对话
            if (ApplicationTypeEnum.isIntelligen(dto.getApplicationType())) {
                intelligenDialogue(params, event);
                resVo.setModelType(params.getModelCode());
                return resVo;
            }

            // 获取用户设置的模型，没有设置则使用默认模型
            ChatConfigEntity chatConfigEntity = chatConfigServiceDomainService.getUserCanUseModel(dto.getUserId(),
                    RequestContextHolder.getPhoneNumber(), assistantEnum, businessType);

            // （dialogue+图片）执行视觉大模型对话
            if (StringUtils.isNotEmpty(dialogue) && ResourceTypeEnum.isImage(innerDTO.getContent().getResourceType())
                    && StringUtils.isNotEmpty(innerDTO.getContent().getResourceId())) {
                if (null != chatConfigEntity) {
                    // 设置用户模型
                    params.setModelCode(chatConfigEntity.getModelType());
                }
                vlmDialogue(innerDTO.getDialogueId(), content, params, event);
                resVo.setModelType(params.getModelCode());
                return resVo;
            }

            // 长文本处理对话
            // 长文本处理-本地长文本处理
            List<String> longTextFileLocalPathList = content.getLongTextFileLocalPathList();
            // 长文本处理-阿里百炼千问长文本处理
            List<TextModelFileDTO> textModelFiles = params.textDto.getTextModelFiles();
            if (CollUtil.isNotEmpty(textModelFiles) || CollUtil.isNotEmpty(longTextFileLocalPathList)) {
                if (CollUtil.isNotEmpty(content.getLongTextFileLocalPathList())) {
                    // 长文本处理-本地长文本处理方法
                    // 指令匹配列表
                    List<String> promptKeys = new ArrayList<>();
                    // 用户提示词（英文key时候场景优先匹配）
                    String promptKey = content.getPrompt();
                    if (StringUtils.isNotBlank(promptKey)) {
                        promptKeys.add(promptKey);
                    }
                    // 用户指令
                    String orderTip = ChatAddUtils.getOrderTip(content.getExtInfo());
                    if (StringUtils.isNotBlank(orderTip)) {
                        promptKeys.add(orderTip);
                    }
                    if (null != chatConfigEntity) {
                        // 设置用户模型
                        params.setModelCode(chatConfigEntity.getModelType());
                    }
                    localLongTextDialogue(content.getDialogue(), promptKeys, longTextFileLocalPathList, params, event);
                    resVo.setModelType(params.getModelCode());
                    return resVo;
                } else if (CollUtil.isNotEmpty(params.textDto.getTextModelFiles())) {
                    // 长文本处理-阿里百炼千问长文本处理方法
                    // 文档资源类型，指定文本模型
                    simpleDialogue(params, event, TextModelEnum.BLIAN_QWEN_LONG.getCode());
                    resVo.setModelType(params.getModelCode());
                    return resVo;
                }
            }

            //邮件AI总结、回复指定模型
            MailAiPromptProperties.MailAiPrompt promptProperties = mailAiPromptProperties.getByPromptKeyAndChannel(content.getPrompt(), content.getSourceChannel());
            if (ObjectUtil.isNotEmpty(promptProperties) && promptProperties.isEnable()) {
                log.info("###flowTypeHandle 邮件AI总结、回复指定模型 promptProperties:{}", JSONUtil.toJsonStr(promptProperties));
                simpleDialogue(params, event, promptProperties.getModelCode());
                resVo.setModelType(params.getModelCode());
                return resVo;
            }

            // h5Version < 小天1.7.3版本，走智能调度
            if (AssistantEnum.XIAO_TIAN.equals(assistantEnum) && VersionUtil.xtH5VersionLt173()) {
                log.info("h5Version < 小天1.7.3版本，走智能调度，不支持设置则使用默认模型，h5Version：{}", RequestContextHolder.getH5Version());
            } else {
                if (null != chatConfigEntity) {
                    simpleDialogue(params, event, chatConfigEntity.getModelType());
                    resVo.setModelType(params.getModelCode());
                    return resVo;
                }
            }

            // 智能调度
            if (!intelligentSchedule(params, event)) {
                resVo.setModelType(params.getModelCode());
                return resVo;
            } else {
                // 更新模型编码
                algorithmChatContentRepository.updateModelCode(params.dialogId, params.modelCode);
            }
        } catch (YunAiBusinessException e) {
            // 回滚权益
            memberService.consumeBenefitFail(dto.getUserId(), RequestContextHolder.getPhoneNumber(), Long.parseLong(resVo.getDialogueId()));
            params.handleFailMsg(ResultCodeEnum.ERROR_AI_MODEL);
            e = SseEmitterDataUtils.caseAiModelException(e);
            throw e;
        } catch (Exception e) {
            // 回滚权益
            memberService.consumeBenefitFail(dto.getUserId(), RequestContextHolder.getPhoneNumber(), Long.parseLong(resVo.getDialogueId()));

            // 发送消息
            log.error("TextGenerateTextServiceImpl flowTypeHandle dialogueId:{}, error:", resVo.getDialogueId(), e);
            params.handleFailMsg(ResultCodeEnum.ERROR_AI_MODEL);
        }
        resVo.setModelType(params.getModelCode());
        return resVo;
    }

    /**
     * 视觉大模型对话
     *
     * @param dialogueId
     * @param content
     * @param params
     * @param event
     */
    private void vlmDialogue(Long dialogueId, AlgorithmChatAddContentDTO content, StreamEventListenerParams params,
                             TextModelStreamEventListener event) {
        BusinessModelConfig dialogueAndImageConfig = vlModelConfig.getDialogueAndImageConfig();
        String dialogueAndImageModelCode = dialogueAndImageConfig.getModelCode();
        // 令牌桶限流
        if (!qpslimitService.modelQpsLimit(dialogueAndImageModelCode)) {
            log.info("【视觉大模型对话】请求过多，qps限流，模型编码：{}，对话id：{}", dialogueAndImageModelCode, params.dialogId);
            params.handleFailMsg(ResultCodeEnum.ERROR_LIMITATION);
            return;
        }

        AIFileVO vo = yunDiskExternalService.getFileInfo(params.getUserId(), content.getResourceId());
        if (!(null != vo && StringUtils.isNotEmpty(vo.getContent()))) {
            throw new YunAiBusinessException(YunAiCommonResultCode.FILE_UPLOAD_FAILED);
        }
        // 获取图片url
        String url = vo.getContent();
        String imageBase64 = FileBase64Util.urlToBase64(url);
        if (StringUtils.isEmpty(imageBase64)) {
            throw new YunAiBusinessException(YunAiCommonResultCode.FILE_UPLOAD_FAILED);
        }
        String userContentText = content.getDialogue();
        String systemPrompt = dialogueAndImageConfig.getSystemPrompt();
        String userPrompt = dialogueAndImageConfig.getUserPrompt();
        Integer maxLength = dialogueAndImageConfig.getMaxLength();
        log.info("vlmDialogue execute dialogueId:{}, dialogueAndImageModelCode:{}, maxLength:{}", dialogueId,
                dialogueAndImageModelCode, maxLength);
        // 视觉模型对象入参，按maxLength追加历史上下文（公共方法，默认追加当前对话）
        TextModelVlReqDTO reqDTO = params.textDto.toTextVlReqDTO(maxLength);
        List<TextModelMessageVlDTO> allMsg = reqDTO.getMessageVlDtoList();
        if (CollUtil.isNotEmpty(allMsg)) {
            // （公共方法，默认追加当前对话）最后一条需要去除
            allMsg = allMsg.subList(0, allMsg.size() - 1);
        } else {
            // 不存在，直接new list
            allMsg = new ArrayList<>();
        }

        // 对话内容追加 start
        List<VlContent> vlContent = new ArrayList<>();
        // 系统提示词
        if (StringUtils.isNotEmpty(systemPrompt)) {
            vlContent.add(
                    VlContent.builder().type(TextModelVlContentTypeEnum.TEXT.getType()).text(systemPrompt).build());
        }
        // 图片
        vlContent.add(VlContent.builder().type(TextModelVlContentTypeEnum.IMAGE_BASE64.getType())
                .imageBase64(imageBase64).build());
        // 用户提示词
        if (StringUtils.isNotEmpty(userPrompt)) {
            userContentText = userPrompt.replace("{query}", userContentText);
        }
        vlContent
                .add(VlContent.builder().type(TextModelVlContentTypeEnum.TEXT.getType()).text(userContentText).build());
        allMsg.add(new TextModelMessageVlDTO(TextModelRoleEnum.USER.getName(), vlContent));
        // 对话内容追加 end

        // 设置vl消息体
        reqDTO.setMessageVlDtoList(allMsg);
        // 设置模型参数
        reqDTO.setTextModelConfig(new TextModelConfigDTO(dialogueAndImageConfig.getMaxTokens(),
                dialogueAndImageConfig.getTemperature(), null, dialogueAndImageConfig.getTopP(),
                dialogueAndImageConfig.getTopK(), dialogueAndImageConfig.getFrequencyPenalty(), null));
        textModelExternalService.streamVlDialogue(dialogueAndImageModelCode, reqDTO, event);
        // 更新模型编码
        algorithmChatContentRepository.updateModelCode(params.dialogId, params.modelCode);
    }

    /**
     * 智能体对话
     *
     * @param params
     * @param event
     * @return
     */
    private void intelligenDialogue(StreamEventListenerParams params, TextModelStreamEventListener event) {
        AlgorithmChatAddDTO dto = params.innerDTO.getReqParams();
        String typeRelationId = null;
        ChatApplicationType chatApplicationType = applicationTypeService.getChatApplicationTypeCache(dto.getApplicationId(), dto.getApplicationType());
        if (null != chatApplicationType) {
            typeRelationId = chatApplicationType.getTypeRelationId();
        }
        log.info("【智能体对话】应用id：{}，关联id：{}，对话id：{}，用户id：{}", dto.getApplicationId(), typeRelationId, params.dialogId, dto.getUserId());

        //设置模型编码
        if (null != chatApplicationType
                && String.valueOf(SupplierTypeEnum.ALI.getCode()).equals(chatApplicationType.getSupplierType())) {
            if (TextModelEnum.isBlianModelDeepSeekR1(typeRelationId)) {
                //智能体特殊处理阿里厂商，百炼deepseek模型设置
                params.modelCode = typeRelationId;
            } else {
                params.modelCode = TextModelEnum.XCHEN.getCode();
            }
        } else {
            params.modelCode = typeRelationId;
        }

        // 令牌桶限流
        if (!qpslimitService.modelQpsLimit(params.modelCode)) {
            log.info("【智能体对话】请求过多，qps限流，模型编码：{}，对话id：{}", params.modelCode, params.dialogId);
            params.handleFailMsg(ResultCodeEnum.ERROR_LIMITATION);
            return;
        }

        // VIP用户
        boolean isVipUser = whiteListProperties.getTextModelVipUser().contains(RequestContextHolder.getPhoneNumber());

        // 其他智能体暂无字数限制

        Integer agentMaxLength = modelProperties.getAgentMaxLength(params.modelCode);
        if (null != chatApplicationType
                && String.valueOf(SupplierTypeEnum.ZY.getCode()).equals(chatApplicationType.getSupplierType())) {
            log.info("自研智能体字数限制获取 modelCode:{}, agentMaxLength:{}", params.modelCode, agentMaxLength);
            TextModelTextReqDTO req = params.textDto.toTextReqDTO(agentMaxLength);
            // 模型编码设置
            req.setModelValue(params.modelCode);
            req.setVipUser(isVipUser);
            // 智能体调用大模型不加角标
            req.setEnableNetworkSearchCitation(false);
            textModelExternalService.streamDialogue(params.modelCode, req, event);
        } else if (null != chatApplicationType
                && String.valueOf(SupplierTypeEnum.ALI.getCode()).equals(chatApplicationType.getSupplierType())
                && TextModelEnum.isBlianModelDeepSeekR1(params.modelCode)) {
            log.info("【阿里百炼】智能体字数限制获取 modelCode:{}, agentMaxLength:{}", params.modelCode, agentMaxLength);
            TextModelTextReqDTO req = params.textDto.toTextReqDTO(agentMaxLength);
            // 模型编码设置
            req.setModelValue(params.modelCode);
            req.setVipUser(isVipUser);
            // 智能体调用大模型不加角标
            req.setEnableNetworkSearchCitation(false);
            textModelExternalService.blianStream(req, event);
        } else if (null != chatApplicationType
                && String.valueOf(SupplierTypeEnum.HS.getCode()).equals(chatApplicationType.getSupplierType())) {
            log.info("【火山方舟】智能体字数限制获取 modelCode:{}, agentMaxLength:{}", params.modelCode, agentMaxLength);
            TextModelTextReqDTO req = params.textDto.toTextReqDTO(agentMaxLength);
            // 模型编码设置
            req.setModelValue(params.modelCode);
            req.setVipUser(isVipUser);
            // 智能体调用大模型不加角标
            req.setEnableNetworkSearchCitation(false);
            if (TextModelEnum.isHuoshan(params.modelCode)) {
                textModelExternalService.hshanStream(req, event);
            } else {
                textModelExternalService.hshanArkStream(req, event);
            }
        } else {
            TextModelTextReqDTO req = params.textDto.toTextReqDTO(null);
            req.setVipUser(isVipUser);
            // 通义星尘大文本模型
            textModelExternalService.xchenStream(req, typeRelationId, event);
        }

        // 更新模型编码
        algorithmChatContentRepository.updateModelCode(params.dialogId, params.modelCode);
    }

    /**
     * 长文本普通对话，指定模型
     *
     * @param dialogue                  用户对话输入参数
     * @param promptKeyList             长文本提示词key列表参数
     * @param longTextFileLocalPathList 长文本文档本地共享存储路径参数
     * @param params                    流式监听辅助处理参数
     * @param event                     流式监听事件
     */
    private void localLongTextDialogue(String dialogue, List<String> promptKeyList, List<String> longTextFileLocalPathList,
                                       StreamEventListenerParams params, TextModelStreamEventListener event) {
        if (StringUtils.isBlank(params.modelCode)) {
            log.error("localLongTextDialogue 模型参数异常");
            params.handleFailMsg(ResultCodeEnum.ERROR_PARAMS);
            return;
        }
        // qps限制
        if (!qpslimitService.modelQpsLimit(params.modelCode)) {
            log.info("localLongTextDialogue 请求过多，qps限流，model:{}", params.modelCode);
            params.handleFailMsg(ResultCodeEnum.ERROR_LIMITATION);
            return;
        }
        // 根据模型自定义提示词
        String customerPrompt = modelPromptProperties.getPrompt(promptKeyList, params.modelCode);
        if (StringUtils.isBlank(customerPrompt) && CollUtil.isNotEmpty(promptKeyList)) {
            // 兼容无法获取nacos配置提示词，用第一个提示词
            customerPrompt = promptKeyList.get(0);
        }
        if (StringUtils.isBlank(customerPrompt)) {
            // 兼容都已经无法获取配置提示词，最后用用户输入
            customerPrompt = dialogue;
        } else if (ObjectUtil.isNotEmpty(dialogue)) {
            customerPrompt += "：" + dialogue;
        }
        if (StringUtils.isBlank(customerPrompt)) {
            log.error("localLongTextDialogue 提示词参数获取异常，model:{}", params.modelCode);
            params.handleFailMsg(ResultCodeEnum.ERROR_PARAMS);
            return;
        }
        // 输入字数判断
        AssistantEnum assistantEnum = params.getInnerDTO().getContent().getAssistantEnum();
        String businessType = params.getInnerDTO().getContent().getBusinessType();
        Integer lengthLimit = modelProperties.getLengthLimit(assistantEnum, businessType, params.modelCode);
        Integer maxLenLimit = lengthLimit == null ? null : (lengthLimit - customerPrompt.length());
        DocumentParsingResultVO content = null;
        try {
            content = documentParsingExternalService.parsing(longTextFileLocalPathList, maxLenLimit);
        } catch (Exception e) {
            if (e instanceof YunAiBusinessException) {
                params.handleFailMsg(AiResultCode.getByCodeOrMsg(((YunAiBusinessException) e).getCode(), null));
                return;
            }
        }
        if (null == content || StringUtils.isEmpty(content.getText())) {
            // 文件内容为空
            params.handleFailMsg(AiResultCode.CODE_10000020);
            return;
        }
        // 长文本模型对象入参，不追加历史上下文
        TextModelTextReqDTO reqDTO = params.textDto.toSimpleTextReqDTO();
        //新增单消息对象
        reqDTO.setMessageDtoList(Collections.singletonList(
                new TextModelMessageDTO(TextModelRoleEnum.USER.getName(), content.getText(), customerPrompt)));
        log.info("localLongTextDialogue 指定文本模型对话 model:{}, reqDTO:{}", params.modelCode, JSONUtil.toJsonStr(reqDTO));
        //指定用户模型
        reqDTO.setModelValue(params.modelCode);
        textModelExternalService.streamDialogue(params.modelCode, reqDTO, event);

        // 更新模型编码
        algorithmChatContentRepository.updateModelCode(params.dialogId, params.modelCode);
    }

    /**
     * 普通对话，指定模型
     *
     * @param params    流式监听辅助处理参数
     * @param event     流式监听事件
     * @param modelCode 模型编码
     */
    private void simpleDialogue(StreamEventListenerParams params, TextModelStreamEventListener event, String modelCode) {
        AssistantEnum assistantEnum = params.getInnerDTO().getContent().getAssistantEnum();
        String businessType = params.getInnerDTO().getContent().getBusinessType();
        params.modelCode = modelCode;

        // qps限制
        if (!qpslimitService.modelQpsLimit(params.modelCode)) {
            log.info("simpleDialogue 请求过多，qps限流，model:{}", params.modelCode);
            params.handleFailMsg(ResultCodeEnum.ERROR_LIMITATION);
            return;
        }

        // 大模型对话文本
        String content = params.textDto.getContent();
        // 输入字数判断
        Integer lengthLimit = modelProperties.getLengthLimit(assistantEnum, businessType, params.modelCode);

        //符合的资源类型，超过后，截取lengthLimit个字符串
        if (null != lengthLimit
                && ResourceTypeEnum.isSubStringType(params.getInnerDTO().getContent().getResourceType())) {
            if (StringUtils.isNotEmpty(content) && content.length() > lengthLimit) {
                log.info("当前符合需要截取的资源类型，执行截取， dialogueId:{}, content size:{}, lengthLimit size:{}",
                        params.getInnerDTO().getDialogueId(), content.length(), lengthLimit.intValue());
                params.textDto.setContent(content.substring(0, lengthLimit.intValue()));
            }
        }

        if (lengthLimit != null && params.textDto.getContent().length() > lengthLimit) {
            log.info("simpleDialogue 文本模型输入字数超限，模型：{}，输入限制为：{}，输入字数为：{}", params.modelCode, lengthLimit, params.textDto.getContent().length());
            params.handleFailMsg(ResultCodeEnum.CONTENT_EXCEEDS_LIMIT);
            return;
        }

        // 文本模型
        TextModelTextReqDTO reqDTO = params.textDto.toTextReqDTO(modelProperties.getMaxLength(assistantEnum, businessType, params.modelCode));
        //设置角标启用按界面版本控制
        setEnableNetworkSearchCitationByVersion(assistantEnum, reqDTO);
        log.info("simpleDialogue 指定文本模型对话 model:{}, reqDTO:{}", params.modelCode, JSONUtil.toJsonStr(reqDTO));
        textModelExternalService.streamDialogue(params.modelCode, reqDTO, event);

        // 更新模型编码
        algorithmChatContentRepository.updateModelCode(params.dialogId, params.modelCode);
    }

    /**
     * 智能调度
     *
     * @param params 流式监听参数
     * @param event  sse监听事件
     * @return
     */
    private boolean intelligentSchedule(StreamEventListenerParams params, TextModelStreamEventListener event) {
        AssistantEnum assistantEnum = params.getInnerDTO().getContent().getAssistantEnum();
        String businessType = params.getInnerDTO().getContent().getBusinessType();
        List<String> executeSort = modelProperties.getExecuteSort(params.getSourceChannel());
        if (ObjectUtil.isEmpty(executeSort)) {
            log.info("nacos配置为空，model.intelligent-schedules，对话id：{}", params.getDialogId());
            params.handleFailMsg(ResultCodeEnum.ERROR_NACOS_CONFIG);
            return false;
        }

        ResultCodeEnum error = null;
        boolean success = false;
        for (String code : executeSort) {
            error = null;
            TextModelEnum textModelEnum = TextModelEnum.getByCode(code);
            if (textModelEnum == null) {
                log.info("文本模型不存在，配置的模型编码：{}", code);
                continue;
            }

            // 输入字数判断
            Integer lengthLimit = modelProperties.getLengthLimit(assistantEnum, businessType, code);
            if (lengthLimit != null && params.textDto.getContent().length() > lengthLimit) {
                log.info("文本模型输入字数超限，输入限制为：{}，输入字数为：{}", lengthLimit, params.textDto.getContent().length());
                error = ResultCodeEnum.CONTENT_EXCEEDS_LIMIT;
                continue;
            }

            // qps限制
            if (!qpslimitService.modelQpsLimit(code)) {
                log.info("请求过多，qps限流，model:{}", code);
                error = ResultCodeEnum.ERROR_LIMITATION;
                continue;
            }

            try {
                params.modelCode = code;
                TextModelTextReqDTO reqDTO = params.textDto.toTextReqDTO(modelProperties.getMaxLength(assistantEnum, businessType, code));
                //设置角标启用按界面版本控制
                setEnableNetworkSearchCitationByVersion(assistantEnum, reqDTO);
                textModelExternalService.streamDialogue(code, reqDTO, event);
                success = true;
                break;
            } catch (Exception e) {
                log.error("调用文本大模型异常:{},对话id:{}", e.getMessage(), params.getDialogId(), e);
            }
        }

        if (!success) {
            log.warn("对话失败，智能调度大文本模型处理失败，对话id：{}", params.getDialogId());
            // 回滚权益
            memberService.consumeBenefitFail(params.getUserId(), RequestContextHolder.getPhoneNumber(), params.getDialogId());

            if (error != null) {
                params.handleFailMsg(error);
            } else {
                params.handleFailMsg(ResultCodeEnum.ERROR_AI_MODEL);
            }
        }

        return true;
    }

    /**
     * 角标开启设置
     *
     * @param assistantEnum
     * @param reqDTO
     */
    private void setEnableNetworkSearchCitationByVersion(AssistantEnum assistantEnum, TextModelTextReqDTO reqDTO) {
		if ((AssistantEnum.XIAO_TIAN.equals(assistantEnum) && !VersionUtil.xtH5VersionLt191())
				|| (AssistantEnum.YUN_MAIL.equals(assistantEnum) && !VersionUtil.mailPcVersionLt160())
				|| AssistantEnum.CLOUD_PHONE.equals(assistantEnum)) {
            if (ApiVersionEnum.V6.getVersion().equals(RequestContextHolder.getApiVersion())) {
                // add接口：v6场景
                // ①小天h5版本，1.9.1及以上开启角标
                // ②云邮（webai）pc版本，1.6.0及以上开启角标
                // ③TODO 后续其他的需要单独控制追加这边
                log.info("大模型联网搜索开启角标 assistantEnum:{}, H5Version:{}, PcVersion:{}", assistantEnum.getCode(),
                        RequestContextHolder.getH5Version(), RequestContextHolder.getPcVersion());
                reqDTO.setEnableNetworkSearchCitation(true);
                return;
            }
        }
        reqDTO.setEnableNetworkSearchCitation(false);
    }

    /**
     * 监听调用厂商事件处理
     *
     * @param params 辅助处理参数
     * @return 事件监听Event
     */
    private TextModelStreamEventListener handleEventListener(StreamEventListenerParams params) {
        final Map<String, String> logMap = MDC.getCopyOfContextMap();
        /**
         * 低版本，即需要合并思维链版本
         */
        final boolean mergeThinkContentVersion = ApiVersionEnum
                .isMergeThinkContentVersion(null != params.innerDTO ? RequestContextHolder.getApiVersion() : null);

        // 监听处理事件
        return new TextModelStreamEventListener() {

            @Override
            public boolean onEvent(TextModelBaseVo response) {
            	//设置用户信息
            	RequestContextHolder.setUserInfo(NumberUtil.getLongValue(params.getUserId()), params.getPhone(), params.getBelongsPlatform());
                MDC.setContextMap(logMap);
                CheckSystemTextModel checkSystemTextModel = null;
                try {
                    checkSystemTextModel = checkSystemConfig.getCheckSystemTextModel(params.modelCode);
                    // 停止对话
                    if (params.finishFlag) {
                        log.info("【流式对话文生文】对话已结束，结束大模型对话，对话id：{}", params.dialogId);
                        return false;
                    }
                    if (redisOperateRepository.getStopDialogue(params.dialogId)) {
                        // 对话结束
                        log.info("【流式对话文生文】对话已停止，对话id：{}", params.dialogId);
                        params.finishFlag = true;
                        params.handleStop();
                        return false;
                    }
                    List<NetworkSearchInfoVo> networkSearchInfoList = null;
                    // 最后一次事件（不同模型判断不一样）
                    boolean lastEvent = Boolean.TRUE.equals(response.getIsLast()) || params.outputTokens.equals(response.getOutputTokens());
                    params.outputTokens = response.getOutputTokens() == null ? 0 : response.getOutputTokens();

                    String thinkText = CharSequenceUtil.nullToDefault(response.getReasoningContent(), StringUtils.EMPTY);
                    String text = CharSequenceUtil.nullToDefault(response.getText(), StringUtils.EMPTY);
                    params.allThinkTextMsg.append(SseEmitterDataUtils.filterIgnoreString(thinkText));
                    params.allTextMsg.append(SseEmitterDataUtils.filterIgnoreString(text));

                    // 根据版本号判断是否合并思维链内容
                    if (mergeThinkContentVersion) {
                        if (StringUtils.isNotEmpty(thinkText)) {
                            if (!Boolean.TRUE.equals(this.getStartThinkContent())) {
                                this.setStartThinkContent(true);
                                // 开始思维链
                                text = TextModelUtil.TAG_START_THINK + thinkText;
                            } else {
                                text = thinkText;
                            }
                        } else {
                            if (StringUtils.isNotEmpty(text) && Boolean.TRUE.equals(this.getStartThinkContent())
                                    && !Boolean.TRUE.equals(this.getStartContent())) {
                                this.setStartContent(true);
                                // 结束思维链
                                text = TextModelUtil.TAG_END_THINK + text;
                            }
                        }
                    }

                    //未达到送审字数

                    //根据版本号判断是否合并思维链内容，非合并思维链和展示联网搜索来源信息，才直接追加
                    if (!mergeThinkContentVersion) {
                        params.addThinkMsg.append(thinkText);
                        networkSearchInfoList = response.getNetworkSearchInfoList();
                    }
                    if (StringUtils.isNotEmpty(text)) {
                        params.addMsg.append(text);
                    }

                    // 过滤送审大模型返回忽略文本方法
                    String newMsg = SseEmitterDataUtils
                            .filterIgnoreAudit(params.addThinkMsg.toString() + params.addMsg.toString());
                    if (!lastEvent && newMsg.length() < checkSystemTextModel.getOutputSize()) {
                        //【流式对话文生文】未达输出字数阈值
                        return true;
                    }

                    if (CharSequenceUtil.isNotBlank(newMsg)) {
                        params.allMsg.append(newMsg);
                        if (CheckSystemConfig.isAuditPart(checkSystemTextModel.getType()) || CheckSystemConfig.isAuditLocalPart(checkSystemTextModel.getType())) {
                            // 执行文本内容送审（部分送审）
                            if (CheckResultVO.isFail(checkSystemCheckTextDeal(params, checkSystemTextModel))) {
                                log.error("（增量部分送审）【流式对话文生文】生成文本送审失败，送审文本：{}，对话id：{}", params.allMsg, params.dialogId);
                                return params.handleFailMsg(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
                            }
                        }
                        if (mergeThinkContentVersion) {
                            // 发送SseEmitter消息
                            params.resVO.setFlowResult(new FlowTypeResultVO(params.addMsg.toString(),
                                    OutAuditStatusEnum.SUCCESS.getCode()));
                        } else {
                            // 发送SseEmitter消息
                            params.resVO.setFlowResult(new FlowTypeResultV2VO(this.getIndexAndIncrement(), params.addThinkMsg.toString(),
                                    params.addMsg.toString(), OutAuditStatusEnum.SUCCESS.getCode()));
                        }
                        //设置联网搜索信息来源
                        setNetworkSearchInfoList(params.resVO, networkSearchInfoList);
                        if (CollUtil.isNotEmpty(params.resVO.getNetworkSearchInfoList())) {
                            //大模型联网搜索信息来源存在则设置，需要入库到hbase
                            params.networkSearchInfoList = params.resVO.getNetworkSearchInfoList();
                        }
                        if (mergeThinkContentVersion) {
                            //低版本，不返回联网搜索来源字段
                            params.resVO.setNetworkSearchInfoList(null);
                        }
                        SseEmitterDataUtils.sendMsg(params.sseEmitter, BaseResult.success(params.resVO), params.completeFlag);

                        // 发送消息后参数处理
                        params.sendFlag = true;
                        params.addThinkMsg.setLength(0);
                        params.addMsg.setLength(0);
                    }

                    // 最后一次 更新会话任务状态
                    if (lastEvent) {

                        if (CheckSystemConfig.isAuditAll(checkSystemTextModel.getType())) {
                            // 执行文本内容送审（全量送审）
                            log.info("（全量送审一次）正常结束，对话id：{}", params.dialogId);
                            params.fullAuditFlag = true;

                            if (CheckResultVO.isFail(checkSystemCheckTextDeal(true, params, checkSystemTextModel))) {
                                log.error("（全量送审一次）正常结束，【流式对话文生文】生成文本送审失败，送审文本：{}，对话id：{}", params.allMsg, params.dialogId);
                                return params.handleFailMsg(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
                            }
                        }

                        // 对话结束
                        params.finishFlag = true;
                        return params.handleSuccessMsg(this, mergeThinkContentVersion, this.getIndexAndIncrement());
                    }
                } catch (YunAiBusinessException e) {
                    params.finishFlag = true;
                    log.error("【流式对话文生文】flowTypeHandle onEvent YunAiBusinessException response: {} | e:", JsonUtil.toJson(response), e);
                    return params.handleFailMsg(AiResultCode.getByCodeOrMsg(e.getCode(), e.getMessage()));
                } catch (ClientAbortException e) {
                    if (CheckSystemConfig.isAuditAll(checkSystemTextModel.getType())) {
                        // 执行文本内容送审（全量送审）
                        log.info("【流式对话文生文】对话中断，全量送审一次，对话id：{}", params.dialogId);
                        if (CheckResultVO.isFail(checkSystemCheckTextDeal(true, params, checkSystemTextModel))) {
                            log.error("【流式对话文生文】对话中断，全量送审失败，对话id：{}", params.dialogId);
                            return params.handleFailMsg(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
                        }
                    }

                    // 对话终止情况 （一般情况下是前端杀进程会出现）
                    log.info("【流式对话文生文】对话中断，对话id：{}，输出信息：{}", params.dialogId, params.allMsg);
                    return params.handleChatEndMsg();
                } catch (Exception e) {
                    log.error("【流式对话文生文】flowTypeHandle onEvent Exception response: {} | e:", JsonUtil.toJson(response), e);

                    params.finishFlag = true;
                    if (params.sendFlag) {
                        if (CheckSystemConfig.isAuditAll(checkSystemTextModel.getType())) {
                            // 执行文本内容送审（全量送审）
                            log.info("（全量送审一次）终止对话【流式对话文生文】生成文本送审，对话id：{}", params.dialogId);
                            params.fullAuditFlag = true;

                            if (CheckResultVO.isFail(checkSystemCheckTextDeal(true, params, checkSystemTextModel))) {
                                log.error("（全量送审一次）终止对话【流式对话文生文】生成文本送审失败，送审文本：{}，对话id：{}", params.allMsg, params.dialogId);
                                return params.handleFailMsg(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
                            }
                        }
                        // 已经发送过消息，然后抛出了非业务异常，对话终止情况 （一般情况下是前端杀进程会出现）
                        return params.handleChatEndMsg();
                    }

                    return params.handleFailMsg(AiResultCode.CODE_10000101);
                }

                return true;
            }

            private void setNetworkSearchInfoList(AlgorithmChatAddVO resVO,
                                                  List<NetworkSearchInfoVo> networkSearchInfoList) {
                if (CollUtil.isEmpty(networkSearchInfoList)) {
                    resVO.setNetworkSearchInfoList(Collections.emptyList());
                    return;
                }
                List<NetworkSearchInfo> networkSearchInfos = null;
                networkSearchInfos = new ArrayList<>();
                for (NetworkSearchInfoVo searchInfo : networkSearchInfoList) {
                    networkSearchInfos.add(NetworkSearchInfo.builder().index(searchInfo.getIndex())
                            .icon(searchInfo.getIcon()).siteName(searchInfo.getSiteName())
                            .title(searchInfo.getTitle()).url(searchInfo.getUrl()).build());
                }
                resVO.setNetworkSearchInfoList(networkSearchInfos);
            }

            /**
             * 送审处理
             */
            private CheckResultVO checkSystemCheckTextDeal(StreamEventListenerParams params,
                                                           CheckSystemTextModel checkSystemTextModel) {
                return checkSystemCheckTextDeal(false, params, checkSystemTextModel);
            }

            /**
             * 送审处理
             */
            private CheckResultVO checkSystemCheckTextDeal(Boolean isMustApi, StreamEventListenerParams params,
                                                           CheckSystemTextModel checkSystemTextModel) {
                CheckTypeEnum checkType = null;
                if (Boolean.TRUE.equals(isMustApi)) {
                    checkType = CheckTypeEnum.API;
                } else {
                    checkType = (CheckSystemConfig.isAuditLocalPart(checkSystemTextModel.getType()))
                            ? CheckTypeEnum.LOCAL
                            : CheckTypeEnum.API;
                }
                return checkSystemDomainService.checkSystemCheckText(checkSystemTextModel, checkType,
                        new CheckTextReqDTO(String.valueOf(params.dialogId), params.userId,
                                SseEmitterDataUtils.filterIgnoreAudit(String.valueOf(params.allMsg))));
            }

            @Override
            public void onClosed(EventSource eventSource) {
                MDC.setContextMap(logMap);
                log.info("【流式对话文生文】onClosed，flowTypeHandle sessionId:{} |  dialogueId:{} | userId:{} | onClosed msgAll:{}",
                        params.resVO.getSessionId(), params.dialogId, params.userId, params.allMsg);
                if (StringUtils.isBlank(params.allMsg)) {
                    //文本大模型无回复内容，设置默认文案
                    if (mergeThinkContentVersion) {
                        // 发送SseEmitter消息
                        params.resVO.setFlowResult(new FlowTypeResultVO(flowTypeProperties.getDefaultAnswer(),
                                OutAuditStatusEnum.SUCCESS.getCode()));
                    } else {
                        // 发送SseEmitter消息
                        params.resVO.setFlowResult(
                                new FlowTypeResultV2VO(this.getIndexAndIncrement(), flowTypeProperties.getDefaultAnswer(),
                                        StringUtils.EMPTY, OutAuditStatusEnum.SUCCESS.getCode()));
                    }
                    try {
                        SseEmitterDataUtils.sendMsg(params.sseEmitter, BaseResult.success(params.resVO),
                                params.completeFlag);
                    } catch (Exception e) {
                        log.error("【流式对话文生文】flowTypeHandle onClosed Exception | e:", e);
                    }
                }
                super.onClosed(eventSource);
                params.sendRecommandInfo(this, mergeThinkContentVersion, this.getIndexAndIncrement());
                SseEmitterDataUtils.complete(params.sseEmitter, params.completeFlag);
            }

            @Override
            public void onFailure(Throwable t, int code, String body) {
                MDC.setContextMap(logMap);

                if (!params.finishFlag && !params.fullAuditFlag) {
                    CheckSystemTextModel checkSystemTextModel = checkSystemConfig.getCheckSystemTextModel(params.modelCode);
                    if (CheckSystemConfig.isAuditAll(checkSystemTextModel.getType())) {
                        // 执行文本内容送审（全量送审）
                        log.info("（全量送审一次）其他异常中断【流式对话文生文】生成文本送审，对话id：{}", params.dialogId);
                        params.fullAuditFlag = true;

                        if (CheckResultVO.isFail(checkSystemCheckTextDeal(true, params, checkSystemTextModel))) {
                            log.error("（全量送审一次）其他异常中断【流式对话文生文】生成文本送审失败，送审文本：{}，对话id：{}", params.allMsg, params.dialogId);
                            params.handleFailMsg(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
                            return;
                        }
                    }
                }

                if (params.finishFlag) {
                    log.warn("【流式对话文生文】onFailure，对话已结束，对话id：{}", params.resVO.getDialogueId());
                    return;
                }
                if (ApiCommonResultCode.SENSITIVE_ERROR.getResultCode().equals(String.valueOf(code))) {
                    //强制转换，统一错误码
                    t = new YunAiBusinessException(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
                }
                log.warn("【流式对话文生文】flowTypeHandle onFailure code:{} | body:{} | msgAll:{}", code, body, params.allMsg);
                if (t instanceof YunAiBusinessException) {
                    YunAiBusinessException te = (YunAiBusinessException) t;
                    params.handleFailMsg(AiResultCode.getByCodeOrMsg(te.getCode(), te.getMessage()));
                    return;
                }

                params.handleFailMsg(ResultCodeEnum.ERROR_AI_MODEL);
            }
        };
    }

    /**
     * 流式监听辅助处理参数
     */
    @Data
    private class StreamEventListenerParams {
        /**
         * 全量内容（送审使用全量，保存数据库使用全量）
         */
        StringBuffer allMsg = new StringBuffer();
        /**
         * 增量内容（输出使用增量）
         */
        StringBuffer addMsg = new StringBuffer();
        /**
         * 增量思维链内容（输出使用增量）
         */
        StringBuffer addThinkMsg = new StringBuffer();
        /**
         * 全量文本内容（送审使用全量，保存数据库使用全量）
         */
        StringBuffer allTextMsg = new StringBuffer();
        /**
         * 全量思维链文本内容（送审使用全量，保存数据库使用全量）
         */
        StringBuffer allThinkTextMsg = new StringBuffer();
        /**
         * 大模型联网搜索内容
         */
        private List<NetworkSearchInfo> networkSearchInfoList;

        // 大模型token标识
        Integer outputTokens = 0;
        // 发送信息标识
        boolean sendFlag = false;
        // 结束对话标识
        boolean finishFlag = false;
        // 全量送审标识
        boolean fullAuditFlag = false;
        // 流式完成标识
        AtomicBoolean completeFlag = new AtomicBoolean(false);

        private String userId;
        private String phone = RequestContextHolder.getPhoneNumber();
        private Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();
        private Long sessionId;
        private Long dialogId;
        private String sourceChannel;
        private SseEmitter sseEmitter = null;
        private AlgorithmChatAddVO resVO = null;
        private String resourceContent = null;
        private String modelCode = "";
        private TextModelDTO textDto;
        private ChatAddInnerDTO innerDTO;


        public StreamEventListenerParams(ChatAddInnerDTO innerDTO, String resourceContent, List<TextModelMessageDTO> historyList, String promptTemplate) {
            this.innerDTO = innerDTO;
            this.sseEmitter = innerDTO.getSseEmitter();
            this.resourceContent = resourceContent;
            this.resVO = innerDTO.getRespParams();

            this.userId = innerDTO.getReqParams().getUserId();
            this.sessionId = innerDTO.getSessionId();
            this.dialogId = innerDTO.getDialogueId();
            this.sourceChannel = innerDTO.getContent().getSourceChannel();

            this.textDto = new TextModelDTO(innerDTO.getReqParams(), innerDTO.getRespParams(), resourceContent, historyList, promptTemplate);
        }

        /**
         * 发送推荐信息
         *
         * @param event
         * @param mergeThinkContentVersion
         * @param index
         */
        private void sendRecommandInfo(TextModelStreamEventListener event, Boolean mergeThinkContentVersion,
                                       Long index) {
            if (ObjectUtil.isNotEmpty(innerDTO.getFutures())) {
                // 返回推荐信息
                dialogueRecommendService.setFuturesResult(dialogId, resVO.getRecommend(), innerDTO.getFutures());
            }
            try {
                if (Boolean.TRUE.equals(mergeThinkContentVersion)) {
                    resVO.setFlowResult(new FlowTypeResultVO(StringUtils.EMPTY, OutAuditStatusEnum.SUCCESS.getCode()));
                } else {
                    resVO.setFlowResult(new FlowTypeResultV2VO(index, StringUtils.EMPTY, StringUtils.EMPTY,
                            OutAuditStatusEnum.SUCCESS.getCode()));
                }
                SseEmitterDataUtils.sendMsg(sseEmitter, BaseResult.success(resVO), completeFlag);
            } catch (Exception e) {
                log.warn("【流式对话文生文】发送推荐信息异常：", e);
            }
        }

        // 处理对话终止
        private boolean handleChatEndMsg() {
            return handleSuccessAndChatStatusMsg(ChatStatusEnum.CHAT_END.getCode());
        }

        // 处理对话成功
        private boolean handleSuccessMsg(TextModelStreamEventListener event, Boolean mergeThinkContentVersion, Long index) {
            sendRecommandInfo(event, mergeThinkContentVersion, index);

            return handleSuccessAndChatStatusMsg(ChatStatusEnum.CHAT_SUCCESS.getCode());
        }

        // 处理成功的情况
        private boolean handleSuccessAndChatStatusMsg(Integer chatStatus) {
            // sse结束
            SseEmitterDataUtils.complete(sseEmitter, completeFlag);

            // 更新会话状态
            algorithmChatHistoryService.updateOutResult(dialogId, OutAuditStatusEnum.SUCCESS.getCode(), chatStatus, null);

            // 更新hbase会话内容
            AiTextResultRespParameters result = new AiTextResultRespParameters(ResultCodeEnum.SUCCESS, networkSearchInfoList, String.valueOf(allThinkTextMsg), String.valueOf(allTextMsg));
            aiTextResultRepository.update(userId, dialogId, textDto.addAnswer(allTextMsg.toString()), result);

            // 文档资源类型，处理完成后删除大模型的文件
            handleTextModelFilesAsync();

            return true;
        }

        // 处理停止对话的情况，前端调停止接口修改状态，这里只需要更新输出审核状态
        private boolean handleStop() {
            // sse结束
            SseEmitterDataUtils.complete(sseEmitter, completeFlag);

            // 更新会话状态（对话成功）
            algorithmChatHistoryService.updateOutResultStop(dialogId);

            // 更新hbase会话内容
            AiTextResultRespParameters result = new AiTextResultRespParameters(ResultCodeEnum.SUCCESS, networkSearchInfoList, String.valueOf(allThinkTextMsg), String.valueOf(allTextMsg));
            aiTextResultRepository.update(userId, dialogId, textDto.addAnswer(allTextMsg.toString()), result);

            // 文档资源类型，处理完成后删除大模型的文件
            handleTextModelFilesAsync();

            return true;
        }

        // 处理失败的情况
        private boolean handleFailMsg(AiResultCode aiResultCode) {
            // 发送SseEmitter消息并关闭连接
            BaseResult<?> errRes = BaseResult.error(aiResultCode.getCode(), aiResultCode.getMsg());
            SseEmitterDataUtils.sendMsgAndComplete(sseEmitter, errRes, completeFlag);

            // 更新会话状态（对话失败）
            AiTextResultRespParameters result = new AiTextResultRespParameters(aiResultCode);
            algorithmChatHistoryService.updateOutResult(dialogId, OutAuditStatusEnum.FAIL.getCode(), ChatStatusEnum.CHAT_FAIL.getCode(), JsonUtil.toJson(result));

            // 更新hbase会话内容
            aiTextResultRepository.update(userId, dialogId, null, result);

            // 文档资源类型，处理完成后删除大模型的文件
            handleTextModelFilesAsync();

            return false;
        }

        private boolean handleFailMsg(ResultCodeEnum aiResultCode) {
            // 发送SseEmitter消息并关闭连接
            BaseResult<?> errRes = BaseResult.error(aiResultCode);
            SseEmitterDataUtils.sendMsgAndComplete(sseEmitter, errRes, completeFlag);

            // 更新会话状态（对话失败）
            AiTextResultRespParameters result = new AiTextResultRespParameters(aiResultCode);
            algorithmChatHistoryService.updateOutResult(dialogId, OutAuditStatusEnum.FAIL.getCode(), ChatStatusEnum.CHAT_FAIL.getCode(), JsonUtil.toJson(result));

            // 更新hbase会话内容
            aiTextResultRepository.update(userId, dialogId, null, result);

            // 文档资源类型，处理完成后删除大模型的文件
            handleTextModelFilesAsync();

            return false;
        }

        private void handleTextModelFilesAsync() {
            if (CollUtil.isEmpty(textDto.getTextModelFiles())) {
                return;
            }

            for (TextModelFileDTO textModelFile : textDto.getTextModelFiles()) {
                CompletableFuture.runAsync(() -> {
                    try {
                        TextModelFileDeleteReqDTO fileDeleteDTO = TextModelFileDeleteReqDTO.builder()
                                .businessId(String.valueOf(dialogId))
                                .fileId(textModelFile.getId())
                                .build();
                        log.info("【文档类型大模型对话】对话结束删除上传到大模型的文件 fileDeleteDTO:{}", JsonUtil.toJson(fileDeleteDTO));
                        externalBlianClient.longDeleteFile(fileDeleteDTO);
                    } catch (Exception e) {
                        log.info("【文档类型大模型对话】对话结束删除上传到大模型失败，businessId:{},fileId:{},e:", dialogId, textModelFile.getId(), e);
                    }
                });
            }
        }

    }

}
