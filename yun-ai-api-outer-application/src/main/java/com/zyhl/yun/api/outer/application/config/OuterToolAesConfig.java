package com.zyhl.yun.api.outer.application.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;

/**
 * 对外工具aes（当前给ai ppt使用）
 * 
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "outer.tool.aes")
public class OuterToolAesConfig {

	private static AES aes = null;
	
	private String key = "3fda899ca6d94ac3";

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	/**
	 * 获取aes
	 * 
	 * @return
	 */
	public AES getAES() {
		if (null == aes) {
			aes = SecureUtil.aes(this.getKey().getBytes());
		}
		return aes;
	}

}
