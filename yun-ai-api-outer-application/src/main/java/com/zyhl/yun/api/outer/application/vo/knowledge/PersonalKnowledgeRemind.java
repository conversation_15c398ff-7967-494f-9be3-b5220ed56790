package com.zyhl.yun.api.outer.application.vo.knowledge;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/18 12:01
 */
@Data
public class PersonalKnowledgeRemind implements Serializable {

    /**
     * 唯一标识
     */
    private Long oId;
    /**
     * 提醒用户时间
     */
    private String remindTime;
    /**
     * 提醒短信内容
     */
    private String content;
    /**
     * 提醒短信发送状态（0 未发送；1 已发送； -1 发送失败, 2正在处理）
     */
    private String sendStatus;
    /**
     * 提醒短信发送时间
     */
    private String sendTime;
    /**
     * 短信发送周期,1：仅一次，2：每天，3：工作日，4：每周，5：每月，6：每年
     */
    private String cycleType;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 手机号
     */
    private String userPhone;
    /**
     * 渠道
     */
    private String cp	  ;








}
