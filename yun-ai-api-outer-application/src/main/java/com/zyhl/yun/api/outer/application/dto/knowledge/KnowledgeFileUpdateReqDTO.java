package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 个人知识库资源信息更新操作请求参数
 *
 * <AUTHOR>
 */
@Data
@Slf4j
public class KnowledgeFileUpdateReqDTO extends BaseChannelDTO implements Serializable {


    /**
     * 渠道来源
     */
    private String sourceChannel;


    /**
     * 用户Id，默认从token获取，第三方平台调用时必填。
     */
    private String userId;
    /**
     * 知识库
     */
    private String baseId;

    /**
     * 资源ID
     */
    private String resourceId;
    /**
     * 名称，按照 utf8 编码规则最长 255字节，禁止出现以下9 个非法字符：'\'、'/'、':'、'*'、'?'、'"'、'<'、'>'、'|'
     */
    private String name;
    /**
     * 重命名规则：
     * 文件重名检查时不区分文件名大小写，文件重命名时保持文件名大小写不变，仅在主文件名后拼接当前时间戳，格式：{主文件名}_{yyyyMMdd}_{HHmmss}
     * 同名文件处理模式，可选值如下：
     * force_rename：当发现同名文件时，云端强制重命名
     * refuse：当云端存在同名文件时，拒绝更新文件
     * 默认是force_rename
     */
    private String renameMode = "force_rename";
}
