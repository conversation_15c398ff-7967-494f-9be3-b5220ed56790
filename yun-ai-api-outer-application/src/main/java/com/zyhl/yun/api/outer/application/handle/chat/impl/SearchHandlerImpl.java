package com.zyhl.yun.api.outer.application.handle.chat.impl;

import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.handle.chat.AbstractChatAddHandler;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 搜索意图
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SearchHandlerImpl extends AbstractChatAddHandler {

    @Override
    public int order() {
        return ExecuteSort.SEARCH.getSort();
    }

    @Override
    public boolean execute(ChatAddInnerDTO innerDTO) {
        /**
         * 执行的条件：
         * 1、应用类型 == chat（普通对话）
         * 2、小天助手渠道号
         * 3、搜索意图
         */
        return ApplicationTypeEnum.isChat(innerDTO.getReqParams().getApplicationType())
                && sourceChannelsProperties.isXiaoTian(innerDTO.getContent().getSourceChannel())
                && DialogueIntentionEnum.isSearchIntention(innerDTO.getIntentionCode());
    }

    @Override
    public boolean run(ChatAddInnerDTO innerDTO) {
        log.info("进入搜索功能处理");

        /** 搜索意图处理 */
        searchIntentionHandle(innerDTO);

        return false;
    }

}
