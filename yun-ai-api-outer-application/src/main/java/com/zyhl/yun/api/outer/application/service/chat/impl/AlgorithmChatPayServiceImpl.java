package com.zyhl.yun.api.outer.application.service.chat.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSONObject;
import com.zyhl.hcy.yun.ai.common.base.annotation.MethodExecutionTimeLog;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.AIAssistantPayReqDTO;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatPayService;
import com.zyhl.yun.api.outer.application.service.external.MemberCenterService;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.constants.RegConst;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.entity.TaskAiAbilityEntity;
import com.zyhl.yun.api.outer.domainservice.BenefitNoDomainService;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.task.TaskFeePaidStatusEnum;
import com.zyhl.yun.api.outer.enums.task.TaskFeeTypeEnum;
import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.repository.TaskAiAbilityRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.ChatPayResultVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlgorithmChatPayServiceImpl implements AlgorithmChatPayService {
    private static final String ALGORITHM_CHAT_CONTENT_BENEFIT = "yun:ai:api:outer:chatpay:lock:%s";

    private static final Integer CLIENT_INFO_MAX_LENGTH = 3;

    private final TaskAiAbilityRepository taskAiAbilityRepository;
    private final AlgorithmChatContentRepository algorithmChatContentRepository;
    private final BenefitNoDomainService benefitNoDomainService;
    private final MemberCenterService memberCenterService;

    @Resource
    private RedissonClient redissonClient;

    @Override
    @MethodExecutionTimeLog("对话结果扣费-serviceImpl")
    public ChatPayResultVO chatPay(AIAssistantPayReqDTO dto) {
        String clientType = null;
        Map<String, String> requestHeaders = RequestContextHolder.getRequestHeaders();
        String clientInfo = requestHeaders.get(ReqHeadConst.CLIENT_INFO);
		clientType = getClientType(clientType, clientInfo);
		Long dialogueId = Long.valueOf(dto.getDialogueId());
        log.info("AlgorithmChatPayService chatPay dialogueId:{}", dialogueId);
        RLock lock = null;
        try {
            // 获取redis锁
            String lockKey = String.format(ALGORITHM_CHAT_CONTENT_BENEFIT, dialogueId);
            lock = redissonClient.getLock(lockKey);
            boolean acquire = false;
            try {
                acquire = lock.tryLock(RedisConstants.WAIT_TIME_10, RedisConstants.LEASE_TIME_30, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("AlgorithmChatPayService lock.tryLock lockKey:{} error:", lockKey, e);
            } finally {
                log.info("对话结果扣费-权益扣费加锁，dialogueId={}，获取锁状态：{}", dialogueId, acquire);
                if (!acquire) {
                    // 获取锁失败，请求过多异常
                    throw new YunAiBusinessException(AiResultCode.CODE_01000007.getCode(), AiResultCode.CODE_01000007.getMsg());
                }
            }
            AlgorithmChatContentEntity chatContentEntity = algorithmChatContentRepository.getById(dialogueId);
            if (null == chatContentEntity) {
                // 空则返回权限不足
                throw new YunAiBusinessException(AiResultCode.CODE_01000004.getCode(), AiResultCode.CODE_01000004.getMsg());
            }
            Long taskId = chatContentEntity.getTaskId();
            TaskAiAbilityEntity taskEntity = taskAiAbilityRepository.getTaskEntity(taskId);
            if (null == taskEntity || !chatContentEntity.getUserId().equals(dto.getUserId())) {
                // 空则返回权限不足
                throw new YunAiBusinessException(AiResultCode.CODE_01000004.getCode(), AiResultCode.CODE_01000004.getMsg());
            }
            String sourceChannel = taskEntity.getSourceChannel();
            String algorithmCode = taskEntity.getAlgorithmCode();

			String benefitNo = getBenefitNo(clientType, taskEntity, sourceChannel, algorithmCode);
			String consumeSeq = null;
            // 判断权益是否开启执行
            consumeSeq = getBenefitStatus(clientType, dialogueId, taskEntity, sourceChannel, benefitNo);

            if (dealConsumSeq(dialogueId, taskId, taskEntity, benefitNo, consumeSeq)) {
                return ChatPayResultVO.builder().dialogueId(String.valueOf(dialogueId)).build();
            }
        } catch (Exception e) {
            if (e instanceof YunAiBusinessException) {
                throw e;
            }
            log.error("对话结果扣费-权益扣费异常 dialogueId={}", dialogueId, e);
        } finally {
            // redis解锁
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        log.error("对话结果扣费-权益扣费consumeSeq获取失败，默认任务扣费失败 dialogueId={}", dialogueId);
        // 默认扣费失败
        throw new YunAiBusinessException(AiResultCode.CODE_10000204.getCode(), AiResultCode.CODE_10000204.getMsg());
    }

	@NotNull
	private String getBenefitNo(String clientType, TaskAiAbilityEntity taskEntity, String sourceChannel, String algorithmCode) {
		if (TaskStatusEnum.isProcessing(taskEntity.getTaskStatus())) {
			// 任务处理中
			throw new YunAiBusinessException(AiResultCode.CODE_10000201.getCode(), AiResultCode.CODE_10000201.getMsg());
		}
		if (TaskStatusEnum.isTaskFail(taskEntity.getTaskStatus())) {
			// 任务失败
			throw new YunAiBusinessException(AiResultCode.CODE_10000202.getCode(), AiResultCode.CODE_10000202.getMsg());
		}
		if (TaskFeeTypeEnum.YES.getCode().equals(taskEntity.getFeeType())
				&& TaskFeePaidStatusEnum.PAID.getCode().equals(taskEntity.getFeePaidStatus())) {
			// 任务已扣费
			throw new YunAiBusinessException(AiResultCode.CODE_10000203.getCode(), AiResultCode.CODE_10000203.getMsg());
		}
		// 当前权益编号
		String benefitNo = benefitNoDomainService.getBenefitNoForTool(sourceChannel, clientType, algorithmCode);
		if (StringUtils.isBlank(benefitNo)) {
			// 当前权益编号空了，任务扣费失败,一般这个不会出现！！！
			throw new YunAiBusinessException(AiResultCode.CODE_10000204.getCode(),
					AiResultCode.CODE_10000204.getMsg() + ",原因" + TaskFeePaidStatusEnum.NO_PAYMENT.getDesc());

		}
		return benefitNo;
	}

	private String getClientType(String clientType, String clientInfo) {
		if (!CharSequenceUtil.isEmpty(clientInfo)) {
			// 网络类型|用户终端IP|客户端类型|客户端当前版本|设备品牌|设备型号|设备标识|设备MAC地址|操作系统和版本|设备分辨率|客户端语言类型|设备经度|设备纬度|设备名称|客户端上架的应用市场编码
			String[] clientInfoArr = clientInfo.split(RegConst.REG_SPLIT_STR);
			if (clientInfoArr.length >= CLIENT_INFO_MAX_LENGTH) {
				// 赋值客户端类型
				clientType = clientInfoArr[2];
			}
		}
		return clientType;
	}

	private boolean dealConsumSeq(Long dialogueId, Long taskId, TaskAiAbilityEntity taskEntity, String benefitNo, String consumeSeq) {
        if (StringUtils.isNotBlank(consumeSeq)) {
            log.info("对话结果扣费-权益扣费consumeSeq获取成功，dialogueId={}, consumeSeq={}", dialogueId, consumeSeq);
            String feePaidTime = DateUtil.format(new Date(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN);
            JSONObject json = new JSONObject();
            json.put("benefitNo", benefitNo);
            json.put("feePaidTime", feePaidTime);
            json.put("feeConsumeSeq", consumeSeq);
            //更新taskAiAbility
            if (taskAiAbilityRepository.updateFeePaidStatus(taskId, TaskFeeTypeEnum.YES.getCode(),
                    TaskFeePaidStatusEnum.PAID.getCode(), taskEntity.appendJsonExtInfo(json))) {
                // 扣费成功
                return true;
            }
        }
        return false;
    }

    /**
     * 获取权益状态
     *
     * @param clientType    客户端类型
     * @param dialogueId    对话id
     * @param taskEntity    任务实体
     * @param sourceChannel 渠道
     * @param benefitNo     权益编号
     * @return 权益状态
     */
    private String getBenefitStatus(String clientType, Long dialogueId, TaskAiAbilityEntity taskEntity, String sourceChannel, String benefitNo) {
        String consumeSeq;
        if (benefitNoDomainService.benefitNoForToolOpen(sourceChannel, clientType)) {
            try {
                consumeSeq = memberCenterService.consumeBenefit(taskEntity.getUserId(),
                        RequestContextHolder.getPhoneNumber(), benefitNo, String.valueOf(dialogueId));

            } catch (Exception e) {
                if (e instanceof YunAiBusinessException
                        && ((YunAiBusinessException) e).getCode().equals(AiResultCode.CODE_10000007.getCode())) {
                    throw new YunAiBusinessException(AiResultCode.CODE_10000013.getCode(),
                            AiResultCode.CODE_10000013.getMsg());
                }
                throw e;
            }
        } else {
            // 设置不开启，不调接口，默认TEST_当前时间戳为消费码
            log.info("设置不开启，不调接口，默认TEST_当前时间戳为消费码 dialogueId:{}", dialogueId);
            consumeSeq = "TEST_" + System.currentTimeMillis();
        }
        return consumeSeq;
    }
}