package com.zyhl.yun.api.outer.application.vo.knowledge;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/17 18:02
 */
@Data
public class PersonalKnowledgeNoteAttachment implements Serializable {
    /**
     * 个人知识库资源ID
     */
    private String resourceId;
    /**
     * 附件ID
     */
    private String attachmentId;
    /**
     * 网盘上附件id
     */
    private String rsId;
    /**
     * 绝对路径(含文件名)
     */
    private String relativePath;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 文件扩展名
     */
    private String fileExtension;
    /**
     * 附件类型audio|video|image|other
     */
    private String type;
    /**
     * 缩略图的真实URL地址(当附件为图片时有值)
     */
    private String thumbnailUrl;
    /**
     * 文件ID（新底座）
     */
    private String fileId;
    /**
     * 预览url
     */
    private String previewUrl;

    /**
     * 附件大小
     */
    private Long size;

    /**
     * 时长，单位s
     */
    public String duration;

    /**
     * 文件目录id
     */
    private String parentFileId;

    /**
     * 审核状态
     */
    private Integer auditStatus;

}
