package com.zyhl.yun.api.outer.application.dto;

import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description}
 * <p>
 *
 * <AUTHOR>
 * @since 4月14 2025
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FileCreateDTO extends BaseDTO implements Serializable {

  /**
   * 来源渠道
   */
  @NotBlank(message = "来源渠道不能为空！")
  private String sourceChannel;
  /**
   * 知识库Id
   */
  //@NotBlank(message = "知识库Id不能为空！")
  private String baseId;


  /**
   * 父目录id, 如果是根目录则传入"/"表示根目录,"/00019700101000000227"表示是通话记录备份目录
   */
  //@NotBlank(message = "父目录id不能为空！")
  private String parentFileId;

  /**
   * 文件id，覆盖写时必填
   */
  //@NotBlank(message = "文件id不能为空！")
  private String fileId;

  /**
   * 文件名称，按照 utf8 编码最长255字节，禁止出现以下9 个非法字符：'\'、'/'、':'、'*'、 '?'、'"'、'<'、'>'、'|'
   */
  @NotBlank(message = "文件名称不能为空！")
  private String name;

  /**
   * 文件大小，单位为 byte。 当文件类型为file时，该字段为必填
   */
  @NotNull(message = "文件大小不能为空！")
  private Long size;

  /**
   * 分片信息，仅分片上传需要指定 分片串行上传时，UploadPartInfo 仅需填写partNumber。
   * 分片并行上传时，第一个分片UploadPartInfo 仅需填
   * 写partNumber，除第一个分片外的其他分片需填写partNumber、partOffset、parallelHashCtx。
   */
  //@NotEmpty(message = "分片信息不能为空！")
  @Size(max = 100 , message = "分片信息不能超过100！")
  private UploadPartInfo[] partInfos;

  /**
   * 同名文件处理模式，可选值如下： ignore：允许同名文件
   * force_rename：当发现同名文件时，云端强制重命名，括号+编号自动命名，追加(1)、(2)等
   * refuse：当云端存在同名文件时，拒绝创建新文件，返回客户端已存在同名文件的详细信息
   * auto_rename：当发现同名文件时，检查hash是否相同，如果不同，云端重命名，括号+编号自动命名，追加(1)、(2)等，如果相同,拒绝创建，返回已存在的文件信息
   * 默认是force_rename
   */
  private String fileRenameMode;

  /**
   * 文件内容 hash 值，需要根据 contentHashAlgorithm 指定的算法计算
   */
  private String contentHash;

  /**
   * 文件内容hash算法名，当前只支持 sha1
   */
  private String contentHashAlgorithm;

  /**
   * 是否开启并行上传分片功能，默认不开启，和表单上传互斥
   */
  private Boolean parallelUpload;

  /**
   * 是否采用表单上传，默认不开启，表单上传最大支持单文件5GB，和并行上传互斥
   */
  private Boolean formUpload;

}
