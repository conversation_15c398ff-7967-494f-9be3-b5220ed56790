package com.zyhl.yun.api.outer.application.config;

import java.net.URLEncoder;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import com.zyhl.yun.api.outer.constants.ReplaceConstants;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.Data;

/**
 * 对话文本工具意图业务配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties("chat.text-tool.business")
public class ChatTextToolBusinessConfig {
	
	/**
	 * 参数名称-ai编程助手文件名称列表
	 */
	public static final String ARGUMENT_AI_CODER_FILE_NAME = "aiCoderFileName";

	/**
	 * 参数名称-ai生成回忆相册关键字列表
	 */
	public static final String ARGUMENT_AI_MEMORY_ALBUM_KEYWORD = "keyword";

    /**
     * 参数名称-aippt历史搜索关键字参数
     */
    public static final String ARGUMENT_AI_PPT_HISTORY_SEARCH = "depend";

    /**
     * 参数名称-aippt历史搜索关键字值
     */
    public static final String ARGUMENT_AI_PPT_HISTORY_SEARCH_VALUE = "historySearch";

	/**
	 * 参数名称-ai智能会议标题列表
	 */
	public static final String ARGUMENT_AI_MEETING_TITLE_LIST = "titleList";

	/**
	 * 参数名称-ai智能会议时间列表
	 */
	public static final String ARGUMENT_AI_MEETING_TIME_LIST = "timeList";

	/**
	 * 参数名称-ai智能会议地点列表
	 */
	public static final String ARGUMENT_AI_MEETING_PLACE_LIST = "placeList";

	/**
	 * 参数名称-ai智能参会人员地点列表
	 */
	public static final String ARGUMENT_AI_MEETING_RECIPIENT_LIST = "recipientList";
	
	/**
	 * 参数名称-typeList
	 */
	public static final String ARGUMENT_AI_MEETING_TYPE_LIST = "typeList";
	
	/**
	 * 参数名称-contentList
	 */
	public static final String ARGUMENT_AI_MEETING_CONTENT_LIST = "contentList";
	
	/**
	 * ai编程助手业务数据
	 */
	private AiCoderBusiness aiCoder = new AiCoderBusiness();

	/**
	 * ai会议纪要业务数据
	 */
	private AiMeetingMinutesBusiness aiMeetingMinutes = new AiMeetingMinutesBusiness();

	/**
	 * AI的PPT生成业务数据
	 */
	private AiPptGenerateBusiness aiPptGenerate = new AiPptGenerateBusiness();

	/**
	 * AI画图
	 */
	private AiTextGeneratePictureBusiness textGeneratePictureBusiness = new AiTextGeneratePictureBusiness();

	/**
	 * ai图书快速阅读业务数据
	 */
	private AiSpeedRead aiSpeedRead = new AiSpeedRead();

	/**
	 * ai生成回忆相册
	 */
	private AiMemoryAlbum aiMemoryAlbum = new AiMemoryAlbum();

	/**
	 * ai智能体-智能会议
	 */
	private IntelligentMeeting intelligentMeeting = new IntelligentMeeting();

	@Data
	public static class AiCoderBusiness {

		/**
		 * 默认提示词
		 */
		private String prompt = "AI_CODER_ASSISTANT";

	}

	@Data
	public static class AiPptGenerateBusiness {

		/**
		 * 最大历史记录数
		 */
		private Integer maxHistoryCount = 10;
		/**
		 * 是否搜索知识库，默认true
		 */
		private boolean searchKnowledgeEnable = true;

		/**
		 * ppt默认存储路径(个人云的位置)
		 */
		private String personalPath = "/AI文件库/AI生成PPT";

		/**
		 * ppt封面的位置
		 */
		private String personalCoverPath = "/我的应用收藏/AI助手/PPT封面";

		/**
		 * ppt模板白名单
		 */
		private List<PPTTemplateWhite> templateWhiteList;

		/**
		 * h5端预览路径
		 */
		private String h5PreviewPath = "https://test.yun.139.com/ai/ai-test/aippt/web/?taskid={taskid}&token={token}";

		/**
		 * pc端预览路径
		 */
		private String pcPreviewPath = "https://test.yun.139.com/ai/ai-test/aippt/web/?taskid={taskid}&token={token}";

		/**
		 * h5端ppt路径
		 */
		private String h5PptPath = "https://test.yun.139.com/ai/ai-test/aippt/web/?pptid={pptid}&token={token}";

		/**
		 * pc端ppt路径
		 */
		private String pcPptPath = "https://test.yun.139.com/ai/ai-test/aippt/web/?pptid={pptid}&token={token}";

		public String getH5Url(Long taskId, Long pptId) {
			if (null == pptId || pptId <= 0) {
				return ChatTextToolBusinessConfig
						.replaceToken(h5PreviewPath.replace("{taskid}", String.valueOf(taskId)));
			}
			return ChatTextToolBusinessConfig.replaceToken(h5PptPath.replace("{pptid}", String.valueOf(pptId)));
		}

		public String getPcUrl(Long taskId, Long pptId) {
			if (null == pptId || pptId <= 0) {
				return ChatTextToolBusinessConfig
						.replaceToken(pcPreviewPath.replace("{taskid}", String.valueOf(taskId)));
			}
			return ChatTextToolBusinessConfig.replaceToken(pcPptPath.replace("{pptid}", String.valueOf(pptId)));
		}
	}

	@Data
	public static class AiMeetingMinutesBusiness {

		/**
		 * 默认提示词
		 */
		private String prompt = "MINUTES_ORGANIZATION_GENERAL";

	}

	@Data
	public static class AiTextGeneratePictureBusiness {

		/**
		 * 默认提示词
		 */
		private String prompt = "AI_TEXT_GENERATE_PICTURE";

	}

	@Data
	public static class AiSpeedRead {

		/**
		 * 默认提示词
		 */
		private String prompt = "SPEED_READ_SUMMARY_SYNTHESIS";
	}

	@Data
	public static class AiMemoryAlbum {

		private String analysisQuestionTitle = "已分析问题类型";
		private String analysisTip = "AI生成回忆相册";

		private String schedulerLlmTitle = "已调度LLM大模型";

		private String analysisKeywordTitle = "已分析搜索关键词";

		private String searchImageEmptyTitle = "未找到符合要求的图片";
		
		private String searchImageOneEmptyTitle = "未找到合适的图片";

		private String searchImageErrorTip = "没有找到合适的照片，您可以再上传一些";

		private String searchImageFindTitle = "已找到%s张云盘图片";

		private String selectImageEmptyTitle = "精选图片数量较少";

		private String selectImageErrorTip = "精选的靓照有点少，您可以再上传一些照片";

		private String selectImageFindTitle = "已精选相关图片";

        /**
         * 搜索图片每页数目，默认2000条
         */
        private int searchImagePageSize = 2000;

        private String selectImageFindTip = "精选出%s张照片生成回忆故事";

		private String albumGenErrorTitle = "生成回忆相册失败";

		private String albumGenErrorTip = "生成回忆相册失败，请稍候再试";

		private String albumGenSuccTitle = "已生成回忆相册";

		private String imageOnlyOneTitle = "一张图片无法生成故事";
		
		private String imageOnlyOneErrorTip = "一张照片有点少，您可以试试多选一些去生成故事～";
		
		private String modelCode;

        private String modelPrompt;

        /**
         * 原文搜图，默认是
         */
        private boolean originalSearchQuery = true;


		/**
		 * 搜索过滤匹配后面字符串，替换为""
		 */
		private List<String> searchFilterEndStrs;

		/**
		 * 搜索过滤匹配后面字符串，替换为""
		 *
		 * @param dialogue
		 * @return
		 */
		public String searchFilterEndStrsExecute(String dialogue) {
			if (CollUtil.isEmpty(this.getSearchFilterEndStrs()) || StringUtils.isEmpty(dialogue)) {
				return dialogue;
			}
			for (String str : this.getSearchFilterEndStrs()) {
				if (dialogue.endsWith(str)) {
					return dialogue.replace(str, "");
				}
			}
			return dialogue;
		}
	}

	@Data
	public static class IntelligentMeeting {

		/**
		 * 默认大模型
		 */
		private String defaultModelCode = "blian";
		/**
		 * 默认ppt模板
		 */
		private String defaultPptTemplateId = "2";

		/**
		 * 会议通知邮件大模型
		 */
		private String meetingMailModelCode = "qwen";
		
		/**
		 * 会议通知邮件大模型提示词
		 */
		private String meetingMailPrompt = "";

		/**
		 * 会议通知ppt邮件大模型
		 */
		private String pptMailModelCode = "qwen";

		/**
		 * 会议通知ppt邮件大模型提示词
		 */
		private String pptMailPrompt = "";

		
		// --- 固定 -- //

		private String titlePrefix = "【会议通知】";

		private String roomName = "会议室";

		// ---步骤使用--//

		private String meetingMemberTitle = "确认参会人员";

		private String meetingMemberTip = "通过通讯录匹配参会名单。";

		private String meetingRoomTitle = "准备会议室";

		private String meetingRoomTip = "预定%s，确保会议室可使用状态。";

		private String meetingAgendaTitle = "制定会议议程";

		private String meetingAgendaTip = "解析%s所需议程，制定详细会议议程，包括时间分配、发言内容、发言顺序等。";

		private String meetingNoticeTitle = "编写会议通知邮件";

		private String meetingNoticeTip = "生成邮件主题，拟定会议邮件通知内容，包括会议时间、地点、参会人员、会议主题、会议议程等。";

		private String sendMailNoticeTitle = "发送邮件";
		
		private String sendMailNoticeTip = "使用139邮箱发送会议邀请，自动识别参会人员邮箱地址，用户确认信息无误后发送邮件。";

		private String sendMailPptTitle = "发送邮件";
		
		private String sendMailPptTip = "使用139邮箱发送PPT，自动识别参会人员邮箱地址，生成邮件主题、邮件内容，关联附件，用户确认信息无误后发送邮件。";
		
		private String sendMailPptNoticeRecTitle = "确认收件人";

		private String sendMailPptNoticeRecTip = "分析会议参会人员，识别收件人。";

		private String sendMailPptNoticeMailTitle = "编写会议通知邮件";

		private String sendMailPptNoticeMailTip = "生成邮件主题，确认方案附件，拟定会议邮件内容。";
		
		private String pptOutlineTextModelTitle = "PPT大纲";
		
		private String pptOutlineTitle = "生成PPT大纲";

		private String pptOutlineTip = "根据录音笔记内容，智能总结会议要点及待办事项，生成PPT大纲。";
		
		private String autoGenMailContentTitle = "已帮您自动生成会议通知邮件内容";
		
		private String autoGenPptMailContentTitle = "已帮您自动生成会议方案邮件内容";

		private String recipientTitie = "**收件人**：%s\n";
		private String subjectTitie = "**主题**：%s\n";
		private String attachmentTitie = "**附件**：%s\n";
		private String contentTitie = "**正文**：\n";

		
		private String sendMailErrorMsg = "邮件发送失败：%s，请点击【编辑邮件】核对后重发。";
		
		/**
		 * 拆分邮件内容
		 * 
		 * @param outContent 内容
		 * @return 邮件内容
		 */
		public String splitMailContent(String outContent) {
			if (StringUtils.isEmpty(outContent)) {
				return null;
			}
			int contentTitieIndex = outContent.indexOf(this.getContentTitie());
			if (-1 == contentTitieIndex) {
				return outContent;
			}
			return outContent.substring(contentTitieIndex + contentTitie.length());
		}

		/**
		 * 移除标题前缀 titlePrefix 为 ""
		 * 
		 * @param title 标题
		 * @return
		 */
		public String removeTitlePrefix(String title) {
			if (null == title) {
				return null;
			}
			return title.replace(this.getTitlePrefix(), StringUtils.EMPTY);
		}
	}
	
	/**
	 * token替换方法，aes加密
	 *
	 * @param text
	 * @return
	 */
	@SuppressWarnings("deprecation")
	public static String replaceToken(String text) {
		OuterToolAesConfig outerToolAesConfig = SpringUtil.getBean(OuterToolAesConfig.class);
		return text.replace(ReplaceConstants.REPLACE_TOKEN,
				URLEncoder.encode(outerToolAesConfig.getAES().encryptBase64(RequestContextHolder.getToken())));
	}

	@Data
	public static class PPTTemplateWhite {

		/**
		 * ppt模板id数组白名单
		 */
		private List<String> ids;

		/**
		 * ppt模板用户白名单
		 */
		private List<String> userList;
	}
}
