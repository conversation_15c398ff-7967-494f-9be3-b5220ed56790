package com.zyhl.yun.api.outer.application.dto;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;

/**
 * AI编辑更新请求DTO
 *
 * <AUTHOR> Assistant
 */
@Data
public class AiEditUpdateReqDTO {

    /**
     * 对话ID
     */
    @NotBlank(message = "对话ID不能为空")
    private String dialogueId;

    /**
     * 文件ID
     */
    @NotBlank(message = "文件ID不能为空")
    private String fileId;

    /**
     * 文件名
     */
    @NotBlank(message = "文件名fileName不能为空")
    private String fileName;


    /**
     * 文件父目录Id
     */
    @NotBlank(message = "parentFileId文件父目录Id不能为空")
    private String parentFileId;

    /**
     * 更新的Markdown内容
     */
    @NotBlank(message = "更新内容不能为空")
    private String content;


} 