package com.zyhl.yun.api.outer.application.service.chat.impl;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.service.ApplicationService;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatMessageService;
import com.zyhl.yun.api.outer.application.util.ChatAddUtils;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatMessageEntity;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.ChatMessageStarEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.repository.AlgorithmChatMessageRepository;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 会话信息操作
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AlgorithmChatMessageServiceImpl implements AlgorithmChatMessageService {

    @Resource
    private AlgorithmChatMessageRepository algorithmChatMessageRepository;
    @Resource
    private SourceChannelsProperties channelsProperties;
    @Resource
    private UidGenerator uidGenerator;
    @Resource
    private ApplicationService applicationService;

    private static final String SYMBOL = " | ";

    @Override
    public long save(AlgorithmChatAddDTO dto) {
        Long sessionId = null;
        if (!CharSequenceUtil.isEmpty(dto.getSessionId())) {
            sessionId = Long.parseLong(dto.getSessionId());
        }

        // 智能体会话，需要先查询会话id
        if (sessionId == null && ApplicationTypeEnum.isIntelligen(dto.getApplicationType())) {
            sessionId = applicationService.intelligentGetSessionId(dto);
        }

        AlgorithmChatMessageEntity messageEntity = null;
        if (sessionId != null) {
            log.info("更新会话信息，会话id：{}", sessionId);
            messageEntity = AlgorithmChatMessageEntity.builder().id(sessionId).updateTime(DateUtil.date()).build();
            algorithmChatMessageRepository.updateMessage(messageEntity);
            log.info("更新会话信息成功，会话id：{}", sessionId);

            return sessionId;
        } else {
            // 新增会话信息
            messageEntity = addChatMessage(dto, null);
        }

        return messageEntity.getId();
    }
    
    @Override
    public long save(ChatAddInnerDTO innerDTO) {
        AlgorithmChatMessageEntity messageEntity = null;
        // 保存会话（true-新增；false-更新）
        Boolean saveMessage = innerDTO.getSaveMessage();
        Long sessionId = innerDTO.getSessionId();
        if (ObjectUtil.isNotNull(saveMessage) && saveMessage) {
            AlgorithmChatAddDTO dto = innerDTO.getReqParams();
            // 新增会话信息（指定sessionId）
            messageEntity = addChatMessage(dto, sessionId);
            return messageEntity.getId();
        } else {
            log.info("更新会话信息，会话id：{}", sessionId);
            messageEntity = AlgorithmChatMessageEntity.builder().id(sessionId).updateTime(DateUtil.date()).build();
            algorithmChatMessageRepository.updateMessage(messageEntity);
            log.info("更新会话信息成功，会话id：{}", sessionId);

            return sessionId;
        }
    }
    
    @Override
    public boolean exist(String id, String userId) {
        AlgorithmChatMessageEntity entity = algorithmChatMessageRepository.queryBySessionId(id, userId);
        return Objects.nonNull(entity);
    }

    private AlgorithmChatMessageEntity addChatMessage(AlgorithmChatAddDTO dto, Long sessionId) {
        AlgorithmChatMessageEntity messageEntity = new AlgorithmChatMessageEntity();
        messageEntity.setId(null == sessionId ? uidGenerator.getUID() : sessionId);
        messageEntity.setUserId(dto.getUserId());
        messageEntity.setTitle(createTitle(dto));
        //星标设置
        messageEntity.setEnableStar(StringUtils.isNoneBlank(ChatAddUtils.getOrderTip(dto.getContent().getExtInfo()))
                ? ChatMessageStarEnum.YES.getCode()
                : ChatMessageStarEnum.NO.getCode());
        messageEntity.setCreateTime(DateUtil.date());
        messageEntity.setUpdateTime(DateUtil.date());

        // 业务类型、应用类型、应用id
        messageEntity.setBusinessType(channelsProperties.getType(dto.getContent().getSourceChannel()));
        messageEntity.setApplicationId(CharSequenceUtil.emptyToDefault(dto.getApplicationId(), "0"));
        messageEntity.setApplicationType(ApplicationTypeEnum.getByCodeDefaultChat(dto.getApplicationType()).getCode());

        boolean success = false;
        try {
            success = algorithmChatMessageRepository.saveMessage(messageEntity);
        } finally {
            log.info("新增会话信息 success:{}, 会话id:{}", success, messageEntity.getId());
        }
        return messageEntity;
    }
    
	/**
     * 生成会话标题
     *
     * @param dto 请求参数
     * @return 会话标题
     */
    private String createTitle(AlgorithmChatAddDTO dto) {
        String dialogue = dto.getContent().getDialogue();
        String extInfo = dto.getContent().getExtInfo();
        String commands = dto.getContent().getCommands();
        boolean dialogueExist = CharSequenceUtil.isNotEmpty(dialogue);

        DialogueIntentionEnum intentionEnum = DialogueIntentionEnum.getByCodeOrDefault(commands);

        // 意图结果
        String intentionName = intentionEnum.getName();

        String orderTip = ChatAddUtils.getOrderTip(extInfo);
        if (StringUtils.isEmpty(orderTip)) {
            // 对话存在返回对话，对话不存在返回意图名称（兜底）
            return dialogueExist ? dialogue : intentionName;
        } else {
            // 对话存在返回指令名称+对话，对话不存在只返回指令名称
            return dialogueExist ? orderTip.concat(SYMBOL).concat(dialogue) : orderTip;
        }
    }
    
}
