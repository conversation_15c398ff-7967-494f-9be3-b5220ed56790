package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.FileOperationUtil;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.enums.YunAiYunDiskResultCode;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueAttachmentDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.pojo.LocalFileInfo;
import com.zyhl.yun.api.outer.application.chatv2.service.BenefitService;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatAddCheckService;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatCommonService;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatDialogueRecommendService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.config.DialogueRecommendProperties;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.config.textmodel.ModelPromptProperties;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.valueobject.MailInfo;
import com.zyhl.yun.api.outer.domain.valueobject.NoteInfo;
import com.zyhl.yun.api.outer.domain.vo.DocumentParsingResultVO;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.domainservice.CheckSystemDomainService;
import com.zyhl.yun.api.outer.domainservice.QpsLimitService;
import com.zyhl.yun.api.outer.domainservice.ResourceInfoDomainService;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.SseNameEnum;
import com.zyhl.yun.api.outer.external.DocumentParsingExternalService;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.ExternalCheckSystemClient;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 文档流式对话
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TextModelDocSseHandlerImpl extends AbstractChatAddV2Handler {

    /**
     * 当前执行顺序
     */
    private final ExecuteSort thisExecuteSort = ExecuteSort.TEXT_MODE_DOC_SSE;

    @Resource
    private BenefitService benefitService;
    @Resource
    private DataSaveService dataSaveService;
    @Resource
    private ChatAddCheckService chatAddCheckService;
    @Resource
    private ChatConfigServiceDomainService chatConfigServiceDomainService;
    @Resource
    private DocumentParsingExternalService documentParsingExternalService;
    @Resource
    private QpsLimitService qpslimitService;
    @Resource
    private ModelPromptProperties modelPromptProperties;
    @Resource
    private ModelProperties modelProperties;
    @Resource
    private AlgorithmChatContentRepository algorithmChatContentRepository;
    @Resource
    private TextModelExternalService textModelExternalService;
    @Resource
    private ResourceInfoDomainService resourceInfoDomainService;
    @Resource
    private CheckSystemDomainService checkSystemDomainService;
    @Resource
    private ChatCommonService chatCommonService;
    @Resource
    private ChatDialogueRecommendService recommendService;
    @Resource
    private DialogueRecommendProperties recommendProperties;

    @Override
    public void afterPropertiesSet() throws Exception {
        //支持的业务初始化
        List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        thisBusinessTypes.add(ChatBusinessTypeEnum.CLOUD_PHONE);
        thisBusinessTypes.add(ChatBusinessTypeEnum.MESSAGE_5G);
        this.setBusinessTypes(thisBusinessTypes);
    }

    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public boolean execute(ChatAddHandleDTO handleDTO) {
        // 非文档资源不执行
        DialogueAttachmentDTO attachmentDTO = handleDTO.getInputInfoDTO().getAttachment();
        if (ObjectUtil.isEmpty(attachmentDTO) || ObjectUtil.isEmpty(attachmentDTO.getAttachmentTypeList())) {
            return false;
        }

        // 是否文档对话类型
        return handleDTO.isReqResourceDocSse();
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        log.info("进入{}", thisExecuteSort.getDesc());

        // 获取资源内容
        String resourceContent = getResourceContent(handleDTO);
        if (ObjectUtil.isEmpty(resourceContent)) {
            log.info("获取资源内容为空 dialogueId:{}", handleDTO.getDialogueId());
            return false;
        }

        // 过滤提示词
        String filterPrompt = handleDTO.getInputInfoDTO().getPrompt();

        // 需要引流和推荐的文档后缀
        List<String> recomendExtList = recommendProperties.getTextToolRecomendFileExtList();
        // 设置工具推荐列表对象【如果发出文档类型为PPT、WORD、PDF、TXT（doc/docx/pdf/ppt/pptx/txt），则显示文档处理工具推荐；如果发出文档非上述格式，则不显示文档处理工具推荐】
        log.info("判断设置推荐对象 dialogueId:{}, resourceExt:{}, recomendExtList:{}", handleDTO.getDialogueId(),
                handleDTO.getResourceExt(), JSONUtil.toJsonStr(recomendExtList));
        if (handleDTO.isReqResourceOnlyDocumentSse() && null != handleDTO.getResourceExt()
                && recomendExtList.contains(handleDTO.getResourceExt().toLowerCase())) {
            log.info("设置图书快速阅读中部推荐对象");
            // 设置图书快速阅读中部推荐对象
            handleDTO.getRespVO().getMiddleRecommend()
                    .setToolList(recommendService.getSpeedReadMiddleRecommendListVo(handleDTO));
            log.info("设置文本工具推荐");
            // 文本工具推荐
            handleDTO.getRespVO().getRecommend()
                    .setToolList(recommendService.getToolRecommendListVo(handleDTO, filterPrompt));
        } else {
            handleDTO.getRespVO().getRecommend().setToolList(null);
            handleDTO.getRespVO().getMiddleRecommend().setToolList(null);
        }
        // 扣减权益
        benefitService.consumeBenefit(handleDTO.getReqDTO(), RequestContextHolder.getPhoneNumber(),
                handleDTO.getDialogueId());

        // 保存到hbase（保存resourceContent到hbase）
        dataSaveService.saveTextResult(handleDTO, "", resourceContent);

        // 保存数据库
        dataSaveService.add(handleDTO, ChatStatusEnum.CHAT_IN);


        // 监听器
        SseEventListener event = new SseEventListener(handleDTO, null);
        event.getSseEmitterOperate().setSseName(SseNameEnum.DOC_SSE.getCode());

        // 智能调度
        docDialogue(event, handleDTO, resourceContent);

        return false;
    }

    /**
     * 长文本普通对话，指定模型
     *
     * @param event 流式监听事件
     */
    private void docDialogue(SseEventListener event, ChatAddHandleDTO handleDTO, String resourceContent) {

        // 获取用户设置的模型，没有设置则使用默认模型
        ChatConfigEntity chatConfigEntity = chatConfigServiceDomainService.getUserCanUseModel(event.getUserId(), event.getPhone(), handleDTO.getAssistantEnum(), handleDTO.getBusinessType());
        event.setModelCode(chatConfigEntity.getModelType());
        event.getSseEmitterOperate().setSseName(SseNameEnum.DOC_SSE.getCode());

        // qps限制
        if (!qpslimitService.modelQpsLimit(chatConfigEntity.getModelType())) {
            log.info("localLongTextDialogue 请求过多，qps限流，model:{}", chatConfigEntity.getModelType());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_LIMITATION);
        }

        DialogueInputInfoDTO inputInfo = handleDTO.getReqDTO().getDialogueInput();
        // 获取用户对话
        String dialogue = inputInfo.getDialogue();
        if (null == dialogue) {
            dialogue = StringUtils.EMPTY;
        }
        if (null == resourceContent) {
            resourceContent = StringUtils.EMPTY;
        }
        String fullText = (dialogue + resourceContent);

        // 从配置获取提示词
        String newPrompt = chatCommonService.getPromptByConfig(event);
        // 从db获取提示词
        String finalPrompt = chatAddCheckService.getDialoguePrompt(newPrompt, handleDTO.getReqDTO().getSourceChannel());
        // 追加重新生成提示词
        finalPrompt = chatAddCheckService.getAppendEnableRegenerate(inputInfo, finalPrompt);
        // 重新设置最后匹配的提示词
        event.getTextDto().setPrompt(finalPrompt);

        // 输入字数判断
        Integer lengthLimit = modelProperties.getLengthLimit(handleDTO.getAssistantEnum(), handleDTO.getBusinessType(), chatConfigEntity.getModelType());
        int maxLenLimit = Objects.isNull(lengthLimit) ? Integer.MAX_VALUE : (lengthLimit - finalPrompt.length());

        // 新的对话信息
        TextModelMessageDTO msgDTO = new TextModelMessageDTO();
        msgDTO.setRole(TextModelRoleEnum.USER.getName());
        msgDTO.setContent(fullText.substring(0, Math.min(maxLenLimit, fullText.length())));
        msgDTO.setCommand(finalPrompt);

        // 不需要历史对话信息
        TextModelTextReqDTO reqDTO = event.getTextDto().toTextReqDTO(null);
        TextModelTextReqDTO req = genTextModelTextReq(reqDTO, Collections.singletonList(msgDTO));
        textModelExternalService.streamDialogue(event.getModelCode(), req, event);

        // 更新模型编码
        algorithmChatContentRepository.updateModelCode(event.getDialogId(), event.getModelCode());
    }

    /**
     * 获取资源内容
     *
     * @param handleDTO dto
     * @return resourceContent
     */
    private String getResourceContent(ChatAddHandleDTO handleDTO) {
        DialogueAttachmentDTO attachmentDTO = handleDTO.getInputInfoDTO().getAttachment();

        // 资源内容
        String resourceContent = "";
        String resourceName = "";

        if (ResourceTypeEnum.contains(attachmentDTO.getAttachmentTypeList(), ResourceTypeEnum.MAIL)) {
            log.info("读取邮件内容，邮件参数：{}", JsonUtil.toJson(attachmentDTO.getMailList()));
            // 获取邮件内容
            String mailId = attachmentDTO.getMailList().get(0).getMailId();
            String sid = attachmentDTO.getMailList().get(0).getSid();
            String rmkey = attachmentDTO.getMailList().get(0).getRmkey();
            resourceContent = resourceInfoDomainService.getMailContent(sid, rmkey, mailId);
        } else if (ResourceTypeEnum.contains(attachmentDTO.getAttachmentTypeList(), ResourceTypeEnum.NOTE)) {
            log.info("读取笔记内容，笔记参数：{}", JsonUtil.toJson(attachmentDTO.getNoteList()));
            // 获取笔记内容
            NoteInfo noteInfo = attachmentDTO.getNoteList().get(0);
            String noteId = noteInfo.getNoteId();
            resourceContent = resourceInfoDomainService.getNoteContent(RequestContextHolder.getToken(), noteId);
            if (StringUtils.isNotEmpty(noteInfo.getTitle())) {
                resourceName = noteInfo.getTitle();
            } else {
                resourceName = "笔记对话";
            }
        } else if (ResourceTypeEnum.contains(attachmentDTO.getAttachmentTypeList(), ResourceTypeEnum.CLOUD_DISK_DOCUMENT)) {
            log.info("读取文档内容，文档参数：{}", JsonUtil.toJson(attachmentDTO.getFileList()));
            // 下载文档 并 获取文档内容
            DocumentParsingResultVO docContent;
            // 文档资源
            File file = attachmentDTO.getFileList().get(0);
            try {
                List<String> fileIdList = attachmentDTO.getFileList().stream().map(File::getFileId).collect(Collectors.toList());
                List<LocalFileInfo> localPathPath = chatAddCheckService.getFilesByCloudDiskDocumentLocalPath(fileIdList, handleDTO.getReqDTO().getUserId(), false);
                if (CollUtil.isEmpty(localPathPath)) {
                    throw new YunAiBusinessException(YunAiYunDiskResultCode.FILE_NOT_FOUND);
                }
                List<String> localPathList = localPathPath.stream().map(LocalFileInfo::getLocalPath).collect(Collectors.toList());
                handleDTO.setResourceName(localPathPath.get(0).getName());
                handleDTO.setResourceExt(localPathPath.get(0).getExt());
                docContent = documentParsingExternalService.parsingAfterDelete(localPathList, ExternalCheckSystemClient.MAX_LENGTH);
            } catch (YunAiBusinessException e) {
                AiResultCode aiResultCode = AiResultCode.getByCodeOrMsg(e.getCode(), null);
                BaseResult<?> result = BaseResult.error(aiResultCode.getCode(), aiResultCode.getMsg());
                handleDTO.getSseEmitterOperate().sendAndComplete(result);
                return "";
            }
            if (StringUtils.isNotEmpty(file.getName())) {
                resourceName = file.getName();
            } else {
                if (StringUtils.isNotEmpty(handleDTO.getResourceName())) {
                    resourceName = handleDTO.getResourceName();
                } else {
                    resourceName = "文档对话";
                }
            }
            if (ObjectUtil.isEmpty(docContent) || ObjectUtil.isEmpty(docContent.getText())) {
                // 文件内容为空
                BaseResult<?> result = BaseResult.error(AiResultCode.CODE_10000020.getCode(), AiResultCode.CODE_10000020.getMsg());
                handleDTO.getSseEmitterOperate().sendAndComplete(result);
                return "";
            }
            resourceContent = docContent.getText();
        } else if (ResourceTypeEnum.contains(attachmentDTO.getAttachmentTypeList(), ResourceTypeEnum.MAIL_ATTACHMENT)) {
            log.info("读取邮件附件内容，邮件附件参数：{}", JsonUtil.toJson(attachmentDTO.getMailList()));
            // 下载附件 并 获取文档内容
            DocumentParsingResultVO docContent = null;
            List<String> localPathList = Collections.emptyList();
            try {
                List<MailInfo> list = attachmentDTO.getMailList();
                if (CollectionUtils.isEmpty(list)) {
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }

                localPathList = chatAddCheckService.getFilesByEmailAttachmentLocalPath(list, handleDTO.getReqDTO(), resourceName);
                docContent = documentParsingExternalService.parsingAfterDelete(localPathList, ExternalCheckSystemClient.MAX_LENGTH);
            } catch (YunAiBusinessException e) {
                AiResultCode aiResultCode = AiResultCode.getByCodeOrMsg(e.getCode(), null);
                BaseResult<?> result = BaseResult.error(aiResultCode.getCode(), aiResultCode.getMsg());
                handleDTO.getSseEmitterOperate().sendAndComplete(result);
                return "";
            } finally {
                if (Boolean.FALSE.equals(CollectionUtils.isEmpty(localPathList))) {
                    localPathList.forEach(FileOperationUtil::deleteLocalFile);
                }
            }
            if (ObjectUtil.isEmpty(docContent) || ObjectUtil.isEmpty(docContent.getText())) {
                // 文件内容为空
                BaseResult<?> result = BaseResult.error(AiResultCode.CODE_10000020.getCode(), AiResultCode.CODE_10000020.getMsg());
                handleDTO.getSseEmitterOperate().sendAndComplete(result);
                return "";
            }
            resourceContent = docContent.getText();
        }

        // 判断是否为空
        if (ObjectUtil.isEmpty(resourceContent)) {
            AiResultCode aiResultCode = AiResultCode.CODE_10000020;
            BaseResult<?> result = BaseResult.error(aiResultCode.getCode(), aiResultCode.getMsg());
            handleDTO.getSseEmitterOperate().sendAndComplete(result);
        }

        handleDTO.setResourceName(resourceName);

        // 送审
        if (ObjectUtil.isNotEmpty(resourceContent)) {
            // 邮件笔记内容校验一起送审
            checkSystemDomainService.checkLocalAndPlatformException(null, handleDTO.getReqDTO().getUserId(), resourceContent);
        }

        return resourceContent;
    }

    /**
     * 获取新的对话信息
     *
     * @param reqDTO  模型请求
     * @param msgList 消息请求
     * @return 模型请求实体
     */
    private TextModelTextReqDTO genTextModelTextReq(TextModelTextReqDTO reqDTO, List<TextModelMessageDTO> msgList) {
        TextModelTextReqDTO req = new TextModelTextReqDTO();
        req.setTaskId(reqDTO.getTaskId());
        req.setUserId(reqDTO.getUserId());
        req.setSessionId(reqDTO.getSessionId());
        req.setMessageDtoList(msgList);
        // 文档对话，强制不联网
        req.setEnableForceNetworkSearch(false);
        return req;
    }
}
