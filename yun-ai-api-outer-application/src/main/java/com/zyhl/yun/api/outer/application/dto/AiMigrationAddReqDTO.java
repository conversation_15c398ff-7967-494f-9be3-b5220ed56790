package com.zyhl.yun.api.outer.application.dto;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.yun.api.outer.constants.RegConst;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.enums.MigrationTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.external.ose.enums.UserBelongsPlatformEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * AI迁移表：小天助手1.0.1
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class AiMigrationAddReqDTO extends BaseDTO implements Serializable {

    /**
     * 请求头的设备信息
     */
    private String device;

    /**
     * 来源渠道
     */
    private String sourceChannel = "101";

    /**
     * 迁移报名，不填默认1，无论通过什么页面报名，只需报名一次
     * 1--图片搜索页面
     * 2--文档搜索页面（如果想迁移后就打开文档全文搜索，必须传2）
     *
     * @see com.zyhl.yun.api.outer.enums.MigrationTypeEnum
     */
    private String type = "1";

    /**
     * 参数校验
     *
     * @return 枚举
     */
    public AbstractResultCode checkParam() {
        this.device = RequestContextHolder.getRequestHeadersField(ReqHeadConst.CLIENT_INFO);
        if (ObjectUtil.isEmpty(device)) {
            log.info("请求头设备信息为空");
            return ResultCodeEnum.ERROR_PARAMS;
        } else if (!UserBelongsPlatformEnum.OSE.getBelongsPlatform().equals(RequestContextHolder.getBelongsPlatform())) {
            log.info("新底座用户无需数据迁移，用户所属平台：{}", RequestContextHolder.getBelongsPlatform());
            return ResultCodeEnum.NOT_TARGET_USER;
        } else if (CharSequenceUtil.isEmpty(type) || !type.matches(RegConst.REG_DATA_STR)) {
            log.info("迁移类型为空 或者 不是数字 type = {}", type);
            return ResultCodeEnum.ERROR_PARAMS;
        } else if (!MigrationTypeEnum.isExist(Integer.valueOf(type))) {
            log.info("迁移类型不存在");
            return ResultCodeEnum.ERROR_PARAMS;
        }

        return checkUserId();
    }
}
