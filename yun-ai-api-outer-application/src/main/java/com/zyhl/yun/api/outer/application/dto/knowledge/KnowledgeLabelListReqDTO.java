package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 知识库标签列表请求参数
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class KnowledgeLabelListReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 是否显示“全部”和“未分类”标签
     */
    private Boolean showAll = Boolean.FALSE;


}
