package com.zyhl.yun.api.outer.application.service.external;

import java.util.Map;

import com.zyhl.yun.api.outer.application.vo.ContentExtInfoVO;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchResult;

/**
 * 智能图片的相册列表
 *
 * <AUTHOR>
 * @date 2025/03/19 16:29
 */
public interface SearchImageAlbumListService {

	/**
	 * 实时搜索人物关系相册推荐（返回leadCopyVO和recommendVO）
	 * 
	 * @param sourceChannel 渠道
	 * @param versionMap    版本map
	 * @param leadCopyVO    leadCopy信息
	 * @param recommendVO   推荐信息
	 * @param searchResult  搜索结果
	 * @return 人物关系相册推荐信息
	 */
	ContentExtInfoVO searchImageSetAlbumList(String sourceChannel, Map<String, String> versionMap,
			LeadCopyVO leadCopyVO, DialogueRecommendVO recommendVO, SearchResult searchResult);

}
