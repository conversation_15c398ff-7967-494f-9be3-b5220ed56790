package com.zyhl.yun.api.outer.application.vo.knowledge;

import com.zyhl.yun.api.outer.domain.vo.PersonalKnowledgeImportCheckResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * 导入知识库文件返回结果
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KnowledgeFileImportVO {

    /**
     * 知识库ID
     */
   private String baseId;

    /**
     * 错误列表
     */
   private List<PersonalKnowledgeImportCheckResult> errorList;
} 