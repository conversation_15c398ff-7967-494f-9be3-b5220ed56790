package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.hcy.yun.ai.common.base.utils.StopWatchUtil;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.hcy.yun.ai.common.rag.vo.RewriteResultVO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueAttachmentDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.pojo.KnowledgeFlowInfo;
import com.zyhl.yun.api.outer.application.chatv2.service.BenefitService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.service.KnowledgeSearchService;
import com.zyhl.yun.api.outer.application.chatv2.service.NoteKnowledgeSearchService;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueFlowResultVO;
import com.zyhl.yun.api.outer.application.dto.ChatConfigGetDTO;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatAddCheckService;
import com.zyhl.yun.api.outer.application.service.chat.ChatConfigService;
import com.zyhl.yun.api.outer.config.KnowledgeDialogueProperties;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.config.textmodel.ModelPromptProperties;
import com.zyhl.yun.api.outer.config.textmodel.SpecialPromptProperties;
import com.zyhl.yun.api.outer.domain.vo.ChatConfigVO;
import com.zyhl.yun.api.outer.domainservice.QpsLimitService;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.*;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 笔记知识库流式对话
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TextModelKnowledgeSseNoteHandlerImpl extends AbstractChatAddV2Handler {

    /**
     * 当前执行顺序
     */
    private final ExecuteSort thisExecuteSort = ExecuteSort.TEXT_MODE_KNOWLEDGE_SSE_NOTE;

    private static final String REPLACE_KEY_OF_POLITICIAN = "{politician}";
    private static final String REPLACE_KEY_OF_KNOWLEDGE = "{knowledge}";
    private static final String REPLACE_KEY_OF_QUERY = "{query}";
    private static final String REPLACE_KEY_OF_SEARCH_RESULT = "{search_result}";
    private static final String SUPPORT_NETWORK = "support-network";
    private static final String UNSUPPORT_NETWORK = "unsupport-network";


    @Resource
    private KnowledgeDialogueProperties knowledgeDialogueProperties;
    @Resource
    private ModelProperties modelProperties;
    @Resource
    private TextModelExternalService textModelExternalService;
    @Resource
    private QpsLimitService qpslimitService;
    @Resource
    private ModelPromptProperties modelPromptProperties;
    @Resource
    private SpecialPromptProperties specialPromptProperties;
    @Resource
    private BenefitService benefitService;
    @Resource
    private AlgorithmChatContentRepository algorithmChatContentRepository;
    @Resource
    private AiTextResultRepository aiTextResultRepository;
    @Resource
    private DataSaveService dataSaveService;
    @Resource
    private ChatConfigService chatConfigService;
    @Resource
    private AlgorithmChatAddCheckService chatAddCheckService;
    @Resource
    private NoteKnowledgeSearchService noteKnowledgeSearchService;

    @Override
    public void afterPropertiesSet() throws Exception {
        //支持的业务初始化
        List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.NOTE);
        this.setBusinessTypes(thisBusinessTypes);
    }

    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public boolean execute(ChatAddHandleDTO handleDTO) {
        // 非文本对话 || 强制大模型对话 || 非笔记助手渠道 || 非知识库资源
        if (!DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode().equals(handleDTO.getIntentionCode())) {
            return false;
        }
        if (handleDTO.getInputInfoDTO().isEnableForceLlm()) {
            return false;
        }
        if (!AssistantEnum.NOTE.equals(handleDTO.getAssistantEnum())) {
            return false;
        }

        KnowledgeFlowInfo knowledgeFlowInfo = noteKnowledgeSearchService.setKnowledgeFlowInfo(handleDTO);
        if (null == knowledgeFlowInfo) {
            return false;
        }
        log.info("【知识库对话】公共知识库可用：{}，个人知识库可用：{}, VIP专属智能体知识库可用:{}", handleDTO.getKnowledgeFlowInfo().isCommonEnable(),
                handleDTO.getKnowledgeFlowInfo().isPersonalEnable(), knowledgeFlowInfo.isVipCommonEnable());
        return knowledgeFlowInfo.isEnable();
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        log.info("进入{}", thisExecuteSort.getDesc());
        handleDTO.getSseEmitterOperate().setSseName(SseNameEnum.KNOWLEDGE_NOTE.getCode());
        String userId = handleDTO.getReqDTO().getUserId();
        List<TextModelMessageDTO> historyList = aiTextResultRepository.getHistoryList(userId, handleDTO.getReqDTO().getSessionId());

        // 查询用户配置的模型
        ChatConfigGetDTO dto = new ChatConfigGetDTO();
        dto.setUserId(RequestContextHolder.getUserId());
        dto.setBusinessType(RequestContextHolder.getBusinessType());
        dto.setSourceChannel(RequestContextHolder.getSourceChannel());
        dto.setAssistantEnum(RequestContextHolder.getAssistantEnum());
        ChatConfigVO vo = chatConfigService.get(dto).stream().filter(item -> item.getDefaultMode() == 1).findFirst().get();
        handleDTO.getKnowledgeFlowInfo().setChatConfigVO(vo);

        // 召回知识库信息，保存在KnowledgeFlowInfo
        noteKnowledgeSearchService.knowledgeSearch(handleDTO, historyList);
        if (ObjectUtil.isNotEmpty(handleDTO.getKnowledgeFlowInfo().getSelectedKnowledgeList())
                && ObjectUtil.isEmpty(handleDTO.getKnowledgeFlowInfo().getPersonalFileList())) {
            log.info("【笔记AI助手知识库流式对话】【RAG重要节点日志】用户主页发起对话，选择了知识库，但是未命中个人知识库，走普通对话");
            handleDTO.setEnding(knowledgeDialogueProperties.getEnding());
            return true;
        }

        // 扣减权益
        benefitService.consumeBenefit(handleDTO.getReqDTO(), RequestContextHolder.getPhoneNumber(), handleDTO.getDialogueId());

        //保存
        dataSaveService.saveTextResult(handleDTO, "", "");
        dataSaveService.add(handleDTO, ChatStatusEnum.CHAT_IN);

        // 监听器
        SseEventListener event = new SseEventListener(handleDTO, historyList);
        event.setOutputNetworkSearchInfo(false);
        event.setPersonalKnowledgeFileList(handleDTO.getKnowledgeFlowInfo().getPersonalFileList());

        // 智能调度
        schedule(event, handleDTO);

        return false;
    }

    /**
     * 智能调度
     *
     * @param event     流式监听参数
     * @param handleDTO 内部流转数据
     */
    private void schedule(SseEventListener event, ChatAddHandleDTO handleDTO) {
        // 开启计时
        StopWatch stopWatch = StopWatchUtil.createStarted();
        try {
            List<String> executeSort = new ArrayList<>();
            executeSort.add(handleDTO.getKnowledgeFlowInfo().getChatConfigVO().getModelType());
            executeSort.addAll(Arrays.asList(knowledgeDialogueProperties.getDialogueConfig().getModelCode().split(",")));

            AbstractResultCode error = null;
            boolean success = false;
            for (String code : executeSort) {
                error = null;

                // qps限制
                if (!qpslimitService.modelQpsLimit(code)) {
                    log.info("【笔记AI助手知识库流式对话】【RAG重要节点日志】请求过多，qps限流，model:{}", code);
                    error = ResultCodeEnum.ERROR_LIMITATION;
                } else {
                    event.setModelCode(code);
                    try {
                        Integer maxLength = modelProperties.getMaxLength(handleDTO.getAssistantEnum(), handleDTO.getBusinessType(), code);
                        TextModelTextReqDTO reqDTO = event.getTextDto().toTextReqDTO(maxLength);
                        success = modelStreamHandle(reqDTO, event, handleDTO.getKnowledgeFlowInfo());

                        // 更新模型编码
                        algorithmChatContentRepository.updateModelCode(event.getDialogId(), code);
                        break;
                    } catch (YunAiBusinessException e) {
                        error = e.getExceptionEnum();
                        log.error("【笔记AI助手知识库流式对话】【RAG重要节点日志】调用文本大模型异常:{}，对话id:{}", e.getMessage(), event.getDialogId(), e);
                    } catch (Exception e) {
                        log.error("【笔记AI助手知识库流式对话】【RAG重要节点日志】调用文本大模型异常:{}，对话id:{}", e.getMessage(), event.getDialogId(), e);
                    }
                }

            }

            if (!success) {
                log.warn("【笔记AI助手知识库流式对话】【RAG重要节点日志】对话失败，智能调度大文本模型处理失败，对话id：{}", event.getDialogId());
                if (error != null) {
                    event.dialogueFail(error);
                } else {
                    event.dialogueFail(ResultCodeEnum.ERROR_SERVER_INTERNAL);
                }
            }
        } finally {
            log.info("【笔记AI助手知识库流式对话】【RAG重要节点日志】结束，耗时：{}，对话id：{}\n 最终结果：{}", StopWatchUtil.logTime(stopWatch), event.getDialogId(), JsonUtil.toJson(event.getAllMsg()));
            StopWatchUtil.clearDuration();
        }

    }

    /**
     * 调大文本模型流式处理
     *
     * @param reqDTO            请求参数
     * @param event             监听事件
     * @param knowledgeFlowInfo 知识库流程信息
     */
    private boolean modelStreamHandle(TextModelTextReqDTO reqDTO, SseEventListener event, KnowledgeFlowInfo knowledgeFlowInfo) {

        // 无召回结果 且 无联网能力
        if (ObjectUtil.isEmpty(knowledgeFlowInfo.getKnowledgeList()) && !knowledgeFlowInfo.getChatConfigVO().isEnableNetworkSearch()) {
            log.info("【笔记AI助手知识库流式对话】【RAG重要节点日志】没有召回信息且无联网能力，执行普通对话");
            event.getSseEmitterOperate().setSseName(SseNameEnum.KNOWLEDGE_NOTE_NO_RECALL_NO_NETWORK.getCode());
            if (ObjectUtil.isEmpty(knowledgeFlowInfo.getSelectedKnowledgeList())) {
                // 非主页对话
                event.setParentTitle(knowledgeDialogueProperties.getTitleMap().get(UNSUPPORT_NETWORK));
            }

            String prompt = chatAddCheckService.getDialoguePrompt(event.getInputInfoDTO().getPrompt(), event.getReqDTO().getSourceChannel());
            reqDTO.getMessageDtoList().get(reqDTO.getMessageDtoList().size() - 1).setCommand(prompt);
            return textModelExternalService.streamDialogue(event.getModelCode(), reqDTO, event);
        }

        // 命中的个人知识库文档先返回
        if (ObjectUtil.isNotEmpty(knowledgeFlowInfo.getPersonalFileList())) {
            DialogueFlowResultVO flowResultVO = new DialogueFlowResultVO(knowledgeFlowInfo.getPersonalFileList(), event.getModelCode());
            event.getSseEmitterOperate().sendSuccess(event.getRespVO().processingFlowResult(flowResultVO));
        }

        // 是否联网对话
        boolean networkEnable = getNetworkEnable(knowledgeFlowInfo, event);
        // 是否支持并命中知识库名词库
        boolean nounLibraryEnable = knowledgeDialogueProperties.isEnableNounLibrary() && !ObjectUtil.isEmpty(knowledgeFlowInfo.getNounLibraryList());
        // 获取提示词
        ChatPromptNoteCodeEnum prompt = getPrompt(networkEnable, nounLibraryEnable, knowledgeFlowInfo.getRewrite(), event.getInputInfoDTO().isEnableKnowledgeAndNetworkSearch());
        if (networkEnable) {
            event.setOutputNetworkSearchInfo(true);
            event.setNetworkSearchInfoList(knowledgeFlowInfo.getHtmlInfoList());
        }

        // 系统提示词
        String systemPrompt = modelPromptProperties.getPrompt(prompt.getSystemPrompt(), event.getModelCode());
        // 用户输入内容
        String userPrompt = modelPromptProperties.getPrompt(prompt.getUserPrompt(), event.getModelCode());
        // 支持并命中知识库名词库-设置流式名称
        if (nounLibraryEnable) {
            event.getSseEmitterOperate().setSseName(SseNameEnum.KNOWLEDGE_NOTE_NOUN_LIBRARY.getCode());
        }

        // 历史记录处理（打日志）
        knowledgeFlowInfo.historyHandle(reqDTO);

        // 行政人物处理
        knowledgeFlowInfo.politicianHandle(knowledgeDialogueProperties);

        // 名词库
        if (nounLibraryEnable) {
            userPrompt = userPrompt.replace(REPLACE_KEY_OF_QUERY, knowledgeFlowInfo.getQuery())
                    .replace(REPLACE_KEY_OF_KNOWLEDGE, knowledgeFlowInfo.getNounLibraryText())
                    .replace(REPLACE_KEY_OF_POLITICIAN, knowledgeFlowInfo.getPolitician())
                    .replace(REPLACE_KEY_OF_SEARCH_RESULT, knowledgeFlowInfo.getNetworkText(knowledgeDialogueProperties.isEnableMark()));
        } else {
            // 用户输入内容
            userPrompt = userPrompt.replace(REPLACE_KEY_OF_QUERY, knowledgeFlowInfo.getQuery())
                    .replace(REPLACE_KEY_OF_KNOWLEDGE, knowledgeFlowInfo.getKnowledgeText(knowledgeDialogueProperties.isEnableMark()))
                    .replace(REPLACE_KEY_OF_POLITICIAN, knowledgeFlowInfo.getPolitician())
                    .replace(REPLACE_KEY_OF_SEARCH_RESULT, knowledgeFlowInfo.getNetworkText(knowledgeDialogueProperties.isEnableMark()));
        }

        // 日志
        LogCommonUtils.printlnListLog("【笔记AI助手知识库流式对话】【RAG重要节点日志】使用的system提示词第{}个分块：\n{}", systemPrompt);
        LogCommonUtils.printlnListLog("【笔记AI助手知识库流式对话】【RAG重要节点日志】使用的user提示词第{}个分块：\n{}", userPrompt);

        // 当前对话
        TextModelMessageDTO msgDTO = reqDTO.getMessageDtoList().get(reqDTO.getMessageDtoList().size() - 1);
        msgDTO.setRole(TextModelRoleEnum.USER.getName());
        msgDTO.setContent(userPrompt);
        msgDTO.setCommand(systemPrompt);

        // 大模型参数
        reqDTO.setTextModelConfig(knowledgeDialogueProperties.getDialogueConfig().getTextModelConfig());

        // 是否支持知识库名词库
        reqDTO.setEnableForceNetworkSearch(nounLibraryEnable);

        log.info("【笔记AI助手知识库流式对话】【RAG重要节点日志】大模型配置参数：{}", JsonUtil.toJson(reqDTO.getTextModelConfig()));

        // 调大模型
        return textModelExternalService.streamDialogue(event.getModelCode(), reqDTO, event);
    }

    /**
     * 判断是否联网
     *
     * @param flowInfo flowInfo
     * @param event    event
     * @return boolean true-联网，false-不联网
     */
    private boolean getNetworkEnable(KnowledgeFlowInfo flowInfo, SseEventListener event) {
        // 没有召回结果，默认联网搜索
        boolean networkEnable = true;
        if (ObjectUtil.isNotEmpty(flowInfo.getKnowledgeList())) {
            // 有召回结果，返回端侧传参状态
            networkEnable = event.getInputInfoDTO().isEnableKnowledgeAndNetworkSearch();
        } else {
            if (!event.getReqDTO().getDialogueInput().isEnableForceNetworkSearch()) {
                // 没有召回结果，未打开联网开关，返回强制联网事件
                DialogueFlowResultVO flowResultVO = new DialogueFlowResultVO(ChatEventCodeEnum.OPEN_NETWORK_SEARCH, event.getModelCode());
                event.getSseEmitterOperate().sendSuccess(event.getRespVO().processingFlowResult(flowResultVO));
            }

            // 没有召回结果，强制联网
            if (ObjectUtil.isEmpty(flowInfo.getSelectedKnowledgeList())) {
                // 非主页对话
                event.setParentTitle(knowledgeDialogueProperties.getTitleMap().get(SUPPORT_NETWORK));
            }
        }

        if (networkEnable) {
            // 返回联网搜索的提示，虽然已经搜索完成了
            DialogueFlowResultVO flowResultVO = new DialogueFlowResultVO(ChatMiddleCodeEnum.NETWORK_SEARCHING, event.getModelCode());
            event.getSseEmitterOperate().sendSuccess(event.getRespVO().processingFlowResult(flowResultVO));
        }

        return networkEnable;
    }

    /**
     * 获取提示词
     *
     * @param networkEnable                   是否联网和有无开启角标
     * @param nounLibraryEnable               是否支持知识库名词库
     * @param rewrite                         问题重写结果
     * @param enableKnowledgeAndNetworkSearch 笔记AI助手知识库流式对话强制联网
     * @return 提示词
     */
    private ChatPromptNoteCodeEnum getPrompt(boolean networkEnable, boolean nounLibraryEnable, RewriteResultVO rewrite, boolean enableKnowledgeAndNetworkSearch) {
        // 知识库名词库
        if (nounLibraryEnable) {
            return ChatPromptNoteCodeEnum.RAG_NOTE_NOUN_LIBRARY;
        }

        // 强制联网
        if (enableKnowledgeAndNetworkSearch) {
            return ChatPromptNoteCodeEnum.getByCode(knowledgeDialogueProperties.isEnableMark());
        }

        // 判断使用哪个提示词（只跟是否联网和有无开启角标有关） - 其他、总结、建议、发言
        return ChatPromptNoteCodeEnum.getByCode(networkEnable, knowledgeDialogueProperties.isEnableMark(), rewrite.getQueryType());
    }

}
