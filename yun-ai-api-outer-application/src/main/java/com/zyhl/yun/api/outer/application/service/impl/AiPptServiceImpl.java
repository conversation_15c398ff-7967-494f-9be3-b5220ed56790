package com.zyhl.yun.api.outer.application.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;

import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.enums.AIModuleEnum;
import com.zyhl.hcy.yun.ai.common.base.enums.SupplierTypeEnum;
import com.zyhl.hcy.yun.ai.common.base.enums.YunAiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.FileReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.FileUploadContentReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.FileUploadVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.YunDiskClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.dto.AipptDesignInfoRequestDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.dto.AipptExportRequestDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.properties.AipptProperties;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.vo.AipptDesignInfoResponseVO;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.vo.AipptExportResponseVO;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.vo.AipptExportResultResponseVO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueIntentionDTO;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatTidbSaveDTO;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.enums.*;
import com.zyhl.yun.api.outer.enums.chat.*;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.application.chatv2.service.YunDiskV2Service;
import com.zyhl.yun.api.outer.application.dto.YunDiskReqDTO;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.vo.AipptCodeResponseVO;
import com.zyhl.yun.api.outer.application.dto.AiPptFileSaveReqDTO;
import com.zyhl.yun.api.outer.application.dto.AiPptResultInformReqDTO;
import com.zyhl.yun.api.outer.application.dto.AiPptResultInformRespDTO;
import com.zyhl.yun.api.outer.application.service.AiPptService;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.AiFunctionResult;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.external.YunDiskExternalService;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.domain.dto.aippt.AipptCodeGetRequestDTO;
import com.zyhl.yun.api.outer.domain.vo.aippt.AipptCodeGetResponseVO;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.external.service.AipptExternalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Objects;

/**
 * AIPPT服务实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class AiPptServiceImpl implements AiPptService {

    @Resource
    private AipptExternalService aipptExternalService;

    @Resource
    private SourceChannelsProperties sourceChannelsProperties;

    @Resource
    private DataSaveService dataSaveService;

    @Resource
    private AiTextResultRepository aiTextResultRepository;

    @Resource
    private YunDiskExternalService yunDiskExternalService;

    @Resource
    private ChatTextToolBusinessConfig chatTextToolBusinessConfig;

    @Resource
    private AipptProperties aipptProperties;

    @Resource
    private UidGenerator uidGenerator;

    @Resource
    private AlgorithmChatContentRepository algorithmChatContentRepository;

    @Resource
    private YunDiskV2Service yunDiskV2Service;

    @Resource
    private YunDiskClient yunDiskClient;

    @Resource
    private ExecutorService platformThreadPool;

    private static final String AIPPT_MOBILE_TYPE = "2";

    @Override
    public AiPptResultInformRespDTO resultInform(AiPptResultInformReqDTO reqDTO) {
        long totalStartTime = System.currentTimeMillis();
        log.info("AIPPT结果通知事件开始, contentDialogueId: {}, taskId: {}, designId: {}",
                reqDTO.getContentDialogueId(), reqDTO.getTaskId(), reqDTO.getDesignId());

        try {
            // 1.异步启动耗时的文件处理任务
            long asyncStartTime = System.currentTimeMillis();
            log.info("AIPPT-开始异步文件处理任务");

            // 异步下载并上传PPT文件 - 使用platformThreadPool
            CompletableFuture<String> pptFuture = CompletableFuture.supplyAsync(() -> {
                long pptStartTime = System.currentTimeMillis();
                log.info("AIPPT-异步任务开始: 下载并上传PPT文件, 线程: {}", Thread.currentThread().getName());
                try {
                    String pptFileId = downloadAndUploadPpt(reqDTO.getDesignId(), reqDTO.getUserId(), reqDTO.getSourceChannel(), reqDTO.getPptName());
                    long pptCostTime = System.currentTimeMillis() - pptStartTime;
                    log.info("AIPPT-异步任务完成: 下载并上传PPT文件, 耗时: {}ms, pptFileId: {}, 线程: {}",
                            pptCostTime, pptFileId, Thread.currentThread().getName());
                    return pptFileId;
                } catch (Exception e) {
                    log.error("AIPPT-异步任务失败: 下载并上传PPT文件, 耗时: {}ms, 线程: {}, error: {}",
                            System.currentTimeMillis() - pptStartTime, Thread.currentThread().getName(), e.getMessage(), e);
                    throw new RuntimeException("PPT文件处理失败: " + e.getMessage(), e);
                }
            }, platformThreadPool);

            // 异步下载并上传封面图片 - 使用platformThreadPool
            CompletableFuture<String> coverFuture = CompletableFuture.supplyAsync(() -> {
                long coverStartTime = System.currentTimeMillis();
                log.info("AIPPT-异步任务开始: 下载并上传封面图片, 线程: {}", Thread.currentThread().getName());
                try {
                    String coverFileId = downloadAndUploadCover(reqDTO.getDesignId(), reqDTO.getUserId(), reqDTO.getPptName());
                    long coverCostTime = System.currentTimeMillis() - coverStartTime;
                    log.info("AIPPT-异步任务完成: 下载并上传封面图片, 耗时: {}ms, coverFileId: {}, 线程: {}",
                            coverCostTime, coverFileId, Thread.currentThread().getName());
                    return coverFileId;
                } catch (Exception e) {
                    log.error("AIPPT-异步任务失败: 下载并上传封面图片, 耗时: {}ms, 线程: {}, error: {}",
                            System.currentTimeMillis() - coverStartTime, Thread.currentThread().getName(), e.getMessage(), e);
                    throw new RuntimeException("封面图片处理失败: " + e.getMessage(), e);
                }
            }, platformThreadPool);

            // 2.获取HBase数据（只查询一次，供后续两个方法使用）
            long hbaseQueryStartTime = System.currentTimeMillis();
            AiTextResultEntity hbaseResult = dataSaveService.getHbaseResult(reqDTO.getUserId(), reqDTO.getContentDialogueId());
            long hbaseQueryTime = System.currentTimeMillis() - hbaseQueryStartTime;
            log.info("AIPPT-获取HBase数据耗时: {}ms", hbaseQueryTime);

            // 3.同步更新HBase中的outContent内容（仅当content不为空时）
            long stage1StartTime = System.currentTimeMillis();
            long stage1CostTime = 0;
            if (StrUtil.isNotBlank(reqDTO.getContent())) {
                updateHbaseOutContent(reqDTO.getUserId(), reqDTO.getContentDialogueId(), reqDTO.getContent(), hbaseResult);
                stage1CostTime = System.currentTimeMillis() - stage1StartTime;
                log.info("AIPPT-阶段1完成: 更新HBase内容, 耗时: {}ms", stage1CostTime);
            } else {
                log.info("AIPPT-跳过阶段1: content为空，不更新HBase内容");
            }

            // 4.等待异步任务完成并获取结果
            log.info("AIPPT-开始等待异步文件处理任务完成");
            String coverFileId = coverFuture.get();
            String pptFileId = pptFuture.get();
            long waitTime = System.currentTimeMillis() - asyncStartTime;
            log.info("AIPPT-异步文件处理任务全部完成, 等待耗时: {}ms, pptFileId: {}, coverFileId: {}",
                    waitTime, pptFileId, coverFileId);

            // 5.插入新对话到TiDB和HBase
            long stage4StartTime = System.currentTimeMillis();
            String newDialogueId = insertNewDialogue(reqDTO, pptFileId, coverFileId, hbaseResult);
            long stage4CostTime = System.currentTimeMillis() - stage4StartTime;
            log.info("AIPPT-阶段4完成: 插入新对话, 耗时: {}ms", stage4CostTime);

            long totalCostTime = System.currentTimeMillis() - totalStartTime;
            log.info("AIPPT结果通知事件完成, 总耗时: {}ms, 各阶段耗时: [ HBase更新: {}ms, 异步等待: {}ms, 数据插入: {}ms]",
                    totalCostTime, stage1CostTime, waitTime, stage4CostTime);

            return AiPptResultInformRespDTO.builder()
                    .pptDialogueId(newDialogueId)
                    .pptFileId(pptFileId)
                    .build();

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            long totalCostTime = System.currentTimeMillis() - totalStartTime;
            log.error("AIPPT结果通知事件被中断, 总耗时: {}ms, error: {}", totalCostTime, e.getMessage(), e);
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR, "AIPPT处理被中断: " + e.getMessage());
        } catch (ExecutionException e) {
            long totalCostTime = System.currentTimeMillis() - totalStartTime;
            log.error("AIPPT结果通知事件执行失败, 总耗时: {}ms, error: {}", totalCostTime, e.getMessage(), e);
            Throwable cause = e.getCause();
            if (cause instanceof RuntimeException) {
                throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR, cause.getMessage());
            }
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR, "AIPPT处理失败: " + e.getMessage());
        } catch (Exception e) {
            long totalCostTime = System.currentTimeMillis() - totalStartTime;
            log.error("AIPPT结果通知事件异常, 总耗时: {}ms, error: {}", totalCostTime, e.getMessage(), e);
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR, "AIPPT处理异常: " + e.getMessage());
        }
    }


    @Override
    public void fileSave(AiPptFileSaveReqDTO reqDTO) {
        long totalStartTime = System.currentTimeMillis();
        log.info("AIPPT文件保存开始, dialogueId: {}, designId: {}, pptName: {}",
                reqDTO.getDialogueId(), reqDTO.getDesignId(), reqDTO.getPptName());

        try {
            // 1.从HBase查询原有对话数据
            AiTextResultRespParameters respParams = getHbaseRespParameters(reqDTO.getUserId(), reqDTO.getDialogueId());
            log.info("AIPPT-成功获取HBase响应参数, dialogueId: {}", reqDTO.getDialogueId());

            // 2.获取原文件信息（可能为null）
            File originalFile = getOriginalFileFromRespParams(respParams);
            if (originalFile != null) {
                log.info("AIPPT-获取到原文件信息, fileId: {}, parentFileId: {}",
                        originalFile.getFileId(), originalFile.getParentFileId());
            } else {
                log.info("AIPPT-原文件不存在，将使用新建模式");
            }

            // 3.启动异步任务
            long asyncStartTime = System.currentTimeMillis();
            log.info("AIPPT-开始异步文件处理任务");

            // 异步上传PPT文件（统一处理覆盖/新建）
            CompletableFuture<String> pptFuture = CompletableFuture.supplyAsync(() -> {
                long pptStartTime = System.currentTimeMillis();
                log.info("AIPPT-异步任务开始: 上传PPT文件, 线程: {}", Thread.currentThread().getName());
                try {
                    String pptFileId = uploadPptFileUnified(reqDTO, originalFile);
                    long pptCostTime = System.currentTimeMillis() - pptStartTime;
                    log.info("AIPPT-异步任务完成: 上传PPT文件, 耗时: {}ms, pptFileId: {}, 线程: {}",
                            pptCostTime, pptFileId, Thread.currentThread().getName());
                    return pptFileId;
                } catch (Exception e) {
                    log.error("AIPPT-异步任务失败: 上传PPT文件, 耗时: {}ms, 线程: {}, error: {}",
                            System.currentTimeMillis() - pptStartTime, Thread.currentThread().getName(), e.getMessage(), e);
                    throw new RuntimeException("PPT文件处理失败: " + e.getMessage(), e);
                }
            }, platformThreadPool);

            // 异步下载并上传封面图片
            CompletableFuture<String> coverFuture = CompletableFuture.supplyAsync(() -> {
                long coverStartTime = System.currentTimeMillis();
                log.info("AIPPT-异步任务开始: 下载并上传封面图片, 线程: {}", Thread.currentThread().getName());
                try {
                    String coverFileId = downloadAndUploadCover(reqDTO.getDesignId(), reqDTO.getUserId(), reqDTO.getPptName());
                    long coverCostTime = System.currentTimeMillis() - coverStartTime;
                    log.info("AIPPT-异步任务完成: 下载并上传封面图片, 耗时: {}ms, coverFileId: {}, 线程: {}",
                            coverCostTime, coverFileId, Thread.currentThread().getName());
                    return coverFileId;
                } catch (Exception e) {
                    log.error("AIPPT-异步任务失败: 下载并上传封面图片, 耗时: {}ms, 线程: {}, error: {}",
                            System.currentTimeMillis() - coverStartTime, Thread.currentThread().getName(), e.getMessage(), e);
                    throw new RuntimeException("封面图片处理失败: " + e.getMessage(), e);
                }
            }, platformThreadPool);

            long asyncCostTime = System.currentTimeMillis() - asyncStartTime;
            log.info("AIPPT-异步任务启动完成, 耗时: {}ms", asyncCostTime);

            // 4.等待异步任务完成并获取结果
            long waitStartTime = System.currentTimeMillis();
            String pptFileId = pptFuture.get();
            String coverFileId = coverFuture.get();
            long waitCostTime = System.currentTimeMillis() - waitStartTime;
            log.info("AIPPT-异步任务全部完成, 等待耗时: {}ms, pptFileId: {}, coverFileId: {}",
                    waitCostTime, pptFileId, coverFileId);

            // 5.构造AiFunctionResult并更新HBase
            updateHbaseAiFunctionResult(reqDTO.getUserId(), reqDTO.getDialogueId(), reqDTO,
                    pptFileId, coverFileId, respParams);

            long totalCostTime = System.currentTimeMillis() - totalStartTime;
            log.info("AIPPT文件保存成功, 总耗时: {}ms, dialogueId: {}, designId: {}",
                    totalCostTime, reqDTO.getDialogueId(), reqDTO.getDesignId());
        } catch (ExecutionException | InterruptedException e) {
            long totalCostTime = System.currentTimeMillis() - totalStartTime;
            log.error("AIPPT文件保存异步任务异常, 总耗时: {}ms, error: {}", totalCostTime, e.getMessage(), e);
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR, "AIPPT处理异常: " + e.getMessage());
        } catch (Exception e) {
            long totalCostTime = System.currentTimeMillis() - totalStartTime;
            log.error("AIPPT文件保存失败, 总耗时: {}ms, dialogueId: {}, designId: {}, error: {}",
                    totalCostTime, reqDTO.getDialogueId(), reqDTO.getDesignId(), e.getMessage(), e);
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                    "AIPPT文件保存失败: " + e.getMessage());
        }
    }

    /**
     * 更新HBase对话结果中的outContent内容
     * 参考AiEditServiceImpl的updateHbaseOutContent方法
     *
     * @param userId     用户ID
     * @param dialogueId 对话ID
     * @param newContent 新的内容
     * @param hbaseResult HBase对话结果实体
     */
    private void updateHbaseOutContent(String userId, String dialogueId, String newContent, AiTextResultEntity hbaseResult) {
        long startTime = System.currentTimeMillis();
        log.info("AIPPT-开始更新HBase对话结果中的outContent内容, userId: {}, dialogueId: {}", userId, dialogueId);
        try {
            if (hbaseResult == null) {
                log.error("未找到HBase对话结果, userId: {}, dialogueId: {}", userId, dialogueId);
                throw new YunAiBusinessException(YunAiCommonResultCode.HBASE_ERROR, "未找到HBase对话结果");
            }

            // 解析响应参数,获取index=0的对话流式结果
            long parseStartTime = System.currentTimeMillis();
            AiTextResultRespParameters respBean = JSONUtil.toBean(hbaseResult.getRespParameters(), AiTextResultRespParameters.class);
            DialogueFlowResult targetResult = null;
            if (ObjectUtil.isNotEmpty(respBean.getOutputList())) {
                for (DialogueFlowResult result : respBean.getOutputList()) {
                    if (Objects.equals(result.getIndex(), 0)) {
                        targetResult = result;
                        break;
                    }
                }
            }
            if (targetResult == null) {
                log.error("AIPPT-未找到index=0的对话流式结果, userId: {}, dialogueId: {}", userId, dialogueId);
                throw new YunAiBusinessException(YunAiCommonResultCode.HBASE_ERROR, "AIPPT-HBase对话中没有index=0的对话流式结果");
            }

            // 直接替换outContent内容
            targetResult.setOutContent(newContent);
            String respParameters = JSONUtil.toJsonStr(respBean);
            long parseTime = System.currentTimeMillis() - parseStartTime;
            log.info("AIPPT-解析和构造响应参数耗时: {}ms", parseTime);

            // 更新HBase
            long updateStartTime = System.currentTimeMillis();
            aiTextResultRepository.updateRespParameters(userId, Long.valueOf(dialogueId), respParameters);
            long updateTime = System.currentTimeMillis() - updateStartTime;
            log.info("AIPPT-HBase更新操作耗时: {}ms", updateTime);

            long totalTime = System.currentTimeMillis() - startTime;
            log.info("AIPPT-成功更新HBase对话结果中的outContent内容, userId: {}, dialogueId: {}, 总耗时: {}ms [解析: {}ms, 更新: {}ms]",
                    userId, dialogueId, totalTime, parseTime, updateTime);
        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("AIPPT-更新HBase对话结果中的outContent内容失败, 耗时: {}ms, error: {}", totalTime, e.getMessage(), e);
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR, "AIPPT-更新对话内容失败: " + e.getMessage());
        }
    }


    /**
     * 获取PPT封面图片URL并上传到个人云盘
     *
     * @param designId 厂商作品ID
     * @param userId   用户ID
     * @param pptName  PPT作品名称
     * @return 上传到个人云盘的封面图片文件ID
     */
    private String downloadAndUploadCover(String designId, String userId, String pptName) {
        long startTime = System.currentTimeMillis();
        log.info("AIPPT-开始获取并上传封面图片, userId: {}, designId: {}",
                userId, designId);

        try {
            // 1. 调用getDesignInfoWithRetry获取封面URL
            long getInfoStartTime = System.currentTimeMillis();
            AipptDesignInfoRequestDTO designInfoRequest = AipptDesignInfoRequestDTO.builder()
                    .userDesignId(Long.valueOf(designId))
                    .build();

            AipptDesignInfoResponseVO designInfoResponse = aipptExternalService.getDesignInfoWithRetry(
                    designInfoRequest, userId, "");
            long getInfoTime = System.currentTimeMillis() - getInfoStartTime;
            log.info("AIPPT-获取设计信息耗时: {}ms", getInfoTime);

            if (designInfoResponse == null || designInfoResponse.getCode() != 0
                    || designInfoResponse.getData() == null
                    || StrUtil.isBlank(designInfoResponse.getData().getCoverUrl())) {
                log.warn("AIPPT-获取封面URL失败或为空, designId: {}, response: {}",
                        designId, designInfoResponse);
                return null;
            }

            String coverUrl = designInfoResponse.getData().getCoverUrl();
            log.info("AIPPT-成功获取封面URL, designId: {}, coverUrl: {}", designId, coverUrl);

            // 2. 下载封面图片并转换为base64
            long downloadStartTime = System.currentTimeMillis();
            String base64Content = downloadFileToBase64(coverUrl, "封面图片");
            long downloadTime = System.currentTimeMillis() - downloadStartTime;
            log.info("AIPPT-成功下载封面图片并转换为base64, designId: {}, base64长度: {}, 耗时: {}ms",
                    designId, base64Content != null ? base64Content.length() : 0, downloadTime);

            // 3. 上传到个人云盘的封面路径
            long uploadStartTime = System.currentTimeMillis();
            String path = chatTextToolBusinessConfig.getAiPptGenerate().getPersonalCoverPath();
            String fileSuffix = getImageSuffix(coverUrl);
            String fileName = pptName + "_封面";
            Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();

            FileUploadVO fileUploadVO = yunDiskExternalService.uploadFileToCustomPath(
                    path,
                    AIModuleEnum.AI_TOOLS_BOX,
                    userId,
                    belongsPlatform,
                    fileSuffix,
                    base64Content,
                    fileName
            );
            long uploadTime = System.currentTimeMillis() - uploadStartTime;

            String fileId = fileUploadVO != null ? fileUploadVO.getFileId() : null;
            long totalTime = System.currentTimeMillis() - startTime;
            log.info("AIPPT-成功上传封面图片到个人云盘, userId: {}, fileId: {}, fileName: {}, 总耗时: {}ms [获取信息: {}ms, 下载: {}ms, 上传: {}ms]",
                    userId, fileId, fileName, totalTime, getInfoTime, downloadTime, uploadTime);
            return fileId;
        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("AIPPT-获取并上传封面图片失败: userId: {}, designId: {}, 耗时: {}ms, error: {}",
                    userId, designId, totalTime, e.getMessage(), e);
            // 封面图片上传失败不影响主流程，返回null
            return null;
        }
    }

    /**
     * 根据designId获取PPT下载URL，下载文件并上传到个人云盘
     *
     * @param designId      厂商作品ID
     * @param userId        用户ID
     * @param sourceChannel 渠道来源
     * @param pptName       PPT作品名称
     * @return 上传到个人云盘的PPT文件ID
     */
    private String downloadAndUploadPpt(String designId, String userId, String sourceChannel, String pptName) {
        long startTime = System.currentTimeMillis();
        log.info("AIPPT-开始下载并上传PPT文件, userId: {}, designId: {}, pptName: {}",
                userId, designId, pptName);

        try {
            // 1. 根据designId获取PPT的下载exportUrl
            long getUrlStartTime = System.currentTimeMillis();
            String exportUrl = getPptExportUrl(designId, userId, sourceChannel);
            long getUrlTime = System.currentTimeMillis() - getUrlStartTime;
            log.info("AIPPT-获取到PPT下载URL, designId: {}, exportUrl: {}, 耗时: {}ms",
                    designId, exportUrl, getUrlTime);

            // 2. 根据下载exportUrl下载文件，获取文件的base64编码
            long downloadStartTime = System.currentTimeMillis();
            String base64Content = downloadFileToBase64(exportUrl, "PPT");
            long downloadTime = System.currentTimeMillis() - downloadStartTime;
            log.info("AIPPT-成功下载PPT文件并转换为base64, designId: {}, base64长度: {}, 耗时: {}ms",
                    designId, base64Content != null ? base64Content.length() : 0, downloadTime);

            // 3. 上传到个人云盘
            long uploadStartTime = System.currentTimeMillis();
            String path = chatTextToolBusinessConfig.getAiPptGenerate().getPersonalPath();
            String fileSuffix = "pptx";
            String fileName = pptName;
            Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();
            FileUploadVO fileUploadVO = yunDiskExternalService.uploadFileToCustomPath(
                    path,
                    AIModuleEnum.AI_TOOLS_BOX,
                    userId,
                    belongsPlatform,
                    fileSuffix,
                    base64Content,
                    fileName
            );
            long uploadTime = System.currentTimeMillis() - uploadStartTime;

            String fileId = fileUploadVO != null ? fileUploadVO.getFileId() : null;
            long totalTime = System.currentTimeMillis() - startTime;
            log.info("AIPPT-成功上传PPT文件到个人云盘, userId: {}, fileId: {}, fileName: {}, 总耗时: {}ms [获取URL: {}ms, 下载: {}ms, 上传: {}ms]",
                    userId, fileId, fileName, totalTime, getUrlTime, downloadTime, uploadTime);
            return fileId;
        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("AIPPT-下载并上传PPT文件失败: userId: {}, designId: {}, 耗时: {}ms, error: {}",
                    userId, designId, totalTime, e.getMessage(), e);
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                    "AIPPT-下载并上传PPT文件失败: " + e.getMessage());
        }
    }

    /**
     * 根据designId获取PPT的下载exportUrl
     * 使用aipptExternalService的exportFileWithRetry和queryExportStatusWithRetry方法
     *
     * @param designId 厂商作品ID
     * @param userId   用户ID
     * @return PPT下载URL
     */
    private String getPptExportUrl(String designId, String userId, String channel) {
        long startTime = System.currentTimeMillis();
        log.info("AIPPT-开始获取PPT下载URL, designId: {}, userId: {}", designId, userId);
        channel = "";
        try {
            // 1. 调用exportFileWithRetry获取taskKey
            long exportStartTime = System.currentTimeMillis();
            AipptExportRequestDTO exportRequest = AipptExportRequestDTO.builder()
                    .id(Long.valueOf(designId))
                    .format("ppt")
                    .edit("true")
                    .filesToZip("false")
                    .build();

            AipptExportResponseVO exportResponse = aipptExternalService.exportFileWithRetry(exportRequest, userId, channel);
            long exportTime = System.currentTimeMillis() - exportStartTime;
            log.info("AIPPT-调用导出接口耗时: {}ms", exportTime);

            if (exportResponse.getCode() != 0 || exportResponse.getData() == null) {
                throw new YunAiBusinessException(YunAiCommonResultCode.DOWNSTREAM_SERVICES_EXCEPTION,
                        "AIPPT-导出文件失败: " + exportResponse.getMsg());
            }

            String taskKey = exportResponse.getData();
            log.info("AIPPT-成功获取导出任务ID, taskKey: {}", taskKey);

            // 2. 调用queryExportStatusWithRetry得到下载url，如果返回"导出中"则重试
            // 使用配置的重试次数和间隔进行重试
            long queryStartTime = System.currentTimeMillis();
            int maxRetries = aipptProperties.getMaxRetries() != null ? aipptProperties.getMaxRetries() : 3;
            int retryInterval = aipptProperties.getRetryInterval() != null ? aipptProperties.getRetryInterval() : 1000;

            AipptExportResultResponseVO resultResponse = null;
            String downloadUrl = null;

            for (int attempt = 0; attempt <= maxRetries; attempt++) {
                long singleQueryStartTime = System.currentTimeMillis();
                resultResponse = aipptExternalService.queryExportStatusWithRetry(taskKey, userId, channel);
                long singleQueryTime = System.currentTimeMillis() - singleQueryStartTime;
                log.info("AIPPT-第{}次查询导出状态耗时: {}ms", attempt + 1, singleQueryTime);

                // 检查是否为"导出中"状态
                if ("导出中".equals(resultResponse.getMsg())) {
                    if (attempt < maxRetries) {
                        log.info("AIPPT-查询导出状态为'导出中'，等待{}ms后进行第{}次重试, taskKey: {}, userId: {}",
                                retryInterval, attempt + 1, taskKey, userId);
                        try {
                            Thread.sleep(retryInterval);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                                    "AIPPT-重试过程中被中断");
                        }
                        continue;
                    } else {
                        log.warn("AIPPT-达到最大重试次数{}，查询导出状态仍为'导出中', taskKey: {}, userId: {}",
                                maxRetries, taskKey, userId);
                        throw new YunAiBusinessException(YunAiCommonResultCode.DOWNSTREAM_SERVICES_EXCEPTION,
                                "AIPPT-导出超时，请稍后重试");
                    }
                }

                // 导出完成，获取下载链接
                downloadUrl = resultResponse.getData().get(0);
                long queryTime = System.currentTimeMillis() - queryStartTime;
                log.info("AIPPT-成功获取PPT下载URL, taskKey: {}, downloadUrl: {}, 尝试次数: {}, 查询总耗时: {}ms",
                        taskKey, downloadUrl, attempt + 1, queryTime);
                break;
            }

            long totalTime = System.currentTimeMillis() - startTime;
            log.info("AIPPT-获取PPT下载URL完成, 总耗时: {}ms [导出: {}ms, 查询: {}ms]",
                    totalTime, exportTime, System.currentTimeMillis() - queryStartTime);
            return downloadUrl;

        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("AIPPT-获取PPT下载URL失败, designId: {}, userId: {}, channel: {}, 耗时: {}ms, error: {}",
                    designId, userId, channel, totalTime, e.getMessage(), e);
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                    "AIPPT-获取PPT下载URL失败: " + e.getMessage());
        }
    }

    /**
     * 下载文件并转换为base64编码
     *
     * @param fileUrl  文件URL
     * @param fileType 文件类型（用于日志）
     * @return 文件的base64编码
     */
    private String downloadFileToBase64(String fileUrl, String fileType) {
        long startTime = System.currentTimeMillis();
        log.info("AIPPT-开始下载{}文件, fileUrl: {}", fileType, fileUrl);

        try {
            // 使用Hutool的HttpUtil下载文件
            long downloadStartTime = System.currentTimeMillis();
            byte[] fileBytes = HttpUtil.downloadBytes(fileUrl);
            long downloadTime = System.currentTimeMillis() - downloadStartTime;

            if (fileBytes == null || fileBytes.length == 0) {
                throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                        "AIPPT-下载的" + fileType + "文件为空");
            }

            // 转换为base64编码
            long encodeStartTime = System.currentTimeMillis();
            String base64Content = Base64.getEncoder().encodeToString(fileBytes);
            long encodeTime = System.currentTimeMillis() - encodeStartTime;

            long totalTime = System.currentTimeMillis() - startTime;
            log.info("AIPPT-成功下载{}文件, 文件大小: {} bytes, base64长度: {}, 总耗时: {}ms [下载: {}ms, 编码: {}ms]",
                    fileType, fileBytes.length, base64Content.length(), totalTime, downloadTime, encodeTime);
            return base64Content;
        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("AIPPT-下载{}文件失败, fileUrl: {}, 耗时: {}ms, error: {}", fileType, fileUrl, totalTime, e.getMessage(), e);
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                    "AIPPT-下载" + fileType + "文件失败: " + e.getMessage());
        }
    }

    /**
     * 根据图片URL获取图片后缀
     *
     * @param imageUrl 图片URL
     * @return 图片后缀，默认为jpg
     */
    private String getImageSuffix(String imageUrl) {
        if (StrUtil.isBlank(imageUrl)) {
            return "jpg";
        }

        // 从URL中提取文件后缀
        String lowerUrl = imageUrl.toLowerCase();
        if (lowerUrl.contains(".png")) {
            return "png";
        } else if (lowerUrl.contains(".jpeg") || lowerUrl.contains(".jpg")) {
            return "jpg";
        } else if (lowerUrl.contains(".gif")) {
            return "gif";
        } else if (lowerUrl.contains(".webp")) {
            return "webp";
        } else {
            return "jpg"; // 默认为jpg
        }
    }


    /**
     * 插入新对话到TiDB和HBase
     *
     * @param reqDTO      请求参数
     * @param pptFileId   PPT文件ID
     * @param coverFileId 封面图片文件ID
     * @param hbaseResult HBase对话结果实体
     * @return 新的对话ID
     */
    private String insertNewDialogue(AiPptResultInformReqDTO reqDTO, String pptFileId, String coverFileId, AiTextResultEntity hbaseResult) {
        long startTime = System.currentTimeMillis();
        log.info("AIPPT-开始插入新对话, userId: {}, contentDialogueId: {}, pptName: {}, pptFileId: {}, coverFileId: {}",
                reqDTO.getUserId(), reqDTO.getContentDialogueId(), reqDTO.getPptName(), pptFileId, coverFileId);
        try {
            // 1. 根据旧对话ID获取sessionId
            long getSessionStartTime = System.currentTimeMillis();
            Long sessionId = getSessionIdByDialogueId(reqDTO.getUserId(), reqDTO.getContentDialogueId());
            long getSessionTime = System.currentTimeMillis() - getSessionStartTime;
            log.info("AIPPT-获取到sessionId, contentDialogueId: {}, sessionId: {}, 耗时: {}ms",
                    reqDTO.getContentDialogueId(), sessionId, getSessionTime);

            // 2. 生成新的dialogueId
            long generateIdStartTime = System.currentTimeMillis();
            Long newDialogueId = uidGenerator.getUID();
            long generateIdTime = System.currentTimeMillis() - generateIdStartTime;
            log.info("AIPPT-生成新的dialogueId: {}, 耗时: {}ms", newDialogueId, generateIdTime);

            // 3. 从HBase结果中转换DialogueInputInfoDTO
            long convertStartTime = System.currentTimeMillis();
            DialogueInputInfoDTO originalDialogueInput = null;
            if (hbaseResult != null && StrUtil.isNotBlank(hbaseResult.getReqParameters())) {
                try {
                    originalDialogueInput = JSONUtil.toBean(hbaseResult.getReqParameters(), DialogueInputInfoDTO.class);
                    log.info("AIPPT-成功转换原始DialogueInputInfoDTO");
                } catch (Exception e) {
                    log.warn("AIPPT-转换原始DialogueInputInfoDTO失败, 将使用默认值, error: {}", e.getMessage());
                }
            }
            long convertTime = System.currentTimeMillis() - convertStartTime;
            log.info("AIPPT-转换DialogueInputInfoDTO耗时: {}ms", convertTime);

            // 4. 构造ChatAddHandleDTO
            long buildDtoStartTime = System.currentTimeMillis();
            ChatAddHandleDTO handleDTO = buildChatAddHandleDTO(reqDTO, sessionId, newDialogueId, originalDialogueInput);

            // 5. 构造AiTextResultRespParameters
            AiTextResultRespParameters hbaseResp = buildHbaseResponse(reqDTO, pptFileId, coverFileId);
            handleDTO.setHbaseResp(hbaseResp);
            long buildDtoTime = System.currentTimeMillis() - buildDtoStartTime;
            log.info("AIPPT-构造DTO和响应参数耗时: {}ms", buildDtoTime);

            // 6. 调用saveTidbAndHbaseResult保存数据
            long saveStartTime = System.currentTimeMillis();
            saveTidbAndHbaseResult(handleDTO);
            long saveTime = System.currentTimeMillis() - saveStartTime;
            log.info("AIPPT-保存数据耗时: {}ms", saveTime);

            long totalTime = System.currentTimeMillis() - startTime;
            log.info("AIPPT-成功插入新对话, newDialogueId: {}, sessionId: {}, 总耗时: {}ms [获取Session: {}ms, 生成ID: {}ms, 转换DTO: {}ms, 构造DTO: {}ms, 保存数据: {}ms]",
                    newDialogueId, sessionId, totalTime, getSessionTime, generateIdTime, convertTime, buildDtoTime, saveTime);
            return String.valueOf(newDialogueId);
        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("AIPPT-插入新对话失败: userId: {}, contentDialogueId: {}, 耗时: {}ms, error: {}",
                    reqDTO.getUserId(), reqDTO.getContentDialogueId(), totalTime, e.getMessage(), e);
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                    "AIPPT-插入新对话失败: " + e.getMessage());
        }
    }

    /**
     * 根据旧对话ID获取sessionId
     *
     * @param userId     用户ID
     * @param dialogueId 对话ID
     * @return sessionId
     */
    private Long getSessionIdByDialogueId(String userId, String dialogueId) {
        log.info("AIPPT-根据对话ID获取sessionId, userId: {}, dialogueId: {}", userId, dialogueId);
        try {
            // 从TiDB中查询对话记录获取sessionId
            AlgorithmChatContentEntity contentEntity = algorithmChatContentRepository.getByIdUserId(Long.valueOf(dialogueId), userId);
            if (contentEntity == null) {
                throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                        "AIPPT-未找到对话记录: " + dialogueId);
            }
            Long sessionId = contentEntity.getSessionId();
            if (sessionId == null) {
                throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                        "AIPPT-对话记录中缺少sessionId: " + dialogueId);
            }
            log.info("AIPPT-成功获取sessionId, dialogueId: {}, sessionId: {}", dialogueId, sessionId);
            return sessionId;
        } catch (Exception e) {
            log.error("AIPPT-根据对话ID获取sessionId失败: userId: {}, dialogueId: {}, error: {}",
                    userId, dialogueId, e.getMessage(), e);
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                    "AIPPT-获取sessionId失败: " + e.getMessage());
        }
    }

    /**
     * 构造ChatAddHandleDTO
     *
     * @param reqDTO                请求参数
     * @param sessionId             会话ID
     * @param newDialogueId         新对话ID
     * @param originalDialogueInput 原始对话输入信息（从HBase获取）
     * @return ChatAddHandleDTO
     */
    private ChatAddHandleDTO buildChatAddHandleDTO(AiPptResultInformReqDTO reqDTO, Long sessionId, Long newDialogueId, DialogueInputInfoDTO originalDialogueInput) {
        log.info("AIPPT-构造ChatAddHandleDTO, sessionId: {}, newDialogueId: {}", sessionId, newDialogueId);

        // 构造ChatAddReqDTO
        ChatAddReqDTO chatAddReqDTO = new ChatAddReqDTO();
        chatAddReqDTO.setUserId(reqDTO.getUserId());
        chatAddReqDTO.setSessionId(String.valueOf(sessionId));
        chatAddReqDTO.setSourceChannel(reqDTO.getSourceChannel());
        chatAddReqDTO.setApplicationType(ApplicationTypeEnum.CHAT.getCode());

        // 构造DialogueInputInfoDTO
        DialogueInputInfoDTO dialogueInput = new DialogueInputInfoDTO();
        dialogueInput.setDialogue("基于大纲和所选模板生成PPT");
        dialogueInput.setPrompt(StringUtils.EMPTY);
        dialogueInput.setInputTime(OffsetDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX")));

        // 设置意图编码
        DialogueIntentionDTO command = new DialogueIntentionDTO();
        command.setCommand(DialogueIntentionEnum.TEXT_TOOL.getCode());
        command.setSubCommand(DialogueIntentionSubEnum.AI_GENERATE_PPT.getCode());
        dialogueInput.setCommand(command);
        dialogueInput.setDialogueType(TalkTypeEnum.DIALOG.getType());
        dialogueInput.setCommandType(DialogueCommandTypeEnum.ORDINARY.getType());
        dialogueInput.setEnableForceNetworkSearch(false);
        dialogueInput.setEnableAllNetworkSearch(false);
        dialogueInput.setEnableAiSearch(false);
        dialogueInput.setEnableKnowledgeAndNetworkSearch(false);

        // 从原始DialogueInputInfoDTO中提取versionInfo和extInfo
        if (originalDialogueInput != null) {
            if (originalDialogueInput.getVersionInfo() != null) {
                dialogueInput.setVersionInfo(originalDialogueInput.getVersionInfo());
                log.info("AIPPT-设置versionInfo: {}", originalDialogueInput.getVersionInfo());
            }
            if (StrUtil.isNotBlank(originalDialogueInput.getExtInfo())) {
                dialogueInput.setExtInfo(originalDialogueInput.getExtInfo());
                log.info("AIPPT-设置extInfo: {}", originalDialogueInput.getExtInfo());
            }
        }
        chatAddReqDTO.setDialogueInput(dialogueInput);

        // 构造ChatAddHandleDTO
        ChatAddHandleDTO handleDTO = new ChatAddHandleDTO(chatAddReqDTO, null);
        handleDTO.setSessionId(sessionId);
        handleDTO.setDialogueId(newDialogueId);
        handleDTO.setIntentionCode(DialogueIntentionEnum.TEXT_TOOL.getCode());
        handleDTO.setSubIntentionCode(DialogueIntentionSubEnum.AI_GENERATE_PPT.getCode());
        handleDTO.setSaveMessage(false);
        handleDTO.setTaskId(Long.valueOf(reqDTO.getTaskId()));

        // 构造AlgorithmChatTidbSaveDTO
        AlgorithmChatTidbSaveDTO tidbSaveDTO = AlgorithmChatTidbSaveDTO.builder()
                .dialogueId(newDialogueId)
                .outAuditStatus(OutAuditStatusEnum.SUCCESS.getCode())
                .chatStatus(ChatStatusEnum.CHAT_SUCCESS.getCode())
                .msg("") // PPT生成成功，具体内容在HBase中
                .build();
        handleDTO.setAlgorithmChatTidbSaveDTO(tidbSaveDTO);
        log.info("AIPPT-成功构造ChatAddHandleDTO");
        return handleDTO;
    }

    /**
     * 构造HBase响应数据
     *
     * @param reqDTO      请求参数
     * @param pptFileId   PPT文件ID
     * @param coverFileId 封面图片文件ID
     * @return AiTextResultRespParameters
     */
    private AiTextResultRespParameters buildHbaseResponse(AiPptResultInformReqDTO reqDTO, String pptFileId, String coverFileId) {
        log.info("AIPPT-构造HBase响应数据, pptName: {}, pptFileId: {}, coverFileId: {}",
                reqDTO.getPptName(), pptFileId, coverFileId);

        // 构造PPT File对象
        File pptFile = buildFileFromFileId(pptFileId);

        // 构造封面 File对象
        File coverFile = null;
        if (StrUtil.isNotBlank(coverFileId)) {
            try {
                coverFile = buildFileFromFileId(coverFileId);
            } catch (Exception e) {
                log.warn("AIPPT-构造封面File对象失败, coverFileId: {}, error: {}", coverFileId, e.getMessage());
            }
        }

        // 构造AiFunctionResult
        AiFunctionResult aiFunctionResult = AiFunctionResult.builder()
                .code(DialogueIntentionEnum.TEXT_TOOL.getCode())
                .designId(reqDTO.getDesignId())
                .supplierType(SupplierTypeEnum.AIPPT.getCode())
                .progress(100)
                .file(pptFile)
                .cover(coverFile)
                .title(reqDTO.getPptName())
                .build();

        // 构造DialogueFlowResult
        DialogueFlowResult flowResult = new DialogueFlowResult();
        flowResult.setIndex(0);
        flowResult.setResultType(FlowResultTypeEnum.TOOL_RESULT.getType());
        flowResult.setOutContent("");
        flowResult.setReasoningContent("");
        flowResult.setFinishReason("stop");
        flowResult.setErrorCode("0000");
        flowResult.setErrorMessage("请求成功");
        flowResult.setOutputTime(new java.util.Date());
        flowResult.setOutAuditStatus(2);
        flowResult.setAiFunctionResult(aiFunctionResult);

        // 构造输出列表
        List<DialogueFlowResult> outputList = new ArrayList<>();
        outputList.add(flowResult);

        // 构造AiTextResultRespParameters
        AiTextResultRespParameters hbaseResp = AiTextResultRespParameters.builder()
                .version("v2")
                .resultCode("0000")
                .outputList(outputList)
                .build();
        hbaseResp.setResult(ResultCodeEnum.SUCCESS);
        log.info("AIPPT-成功构造HBase响应数据");
        return hbaseResp;
    }

    /**
     * 根据fileId构造File对象
     * 直接调用YunDiskV2ServiceImpl.getYunDiskContent方法
     *
     * @param fileId 文件ID
     * @return File对象
     */
    private File buildFileFromFileId(String fileId) {
        if (StrUtil.isBlank(fileId)) {
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                    "AIPPT-文件ID为空");
        }

        log.info("AIPPT-开始获取云盘文件详细信息, fileId: {}", fileId);

        try {
            // 直接调用YunDiskV2Service获取File对象
            YunDiskReqDTO yunDiskReqDTO = new YunDiskReqDTO(fileId);
            File file = yunDiskV2Service.getYunDiskContentAndBigThumbnailUrl(yunDiskReqDTO);
            if (file == null) {
                throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                        "AIPPT-未找到云盘文件信息: " + fileId);
            }
            log.info("AIPPT-成功获取File对象, fileId: {}, name: {}, size: {}",
                    file.getFileId(), file.getName(), file.getSize());
            return file;
        } catch (Exception e) {
            log.error("AIPPT-获取File对象失败, fileId: {}, error: {}", fileId, e.getMessage(), e);
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                    "AIPPT-获取File对象失败: " + e.getMessage());
        }
    }

    /**
     * 保存TiDB和HBase结果
     *
     * @param handleDTO 处理DTO
     */
    private void saveTidbAndHbaseResult(ChatAddHandleDTO handleDTO) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("AIPPT-开始保存TiDB和HBase数据, dialogueId: {}", handleDTO.getDialogueId());

            // 保存hbase-所有对话结果
            long hbaseStartTime = System.currentTimeMillis();
            if (handleDTO.getHbaseResp() != null && handleDTO.getHbaseResp().getOutputList() != null) {
                dataSaveService.saveHbaseAllChatResult(handleDTO, handleDTO.getHbaseResp());
                long hbaseTime = System.currentTimeMillis() - hbaseStartTime;
                log.info("AIPPT-成功保存HBase数据, dialogueId: {}, 耗时: {}ms", handleDTO.getDialogueId(), hbaseTime);
            }

            // 保存tidb-所有对话结果
            long tidbStartTime = System.currentTimeMillis();
            dataSaveService.saveTidbAllChatResult(handleDTO);
            long tidbTime = System.currentTimeMillis() - tidbStartTime;
            log.info("AIPPT-成功保存TiDB数据, dialogueId: {}, 耗时: {}ms", handleDTO.getDialogueId(), tidbTime);

            long totalTime = System.currentTimeMillis() - startTime;
            log.info("AIPPT-保存TiDB和HBase数据完成, dialogueId: {}, 总耗时: {}ms [HBase: {}ms, TiDB: {}ms]",
                    handleDTO.getDialogueId(), totalTime,
                    System.currentTimeMillis() - hbaseStartTime, tidbTime);
        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("AIPPT-保存TiDB和HBase数据失败, dialogueId: {}, 耗时: {}ms, error: {}",
                    handleDTO.getDialogueId(), totalTime, e.getMessage(), e);
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                    "AIPPT-保存对话数据失败: " + e.getMessage());
        }
    }

    /**
     * 第一步：从HBase查询原有对话数据
     *
     * @param userId     用户ID
     * @param dialogueId 对话ID
     * @return HBase响应参数
     */
    private AiTextResultRespParameters getHbaseRespParameters(String userId, String dialogueId) {
        log.info("AIPPT-开始查询HBase对话数据, userId: {}, dialogueId: {}", userId, dialogueId);

        try {
            // 从HBase查询对话记录
            AiTextResultEntity entity = aiTextResultRepository.getByRowKey(userId, dialogueId);
            if (entity == null) {
                throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                        "AIPPT-未找到对话记录: " + dialogueId);
            }

            // 解析响应参数
            String respParameters = entity.getRespParameters();
            if (ObjectUtil.isEmpty(respParameters)) {
                throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                        "AIPPT-对话记录中缺少响应参数");
            }

            AiTextResultRespParameters respParams = JSONUtil.toBean(respParameters, AiTextResultRespParameters.class);
            if (respParams == null || ObjectUtil.isEmpty(respParams.getOutputList())) {
                throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                        "AIPPT-响应参数解析失败或输出列表为空");
            }
            log.info("AIPPT-成功获取HBase响应参数, dialogueId: {}", dialogueId);
            return respParams;
        } catch (Exception e) {
            log.error("AIPPT-查询HBase对话数据失败, userId: {}, dialogueId: {}, error: {}",
                    userId, dialogueId, e.getMessage(), e);
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                    "AIPPT-查询HBase对话数据失败: " + e.getMessage());
        }
    }

    /**
     * 从响应参数中获取原文件信息
     *
     * @param respParams HBase响应参数
     * @return 原文件信息，如果不存在则返回null
     */
    private File getOriginalFileFromRespParams(AiTextResultRespParameters respParams) {
        log.info("AIPPT-开始从响应参数中获取原文件信息");
        try {
            // 获取第一条记录的文件信息
            DialogueFlowResult firstOutput = respParams.getOutputList().get(0);
            if (firstOutput.getAiFunctionResult() == null || firstOutput.getAiFunctionResult().getFile() == null) {
                log.info("AIPPT-HBase中File不存在，返回null");
                return null;
            }
            File originalFile = firstOutput.getAiFunctionResult().getFile();
            log.info("AIPPT-成功获取原文件信息, fileId: {}, parentFileId: {}",
                    originalFile.getFileId(), originalFile.getParentFileId());
            return originalFile;
        } catch (Exception e) {
            log.error("AIPPT-从响应参数中获取原文件信息失败, error: {}", e.getMessage(), e);
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                    "AIPPT-获取原文件信息失败: " + e.getMessage());
        }
    }

    /**
     * 统一的PPT上传方法，支持覆盖和新建两种模式
     *
     * @param reqDTO       请求参数
     * @param originalFile 原文件信息，如果为null则新建，否则覆盖
     * @return PPT文件ID
     */
    private String uploadPptFileUnified(AiPptFileSaveReqDTO reqDTO, File originalFile) {
        log.info("AIPPT-开始统一PPT上传, designId: {}, originalFile: {}",
                reqDTO.getDesignId(), originalFile != null ? originalFile.getFileId() : "null");

        try {
            if (originalFile != null) {
                // 覆盖上传模式
                log.info("AIPPT-使用覆盖上传模式, fileId: {}", originalFile.getFileId());
                // 1. 下载新的PPT文件
                String base64Content = downloadPptByDesignId(reqDTO.getDesignId(), reqDTO.getUserId(), reqDTO.getSourceChannel());
                // 2. 覆盖上传到个人云盘
                overwriteUploadToYunDisk(reqDTO, originalFile, base64Content);
                return originalFile.getFileId();
            } else {
                // 新建上传模式，直接调用复用的方法
                log.info("AIPPT-使用新建上传模式");
                return downloadAndUploadPpt(reqDTO.getDesignId(), reqDTO.getUserId(), reqDTO.getSourceChannel(), reqDTO.getPptName());
            }
        } catch (Exception e) {
            log.error("AIPPT-统一PPT上传失败, designId: {}, error: {}", reqDTO.getDesignId(), e.getMessage(), e);
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                    "AIPPT-PPT上传失败: " + e.getMessage());
        }
    }


    /**
     * 第二步：根据designId下载新的PPT作品
     *
     * @param designId 设计ID
     * @param userId   用户ID
     * @param channel  渠道标识
     * @return base64编码的文件内容
     */
    private String downloadPptByDesignId(String designId, String userId, String channel) {
        log.info("AIPPT-开始下载新PPT作品, designId: {}, userId: {}, channel: {}", designId, userId, channel);

        try {
            // 获取PPT下载URL
            String exportUrl = getPptExportUrl(designId, userId, channel);

            // 下载文件并转换为base64
            String base64Content = downloadFileToBase64(exportUrl, "PPT");
            log.info("AIPPT-成功下载新PPT作品, designId: {}, userId: {}, channel: {}, base64长度: {}",
                    designId, userId, channel, base64Content.length());
            return base64Content;

        } catch (Exception e) {
            log.error("AIPPT-下载新PPT作品失败, designId: {}, userId: {}, channel: {}, error: {}",
                    designId, userId, channel, e.getMessage(), e);
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                    "AIPPT-下载新PPT作品失败: " + e.getMessage());
        }
    }

    /**
     * 第三步：覆盖上传到个人云盘
     *
     * @param reqDTO        请求参数
     * @param originalFile  原文件信息
     * @param base64Content 新文件的base64内容
     */
    private void overwriteUploadToYunDisk(AiPptFileSaveReqDTO reqDTO, File originalFile, String base64Content) {
        log.info("AIPPT-开始覆盖上传到个人云盘, fileId: {}, pptName: {}",
                originalFile.getFileId(), reqDTO.getPptName());

        try {
            String userId = reqDTO.getUserId();
            String fileName = reqDTO.getPptName();
            Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();

            // 构造覆盖上传请求，参考AiEditServiceImpl.uploadToYunDisk
            FileUploadContentReqDTO fileUploadContentReqDTO = FileUploadContentReqDTO.builder()
                    .virtualFile(FileReqDTO.builder()
                            .base64(base64Content)
                            .name(fileName)
                            .build())
                    .userId(userId)
                    .catalogId(originalFile.getParentFileId())
                    .belongsPlatform(belongsPlatform)
                    .module(AIModuleEnum.AI_TOOLS_BOX.getModule())
                    .fileId(originalFile.getFileId()) // 使用原文件ID进行覆盖
                    .fileSuffix("pptx")
                    .build();

            // 执行覆盖上传
            yunDiskClient.uploadFile(fileUploadContentReqDTO);
            log.info("AIPPT-成功覆盖上传PPT文件, fileId: {}, fileName: {}",
                    originalFile.getFileId(), fileName);
        } catch (Exception e) {
            log.error("AIPPT-覆盖上传失败, fileId: {}, error: {}",
                    originalFile.getFileId(), e.getMessage(), e);

//            TODO 个人云目前覆盖报错了 throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
//                    "AIPPT-覆盖上传失败: " + e.getMessage());
        }
    }

    /**
     * 构造AiFunctionResult并更新HBase
     *
     * @param userId      用户ID
     * @param dialogueId  对话ID
     * @param reqDTO      请求参数
     * @param pptFileId   PPT文件ID
     * @param coverFileId 封面文件ID
     * @param respParams  HBase响应参数
     */
    private void updateHbaseAiFunctionResult(String userId, String dialogueId, AiPptFileSaveReqDTO reqDTO,
                                             String pptFileId, String coverFileId, AiTextResultRespParameters respParams) {
        log.info("AIPPT-开始构造AiFunctionResult并更新HBase, userId: {}, dialogueId: {}, pptFileId: {}, coverFileId: {}",
                userId, dialogueId, pptFileId, coverFileId);

        try {
            // 1. 构造PPT File对象
            File pptFile = buildFileFromFileId(pptFileId);

            // 2. 构造封面File对象
            File coverFile  = buildFileFromFileId(coverFileId);

            // 3. 构造AiFunctionResult
            AiFunctionResult aiFunctionResult = AiFunctionResult.builder()
                    .code(DialogueIntentionEnum.TEXT_TOOL.getCode())
                    .designId(reqDTO.getDesignId())
                    .supplierType(SupplierTypeEnum.AIPPT.getCode())
                    .progress(100)
                    .file(pptFile)
                    .cover(coverFile)
                    .title(reqDTO.getPptName())
                    .build();

            // 4. 更新响应参数中的AiFunctionResult
            if (respParams.getOutputList() != null && !respParams.getOutputList().isEmpty()) {
                DialogueFlowResult firstOutput = respParams.getOutputList().get(0);
                firstOutput.setAiFunctionResult(aiFunctionResult);
            }

            // 5. 更新HBase中的响应参数
            aiTextResultRepository.updateRespParameters(userId, Long.valueOf(dialogueId), JSONUtil.toJsonStr(respParams));
            log.info("AIPPT-成功构造AiFunctionResult并更新HBase, dialogueId: {}", dialogueId);
        } catch (Exception e) {
            log.error("AIPPT-构造AiFunctionResult并更新HBase失败, userId: {}, dialogueId: {}, error: {}",
                    userId, dialogueId, e.getMessage(), e);
            throw new YunAiBusinessException(YunAiCommonResultCode.UNKNOWN_ERROR,
                    "AIPPT-更新HBase失败: " + e.getMessage());
        }
    }

    @Override
    public AipptCodeGetResponseVO getCode(AipptCodeGetRequestDTO requestDTO) {
        String type = Boolean.TRUE.equals(requestDTO.getMobileTerminal()) ? AIPPT_MOBILE_TYPE : null;
        long startTime = System.currentTimeMillis();
        log.info("【AiPPT获取Code】开始执行，sourceChannel: {}, userId: {}",
                requestDTO.getSourceChannel(), requestDTO.getUserId());

        try {
            // 渠道校验
            if (!sourceChannelsProperties.isExist(requestDTO.getSourceChannel())) {
                log.error("【AiPPT获取Code】渠道不存在，sourceChannel: {}", requestDTO.getSourceChannel());
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }

            // 获取用户ID
            String userId = getUserId(requestDTO.getUserId());

            // 调用外部服务获取Code
            AipptCodeResponseVO codeResponse = aipptExternalService.getCode(userId, "", type);

            // 检查响应
            if (codeResponse == null || codeResponse.getCode() != 0 || codeResponse.getData() == null) {
                log.error("【AiPPT获取Code】外部服务返回异常，response: {}", codeResponse);
                throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
            }

            // 转换响应
            AipptCodeGetResponseVO responseVO = AipptCodeGetResponseVO.builder()
                    .apikey(codeResponse.getData().getApiKey())
                    .code(codeResponse.getData().getCode())
                    .timeExpire(codeResponse.getData().getTimeExpire())
                    .build();

            log.info("【AiPPT获取Code】执行成功，userId: {}, sourceChannel: {}", userId, requestDTO.getSourceChannel());
            return responseVO;

        } catch (YunAiBusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("【AiPPT获取Code】处理异常，sourceChannel: {}, userId: {}",
                    requestDTO.getSourceChannel(), requestDTO.getUserId(), e);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_SERVER_INTERNAL);
        } finally {
            long costTime = System.currentTimeMillis() - startTime;
            log.info("【AiPPT获取Code】执行完毕，总耗时: {}ms", costTime);
        }
    }

    /**
     * 获取用户ID
     * 优先使用传入的userId，如果为空则从RequestContextHolder获取
     *
     * @param userId 传入的用户ID
     * @return 用户ID
     */
    private String getUserId(String userId) {
        if (StrUtil.isNotBlank(userId)) {
            return userId;
        }

        String contextUserId = RequestContextHolder.getUserId();
        if (StrUtil.isBlank(contextUserId)) {
            log.error("【AiPPT获取Code】无法获取用户ID，请传入userId参数或确保token有效");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        return contextUserId;
    }
}