package com.zyhl.yun.api.outer.application.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 * ppt模板信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AiPptTemplateInfoVO {

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 封面图URL地址
     */
    private String cover;

    /**
     * 预览图URL列表
     */
    private List<String> previewList;

    /**
     * 模板名称
     */
    private String title;
}
