package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.dto.PptGenDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.vo.PptGenVO;
import com.zyhl.yun.api.outer.application.assembler.ChatFlowResultAssembler;
import com.zyhl.yun.api.outer.application.chatv2.dto.AiPptValidationResult;
import com.zyhl.yun.api.outer.application.chatv2.dto.AssistantChatV2PollingUpdateDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueIntentionDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueTextToolSettingDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueToolSettingDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.service.AlgorithmChatV2ContentService;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatContentService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.service.YunDiskV2Service;
import com.zyhl.yun.api.outer.application.chatv2.service.openapi.OpenapiLingxiCommonService;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueFlowResultVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueResultV2VO;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO.OpenApiLingxiCardAutoLink;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO.OpenApiLingxiCardLink;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO.OpenApiLingxiCardReplyMessage;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO.OpenApiLingxiCardReplyMessage.CardReplyItem;
import com.zyhl.yun.api.outer.application.config.ApplicationAgentLingxiConfig;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.application.config.ChatTextToolOpenConfig;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.config.BusinessParamProperties;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.AiFunctionResult;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueIntentionOutput;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.AiTextResultVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddFlowStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * AI PPT
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TextToolAiPptHandlerImpl extends AbstractChatAddV2Handler {

	/**
	 * 当前执行顺序
	 */
	private ExecuteSort thisExecuteSort = ExecuteSort.TEXT_TOOL_AI_PPT;
	@Resource
	private TextModelExternalService textModelExternalService;
	@Resource
	private ChatTextToolBusinessConfig chatTextToolBusinessConfig;
	@Resource
	private DataSaveService dataSaveService;
	@Resource
	private ChatContentService chatContentService;
	@Resource
	private AlgorithmChatV2ContentService contentService;
	@Resource
	private BusinessParamProperties businessParamProperties;
	@Resource
	private YunDiskV2Service yunDiskV2Service;
	@Resource
	private OpenapiLingxiCommonService openapiLingxiCommonService;
	@Resource
	private ChatFlowResultAssembler chatFlowResultAssembler;
	@Resource
	private AlgorithmChatContentRepository algorithmChatContentRepository;
	@Resource
	private ChatTextToolOpenConfig chatTextToolOpenConfig;
	@Resource
	private ApplicationAgentLingxiConfig applicationAgentLingxiConfig;

	/**
	 * AIPPT意图
	 */
	private static final String THIS_MAIN_INTENTION = DialogueIntentionEnum.TEXT_TOOL.getCode();
	private static final String THIS_SUB_INTENTION = DialogueIntentionSubEnum.AI_GENERATE_PPT.getCode();

	/**
	 * 创建线程池
	 */
	private final static int CORE_POOL_SIZE = 100;
	private final static int MAX_POOL_SIZE = 300;
	private final static long KEEP_ALIVE_TIME = 60;
	private final static int QUEUE_SIZE = 100000;
	private final static ThreadPoolExecutor POOL = new ThreadPoolExecutor(CORE_POOL_SIZE, MAX_POOL_SIZE,
			KEEP_ALIVE_TIME, TimeUnit.SECONDS, new ArrayBlockingQueue<>(QUEUE_SIZE));

	@Override
	public void afterPropertiesSet() throws Exception {
		// 支持的业务初始化
		List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
		thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
		thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
		this.setBusinessTypes(thisBusinessTypes);
	}

	@Override
	public int order() {
		return thisExecuteSort.getSort();
	}

	@Override
	public boolean execute(ChatAddHandleDTO innerDTO) {

		// 检查 innerDTO 是否为 null
		if (innerDTO == null || innerDTO.getReqDTO() == null || innerDTO.getReqDTO().getDialogueInput() == null) {
			return false;
		}
		DialogueInputInfoDTO dialogueInput = innerDTO.getReqDTO().getDialogueInput();

		// 检查意图
		DialogueIntentionDTO intention = dialogueInput.getCommand();
		if (intention == null || intention.getCommand() == null || intention.getSubCommand() == null) {
			return false;
		}
		String mainCommand = intention.getCommand();
		String subCommand = intention.getSubCommand();
		if (!(DialogueIntentionEnum.isTextToolIntention(mainCommand) && THIS_SUB_INTENTION.equals(subCommand))) {
			return false;
		}

		// 检查是否 PPT 生成
		DialogueToolSettingDTO toolSetting = dialogueInput.getToolSetting();
		if (toolSetting == null || toolSetting.getTextToolSetting() == null) {
			return false;
		}
		DialogueTextToolSettingDTO pptSetting = toolSetting.getTextToolSetting();
		if (!Boolean.TRUE.equals(pptSetting.getEnablePptMake())) {
			return false;
		}

		return true;
	}

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		log.info("进入{}", thisExecuteSort.getDesc());

		if (!chatTextToolOpenConfig.getAiPptOpenByChannel(RequestContextHolder.getSourceChannel())) {
			log.info("isAiPptOpen=false，不执行aippt意图，aippt生成转换为文本意图");
			handleDTO.setTextGenerateTextIntention();
			return true;
		}

		// 必填参数校验
		AiPptValidationResult aiPptValidationResult = AiPptValidationResult.validateRequiredParams(handleDTO);
		String editedOutline = aiPptValidationResult.getEditedOutline();
		String templateId = aiPptValidationResult.getTemplateId();
		checkPPTTemplate(templateId);
		String previousDialogueId = aiPptValidationResult.getPreviousDialogueId();
		// 获取上一次AI文本结果
		AiTextResultEntity aiTextResult = dataSaveService.getHbaseResult(RequestContextHolder.getUserId(),
				previousDialogueId);
		if (null == aiTextResult || null == aiTextResult.getRespParameters()) {
			log.error("hb查询不到上一次对话信息,dialogueId:{}", handleDTO.getDialogueId());
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}
		AiTextResultRespParameters respParams = JSONUtil.toBean(aiTextResult.getRespParameters(),
				AiTextResultRespParameters.class);
		// 检查上一次意图是否为AI-PPT
		DialogueIntentionOutput firstIntention = respParams.getOutputCommand();
		String mainCommand = firstIntention.getCommand();
		String subCommand = firstIntention.getSubCommand();
		if (!(DialogueIntentionEnum.isTextToolIntention(mainCommand) && THIS_SUB_INTENTION.equals(subCommand))) {
			return true;
		}
		handleDTO.setIntentionVO(DialogueIntentionVO.newMainIntention(THIS_MAIN_INTENTION, THIS_SUB_INTENTION));
		DialogueIntentionVO.IntentionInfo mainIntention = DialogueIntentionVO
				.getMainIntention(handleDTO.getIntentionVO());
		// 设置输出意图及参数信息
		handleDTO.getRespVO().setOutputCommandVO(mainIntention);
		// 处理输出内容
		List<DialogueFlowResult> outputList = respParams.getOutputList();
		if (CollUtil.isEmpty(outputList)) {
			log.error("查询大纲信息为空,dialogueId:{}", handleDTO.getDialogueId());
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}
		DialogueFlowResult firstOutput = outputList.get(0);
		// 端没传大纲
		if (StringUtils.isBlank(editedOutline)) {
			editedOutline = firstOutput.getOutContent();
		} else {
			firstOutput.setOutContent(editedOutline);
			dataSaveService.updateResult(RequestContextHolder.getUserId(), previousDialogueId, firstOutput);
		}
		// 创建PPT生成DTO
		PptGenDTO pptGenDTO = new PptGenDTO();
		pptGenDTO.setMarkdown(editedOutline);
		pptGenDTO.setTemplateId(Long.valueOf(templateId));
		pptGenDTO.setUserId(RequestContextHolder.getUserId());
		pptGenDTO.setParentPath(chatTextToolBusinessConfig.getAiPptGenerate().getPersonalPath());
		pptGenDTO.setCoverPath(chatTextToolBusinessConfig.getAiPptGenerate().getPersonalCoverPath());
		pptGenDTO.setToken(RequestContextHolder.getToken());
		// 生成PPT任务
		PptGenVO pptGenVO = textModelExternalService.generateAiPpt(pptGenDTO);
		if (null == pptGenVO || null == pptGenVO.getTaskId()) {
			log.error("创建任务失败 dialogueId:{}, pptGenVO:{}", handleDTO.getDialogueId(), JSONUtil.toJsonStr(pptGenVO));
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_AI_MODEL);
		}
		Long taskId = pptGenVO.getTaskId();
		String title = pptGenVO.getTitle();
		JSONObject jsonObject = new JSONObject();
		jsonObject.set("taskId", taskId);
		handleDTO.setOutResourceId(jsonObject.toString());
		// 保存数据库
		dataSaveService.add(handleDTO, ChatStatusEnum.CHAT_IN);

		// 任务创建完推送结果
		DialogueFlowResultVO flowResult = handleDTO.getRespVO().getFlowResult();
		handleDTO.getRespVO().setOutputCommandCode(THIS_MAIN_INTENTION, THIS_SUB_INTENTION);
		handleDTO.getRespVO().setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());
		flowResult.setResultType(FlowResultTypeEnum.TOOL_RESULT.getType());
		flowResult.setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());
		flowResult.setIndex(handleDTO.getFlowResultIndex());
		flowResult.setAiFunctionResult(buildAiFunctionResult(Boolean.FALSE, 0, taskId, title, null, null, null));

		// 保存hbase-所有对话结果
		AiTextResultRespParameters respParameters = AiTextResultRespParameters.builder()
				.version(AiTextResultVersionEnum.V2.getVersion()).resultCode(ResultCodeEnum.SUCCESS.getResultCode())
				.outputList(Collections.singletonList(chatFlowResultAssembler.getFlowResult(flowResult))).build();
		// 保存hbase
		dataSaveService.saveHbaseAllChatResult(handleDTO, respParameters);

		respParameters.setOutputCommandVO(handleDTO.getIntentionVO());

		// 轮询推送PPT生成结果
		runThread(handleDTO, taskId);
		return false;
	}

	/**
	 * 构建AiFunctionResult
	 *
	 * @param isFinish
	 * @param progress
	 * @param taskId
	 * @param title
	 * @param pptId
	 * @param file
	 * @param cover
	 * @return
	 */
	public AiFunctionResult buildAiFunctionResult(Boolean isFinish, Integer progress, Long taskId, String title,
			Long pptId, File file, File cover) {
		ChatTextToolBusinessConfig.AiPptGenerateBusiness aiPptGenerate = chatTextToolBusinessConfig.getAiPptGenerate();
		String previewUrl = aiPptGenerate.getH5Url(taskId, pptId);
		String pcPreviewUrl = aiPptGenerate.getPcUrl(taskId, pptId);
		return AiFunctionResult.builder().code(THIS_MAIN_INTENTION).title(title).progress(progress)
				.previewUrl(previewUrl).pcPreviewUrl(pcPreviewUrl).file(file).cover(cover).build();
	}

	/**
	 * 启动多线程轮询
	 *
	 * @param handleDTO the handle dto
	 */
	public void runThread(ChatAddHandleDTO handleDTO, Long taskId) {
		Map<String, String> logMap = LogCommonUtils.getCopyOfContextMap();
		RequestContextHolder.UserInfo userInfo = RequestContextHolder.getUserInfo();
		final String token = RequestContextHolder.getToken();
		// 开启异步线程轮询阿里结果
		POOL.execute(() -> {
			RequestContextHolder.setToken(token);
			RequestContextHolder.setUserInfo(userInfo);
			LogCommonUtils.initLogMDC(logMap);

			Long dialogueId = handleDTO.getDialogueId();
			int pollingUpdateCount = 0;
			while (true) {
				pollingUpdateCount++;
				// 默认进行中
				try {
					int maxThreadMaxPullUpdateCount = businessParamProperties.getTextToolThreadMaxPullUpdateCount();
					if (pollingUpdateCount > maxThreadMaxPullUpdateCount) {
						// 超过轮询次数
						break;
					}
					TimeUnit.SECONDS.sleep(businessParamProperties.getTextToolThreadSleepTime());
					AssistantChatV2PollingUpdateDTO dto = new AssistantChatV2PollingUpdateDTO();
					dto.setDialogueId(dialogueId);
					DialogueResultV2VO result = contentService.pollingUpdate(dto);
					log.info("对话循环输出 dialogueId:{}, pollingUpdateCount:{}, maxThreadMaxPullUpdateCount:{}", dialogueId,
							pollingUpdateCount, businessParamProperties.getImageToolThreadMaxPullUpdateCount());
					if (null != result) {
						if (!ChatStatusEnum.isChatIn(result.getStatus())) {
							// 非对话中，停止轮询
							break;
						}
					}
				} catch (Exception e) {
					log.info("AI PPT 循环获取结果 第{}次 dialogueId:{} error:", pollingUpdateCount, dialogueId, e);
				}
			}
		});

		// 设置完成，让前端跳出去，线程继续轮询
		handleDTO.getRespVO().getFlowResult().setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
		handleDTO.getRespVO().setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
		if (null != handleDTO.getLingxiParamInfo() && handleDTO.getLingxiParamInfo().isLingxiRespFlag()) {
			String paramCardButtonUrl = "";
			AiFunctionResult aiFunctionResult = handleDTO.getRespVO().getFlowResult().getAiFunctionResult();
			if (null != aiFunctionResult) {
				paramCardButtonUrl = aiFunctionResult.getPreviewUrl();
			}
			// 会议对话信息
			AlgorithmChatContentEntity meetingDialogue = openapiLingxiCommonService.getLastIntentionDialogue(
					handleDTO.getReqDTO().getUserId(), handleDTO.getSessionId(),
					String.valueOf(handleDTO.getDialogueId()));
			// 会议通知邮件信息
			MailInfoVO mailInfo = openapiLingxiCommonService.getMailInfoResult(meetingDialogue);

			// 结束之前，追加2个消息（卡片【查看PPT】、追尾【发送邮件通知】）
			String startPptColor = applicationAgentLingxiConfig.getTextConfig().getStartPptColor();
			String startPptImage = applicationAgentLingxiConfig.getTextConfig().getStartPptImage();
			String startPptTitle = applicationAgentLingxiConfig.getTextConfig().getStartPptTitle();
			String startPptSubtitle = applicationAgentLingxiConfig.getTextConfig().getStartPptSubtitle();
			String startPptButtonName = applicationAgentLingxiConfig.getTextConfig().getStartPptButtonName();
			String startPptButtonColor = applicationAgentLingxiConfig.getTextConfig().getStartPptButtonColor();
			String startPptButtonImage = applicationAgentLingxiConfig.getTextConfig().getStartPptButtonImage();
			String startPptBrowser = applicationAgentLingxiConfig.getTextConfig().getStartPptBrowser();
			String startPptButtonAutoOpenTs = applicationAgentLingxiConfig.getTextConfig()
					.getStartPptButtonAutoOpenTs();
			String pptSuccTitle = applicationAgentLingxiConfig.getTextConfig().getPptSuccTitle();
			String pptSuccReplyText = applicationAgentLingxiConfig.getTextConfig().getPptSuccReplyText();
			
			// 会议标题
			String meetingTitle = (null != mailInfo
					? chatTextToolBusinessConfig.getIntelligentMeeting().removeTitlePrefix(mailInfo.getTitle())
					: handleDTO.getInputInfoDTO().getDialogue());
			OpenApiLingxiChatRespVO.WebLink startPptWebLink = new OpenApiLingxiChatRespVO.WebLink(paramCardButtonUrl);
			if (StringUtils.isNotEmpty(startPptBrowser)) {
				// 设置浏览器前缀
				startPptWebLink.setUrl(startPptBrowser + startPptWebLink.getUrl());
			}
			OpenApiLingxiCardReplyMessage replyMessage = new OpenApiLingxiCardReplyMessage(
					Collections.singletonList(new CardReplyItem(startPptColor, startPptImage, startPptTitle,
							startPptSubtitle,
							new OpenApiLingxiCardLink(
									startPptWebLink, startPptButtonColor, startPptButtonImage, startPptButtonName))),
					"",
					Collections.singletonList(new OpenApiLingxiCardLink(pptSuccTitle,
							new OpenApiLingxiCardLink.ReplyLink(String.format(pptSuccReplyText, meetingTitle)))),
					new OpenApiLingxiCardAutoLink(startPptWebLink, startPptButtonAutoOpenTs));
			handleDTO.getSseEmitterOperate()
					.sendAndComplete(OpenApiLingxiChatRespVO.getOpenApiLingxiChatReplyResp(handleDTO, replyMessage));

		} else {
			handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(handleDTO.getRespVO()));
		}

	}

	/**
	 * 校验PPT模板ID
	 *
	 * @param templateId the template id
	 * <AUTHOR>
	 * @date 2025/5/27 14:01
	 */
	private void checkPPTTemplate(String templateId) {
		Optional<List<ChatTextToolBusinessConfig.PPTTemplateWhite>> pptTemplateWhitesOptional = Optional
				.of(chatTextToolBusinessConfig).map(ChatTextToolBusinessConfig::getAiPptGenerate)
				.map(ChatTextToolBusinessConfig.AiPptGenerateBusiness::getTemplateWhiteList);
		if (pptTemplateWhitesOptional.isPresent() && CollUtil.isNotEmpty(pptTemplateWhitesOptional.get())) {
			// 当白名单配置的列表里包含这个模板id，并且当前用户不在白名单中，则抛异常
			boolean anyMatch = pptTemplateWhitesOptional.get().stream()
					.anyMatch(pptTemplateWhite -> pptTemplateWhite.getIds().contains(templateId)
							&& !pptTemplateWhite.getUserList().contains(RequestContextHolder.getPhoneNumber()));
			if (anyMatch) {
				log.warn("PPT模板ID不存在或无权限使用,templateId:{}", templateId);
				throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
			}
		}
	}

}