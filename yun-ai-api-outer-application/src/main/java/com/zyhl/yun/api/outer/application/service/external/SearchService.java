package com.zyhl.yun.api.outer.application.service.external;


import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.dto.FaceSearchDTO;
import com.zyhl.yun.api.outer.application.dto.SearchDTO;
import com.zyhl.yun.api.outer.application.dto.TemplateMatchDTO;
import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;
import com.zyhl.yun.api.outer.application.vo.FaceSearchVO;
import com.zyhl.yun.api.outer.application.vo.SearchPageVO;
import com.zyhl.yun.api.outer.domain.resp.TemplateMatchRsqEntity;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchCommonParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchResult;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

/**
 * 智能搜索
 *
 * <AUTHOR>
 */
public interface SearchService {

    /**
     * 获取搜索结果
     *
     * @param dto 搜索参数
     * @return 搜索结果
     */
    SearchPageVO getSearchPage(SearchDTO dto);

    /**
     * 人脸搜索
     * @param dto dto
     * @return 人脸搜索结果
     */
    FaceSearchVO getClassFaceInfo(FaceSearchDTO dto);

    /**
     * 模板匹配
     * @param dto dto
     * @return 模版匹配结果
     */
    BaseResult<TemplateMatchRsqEntity> templateMatch(TemplateMatchDTO dto);

}
