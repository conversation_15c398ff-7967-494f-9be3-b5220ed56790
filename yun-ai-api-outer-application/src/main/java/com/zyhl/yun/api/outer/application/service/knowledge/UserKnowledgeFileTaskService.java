package com.zyhl.yun.api.outer.application.service.knowledge;

import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileAddReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileDeleteReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileListBatchReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileTaskResultReqDTO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeTaskResultVO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeTaskV2ResultVO;

/**
 * 用户知识库文件转存任务
 *
 * <AUTHOR>
 */
public interface UserKnowledgeFileTaskService {

    /**
     * 用户添加知识库文件，发起转存任务
     *
     * @param dto 入参
     * @return 任务id
     */
    Long add(KnowledgeFileAddReqDTO dto);

    /**
     * 用户添加知识库文件，发起转存任务
     *
     * @param dto 入参
     * @return 任务id
     */
    KnowledgeTaskResultVO add180(KnowledgeFileAddReqDTO dto);

    /**
     * 获取上传任务结果
     *
     * @param dto 入参
     * @return 结果
     */
    KnowledgeTaskResultVO getTaskResult(KnowledgeFileTaskResultReqDTO dto);

    /**
     * 知识库文件删除
     *
     * @param dto 入参
     */
    void delete(KnowledgeFileDeleteReqDTO dto);

    void batchDelete(KnowledgeFileDeleteReqDTO dto);

    Long batchDeleteAsync(KnowledgeFileListBatchReqDTO dto);

    KnowledgeTaskV2ResultVO getTask(KnowledgeFileTaskResultReqDTO dto);
}
