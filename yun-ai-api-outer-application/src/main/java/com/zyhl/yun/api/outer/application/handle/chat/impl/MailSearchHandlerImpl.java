package com.zyhl.yun.api.outer.application.handle.chat.impl;

import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.enums.UserBelongsPlatformEnum;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.handle.chat.AbstractChatAddHandler;
import com.zyhl.yun.api.outer.application.service.AlgorithmAiRegisterService;
import com.zyhl.yun.api.outer.application.service.external.SearchService;
import com.zyhl.yun.api.outer.application.vo.AlgorithmChatAddVO;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddResultTypeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.VersionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * 搜索意图（云邮助手）
 */
@Slf4j
@Component
public class MailSearchHandlerImpl extends AbstractChatAddHandler {

    @Resource
    private SearchService searchService;

    @Resource
    private AlgorithmAiRegisterService aiRegisterService;

    @Override
    public int order() {
        return ExecuteSort.MAIL_SEARCH.getSort();
    }

    @Override
    public boolean execute(ChatAddInnerDTO innerDTO) {
        /**
         * 执行的条件：
         * 1、应用类型 == chat（普通对话）
         * 2、云邮助手渠道号
         * 3、云邮助手搜索意图
         */
		String sourceChannel = innerDTO.getContent().getSourceChannel();
		String businessType = innerDTO.getContent().getBusinessType();
		String currentIntentionCode = innerDTO.getIntentionCode();
		boolean isChat = ApplicationTypeEnum.isChat(innerDTO.getReqParams().getApplicationType());
		boolean isMail = sourceChannelsProperties.isMail(sourceChannel);
		boolean isMailSearchIntention = DialogueIntentionEnum.isMailSearchIntention(businessType, currentIntentionCode);
		log.info(
				"execute sourceChannel:{}, businessType:{}, currentIntentionCode:{}, isChat:{}, isMail:{}, isMailSearchIntention:{}",
				sourceChannel, businessType, currentIntentionCode, isChat, isMail, isMailSearchIntention);
        return isChat && isMail && isMailSearchIntention;
    }

    @Override
    public boolean run(ChatAddInnerDTO innerDTO) {
        log.info("进入【云邮助手】搜索功能处理");

        /** 参数初始化 */
        // 意图编码
        String intentionCode = innerDTO.getIntentionCode();
        // 用户当前底座
        Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();

        /** 检查是否更新为对话意图（000）（true-更新为对话意图（000），执行下一个Handler；false-继续执行搜索逻辑） */
        if (checkAndUpdateTextIntention(innerDTO, intentionCode, belongsPlatform)) {
            return true;
        }

        /** 用户授权处理 */
        // 意图012或018 && 用户为【新底座】
        if (DialogueIntentionEnum.existImageIntention(intentionCode)
                && !UserBelongsPlatformEnum.OSE.getBelongsPlatform().equals(belongsPlatform)) {
            AlgorithmChatAddDTO dto = innerDTO.getReqParams();
            boolean albumFlag = aiRegisterService.checkAlbum(dto.getUserId());
            // 用户未报名，设置用户【智能相册】授权开关状态：0-未开启，返回同步结果，终止Handler执行，不需要保存数据库
            if (!albumFlag) {
                AlgorithmChatAddVO respParams = innerDTO.getRespParams();
                respParams.setAiAlbumStatus(0);
                respParams.setResultType(ChatAddResultTypeEnum.SYNCHRONIZATION.getType());
                log.info("【云邮助手】搜索功能处理，用户为新底座，【智能相册】授权开关状态：0-未开启，返回同步结果，终止Handler执行，不需要保存数据库");
                return false;
            }
        }

        // 继续执行搜索逻辑
        mailSearchIntentionHandle(innerDTO);

        return false;
    }

    /**
     * 检查是否更新为对话意图（000）
     * @Author: WeiJingKun
     *
     * @param innerDTO 用户输入对象
     * @param intentionCode 意图编码
     * @param belongsPlatform 用户当前底座
     * @return boolean true-更新为对话意图（000），执行下一个Handler；false-继续执行搜索逻辑
     */
    private boolean checkAndUpdateTextIntention(ChatAddInnerDTO innerDTO, String intentionCode, Integer belongsPlatform) {
        DialogueIntentionEnum intentionEnum = DialogueIntentionEnum.getByCode(intentionCode);
        if (Objects.isNull(intentionEnum)) {
            // 对话意图枚举为null，更新为对话意图（000），执行下一个Handler
            log.error("【云邮助手】搜索功能处理，dialogueIntentionEnum is null，更新为对话意图（000），执行下一个Handler");
            innerDTO.setTextGenerateTextIntention();
            return true;
        }
        switch (intentionEnum) {
            /** 搜图片 */
            case SEARCH_IMAGE:
                // h5Version < 邮箱2.0.13版本，更新为对话意图（000），执行下一个Handler
                if (VersionUtil.mailH5VersionLt2013()) {
                    log.info("【云邮助手】搜索功能处理，h5Version < 邮箱2.0.13版本：{}，【搜图片】更新为对话意图（000），执行下一个Handler", RequestContextHolder.getH5Version());
                    innerDTO.setTextGenerateTextIntention();
                    return true;
                }
                // 用户为【老底座】，更新为对话意图（000），执行下一个Handler
                if (UserBelongsPlatformEnum.OSE.getBelongsPlatform().equals(belongsPlatform)) {
                    innerDTO.setTextGenerateTextIntention();
                    log.info("【云邮助手】搜索功能处理，用户为【老底座】，【搜图片】更新为对话意图（000），执行下一个Handler");
                    return true;
                }
                break;
            /** 搜文档 */
            case SEARCH_DOCUMENT:
                // h5Version < 邮箱2.0.15版本，更新为对话意图（000），执行下一个Handler
                if (VersionUtil.mailH5VersionLt2015()) {
                    log.info("【云邮助手】搜索功能处理，h5Version < 邮箱2.0.15版本：{}，【搜文档】更新为对话意图（000），执行下一个Handler", RequestContextHolder.getH5Version());
                    innerDTO.setTextGenerateTextIntention();
                    return true;
                }
                break;
            /** 搜邮件 */
            case SEARCH_MAIL:
                // h5Version < 邮箱2.0.15版本，更新为对话意图（000），执行下一个Handler
                if (VersionUtil.mailH5VersionLt2015()) {
                    log.info("【云邮助手】搜索功能处理，h5Version < 邮箱2.0.15版本：{}，【搜邮件】更新为对话意图（000），执行下一个Handler", RequestContextHolder.getH5Version());
                    innerDTO.setTextGenerateTextIntention();
                    return true;
                }
                break;
            /** 搜笔记 */
            case SEARCH_NOTE:
                // h5Version < 邮箱2.0.16版本，更新为对话意图（000），执行下一个Handler
                if (VersionUtil.mailH5VersionLt2016()) {
                    log.info("【云邮助手】搜索功能处理，h5Version < 邮箱2.0.16版本：{}，【搜笔记】更新为对话意图（000），执行下一个Handler", RequestContextHolder.getH5Version());
                    innerDTO.setTextGenerateTextIntention();
                    return true;
                }
                break;
            /** 功能搜索 */
            case SEARCH_FUNCTION:
                // h5Version < 邮箱2.0.16版本，更新为对话意图（000），执行下一个Handler
                if (VersionUtil.mailH5VersionLt2016()) {
                    log.info("【云邮助手】搜索功能处理，h5Version < 邮箱2.0.16版本：{}，【功能搜索】更新为对话意图（000），执行下一个Handler", RequestContextHolder.getH5Version());
                    innerDTO.setTextGenerateTextIntention();
                    return true;
                }
                break;
            default:
                break;
        }
        return false;
    }

}
