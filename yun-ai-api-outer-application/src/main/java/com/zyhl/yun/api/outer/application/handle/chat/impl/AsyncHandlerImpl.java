package com.zyhl.yun.api.outer.application.handle.chat.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.client.centertask.vo.CenterTaskCreateVO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.handle.chat.AbstractChatAddHandler;
import com.zyhl.yun.api.outer.application.service.chat.ChatConfigService;
import com.zyhl.yun.api.outer.application.strategy.DialogueTaskManager;
import com.zyhl.yun.api.outer.domain.vo.ChatConfigVO;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddResultTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 异步处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AsyncHandlerImpl extends AbstractChatAddHandler {

    @Resource
    private DialogueTaskManager dialogueTaskManager;
    @Resource
    private ChatConfigService chatConfigService;

    @Resource
    private UidGenerator uidGenerator;

    @Override
    public int order() {
        return ExecuteSort.ASYNC.getSort();
    }


    @Override
    public boolean run(ChatAddInnerDTO innerDTO) {
        log.info("进入异步结果处理");
        AlgorithmChatAddDTO dto = innerDTO.getReqParams();

        //对会话资源类型判断送审
        String resourceContent = resourceContent(dto);

        // 用户设置的模型
        ChatConfigVO model = chatConfigService.getUserSetModel(dto);

        // 先生成任务id，保存hbase（否则消费的时候可能查不到数据）
        Long taskId = uidGenerator.getUID();
        saveTextResult(innerDTO, String.valueOf(taskId),"", resourceContent);
        dto.setTaskId(taskId);

        // 生成任务
        CenterTaskCreateVO taskVO = dialogueTaskManager.initializeDialogueIntentionTask(dto, innerDTO.getIntentionCode(), model, innerDTO.getDialogueId(), innerDTO.getContent().getSourceChannel());
        if (taskVO == null || CharSequenceUtil.isEmpty(taskVO.getTaskId())) {
            log.error("用户{}会话-创建任务表失败, dialogueId: {}, sessionId: {}", dto.getUserId(), innerDTO.getDialogueId(), innerDTO.getSessionId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_CALL_EXCEPTION);
        }

        // 保存数据库
        add(innerDTO, ChatStatusEnum.CHAT_IN, taskId, model.getModelType());

        //返回异步结果
        innerDTO.getRespParams().setResultType(ChatAddResultTypeEnum.ASYNCHRONOUS.getType());
        return false;
    }

}
