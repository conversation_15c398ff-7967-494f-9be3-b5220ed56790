package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowLedgeLabelListVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

/**
 * 知识库标签列表请求参数
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class KnowledgeLabelSortReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 标签列表，无需把“全部”和“未分类”传过来 （但是不要相信前端的传参，后端做过滤）
     */
    private List<KnowLedgeLabelListVO> labelList;


}
