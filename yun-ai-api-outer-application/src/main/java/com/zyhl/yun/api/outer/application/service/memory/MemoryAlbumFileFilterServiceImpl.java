package com.zyhl.yun.api.outer.application.service.memory;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.api.outer.application.service.MemoryAlbumFileFilterService;
import com.zyhl.yun.api.outer.config.MemoryAlbumConfig;
import com.zyhl.yun.api.outer.domain.aggregate.PictureFileAggregate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-06-11 15:19
 */
@Service
@Slf4j
public class MemoryAlbumFileFilterServiceImpl implements MemoryAlbumFileFilterService {

    @Resource
    private MemoryAlbumConfig memoryAlbumConfig;

    @Override
    public List<String> defaultFilterFile(List<PictureFileAggregate> fileAggregateList, Integer searchSize) {
        // 按照全部的处理
        log.info("【意图回忆相册】采取全部筛选规则,searchSize为空或少于0按照全部筛选处理:{}", searchSize);
        return dealAllPictureFile(fileAggregateList);
    }

    @Override
    public List<String> yearFilterFile(List<PictureFileAggregate> fileAggregateList, Integer searchSize) {
        if (ObjectUtil.isEmpty(searchSize) || searchSize <= 0){
            //按照全部的处理
            log.info("【意图回忆相册】采取年筛选规则,searchSize为空或少于0按照全部筛选处理:{}", searchSize);
            return dealAllPictureFile(fileAggregateList);
        }

        // 2.根据拍摄时间年份进行分组
        Map<String, List<PictureFileAggregate>> groupedByYear = fileAggregateList.stream()
                //设置年份（如有需要）
                .peek(PictureFileAggregate::setYearFilter)
                .collect(Collectors.groupingBy(
                		// 按年份分组
                        PictureFileAggregate::getFilterGroup, 
                        Collectors.toList()
                ));

        log.info("【意图回忆相册】采取年筛选规则,分组筛选数量:{},对应年key:{},", JSONUtil.toJsonStr(groupedByYear.keySet()), searchSize);

        // 3.根据分组进行挑选 从每组中挑选 searchSize 条记录（如按评分排序后取前 searchSize 个）
        fileAggregateList= groupedByYear.values().stream()
                .flatMap(yearGroupFileList -> yearGroupFileList.stream()
                        // 按评分降序
                        .sorted(Comparator.comparing(PictureFileAggregate::getScore).reversed())
                        // 每组取前searchSize个
                        .limit(searchSize)
                        // 最大总数量限制
                ).limit(memoryAlbumConfig.getAlbumFileMaxNum())
                .collect(Collectors.toList());

        List<String> fileIdList = fileAggregateList.stream()
                .map(PictureFileAggregate::getFileId)
                .collect(Collectors.toList());
        log.info("【意图回忆相册】采取年筛选规则结束,筛选文件id列表:{}", JSONUtil.toJsonStr(fileIdList));
        return fileIdList;
    }

    /**
     * 处理全部的图片
     *
     * @param fileAggregateList 图片文件列表
     * @return 待生成相册的文件id列表
     */
    private List<String> dealAllPictureFile(List<PictureFileAggregate> fileAggregateList) {
        fileAggregateList.sort(Comparator.comparing(PictureFileAggregate::getShootTime));

        fileAggregateList = fileAggregateList.stream()
                .limit(memoryAlbumConfig.getAlbumFileMaxNum())
                .collect(Collectors.toList());

        List<String> fileIdList = fileAggregateList.stream()
                .map(PictureFileAggregate::getFileId)
                .collect(Collectors.toList());
        log.info("【意图回忆相册】采取全部筛选规则结束,筛选文件id列表:{}", JSONUtil.toJsonStr(fileIdList));
        return fileIdList;
    }

    @Override
    public List<String> seasonFilterFile(List<PictureFileAggregate> fileAggregateList, Integer searchSize) {
        // 1.分组单次取值长度为空,默认取全部,取全部根据质量评分选择即可
        if (ObjectUtil.isEmpty(searchSize) || searchSize <= 0){
            //按照全部的处理
            log.info("【意图回忆相册】采取季度筛选规则,searchSize为空或少于0按照全部筛选处理:{}", searchSize);
            return dealAllPictureFile(fileAggregateList);
        }

        // 2.根据拍摄时间年份进行分组
        Map<String, List<PictureFileAggregate>> groupedByYear = fileAggregateList.stream()
                //设置年份（如有需要）
                .peek(PictureFileAggregate::setYearSeasonFilter)
                .collect(Collectors.groupingBy(
                        PictureFileAggregate::getFilterGroup,
                        Collectors.toList()
                ));
        log.info("【意图回忆相册】采取季度筛选规则,分组筛选数量:{},对应季度key:{},", JSONUtil.toJsonStr(groupedByYear.keySet()), searchSize);

        // 3.根据分组进行挑选 从每组中挑选 searchSize 条记录（如按评分排序后取前 searchSize 个）
        fileAggregateList= groupedByYear.values().stream()
                .flatMap(yearGroupFileList -> yearGroupFileList.stream()
                                // 按评分降序
                                .sorted(Comparator.comparing(PictureFileAggregate::getScore).reversed())
                                // 每组取前searchSize个
                                .limit(searchSize)
                        // 最大总数量限制
                ).limit(memoryAlbumConfig.getAlbumFileMaxNum())
                .collect(Collectors.toList());

        List<String> fileIdList = fileAggregateList.stream()
                .map(PictureFileAggregate::getFileId)
                .collect(Collectors.toList());
        log.info("【意图回忆相册】采取季度筛选规则,筛选文件id列表:{}", JSONUtil.toJsonStr(fileIdList));
        return fileIdList;
    }

    @Override
    public List<String> monthFilterFile(List<PictureFileAggregate> fileAggregateList, Integer searchSize) {
        // 1.分组单次取值长度为空,默认取全部,取全部根据质量评分选择即可
        if (ObjectUtil.isEmpty(searchSize) || searchSize <= 0){
            //按照全部的处理
            log.info("【意图回忆相册】采取月度筛选规则,searchSize为空或少于0按照全部筛选处理:{}", searchSize);
            return dealAllPictureFile(fileAggregateList);
        }

        // 2.根据拍摄时间年份进行分组
        Map<String, List<PictureFileAggregate>> groupedByYear = fileAggregateList.stream()
                //设置年+月份
                .peek(PictureFileAggregate::setYearMonthFilter)
                .collect(Collectors.groupingBy(
                        // 按年+月份分组
                        PictureFileAggregate::getFilterGroup,
                        Collectors.toList()
                ));

        log.info("【意图回忆相册】采取月筛选规则,分组筛选数量:{},对应月key:{},", JSONUtil.toJsonStr(groupedByYear.keySet()), searchSize);

        // 3.根据分组进行挑选 从每组中挑选 searchSize 条记录（如按评分排序后取前 searchSize 个）
        fileAggregateList= groupedByYear.values().stream()
                .flatMap(yearGroupFileList -> yearGroupFileList.stream()
                                // 按评分降序
                                .sorted(Comparator.comparing(PictureFileAggregate::getScore).reversed())
                                // 每组取前searchSize个
                                .limit(searchSize)
                        // 最大总数量限制
                ).limit(memoryAlbumConfig.getAlbumFileMaxNum())
                .collect(Collectors.toList());

        List<String> fileIdList = fileAggregateList.stream()
                .map(PictureFileAggregate::getFileId)
                .collect(Collectors.toList());
        log.info("【意图回忆相册】采取月筛选规则,筛选文件id列表:{}", JSONUtil.toJsonStr(fileIdList));
        return fileIdList;
    }

    @Override
    public List<String> weekFilterFile(List<PictureFileAggregate> fileAggregateList, Integer searchSize) {
        // 1.分组单次取值长度为空,默认取全部,取全部根据质量评分选择即可
        if (ObjectUtil.isEmpty(searchSize) || searchSize <= 0){
            //按照全部的处理
            log.info("【意图回忆相册】采取周筛选规则,searchSize为空或少于0按照全部筛选处理:{}", searchSize);
            return dealAllPictureFile(fileAggregateList);
        }

        // 2.根据拍摄时间年份进行分组
        Map<String, List<PictureFileAggregate>> groupedByYear = fileAggregateList.stream()
                //设置年+月份
                .peek(PictureFileAggregate::setYearWeekFilter)
                .collect(Collectors.groupingBy(
                        // 按年+月份分组
                        PictureFileAggregate::getFilterGroup,
                        Collectors.toList()
                ));
        log.info("【意图回忆相册】周筛选规则,对应周key:{},分组筛选数量:{}", JSONUtil.toJsonStr(groupedByYear.keySet()), searchSize);

        // 3.根据分组进行挑选 从每组中挑选 searchSize 条记录（如按评分排序后取前 searchSize 个）
        fileAggregateList= groupedByYear.values().stream()
                .flatMap(yearGroupFileList -> yearGroupFileList.stream()
                                // 按评分降序
                                .sorted(Comparator.comparing(PictureFileAggregate::getScore).reversed())
                                // 每组取前searchSize个
                                .limit(searchSize)
                        // 最大总数量限制
                ).limit(memoryAlbumConfig.getAlbumFileMaxNum())
                .collect(Collectors.toList());

        List<String> fileIdList = fileAggregateList.stream()
                .map(PictureFileAggregate::getFileId)
                .collect(Collectors.toList());
        log.info("【意图回忆相册】周筛选规则,筛选文件id列表:{}", JSONUtil.toJsonStr(fileIdList));
        return fileIdList;
    }
}
