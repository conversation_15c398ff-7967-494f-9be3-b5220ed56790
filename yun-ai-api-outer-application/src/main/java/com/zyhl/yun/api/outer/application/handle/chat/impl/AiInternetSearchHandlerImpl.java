package com.zyhl.yun.api.outer.application.handle.chat.impl;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.hcy.yun.ai.common.base.enums.PantaLabelEnum;
import com.zyhl.hcy.yun.ai.common.base.es.domain.datahelper.repository.EsPanTaResourceRepository;
import com.zyhl.hcy.yun.ai.common.base.es.dto.PanTaResourceOuterSearchV2DTO;
import com.zyhl.hcy.yun.ai.common.base.es.entity.EsPanTaResourceEntity;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.hcy.yun.ai.common.model.api.client.ali.dto.GenericSearchDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.ali.vo.GenericSearchVO;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.dto.TextNerExtractDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.event.TextModelStreamEventListener;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.AiInternetSearchConvertor;
import com.zyhl.yun.api.outer.application.dto.AiInternetSearchQueryDTO;
import com.zyhl.yun.api.outer.application.dto.AiInternetSearchScheduleDTO;
import com.zyhl.yun.api.outer.application.dto.AiMultiSearchDTO;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.handle.chat.AbstractChatAddHandler;
import com.zyhl.yun.api.outer.application.handle.chat.listener.SseDialogueEventListener;
import com.zyhl.yun.api.outer.config.AllNetworkSearchProperties;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.config.textmodel.ModelPromptProperties;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domainservice.BlackResourceHandleService;
import com.zyhl.yun.api.outer.domainservice.QpsLimitService;
import com.zyhl.yun.api.outer.enums.BlackResourceParityEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.external.AiInternetSearchExternalService;
import com.zyhl.yun.api.outer.external.CmicTextService;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.MultiSearchVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * className: AiInternetSearchHandlerImpl
 * description: AI全网搜处理器实现类
 *
 * <AUTHOR>
 * @date 2025/3/12
 */
@Slf4j
@Component
public class AiInternetSearchHandlerImpl extends AbstractChatAddHandler {

    @Resource
    private AllNetworkSearchProperties searchProperties;
    @Resource
    private EsPanTaResourceRepository pantaResourceRepository;
    @Resource
    private ModelProperties modelProperties;
    @Resource
    private TextModelExternalService textModelExternalService;
    @Resource
    private QpsLimitService qpslimitService;
    @Resource
    private ModelPromptProperties modelPromptProperties;
    @Resource
    private AllNetworkSearchProperties allNetworkSearchProperties;
    @Resource
    private CmicTextService cmicTextService;
    @Resource
    private AiInternetSearchExternalService aiInternetSearchExternalService;
    @Resource
    private AiInternetSearchConvertor convertor;
    @Resource
    private BlackResourceHandleService blackResourceHandleService;

    /**
     * 随机器
     */
    private final SecureRandom random = new SecureRandom();

    /**
     * AI全网搜SSE名称
     */
    private static final String SSE_NAME_AI_INTERNET_SEARCH = "AI-chat-sse-ai-search";

    /**
     * 用户输入原文占位符
     */
    private static final String REPLACE_KEY_OF_CONTENT = "{CONTENT}";
    /**
     * 搜索SNIPPET结果提示词占位符
     */
    private static final String REPLACE_KEY_OF_SEARCH_RESULTS = "{SEARCH_RESULTS}";

    /**
     * 提示词配置对应配置key——system
     */
    private static final String AI_INTERNET_SEARCH_SYSTEM_PROMPT = "ai_internet_search_system_prompt";

    /**
     * 小站资源snippet分隔符
     */
    private static final String CHINESE_COMMA = "，";
    private static final String PANTA_TITLE = "标题：";
    private static final String PANTA_RESOURCE_TAG = "资源标签类型：";
    private static final String PANTA_DIRECTOR = "导演：";
    private static final String PANTA_CAST_NAME = "主演：";
    private static final String PANTA_OVERVIEW = "简介：";

    /**
     * 搜索类型key
     */
    private static final String PANTA_RESULT_KEY = "panta";
    private static final String ALI_RESULT_KEY = "ali";

    @Override
    public int order() {
        return ExecuteSort.AI_INTERNET_SEARCH.getSort();
    }

    /**
     * 超时线程池
     */
    @Resource(name = "multiSearchSchedulePool")
    private ScheduledExecutorService multiSearchScheduler;

    /**
     * 调用通用搜索的线程池
     */
    @Resource(name = "multiSearchThreadPool")
    private ExecutorService multiSearchThreadPool;

    @Override
    public boolean execute(ChatAddInnerDTO innerDTO) {
        return Boolean.TRUE.equals(innerDTO.getReqParams().getEnableAllNetworkSearch())
                && Boolean.TRUE.equals(searchProperties.isExecute());
    }

    @Override
    public boolean run(ChatAddInnerDTO innerDTO) {

        log.info("进入AI全网搜节点处理流程");
        // 兼容搜索对话进入全网搜流程sessionid不为空时，清除sessionid
        if(!Objects.equals(innerDTO.getSaveMessage(), null) && Boolean.TRUE.equals(innerDTO.getSaveMessage())) {
            log.info("AI全网搜节点处理流程，兼容搜索对话进入全网搜流程sessionid不为空且状态为新增时，清除sessionid");
            innerDTO.setSessionId(null);
            innerDTO.getReqParams().setSessionId(null);
        }
        // 1. get search entity by algorithm api
        Map<PantaLabelEnum, List<String>> extractResult =
                searchEntityExtract(innerDTO.getReqParams().getContent().getDialogue());
        // 1.1 handle none
        if (CollUtil.isEmpty(extractResult)) {
            return aiInternetSearchHandlerNone(innerDTO, false);
        }

        // 1.2 black filter
        // black filter
        Iterator<Map.Entry<PantaLabelEnum, List<String>>> iterator = extractResult.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<PantaLabelEnum, List<String>> entry = iterator.next();
            List<String> filtered = entry.getValue().stream()
                    .filter(s -> !blackResourceHandleService.isSensitive(s, BlackResourceParityEnum.PRECISE.getType()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(filtered)) {
                iterator.remove();
            } else {
                entry.setValue(filtered);
            }
        }

        if (CollUtil.isEmpty(extractResult)) {
            return aiInternetSearchHandlerNone(innerDTO, true);
        }

        // 2. search data assemble
        List<AiMultiSearchDTO> paramList = assemblerSearchData(extractResult);

        // 3. multi-invoke search
        List<MultiSearchVO> multiSearchResult = multiSearch(paramList);
        // 3.1 handle none
        if (CollUtil.isEmpty(multiSearchResult)) {
            return aiInternetSearchHandlerNone(innerDTO, false);
        }

        // 4. filter
        List<GenericSearchVO> dataList = dataFilter(multiSearchResult);
        // 4.1 handle none
        if (CollUtil.isEmpty(dataList)) {
            return aiInternetSearchHandlerNone(innerDTO, false);
        }

        // 4.2 black filter
        List<GenericSearchVO> filteredList = dataList.stream()
                .filter(s -> !blackResourceHandleService.isSensitive(s.getTitle(), BlackResourceParityEnum.PRECISE.getType()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(filteredList)) {
            return aiInternetSearchHandlerNone(innerDTO, true);
        }

        // 5. get query text
        AiInternetSearchQueryDTO queryDTO = getQueryInfo(filteredList);

        // 6. benefit consume
        memberCenterService.consumeBenefit(innerDTO.getReqParams(), RequestContextHolder.getPhoneNumber(), innerDTO.getDialogueId());

        // 7. save dialogue
        save(innerDTO);

        // 8. launch llm
        SseDialogueEventListener event = new SseDialogueEventListener(innerDTO, null);
        event.setSseName(SSE_NAME_AI_INTERNET_SEARCH);

        // 智能调度
        schedule(innerDTO, event, queryDTO.getNetworkSearchInfoList(),
                AiInternetSearchScheduleDTO.builder()
                        .content(innerDTO.getReqParams().getContent().getDialogue())
                        .sourceChannel(innerDTO.getContent().getSourceChannel())
                        .clientType(RequestContextHolder.getClientType())
                        .searchResults(queryDTO.getCloudStorageLinkJson()).build());


        return false;
    }

    /**
     * 根据搜索结果，获取对应的query文本
     * @param dataList 搜搜结果数据集
     * @return query text
     */
    private AiInternetSearchQueryDTO getQueryInfo(List<GenericSearchVO> dataList) {

        long startTime = System.currentTimeMillis();

        // networkSearchInfoList handle
        List<AiTextResultRespParameters.NetworkSearchInfo> networkSearchInfoList = CollUtil.newArrayList();
        AtomicInteger index = new AtomicInteger(1);
        dataList.forEach(vo -> {
            networkSearchInfoList.add(AiTextResultRespParameters.NetworkSearchInfo.builder()
                .index(index.get()).icon(null).siteName(vo.getSnippet())
                .title(vo.getTitle()).url(vo.getLink()).build());
            index.getAndIncrement();
        });

        String queryText = JsonUtil.toJson(dataList);
        log.info("【AI全网搜】获取搜索输送大模型query文本结束，耗时：{}ms，结果：{}", System.currentTimeMillis() - startTime, queryText);

        return AiInternetSearchQueryDTO.builder()
                .cloudStorageLinkJson(queryText)
                .networkSearchInfoList(networkSearchInfoList)
                .build();
    }

    /**
     * 针对搜索数据进行过滤
     *
     * @param multiSearchResult 搜索数据集合
     * @return 过滤后的数据集合
     */
    private List<GenericSearchVO> dataFilter(List<MultiSearchVO> multiSearchResult) {

        long startTime = System.currentTimeMillis();
        log.info("【AI全网搜】进入搜索记录过滤处理环节，过滤前结果：{}", multiSearchResult);
        Map<String, List<GenericSearchVO>> map = new HashMap<>(Const.NUM_16);

        // 1. total handle
        multiSearchResult.forEach(multiSearchVO -> {
            List<GenericSearchVO> list = multiSearchVO.getVoList().stream()
                    // link-prefix filter
                    .filter(vo -> AllNetworkSearchProperties.whiteLinkPrefixFilter(vo.getLink(), allNetworkSearchProperties.getCloudStorageLinkPrefix()))
                    // duplicate filter
                    .filter(distinctByKeys(GenericSearchVO::getLink, null))
                    .collect(Collectors.toList());
            if (PANTA_RESULT_KEY.equals(multiSearchVO.getType())) {
                map.put(PANTA_RESULT_KEY, list);
            }
            if (ALI_RESULT_KEY.equals(multiSearchVO.getType())) {
                if (CollUtil.isNotEmpty(map.get(ALI_RESULT_KEY))) {
                    List<GenericSearchVO> tempList = map.get(ALI_RESULT_KEY);
                    tempList.addAll(list);
                    map.put(ALI_RESULT_KEY, tempList);
                } else {
                    map.put(ALI_RESULT_KEY, list);
                }
            }
        });

        // define argument
        int totalSize = searchProperties.getTotalSize();
        int pantaSize = 0;
        int aliSize;
        List<GenericSearchVO> resultList = new ArrayList<>();

        // sub list
        // panta
        List<GenericSearchVO> pantaList = map.get(PANTA_RESULT_KEY);
        if (CollUtil.isNotEmpty(pantaList)) {
            pantaSize = pantaList.size();
            resultList.addAll(pantaList.stream().limit(searchProperties.getPantaSize()).collect(Collectors.toList()));
        }
        // ali
        List<GenericSearchVO> aliList = map.get(ALI_RESULT_KEY);
        if (CollUtil.isNotEmpty(aliList) && pantaSize < totalSize) {
            aliSize = totalSize - pantaSize;
            resultList.addAll(aliList.stream()
                    .sorted(Comparator.comparing(GenericSearchVO::getPublishTime).reversed())
                    .limit(aliSize).collect(Collectors.toList()));
        }
        LogCommonUtils.printlnListLog("【AI全网搜-数据过滤结果】", resultList, resultList.size());
        // return
        log.info("【AI全网搜】搜索记录过滤处理环节耗时：{}ms，过滤后结果：{}", System.currentTimeMillis() - startTime, resultList);
        return resultList;
    }

    /**
     * 辅助方法：返回一个Predicate，用于基于指定的keyExtractor对象进行去重判断。
     *
     * @param keyExtractor1 排重字段1
     * @param keyExtractor2 排重字段2
     * @return 布尔值函数执行结果
     * @param <T> 泛型参数
     */
    private <T> Predicate<T> distinctByKeys(Function<? super T, ?> keyExtractor1, Function<? super T, ?> keyExtractor2) {
        Set<String> seen = ConcurrentHashMap.newKeySet();
        return t -> {
            String compositeKey = keyExtractor1.apply(t) + StrPool.DASHED + (ObjectUtil.isNull(keyExtractor2) ? "" : keyExtractor2.apply(t));
            return seen.add(compositeKey);
        };
    }

    /**
     * 多线程调用厂商搜索接口
     *
     * @param dtoList 搜索参数集合
     * @return 搜索结果集合
     */
    private List<MultiSearchVO> multiSearch(List<AiMultiSearchDTO> dtoList) {

        long startTime = System.currentTimeMillis();
        final String mainThread = Thread.currentThread().getName();
        log.info("【AI全网搜】开始多线程调用搜索接口，预执行次数：{}", dtoList.size());
        List<CompletableFuture<List<MultiSearchVO>>> futureList = dtoList.stream()
                .map(dto -> {
                    CompletableFuture<List<MultiSearchVO>> taskFuture = CompletableFuture.supplyAsync(() -> {
                                if (dto.isPantaFlag()) {
                                    log.info("【AI全网搜】异步搜小站任务执行中，当前线程：{}，主线程：{}，参数：{}",
                                            Thread.currentThread().getName(), mainThread, dto.getMetadataMap());
                                    return pantaSearch(dto.getMetadataMap());
                                }
                                log.info("【AI全网搜】异步阿里云搜索任务执行中，当前线程：{}，主线程：{}，参数：{}",
                                        Thread.currentThread().getName(), mainThread, dto.getGenericSearchDTO());
                                List<GenericSearchVO> voList = aiInternetSearchExternalService.genericSearch(dto.getGenericSearchDTO());
                                return CollUtil.newArrayList(MultiSearchVO.builder().type(ALI_RESULT_KEY).voList(voList).build());
                            }, multiSearchThreadPool)
                            // exception
                            .handle((result, ex) -> {
                                if (ex != null) {
                                    log.error("【AI全网搜】调用 multiSearch 异常，参数dto：{}", dto, ex);
                                    return Collections.emptyList();
                                }
                                return result;
                            });
                    // timeout
                    return withTimeout(taskFuture, searchProperties.getSearchWaitTime(), Collections.emptyList());
                })
                .collect(Collectors.toList());

        List<MultiSearchVO> resultList = futureList.stream()
                .map(CompletableFuture::join)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        log.info("【AI全网搜】多线程调用搜索接口完成，耗时：{} ms", System.currentTimeMillis() - startTime);
        return resultList;
    }

    /**
     * 超时控制方法
     *
     * @param future 执行任务
     * @param timeout 超时时间
     * @param defaultValue 默认值
     * @return 返回结果
     * @param <T> 任意类型
     */
    private <T> CompletableFuture<T> withTimeout(CompletableFuture<T> future, long timeout, T defaultValue) {
        CompletableFuture<T> timeoutFuture = new CompletableFuture<>();
        multiSearchScheduler.schedule(() -> {
            if (!future.isDone()) {
                timeoutFuture.complete(defaultValue);
            }
        }, timeout, TimeUnit.SECONDS);
        return future.applyToEither(timeoutFuture, result -> {
            if (result == defaultValue) {
                log.warn("【AI全网搜】多线程查询超时，返回默认值，当前线程：{}，数据：{}", Thread.currentThread().getName(), defaultValue);
            }
            return result;
        });
    }

    /**
     * 搜索实体抽取
     *
     * @param dialogue 对话内容
     *
     * @return 搜索实体抽取结果
     */
    private Map<PantaLabelEnum, List<String>> searchEntityExtract(String dialogue) {
        AllNetworkSearchProperties.SearchEntityExtractDTO searchEntityExtract = searchProperties.getSearchEntityExtract();
        TextNerExtractDTO dto = new TextNerExtractDTO();
        dto.setText(dialogue);
        dto.setMaxTextLength(searchEntityExtract.getMaxLength());
        dto.setEnableAllEntity(searchEntityExtract.isReturnAll());
        dto.setEntityTypeList(searchEntityExtract.getEntityTypeList());
        dto.setAllEntityExample(searchEntityExtract.getAllEntityExample());
        dto.setTypeEntityExample(searchEntityExtract.getTypeEntityExample());
        dto.setRequestId(MDC.get(LogConstants.TRACE_ID));
        return cmicTextService.searchEntityExtract("【AI全网搜】", dto);
    }

    /**
     * 组装搜索参数
     *
     * @param extractResult 搜索实体抽取结果
     * @return 合并后的搜索参数
     */
    private List<AiMultiSearchDTO> assemblerSearchData(Map<PantaLabelEnum, List<String>> extractResult) {

        long startTime = System.currentTimeMillis();
        List<AiMultiSearchDTO> paramList = new ArrayList<>();
        // 1. panta search handle
        if (searchProperties.isSearchPanta()) {
            paramList.add(AiMultiSearchDTO.builder().pantaFlag(true).metadataMap(extractResult).build());
        }

        // 2. third-party search handle (aliyun)
        // 2.1 get entity set ==> entity param prefix
        Set<String> entitySet = new HashSet<>();
        extractResult.forEach((key, value) -> entitySet.addAll(value));
        String prefix = String.join(" ", entitySet);
        Set<String> thirdPartyKind = searchProperties.getThirdPartyKind();
        thirdPartyKind.forEach(suffix -> {
            GenericSearchDTO searchDTO = convertor.toGenericSearchDTO(searchProperties.getAliSearchParams());
            StringBuilder temp = new StringBuilder(prefix);
            searchDTO.setQuery(String.valueOf(temp.append(suffix)));
            paramList.add(AiMultiSearchDTO.builder().genericSearchDTO(searchDTO).build());
        });
        log.info("【AI全网搜】多线程搜索实体组装耗时：{}ms，结果：{}", System.currentTimeMillis() - startTime, paramList);
        return paramList;
    }

    /**
     * 小站搜索方法
     *
     * @param extractResult 搜索实体
     * @return 小站搜索结果
     */
    private List<MultiSearchVO> pantaSearch(Map<PantaLabelEnum, List<String>> extractResult) {

        long startTime = System.currentTimeMillis();
        List<EsPanTaResourceEntity> pantaResultList;
        // search param from nacos
        AllNetworkSearchProperties.SearchPantaParam searchConfig = searchProperties.getSearchPantaParam();
        // invoke es-search client
        PanTaResourceOuterSearchV2DTO searchDTO = new PanTaResourceOuterSearchV2DTO(
                extractResult,
                searchConfig.getReleaseYear(),
                searchConfig.getAdult(),
                searchConfig.getPriorityList(),
                searchConfig.getDomestic(),
                searchConfig.getBusinessResourceTypeList()
        );
        pantaResultList = pantaResourceRepository.searchPanTaResourceV2(searchDTO);
        for (int i = 0; i < pantaResultList.size(); i++) {
            log.info("【AI全网搜-小站搜索结果{}】总条数：{}，标题：{}，链接：{}", i + 1,
                    pantaResultList.size(), pantaResultList.get(i).getTitle(), pantaResultList.get(i).getShareUrl());
        }
        log.info("【AI全网搜】搜小站耗时：{}ms", System.currentTimeMillis() - startTime);

        if (CollUtil.isEmpty(pantaResultList)) {
            log.info("【AI全网搜】搜小站结果为空，返回空集合");
            return Collections.emptyList();
        }
        // filter
        pantaResultList = pantaResultList.stream()
                .filter(distinctByKeys(EsPanTaResourceEntity::getShareUrl, EsPanTaResourceEntity::getTitle))
                .collect(Collectors.toList());
        // handle
        List<GenericSearchVO> resultList = CollUtil.newArrayList();
        pantaResultList.forEach(s -> {
            StringBuilder start = new StringBuilder(PANTA_TITLE);
            s.setOverview(String.valueOf(
                    start.append(s.getTitle()).append(CHINESE_COMMA)
                            .append(PANTA_RESOURCE_TAG).append(s.getResourceTag()).append(CHINESE_COMMA)
                            .append(PANTA_DIRECTOR).append(s.getDirector()).append(CHINESE_COMMA)
                            .append(PANTA_CAST_NAME).append(s.getCastName()).append(CHINESE_COMMA)
                            .append(PANTA_OVERVIEW).append(s.getOverview())
            ));
            resultList.add(convertor.toGenericSearchVO(s));
        });
        log.info("【AI全网搜】搜小站针对结果处理完毕，耗时：{}ms，搜索结果集合：{}",
                System.currentTimeMillis() - startTime, resultList);
        return CollUtil.newArrayList(MultiSearchVO.builder().type(PANTA_RESULT_KEY).voList(resultList).build());
    }

    /**
     * 保存数据到数据库
     *
     * @param innerDTO 对话参数
     */
    private void save(ChatAddInnerDTO innerDTO) {

        // hbase
        String resourceContent = resourceContent(innerDTO.getReqParams());
        saveTextResult(innerDTO, "", resourceContent);
        // tidb
        add(innerDTO, ChatStatusEnum.CHAT_IN);
        log.info("【AI全网搜】保存对话数据成功");
    }

    /**
     * 智能调度
     *
     * @param innerDTO     用户输入对象
     * @param event        流式监听参数
     * @param networkSearchInfoList 联网搜索引用信息（伪联网）
     * @param dialogueTemplateDTO 对话模板
     */
    private void schedule(ChatAddInnerDTO innerDTO, SseDialogueEventListener event,
                          List<AiTextResultRespParameters.NetworkSearchInfo> networkSearchInfoList,
                          AiInternetSearchScheduleDTO dialogueTemplateDTO) {

        List<String> executeSort = new ArrayList<>();
        executeSort.add(searchProperties.getModelCode());
        AbstractResultCode error = null;
        boolean success = false;
        for (String code : executeSort) {
            error = null;
            // qps限制
            if (!qpslimitService.modelQpsLimit(code)) {
                log.info("请求过多，qps限流，model:{}", code);
                error = ResultCodeEnum.ERROR_LIMITATION;
            } else {
                try {
                    TextModelTextReqDTO reqDTO = event.getTextDto().toTextReqDTO(modelProperties.getMaxLength(innerDTO.getContent().getAssistantEnum(), innerDTO.getContent().getBusinessType(), code));
                    reqDTO.setEnableNetworkSearch(true);
                    event.setModelCode(code);
                    event.setOutputNetworkSearchInfo(true);
                    event.setNetworkSearchInfoList(networkSearchInfoList);
                    success = modelStreamHandle(reqDTO, code, event, dialogueTemplateDTO);
                    // 更新模型编码
                    algorithmChatContentRepository.updateModelCode(event.getDialogId(), code);
                    innerDTO.getRespParams().setModelType(code);
                    break;
                } catch (YunAiBusinessException e) {
                    error = e.getExceptionEnum();
                    log.error("调用文本大模型异常:{}，对话id:{}", e.getMessage(), event.getDialogId(), e);
                } catch (Exception e) {
                    log.error("调用文本大模型异常:{}，对话id:{}", e.getMessage(), event.getDialogId(), e);
                }
            }
        }

        if (!success) {
            log.warn("对话失败，智能调度大文本模型处理失败，对话id：{}", event.getDialogId());
            if (error != null) {
                event.dialogueFail(error);
            } else {
                event.dialogueFail(ResultCodeEnum.ERROR_SERVER_INTERNAL);
            }
        }
    }

    /**
     * 调大文本模型流式处理
     *
     * @param reqDTO 请求参数
     * @param code   模型编码
     * @param event  监听事件
     */
    private boolean modelStreamHandle(TextModelTextReqDTO reqDTO, String code, TextModelStreamEventListener event, AiInternetSearchScheduleDTO dialogueTemplateDTO) {

        // 提示词
        String prompt = modelPromptProperties.getPrompt(AI_INTERNET_SEARCH_SYSTEM_PROMPT, code)
                .replace(REPLACE_KEY_OF_CONTENT, dialogueTemplateDTO.getContent())
                .replace(REPLACE_KEY_OF_SEARCH_RESULTS, dialogueTemplateDTO.getSearchResults());

        // print
        LogCommonUtils.printlnListLog("【AI全网搜-完整提示词】第{}个分块：\n{}", prompt);

        // 新的对话信息
        TextModelMessageDTO msgDTO = new TextModelMessageDTO();
        msgDTO.setRole(TextModelRoleEnum.USER.getName());
        msgDTO.setContent(prompt);

        return textModelExternalService.streamDialogue(code, genTextModelTextReq(reqDTO, msgDTO), event);
    }

    /**
     * 获取新的对话信息
     *
     * @param reqDTO 模型请求
     * @param msgDTO 消息请求
     * @return 模型请求实体
     */
    private TextModelTextReqDTO genTextModelTextReq(TextModelTextReqDTO reqDTO, TextModelMessageDTO msgDTO) {

        TextModelTextReqDTO req = new TextModelTextReqDTO();
        req.setTaskId(reqDTO.getTaskId());
        req.setUserId(reqDTO.getUserId());
        req.setSessionId(reqDTO.getSessionId());
        req.setMessageDtoList(Collections.singletonList(msgDTO));
        req.setEnableNetworkSearch(reqDTO.getEnableNetworkSearch());
        req.setEnableModelSystemRolePrompt(false);
        req.setTextModelConfig(allNetworkSearchProperties.getTextModelConfig());
        return req;
    }
}