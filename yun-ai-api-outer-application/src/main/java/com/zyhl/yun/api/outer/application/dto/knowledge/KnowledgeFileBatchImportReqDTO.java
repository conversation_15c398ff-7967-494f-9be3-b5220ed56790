package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.vo.ImportHtmlInfoVO;
import com.zyhl.yun.api.outer.domain.vo.ImportMailInfoVO;
import com.zyhl.yun.api.outer.domain.vo.ImportNoteInfoVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;

/**
 * 个人知识库批量导入请求参数
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KnowledgeFileBatchImportReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 资源类型
     * 0--个人云文件
     * 1--邮件
     * 2--笔记
     * 3--在线链接
     * 4--笔记同步，如果重试，则需要填 parentFileId，把笔记同步文件夹的id填进来 （废弃，没使用到）
     * 5--笔记同步预处理
     */
    private Integer resourceType;

    /**
     * 知识库Id
     */
    private String baseId;

    /**
     * 个人云文件列表,支持文件和文件夹格式
     */
    private List<File> fileList;

    /**
     * 邮件列表
     */
    private List<ImportMailInfoVO> mailList;

    /**
     * 笔记列表
     */
    private List<ImportNoteInfoVO> noteList;

    /**
     * 网页链接信息
     */
    private @Valid ImportHtmlInfoVO htmlInfo;

    /**
     * 父目录id
     */
    private String parentFileId;
} 