package com.zyhl.yun.api.outer.application.service;

import com.zyhl.yun.api.outer.application.dto.ObjectKeysDTO;
import com.zyhl.yun.api.outer.application.dto.ReportInfoDTO;
import com.zyhl.yun.api.outer.vo.UploadPicVO;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
public interface ReportService {

    /**
     * 上传图片
     * @param dto 入参
     * @return 图片上传结果
     */
    UploadPicVO uplaodPicture(MultipartFile dto);

    /**
     * 删除报告
     * @param dto 入参
     * @return 数量
     */
    Integer cancel(ObjectKeysDTO dto);

    /**
     * 提交报告
     * @param dto 入参
     * @return 数量
     */
    Integer submit(ReportInfoDTO dto);
}
