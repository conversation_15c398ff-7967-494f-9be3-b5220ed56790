package com.zyhl.yun.api.outer.application.vo;

import com.zyhl.yun.api.outer.vo.TaskRespParamVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * className:SmartFakeCheckVO
 * description: 图片智能鉴伪结果VO
 *
 * <AUTHOR>
 * @date 2024/12/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SmartFakeCheckVO implements Serializable {

    /**
     * 鉴伪结果
     * 12 代表伪造，0 代表非伪造
     */
    private Integer result;

    /**
     * 伪造分值[0,1]
     * 分值越大伪造程度越高
     */
    private Float percent;

    /**
     * 初版图片智能鉴伪,仅有一个结果,用此初始化构造
     * @param list 鉴别结果集合
     * @return 鉴别映射后结果
     */
    public SmartFakeCheckVO initialVersionCreate(List<TaskRespParamVO> list) {

        TaskRespParamVO vo = list.get(0);
        this.result = Integer.valueOf(vo.getOutContent());
        this.percent = vo.getScore();
        return this;
    }
}
