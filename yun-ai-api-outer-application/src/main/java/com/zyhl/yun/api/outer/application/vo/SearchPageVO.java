package com.zyhl.yun.api.outer.application.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchPageVO {

    /**
     * 文件信息
     */
    private List<FileVO> fileInfo;

    /**
     * 下一页起始资源标识符，最后一页该值为空
     */
    private String nextPageCursor;


}
