package com.zyhl.yun.api.outer.application.service.impl;

import com.zyhl.hcy.plugin.redis.HcyRedisTemplate;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.ObjectKeysDTO;
import com.zyhl.yun.api.outer.application.dto.ReportInfoDTO;
import com.zyhl.yun.api.outer.application.service.ReportService;
import com.zyhl.yun.api.outer.constants.CommonConstant;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.domain.entity.ObjectKeysEntity;
import com.zyhl.yun.api.outer.domain.entity.ReportInfoEntity;
import com.zyhl.yun.api.outer.domain.entity.UploadPicEntity;
import com.zyhl.yun.api.outer.domainservice.EosBusinessService;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.UploadPicVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.zyhl.yun.api.outer.constants.Const.*;
import static com.zyhl.yun.api.outer.enums.ResultCodeEnum.ERROR_LIMITATION;
import static com.zyhl.yun.api.outer.enums.ResultCodeEnum.FILE_CONTENT_INVALID;
import static com.zyhl.yun.api.outer.enums.ResultCodeEnum.FILE_SIZE_LARGE;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReportServiceImpl implements ReportService {

    @Value("${report.upload.allowedExtensions}")
    private String[] allowedExtensions;

    @Autowired
    EosBusinessService businessService;

    @Resource
    private HcyRedisTemplate<String, Object> hcyRedisTemplate;

    private final Long picSize = 5242880L;

    @Override
    public UploadPicVO uplaodPicture(MultipartFile dto) {
        log.info("getContentType:{},getOriginalFilename:{},getSize:{}", dto.getContentType(), dto.getOriginalFilename(), dto.getSize());

        String cacheKey = String.format(CommonConstant.AI_REPORT_LOCK, RequestContextHolder.getUserId(), dto.getOriginalFilename());
        if (Boolean.FALSE.equals(hcyRedisTemplate.opsForValue().setIfAbsent(cacheKey, NUM_STRING_1, RedisConstants.LEASE_TIME_30, TimeUnit.SECONDS))) {
            throw new YunAiBusinessException(ERROR_LIMITATION);
        }

        if (dto.getSize() > picSize){
            throw new YunAiBusinessException(FILE_SIZE_LARGE);
        }

        if (!dto.getContentType().startsWith(IMAGE_WORD)) {
            throw new YunAiBusinessException(FILE_CONTENT_INVALID);
        }
        String originalFilename = dto.getOriginalFilename();
        if (originalFilename == null) {
            throw new YunAiBusinessException(FILE_CONTENT_INVALID);
        }
        // 获取文件扩展名，并转换为小写
        String img = FilenameUtils.getExtension(originalFilename).toLowerCase();
        // 定义允许的文件扩展名白名单
//        String[] allowedExtensions = {"jpg", "jpeg", "png", "heic"};
        // 检查文件扩展名是否在白名单中
        if (!Arrays.asList(allowedExtensions).contains(img)) {
            throw new YunAiBusinessException(FILE_CONTENT_INVALID);
        }
        UploadPicEntity entity = new UploadPicEntity();
        //拼接后缀
		String objectKey = UUID.randomUUID().toString() + "." + img;
        InputStream inputStream = null;
        try {
            inputStream = dto.getInputStream();
        } catch (IOException e) {
            throw new YunAiBusinessException(FILE_CONTENT_INVALID);
        }
        entity.setObjectKey(objectKey);
        entity.setInputStream(inputStream);
        return businessService.uplaodAndGetUrl(entity);
    }

    @Override
    public Integer cancel(ObjectKeysDTO dto) {
        ObjectKeysEntity entity = new ObjectKeysEntity();
        entity.setObjectKeys(dto.getObjectKeys());
        return businessService.cancel(entity);

    }
    @Override
    public Integer submit(ReportInfoDTO dto) {
        ReportInfoEntity entity = new ReportInfoEntity();
        if (StringUtils.isBlank(dto.getUserId())){
            dto.setUserId(RequestContextHolder.getUserId());
        }
        entity.setUserId(dto.getUserId());
        entity.setBusinessType(dto.getBusinessType());
        entity.setReasons(StringUtils.join(dto.getReasons(), "|"));
        entity.setContactInfo(dto.getContact_info());
        entity.setDescribe(dto.getDescribe());
        entity.setObjectKeys(dto.getObjectKeys());
        return businessService.submit(entity);
    }

}
