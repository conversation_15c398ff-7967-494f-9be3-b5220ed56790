package com.zyhl.yun.api.outer.application.service.knowledge;

import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeShareAddReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeShareGetReqDTO;

/**
 * className:KnowledgeShareService
 *
 * <AUTHOR>
 * @date 2025/04/12
 */
public interface KnowledgeShareService {

    /**
	 * 保存加入知识库
	 * @param dto 保存加入知识库参数
	 */
	void joinKnowledge(KnowledgeShareAddReqDTO dto);

	/**
	 * 保存加入知识库
	 * @param dto 参数
	 */
	Boolean get(KnowledgeShareGetReqDTO dto);

}
