package com.zyhl.yun.api.outer.application.chatv2.service;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.pojo.LocalFileInfo;
import com.zyhl.yun.api.outer.domain.valueobject.MailInfo;

import java.util.List;

/**
 * 会话输入校验类
 *
 * <AUTHOR>
 * @data 2024/6/3 15:52
 */
public interface ChatAddCheckService {


    /**
     * 根据云盘文件id列表获取文件共享存储路径列表
     *
     * @param fileIdList     云盘文件id列表
     * @param userId         用户id
     * @param fileListFilter 文件列表是否过滤 true是 false否
     * @return 上传文件列表vo
     */
    List<LocalFileInfo> getFilesByCloudDiskDocumentLocalPath(List<String> fileIdList, String userId, boolean fileListFilter);


    /**
     * 根据邮件附件列表获取文件共享存储路径列表
     *
     * @param mailList     邮箱附件列表dto
     * @param reqDTO       会话输入dto
     * @param resourceName 资源名称
     * @return 上传文件列表vo
     */
    List<String> getFilesByEmailAttachmentLocalPath(List<MailInfo> mailList, ChatAddReqDTO reqDTO, String resourceName);

    /**
     * 根据关键字获取对话关键字提示词
     *
     * @param prompt the prompt
     * @return {@link String}
     * <AUTHOR>
     * @date 2024-6-27 17:24
     */
    String getDialoguePrompt(String prompt, String channel);

    /**
     * 追加重新生成提示词
     *
     * @param inputInfo   用户输入
     * @param finalPrompt 最终提示词文字
     * @return
     */
    String getAppendEnableRegenerate(DialogueInputInfoDTO inputInfo, String finalPrompt);

}
