package com.zyhl.yun.api.outer.application.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 继续执行大模型回答DTO
 * @Author: WeiJingKun
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContinueTextSseDTO {

    /** 继续执行大模型回答 */
    private boolean continueTextSseHandler = false;

    /** 继续执行的大模型 */
    private String modelCode;

    /** 是否强制联网搜索 */
    private Boolean enableForceNetworkSearch;

    public ContinueTextSseDTO(String modelCode) {
        this.continueTextSseHandler = true;
        this.modelCode = modelCode;
        this.enableForceNetworkSearch = true;
    }

    public ContinueTextSseDTO(String modelCode, Boolean enableForceNetworkSearch) {
        this.continueTextSseHandler = true;
        this.modelCode = modelCode;
        this.enableForceNetworkSearch = enableForceNetworkSearch;
    }


}
