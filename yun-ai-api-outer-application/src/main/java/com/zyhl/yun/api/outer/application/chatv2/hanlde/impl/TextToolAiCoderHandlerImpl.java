package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueIntentionDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.application.service.chat.DialogueIntentionService;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * AI编程
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TextToolAiCoderHandlerImpl extends AbstractChatAddV2Handler {

	/**
	 * 当前执行顺序
	 */
	private ExecuteSort thisExecuteSort = ExecuteSort.TEXT_TOOL_AI_CODER;

	/**
	 * AI编程代码助手意图
	 */
	private static final String THIS_MAIN_INTENTION = DialogueIntentionEnum.TEXT_TOOL.getCode();
	private static final String THIS_SUB_INTENTION = DialogueIntentionSubEnum.AI_PROGRAMMING_INTENTION.getCode();

	@Resource
	private DialogueIntentionService dialogueIntentionService;
	@Resource
	private TextModelTextSseHandlerImpl textModelTextSseHandlerImpl;
	@Resource
	private ChatTextToolBusinessConfig chatTextToolBusinessConfig;

	@Override
	public void afterPropertiesSet() throws Exception {
		// 支持的业务初始化
		List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        this.setBusinessTypes(thisBusinessTypes);
	}

	@Override
	public int order() {
		return thisExecuteSort.getSort();
	}

	@Override
	public boolean execute(ChatAddHandleDTO handleDTO) {

		if (StringUtils.isEmpty(handleDTO.getReqDTO().getDialogueInput().getDialogue())) {
			// 对话内容空，不执行
			return false;
		}

		DialogueIntentionDTO command = handleDTO.getReqDTO().getDialogueInput().getCommand();
		if (null != command && DialogueIntentionEnum.isTextToolIntention(command.getCommand())
				&& THIS_SUB_INTENTION.equals(command.getSubCommand())) {
			// 判断入参是ai编程助手意图（主意图+子意图联合判断）
			return true;
		}

		if (null != handleDTO.getIntentionVO()
				&& CollUtil.isNotEmpty(handleDTO.getIntentionVO().getIntentionInfoList())) {
			IntentionInfo mainIntention = DialogueIntentionVO.getMainIntention(handleDTO.getIntentionVO());
			// 判断意图识别是ai编程助手意图（主意图+子意图联合判断+参数不为空才是执行意图，并且有key=ChatTextToolBusinessConfig.ARGUMENT_AI_CODER_FILE_NAME）
			return null != mainIntention && DialogueIntentionEnum.isTextToolIntention(mainIntention.getIntention())
					&& THIS_SUB_INTENTION.equals(mainIntention.getSubIntention())
					&& CollUtil.isNotEmpty(mainIntention.getArgumentMap()) && mainIntention.getArgumentMap()
					.containsKey(ChatTextToolBusinessConfig.ARGUMENT_AI_CODER_FILE_NAME);
		}
		return false;
	}

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		log.info("进入{}", thisExecuteSort.getDesc());

		Map<String, List<String>> argumentMap = null;
		IntentionInfo mainIntention = null;

		if (null != handleDTO.getReqDTO().getDialogueInput().getCommand() && DialogueIntentionEnum
				.isTextToolIntention(handleDTO.getReqDTO().getDialogueInput().getCommand().getCommand())) {
			// 前端主动入参意图，需要重查意图结果
			DialogueIntentionVO newIntentionVO = getDialogueIntention(handleDTO);
			mainIntention = DialogueIntentionVO.getMainIntention(newIntentionVO);
		}else {
			mainIntention = DialogueIntentionVO.getMainIntention(handleDTO.getIntentionVO());
		}

		if (null == mainIntention) {
			// 意图识别后主意图还是空，需要 new 对象
			mainIntention = new IntentionInfo();
		}

		// 意图识别后，输出前端：强制重置指定AI编程代码意图
		mainIntention.setIntention(THIS_MAIN_INTENTION);
		mainIntention.setSubIntention(THIS_SUB_INTENTION);
		argumentMap = mainIntention.getArgumentMap();
		// 空map或者空ARGUMENT_AI_CODER_FILE_NAME
		if (CollUtil.isEmpty(argumentMap)
				|| !argumentMap.containsKey(ChatTextToolBusinessConfig.ARGUMENT_AI_CODER_FILE_NAME)) {
			argumentMap = new HashMap<>(Const.NUM_16);
			argumentMap.put(ChatTextToolBusinessConfig.ARGUMENT_AI_CODER_FILE_NAME, Collections.emptyList());
		}
		mainIntention.setArgumentMap(argumentMap);

		if (StringUtils.isEmpty(handleDTO.getReqDTO().getDialogueInput().getPrompt())) {
			// 空则指定提示词
			handleDTO.getReqDTO().getDialogueInput().setPrompt(chatTextToolBusinessConfig.getAiCoder().getPrompt());
		}

		// 设置输出意图及参数信息
		handleDTO.getRespVO().setOutputCommandVO(mainIntention);
		handleDTO.setIntentionVO(DialogueIntentionVO.instanceMainIntention(mainIntention));
		
		// 执行大模型流式对话
		textModelTextSseHandlerImpl.run(handleDTO);

		return false;
	}

	/**
	 * 获取意图识别结果
	 *
	 * @param handleDTO 用户输入对象
	 */
	private DialogueIntentionVO getDialogueIntention(ChatAddHandleDTO handleDTO) {
		DialogueIntentionVO intention = null;
		try {
			intention = dialogueIntentionService.getIntentionVOV2(handleDTO);
		} catch (Exception e) {
			log.error("TextToolAiCoderHandlerImpl-getDialogueIntention api异常", e);
		}
		return intention;
	}
}
