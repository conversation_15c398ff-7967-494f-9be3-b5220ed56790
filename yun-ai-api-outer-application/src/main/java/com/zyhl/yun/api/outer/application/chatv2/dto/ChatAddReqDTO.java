package com.zyhl.yun.api.outer.application.chatv2.dto;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;

import lombok.Data;

/**
 * 助手对话请求参数
 *
 * <AUTHOR> zhumaoxian  2025/4/12 9:41
 */
@Data
public class ChatAddReqDTO extends BaseChannelDTO {

    /**
     * 会话Id，如果不输入，则创建新会话。
     */
    private String sessionId;

    /**
     * 本次对话内容
     */
    private DialogueInputInfoDTO dialogueInput;

    /**
     * 应用Id
     */
    private String applicationId;

    /**
     * 应用类型，英文简称。枚举参考ApplicationType
     *
     * @see com.zyhl.yun.api.outer.enums.ApplicationTypeEnum
     */
    private String applicationType;

    /**
     * 断点续传参数
     */
    private ContinuationInfoDTO continuationInfo;

}