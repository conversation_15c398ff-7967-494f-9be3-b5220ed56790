package com.zyhl.yun.api.outer.application.convertor.dto.dynamic;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.yun.ai.common.base.es.entity.EsPanTaResourceEntity;
import com.zyhl.hcy.yun.ai.common.model.api.client.ali.dto.GenericSearchDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.ali.vo.GenericSearchVO;
import com.zyhl.yun.api.outer.config.AllNetworkSearchProperties;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

/**
 * interfaceName: AiInternetSearchConvertor
 * description: AI全网搜数据转换器
 *
 * <AUTHOR>
 * @date 2025/3/19
 */
@Mapper(componentModel = "spring")
public interface AiInternetSearchConvertor {

	/**
	 * 转换全网搜实体
	 * 
	 * @param entity 实体对象
	 * @return 实体信息
	 */
    @Mapping(source = "title", target = "title")
    @Mapping(source = "shareUrl", target = "link")
    @Mapping(source = "overview", target = "snippet")
    @Mapping(source = "releaseUrl", target = "publishLink")
    GenericSearchVO toGenericSearchVO(EsPanTaResourceEntity entity);
    
	/**
	 * 转换全网搜实体
	 * 
	 * @param aliSearchParams 实体参数
	 * @return 实体信息
	 */
    @Mapping(source = "industry", target = "industry", qualifiedByName = "industry")
    GenericSearchDTO toGenericSearchDTO(AllNetworkSearchProperties.AliSearchParams aliSearchParams);

    /**
     * 简介转换
     * 
     * @param obj 对象
     * @return 简介
     */
    @Named("industry")
    default String industry(String obj) {
        if (ObjectUtil.isEmpty(obj)) {
            return null;
        }
        return obj;
    }
}