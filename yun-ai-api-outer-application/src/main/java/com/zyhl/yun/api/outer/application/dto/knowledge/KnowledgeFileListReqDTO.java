package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeLabelEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 知识库文件列表请求参数
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class KnowledgeFileListReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 标签id，默认全部
     *
     * @see KnowledgeLabelEnum
     */
    private String labelId = String.valueOf(KnowledgeLabelEnum.ALL.getId());

    /**
     * 分页信息
     */
    private PageInfoDTO pageInfo;

    /**
     * -1--处理失败
     * 0--处理中（同时返回-1状态）
     * 1--处理成功
     */
    private Integer status;

    /**
     * 1--按照文件创建时间倒序
     * 2--按照文件更新时间倒序
     */
    private Integer orderBy;

}
