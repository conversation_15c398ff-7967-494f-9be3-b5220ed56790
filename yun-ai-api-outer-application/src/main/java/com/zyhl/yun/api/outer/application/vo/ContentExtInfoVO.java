package com.zyhl.yun.api.outer.application.vo;

import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 对话内容扩展信息
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContentExtInfoVO {

	/**
	 * 对话ID
	 */
	private String dialogueId;

	/**
	 * 会话ID
	 */
	private String sessionId;

	/**
	 * 引导文案对象
	 */
	private LeadCopyVO leadCopy;

	/**
	 * 对话结果推荐对象，当对话信息不为空时返回
	 */
	private DialogueRecommendVO recommend;

	public ContentExtInfoVO(LeadCopyVO leadCopy, DialogueRecommendVO recommend) {
		this.leadCopy = leadCopy;
		this.recommend = recommend;
	}

}
