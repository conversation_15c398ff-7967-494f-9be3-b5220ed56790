package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.annotation.MethodExecutionTimeLog;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueAttachmentDTO;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatContentService;
import com.zyhl.yun.api.outer.application.config.ApplicationAgentLingxiConfig;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.VoConverter;
import com.zyhl.yun.api.outer.application.dto.*;
import com.zyhl.yun.api.outer.application.service.chat.ChatApplicationTypeService;
import com.zyhl.yun.api.outer.application.service.mq.AiAssistantMqService;
import com.zyhl.yun.api.outer.application.vo.ChatStatusVO;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.entity.ChatCommentEntity;
import com.zyhl.yun.api.outer.domain.entity.TaskAiAbilityEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.chat.ShareDialogueResultVO;
import com.zyhl.yun.api.outer.domainservice.IImageCommonService;
import com.zyhl.yun.api.outer.domainservice.ResourceInfoDomainService;
import com.zyhl.yun.api.outer.enums.*;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.OutAuditStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.TalkTypeEnum;
import com.zyhl.yun.api.outer.enums.task.FileExpiredStatusEnum;
import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;
import com.zyhl.yun.api.outer.external.service.EOSExternalService;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatCommentRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.repository.TaskAiAbilityRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;
import com.zyhl.yun.api.outer.vo.TaskRespParamVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 对话内容操作类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ChatContentServiceImpl implements ChatContentService {

    @Resource
    private AlgorithmChatContentRepository algorithmChatContentRepository;
    @Resource
    private SourceChannelsProperties channelsProperties;
    @Resource
    private AiAssistantMqService aiAssistantMqService;
    @Resource
    private AlgorithmChatCommentRepository algorithmChatCommentRepository;
    @Resource
    private VoConverter voConverter;
    @Resource
    private AiTextResultRepository aiTextResultRepository;
    @Resource
    private ChatApplicationTypeService chatApplicationTypeService;
    @Resource
    private ResourceInfoDomainService resourceInfoDomainService;
    @Resource
    private TaskAiAbilityRepository taskAiAbilityRepository;
    @Resource
    private EOSExternalService eosExternalService;
    @Resource
    private IImageCommonService imageCommonService;
	@Resource
	private ApplicationAgentLingxiConfig applicationAgentLingxiConfig;


    @Override
    public AlgorithmChatContentEntity add(ChatAddHandleDTO handleDTO, Integer chatStatus, Long taskId, String modelCode) {

        AlgorithmChatContentEntity contentEntity = createEntity(handleDTO, chatStatus, taskId, modelCode);

        log.info("保存对话内容，对话id：{}，对话状态：{}", handleDTO.getDialogueId(), contentEntity.getChatStatus());
        algorithmChatContentRepository.saveChatContent(contentEntity);
        log.info("保存对话内容成功，对话id：{}", handleDTO.getDialogueId());

        return contentEntity;
    }

    private AlgorithmChatContentEntity createEntity(ChatAddHandleDTO handleDTO, Integer chatStatus, Long taskId, String modelCode) {
        ChatAddReqDTO dto = handleDTO.getReqDTO();

        AlgorithmChatContentEntity contentEntity = new AlgorithmChatContentEntity();
        contentEntity.setId(handleDTO.getDialogueId());
        contentEntity.setSessionId(handleDTO.getSessionId());
        contentEntity.setUserId(dto.getUserId());
        contentEntity.setBelongsPlatform(RequestContextHolder.getBelongsPlatform());
        contentEntity.setTaskId(taskId);
        if (StringUtils.isNotBlank(handleDTO.getOutResourceId())) {
            contentEntity.setOutResourceId(handleDTO.getOutResourceId());
        }
        contentEntity.setModelType(CharSequenceUtil.nullToEmpty(modelCode));

        // 输入信息
        int resourceType = ResourceTypeEnum.TEXT.getType();
        String resourceId = null;
        DialogueAttachmentDTO attachment = dto.getDialogueInput().getAttachment();
        if (ObjectUtil.isNotEmpty(attachment) && ObjectUtil.isNotEmpty(attachment.getAttachmentTypeList())) {
            resourceType = attachment.getAttachmentTypeList().get(0);
            resourceId = JsonUtil.toJson(attachment.resourceList());
        }
        
        // 设置对话类型（默认TalkTypeEnum.DIALOG）
        if(null == dto.getDialogueInput().getDialogueType()) {
        	contentEntity.setTalkType(TalkTypeEnum.DIALOG.getType());
        }else {
        	contentEntity.setTalkType(dto.getDialogueInput().getDialogueType());
        }
        
        contentEntity.setToolsCommand(handleDTO.getIntentionCode());
        if(StringUtils.isNoneEmpty(handleDTO.getSubIntentionCode())) {
        	// 子意图不为空，才设置，否则按数据库默认0入库
        	contentEntity.setSubToolsCommand(handleDTO.getSubIntentionCode());
        }
        contentEntity.setPrompt(dto.getDialogueInput().getPrompt());
        contentEntity.setResourceType(resourceType);
        contentEntity.setInContent(dto.getDialogueInput().getDialogue());
        contentEntity.setInResourceId(resourceId);
        contentEntity.setInAuditStatus(OutAuditStatusEnum.SUCCESS.getCode());
        contentEntity.setInAuditTime(DateUtil.date());
        contentEntity.setExtInfo(dto.getDialogueInput().getExtInfo());

        contentEntity.setCommandType(dto.getDialogueInput().getCommandType());
        contentEntity.setSceneTag(dto.getDialogueInput().getSceneTag());

        contentEntity.setChatStatus(chatStatus);
        contentEntity.setCreateTime(DateUtil.date());
        contentEntity.setUpdateTime(DateUtil.date());

        String sourceChannel = RequestContextHolder.getSourceChannel();
        if(StringUtils.isEmpty(sourceChannel) && null != handleDTO.getReqDTO()) {
        	// RequestContextHolder获取渠道为空，重新设置
        	sourceChannel = handleDTO.getReqDTO().getSourceChannel();
        }
        // 渠道、业务类型、应用类型、应用id
        contentEntity.setSourceChannel(sourceChannel);
        contentEntity.setBusinessType(channelsProperties.getType(sourceChannel));
        contentEntity.setApplicationId(CharSequenceUtil.emptyToDefault(dto.getApplicationId(), "0"));
        contentEntity.setApplicationType(ApplicationTypeEnum.getByCodeDefaultChat(dto.getApplicationType()).getCode());

        // 推荐信息
        contentEntity.setRecommendInfo(JsonUtil.toJson(handleDTO.getRespVO().getRecommend()));

        // 推荐信息中部
        contentEntity.setMiddleRecommendInfo(JsonUtil.toJson(handleDTO.getRespVO().getMiddleRecommend()));

        // 输出类型
        if (DialogueIntentionEnum.SMART_FAKE_CHECK.getCode().equals(handleDTO.getIntentionCode())) {
            contentEntity.setOutContentType(OutContentTypeEnum.JSON_OBJECT.getType());
        }

        return contentEntity;
    }

    @Override
    public AlgorithmChatContentEntity addSuccess(ChatAddHandleDTO handleDTO, String modelCode, Integer outContentType) {
    	return addSuccessOrFail(ChatStatusEnum.CHAT_SUCCESS.getCode(), handleDTO, modelCode, outContentType);
    }
    
    @Override
    public AlgorithmChatContentEntity addFail(ChatAddHandleDTO handleDTO, String modelCode, Integer outContentType) {
    	return addSuccessOrFail(ChatStatusEnum.CHAT_FAIL.getCode(), handleDTO, modelCode, outContentType);
    }

    private AlgorithmChatContentEntity addSuccessOrFail(Integer chatStatus, ChatAddHandleDTO handleDTO, String modelCode, Integer outContentType) {

        // 智能鉴伪需要outContentResource=3特殊处理
        if (DialogueIntentionEnum.SMART_FAKE_CHECK.getCode().equals(handleDTO.getIntentionCode())) {
            outContentType = OutContentTypeEnum.JSON_OBJECT.getType();
        }
        AlgorithmChatContentEntity contentEntity = createEntity(handleDTO, chatStatus, null, modelCode);

        // 结果
        contentEntity.setOutAuditStatus(OutAuditStatusEnum.SUCCESS.getCode());
        contentEntity.setOutAuditTime(DateUtil.date());
        contentEntity.setOutContentType(outContentType);
        if (handleDTO.getInterventionVO() != null) {
            contentEntity.setOutResourceId(handleDTO.getInterventionVO().getId());
        }

        boolean success = false;
        try {
            success = algorithmChatContentRepository.saveChatContent(contentEntity);
        } finally {
            log.info("保存对话内容 success:{}, 对话id:{}, 对话状态:{}", success, handleDTO.getDialogueId(),
                    contentEntity.getChatStatus());
        }

        //发送AI助手对话完成mq
        aiAssistantMqService.sendDialogueCompletedMq(contentEntity);

        return contentEntity;
    }

    @Override
    public AlgorithmChatContentEntity saveAll(ChatAddHandleDTO handleDTO) {
        // 获取tidb需要保存的数据对象
        AlgorithmChatTidbSaveDTO tidbSaveDTO = handleDTO.getAlgorithmChatTidbSaveDTO();

        // 构建对话内容数据
        AlgorithmChatContentEntity contentEntity = createEntity(handleDTO, tidbSaveDTO.getChatStatus(), null, "");

        // 结果
        contentEntity.setOutAuditStatus(tidbSaveDTO.getOutAuditStatus());
        contentEntity.setOutAuditTime(DateUtil.date());
        contentEntity.setOutContent(tidbSaveDTO.getMsg());

        boolean success = false;
        try {
            success = algorithmChatContentRepository.saveChatContent(contentEntity);
        } finally {
            log.info("保存对话内容 success:{}, 对话id:{}, 对话状态:{}", success, handleDTO.getDialogueId(),
                    contentEntity.getChatStatus());
        }

        //发送AI助手对话完成mq
        aiAssistantMqService.sendDialogueCompletedMq(contentEntity);

        return contentEntity;
    }

    @Override
    @MethodExecutionTimeLog("查询对话状态-serviceImpl")
    public ChatStatusVO getChatStatus(AiAssistantStatusReqDTO dto) {
        AlgorithmChatContentEntity entity = algorithmChatContentRepository.getById(Long.valueOf(dto.getDialogueId()));

        ChatStatusVO vo = new ChatStatusVO();
        if (entity == null) {
            log.info("对话不存在，对话id：{}", dto.getDialogueId());
            return vo.notExist();
        } else if (!dto.getUserId().equals(entity.getUserId())) {
            log.info("对话id和用户id不匹配，对话id：{}，用户id：{}", dto.getDialogueId(), dto.getUserId());
            return vo.notExist();
        }

        vo.setChatStatus(entity.getChatStatus());

        // 获取评论
        ChatCommentEntity comment = algorithmChatCommentRepository.getByDialogueId(entity.getUserId(), entity.getId());
        if (comment != null) {
            vo.setLikeStatus(comment.getLikeComment());
        }

        return vo;
    }

    @Override
    public List<ShareDialogueResultVO> shareBatchGet(ShareBatchGetDTO dto) {
        // 查询数据
        Long sessionId = ObjectUtil.isEmpty(dto.getSessionId()) ? null : Long.valueOf(dto.getSessionId());
        List<AlgorithmChatContentEntity> list = algorithmChatContentRepository.getShareContentList(sessionId, dto.getApplicationType(), dto.getUserId(), dto.getDialogueIdList());
        if (ObjectUtil.isEmpty(list)) {
            log.info("对话不存在，请求参数：{}", JSONUtil.toJsonStr(dto));
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_DIALOGUE_ID);
        }

        List<ShareDialogueResultVO> voList = new ArrayList<>();

        // 数据处理
        list.forEach(entity -> {
            ShareDialogueResultVO vo = voConverter.toShareDialogueResultVO(entity);
            vo.setInAuditTime(DateUtil.format(entity.getInAuditTime(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN));
            vo.setOutAuditTime(DateUtil.format(entity.getOutAuditTime(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN));
            vo.setCreateTime(DateUtil.format(entity.getCreateTime(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN));
            vo.setOutContent(entity.getOutContent());

            // 输入资源信息
            if (ResourceTypeEnum.isMail(entity.getResourceType())) {
                vo.setInResourceExtInfo(resourceInfoDomainService.getMailInfo(RequestContextHolder.getPhoneNumber(), entity.getInResourceId()));
            } else if (ResourceTypeEnum.isNote(entity.getResourceType())) {
                vo.setInResourceExtInfo(resourceInfoDomainService.getNoteInfo(RequestContextHolder.getUserId(), entity.getInResourceId()));
            } else if (ResourceTypeEnum.isImage(entity.getResourceType())) {
                vo.setInResourceExtInfo(resourceInfoDomainService.getImgInfo(entity.getUserId(), RequestContextHolder.getBelongsPlatform(), entity.getInResourceId()));
            } else if (ResourceTypeEnum.isDocument(entity.getResourceType())) {
                CloudDiskDocumentDTO ids = JSON.parseObject(entity.getInResourceId(), CloudDiskDocumentDTO.class);
                vo.setInResourceExtInfo(resourceInfoDomainService.getImgInfo(entity.getUserId(), RequestContextHolder.getBelongsPlatform(), ids.getFileIdList().get(0)));
            } else if (ResourceTypeEnum.isDialogueId(entity.getResourceType())) {
                AlgorithmChatContentEntity dialogueEntity = algorithmChatContentRepository.getById(Long.parseLong(entity.getInResourceId()));
                if (Objects.nonNull(dialogueEntity)) {
                    TaskAiAbilityEntity taskEntity = taskAiAbilityRepository.getTaskEntity(dialogueEntity.getTaskId());
                    taskRespHandle(taskEntity, entity, vo, true);
                }
            }

            // 输出资源信息
            if (ObjectUtil.isNotEmpty(entity.getTaskId())) {
                TaskAiAbilityEntity taskEntity = taskAiAbilityRepository.getTaskEntity(entity.getTaskId());
                taskRespHandle(taskEntity, entity, vo, false);
            } else {
                try {
                    if (Objects.nonNull(entity.getRecommendInfo())) {
                        vo.setRecommend(JsonUtil.parseObject(entity.getRecommendInfo(), DialogueRecommendVO.class));
                    }
                } catch (Exception e) {
                    log.error("推荐信息json解析失败，recommendInfo:{}", entity.getRecommendInfo(), e);
                }
            }

            // 查询hbase
            AiTextResultEntity aiTextResult = aiTextResultRepository.getByRowKey(entity.getUserId(), entity.getId());
            if (Objects.nonNull(aiTextResult) && ObjectUtil.isNotEmpty(aiTextResult.getRespParameters())) {
                if (aiTextResult.getRespParameters().indexOf("[") == 0) {
                    vo.setOutContent(aiTextResult.getRespParameters());
                } else {
                    AiTextResultRespParameters resp = JsonUtil.parseObject(aiTextResult.getRespParameters(), AiTextResultRespParameters.class);
                    if (Objects.nonNull(resp)) {
                        vo.setOutContent(resp.getData());
                        vo.setReasoningContent(resp.getReasoningContent());
                        vo.setNetworkSearchInfoList(resp.getNetworkSearchInfoList());
                        vo.setPersonalKnowledgeFileList(resp.getPersonalKnowledgeFileList());
                        vo.setLeadCopy(resp.getLeadCopy());
                        vo.setTitle(resp.getTitle());
                    }
                }
            }

            // 智能体
            if (ApplicationTypeEnum.isIntelligen(entity.getApplicationType()) && ObjectUtil.isNotEmpty(entity.getApplicationId())) {
                vo.setApplicationInfo(chatApplicationTypeService.getByApplicationId(entity.getApplicationId()));
            }

            voList.add(vo);
        });

        return voList;
    }


    private void taskRespHandle(TaskAiAbilityEntity taskEntity, AlgorithmChatContentEntity entity, ShareDialogueResultVO vo, boolean in) {
        if (Objects.nonNull(taskEntity) && ObjectUtil.isNotEmpty(taskEntity.getRespParam())) {
            List<TaskRespParamVO> paramVOList = JSONUtil.toList(taskEntity.getRespParam(), TaskRespParamVO.class);
            if (ObjectUtil.isNotEmpty(paramVOList)) {
                TaskRespParamVO respParamVO = paramVOList.get(0);
                if (ResourceTypeEnum.isImage(respParamVO.getOutResourceType())) {
                    if (in) {
                        if (ImageTransmissionTypeEnum.isYunDisk(respParamVO.getImageTransmissionType())) {
                            // 输入结果个人云
                            vo.setInResourceExtInfo(resourceInfoDomainService.getImgInfo(entity.getUserId(), RequestContextHolder.getBelongsPlatform(), respParamVO.getOutResourceId()));
                        } else if (ImageTransmissionTypeEnum.isEos(respParamVO.getImageTransmissionType())) {
                            // 输出结果是EOS
                            String fileName = "file." + (DialogueIntentionEnum.INTELLIGENT_CUTOUT.getCode().equals(entity.getToolsCommand()) ? "png" : "jpg");
                            vo.setInResourceId(eosExternalService.getFileUrl(respParamVO.getOutResourceId(), fileName, 24 * 60 * 60 * 1000L));
                        }
                    } else {
                        if (ImageTransmissionTypeEnum.isYunDisk(respParamVO.getImageTransmissionType())) {
                            // 输出结果个人云
                            vo.setOutResourceType(1);
                            vo.setOutResourceId(respParamVO.getOutResourceId());
                            vo.setOutResourceExtInfo(resourceInfoDomainService.getImgInfo(entity.getUserId(), RequestContextHolder.getBelongsPlatform(), respParamVO.getOutResourceId()));
                        } else if (ImageTransmissionTypeEnum.isEos(respParamVO.getImageTransmissionType())) {
                            // 输出结果是EOS
                            vo.setOutResourceType(2);
                            String fileName = "file." + (DialogueIntentionEnum.INTELLIGENT_CUTOUT.getCode().equals(entity.getToolsCommand()) ? "png" : "jpg");
                            vo.setOutResourceId(eosExternalService.getFileUrl(respParamVO.getOutResourceId(), fileName, 24 * 60 * 60 * 1000L));
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取上次对话信息
     *
     * @param dialogueId 对话id
     * @param userId     用户id
     * @return 上次对话信息DTO
     */
    @Override
    public LastDialogueInfoDTO getLastDialogueInfo(String dialogueId, String userId) {
        try {
            // 获取对话内容信息
            AlgorithmChatContentEntity contentEntity = algorithmChatContentRepository.getByIdUserId(Long.parseLong(dialogueId), userId);
            if (contentEntity == null) {
                log.info("获取上次对话信息，对话信息不存在 dialogueId:{}", dialogueId);
                return null;
            }

            // 获取任务信息
            TaskAiAbilityEntity taskEntity = taskAiAbilityRepository.getTaskEntity(contentEntity.getTaskId());
            if (taskEntity == null || !TaskStatusEnum.PROCESS_FINISH.getCode().equals(taskEntity.getTaskStatus())) {
                log.info("获取上次对话信息，任务信息无效 taskId:{}", contentEntity.getTaskId());
                return null;
            }

            if (null != taskEntity.getFileExpiredStatus() && taskEntity.getFileExpiredStatus()
                    .intValue() == FileExpiredStatusEnum.EXPIRED.getCode().intValue()) {
                log.info("获取上次对话信息，文件信息过期 taskId:{}", contentEntity.getTaskId());
                throw new YunAiBusinessException(AiResultCode.CODE_10000205.getCode(), AiResultCode.CODE_10000205.getMsg());
            }

            // 扩展信息不存在
            if (CharSequenceUtil.isEmpty(taskEntity.getRespParam())) {
                log.info("获取上次对话信息，任务表扩展信息为空 dialogueId:{} | taskEntity:{}", dialogueId, JsonUtil.toJson(taskEntity));
                return null;
            }

            // 扩展信息转换异常
            Optional<TaskRespParamVO> optional = JSONUtil.toList(taskEntity.getRespParam(), TaskRespParamVO.class)
                    .stream().findFirst();
            if (!optional.isPresent()) {
                log.info("获取上次对话信息，扩展信息转换异常 dialogueId:{} | taskEntity:{}", dialogueId, JsonUtil.toJson(taskEntity));
                return null;
            }

            // 资源类型非图片不处理
            TaskRespParamVO taskRespParamVO = optional.get();
            if (!taskRespParamVO.getOutResourceType().equals(ResourceTypeEnum.PICTURE.getType())) {
                log.info("获取上次对话信息，资源类型非图片不处理 dialogueId:{} | taskEntity:{}", dialogueId, JsonUtil.toJson(taskEntity));
                return null;
            }

            // EOS对象存储类型处理
            if (null != taskRespParamVO.getImageTransmissionType() &&
                    ImageTransmissionTypeEnum.EOS.getCode() == taskRespParamVO.getImageTransmissionType()) {
                String fileSuffix = ImageSuffixEnum.getByAlgorithmCode(taskEntity.getAlgorithmCode()).getCode();
                /*
                // 获取EOS对象存储url
                String inlineUrl = eosExternalService.getFileUrl(taskRespParamVO.getOutResourceId(),
                        (taskRespParamVO.getOutResourceId() + StrPool.DOT + fileSuffix),
                        eosFileExpireConfig.getExpireTime());
                if (StringUtils.isBlank(inlineUrl)) {
                    log.info("获取上次对话信息，获取EOS对象存储url为空 dialogueId:{} | taskEntity:{}", dialogueId, JsonUtil.toJson(taskEntity));
                    return null;
                }
                */
                //获取文件共享存储
                String eosObjectKey = taskRespParamVO.getOutResourceId();
                String localPath = imageCommonService.createPathBaseOnDate(eosObjectKey, fileSuffix, userId);
                eosExternalService.downloadEos(localPath, eosObjectKey);
                if (StringUtils.isBlank(localPath)) {
                    log.info("获取上次对话信息，获取EOS对象存储文件共享存储为空 eosObjectKey:{}, dialogueId:{} | taskEntity:{}", eosObjectKey,
                            dialogueId, JsonUtil.toJson(taskEntity));
                    return null;
                }
                // 设置EOS对象存储类型处理参数
                return LastDialogueInfoDTO.builder()
                        .localPath(localPath)
                        .imageExt(fileSuffix)
                        .imageParamType(ImageParamTypeEnum.LOCAL_PATH.getCode())
                        .build();
            }

            // 个人云存储类型处理
            return LastDialogueInfoDTO.builder()
                    .fileId(taskRespParamVO.getOutResourceId())
                    .imageParamType(ImageParamTypeEnum.FILE_ID.getCode())
                    .build();

        } catch (Exception e) {
            log.error("获取上次对话信息异常，dialogueId:{} | e:", dialogueId, e);
            if (e instanceof YunAiBusinessException) {
                throw (YunAiBusinessException) e;
            }
        }

        return null;
    }

	@Override
	public boolean updateChatFail(Long dialogueId) {
		return algorithmChatContentRepository.updateChatFailById(dialogueId);
	}

    @Override
    public AlgorithmChatContentEntity getLastIntentionDialogue(AlgorithmChatContentEntity condition) {
        return algorithmChatContentRepository.selectLastIntentionDialogue(condition);
    }

	@Override
	public String getDialogueIdByIntention(String userId, Long sessionId, IntentionInfo mainIntention, int querySize) {
		if (null == mainIntention) {
			return null;
		}
		String content = null;
		IntelligentMeetingTypeEnum typeEnum = null;
		if (DialogueIntentionEnum.isTextToolIntention(mainIntention.getIntention())
				&& null != mainIntention.getArgumentMap()) {
			List<String> contents = mainIntention.getArgumentMap()
					.get(ChatTextToolBusinessConfig.ARGUMENT_AI_MEETING_CONTENT_LIST);
			if (CollUtil.isNotEmpty(contents)) {
				content = contents.get(0);
			}
			List<String> types = mainIntention.getArgumentMap()
					.get(ChatTextToolBusinessConfig.ARGUMENT_AI_MEETING_TYPE_LIST);
			if (CollUtil.isNotEmpty(types)) {
				typeEnum = IntelligentMeetingTypeEnum.getByKey(types.get(0));
			}
		} else if (!DialogueIntentionEnum.isTextToolIntention(mainIntention.getIntention())) {
			if (CollUtil.isNotEmpty(mainIntention.getEntityList())
					&& CollUtil.isNotEmpty(mainIntention.getEntityList().get(0).getTypeList())) {
				typeEnum = IntelligentMeetingTypeEnum
						.getByKey(mainIntention.getEntityList().get(0).getTypeList().get(0));
			}

			if (CollUtil.isNotEmpty(mainIntention.getEntityList())) {
				if (CollUtil.isNotEmpty(mainIntention.getEntityList().get(0).getContentList())) {
					content = mainIntention.getEntityList().get(0).getContentList().get(0);
				} else if (CollUtil.isNotEmpty(mainIntention.getEntityList().get(0).getTitleList())) {
					// 兼容contentList空取titleList
					content = mainIntention.getEntityList().get(0).getTitleList().get(0);
				}
			}
		}
		if (StringUtils.isEmpty(content) || null == typeEnum) {
			log.warn("意图抽取失败 content or typeEnum is null mainIntention:{}", JSONUtil.toJsonStr(mainIntention));
			return null;
		}

		// 获取意图列表，按创建时间倒序
		List<AlgorithmChatContentEntity> intentionDialogues = algorithmChatContentRepository
				.selectIntentionDialogueWithDesc(userId, sessionId, typeEnum.getIntentionCode(),
						typeEnum.getSubIntentionCode(), querySize);
		if (CollUtil.isEmpty(intentionDialogues)) {
			log.warn("意图查询失败 intentionDialogues 为空", JSONUtil.toJsonStr(mainIntention));
			return null;
		}

		for (AlgorithmChatContentEntity intentionDialogue : intentionDialogues) {
			if (null != intentionDialogue.getInContent() && intentionDialogue.getInContent()
					.startsWith(applicationAgentLingxiConfig.getTextConfig().getGenPptReplyText())) {
				log.warn("当前对话为大纲，不查询判断... id:{}", intentionDialogue.getId());
				continue;
			}
			if (!(DialogueIntentionEnum.isTextToolIntention(intentionDialogue.getToolsCommand())
					&& DialogueIntentionSubEnum.isAiPpt(intentionDialogue.getSubToolsCommand()))
					&& !ChatStatusEnum.isChatSuccess(intentionDialogue.getChatStatus())) {
				log.warn("当前对话非aippt意图，不成功的跳过... id:{}, chatStatus:{}", intentionDialogue.getId(),
						intentionDialogue.getChatStatus());
				continue;
			}
			String inContent = intentionDialogue.getInContent();
			if (null != inContent && inContent.contains(content)) {
				// 判断inContent是否包含content
				return String.valueOf(intentionDialogue.getId());
			}
			if (null != inContent && applicationAgentLingxiConfig.removeSomeFilterChar(inContent)
					.contains(applicationAgentLingxiConfig.removeSomeFilterChar(content))) {
				// 移除部分配置字后再匹配
				return String.valueOf(intentionDialogue.getId());
			}
		}
		return null;
	}

}
