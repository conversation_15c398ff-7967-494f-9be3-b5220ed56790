package com.zyhl.yun.api.outer.application.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Classname NoteDetailDTO
 * @Description 笔记详情接口入参dto
 * @Date 2024/2/29 9:46
 */
@Data
public class NoteDetailReqDTO {

    /**
     * 笔记ID
     */
    @NotEmpty(message = "笔记ID不能为空")
    private String noteId;

    /**
     * 用户输入的内容
     */
    private String inputContent;

}
