package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import static java.util.Collections.singletonList;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.zyhl.yun.api.outer.application.chatv2.hanlde.impl.TextModelKnowledgeSseHandlerImpl;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.hcy.yun.ai.common.base.annotation.MethodExecutionTimeLog;
import com.zyhl.hcy.yun.ai.common.base.enums.NumberEnum;
import com.zyhl.hcy.yun.ai.common.base.enums.PantaLabelEnum;
import com.zyhl.hcy.yun.ai.common.base.enums.ParseTypeEnum;
import com.zyhl.hcy.yun.ai.common.base.es.dto.PanTaResourceOuterSearchV2DTO;
import com.zyhl.hcy.yun.ai.common.base.es.entity.EsPanTaResourceEntity;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.hcy.yun.ai.common.base.utils.StopWatchUtil;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.dto.TextNerExtractDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.enums.UserBelongsPlatformEnum;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.RecallQueryDTO;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.dialogue.RecallConfig;
import com.zyhl.hcy.yun.ai.common.rag.enums.KnowledgeRerankTypeEnum;
import com.zyhl.hcy.yun.ai.common.rag.enums.RecallVersionEnum;
import com.zyhl.hcy.yun.ai.common.rag.vo.RecallResultVO;
import com.zyhl.hcy.yun.ai.common.rag.vo.RerankResultVO;
import com.zyhl.yun.api.outer.application.assembler.ChatFlowResultAssembler;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.impl.InputTextIntentionHandlerImpl;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.impl.SpecialAiInternetSearchHandlerImpl;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.impl.TextModelTextSseHandlerImpl;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatDialogueRecommendService;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatDialogueSearchService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatTidbSaveDTO;
import com.zyhl.yun.api.outer.application.dto.ContinueTextSseDTO;
import com.zyhl.yun.api.outer.application.dto.SearchImageReqDTO;
import com.zyhl.yun.api.outer.application.service.AlgorithmAiRegisterService;
import com.zyhl.yun.api.outer.application.service.chat.DialogueIntentionService;
import com.zyhl.yun.api.outer.application.service.chat.DialogueRecommendService;
import com.zyhl.yun.api.outer.application.service.chat.LeadCopyService;
import com.zyhl.yun.api.outer.application.service.external.SearchImageAlbumListService;
import com.zyhl.yun.api.outer.application.util.TimeRangeUtils;
import com.zyhl.yun.api.outer.application.vo.ContentExtInfoVO;
import com.zyhl.yun.api.outer.config.AllNetworkSearchProperties;
import com.zyhl.yun.api.outer.config.KnowledgeDialogueProperties;
import com.zyhl.yun.api.outer.config.MailaiSearchProperties;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.config.SearchParamProperties;
import com.zyhl.yun.api.outer.config.SearchPromptProperties;
import com.zyhl.yun.api.outer.config.SearchResultProperties;
import com.zyhl.yun.api.outer.config.SearchSuffixProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.config.WhiteListProperties;
import com.zyhl.yun.api.outer.config.note.NoteSearchProperties;
import com.zyhl.yun.api.outer.constants.CommonConstant;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.constants.FutureConstants;
import com.zyhl.yun.api.outer.constants.SearchConstant;
import com.zyhl.yun.api.outer.domain.dto.RerankDTO;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domain.entity.DialogueIntentionEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.vo.AllNetworkSearchRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.IntentionRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.domain.vo.chat.search.SearchInfo;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.MillisecondTimeRange;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchActivityParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchCommonParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchDiscoveryParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchFileParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchFunctionParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchImageParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchKnowledgeBaseResourceParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchMailAttachmentParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchMailParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchMyGroupParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchNoteContentParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchNoteParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchRecommendGroupParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SecondTimeRange;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchDiscoveryResult;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchFileResult;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchFunctionResult;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchImageResult;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchNoteContent;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchNoteContentResult;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchResult;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.domainservice.PersonResourceHandleService;
import com.zyhl.yun.api.outer.domainservice.SearchReturnTermsService;
import com.zyhl.yun.api.outer.domainservice.UserKnowledgeDomainService;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.ChatNetworkSearchStatusEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.AiTextResultVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.OutAuditStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.SseNameEnum;
import com.zyhl.yun.api.outer.enums.chat.search.ImageSortTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.search.LeadCopyTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.search.SearchDiscoveryParamQueryTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.search.SearchDiscoveryParamSearchTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.search.SearchFileParamTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.search.SearchTypeEnum;
import com.zyhl.yun.api.outer.external.ApiTextExternalService;
import com.zyhl.yun.api.outer.external.CmicTextService;
import com.zyhl.yun.api.outer.external.RagExternalService;
import com.zyhl.yun.api.outer.external.service.UserEtnService;
import com.zyhl.yun.api.outer.strategy.chat.searchresult.SearchStrategy;
import com.zyhl.yun.api.outer.strategy.chat.searchresult.SearchStrategyEnum;
import com.zyhl.yun.api.outer.util.EntityListUtils;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.VersionUtil;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;
import com.zyhl.yun.api.outer.vo.IntentEntityVO;
import com.zyhl.yun.api.outer.vo.KeyValueVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 智能搜索（异步搜索数据）
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RefreshScope
@RequiredArgsConstructor
public class ChatDialogueSearchServiceImpl implements ChatDialogueSearchService {
	private final static String FILE_CONTENT_STR = "[文件内容]:";
	private final DialogueIntentionService intentionService;

	private final SearchPromptProperties promptProperties;

	private final AlgorithmAiRegisterService aiRegisterService;

	private final DialogueRecommendService recommendService;

	private final SearchSuffixProperties searchSuffixProperties;

	private final WhiteListProperties whiteListProperties;

	private final SearchReturnTermsService searchReturnTermsService;

	private final SearchResultProperties searchResultProperties;

	private final SearchParamProperties searchParamProperties;

	private final SourceChannelsProperties sourceChannelsProperties;

	private final AllNetworkSearchProperties allNetworkSearchProperties;

	private final UserEtnService userEtnService;

	private final SearchImageAlbumListService searchImageAlbumListService;

	@Resource
	private UserKnowledgeDomainService userKnowledgeDomainService;

	private final ChatConfigServiceDomainService chatConfigServiceDomainService;

	private final ChatDialogueRecommendService chatDialogueRecommendService;
	@Resource
	private RagExternalService ragExternalService;

	@Resource(name = "multiRouteRerankThreadPool")
	private ExecutorService multiRouteRerankThreadPool;

	@Resource
	private CmicTextService cmicTextService;

	@Resource(name = "searchThreadPool")
	private ExecutorService searchThreadPool;

	@Resource(name = "searchPantaThreadPool")
	private ExecutorService searchPantaThreadPool;

	@Resource
	private ModelProperties modelProperties;

	@Resource
	private DataSaveService dataSaveService;

	@Resource
	private SpecialAiInternetSearchHandlerImpl specialAiInternetSearchHandlerImpl;

	@Resource
	private TextModelTextSseHandlerImpl textModelTextSseHandlerImpl;

	@Resource
	private TextModelKnowledgeSseHandlerImpl textModelKnowledgeSseHandlerImpl;

	@Resource
	private MailaiSearchProperties mailaiSearchProperties;

	@Resource
	private KnowledgeDialogueProperties knowledgeDialogueProperties;

	@Resource
	private InputTextIntentionHandlerImpl inputTextIntentionHandlerImpl;

	@Resource
	private ApiTextExternalService apiTextExternalService;

	private final ChatFlowResultAssembler chatFlowResultAssembler;

	private final LeadCopyService leadCopyService;

	@Resource
	private PersonResourceHandleService personResourceHandleService;
	@Resource
	private NoteSearchProperties noteSearchProperties;
	/**
	 * 【小天助手】搜索意图处理
	 *
	 * @param handleDTO 用户输入对象
	 */
	@Override
	public boolean commonSearchIntentionHandle(ChatAddHandleDTO handleDTO) {
		/** 智能搜索意图处理 */
		searchIntentionHandle(handleDTO);

		/** 【小天助手】判断是否自动进入全网搜流程 */
		boolean saveSearchResultFlag = autoAllNetworkSearch(handleDTO);

		/** 同步保存数据 */
		if (saveSearchResultFlag) {
			StopWatch stopWatch = StopWatchUtil.createStarted();
			try {
				AiTextResultRespParameters hbaseResp = handleDTO.getHbaseResp();
				if(ObjectUtil.isNotNull(hbaseResp)) {
					hbaseResp.setOutputCommandVO(handleDTO.getIntentionVO());
				}

				// 保存hbase-所有对话结果
				dataSaveService.saveHbaseAllChatResult(handleDTO, hbaseResp);

				// 保存tidb-所有对话结果
				dataSaveService.saveTidbAllChatResult(handleDTO);
			} catch (Exception e) {
				log.error("【小天助手】搜索功能处理-同步保存数据，execute-异常\n dialogueId：{}", handleDTO.getDialogueId(), e);
				throw new YunAiBusinessException(ResultCodeEnum.ERROR_SERVER_UNAVAILABLE);
			} finally {
				log.info("【小天助手】搜索功能处理-同步保存数据，方法-耗时：{}", StopWatchUtil.logTime(stopWatch));
				StopWatchUtil.clearDuration();
			}
		} else {
			log.info("【小天助手】搜索功能处理-执行全网搜");
			// 去掉引导文案
			handleDTO.getRespVO().setLeadCopy(null);
			// 去掉全网搜推荐数据
			handleDTO.getRespVO().getRecommend().setAllNetworkSearchList(null);
		}
		return saveSearchResultFlag;
	}

	/**
	 * 【云邮助手】搜索意图处理
	 *
	 * @param handleDTO 用户输入对象
	 */
	@Override
	public boolean mailSearchIntentionHandle(ChatAddHandleDTO handleDTO) {
		/** 智能搜索意图处理 */
		searchIntentionHandle(handleDTO);

		/** 【云邮助手】判断是否自动进入全网搜流程 */
		boolean saveSearchResultFlag = true;
		if (mailaiSearchProperties.isAutoAllNetworkSearch()) {
			saveSearchResultFlag = mailAutoAllNetworkSearch(handleDTO);
		}

		/** 同步保存数据 */
		if (saveSearchResultFlag) {
			StopWatch stopWatch = StopWatchUtil.createStarted();
			try {
				AiTextResultRespParameters hbaseResp = handleDTO.getHbaseResp();
				if(ObjectUtil.isNotNull(hbaseResp)) {
					hbaseResp.setOutputCommandVO(handleDTO.getIntentionVO());
				}

				// 保存hbase-所有对话结果
				dataSaveService.saveHbaseAllChatResult(handleDTO, hbaseResp);

				// 保存tidb-所有对话结果
				dataSaveService.saveTidbAllChatResult(handleDTO);
			} catch (Exception e) {
				log.error("【云邮助手】搜索功能处理-同步保存数据，execute-异常\n dialogueId：{}", handleDTO.getDialogueId(), e);
				throw new YunAiBusinessException(ResultCodeEnum.ERROR_SERVER_UNAVAILABLE);
			} finally {
				log.info("【云邮助手】搜索功能处理-同步保存数据，方法-耗时：{}", StopWatchUtil.logTime(stopWatch));
				StopWatchUtil.clearDuration();
			}
		} else {
			log.info("【云邮助手】搜索功能处理-执行全网搜");
			// 去掉引导文案
			handleDTO.getRespVO().setLeadCopy(null);
			// 去掉全网搜推荐数据
			handleDTO.getRespVO().getRecommend().setAllNetworkSearchList(null);
		}
		return saveSearchResultFlag;
	}

	@Override
	public ChatAddRespVO searchIntentionHandle(ChatAddHandleDTO handleDTO) {
		long start = System.currentTimeMillis();
		/** 参数初始化 */
		ChatAddReqDTO dto = handleDTO.getReqDTO();
		ChatAddRespVO resVO = handleDTO.getRespVO();
		// 对话id
		String dialogueId = resVO.getDialogueId();

		try {
			/**
			 * 处理前端传入了意图，但是没有意图识别实体的情况
			 * 意图识别获取实体，但把意图替换成前端传入的意图
			 */
			DialogueIntentionVO intentionVO = inputTextIntentionHandlerImpl.createInputIntention(handleDTO);
			log.info("ASyncSearchServiceImpl-searchIntentionHandle，意图识别-耗时：{}ms", System.currentTimeMillis() - start);

			// 获取意图结果code
			String intention = intentionService.getIntentionCode(intentionVO);

			// 初始化会话输入返回结果VO
			resVO.setOutputCommandCode(intention);
			// 设置授权报名记录
			start = System.currentTimeMillis();
			handleSetRegisterStatus(dto, resVO, intention);
			log.info("ASyncSearchServiceImpl-searchIntentionHandle，设置授权报名记录-耗时：{}ms",
					System.currentTimeMillis() - start);

			/** 构建搜索参数 */
			start = System.currentTimeMillis();
			SearchParam searchParam = createSearchParam(intention, dto, intentionVO);
			log.info("ASyncSearchServiceImpl-searchIntentionHandle，构建搜索参数-耗时：{}ms", System.currentTimeMillis() - start);

			/** 异步处理全网搜推荐 */
			asyncAllNetworkSearchRecommend(searchParam, intention, handleDTO);

			/** 构建公共搜索参数 */
			SearchCommonParam searchCommonParam = new SearchCommonParam();
			searchCommonParam.setDialogue(dto.getDialogueInput().getDialogue());

			/** 获取搜索结果 */
			SearchResult searchResult = getSearchResult(searchParam, searchCommonParam);
			/** 实时搜索人物关系相册推荐 */
			ContentExtInfoVO extInfo = searchImageAlbumListService.searchImageSetAlbumList(dto.getSourceChannel(),
					VersionUtil.getVersionMap(), resVO.getLeadCopy(), resVO.getRecommend(), searchResult);
			if (null != extInfo) {
				// 设置扩展内容
				resVO.setLeadCopy(extInfo.getLeadCopy());
				resVO.setRecommend(extInfo.getRecommend());
			}
			// 临时改动，为了后面逻辑能校验是否涉及人物关系，加了个字段存【语义搜图结果】
			handleDTO.setTmpSearchImageResult((null != searchResult && null != searchResult.getSearchImageResult()) ?
					searchResult.getSearchImageResult() : null);

			/** 处理成功消息 */
			start = System.currentTimeMillis();
			ChatAddRespVO algorithmChatAddVO = handleSuccessMsg(intention, dto, resVO, searchParam, searchResult,
					handleDTO, intentionVO);
			log.info("ASyncSearchServiceImpl-searchIntentionHandle，处理成功消息-耗时：{}ms", System.currentTimeMillis() - start);
			return algorithmChatAddVO;
		} catch (YunAiBusinessException e) {
			log.info("searchIntentionHandle dto:{} | resVO:{} | YunAiBusinessException error:", JsonUtil.toJson(dto),
					JsonUtil.toJson(resVO), e);
			// 特定错误码处理
			start = System.currentTimeMillis();
			if (AiResultCode.CODE_10022024.getCode().equals(e.getCode())) {
				// 语义实体为空、搜索条件为空或搜索结果为空时的返回，返回【引导文案-LeadCopy，type=6】
				updateFailForException10022024(resVO, AiResultCode.CODE_10022024.getCode(), e.getMessage(), dialogueId,
						handleDTO);
			} else {
				// 处理失败消息
				handleFailMsg(AiResultCode.getByCodeOrMsg(e.getCode(), e.getMessage()), dialogueId, handleDTO);
			}
			log.info("ASyncSearchServiceImpl-searchIntentionHandle，特定错误码处理-耗时：{}ms",
					System.currentTimeMillis() - start);
		} catch (Exception e) {
			log.error("searchIntentionHandle dto:{} | resVO:{} | Exception error:", JsonUtil.toJson(dto),
					JsonUtil.toJson(resVO), e);
			// 处理失败消息
			start = System.currentTimeMillis();
			handleFailMsg(AiResultCode.CODE_10000101, dialogueId, handleDTO);
			log.info("ASyncSearchServiceImpl-searchIntentionHandle，处理失败消息-耗时：{}ms", System.currentTimeMillis() - start);
		}

		return resVO;
	}

	@Override
	public void handleSetRegisterStatus(ChatAddReqDTO dto, ChatAddRespVO resVO, String intention) {
		// 获取用户底座信息
		Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();
		// 老底座不需要设置
		if (UserBelongsPlatformEnum.OSE.getBelongsPlatform().equals(belongsPlatform)) {
			return;
		}

		// 意图012和018才需要设置智能相册报名记录
		if (DialogueIntentionEnum.existImageIntention(intention)) {
			// 新底座设置智能相册报名记录
			Integer aiAlbumStatus = aiRegisterService.checkAlbum(dto.getUserId()) ? 1 : 0;
			resVO.setAiAlbumStatus(aiAlbumStatus);
		}

		// 意图013和018才需要设置文档检索报名记录
		if (DialogueIntentionEnum.existDocumentIntention(intention)) {
			resVO.setAiDocStatus(aiRegisterService.checkDocSearch(dto.getUserId()) ? 1 : 0);
		}
	}

	/**
	 * 校验语义结果，数据不可用抛出搜索引导文案业务异常
	 *
	 * @param entityList 语义结果列表
	 */
	private void handleEntityList(ChatAddReqDTO reqDTO, DialogueIntentionEnum intentionEnum,
			List<IntentEntityVO> entityList) {
		String message = null;
		if (sourceChannelsProperties.isMail(reqDTO.getSourceChannel())) {
			// 获取云邮助手引导语
			message = String.valueOf(promptProperties.getMailGuideRandomString(intentionEnum.getInstruction()));
		} else {
			// 获取引导文案
			message = String.valueOf(promptProperties.getGuideRandomString(intentionEnum.getInstruction()));
		}

		// 校验实体结果列表
		EntityListUtils.checkEntityList(entityList, message, intentionEnum);
	}

	/**
	 * 处理成功消息
	 *
	 * @param intention    对话意图
	 * @param dto          会话输入DTO
	 * @param resVO        会话输入返回结果VO
	 * @param searchParam  搜索条件对象
	 * @param searchResult 搜索结果对象
	 * @param handleDTO    用户输入对象
	 * @param intentionVO  对话意图响应VO
	 * @return 会话输入返回结果VO
	 */
	@MethodExecutionTimeLog("智能搜索意图处理-handleSuccessMsg")
	private ChatAddRespVO handleSuccessMsg(String intention, ChatAddReqDTO dto, ChatAddRespVO resVO,
			SearchParam searchParam, SearchResult searchResult, ChatAddHandleDTO handleDTO,
			DialogueIntentionVO intentionVO) {
		// 对话id
		String dialogueId = resVO.getDialogueId();
		Long dialogueIdLong = Long.parseLong(dialogueId);
		String dialogue = dto.getDialogueInput().getDialogue();
		String title = null;
		DialogueIntentionEnum intentionEnum = DialogueIntentionEnum.getByCode(intention);
		if (Objects.isNull(intentionEnum)) {
			log.error("ASyncSearchServiceImpl-handleSuccessMsg，dialogueIntentionEnum is null");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_SERVER_NOT_READY);
		}
		List<IntentionInfo> intentionInfoList = intentionVO.getIntentionInfoList();

		// 构建对话tidb保存数据
		AlgorithmChatTidbSaveDTO tidbSaveDTO = AlgorithmChatTidbSaveDTO.builder().dialogueId(dialogueIdLong)
				.outAuditStatus(OutAuditStatusEnum.SUCCESS.getCode()).chatStatus(ChatStatusEnum.CHAT_SUCCESS.getCode())
				.msg(null).build();
		handleDTO.setAlgorithmChatTidbSaveDTO(tidbSaveDTO);

		// 构建hbase的resp
		AiTextResultRespParameters respParameters = AiTextResultRespParameters.builder()
				.version(AiTextResultVersionEnum.V2.getVersion())
				.param(searchParam)
				.intentionInfoList(intentionInfoList).build();

		// 校验搜索结果为空时，额外将【引导文案-LeadCopy，type=6】保存到hbase
		List<SearchInfo> searchInfoList = searchResult.createSearchInfoList(intentionInfoList, searchResultProperties);
		if (CollUtil.isEmpty(searchInfoList)) {
			// 非018-综合搜索 && 邮箱搜索结果存在tips，放到【引导文案-LeadCopy，type=6】
			String tips = searchResult.checkAndGetSearchMailResultTips();
			if (!DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode().equals(intentionEnum.getCode())
					&& CharSequenceUtil.isNotBlank(tips)) {
				createLeadCopy6ByMessage(resVO, respParameters, tips);
				log.info("邮箱搜索结果存在tips，dialogueId:{} | searchResult:{}", dialogueId, JsonUtil.toJson(searchResult));
			} else {
				// 判断渠道，邮件渠道返回邮件引导文案，其他渠道返回普通引导文案
				boolean isMail = SourceChannelsProperties.isMailChannel(dto.getSourceChannel());
				String message = isMail ? mailaiSearchProperties.getDialogueSearchNoResult()
//						promptProperties.getMailPromptRandomString(intentionEnum.getInstruction())
						: promptProperties.getPromptRandomString(intentionEnum.getInstruction());
				createLeadCopy6ByMessage(resVO, respParameters, message);
			}
			log.info("搜索结果为空，dialogueId:{} | searchResult:{}", dialogueId, JsonUtil.toJson(searchResult));
		} else {
			respParameters.setResult(ResultCodeEnum.SUCCESS);
			/** 获取搜索返回词V1（异常时返回默认值） */
			title = searchReturnTermsService.getSearchReturnTermsV1(handleDTO.getSearchReturnTermsFuture(), intention,
					dialogueIdLong, dialogue);
		}

		/** 更新会话输入返回结果VO */
		handleDTO.setSearchParam(searchParam);
		//设置搜索类型
		resVO.getFlowResult().setResultType(FlowResultTypeEnum.SEARCH.getType());
		resVO.getFlowResult().setSearchInfoList(searchInfoList);
		// set搜索结果的标题
		resVO.setTitle(title);
		// set对话结果推荐
		setDialogueRecommend(intention, resVO, searchResult, handleDTO, dialogueIdLong);

		/** 获取和设置全网搜推荐 */
		getAndSetAllNetworkSearchList(handleDTO);

		/** 构建知识库资源搜索意图推荐 */
		boolean isMail = SourceChannelsProperties.isMailChannel(dto.getSourceChannel());
		if (isMail){
			createMailSearchResourceRecommend(handleDTO);
		} else {
			createSearchResourceRecommend(handleDTO);
		}

		/** 【放最后处理】把hbase的resp，放入innerDTO */
		// set搜索结果的标题（hbase）
		respParameters.setTitle(title);
		// set流式结果
		List<DialogueFlowResult> outputList = respParameters.getOutputList();
		if(CollUtil.isNotEmpty(outputList)){
			outputList.add(chatFlowResultAssembler.getFlowResult(resVO.getFlowResult()));
		} else {
			outputList = ListUtil.toList(chatFlowResultAssembler.getFlowResult(resVO.getFlowResult()));
		}
		respParameters.setOutputList(outputList);
		handleDTO.setHbaseResp(respParameters);

		return resVO;
	}

	/**
	 * set对话结果推荐
	 *
	 * @Author: WeiJingKun
	 *
	 * @param intention      对话意图
	 * @param resVO          会话输入返回结果VO
	 * @param searchResult   搜索结果对象
	 * @param params         用户输入对象
	 * @param dialogueIdLong 对话id
	 * @return void
	 */
	private void setDialogueRecommend(String intention, ChatAddRespVO resVO, SearchResult searchResult,
			ChatAddHandleDTO params, Long dialogueIdLong) {
		long start = System.currentTimeMillis();
		// 获取功能搜索搜索意图推荐状态
		boolean functionRecommendFlag = getFunctionRecommendFlag(intention, params);
		// 功能搜索时，不走大模型获取推荐数据
		if (functionRecommendFlag) {
			// 功能搜索有结果，用搜索结果第一个功能名(如果有多个结果，只取第一个)，通过【配置的模板】构建下一个问题推荐语句
			SearchFunctionResult searchFunctionResult = searchResult.getSearchFunctionResult();
			if (!(null == searchFunctionResult || CollUtil.isEmpty(searchFunctionResult.getFunctionList()))) {
				// 构建意图推荐
				DialogueRecommendVO recommendVO = resVO.getRecommend();
				List<IntentionRecommendVO> intentionRecommendList = new ArrayList<>();
				// 获取推荐模板配置
				SearchResultProperties.Recommend recommend = searchResultProperties
						.getRecommend(SearchTypeEnum.FUNCTION.getSearchType());
				// format引导文案
				String copy = String.format(recommend.getIntentionTemplate(),
						searchFunctionResult.getFunctionList().get(0).getTitle());
				intentionRecommendList
						.add(new IntentionRecommendVO(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode(), copy, ""));
				if (ObjectUtil.isNull(recommendVO)) {
					resVO.setRecommend(DialogueRecommendVO.builder().intentionList(intentionRecommendList).build());
				} else {
					recommendVO.setIntentionList(intentionRecommendList);
				}
			}
		} else {
			recommendService.setFuturesResult(dialogueIdLong, resVO.getRecommend(), params.getFutures());
		}
		log.info("ASyncSearchServiceImpl-handleSuccessMsg，获取对话结果推荐-耗时：{}ms", System.currentTimeMillis() - start);
	}

	/**
	 * 获取功能搜索搜索意图推荐状态
	 *
	 * @Author: WeiJingKun
	 *
	 * @param intention 意图编码
	 * @param params    用户输入对象
	 * @return boolean
	 */
	private boolean getFunctionRecommendFlag(String intention, ChatAddHandleDTO params) {
		boolean functionRecommendFlag = false;
		/**
		 * 条件： 1、功能搜索 2、多意图数量 >= 2 3、第二个意图为【000-文生文意图】
		 */
		List<IntentionInfo> intentionInfoList = params.getIntentionVO().getIntentionInfoList();
		if (DialogueIntentionEnum.SEARCH_FUNCTION.getCode().equals(intention) && intentionInfoList.size() >= 2
				&& DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode().equals(intentionInfoList.get(1).getIntention())) {
			functionRecommendFlag = true;
		}
		return functionRecommendFlag;
	}

	/**
	 * 根据提示文案创建【引导文案-LeadCopy，type=6】
	 *
	 * @Author: WeiJingKun
	 *
	 * @param resVO          会话输入返回结果VO
	 * @param respParameters
	 * @param message
	 * @return void
	 */
	private static void createLeadCopy6ByMessage(ChatAddRespVO resVO, AiTextResultRespParameters respParameters,
			String message) {
		long start = System.currentTimeMillis();
		respParameters.setResultCode(AiResultCode.CODE_10022024.getCode());
		respParameters.setResultMsg(message);
		if (null == resVO.getLeadCopy()) {
			// 未设置leadcopy才执行
			/** 将抛异常处理改为返回【引导文案-LeadCopy，type=6】 */
			// 构建引导文案
			LeadCopyVO leadCopyVO = LeadCopyVO.builder()
					// 类型6：语义实体为空、搜索条件为空或搜索结果为空时的返回，仅返回提示文案promptCopy
					.type(LeadCopyTypeEnum.TYPE6.getCode())
					// 提示文案
					.promptCopy(message).build();
			// hbase数据存入leadCopyVO
			respParameters.setLeadCopy(leadCopyVO);
			// 会话输入返回结果VO，set引导文案
			resVO.setLeadCopy(leadCopyVO);
		}
		log.info("ASyncSearchServiceImpl-createLeadCopy6ByMessage，根据提示文案创建【引导文案-LeadCopy，type=6】-耗时：{}ms",
				System.currentTimeMillis() - start);
	}

	/**
	 * 处理失败消息
	 *
	 * @param resultCode AI结果code对象
	 * @param dialogueId 对话id
	 * @param params     用户输入对象
	 */
	private void handleFailMsg(AiResultCode resultCode, String dialogueId, ChatAddHandleDTO params) {
		updateFailThrowException(resultCode.getCode(), resultCode.getMsg(), dialogueId, params);
	}

	/**
	 * 更新失败状态并抛出异常
	 *
	 * @param code       错误码code
	 * @param msg        错误码信息
	 * @param dialogueId 对话id
	 * @param params     用户输入对象
	 */
	private void updateFailThrowException(String code, String msg, String dialogueId, ChatAddHandleDTO params) {
		// 构建对话tidb保存数据
		AlgorithmChatTidbSaveDTO tidbSaveDTO = AlgorithmChatTidbSaveDTO.builder().dialogueId(Long.parseLong(dialogueId))
				.outAuditStatus(OutAuditStatusEnum.FAIL.getCode()).chatStatus(ChatStatusEnum.CHAT_FAIL.getCode())
				.msg(msg).build();
		params.setAlgorithmChatTidbSaveDTO(tidbSaveDTO);

		// 构建hbase的resp
		AiTextResultRespParameters respParameters = AiTextResultRespParameters.builder()
				.version(AiTextResultVersionEnum.V2.getVersion()).resultCode(code).resultMsg(msg).build();
		// 把hbase的resp，放入innerDTO
		params.setHbaseResp(respParameters);

		// 抛出转换后的异常
		throw new YunAiBusinessException(code, msg);
	}

	/**
	 * 语义实体为空、搜索条件为空或搜索结果为空时的返回 将抛异常处理改为返回【引导文案-LeadCopy，type=6】
	 *
	 * @param resVO      会话输入返回结果VO
	 * @param code       错误码code
	 * @param msg        错误码信息
	 * @param dialogueId 对话id
	 * @param params     用户输入对象
	 */
	private void updateFailForException10022024(ChatAddRespVO resVO, String code, String msg, String dialogueId,
			ChatAddHandleDTO params) {
		if (AiResultCode.CODE_10022024.getCode().equals(code)) {
			// 构建对话tidb保存数据
			AlgorithmChatTidbSaveDTO tidbSaveDTO = AlgorithmChatTidbSaveDTO.builder()
					.dialogueId(Long.parseLong(dialogueId)).outAuditStatus(OutAuditStatusEnum.SUCCESS.getCode())
					.chatStatus(ChatStatusEnum.CHAT_SUCCESS.getCode()).msg(msg).build();
			params.setAlgorithmChatTidbSaveDTO(tidbSaveDTO);

			/** 更新hbase结果 */
			// 构建引导文案
			LeadCopyVO leadCopyVO = LeadCopyVO.builder()
					// 类型6：语义实体为空、搜索条件为空或搜索结果为空时的返回，仅返回提示文案promptCopy
					.type(LeadCopyTypeEnum.TYPE6.getCode())
					// 提示文案
					.promptCopy(msg).build();
			// 构建hbase的resp
			AiTextResultRespParameters respParameters = AiTextResultRespParameters.builder()
					.version(AiTextResultVersionEnum.V2.getVersion()).resultCode(code).resultMsg(msg)
					.leadCopy(leadCopyVO).build();
			// 把hbase的resp，放入innerDTO
			params.setHbaseResp(respParameters);

			/** 会话输入返回结果VO，set引导文案 */
			resVO.setLeadCopy(leadCopyVO);
		}
	}

	/**
	 * 获取搜索结果
	 *
	 * @Author: WeiJingKun
	 */
	@MethodExecutionTimeLog("获取搜索结果-serviceImpl")
	@Override
	public SearchResult getSearchResult(SearchParam searchParam, SearchCommonParam searchCommonParam)
			throws ExecutionException, InterruptedException, TimeoutException {
		log.info("获取搜索结果-serviceImpl enter searchParam:{}", JSONUtil.toJsonStr(searchParam));
		/** 参数初始化 */
		SearchResult searchResult = SearchResult.builder().build();
		if (null == searchParam) {
			return searchResult;
		}

		// 获取主线程的ThreadLocal信息，并且异步调用前，将父线程中的请求信息绑定给子线程
		RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo = RequestContextHolder
				.getThreadLocalInfoAndBindingAttributes();

		/** 添加搜索处理任务 */
		List<CompletableFuture<Void>> futures = new ArrayList<>();
		List<Object> needSearchSubParamList = searchParam.createNeedSearchSubParamList();
		if (CollUtil.isNotEmpty(needSearchSubParamList)) {
			needSearchSubParamList.forEach(needSearchSubParam -> {
				futures.add(asyncSearch(searchResult, needSearchSubParam, searchCommonParam, mainThreadLocalInfo));
			});
		}

		/** 过滤null，获取异步线程CompletableFuture集合 */
		List<CompletableFuture<Void>> completableFutureList = futures.stream().filter(Objects::nonNull)
				.collect(Collectors.toList());

		/** 等到所有任务都完成，并设置超时 */
		if (CollUtil.isNotEmpty(completableFutureList)) {
			for (CompletableFuture<Void> future : completableFutureList) {
				try {
					future.get(FutureConstants.SIXTY_SECONDS, TimeUnit.SECONDS);
				} catch (InterruptedException e) {
					log.error("ASyncSearchServiceImpl-getSearchResult，future.get()，异常", e);
				}
			}
		}

		return searchResult;
	}

	@Override
	public void mailAiSearchIntentionHandle(ChatAddHandleDTO handleDTO) {
		/** 参数初始化 */
		ChatAddReqDTO dto = handleDTO.getReqDTO();
		ChatAddRespVO resVO = handleDTO.getRespVO();
		DialogueIntentionVO intentionVO = handleDTO.getIntentionVO();
		// 对话id
		String dialogueId = resVO.getDialogueId();

		try {
			long t1 = System.currentTimeMillis();

			// 获取主意图结果code
			String intention = intentionService.getIntentionCode(intentionVO);

			// 初始化会话输入返回结果VO
			resVO.setOutputCommandCode(intention);

			// 设置授权报名记录
			handleSetRegisterStatus(dto, resVO, intention);

			long t2 = System.currentTimeMillis();

			// 构建搜索参数
			SearchParam searchParam = createSearchParam(intention, dto, intentionVO);
			SearchImageParam searchImageParam = searchParam.getSearchImageParam();
			if (null != searchImageParam) {
				Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();
				String userId = RequestContextHolder.getUserId();
				boolean albumFlag = aiRegisterService.checkAlbum(userId);
				// 【老底座】和新底座没报名的用户不能语义搜图
				if (UserBelongsPlatformEnum.OSE.getBelongsPlatform().equals(belongsPlatform)|| !albumFlag) {
					searchParam.setSearchImageParam(null);
				}

			}
			/** 异步处理全网搜推荐 */
			asyncAllNetworkSearchRecommend(searchParam, intention, handleDTO);


			long t3 = System.currentTimeMillis();

			// 构建公共搜索参数
			SearchCommonParam searchCommonParam = new SearchCommonParam();
			searchCommonParam.setDialogue(dto.getDialogueInput().getDialogue());

			// 获取搜索结果
			SearchResult searchResult = getSearchResult(searchParam, searchCommonParam);

			long t4 = System.currentTimeMillis();

			// 处理成功消息
			handleSuccessMsg(intention, dto, resVO, searchParam, searchResult, handleDTO, intentionVO);
			handleDTO.setRespVO(resVO);

			long t5 = System.currentTimeMillis();

			log.info("ChatDialogueSearchServiceImpl-mailAiSearchIntentionHandle, handleSetRegisterStatus-useTime={}ms, " +
					"createSearchParam-useTime={}ms, getSearchResult-useTime={}ms, handleSuccessMsg-useTime={}ms, total-useTime={}ms", t2-t1, t3-t2, t4-t3, t5-t4, t5-t1);

		} catch (YunAiBusinessException e) {
			log.info("mailAiSearchIntentionHandle dto:{} | resVO:{} | YunAiBusinessException error:", JsonUtil.toJson(dto), JsonUtil.toJson(resVO), e);
			// 特定错误码处理
			long start = System.currentTimeMillis();
			if (AiResultCode.CODE_10022024.getCode().equals(e.getCode())) {
				// 语义实体为空、搜索条件为空或搜索结果为空时的返回，返回【引导文案-LeadCopy，type=6】
				updateFailForException10022024(resVO, AiResultCode.CODE_10022024.getCode(), e.getMessage(), dialogueId, handleDTO);
			} else {
				// 处理失败消息
				handleFailMsg(AiResultCode.getByCodeOrMsg(e.getCode(), e.getMessage()), dialogueId, handleDTO);
			}
			log.info("ChatDialogueSearchServiceImpl-mailAiSearchIntentionHandle，特定错误码处理-耗时：{}ms", System.currentTimeMillis() - start);
		} catch (Exception e) {
			log.error("mailAiSearchIntentionHandle dto:{} | resVO:{} | Exception error:", JsonUtil.toJson(dto), JsonUtil.toJson(resVO), e);
			// 处理失败消息
			long start = System.currentTimeMillis();
			handleFailMsg(AiResultCode.CODE_10000101, dialogueId, handleDTO);
			log.info("ChatDialogueSearchServiceImpl-mailAiSearchIntentionHandle，处理失败消息-耗时：{}ms", System.currentTimeMillis() - start);
		}
	}

	/**
	 * 使用策略工厂模式，【异步】获取搜索接口结果
	 *
	 * @param searchResult        搜索结果
	 * @param searchSubParam      搜索子参数
	 * @param searchCommonParam   搜索公共参数
	 * @param mainThreadLocalInfo 主线程ThreadLocal信息
	 * @return java.util.concurrent.CompletableFuture<java.lang.Void>
	 * @Author: WeiJingKun
	 */
	private CompletableFuture<Void> asyncSearch(SearchResult searchResult, Object searchSubParam,
			SearchCommonParam searchCommonParam, RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo) {
		if (ObjectUtil.isNotNull(searchSubParam)) {
			return CompletableFuture.runAsync(() -> {
				/**
				 * 获取搜索策略（新增搜索类型，需要在SearchStrategyEnum添加）
				 *
				 * @see com.zyhl.yun.api.outer.strategy.chat.searchresult.SearchStrategyEnum
				 */
				SearchStrategy strategy = SearchStrategyEnum.getSearchStrategyByKey(searchSubParam.getClass());
				if (ObjectUtil.isNull(strategy)) {
					throw new YunAiBusinessException(ResultCodeEnum.ERROR_SERVER_NOT_READY);
				}
				// 把主线程ThreadLocal信息set到子线程
				RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);
				// 执行搜索策略
				strategy.performSearch(searchResult, searchSubParam, searchCommonParam);
			}, searchThreadPool);
		}
		return null;
	}

	/**
	 * 构建搜索参数
	 *
	 * @Author: WeiJingKun
	 * @param intention   意图
	 * @param dto         会话输入入参DTO
	 * @param intentionVO 对话意图响应VO
	 * @return 搜索参数
	 */
	private SearchParam createSearchParam(String intention, ChatAddReqDTO dto, DialogueIntentionVO intentionVO) {
		DialogueIntentionEnum intentionEnum = DialogueIntentionEnum.getByCode(intention);
		if (Objects.isNull(intentionEnum)) {
			log.error("SearchServiceImpl-createSearchParam，dialogueIntentionEnum is null");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_SERVER_NOT_READY);
		}
		/** 参数初始化 */
		String userId = dto.getUserId();
		Integer provinceCode = userEtnService.getUserInfoData(Long.parseLong(userId)).getProvinceCode();
		IntentionInfo firstIntentionInfo = intentionVO.getIntentionInfoList().get(0);
		List<IntentionInfo> subIntentions = firstIntentionInfo.getSubIntentions();
		List<IntentEntityVO> entityList = firstIntentionInfo.getEntityList();
		/** 构建搜索参数 */
		SearchParam searchParam = SearchParam.builder().build();
		switch (intentionEnum) {
		/** 搜图片 */
		case SEARCH_IMAGE:
			handleImageCreateParam(searchParam, dto, intentionEnum, entityList, userId);
			break;
		/** 搜文档 */
		case SEARCH_DOCUMENT:
			createSearchFileParam(searchParam, dto, intentionEnum, entityList, false);
			break;
		/** 搜视频 */
		case SEARCH_VIDEO:
			createSearchFileParam(searchParam, dto, intentionEnum, entityList, false);
			break;
		/** 搜音频 */
		case SEARCH_AUDIO:
			createSearchFileParam(searchParam, dto, intentionEnum, entityList, false);
			break;
		/** 搜文件夹 */
		case SEARCH_FOLDER:
			createSearchFileParam(searchParam, dto, intentionEnum, entityList, false);
			break;
		/** 搜笔记 */
		case SEARCH_NOTE:
			createSearchNoteParam(searchParam, dto, intentionEnum, entityList);
			break;
		/** 综合搜索或者泛化意图 */
		case COMPREHENSIVE_SEARCH:
		case OTHER:
			createAllSearchParam(searchParam, dto, intentionEnum, subIntentions, entityList, provinceCode, intentionVO.getAlgorithmIntentionCode());
			break;
		/** 活动搜索 */
		case SEARCH_ACTIVITY:
			handleActivityParamCreate(searchParam, dto, intentionEnum, entityList, provinceCode);
			break;
		/** 功能搜索 */
		case SEARCH_FUNCTION:
			handleFunctionParamCreate(searchParam, dto, intentionEnum, entityList, provinceCode);
			break;
		/** 发现广场搜素 */
		case SEARCH_DISCOVERY:
			handleDiscoveryParamCreate(searchParam, dto, intentionEnum, entityList);
			break;
		/** 圈子搜索 */
		case SEARCH_GROUP:
			createSearchGroupParam(searchParam, dto, intentionEnum, entityList);
			break;
		/** 邮件搜索 邮件附件列表搜索 */
		case SEARCH_MAIL:
			createSearchMailParam(searchParam, dto, intentionEnum, entityList);
			break;
		/** 搜索知识库资源 */
		case SEARCH_KNOWLEDGE_BASE_RESOURCE:
			createSearchKnowledgeBaseResourceParam(searchParam, dto, intentionEnum, entityList);
			break;
		default:
			break;
		}

		return searchParam;
	}

	/**
	 * 处理搜索图片意图创建参数
	 */
	private void handleImageCreateParam(SearchParam searchParam, ChatAddReqDTO reqDTO,
			DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList, String userId) {
		/** 判断用户所属底座 */
		Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();

		// 【老底座】使用个人资产搜索接口搜图
		if (UserBelongsPlatformEnum.OSE.getBelongsPlatform().equals(belongsPlatform)) {
			createSearchFileParam(searchParam, reqDTO, intentionEnum, entityList, false);
			return;
		}

		// 【新底座】查询智能相册报名记录
		boolean albumFlag = aiRegisterService.checkAlbum(userId);
		if (albumFlag) {
			// 调用语义搜图【最新版本只要是新底座已报名就直接调用语义搜图，不需要判断是否存在图片标签 20240913】
			Integer imageSortType = (reqDTO.getDialogueInput().getSortInfo() != null)
					? reqDTO.getDialogueInput().getSortInfo().getImageSortType()
					: null;
			createSearchImageParam(searchParam, reqDTO.getDialogueInput().getDialogue(), imageSortType);
			return;
		}

		// 未报名 使用个人资产搜索接口
		createSearchFileParam(searchParam, reqDTO, intentionEnum, entityList, false);
	}

	/**
	 * 构建所有搜索接口的请求参数（除语义搜图外）
	 *
	 * @Author: WeiJingKun
	 */
	private void createAllSearchParam(SearchParam searchParam, ChatAddReqDTO reqDTO,
			DialogueIntentionEnum intentionEnum, List<IntentionInfo> subIntentions, List<IntentEntityVO> entityList,
			Integer provinceCode, String algorithmIntentionCode) {
		String userId = RequestContextHolder.getUserId();
		/** 判断用户所属底座 */
		Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();
		// 【新底座】查询智能相册报名记录
		boolean albumFlag = aiRegisterService.checkAlbum(userId);
		// 综合搜索：需要判断子意图存在情况，按子意图来搜索，无子意图则搜索全部【2024-09-20修改】
		if (allowSearchByType(DialogueIntentionEnum.SEARCH_NOTE, subIntentions)) {
			try {
				/** 构建笔记搜索接口参数 */
				createSearchNoteParam(searchParam, reqDTO, intentionEnum,
						getEntityListByType(DialogueIntentionEnum.SEARCH_NOTE, subIntentions, entityList));
			} catch (YunAiBusinessException e) {
				log.warn("综合搜索意图下存在语义实体为空的情况,不应该影响【笔记搜索】接口，直接捕获这个异常 createSearchNoteParam entityList:{}, subIntentions:{} ｜ error:",
						JsonUtil.toJson(entityList), JsonUtil.toJson(subIntentions), e);
			}
		}

		// 文档搜索意图列表（4个搜索【老底座或者未报名5个搜索】都是执行一个方法）
		List<DialogueIntentionEnum> fileIntentionEnums = getSearchPersonFileEnums(albumFlag, userId, belongsPlatform);
		boolean findSearchFileParam = false;
		for (DialogueIntentionEnum fileIntentionEnum : fileIntentionEnums) {
			// 综合搜索：判断是否为允许搜索的类型
			boolean allowSearch = DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode().equals(intentionEnum.getCode()) && allowSearchByType(fileIntentionEnum, subIntentions);
			if (intentionEnum.getCode().equals(fileIntentionEnum.getCode()) || allowSearch) {
				try {
					/** 构建文件搜索接口参数 */
					// 子意图包含只有一个搜xxx指定意图，按fileIntentionEnum入参
					DialogueIntentionEnum searchIntentionEnum = hasSearchFileOnlyOne(fileIntentionEnums, subIntentions)
							? fileIntentionEnum
							: intentionEnum;
					// 按具体的子意图搜索
					createSearchFileParamV3(searchParam, reqDTO, searchIntentionEnum, getEntityListByType(fileIntentionEnum, subIntentions, entityList),
							false, fileIntentionEnums, subIntentions);
					// 是否找到搜索文件参数
					findSearchFileParam = (null != searchParam.getSearchFileParam());
				} catch (YunAiBusinessException e) {
					log.warn("综合搜索意图下存在语义实体为空的情况,不应该影响【文档搜索】接口，直接捕获这个异常 createSearchFileParam entityList:{}, subIntentions:{} ｜ error:",
							JsonUtil.toJson(entityList), JsonUtil.toJson(subIntentions), e);
				}
			}
			if (findSearchFileParam) {
				// 找到搜索文件参数则退出
				break;
			}
		}

		if (allowSearchByType(DialogueIntentionEnum.SEARCH_ACTIVITY, subIntentions)) {
			try {
				/** 构建活动搜索接口参数 */
				handleActivityParamCreate(searchParam, reqDTO, intentionEnum,
						getEntityListByType(DialogueIntentionEnum.SEARCH_ACTIVITY, subIntentions, entityList),
						provinceCode);
			} catch (YunAiBusinessException e) {
				log.warn("综合搜索意图下存在语义实体为空的情况,不应该影响【活动搜索】接口，直接捕获这个异常 createSearchActivityParam entityList:{}, subIntentions:{} ｜ error:",
						JsonUtil.toJson(entityList), JsonUtil.toJson(subIntentions), e);
			}
		}

		if (allowSearchByType(DialogueIntentionEnum.SEARCH_FUNCTION, subIntentions)) {
			try {
				/** 构建功能搜索接口参数 */
				handleFunctionParamCreate(searchParam, reqDTO, intentionEnum,
						getEntityListByType(DialogueIntentionEnum.SEARCH_FUNCTION, subIntentions, entityList),
						provinceCode);
			} catch (YunAiBusinessException e) {
				log.warn("综合搜索意图下存在语义实体为空的情况,不应该影响【功能搜索】接口，直接捕获这个异常 createSearchFunctionParam entityList:{}, subIntentions:{} ｜ error:",
						JsonUtil.toJson(entityList), JsonUtil.toJson(subIntentions), e);
			}
		}

		if (allowSearchByType(DialogueIntentionEnum.SEARCH_GROUP, subIntentions)) {
			try {
				/** 构建圈子搜索接口参数 */
				createSearchGroupParam(searchParam, reqDTO, intentionEnum,
						getEntityListByType(DialogueIntentionEnum.SEARCH_GROUP, subIntentions, entityList));
			} catch (YunAiBusinessException e) {
				log.warn("综合搜索意图下存在语义实体为空的情况,不应该影响【圈子搜索】接口，直接捕获这个异常 createSearchGroupParam entityList:{}, subIntentions:{} ｜ error:",
						JsonUtil.toJson(entityList), JsonUtil.toJson(subIntentions), e);
			}
		}

		if (allowSearchByType(DialogueIntentionEnum.SEARCH_MAIL, subIntentions)) {
			try {
				/** 构建邮件搜索接口参数 */
				createSearchMailParam(searchParam, reqDTO, intentionEnum,
						getEntityListByType(DialogueIntentionEnum.SEARCH_MAIL, subIntentions, entityList));
			} catch (YunAiBusinessException e) {
				log.warn("综合搜索意图下存在语义实体为空的情况,不应该影响【邮件搜索】接口，直接捕获这个异常 createSearchMailParam entityList:{}, subIntentions:{} ｜ error:",
						JsonUtil.toJson(entityList), JsonUtil.toJson(subIntentions), e);
			}
		}

		/** 构建图片搜索接口参数， 非老底座并且智能相册报名 */
		// 算法主意图不是018和999的情况下执行语义搜图逻辑
		if (!DialogueIntentionEnum.isComprehensiveIntention(algorithmIntentionCode)) {
			if (allowSearchByType(DialogueIntentionEnum.SEARCH_IMAGE, subIntentions)) {
				if (!UserBelongsPlatformEnum.OSE.getBelongsPlatform().equals(belongsPlatform) && albumFlag) {
					// 【新底座】语义搜图
					Integer imageSortType = (reqDTO.getDialogueInput().getSortInfo() != null)
							? reqDTO.getDialogueInput().getSortInfo().getImageSortType()
							: null;
					try {
						/** 构建图片搜索接口参数 */
						createSearchImageParam(searchParam, reqDTO.getDialogueInput().getDialogue(), imageSortType);
					} catch (YunAiBusinessException e) {
						log.warn("综合搜索意图下存在语义实体为空的情况,不应该影响【图片搜索】接口，直接捕获这个异常 createSearchImageParam entityList:{}, subIntentions:{} ｜ error:",
								JsonUtil.toJson(entityList), JsonUtil.toJson(subIntentions), e);
					}
				}
			}
		}

		if (allowSearchByType(DialogueIntentionEnum.SEARCH_DISCOVERY, subIntentions)) {
			try {
				/** 构建发现广场搜索参数 */
				handleDiscoveryParamCreate(searchParam, reqDTO, intentionEnum,
						getEntityListByType(DialogueIntentionEnum.SEARCH_DISCOVERY, subIntentions, entityList));
			} catch (YunAiBusinessException e) {
				log.warn("综合搜索意图下存在语义实体为空的情况,不应该影响【发现广场搜索】接口，直接捕获这个异常 handleDiscoveryParamCreate entityList:{}, subIntentions:{} ｜ error:",
						JsonUtil.toJson(entityList), JsonUtil.toJson(subIntentions), e);
			}
		}

	}

	/**
	 * 获取个人云搜索枚举列表（老底座或者未报名需要追加搜image）
	 *
	 * @param userId
	 * @param belongsPlatform
	 * @return
	 */
	private List<DialogueIntentionEnum> getSearchPersonFileEnums(boolean albumFlag, String userId,
			Integer belongsPlatform) {
		log.info("getSearchPersonFileEnums userId:{}, albumFlag:{}, belongsPlatform:{}", userId, albumFlag,
				belongsPlatform);
		if (UserBelongsPlatformEnum.OSE.getBelongsPlatform().equals(belongsPlatform) || !albumFlag) {
			return DialogueIntentionEnum.searchPersonOseFileEnums();
		}
		return DialogueIntentionEnum.searchPersonFileEnums();
	}

	/**
	 * 个人云搜索只包含单个搜索XXX的
	 */
	private boolean hasSearchFileOnlyOne(List<DialogueIntentionEnum> fileIntentionEnums,
			List<IntentionInfo> subIntentions) {
		if (CollUtil.isEmpty(subIntentions)) {
			return false;
		}
		int total = 0;
		for (IntentionInfo subIntention : subIntentions) {
			// 个人云搜索包含，后面需要增加个人云搜索类型，枚举则需要添加到DialogueIntentionEnum.searchPersonFiles()方法内
			if (fileIntentionEnums.contains(DialogueIntentionEnum.getByCode(subIntention.getIntention()))) {
				total++;
			}
		}
		// 只存在一个搜索xxx
		return total == 1;
	}

	/**
	 * 构建笔记搜索接口参数
	 *
	 * @Author: WeiJingKun
	 */
	private void createSearchNoteParam(SearchParam searchParam, ChatAddReqDTO reqDTO,
			DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList) {
		// 校验语义结果，数据不可用抛出搜索引导文案业务异常
		handleEntityList(reqDTO, intentionEnum, entityList);

		// 根据【语义实体】处理
		SearchNoteParam searchNoteParam = SearchNoteParam.builder().build();
		List<String> keywords = new ArrayList<>();
		List<MillisecondTimeRange> timeRangeList = new ArrayList<>();
		for (IntentEntityVO intentEntityVO : entityList) {
			// 设置关键字
			setResultList(keywords, intentEntityVO.getMetaDataList());

			// 设置时间
			setTimeRangeList(timeRangeList, intentEntityVO);
		}

		// 笔记要求关键字必填
		if (isEmpty(keywords)) {
			// 综合搜索意图特殊处理
			if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.equals(intentionEnum)) {
				searchParam.setSearchNoteParam(null);
				return;
			}

			// 【笔记搜索】获取引导语，并抛出异常
			promptProperties.getGuideRandomStringAndThrowException(intentionEnum.getInstruction());
		}

		// 获取旧版本默认noteTypeList：0、1、2、3
		List<Integer> defalutNoteTypeList = ListUtil.toList(SearchNoteParam.NoteTypeEnum.TYPE0.getCode(),
				SearchNoteParam.NoteTypeEnum.TYPE1.getCode(), SearchNoteParam.NoteTypeEnum.TYPE2.getCode(),
				SearchNoteParam.NoteTypeEnum.TYPE3.getCode());
		// 客户端版本 < 小天11.3版本
		if (VersionUtil.xtClientLt113()) {
			searchNoteParam.setNoteTypeList(defalutNoteTypeList);
		} else {
			// 新版本端：noteTypeList传0、1、2、3、4
			defalutNoteTypeList.add(SearchNoteParam.NoteTypeEnum.TYPE4.getCode());
			searchNoteParam.setNoteTypeList(defalutNoteTypeList);
		}

		/** set关键字 */
		keywords = keywords.stream().filter(StringUtils::isNotBlank).distinct().limit(10).collect(Collectors.toList());
		searchNoteParam.setKeywords(keywords);

		/** set搜索时间范围列表 */
		timeRangeList = timeRangeList.stream().distinct().collect(Collectors.toList());
		searchNoteParam.setTimeRangeList(timeRangeList);

		/** set笔记搜索接口参数 */
		searchParam.setSearchNoteParam(searchNoteParam);
	}

	/**
	 * 构建个人资产搜索接口参数
	 *
	 * @Author: WeiJingKun
	 */
	private void createSearchFileParam(SearchParam searchParam, ChatAddReqDTO reqDTO, DialogueIntentionEnum intentionEnum,
									   List<IntentEntityVO> entityList, boolean imageNamePriority) {

		List<Integer> typeList = new ArrayList<>();
		SearchFileParamTypeEnum searchFileParamTypeEnum = SearchFileParamTypeEnum
				.getByDialogueIntentionEnum(intentionEnum);
		if (null != searchFileParamTypeEnum) {
			typeList.add(searchFileParamTypeEnum.getCode());
		} else {
			// 默认：综合(全部)
			typeList.add(SearchFileParamTypeEnum.COMPREHENSIVE.getCode());
			log.warn("SearchServiceImpl-createSearchFileParam，searchFileParamTypeEnum is null");
		}

		this.createSearchFileParam(searchParam, reqDTO, intentionEnum, entityList, imageNamePriority, typeList);
	}

	/**
	 * 构建个人资产搜索接口参数
	 *
	 * @Author: WeiJingKun
	 */
	private void createSearchFileParam(SearchParam searchParam, ChatAddReqDTO reqDTO, DialogueIntentionEnum intentionEnum,
									   List<IntentEntityVO> entityList, boolean imageNamePriority, List<Integer> typeList) {
		// 校验语义结果，数据不可用抛出搜索引导文案业务异常
		handleEntityList(reqDTO, intentionEnum, entityList);

		// 根据【语义实体】处理
		SearchFileParam searchFileParam = SearchFileParam.builder().build();

		String sourceChannel = reqDTO.getSourceChannel();
		boolean isMail = SourceChannelsProperties.isMailChannel(sourceChannel);
		boolean isXiaoTian = sourceChannelsProperties.isXiaoTian(sourceChannel);

		List<String> nameList = new ArrayList<>();
		List<SecondTimeRange> fileTimeRangeList = new ArrayList<>();
		List<String> thingList = new ArrayList<>();
		List<String> suffixList = new ArrayList<>();
		for (IntentEntityVO intentEntityVO : entityList) {
			// 关键字列表，添加实体数据
			setResultList(nameList, intentEntityVO.getMetaDataList());
			// 关键字列表，添加人名列表数据
			setResultList(nameList, intentEntityVO.getPersonList());

			// 处理搜索图片意图
			handleSearchImage(intentionEnum, imageNamePriority, nameList, thingList, intentEntityVO);

			// 设置时间
			setSecondTimeRangeList(fileTimeRangeList, intentEntityVO);

			/**
			 * 1、h5Version >= 小天1.3.0版本 2、文档后缀映射 3、设置后缀名, 云邮搜素支持指定后缀名
			 */
			if (VersionUtil.xtH5VersionGte130(null) || isMail) {
				suffixList.addAll(searchSuffixProperties.mapingSuffixList(intentEntityVO.getSuffixList()));
				// 文档搜索默认每页50条
				searchFileParam.getPageInfo().setPageSize(50);
			}
		}

		/** set文件名称集合-最大支持10条 */
		searchFileParam.setNameList(nameList.stream().filter(StringUtils::isNotBlank).distinct().limit(10).collect(Collectors.toList()));

		/** set事物标签集合-最大支持10条 */
		thingList = thingList.stream().filter(StringUtils::isNotBlank).distinct().limit(10)
				.collect(Collectors.toList());
		searchFileParam.setThingList(thingList);

		/** set搜索时间范围列表 */
		fileTimeRangeList = fileTimeRangeList.stream().distinct().collect(Collectors.toList());
		searchFileParam.setTimeList(fileTimeRangeList);

		/** set地理标签集合-最大支持10条 */
		searchFileParam.setAddressList(getAddressList(entityList));

		/** set后缀名集合-最大支持10条 */
		if (CollUtil.isNotEmpty(suffixList)) {
			suffixList = suffixList.stream().filter(StringUtils::isNotBlank).distinct().limit(10).collect(Collectors.toList());
			searchFileParam.setSuffixList(suffixList);

			// 添加文件后缀名集合到关键字列表（使用 Optional 类简化空值判断）
			List<String> tempNameList = Optional.ofNullable(searchFileParam.getNameList()).orElseGet(ArrayList::new);
			tempNameList.addAll(suffixList);
			searchFileParam.setNameList(tempNameList);
		}

		/** set类别 */
		// 小天渠道 && 客户端版本 < 12.0.3
		if (isXiaoTian && VersionUtil.xtClientLt1203()) {
			if(CollUtil.isNotEmpty(typeList)){
				// typeList > 1，则搜综合，否则搜第一个type
				searchFileParam.setType(typeList.size() > NumberEnum.ONE.getValue() ? SearchFileParamTypeEnum.COMPREHENSIVE.getCode() : typeList.get(0));
			} else {
				searchFileParam.setType(SearchFileParamTypeEnum.COMPREHENSIVE.getCode());
			}
		} else {
			searchFileParam.setTypeList(typeList);
		}

		/** 是否打开文档全文检索 */
		// 小天渠道 && 客户端版本 >= 12.0.3：【关闭】文档全文检索
		if(isXiaoTian && !VersionUtil.xtClientLt1203()){
			searchFileParam.setEnableFullTextSearch(false);
		}

		/** set个人资产搜索接口参数 */
		searchParam.setSearchFileParam(searchFileParam);
	}

	/**
	 * 构建个人资产搜索V3接口参数
	 *
	 * @Author: WeiJingKun
	 */
	private void createSearchFileParamV3(SearchParam searchParam, ChatAddReqDTO reqDTO, DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList,
										 boolean imageNamePriority, List<DialogueIntentionEnum> fileIntentionEnums, List<IntentionInfo> subIntentions) {
		log.info("构建个人资产搜索V3接口参数");

		List<Integer> typeList = getSearchFileParamTypeList(fileIntentionEnums, subIntentions);
		typeList = CollUtil.isNotEmpty(typeList) ? typeList : ListUtil.toList(SearchFileParamTypeEnum.COMPREHENSIVE.getCode());

		// 构建个人资产搜索接口参数
		this.createSearchFileParam(searchParam, reqDTO, intentionEnum, entityList, imageNamePriority, typeList);
	}

	/**
	 * 构建文件搜索参数类型集合
	 * @Author: WeiJingKun
	 *
	 * @param fileIntentionEnums 个人资产搜索意图枚举列表
	 * @param subIntentions 意图识别结果子意图列表
	 * @return 文件搜索参数类型集合
	 */
	private List<Integer> getSearchFileParamTypeList(List<DialogueIntentionEnum> fileIntentionEnums, List<IntentionInfo> subIntentions) {
		List<Integer> searchFileParamTypeList = new ArrayList<>();
		if(CollUtil.isNotEmpty(subIntentions)){
			for(IntentionInfo subIntention : subIntentions){
				for (DialogueIntentionEnum fileIntentionEnum : fileIntentionEnums) {
					if (fileIntentionEnum.getCode().equals(subIntention.getIntention())) {
						searchFileParamTypeList.add(SearchFileParamTypeEnum.getByDialogueIntentionEnum(fileIntentionEnum).getCode());
					}
				}
			}
		}
		return searchFileParamTypeList;
	}

	/**
	 * 公共关键字过滤
	 * @Author: WeiJingKun
	 *
	 * @param keywords 关键字列表
	 * @return 关键字列表
	 */
	private List<String> filterCommonKeywords(String logPrefix, List<String> keywords) {
		// 复制一份keywords
		List<String> tempKeywords = new ArrayList<>(keywords);
		log.info("{}，关键字列表过滤前：{}", logPrefix, JSON.toJSONString(tempKeywords));
		// 公共关键字过滤
		tempKeywords = searchParamProperties.filterCommonKeywords(tempKeywords);
		log.info("{}，关键字列表过滤后：{}", logPrefix, JSON.toJSONString(tempKeywords));
		if(CollUtil.isEmpty(tempKeywords)){
			log.info("{}，关键字列表过滤后，没有关键字，使用过滤前的：{}", logPrefix, JSON.toJSONString(keywords));
			tempKeywords = keywords;
		}
		return tempKeywords;
	}

	/**
>>>>>>> refs/remotes/origin/*******.feature
	 * 获取地理标签集合
	 */
	private List<String> getAddressList(List<IntentEntityVO> entityList) {
		return entityList.stream().filter(intentEntityVO -> !isEmpty(intentEntityVO.getPlaceList()))
				.flatMap(intentEntityVO -> intentEntityVO.getPlaceList().stream()).distinct()
				.filter(StringUtils::isNotBlank).limit(10).collect(Collectors.toList());
	}

	/**
	 * 处理搜索图片意图
	 *
	 * @param intentionEnum     意图枚举
	 * @param imageNamePriority 是否图片名称标签优先
	 * @param nameList          名称标签列表
	 * @param thingList         事物标签列表
	 * @param intentEntityVO    语义识别结果VO
	 */
	private void handleSearchImage(DialogueIntentionEnum intentionEnum, boolean imageNamePriority,
			List<String> nameList, List<String> thingList, IntentEntityVO intentEntityVO) {
		// 非搜索图片意图不需要处理
		if (!DialogueIntentionEnum.SEARCH_IMAGE.equals(intentionEnum)) {
			return;
		}

		// 图片名称标签搜索优先 加入图片标签列表
		if (imageNamePriority) {
			nameList.clear();
			nameList.addAll(intentEntityVO.getImageNameList());
			return;
		}

		// 事物标签列表存在数据，设置事务标签
		if (!isEmpty(intentEntityVO.getLabelList())) {
			setResultList(thingList, intentEntityVO.getLabelList());
			// 事物标签优先级最高,清空nameList
			nameList.clear();
			return;
		}

		// 事物标签不存在数据，图片名称标签列表存在数据，加入图片名称标签列表
		if (!isEmpty(intentEntityVO.getImageNameList())) {
			nameList.addAll(intentEntityVO.getImageNameList());
		}
	}

	/**
	 * 构建语义搜图接口参数
	 *
	 * @Author: WeiJingKun
	 */
	private void createSearchImageParam(SearchParam searchParam, String dialogue, Integer sortType) {
		SearchImageParam searchImageParam = SearchImageParam.builder().build();

		/** set关键字 */
		searchImageParam.setText(dialogue);

		/** set排序方式 */
		searchImageParam.setSortType(sortType != null ? sortType : ImageSortTypeEnum.CORRELATION_SORT.getCode());

		/** set语义搜图接口参数 */
		searchParam.setSearchImageParam(searchImageParam);
	}

	/**
	 * 获取对话意图响应VO
	 *
	 * @param dto    会话输入入参DTO
	 * @param result 会话输入返回结果VO
	 * @return 对话意图响应VO
	 */
	@Override
	public DialogueIntentionVO getIntentionVO(ChatAddReqDTO dto, ChatAddRespVO result) {
		DialogueIntentionEntity.DialogueInfo currentDialogueInfo = new DialogueIntentionEntity.DialogueInfo();
		String prompt = dto.getDialogueInput().getPrompt();
		if (CharSequenceUtil.isNotEmpty(prompt)) {
			currentDialogueInfo.setDialogue(prompt.concat(dto.getDialogueInput().getDialogue()));
		} else {
			currentDialogueInfo.setDialogue(dto.getDialogueInput().getDialogue());
		}

		currentDialogueInfo.setTimestamp(dto.getDialogueInput().getInputTime());
		return intentionService.getDialogueIntentionVO(dto.getSourceChannel(), result.getSessionId(),
				result.getDialogueId(), dto.getUserId(), currentDialogueInfo, dto.getDialogueInput().isEnableAiSearch());
	}







	/**
	 * 设置时间范围列表（秒），长度14位
	 *
	 * @param secondTimeRangeList 时间范围列表（秒）
	 * @param intentEntityVO      实体识别结果VO
	 */
	private void setSecondTimeRangeList(List<SecondTimeRange> secondTimeRangeList, IntentEntityVO intentEntityVO) {
		List<String> timeList = intentEntityVO.getTimeList();
		if (isEmpty(timeList)) {
			return;
		}
		List<MillisecondTimeRange> timeRangeList = TimeRangeUtils.convertTimeToRanges(timeList);
		timeRangeList.forEach(timeRange -> {
			SecondTimeRange secondTimeRange = SecondTimeRange.builder()
					.startAt(CharSequenceUtil.sub(timeRange.getStartAt(), 0, 14))
					.endAt(CharSequenceUtil.sub(timeRange.getEndAt(), 0, 14)).build();
			secondTimeRangeList.add(secondTimeRange);
		});
	}

	/**
	 * 设置时间范围列表
	 *
	 * @param timeRangeList  时间范围列表
	 * @param intentEntityVO 实体识别结果VO
	 */
	private void setTimeRangeList(List<MillisecondTimeRange> timeRangeList, IntentEntityVO intentEntityVO) {
		List<String> timeList = intentEntityVO.getTimeList();
		if (isEmpty(timeList)) {
			return;
		}

		timeRangeList.addAll(TimeRangeUtils.convertTimeToRanges(timeList));
	}

	@Override
	public void setResultList(List<String> resultList, List<KeyValueVO> keyValueVoList) {
		if (isEmpty(keyValueVoList)) {
			return;
		}

		// 处理resultList
		keyValueVoList.stream().map(KeyValueVO::getValue).filter(Objects::nonNull).forEach(resultList::addAll);
	}

	/**
	 * 处理活动搜索参数创建
	 *
	 * @Author: WeiJingKun
	 */
	private void handleActivityParamCreate(SearchParam searchParam, ChatAddReqDTO dtoDTO,
										   DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList, Integer provinceCode) {
		// h5Version < 小天2.0.3版本，创建功能搜索V1接口参数
		if (VersionUtil.xtH5VersionLt203(null)) {
			log.info("handleDiscoveryParamCreate，h5Version < 小天2.0.3版本，创建活动搜索V1接口参数");
			createSearchActivityParam(searchParam, dtoDTO, intentionEnum, entityList, provinceCode);
		} else {
			// h5Version >= 小天2.0.3版本，创建功能搜索V2接口参数
			log.info("handleDiscoveryParamCreate，h5Version >= 小天2.0.3版本，创建活动搜索V2接口参数");
			createSearchActivityParamV2(searchParam, dtoDTO, intentionEnum, entityList, provinceCode);
		}
	}

	/**
	 * 构建活动搜索参数
	 *
	 * <AUTHOR>
	 * @date 2024-6-3 11:11
	 */
	private void createSearchActivityParam(SearchParam searchParam, ChatAddReqDTO reqDTO,
			DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList, Integer provinceCode) {
		// 校验语义结果，数据不可用抛出搜索引导文案业务异常
		handleEntityList(reqDTO, intentionEnum, entityList);

		// 根据【语义实体】处理
		SearchActivityParam searchActivityParam = SearchActivityParam.builder().build();
		List<String> keywords = new ArrayList<>();
		for (IntentEntityVO intentEntityVO : entityList) {
			setResultList(keywords, intentEntityVO.getMetaDataList());
		}

		keywords = keywords.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
		if (isEmpty(keywords)) {
			// 综合搜索意图特殊处理
			if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.equals(intentionEnum)) {
				searchParam.setSearchActivityParam(null);
				return;
			}

			// 【活动搜索】获取引导语，并抛出异常
			promptProperties.getGuideRandomStringAndThrowException(intentionEnum.getInstruction());
		}

		// 活动搜索接口不支持数组提供关键字
		searchActivityParam.setKeywords(keywords.get(0));
		searchActivityParam.setProvinceCode(provinceCode);

		/** set活动搜索接口参数 */
		searchParam.setSearchActivityParam(searchActivityParam);
	}

	/**
	 * 构建活动搜索参数
	 *
	 * <AUTHOR>
	 * @date 2024-6-3 11:11
	 */
	private void createSearchActivityParamV2(SearchParam searchParam, ChatAddReqDTO reqDTO,
										   DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList, Integer provinceCode) {
		// 校验语义结果，数据不可用抛出搜索引导文案业务异常
		handleEntityList(reqDTO, intentionEnum, entityList);

		// 根据【语义实体】处理
		SearchActivityParam searchActivityParam = SearchActivityParam.builder().build();
		List<String> keywords = new ArrayList<>();
		for (IntentEntityVO intentEntityVO : entityList) {
			setResultList(keywords, intentEntityVO.getMetaDataList());
		}

		keywords = keywords.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
		if (isEmpty(keywords)) {
			// 综合搜索意图特殊处理
			if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.equals(intentionEnum)) {
				searchParam.setSearchActivityParam(null);
				return;
			}

			// 【活动搜索】获取引导语，并抛出异常
			promptProperties.getGuideRandomStringAndThrowException(intentionEnum.getInstruction());
		}

		// 活动搜索接口不支持数组提供关键字
		searchActivityParam.setKeywordList(keywords.stream().filter(CharSequenceUtil::isNotBlank).distinct().limit(10).collect(Collectors.toList()));
		searchActivityParam.setProvinceCode(provinceCode);

		/** set活动搜索接口参数 */
		searchParam.setSearchActivityParam(searchActivityParam);
	}

	/**
	 * 处理功能搜索参数创建
	 *
	 * @Author: WeiJingKun
	 */
	private void handleFunctionParamCreate(SearchParam searchParam, ChatAddReqDTO dtoDTO,
										   DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList, Integer provinceCode) {
		// h5Version < 小天2.0.3版本，创建功能搜索V1接口参数
		if (VersionUtil.xtH5VersionLt203(null)) {
			log.info("handleDiscoveryParamCreate，h5Version < 小天2.0.3版本，创建功能搜索V1接口参数");
			createSearchFunctionParam(searchParam, dtoDTO, intentionEnum, entityList, provinceCode);
		} else {
			// h5Version >= 小天2.0.3版本，创建功能搜索V2接口参数
			log.info("handleDiscoveryParamCreate，h5Version >= 小天2.0.3版本，创建功能搜索V2接口参数");
			createSearchFunctionParamV2(searchParam, dtoDTO, intentionEnum, entityList, provinceCode);
		}
	}

	/**
	 * 构建功能搜索参数
	 *
	 * <AUTHOR>
	 * @date 2024-6-3 11:11
	 */
	private void createSearchFunctionParam(SearchParam searchParam, ChatAddReqDTO dtoDTO,
			DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList, Integer provinceCode) {
		// 校验语义结果，数据不可用抛出搜索引导文案业务异常
		handleEntityList(dtoDTO, intentionEnum, entityList);

		// 根据【语义实体】处理
		SearchFunctionParam searchFunctionParam = SearchFunctionParam.builder().build();
		List<String> keywords = new ArrayList<>();
		for (IntentEntityVO intentEntityVO : entityList) {
			setResultList(keywords, intentEntityVO.getMetaDataList());
		}

		keywords = keywords.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
		if (isEmpty(keywords)) {
			// 综合搜索意图特殊处理
			if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.equals(intentionEnum)) {
				searchParam.setSearchFunctionParam(null);
				return;
			}

			// 【功能搜索】获取引导语，并抛出异常
			promptProperties.getGuideRandomStringAndThrowException(intentionEnum.getInstruction());
		}

		// 功能搜索接口不支持数组提供关键字
		searchFunctionParam.setKeywords(keywords.get(0));
		searchFunctionParam.setProvinceCode(provinceCode);

		/** set功能搜索接口参数 */
		searchParam.setSearchFunctionParam(searchFunctionParam);
	}

	/**
	 * 构建功能搜索参数
	 *
	 * <AUTHOR>
	 * @date 2024-6-3 11:11
	 */
	private void createSearchFunctionParamV2(SearchParam searchParam, ChatAddReqDTO dtoDTO,
										   DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList, Integer provinceCode) {
		// 校验语义结果，数据不可用抛出搜索引导文案业务异常
		handleEntityList(dtoDTO, intentionEnum, entityList);

		// 根据【语义实体】处理
		SearchFunctionParam searchFunctionParam = SearchFunctionParam.builder().build();
		List<String> keywords = new ArrayList<>();
		for (IntentEntityVO intentEntityVO : entityList) {
			setResultList(keywords, intentEntityVO.getMetaDataList());
		}

		keywords = keywords.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
		if (isEmpty(keywords)) {
			// 综合搜索意图特殊处理
			if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.equals(intentionEnum)) {
				searchParam.setSearchFunctionParam(null);
				return;
			}

			// 【功能搜索】获取引导语，并抛出异常
			promptProperties.getGuideRandomStringAndThrowException(intentionEnum.getInstruction());
		}

		// 功能搜索V2接口最多支持10个关键字
		searchFunctionParam.setKeywordList(keywords.stream().filter(CharSequenceUtil::isNotBlank).distinct().limit(10).collect(Collectors.toList()));
		searchFunctionParam.setProvinceCode(provinceCode);

		/** set功能搜索接口参数 */
		searchParam.setSearchFunctionParam(searchFunctionParam);
	}

	/**
	 * 处理发现广场搜索参数创建
	 * @Author: WeiJingKun
	 */
	private void handleDiscoveryParamCreate(SearchParam searchParam, ChatAddReqDTO reqDTO,
			DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList) {
		// h5Version < 小天1.5.1版本，创建发现广场搜索V1接口参数
		if (VersionUtil.xtH5VersionLt151(null)) {
			log.info("handleDiscoveryParamCreate，h5Version < 小天1.5.1版本，创建发现广场搜索V1接口参数");
			createSearchDiscoveryParam(searchParam, reqDTO, entityList);
		} else {
			// h5Version >= 小天1.5.1版本，创建发现广场搜索V2接口参数
			log.info("handleDiscoveryParamCreate，h5Version >= 小天1.5.1版本，创建发现广场搜索V2接口参数");
			createSearchDiscoveryParamV2(searchParam, reqDTO, intentionEnum, entityList);
		}
	}

	/**
	 * 构建发现广场搜索参数V2
	 *
	 * @Author: WeiJingKun
	 */
	private void createSearchDiscoveryParamV2(SearchParam searchParam, ChatAddReqDTO reqDTO,
			DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList) {
		// 校验语义结果，数据不可用抛出搜索引导文案业务异常
		handleEntityList(reqDTO, intentionEnum, entityList);

		/** 根据【语义实体】处理 */
		SearchDiscoveryParam searchDiscoveryParam = SearchDiscoveryParam.builder().build();
		List<String> keywords = new ArrayList<>();
		List<String> queryTypeDescList = new ArrayList<>();
		for (IntentEntityVO intentEntityVO : entityList) {
			// 关键字列表，添加实体数据
			setResultList(keywords, intentEntityVO.getMetaDataList());
			// 关键字列表，添加人名列表数据
			setResultList(keywords, intentEntityVO.getPersonList());
			// 关键字列表，添加地点数据
			keywords.addAll(intentEntityVO.getPlaceList());
			// 搜索类型，添加内容类型数据
			setResultList(queryTypeDescList, intentEntityVO.getLabelList());
		}

		/** 需要先获取到keywords，再获取扩词数据 */
		List<String> expandWordList = getExpandWord(reqDTO, keywords, entityList);
		// 关键字列表，添加扩词
		keywords.addAll(expandWordList);

		/** 搜索类型处理 */
		List<Integer> queryTypeList = searchDiscoveryParamV2HandleQueryTypeList(queryTypeDescList);

		/** 关键字列表过滤 */
		keywords = searchDiscoveryParamV2FilterKeywords(keywords, queryTypeList);

		/** 去空，去重，判断必填参数 */
		keywords = keywords.stream().filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.toList());
		if (CollUtil.isEmpty(keywords)) {
			// 综合搜索意图特殊处理
			if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.equals(intentionEnum)) {
				searchParam.setSearchDiscoveryParam(null);
				return;
			}

			// 【发现广场搜索】获取引导语，并抛出异常
			promptProperties.getGuideRandomStringAndThrowException(intentionEnum.getInstruction());
		}

		/** set内容类型 */
		searchDiscoveryParam.setQueryTypes(queryTypeList);

		/** set搜索关键字列表 */
		searchDiscoveryParam.setKeywords(keywords);

		/** set搜索类型 */
		if(CollUtil.isNotEmpty(expandWordList)){
			// 如果有扩词，进行2-扩词搜索
			searchDiscoveryParam.setSearchType(SearchDiscoveryParamSearchTypeEnum.EXPAND_WORD_SEARCH.getCode());
		}

		/** set发现广场搜索接口参数 */
		searchParam.setSearchDiscoveryParam(searchDiscoveryParam);
	}

	/**
	 * 获取扩词数据
	 * @Author: WeiJingKun
	 *
	 * @param reqDTO 请求参数
	 * @param keywords 关键字数据
	 * @param entityList 实体数据
	 * @return 扩词数据
	 */
	@NotNull
	private List<String> getExpandWord(ChatAddReqDTO reqDTO, List<String> keywords, List<IntentEntityVO> entityList) {
		List<String> expandWordList = new ArrayList<>();

		String sourceChannel = reqDTO.getSourceChannel();
		boolean isXiaoTian = sourceChannelsProperties.isXiaoTian(sourceChannel);
		// 小天渠道 && 客户端版本 >= 12.1.0，才进行扩词
		if (isXiaoTian && !VersionUtil.xtClientLt1210()) {
			/** 意图识别实体的personList结构转换 */
			Map<String, List<String>> expandMap = transformPersonList(entityList);

			/** 调用扩词方法，获取扩词 */
			if(MapUtil.isNotEmpty(expandMap)){
				expandMap.forEach((name, typeList) -> {
					// 获取扩词
					List<String> oneNameList = new ArrayList<>();
					for(String type : typeList){
						List<String> resourceList = personResourceHandleService.getResourceList(name, type);
						CollUtil.addAll(oneNameList, resourceList);
					}
					// 扩词【不包含】任意一个关键字，添加扩词
					if(!CollUtil.containsAny(oneNameList, keywords)){
						CollUtil.addAll(expandWordList, oneNameList);
					}
				});
			}
			log.info("【发现广场参数构建】【获取扩词数据】：{}", JSON.toJSONString(expandWordList));
		} else {
			log.info("【发现广场参数构建】【获取扩词数据】小天渠道 && 客户端版本 >= 12.1.0，才进行扩词");
		}

		return expandWordList;
	}

	/**
	 * 意图识别实体的personList结构转换
	 * 原结构，[{"key": "主演", "value": ["刘德华"]}, {"key": "导演", "value": ["刘德华"]}]
	 * 转换为，{"刘德华": ["主演", "导演"]}
	 * @Author: WeiJingKun
	 *
	 * @param entityList 实体数据
	 * @return 转换后数据，例如，{"刘德华": ["主演", "导演"]}
	 */
	@NotNull
	private Map<String, List<String>> transformPersonList(List<IntentEntityVO> entityList) {
		Map<String, List<String>> expandMap = new HashMap<>(Const.NUM_16);
		for (IntentEntityVO entity : entityList) {
			List<KeyValueVO> personList = entity.getPersonList();
			if (CollUtil.isEmpty(personList)) {
				continue;
			}
			/**
			 * 原结构，[{"key": "主演", "value": ["刘德华"]}, {"key": "导演", "value": ["刘德华"]}]
			 * 转换为，{"刘德华": ["主演", "导演"]}
			 */
			for (KeyValueVO person : personList) {
				String personKey = person.getKey();
				List<String> personValueList = person.getValue();
				// key和value都不能为空
				if (CharSequenceUtil.isBlank(personKey) || CollUtil.isEmpty(personValueList)) {
					continue;
				}
				for (String personValue : personValueList) {
					/**
					 * computeIfAbsent(personValue, k -> new ArrayList<>())
					 * 如果 expandMap 中 没有键等于 personValue，则使用 lambda 表达式 k -> new ArrayList<>() 创建一个新的 ArrayList 并放入 resultMap
					 * 如果已经存在该键，则直接返回对应的 List<String>
					 */
					expandMap.computeIfAbsent(personValue, k -> new ArrayList<>()).add(personKey);
				}
			}
		}
		return expandMap;
	}

	/**
	 * 构建发现广场搜索参数V2-关键字列表过滤
	 *
	 * @Author: WeiJingKun
	 *
	 * @param keywords      关键字列表
	 * @param queryTypeList 搜索类型列表
	 * @return 关键字列表
	 */
	private List<String> searchDiscoveryParamV2FilterKeywords(List<String> keywords, List<Integer> queryTypeList) {
		log.info("searchDiscoveryParamV2FilterKeywords，关键字列表过滤前：{}", JSON.toJSONString(keywords));
		List<SearchParamProperties.ParamFilter> queryTypesFilterList = searchParamProperties
				.getParamFilterList(SearchTypeEnum.DISCOVERY.getSearchType(), SearchParamProperties.ParamFilter.QUERY_TYPES);
		if (CollUtil.isNotEmpty(queryTypesFilterList)) {
			for (Integer queryType : queryTypeList) {
				for (SearchParamProperties.ParamFilter queryTypesFilter : queryTypesFilterList) {
					// 查全部或类型匹配，则过滤关键字
					if (SearchDiscoveryParamQueryTypeEnum.ALL.getCode().equals(queryType)
							|| queryTypesFilter.getType().equals(queryType.toString())) {
						keywords = queryTypesFilter.filterKeywords(keywords);
					}
				}
			}
		}
		log.info("searchDiscoveryParamV2FilterKeywords，关键字列表过滤后：{}", JSON.toJSONString(keywords));
		return keywords;
	}

	@Override
	@NotNull
	public List<Integer> searchDiscoveryParamV2HandleQueryTypeList(List<String> queryTypeDescList) {
		// 搜索类型转换成数字
		List<Integer> queryTypeList = new ArrayList<>();
		queryTypeDescList = queryTypeDescList.stream().filter(CharSequenceUtil::isNotBlank).distinct()
				.collect(Collectors.toList());
		if (CollUtil.isNotEmpty(queryTypeDescList)) {
			for (String queryTypeDesc : queryTypeDescList) {
				SearchDiscoveryParamQueryTypeEnum queryTypeEnum = SearchDiscoveryParamQueryTypeEnum
						.getByKeyReturnDefault(queryTypeDesc);
				if (ObjectUtil.isNotNull(queryTypeEnum)) {
					queryTypeList.add(queryTypeEnum.getCode());
				}
			}
		}
		// 搜索类型去重
		queryTypeList = queryTypeList.stream().filter(ObjUtil::isNotNull).distinct().collect(Collectors.toList());
		// 没有值，则默认全部
		if (CollUtil.isEmpty(queryTypeList)) {
			queryTypeList.add(SearchDiscoveryParamQueryTypeEnum.ALL.getCode());
		}
		return queryTypeList;
	}

	/**
	 * 构建发现广场搜索参数
	 *
	 * @Author: WeiJingKun
	 */
	private void createSearchDiscoveryParam(SearchParam searchParam, ChatAddReqDTO reqDTO,
			List<IntentEntityVO> entityList) {
		// 根据【语义实体】处理
		SearchDiscoveryParam searchDiscoveryParam = SearchDiscoveryParam.builder().build();
		List<String> keywords = new ArrayList<>();
		for (IntentEntityVO intentEntityVO : entityList) {
			setResultList(keywords, intentEntityVO.getMetaDataList());
			// 关键字列表，添加地点数据
			keywords.addAll(intentEntityVO.getPlaceList());
		}

		keywords = keywords.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
		if (CollUtil.isEmpty(keywords)) {
			// 意图识别不到关键字，把用户的输入作为搜索关键字
			keywords.add(reqDTO.getDialogueInput().getDialogue());
		}

		/** set搜索关键字 */
		searchDiscoveryParam.setKeyword(CollUtil.join(keywords, ""));

		/** set发现广场搜索接口参数 */
		searchParam.setSearchDiscoveryParam(searchDiscoveryParam);
	}

	/**
	 * 构建圈子搜索参数（我的圈子 + 热门圈子）
	 *
	 * @Author: WeiJingKun
	 */
	private void createSearchGroupParam(SearchParam searchParam, ChatAddReqDTO reqDTO,
			DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList) {
		// h5Version < 小天1.4.0版本，不进行圈子搜索
		if (VersionUtil.xtH5VersionLt140()) {
			log.info("createSearchGroupParam，h5Version < 小天1.4.0版本，不进行圈子搜索");
			return;
		}
		// 构建我的圈子搜索参数
		createSearchMyGroupParam(searchParam, reqDTO, intentionEnum, entityList);
		// 构建热门圈子搜索参数
		createSearchRecommendGroupParam(searchParam, reqDTO, intentionEnum, entityList);
	}

	/**
	 * 构建我的圈子搜索参数
	 *
	 * @Author: WeiJingKun
	 */
	private void createSearchMyGroupParam(SearchParam searchParam, ChatAddReqDTO reqDTO,
			DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList) {
		// 校验语义结果，数据不可用抛出搜索引导文案业务异常
		handleEntityList(reqDTO, intentionEnum, entityList);

		// 根据【语义实体】处理
		SearchMyGroupParam searchMyGroupParam = SearchMyGroupParam.builder().build();
		List<String> keywords = new ArrayList<>();
		for (IntentEntityVO intentEntityVO : entityList) {
			setResultList(keywords, intentEntityVO.getMetaDataList());
		}

		keywords = keywords.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
		if (CollUtil.isEmpty(keywords)) {
			// 综合搜索意图特殊处理
			if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.equals(intentionEnum)) {
				searchParam.setSearchMyGroupParam(null);
				return;
			}

			// 【圈子搜索】获取引导语，并抛出异常
			promptProperties.getGuideRandomStringAndThrowException(intentionEnum.getInstruction());
		}

		/** set搜索关键字 */
		searchMyGroupParam.setKeyword(CollUtil.join(keywords, ""));

		/** set我的圈子搜索接口参数 */
		searchParam.setSearchMyGroupParam(searchMyGroupParam);
	}

	/**
	 * 构建热门圈子搜索参数
	 *
	 * @Author: WeiJingKun
	 */
	private void createSearchRecommendGroupParam(SearchParam searchParam, ChatAddReqDTO reqDTO,
			DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList) {
		// 校验语义结果，数据不可用抛出搜索引导文案业务异常
		handleEntityList(reqDTO, intentionEnum, entityList);

		// 根据【语义实体】处理
		SearchRecommendGroupParam searchRecommendGroupParam = SearchRecommendGroupParam.builder().build();
		List<String> keywords = new ArrayList<>();
		for (IntentEntityVO intentEntityVO : entityList) {
			setResultList(keywords, intentEntityVO.getMetaDataList());
		}

		keywords = keywords.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
		if (CollUtil.isEmpty(keywords)) {
			// 综合搜索意图特殊处理
			if (DialogueIntentionEnum.COMPREHENSIVE_SEARCH.equals(intentionEnum)) {
				searchParam.setSearchRecommendGroupParam(null);
				return;
			}

			// 【圈子搜索】获取引导语，并抛出异常
			promptProperties.getGuideRandomStringAndThrowException(intentionEnum.getInstruction());
		}

		/** set搜索关键字 */
		searchRecommendGroupParam.setKeyword(CollUtil.join(keywords, ""));

		/** set热门圈子搜索接口参数 */
		searchParam.setSearchRecommendGroupParam(searchRecommendGroupParam);
	}

	/**
	 * 构建邮件搜索参数
	 *
	 * @Author: WeiJingKun
	 */
	private void createSearchMailParam(SearchParam searchParam, ChatAddReqDTO reqDTO,
									   DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList) {
		// 校验语义结果，数据不可用抛出搜索引导文案业务异常
		handleEntityList(reqDTO, intentionEnum, entityList);

		// 根据【语义实体】处理
		SearchMailParam searchMailParam = SearchMailParam.builder().build();
		//如果是邮件搜索，且意图中有附件相关实体，则需要设置附件搜索参数
		SearchMailAttachmentParam searchMailAttachmentParam = SearchMailAttachmentParam.builder().build();

		// 初始化请求参数
		List<String> fromList = new ArrayList<>();
		Integer read = null;
		List<MillisecondTimeRange> receivedDateList = new ArrayList<>();
		// 指定收件人列表
		List<String> recipientList = new ArrayList<>();
		//附件后缀数组，docx,pdf....， or 关系
		List<String> suffixList = new ArrayList<>();
		//邮件标题
		List<String> titleList = new ArrayList<>();
		// 邮件内容列表,用,隔开
		List<String> contentList = new ArrayList<>();
		// 邮件附件名称，多个的情况下只取第一个
		List<String> attachNameList = new ArrayList<>();
		//邮件类型 0-普通邮件 1-重要邮件
		Integer mailType = null;

		// 遍历语义实体，构建请求参数
		for (IntentEntityVO intentEntityVO : entityList) {
			/**
			 * 指定发件人列表 支持多个（发件人名称和发件人地址）搜索
			 */
			if (CollUtil.isNotEmpty(intentEntityVO.getSenderList())) {
				CollUtil.addAll(fromList, intentEntityVO.getSenderList());
			}
			//emailAddressList算法侧预计20250525废弃
//			if (CollUtil.isNotEmpty(intentEntityVO.getEmailAddressList())) {
//				CollUtil.addAll(fromList, intentEntityVO.getEmailAddressList());
//			}

			//收件人需要等意图识别支持
			if (CollUtil.isNotEmpty(intentEntityVO.getRecipientList())) {
				CollUtil.addAll(recipientList, intentEntityVO.getRecipientList());
			}
			List<KeyValueVO> metaDataList = intentEntityVO.getMetaDataList();
			// 防止metaDataList为null导致空指针异常
			if (metaDataList == null) {
				metaDataList = new ArrayList<>();
			}
			// 邮件标题只取一个，等后续支持全文检索后再多个搜索
			if (CollUtil.isNotEmpty(intentEntityVO.getTitleList())) {
				CollUtil.addAll(titleList, intentEntityVO.getTitleList());
			} else {
				// 如果没有标题，则使用意图实体内容作为标题
//				CollUtil.addAll(titleList, metaDataList.stream().map(KeyValueVO::getValue).collect(Collectors.toList()));
				metaDataList.forEach(keyValueVO -> {
					if (CollUtil.isNotEmpty(keyValueVO.getValue())) {
						CollUtil.addAll(titleList, keyValueVO.getValue());
					}
				});
			}
			//正文搜索，用的是提取出来的关键字，而不是正文内容
			if (CollUtil.isNotEmpty(metaDataList)) {
				CollUtil.addAll(contentList, metaDataList.stream().map(KeyValueVO::getValue).collect(Collectors.toList()));
			}
			// 附件名称搜索，只取第一个，等后续支持全文检索后再多个搜索
			if (CollUtil.isNotEmpty(intentEntityVO.getAttachmentList())) {
				CollUtil.addAll(attachNameList, intentEntityVO.getAttachmentList());
			} else {
				// 如果没有附件名称，则使用意图实体内容作为附件名称
				metaDataList.forEach(keyValueVO -> {
					if (CollUtil.isNotEmpty(keyValueVO.getValue())) {
						CollUtil.addAll(attachNameList, keyValueVO.getValue());
					}
				});
			}
			// 附件后缀数组，docx,pdf....， or 关系
			if (CollUtil.isNotEmpty(intentEntityVO.getSuffixList())) {
				CollUtil.addAll(suffixList, intentEntityVO.getSuffixList());
			}

			// 状态包含：0-不限 或 (1-未读和2-已读)，则设置为：0-不限
			List<String> statusList = intentEntityVO.getStatusList();
			if (CollUtil.isNotEmpty(statusList)) {
				if (SearchMailParam.ReadEnum.hasAllReadStatus(statusList)) {
					read = SearchMailParam.ReadEnum.ALL.getCode();
				} else {
					read = SearchMailParam.getReadEnumByRemark(statusList.get(0));
				}
			}

			// 时间区间
//			setSecondTimeRangeList(receivedDateList, intentEntityVO);
			List<String> timeList = intentEntityVO.getTimeList();
			if (!isEmpty(timeList)) {
				List<MillisecondTimeRange> timeRangeList = TimeRangeUtils.convertTimeToRanges(timeList);
				CollUtil.addAll(receivedDateList, timeRangeList);
			}

			// 邮件类型
			List<String> typeList = intentEntityVO.getTypeList();
			if (CollUtil.isNotEmpty(typeList)) {
				mailType = SearchMailParam.getMailTypeEnumByRemark(typeList.get(0));
			}
		}

		// 检查是否可以使用邮件搜索（true-可以搜索，false-不可以搜索）
		if (null == mailType) {
			// 小天渠道，默认【重要邮件】搜索
			if (sourceChannelsProperties.isXiaoTian(reqDTO.getSourceChannel())) {
				mailType = SearchMailParam.MailTypeEnum.IMPORTANT.getCode();
			} else {
				mailType = SearchMailParam.MailTypeEnum.GEN.getCode();
			}
		}
		if (!checkCanSearchMail(reqDTO, mailType)) {
			return;
		}

		/**
		 * set-指定发件人列表 支持多个（发件人名称和发件人地址）搜索
		 */
		searchMailParam.setFromList(fromList);

		/** set-读状态 */
		searchMailParam.setRead(read);

		/**
		 * 搜索收信时间范围列表，每个时间范围均为或的关系，如果填了，TimeRange中的两个字段就是必填 邮箱目前只支持传1个
		 */
		if (CollUtil.isNotEmpty(receivedDateList)) {
			searchMailParam.setReceivedDateList(CollUtil.toList(receivedDateList.get(0)));
		}

		/** set邮件类型：1-重要（默认）；0-普通 */
		searchMailParam.setMailType(mailType);

		// 邮件标题,只取第一个
		if (CollUtil.isNotEmpty(titleList)) {
			searchMailParam.setTitle(titleList.get(0));
		}
		// 邮件内容,用英文半角符号隔开
		if (CollUtil.isNotEmpty(contentList)) {
			contentList = contentList.stream().distinct().collect(Collectors.toList());
			searchMailParam.setContent(CollUtil.join(contentList, ","));
		}
		//  收件人
		if (CollUtil.isNotEmpty(recipientList)) {
			searchMailParam.setRecipientList(recipientList);
		}

		/** set邮件搜索接口参数 */
		searchParam.setSearchMailParam(searchMailParam);
		/** set邮件附件搜索接口参数 */
		setSearchMailAttachmentParam(searchParam, searchMailAttachmentParam, fromList, read, receivedDateList, attachNameList, suffixList);
	}

	/**
	 * 设置邮件附件搜索参数
	 * @param searchParam
	 * @param searchMailAttachmentParam
	 * @param fromList
	 * @param read
	 * @param receivedDateList
	 * @param attachNameList
	 * @param suffixList
	 */
	private void setSearchMailAttachmentParam(SearchParam searchParam, SearchMailAttachmentParam searchMailAttachmentParam, List<String> fromList,
											  Integer read, List<MillisecondTimeRange> receivedDateList, List<String> attachNameList, List<String> suffixList) {


		if (CollUtil.isNotEmpty(attachNameList) || CollUtil.isNotEmpty(suffixList)) {
			if (CollUtil.isNotEmpty(attachNameList)) {
				searchMailAttachmentParam.setAttachName(attachNameList.get(0));
			} else  {
				//由于邮件附件搜索，附件名与附件后缀，邮箱接口只能有一个，所以这里优先取附件名，没有附件名的情况才用附件后缀搜索
				searchMailAttachmentParam.setSuffixList(suffixList);
			}

			/**
			 * set-指定发件人列表 支持多个（发件人名称和发件人地址）搜索
			 */
			searchMailAttachmentParam.setFromList(fromList);

			/** set-读状态 */
			searchMailAttachmentParam.setRead(read);
			/**
			 * 搜索收信时间范围列表，每个时间范围均为或的关系，如果填了，TimeRange中的两个字段就是必填 邮箱目前只支持传1个
			 */
			if (CollUtil.isNotEmpty(receivedDateList)) {
				MillisecondTimeRange timeRange = receivedDateList.get(0);
				searchMailAttachmentParam.setTimeRange(timeRange);
			}
			/** set邮件附件搜索接口参数 */
			searchParam.setSearchMailAttachmentParam(searchMailAttachmentParam);
		}

	}

	/**
	 * 检查是否可以使用邮件搜索
	 *
	 * @Author: WeiJingKun
	 *
	 * @return boolean true-可以搜索，false-不可以搜索
	 */
	private boolean checkCanSearchMail(ChatAddReqDTO reqDTO, Integer mailType) {
		/** h5Version判断 */
		// h5Version < 小天1.4.0版本，不进行邮件搜索
		if (VersionUtil.xtH5VersionLt140() && sourceChannelsProperties.isXiaoTian(reqDTO.getSourceChannel())) {
			log.info("createSearchMailParam-checkCanSearchMail，h5Version < 小天1.4.0版本，不进行邮件搜索");
			return false;
		}

		/** 邮件类型判断 */
		SearchMailParam.MailTypeEnum mailTypeEnum = SearchMailParam.MailTypeEnum.getByCode(mailType);
		if (ObjectUtil.isNotNull(mailTypeEnum)) {
			switch (mailTypeEnum) {
			/** 普通邮件 */
			case GEN:
				// 小天渠道，目前不支持【普通邮件】搜索
				if (sourceChannelsProperties.isXiaoTian(reqDTO.getSourceChannel())) {
					log.info("createSearchMailParam-checkCanSearchMail，小天渠道，目前不支持【普通邮件】搜索");
					return false;
				}
				break;
			/** 重要邮件 */
			case IMPORTANT:
				// 【重要邮件】搜索 && 不在邮件搜索白名单，给提示语，放到LeadCopy=6，返回前端
				if (!whiteListProperties.getMailSearch().canUse(RequestContextHolder.getUserId())) {
					String prompt = whiteListProperties.getMailSearch().getPrompt();
					log.error("createSearchMailParam-checkCanSearchMail，【重要邮件】搜索 && 不在邮件搜索白名单，提示词：{}", prompt);
					throw new YunAiBusinessException(AiResultCode.CODE_10022024.getCode(), prompt);
				}
				break;
			default:
				break;
			}
		}

		return true;
	}

	/**
	 * 获取搜索实体，先取子意图实体，兜底返回主意图实体列表
	 *
	 * @param intentionEnum 意图枚举
	 * @param subIntentions 子意图列表
	 * @param entityList    实体识别结果列表
	 * @return 实体识别结果列表
	 */
	private List<IntentEntityVO> getEntityListByType(DialogueIntentionEnum intentionEnum,
			List<IntentionInfo> subIntentions, List<IntentEntityVO> entityList) {
		if (CollUtil.isNotEmpty(subIntentions)) {
			for (IntentionInfo subIntention : subIntentions) {
				if (intentionEnum.getCode().equals(subIntention.getIntention())) {
					// 匹配意图，允许搜索
					return subIntention.getEntityList();
				}
			}
		}
		// 兜底返回主意图实体列表
		return entityList;
	}

	/**
	 * allow 执行搜索（有自意图执行自意图的特定搜索，无子意图则允许搜索intentionEnum）
	 *
	 * @param intentionEnum 意图枚举
	 * @param subIntentions 子意图列表
	 * @return
	 */
	private boolean allowSearchByType(DialogueIntentionEnum intentionEnum, List<IntentionInfo> subIntentions) {
		if (CollUtil.isEmpty(subIntentions)) {
			// 子意图列表为空，允许搜索
			return true;
		}
		for (IntentionInfo subIntention : subIntentions) {
			if (intentionEnum.getCode().equals(subIntention.getIntention())) {
				// 匹配意图，允许搜索
				return true;
			}
		}
		return false;
	}

	/**
	 * 获取和设置全网搜推荐
	 *
	 * @Author: WeiJingKun
	 * @param handleDTO 用户输入对象
	 */
	private void getAndSetAllNetworkSearchList(ChatAddHandleDTO handleDTO) {
		List<AllNetworkSearchRecommendVO> allNetworkSearchRecommendList = null;
		try {
			Future<List<AllNetworkSearchRecommendVO>> future = handleDTO.getAllNetworkSearchRecommendFuture();
			/** 获取和设置全网搜推荐 */
			if (null != future) {
				// 获取全网搜推荐列表
				allNetworkSearchRecommendList = future.get(FutureConstants.SIXTY_SECONDS, TimeUnit.SECONDS);
				if (CollUtil.isNotEmpty(allNetworkSearchRecommendList)) {
					// 设置到 对话结果推荐对象
					ChatAddRespVO resVO = handleDTO.getRespVO();
					DialogueRecommendVO recommendVO = resVO.getRecommend();
					if (ObjectUtil.isNull(recommendVO)) {
						resVO.setRecommend(DialogueRecommendVO.builder()
								.allNetworkSearchList(allNetworkSearchRecommendList).build());
					} else {
						recommendVO.setAllNetworkSearchList(allNetworkSearchRecommendList);
					}
				}
			} else {
				log.info("【获取和设置全网搜推荐】future为空");
			}
		} catch (Exception e) {
			log.error("【获取和设置全网搜推荐】异常", e);
		} finally {
			log.info("【获取和设置全网搜推荐】结果：{}", JsonUtil.toJson(allNetworkSearchRecommendList));
		}
	}

	/**
	 * 异步处理全网搜推荐
	 *
	 * @Author: WeiJingKun
	 * @param searchParam 搜索参数
	 * @param intention   意图编码
	 * @param params      用户输入对象
	 */
	private void asyncAllNetworkSearchRecommend(SearchParam searchParam, String intention, ChatAddHandleDTO params) {
		// 判断是否执行【异步处理全网搜推荐】
		boolean judgeSearchPanta = judgeSearchPanta(searchParam, intention, params);
		log.info("【异步处理全网搜推荐】是否执行：{}", judgeSearchPanta);
		if (!judgeSearchPanta) {
			return;
		}

		try {
			// 获取主线程的ThreadLocal信息，并且异步调用前，将父线程中的请求信息绑定给子线程
			RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo = RequestContextHolder.getThreadLocalInfoAndBindingAttributes();

			/** 异步处理全网搜推荐 */
			Future<List<AllNetworkSearchRecommendVO>> future = CompletableFuture.supplyAsync(() -> {
				StopWatch stopWatch = StopWatchUtil.createStarted();
				PanTaResourceOuterSearchV2DTO searchDTO = null;
				List<EsPanTaResourceEntity> pantaResourceList = null;
				List<AllNetworkSearchRecommendVO> allNetworkSearchRecommendList = new ArrayList<>();
				try {
					// 把主线程ThreadLocal信息set到子线程
					RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);

//					// 搜索实体抽取
//					Map<PantaLabelEnum, List<String>> extractResult = searchEntityExtract(
//							params.getReqDTO().getDialogueInput().getDialogue());
//
//					// 黑名单过滤
//					Iterator<Map.Entry<PantaLabelEnum, List<String>>> iterator = extractResult.entrySet().iterator();
//					while (iterator.hasNext()) {
//						Map.Entry<PantaLabelEnum, List<String>> entry = iterator.next();
//						List<String> filtered = entry.getValue().stream().filter(s -> !blackResourceHandleService
//								.isSensitive(s, BlackResourceParityEnum.PRECISE.getType()))
//								.collect(Collectors.toList());
//						if (CollUtil.isEmpty(filtered)) {
//							iterator.remove();
//						} else {
//							entry.setValue(filtered);
//						}
//					}
//					if (CollUtil.isEmpty(extractResult)) {
//						log.info("【异步处理全网搜推荐】metaDataList为空");
//						return allNetworkSearchRecommendList;
//					}
//
//					// 构建 小站资源搜索条件
//					searchDTO = createPanTaResourceOuterSearchV2DTO(extractResult);
//
//					// 搜索小站资源
//					pantaResourceList = esPanTaResourceRepository.searchPanTaResourceV2(searchDTO);
//
//					// 构建 全网搜推荐列表，【无搜索结果+影视类搜索意图】才返回
//					if (CollUtil.isNotEmpty(pantaResourceList)) {
//						AllNetworkSearchRecommendVO allNetworkSearchRecommend = new AllNetworkSearchRecommendVO();
//						allNetworkSearchRecommend.setQuery(allNetworkSearchProperties.getSuffix());
//						allNetworkSearchRecommend.setButtonCopy(allNetworkSearchProperties.getButtonCopy());
//						allNetworkSearchRecommendList.add(allNetworkSearchRecommend);
//					}
					AllNetworkSearchRecommendVO allNetworkSearchRecommend = new AllNetworkSearchRecommendVO();
					allNetworkSearchRecommend.setQuery(allNetworkSearchProperties.getSuffix());
					allNetworkSearchRecommend.setButtonCopy(allNetworkSearchProperties.getButtonCopy());
					allNetworkSearchRecommendList.add(allNetworkSearchRecommend);
				} catch (Exception e) {
					log.error("【异步处理全网搜推荐】异常", e);
				} finally {
					log.info("【异步处理全网搜推荐】耗时：{}\n 全网搜推荐列表：{}\n 入参：{}\n 小站资源结果：{}",
							StopWatchUtil.logTime(stopWatch), JsonUtil.toJson(allNetworkSearchRecommendList),
							JsonUtil.toJson(searchDTO), JsonUtil.toJson(pantaResourceList));
					StopWatchUtil.clearDuration();
				}
				return allNetworkSearchRecommendList;
			}, searchPantaThreadPool);
			// set全网搜推荐异步处理future
			params.setAllNetworkSearchRecommendFuture(future);
		} catch (Exception e) {
			log.error("【异步处理全网搜推荐】线程池异常", e);
		}
	}

	/**
	 * 构建小站资源搜索条件V2
	 *
	 * @Author: WeiJingKun
	 *
	 * @param extractResult 实体元数据列表
	 * @return 小站资源搜索条件
	 */
	@NotNull
	private PanTaResourceOuterSearchV2DTO createPanTaResourceOuterSearchV2DTO(
			Map<PantaLabelEnum, List<String>> extractResult) {
		AllNetworkSearchProperties.SearchPantaParam searchPantaParam = allNetworkSearchProperties.getSearchPantaParam();
		PanTaResourceOuterSearchV2DTO searchDTO = new PanTaResourceOuterSearchV2DTO();
		searchDTO.setMetaDataMap(extractResult);
		searchDTO.setReleaseYear(searchPantaParam.getReleaseYear());
		searchDTO.setIsAdult(searchPantaParam.getAdult());
		searchDTO.setIsDomestic(searchPantaParam.getDomestic());
		searchDTO.setPriority(searchPantaParam.getPriorityList());
		searchDTO.setSearchResourceTagRange(searchPantaParam.getBusinessResourceTypeList());
		return searchDTO;
	}

	/**
	 * 搜索实体抽取
	 *
	 * @param dialogue 对话内容
	 *
	 * @return 搜索实体抽取结果
	 */
	private Map<PantaLabelEnum, List<String>> searchEntityExtract(String dialogue) {
		AllNetworkSearchProperties.SearchEntityExtractDTO searchEntityExtract = allNetworkSearchProperties
				.getSearchEntityExtract();
		TextNerExtractDTO dto = new TextNerExtractDTO();
		dto.setText(dialogue);
		dto.setMaxTextLength(searchEntityExtract.getMaxLength());
		dto.setEnableAllEntity(searchEntityExtract.isReturnAll());
		dto.setEntityTypeList(searchEntityExtract.getEntityTypeList());
		dto.setAllEntityExample(searchEntityExtract.getAllEntityExample());
		dto.setTypeEntityExample(searchEntityExtract.getTypeEntityExample());
		dto.setRequestId(MDC.get(LogConstants.TRACE_ID));
		return cmicTextService.searchEntityExtract("【AI全网搜】", dto);
	}

	/**
	 * 判断是否执行【小站资源搜索】
	 *
	 * @Author: WeiJingKun
	 * @param searchParam 搜索参数
	 * @param intention   意图编码
	 * @param handerDTO   用户输入对象
	 * @return true：执行；false：不执行
	 */
	private boolean judgeSearchPanta(SearchParam searchParam, String intention, ChatAddHandleDTO handerDTO) {
		// 是否搜小站：true-是，false-否
//		if (!allNetworkSearchProperties.isSearchPanta()) {
//			log.info("【异步处理全网搜推荐】未开启搜小站");
//			return false;
//		}

		// 白名单校验
		List<String> allNetworkSearchWhiteUser = whiteListProperties.getAllNetworkSearchWhiteUser();
		if (CollUtil.isNotEmpty(allNetworkSearchWhiteUser)
				&& !allNetworkSearchWhiteUser.contains(RequestContextHolder.getPhoneNumber())) {
			log.info("【异步处理全网搜推荐】非白名单用户");
			return false;
		}

		// 判断是否为影视类意图
		boolean isMovieIntention = false;
		ChatAddReqDTO dto = handerDTO.getReqDTO();
		if (sourceChannelsProperties.isMail(dto.getSourceChannel())) {
			isMovieIntention = checkMailMovieIntention(handerDTO);
		} else {
			isMovieIntention = checkMovieIntention(handerDTO);
		}
		log.info("【异步处理全网搜推荐】搜小站的意图：{}", isMovieIntention);
		return isMovieIntention;
	}

	/**
	 * 【小天助手】判断是否自动进入全网搜流程
	 *
	 * @Author: WeiJingKun
	 *
	 * @param handleDTO 用户输入对象
	 * @return true-普通对话搜索；false-AI全网搜
	 */
	private boolean autoAllNetworkSearch(ChatAddHandleDTO handleDTO) {
		ChatAddRespVO respVO = handleDTO.getRespVO();
		List<SearchInfo> searchInfoList = respVO.getFlowResult().getSearchInfoList();
		// 获取意图 VO 和意图列表
		DialogueIntentionVO intentionVo = handleDTO.getIntentionVO();
		List<IntentionInfo> intentionInfoList = (intentionVo != null) ? intentionVo.getIntentionInfoList() : null;
		// 如果意图列表为空，直接返回
		if (CollUtil.isEmpty(intentionInfoList)) {
			log.info("【判断是否自动进入全网搜流程处理】意图列表为空，保存搜索结果");
			return true;
		}

		// 获取算法返回的所有意图编码列表
		List<String> allIntentionCodes = new ArrayList<>();
		for (IntentionInfo intentionInfo : intentionInfoList) {
			if (CollUtil.isNotEmpty(intentionInfo.getSubIntentions())) {
				allIntentionCodes.addAll(intentionInfo.getSubIntentions().stream().map(IntentionInfo::getIntention)
						.collect(Collectors.toList()));
			}
			allIntentionCodes.add(intentionInfo.getIntention());
		}
		if (CollUtil.isEmpty(allIntentionCodes)) {
			log.info("【判断是否自动进入全网搜流程处理】意图编码列表为空，保存搜索结果");
			return true;
		}

		// 搜索有结果
		if (CollUtil.isNotEmpty(searchInfoList)) {
			log.info("【判断是否自动进入全网搜流程处理】搜索有结果，保存搜索结果");
			return true;
		}

		/** 无结果 + 单意图（包含：999、018） */
		if (allIntentionCodes.size() == 1) {
			// 单意图：图片意图
			if (allIntentionCodes.contains(DialogueIntentionEnum.SEARCH_IMAGE.getCode())) {
				log.info("【判断是否自动进入全网搜流程处理】无结果 + 单意图 + 图片意图，保存搜索结果");
				return true;
			}
			// 无结果 + 单意图 + 【非】图片意图
			log.info("【判断是否自动进入全网搜流程处理】无结果 + 单意图 + 【非】图片意图，进入大模型回答，allIntentionCodes:{}", JsonUtil.toJson(allIntentionCodes));
			// 进入大模型回答
			return bigModelAnswer(handleDTO);
		}

		/** 无结果 + 多意图 */
		// 无结果 + 多意图 + 包含图片意图
		if (allIntentionCodes.contains(DialogueIntentionEnum.SEARCH_IMAGE.getCode())) {
			SearchImageResult tmpImageResult = handleDTO.getTmpSearchImageResult();
			Map<String, List<String>> hitRelationshipNameMap = (tmpImageResult != null) ? tmpImageResult.getHitRelationshipNameMap() : null;
			log.info("【判断是否自动进入全网搜流程处理】无结果 + 多意图 + 包含图片意图，判断是否有涉及人物关系，tmpImageResult:{}", JsonUtil.toJson(tmpImageResult));
			// 多意图 + 包含图片意图 + 涉及人物关系
			if (CollUtil.isNotEmpty(hitRelationshipNameMap)) {
				log.info("【判断是否自动进入全网搜流程处理】无结果 + 多意图 + 包含图片意图 + 涉及人物关系，保存搜索结果，allIntentionCodes:{}", JsonUtil.toJson(allIntentionCodes));
				return true;
			}
		}

		// 是否影视类意图
		boolean isMovieIntention = DialogueIntentionEnum.checkMovieIntention(allIntentionCodes);

		// 无结果 + 多意图 + 非包含图片意图 + 影视类意图
		if (isMovieIntention) {
			log.info("【判断是否自动进入全网搜流程处理】无结果 + 多意图 + 非包含图片意图 + 影视类意图，进行全网搜，allIntentionCodes:{}", JsonUtil.toJson(allIntentionCodes));
			// 执行AI全网搜的流程
			return allNetworkSearch(handleDTO);
		}
		// 无结果 + 多意图 + 非包含图片意图 + 非影视类意图
		log.info("【判断是否自动进入全网搜流程处理】无结果 + 多意图 + 非包含图片意图 + 非影视类意图，进入大模型回答，allIntentionCodes:{}", JsonUtil.toJson(allIntentionCodes));
		// 进入大模型回答
		return bigModelAnswer(handleDTO);
	}

	/**
	 * 执行AI全网搜的流程
	 * @Author: WeiJingKun
	 *
	 * @param handleDTO 用户输入对象
	 * @return true-继续执行下一个，false-终止执行
	 */
	private boolean allNetworkSearch(ChatAddHandleDTO handleDTO) {
		ChatAddRespVO respVO = handleDTO.getRespVO();
		// 打开全网搜
		handleDTO.getReqDTO().getDialogueInput().setEnableAllNetworkSearch(Boolean.TRUE);
		// 手动设置意图结果为文生文意图
		handleDTO.setTextGenerateTextIntention();
		// 清空普通搜索产生的leadCopy
		respVO.setLeadCopy(null);
		// 结果类型：大模型文本回答
		respVO.getFlowResult().setResultType(FlowResultTypeEnum.TEXT_MODEL.getType());
		// 全网搜没结果，进行大模型联网回答
		handleDTO.setContinueTextSseDTO(new ContinueTextSseDTO(allNetworkSearchProperties.getModelCode()));
		// 执行AI全网搜的流程
		return specialAiInternetSearchHandlerImpl.run(handleDTO);
	}

	/**
	 * 进入大模型回答
	 * @Author: WeiJingKun
	 *
	 * @param handleDTO 用户输入对象
	 * @return true-继续执行下一个，false-终止执行
	 */
	private boolean bigModelAnswer(ChatAddHandleDTO handleDTO) {
		ChatAddRespVO respVO = handleDTO.getRespVO();
		// 手动设置意图结果为文生文意图
		handleDTO.setTextGenerateTextIntention();
		// 清空普通搜索产生的leadCopy
		respVO.setLeadCopy(null);
		// 结果类型：大模型文本回答
		respVO.getFlowResult().setResultType(FlowResultTypeEnum.TEXT_MODEL.getType());
		// 使用大模型回答时，不需要返回【大模型推荐】
		chatDialogueRecommendService.removeTextIntentionRecommend(respVO.getRecommend());
		// 设置【继续执行大模型回答DTO】
		setContinueTextSseDTO(handleDTO);
		// 勾选知识库时，走知识库对话
		if(textModelKnowledgeSseHandlerImpl.execute(handleDTO)){
			// 进入知识库对话
			if(textModelKnowledgeSseHandlerImpl.run(handleDTO)){
				// 选择了知识库，但是未命中知识库，走普通对话
				return textModelTextSseHandlerImpl.run(handleDTO);
			}
			return false;
		} else {
			return textModelTextSseHandlerImpl.run(handleDTO);
		}
	}

	/**
	 * 判断是否为影视类意图
	 *
	 * @Author: WeiJingKun
	 *
	 * @param handleDTO 用户输入对象
	 * @return true-影视类意图
	 */
	private boolean checkMovieIntention(ChatAddHandleDTO handleDTO) {
		// 获取意图 VO 和意图列表
		DialogueIntentionVO intentionVo = handleDTO.getIntentionVO();
		List<IntentionInfo> intentionInfoList = ObjectUtil.isNotNull(intentionVo) ? intentionVo.getIntentionInfoList() : null;

		// 获取算法返回的所有意图编码列表
		List<String> allIntentionCodes = new ArrayList<>();
		if (CollUtil.isNotEmpty(intentionInfoList)) {
            assert intentionInfoList != null;
            for (IntentionInfo intentionInfo : intentionInfoList) {
				if (CollUtil.isNotEmpty(intentionInfo.getSubIntentions())) {
					allIntentionCodes.addAll(intentionInfo.getSubIntentions().stream().map(IntentionInfo::getIntention).collect(Collectors.toList()));
				}
				allIntentionCodes.add(intentionInfo.getIntention());
			}
		}

		// 判断是否为影视类意图
		return DialogueIntentionEnum.checkMovieIntention(allIntentionCodes);
	}

	/**
	 * 邮箱渠道判断是否为影视类意图
	 * @param handleDTO
	 * @return
	 */
	private boolean checkMailMovieIntention(ChatAddHandleDTO handleDTO) {
		// 获取意图 VO 和意图列表
		DialogueIntentionVO intentionVo = handleDTO.getIntentionVO();
		List<IntentionInfo> intentionInfoList = ObjectUtil.isNotNull(intentionVo) ? intentionVo.getIntentionInfoList() : null;

		// 获取算法返回的所有意图编码列表
		List<String> allIntentionCodes = new ArrayList<>();
		if (CollUtil.isNotEmpty(intentionInfoList)) {
			assert intentionInfoList != null;
			for (IntentionInfo intentionInfo : intentionInfoList) {
				if (CollUtil.isNotEmpty(intentionInfo.getSubIntentions())) {
					allIntentionCodes.addAll(intentionInfo.getSubIntentions().stream().map(IntentionInfo::getIntention).collect(Collectors.toList()));
				}
				allIntentionCodes.add(intentionInfo.getIntention());
			}
		}

		// 判断是否为影视类意图
		return checkMailMovieIntention(allIntentionCodes);
	}

	/**
	 * 判断是否为影视类意图
	 */
	private boolean checkMailMovieIntention(List<String> allIntentionCodes) {
		List<String> intentionList = new ArrayList<>();
		intentionList.add(DialogueIntentionEnum.SEARCH_FOLDER.getCode());
		intentionList.add(DialogueIntentionEnum.SEARCH_VIDEO.getCode());
		if (allIntentionCodes.size() == 1) {
			return allIntentionCodes.contains(DialogueIntentionEnum.SEARCH_VIDEO.getCode());
		}
		if (allIntentionCodes.size() == 2) {
			return allIntentionCodes.containsAll(intentionList);
		}
		if (allIntentionCodes.size() == 3) {
			intentionList.add(DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode());
			return allIntentionCodes.containsAll(intentionList);
		}
		return false;
	}

	/**
	 * 【云邮助手】判断是否自动进入全网搜流程
	 *
	 * @Author: WeiJingKun
	 *
	 * @param handleDTO 用户输入对象
	 * @return true-普通对话搜索；false-AI全网搜
	 */
//	private boolean mailAutoAllNetworkSearch(ChatAddHandleDTO handleDTO) {
//		ChatAddRespVO resVO = handleDTO.getRespVO();
//		List<SearchInfo> searchInfoList = resVO.getFlowResult().getSearchInfoList();
//		// 搜索无视频资源（搜视频014、发现广场搜索022）
//		boolean noSearchResultFlag = false;
//		if (CollUtil.isEmpty(searchInfoList)) {
//			noSearchResultFlag = true;
//		} else {
//			// 个人资产搜索
//			SearchFileResult searchFileResult = getSearchFileResultByList(searchInfoList);
//			// 发现广场搜索
//			SearchDiscoveryResult searchDiscoveryResult = getSearchDiscoveryResultByList(searchInfoList);
//			// 搜索结果都为空时，noSearchResultFlag = true
//			noSearchResultFlag = (null == searchFileResult || CollUtil.isEmpty(searchFileResult.getFileList()))
//					&& (null == searchDiscoveryResult || CollUtil.isEmpty(searchDiscoveryResult.getDiscoveryList()));
//		}
//		// 自动直接进入全网搜流程 以下两种情况自动进入全网搜流程：1、联网模型+已开开关 2、非联网模型
//		boolean isNetworked = isNetworked(handleDTO);
//		if(isNetworked && noSearchResultFlag){
//			// 更新为对话意图（000），执行AI全网搜
//			handleDTO.setTextGenerateTextIntention();
//			handleDTO.getReqDTO().getDialogueInput().setEnableAllNetworkSearch(Boolean.TRUE);
//			// 清空普通搜索产生的leadCopy
//			handleDTO.getRespVO().setLeadCopy(null);
//			handleDTO.getRespVO().getFlowResult().setResultType(FlowResultTypeEnum.TEXT_MODEL.getType());
//			// 执行AI全网搜的流程
//			return specialAiInternetSearchHandlerImpl.run(handleDTO);
//		}
//		return true;
//	}

	@Override
    public boolean mailAutoAllNetworkSearch(ChatAddHandleDTO handleDTO) {
		ChatAddRespVO resVO = handleDTO.getRespVO();
		List<SearchInfo> searchInfoList = resVO.getFlowResult().getSearchInfoList();
		// 获取意图 VO 和意图列表
		DialogueIntentionVO intentionVo = handleDTO.getIntentionVO();
		List<IntentionInfo> intentionInfoList = (intentionVo != null) ? intentionVo.getIntentionInfoList() : null;
		// 如果意图列表为空，直接返回
		if (CollUtil.isEmpty(intentionInfoList)) {
			log.info("【139mail-判断是否自动进入全网搜流程处理】意图列表为空，保存搜索结果");
			return true;
		}

		// 获取算法返回的所有意图编码列表
		List<String> allIntentionCodes = new ArrayList<>();
		for (IntentionInfo intentionInfo : intentionInfoList) {
			if (CollUtil.isNotEmpty(intentionInfo.getSubIntentions())) {
				allIntentionCodes.addAll(intentionInfo.getSubIntentions().stream().map(IntentionInfo::getIntention)
						.collect(Collectors.toList()));
			}
			allIntentionCodes.add(intentionInfo.getIntention());
		}
		if (CollUtil.isEmpty(allIntentionCodes)) {
			log.info("【139mail-判断是否自动进入全网搜流程处理】意图编码列表为空，保存搜索结果");
			return true;
		}

		// 搜索有结果
		if (CollUtil.isNotEmpty(searchInfoList)) {
			log.info("【139mail-判断是否自动进入全网搜流程处理】搜索有结果，保存搜索结果");
			return true;
		}


		/** 无结果 + 单意图（包含：999、018） */
		if (allIntentionCodes.size() == 1) {
			// 单意图：图片意图
//			if (allIntentionCodes.contains(DialogueIntentionEnum.SEARCH_IMAGE.getCode())) {
//				log.info("【139mail-判断是否自动进入全网搜流程处理】无结果 + 单意图 + 图片意图，保存搜索结果");
//				return true;
//			}

			reSetMailAiSearchLeadCopy(handleDTO);
			log.info("【139mail-判断是否自动进入全网搜流程处理】无结果 + 单意图 + 【非】图片意图，大模型回答，allIntentionCodes:{}", JsonUtil.toJson(allIntentionCodes));
			// 使用大模型回答时，不需要返回【大模型推荐】
			chatDialogueRecommendService.removeTextIntentionRecommend(resVO.getRecommend());
			return textModelTextSseHandlerImpl.run(handleDTO);
		}

		/** 无结果 + 多意图 */
		// 无结果 + 多意图 + 包含图片意图
//		if (allIntentionCodes.contains(DialogueIntentionEnum.SEARCH_IMAGE.getCode())) {
//			SearchImageResult tmpImageResult = handleDTO.getTmpSearchImageResult();
//			Map<String, List<String>> hitRelationshipNameMap = (tmpImageResult != null) ? tmpImageResult.getHitRelationshipNameMap() : null;
//			log.info("【139mail-判断是否自动进入全网搜流程处理】无结果 + 多意图 + 包含图片意图，判断是否有涉及人物关系，tmpImageResult:{}", JsonUtil.toJson(tmpImageResult));
//			// 多意图 + 包含图片意图 + 涉及人物关系
//			if (CollUtil.isNotEmpty(hitRelationshipNameMap)) {
//				log.info("【139mail-判断是否自动进入全网搜流程处理】无结果 + 多意图 + 包含图片意图 + 涉及人物关系，保存搜索结果，allIntentionCodes:{}", JsonUtil.toJson(allIntentionCodes));
//				return true;
//			}
//		}

		// 是否影视类意图
		boolean isMovieIntention = checkMailMovieIntention(allIntentionCodes);

		// 无结果 + 多意图 + 非包含图片意图 + 影视类意图
		if (isMovieIntention) {
			handleDTO.setTextGenerateTextIntention();
			handleDTO.getReqDTO().getDialogueInput().setEnableAllNetworkSearch(Boolean.TRUE);
			//全网搜，清空普通搜索产生的leadCopy
			handleDTO.getRespVO().setLeadCopy(null);
			handleDTO.getRespVO().getFlowResult().setResultType(FlowResultTypeEnum.TEXT_MODEL.getType());
			handleDTO.setUseOrderTip(true);
			// 全网搜没结果，进行大模型联网回答
			handleDTO.setContinueTextSseDTO(new ContinueTextSseDTO(allNetworkSearchProperties.getModelCode()));
			log.info("【139mail-判断是否自动进入全网搜流程处理】无结果 + 多意图 + 非包含图片意图 + 影视类意图，进行全网搜，allIntentionCodes:{}", JsonUtil.toJson(allIntentionCodes));
			// 执行AI全网搜的流程
			return specialAiInternetSearchHandlerImpl.run(handleDTO);
		}
		// 无结果 + 多意图 + 非包含图片意图 + 非影视类意图
		reSetMailAiSearchLeadCopy(handleDTO);
		log.info("【139mail-判断是否自动进入全网搜流程处理】无结果 + 多意图 + 非包含图片意图 + 非影视类意图，大模型回答，allIntentionCodes:{}", JsonUtil.toJson(allIntentionCodes));
		// 使用大模型回答时，不需要返回【大模型推荐】
		chatDialogueRecommendService.removeTextIntentionRecommend(resVO.getRecommend());
		return textModelTextSseHandlerImpl.run(handleDTO);
	}

	/**
	 * 重置邮箱AI搜索的leadCopy
	 * @param handleDTO
	 */
	private void reSetMailAiSearchLeadCopy(ChatAddHandleDTO handleDTO) {
		boolean enableAiSearch = handleDTO.getInputInfoDTO().isEnableAiSearch();
		// 获取用户设置的模型，没有设置则使用默认模型
		ChatConfigEntity chatConfigEntity = chatConfigServiceDomainService.getUserCanUseModel(handleDTO.getReqDTO().getUserId(),
				RequestContextHolder.getPhoneNumber(), handleDTO.getAssistantEnum(), handleDTO.getBusinessType());
		boolean enableForceNetworkSearch = true;
		String intentionCode = handleDTO.getIntentionCode();
		if (!intentionCode.equals(DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode())) {
			enableForceNetworkSearch = handleDTO.getInputInfoDTO().isEnableForceNetworkSearch();
		}
		handleDTO.setTextGenerateTextIntention();
		if (enableAiSearch) {
			//暂时没有找到相关结果，建议调整一下问题重新搜索。另根据你刚才的问题，整理了一些可能对你有用的内容如下：
			String msg = mailaiSearchProperties.getNoResultPromptCopy();
			// 构建引导文案
			LeadCopyVO leadCopyVO = LeadCopyVO.builder()
					// 类型6：语义实体为空、搜索条件为空或搜索结果为空时的返回，仅返回提示文案promptCopy
					.type(LeadCopyTypeEnum.TYPE6.getCode())
					// 提示文案
					.promptCopy(msg).build();
			handleDTO.getRespVO().setLeadCopy(leadCopyVO);
			handleDTO.setTextGenerateTextIntention();
			handleDTO.setContinueTextSseDTO(new ContinueTextSseDTO(chatConfigEntity.getModelType(), enableForceNetworkSearch));
			handleDTO.setUseOrderTip(true);
		} else {
			//非AI搜索，清空普通搜索产生的leadCopy
			handleDTO.getRespVO().setLeadCopy(null);
			handleDTO.getRespVO().getFlowResult().setResultType(FlowResultTypeEnum.TEXT_MODEL.getType());
			handleDTO.setContinueTextSseDTO(new ContinueTextSseDTO(chatConfigEntity.getModelType(), enableForceNetworkSearch));
			handleDTO.setUseOrderTip(true);
		}
		log.info("【139mail-判断是否自动进入全网搜流程处理】重置leadCopy");

	}


	private SearchDiscoveryResult getSearchDiscoveryResultByList(List<SearchInfo> searchInfoList) {
		Object result = SearchInfo.getSearchTypeResultByList(searchInfoList, SearchTypeEnum.DISCOVERY);
		return (result instanceof SearchDiscoveryResult) ? ((SearchDiscoveryResult) result) : null;
	}

	private SearchFileResult getSearchFileResultByList(List<SearchInfo> searchInfoList) {
		Object result = SearchInfo.getSearchTypeResultByList(searchInfoList, SearchTypeEnum.FILE);
		return (result instanceof SearchFileResult) ? ((SearchFileResult) result) : null;
	}

	private boolean isNetworked(ChatAddHandleDTO handleDTO) {
		// 如果开启了开启AI搜索，直接返回true
		if (Boolean.TRUE.equals(handleDTO.getReqDTO().getDialogueInput().isEnableAiSearch())) {
			return true;
		}
		String userId = RequestContextHolder.getUserId();
		AssistantEnum assistantEnum = RequestContextHolder.getAssistantEnum();
		String businessType = RequestContextHolder.getBusinessType();
		// 获取需要选中的模型
		ChatConfigEntity entity = chatConfigServiceDomainService.getUserCanUseModel(userId,
				RequestContextHolder.getPhoneNumber(), assistantEnum, businessType);
		if (null != entity) {
			String modelType = entity.getModelType();
			// 先判断模型是否为空，为空直接返回False
			if (StringUtils.isBlank(modelType)) {
				log.error("【全网搜联网判断】模型为空，model 配置为空");
				return false;
			}
			// 判断是否联网模型
			Map<String, ModelProperties.ModelLimitConfig> limit = modelProperties.getLimitByAssistantEnum(assistantEnum,
					businessType);
			if (ObjectUtil.isEmpty(limit)) {
				log.error("【全网搜联网判断】模型配置为空，model 配置为空");
				return false;
			}
			ModelProperties.ModelLimitConfig modelLimitConfig = limit.get(modelType);
			if (null == modelLimitConfig) {
				log.error("【全网搜联网判断】模型配置为空，model 配置为空");
				return false;
			}
			if (!modelLimitConfig.isEnableNetworkSearch()) {
				log.info("【全网搜联网判断】非联网模型自动直接进入全网搜流程");
				return true;
			}
			// 判断用户是否设置联网
			Integer configNetworkSearchStatus = entity.getNetworkSearchStatus();
			if (null != configNetworkSearchStatus) {
				// 用户配置了，按用户配置的设置
				log.info("【全网搜联网判断】按用户配置的设置,是否联网配置为：{}", configNetworkSearchStatus);
				return ChatNetworkSearchStatusEnum.isOpen(configNetworkSearchStatus);
			}
		} else {
			log.info("【全网搜联网判断】用户未配置模型");
			return false;
		}
		return false;
	}

	@Override
	public void searchKnowledgeBaseResourceHandle(ChatAddHandleDTO handleDTO) {
		// 判断个人知识库有效的文件数量
		if(handleDTO.getPersonalKnowledgeFileCount() > 0){
			/** 智能搜索意图处理 */
			searchIntentionHandle(handleDTO);
		} else {
			/** 没有文件，构建引导文案 */
			LeadCopyVO leadCopy7 = leadCopyService.getLeadCopy7(CommonConstant.SEARCH_KNOWLEDGE_BASE_RESOURCE_NO_KNOWLEDGE, null);

			/** 构建tidb保存数据 */
			AlgorithmChatTidbSaveDTO tidbSaveDTO = AlgorithmChatTidbSaveDTO.builder()
					.dialogueId(handleDTO.getDialogueId())
					.outAuditStatus(OutAuditStatusEnum.SUCCESS.getCode())
					.chatStatus(ChatStatusEnum.CHAT_SUCCESS.getCode())
					.msg(AiResultCode.CODE_10022024.getMsg())
					.build();
			handleDTO.setAlgorithmChatTidbSaveDTO(tidbSaveDTO);

			/** 构建hbase保存数据 */
			// 构建hbase的resp
			AiTextResultRespParameters respParameters = AiTextResultRespParameters.builder()
					.version(AiTextResultVersionEnum.V2.getVersion())
					.resultCode(AiResultCode.CODE_10022024.getCode())
					.resultMsg(AiResultCode.CODE_10022024.getMsg())
					.leadCopy(leadCopy7)
					.build();
			// 把hbase的resp，放入innerDTO
			handleDTO.setHbaseResp(respParameters);

			/** 会话输入返回结果VO，set引导文案 */
			handleDTO.getRespVO().setLeadCopy(leadCopy7);
		}

		/** 同步保存数据 */
		StopWatch stopWatch = StopWatchUtil.createStarted();
		try {
			AiTextResultRespParameters hbaseResp = handleDTO.getHbaseResp();
			if(ObjectUtil.isNotNull(hbaseResp)) {
				hbaseResp.setOutputCommandVO(handleDTO.getIntentionVO());
			}

			// 保存hbase-所有对话结果
			dataSaveService.saveHbaseAllChatResult(handleDTO, hbaseResp);

			// 保存tidb-所有对话结果
			dataSaveService.saveTidbAllChatResult(handleDTO);
		} catch (Exception e) {
			log.error("搜索知识库资源-同步保存数据，execute-异常\n dialogueId：{}", handleDTO.getDialogueId(), e);
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_SERVER_UNAVAILABLE);
		} finally {
			log.info("搜索知识库资源-同步保存数据，方法-耗时：{}", StopWatchUtil.logTime(stopWatch));
			StopWatchUtil.clearDuration();
		}
	}

	/**
	 * 构建知识库资源参数
	 * @Author: WeiJingKun
	 */
	private void createSearchKnowledgeBaseResourceParam(SearchParam searchParam, ChatAddReqDTO dtoDTO,
														DialogueIntentionEnum intentionEnum, List<IntentEntityVO> entityList) {
		// 校验语义结果，数据不可用抛出搜索引导文案业务异常
		handleEntityList(dtoDTO, intentionEnum, entityList);

		// 根据【语义实体】处理
		SearchKnowledgeBaseResourceParam searchKnowledgeBaseResourceParam = SearchKnowledgeBaseResourceParam.builder().build();
		List<String> nameList = new ArrayList<>();
		List<String> suffixList = new ArrayList<>();
		List<SecondTimeRange> fileTimeRangeList = new ArrayList<>();
		for (IntentEntityVO intentEntityVO : entityList) {
			// 设置名称
			setResultList(nameList, intentEntityVO.getMetaDataList());

			// 设置时间
			setSecondTimeRangeList(fileTimeRangeList, intentEntityVO);

			// 设置后缀名
			suffixList.addAll(searchSuffixProperties.mapingSuffixList(intentEntityVO.getSuffixList()));
		}

		/** set文件名称集合-最大支持10条 */
		searchKnowledgeBaseResourceParam.setNameList(nameList.stream().filter(CharSequenceUtil::isNotBlank).distinct().limit(10).collect(Collectors.toList()));

		/** set后缀名集合-最大支持10条 */
		if (CollUtil.isNotEmpty(suffixList)) {
			suffixList = suffixList.stream().filter(CharSequenceUtil::isNotBlank).distinct().limit(10).collect(Collectors.toList());
			searchKnowledgeBaseResourceParam.setSuffixList(suffixList);
		}

		/** set搜索时间范围列表 */
		if (CollUtil.isNotEmpty(fileTimeRangeList)) {
			fileTimeRangeList = fileTimeRangeList.stream().distinct().collect(Collectors.toList());
			searchKnowledgeBaseResourceParam.setTimeList(fileTimeRangeList);
		}

		/** set知识库资源参数搜索接口参数 */
		searchParam.setSearchKnowledgeBaseResourceParam(searchKnowledgeBaseResourceParam);
	}

	/**
	 * 构建资源搜索意图推荐
	 *
	 * @Author: WeiJingKun
	 * @param handleDTO 用户输入对象
	 */
	private void createSearchResourceRecommend(ChatAddHandleDTO handleDTO) {
		int count = handleDTO.getPersonalKnowledgeFileCount();
		ChatAddRespVO resVO = handleDTO.getRespVO();
		DialogueRecommendVO recommendVO = resVO.getRecommend();
		List<IntentionRecommendVO> intentionList = recommendVO.getIntentionList();
		try {

			List<IntentionRecommendVO> newIntentionList = new ArrayList<>();
			// 算法返回结果为泛化意图，添加【模型查】意图推荐
			DialogueIntentionVO intentionVO = handleDTO.getIntentionVO();
			log.info("【全网搜联网判断】算法返回结果为泛化意图，添加【模型查】意图推荐 intentionVO:{}", JsonUtil.toJson(intentionVO));
			if (intentionVO != null && DialogueIntentionEnum.OTHER.getCode().equals(intentionVO.getAlgorithmIntentionCode())) {
				// 获取推荐模板配置
				SearchResultProperties.Recommend recommendProperties = searchResultProperties.getRecommend(SearchConstant.LARGE_MODEL_ANSWER);
				// 构建文生文意图推荐
				IntentionRecommendVO intentionRecommendVO = new IntentionRecommendVO(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode(), recommendProperties.getIntentionTemplate(), "");
				newIntentionList.add(intentionRecommendVO);
			}

			// 判断个人知识库有效的文件数量
			// 202500522 要求去掉追尾的知识库搜索引导语
//			if (count > 0) {
//				// 获取推荐模板配置-添加【知识库搜索】意图推荐
//				SearchResultProperties.Recommend recommendProperties = searchResultProperties.getRecommend(SearchTypeEnum.KNOWLEDGE_BASE_RESOURCE.getSearchType());
//				// 构建知识库资源搜索意图推荐
//				IntentionRecommendVO intentionRecommendVO = new IntentionRecommendVO(DialogueIntentionEnum.SEARCH_KNOWLEDGE_BASE_RESOURCE.getCode(), recommendProperties.getIntentionTemplate(), "");
//				newIntentionList.add(intentionRecommendVO);
//			}

			// 添加已有的意图推荐，主要为了保证顺序
			if (CollUtil.isNotEmpty(intentionList)){
				newIntentionList.addAll(intentionList);
			}

			// 设置意图推荐列表
			if (CollUtil.isNotEmpty(newIntentionList)) {
				recommendVO.setIntentionList(newIntentionList);
			}

		} catch (Exception e) {
			log.error("【构建知识库资源搜索意图推荐】异常", e);
		} finally {
			log.info("【构建知识库资源搜索意图推荐】个人知识库有效的文件数量：{}，结果：{}", count, JsonUtil.toJson(recommendVO));
		}
	}

	private void createMailSearchResourceRecommend(ChatAddHandleDTO handleDTO) {
		int count = handleDTO.getPersonalKnowledgeFileCount();
		ChatAddRespVO resVO = handleDTO.getRespVO();
		DialogueRecommendVO recommendVO = resVO.getRecommend();
		List<IntentionRecommendVO> intentionList = recommendVO.getIntentionList();
		try {

			ChatAddReqDTO dto = handleDTO.getReqDTO();
			List<IntentionRecommendVO> newIntentionList = new ArrayList<>();
			// 算法返回结果为泛化意图，添加【模型查】意图推荐
			DialogueIntentionVO intentionVO = handleDTO.getIntentionVO();
			log.info("【构建邮箱搜索意图推荐】搜索有结果，推荐大模型回答 intentionVO:{}", JsonUtil.toJson(intentionVO));
			List<SearchInfo> searchInfoList = handleDTO.getRespVO().getFlowResult().getSearchInfoList();
			if (CollUtil.isNotEmpty(searchInfoList)) {
				// 获取推荐模板配置
				SearchResultProperties.Recommend recommendProperties = searchResultProperties.getRecommend(SearchConstant.LARGE_MODEL_ANSWER);
				// 构建文生文意图推荐
				IntentionRecommendVO intentionRecommendVO = new IntentionRecommendVO(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode(), recommendProperties.getIntentionTemplate(), "");
				newIntentionList.add(intentionRecommendVO);
			}


			// 判断个人知识库有效的文件数量
			if (count > 0) {
				// 获取推荐模板配置-添加【知识库搜索】意图推荐
				SearchResultProperties.Recommend recommendProperties = searchResultProperties.getRecommend(SearchTypeEnum.KNOWLEDGE_BASE_RESOURCE.getSearchType());
				// 构建知识库资源搜索意图推荐
				IntentionRecommendVO intentionRecommendVO = new IntentionRecommendVO(DialogueIntentionEnum.SEARCH_KNOWLEDGE_BASE_RESOURCE.getCode(), recommendProperties.getIntentionTemplate(), "");
				newIntentionList.add(intentionRecommendVO);
			}

			// 添加已有的意图推荐，主要为了保证顺序
			if (CollUtil.isNotEmpty(intentionList)){
				newIntentionList.addAll(intentionList);
			}

			// 设置意图推荐列表
			if (CollUtil.isNotEmpty(newIntentionList)) {
				recommendVO.setIntentionList(newIntentionList);
			}

		} catch (Exception e) {
			log.error("【构建邮箱搜索意图推荐】异常", e);
		} finally {
			log.info("【构建邮箱搜索意图推荐】个人知识库有效的文件数量：{}，结果：{}", count, JsonUtil.toJson(recommendVO));
		}
	}

	@Override
	public SearchInfo searchImages(SearchImageReqDTO reqDTO) {
		try {
			SearchImageParam searchImageParam = SearchImageParam.builder().isOriginal(reqDTO.getIsOriginal()).build();
			// set关键字
			searchImageParam.setText(reqDTO.getDialogue());
			searchImageParam.setDialogue(reqDTO.getDialogue());
			// set排序方式
			searchImageParam.setSortType(ImageSortTypeEnum.CORRELATION_SORT.getCode());
			if(null != reqDTO.getPageSize()) {
				// 设置分页数
				searchImageParam.setPageInfo(PageInfoDTO.builder().pageSize(reqDTO.getPageSize()).build());
			}

			//需要返回总数
			searchImageParam.getPageInfo().setNeedTotalCount(1);

			SearchImageResult imageResult = apiTextExternalService.searchImage(searchImageParam);
			if(null == imageResult) {
				return null;
			}
			imageResult.setSearchInfo(null);
			// 返回
			return SearchInfo.builder().searchType(SearchTypeEnum.IMAGE.getSearchType()).searchParam(searchImageParam)
					.searchResult(imageResult).build();
		} catch (Exception e) {
			log.error("searchImages error:", e);
		}
		return null;
	}

	@Override
	public void searchForNoteHandler(ChatAddHandleDTO dto) {
		// query向量化
		List<BigDecimal> featureList = ragExternalService.textEmbed(dto.getInputInfoDTO().getDialogue(), dto.getDialogueId());
		if (ObjectUtil.isEmpty(featureList)) {
			log.info("【笔记正文搜索】【文本向量化】结果为空");
			dto.getSseEmitterOperate().setSseName(SseNameEnum.KNOWLEDGE_NO_VECTOR.getCode());
			dto.getRespVO().setSessionId("");
			return;
		}

		// 封装参数
		RecallQueryDTO recallDTO = new RecallQueryDTO();
		recallDTO.setText(dto.getInputInfoDTO().getDialogue());
		recallDTO.setFeature(featureList);
		recallDTO.setUserId(dto.getReqDTO().getUserId());
		String noteId = dto.getInputInfoDTO().getAttachment().getNoteList().get(0).getNoteId();
		List<String> fileIdList = singletonList(noteId);
		recallDTO.setFileIdList(fileIdList);

		// 资源信息列表
		List<RecallQueryDTO.KnowledgeGroup> resourceInfoList = userKnowledgeDomainService.fileList(fileIdList);

		recallDTO.setKnowledgeGroupList(resourceInfoList);
		recallDTO.setVersion(RecallVersionEnum.V2.getVersion());
		//获取问题关键字 1、输入原文获取关键字、2重写后内容获取关键字 合并去重得到结果
		List<String> keywordList = keywordExtract(dto);
		log.info("【笔记正文搜索】【关键字】筛选后：{}",JsonUtil.toJson(keywordList));
		if (CollUtil.isNotEmpty(keywordList)) {
			//将提取到的关键字列表转换为空格分隔的字符串
			recallDTO.setKeyword(StringUtils.join(keywordList, StrPool.C_SPACE));
			// 重新设置text，使用关键字做全文检索
			recallDTO.setText(recallDTO.getKeyword());
		}
		// 召回配置
		RecallConfig config = noteSearchProperties.getRecallConfig();

		List<RecallResultVO> recallResult = ragExternalService.noteContentRecall(recallDTO,config);
		if (ObjectUtil.isEmpty(recallResult)) {
			log.info("【笔记正文搜索】【多路召回】结果为空");
			dto.getSseEmitterOperate().setSseName(SseNameEnum.KNOWLEDGE_NO_RECALL.getCode());
			dto.getRespVO().setSessionId("");
			return;
		}
		// 多路重排
		List<RerankResultVO> rerankResult = multiRouteRerank(dto, recallResult);
		if (CollUtil.isEmpty(rerankResult)) {
			log.info("【笔记正文搜索】【多路重排】结果为空");
			dto.getSseEmitterOperate().setSseName(SseNameEnum.KNOWLEDGE_NO_RERANK.getCode());
			dto.getRespVO().setSessionId("");
			return;
		}
		// 搜索结束，数据复制
		dto.setRespVO(dto.getRespVO());
		List<SearchInfo> searchInfoList = new ArrayList<>();
		SearchNoteContentResult result = getSearchNoteContentResult(rerankResult,keywordList);
		searchInfoList.add(SearchInfo.builder().searchType(SearchTypeEnum.SEARCH_NOTE_CONTENT.getSearchType())
				.searchParam(SearchNoteContentParam.builder().noteId(noteId).query(dto.getInputInfoDTO().getDialogue()).build())
				.searchResult(result).build());

		dto.getRespVO().getFlowResult().setSearchInfoList(searchInfoList);
        //处理对话数据并保存
		handleNoteSearchResult(dto,searchInfoList,noteId);
	}

    @Override
    public boolean cloudPhoneSearchIntentionHandle(ChatAddHandleDTO handleDTO) {
        // 智能搜索意图处理
        searchIntentionHandle(handleDTO);

        // 【云手机】判断是否自动进入全网搜流程
        boolean saveSearchResultFlag = autoAllNetworkSearch(handleDTO);

        // 同步保存数据
        if (saveSearchResultFlag) {
            StopWatch stopWatch = StopWatchUtil.createStarted();
            try {
                AiTextResultRespParameters hbaseResp = handleDTO.getHbaseResp();
                if (ObjectUtil.isNotNull(hbaseResp)) {
                    hbaseResp.setOutputCommandVO(handleDTO.getIntentionVO());
                }

                // 保存hbase-所有对话结果
                dataSaveService.saveHbaseAllChatResult(handleDTO, hbaseResp);

                // 保存tidb-所有对话结果
                dataSaveService.saveTidbAllChatResult(handleDTO);
            } catch (Exception e) {
                log.error("【云手机】搜索功能处理-同步保存数据，execute-异常\n dialogueId：{}", handleDTO.getDialogueId(), e);
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_SERVER_UNAVAILABLE);
            } finally {
                log.info("【云手机】搜索功能处理-同步保存数据，方法-耗时：{}", StopWatchUtil.logTime(stopWatch));
                StopWatchUtil.clearDuration();
            }
        } else {
            log.info("【云手机】搜索功能处理-执行全网搜");
            // 去掉引导文案
            handleDTO.getRespVO().setLeadCopy(null);
            // 去掉全网搜推荐数据
            handleDTO.getRespVO().getRecommend().setAllNetworkSearchList(null);
        }
        return saveSearchResultFlag;
    }

	private void handleNoteSearchResult(ChatAddHandleDTO dto, List<SearchInfo> searchInfoList, String noteId) {
		// 对话id
		String dialogueId = dto.getRespVO().getDialogueId();
		Long dialogueIdLong = Long.parseLong(dialogueId);
		String title = dto.getInputInfoDTO().getAttachment().getNoteList().get(0).getTitle();

		List<IntentionInfo> intentionInfoList = dto.getIntentionVO().getIntentionInfoList();

		// 构建对话tidb保存数据
		AlgorithmChatTidbSaveDTO tidbSaveDTO = AlgorithmChatTidbSaveDTO.builder().dialogueId(dialogueIdLong)
				.outAuditStatus(OutAuditStatusEnum.SUCCESS.getCode()).chatStatus(ChatStatusEnum.CHAT_SUCCESS.getCode())
				.msg(null).build();
		dto.setAlgorithmChatTidbSaveDTO(tidbSaveDTO);

		SearchNoteContentParam contentParam = SearchNoteContentParam.builder()
				.noteId(noteId)
				.query(dto.getInputInfoDTO().getDialogue())
				.build();

		SearchParam searchParam = SearchParam.builder()
				.searchNoteContentParam(contentParam)
				.build();
		// 构建hbase的resp
		AiTextResultRespParameters respParameters = AiTextResultRespParameters.builder()
				.version(AiTextResultVersionEnum.V2.getVersion())
				.param(searchParam)
				.intentionInfoList(intentionInfoList).build();

		dto.setSearchParam(searchParam);
		//设置搜索类型
		dto.getRespVO().getFlowResult().setResultType(FlowResultTypeEnum.SEARCH.getType());
		dto.getRespVO().getFlowResult().setSearchInfoList(searchInfoList);
		// set搜索结果的标题
		dto.getRespVO().getFlowResult().setTitle(title);

		// set搜索结果的标题（hbase）
		respParameters.setTitle(title);
		// set流式结果
		List<DialogueFlowResult> outputList = respParameters.getOutputList();
		if(CollUtil.isNotEmpty(outputList)){
			outputList.add(chatFlowResultAssembler.getFlowResult(dto.getRespVO().getFlowResult()));
		} else {
			outputList = ListUtil.toList(chatFlowResultAssembler.getFlowResult(dto.getRespVO().getFlowResult()));
		}
		respParameters.setOutputList(outputList);
		dto.setHbaseResp(respParameters);
		// 保存搜索结果到tidb和hbase
		saveTidbAndHbaseResult(dto);
	}

	private void saveTidbAndHbaseResult(ChatAddHandleDTO handleDTO) {
		try {
			if(null != handleDTO.getHbaseResp()) {
				handleDTO.getHbaseResp().setOutputCommandVO(handleDTO.getIntentionVO());
			}
			// 保存hbase-所有对话结果
			DialogueFlowResult flowResult =chatFlowResultAssembler.getFlowResult(handleDTO.getRespVO().getFlowResult());
			if(null != handleDTO.getHbaseResp().getOutputList()) {
				handleDTO.getHbaseResp().getOutputList().add(flowResult);
			} else {
				List<DialogueFlowResult> outputList = new ArrayList<>();
				outputList.add(flowResult);
				handleDTO.getHbaseResp().setOutputList(outputList);
			}
			dataSaveService.saveHbaseAllChatResult(handleDTO, handleDTO.getHbaseResp());

			// 保存tidb-所有对话结果
			dataSaveService.saveTidbAllChatResult(handleDTO);
		} catch (Exception e) {
			log.error("同步保存数据，execute-异常\n dialogueId：{}", handleDTO.getDialogueId(), e);
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_SERVER_UNAVAILABLE);
		}
	}

	private SearchNoteContentResult getSearchNoteContentResult(List<RerankResultVO> rerankResult, List<String> keywordList) {
		SearchNoteContentResult result = new SearchNoteContentResult();
		List<SearchNoteContent> paragraphList = new ArrayList<>();
		for (RerankResultVO rerankResultVO : rerankResult) {
			//如果text为空则跳过
			if (StringUtils.isBlank(rerankResultVO.getDocument().getText())) {
				continue;
			}
			SearchNoteContent content = new SearchNoteContent();
			content.setNoteId(rerankResultVO.getDocument().getFileId());
			content.setParagraphId(rerankResultVO.getDocument().getParagraphId());

			String originalText = rerankResultVO.getDocument().getText();
			//配置高亮
			String highlightedText = highlightKeywords(originalText, keywordList);
			content.setSummary(highlightedText);

			paragraphList.add(content);
		}
		result.setTotalCount(paragraphList.size());
		result.setParagraphList(paragraphList);
		return result;
	}

	private String highlightKeywords(String text, List<String> keywordList) {
		if (text == null || keywordList == null || keywordList.isEmpty()) {
			return text;
		}
		for (String keyword : keywordList) {
			if (keyword == null || keyword.isEmpty()) {
				continue;
			}
			text = text.replace(keyword, noteSearchProperties.getPreTags() + keyword + noteSearchProperties.getPostTags());
		}
		return text;
	}

	private List<RerankResultVO> multiRouteRerank(ChatAddHandleDTO dto, List<RecallResultVO> recallResult) {
		/**
		 * 将召回结果分为2大类：
		 * 1、向量结果（切片向量化检索）
		 * 2、使用关键字查询全文结果（全文检索字段：text）
		 */
		Set<String> textSet = new HashSet<>();
		// 向量结果
		List<RecallResultVO> vectorRecallResult = recallResult.stream()
				.filter(recallResultVO -> !ParseTypeEnum.TEXT.getCode().equals(recallResultVO.getRecallParseType()))
				// 过滤：仅保留 text不重复的元素（add方法返回false表示text已经存在）
				.filter(item -> textSet.add(item.getText())).collect(Collectors.toList());

		// 使用关键字查询的全文结果
		textSet.clear();
		List<RecallResultVO> keywordRecallResult = recallResult.stream()
				.filter(recallResultVO -> ParseTypeEnum.TEXT.getCode().equals(recallResultVO.getRecallParseType()))
				// 过滤：仅保留 text不重复的元素（add方法返回false表示text已经存在）
				.filter(item -> textSet.add(item.getText())).collect(Collectors.toList());

		/** 多路重排 */
		// 开启计时
		StopWatch allStopWatch = StopWatchUtil.createStarted();
		List<RerankResultVO> rerankResult = new CopyOnWriteArrayList<>();
		Map<String, String> logMap = MDC.getCopyOfContextMap();
		// 获取主线程的ThreadLocal信息，并且异步调用前，将父线程中的请求信息绑定给子线程
		RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo = RequestContextHolder.getThreadLocalInfoAndBindingAttributes();
		// 2路
		CountDownLatch countDownLatch = new CountDownLatch(2);
		// 【向量】算法重排
		List<RerankResultVO> vectorRerankResult = new ArrayList<>();
		multiRouteRerankThreadPool.submit(() -> {
			// 开启计时
			StopWatch stopWatch = StopWatchUtil.createStarted();
			try {
				MDC.setContextMap(logMap);
				// 把主线程ThreadLocal信息set到子线程
				RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);

				RerankDTO rerankDTO = new RerankDTO();
				rerankDTO.setText(dto.getInputInfoDTO().getDialogue());
				rerankDTO.setRecallList(vectorRecallResult);
				//个性化重排配置
				rerankDTO.setConfig(noteSearchProperties.getVectorRerankConfig());
				rerankDTO.setRerankType(KnowledgeRerankTypeEnum.VECTOR.getType());
				vectorRerankResult.addAll(ragExternalService.rerank(rerankDTO));
				if (CollUtil.isEmpty(vectorRerankResult)) {
					log.info("【笔记正文搜索】【rag算法重排】【向量】结果为空");
				}
			} catch (Exception e) {
				log.error("【笔记正文搜索】【rag算法重排】【向量】，异常：{}", e.getMessage(), e);
			} finally {
				countDownLatch.countDown();
				log.info("【笔记正文搜索】【rag算法重排】【向量】，耗时：{}，结果数：{}", StopWatchUtil.logTime(stopWatch), vectorRerankResult.size());
				StopWatchUtil.clearDuration();
			}
		});

		// 【关键字】算法重排
		List<RerankResultVO> keywordRerankResult = new ArrayList<>();
		multiRouteRerankThreadPool.submit(() -> {
			// 开启计时
			StopWatch stopWatch = StopWatchUtil.createStarted();
			try {
				MDC.setContextMap(logMap);
				// 把主线程ThreadLocal信息set到子线程
				RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);

				RerankDTO rerankDTO = new RerankDTO();
				rerankDTO.setText(dto.getInputInfoDTO().getDialogue());
				rerankDTO.setRecallList(keywordRecallResult);
				//个性化重排配置配置
				rerankDTO.setConfig(noteSearchProperties.getKeywordRerankConfig());
				rerankDTO.setRerankType(KnowledgeRerankTypeEnum.KEYWORD.getType());
				keywordRerankResult.addAll(ragExternalService.rerank(rerankDTO));
				if (CollUtil.isEmpty(keywordRerankResult)) {
					log.info("【笔记正文搜索】【rag算法重排】【关键字】结果为空");
				}
			} catch (Exception e) {
				log.error("【笔记正文搜索】【rag算法重排】【关键字】，异常：{}", e.getMessage(), e);
			} finally {
				countDownLatch.countDown();
				log.info("【笔记正文搜索】【rag算法重排】【关键字】，耗时：{}，结果数：{}", StopWatchUtil.logTime(stopWatch), keywordRerankResult.size());
				StopWatchUtil.clearDuration();
			}
		});

		try {
			boolean noTimeout = countDownLatch.await(knowledgeDialogueProperties.getSearchTimeout(), TimeUnit.SECONDS);
			if (!noTimeout) {
				dto.getSseEmitterOperate().setSseName(SseNameEnum.KNOWLEDGE_RERANK_TIMEOUT.getCode());
				log.info("【笔记正文搜索】【rag算法重排】超时，超时时间：{}s", knowledgeDialogueProperties.getSearchTimeout());
			}
		} catch (Exception e) {
			log.error("【笔记正文搜索】【rag算法重排】线程等待异常", e);
		}
		// 按顺序合并（关键字在前，向量在后）
		// 先加关键字结果
		rerankResult.addAll(keywordRerankResult); 
		// 再加向量结果
		rerankResult.addAll(vectorRerankResult);  
		// 过滤：同一个段落ID 只保留最大分块
		List<RerankResultVO> distinctRerankResult = filterText(rerankResult);
		log.info("【笔记正文搜索】【rag算法重排】结束，总结果数：{}，总耗时：{}，去重前结果数：{}",
				distinctRerankResult.size(), StopWatchUtil.logTime(allStopWatch), rerankResult.size());

		LogCommonUtils.printlnStrLog("【笔记正文搜索】【rag算法重排】合并去重后结果", JsonUtil.toJson(distinctRerankResult));
		return distinctRerankResult;
	}

	private List<RerankResultVO> filterText(List<RerankResultVO> originalList) {
		// 1. 处理 paragraphId 不为 null 的条目（按原逻辑分组）
		Map<String, Optional<RerankResultVO>> groupedById = originalList.stream()
				.filter(item -> item.getDocument() != null && item.getDocument().getParagraphId() != null)
				.collect(Collectors.groupingBy(
						item -> item.getDocument().getParagraphId(),
						Collectors.maxBy(Comparator.comparingInt(
								item -> item.getDocument().getText() != null
										? item.getDocument().getText().length()
										: 0
						))
				));

		// 2. 处理 paragraphId 为 null 的条目（按文本包含关系去重）
		List<RerankResultVO> nullParagraphItems = originalList.stream()
				.filter(item -> item.getDocument() != null && item.getDocument().getParagraphId() == null)
				.collect(Collectors.toList());

		List<RerankResultVO> filteredNullItems = new ArrayList<>();
		for (RerankResultVO item : nullParagraphItems) {
			String text = item.getDocument().getText();
			if (text == null) {
				continue;
			}
			boolean shouldAdd = true;
			// 使用迭代器安全删除
			for (Iterator<RerankResultVO> iterator = filteredNullItems.iterator(); iterator.hasNext();) {
				RerankResultVO selectedItem = iterator.next();
				String selectedText = selectedItem.getDocument().getText();
				if (selectedText != null) {
					if (selectedText.contains(text)) {
						shouldAdd = false;
						break;
					}
					if (text.contains(selectedText)) {
						iterator.remove();
					}
				}
			}
			if (shouldAdd) {
				filteredNullItems.add(item);
			}
		}

		// 3. 合并所有结果（包括分组后的和去重后的null条目）
		List<RerankResultVO> combinedResults = new ArrayList<>();
		groupedById.values().stream()
				.filter(Optional::isPresent)
				.map(Optional::get)
				.forEach(combinedResults::add);
		combinedResults.addAll(filteredNullItems);

		// 4. 对所有结果统一进行文本截取处理
		return combinedResults.stream()
				.peek(item -> {
					if (item.getDocument().getText() != null) {
						String text = item.getDocument().getText();
						int index = text.indexOf(FILE_CONTENT_STR);
						if (index >= 0) {
							String newText = text.substring(index + FILE_CONTENT_STR.length());
							//提取标题之后的内容
							String[] parts = newText.split("\t\t", 3); // 限制分割为3部分
							if (parts.length >= 3) {
								newText = parts[2];
							}
							if (!isBlankOrSpecialWhitespace(newText)) {
								item.getDocument().setText(trimAllWhitespace(newText));
							} else {
								item.getDocument().setText(null);
							}
						}
					}
				})
				.collect(Collectors.toList());
	}
	private String trimAllWhitespace(String str) {
		// 匹配所有空白字符（包括\u00A0等）在开头或结尾
		return str.replaceAll("^[\\s\\u00A0]+|[\\s\\u00A0]+$", "");
	}

	private boolean isBlankOrSpecialWhitespace(String str) {
		return str == null || str.trim().isEmpty() || str.matches("^[\\s\\u00A0\\u200B]+$");
	}

	private List<String> keywordExtract(ChatAddHandleDTO addDTO) {
		if (addDTO.getIntentionVO() == null
				|| addDTO.getIntentionVO().getIntentionInfoList() == null) {
			log.warn("【笔记正文搜索】无意图信息,无法进行关键字提取");
			return new ArrayList<>();
		}
		Set<String> keywordSet = new LinkedHashSet<>();
		// 使用意图实体获取关键字
		addDTO.getIntentionVO().getIntentionInfoList().forEach(intentionInfo -> {
			if (intentionInfo.getEntityList() == null) {
				return;
			}
			intentionInfo.getEntityList().forEach(entity -> {
				keywordSet.addAll(entity.getTimeList());
				keywordSet.addAll(entity.getPlaceList());

				if (entity.getLabelList() != null) {
					entity.getLabelList().forEach(label ->
							keywordSet.addAll(label.getValue()));
				}

				if (entity.getMetaDataList() != null) {
					entity.getMetaDataList().forEach(metaData ->
							keywordSet.addAll(metaData.getValue()));
				}

				if (entity.getPersonList() != null) {
					entity.getPersonList().forEach(person ->
							keywordSet.addAll(person.getValue()));
				}

				keywordSet.addAll(entity.getImageNameList());
				keywordSet.addAll(entity.getSuffixList());
				keywordSet.addAll(entity.getSenderList());
			});
		});
		// 打印过滤前的关键字
		log.info("【笔记正文搜索】【关键字】筛选之前：{}",JsonUtil.toJson(keywordSet));

		// 3. 获取排除关键字
		List<String> excludeKeywords = noteSearchProperties.getExcludeKeywords();
		boolean shouldFilter = !isEmpty(excludeKeywords);

		return keywordSet.stream()
				.filter(Objects::nonNull)
				.filter(keyword -> !shouldFilter || !excludeKeywords.contains(keyword))
				.collect(toList());
	}

	/**
	 * 设置【继续执行大模型回答DTO】
	 */
	private void setContinueTextSseDTO(ChatAddHandleDTO handleDTO) {
		ChatConfigEntity chatConfigEntity = chatConfigServiceDomainService.getUserCanUseModel(handleDTO.getReqDTO().getUserId(),
				RequestContextHolder.getPhoneNumber(), handleDTO.getAssistantEnum(), handleDTO.getBusinessType());
		handleDTO.setContinueTextSseDTO(new ContinueTextSseDTO(chatConfigEntity.getModelType(), handleDTO.getInputInfoDTO().isEnableForceNetworkSearch()));
	}
}
