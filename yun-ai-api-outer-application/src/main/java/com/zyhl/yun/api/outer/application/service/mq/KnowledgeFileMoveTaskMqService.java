package com.zyhl.yun.api.outer.application.service.mq;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileTaskEntity;


/**
 * interfaceName: KnowledgeFileMoveTaskMqService
 * description: 个人知识库 - 文件移动接口类
 *
 * <AUTHOR>
 * @date 2025/5/29
 */
public interface KnowledgeFileMoveTaskMqService {

    /**
     * 发送文件删除消息
     *
     * @param entityList 文件实体列表
     */
    void sendMq(UserKnowledgeFileTaskEntity entityList);

}
