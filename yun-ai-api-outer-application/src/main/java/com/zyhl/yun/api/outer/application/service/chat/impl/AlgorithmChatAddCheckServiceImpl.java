package com.zyhl.yun.api.outer.application.service.chat.impl;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.client.blian.ExternalBlianClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.blian.vo.TextModelFileVO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelFileUploadReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.YunDiskClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.FileGetContentReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.AIFileVO;
import com.zyhl.hcy.yun.ai.common.platform.third.dto.GetUserInfoByPhoneNumberRespDTO;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.LastDialogueInfoDTO;
import com.zyhl.yun.api.outer.application.dto.MailAttachmentDTO;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatAddCheckService;
import com.zyhl.yun.api.outer.application.service.external.MailAttachService;
import com.zyhl.yun.api.outer.application.util.RegexUtils;
import com.zyhl.yun.api.outer.application.vo.MailAttachVO;
import com.zyhl.yun.api.outer.config.TextFileModeProperties;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiPromptEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.entity.TaskAiAbilityEntity;
import com.zyhl.yun.api.outer.domainservice.IImageCommonService;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.ImageParamTypeEnum;
import com.zyhl.yun.api.outer.enums.ImageSuffixEnum;
import com.zyhl.yun.api.outer.enums.ImageTransmissionTypeEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.task.FileExpiredStatusEnum;
import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;
import com.zyhl.yun.api.outer.external.service.EOSExternalService;
import com.zyhl.yun.api.outer.repository.AlgorithmAiPromptRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.repository.TaskAiAbilityRepository;
import com.zyhl.yun.api.outer.vo.TaskRespParamVO;
import com.zyhl.yun.outer.datahelper.util.constant.CacheConfigConstants;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/6/4 13:13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlgorithmChatAddCheckServiceImpl implements AlgorithmChatAddCheckService {

    private final EOSExternalService eosExternalService;

    private final TaskAiAbilityRepository aiAbilityRepository;

    private final AlgorithmChatContentRepository contentRepository;

    private final MailAttachService mailAttachService;

    private final ExternalBlianClient externalBlianClient;

    private final YunDiskClient yunDiskClient;

    private final IImageCommonService imageCommonService;

    private final AlgorithmAiPromptRepository algorithmAiPromptRepository;

    private final TextFileModeProperties textFileModeProperties;

    private final SourceChannelsProperties sourceChannelsProperties;

    @Resource(name = "longUploadFileThreadPool")
    private ExecutorService longUploadFileThreadPool;

    @Resource(name = "getFileContentThreadPool")
    private ExecutorService getFileContentThreadPool;

    @Value("${dialogue.prompt-length:100}")
    private Integer promptLength;

    /**
     * 获取上次对话信息
     *
     * @param dialogueId 对话id
     * @param userId     用户id
     * @return 上次对话信息DTO
     */
    @Override
    public LastDialogueInfoDTO getLastDialogueInfo(String dialogueId, String userId) {
        try {
            // 获取对话内容信息
            AlgorithmChatContentEntity contentEntity = contentRepository.getByIdUserId(Long.parseLong(dialogueId), userId);
            if (contentEntity == null) {
                log.info("获取上次对话信息，对话信息不存在 dialogueId:{}", dialogueId);
                return null;
            }

            // 获取任务信息
            TaskAiAbilityEntity taskEntity = aiAbilityRepository.getTaskEntity(contentEntity.getTaskId());
            if (taskEntity == null || !TaskStatusEnum.PROCESS_FINISH.getCode().equals(taskEntity.getTaskStatus())) {
                log.info("获取上次对话信息，任务信息无效 taskId:{}", contentEntity.getTaskId());
                return null;
            }

            if (null != taskEntity.getFileExpiredStatus() && taskEntity.getFileExpiredStatus()
                    .intValue() == FileExpiredStatusEnum.EXPIRED.getCode().intValue()) {
                log.info("获取上次对话信息，文件信息过期 taskId:{}", contentEntity.getTaskId());
                throw new YunAiBusinessException(AiResultCode.CODE_10000205.getCode(), AiResultCode.CODE_10000205.getMsg());
            }

            // 扩展信息不存在
            if (CharSequenceUtil.isEmpty(taskEntity.getRespParam())) {
                log.info("获取上次对话信息，任务表扩展信息为空 dialogueId:{} | taskEntity:{}", dialogueId, JsonUtil.toJson(taskEntity));
                return null;
            }

            // 扩展信息转换异常
            Optional<TaskRespParamVO> optional = JSONUtil.toList(taskEntity.getRespParam(), TaskRespParamVO.class)
                    .stream().findFirst();
            if (!optional.isPresent()) {
                log.info("获取上次对话信息，扩展信息转换异常 dialogueId:{} | taskEntity:{}", dialogueId, JsonUtil.toJson(taskEntity));
                return null;
            }

            // 资源类型非图片不处理
            TaskRespParamVO taskRespParamVO = optional.get();
            if (!taskRespParamVO.getOutResourceType().equals(ResourceTypeEnum.PICTURE.getType())) {
                log.info("获取上次对话信息，资源类型非图片不处理 dialogueId:{} | taskEntity:{}", dialogueId, JsonUtil.toJson(taskEntity));
                return null;
            }

            // EOS对象存储类型处理
            if (null != taskRespParamVO.getImageTransmissionType() &&
                    ImageTransmissionTypeEnum.EOS.getCode() == taskRespParamVO.getImageTransmissionType()) {
                String fileSuffix = ImageSuffixEnum.getByAlgorithmCode(taskEntity.getAlgorithmCode()).getCode();
                /*
                // 获取EOS对象存储url
                String inlineUrl = eosExternalService.getFileUrl(taskRespParamVO.getOutResourceId(),
                        (taskRespParamVO.getOutResourceId() + StrPool.DOT + fileSuffix),
                        eosFileExpireConfig.getExpireTime());
                if (StringUtils.isBlank(inlineUrl)) {
                    log.info("获取上次对话信息，获取EOS对象存储url为空 dialogueId:{} | taskEntity:{}", dialogueId, JsonUtil.toJson(taskEntity));
                    return null;
                }
                */
                //获取文件共享存储
        		String eosObjectKey = taskRespParamVO.getOutResourceId();
        		String localPath = imageCommonService.createPathBaseOnDate(eosObjectKey, fileSuffix, userId);
				eosExternalService.downloadEos(localPath, eosObjectKey);
                if (StringUtils.isBlank(localPath)) {
					log.info("获取上次对话信息，获取EOS对象存储文件共享存储为空 eosObjectKey:{}, dialogueId:{} | taskEntity:{}", eosObjectKey,
							dialogueId, JsonUtil.toJson(taskEntity));
                    return null;
                }
                // 设置EOS对象存储类型处理参数
                return LastDialogueInfoDTO.builder()
                        .localPath(localPath)
                        .imageExt(fileSuffix)
                        .imageParamType(ImageParamTypeEnum.LOCAL_PATH.getCode())
                        .build();
            }

            // 个人云存储类型处理
            return LastDialogueInfoDTO.builder()
                    .fileId(taskRespParamVO.getOutResourceId())
                    .imageParamType(ImageParamTypeEnum.FILE_ID.getCode())
                    .build();

        } catch (Exception e) {
            log.error("获取上次对话信息异常，dialogueId:{} | e:", dialogueId, e);
            if (e instanceof YunAiBusinessException) {
                throw (YunAiBusinessException) e;
            }
        }

        return null;
    }

    @Override
    public String getDialoguePrompt(String prompt,String channel) {
        // 输入校验
        if (prompt == null || prompt.trim().isEmpty()) {
            log.info("输入的prompt为空或仅包含空格");
            return prompt;
        }
        //少于100字才做关键字匹配prompt, 大于的直接返回原来的
        if (promptLength.compareTo(StringUtils.length(prompt.trim())) < 0) {
            return prompt;
        }
        return ((AlgorithmChatAddCheckServiceImpl) AopContext.currentProxy()).getDialoguePromptByCache(prompt, channel);
    }

    /**
     * 根据云盘文件id列表获取文件共享存储路径列表
     *
     * @param fileIdList     云盘文件id列表
     * @param userId         用户id
     * @param fileListFilter 文件列表是否过滤 true是 false否
     * @return 上传文件列表vo
     */
    @Override
	public List<String> getFilesByCloudDiskDocumentLocalPath(List<String> fileIdList, String userId,
			boolean fileListFilter) {
		if (!fileListFilter) {
			// 校验资源id参数不能为空
			if (fileIdList == null || CollectionUtils.isEmpty(fileIdList)) {
				return null;
			}

			fileIdList = getFilterList(fileIdList);
			if (CollUtil.isEmpty(fileIdList)) {
				return null;
			}
		}

		// 大模型上传qps限制，不允许超过3个
		if (fileIdList.size() > textFileModeProperties.getFileNum()) {
			log.error("根据云盘文档列表获取上传文件LocalPath列表,文件数量过多 | dto:{}", JsonUtil.toJson(fileIdList));
			throw new YunAiBusinessException(AiResultCode.CODE_10000018.getCode(), AiResultCode.CODE_10000018.getMsg());
		}

		List<AIFileVO> fileVOList = getFileList(fileIdList, userId);

		// 校验文件下载地址
		log.info("根据云盘文档列表获取上传文件LocalPath列表 获取文件列表信息 fileVOList:{}", JsonUtil.toJson(fileIdList));
		if (CollUtil.isEmpty(fileVOList)) {
			return null;
		}
		List<String> localPathList = new ArrayList<>();
		for (AIFileVO fileVo : fileVOList) {
			String localPath = null;
			try {
				localPath = imageCommonService.urlToLocalPath(fileVo.getContent(), fileVo.getFileSuffix(), userId);
				if (StringUtils.isNotBlank(localPath)) {
					localPathList.add(localPath);
				}
			} catch (Exception e) {
				log.error("文件下载失败 userId:{}, getContentId:{}, getContent:{} error:", userId, fileVo.getContentId(),
						fileVo.getContent(), e);
			}
		}
		if (CollUtil.isEmpty(localPathList)) {
			throw new YunAiBusinessException(AiResultCode.CODE_10000017.getCode(), AiResultCode.CODE_10000017.getMsg());
		}
		return localPathList;
	}
    /**
     * 根据云盘文件id列表获取上传文件列表
     *
     * @param fileIdList     云盘文件id列表
     * @param userId         用户id
     * @param fileListFilter 文件列表是否过滤 true是 false否
     * @return 上传文件列表vo
     */
    @Override
    public List<TextModelFileVO> getFilesByCloudDiskDocument(List<String> fileIdList, String userId, boolean fileListFilter) {
        if (!fileListFilter) {
            // 校验资源id参数不能为空
            if (fileIdList == null || CollectionUtils.isEmpty(fileIdList)) {
                return null;
            }

            fileIdList = getFilterList(fileIdList);
            if (CollUtil.isEmpty(fileIdList)) {
                return null;
            }
        }

        // 大模型上传qps限制，不允许超过3个
        if (fileIdList.size() > textFileModeProperties.getFileNum()) {
            log.error("根据云盘文档列表获取上传文件列表,文件数量过多 | dto:{}", JsonUtil.toJson(fileIdList));
            throw new YunAiBusinessException(AiResultCode.CODE_10000018.getCode(), AiResultCode.CODE_10000018.getMsg());
        }

        List<AIFileVO> fileVOList = getFileList(fileIdList, userId);

        // 校验文件下载地址
        log.info("根据云盘文档列表获取上传文件列表 获取文件列表信息 fileVOList:{}", JsonUtil.toJson(fileIdList));
        if (CollUtil.isEmpty(fileVOList)) {
            return null;
        }

        // 上传文档到大模型
        List<TextModelFileVO> uploadFileVOList = new ArrayList<>();
        List<Future<TextModelFileVO>> uploadFileFutures = new ArrayList<>();
        for (AIFileVO fileVo : fileVOList) {
            Future<TextModelFileVO> future = this.longUploadFileThreadPool.submit(() -> {
                String localPath = null;
                try {
                    localPath = imageCommonService.urlToLocalPath(fileVo.getContent(), fileVo.getFileSuffix(), userId);
                    // 上传到文本大模型
                    TextModelFileUploadReqDTO uploadReq = getTextModelFileUploadReqDTO(fileVo.getFileName(),
                            fileVo.getFileSuffix(), localPath);
                    TextModelFileVO modelFile = externalBlianClient.longUploadFile(uploadReq);
                    log.info("根据云盘文档列表获取上传文件列表 上传到文本大模型 uploadReq:{} | fileVo:{}", JsonUtil.toJson(uploadReq),
                            JsonUtil.toJson(fileVo));
                    if (modelFile == null) {
                        log.error("根据云盘文档列表获取上传文件列表 文件上传失败 fileVo:{}", JsonUtil.toJson(fileVo));
                        throw new YunAiBusinessException(AiResultCode.CODE_10000017.getCode(),
                                AiResultCode.CODE_10000017.getMsg());
                    }
                    return modelFile;
                } finally {
                    // 删除localPath
                    deleteLocalFile(localPath);
                }
            });
            uploadFileFutures.add(future);
        }

        // 获取上传文件线程结果
        for (Future<TextModelFileVO> ufileFuture : uploadFileFutures) {
            try {
                TextModelFileVO vo = ufileFuture.get();
                if (null != vo) {
                    uploadFileVOList.add(vo);
                }
            } catch (Exception e) {
                log.error("根据云盘文档列表获取上传文件后上传到大模型 error:", e);
                if (null != e && null != e.getCause() && e.getCause() instanceof YunAiBusinessException) {
                    throw (YunAiBusinessException) e.getCause();
                }
            }
        }

        return uploadFileVOList;
    }

	private List<AIFileVO> getFileList(List<String> fileIdList, String userId) {
		// 获取文件
        List<AIFileVO> fileVOList = new ArrayList<>();
        List<Future<AIFileVO>> fileFutures = new ArrayList<>();
        for (String fileId : fileIdList) {
            Future<AIFileVO> future = this.longUploadFileThreadPool.submit(() -> {
                try {
                    // 获取文件信息
                    AIFileVO fileVo = getFileContent(fileId, userId);

                    // 校验文件大小
                    if (fileVo.getFileSize() >= textFileModeProperties.getFileSize()) {
                        log.error("根据云盘文档列表获取上传文件列表 文件格式过大 fileVo:{}", JsonUtil.toJson(fileVo));
                        throw new YunAiBusinessException(AiResultCode.CODE_10000006.getCode(),
                                AiResultCode.CODE_10000006.getMsg());
                    }

                    // 文件后缀校验
                    String fileSuffix = StringUtils.isBlank(fileVo.getFileSuffix()) ? null : fileVo.getFileSuffix().toLowerCase();
                    if (null == fileSuffix || !textFileModeProperties.getFileSuffixList().contains(fileSuffix)) {
                        log.error("根据云盘文档列表获取上传文件列表 不支持的文件格式 fileVo:{}", JsonUtil.toJson(fileVo));
                        throw new YunAiBusinessException(AiResultCode.CODE_10000019.getCode(), AiResultCode.CODE_10000019.getMsg());
                    }

                    return fileVo;
                } catch (Exception e) {
                    log.error("longUploadFileThreadPool 根据云盘文档列表获取上传文件列表 fileId:{}, error:", fileId, e);
                    throw e;
                }
            });
            fileFutures.add(future);
        }

        // 获取文件线程结果
        for (Future<AIFileVO> fileFuture : fileFutures) {
            try {
                AIFileVO vo = fileFuture.get();
                if (null != vo) {
                    fileVOList.add(vo);
                }
            } catch (Exception e) {
                log.error("根据云盘文档列表获取上传文件列表 处理文件异常 error:", e);
				if (null != e && null != e.getCause() && e.getCause() instanceof YunAiBusinessException) {
                    throw (YunAiBusinessException) e.getCause();
                }
            }
        }
		return fileVOList;
	}

    /**
     * 获取文件信息
     *
     * @param fileId 文件id
     * @param userId 用户id
     * @return 文件信息对象AIFileVO
     */
    private AIFileVO getFileContent(String fileId, String userId) {
        try {
            FileGetContentReqDTO fileGetContentReqDTO = new FileGetContentReqDTO();
            fileGetContentReqDTO.setFileId(fileId);

            GetUserInfoByPhoneNumberRespDTO userInfo = new GetUserInfoByPhoneNumberRespDTO();
            userInfo.setUserDomainId(Long.valueOf(userId));
            fileGetContentReqDTO.setUserInfo(userInfo);

            //获取原图
            fileGetContentReqDTO.setIsOriginal(true);
            return yunDiskClient.getFileContent(fileGetContentReqDTO);

        } catch (Exception e) {
            log.error("根据云盘文档列表获取上传文件列表 获取云盘文件信息失败 fileId:{} | userId:{} | e:", fileId, userId, e);
            throw new YunAiBusinessException(ResultCodeEnum.FILE_ID_INFO_ERROR);
        }
    }

    /**
     * 根据邮件附件列表获取文件共享存储路径列表
     *
     * @param attachmentDto 邮箱附件列表dto
     * @param dto           会话输入dto
     * @return 上传文件列表vo
     */
    @Override
	public List<String> getFilesByEmailAttachmentLocalPath(MailAttachmentDTO attachmentDto, AlgorithmChatAddDTO dto) {
		// 过滤去重和去除前后空格
		List<String> attachNameList = getFilterList(attachmentDto.getAttachNameList());
		List<String> fileIdList = getFilterList(attachmentDto.getFileIdList());
		// 大模型上传qps限制，不允许超过3个
		if (attachNameList.size() + fileIdList.size() > textFileModeProperties.getFileNum()) {
			log.error("根据邮件文档列表获取上传文件LocalPath列表,文件数量过多 | dto:{}", JsonUtil.toJson(attachmentDto));
			throw new YunAiBusinessException(AiResultCode.CODE_10000018.getCode(), AiResultCode.CODE_10000018.getMsg());
		}

		List<String> localPathList = new ArrayList<>();
		// 获取邮箱附件列表
		if (!CollUtil.isEmpty(attachNameList)) {
			Map<String, Object> map = dto.getContent().getExtInfoMap();
			String sid = (String) map.getOrDefault("sid", "");
			String rmKey = (RegexUtils.extractAfterEqualSymbolValue((String) map.getOrDefault("rmkey", "")));
			List<MailAttachVO> attachList = mailAttachService.getMailFiles(dto.getUserId(), attachmentDto.getMailId(),
					sid, rmKey, attachNameList);
			log.info("根据邮件附件列表获取上传文件LocalPath列表 获取邮箱附件列表 attachmentDto:{} | attachList:{}",
					JsonUtil.toJson(attachmentDto), JsonUtil.toJson(attachList));
			if (CollUtil.isNotEmpty(attachList)) {
				// 遍历处理
				for (MailAttachVO mailAttachVO : attachList) {
					if (StringUtils.isNotBlank(mailAttachVO.getLocalFilePath())) {
						localPathList.add(mailAttachVO.getLocalFilePath());
					}
				}
			}
		}
		if (CollUtil.isEmpty(localPathList)) {
			throw new YunAiBusinessException(AiResultCode.CODE_10000017.getCode(), AiResultCode.CODE_10000017.getMsg());
		}
		return localPathList;
	}   
    /**
     * 根据邮件附件列表获取上传文件列表
     *
     * @param attachmentDto 邮箱附件列表dto
     * @param dto           会话输入dto
     * @return 上传文件列表vo
     */
    @Override
    public List<TextModelFileVO> getFilesByEmailAttachment(MailAttachmentDTO attachmentDto, AlgorithmChatAddDTO dto) {
        List<TextModelFileVO> voList = new ArrayList<>();
        // 过滤去重和去除前后空格
        List<String> attachNameList = getFilterList(attachmentDto.getAttachNameList());
        List<String> fileIdList = getFilterList(attachmentDto.getFileIdList());
        // 大模型上传qps限制，不允许超过3个
        if (attachNameList.size() + fileIdList.size() > textFileModeProperties.getFileNum()) {
            log.error("根据邮件文档列表获取上传文件列表,文件数量过多 | dto:{}", JsonUtil.toJson(attachmentDto));
            throw new YunAiBusinessException(AiResultCode.CODE_10000018.getCode(), AiResultCode.CODE_10000018.getMsg());
        }

        // 获取邮箱附件列表
        if (!CollUtil.isEmpty(attachNameList)) {
            Map<String, Object> map = dto.getContent().getExtInfoMap();
            String sid = (String) map.getOrDefault("sid", "");
            String rmKey = (RegexUtils.extractAfterEqualSymbolValue((String) map.getOrDefault("rmkey", "")));
            List<MailAttachVO> attachList = mailAttachService.getMailFiles(dto.getUserId(), attachmentDto.getMailId(), sid, rmKey, attachNameList);
            log.info("根据邮件附件列表获取上传文件列表 获取邮箱附件列表 attachmentDto:{} | attachList:{}", JsonUtil.toJson(attachmentDto),
                    JsonUtil.toJson(attachList));

            // 遍历处理
            CountDownLatch costDownLatch = new CountDownLatch(attachList.size());
            for (MailAttachVO mailAttachVO : attachList) {
                this.longUploadFileThreadPool.execute(() -> {
                    try {
                        // 文件后缀校验
                        String fileSuffix = StringUtils.isBlank(mailAttachVO.getFileSuffix()) ? null : mailAttachVO.getFileSuffix().toLowerCase();
                        if (null == fileSuffix || !textFileModeProperties.getFileSuffixList().contains(fileSuffix)) {
                            log.error("根据邮件附件列表获取上传文件列表 不支持的文件格式 mailAttachVO:{}", JsonUtil.toJson(mailAttachVO));
                            throw new YunAiBusinessException(AiResultCode.CODE_10000019.getCode(), AiResultCode.CODE_10000019.getMsg());
                        }

                        // 上传到文本大模型
                        TextModelFileUploadReqDTO uploadReq = getTextModelFileUploadReqDTO(mailAttachVO.getFileName(),
                                mailAttachVO.getFileSuffix(), mailAttachVO.getLocalFilePath());
                        TextModelFileVO fileVo = externalBlianClient.longUploadFile(uploadReq);
                        log.info("根据邮件附件列表获取上传文件列表 上传到文本大模型 uploadReq:{} | fileVo:{}",
                                JsonUtil.toJson(uploadReq), JsonUtil.toJson(fileVo));
                        if (fileVo == null) {
                            log.error("根据邮件附件列表获取上传文件列表 文件上传失败 mailAttachVO:{}", JsonUtil.toJson(mailAttachVO));
                            throw new YunAiBusinessException(AiResultCode.CODE_10000017.getCode(), AiResultCode.CODE_10000017.getMsg());
                        }
                        // 添加文件信息
                        voList.add(fileVo);
                    } finally {
                    	// 删除localPath
                        deleteLocalFile(mailAttachVO.getLocalFilePath());
                        // 递减计数器并释放等待的线程
                        costDownLatch.countDown();
                    }
                });
            }

            try {
                costDownLatch.await();
            } catch (Exception e) {
                log.error("根据邮件附件列表获取上传文件列表 文件上传失败 attachList:{}", JsonUtil.toJson(attachList));
            }
        }

        // 获取云盘文件信息
        if (!CollUtil.isEmpty(fileIdList)) {
            List<TextModelFileVO> fileVOList = getFilesByCloudDiskDocument(fileIdList, dto.getUserId(), true);
            if (!CollUtil.isEmpty(fileVOList)) {
                voList.addAll(fileVOList);
            }
        }

        return voList;
    }


    /**
     * 缓存获取对话prompt
     * <br><b>权重展示</b>，以下展示为关键字提示词使用的规则:
     * <table cellspacing=2 cellpadding=3 border=1 width=70%">
     *     <tr style="background-color: rgb(204, 204, 255);">
     *         <th align=left>ID
     *         <th align=left>PROMPT_KEY
     *         <th align=left>PROMPT_NAME
     *         <th align=left>PROMPT_TEMPLATE
     *         <th align=left>WEIGHT
     *         <th align=left>RANGE (左开右闭)
     *     <tr style="background-color: rgb(218, 238, 203);">
     *         <td>1
     *         <td>SUMMARIZE
     *         <td>总结概况
     *         <td>帮我用俏皮一点的语言总结概括
     *         <td>1
     *         <td>(0, 1]
     *     <tr style="background-color: rgb(218, 238, 203);">
     *         <td>2
     *         <td>SUMMARIZE
     *         <td>总结概况
     *         <td>帮我用庄严一点的语言总结概括
     *         <td>3
     *         <td>(1, 4]
     *     <tr style="background-color: rgb(218, 238, 203);">
     *         <td>3
     *         <td>SUMMARIZE
     *         <td>总结概况
     *         <td>帮我用优美一点的语言总结概括
     *         <td>1
     *         <td>(4, 5]
     * </table>
     *
     * @param prompt the prompt
     * @return {@link String}
     * <AUTHOR>
     * @date 2024-6-27 18:47
     */
    @Cacheable(value = CacheConfigConstants.EXPIRE_30_MINUTES_CACHE,
            key = "'getDialoguePrompt' + #channel + #prompt.hashCode()",
            unless = "#result == null || #result.equals(#prompt)", cacheManager = CacheConfigConstants.REDIS_CACHE)
    public String getDialoguePromptByCache(String prompt,String channel) {
        try {
            List<AlgorithmAiPromptEntity> promptEntityList = algorithmAiPromptRepository.queryByPromptKey(prompt);
            if (CollectionUtils.isEmpty(promptEntityList)) {
                // 未匹配到prompt
                log.info("未匹配到prompt, prompt:{}", prompt);
                return prompt;
            }
            // 按大业务类型过滤
            SourceChannelsProperties.SourceChannel sourceChannel = sourceChannelsProperties.getByChannel(channel);
            if(sourceChannel == null){
                log.info("未匹配到channle:{}的渠道,请检查渠道配置/渠道号");
                return prompt;
            }
            String businessType = sourceChannel.getBusinessType();
            promptEntityList = promptEntityList.stream().filter(entity -> businessType.equals(entity.getBusinessType())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(promptEntityList)) {
                log.info("按渠道未匹配到prompt, prompt:{}，channel:{}", prompt,channel);
                return prompt;
            }
            log.info("匹配到prompt, prompt:{} | promptEntityList:{}", prompt, JsonUtil.toJson(promptEntityList));
            //获取全部权重
            int sum = calculateSumAndSetBounds(promptEntityList);
            // 处理权重总和为0的边界情况
            if (sum == 0) {
                log.info("权重总和为0, prompt:{}", prompt);
                return prompt;
            }
            //获取一个在sum范围内的随机值, 不包含0
            int randomInt = RandomUtil.randomInt(sum) + 1;
            //获取随机值在范围内的prompt
            Optional<AlgorithmAiPromptEntity> optional = promptEntityList.stream()
                    .filter(algoPrompt -> randomInt > algoPrompt.getLowerBound() && randomInt <= algoPrompt.getUpperBound())
                    .findFirst();
            if (optional.isPresent()) {
                AlgorithmAiPromptEntity algorithmAiPromptEntity = optional.get();
                log.info("匹配到prompt, prompt:{} | promptEntity:{}", prompt, JsonUtil.toJson(algorithmAiPromptEntity));
                return algorithmAiPromptEntity.getPromptTemplate();
            }
        } catch (Exception e) {
            log.error("获取对话prompt异常，prompt:{} | e:", prompt, e);
        }
        log.info("未匹配到prompt, prompt:{}", prompt);
        return prompt;
    }

    /**
     * 赋值权重
     *
     * @param promptEntityList the prompt entity list
     * @return {@link int}
     * <AUTHOR>
     * @date 2024-6-28 15:53
     */
    private int calculateSumAndSetBounds(List<AlgorithmAiPromptEntity> promptEntityList) {
        int sum = 0;
        for (AlgorithmAiPromptEntity algorithmAiPromptEntity : promptEntityList) {
            algorithmAiPromptEntity.setLowerBound(sum);
            sum += algorithmAiPromptEntity.getWeight();
            algorithmAiPromptEntity.setUpperBound(sum);
        }
        return sum;
    }

    /**
     * 删除指定的本地文件。
     *
     * @param localPath 本地文件的完整路径。
     * @return 如果文件被成功删除或文件不存在，则返回true；否则返回false。
     */
    private boolean deleteLocalFile(String localPath) {
        try {
            File file = new File(localPath);
            // 检查文件是否存在
            if (!file.exists()) {
                // 文件不存在，可以认为已经"删除"
                return true;
            }

            // 删除文件
            return file.delete();
        } catch (Exception e) {
            log.error("根据邮件附件列表获取上传文件列表 删除localPath异常 localPath:{} | e:", localPath, e);
        } finally {
            log.info("根据邮件附件列表获取上传文件列表 删除localPath localPath:{}", localPath);
        }

        return false;
    }

    /**
     * 过滤去重和去除前后空格处理
     *
     * @param list 字符串列表
     * @return 过滤去重和去除前后空格后的字符串列表
     */
    private List<String> getFilterList(List<String> list) {
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        return list.stream()
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取上传文件请求参数
     *
     * @param fileName   文件名称
     * @param fileSuffix 文件后缀
     * @param localPath  本地存储路径
     * @return TextModelFileUploadReqDTO对象
     */
    private TextModelFileUploadReqDTO getTextModelFileUploadReqDTO(String fileName, String fileSuffix, String localPath) {
        return TextModelFileUploadReqDTO.builder()
                .fileSuffix(fileSuffix)
                .fileName(fileName)
                .localFilePath(localPath)
                .build();
    }

}
