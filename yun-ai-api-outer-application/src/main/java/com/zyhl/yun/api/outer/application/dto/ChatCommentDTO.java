package com.zyhl.yun.api.outer.application.dto;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.yun.api.outer.enums.LikeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2024/1/6 16:37
 */
@Slf4j
@Data
public class ChatCommentDTO extends BaseDTO implements Serializable {

    /**
     * 用户自定义评论长度大小限制
     */
    private static final Integer CUSTOM_COMMENT_MAX_LENGTH = 1024;

    /**
     * 会话Id
     */
    private Long sessionId;

    /**
     * 当前对话Id（任务id）
     */
    private Long dialogueId;

    /**
     * 是否喜欢 0:不喜欢，1:喜欢
     *
     * @see com.zyhl.yun.api.outer.enums.LikeEnum
     */
    private Integer likeComment;

    private String defaultComment;

    private String customComment;

    /**
     * 参数校验
     *
     * @return 枚举
     */
    public AbstractResultCode checkParam() {
        if (sessionId == null) {
            log.info("【评论对话】会话id为空");
            return ResultCodeEnum.ERROR_PARAMS;
        } else if (dialogueId == null) {
            log.info("【评论对话】对话id为空");
            return ResultCodeEnum.ERROR_PARAMS;
        } else if (!LikeEnum.isExist(likeComment)) {
            log.info("【评论对话】评论状态不存在，likeComment：{}", likeComment);
            return ResultCodeEnum.ERROR_PARAMS;
        } else if (!CharSequenceUtil.isEmpty(customComment) && customComment.length() > CUSTOM_COMMENT_MAX_LENGTH) {
            log.info("【评论对话】评论内容过长，用户自定义评论不能超过{}个字，输入的评论字数为：{}", CUSTOM_COMMENT_MAX_LENGTH, customComment.length());
            return ResultCodeEnum.ERROR_PARAMS;
        }

        return checkUserId();
    }

}
