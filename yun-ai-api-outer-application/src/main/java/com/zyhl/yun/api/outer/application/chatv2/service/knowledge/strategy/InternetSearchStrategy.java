package com.zyhl.yun.api.outer.application.chatv2.service.knowledge.strategy;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.hcy.plugin.logger.util.LogUtil;
import com.zyhl.hcy.yun.ai.common.model.api.client.ali.vo.GenericSearchVO;
import com.zyhl.hcy.yun.ai.common.model.api.client.vo.TextModelBaseVo;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.hcy.yun.ai.common.model.api.utils.TextModelUtil;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.config.KnowledgeDialogueProperties;
import com.zyhl.yun.api.outer.config.knowledge.InternetSearchConfig;
import com.zyhl.yun.api.outer.external.AiInternetSearchExternalService;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.util.JsonHandleUtils;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 描述：知识库网络搜索策略
 *
 * <AUTHOR> zhumaoxian  2025/6/11 11:56
 */
@Slf4j
@Component
public class InternetSearchStrategy {

    @Resource
    private KnowledgeDialogueProperties knowledgeDialogueProperties;
    @Resource
    private TextModelExternalService textModelExternalService;
    @Resource
    private AiInternetSearchExternalService aiInternetSearchExternalService;

    // 创建线程池
    private final static int corePoolSize = 5;
    private final static int maxPoolSize = 50;
    private final static long keepAliveTime = 600;
    private final static int queueSize = 10000;
    private final static ThreadPoolExecutor pool = new ThreadPoolExecutor(corePoolSize, maxPoolSize, keepAliveTime, TimeUnit.SECONDS, new ArrayBlockingQueue<>(queueSize));

    /**
     * 联网搜索
     *
     * @param handleDTO 对话参数
     * @return
     */
    public List<GenericSearchVO> internetSearch(ChatAddHandleDTO handleDTO) {
        if (!handleDTO.getKnowledgeFlowInfo().getChatConfigVO().isEnableNetworkSearch()) {
            return new ArrayList<>();
        }

        // 提取关键字
        List<String> keywordList = extractKeyword(handleDTO.getInputInfoDTO().getDialogue());

        // 联网搜索
        List<GenericSearchVO> searchResult = search(keywordList);

        // 未启用大模型提取关键词
        if (!knowledgeDialogueProperties.getInternetSearchConfig().getExtractKeywordConfig().isEnabled()) {
            return searchResult;
        }

        // 搜索结果处理
        return resultHandle(searchResult);
    }


    /**
     * 提取关键词
     *
     * @param query 用户问题
     */
    private List<String> extractKeyword(String query) {
        List<String> list = new ArrayList<>();
        InternetSearchConfig internetSearchConfig = knowledgeDialogueProperties.getInternetSearchConfig();

        // 如果问题长度小于等于配置的查询长度，则直接返回问题
        if (query.length() <= internetSearchConfig.getQueryLimit()) {
            list.add(query);
            return list;
        }

        // 未启用大模型提取关键词
        if (!internetSearchConfig.getExtractKeywordConfig().isEnabled()) {
            list.add(query);
            return list;
        }

        // 启用大模型提取关键词

        // 提示词
        String userPrompt = internetSearchConfig.getExtractKeywordConfig().getUserPrompt()
                .replace("{query}", query)
                .replace("{time}", "{formatted_now}");

        // 当前对话
        TextModelMessageDTO messageDTO = new TextModelMessageDTO();
        messageDTO.setRole(TextModelRoleEnum.USER.getName());
        messageDTO.setContent(TextModelUtil.getValueSystemRoleContent(userPrompt));
        messageDTO.setCommand(internetSearchConfig.getExtractKeywordConfig().getSystemPrompt());

        // 大模型参数
        TextModelTextReqDTO reqDTO = new TextModelTextReqDTO();
        reqDTO.setTaskId(LogUtil.getTraceId());
        reqDTO.setUserId("");
        reqDTO.setModelValue(internetSearchConfig.getExtractKeywordConfig().getModelCode());
        reqDTO.setTextModelConfig(internetSearchConfig.getExtractKeywordConfig().getTextModelConfig());
        reqDTO.setMessageDtoList(Collections.singletonList(messageDTO));

        // 调大模型
        long start = System.currentTimeMillis();
        try {
            TextModelBaseVo textModelResult = textModelExternalService.textModelDialogue(reqDTO);

            // 大模型结果处理
            if (textModelResult.isSuccess() && ObjectUtil.isNotEmpty(textModelResult.getText())) {
                String resultStr = JsonHandleUtils.formatJsonStr(textModelResult.getText());
                list = JSON.parseArray(resultStr, String.class);
            }
        } catch (Exception e) {
            log.error("【知识库对话】【RAG重要节点日志】【联网搜索】【提取关键字】调用大模型异常 入参:{} | e:", JsonUtil.toJson(reqDTO), e);
        } finally {
            log.info("【知识库对话】【RAG重要节点日志】【联网搜索】【提取关键字】调用大模型结束，耗时：{} ", System.currentTimeMillis() - start);
        }

        if (ObjectUtil.isEmpty(list)) {
            list.add(query);
        }
        return list.subList(0, Math.min(list.size(), internetSearchConfig.getKeywordCount()));
    }

    /**
     * 联网搜索
     *
     * @param keywordList 关键词
     * @return 搜索结果
     */
    private List<GenericSearchVO> search(List<String> keywordList) {
        // 获取主线程的ThreadLocal信息，并且异步调用前，将父线程中的请求信息绑定给子线程
        RequestContextHolder.ThreadLocalInfo mainThreadLocalInfo = RequestContextHolder.getThreadLocalInfoAndBindingAttributes();
        Map<String, String> logMap = MDC.getCopyOfContextMap();

        List<GenericSearchVO> result = new ArrayList<>();

        // 多线程联网搜索
        CountDownLatch countDownLatch = new CountDownLatch(keywordList.size());
        for (String keyword : keywordList) {
            pool.submit(() -> {
                long startTime = System.currentTimeMillis();
                try {
                    // 日志、线程信息绑定
                    MDC.setContextMap(logMap);
                    RequestContextHolder.mainThreadInfoSetToSubThread(mainThreadLocalInfo);

                    List<GenericSearchVO> list = aiInternetSearchExternalService.knowledgeSearch(keyword);
                    result.addAll(list);
                } catch (Exception e) {
                    log.error("【知识库对话】【RAG重要节点日志】【联网搜索】网络搜索异常，异常信息：{}", e.getMessage(), e);
                } finally {
                    countDownLatch.countDown();
                    MDC.put(LogConstants.DURATION, String.valueOf(System.currentTimeMillis() - startTime));
                    log.info("【知识库对话】【RAG重要节点日志】【联网搜索】网络搜索结束，耗时：{}ms", System.currentTimeMillis() - startTime);
                    MDC.remove(LogConstants.DURATION);
                }
            });
        }

        try {
            boolean notimeout = countDownLatch.await(knowledgeDialogueProperties.getInternetSearchConfig().getNetworkTimeOut(), TimeUnit.SECONDS);
            if (!notimeout) {
                log.info("【知识库对话】【RAG重要节点日志】【联网搜索】联网搜索超时，超时时间：{}s", knowledgeDialogueProperties.getInternetSearchConfig().getNetworkTimeOut());
            }
        } catch (Exception e) {
            log.error("【知识库对话】【RAG重要节点日志】【联网搜索】线程等待异常", e);
        }

        return result;
    }

    /**
     * 结果处理
     *
     * @param list 搜索结果
     * @return 处理结果
     */
    private List<GenericSearchVO> resultHandle(List<GenericSearchVO> list) {
        if (ObjectUtil.isEmpty(list)) {
            return list;
        }

        // 数据替换，默认使用mainText，如果为空则使用Snippet
        for (GenericSearchVO vo : list) {
            if (ObjectUtil.isNotEmpty(vo.getMainText())) {
                vo.setSnippet(vo.getMainText());
            }
        }

        // 按分数倒序
        list.sort(Comparator.comparingDouble(GenericSearchVO::getScore).reversed());

        // 取前topK
        return list.subList(0, Math.min(list.size(), knowledgeDialogueProperties.getInternetSearchConfig().getResultTopk()));
    }


}
