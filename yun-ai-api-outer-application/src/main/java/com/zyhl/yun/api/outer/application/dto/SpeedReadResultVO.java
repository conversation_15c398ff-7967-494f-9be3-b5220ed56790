package com.zyhl.yun.api.outer.application.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.File;
import com.zyhl.yun.api.outer.enums.AiResultCode;

import java.io.Serializable;
import java.util.Date;

import cn.hutool.core.date.DatePattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description}
 * <p>
 *
 * <AUTHOR>
 * @since 4月15 2025
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SpeedReadResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 个人云文档信息
     */
    private File file;

    /**
     * 任务处理状态 -1--处理失败 0--处理中 1--处理成功
     */
    private Integer status;

    /**
     * 总结
     */
    private String summary;

    /**
     * 大纲
     */
    private String outline;
    /**
     * 脑图
     */
    private String mindMap;

    /**
     * 全文--暂不返回
     */
    //@Deprecated
    private String fullText;

    /**
     * 翻译--暂不返回
     */
    @Deprecated
    private String translation;

    /**
     * 失败原因
     */
    private String errorMessage;

    /**
     * 关联的会话ID
     */
    private String sessionId;

    /**
     * 创建时间，RFC 3339，2019-08-20T06:51:27.292+08:00
     */
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date createdAt;
    /**
     * 更新时间，RFC 3339，2019-08-20T06:51:27.292+08:00
     */
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 错误的时候过渡字段
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private AiResultCode aiResultCode;
}
