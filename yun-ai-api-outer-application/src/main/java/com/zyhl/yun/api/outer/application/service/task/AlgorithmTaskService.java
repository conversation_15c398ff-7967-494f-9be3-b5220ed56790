package com.zyhl.yun.api.outer.application.service.task;

import com.zyhl.yun.api.outer.application.dto.AITaskResultPageReqDTO;
import com.zyhl.yun.api.outer.application.dto.AiTaskResultReqDTO;
import com.zyhl.yun.api.outer.domain.vo.common.PageInfoVO;
import com.zyhl.yun.api.outer.vo.AlgorithmTaskListResultVO;
import com.zyhl.yun.api.outer.vo.AlgorithmTaskResultVO;

/**
 * AI算法任务接口
 *
 * <AUTHOR>
 */
public interface AlgorithmTaskService {


    /**
     * 查询算法任务结果信息
     *
     * @param dto 任务结果信息请求入参req
     * @return 算法任务结果VO
     */
    AlgorithmTaskResultVO getAiTaskResult(AiTaskResultReqDTO dto);

    /**
     * 查询算法任务结果信息列表 历史记录
     *
     * @param dto 任务结果信息请求入参分页req
     * @return 算法任务结果VO列表
     */
    PageInfoVO<AlgorithmTaskListResultVO> listTaskRecord(AITaskResultPageReqDTO dto);

}
