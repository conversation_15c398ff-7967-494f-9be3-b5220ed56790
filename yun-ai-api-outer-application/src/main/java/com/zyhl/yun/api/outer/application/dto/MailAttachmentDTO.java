package com.zyhl.yun.api.outer.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 对话接口邮箱附件DTO入参
 *
 * <AUTHOR>
 * @date 2024/6/27 19:14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MailAttachmentDTO {

    /**
     * 云盘文件ID列表
     */
    private List<String> fileIdList;

    /**
     * 邮件ID
     */
    private String mailId;

    /**
     * 邮箱附件名列表
     */
    private List<String> attachNameList;

}
