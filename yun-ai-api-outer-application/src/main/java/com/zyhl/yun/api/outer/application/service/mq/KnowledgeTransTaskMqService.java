package com.zyhl.yun.api.outer.application.service.mq;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileTaskEntity;

import java.util.Map;

/**
 * 知识库文档向量化消息服务
 *
 * <AUTHOR>
 * @date 2024-10-09
 */
public interface KnowledgeTransTaskMqService {

    /**
     * 发送任务状态查询消息
     * 
     * @param taskEntity 任务实体入参
     * @param ext 扩展字段
     */
    void sendMq(UserKnowledgeFileTaskEntity taskEntity, String ext);

    /**
     * 
     * @param taskEntity 任务实体入参
     * @param message 消息
     */
    void sendMqV3(UserKnowledgeFileTaskEntity taskEntity, Map<String, Object> message);
}
