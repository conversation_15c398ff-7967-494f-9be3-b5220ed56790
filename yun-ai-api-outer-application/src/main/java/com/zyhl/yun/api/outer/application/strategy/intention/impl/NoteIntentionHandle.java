package com.zyhl.yun.api.outer.application.strategy.intention.impl;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.zyhl.yun.api.outer.application.strategy.intention.AbstractIntentionHandle;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.IntentionTypeEnum;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 描述
 *
 * <AUTHOR> zhumaoxian  2025/6/21 9:33
 */
@Slf4j
@Component
public class NoteIntentionHandle extends AbstractIntentionHandle {
    @Override
    public void afterPropertiesSet() throws Exception {
        HANDLE_MAP.put(AssistantEnum.NOTE, this);
    }

    /**
     * 支持的意图，根据版本控制
     */
    private Map<String, DialogueIntentionEnum> getSupportIntentions() {
        Map<String, DialogueIntentionEnum> map = new HashMap<>(Const.NUM_16);
        map.put(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode(), DialogueIntentionEnum.TEXT_GENERATE_TEXT);
        map.put(DialogueIntentionEnum.SEARCH_NOTE_KNOWLEDGE_BASE.getCode(), DialogueIntentionEnum.SEARCH_NOTE_KNOWLEDGE_BASE);

        return map;
    }

    /**
     * 笔记意图处理
     *
     * @param intentionVO 意图结果
     * @return 笔记意图处理结果
     */
    @Override
    public DialogueIntentionVO intentionHandle(DialogueIntentionVO intentionVO) {

        List<DialogueIntentionVO.IntentionInfo> intentionInfoList = intentionVO.getIntentionInfoList();
        if (ObjectUtil.isEmpty(intentionInfoList)) {
            return intentionVO;
        }

        DialogueIntentionVO.IntentionInfo intentionInfo = intentionInfoList.get(0);
        Map<String, DialogueIntentionEnum> supportIntentions = getSupportIntentions();
        if (ObjectUtil.isEmpty(supportIntentions.get(intentionInfo.getIntention()))) {
            // 不在支持的意图列表内

            // 判断是否是搜索意图
            boolean isSearch = DialogueIntentionEnum.MAP_TYPE.get(IntentionTypeEnum.SEARCH).stream().anyMatch(type -> type.getCode().equals(intentionInfo.getIntention()));
            if (isSearch) {
                // 搜索意图统一转换为搜笔记知识库意图
                intentionInfo.setIntention(DialogueIntentionEnum.SEARCH_NOTE_KNOWLEDGE_BASE.getCode());
            } else {
                // 统一转换文生文意图
                intentionInfo.setIntention(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());
            }
        }

        intentionVO.setIntentionInfoList(Collections.singletonList(intentionInfo));
        intentionVO.setAlgorithmIntentionCode(intentionInfo.getIntention());
        return intentionVO;
    }
}
