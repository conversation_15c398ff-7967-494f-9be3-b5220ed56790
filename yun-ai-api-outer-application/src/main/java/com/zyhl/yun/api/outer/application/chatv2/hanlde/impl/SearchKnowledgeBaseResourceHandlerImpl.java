package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatDialogueSearchService;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 搜索知识库资源
 * @Author: WeiJingKun
 */
@Slf4j
@Component
public class SearchKnowledgeBaseResourceHandlerImpl extends AbstractChatAddV2Handler {

	/**
	 * 当前执行顺序
	 */
	private ExecuteSort thisExecuteSort = ExecuteSort.SEARCH_KNOWLEDGE_BASE_RESOURCE;

	@Resource
	private ChatDialogueSearchService chatDialogueSearchService;

	@Override
	public void afterPropertiesSet() throws Exception {
    	//支持的业务初始化
    	List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        this.setBusinessTypes(thisBusinessTypes);
    }
    
	@Override
	public int order() {
		return thisExecuteSort.getSort();
	}

	@Override
	public boolean execute(ChatAddHandleDTO handleDTO) {
		/**
		 * 执行的条件： 1、应用类型 == chat（普通对话） 2、小天助手渠道号 3、搜索知识库资源
		 */
		return ApplicationTypeEnum.isChat(handleDTO.getReqDTO().getApplicationType())
				&& AssistantEnum.isXiaoTian(RequestContextHolder.getAssistantEnum())
				&& DialogueIntentionEnum.SEARCH_KNOWLEDGE_BASE_RESOURCE.getCode().equals(handleDTO.getIntentionCode());
	}

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		log.info("进入{}", thisExecuteSort.getDesc());

		// 执行搜索
		chatDialogueSearchService.searchKnowledgeBaseResourceHandle(handleDTO);

		// 返回检索结果回答
		handleDTO.getRespVO().getFlowResult().setResultType(FlowResultTypeEnum.SEARCH.getType());

		// 返回搜索结果
		handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(handleDTO.getRespVO()));

		return false;
	}

}
