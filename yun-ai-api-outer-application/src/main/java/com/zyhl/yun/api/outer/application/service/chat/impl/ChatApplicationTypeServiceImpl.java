package com.zyhl.yun.api.outer.application.service.chat.impl;

import static com.zyhl.yun.api.outer.constants.Const.NUM_16;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.zyhl.hcy.yun.ai.common.base.annotation.MethodExecutionTimeLog;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.config.ApplicationAgentWhiteConfig;
import com.zyhl.yun.api.outer.application.config.ChatAgentApplicationConfig;
import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.BaseApplicationTypeListConvertor;
import com.zyhl.yun.api.outer.application.dto.ApplicationTypeListDTO;
import com.zyhl.yun.api.outer.application.service.chat.ChatApplicationTypeService;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.config.TabsProperties;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatMessageEntity;
import com.zyhl.yun.api.outer.domain.entity.ApplicationTypeListEntity;
import com.zyhl.yun.api.outer.domain.vo.ChatApplicationType;
import com.zyhl.yun.api.outer.domain.vo.TabChatApplicationType;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.BusinessTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.repository.AlgorithmChatMessageRepository;
import com.zyhl.yun.api.outer.repository.ChatApplicationTypeRepository;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 对话应用类型信息Service实现类
 *
 * <AUTHOR>
 * @version 2024年02月28日 15:31
 */
@SuppressWarnings("deprecation")
@Service
@Slf4j
@RequiredArgsConstructor
public class ChatApplicationTypeServiceImpl implements ChatApplicationTypeService {


    private final BaseApplicationTypeListConvertor applicationTypeListConvertor;

    private final ChatApplicationTypeRepository chatApplicationTypeRepository;

    private final AlgorithmChatMessageRepository algorithmChatMessageRepository;

    private final TabsProperties tabsProperties;

    private final SourceChannelsProperties channelsProperties;

    private final ChatAgentApplicationConfig chatAgentApplicationConfig;
    
    private final ApplicationAgentWhiteConfig applicationAgentWhiteConfig;

    private final RedisOperateRepository redisOperateRepository;

    /**
     * 默认标签
     */
    private static final String DEFAULT_LABEL = "其他";


    @Override
    @MethodExecutionTimeLog("对话应用类型信息列表-serviceImpl")
    public List<TabChatApplicationType> typeList(ApplicationTypeListDTO dto) {
        // 参数校验
        dto.validate();
        ApplicationTypeListEntity entity = applicationTypeListConvertor.toEntity(dto);
        if (!StringUtils.hasText(entity.getBusinessType())) {
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_BUSINESS_TYPE);
        }
        List<ChatApplicationType> chatApplicationTypes = chatApplicationTypeRepository.typeList(entity);
        // （渠道/手机号）白名单权限拦截控制：2025-06-05
        chatApplicationTypes = whitelistPermissionFilter(dto, chatApplicationTypes);
        // 构建sessionId查询条件列表
        Map<String, AlgorithmChatMessageEntity> sessionIdMap = algorithmChatMessageRepository.findSessionIdByBusinessType(entity.getUserId(), entity.getBusinessType());
        // 为列表中的每个元素设置sessionId
        if (sessionIdMap != null && !sessionIdMap.isEmpty()) {
            // 为列表中的每个元素设置sessionId，如果application_id匹配的话
            chatApplicationTypes.forEach(type -> {
                if (sessionIdMap.containsKey(type.getApplicationId())) {
                    type.setSessionId(String.valueOf(sessionIdMap.get(type.getApplicationId()).getId()));
                }
            });
        }
        // 构建返回结果
        List<TabChatApplicationType> tabChatApplicationTypeList = new ArrayList<>();
        // tabsProperties.getTabLabel() is not null or is not empty
        if (tabsProperties.getTabLabel() != null && !tabsProperties.getTabLabel().isEmpty()) {
            Map<String, List<ChatApplicationType>> groupedTypes = groupByTabLabels(chatApplicationTypes);
            tabsProperties.getTabLabel().forEach(tabLabel -> {
                if (groupedTypes.containsKey(tabLabel)) {
                    tabChatApplicationTypeList.add(TabChatApplicationType.builder()
                            .tabLabel(tabLabel)
                            .tabLabelEn("")
                            .chatApplicationTypeList(groupedTypes.get(tabLabel))
                            .build());
                } else {
                    tabChatApplicationTypeList.add(TabChatApplicationType.builder()
                            .tabLabel(tabLabel)
                            .tabLabelEn("")
                            .chatApplicationTypeList(new ArrayList<>())
                            .build());
                }
            });
        } else {
            tabChatApplicationTypeList.add(TabChatApplicationType.builder()
                    .tabLabel(null)
                    .tabLabelEn(null)
                    .chatApplicationTypeList(chatApplicationTypes)
                    .build());
        }
        return chatAgentApplicationWhiteFilter(tabChatApplicationTypeList);
    }

	/**
	 * （渠道/手机号）白名单权限拦截控制
	 * 
	 * @param dto                  入参
	 * @param chatApplicationTypes 应用列表
	 * @return 应用列表
	 */
	private List<ChatApplicationType> whitelistPermissionFilter(ApplicationTypeListDTO dto,
			List<ChatApplicationType> chatApplicationTypes) {
		if (CollUtil.isEmpty(chatApplicationTypes)) {
			return chatApplicationTypes;
		}
		List<ChatApplicationType> newChatApplicationTypes = new ArrayList<>();
		for (ChatApplicationType chatApplicationType : chatApplicationTypes) {
			if (applicationAgentWhiteConfig.allowIdPermission(dto.getSourceChannel(),
					RequestContextHolder.getPhoneNumber(), chatApplicationType.getApplicationId())) {
				newChatApplicationTypes.add(chatApplicationType);
			}
		}
		return newChatApplicationTypes;
	}

	/**
     * 智能体白名单过滤
     *
     * @param tabChatApplicationTypeList
     * @return
     */
    private List<TabChatApplicationType> chatAgentApplicationWhiteFilter(
            List<TabChatApplicationType> tabChatApplicationTypeList) {
        if (CollUtil.isEmpty(tabChatApplicationTypeList)) {
            return tabChatApplicationTypeList;
        }
        //无白名单开关或者应用id为空，直接返回
        if (Boolean.FALSE.equals(chatAgentApplicationConfig.getWhiteSwitch())
                || CollUtil.isEmpty(chatAgentApplicationConfig.getWhiteApplicationIds())) {
            return tabChatApplicationTypeList;
        }

        //过滤白名单
        List<TabChatApplicationType> newList = new ArrayList<>();
        for (TabChatApplicationType tabChatApplicationType : tabChatApplicationTypeList) {
            tabChatApplicationType.setChatApplicationTypeList(
                    chatAgentApplicationWhiteIdAndUserFilter(tabChatApplicationType.getChatApplicationTypeList()));
            newList.add(tabChatApplicationType);
        }
        return newList;
    }

    /**
     * 智能体白名单id和用户过滤
     *
     * @param chatApplicationTypeList
     * @return
     */
    private List<ChatApplicationType> chatAgentApplicationWhiteIdAndUserFilter(
            List<ChatApplicationType> chatApplicationTypeList) {
        if (CollUtil.isEmpty(chatApplicationTypeList)) {
            return chatApplicationTypeList;
        }
        List<ChatApplicationType> newTypeList = new ArrayList<>();
        for (ChatApplicationType chatApplicationType : chatApplicationTypeList) {
            //判断是否白名单应用id
            if (isAgentWhiteApplicationId(chatApplicationType)) {
                if (isAgentWhiteApplicationUser()) {
                    //白名单用户才展示
                    newTypeList.add(chatApplicationType);
                }
            } else {
                newTypeList.add(chatApplicationType);
            }
        }
        return newTypeList;
    }

    /**
     * 智能体白名单应用id
     *
     * @param chatApplicationType
     * @return
     */
    private boolean isAgentWhiteApplicationId(ChatApplicationType chatApplicationType) {
        return (null != chatAgentApplicationConfig.getWhiteApplicationIds() && chatAgentApplicationConfig
                .getWhiteApplicationIds().contains(chatApplicationType.getApplicationId()));
    }

    /**
     * 智能体白名单用户
     *
     * @return
     */
    private boolean isAgentWhiteApplicationUser() {
        return (null != chatAgentApplicationConfig.getUserWhiteList()
                && chatAgentApplicationConfig.getUserWhiteList().contains(RequestContextHolder.getPhoneNumber()));
    }

    /**
     * 根据应用id获取对话应用类型信息
     *
     * @param applicationId 应用类型
     * @return 对话应用类型信息
     */
    @Override
    public ChatApplicationType getByApplicationId(String applicationId) {
        if (!StringUtils.hasText(applicationId)) {
            return null;
        }

        return chatApplicationTypeRepository.getByApplicationId(applicationId);
    }

    /**
     * 根据应用id+应用类型，获取对话应用类型信息，优先读取redis缓存
     *
     * @param applicationId   应用类型
     * @param applicationType 应用类型
     * @return 对话应用类型信息
     */
    @Override
    public ChatApplicationType getChatApplicationTypeCache(String applicationId, String applicationType) {
        // 获取缓存数据
        ChatApplicationType chatApplicationType = redisOperateRepository.getBeanApplicationId(applicationId);
        if (null != chatApplicationType) {
            return chatApplicationType;
        }

        // 查询对话应用类型信息
        ChatApplicationType typeEntity = getByApplicationId(applicationId);
        if (typeEntity == null) {
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_APPLICATION_ID_INVALID);
        }

        // 对话应用类型信息表的应用类型 需要和传入的应用类型一致
        String infoType = typeEntity.getApplicationType();
        if (!StringUtils.hasText(infoType) || !applicationType.equals(infoType)) {
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_APPLICATION_ID_INVALID);
        }

        // 设置缓存数据
        redisOperateRepository.setBeanApplicationId(applicationId, typeEntity);
        return typeEntity;
    }

    /**
     * 获取业务类型
     *
     * @param applicationType 应用类型
     * @param sourceChannel   来源渠道
     * @return 业务类型
     */
    @Override
    public String getBusinessType(String applicationType, String sourceChannel) {
        // 校验应用类型
        if (!StringUtils.hasText(applicationType) && ApplicationTypeEnum.CHAT.getCode().equals(applicationType)) {
            return BusinessTypeEnum.MAIL_H5.getCode();
        }

        // 获取业务类型
        return channelsProperties.getType(sourceChannel);
    }

    @Override
    public List<ChatApplicationType> getByAppList(List<String> idList) {
        return chatApplicationTypeRepository.getByAppList(idList);
    }

    @Override
    public String getTypeRelationId(String applicationId, String applicationType) {
        // 校验应用类型
        if (!ApplicationTypeEnum.isIntelligen(applicationType)) {
            return null;
        }

        // 获取缓存数据
        String typeRelationId = redisOperateRepository.getTypeRelationId(applicationId);
        if (StringUtils.hasText(typeRelationId)) {
            return typeRelationId;
        }

        // 查询对话应用类型信息
        ChatApplicationType typeEntity = getByApplicationId(applicationId);
        if (typeEntity == null) {
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_APPLICATION_ID_INVALID);
        }

        // 对话应用类型信息表的应用类型 需要和传入的应用类型一致
        String infoType = typeEntity.getApplicationType();
        if (!StringUtils.hasText(infoType) || !applicationType.equals(infoType)) {
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_APPLICATION_ID_INVALID);
        }

        // 获取应用关联id
        typeRelationId = typeEntity.getTypeRelationId();
        // 设置缓存数据
        redisOperateRepository.setTypeRelationId(applicationId, typeRelationId);
        return typeRelationId;
    }

    public Map<String, List<ChatApplicationType>> groupByTabLabels(List<ChatApplicationType> chatApplicationTypes) {
        Map<String, List<ChatApplicationType>> groupedByTabLabels = new HashMap<>(NUM_16);

        // 遍历每个ChatApplicationType
        for (ChatApplicationType chat : chatApplicationTypes) {
            if (chat.getTabLabel() == null || chat.getTabLabel().isEmpty()) {
                // 如果tabLabel为空或null，归类到默认标签
                groupedByTabLabels.computeIfAbsent(DEFAULT_LABEL, k -> new ArrayList<>()).add(chat);
            } else {
                // 拆分tabLabel字段
                String[] labels = chat.getTabLabel().split(",");
                for (String label : labels) {
                    // 去除前后空格
                    label = label.trim();
                    if (label.isEmpty()) {
                        // 如果拆分后的标签为空，也归类到默认标签
                        groupedByTabLabels.computeIfAbsent(DEFAULT_LABEL, k -> new ArrayList<>()).add(chat);
                    } else {
                        // 将chat添加到对应的标签列表中
                        groupedByTabLabels.computeIfAbsent(label, k -> new ArrayList<>()).add(chat);
                    }
                }
            }
        }

        return groupedByTabLabels;
    }
}
