package com.zyhl.yun.api.outer.application.service.chat;

import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;

/**
 * 会话信息操作
 *
 * <AUTHOR>
 */
public interface AlgorithmChatMessageService {

    /**
     * 保存会话信息：
     * 1、如果有会话id，则更新数据
     * 2、如果没有会话id，则新增数据
     *
     * @param dto 请求入参
     * @return 会话id
     */
    long save(AlgorithmChatAddDTO dto);
    
    /**
     * 保存会话信息：
     * 1、innerDTO.asyncSaveMessage： true，则新增数据
     * 2、innerDTO.asyncSaveMessage： false，则更新数据
     *
     * @param innerDTO 用户输入对象
     * @return long 会话id
     * @Author: WeiJingKun
     */
    long save(ChatAddInnerDTO innerDTO);
    
    /**
     * 判断会话是否存在
     *
     * @param id     会话id
     * @param userId 用户id
     * @return boolean
     */
    boolean exist(String id, String userId);

}
