package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.dto.ReadTaskDTO;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.application.service.TaskApplicationTypeService;
import com.zyhl.yun.api.outer.config.textmodel.SpeedReadProperties;
import com.zyhl.yun.api.outer.domainservice.QpsLimitService;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.SseNameEnum;
import com.zyhl.yun.api.outer.enums.chat.TalkTypeEnum;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import lombok.extern.slf4j.Slf4j;

/**
 * 文本大模型图书快速阅读对话【applicationType=speedread && dialogueType=0】
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SpecialSpeedReadTextModelHandlerImpl extends AbstractChatAddV2Handler {

	/**
	 * 当前执行顺序
	 */
	private final ExecuteSort thisExecuteSort = ExecuteSort.SPECIAL_APP_SPEED_READ_CHAT;

	@Resource
	private DataSaveService dataSaveService;
	@Resource
	private QpsLimitService qpslimitService;
	@Resource
	private SpeedReadProperties speedReadProperties;
	@Resource
	private TextModelExternalService textModelExternalService;
	@Resource
	private TaskApplicationTypeService taskApplicationTypeService;
	@Resource
	private AlgorithmChatContentRepository algorithmChatContentRepository;
	@Resource
	private AiTextResultRepository aiTextResultRepository;

	@Override
	public void afterPropertiesSet() throws Exception {
		// 支持的业务初始化
		List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        this.setBusinessTypes(thisBusinessTypes);
	}

	@Override
	public int order() {
		return thisExecuteSort.getSort();
	}

	@Override
	public boolean execute(ChatAddHandleDTO handleDTO) {
		return ApplicationTypeEnum.isSpeedRead(handleDTO.getReqDTO().getApplicationType())
				&& TalkTypeEnum.isDialogue(handleDTO.getInputInfoDTO().getDialogueType());
	}

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		log.info("进入{}", thisExecuteSort.getDesc());

		// 1. 设置文本意图
		handleDTO.setTextGenerateTextIntention();

		// 2. 获取sessionId(任务id)
		String sessionId = handleDTO.getReqDTO().getSessionId();
		if (StringUtils.isBlank(sessionId)) {
			log.error("任务id[sessionId]为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		// 3. 获取hbase全文
		String fullText = taskApplicationTypeService.getSpeedReadFullText(new ReadTaskDTO(RequestContextHolder.getUserId(), sessionId));

		String dialogue = handleDTO.getInputInfoDTO().getDialogue();
		if (StringUtils.isBlank(dialogue)) {
			log.error("对话内容为空，sessionId:{}", sessionId);
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		// 4. 获取模型编码
		String modelCode = speedReadProperties.getModelCode();

		// 检查QPS限制
		if (!qpslimitService.modelQpsLimit(modelCode)) {
			log.info("请求过多，qps限流，model:{}", modelCode);
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_LIMITATION);
		}

		String userPrompt = speedReadProperties.getUserPrompt();

		// 获取模型最大长度限制
		Integer maxLength = speedReadProperties.getModelMaxLength();

		// 计算已使用的长度
		int dialogueLength = dialogue.length() + userPrompt.length();
		int fullTextLength = fullText.length();
		int userLength = dialogueLength + fullTextLength;

		// 获取历史上下文问答长度
		// 获取对话消息列表
		List<TextModelMessageDTO> selectedMessages = new ArrayList<>();

		log.info("speedread sessionId:{}, dialogue:{}, maxLength:{}, dialogueLength:{}, fullTextLength:{}", sessionId,
				dialogue, maxLength, dialogueLength, fullTextLength);

		boolean isFull = true;
		if (userLength > maxLength) {
			fullText = fullText.substring(0, maxLength - dialogueLength);
			isFull = false;
		}
		String text = fullText + userPrompt + dialogue;
		// 只有当全文和当前对话没有达到最大长度限制时，才去获取历史对话
		if (isFull) {
			// 5. 处理对话
			List<TextModelMessageDTO> historyList = aiTextResultRepository
					.getHistoryList(handleDTO.getReqDTO().getUserId(), handleDTO.getSessionId());
			selectedMessages = getReqHistoryList(sessionId, historyList, maxLength, userLength);
		} else {
			log.info("提示词和当前对话长度已达到模型最大长度限制，不再获取历史对话，promptLength: {}, maxLength: {}", dialogueLength, maxLength);
		}

		SseEventListener event = new SseEventListener(handleDTO, null);
		event.getSseEmitterOperate().setSseName(SseNameEnum.SPEEDREAD_NAME.getCode());
		event.setModelCode(modelCode);
		// 构建请求
		TextModelTextReqDTO reqDTO = event.getTextDto().toSimpleTextReqDTO();
		reqDTO.setModelValue(modelCode);

		// 将截取后的文本添加到消息列表中
		TextModelMessageDTO messageDTO = new TextModelMessageDTO();
		messageDTO.setRole(TextModelRoleEnum.USER.getName());
		messageDTO.setContent(text);
		selectedMessages.add(messageDTO);

		// 设置所有会话
		reqDTO.setMessageDtoList(selectedMessages);

		// 确保禁用联网搜索
		reqDTO.setEnableForceNetworkSearch(false);

		// 保存到hbase
		dataSaveService.saveTextResult(handleDTO, "", "");

		// 保存数据库（message不存在，会设置新增）
		dataSaveService.add(handleDTO, ChatStatusEnum.CHAT_IN);

		// 调用模型服务
		textModelExternalService.streamDialogue(modelCode, reqDTO, event);

		// 更新模型编码
		algorithmChatContentRepository.updateModelCode(event.getDialogId(), modelCode);

		return false;
	}

	/**
	 * 
	 * 获取请求历史记录列表
	 * 
	 * @param sessionId
	 * @param historyList
	 * @param maxLength
	 * @param historyLength
	 * @param userLength
	 * @return
	 */
	private List<TextModelMessageDTO> getReqHistoryList(String sessionId, List<TextModelMessageDTO> historyList,
			Integer maxLength, int userLength) {
		int historyLength = 0;
		List<TextModelMessageDTO> selectedMessages = new ArrayList<>();

		// 动态获取对话历史，根据模型长度限制决定获取多少轮对话
		if (historyList != null && !historyList.isEmpty()) {
			// 从最新的对话开始，每次取一轮（最多2条消息，一问一答）
			int startIndex = historyList.size();
			int roundCount = 0;

			while (true) {
				// 计算当前轮次的起始索引
				int currentRoundStartIndex = Math.max(0, startIndex - 2);
				// 获取当前轮次的消息
				List<TextModelMessageDTO> currentRoundMessages = historyList.subList(currentRoundStartIndex,
						startIndex);

				// 计算当前轮次消息的总长度
				int currentRoundLength = 0;
				for (TextModelMessageDTO message : currentRoundMessages) {
					if (message != null && message.getContent() != null) {
						currentRoundLength += message.getContent().length();
					}
				}

				// 计算当前已使用的长度（问题 + 已选择的历史对话 + 当前轮次）
				int currentUsedLength = userLength + historyLength + currentRoundLength;

				// 如果当前轮次加入后超过模型长度限制，则停止添加
				if (currentUsedLength > maxLength) {
					log.info("添加第{}轮对话后超过模型长度限制，停止添加更多轮次", roundCount);
					break;
				}

				// 将当前轮次的消息添加到已选择的消息列表中
				selectedMessages.addAll(0, currentRoundMessages);
				historyLength += currentRoundLength;
				roundCount++;

				// 更新起始索引，准备获取下一轮
				startIndex = currentRoundStartIndex;

				// 如果已经处理完所有消息，则退出循环
				if (startIndex == 0) {
					break;
				}
			}

			log.info("动态获取对话历史 sessionId:{}，共获取{}轮对话，消息数量：{}，总字数：{}", sessionId, roundCount, selectedMessages.size(),
					historyLength);
		}
		return selectedMessages;
	}

}
