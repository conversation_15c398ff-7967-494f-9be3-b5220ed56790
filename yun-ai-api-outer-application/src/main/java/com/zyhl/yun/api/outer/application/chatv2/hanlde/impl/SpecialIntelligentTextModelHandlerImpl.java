package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.service.BenefitService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.application.service.chat.ChatApplicationTypeService;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.constants.IntelligentConstants;
import com.zyhl.yun.api.outer.domain.vo.ChatApplicationType;
import com.zyhl.yun.api.outer.domainservice.QpsLimitService;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.SseNameEnum;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import lombok.extern.slf4j.Slf4j;

/**
 * 文本大模型角色智能体对话【applicationType=intelligent】
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SpecialIntelligentTextModelHandlerImpl extends AbstractChatAddV2Handler {

	/**
	 * 当前执行顺序
	 */
	private final ExecuteSort thisExecuteSort = ExecuteSort.SPECIAL_APP_INTELLIGENT_CHAT;

	@Resource
    private BenefitService benefitService;
    @Resource
    private DataSaveService dataSaveService;
    @Resource
    private QpsLimitService qpslimitService;
    @Resource
    private ModelProperties modelProperties;
    @Resource
    private AlgorithmChatContentRepository algorithmChatContentRepository;
    @Resource
    private TextModelExternalService textModelExternalService;
    @Resource
    private AiTextResultRepository aiTextResultRepository;
    @Resource
    private ChatApplicationTypeService applicationTypeService;
    @Resource
    private SpecialIntelligentMeetingHandlerImpl specialIntelligentMeetingHandlerImpl;

    @Override
    public void afterPropertiesSet() throws Exception {
    	//支持的业务初始化
    	List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        this.setBusinessTypes(thisBusinessTypes);
    }
    
    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public boolean execute(ChatAddHandleDTO handleDTO) {
        return ApplicationTypeEnum.isIntelligen(handleDTO.getReqDTO().getApplicationType());
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        log.info("进入{}", thisExecuteSort.getDesc());
        
        ChatAddReqDTO reqDTO = handleDTO.getReqDTO();
        ChatApplicationType chatApplicationType = applicationTypeService.getChatApplicationTypeCache(reqDTO.getApplicationId(), reqDTO.getApplicationType());
        if(null == chatApplicationType) {
			log.info("智能体信息不存在 applicationId:{}", reqDTO.getApplicationId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        
		handleDTO.getSseEmitterOperate().setSseName(SseNameEnum.AGENT_NAME.getCode());
		if (IntelligentConstants.TYPE_OF_INTELLIGENT_MEETING.equals(chatApplicationType.getTypeRelationId())) {
			return specialIntelligentMeetingHandlerImpl.run(handleDTO);
		}
		
		//非文本意图，需要重置为文本意图
		if(!DialogueIntentionEnum.isTextIntention(handleDTO.getIntentionCode())) {
			handleDTO.setTextGenerateTextIntention();
		}
        
        // 扣减权益
        benefitService.consumeBenefit(handleDTO.getReqDTO(), RequestContextHolder.getPhoneNumber(), handleDTO.getDialogueId());

        // 保存到hbase
        dataSaveService.saveTextResult(handleDTO, "", "");

        // 保存数据库
        dataSaveService.add(handleDTO, ChatStatusEnum.CHAT_IN);

        // 历史对话
        List<TextModelMessageDTO> historyList = aiTextResultRepository.getHistoryList(handleDTO.getReqDTO().getUserId(), handleDTO.getSessionId());

        // 监听器
        SseEventListener event = new SseEventListener(handleDTO, historyList);
        // 智能体不输出联网搜索信息
        event.setOutputNetworkSearchInfo(false);

        // 智能体对话
        intelligenDialogue(event, chatApplicationType);

        return false;
    }

    /**
     * 智能体对话
     *
     * @param event 监听器
     */
    private void intelligenDialogue(SseEventListener event, ChatApplicationType chatApplicationType) {
        ChatAddReqDTO reqDTO = event.getReqDTO();
        String typeRelationId = chatApplicationType.getTypeRelationId();
        log.info("【智能体对话】应用id：{}，关联id：{}，对话id：{}，用户id：{}", reqDTO.getApplicationId(), typeRelationId, event.getDialogId(), event.getUserId());

        // 使用的模型编码
        String modelCode = TextModelEnum.isExist(typeRelationId) ? typeRelationId : TextModelEnum.XCHEN.getCode();
        event.setModelCode(modelCode);

        // 令牌桶限流
        if (!qpslimitService.modelQpsLimit(modelCode)) {
            log.info("【智能体对话】请求过多，qps限流，模型编码：{}，对话id：{}", modelCode, event.getDialogId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_LIMITATION);
        }

        // 其他智能体暂无字数限制
        Integer agentMaxLength = modelProperties.getAgentMaxLength(modelCode);
        TextModelTextReqDTO req = event.getTextDto().toTextReqDTO(agentMaxLength);
        req.setModelValue(modelCode);
        //智能体不展示角标
        req.setEnableNetworkSearchCitation(false);
        if (TextModelEnum.XCHEN.getCode().equals(modelCode)) {
            textModelExternalService.xchenStream(req, typeRelationId, event);
        } else {
            textModelExternalService.streamDialogue(modelCode, req, event);
        }

        // 更新模型编码
        algorithmChatContentRepository.updateModelCode(event.getDialogId(), modelCode);
    }
}
