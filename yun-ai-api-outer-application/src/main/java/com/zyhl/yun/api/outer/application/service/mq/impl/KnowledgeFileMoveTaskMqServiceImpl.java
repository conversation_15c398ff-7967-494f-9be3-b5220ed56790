package com.zyhl.yun.api.outer.application.service.mq.impl;

import com.alibaba.fastjson.JSON;
import com.chinamobile.tuxedo.sdk.api.Message;
import com.chinamobile.tuxedo.sdk.api.Producer;
import com.chinamobile.tuxedo.sdk.api.SendResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.logger.annotation.LogAnnotation;
import com.zyhl.hcy.plugin.logger.enums.LogType;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.rocketmq.message.MessageDTO;
import com.zyhl.hcy.yun.ai.common.base.utils.MessageUtil;
import com.zyhl.yun.api.outer.application.service.mq.KnowledgeFileDeleteTaskMqService;
import com.zyhl.yun.api.outer.application.service.mq.KnowledgeFileMoveTaskMqService;
import com.zyhl.yun.api.outer.config.RocketmqProducerProperties;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileTaskEntity;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * interfaceName: KnowledgeFileDeleteTaskMqServiceImpl
 * description: 个人知识库 - 文件移动接口实现类
 *
 * <AUTHOR>
 * @date 2025/5/29
 */
@Slf4j
@Service
public class KnowledgeFileMoveTaskMqServiceImpl implements KnowledgeFileMoveTaskMqService {

    @Resource
    private RocketmqProducerProperties rocketmqProducerProperties;

    // 删除和移动是同一个生产者和消费者
    @Resource(name = "knowledgeFileDeleteTaskProducer")
    private Producer knowledgeFileMoveTaskProducer;

    @Override
    @LogAnnotation(logType = LogType.MQ)
    public void sendMq(UserKnowledgeFileTaskEntity entity) {
        Map<String, Object> params = new HashMap<>(Const.NUM_16);
        // 兼容center-task消费情况
        params.put("id", entity.getId());

        params.put("taskId", entity.getId());
        params.put("userId", entity.getUserId());
        params.put("baseId", entity.getBaseId());
        params.put("taskType", entity.getTaskType());

        MessageDTO<String> baseMQTO = new MessageDTO<>(JSON.toJSONString(params));
        String jsonStr = JSON.toJSONString(baseMQTO);

        Message msg = MessageUtil.createMessage();
        msg.setBody(jsonStr.getBytes());
        msg.setTopic(rocketmqProducerProperties.getPersonalKnowledgeFileDeleteTask().getTopic());
        msg.setTag(rocketmqProducerProperties.getPersonalKnowledgeFileDeleteTask().getTag());

        long startTime = System.currentTimeMillis();
        try {
            SendResult sendResult = knowledgeFileMoveTaskProducer.send(msg);
            log.info("知识库资源文件移动任务mq发送成功，消息体：{}，耗时：{}，发送结果：{}", JsonUtil.toJson(entity), System.currentTimeMillis() - startTime, JsonUtil.toJson(sendResult));
        } catch (Exception e) {
            log.error("知识库资源文件移动任务mq发送失败，消息体：{}，耗时：{}，异常信息：{}", JsonUtil.toJson(entity), System.currentTimeMillis() - startTime, e.getMessage(), e);
            throw new YunAiBusinessException(ResultCodeEnum.MQ_SEND_EXCEPTION);
        }
    }
}
