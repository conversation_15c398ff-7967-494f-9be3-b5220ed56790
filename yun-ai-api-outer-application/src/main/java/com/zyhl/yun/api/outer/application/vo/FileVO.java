package com.zyhl.yun.api.outer.application.vo;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileVO {

    /**
     * 文件id
     */
    private String fileId;

    /**
     * 父目录id
     */
    private String parentFileId;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 类型，枚举值file/folder
     */
    private String type;

    /**
     * 文件扩展名，一般是后缀名
     */
    private String fileExtension;

    /**
     * 分类， 根据文件的后缀名和mime-type对文件进行分了分类，主要分类有app：安装包 zip：压缩包 image：图片 doc：文档 video：视频 audio：音频 folder：目录 others：其他
     */
    private String category;

    /**
     * 创建时间，RFC 3339，2019-08-20T06:51:27.292Z
     */
    private String createdAt;

    /**
     * 更新时间，RFC 3339，2019-08-20T06:51:27.292Z
     */
    private String updatedAt;

    /**
     * 放入回收站时间,使用rcf3339
     */
    private String trashedAt;

    /**
     * 文件本地创建时间, 本地创建时间是指文件在上传时刻的本地时间，使用rcf3339
     */
    private String localCreatedAt;

    /**
     * 文件本地修改时间，文件本地更新时间, 本地更新时间是指文件在上传时刻的本地时间，使用rcf3339
     */
    private String localUpdatedAt;

    /**
     * 是否收藏
     */
    private Boolean starred;

    /**
     * 大小
     */
    private Long size;

    /**
     * 用户自定义tag，copy时候不复制
     */
    private TagVO[] userTags;

    /**
     * AI标签，copy时候复制
     */
    private String[] labels;

    /**
     * 缩略图地址
     */
    private ThumbnailInfoVO[] thumbnailUrls;

    /**
     * 处罚状态，可选字段为：0:未处罚 1：被冻结
     */
    private Integer punishMode;

    /**
     * 内容hash
     */
    private String contentHash;

    /**
     * 内容hash算法名，当前hash算法是sha1或sha256
     */
    private String contentHashAlgorithm;

    /**
     * 版本id， 当相同file-id 文件覆盖上传时， 会有新的revision_id 产生
     */
    private String revisionId;

    /**
     * 是否系统目录（保险箱属于系统目录），默认否
     */
    private Boolean systemDir;

    /**
     * 如果存在就返回
     */
    private MediaMetaInfoVO mediaMetaInfo;

    /**
     * 如果存在就返回
     */
    private AddressDetailVO addressDetail;
}
