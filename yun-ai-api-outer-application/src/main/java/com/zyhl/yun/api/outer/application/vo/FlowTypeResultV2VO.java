package com.zyhl.yun.api.outer.application.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 对话信息流式结果V2VO
 *
 * <AUTHOR>
 * @date 2025/2/24 15:23
 */
@Data
public class FlowTypeResultV2VO extends FlowTypeResultVO{

	/**
     * 事件流式编码，0开始
     */
    private Long index;
    
    /**
     * 输出文本
     */
    private String outContent;
    /**
     * 输出思维链文本
     */
    private String reasoningContent;
    
    /**
     * 输出时间
     */
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date outAuditTime;

    /**
     * 输出内容审批结果;状态码：2通过，其他失败
     */
    private Integer outAuditStatus;

    public FlowTypeResultV2VO() {

    }
    
	public FlowTypeResultV2VO(Long index, String reasoningContent, String outContent, Integer outAuditStatus) {
		this.index = index;
		this.reasoningContent = reasoningContent;
		this.outContent = outContent;
		this.outAuditStatus = outAuditStatus;
		this.outAuditTime = new Date();
	}

}
