package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ContinuationInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueFlowResultVO;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.constants.RegConst;
import com.zyhl.yun.api.outer.enums.chat.ChatAddFlowStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.SseNameEnum;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 断点续传处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ContinueTransHandlerImpl extends AbstractChatAddV2Handler {

    @Resource
    private RedisOperateRepository redisOperateRepository;

    /**
     * 创建线程池
     */
    private final static int CORE_POOL_SIZE = 100;
    private final static int MAX_POOL_SIZE = 300;
    private final static long KEEP_ALIVE_TIME = 60;
    private final static int QUEUE_SIZE = 100000;
    private final static ThreadPoolExecutor POOL = new ThreadPoolExecutor(CORE_POOL_SIZE, MAX_POOL_SIZE, KEEP_ALIVE_TIME, TimeUnit.SECONDS, new ArrayBlockingQueue<>(QUEUE_SIZE));

    /**
     * 当前执行顺序
     */
    private ExecuteSort thisExecuteSort = ExecuteSort.CONTINUE_TRANS;


    @Override
    public void afterPropertiesSet() throws Exception {
        //支持的业务初始化
        List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        thisBusinessTypes.add(ChatBusinessTypeEnum.CLOUD_PHONE);
        thisBusinessTypes.add(ChatBusinessTypeEnum.NOTE);
        thisBusinessTypes.add(ChatBusinessTypeEnum.MESSAGE_5G);
        this.setBusinessTypes(thisBusinessTypes);
    }

    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public boolean execute(ChatAddHandleDTO handleDTO) {

        // 断点续传参数校验
        ContinuationInfoDTO param = handleDTO.getReqDTO().getContinuationInfo();
        if (Objects.isNull(param)) {
            return false;
        }
        if (ObjectUtil.isEmpty(param.getDialogueId()) || !param.getDialogueId().matches(RegConst.REG_ID_STR)) {
            log.info("【断点续传】对话id参数错误，继续下一个节点，dialogueId:{}", param.getDialogueId());
            return false;
        }
        if (param.getIndex() == null || param.getIndex() < -1) {
            log.info("【断点续传】对话序号错误，继续下一个节点，index:{}", param.getIndex());
            return false;
        }

        return super.execute(handleDTO);
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        log.info("进入断点续传处理");

        // 线程池执行
        POOL.execute(() -> threadHandle(handleDTO));

        return false;
    }


    private void threadHandle(ChatAddHandleDTO handleDTO) {
        handleDTO.getSseEmitterOperate().setSseName(SseNameEnum.CONTINUE_TRANS.getCode());
        ContinuationInfoDTO param = handleDTO.getReqDTO().getContinuationInfo();
        for (int i = param.getIndex() + 1; ; i++) {
            try {
                String flowResult = redisOperateRepository.getFlowResult(Long.valueOf(param.getDialogueId()), i);
                BaseResult<?> result = JsonUtil.parseObject(flowResult, BaseResult.class);
                if (Objects.isNull(result.getData())) {
                    handleDTO.getSseEmitterOperate().sendAndComplete(flowResult);
                    break;
                }
                DialogueFlowResultVO flowResultVo = JsonUtil.parseObject(JsonUtil.toJson(result.getData()), DialogueFlowResultVO.class);
                if (ChatAddFlowStatusEnum.STOP.getStatus().equals(flowResultVo.getFinishReason())) {
                    handleDTO.getSseEmitterOperate().sendAndComplete(flowResult);
                    break;
                } else {
                    handleDTO.getSseEmitterOperate().send(flowResult);
                }
            } catch (YunAiBusinessException e) {
                log.error("【断点续传】发送信息失败，异常信息：{}", e.getMessage(), e);
                handleDTO.getSseEmitterOperate().sendAndComplete(JsonUtil.toJson(BaseResult.error(e.getExceptionEnum())));
                break;
            } catch (Exception e) {
                log.error("【断点续传】发送信息失败，异常信息：{}", e.getMessage(), e);
                handleDTO.getSseEmitterOperate().complete();
                break;
            }
        }
    }

}
