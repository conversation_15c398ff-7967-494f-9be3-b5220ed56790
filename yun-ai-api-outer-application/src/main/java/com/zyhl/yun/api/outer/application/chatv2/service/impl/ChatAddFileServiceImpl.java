package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.FileOperationUtil;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.AIFileVO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueAttachmentDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatAddCheckService;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatAddFileService;
import com.zyhl.yun.api.outer.config.ImageCommonProperties;
import com.zyhl.yun.api.outer.domain.valueobject.MailInfo;
import com.zyhl.yun.api.outer.domain.valueobject.NoteInfo;
import com.zyhl.yun.api.outer.domain.vo.DocumentParsingResultVO;
import com.zyhl.yun.api.outer.domainservice.ResourceInfoDomainService;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.external.DocumentParsingExternalService;
import com.zyhl.yun.api.outer.external.YunDiskExternalService;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.text.StrPool;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 对话文件服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ChatAddFileServiceImpl implements ChatAddFileService {

    private static final String FILE_SUFFIX = "txt";

    @Resource
    private ChatAddCheckService chatAddCheckService;
    @Resource
    private ResourceInfoDomainService resourceInfoDomainService;
    @Resource
    private DocumentParsingExternalService documentParsingExternalService;
    @Resource
    private YunDiskExternalService yunDiskExternalService;
    @Resource
    private ImageCommonProperties imageCommonProperties;
    @Resource
    private UidGenerator uidGenerator;

    /**
     * 获取file集合
     *
     * @param handleDTO the handle dto
     * @return {@link List<File>}
     * <AUTHOR>
     * @date 2025-4-30 12:07
     */
    @Override
    public List<File> getAttachmentToFile(ChatAddHandleDTO handleDTO) {
        List<File> fileList = new ArrayList<>();
        // 基础路径
        String catalog = imageCommonProperties.getSharePath() + File.separator
                + imageCommonProperties.getFodderCatalog();
        String resultPath = catalog + File.separator + RequestContextHolder.getUserId() + File.separator;
        Optional<DialogueAttachmentDTO> dialogueAttachmentOptional = Optional.ofNullable(handleDTO)
                .map(ChatAddHandleDTO::getInputInfoDTO).map(DialogueInputInfoDTO::getAttachment);
        Optional<List<Integer>> attachmentTypeOptional = dialogueAttachmentOptional
                .map(DialogueAttachmentDTO::getAttachmentTypeList);
        if (!attachmentTypeOptional.isPresent()) {
            return null;
        }
        for (Integer resourceType : attachmentTypeOptional.get()) {
            if (ResourceTypeEnum.isImage(resourceType) || ResourceTypeEnum.isDocument(resourceType)) {
                Optional<List<com.zyhl.yun.api.outer.domain.valueobject.File>> fileOptional = dialogueAttachmentOptional
                        .map(DialogueAttachmentDTO::getFileList);
                if (!fileOptional.isPresent() || fileOptional.get().isEmpty()) {
                    log.warn("==> 输入了云盘图片或者文档类型，但是参数为空");
                    continue;
                }
                log.info("==> 输入了存在云盘图片或者文档类型，获取到内容:{}", fileOptional.get());
                // 获取图片内容
                List<String> fileIdList = fileOptional.get().stream()
                        .map(com.zyhl.yun.api.outer.domain.valueobject.File::getFileId).collect(Collectors.toList());
                if (fileIdList.isEmpty()) {
                    log.warn("==> 输入了存在云盘图片或者文档类型，但是参数为空");
                }
                List<AIFileVO> aiFileVoList = fileIdList.stream()
                        .map(fileId -> yunDiskExternalService.getFileInfo(RequestContextHolder.getUserId(), fileId))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(aiFileVoList)) {
                    // 根据url下载转成file
                    fileList.addAll(aiFileVoList.stream().map(aiFileVO -> {
                        try {
                            File file = new File(
                                    resultPath + aiFileVO.getContentId() + StrPool.DOT + aiFileVO.getFileSuffix());
                            FileUtils.writeByteArrayToFile(file, HttpUtil.downloadBytes(aiFileVO.getContent()));
                            return file;
                        } catch (IOException e) {
                            log.error("==> urlToLocalPath write file fail, | fileId:{}, | e: ", aiFileVO.getContentId(),
                                    e);
                            return null;
                        }
                    }).collect(Collectors.toList()));
                }
            } else if (ResourceTypeEnum.isMail(resourceType)) {
                Optional<List<MailInfo>> mailInfoOptional = dialogueAttachmentOptional
                        .map(DialogueAttachmentDTO::getMailList);
                if (!mailInfoOptional.isPresent() || mailInfoOptional.get().isEmpty()) {
                    log.warn("==> 输入了存在邮件类型，但是邮件参数为空");
                    continue;
                }
                log.info("==> 输入了存在邮件类型，获取到邮件内容:{}", mailInfoOptional.get());
                // 获取邮件内容
                MailInfo mailInfo = mailInfoOptional.get().get(0);
                String resourceContent = resourceInfoDomainService.getMailContent(mailInfo.getSid(),
                        mailInfo.getRmkey(), mailInfo.getMailId());
                try {
                    File file = new File(resultPath + mailInfo.getMailId() + StrPool.DOT + FILE_SUFFIX);
                    writeFile(file, resourceContent);
                    fileList.add(file);
                } catch (IOException e) {
                    log.error("==> urlToLocalPath write file fail, | fileId:{}, | e: ", mailInfo.getMailId(), e);
                }
            } else if (ResourceTypeEnum.isNote(resourceType)) {
                Optional<List<NoteInfo>> noteOptional = dialogueAttachmentOptional
                        .map(DialogueAttachmentDTO::getNoteList);
                if (!noteOptional.isPresent() || noteOptional.get().isEmpty()) {
                    log.warn("==> 输入了存在笔记类型，但是笔记参数为空");
                    continue;
                }
                log.info("==> 输入了存在笔记类型，获取到笔记内容:{}", noteOptional.get());
                // 获取笔记内容
                NoteInfo noteInfo = noteOptional.get().get(0);
                String resourceContent = resourceInfoDomainService.getNoteContent(RequestContextHolder.getToken(),
                        noteInfo.getNoteId());
                try {
                    File file = new File(resultPath + noteInfo.getNoteId() + StrPool.DOT + FILE_SUFFIX);
                    writeFile(file, resourceContent);
                    fileList.add(file);
                } catch (IOException e) {
                    log.error("==> urlToLocalPath write file fail, | fileId:{}, | e: ", noteInfo.getNoteId(), e);
                }
            } else if (ResourceTypeEnum.isAttachment(resourceType)) {
                Optional<List<MailInfo>> mailInfoListOptional = dialogueAttachmentOptional
                        .map(DialogueAttachmentDTO::getMailList);
                if (!mailInfoListOptional.isPresent() || mailInfoListOptional.get().isEmpty()) {
                    log.warn("==> 输入了邮件和附件类型，但是邮件和附件参数为空");
                    continue;
                }
                log.info("==> 输入了存在邮件和附件类型，获取到邮件和附件内容:{}", mailInfoListOptional.get());
                List<String> localPathList = Collections.emptyList();
                try {
                    List<MailInfo> list = mailInfoListOptional.get();
                    localPathList = chatAddCheckService.getFilesByEmailAttachmentLocalPath(list,
                            handleDTO.getReqDTO(), StringUtils.EMPTY);
                    DocumentParsingResultVO docContent = documentParsingExternalService.parsing(localPathList, null);
                    Optional<String> optional = Optional.ofNullable(docContent).map(DocumentParsingResultVO::getText);
                    if (!optional.isPresent()) {
                        log.warn("==> 获取邮件和附件类型出现异常, 返回解析结果为空");
                        continue;
                    }
                    String resourceContent = optional.get();
                    try {
                        File file = new File(resultPath + uidGenerator.getUID() + StrPool.DOT + FILE_SUFFIX);
                        writeFile(file, resourceContent);
                        fileList.add(file);
                    } catch (IOException e) {
                        log.error("==> urlToLocalPath write file fail, | fileId:{}, | e: ", JsonUtil.toJson(list), e);
                    }
                } catch (YunAiBusinessException e) {
                    log.error("==> 获取邮件和附件类型出现异常，错误信息", e);
                } finally {
                    if (Boolean.FALSE.equals(CollectionUtils.isEmpty(localPathList))) {
                        localPathList.forEach(FileOperationUtil::deleteLocalFile);
                    }
                }
            }
        }
        return fileList;
    }

    /**
     * content 写 file
     *
     * @param file    the file
     * @param content the content
     * <AUTHOR>
     * @date 2025-4-30 12:07
     */
    private void writeFile(File file, String content) throws IOException {
        FileUtils.writeByteArrayToFile(file, content.getBytes(StandardCharsets.UTF_8));
    }
}
