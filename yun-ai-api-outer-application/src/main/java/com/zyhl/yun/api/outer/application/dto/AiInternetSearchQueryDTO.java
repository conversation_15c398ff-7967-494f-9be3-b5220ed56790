package com.zyhl.yun.api.outer.application.dto;

import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * className: AiInternetSearchQueryDTO
 * description: AI全网搜调用大模型前入参
 *
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@Builder
public class AiInternetSearchQueryDTO {

    /**
     * 【正版平台】搜索结果json
     */
    private String genericPlatformJson;

    /**
     * 【网盘链接】搜索结果json
     */
    private String cloudStorageLinkJson;

    /**
     * 【在线网址】搜索结果json
     */
    private String onlineUrlJson;

    /**
     * 联网搜索引用信息（伪联网搜索）
     */
    private List<AiTextResultRespParameters.NetworkSearchInfo> networkSearchInfoList;
}