package com.zyhl.yun.api.outer.application.chatv2.dto;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO.OpenApiLingxiChatSession;
import com.zyhl.yun.api.outer.constants.OpenApiLingxiChatConstants;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * openapi lingxi 助手对话请求参数
 *
 * <AUTHOR> liuxuewen
 * @date 2025-06-19 08:54
 */
@Data
@Slf4j
public class OpenApiLingxiChatReqDTO {
	/**
	 * 空字符串
	 */
	private static final String NULL_STRING = "null";

	/**
	 * 请求id
	 */
	private String reqId;

	/**
	 * 设备id
	 */
	private String deviceId;

	/**
	 * 请求消息
	 */
	private List<OpenApiLingxiChatMessage> messages;

	/**
	 * 模型
	 */
	private String model;

	/**
	 * 是否流式
	 */
	private boolean stream;

	/**
	 * session信息
	 */
	private OpenApiLingxiChatSession session;

	/**
	 * 其他参数
	 */
	private Map<String, Object> other;

	@Data
	public static class OpenApiLingxiChatMessage {
		private String role;
		private String content;
	}

	/**
	 * 获取助手对话请求参数
	 * 
	 * @return 助手对话请求
	 */
	public ChatAddReqDTO getChatAddReqDTO() {
		ChatAddReqDTO req = new ChatAddReqDTO();
		DialogueInputInfoDTO input = new DialogueInputInfoDTO();
		input.setVersionInfo(new DialogueVersionInfoDTO("2.0.0", ""));
		input.setInputTime(DateUtil.format(new Date(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN));
		input.setDialogue(getDialogue(this.getMessages()));
		input.setAttachment(getAttachment(this.getSession()));
		// input.setCommand(getCommand(this.getSession()));
		// req.setSessionId(null != this.getSession() ? this.getSession().getSessionId() : null);
		//if (StringUtils.isEmpty(req.getSessionId()) || NULL_STRING.equals(req.getSessionId())) {
		//	req.setSessionId(null);
		//}
		req.setSessionId(null);
		req.setDialogueInput(input);
		return req;
	}

	/**
	 * 如果需要将attributes解析为Map，可以添加这个方法
	 * 
	 * @param thisAttributes 属性参数
	 * @return map结果
	 */
	public static Map<String, Object> getParsedAttributes(String thisAttributes) {
		try {
			if (StringUtils.isNotEmpty(thisAttributes)) {
				return JSONObject.parseObject(thisAttributes).getInnerMap();
			}
		} catch (Exception e) {
			log.error("session.getAttributes parse error:", e);
		}
		return Collections.emptyMap();
	}

	public static String getDialogue(List<OpenApiLingxiChatMessage> thisMessages) {
		if (CollUtil.isEmpty(thisMessages)) {
			return StringUtils.EMPTY;
		}
		OpenApiLingxiChatMessage lastMessage = thisMessages.get(thisMessages.size() - 1);
		if (TextModelRoleEnum.isUser(lastMessage.getRole())) {
			return lastMessage.getContent();
		}
		return StringUtils.EMPTY;
	}

	private DialogueIntentionDTO getCommand(OpenApiLingxiChatSession thisSession) {
		if (null == thisSession || null == thisSession.getAttributes()) {
			return null;
		}
		DialogueIntentionDTO command = null;
		Map<String, Object> attrMap = getParsedAttributes(thisSession.getAttributes());
		String keyOfCommand = OpenApiLingxiChatConstants.ATTR_OF_COMMAND;
		String keyOfSubCommand = OpenApiLingxiChatConstants.ATTR_OF_SUB_COMMAND;
		if (attrMap.containsKey(keyOfCommand)) {
			command = new DialogueIntentionDTO();
			command.setCommand(String.valueOf(attrMap.get(keyOfCommand)));
			if (attrMap.containsKey(keyOfSubCommand)) {
				command.setSubCommand(String.valueOf(attrMap.get(keyOfSubCommand)));
			}
			return command;
		}
		return null;
	}

	public static DialogueAttachmentDTO getAttachment(OpenApiLingxiChatSession thisSession) {
		if (null == thisSession || null == thisSession.getAttributes()) {
			return null;
		}
		DialogueAttachmentDTO attachment = null;
		Map<String, Object> attrMap = getParsedAttributes(thisSession.getAttributes());
		if (attrMap.containsKey(OpenApiLingxiChatConstants.ATTR_OF_DIALOGUE_ID)) {
			String did = String.valueOf(attrMap.get(OpenApiLingxiChatConstants.ATTR_OF_DIALOGUE_ID));
			if (StringUtils.isEmpty(did) || NULL_STRING.equals(did)) {
				return null;
			}
			attachment = new DialogueAttachmentDTO();
			attachment.setAttachmentTypeList(Collections.singletonList(ResourceTypeEnum.DIALOGUE.getType()));
			attachment.setDialogueIdList(Collections.singletonList(did));
			return attachment;
		}
		return null;
	}

	/**
	 * 是否设置最后的录音笔记id
	 * 
	 * @return
	 */
	public boolean isGetLastVoiceNote() {
		if (null != this.getSession()) {
			Map<String, Object> attrs = OpenApiLingxiChatReqDTO.getParsedAttributes(this.getSession().getAttributes());
			if (null != attrs && attrs.containsKey(OpenApiLingxiChatConstants.ATTR_OF_GET_LAST_VOICE_NOTE_ID)) {
				return Boolean
						.valueOf(String.valueOf(attrs.get(OpenApiLingxiChatConstants.ATTR_OF_GET_LAST_VOICE_NOTE_ID)));
			}
		}
		return false;
	}
}