package com.zyhl.yun.api.outer.application.handle.chat.impl;

import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.handle.chat.AbstractChatAddHandler;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 多意图推荐、提问语句推荐
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RecommendHandlerImpl extends AbstractChatAddHandler {

    @Override
    public int order() {
        return ExecuteSort.RECOMMEND.getSort();
    }

    @Override
    public boolean execute(ChatAddInnerDTO innerDTO) {
        // 流式接口 并且 对话类型 并且 小天渠道 并且 非强制大模型对话
        return innerDTO.getSseEmitter() != null
                && ApplicationTypeEnum.isChat(innerDTO.getReqParams().getApplicationType())
                && sourceChannelsProperties.isXiaoTian(innerDTO.getContent().getSourceChannel())
                && !Boolean.TRUE.equals(innerDTO.getReqParams().getEnableForceLlm());
    }

    @Override
    public boolean run(ChatAddInnerDTO innerDTO) {
        log.info("进入推荐信息处理");
        // 设置对话结果推荐对象
        innerDTO.getRespParams().setRecommend(dialogueRecommendService.getDialogueRecommendVO(innerDTO));

        return true;
    }

}
