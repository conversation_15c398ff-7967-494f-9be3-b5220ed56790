package com.zyhl.yun.api.outer.application.chatv2.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 描述：任务提示词对话信息
 *
 * <AUTHOR> liu<PERSON><PERSON><PERSON>
 * @date 2025/4/28 19:54
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskPromptOfChatInfo {

	/**
	 * 模块
	 * 
	 * @see com.zyhl.yun.api.outer.application.enums.TaskModuleEnum
	 */
	private String module;

	/**
	 * 提示词
	 */
	private String prompt;

	/**
	 * 对话结果信息
	 */
	private String chatResult;

	/**
	 * 是否完成
	 */
	private boolean complete;
	
	/**
	 * 模型最大长度
	 */
	private long modelMaxLength;

	public String getFullTextAndPrompt(String fullText) {
		String result = prompt + fullText;
		if (modelMaxLength >= 0 && result.length() > modelMaxLength) {
			return result.substring(0, (int) modelMaxLength);
		}
		return result;
	}

}
