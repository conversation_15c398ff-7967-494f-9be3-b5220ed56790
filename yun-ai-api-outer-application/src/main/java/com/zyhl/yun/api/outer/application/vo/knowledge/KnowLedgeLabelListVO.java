package com.zyhl.yun.api.outer.application.vo.knowledge;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeLabelEntity;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeLabelEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 知识库标签列表
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class KnowLedgeLabelListVO {

    /**
     * 标签ID，必填，特殊ID为：
     * -1 全部
     * 0 未分类
     */
    private String labelId;

    /**
     * 标签名称，必填
     */
    private String labelName;

    /**
     * 排序值，非必填
     */
    private String sort;

    public KnowLedgeLabelListVO(UserKnowledgeLabelEntity entity) {
        this.labelId = String.valueOf(entity.getId());
        this.labelName = entity.getLabel();
        this.sort = String.valueOf(entity.getSort());
    }

    public KnowLedgeLabelListVO(KnowledgeLabelEnum labelEnum) {
        this.labelId = String.valueOf(labelEnum.getId());
        this.labelName = labelEnum.getName();
    }


}
