package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import javax.annotation.Resource;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatAddCheckService;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatCommonService;
import com.zyhl.yun.api.outer.application.util.ChatAddUtils;
import com.zyhl.yun.api.outer.config.textmodel.ModelPromptProperties;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 对话公共服务接口
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class ChatCommonServiceImpl implements ChatCommonService {

    @Resource
    private ChatAddCheckService chatAddCheckService;
	@Resource
	private ModelPromptProperties modelPromptProperties;

	/**
	 * 获取提示词
	 *
	 * @param event 监听器
	 * @return 提示词
	 */
	@Override
	public String getPromptByConfig(SseEventListener event) {
		String prompt = StringUtils.EMPTY;

		String inputPrompt = event.getInputInfoDTO().getPrompt();
		String inputOrderTip = ChatAddUtils.getOrderTip(event.getInputInfoDTO().getExtInfo());
		if (ObjectUtil.isNotEmpty(inputPrompt)) {
			prompt = modelPromptProperties.getPrompt(inputPrompt, event.getModelCode());
		}
		//不使用orderTip，直接返回
		if (event.isUseOrderTip()) {
			return prompt;
		}
		if (ObjectUtil.isEmpty(prompt) && ObjectUtil.isNotEmpty(inputOrderTip)) {
			prompt = modelPromptProperties.getPrompt(inputOrderTip, event.getModelCode());
		}
		if (ObjectUtil.isEmpty(prompt)) {
			prompt = StrUtil.emptyToDefault(inputPrompt, inputOrderTip);
		}

		return prompt;
	}

	@Override
	public String getPromptByConfigAndDB(String inputPrompt, String modelCode, String channel) {
		String prompt = StringUtils.EMPTY;

		if (ObjectUtil.isNotEmpty(inputPrompt)) {
			prompt = modelPromptProperties.getPrompt(inputPrompt, modelCode);
		}else {
			prompt = chatAddCheckService.getDialoguePrompt(prompt, channel);
		}

		return prompt;
	}
}
