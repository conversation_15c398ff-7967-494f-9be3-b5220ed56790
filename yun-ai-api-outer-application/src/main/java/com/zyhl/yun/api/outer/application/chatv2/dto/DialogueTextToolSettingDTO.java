package com.zyhl.yun.api.outer.application.chatv2.dto;

import lombok.Data;

/**
 * 描述
 *
 * <AUTHOR> zhumaoxian 2025/4/12 10:29
 */
@Data
public class DialogueTextToolSettingDTO {

	/**
	 * page String O 页数类型 不限 5-10页 10-15页 15-20页 20-30页 30-35页 35-45页 reportTo
	 * String O 汇报对象 scene String O 场景 style String O 风格 language String O
	 * 语言：默认zh-CN zh-CN=简体中文 EN=英文
	 */

	/**
	 * 页数类型
	 */
	private String page;
	/**
	 * 汇报对象
	 */
	private String group;
	/**
	 * 场景
	 */
	private String scene;
	/**
	 * 风格
	 */
	private String style;
	/**
	 * 语言：默认zh-CN
	 * 
	 * @see com.zyhl.yun.api.outer.enums.chat.LanguageEnum
	 */
	private String language;

	/**
	 * 是否是制作PPT文件，默认fasle，当该字段为fasle时，用户输入AIPPT的意图时，生成的是大纲，打开该开关时，则是使用之前对话生成大纲进行PPT文件生成
	 * 选择制作ppt文件时，需要把PPT大纲结果所在的dialogueId传到DialogueAttachment的dialogueIdList中
	 */
	private Boolean enablePptMake = false;

	/**
	 * 编辑后的大纲，替换输入的dialogueId的生成结果
	 */
	private String editedOutline;

	/**
	 * 模板Id 会议纪要模板传模板编码
	 */
	private String templateId;

	public static DialogueTextToolSettingDTO getDefault() {
		DialogueTextToolSettingDTO toolSettingDTO = new DialogueTextToolSettingDTO();
		toolSettingDTO.setGroup("1");
		toolSettingDTO.setStyle("1");
		toolSettingDTO.setLanguage("1");
		return toolSettingDTO;
	}

}
