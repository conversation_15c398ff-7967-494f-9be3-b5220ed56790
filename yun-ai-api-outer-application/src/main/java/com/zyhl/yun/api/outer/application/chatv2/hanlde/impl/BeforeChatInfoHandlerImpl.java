package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeInviteEntity;
import com.zyhl.yun.api.outer.repository.UserKnowledgeInviteRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatMessageEntity;
import com.zyhl.yun.api.outer.domainservice.SearchReturnTermsService;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.repository.AlgorithmChatMessageRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import lombok.extern.slf4j.Slf4j;

/**
 * 对话前处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class BeforeChatInfoHandlerImpl extends AbstractChatAddV2Handler {

    /**
     * 当前执行顺序
     */
    private ExecuteSort thisExecuteSort = ExecuteSort.BEFORE_CHAT_INFO;

    @Resource
    private UidGenerator uidGenerator;
    @Resource
    private SearchReturnTermsService searchReturnTermsService;
    @Resource
    private UserKnowledgeFileRepository userKnowledgeFileRepository;
    @Resource
    private AlgorithmChatMessageRepository algorithmChatMessageRepository;
    @Resource
    private UserKnowledgeInviteRepository userKnowledgeInviteRepository;

    @Override
    public void afterPropertiesSet() throws Exception {
    	//支持的业务初始化
    	List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
    	thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        thisBusinessTypes.add(ChatBusinessTypeEnum.YUN_MAIL);
        thisBusinessTypes.add(ChatBusinessTypeEnum.CLOUD_PHONE);
        thisBusinessTypes.add(ChatBusinessTypeEnum.NOTE);
        thisBusinessTypes.add(ChatBusinessTypeEnum.MESSAGE_5G);
    	this.setBusinessTypes(thisBusinessTypes);
    }
    
    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public boolean execute(ChatAddHandleDTO innerDTO) {
        return true;
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        log.info("进入{}", thisExecuteSort.getDesc());
        // 生成对话id
        long dialogueId = uidGenerator.getUID();
        handleDTO.setDialogueId(dialogueId);

        // 会话id处理
        ChatAddReqDTO reqDTO = handleDTO.getReqDTO();

        // 是智能体对话类型，又没传sessionId，需要查找sessionId
		if (ApplicationTypeEnum.isIntelligen(handleDTO.getReqDTO().getApplicationType())
				&& StringUtils.isEmpty(reqDTO.getSessionId())) {
			AlgorithmChatMessageEntity algorithmChatMessageEntity = algorithmChatMessageRepository
					.selectOneByApplicationId(handleDTO.getReqDTO().getUserId(),
							handleDTO.getReqDTO().getApplicationId(), RequestContextHolder.getBusinessType());
			if (null != algorithmChatMessageEntity) {
				handleDTO.setSessionId(algorithmChatMessageEntity.getId());
				reqDTO.setSessionId(String.valueOf(algorithmChatMessageEntity.getId()));
			}
		}

        if (StringUtils.isEmpty(reqDTO.getSessionId())) {
            long sessionId = uidGenerator.getUID();
            handleDTO.setSessionId(sessionId);
            handleDTO.setSaveMessage(true);
        } else {
        	if(!Boolean.TRUE.equals(handleDTO.getSaveMessage())) {
        		// 未设置true，才设置false（图书快速阅读使用） 
        		handleDTO.setSaveMessage(false);
        	}
        }

        /** set搜索返回词的异步处理future */
        handleDTO.setSearchReturnTermsFuture(searchReturnTermsService.getOptimizeReturnTermsFutureV1(dialogueId, handleDTO.getReqDTO().getDialogueInput().getDialogue()));

        /** set个人知识库有效的文件数量 TODO 有时间改成异步 */
        int countCanUse = userKnowledgeFileRepository.countCanUse(RequestContextHolder.getUserId());
        List<UserKnowledgeInviteEntity> inviteEntityList = userKnowledgeInviteRepository.getKnowledgeListByUserId(RequestContextHolder.getUserId());
        handleDTO.setPersonalKnowledgeFileCount(countCanUse + (CollUtil.isEmpty(inviteEntityList) ? 0 : inviteEntityList.size()));

        return true;
    }

}
