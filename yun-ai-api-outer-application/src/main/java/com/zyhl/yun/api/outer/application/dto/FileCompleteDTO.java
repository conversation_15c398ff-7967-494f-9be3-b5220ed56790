package com.zyhl.yun.api.outer.application.dto;

import java.io.Serializable;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description}
 * <p>
 *
 * <AUTHOR>
 * @since 4月14 2025
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FileCompleteDTO extends BaseDTO implements Serializable {

  /**
   * 来源渠道
   */
  @NotEmpty(message = "来源渠道不能为空！")
  private String sourceChannel;

  /**
   * 知识库Id
   */
  //@NotEmpty(message = "知识库Id不能为空！")
  private String baseId;

  /**
   * 文件id，覆盖写时必填
   */
  @NotEmpty(message = "文件id不能为空！")
  private String fileId;

  /**
   * 上传id
   */
  @NotEmpty(message = "上传id不能为空！")
  private String uploadId;


  /**
   * 文件内容hash算法名，当前只支持 sha256
   */
  @NotEmpty(message = "文件内容hash算法名不能为空！")
  private String contentHashAlgorithm;

  /**
   * 文件内容 hash 值，需要根据 contentHashAlgorithm 指定的算法计算
   */
  @NotEmpty(message = "文件内容hash值不能为空！")
  private String contentHash;

}
