package com.zyhl.yun.api.outer.application.chatv2.service.impl.openapi;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.service.openapi.OpenapiLingxiCommonService;
import com.zyhl.yun.api.outer.application.chatv2.service.openapi.OpenapiLingxiMeetingStartService;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO.OpenApiLingxiCardLink;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO.OpenApiLingxiCardReplyMessage;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO.OpenApiLingxiCardReplyMessage.CardReplyItem;
import com.zyhl.yun.api.outer.application.config.ApplicationAgentLingxiConfig;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.config.LeadCopyV2Properties;
import com.zyhl.yun.api.outer.config.LeadCopyV2Properties.Copy;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyV2VO;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.external.service.UserEtnService;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * openapi lingxi 智能体对话-智能会议开始-服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-18 21:00
 */
@Slf4j
@Service
public class OpenapiLingxiMeetingStartServiceImpl implements OpenapiLingxiMeetingStartService {

	/**
	 * 笔记标题参数
	 */
	private static final String PARAM_OF_NOTE_TITLE = "noteTitle";

	@Resource
	private OpenapiLingxiCommonService openapiLingxiCommonService;
	@Resource
	private DataSaveService dataSaveService;
	@Resource
	private UserEtnService userEtnService;
	@Resource
	private LeadCopyV2Properties leadCopyV2Properties;
	@Resource
	private ApplicationAgentLingxiConfig applicationAgentLingxiConfig;
	@Resource
	private ChatTextToolBusinessConfig chatTextToolBusinessConfig;

	@SuppressWarnings("deprecation")
	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {

		IntentionInfo mainIntention = DialogueIntentionVO.getMainIntention(handleDTO.getIntentionVO());
		// 聊天添加响应对象
		ChatAddRespVO respVO = handleDTO.getRespVO();

		// 获取意图枚举
		DialogueIntentionEnum intentionEnum = DialogueIntentionEnum.getByCode(mainIntention.getIntention());

		// 获取copy->特殊配置instruction + "-intelligent"
		Copy copy = leadCopyV2Properties.getByInstruction(intentionEnum.getInstruction() + "-intelligent");

		if (null == copy) {
			log.info("lead copy找不到配置");
			return true;
		}

		/**
		 * 笔记标题
		 */
		String noteTitle = null;
		// 会议通知邮件信息
		MailInfoVO mailInfo = null;
		AlgorithmChatContentEntity lastMailDialogueInfo = openapiLingxiCommonService.getLastIntentionDialogue(
				handleDTO.getReqDTO().getUserId(), handleDTO.getSessionId(), String.valueOf(handleDTO.getDialogueId()));
		if (null != lastMailDialogueInfo) {
			mailInfo = openapiLingxiCommonService.getMailInfoResult(lastMailDialogueInfo);
		}
		if (null != mailInfo) {
			noteTitle = chatTextToolBusinessConfig.getIntelligentMeeting().removeTitlePrefix(mailInfo.getTitle());
			log.info("查找到笔记标题 noteTitle:{}", noteTitle);
		}

		// copy结果
		Copy copyResult = new Copy();
		BeanUtil.copyProperties(copy, copyResult);
		if (StringUtils.isNotEmpty(noteTitle) && copyResult.getLinkURL().contains(PARAM_OF_NOTE_TITLE)) {
			copyResult.setLinkURL(copyResult.getLinkURL() + URLEncoder.encode(noteTitle));
		}

		// 设置lead copy
		respVO.setLeadCopy(LeadCopyV2VO.getLeadCopyVo(copyResult, intentionEnum));

		// 保存leadCopy到hbase【LeadCopy，type=1、2、3、4】
		dataSaveService.saveTextResult(handleDTO, "", "");

		// 保存tidb
		dataSaveService.addSuccess(handleDTO, OutContentTypeEnum.TEXT);

		// 前面无大大模型输出，先发一个大模型回复
		String voiceNoteCardModelText = applicationAgentLingxiConfig.getTextConfig().getVoiceNoteCardModelText();
		handleDTO.getSseEmitterOperate()
				.send(OpenApiLingxiChatRespVO.getOpenApiLingxiChatTextModelResp(handleDTO, voiceNoteCardModelText));

		// 结束之前，追加2个消息（卡片【前往录音】、追尾【会议已结束？根据录音笔记生成PPT】）
		String bubbleDesc = applicationAgentLingxiConfig.getTextConfig().getBubbleDesc();
		String voiceNoteCardColor = applicationAgentLingxiConfig.getTextConfig().getVoiceNoteCardColor();
		String voiceNoteCardImage = applicationAgentLingxiConfig.getTextConfig().getVoiceNoteCardImage();
		String voiceNoteCardTitle = applicationAgentLingxiConfig.getTextConfig().getVoiceNoteCardTitle();
		String voiceNoteCardSubtitle = applicationAgentLingxiConfig.getTextConfig().getVoiceNoteCardSubtitle();
		String voiceNoteCardButtonName = applicationAgentLingxiConfig.getTextConfig().getVoiceNoteCardButtonName();
		String voiceNoteCardButtonColor = applicationAgentLingxiConfig.getTextConfig().getVoiceNoteCardButtonColor();
		String voiceNoteCardButtonImage = applicationAgentLingxiConfig.getTextConfig().getVoiceNoteCardButtonImage();
		String genPptTitle = applicationAgentLingxiConfig.getTextConfig().getGenPptTitle();
		String genPptReplyText = applicationAgentLingxiConfig.getTextConfig().getGenPptReplyText();
		String genPptReplyTextWithMeeting = applicationAgentLingxiConfig.getTextConfig()
				.getGenPptReplyTextWithMeeting();
		String selectNoteGenPptTitle = applicationAgentLingxiConfig.getTextConfig().getSelectNoteGenPptTitle();
		String selectNoteGenPptColor = applicationAgentLingxiConfig.getTextConfig().getSelectNoteGenPptColor();
		String selectNoteGenPptImage = applicationAgentLingxiConfig.getTextConfig().getSelectNoteGenPptImage();

		// 生成ppt追尾
		List<OpenApiLingxiCardLink> bubbles = new ArrayList<>();
		OpenApiLingxiCardLink genPptBubble = new OpenApiLingxiCardLink(genPptTitle,
				new OpenApiLingxiCardLink.ReplyLink(genPptReplyText));
		if (null != mailInfo) {
			log.info("查询到会议通知邮件信息，拼接会议内容追尾一键生成ppt");
			genPptBubble = new OpenApiLingxiCardLink(genPptTitle,
					new OpenApiLingxiCardLink.ReplyLink(String.format(genPptReplyTextWithMeeting, noteTitle)));
		}
		bubbles.add(genPptBubble);
		// 选择已有笔记追尾【跳链】--AI助手H5
		OpenApiLingxiCardLink selectNoteGenPptBubble = new OpenApiLingxiCardLink(
				new OpenApiLingxiChatRespVO.WebLink(
						openapiLingxiCommonService.getAiHelperJumpUrl(RequestContextHolder.getToken())),
				selectNoteGenPptColor, selectNoteGenPptImage, selectNoteGenPptTitle);
		bubbles.add(selectNoteGenPptBubble);
		OpenApiLingxiCardReplyMessage replyMessage = new OpenApiLingxiCardReplyMessage(
				Collections.singletonList(new CardReplyItem(voiceNoteCardColor, voiceNoteCardImage, voiceNoteCardTitle,
						voiceNoteCardSubtitle,
						new OpenApiLingxiCardLink(
								new OpenApiLingxiChatRespVO.WebLink(openapiLingxiCommonService
										.getNoteVoiceJumpUrl(RequestContextHolder.getToken(), noteTitle)),
								voiceNoteCardButtonColor, voiceNoteCardButtonImage, voiceNoteCardButtonName))),
				bubbleDesc, bubbles);
		handleDTO.getSseEmitterOperate()
				.sendAndComplete(OpenApiLingxiChatRespVO.getOpenApiLingxiChatReplyResp(handleDTO, replyMessage));

		return false;
	}

}
