package com.zyhl.yun.api.outer.application.chatv2.dto;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 描述
 *
 * <AUTHOR> zhumaoxian  2025/4/12 11:35
 */
@Slf4j
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DialogueVersionInfoDTO {
    /**
     * H5版本号
     */
    private String h5Version;
    /**
     * PC版本号
     */
    private String pcVersion;

    /**
     * 根据扩展信息（json格式）转成对象
     * @Author: WeiJingKun
     *
     * @param extInfo 扩展信息（json格式）
     */
    public static DialogueVersionInfoDTO parse(String extInfo) {
        DialogueVersionInfoDTO versionInfoDTO = null;
        if (CharSequenceUtil.isNotBlank(extInfo)) {
            try {
                versionInfoDTO = JSON.parseObject(extInfo, DialogueVersionInfoDTO.class);
            } catch (Exception e) {
                log.info("extInfo -> 终端的版本信息，异常，extInfo：{}", extInfo);
            }
        }
        return versionInfoDTO;
    }
}
