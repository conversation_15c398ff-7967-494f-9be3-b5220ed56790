package com.zyhl.yun.api.outer.application.handle.chat;

import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;

/**
 * AI对话处理器
 *
 * <AUTHOR>
 */
public interface ChatAddHandler {

    /**
     * 处理器执行顺序序号
     *
     * @return 执行顺序
     */
    int order();

    /**
     * 处理器是否执行
     *
     * @param dto 请求dto
     * @return false-不执行
     */
    boolean execute(ChatAddInnerDTO dto);

    /**
     * 运行处理器
     *
     * @param innerDTO 内部请求dto
     * @return true-继续执行下一个，false-终止执行
     */
    boolean run(ChatAddInnerDTO innerDTO);

}
