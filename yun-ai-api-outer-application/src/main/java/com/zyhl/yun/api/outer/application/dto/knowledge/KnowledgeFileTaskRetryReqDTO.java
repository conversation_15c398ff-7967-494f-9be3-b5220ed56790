package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 知识库导入任务重试请求参数
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KnowledgeFileTaskRetryReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 导入任务ID
     */
    private String taskId;
} 