package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import java.io.Serializable;

/**
 * 个人知识库列表请求参数
 *
 * <AUTHOR>
 * @date 2025/04/16
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class PersonalKnowledgeListReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 知识库类型，不填默认全部
     * 1--个人知识库
     * 2--分享知识库
     * 3--公共知识库
     */
    private Integer baseType;

    /**
     * 分页信息
     */
    private PageInfoDTO pageInfo;

}
