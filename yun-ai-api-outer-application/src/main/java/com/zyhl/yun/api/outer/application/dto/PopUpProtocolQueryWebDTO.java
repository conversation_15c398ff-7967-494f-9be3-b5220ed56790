package com.zyhl.yun.api.outer.application.dto;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.Data;

import java.io.Serializable;

/**
 * PopUpProtocolController.query，接收参数
 *
 * @Author: WeiJingKun
 */
@Data
public class PopUpProtocolQueryWebDTO implements Serializable {

    /**
     * 协议类型：app：ai工具协议（默认），aiass：ai助手协议
     */
    private String channel = "app";

    /**
     * 类别， 0：报名（默认）；1：授权
     */
    private String type = "0";

    /**
     * 给默认值，防止传了key但value为null或""
     */
    public void setChannel(String channel) {
        if (CharSequenceUtil.isBlank(channel)) {
            channel = "app";
        }
        this.channel = channel;
    }

    /**
     * 给默认值，防止传了key但value为null或""
     */
    public void setType(String type) {
        if (CharSequenceUtil.isBlank(type)) {
            type = "0";
        }
        this.type = type;
    }

}
