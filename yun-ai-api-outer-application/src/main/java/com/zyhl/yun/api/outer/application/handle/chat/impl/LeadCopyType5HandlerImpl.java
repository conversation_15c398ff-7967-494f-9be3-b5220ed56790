package com.zyhl.yun.api.outer.application.handle.chat.impl;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.handle.chat.AbstractChatAddHandler;
import com.zyhl.yun.api.outer.application.service.chat.LeadCopyService;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddResultTypeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 引导语 类型5 的处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class LeadCopyType5HandlerImpl extends AbstractChatAddHandler {

	@Resource
	private LeadCopyService leadCopyService;

	@Override
	public int order() {
		return ExecuteSort.LEAD_COPY_5.getSort();
	}

	@Override
	public boolean execute(ChatAddInnerDTO innerDTO) {
		// 对话类型 并且 小天渠道 并且 非强制大模型对话
		return ApplicationTypeEnum.isChat(innerDTO.getReqParams().getApplicationType())
				&& sourceChannelsProperties.isXiaoTian(innerDTO.getContent().getSourceChannel())
				&& !Boolean.TRUE.equals(innerDTO.getReqParams().getEnableForceLlm());
	}

	@Override
	public boolean run(ChatAddInnerDTO innerDTO) {
		log.info("进入引导语类型5处理");
		// 1，意图识别为非图片工具意图，并且有图片，则支持对dialogue+图片，文本处理类执行视觉大模型
		if (!DialogueIntentionEnum.isAiToolIntention(innerDTO.getIntentionCode())
				&& ResourceTypeEnum.isImage(innerDTO.getContent().getResourceType())
				&& StringUtils.isNotEmpty(innerDTO.getContent().getResourceId())) {
			// 此处不执行 LeadCopy5 即将在TextGenerateTextService执行视觉大模型
			log.info("意图识别为非图片工具意图，并且有图片，则支持对dialogue+图片，文本处理类执行视觉大模型，转换为文本意图，不执行引导语类型5处理 dialogueId:{}",
					innerDTO.getDialogueId());
			//强制转换为文本意图
			innerDTO.setTextGenerateTextIntention();
			return true;
		}

		// 2，意图识别为图片工具意图，则同现网一致正常对图片进行AI图片工具处理

		// 保存leadCopy到hbase
		leadCopyService.leadCopyType5(innerDTO);
		if (innerDTO.getRespParams().getLeadCopy() == null) {
			log.info("没有类型5的引导语");
			return true;
		}

		// 保存hbase
		saveTextResult(innerDTO, "", "");

		// 保存数据库
		addSuccess(innerDTO, OutContentTypeEnum.TEXT);

		// 返回同步结果
		innerDTO.getRespParams().setResultType(ChatAddResultTypeEnum.SYNCHRONIZATION.getType());
		return false;
	}

}
