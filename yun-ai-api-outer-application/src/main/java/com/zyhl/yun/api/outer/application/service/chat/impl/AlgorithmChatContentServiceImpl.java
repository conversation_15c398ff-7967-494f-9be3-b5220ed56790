package com.zyhl.yun.api.outer.application.service.chat.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.annotation.MethodExecutionTimeLog;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.VoConverter;
import com.zyhl.yun.api.outer.application.dto.*;
import com.zyhl.yun.api.outer.application.service.chat.AlgorithmChatContentService;
import com.zyhl.yun.api.outer.application.service.chat.ChatApplicationTypeService;
import com.zyhl.yun.api.outer.application.service.mq.AiAssistantMqService;
import com.zyhl.yun.api.outer.application.vo.ChatStatusVO;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.entity.ChatCommentEntity;
import com.zyhl.yun.api.outer.domain.entity.TaskAiAbilityEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.valueobject.MailInfo;
import com.zyhl.yun.api.outer.domain.valueobject.NoteInfo;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.chat.ShareDialogueResultVO;
import com.zyhl.yun.api.outer.domainservice.ResourceInfoDomainService;
import com.zyhl.yun.api.outer.enums.*;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.OutAuditStatusEnum;
import com.zyhl.yun.api.outer.external.service.EOSExternalService;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatCommentRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.repository.TaskAiAbilityRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.TaskRespParamVO;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 对话内容操作类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AlgorithmChatContentServiceImpl implements AlgorithmChatContentService {

    @Resource
    private AlgorithmChatContentRepository algorithmChatContentRepository;
    @Resource
    private SourceChannelsProperties channelsProperties;
    @Resource
    private AiAssistantMqService aiAssistantMqService;
    @Resource
    private AlgorithmChatCommentRepository algorithmChatCommentRepository;
    @Resource
    private VoConverter voConverter;
    @Resource
    private AiTextResultRepository aiTextResultRepository;
    @Resource
    private ChatApplicationTypeService chatApplicationTypeService;
    @Resource
    private ResourceInfoDomainService resourceInfoDomainService;
    @Resource
    private TaskAiAbilityRepository taskAiAbilityRepository;
    @Resource
    private EOSExternalService eosExternalService;

    @Override
    public AlgorithmChatContentEntity add(ChatAddInnerDTO params, Integer chatStatus, Long taskId, String modelCode) {

        AlgorithmChatContentEntity contentEntity = createEntity(params, chatStatus, taskId, modelCode);

        log.info("保存对话内容，对话id：{}，对话状态：{}", params.getDialogueId(), contentEntity.getChatStatus());
        algorithmChatContentRepository.saveChatContent(contentEntity);
        log.info("保存对话内容成功，对话id：{}", params.getDialogueId());

        return contentEntity;
    }

    private AlgorithmChatContentEntity createEntity(ChatAddInnerDTO params, Integer chatStatus, Long taskId, String modelCode) {
        AlgorithmChatAddDTO dto = params.getReqParams();

        AlgorithmChatContentEntity contentEntity = new AlgorithmChatContentEntity();
        contentEntity.setId(params.getDialogueId());
        contentEntity.setSessionId(params.getSessionId());
        contentEntity.setUserId(dto.getUserId());
        contentEntity.setBelongsPlatform(RequestContextHolder.getBelongsPlatform());
        contentEntity.setTaskId(taskId);
        contentEntity.setModelType(CharSequenceUtil.nullToEmpty(modelCode));

        // 输入信息
        contentEntity.setTalkType(dto.getContent().getDialogueType());
        contentEntity.setToolsCommand(params.getIntentionCode());
        contentEntity.setPrompt(dto.getContent().getPrompt());
        contentEntity.setResourceType(dto.getContent().getResourceType());
        contentEntity.setInContent(dto.getContent().getDialogue());
        contentEntity.setInResourceId(dto.getContent().getResourceId());
        contentEntity.setInAuditStatus(OutAuditStatusEnum.SUCCESS.getCode());
        contentEntity.setInAuditTime(DateUtil.date());
        contentEntity.setExtInfo(dto.getContent().getExtInfo());

        contentEntity.setCommandType(dto.getContent().getCommandType());
        contentEntity.setSceneTag(dto.getContent().getSceneTag());

        contentEntity.setChatStatus(chatStatus);
        contentEntity.setCreateTime(DateUtil.date());
        contentEntity.setUpdateTime(DateUtil.date());

        // 渠道、业务类型、应用类型、应用id
        contentEntity.setSourceChannel(dto.getContent().getSourceChannel());
        contentEntity.setBusinessType(channelsProperties.getType(dto.getContent().getSourceChannel()));
        contentEntity.setApplicationId(CharSequenceUtil.emptyToDefault(dto.getApplicationId(), "0"));
        contentEntity.setApplicationType(ApplicationTypeEnum.getByCodeDefaultChat(dto.getApplicationType()).getCode());

        // 推荐信息
        contentEntity.setRecommendInfo(JsonUtil.toJson(params.getRespParams().getRecommend()));

        // 输出类型
        if (DialogueIntentionEnum.SMART_FAKE_CHECK.getCode().equals(params.getIntentionCode())) {
            contentEntity.setOutContentType(OutContentTypeEnum.JSON_OBJECT.getType());
        }

        return contentEntity;
    }

    @Override
    public AlgorithmChatContentEntity addSuccess(ChatAddInnerDTO params, String modelCode, Integer outContentType) {

        // 智能鉴伪需要outContentResource=3特殊处理
        if (DialogueIntentionEnum.SMART_FAKE_CHECK.getCode().equals(params.getIntentionCode())) {
            outContentType = OutContentTypeEnum.JSON_OBJECT.getType();
        }
        AlgorithmChatContentEntity contentEntity = createEntity(params, ChatStatusEnum.CHAT_SUCCESS.getCode(), null, modelCode);

        // 结果
        contentEntity.setOutAuditStatus(OutAuditStatusEnum.SUCCESS.getCode());
        contentEntity.setOutAuditTime(DateUtil.date());
        contentEntity.setOutContentType(outContentType);
        if (params.getInterventionVO() != null) {
            contentEntity.setOutResourceId(params.getInterventionVO().getId());
        }

        boolean success = false;
        try {
            success = algorithmChatContentRepository.saveChatContent(contentEntity);
        } finally {
            log.info("保存对话内容 success:{}, 对话id:{}, 对话状态:{}", success, params.getDialogueId(),
                    contentEntity.getChatStatus());
        }

        //发送AI助手对话完成mq
        aiAssistantMqService.sendDialogueCompletedMq(contentEntity);

        return contentEntity;
    }

    @Override
    public AlgorithmChatContentEntity saveAll(ChatAddInnerDTO params) {
        // 获取tidb需要保存的数据对象
        AlgorithmChatTidbSaveDTO tidbSaveDTO = params.getAlgorithmChatTidbSaveDTO();

        // 构建对话内容数据
        AlgorithmChatContentEntity contentEntity = createEntity(params, tidbSaveDTO.getChatStatus(), null, "");

        // 结果
        contentEntity.setOutAuditStatus(tidbSaveDTO.getOutAuditStatus());
        contentEntity.setOutAuditTime(DateUtil.date());
        contentEntity.setOutContent(tidbSaveDTO.getMsg());

        boolean success = false;
        try {
            success = algorithmChatContentRepository.saveChatContent(contentEntity);
        } finally {
            log.info("保存对话内容 success:{}, 对话id:{}, 对话状态:{}", success, params.getDialogueId(),
                    contentEntity.getChatStatus());
        }

        //发送AI助手对话完成mq
        aiAssistantMqService.sendDialogueCompletedMq(contentEntity);

        return contentEntity;
    }

    @Override
    @MethodExecutionTimeLog("查询对话状态-serviceImpl")
    public ChatStatusVO getChatStatus(AiAssistantStatusReqDTO dto) {
        AlgorithmChatContentEntity entity = algorithmChatContentRepository.getById(Long.valueOf(dto.getDialogueId()));

        ChatStatusVO vo = new ChatStatusVO();
        if (entity == null) {
            log.info("对话不存在，对话id：{}", dto.getDialogueId());
            return vo.notExist();
        } else if (!dto.getUserId().equals(entity.getUserId())) {
            log.info("对话id和用户id不匹配，对话id：{}，用户id：{}", dto.getDialogueId(), dto.getUserId());
            return vo.notExist();
        }

        vo.setChatStatus(entity.getChatStatus());

        // 获取评论
        ChatCommentEntity comment = algorithmChatCommentRepository.getByDialogueId(entity.getUserId(), entity.getId());
        if (comment != null) {
            vo.setLikeStatus(comment.getLikeComment());
        }

        return vo;
    }

    @Override
    public List<ShareDialogueResultVO> shareBatchGet(ShareBatchGetDTO dto) {
        // 查询数据
        Long sessionId = ObjectUtil.isEmpty(dto.getSessionId()) ? null : Long.valueOf(dto.getSessionId());
        List<AlgorithmChatContentEntity> list = algorithmChatContentRepository.getShareContentList(sessionId, dto.getApplicationType(), dto.getUserId(), dto.getDialogueIdList());
        if (ObjectUtil.isEmpty(list)) {
            log.info("对话不存在，请求参数：{}", JSONUtil.toJsonStr(dto));
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_DIALOGUE_ID);
        }

        List<ShareDialogueResultVO> voList = new ArrayList<>();

        // 数据处理
        list.forEach(entity -> {
            ShareDialogueResultVO vo = voConverter.toShareDialogueResultVO(entity);
            vo.setInAuditTime(DateUtil.format(entity.getInAuditTime(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN));
            vo.setOutAuditTime(DateUtil.format(entity.getOutAuditTime(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN));
            vo.setCreateTime(DateUtil.format(entity.getCreateTime(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN));
            vo.setOutContent(entity.getOutContent());

            // 输入资源信息
            String inResourceId = entity.getInResourceId();
            boolean v2Resource = ObjectUtil.isNotEmpty(inResourceId) && inResourceId.startsWith("[");
            if (ResourceTypeEnum.isMail(entity.getResourceType())) {
                if (v2Resource) {
                    List<MailInfo> mailList = JsonUtil.parseArray(inResourceId, new TypeReference<List<MailInfo>>() {
                    });
                    inResourceId = mailList.get(0).getMailId();
                }
                vo.setInResourceExtInfo(resourceInfoDomainService.getMailInfo(RequestContextHolder.getPhoneNumber(), inResourceId));
            } else if (ResourceTypeEnum.isNote(entity.getResourceType())) {
                if (v2Resource) {
                    List<NoteInfo> noteList = JsonUtil.parseArray(inResourceId, new TypeReference<List<NoteInfo>>() {
                    });
                    inResourceId = noteList.get(0).getNoteId();
                }
                vo.setInResourceExtInfo(resourceInfoDomainService.getNoteInfo(RequestContextHolder.getUserId(), inResourceId));
            } else if (ResourceTypeEnum.isImage(entity.getResourceType())) {
                if (v2Resource) {
                    List<File> fileList = JsonUtil.parseArray(inResourceId, new TypeReference<List<File>>() {
                    });
                    inResourceId = fileList.get(0).getFileId();
                }
                vo.setInResourceExtInfo(resourceInfoDomainService.getImgInfo(entity.getUserId(), RequestContextHolder.getBelongsPlatform(), inResourceId));
            } else if (ResourceTypeEnum.isDocument(entity.getResourceType())) {
                String fileIds = "";
                if (v2Resource) {
                    List<File> fileList = JsonUtil.parseArray(inResourceId, new TypeReference<List<File>>() {
                    });
                    fileIds = fileList.stream().map(File::getFileId).collect(Collectors.joining(","));
                    vo.setInResourceExtInfo(resourceInfoDomainService.getImgInfo(entity.getUserId(), RequestContextHolder.getBelongsPlatform(), fileIds));
                } else {
                    CloudDiskDocumentDTO ids = JSON.parseObject(entity.getInResourceId(), CloudDiskDocumentDTO.class);
                    fileIds = ids.getFileIdList().get(0);
                }
                vo.setInResourceExtInfo(resourceInfoDomainService.getImgInfo(entity.getUserId(), RequestContextHolder.getBelongsPlatform(), fileIds));
            } else if (ResourceTypeEnum.isDialogueId(entity.getResourceType())) {
                if (v2Resource) {
                    List<String> dialogueIdList = JsonUtil.parseArray(inResourceId, new TypeReference<List<String>>() {
                    });
                    inResourceId = dialogueIdList.get(0);
                }
                AlgorithmChatContentEntity dialogueEntity = algorithmChatContentRepository.getById(Long.parseLong(inResourceId));
                if (Objects.nonNull(dialogueEntity)) {
                    TaskAiAbilityEntity taskEntity = taskAiAbilityRepository.getTaskEntity(dialogueEntity.getTaskId());
                    taskRespHandle(taskEntity, entity, vo, true);
                }
            }

            // 输出资源信息
            if (ObjectUtil.isNotEmpty(entity.getTaskId())) {
                TaskAiAbilityEntity taskEntity = taskAiAbilityRepository.getTaskEntity(entity.getTaskId());
                taskRespHandle(taskEntity, entity, vo, false);
            } else {
                try {
                    if (Objects.nonNull(entity.getRecommendInfo())) {
                        vo.setRecommend(JsonUtil.parseObject(entity.getRecommendInfo(), DialogueRecommendVO.class));
                    }
                } catch (Exception e) {
                    log.error("推荐信息json解析失败，recommendInfo:{}", entity.getRecommendInfo(), e);
                }
            }

            // 查询hbase
            AiTextResultEntity aiTextResult = aiTextResultRepository.getByRowKey(entity.getUserId(), entity.getId());
            if (Objects.nonNull(aiTextResult) && ObjectUtil.isNotEmpty(aiTextResult.getRespParameters())) {
                if (aiTextResult.getRespParameters().indexOf("[") == 0) {
                    vo.setOutContent(aiTextResult.getRespParameters());
                } else {
                    AiTextResultRespParameters resp = JsonUtil.parseObject(aiTextResult.getRespParameters(), AiTextResultRespParameters.class);
                    if (Objects.nonNull(resp)) {
                        vo.setOutContent(resp.getData());
                        vo.setReasoningContent(resp.getReasoningContent());
                        vo.setNetworkSearchInfoList(resp.getNetworkSearchInfoList());
                        vo.setPersonalKnowledgeFileList(resp.getPersonalKnowledgeFileList());
                        vo.setLeadCopy(resp.getLeadCopy());
                        vo.setTitle(resp.getTitle());
                    }
                }
            }

            // 智能体
            if (ApplicationTypeEnum.isIntelligen(entity.getApplicationType()) && ObjectUtil.isNotEmpty(entity.getApplicationId())) {
                vo.setApplicationInfo(chatApplicationTypeService.getByApplicationId(entity.getApplicationId()));
            }

            voList.add(vo);
        });

        return voList;
    }


    private void taskRespHandle(TaskAiAbilityEntity taskEntity, AlgorithmChatContentEntity entity, ShareDialogueResultVO vo, boolean in) {
        if (Objects.nonNull(taskEntity) && ObjectUtil.isNotEmpty(taskEntity.getRespParam())) {
            List<TaskRespParamVO> paramVOList = JSONUtil.toList(taskEntity.getRespParam(), TaskRespParamVO.class);
            if (ObjectUtil.isNotEmpty(paramVOList)) {
                TaskRespParamVO respParamVO = paramVOList.get(0);
                if (ResourceTypeEnum.isImage(respParamVO.getOutResourceType())) {
                    if (in) {
                        if (ImageTransmissionTypeEnum.isYunDisk(respParamVO.getImageTransmissionType())) {
                            // 输入结果个人云
                            vo.setInResourceExtInfo(resourceInfoDomainService.getImgInfo(entity.getUserId(), RequestContextHolder.getBelongsPlatform(), respParamVO.getOutResourceId()));
                        } else if (ImageTransmissionTypeEnum.isEos(respParamVO.getImageTransmissionType())) {
                            // 输出结果是EOS
                            String fileName = "file." + (DialogueIntentionEnum.INTELLIGENT_CUTOUT.getCode().equals(entity.getToolsCommand()) ? "png" : "jpg");
                            vo.setInResourceId(eosExternalService.getFileUrl(respParamVO.getOutResourceId(), fileName, 24 * 60 * 60 * 1000L));
                        }
                    } else {
                        if (ImageTransmissionTypeEnum.isYunDisk(respParamVO.getImageTransmissionType())) {
                            // 输出结果个人云
                            vo.setOutResourceType(1);
                            vo.setOutResourceId(respParamVO.getOutResourceId());
                            vo.setOutResourceExtInfo(resourceInfoDomainService.getImgInfo(entity.getUserId(), RequestContextHolder.getBelongsPlatform(), respParamVO.getOutResourceId()));
                        } else if (ImageTransmissionTypeEnum.isEos(respParamVO.getImageTransmissionType())) {
                            // 输出结果是EOS
                            vo.setOutResourceType(2);
                            String fileName = "file." + (DialogueIntentionEnum.INTELLIGENT_CUTOUT.getCode().equals(entity.getToolsCommand()) ? "png" : "jpg");
                            vo.setOutResourceId(eosExternalService.getFileUrl(respParamVO.getOutResourceId(), fileName, 24 * 60 * 60 * 1000L));
                        }
                    }
                }
            }
        }
    }

}
