package com.zyhl.yun.api.outer.application.chatv2.service.impl.openapi;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.FileOperationUtil;
import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.dto.CreateOutlineDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.dto.PptGenDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.vo.PptGenVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.note.dto.NoteListReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.note.dto.PageInfo;
import com.zyhl.hcy.yun.ai.common.platform.third.client.note.vo.NoteDetailVO;
import com.zyhl.yun.api.outer.application.assembler.ChatFlowResultAssembler;
import com.zyhl.yun.api.outer.application.chatv2.dto.AiPptValidationResult;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueAttachmentDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueTextToolSettingDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueToolSettingDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.event.completeimpl.OutlineToPptCallbackEvent;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.impl.TextToolAiPptHandlerImpl;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatAddFileService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.service.openapi.OpenapiLingxiMeetingAfterService;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueFlowResultVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO;
import com.zyhl.yun.api.outer.application.config.ApplicationAgentLingxiConfig;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig.IntelligentMeeting;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueIntentionOutput;
import com.zyhl.yun.api.outer.domain.valueobject.NoteInfo;
import com.zyhl.yun.api.outer.domainservice.ChatConfigServiceDomainService;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.AiTextResultVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddFlowStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.external.NoteExternalService;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * openapi lingxi 智能体对话-智能会议后置-服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-18 21:00
 */
@Slf4j
@Service
public class OpenapiLingxiMeetingAfterServiceImpl implements OpenapiLingxiMeetingAfterService {

	@Resource
	private DataSaveService dataSaveService;
	@Resource
	private ChatAddFileService chatAddFileService;
	@Resource
	private ChatConfigServiceDomainService chatConfigServiceDomainService;
	@Resource
	private TextModelExternalService textModelExternalService;
	@Resource
	private AlgorithmChatContentRepository algorithmChatContentRepository;
	@Resource
	private ChatTextToolBusinessConfig chatTextToolBusinessConfig;
	@Resource
	private ChatFlowResultAssembler chatFlowResultAssembler;
	@Resource
	private TextToolAiPptHandlerImpl textToolAiPptHandlerImpl;
	@Resource
	private OutlineToPptCallbackEvent outlineToPptCallbackEvent;
	@Resource
	private ApplicationAgentLingxiConfig applicationAgentLingxiConfig;
	@Resource
	private NoteExternalService noteExternalService;

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		DialogueToolSettingDTO toolSetting = handleDTO.getInputInfoDTO().getToolSetting();
		if (null != toolSetting && null != toolSetting.getTextToolSetting()
				&& Boolean.TRUE.equals(toolSetting.getTextToolSetting().getEnablePptMake())) {
			// 3.2 AI生成ppt意图：大纲生成ppt（默认固定配置模板nacos
			// chatTextToolBusinessConfig.getIntelligentMeeting().getDefaultPptTemplateId()）
			String templateId = chatTextToolBusinessConfig.getIntelligentMeeting().getDefaultPptTemplateId();
			toolSetting.getTextToolSetting().setTemplateId(templateId);
			log.info("进入 runGenPpt dialogueId:{}, 设置默认ppt模板id:{}", handleDTO.getDialogueId(), templateId);
			return runGenPpt(handleDTO);
		} else {
			log.info("进入 runPptOutline dialogueId:{}", handleDTO.getDialogueId());
			// 3.1 AI生成ppt意图：笔记生成大纲
			return runPptOutline(handleDTO);
		}
	}

	/**
	 * AI生成ppt意图：笔记生成大纲
	 * 
	 * @param handleDTO
	 * @return
	 */
	private boolean runPptOutline(ChatAddHandleDTO handleDTO) {

		if (null == handleDTO.getReqDTO().getDialogueInput().getAttachment()
				|| CollUtil.isEmpty(handleDTO.getReqDTO().getDialogueInput().getAttachment().getNoteList())) {
			NoteListReqDTO noteReqDTO = new NoteListReqDTO();
			noteReqDTO.setToken(RequestContextHolder.getToken());
			// 查找录音笔记
			noteReqDTO.setNoteTypeFilter(
					Collections.singletonList(applicationAgentLingxiConfig.getBusinessConfig().getNoteType()));
			PageInfo pageInfo = new PageInfo();
			pageInfo.setPageSize(applicationAgentLingxiConfig.getBusinessConfig().getNotePageSize());
			noteReqDTO.setPageInfo(pageInfo);
			List<NoteDetailVO> noteList = noteExternalService.getNoteList(noteReqDTO);
			// 判断笔记是否为空
			if (CollUtil.isEmpty(noteList)) {
				log.warn("暂无录音笔记内容");
				String emptyNoteModelText = applicationAgentLingxiConfig.getTextConfig().getEmptyNoteModelText();
				handleDTO.getSseEmitterOperate().sendAndComplete(
						OpenApiLingxiChatRespVO.getOpenApiLingxiChatTextModelErrorResp(handleDTO, emptyNoteModelText));
				return false;
			}
			// 查找笔记信息
			NoteInfo note = getNoteInfoByLikeTitle(noteList,
					DialogueIntentionVO.getMainIntention(handleDTO.getIntentionVO()));
			if (null == note) {
				log.warn("会议对应的录音笔记不存在");
				String notFoundNoteModelText = applicationAgentLingxiConfig.getTextConfig().getNotFoundNoteModelText();
				handleDTO.getSseEmitterOperate().sendAndComplete(OpenApiLingxiChatRespVO
						.getOpenApiLingxiChatTextModelErrorResp(handleDTO, notFoundNoteModelText));
				return false;
			}
			// 设置对应的最新笔记
			DialogueAttachmentDTO attachment = new DialogueAttachmentDTO();
			attachment.setAttachmentTypeList(Collections.singletonList(ResourceTypeEnum.NOTE.getType()));
			attachment.setNoteList(Collections.singletonList(note));
			handleDTO.getReqDTO().getDialogueInput().setAttachment(attachment);
		}

		if (!(handleDTO.isReqResourceOnlyNoteSse()
				&& CollUtil.isNotEmpty(handleDTO.getInputInfoDTO().getAttachment().getNoteList()))) {
			log.info("不支持笔记以外的附件生成大纲");
			return true;
		}

		List<File> attachmentToFiles = Collections.emptyList();
		try {

			// 聊天添加响应对象
			ChatAddRespVO respVO = handleDTO.getRespVO();
			respVO.setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());
			// 入库hbase结果列表-大模型之前
			List<DialogueFlowResult> beforeOutputList = new ArrayList<>();
			IntelligentMeeting intelligentMeeting = chatTextToolBusinessConfig.getIntelligentMeeting();
			// 思考过程
			respVO.setFlowResult(new DialogueFlowResultVO(0, FlowResultTypeEnum.REASONING_RESULT,
					intelligentMeeting.getPptOutlineTitle(), intelligentMeeting.getPptOutlineTip()));
			beforeOutputList.add(chatFlowResultAssembler.getFlowResult(respVO.getFlowResult()));
			handleDTO.getSseEmitterOperate().send(OpenApiLingxiChatRespVO.getOpenApiLingxiChatResp(handleDTO, respVO));

			AiTextResultRespParameters respParameters = AiTextResultRespParameters.builder()
					.version(AiTextResultVersionEnum.V2.getVersion()).resultCode(ResultCodeEnum.SUCCESS.getResultCode())
					.resultMsg(ResultCodeEnum.SUCCESS.getResultMsg()).build();
			respParameters.setOutputCommandVO(handleDTO.getIntentionVO());

			// 监听器
			SseEventListener event = new SseEventListener(handleDTO, null);
			// 获取用户设置的模型，没有设置则使用默认模型
			ChatConfigEntity chatConfigEntity = chatConfigServiceDomainService.getUserCanUseModel(event.getUserId(),
					event.getPhone(), handleDTO.getAssistantEnum(), handleDTO.getBusinessType());
			event.setModelCode(chatConfigEntity.getModelType());
			// 设置输出之前的文案
			event.setBeforeOutputList(beforeOutputList);
			event.setTitle(intelligentMeeting.getPptOutlineTextModelTitle());
			// 设置当前的索引，下一次加1
			event.setSendIndex(0);
			// 不操作数据
			event.setOperateDatabase(false);
			event.setCompleteCallbackEvent(outlineToPptCallbackEvent);
			// 前端不传参设置设置默认值
			if (null == handleDTO.getInputInfoDTO().getToolSetting()) {
				handleDTO.getInputInfoDTO().setToolSetting(new DialogueToolSettingDTO());
			}
			if (null == handleDTO.getInputInfoDTO().getToolSetting().getTextToolSetting()) {
				handleDTO.getInputInfoDTO().getToolSetting()
						.setTextToolSetting(DialogueTextToolSettingDTO.getDefault());
			}

			Optional<DialogueTextToolSettingDTO> dialogueTextToolSettingDTO = Optional.of(handleDTO)
					.map(ChatAddHandleDTO::getReqDTO).map(ChatAddReqDTO::getDialogueInput)
					.map(DialogueInputInfoDTO::getToolSetting).map(DialogueToolSettingDTO::getTextToolSetting);
			CreateOutlineDTO createOutlineDTO = new CreateOutlineDTO();
			createOutlineDTO.setRequestId(Long.toString(handleDTO.getDialogueId()));
			dialogueTextToolSettingDTO.map(DialogueTextToolSettingDTO::getPage)
					.ifPresent(pageType -> createOutlineDTO.setPage(Integer.parseInt(pageType)));
			dialogueTextToolSettingDTO.map(DialogueTextToolSettingDTO::getScene)
					.ifPresent(scene -> createOutlineDTO.setScene(Integer.parseInt(scene)));
			dialogueTextToolSettingDTO.map(DialogueTextToolSettingDTO::getLanguage)
					.ifPresent(createOutlineDTO::setLanguage);
			dialogueTextToolSettingDTO.map(DialogueTextToolSettingDTO::getGroup)
					.ifPresent(group -> createOutlineDTO.setGroup(Integer.parseInt(group)));

			attachmentToFiles = chatAddFileService.getAttachmentToFile(handleDTO);
			if (!CollectionUtils.isEmpty(attachmentToFiles)) {
				File[] array = attachmentToFiles.stream().filter(Objects::nonNull).toArray(File[]::new);
				createOutlineDTO.setFiles(array);
				// 设置临时文件路径
				event.setAttachmentToFilePaths(
						attachmentToFiles.stream().distinct().map(File::getAbsolutePath).collect(Collectors.toList()));
			}

			// 设置用户token
			createOutlineDTO.setToken(RequestContextHolder.getToken());

			// 调用阿里ppt大纲（不传入content，只传文件）
			textModelExternalService.streamAiPptOutlineDialogue(createOutlineDTO, event);

			return Boolean.FALSE;
		} catch (Exception e) {
			log.error("streamAiPptOutlineDialogue error:", e);
			if (!CollectionUtils.isEmpty(attachmentToFiles)) {
				// 删除临时文件
				List<String> attachmentToFilePaths = attachmentToFiles.stream().distinct().map(File::getAbsolutePath)
						.collect(Collectors.toList());
				FileOperationUtil.deleteLocalFiles(attachmentToFilePaths);
			}
			throw e;
		}

	}

	/**
	 * AI生成ppt意图：大纲生成ppt（默认固定配置模板nacos）
	 * 
	 * @param handleDTO
	 * @return
	 */
	private boolean runGenPpt(ChatAddHandleDTO handleDTO) {
		// 必填参数校验
		AiPptValidationResult aiPptValidationResult = AiPptValidationResult.validateRequiredParams(handleDTO);
		String editedOutline = aiPptValidationResult.getEditedOutline();
		String templateId = aiPptValidationResult.getTemplateId();
		String previousDialogueId = aiPptValidationResult.getPreviousDialogueId();
		// 获取上一次AI文本结果
		AiTextResultEntity aiTextResult = dataSaveService.getHbaseResult(RequestContextHolder.getUserId(),
				previousDialogueId);
		if (null == aiTextResult || null == aiTextResult.getRespParameters()) {
			log.error("hb查询不到上一次对话信息,dialogueId:{}", handleDTO.getDialogueId());
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}
		AiTextResultRespParameters respParams = JSONUtil.toBean(aiTextResult.getRespParameters(),
				AiTextResultRespParameters.class);
		// 检查上一次意图是否为AI-PPT
		DialogueIntentionOutput firstIntention = respParams.getOutputCommand();
		String mainCommand = firstIntention.getCommand();
		String subCommand = firstIntention.getSubCommand();
		if (!(DialogueIntentionEnum.isTextToolIntention(mainCommand) && DialogueIntentionSubEnum.isAiPpt(subCommand))) {
			return true;
		}
		DialogueIntentionVO.IntentionInfo mainIntention = DialogueIntentionVO
				.getMainIntention(handleDTO.getIntentionVO());
		// 设置输出意图及参数信息
		handleDTO.getRespVO().setOutputCommandVO(mainIntention);
		// 处理输出内容
		List<DialogueFlowResult> outputList = respParams.getOutputList();
		if (CollUtil.isEmpty(outputList)) {
			log.error("查询大纲信息为空,dialogueId:{}", handleDTO.getDialogueId());
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}
		DialogueFlowResult textModelOutput = getTextModelResult(outputList);
		// 端没传大纲
		if (StringUtils.isBlank(editedOutline)) {
			editedOutline = textModelOutput.getOutContent();
		} else {
			textModelOutput.setOutContent(editedOutline);
			dataSaveService.updateResult(RequestContextHolder.getUserId(), previousDialogueId, textModelOutput);
		}
		// 创建PPT生成DTO
		PptGenDTO pptGenDTO = new PptGenDTO();
		pptGenDTO.setMarkdown(editedOutline);
		pptGenDTO.setTemplateId(Long.valueOf(templateId));
		pptGenDTO.setUserId(RequestContextHolder.getUserId());
		pptGenDTO.setParentPath(chatTextToolBusinessConfig.getAiPptGenerate().getPersonalPath());
		pptGenDTO.setCoverPath(chatTextToolBusinessConfig.getAiPptGenerate().getPersonalCoverPath());
		pptGenDTO.setToken(RequestContextHolder.getToken());
		// 生成PPT任务
		PptGenVO pptGenVO = textModelExternalService.generateAiPpt(pptGenDTO);
		if (null == pptGenVO || null == pptGenVO.getTaskId()) {
			log.error("创建任务失败 dialogueId:{}, pptGenVO:{}", handleDTO.getDialogueId(), JSONUtil.toJsonStr(pptGenVO));
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_AI_MODEL);
		}
		Long taskId = pptGenVO.getTaskId();
		String title = pptGenVO.getTitle();
		JSONObject jsonObject = new JSONObject();
		jsonObject.set("taskId", taskId);
		handleDTO.setOutResourceId(jsonObject.toString());
		// 保存数据库
		dataSaveService.add(handleDTO, ChatStatusEnum.CHAT_IN);

		// 任务创建完推送结果
		DialogueFlowResultVO flowResult = handleDTO.getRespVO().getFlowResult();
		handleDTO.getRespVO().setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());
		flowResult.setResultType(FlowResultTypeEnum.TOOL_RESULT.getType());
		flowResult.setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());
		flowResult.setIndex(handleDTO.getFlowResultIndex());
		flowResult.setAiFunctionResult(
				textToolAiPptHandlerImpl.buildAiFunctionResult(Boolean.FALSE, 0, taskId, title, null, null, null));

		// 保存hbase-所有对话结果
		AiTextResultRespParameters respParameters = AiTextResultRespParameters.builder()
				.version(AiTextResultVersionEnum.V2.getVersion()).resultCode(ResultCodeEnum.SUCCESS.getResultCode())
				.outputList(Collections.singletonList(chatFlowResultAssembler.getFlowResult(flowResult))).build();
		// 保存hbase
		dataSaveService.saveHbaseAllChatResult(handleDTO, respParameters);

		respParameters.setOutputCommandVO(handleDTO.getIntentionVO());

		// 轮询推送PPT生成结果
		textToolAiPptHandlerImpl.runThread(handleDTO, taskId);
		return false;
	}

	/**
	 * 获取大模型结果
	 * 
	 * @param dialogResult 对话结果
	 * @return
	 */
	private DialogueFlowResult getTextModelResult(List<DialogueFlowResult> outputList) {
		if (CollUtil.isNotEmpty(outputList)) {
			for (DialogueFlowResult output : outputList) {
				if (Objects.equals(FlowResultTypeEnum.TEXT_MODEL.getType(), output.getResultType())) {
					return output;
				}
			}
		}
		return null;
	}
	
	/**
	 * 获取笔记信息 模糊匹配标题
	 * @param noteList 笔记列表
	 * @param mainIntention 意图信息
	 * @return
	 */
	private NoteInfo getNoteInfoByLikeTitle(List<NoteDetailVO> noteList, IntentionInfo mainIntention) {
		if (CollUtil.isEmpty(noteList)) {
			return null;
		}
		String title = null;
		if (null != mainIntention) {
			List<String> titleList = mainIntention.getArgumentMap()
					.get(ChatTextToolBusinessConfig.ARGUMENT_AI_MEETING_CONTENT_LIST);
			if (CollUtil.isNotEmpty(titleList)) {
				title = titleList.get(0);
			}
		}
		NoteInfo note = null;
		if (StringUtils.isNotEmpty(title)) {
			for (NoteDetailVO noteBean : noteList) {
				if (null != noteBean.getTitle() && noteBean.getTitle().equals(title)) {
					// 优先全匹配
					note = new NoteInfo(noteBean.getNoteId(), noteBean.getTitle());
					break;
				}
			}
		}
		if (null == note && StringUtils.isNotEmpty(title)) {
			for (NoteDetailVO noteBean : noteList) {
				if (null != noteBean.getTitle() && noteBean.getTitle().contains(title)) {
					// 然后模糊匹配
					note = new NoteInfo(noteBean.getNoteId(), noteBean.getTitle());
					break;
				}
			}
		}
		if (null == note && applicationAgentLingxiConfig.getBusinessConfig().getDefaultRecentOne()) {
			// 循环查找空，配置默认最近一条，则返回最新的一条
			log.info("循环查找空，配置默认最近一条，则返回最新的一条 mainIntention:{},", JSONUtil.toJsonStr(mainIntention));
			return new NoteInfo(noteList.get(0).getNoteId(), noteList.get(0).getTitle());
		}
		return note;
	}

}
