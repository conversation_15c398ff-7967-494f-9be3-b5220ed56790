package com.zyhl.yun.api.outer.application.chatv2.pojo;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.dashscope.utils.JsonUtils;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.hcy.yun.ai.common.model.api.client.ali.vo.GenericSearchVO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.rag.vo.RewriteResultVO;
import com.zyhl.yun.api.outer.config.KnowledgeDialogueProperties;
import com.zyhl.yun.api.outer.config.knowledge.PoliticianInfo;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.domain.valueobject.HtmlInfo;
import com.zyhl.yun.api.outer.domain.valueobject.KnowledgeSearchInfo;
import com.zyhl.yun.api.outer.domain.vo.ChatConfigVO;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeBaseEnum;
import com.zyhl.yun.api.outer.vo.KnowledgeAiExpansionVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述：知识库流程处理流传信息
 *
 * <AUTHOR> zhumaoxian  2025/4/13 21:26
 */
@Slf4j
@Data
public class KnowledgeFlowInfo {

    /**
     * 公共知识库可用
     */
    private boolean commonEnable;
    /**
     * 个人知识库可用
     */
    private boolean personalEnable;
    /**
     * VIP专属智能体-公共知识库可用
     */
    private boolean vipCommonEnable;

    public boolean isEnable() {
        return commonEnable || personalEnable || vipCommonEnable;
    }

    /**
     * 用户选中的知识库
     */
    private List<UserKnowledgeEntity> selectedKnowledgeList;

    /**
     * 用户输入
     */
    private String query = "";

    /**
     * 行政人物信息
     */
    private String politician = "";

    /**
     * 相关知识，送给大模型的数据
     */
    private List<KnowledgeFlowInfoDataHandle> knowledgeList = new ArrayList<>();

    /**
     * 个人知识库文件列表
     */
    private List<KnowledgeSearchInfo> personalFileList = new ArrayList<>();

    /**
     * 公共知识库文件列表
     */
    private List<KnowledgeSearchInfo> commonFileList = new ArrayList<>();

    /**
     * 联网搜索数据
     */
    private List<GenericSearchVO> networkList = new ArrayList<>();

    /**
     * 大模型配置信息
     */
    private ChatConfigVO chatConfigVO;

    /**
     * 没有命中个人知识库的补偿搜索-名词库结果
     */
    private List<String> nounLibraryList = new ArrayList<>();

    /**
     * 重写结果
     */
    private RewriteResultVO rewrite;

    /**
     * AI扩写信息
     */
    private KnowledgeAiExpansionVO aiExpansion;

    /**
     * 获取历史内容列表文本
     *
     * @param reqDTO 模型请求
     */
    public void historyHandle(TextModelTextReqDTO reqDTO) {
        // 历史对话处理
        List<TextModelMessageDTO> historyList = new ArrayList<>();
        if (reqDTO.getMessageDtoList().size() > 1) {
            historyList.addAll(reqDTO.getMessageDtoList().subList(0, reqDTO.getMessageDtoList().size() - 1));
        }
        LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】【历史对话】替换行政人物前历史对话信息第{}个分块：\n{}", JsonUtil.toJson(historyList));
    }

    /**
     * 行政人物处理
     *
     * @param config 配置信息
     */
    public void politicianHandle(KnowledgeDialogueProperties config) {

        if (!config.isPoliticianEnabled() || ObjectUtil.isEmpty(config.getPoliticianList())) {
            return;
        }

        // 先排序
        config.getPoliticianList().sort(Comparator.comparing(PoliticianInfo::getSort).reversed());

        // 每个替换信息按照替换字数从多到少排序
        config.getPoliticianList().forEach(item -> {
            item.getReplaceInfoList().forEach(replaceInfo -> replaceInfo.setLength(replaceInfo.getReplace().length()));
            item.getReplaceInfoList().sort(Comparator.comparing(PoliticianInfo.ReplaceInfo::getLength).reversed());
        });

        // 命中的行政人物
        Set<PoliticianInfo> hitSet = new HashSet<>();

        // 相关知识命中的行政人物（人物 -> 尊称 -> 文档 -> 分片）
        for (PoliticianInfo item : config.getPoliticianList()) {
            for (PoliticianInfo.ReplaceInfo replaceInfo : item.getReplaceInfoList()) {
                for (KnowledgeFlowInfoDataHandle data : knowledgeList) {
                    for (int i = 0; i < data.getSegmentList().size(); i++) {
                        String segment = data.getSegmentList().get(i);
                        if (segment.contains(replaceInfo.getReplace())) {
                            hitSet.add(item);
                            data.getSegmentList().set(i, segment.replace(replaceInfo.getReplace(), replaceInfo.getHonorific()));
                        }
                    }
                }
            }
        }

        if (ObjectUtil.isNotEmpty(hitSet)) {
            // 排序
            List<PoliticianInfo> hitList = new ArrayList<>(hitSet);
            hitList.sort(Comparator.comparing(PoliticianInfo::getSort).reversed());
            log.info("【知识库对话】【RAG重要节点日志】【行政人物】命中行政人物：{}", JsonUtils.toJson(hitList));

            StringBuilder sb = new StringBuilder();
            for (int i = 1, size = hitList.size(); i <= size; i++) {
                PoliticianInfo item = hitList.get(i - 1);
                sb.append("➡").append(item.getReplaceInfoList().stream().map(PoliticianInfo.ReplaceInfo::getHonorific).distinct().collect(Collectors.joining("➡")));
            }

            // 最终结果
            politician = sb.substring(1);
            log.info("【知识库对话】【RAG重要节点日志】【行政人物】行政人物回答顺序：{}", politician);
        } else {
            log.info("【知识库对话】【RAG重要节点日志】【行政人物】未命中行政人物");
        }
    }


    /**
     * 相关知识文本
     *
     * @return 相关知识文本
     */
    public String getKnowledgeText(boolean enableMark) {
        if (ObjectUtil.isEmpty(knowledgeList)) {
            return "暂无资料";
        }
        StringBuilder sb = new StringBuilder("<检索结果>");
        for (int i = 0, size = knowledgeList.size(); i < size; i++) {
            KnowledgeFlowInfoDataHandle data = knowledgeList.get(i);
            if (KnowledgeBaseEnum.isCommon(data.getKnowledgeBase())) {
                // 公共知识库无角标
                data.setFileName("内部文件");
                sb.append(data.format(i + 1, false));
            } else {
                sb.append(data.format(i + 1, enableMark));
            }
        }
        sb.append("</检索结果>");

        LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】【行政人物】相关信息替换行政人物后第{}个分块：\n{}", sb.toString());
        return sb.toString();
    }

    /**
     * 名词库文本
     *
     * @return 相关名词库文本
     */
    public String getNounLibraryText() {
        StringBuilder sb = new StringBuilder("<检索结果>");
        for (int i = 0, size = nounLibraryList.size(); i < size; i++) {
            String nounLibrary = nounLibraryList.get(i);
            sb.append("<文档>");
            sb.append("<文档名称>").append("名词库").append("</文档名称>");
            sb.append("<文档片段>");
            sb.append("<片段>").append(nounLibrary).append("</片段>");
            sb.append("</文档片段>");
            sb.append("<文档创建时间>").append("无").append("</文档创建时间>");
            sb.append("<文档更新时间>").append("无").append("</文档更新时间>");
            sb.append("</文档>");
        }
        sb.append("</检索结果>");

        LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】【名词库】相关信息替换名词库后第{}个分块：\n{}", sb.toString());
        return sb.toString();
    }

    public String getNetworkText(boolean enableMark) {
        StringBuilder sb = new StringBuilder("<检索结果>");
        for (int i = 0, size = networkList.size(); i < size; i++) {
            GenericSearchVO vo = networkList.get(i);
            if (ObjectUtil.isEmpty(vo)) {
                continue;
            }
            sb.append("<网页>");
            if (enableMark) {
                sb.append("<网页ID>[").append(i + 1).append("]</网页ID>");
            }
            sb.append("<网页标题>").append(vo.getTitle()).append("</网页标题>");
            sb.append("<网页片段>").append(vo.getSnippet()).append("</网页片段>");
            sb.append("<更新时间>").append(publishTimeFmt(vo.getPublishTime())).append("</更新时间>");
            sb.append("</网页>");
        }
        sb.append("</检索结果>");

        LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】【行政人物】网页信息替换行政人物后第{}个分块：\n{}", sb.toString());
        return sb.toString();
    }

    public String getOutLineInfo() {
        String outline = "";
        LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】【AI扩写】aiExpansion:{}", JsonUtil.toJson(aiExpansion));
        if (aiExpansion == null) {
            return outline;
        }

        if (!Boolean.TRUE.equals(aiExpansion.getEnableWhiteVip()) && !Boolean.TRUE.equals(aiExpansion.getEnableMember())) {
            return outline;
        }

        // 大纲内容不为空
        if (StringUtils.isNotEmpty(aiExpansion.getOutline())) {
            outline = aiExpansion.getOutline();
        }

        LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】【AI扩写】大纲替换后第{}个分块：\n{}", outline);
        return outline;
    }

    private String publishTimeFmt(Long publishTime) {
        if (publishTime == null || publishTime <= 0) {
            return "无";
        }

        return DateUtil.format(new Date(publishTime), DatePattern.UTC_PATTERN);
    }

    public List<HtmlInfo> getHtmlInfoList() {
        List<HtmlInfo> htmlInfoList = new ArrayList<>();
        for (int i = 0, size = networkList.size(); i < size; i++) {
            GenericSearchVO vo = networkList.get(i);
            if (ObjectUtil.isEmpty(vo)) {
                continue;
            }

            HtmlInfo htmlInfo = new HtmlInfo();
            htmlInfo.setIndex(i + 1);
            htmlInfo.setTitle(vo.getTitle());
            htmlInfo.setUrl(vo.getLink());
            htmlInfo.setHtmlContent(vo.getHtmlSnippet());
            htmlInfo.setSiteName(vo.getHostname());
            htmlInfo.setIcon(vo.getHostLogo());

            htmlInfoList.add(htmlInfo);
        }
        return htmlInfoList;
    }
}
