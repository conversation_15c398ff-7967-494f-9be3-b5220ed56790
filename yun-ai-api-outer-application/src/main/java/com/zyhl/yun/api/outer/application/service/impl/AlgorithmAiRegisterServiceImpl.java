package com.zyhl.yun.api.outer.application.service.impl;

import com.chinamobile.tuxedo.sdk.api.Message;
import com.chinamobile.tuxedo.sdk.api.Producer;
import com.chinamobile.tuxedo.sdk.api.SendResult;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.rocketmq.message.MessageDTO;
import com.zyhl.hcy.yun.ai.common.base.utils.MessageUtil;
import com.zyhl.hcy.yun.ai.common.base.utils.StopWatchUtil;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.AICatalogVO;
import com.zyhl.yun.api.outer.application.config.TopicLocalAlgorithmAuthorizeMqConfig;
import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.DtoConverter;
import com.zyhl.yun.api.outer.application.dto.AiRegisterReqDTO;
import com.zyhl.yun.api.outer.application.dto.PopUpProtocolQueryWebDTO;
import com.zyhl.yun.api.outer.application.mq.dto.TopicLocalAlgorithmAuthorizeMqDTO;
import com.zyhl.yun.api.outer.application.service.AlgorithmAiRegisterService;
import com.zyhl.yun.api.outer.config.AiRegisterProperties;
import com.zyhl.yun.api.outer.constants.CommonConstant;
import com.zyhl.yun.api.outer.domain.entity.AiPopUpProtocolEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiRegisterEntity;
import com.zyhl.yun.api.outer.domain.vo.AiRegisterVO;
import com.zyhl.yun.api.outer.domainservice.PopUpProtocolDomainService;
import com.zyhl.yun.api.outer.enums.AIModuleEnum;
import com.zyhl.yun.api.outer.enums.BusinessSourceEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.VideoAIStatusEnum;
import com.zyhl.yun.api.outer.external.client.resp.UserDomainRspDTO;
import com.zyhl.yun.api.outer.external.ose.enums.UserBelongsPlatformEnum;
import com.zyhl.yun.api.outer.external.service.MarketInviteExternalService;
import com.zyhl.yun.api.outer.external.YunDiskExternalService;
import com.zyhl.yun.api.outer.external.service.UserEtnService;
import com.zyhl.yun.api.outer.repository.AlgorithmAiRegisterRepository;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.PopUpProtocolQueryVO;

import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 报名重构
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AlgorithmAiRegisterServiceImpl implements AlgorithmAiRegisterService {

    @Resource
    private AlgorithmAiRegisterRepository algorithmAiRegisterRepository;
    @Resource
    private DtoConverter dtoConverter;
    @Resource
    private PopUpProtocolDomainService popUpProtocolDomainService;
    @Resource
    private YunDiskExternalService yunDiskExternalService;
    @Resource
    private MarketInviteExternalService marketInviteExternalService;
    @Resource
    private RedisOperateRepository redisOperateRepository;

    @Resource
    private UserEtnService userEtnService;

    @Resource(name = "topicLocalAlgorithmAuthorizeProducer")
    private Producer topicLocalAlgorithmAuthorizeProducer;

    @Resource
    private UidGenerator uidGenerator;

    @Resource
    private TopicLocalAlgorithmAuthorizeMqConfig topicLocalAlgorithmAuthorizeMqConfig;

    @Resource
    private AiRegisterProperties aiRegisterProperties;


    @Override
    public AiRegisterVO get(AiRegisterReqDTO dto) {
        final String userId = RequestContextHolder.getUserId();
        final AiRegisterVO result = new AiRegisterVO();
        // 视频分析逻辑
        if(BusinessSourceEnum.AI_VIDEO_ANALYSIS.getCode().equals(dto.getSourceBusiness())){
            return  getVideoStatus(dto, userId);
        }
        // 先查缓存
        final AlgorithmAiRegisterEntity redisEntity = redisOperateRepository.getRegisterEntity(userId, dto.getSourceBusiness(), dto.getModule());
        if (redisEntity != null && redisEntity.registered()) {
            log.info("报名-用户{}在redis缓存有值", userId);
            return result.register(redisEntity.getPath());
        }

        // 查询报名记录
        final AlgorithmAiRegisterEntity entity = algorithmAiRegisterRepository.queryByUserId(userId, dto.getSourceBusiness());
        if (entity == null || entity.checkDisabled()) {
            log.info("报名-用户{}没有报名记录", userId);
            return result;
        }

        log.info("报名-用户{}已经报名业务类型{}", userId, dto.getSourceBusiness());

        // 创建目录并更新数据库
        createDirAndUpdate(entity, dto.getModule());

        // 保存redis
        redisOperateRepository.setRegisterEntity(userId, dto.getSourceBusiness(), dto.getModule(), entity);

        // 返回结果
        return result.register(entity.getPath());
    }

    /**
     * 视频状态
     * @param dto
     * @return
     */
    public AiRegisterVO getVideoStatus(AiRegisterReqDTO dto, String userId){
        final AiRegisterVO result = new AiRegisterVO();
        final AlgorithmAiRegisterEntity redisEntity = redisOperateRepository.getRegisterEntity(userId, dto.getSourceBusiness(), dto.getModule());
        if (redisEntity != null){
            log.info("报名视频状态-用户{}在redis缓存有值为{}", userId, JSONUtil.toJsonStr(redisEntity));
            return new AiRegisterVO(VideoAIStatusEnum.getByStatus(redisEntity.getStatus()),redisEntity.getPath());
        }
        // 查询报名记录
        final AlgorithmAiRegisterEntity entity = algorithmAiRegisterRepository.queryByUserId(userId, dto.getSourceBusiness());
        if (entity == null) {
            log.info("报名视频状态-用户{}没有报名记录", userId);
            return result;
        }
        log.info("报名视频状态-用户{}已经报名业务类型{},{}", userId, dto.getSourceBusiness(), JSONUtil.toJsonStr(entity) );
        // 保存redis
        redisOperateRepository.setRegisterEntity(userId, dto.getSourceBusiness(), dto.getModule(), entity);

        return new AiRegisterVO(VideoAIStatusEnum.getByStatus(entity.getStatus()),entity.getPath());
    }


    /**
     * 创建目录并且更新报名数据
     *
     * @param entity 对象实体
     * @param module 模块
     */
    private void createDirAndUpdate(AlgorithmAiRegisterEntity entity, Integer module) {
        final Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();

        // 底座不一样，且业务不是智能相册时，更新目录
        if (entity.getBelongsPlatform().equals(belongsPlatform) || !BusinessSourceEnum.isAssistant(entity.getBusinessType())) {
            log.info("报名-用户底座一样或者是智能相册，不需要更新目录");
            return;
        }

        log.info("报名-用户{}底座不一样，且业务类型不是智能相册，创建云盘目录", entity.getUserId());
        final AICatalogVO catalogVO = yunDiskExternalService.createDir(module);
        if (catalogVO != null) {
            // 更新数据库报名记录
            entity.setBelongsPlatform(belongsPlatform);
            entity.setPath(catalogVO.getPath());
            entity.setDirId(catalogVO.getCatalogId());
            algorithmAiRegisterRepository.updateById(entity);
        }
    }

    @Override
    public AiRegisterVO accredit(AiRegisterReqDTO dto) {
        log.info("###accredit 开始进入AI授权 dto:{}", JSONUtil.toJsonStr(dto));

        final String userId = dto.getUserId();
        final AiRegisterVO result = new AiRegisterVO();

        // 判断是否已经报名授权
        final AlgorithmAiRegisterEntity entity = algorithmAiRegisterRepository.queryByUserId(userId, dto.getSourceBusiness());
        if (entity != null) {
            log.info("报名-用户{}已经报名授权", userId);
            if (!entity.registered()) {
                log.info("报名-用户报名状态是停用，需要改为启用");
                algorithmAiRegisterRepository.updateById(entity.enabled());
            }

            // 创建目录并更新数据库
            createDirAndUpdate(entity, dto.getModule());

            // 保存redis并返回结果
            redisOperateRepository.setRegisterEntity(userId, dto.getSourceBusiness(), dto.getModule(), entity);

            // 文档检索的需要发送授权mq给下一步
            if (BusinessSourceEnum.isDocSearch(dto.getSourceBusiness())) {
                //如果sourceBusiness为3（文档智能搜索）需要发送授权mq
                sendDocIntelligentSearchMq(userId, CommonConstant.AI_AUTHORIZE_OPEN);
            }
            return result.register(entity.getPath());
        }

        log.info("报名-用户{}没报过名，进行报名", userId);


        String path = null;
        String catalogId = null;
        if (BusinessSourceEnum.isAssistant(dto.getSourceBusiness())) {
            // 同步营销平台
            marketInviteExternalService.accept(RequestContextHolder.getPhoneNumber());

            // 创建目录
            final AICatalogVO catalogVO = yunDiskExternalService.createDir(dto.getModule());
            if (catalogVO != null) {
                path = catalogVO.getPath();
                catalogId = catalogVO.getCatalogId();
            }
        }

        // 写表
        final AlgorithmAiRegisterEntity newEntity = new AlgorithmAiRegisterEntity(dto.getSourceBusiness(), dto.getModule(), catalogId, path);
        if (BusinessSourceEnum.isAssistant(dto.getSourceBusiness())) {
            newEntity.setFactoryType(aiRegisterProperties.getDocVectorFactoryType());
            newEntity.setAlgorithmGroupCode(aiRegisterProperties.getDocVectorAlgorithmGroupCode());
        } else if (BusinessSourceEnum.isDocSearch(dto.getSourceBusiness())) {
            newEntity.setFactoryType(aiRegisterProperties.getDocFactoryType());
            newEntity.setAlgorithmGroupCode(aiRegisterProperties.getDocAlgorithmGroupCode());
        }
        algorithmAiRegisterRepository.insert(newEntity);

        // 写redis
        redisOperateRepository.setRegisterEntity(userId, dto.getSourceBusiness(), dto.getModule(), newEntity);

        // 文档检索的需要发送授权mq给下一步
        if (BusinessSourceEnum.isDocSearch(dto.getSourceBusiness())) {
            //如果sourceBusiness为3（文档智能搜索）需要发送授权mq
            sendDocIntelligentSearchMq(userId, CommonConstant.AI_AUTHORIZE_OPEN);
        }

        // 返回数据
        return result.register(path);
    }

    /**
     * 报名设置视频分析状态
     * @param dto
     * @return
     */
    @Override
    public  BaseResult<AiRegisterVO>  setVideoStatus(AiRegisterReqDTO dto){
        final String userId = dto.getUserId();
        AlgorithmAiRegisterEntity newEntity = null;

        UserDomainRspDTO userDomainRspDTO = userEtnService.getUserInfo(Long.parseLong(userId));
        if(userDomainRspDTO == null || UserBelongsPlatformEnum.OSE.getBelongsPlatform()
                .equals(userDomainRspDTO.getBelongsPlatform())){
            return BaseResult.error(ResultCodeEnum.ERROR_SERVER_NOT_READY);
        }

        //查询是否已经报名
        final AlgorithmAiRegisterEntity entity = algorithmAiRegisterRepository.queryByUserId(userId, dto.getSourceBusiness());
        if (entity != null && entity.checkStatus()) {
            log.info("报名-用户{}已经报名设置视频授权", userId);
            return BaseResult.error(ResultCodeEnum.ACCREDIT_HANDLE_ING);
        }else if (entity != null && !entity.checkStatus()){
            log.info("报名-用户报名状态是停用，改为启用");
            algorithmAiRegisterRepository.updateById(entity.videoProgress());
            newEntity = entity;
        }else {
            // 写表
            newEntity = new AlgorithmAiRegisterEntity(BusinessSourceEnum.AI_VIDEO_ANALYSIS.getCode());
            algorithmAiRegisterRepository.insert(newEntity);
            // 写redis
            redisOperateRepository.setRegisterEntity(userId, dto.getSourceBusiness(), dto.getModule(), newEntity);
        }
        // TODO 发送MQ topic
        sendVideoIntelligentSearchMq(userId, String.valueOf(VideoAIStatusEnum.SYNCHRONIZATION_IN_PROGRESS.getStatus()));

        return BaseResult.success(new AiRegisterVO(VideoAIStatusEnum.SYNCHRONIZATION_IN_PROGRESS.getStatus(), newEntity.getPath()));

    }



    /**
     * 发送智能分析搜索授权mq
     *
     * @param userId 用户id
     * @param status 状态 enable：启用 disable：禁用
     */
    public void sendVideoIntelligentSearchMq(String userId, String status) {
        StopWatch stopWatch = StopWatchUtil.createStarted();
        try {
            TopicLocalAlgorithmAuthorizeMqDTO mqDto = TopicLocalAlgorithmAuthorizeMqDTO.builder()
                    .userId(userId)
                    .aiClusterStatus(status)
                    .businessType(BusinessSourceEnum.AI_VIDEO_ANALYSIS.getCode())
                    .build();

            MessageDTO<TopicLocalAlgorithmAuthorizeMqDTO> baseMqDTO = new MessageDTO<>();
            baseMqDTO.setMessageId(TopicLocalAlgorithmAuthorizeMqConfig.TOPIC_LOCAL_ALGORITHM_VIDEO_SERIES_QUERY_MESSAGE_ID_PREFIX + uidGenerator.getUID());
            baseMqDTO.setEventType(TopicLocalAlgorithmAuthorizeMqConfig.TOPIC_LOCAL_ALGORITHM_VIDEO_SERIES_QUERY_EVENT_TYPE);
            baseMqDTO.setContent(mqDto);
            String jsonStr = JSONUtil.toJsonStr(baseMqDTO);

            Message msg = MessageUtil.createMessage();
            msg.setBody(jsonStr.getBytes());
            msg.setTopic(topicLocalAlgorithmAuthorizeMqConfig.getTopicVideoSeriesQueryTopic());
            msg.setTag(topicLocalAlgorithmAuthorizeMqConfig.getTopicVideoSeriesQuerytag());
            log.info("###sendDocIntelligentSearchMq 发送智能分析搜索授权mq消息 topic:{},tag:{},body:{}"
                    , topicLocalAlgorithmAuthorizeMqConfig.getTopicVideoSeriesQueryTopic(), topicLocalAlgorithmAuthorizeMqConfig.getTopicVideoSeriesQuerytag(), jsonStr);
            SendResult mqSendResult = topicLocalAlgorithmAuthorizeProducer.send(msg);
            log.info("###sendDocIntelligentSearchMq 发送智能分析搜索授权mq消息完成 发送结果 mqSendResult:{}", JSONUtil.toJsonStr(mqSendResult));
        } catch (Exception e) {
            log.error("###sendDocIntelligentSearchMq 发送智能分析搜索授权mq消息异常", e);
        } finally {
            log.info("###sendDocIntelligentSearchMq 发送智能分析搜索授权mq消息总耗时:{}", StopWatchUtil.logTotalTimeMs(stopWatch));
            StopWatchUtil.clearDuration();
        }
    }

    /**
     * 发送文档智能搜索授权mq
     *
     * @param userId 用户id
     * @param status 状态 enable：启用 disable：禁用
     */
    public void sendDocIntelligentSearchMq(String userId, String status) {
        StopWatch stopWatch = StopWatchUtil.createStarted();
        try {
            TopicLocalAlgorithmAuthorizeMqDTO mqDto = TopicLocalAlgorithmAuthorizeMqDTO.builder()
                .userId(userId)
                .aiClusterStatus(status)
                .businessType(BusinessSourceEnum.DOC_SEARCH.getCode())
                .build();

            MessageDTO<TopicLocalAlgorithmAuthorizeMqDTO> baseMqDTO = new MessageDTO<>();
            baseMqDTO.setMessageId(TopicLocalAlgorithmAuthorizeMqConfig.TOPIC_LOCAL_ALGORITHM_AUTHORIZE_MESSAGE_ID_PREFIX + uidGenerator.getUID());
            baseMqDTO.setEventType(TopicLocalAlgorithmAuthorizeMqConfig.TOPIC_LOCAL_ALGORITHM_AUTHORIZE_EVENT_TYPE);
            baseMqDTO.setContent(mqDto);
            String jsonStr = JSONUtil.toJsonStr(baseMqDTO);

            Message msg = MessageUtil.createMessage();
            msg.setBody(jsonStr.getBytes());
            msg.setTopic(topicLocalAlgorithmAuthorizeMqConfig.getTopic());
            msg.setTag(topicLocalAlgorithmAuthorizeMqConfig.getTag());
            log.info("###sendDocIntelligentSearchMq 开始发送文档智能搜索授权mq消息 topic:{},tag:{},body:{}"
                , topicLocalAlgorithmAuthorizeMqConfig.getTopic(), topicLocalAlgorithmAuthorizeMqConfig.getTag(), jsonStr);
            SendResult mqSendResult = topicLocalAlgorithmAuthorizeProducer.send(msg);
            log.info("###sendDocIntelligentSearchMq 发送文档智能搜索授权mq消息完成 发送结果 mqSendResult:{}", JSONUtil.toJsonStr(mqSendResult));
        } catch (Exception e) {
            log.error("###sendDocIntelligentSearchMq 发送文档智能搜索授权mq消息异常", e);
        } finally {
            log.info("###sendDocIntelligentSearchMq 发送文档智能搜索授权mq消息总耗时:{}", StopWatchUtil.logTotalTimeMs(stopWatch));
            StopWatchUtil.clearDuration();
        }
    }

    @Override
    public PopUpProtocolQueryVO protocolGet(PopUpProtocolQueryWebDTO dto) {
        // PopUpProtocolController.query，接收参数 -> PopUpProtocolDomainService.query，请求参数
        AiPopUpProtocolEntity aiPopUpProtocolEntity = dtoConverter.toAiPopUpProtocolEntity(dto);
        // 获取AI弹窗协议配置
        return popUpProtocolDomainService.protocolGet(aiPopUpProtocolEntity);
    }

    @Override
    public boolean checkAiAssistant(String userId) {
        final Integer businessType = BusinessSourceEnum.ASSISTANT.getCode();
        final Integer module = AIModuleEnum.AI_FILE_LIBRARY.getModule();

        // 先查缓存
        final AlgorithmAiRegisterEntity redisEntity = redisOperateRepository.getRegisterEntity(userId, businessType, module);
        if (redisEntity != null) {
            log.info("报名-用户{}在redis缓存有值", userId);
            return redisEntity.registered();
        }

        // 查询报名记录
        final AlgorithmAiRegisterEntity entity = algorithmAiRegisterRepository.queryByUserId(userId, businessType);
        if (entity == null) {
            log.info("报名-用户{}没有报名数据", userId);
            redisOperateRepository.setRegisterEntity(userId, businessType, module, new AlgorithmAiRegisterEntity().notRegistered());
            return false;
        }

        log.info("报名-用户{}已经报名", userId);
        redisOperateRepository.setRegisterEntity(userId, businessType, module, entity);

        return entity.registered();
    }

    @Override
    public boolean checkAlbum(String userId) {
        final Integer businessType = BusinessSourceEnum.ALBUM.getCode();
        final Integer module = AIModuleEnum.AI_FILE_LIBRARY.getModule();

        // 先查缓存
        final AlgorithmAiRegisterEntity redisEntity = redisOperateRepository.getRegisterEntity(userId, businessType, module);
        if (redisEntity != null) {
            log.info("报名-用户{}在redis缓存有值", userId);
            return redisEntity.registered();
        }

        // 查询报名记录
        final AlgorithmAiRegisterEntity entity = algorithmAiRegisterRepository.queryByUserId(userId, businessType);
        if (entity == null) {
            AlgorithmAiRegisterEntity newEntity = new AlgorithmAiRegisterEntity().notRegistered();
            log.info("智能相册报名-用户{}没有报名数据-初始化newEntity结果为:{}", userId, JsonUtil.toJson(newEntity));
            redisOperateRepository.setRegisterEntity(userId, businessType, module, newEntity);
            return false;
        }

        log.info("报名-用户{}已经报名", userId);
        redisOperateRepository.setRegisterEntity(userId, businessType, module, entity);

        return entity.registered();
    }

    @Override
    public boolean checkDocSearch(String userId) {
        final Integer businessType = BusinessSourceEnum.DOC_SEARCH.getCode();
        final Integer module = AIModuleEnum.AI_FILE_LIBRARY.getModule();

        // 先查缓存
        final AlgorithmAiRegisterEntity redisEntity = redisOperateRepository.getRegisterEntity(userId, businessType, module);
        if (redisEntity != null) {
            log.info("报名-用户{}在redis缓存有值", userId);
            return redisEntity.registered();
        }

        // 查询报名记录
        final AlgorithmAiRegisterEntity entity = algorithmAiRegisterRepository.queryByUserId(userId, businessType);
        if (entity == null) {
            AlgorithmAiRegisterEntity newEntity = new AlgorithmAiRegisterEntity().notRegistered();
            log.info("智能相册报名-用户{}没有报名数据-初始化newEntity结果为:{}", userId, JsonUtil.toJson(newEntity));
            redisOperateRepository.setRegisterEntity(userId, businessType, module, newEntity);
            return false;
        }

        log.info("报名-用户{}已经报名", userId);
        redisOperateRepository.setRegisterEntity(userId, businessType, module, entity);

        return entity.registered();
    }

    @Override
    public void cancel(AiRegisterReqDTO dto) {
        log.info("###cancel 开始进入取消AI授权 dto:{}", JSONUtil.toJsonStr(dto));
        // 判断是否已经报名授权
        final AlgorithmAiRegisterEntity entity = algorithmAiRegisterRepository.queryByUserId(dto.getUserId(), dto.getSourceBusiness());
        if (ObjectUtil.isEmpty(entity)) {
            log.info("###cancel 当前用户还没报名 不能取消授权 userId:{},sourceBusiness:{}", dto.getUserId(), dto.getSourceBusiness());
            return;
        }

        if (entity.checkDisabled()) {
            log.info("###cancel 当前用户已经是禁用状态 不能取消授权 userId:{},sourceBusiness:{}", dto.getUserId(), dto.getSourceBusiness());
            return;
        }

        //更新状态
        algorithmAiRegisterRepository.updateById(entity.disabled());

        //删除redis
        redisOperateRepository.delRegisterEntity(dto.getUserId(), dto.getSourceBusiness(), dto.getModule());

        // 文档检索的需要发送授权mq给下一步
        if (BusinessSourceEnum.isDocSearch(dto.getSourceBusiness())) {
            //如果sourceBusiness为3（文档智能搜索）需要发送mq(取消授权)
            sendDocIntelligentSearchMq(dto.getUserId(), CommonConstant.AI_AUTHORIZE_CLOSE);
        }

    }

	@Override
	public void autoRegisterAiAssistant(String userId) {
		if (!checkAiAssistant(userId)) {
			log.info("autoRegisterAiAssistant 用户{}开始报名", userId);
			AiRegisterReqDTO dto = new AiRegisterReqDTO();
			dto.setUserId(userId);
			dto.setSourceBusiness(BusinessSourceEnum.ASSISTANT.getCode());
			accredit(dto);
		}
	}


}
