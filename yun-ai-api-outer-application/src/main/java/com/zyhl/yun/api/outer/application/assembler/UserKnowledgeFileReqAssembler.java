package com.zyhl.yun.api.outer.application.assembler;

import com.zyhl.yun.api.outer.application.dto.knowledge.PersonalKnowledgeResourceListReqDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeResourceListReqEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 类转换 UserKnowledgeResourceListReqEntity <--> PersonalKnowledgeResourceListReqDTO
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface UserKnowledgeFileReqAssembler {

    UserKnowledgeFileReqAssembler INSTANCE = Mappers.getMapper(UserKnowledgeFileReqAssembler.class);

    UserKnowledgeResourceListReqEntity toUserKnowledgeResourceListReqEntity(PersonalKnowledgeResourceListReqDTO dto);

}
