package com.zyhl.yun.api.outer.application.service;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.dto.AiRegisterReqDTO;
import com.zyhl.yun.api.outer.application.dto.PopUpProtocolQueryWebDTO;
import com.zyhl.yun.api.outer.domain.vo.AiRegisterVO;
import com.zyhl.yun.api.outer.vo.PopUpProtocolQueryVO;

/**
 * 云邮AI助手1.1版本重构报名
 * 技术方案：https://www.kdocs.cn/l/cfYFsYWQYJK9
 *
 * <AUTHOR>
 */
public interface AlgorithmAiRegisterService {

    /**
     * 查询报名（授权）状态
     *
     * @param dto 入参
     * @return 返回结果
     */
    AiRegisterVO get(AiRegisterReqDTO dto);

    /**
     * 报名
     *
     * @param dto 入参
     * @return 返回结果
     */
    AiRegisterVO accredit(AiRegisterReqDTO dto);


    /**
     * 报名设置视频分析状态
     * @param dto
     * @return
     */
    BaseResult<AiRegisterVO> setVideoStatus(AiRegisterReqDTO dto);

    /**
     * 查询弹窗协议
     *
     * @param dto 入参
     * @return 返回结果
     */
    PopUpProtocolQueryVO protocolGet(PopUpProtocolQueryWebDTO dto);

    /**
     * 校验用户是否已经报名AI助手
     *
     * @param userId 用户id
     * @return true-已经报名
     */
    boolean checkAiAssistant(String userId);

    /**
     * 校验用户是否已经报名智能相册
     *
     * @param userId 用户id
     * @return true-已经报名
     */
    boolean checkAlbum(String userId);

    /**
     * 校验用户是否已经报名文档检索
     *
     * @param userId 用户id
     * @return true-已经报名
     */
    boolean checkDocSearch(String userId);

    /**
     * 取消授权
     *
     * @param dto 入参
     */
    void cancel(AiRegisterReqDTO dto);
    
    /**
     * 检查未报名，自动报名AI助手
     * @param userId 用户id
     */
	void autoRegisterAiAssistant(String userId);
}
