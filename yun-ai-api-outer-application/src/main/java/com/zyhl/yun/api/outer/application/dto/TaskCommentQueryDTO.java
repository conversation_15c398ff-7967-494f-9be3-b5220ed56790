package com.zyhl.yun.api.outer.application.dto;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 任务评论查询DTO
 * @date 2025/4/22 14:40
 */
@Slf4j
@Data
public class TaskCommentQueryDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -6906459305451391215L;

    /**
     * 任务来源渠道
     */
    @NotBlank(message = "任务来源渠道不能为空")
    private String sourceChannel;

    /**
     * 任务ID
     */
    @NotBlank(message = "任务ID不能为空")
    private String taskId;
}