package com.zyhl.yun.api.outer.application.handle.chat.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddContentDTO;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.handle.chat.AbstractChatAddHandler;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domainservice.InterventionDomainService;
import com.zyhl.yun.api.outer.domainservice.RecommendQueryService;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.InterventionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.concurrent.Future;

/**
 * 干预库匹配
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class InterventionHandlerImpl extends AbstractChatAddHandler {

    @Resource
    private InterventionDomainService interventionDomainService;
    @Resource
    private RecommendQueryService recommendQueryService;

    /**
     * 输入对话内容最大长度
     */
    private static final int DIALOGUE_MAX_LEN = 512;

    @Override
    public int order() {
        return ExecuteSort.INTERVENTION.getSort();
    }

    @Override
    public boolean execute(ChatAddInnerDTO innerDTO) {
        /**
         * 不走干预库：
         * 非流式接口 || 非对话类型 || 非文本资源 || 对话内容为空 || 对话内容超长 || 有对话指令 commands || 有prompt || 走云盘AI全网搜
         */
        if (innerDTO.getSseEmitter() == null
                || ApplicationTypeEnum.isNotChat(innerDTO.getReqParams().getApplicationType())
                || ResourceTypeEnum.isNotText(innerDTO.getContent().getResourceType())
                || CharSequenceUtil.isEmpty(innerDTO.getContent().getDialogue())
                || innerDTO.getContent().getDialogue().length() > DIALOGUE_MAX_LEN
                || CharSequenceUtil.isNotEmpty(innerDTO.getContent().getCommands())
                || CharSequenceUtil.isNotEmpty(innerDTO.getContent().getPrompt())
                || Boolean.TRUE.equals(innerDTO.getReqParams().getEnableAllNetworkSearch())
        ) {
            return false;
        }

        // 意图为空 || 文本意图
        return CharSequenceUtil.isEmpty(innerDTO.getIntentionCode())
                || DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode().equals(innerDTO.getIntentionCode());
    }

    @Override
    public boolean run(ChatAddInnerDTO innerDTO) {
        log.info("进入干预库处理");
        AlgorithmChatAddContentDTO contentDTO = innerDTO.getReqParams().getContent();

        // 干预库匹配
        InterventionVO interventionVO = interventionDomainService.getIntervention(contentDTO.getSourceChannel(), RequestContextHolder.getClientType(), RequestContextHolder.getClientVersion(), RequestContextHolder.getH5Version(), contentDTO.getDialogue());

        if (interventionVO == null) {
            log.debug("干预库没有命中答案，继续执行下一个节点");
            return true;
        }

        // 干预库有答案
        innerDTO.setIntentionCode(DialogueIntentionEnum.TEXT_GENERATE_TEXT.getCode());
        innerDTO.setInterventionVO(interventionVO);

        // 使用用户的提问通过大模型获取推荐提问语句
        Future recommendQueryFuture = recommendQueryService.getRecommendQueryFuture(innerDTO.getDialogueId(), contentDTO.getDialogue());

        // 保存hbase
        saveTextResult(innerDTO, interventionVO.getAnswer(), "");

        // 保存数据库
        addSuccessAndModelCode(innerDTO, TextModelEnum.INTERVENTION.getCode(), OutContentTypeEnum.RICH_TEXT);

        // 获取推荐提问语句
        DialogueRecommendVO dialogueRecommendVO = new DialogueRecommendVO();
        dialogueRecommendService.setFuturesResult(innerDTO.getDialogueId(), dialogueRecommendVO, Collections.singletonList(recommendQueryFuture));

        // 流式响应
        innerDTO.sseSendAndComplete(interventionVO.getAnswer(), innerDTO.getRespParams().getResultType(), dialogueRecommendVO);

        return false;

    }

}
