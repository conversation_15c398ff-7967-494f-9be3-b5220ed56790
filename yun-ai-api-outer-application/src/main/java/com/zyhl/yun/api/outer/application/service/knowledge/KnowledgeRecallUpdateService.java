package com.zyhl.yun.api.outer.application.service.knowledge;

import com.zyhl.hcy.yun.ai.common.rag.vo.RecallResultVO;
import com.zyhl.hcy.yun.ai.common.rag.vo.RerankResultVO;
import com.zyhl.yun.api.outer.domain.dto.KnowledgeEsQueryResultDTO;
import com.zyhl.yun.api.outer.vo.RagReRankDataVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 知识库召回更新接口
 * <AUTHOR>
 */
public interface KnowledgeRecallUpdateService {

    /**
     * 知识库召回更新
     *
     * @param esResult       知识库搜索结果
     * @param rerankResult   重排结果
     */
    void recallUpdate(List<RecallResultVO> esResult, List<RerankResultVO> rerankResult);
}
