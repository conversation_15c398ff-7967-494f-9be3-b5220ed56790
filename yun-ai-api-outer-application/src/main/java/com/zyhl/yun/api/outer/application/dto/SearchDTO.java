package com.zyhl.yun.api.outer.application.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 搜索参数
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchDTO {

    /**
     * 搜索条件
     */
    @Valid
    @NotNull(message = "搜索条件不能为空")
    private ConditionDTO conditions;

    /**
     * 分页信息
     */
    private SearchPageDTO pageInfo;


    @Data
    @NoArgsConstructor
    public static class ConditionDTO {

        /**
         * 关键字
         */
        @NotEmpty(message = "关键字不能为空")
        private String keyword;

        /**
         * 算法类型 1-科大讯飞 2-阿里 3-华为
         */
        @NotEmpty(message = "算法类型不能为空")
        private String algorithmType;

    }


    @Data
    @NoArgsConstructor
    public static class SearchPageDTO {

        /**
         * 起始游标，为空时从第一页开始查询
         */
        private String pageCursor;

        /**
         * 页显示数
         */
        private String pageSize;

    }

}
