package com.zyhl.yun.api.outer.application.service.external;

import com.zyhl.hcy.yun.ai.common.platform.third.client.membercenter.resp.ConsumeAvailableBenefitRsp;
import com.zyhl.yun.api.outer.application.dto.AlgorithmChatAddDTO;

/**
 * <AUTHOR>
 */
public interface MemberCenterService {
    /**
     * 会员权益消费
     *
     * @param userId    用户id手机号二选一
     * @param phone     用户id手机号二选一
     * @param benefitNo  权益编码
     * @param refId     关联id，任务id/对话id
     * @return 消费码
     */
    String consumeBenefit(String userId, String phone, String benefitNo, String refId);

    /**
     * 会员权益消费
     *
     * @param dto        对话接口请求入参
     * @param phone      手机号，可选，存在则优先使用
     * @param dialogueId 对话id
     * @return 消费结果
     */
    boolean consumeBenefit(AlgorithmChatAddDTO dto, String phone, Long dialogueId);

    /**
     * 会员权益消费结果，消费失败（回滚权益）
     *
     * @param userId     用户id，必选
     * @param phone      手机号，可选，存在则优先使用
     * @param dialogueId 对话id
     */
    void consumeBenefitFail(String userId, String phone, Long dialogueId);

    /**
     * 查询会员权益，用户id手机号二选一
     *
     * @param userId      the user id
     * @param phoneNumber the phone number
     * @param benefitNo   the benefit no
     * @return {@link ConsumeAvailableBenefitRsp.AvailableBenefitRsp}
     * <AUTHOR>
     * @date 2024-8-16 17:56
     */
    ConsumeAvailableBenefitRsp.AvailableBenefitRsp queryAvailableBenefit(String userId, String phoneNumber, String benefitNo);

}
