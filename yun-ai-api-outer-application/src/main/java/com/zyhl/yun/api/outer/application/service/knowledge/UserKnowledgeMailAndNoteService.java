package com.zyhl.yun.api.outer.application.service.knowledge;

import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileAddReqDTO;
import com.zyhl.yun.api.outer.application.vo.knowledge.KnowledgeTaskResultVO;

/**
 * className: UserKnowledgeMailAndNoteService
 * description: 个人知识库 - 邮件/笔记 处理业务接口类
 *
 * <AUTHOR>
 * @date 2025/2/10
 */
public interface UserKnowledgeMailAndNoteService {

    /**
     * 用户添加来源为：邮件/笔记 的知识库文件，发起处理任务
     *
     * @param dto 入参
     * @return 任务id
     */
    Long add(KnowledgeFileAddReqDTO dto);

    /**
     * 用户添加来源为：邮件/笔记 的知识库文件，发起处理任务
     *
     * @param dto 入参
     * @return 任务id
     */
    KnowledgeTaskResultVO add180(KnowledgeFileAddReqDTO dto);
}
