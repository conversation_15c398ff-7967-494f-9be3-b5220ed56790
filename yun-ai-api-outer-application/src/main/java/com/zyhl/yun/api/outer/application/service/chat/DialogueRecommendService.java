package com.zyhl.yun.api.outer.application.service.chat;

import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.PromptRecommendVO;

import java.util.List;
import java.util.concurrent.Future;

/**
 * 对话结果推荐接口
 *
 * <AUTHOR>
 */
public interface DialogueRecommendService {

    /**
     * 获取对话结果推荐对象
     *
     * @param params 接口内部传输参数
     * @return 对话结果推荐对象
     */
    DialogueRecommendVO getDialogueRecommendVO(ChatAddInnerDTO params);

    /**
     * 设置意图推荐并发结果集
     *
     * @param dialogueId          对话id
     * @param dialogueRecommendVO 对话推荐
     * @param futures             异步线程列表
     */
    void setFuturesResult(Long dialogueId, DialogueRecommendVO dialogueRecommendVO, List<Future<Object>> futures);

    /**
     * 获取提示词指令推荐列表
     *
     * @param params 参数
     * @return 意图推荐
     */
    List<PromptRecommendVO> getPromptVOList(ChatAddInnerDTO params);
}
