package com.zyhl.yun.api.outer.application.assembler;

import com.zyhl.yun.api.outer.application.vo.knowledge.PersonalKnowledgeImportTaskVO;
import com.zyhl.yun.api.outer.domain.entity.PersonalKnowledgeImportTaskEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 个人知识库导入任务转换器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface PersonalKnowledgeImportTaskAssembler {

    /**
     * 将实体转换为VO
     * @param entity 实体
     * @return VO
     */
    @Mapping(target = "progress", ignore = true)
    PersonalKnowledgeImportTaskVO toVO(PersonalKnowledgeImportTaskEntity entity);
} 