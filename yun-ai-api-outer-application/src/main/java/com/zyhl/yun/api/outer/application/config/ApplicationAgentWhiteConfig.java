package com.zyhl.yun.api.outer.application.config;

import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import cn.hutool.core.collection.CollUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 应用智能体配置-白名单配置
 *
 * <AUTHOR>
 * @date 2025-06-05 16:08
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties("application-agent.white-config")
public class ApplicationAgentWhiteConfig {

	/**
	 * 应用id列表维度配置
	 */
	private List<ApplicationIds> applicationIdList;

	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	public static class ApplicationIds {
		/**
		 * 符合的应用id列表，空则忽略该配置
		 */
		private List<String> ids;

		/**
		 * 符合的渠道列表，空则不限制渠道
		 */
		private List<String> supportChannels;

		/**
		 * 符合的用户列表，空则不限制用户
		 */
		private List<String> supportPhoneNumbers;
	}

	/**
	 * 判断是否为允许的id权限
	 * 
	 * @param sourceChannel 渠道
	 * @param phoneNumber   手机号
	 * @param applicationId 应用id
	 * @return
	 */
	public boolean allowIdPermission(String sourceChannel, String phoneNumber, String applicationId) {
		List<ApplicationIds> thisApplicationIdList = this.getApplicationIdList();
		if (CollUtil.isEmpty(thisApplicationIdList)) {
			return true;
		}
		for (ApplicationIds thisApplicationId : thisApplicationIdList) {
			if (CollUtil.isEmpty(thisApplicationId.getIds())) {
				// ids未配置，不执行
				continue;
			}
			if (thisApplicationId.getIds().contains(applicationId)) {
				// 符合的id，需要执行判断渠道或者手机号
				if (CollUtil.isNotEmpty(thisApplicationId.getSupportChannels())
						&& !thisApplicationId.getSupportChannels().contains(sourceChannel)) {
					// 渠道不匹配
					return false;
				}
				if (CollUtil.isNotEmpty(thisApplicationId.getSupportPhoneNumbers())
						&& !thisApplicationId.getSupportPhoneNumbers().contains(phoneNumber)) {
					// 手机号不匹配
					return false;
				}
			}
		}
		return true;
	}
}
