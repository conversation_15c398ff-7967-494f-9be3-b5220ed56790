package com.zyhl.yun.api.outer.application.chatv2.service;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.domain.dto.redis.HistoryDialogInfoDTO;

import java.util.List;

/**
 * 历史对话信息redis处理接口类
 *
 * <AUTHOR>
 * @date 2025-04-12
 */
public interface HistoryDialogueSaveRedisService {

    /**
     * 保存redis历史对话信息
     *
     * @param handleDTO 对话接口内部数据传输对象
     */
    void setRedisHistoryDialogInfo(ChatAddHandleDTO handleDTO);

	/**
	 * 获取redis历史对话信息列表
	 *
	 * @param sessionId 会话id
	 * @param maxLength 返回的列表长度
	 * @param userId    用户id
	 * @return 保存到redis的历史对话信息DTO列表
	 */
    List<HistoryDialogInfoDTO> getRedisHistoryDialogInfoList(String sessionId, Integer maxLength, String userId);

}
