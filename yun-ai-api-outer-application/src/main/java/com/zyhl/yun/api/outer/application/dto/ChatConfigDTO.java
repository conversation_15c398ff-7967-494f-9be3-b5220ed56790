package com.zyhl.yun.api.outer.application.dto;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.enums.AbstractResultCode;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.enums.BaseResultCodeEnum;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.constants.RegConst;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2024/1/6 16:37
 */
@Data
@Slf4j
public class ChatConfigDTO extends BaseDTO implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    /**
     * 默认云邮的渠道
     */
    private static final String DEFAULT_SOURCE_CHANNEL = "10102";

    /**
     * 业务模型，详见，枚举类的是定义好的数据，
     * 具体需要启用哪个模型是通过配置文件配置
     *
     * @see com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum
     * 枚举值中的code编码值
     */
    private String modelType;

    /**
     * 渠道来源
     *
     * @see com.zyhl.yun.api.outer.config.SourceChannelsProperties
     */
    private String sourceChannel;

    /**
     * 助手类型【非接口参数】
     */
    private AssistantEnum assistantEnum;

    /**
     * 助手业务类型【非接口参数】
     */
    private String businessType;

    /**
     * 大模型联网搜索状态
     */
    private Boolean networkSearchStatus;

    /**
     * 个人知识库选择列表
     */
    private List<String> baseIdList;

    /**
     * 参数校验
     *
     * @param channelsProperties 渠道配置
     * @return 错误码枚举
     */
    public AbstractResultCode check(SourceChannelsProperties channelsProperties) {
        // 保证输入的modelType是合法的，是com.zyhl.yun.api.outer.enums.ChatConfigTypeEnum枚举类中的code编码值
        if (null == this.networkSearchStatus && ObjectUtil.isNull(TextModelEnum.getByCode(this.modelType))) {
            log.error("业务模型-参数无效：{}", this.modelType);
            return ResultCodeEnum.ERROR_PARAMS;
        }

        // 新增的接口入参，需要默认值
        if (CharSequenceUtil.isBlank(this.sourceChannel)) {
            // 默认云邮的渠道
            this.sourceChannel = DEFAULT_SOURCE_CHANNEL;
        }

        if (!channelsProperties.isExist(this.sourceChannel)) {
            log.info("渠道来源不存在：{}", this.sourceChannel);
            return BaseResultCodeEnum.ERROR_PARAMS;
        }

        if (ObjectUtil.isNotEmpty(baseIdList)) {
            for (String id : baseIdList) {
                if (!id.matches(RegConst.REG_ID_STR)) {
                    log.info("知识库id格式错误：{}", id);
                    return BaseResultCodeEnum.ERROR_PARAMS;
                }
            }
        }

        // 根据渠道，获取助手类型（默认云邮）
        this.assistantEnum = channelsProperties.getAssistantEnumDefaultMail(this.sourceChannel);
        // 根据渠道，获取助手业务类型
        this.businessType = channelsProperties.getType(this.sourceChannel);

        return super.checkUserId();
    }

}
