package com.zyhl.yun.api.outer.application.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * AIPPT内容格式转换请求DTO
 *
 * <AUTHOR> Assistant
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AiPptContentSwitchReqDTO extends BaseDTO {

    /**
     * 渠道来源，参考ChannelId 枚举值
     */
    @NotEmpty(message = "sourceChannel不能为空")
    private String sourceChannel;

    /**
     * 用户Id，默认从token获取，第三方平台调用时必填
     */
    private String userId;

    /**
     * 需要转换的PPT大纲内容
     */
    @NotEmpty(message = "content不能为空")
    private String content;
}
