package com.zyhl.yun.api.outer.application.service.knowledge;

import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeInviteExitReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeInviteListReqDTO;
import com.zyhl.yun.api.outer.application.vo.knowledge.PersonalKnowledgeShareMemberVO;
import com.zyhl.yun.api.outer.domain.vo.common.PageInfoVO;

/**
 * User Knowledge Invite Service
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-04-17 00:44:52
 */
public interface UserKnowledgeInviteService {
    /**
     * 查询个人知识库分享成员列表接口
     *
     * @param dto
     * @return
     */
    PageInfoVO<PersonalKnowledgeShareMemberVO> list(KnowledgeInviteListReqDTO dto);

    /**
     * 个人知识库分享退出接口
     *
     * @param dto
     */
    void exit(KnowledgeInviteExitReqDTO dto);
}
