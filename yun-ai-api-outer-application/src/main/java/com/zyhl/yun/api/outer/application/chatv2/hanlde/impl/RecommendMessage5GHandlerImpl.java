package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.config.LeadCopyV2Properties;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 5G消息引导语推荐
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RecommendMessage5GHandlerImpl extends AbstractChatAddV2Handler {

	/**
	 * 当前执行顺序
	 */
	private ExecuteSort thisExecuteSort = ExecuteSort.RECOMMEND_MESSAGE_5G_LEAD_COPY;

	@Resource
	private LeadCopyV2Properties leadCopyV2Properties;
	@Resource
	private DataSaveService dataSaveService;

	@Override
	public void afterPropertiesSet() throws Exception {
		// 支持的业务初始化
		List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
		thisBusinessTypes.add(ChatBusinessTypeEnum.MESSAGE_5G);
		this.setBusinessTypes(thisBusinessTypes);
	}

	@Override
	public int order() {
		return thisExecuteSort.getSort();
	}

	@Override
	public boolean execute(ChatAddHandleDTO handleDTO) {
		return AssistantEnum.isMessage5g(handleDTO.getAssistantEnum());
	}

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		log.info("进入{}", thisExecuteSort.getDesc());

		// 获取主意图
		IntentionInfo mainIntention = DialogueIntentionVO.getMainIntention(handleDTO.getIntentionVO());
		log.info("5G消息暂时无推荐：设置为文本意图，继续执行， mainIntention:{}", JSONUtil.toJsonStr(mainIntention));

		// 暂时无推荐：设置为文本意图，继续执行
		handleDTO.setTextGenerateTextIntention();

		return true;
	}
}
