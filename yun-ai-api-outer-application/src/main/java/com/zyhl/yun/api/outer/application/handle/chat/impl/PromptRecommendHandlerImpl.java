package com.zyhl.yun.api.outer.application.handle.chat.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.handle.chat.AbstractChatAddHandler;
import com.zyhl.yun.api.outer.application.service.chat.DialogueRecommendService;
import com.zyhl.yun.api.outer.domain.vo.DialogueRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.PromptRecommendVO;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddResultTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 推荐提问列表（意图指令和输入的信息不匹配时执行，返回推荐列表给用户选择）
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PromptRecommendHandlerImpl extends AbstractChatAddHandler {

    @Resource
    private DialogueRecommendService recommendService;

    @Override
    public int order() {
        return ExecuteSort.PROMPT_RECOMMEND.getSort();
    }

    @Override
    public boolean execute(ChatAddInnerDTO innerDTO) {
        // 流式接口 并且 对话类型 并且 小天渠道 并且 非强制大模型对话
        return innerDTO.getSseEmitter() != null
                && ApplicationTypeEnum.isChat(innerDTO.getReqParams().getApplicationType())
                && sourceChannelsProperties.isXiaoTian(innerDTO.getContent().getSourceChannel())
                && !Boolean.TRUE.equals(innerDTO.getReqParams().getEnableForceLlm());
    }

    @Override
    public boolean run(ChatAddInnerDTO innerDTO) {
        log.info("进入提示词推荐处理");
        // 获取推荐提问语句
        List<PromptRecommendVO> promptRecommendList = recommendService.getPromptVOList(innerDTO);
        if (ObjectUtil.isEmpty(promptRecommendList)) {
            return true;
        }

        // 推荐信息
        innerDTO.getRespParams().setRecommend(DialogueRecommendVO.builder().promptList(promptRecommendList).build());

        // 保存hbase
        saveTextResult(innerDTO, "", "");

        // 保存tidb
        addSuccess(innerDTO, OutContentTypeEnum.TEXT);

        // 返回同步结果
        innerDTO.getRespParams().setResultType(ChatAddResultTypeEnum.SYNCHRONIZATION.getType());
        return false;
    }

}
