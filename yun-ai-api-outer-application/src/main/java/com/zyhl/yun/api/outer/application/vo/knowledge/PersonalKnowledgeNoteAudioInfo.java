package com.zyhl.yun.api.outer.application.vo.knowledge;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/18 11:50
 */
@Data
public class PersonalKnowledgeNoteAudioInfo implements Serializable {

    /**
     * 语音笔记创建时间，时间戳（开始转写时间）
     */
    private Long audioCTime;
    /**
     * 语音时长，单位为ms
     */
    private Long audioDuration;
    /**
     * 录音名称
     */
    private String audioName;
    /**
     * 语音大小，单位b
     */
    private Long audioSize;
    /**
     * 语音转换状态，0 待转换 1 转换中 2 转换完成 -1 转换失败 3 转换结果接收中（3只针对AI笔记）
     */
    private Integer audioStatus;
    /**"
     * 语音内容更新时间，时间戳（完成转写时间）
     */
    private Long audioUpTime;

    /**
     * 语音采样率：8k,16k
     */
    private String audioRate;
    /**
     * 语音编码，pcm/wav/mp3
     */
    private String audioCode;


    private String audioCreateTime;


    private String contentUpdateTime;

//    private String audioContent;

    public Long getAudioUpTime() {
        if(StringUtils.isNotBlank(contentUpdateTime)){
            return Long.parseLong(contentUpdateTime);
        }
        return audioUpTime;
    }

    public Long getAudioCTime() {
        if(StringUtils.isNotBlank(audioCreateTime)){
            return Long.parseLong(audioCreateTime);
        }
        return audioCTime;
    }
}
