package com.zyhl.yun.api.outer.application.chatv2.hanlde.event.completeimpl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.assembler.ChatFlowResultAssembler;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.event.AbstractCompleteCallbackEvent;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.pojo.CompleteEvent;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO.OpenApiLingxiCardLink;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO.OpenApiLingxiCardReplyMessage;
import com.zyhl.yun.api.outer.application.config.ApplicationAgentLingxiConfig;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueIntentionOutput;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.AiTextResultVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddFlowStatusEnum;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 下一个执行会议发邮件处理器事件
 * 
 * <AUTHOR>
 * @date 2025-06-04
 */
@Slf4j
@Component
public class MeetingMailChatCallbackEvent extends AbstractCompleteCallbackEvent {

	@Resource
	private DataSaveService dataSaveService;
	@Resource
	private ChatFlowResultAssembler chatFlowResultAssembler;
	@Resource
	private ApplicationAgentLingxiConfig applicationAgentLingxiConfig;
	@Resource
	private ChatTextToolBusinessConfig chatTextToolBusinessConfig;

	@Override
	public void complete(CompleteEvent data) {
		log.info("下一个执行会议发邮件处理器事件");
		try {
			SseEventListener event = data.getEventListener();
			ChatAddHandleDTO handleDTO = data.getHandleDTO();
			ChatAddRespVO respVO = handleDTO.getRespVO();
			List<DialogueFlowResult> outputList = new ArrayList<>();
			if (CollUtil.isNotEmpty(event.getBeforeOutputList())) {
				// 追加输出之前的文案
				outputList.addAll(event.getBeforeOutputList());
			}
			int lastIndex = outputList.size();
			respVO.getFlowResult().setIndex(lastIndex++);
			if (StringUtils.isNotEmpty(event.getAppendBeforeOutContent())) {
				// 不为空，是追加outContent
				respVO.getFlowResult().setOutContent(event.getAppendBeforeOutContent() + data.getOutContent());
			} else {
				respVO.getFlowResult().setOutContent(data.getOutContent());
			}
			respVO.getFlowResult().setTitle(event.getCallBackTitle());
			respVO.getFlowResult().setReasoningContent(data.getReasoningContent());
			outputList.add(chatFlowResultAssembler.getFlowResult(respVO.getFlowResult()));

			// 会议通知邮件信息
			MailInfoVO mailInfo = null;
			if (CollUtil.isNotEmpty(event.getAfterOutputList())) {
				// 追加输出之后的文案
				for (DialogueFlowResult output : event.getAfterOutputList()) {
					output.setIndex(lastIndex++);
					outputList.add(output);
					if (null == mailInfo && null != output.getMailInfo()) {
						mailInfo = output.getMailInfo();
					}
				}
			}

			// 设置hbase
			AiTextResultRespParameters result = AiTextResultRespParameters.builder()
					.version(AiTextResultVersionEnum.V2.getVersion())
					.outputCommand(new DialogueIntentionOutput(respVO.getOutputCommand().getCommand(),
							respVO.getOutputCommand().getSubCommand(), respVO.getOutputCommand().getArgumentMap()))
					.outputList(outputList).build();

			// 保存hbase
			dataSaveService.saveHbaseAllChatResult(handleDTO, result);

			// 保存tidb
			dataSaveService.addSuccessAndModelCode(handleDTO, event.getModelCode(), OutContentTypeEnum.TEXT);

			// 存在后置响应
			if (CollUtil.isNotEmpty(event.getAfterOutputList())) {
				int sendLastIndex = event.getSendIndex();
				for (int i = 0; i < event.getAfterOutputList().size(); i++) {
					DialogueFlowResult output = event.getAfterOutputList().get(i);
					respVO.setFlowResult(chatFlowResultAssembler.getFlowResultVO(output));
					respVO.getFlowResult().setIndex(sendLastIndex++);
					if (i == (event.getAfterOutputList().size() - 1)) {
						// 判断是否最后一个
						// 流式响应结束
						respVO.setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
						if (null != handleDTO.getLingxiParamInfo()
								&& handleDTO.getLingxiParamInfo().isLingxiRespFlag()) {
							// 结束之前，追加2个消息（追尾【编辑邮件】、追尾【已确认邮件内容，发送邮件】）
							String meetingEditMailTitle = applicationAgentLingxiConfig.getTextConfig()
									.getMeetingEditMailTitle();
							String meetingEditMailReplyText = applicationAgentLingxiConfig.getTextConfig()
									.getMeetingEditMailReplyText();
							String meetingSendMailTitle = applicationAgentLingxiConfig.getTextConfig()
									.getMeetingSendMailTitle();
							String meetingSendMailReplyText = applicationAgentLingxiConfig.getTextConfig()
									.getMeetingSendMailReplyText();
							List<OpenApiLingxiCardLink> bubbles = new ArrayList<>();
							
							// 会议标题
							String meetingTitle = (null != mailInfo
									? chatTextToolBusinessConfig.getIntelligentMeeting()
											.removeTitlePrefix(mailInfo.getTitle())
									: handleDTO.getInputInfoDTO().getDialogue());
							bubbles.add(
									new OpenApiLingxiCardLink(meetingEditMailTitle, new OpenApiLingxiCardLink.ReplyLink(
											String.format(meetingEditMailReplyText, meetingTitle))));
							bubbles.add(
									new OpenApiLingxiCardLink(meetingSendMailTitle, new OpenApiLingxiCardLink.ReplyLink(
											String.format(meetingSendMailReplyText, meetingTitle))));
							OpenApiLingxiCardReplyMessage replyMessage = new OpenApiLingxiCardReplyMessage(
									StringUtils.EMPTY, bubbles);
							handleDTO.getSseEmitterOperate().sendAndComplete(
									OpenApiLingxiChatRespVO.getOpenApiLingxiChatReplyResp(handleDTO, replyMessage));
						} else {
							// 单独send
							handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(respVO));
						}
					} else {
						if (null != handleDTO.getLingxiParamInfo()
								&& handleDTO.getLingxiParamInfo().isLingxiRespFlag()) {
							handleDTO.getSseEmitterOperate()
									.send(OpenApiLingxiChatRespVO.getOpenApiLingxiChatResp(handleDTO, respVO));
						} else {
							// 单独send
							handleDTO.getSseEmitterOperate().send(BaseResult.success(respVO));
						}
					}
				}
			} else {
				handleDTO.getSseEmitterOperate().complete();
			}
		} catch (YunAiBusinessException e) {
			data.getEventListener().dialogueFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			data.getEventListener().dialogueFail(AiResultCode.CODE_9999);
		}
	}

}
