package com.zyhl.yun.api.outer.application.util;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024/6/28 09:47
 */
@Slf4j
public class RegexUtils {

    private static final Pattern EQUAL_PATTERN = Pattern.compile("=([^=]*)$");

    /**
     * 从字符串中提取等号后的值
     *
     * @param input 用户输入
     * @return 返回string
     */
    public static String extractAfterEqualSymbolValue(String input) {
        if (!StringUtils.hasText(input)) {
            return CharSequenceUtil.EMPTY;
        }
        // 使用正则表达式匹配等号后的值
        Matcher matcher = EQUAL_PATTERN.matcher(input);

        // 如果找到匹配项，则返回捕获的组
        if (matcher.find()) {
            return matcher.group(1);
        }

        // 如果没有匹配项，假设整个字符串就是需要的值
        return input;
    }
}
