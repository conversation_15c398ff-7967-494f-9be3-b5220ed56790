package com.zyhl.yun.api.outer.application.chatv2.service;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;

/**
 * 数据保存服务接口
 * 
 * <AUTHOR>
 * @date 2025-04-13
 */
public interface DataSaveService {

	/**
	 * 保存文本结果
	 * 
	 * @param handleDTO       入参
	 * @param answer          答案
	 * @param resourceContent 资源内容
	 * @return 文本实体
	 */
	AiTextResultEntity saveTextResult(ChatAddHandleDTO handleDTO, String answer, String resourceContent);

	/**
	 * 
	 * @param handleDTO       入参
	 * @param taskId          任务id
	 * @param answer          答案
	 * @param resourceContent 资源内容
	 * @return 文本实体
	 */
	AiTextResultEntity saveTextResult(ChatAddHandleDTO handleDTO, String taskId, String answer, String resourceContent);

	/**
	 * 精准更新某一条数据的dialogueFlowResult
	 * 
	 * @param userId             用户id
	 * @param dialogueId         对话id
	 * @param dialogueFlowResult 对话流式结果
	 * 
	 */
	void updateResult(String userId, String dialogueId, DialogueFlowResult dialogueFlowResult);

	/**
	 * 保存全部对话结果
	 * 
	 * @param handleDTO 入参
	 * @param resp      文本响应
	 */
	void saveHbaseAllChatResult(ChatAddHandleDTO handleDTO, AiTextResultRespParameters resp);

	/**
	 * 添加信息
	 * 
	 * @param handleDTO  入参
	 * @param chatStatus 对话状态
	 */
	void add(ChatAddHandleDTO handleDTO, ChatStatusEnum chatStatus);

	/**
	 * 添加信息
	 * 
	 * @param handleDTO  入参
	 * @param chatStatus 对话状态
	 * @param taskId     任务id
	 * @param modelCode  模型编码
	 */
	void add(ChatAddHandleDTO handleDTO, ChatStatusEnum chatStatus, Long taskId, String modelCode);

	/**
	 * 添加成功
	 * 
	 * @param handleDTO      入参
	 * @param outContentType 输出类型
	 */
	void addSuccess(ChatAddHandleDTO handleDTO, OutContentTypeEnum outContentType);

	/**
	 * 添加成功和模型编码
	 * 
	 * @param handleDTO      入参
	 * @param modelCode      模型编码
	 * @param outContentType 输出类型
	 */
	void addSuccessAndModelCode(ChatAddHandleDTO handleDTO, String modelCode, OutContentTypeEnum outContentType);

	/**
	 * 添加失败和模型编码
	 * 
	 * @param handleDTO      入参
	 * @param modelCode      模型编码
	 * @param outContentType 输出类型
	 */
	void addFailAndModelCode(ChatAddHandleDTO handleDTO, String modelCode, OutContentTypeEnum outContentType);

	/**
	 * 保存全部对话结果
	 * 
	 * @param handleDTO 入参
	 */
	void saveTidbAllChatResult(ChatAddHandleDTO handleDTO);

	/**
	 * 获取hbase结果
	 * 
	 * @param userId     用户id
	 * @param dialogueId 对话id
	 * @return
	 */
	AiTextResultEntity getHbaseResult(String userId, String dialogueId);

	/**
	 * 更新对话失败
	 * 
	 * @param dialogueId 对话id
	 */
	void updateChatFail(Long dialogueId);

}
