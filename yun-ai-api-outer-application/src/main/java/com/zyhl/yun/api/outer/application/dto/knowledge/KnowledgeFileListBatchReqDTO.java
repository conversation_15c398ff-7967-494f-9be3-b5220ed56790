package com.zyhl.yun.api.outer.application.dto.knowledge;

import com.zyhl.yun.api.outer.application.dto.BaseChannelDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

/**
 * 个人知识库资源信息批量操作请求参数
 *
 * <AUTHOR>
 */
@Data
@Slf4j
public class KnowledgeFileListBatchReqDTO extends BaseChannelDTO implements Serializable {

    /**
     * 资源ID列表
     */
    private List<String> resourceIdList;

}
