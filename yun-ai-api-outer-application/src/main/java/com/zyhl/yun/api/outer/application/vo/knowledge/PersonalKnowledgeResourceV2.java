package com.zyhl.yun.api.outer.application.vo.knowledge;

import lombok.Data;

/**
 * 知识库资源v2
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
@Data
public class PersonalKnowledgeResourceV2 {
    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 资源类型
     * 0--文件
     * 1--邮件
     * 2--笔记
     * 3--在线链接
     */
    private Integer resourceType;
    /**
     * 文件特有属性 - 类型，枚举值file/folder
     */
    private String type;

    public PersonalKnowledgeResourceV2(String resourceId, Integer resourceType, String type) {
        this.resourceId = resourceId;
        this.resourceType = resourceType;
        this.type = type;
    }
}
