package com.zyhl.yun.api.outer.application.service.mq;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileTaskEntity;


/**
 * interfaceName: KnowledgeFileDeleteTaskMqService
 * description: 个人知识库 - 文件删除接口类
 *
 * <AUTHOR>
 * @date 2025/5/27
 */
public interface KnowledgeFileDeleteTaskMqService {

    /**
     * 发送文件删除消息
     *
     * @param entityList 文件实体列表
     */
    void sendMq(UserKnowledgeFileTaskEntity entityList);

}
