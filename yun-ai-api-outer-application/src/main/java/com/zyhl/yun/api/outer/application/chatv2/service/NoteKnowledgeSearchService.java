package com.zyhl.yun.api.outer.application.chatv2.service;

import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.pojo.KnowledgeFlowInfo;

import java.util.List;

/**
 * 知识库搜索服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface NoteKnowledgeSearchService {

    /**
     * 笔记助手-设置知识库流式信息
     *
     * @param handleDTO 请求参数
     * @return 知识流式信息
     */
    KnowledgeFlowInfo setKnowledgeFlowInfo(ChatAddHandleDTO handleDTO);

    /**
     * 笔记助手-知识搜索，多线程执行
     *
     * @param handleDTO 请求参数
     * @param historyList 历史记录
     */
    void knowledgeSearch(ChatAddHandleDTO handleDTO, List<TextModelMessageDTO> historyList);

}
