package com.zyhl.yun.api.outer.application.service.mq.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.chinamobile.tuxedo.sdk.api.Message;
import com.chinamobile.tuxedo.sdk.api.Producer;
import com.chinamobile.tuxedo.sdk.api.SendResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.rocketmq.message.MessageDTO;
import com.zyhl.hcy.yun.ai.common.base.utils.MessageUtil;
import com.zyhl.yun.api.outer.application.service.mq.KnowledgeTransTaskMqService;
import com.zyhl.yun.api.outer.config.RocketmqProducerProperties;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileTaskEntity;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 知识库转存任务状态查询消息服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class KnowledgeTransTaskMqServiceImpl implements KnowledgeTransTaskMqService {

    @Resource
    private RocketmqProducerProperties rocketmqProducerProperties;

    @Resource(name = "knowledgeDocVectorProducer")
    private Producer knowledgeDocVectorProducer;

    /**
     * 文档向量 事件类型
     */
    private static final String DOC_VECTOR_DISPATCH_EVENT_TYPE = "algorithm.local.knowledge.store.delay";

    /**
     * 消息id前缀
     */
    private static final String MESSAGE_ID_PREFIX = "personalKnowledgeTransTask_";


    @Override
    public void sendMq(UserKnowledgeFileTaskEntity taskEntity, String ext) {
        if (ObjectUtil.isEmpty(taskEntity)) {
            log.error("知识库任务参数为空");
            return;
        }

        // 生成消息
        Message msg = createMessage(taskEntity, ext);

        // 发送消息
        SpringUtil.getBean(KnowledgeTransTaskMqServiceImpl.class).send(msg, taskEntity.getUserId());
    }


    @Override
    public void sendMqV3(UserKnowledgeFileTaskEntity taskEntity, Map<String, Object> message) {
        // 生成消息
        Message msg = createMessageV3(taskEntity, message);
        // 发送消息
        SpringUtil.getBean(KnowledgeTransTaskMqServiceImpl.class).send(msg, taskEntity.getUserId());
    }

    /**
     * 创建消息
     *
     * @return
     */
    private Message createMessage(UserKnowledgeFileTaskEntity taskEntity, String ext) {
        // 参数
        Map<String, Object> params = new HashMap<>(Const.NUM_16);
        params.put("taskId", taskEntity.getId());
        params.put("ext", ext);

        // 消息实体
        MessageDTO<Map<String, Object>> dto = new MessageDTO<>(MESSAGE_ID_PREFIX + UUID.randomUUID(), params);
        dto.setEventType(DOC_VECTOR_DISPATCH_EVENT_TYPE);
        dto.setUserId(taskEntity.getUserId());
        dto.setOwnerId(taskEntity.getOwnerId());
        dto.setOwnerType(String.valueOf(taskEntity.getOwnerType()));

        String jsonStr = JSONUtil.toJsonStr(dto);

        // 创建消息
        Message msg = MessageUtil.createMessage();
        msg.setBody(jsonStr.getBytes());
        msg.setTopic(rocketmqProducerProperties.getPersonalKnowledgeTransTask().getTopic());
        msg.setTag(rocketmqProducerProperties.getPersonalKnowledgeTransTask().getTag());
        msg.setStartDeliverTime(System.currentTimeMillis() + rocketmqProducerProperties.getPersonalKnowledgeTransTask().getDelayTimeMillis());

        log.info("发送知识库转存任务状态查询消息:{}", jsonStr);
        return msg;
    }


    private Message createMessageV3(UserKnowledgeFileTaskEntity taskEntity, Map<String, Object> params) {
        // 消息实体
        MessageDTO<Map<String, Object>> dto = new MessageDTO<>(MESSAGE_ID_PREFIX + UUID.randomUUID(), params);
        dto.setEventType(DOC_VECTOR_DISPATCH_EVENT_TYPE);
        dto.setUserId(taskEntity.getUserId());
        dto.setOwnerId(taskEntity.getOwnerId());
        dto.setOwnerType(String.valueOf(taskEntity.getOwnerType()));

        String jsonStr = JSONUtil.toJsonStr(dto);

        // 创建消息
        Message msg = MessageUtil.createMessage();
        msg.setBody(jsonStr.getBytes());
        msg.setTopic(rocketmqProducerProperties.getPersonalKnowledgeTransTask().getTopic());
        msg.setTag(rocketmqProducerProperties.getPersonalKnowledgeTransTask().getTag());
        msg.setStartDeliverTime(System.currentTimeMillis() + rocketmqProducerProperties.getPersonalKnowledgeTransTask().getDelayTimeMillis());

        log.info("发送知识库转存任务状态查询消息:{}", jsonStr);
        return msg;
    }

    @Retryable
    protected void send(Message msg, String userId) {
        SendResult sendResult;
        try {
            sendResult = knowledgeDocVectorProducer.send(msg);
            log.info("发送知识库转存任务状态查询消息成功，用户id：{}，发送结果：{}", userId, sendResult);
        } catch (Exception e) {
            log.error("发送知识库转存任务状态查询消息失败，用户id：{}", userId, e);
            throw new YunAiBusinessException(ResultCodeEnum.MQ_SEND_EXCEPTION);
        }
    }


}
