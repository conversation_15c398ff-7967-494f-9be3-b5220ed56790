package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.shaded.com.google.common.base.Objects;
import org.springframework.stereotype.Service;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.dto.SendMailCustomizationReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.vo.MailResultVO;
import com.zyhl.yun.api.outer.application.assembler.ChatFlowResultAssembler;
import com.zyhl.yun.api.outer.application.chatv2.dto.AssistantChatV2PollingUpdateDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.event.completeimpl.PptMailChatCallbackEvent;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.service.AlgorithmChatV2ContentService;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatContentService;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.service.IntelligentMeetingSendMailService;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueFlowResultVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueResultV2VO;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig.IntelligentMeeting;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties.SourceChannel;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.AiFunctionResult;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueIntentionOutput;
import com.zyhl.yun.api.outer.domain.valueobject.MailInfo;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.AiTextResultVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddFlowStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.external.MailExternalService;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 智能体对话-智能会议发邮件-服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-11 16:00
 */
@Slf4j
@Service
public class IntelligentMeetingSendMailServiceImpl implements IntelligentMeetingSendMailService {

	@Resource
	private ChatFlowResultAssembler chatFlowResultAssembler;

	@Resource
	private DataSaveService dataSaveService;

	@Resource
	private TextModelExternalService textModelExternalService;

	@Resource
	private AlgorithmChatV2ContentService algorithmChatV2ContentService;

	@Resource
	private ChatContentService chatContentService;

	@Resource
	private MailExternalService mailExternalService;

	@Resource
	private PptMailChatCallbackEvent pptMailChatCallbackEvent;

	@Resource
	private SourceChannelsProperties sourceChannelsProperties;

	@Resource
	private ChatTextToolBusinessConfig chatTextToolBusinessConfig;

	@Resource
	private ModelProperties modelProperties;

	@Override
	public boolean run(ChatAddHandleDTO handleDTO) {
		DialogueIntentionVO.IntentionInfo mainIntention = DialogueIntentionVO
				.getMainIntention(handleDTO.getIntentionVO());
		List<String> dialogueIds = handleDTO.getInputInfoDTO().getAttachment().getDialogueIdList();
		List<MailInfo> mailList = handleDTO.getInputInfoDTO().getAttachment().getMailList();
		if (CollUtil.isEmpty(dialogueIds)) {
			log.warn("对话id列表为空，继续执行业务");
			return true;
		}

		// 获取对话内容信息
		AssistantChatV2PollingUpdateDTO pollingUpdate = new AssistantChatV2PollingUpdateDTO();
		pollingUpdate.setDialogueId(Long.parseLong(dialogueIds.get(0)));
		pollingUpdate.setUserId(handleDTO.getReqDTO().getUserId());
		DialogueResultV2VO dialogResult = algorithmChatV2ContentService.pollingUpdate(pollingUpdate);
		if (null == dialogResult) {
			log.warn("上一次对话信息为空，继续执行业务");
			return true;
		}

		checkHandleInfo(handleDTO, dialogResult);

		// 意图判断
		DialogueIntentionOutput outputCommand = dialogResult.getOutputCommand();
		if (null == outputCommand) {
			log.warn("上一次对话信息意图为空，继续执行业务");
			return true;
		}

		if (DialogueIntentionEnum.isTextToolIntention(outputCommand.getCommand())
				&& DialogueIntentionSubEnum.isMeetingMail(outputCommand.getSubCommand())) {
			// 前置-会议通知发邮件
			beforeSendMail(handleDTO, mainIntention, mailList, dialogResult);
		} else if (DialogueIntentionEnum.isSendMail(outputCommand.getCommand())) {
			// 后置-ppt生成方案后发邮件
			afterPptSendMail(handleDTO, mainIntention, mailList, dialogResult);
		} else if (DialogueIntentionEnum.isTextToolIntention(outputCommand.getCommand())
				&& DialogueIntentionSubEnum.isAiPpt(outputCommand.getSubCommand())) {
			// ppt生成后生成邮件内容
			afterPptGenMailContent(handleDTO, dialogResult);
		} else {
			log.warn("上一次对话信息意图错误，无法处理意图，继续执行业务");
			return true;
		}

		return false;
	}

	/**
	 * 前置会议通知，发邮件流程
	 * 
	 * @param handleDTO     请求参数
	 * @param mainIntention 意图
	 * @param mailList      邮箱请求
	 * @param dialogResult  对话结果
	 */
	private void beforeSendMail(ChatAddHandleDTO handleDTO, DialogueIntentionVO.IntentionInfo mainIntention,
			List<MailInfo> mailList, DialogueResultV2VO dialogResult) {

		if (CollUtil.isEmpty(mailList)) {
			log.error("对话需要sid+rmkey，邮件列表为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		DialogueFlowResult textModelResult = null;
		MailInfoVO mailInfo = null;
		List<DialogueFlowResult> outputList = dialogResult.getOutputList();
		if (CollUtil.isNotEmpty(outputList)) {
			for (DialogueFlowResult output : outputList) {
				if (null == textModelResult
						&& Objects.equal(FlowResultTypeEnum.TEXT_MODEL.getType(), output.getResultType())) {
					textModelResult = output;
				}

				if (null == mailInfo && Objects.equal(FlowResultTypeEnum.MAIL.getType(), output.getResultType())) {
					mailInfo = output.getMailInfo();
				}
			}
		}

		if (null == textModelResult) {
			log.error("对话大模型信息为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}
		if (null == mailInfo) {
			log.error("对话发邮件信息为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		// 聊天添加响应对象
		ChatAddRespVO respVO = handleDTO.getRespVO();
		respVO.setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());
		List<DialogueFlowResult> saveOutputList = new ArrayList<>();
		IntelligentMeeting intelligentMeeting = chatTextToolBusinessConfig.getIntelligentMeeting();

		// 思考过程
		respVO.setFlowResult(new DialogueFlowResultVO(0, FlowResultTypeEnum.REASONING_RESULT,
				intelligentMeeting.getSendMailNoticeTitle(), intelligentMeeting.getSendMailNoticeTip()));
		saveOutputList.add(chatFlowResultAssembler.getFlowResult(respVO.getFlowResult()));
		handleDTO.getSseEmitterOperate().send(BaseResult.success(respVO));

		// 发邮件（会议通知）
		SendMailCustomizationReqDTO reqDTO = new SendMailCustomizationReqDTO();
		reqDTO.setSid(mailList.get(0).getSid());
		reqDTO.setRmkey(mailList.get(0).getRmkey());
		reqDTO.setRecipientList(mailInfo.getRecipientList());
		reqDTO.setTitle(mailInfo.getTitle());
		reqDTO.setContent(intelligentMeeting.splitMailContent(textModelResult.getOutContent()));
		
		// 执行发邮件
		MailResultVO mailResult = null;
		Exception sendMailException = null;
		try {
			mailResult = mailExternalService.sendMailCustomization(reqDTO);
		} catch (Exception e) {
			log.error("sendMailCustomization reqDTO:{} error:", JSONUtil.toJsonStr(reqDTO), e);
			sendMailException = e;
		}

		// 发邮件失败
		if (!(null != mailResult && mailResult.isSuccess())) {
			String sendMailErrorMsg = intelligentMeeting.getSendMailErrorMsg();
			// 默认下游服务异常
			String errorCode = ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION.getResultCode();
			String errorMsg = ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION.getResultMsg();
			if (sendMailException instanceof YunAiBusinessException) {
				String code = ((YunAiBusinessException) sendMailException).getCode();
				if (AiResultCode.isSendMailErrorCode(code)) {
					// 发邮件错误码，独立转换
					AiResultCode aiResultCode = AiResultCode.getByCodeOrMsg(code,
							((YunAiBusinessException) sendMailException).getMessage());
					errorMsg = aiResultCode.getMsg();
					errorCode = aiResultCode.getCode();
				}
			}
			errorMsg = String.format(sendMailErrorMsg, errorMsg);

			DialogueFlowResultVO mailFlowResultVO = new DialogueFlowResultVO(1, FlowResultTypeEnum.MAIL, null, null);
			mailFlowResultVO.setMailInfo(MailInfoVO.builder().sendMailFlag(false).build());
			mailFlowResultVO.setErrorCode(errorCode);
			mailFlowResultVO.setErrorMessage(errorMsg);
			respVO.setFlowResult(mailFlowResultVO);
			// 流式响应结束
			respVO.setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
			handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(respVO));
			return;
		}

		log.info("发送邮件成功 dialogueId:{}", handleDTO.getDialogueId());

		DialogueFlowResultVO mailFlowResultVO = new DialogueFlowResultVO(1, FlowResultTypeEnum.MAIL, null, null);
		mailFlowResultVO.setMailInfo(MailInfoVO.builder().sendMailFlag(true).build());
		respVO.setFlowResult(mailFlowResultVO);
		saveOutputList.add(chatFlowResultAssembler.getFlowResult(mailFlowResultVO));
		// 设置hbase
		AiTextResultRespParameters result = AiTextResultRespParameters.builder()
				.version(AiTextResultVersionEnum.V2.getVersion())
				.outputCommand(new DialogueIntentionOutput(mainIntention)).outputList(saveOutputList).build();

		// 保存hbase
		dataSaveService.saveHbaseAllChatResult(handleDTO, result);

		// 保存tidb
		dataSaveService.addSuccessAndModelCode(handleDTO, null, OutContentTypeEnum.TEXT);

		// 流式响应结束
		respVO.setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
		handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(respVO));

	}

	/**
	 * ppt生成邮件内容，流式对话
	 * 
	 * @param handleDTO    请求参数
	 * @param dialogResult 对话结果
	 */
	private void afterPptGenMailContent(ChatAddHandleDTO handleDTO, DialogueResultV2VO dialogResult) {

		// 聊天添加响应对象
		ChatAddRespVO respVO = handleDTO.getRespVO();
		respVO.setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());

		AiFunctionResult aiFunctionResult = getAiFunctionResult(dialogResult);
		if (!(null != aiFunctionResult && null != aiFunctionResult.getFile())) {
			log.error("对话tool结果文件信息为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		AlgorithmChatContentEntity lastMailDialogueInfo = getLastIntentionDialogue(handleDTO.getReqDTO().getUserId(),
				handleDTO.getSessionId(), dialogResult.getDialogueId());
		if (null == lastMailDialogueInfo) {
			log.error("对话信息为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}
		MailInfoVO mailInfo = getMailInfoResult(lastMailDialogueInfo);
		if (null == mailInfo) {
			log.error("对话发邮件信息为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		IntelligentMeeting intelligentMeeting = chatTextToolBusinessConfig.getIntelligentMeeting();

		// 入库hbase结果列表-大模型之前
		List<DialogueFlowResult> beforeOutputList = new ArrayList<>();
		// 思考过程
		respVO.setFlowResult(new DialogueFlowResultVO(0, FlowResultTypeEnum.REASONING_RESULT,
				intelligentMeeting.getSendMailPptNoticeRecTitle(), intelligentMeeting.getSendMailPptNoticeRecTip()));
		beforeOutputList.add(chatFlowResultAssembler.getFlowResult(respVO.getFlowResult()));
		handleDTO.getSseEmitterOperate().send(BaseResult.success(respVO));

		respVO.setFlowResult(new DialogueFlowResultVO(1, FlowResultTypeEnum.REASONING_RESULT,
				intelligentMeeting.getSendMailPptNoticeMailTitle(), intelligentMeeting.getSendMailPptNoticeMailTip()));
		beforeOutputList.add(chatFlowResultAssembler.getFlowResult(respVO.getFlowResult()));
		handleDTO.getSseEmitterOperate().send(BaseResult.success(respVO));

		/**
		 * 追加文案到大模型输出之前
		 */
		String autoGenPptMailContentTitle = intelligentMeeting.getAutoGenPptMailContentTitle();
		StringBuilder appendBeforeOutContent = new StringBuilder();
		appendBeforeOutContent.append(String.format(intelligentMeeting.getRecipientTitie(),
				StringUtils.join(mailInfo.getRecipientList(), "、")));
		// 去除附件后缀为邮件标题
		String mailTitle = aiFunctionResult.getTitle();
		appendBeforeOutContent.append(String.format(intelligentMeeting.getSubjectTitie(), mailTitle));
		appendBeforeOutContent
				.append(String.format(intelligentMeeting.getAttachmentTitie(), aiFunctionResult.getFile().getName()));
		appendBeforeOutContent.append(intelligentMeeting.getContentTitie());
		DialogueFlowResultVO firstModelFlowResultVO = new DialogueFlowResultVO(2, FlowResultTypeEnum.TEXT_MODEL,
				autoGenPptMailContentTitle, appendBeforeOutContent.toString());
		firstModelFlowResultVO.setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());
		respVO.setFlowResult(firstModelFlowResultVO);
		handleDTO.getSseEmitterOperate().send(BaseResult.success(respVO));

		/**
		 * 流式对话开始
		 */
		DialogueFlowResult callBackDialogueFlowResult = new DialogueFlowResult();
		mailInfo.setTitle(mailTitle);
		callBackDialogueFlowResult.setMailInfo(mailInfo);
		callBackDialogueFlowResult.setAiFunctionResult(aiFunctionResult);
		callBackDialogueFlowResult.setTitle(autoGenPptMailContentTitle);
		SseEventListener event = new SseEventListener(handleDTO, null);
		// 设置输出之前的文案
		event.setBeforeOutputList(beforeOutputList);
		event.setModelCode(intelligentMeeting.getPptMailModelCode());
		event.setAppendBeforeOutContent(appendBeforeOutContent.toString());
		event.setCallBackDialogueFlowResult(callBackDialogueFlowResult);
		event.setCallBackTitle(autoGenPptMailContentTitle);
		// 文本模型
		Integer maxLength = modelProperties.getMaxLength(handleDTO.getAssistantEnum(), handleDTO.getBusinessType(),
				event.getModelCode());
		TextModelTextReqDTO reqDTO = event.getTextDto().toTextReqDTO(maxLength);
		// 设置不联网
		reqDTO.setEnableForceNetworkSearch(false);
		// 组装提示词+query
		String queryContent = chatTextToolBusinessConfig.getIntelligentMeeting().getPptMailPrompt().replace("{query}",
				dialogResult.getDialogueInputInfo().getDialogue());
		TextModelMessageDTO msgDTO = new TextModelMessageDTO(TextModelRoleEnum.USER.getName(), queryContent);
		reqDTO.setMessageDtoList(Collections.singletonList(msgDTO));
		event.setHandleDTO(handleDTO);
		// 设置当前的索引，下一次加1
		event.setSendIndex(beforeOutputList.size());
		// 设置回调处理事件
		event.setCompleteCallbackEvent(pptMailChatCallbackEvent);

		log.info("【特殊场景智能体文本对话036_036001】指定文本模型对话 model:{}, reqDTO:{}", event.getModelCode(),
				JSONUtil.toJsonStr(reqDTO));
		textModelExternalService.streamDialogue(event.getModelCode(), reqDTO, event);

	}

	/**
	 * 后置ppt文案通知，发邮件流程
	 * 
	 * @param handleDTO     请求参数
	 * @param mainIntention 意图
	 * @param mailList      邮箱请求
	 * @param dialogResult  对话结果
	 */
	private void afterPptSendMail(ChatAddHandleDTO handleDTO, IntentionInfo mainIntention, List<MailInfo> mailList,
			DialogueResultV2VO dialogResult) {

		if (CollUtil.isEmpty(mailList)) {
			log.error("对话需要sid+rmkey，邮件列表为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		DialogueFlowResult textModelResult = getTextModelResult(dialogResult);
		if (null == textModelResult) {
			log.error("对话大模型信息为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		if (null == dialogResult.getExtInfoParam() || null == dialogResult.getExtInfoParam().getDialogueFlowResult()) {
			log.error("上一次对话信息扩展参数为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		MailInfoVO mailInfo = dialogResult.getExtInfoParam().getDialogueFlowResult().getMailInfo();
		if (null == mailInfo) {
			log.error("对话发邮件信息为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		AiFunctionResult aiFunctionResult = dialogResult.getExtInfoParam().getDialogueFlowResult()
				.getAiFunctionResult();
		if (!(null != aiFunctionResult && null != aiFunctionResult.getFile())) {
			log.error("对话tool结果文件信息为空");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}

		// 聊天添加响应对象
		ChatAddRespVO respVO = handleDTO.getRespVO();
		respVO.setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());
		List<DialogueFlowResult> saveOutputList = new ArrayList<>();
		IntelligentMeeting intelligentMeeting = chatTextToolBusinessConfig.getIntelligentMeeting();

		// 思考过程
		respVO.setFlowResult(new DialogueFlowResultVO(0, FlowResultTypeEnum.REASONING_RESULT,
				intelligentMeeting.getSendMailNoticeTitle(), intelligentMeeting.getSendMailNoticeTip()));
		saveOutputList.add(chatFlowResultAssembler.getFlowResult(respVO.getFlowResult()));
		handleDTO.getSseEmitterOperate().send(BaseResult.success(respVO));

		// 发邮件（ppt方案）

		SendMailCustomizationReqDTO.McloudFileInfo pptFile = new SendMailCustomizationReqDTO.McloudFileInfo();
		pptFile.setFileId(aiFunctionResult.getFile().getFileId());
		SendMailCustomizationReqDTO reqDTO = new SendMailCustomizationReqDTO();
		reqDTO.setSid(mailList.get(0).getSid());
		reqDTO.setRmkey(mailList.get(0).getRmkey());
		reqDTO.setRecipientList(mailInfo.getRecipientList());
		reqDTO.setMcloudFileList(Collections.singletonList(pptFile));
		reqDTO.setTitle(mailInfo.getTitle());
		reqDTO.setContent(intelligentMeeting.splitMailContent(textModelResult.getOutContent()));

		// 执行发邮件
		MailResultVO mailResult = null;
		Exception sendMailException = null;
		try {
			mailResult = mailExternalService.sendMailCustomization(reqDTO);
		} catch (Exception e) {
			log.error("sendMailCustomization reqDTO:{} error:", JSONUtil.toJsonStr(reqDTO), e);
			sendMailException = e;
		}

		// 发邮件失败
		if (!(null != mailResult && mailResult.isSuccess())) {
			String sendMailErrorMsg = intelligentMeeting.getSendMailErrorMsg();
			// 默认下游服务异常
			String errorCode = ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION.getResultCode();
			String errorMsg = ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION.getResultMsg();
			if (sendMailException instanceof YunAiBusinessException) {
				String code = ((YunAiBusinessException) sendMailException).getCode();
				if (AiResultCode.isSendMailErrorCode(code)) {
					// 发邮件错误码，独立转换
					AiResultCode aiResultCode = AiResultCode.getByCodeOrMsg(code,
							((YunAiBusinessException) sendMailException).getMessage());
					errorMsg = aiResultCode.getMsg();
					errorCode = aiResultCode.getCode();
				}
			}
			errorMsg = String.format(sendMailErrorMsg, errorMsg);

			DialogueFlowResultVO mailFlowResultVO = new DialogueFlowResultVO(1, FlowResultTypeEnum.MAIL, null, null);
			mailFlowResultVO.setMailInfo(MailInfoVO.builder().sendMailFlag(false).build());
			mailFlowResultVO.setErrorCode(errorCode);
			mailFlowResultVO.setErrorMessage(errorMsg);
			respVO.setFlowResult(mailFlowResultVO);
			// 流式响应结束
			respVO.setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
			handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(respVO));
			return;
		}

		log.info("发送邮件成功 dialogueId:{}", handleDTO.getDialogueId());

		DialogueFlowResultVO mailFlowResultVO = new DialogueFlowResultVO(0, FlowResultTypeEnum.MAIL, null, null);
		mailFlowResultVO.setMailInfo(MailInfoVO.builder().sendMailFlag(true).build());
		respVO.setFlowResult(mailFlowResultVO);
		saveOutputList.add(chatFlowResultAssembler.getFlowResult(mailFlowResultVO));
		// 设置hbase
		AiTextResultRespParameters result = AiTextResultRespParameters.builder()
				.version(AiTextResultVersionEnum.V2.getVersion())
				.outputCommand(new DialogueIntentionOutput(mainIntention)).outputList(saveOutputList).build();

		// 保存hbase
		dataSaveService.saveHbaseAllChatResult(handleDTO, result);

		// 保存tidb
		dataSaveService.addSuccessAndModelCode(handleDTO, null, OutContentTypeEnum.TEXT);

		// 流式响应结束
		respVO.setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
		handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(respVO));

	}

	private void checkHandleInfo(ChatAddHandleDTO handleDTO, DialogueResultV2VO dialogResult) {
		// 业务信息判断
		SourceChannel sourceChannel = sourceChannelsProperties.getByChannel(dialogResult.getSourceChannel());
		SourceChannel reqSourceChannel = sourceChannelsProperties
				.getByChannel(handleDTO.getReqDTO().getSourceChannel());
		if (!(null != sourceChannel && null != reqSourceChannel
				&& sourceChannel.getBusinessType().equals(reqSourceChannel.getBusinessType()))) {
			log.error("上一次对话信息业务信息不一致");
			throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
		}
	}

	/**
	 * 获取大模型结果
	 * 
	 * @param dialogResult 对话结果
	 * @return
	 */
	private DialogueFlowResult getTextModelResult(DialogueResultV2VO dialogResult) {
		List<DialogueFlowResult> outputList = dialogResult.getOutputList();
		if (CollUtil.isNotEmpty(outputList)) {
			for (DialogueFlowResult output : outputList) {
				if (Objects.equal(FlowResultTypeEnum.TEXT_MODEL.getType(), output.getResultType())) {
					return output;
				}
			}
		}
		return null;
	}

	/**
	 * 获取tool结果
	 * 
	 * @param dialogResult 对话结果
	 * @return
	 */
	private AiFunctionResult getAiFunctionResult(DialogueResultV2VO dialogResult) {
		List<DialogueFlowResult> outputList = dialogResult.getOutputList();
		if (CollUtil.isNotEmpty(outputList)) {
			for (DialogueFlowResult output : outputList) {
				if (Objects.equal(FlowResultTypeEnum.TOOL_RESULT.getType(), output.getResultType())) {
					return output.getAiFunctionResult();
				}
			}
		}
		return null;
	}

	/**
	 * 通过对话信息获取邮件信息
	 * 
	 * @param lastMailDialogueInfo 上一次发邮件对话信息
	 * @return
	 */
	private MailInfoVO getMailInfoResult(AlgorithmChatContentEntity lastMailDialogueInfo) {
		// 获取对话内容信息
		AssistantChatV2PollingUpdateDTO pollingUpdate = new AssistantChatV2PollingUpdateDTO();
		pollingUpdate.setDialogueId(lastMailDialogueInfo.getId());
		pollingUpdate.setUserId(lastMailDialogueInfo.getUserId());
		DialogueResultV2VO dialogResult = algorithmChatV2ContentService.pollingUpdate(pollingUpdate);
		if (null == dialogResult) {
			return null;
		}
		List<DialogueFlowResult> outputList = dialogResult.getOutputList();
		if (CollUtil.isNotEmpty(outputList)) {
			for (DialogueFlowResult output : outputList) {
				if (Objects.equal(FlowResultTypeEnum.MAIL.getType(), output.getResultType())) {
					return output.getMailInfo();
				}
			}
		}
		return null;
	}

	private AlgorithmChatContentEntity getLastIntentionDialogue(String userId, Long sessionId, String dialogueId) {
		AlgorithmChatContentEntity condition = new AlgorithmChatContentEntity();
		condition.setUserId(userId);
		condition.setSessionId(sessionId);
		condition.setToolsCommand(DialogueIntentionEnum.TEXT_TOOL.getCode());
		condition.setSubToolsCommand(DialogueIntentionSubEnum.AI_MEETING_MAIL.getCode());
		condition.setId(Long.parseLong(dialogueId));
		return chatContentService.getLastIntentionDialogue(condition);

	}
}
