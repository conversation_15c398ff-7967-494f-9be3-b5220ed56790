package com.zyhl.yun.api.outer.application.convertor.dto.dynamic;

import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDriveFileCompleteReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDriveFileCreateReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDriveGetUploadUrlReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.resp.OwnerDriveFileCreateRespDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveFileVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveGetUploadUrlVO;
import com.zyhl.yun.api.outer.application.dto.FileCompleteDTO;
import com.zyhl.yun.api.outer.application.dto.FileCreateDTO;
import com.zyhl.yun.api.outer.application.dto.GetSharpUploadUrlReqDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeDispatchTaskMqEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.vo.FileCompleteVO;
import com.zyhl.yun.api.outer.domain.vo.FileCreateVO;
import com.zyhl.yun.api.outer.domain.vo.GetSharpUploadUrlReqVO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description}
 * <p>
 *
 * <AUTHOR>
 * @since 4月14 2025
 */
@Mapper(componentModel = "spring")
public interface PojoConverter {

  OwnerDriveFileCreateReqDTO toFileCreateReqDTO(FileCreateDTO dto);

  FileCreateVO toFileCreateVO(OwnerDriveFileCreateRespDTO resp);

  OwnerDriveFileCompleteReqDTO toFileCompleteReqDTO(FileCompleteDTO dto);

  @Mapping(target = "thumbnailUrl", ignore = true)
  FileCompleteVO toFileCompleteVO(OwnerDriveFileVO ownerDriveFileVO );

  /**
   * 获取上传地址请求接口DTO转换为独立空间请求DTO
   * @param dto 接口dto
   * @return 独立空间请求DTO
   */
  OwnerDriveGetUploadUrlReqDTO toOwnerDriveGetUploadUrlReqDTO(GetSharpUploadUrlReqDTO dto);

  /**
   * 独立空间返回VO转换为获取上传地址请求接口VO
   * @param responseVO 独立空间返回VO
   * @return 获取上传地址请求接口VO
   */
  GetSharpUploadUrlReqVO toGetSharpUploadUrlReqVO(OwnerDriveGetUploadUrlVO responseVO);

  @Mapping(target = "resourceType", source = "fromResourceType")
  UserKnowledgeDispatchTaskMqEntity toUserKnowledgeDispatchTaskMqEntity(UserKnowledgeFileEntity entity);

  List<UserKnowledgeDispatchTaskMqEntity> toUserKnowledgeDispatchTaskMqEntityList(
      List<UserKnowledgeFileEntity> list);
}
