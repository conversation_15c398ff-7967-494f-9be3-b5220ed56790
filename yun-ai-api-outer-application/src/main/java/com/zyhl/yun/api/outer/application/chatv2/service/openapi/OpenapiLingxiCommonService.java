package com.zyhl.yun.api.outer.application.chatv2.service.openapi;

import com.zyhl.yun.api.outer.application.chatv2.dto.AssistantChatV2PollingUpdateDTO;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueResultV2VO;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

/**
 * 智能体对话-公共-服务接口
 *
 * <AUTHOR>
 * @date 2025-07-04 08:56
 **/
public interface OpenapiLingxiCommonService {
	
	/**
	 * 内容轮询，异常不抛出，返回null
	 * 
	 * @param pollingUpdate 轮询参数
	 * @return 内容信息
	 */
	DialogueResultV2VO contentPollingUpdate(AssistantChatV2PollingUpdateDTO pollingUpdate);
	/**
	 * 获取对话id by 意图参数
	 * 
	 * @param userId        用户id
	 * @param sessionId     会话id
	 * @param mainIntention 主意图
	 * @return
	 */
	public String getDialogueIdByIntention(String userId, Long sessionId, IntentionInfo mainIntention);

	/**
	 * 获取当前对话id之前最新一条(会议通知邮件)意图对话
	 * 
	 * @param userId     用户id
	 * @param sessionId  会话id
	 * @param dialogueId 当前对话id
	 * @return
	 */
	public AlgorithmChatContentEntity getLastIntentionDialogue(String userId, Long sessionId, String dialogueId);

	/**
	 * 通过对话信息获取邮件信息
	 * 
	 * @param lastMailDialogueInfo 上一次发邮件对话信息
	 * @return 邮件信息
	 */
	public MailInfoVO getMailInfoResult(AlgorithmChatContentEntity lastMailDialogueInfo);

	/**
	 * 获取邮箱平台已发送邮件跳转地址
	 * 
	 * @param token basic token
	 * @return 跳转地址
	 */
	String getSendMailJumpUrl(String token);

	/**
	 * 获取邮箱平台编辑邮件跳转地址
	 * 
	 * @param token      basic token
	 * @param dialogueId 对话id
	 * @return 跳转地址
	 */
	String getMailEditJumpUrl(String token, String dialogueId);

	/**
	 * 获取AI助手H5跳转地址
	 * 
	 * @param token basic token
	 * @return 跳转地址
	 */
	String getAiHelperJumpUrl(String token);

	/**
	 * 获取录音笔记H5跳转地址
	 * 
	 * @param token     basic token
	 * @param noteTitle 笔记标题
	 * @return 跳转地址
	 */
	String getNoteVoiceJumpUrl(String token, String noteTitle);


}
