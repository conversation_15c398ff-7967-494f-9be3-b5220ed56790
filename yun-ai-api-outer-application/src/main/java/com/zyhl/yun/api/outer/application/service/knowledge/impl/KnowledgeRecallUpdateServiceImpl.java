package com.zyhl.yun.api.outer.application.service.knowledge.impl;

import com.alibaba.fastjson.JSON;
import com.zyhl.hcy.yun.ai.common.base.es.entity.CommonKnowledgeEsEntity;
import com.zyhl.hcy.yun.ai.common.base.es.entity.PersonalKnowledgeEsEntity;
import com.zyhl.hcy.yun.ai.common.rag.vo.RecallResultVO;
import com.zyhl.hcy.yun.ai.common.rag.vo.RerankResultVO;
import com.zyhl.yun.api.outer.application.service.knowledge.KnowledgeRecallUpdateService;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.domainservice.CommonKnowledgeDomainService;
import com.zyhl.yun.api.outer.domainservice.PersonalKnowledgeDomainService;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeBaseEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 知识库召回更新接口
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class KnowledgeRecallUpdateServiceImpl implements KnowledgeRecallUpdateService {

    private PersonalKnowledgeDomainService personalKnowledgeDomainService;

    private CommonKnowledgeDomainService commonKnowledgeDomainService;

    @Resource(name = "knowledgeRecallCountThreadPool")
    private ExecutorService knowledgeRecallCountThreadPool;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 缓存过期时间
     */
    private static final Long EXPIRE_TIME = 20L;

    private static final int EMPTY_VALUE = 0;

    @Override
    public void recallUpdate(List<RecallResultVO> esResult, List<RerankResultVO> rerankResult) {
        log.info("【知识库召回次数更新】更新ES召回次数-开始" );
        // 异步操作
        knowledgeRecallCountThreadPool.execute(() -> {
            long start = System.currentTimeMillis();

            for (RerankResultVO vo : rerankResult) {

                    // 倒推获取搜索结果
                    RecallResultVO item = esResult.get(Integer.parseInt(vo.getDocument().getSegmentId()));
                    String userId = item.getUserId();
                    log.info("【知识库召回次数更新】更新ES召回次数，用户id: {}, 重排结果： {}， 倒推获取搜索结果：{}",userId, JSON.toJSONString(vo), JSON.toJSONString(item));
                    try {
                        // 公共知识库搜索
                        if (KnowledgeBaseEnum.isCommon(item.getKnowledgeBase())) {
                            CommonKnowledgeEsEntity entity = getCommonKnowledgeEsEntity(item);
                            // 更新redis
                            int recallCount = updateRedisRecallCount(RedisConstants.COMMON_KNOWLEDGE_RECALL_COUNT_KEY, item.getId(), item.getKnowledgeBase(), entity, userId);
                            // 更新ES
                            if(recallCount > EMPTY_VALUE){
                                commonKnowledgeDomainService.updateCommonKnowledgeRecallCount(entity);
                            }
                        }

                        // 个人知识库搜索
                        if (KnowledgeBaseEnum.isPersonal(item.getKnowledgeBase())) {
                            // 更新redis
                            int recallCount = updateRedisRecallCount(RedisConstants.USER_KNOWLEDGE_RECALL_COUNT_KEY, item.getId(), item.getKnowledgeBase(),null, userId);
                            // 更新ES
                            if(recallCount > EMPTY_VALUE){
                                personalKnowledgeDomainService.updatePersonalKnowledgeRecallCount(userId, item.getId());
                            }
                        }
                    }catch (Exception e){
                        log.error("【知识库召回次数更新】更新ES召回次数失败，用户id: {}, 知识库类型：{}，文件id：{}，es id: {}, 问题 ：",userId, item.getKnowledgeBase(), item.getFileId(), item.getId(), e);
                    }

            }
            log.info("【知识库召回次数更新】更新ES召回次数-耗时：{}ms", System.currentTimeMillis() - start);
        });
    }

    /**
     * redis缓存操作 召回次数原子自增并返回新值
     * @param key 前缀
     * @param id ES库分片id
     * @param knowledgeBase 知识库类型
     * @param entity 公共知识库实体
     * @param userId 用户id
     * @return int
     */
    private int updateRedisRecallCount(String key, String id, String knowledgeBase, CommonKnowledgeEsEntity entity, String userId) {
        // 获取 RAtomicLong 实例
        RAtomicLong atomicLong = redissonClient.getAtomicLong(String.format(key, id));
        log.info("【知识库召回次数更新】查询redis缓存 ES id：{}，召回次数:{}", id, atomicLong.get());
        // 检查 key 是否存在
        if (!atomicLong.isExists()) {
            // 如果 key 不存在，查询一次ES，获取初始化召回次数
            // 公共知识库
            if (KnowledgeBaseEnum.isCommon(knowledgeBase)) {
                CommonKnowledgeEsEntity esEntity = commonKnowledgeDomainService.getCommonKnowledgeById(entity);
                if (null != esEntity){
                    if (null != esEntity.getRecallCount()){
                        atomicLong.set(esEntity.getRecallCount());
                    }else{
                        return EMPTY_VALUE;
                    }
                }
            }

            //个人知识库
            if (KnowledgeBaseEnum.isPersonal(knowledgeBase)) {
                PersonalKnowledgeEsEntity esEntity = personalKnowledgeDomainService.getPersonalKnowledgeById(userId,id);
                if (null != esEntity){
                    if (null != esEntity.getRecallCount()){
                        atomicLong.set(esEntity.getRecallCount());
                    }else{
                        return EMPTY_VALUE;
                    }
                }
            }
        }

        // 原子自增并返回新值
        long newValue = atomicLong.incrementAndGet();

        // 设置过期时间
        boolean isExpireSet = atomicLong.expire(EXPIRE_TIME, TimeUnit.MINUTES);
        if (isExpireSet) {
            log.info("【知识库召回次数更新】redis过期时间设置成功 ES id：{}，召回次数:{}", id, atomicLong.get());
            return (int) newValue;
        } else {
            log.error("【知识库召回次数更新】redis过期时间设置失败 ES id：{}，召回次数:{}", id, atomicLong.get());
            return EMPTY_VALUE;
        }
    }

    /**
     * 封装实体
     * @param item 倒推获取搜索结果
     */
    private CommonKnowledgeEsEntity getCommonKnowledgeEsEntity(RecallResultVO item) {

        // 封装请求实体
        CommonKnowledgeEsEntity entity = new CommonKnowledgeEsEntity();
        entity.setBaseId(item.getBaseId());
        entity.setParseType(item.getParseType());
        entity.setFileId(item.getFileId());
        // ES id
        entity.setId(item.getId());
        return entity;
    }

}
