package com.zyhl.yun.api.outer.application.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;
import java.util.List;

/**
 * className:TaskFileSuffixConfig
 * description: 任务文件资源-文件格式后缀配置类
 *
 * <AUTHOR>
 * @date 2024/10/31
 */
@Data
@RefreshScope
@Configuration
@ConfigurationProperties("task-file.suffix")
public class TaskFileSuffixConfig {

    private List<String> video = Collections.singletonList("mp4");
}
