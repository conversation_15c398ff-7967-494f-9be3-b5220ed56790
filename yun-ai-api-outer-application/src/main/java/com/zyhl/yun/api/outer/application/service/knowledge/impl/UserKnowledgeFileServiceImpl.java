package com.zyhl.yun.api.outer.application.service.knowledge.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.HashBasedTable;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum;
import com.zyhl.hcy.yun.ai.common.base.enums.RenameModeEnum;
import com.zyhl.hcy.yun.ai.common.base.es.domain.datahelper.repository.EsPersonalKnowledgeRepository;
import com.zyhl.hcy.yun.ai.common.base.es.entity.PersonalKnowledgeEsEntity;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.dto.CheckTextReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDriveBatchFileReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDriveFileUpdateReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.resp.OwnerDriveResponse;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveDirVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveFileVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.enums.UserFileThumbnailStyleEnum;
import com.zyhl.yun.api.outer.application.assembler.UserKnowledgeFileReqAssembler;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeCreateFolderReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileInfoReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileListBatchReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileListReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.KnowledgeFileUpdateReqDTO;
import com.zyhl.yun.api.outer.application.dto.knowledge.PersonalKnowledgeResourceListReqDTO;
import com.zyhl.yun.api.outer.application.service.knowledge.UserKnowledgeFileService;
import com.zyhl.yun.api.outer.config.KnowledgeAuditUrlProperties;
import com.zyhl.yun.api.outer.config.KnowledgePersonalProperties;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.domain.dto.AuditResultDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileResEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeInviteEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeResourceListReqEntity;
import com.zyhl.yun.api.outer.domain.vo.knowledge.FileListPageInfoVO;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeContentParagraph;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeFileVO;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeResource;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeResourceListPageInfoVO;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.FileSortTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.PersonalKnowledgeAiStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileAuditStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileCategoryEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileProcessStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeStatusEnum;
import com.zyhl.yun.api.outer.external.CheckSystemExternalService;
import com.zyhl.yun.api.outer.external.UserDriveExternalService;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileResRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeInviteRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeRepository;
import com.zyhl.yun.api.outer.util.CustomStringUtil;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 个人知识库文件业务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class UserKnowledgeFileServiceImpl implements UserKnowledgeFileService {

    private final static String FILE_NAME_STR = "\\[文件名\\]:";
    private final static String FILE_CONTENT_STR = "\\[文件内容\\]:";
    private final static List<String> SPECIAL_EXTENSIONS = Arrays.asList("doc","docx","pdf","html");
    private final KnowledgePersonalProperties knowledgePersonalProperties;

    private final UserKnowledgeFileRepository userKnowledgeFileRepository;

    private final UserKnowledgeFileResRepository userKnowledgeFileResRepository;
    private final UserKnowledgeInviteRepository userKnowledgeInviteRepository;
    @Resource
    private UserKnowledgeRepository userKnowledgeRepository;
    @Resource
    private UserDriveExternalService userDriveExternalService;
    @Resource
    private  EsPersonalKnowledgeRepository esPersonalKnowledgeRepository;
    @Resource
    private CheckSystemExternalService checkSystemExternalService;

    @Resource
    private UserKnowledgeFileReqAssembler userKnowledgeFileReqAssembler;
    @Resource
    private KnowledgeAuditUrlProperties knowledgeAuditUrlProperties;

    /**
     * 独立空间文件名非法
     */
    private static final String OWNER_DRIVE_FILE_NAME_INVALID = "13000002";

    /**
     * 独立空间文件名称送审失败
     */
    private static final String OWNER_DRIVE_FILE_NAME_ILLEGAL = "13010315";

    @Override
    public FileListPageInfoVO list(KnowledgeFileListReqDTO dto) {
        FileListPageInfoVO result = new FileListPageInfoVO();

        // 查资源表
        PageInfo<UserKnowledgeFileEntity> pageInfo = userKnowledgeFileRepository.list(dto.getUserId(), dto.getPageInfo(), Long.valueOf(dto.getLabelId()));
        if (ObjectUtil.isEmpty(pageInfo.getList())) {
            log.info("用户下的文件为空，userId:{}, delFlag:{}", dto.getUserId(), KnowledgeStatusEnum.NORMAL.getStatus());
            return result;
        }

        // 数据转换
        List<PersonalKnowledgeFileVO> list = new ArrayList<>();
        pageInfo.getList().forEach(entity -> list.add(fileResult(entity)));

        // 返回结果
        return result.setData(dto.getPageInfo(), pageInfo, list);
    }

    @Override
    public FileListPageInfoVO list180(KnowledgeFileListReqDTO dto) {
        FileListPageInfoVO result = new FileListPageInfoVO();

        // 统计数据，第一页才返回
        if ("0".equals(dto.getPageInfo().getPageCursor())) {
            result.setPendingCount(userKnowledgeFileRepository.count(dto.getUserId(), 0));
            result.setSuccessCount(userKnowledgeFileRepository.count(dto.getUserId(), 1));
        }

        // 查资源表
        PageInfo<UserKnowledgeFileEntity> pageInfo = userKnowledgeFileRepository.list180(dto.getUserId(), dto.getPageInfo(), dto.getStatus(), dto.getOrderBy());
        if (ObjectUtil.isEmpty(pageInfo.getList())) {
            log.info("用户下的文件为空，userId:{}, delFlag:{}", dto.getUserId(), KnowledgeStatusEnum.NORMAL.getStatus());
            return result;
        }

        // 数据转换
        List<PersonalKnowledgeFileVO> list = new ArrayList<>();
        pageInfo.getList().forEach(entity -> list.add(fileResult(entity)));

        // 返回结果
        return result.setData(dto.getPageInfo(), pageInfo, list);
    }

    /**
     * 处理文件状态和描述
     *
     * @param entity 文件实体
     * @return 个人文件对象
     */
    private PersonalKnowledgeFileVO fileResult(UserKnowledgeFileEntity entity) {
        PersonalKnowledgeFileVO vo = new PersonalKnowledgeFileVO(entity);
        if (FileProcessStatusEnum.isUnProcessed(entity.getAiStatus())) {
            // 未处理
            if (FileAuditStatusEnum.isNotPass(entity.getAuditStatus())) {
                // 审核不通过
                vo.failed("");
                try {
                    AuditResultDTO auditResultDTO = JsonUtil.parseObject(entity.getAuditResult(), AuditResultDTO.class);
                    if (auditResultDTO.isSensitive()) {
                        vo.sensitive();
                    } else {
                        vo.failed(knowledgePersonalProperties.getParseFailedReason().get(auditResultDTO.getAuditCode()));
                    }
                } catch (Exception e) {
                    log.error("审核结果解析异常，json信息：{}，异常信息：{}", entity.getAuditResult(), e.getMessage(), e);
                }
            } else {
                // 未送审 或 送审成功（进入解析流程）
                vo.processing();
            }
        } else if (FileProcessStatusEnum.isSuccess(entity.getAiStatus())) {
            // 成功
            vo.succeed();
        } else if (FileProcessStatusEnum.isFail(entity.getAiStatus())) {
            // 失败
            vo.failed(knowledgePersonalProperties.getParseFailedReason().get(entity.getResultCode()));
        } else {
            // 未知状态
            log.info("文件算法处理状态：{}", entity.getAiStatus());
            vo.failed(knowledgePersonalProperties.getParseFailedReason().get(entity.getResultCode()));
        }

        return vo;
    }

    @Override
    public PersonalKnowledgeFileVO info(KnowledgeFileInfoReqDTO dto) {

        UserKnowledgeFileEntity entity = userKnowledgeFileRepository.selectByFileId(null, dto.getFileId());
        if (ObjectUtil.isNull(entity)) {
            UserKnowledgeFileResEntity noteEntity = userKnowledgeFileResRepository.selectNoteByFileId(null, dto.getFileId());
            if (ObjectUtil.isNotNull(noteEntity)) {
                entity = userKnowledgeFileRepository.selectByFileId(noteEntity.getUserId(), noteEntity.getFromFileId());
                if (ObjectUtil.isNotNull(entity)) {
                    // 如果是笔记类型，重新设置独立空间的文件id值
                    entity.setFileId(noteEntity.getFileId());
                    entity.setAuditStatus(noteEntity.getAuditStatus());
                }
            }
        }
        if (ObjectUtil.isNull(entity)) {
            log.info("文件id不存在，fileId：{}", dto.getFileId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_NOT_FOUND);
        }
        if (!permission(dto.getUserId(), entity)) {
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_NO_PERMISSION);
        }
        return fileResult(entity);
    }

    @Override
    public String getUrlAndCheckAuditStatus(KnowledgeFileInfoReqDTO dto) {
        // 下载地址
        String url = "";
        UserKnowledgeFileEntity entity = userKnowledgeFileRepository.selectByFileId(null, dto.getFileId());
        if (ObjectUtil.isNull(entity)) {
            UserKnowledgeFileResEntity noteEntity = userKnowledgeFileResRepository.selectNoteByFileId(null, dto.getFileId());
            if (ObjectUtil.isNotNull(noteEntity)) {
                entity = userKnowledgeFileRepository.selectByFileId(noteEntity.getUserId(), noteEntity.getFromFileId());
                if (ObjectUtil.isNotNull(entity)) {
                    // 如果是笔记类型，重新设置独立空间的文件id值
                    entity.setFileId(noteEntity.getFileId());
                    entity.setAuditStatus(noteEntity.getAuditStatus());
                }
            }
        }
        if (ObjectUtil.isNull(entity)) {
            log.info("文件id不存在，fileId：{}", dto.getFileId());
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_NOT_FOUND);
        }
        if (!permission(dto.getUserId(), entity)) {
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_NO_PERMISSION);
        }
        //公共知识库 且是非图片 才判断文档送审状态，提示错误码
        if (entity.getBaseId() != null) {
            UserKnowledgeEntity userKnowledge = userKnowledgeRepository.selectById(entity.getBaseId());
            if (userKnowledge != null
                    && KnowledgeStatusEnum.OPEN.getStatus().equals(userKnowledge.getOpenLevel())
                    && FileAuditStatusEnum.isNotPass(entity.getAuditStatus())
                    && !FileCategoryEnum.IMAGE.getKnowledgeCategory().equals(entity.getCategory()) ) {
                throw new YunAiBusinessException(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
            }
            //公开知识库 送审不通过 如果是图片，则返回配置的和谐图片
            if (userKnowledge != null
                    && KnowledgeStatusEnum.OPEN.getStatus().equals(userKnowledge.getOpenLevel())
                    && FileAuditStatusEnum.isNotPass(entity.getAuditStatus())
                    && KnowledgeResourceTypeEnum.isPersonalFile(entity.getFromResourceType())
                    && FileCategoryEnum.IMAGE.getKnowledgeCategory().equals(entity.getCategory())) {
                return knowledgeAuditUrlProperties.getImageUrl();
            }
        }
        if (KnowledgeResourceTypeEnum.isPersonalFile(entity.getFromResourceType()) || KnowledgeResourceTypeEnum.isNote(entity.getFromResourceType())) {
            if(KnowledgeResourceTypeEnum.isNote(entity.getFromResourceType()) && !FileAuditStatusEnum.isPass(entity.getAuditStatus())){
                // 审核状态null，返回默认图，笔记审核中，审核不通过 返回默认地址
                url = knowledgeAuditUrlProperties.getThumbnailUrl();
            } else {
                url = userDriveExternalService.getDownloadUrl(entity.getUserId(), dto.getFileId());
            }
        }
        return url;
    }

    @Override
    public PersonalKnowledgeResourceListPageInfoVO resourceList(PersonalKnowledgeResourceListReqDTO dto) {
        // 获取知识库
        UserKnowledgeEntity userKnowledge = userKnowledgeRepository.selectById(Long.parseLong(dto.getBaseId()));
        if (Objects.isNull(userKnowledge)) {
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_NOT_EXIST);
        }

        // 请求dto转换为reqEntity
        UserKnowledgeResourceListReqEntity reqEntity = userKnowledgeFileReqAssembler.toUserKnowledgeResourceListReqEntity(dto);

        // 是知识库创建者
        if (dto.getUserId().equals(userKnowledge.getUserId())) {
            PersonalKnowledgeResourceListPageInfoVO result = new PersonalKnowledgeResourceListPageInfoVO();
            // 查个人知识库资源表
            PageInfo<UserKnowledgeFileEntity> pageInfo = userKnowledgeFileRepository.getCreatorList(reqEntity, dto.getUserId());
            List<UserKnowledgeFileEntity> resList = pageInfo.getList();
            if (ObjectUtil.isEmpty(pageInfo.getList())) {
                log.info("【查询个人知识库资源列表】个人知识库的文件为空，baseId:{}, delFlag:{}", dto.getBaseId(), KnowledgeStatusEnum.NORMAL.getStatus());
                return result;
            }

            // 获取独立空间文件信息map
            Map<String, OwnerDriveFileVO> fileVoMap = getFileVoMap(resList, dto.getUserId());

            // 数据转换
            List<PersonalKnowledgeResource> list = new ArrayList<>();
            for (UserKnowledgeFileEntity entity : resList) {
                PersonalKnowledgeResource resource = fileResultV2(entity);
                if (fileVoMap.containsKey(entity.getFileId())) {
                    // 设置缩略图信息列表
                    OwnerDriveFileVO fileVo = fileVoMap.get(entity.getFileId());
                    resource.setThumbnailUrls(fileVo.getThumbnailUrls());
                }
                list.add(resource);
            }

            //添加首切片内容
            fillFirstSplitContent(list, userKnowledge.getUserId());

            // 返回结果
            return result.setData(dto.getPageInfo(), pageInfo, list);
        }

        // 检查是否为知识库成员
        if (KnowledgeStatusEnum.isPrivate(userKnowledge.getOpenLevel())) {
            log.warn("【查询个人知识库资源列表】【是否为成员鉴权】【非该知识库成员，request:{}】", dto);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_FORBIDDEN);
        }
        List<UserKnowledgeInviteEntity> invite = userKnowledgeInviteRepository.get(Long.parseLong(dto.getBaseId()), dto.getUserId());
        if (CollUtil.isEmpty(invite) || Objects.isNull(invite.get(0))) {
            log.warn("【查询个人知识库资源列表】【是否为成员鉴权】【非该知识库成员，request:{}】", dto);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_FORBIDDEN);
        }

        PersonalKnowledgeResourceListPageInfoVO result = new PersonalKnowledgeResourceListPageInfoVO();
        // 查个人知识库资源表
        PageInfo<UserKnowledgeFileEntity> pageInfo = userKnowledgeFileRepository.getList(reqEntity, dto.getUserId());
        List<UserKnowledgeFileEntity> resList = pageInfo.getList();
        if (ObjectUtil.isEmpty(resList)) {
            log.info("【查询个人知识库资源列表】个人知识库的文件为空，baseId:{}, delFlag:{}", dto.getBaseId(), KnowledgeStatusEnum.NORMAL.getStatus());
            return result;
        }

        // 获取独立空间文件信息map
        Map<String, OwnerDriveFileVO> fileVoMap = getFileVoMap(resList, userKnowledge.getUserId());

        // 数据转换
        List<PersonalKnowledgeResource> list = new ArrayList<>();
        for (UserKnowledgeFileEntity entity : resList) {
            PersonalKnowledgeResource resource = fileResultV2(entity);
            if (fileVoMap.containsKey(entity.getFileId())) {
                // 设置缩略图信息列表
                OwnerDriveFileVO fileVo = fileVoMap.get(entity.getFileId());
                resource.setThumbnailUrls(fileVo.getThumbnailUrls());
            }
            list.add(resource);
        }

        //添加首切片内容
        fillFirstSplitContent(list,  userKnowledge.getUserId());

        // 返回结果
        return result.setData(dto.getPageInfo(), pageInfo, list);
    }

    /**
     * 获取独立空间文件信息map
     *
     * @param resList 个人知识库文件表列表结果
     * @param userId  用户id
     * @return 缩略图信息Map key: 文件id value: 独立空间文件信息
     */
    private Map<String, OwnerDriveFileVO> getFileVoMap(List<UserKnowledgeFileEntity> resList, String userId) {
        Map<String, OwnerDriveFileVO> fileMap = new HashMap<>(Const.NUM_16);
        try {
            List<String> fileIds = resList.stream()
                    .filter(entity -> FileCategoryEnum.IMAGE.getKnowledgeCategory().equals(entity.getCategory()))
                    .map(UserKnowledgeFileEntity::getFileId)
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(fileIds)) {
                return fileMap;
            }

            OwnerDriveBatchFileReqDTO req = new OwnerDriveBatchFileReqDTO();
            req.setUserId(userId);
            req.setFileIds(fileIds.toArray(new String[0]));
            req.setImageThumbnailStyleList(new String[]{UserFileThumbnailStyleEnum.LARGE.getStyle(), UserFileThumbnailStyleEnum.SMALL.getStyle()});
            List<OwnerDriveFileVO> driveFileList = userDriveExternalService.getBatchFileInfo(req);

            if (CollUtil.isEmpty(driveFileList)) {
                return fileMap;
            }

            driveFileList.stream().filter(driveFile -> CollUtil.isNotEmpty(driveFile.getThumbnailUrls())).forEach(driveFile -> {
                fileMap.put(driveFile.getFileId(), driveFile);
            });
        } catch (Exception e) {
            log.error("【查询个人知识库资源列表】 获取缩略图信息Map异常 resList:{} | e:", JsonUtil.toJson(resList), e);
        }

        return fileMap;
    }

    @Override
    public List<PersonalKnowledgeResource> batchGet(KnowledgeFileListBatchReqDTO dto) {
        List<UserKnowledgeFileEntity> list = userKnowledgeFileRepository.batchGet(dto.getUserId(), dto.getResourceIdList());
        if (ObjectUtil.isNotEmpty(list)) {
            List<PersonalKnowledgeResource> resourceList = new ArrayList<>();
            list.forEach(entity -> resourceList.add(fileResultV2(entity)));
            return resourceList;
        }
        return null;
    }

    @Override
    public PersonalKnowledgeResource update(KnowledgeFileUpdateReqDTO dto) {
        String userId = StrUtil.isNotBlank(dto.getUserId()) ? dto.getUserId() : RequestContextHolder.getUserId();
        UserKnowledgeFileEntity userKnowledgeFileEntity = userKnowledgeFileRepository.getFileByBaseIdAndFileId(userId,
                dto.getBaseId(), dto.getResourceId());
        if (userKnowledgeFileEntity.getFileName().equals(dto.getName())) {
            return fileResultV2(userKnowledgeFileEntity);
        }
        if (ObjectUtil.isEmpty(userKnowledgeFileEntity)) {
            throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_FILE_NOT_EXIST);
        }
        if (ObjectUtil.isEmpty(dto.getName())) {
            return fileResultV2(userKnowledgeFileEntity);
        }
        String name = FileUtil.getPrefix(dto.getName());
        String suffix = FileUtil.getSuffix(dto.getName());
        //判断长度限制 截取
        if (name.length() > knowledgePersonalProperties.getFileNameLength()) {
            name = name.substring(0, knowledgePersonalProperties.getFileNameLength() - 1);
            if (suffix.isEmpty()) {
                dto.setName(name);
            } else {
                dto.setName(name + "." + suffix);
            }
        }
        //默认设置force_rename
        String renameMode = StrUtil.emptyToDefault(dto.getRenameMode(), RenameModeEnum.FORCE_RENAME.getCode());
        //判断是那种类型 做不同处理
        OwnerDriveFileUpdateReqDTO reqDTO = new OwnerDriveFileUpdateReqDTO();
        reqDTO.setFileId(userKnowledgeFileEntity.getFileId());
        reqDTO.setFileRenameMode(renameMode);
        reqDTO.setUserId(userId);

        String newFileName = dto.getName();
        //同步送审文件名
        CheckTextReqDTO checkTextReqDTO = new CheckTextReqDTO();
        checkTextReqDTO.setContent(newFileName);
        checkTextReqDTO.setAccount(userId);
        checkSystemExternalService.checkText(checkTextReqDTO);
        //个人云文件需要处理独立空间更名
        if (KnowledgeResourceTypeEnum.isPersonalFile(userKnowledgeFileEntity.getFromResourceType())) {
            reqDTO.setName(newFileName);
            OwnerDriveFileVO ownerDriveFileVO = updatePersonalFile(reqDTO);
            newFileName = ownerDriveFileVO.getName();
            //反查一遍文件信息是否修改成功
            } else {
            //网址笔记等自己处理名字
            if (userKnowledgeFileEntity.getFileName().equalsIgnoreCase(newFileName)) {
                //文件重命名时保持文件名大小写不变，仅在主文件名后拼接当前时间戳，格式：{主文件名}_{yyyyMMdd}_{HHmmss}
                newFileName = CustomStringUtil.getNewFileNameByNow(dto.getName());
            }
        }

        userKnowledgeFileEntity.setFileName(newFileName);
        log.info("【更新文件信息】更新文件信息,oldFileName:{},newFileName:{}", userKnowledgeFileEntity.getFileName(), newFileName);
        userKnowledgeFileRepository.update(userKnowledgeFileEntity);
        return fileResultV2(userKnowledgeFileEntity);
    }

    private OwnerDriveFileVO updatePersonalFile(OwnerDriveFileUpdateReqDTO reqDTO) {
        try {
            OwnerDriveResponse response = userDriveExternalService.updateFileInfo(reqDTO);
            if (!response.getSuccess()) {
                log.error("【更新文件信息】更新文件信息,结果失败，request:{},response:{}", JsonUtil.toJson(reqDTO),
                        JsonUtil.toJson(response));
                throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
            }
            //修改完在查询一次修改完的名称 有独立空间的命名规则
            return userDriveExternalService.getFileInfo(reqDTO.getUserId(), reqDTO.getFileId());
        } catch (YunAiBusinessException e) {
            if (OWNER_DRIVE_FILE_NAME_INVALID.equals(e.getCode())) {
                throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_FILE_INVALID_NAME);
            } else if (OWNER_DRIVE_FILE_NAME_ILLEGAL.equals(e.getCode())) {
                throw new YunAiBusinessException(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
            }
            //错误码做映射 送审失败 、名称不合法等
            log.error("【更新文件信息】更新文件信息失败，request:{}", JsonUtil.toJson(reqDTO));
            throw e;
        } catch (Exception e) {
            log.error("【更新文件信息】更新文件信息失败，request:{}", JsonUtil.toJson(reqDTO), e);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        }
    }

    @Override
    public PersonalKnowledgeResource createFolder(KnowledgeCreateFolderReqDTO dto) {
        // 参数校验
        UserKnowledgeEntity userKnowledge = userKnowledgeRepository.selectById(Long.valueOf(dto.getBaseId()));
        if (Objects.isNull(userKnowledge)
                || KnowledgeStatusEnum.isDeleted(userKnowledge.getDelFlag())
                || !dto.getUserId().equals(userKnowledge.getUserId())) {
            log.info("【创建文件夹】知识库不存在，baseId：{}，entity：{}", dto.getBaseId(), JsonUtil.toJson(userKnowledge));
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }

        String parentFileId = dto.getParentFileId();
        UserKnowledgeFileEntity fileEntity = null;
        if ("/".equals(parentFileId)) {
            parentFileId = userKnowledge.getFolderId();
        } else {
            fileEntity = userKnowledgeFileRepository.selectByFileId(dto.getUserId(), parentFileId);
            if (Objects.isNull(fileEntity) || FileTypeEnum.isFile(fileEntity.getFileType())) {
                log.info("【创建文件夹】父级目录不存在，parentFileId：{}", parentFileId);
                throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_FILE_PARENT_NOT_EXIST);
            }
        }

        // 调独立空间创建文件夹
        String renameMode = StrUtil.emptyToDefault(dto.getRenameMode(), RenameModeEnum.FORCE_RENAME.getCode());
        OwnerDriveDirVO createResult = userDriveExternalService.createCatalog(dto.getUserId(), dto.getName(), parentFileId, renameMode);
        if (RenameModeEnum.REFUSE.getCode().equals(renameMode)) {
            // 拒绝重命名，需要查询是否已经存在
            UserKnowledgeFileEntity existEntity = userKnowledgeFileRepository.selectByFileId(dto.getUserId(), createResult.getFileId());
            if (Objects.nonNull(existEntity)) {
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS.getResultCode(), "名称已存在");
            }
        }

        // 保存数据库
        UserKnowledgeFileEntity entity = new UserKnowledgeFileEntity();
        entity.setBaseId(Long.parseLong(dto.getBaseId()));
        entity.setParentFileId(createResult.getParentFileId());
        entity.setUserId(dto.getUserId());
        entity.setFileId(createResult.getFileId());
        entity.setFileName(createResult.getFileName());
        entity.setOwnerType(OwnerTypeEnum.AI.getOwnerValue());
        entity.setOwnerId(dto.getUserId());
        entity.setPaasCode(String.valueOf(RequestContextHolder.getBelongsPlatform()));
        entity.setFileType(FileTypeEnum.FOLDER.getKnowledgeFileType());
        entity.setCategory(FileCategoryEnum.FOLDER.getKnowledgeCategory());
        entity.setAiStatus(PersonalKnowledgeAiStatusEnum.PROCESSING_SUCCESS.getCode());
        entity.setAuditStatus(FileAuditStatusEnum.PASS.getStatus());
        if (Objects.isNull(fileEntity)) {
            entity.setParentFilePath(userKnowledge.getFolderId());
        } else {
            entity.setParentFilePath(fileEntity.getParentFilePath() + "/" + parentFileId);
        }
        entity.setSortType(FileSortTypeEnum.FILE.getCode());

        userKnowledgeFileRepository.add(entity);

        return fileResultV2(entity);
    }

    /**
     * 文件权限校验
     */
    private boolean permission(String userId, UserKnowledgeFileEntity entity) {
        // 兼容旧版本，旧版本没有baseId，则直接返回true
        if (Objects.equals(null, entity.getBaseId())) {
            return true;
        }
        if (userId.equals(entity.getUserId())) {
            return true;
        }
        // 知识库邀请列表
        List<UserKnowledgeInviteEntity> inviteEntityList = userKnowledgeInviteRepository.getListByUserId(Collections.singletonList(entity.getBaseId()), userId);
        return ObjectUtil.isNotEmpty(inviteEntityList);
    }

    /**
     * 处理文件状态和描述
     *
     * @param entity 文件实体
     * @return 个人文件对象
     */
    private PersonalKnowledgeResource fileResultV2(UserKnowledgeFileEntity entity) {
        PersonalKnowledgeResource vo = new PersonalKnowledgeResource(entity);
        if (FileProcessStatusEnum.isUnProcessed(entity.getAiStatus())) {
            // 未处理
            if (FileAuditStatusEnum.isNotPass(entity.getAuditStatus())) {
                // 审核不通过
                vo.failed("");
                try {
                    AuditResultDTO auditResultDTO = JsonUtil.parseObject(entity.getAuditResult(), AuditResultDTO.class);
                    if (auditResultDTO.isSensitive()) {
                        vo.sensitive();
                    } else {
                        vo.failed(knowledgePersonalProperties.getParseFailedReason().get(auditResultDTO.getAuditCode()));
                    }
                } catch (Exception e) {
                    log.error("审核结果解析异常，json信息：{}，异常信息：{}", entity.getAuditResult(), e.getMessage(), e);
                }
            } else {
                // 未送审 或 送审成功（进入解析流程）
                vo.processing();
            }
        } else if (FileProcessStatusEnum.isSuccess(entity.getAiStatus())) {
            // 成功
            vo.succeed();
        } else if (FileProcessStatusEnum.isFail(entity.getAiStatus())) {
            // 失败
            vo.failed(knowledgePersonalProperties.getParseFailedReason().get(entity.getResultCode()));
        } else {
            // 未知状态
            log.info("文件算法处理状态：{}", entity.getAiStatus());
            vo.failed(knowledgePersonalProperties.getParseFailedReason().get(entity.getResultCode()));
        }

        return vo;
    }

    public void fillFirstSplitContent(List<PersonalKnowledgeResource> list, String userId) {
        if(CollUtil.isEmpty(list)){
            return;
        }
        List<PersonalKnowledgeEsEntity> sourceList = batchGetEsEntity(list,userId);
        List<Integer>indexes = ListUtil.toList(0,100);
        List<PersonalKnowledgeEsEntity> result = esPersonalKnowledgeRepository.getFirstSplitContentList(sourceList,indexes);
       if(CollUtil.isEmpty(result)){
           log.info("从ES获取到的数据为空。");
           return;
       }
        HashBasedTable<String, Integer, String> table = HashBasedTable.create();
        result.forEach(item -> table.put(item.getFileId(), item.getIndex(), item.getText()));
        list.forEach(item -> {
            String text = table.get(item.getResourceId(), 0);
            if(StrUtil.isEmpty(text)){
                text = table.get(item.getResourceId(), 100);
            }
          if (StrUtil.isNotBlank( text)) {
              //[文件名]: 测试文档1.pdf [文件内容]:  都市生活中的美好瞬间 ，
              String prefixRegex = FILE_NAME_STR + "\\s*.*?" + FILE_CONTENT_STR + "\\s*";
              text = text.replaceFirst(prefixRegex, StrUtil.EMPTY);
              String fileExtension = item.getFileExtension();
              PersonalKnowledgeContentParagraph paragraph = new PersonalKnowledgeContentParagraph();
              if(StrUtil.isNotBlank(fileExtension)&&SPECIAL_EXTENSIONS.contains(fileExtension.toLowerCase())){
                  handleParagraph(paragraph,  text);
              } else {
                  paragraph.setContent(text);
              }
              item.setParagraphList(Collections.singletonList(paragraph));
          }
        });
    }

    private void handleParagraph(PersonalKnowledgeContentParagraph paragraph,String text) {
        // \t标题1 \t标题2 \t标题3 \t\t 分块
        String[] parts = text.split("   ");
        List<String> nonEmptyParts = new ArrayList<>();
        for (String part : parts) {
            String trimmed = part.trim();
            if (StrUtil.isNotBlank(trimmed)) {
                nonEmptyParts.add(trimmed);
            }
        }
        int size = nonEmptyParts.size();
        if(size>1){
            paragraph.setTitle(nonEmptyParts.get(0));
            paragraph.setContent(nonEmptyParts.get(size-1));
        } else {
            paragraph.setContent(text);
        }
    }

    private List<PersonalKnowledgeEsEntity> batchGetEsEntity(List<PersonalKnowledgeResource> list,String userId) {
        List<PersonalKnowledgeEsEntity> sourceList = new ArrayList<>();
        list.forEach(item -> {
            PersonalKnowledgeEsEntity esEntity = PersonalKnowledgeEsEntity.builder()
                    .baseId(item.getBaseId())
                    .fileId(item.getResourceId())
                    .userId(userId).build();
            sourceList.add(esEntity);
        });
        return sourceList;
    }
}
