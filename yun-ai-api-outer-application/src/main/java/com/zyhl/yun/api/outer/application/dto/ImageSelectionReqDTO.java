package com.zyhl.yun.api.outer.application.dto;

import com.zyhl.yun.api.outer.domain.entity.image.AlbumConditionEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/20 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImageSelectionReqDTO {

    @NotEmpty(message = "图片文件id列表不能为空")
    private List<String> fileIds;

    @NotNull(message = "userId不能为空")
    private String userId;

    /**
     * 业务类型： 1、个人云 2、圈子 3、共享群 4、家庭云
     */
    @NotNull(message = "所有者类型不能为空")
    private Integer ownerType;
    
    /**
     * 相册生成条件，空则不限制
     */
    private AlbumConditionEntity albumCondition;

	public ImageSelectionReqDTO(List<String> fileIds, String userId, Integer ownerType) {
		this.fileIds = fileIds;
		this.userId = userId;
		this.ownerType = ownerType;
	}
    
}
