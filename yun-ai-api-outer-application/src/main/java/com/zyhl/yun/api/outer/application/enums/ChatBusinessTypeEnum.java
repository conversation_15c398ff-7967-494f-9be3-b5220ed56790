package com.zyhl.yun.api.outer.application.enums;

import com.zyhl.yun.api.outer.enums.AssistantEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 对话业务类型枚举
 *
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum ChatBusinessTypeEnum {

    /**
     * 对话主流程
     */
    XIAO_TIAN(AssistantEnum.XIAO_TIAN.getCode(), "小天对话流程"),
    YUN_MAIL(AssistantEnum.YUN_MAIL.getCode(), "云邮对话流程"),
    CLOUD_PHONE(AssistantEnum.CLOUD_PHONE.getCode(), "云手机对话流程"),
    NOTE(AssistantEnum.NOTE.getCode(), "笔记对话流程"),
	MESSAGE_5G(AssistantEnum.MESSAGE_5G.getCode(), "5g消息对话流程"),

	;

	private final String type;

	private final String name;

}
