package com.zyhl.yun.api.outer.application.handle.chat.impl;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.handle.chat.AbstractChatAddHandler;
import com.zyhl.yun.api.outer.application.service.task.TextGenerateTextService;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import lombok.extern.slf4j.Slf4j;

/**
 * 文本意图，流式返回
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TextSseHandlerImpl extends AbstractChatAddHandler {

    @Resource
    private TextGenerateTextService textGenerateTextService;

    @Override
    public int order() {
        return ExecuteSort.TEXT_SSE.getSort();
    }

    @Override
    public boolean execute(ChatAddInnerDTO innerDTO) {
        // 流式对话 并且 文本意图
        return innerDTO.getSseEmitter() != null
                && DialogueIntentionEnum.isTextIntention(innerDTO.getIntentionCode());
    }

    @Override
    public boolean run(ChatAddInnerDTO innerDTO) {
        log.info("进入文本流式对话处理");
        //获取资源内容
        String resourceContent = resourceContent(innerDTO.getReqParams());

        // 扣减权益
        memberCenterService.consumeBenefit(innerDTO.getReqParams(), RequestContextHolder.getPhoneNumber(), innerDTO.getDialogueId());

        // 保存到hbase
        AiTextResultEntity resultEntity = saveTextResult(innerDTO, "", resourceContent);

        // 保存数据库
        add(innerDTO, ChatStatusEnum.CHAT_IN);
        
        if(StringUtils.isNotEmpty(resourceContent)) {
        	//资源内容获取不为空，设置进去innerDTO
        	innerDTO.setResourceContent(resourceContent);
        }
        // 文生文意图流式返回处理
        textGenerateTextService.flowTypeHandle(innerDTO);

        return false;
    }

}
