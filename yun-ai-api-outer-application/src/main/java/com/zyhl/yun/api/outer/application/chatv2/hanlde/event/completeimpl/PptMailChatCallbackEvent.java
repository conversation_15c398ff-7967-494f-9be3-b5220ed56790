package com.zyhl.yun.api.outer.application.chatv2.hanlde.event.completeimpl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.assembler.ChatFlowResultAssembler;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.event.AbstractCompleteCallbackEvent;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.pojo.CompleteEvent;
import com.zyhl.yun.api.outer.application.chatv2.service.DataSaveService;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO.OpenApiLingxiCardLink;
import com.zyhl.yun.api.outer.application.chatv2.vo.OpenApiLingxiChatRespVO.OpenApiLingxiCardReplyMessage;
import com.zyhl.yun.api.outer.application.config.ApplicationAgentLingxiConfig;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters.ExtInfoParam;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueIntentionOutput;
import com.zyhl.yun.api.outer.domain.vo.MailInfoVO;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.AiTextResultVersionEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddFlowStatusEnum;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 下一个执行会议发邮件处理器事件
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@Component
public class PptMailChatCallbackEvent extends AbstractCompleteCallbackEvent {

	@Resource
	private DataSaveService dataSaveService;
	@Resource
	private ChatFlowResultAssembler chatFlowResultAssembler;
	@Resource
	private ApplicationAgentLingxiConfig applicationAgentLingxiConfig;
	@Resource
	private ChatTextToolBusinessConfig chatTextToolBusinessConfig;

	@Override
	public void complete(CompleteEvent data) {
		log.info("下一个执行会议方案ppt发邮件处理器事件");
		try {
			SseEventListener event = data.getEventListener();
			ChatAddHandleDTO handleDTO = data.getHandleDTO();
			ChatAddRespVO respVO = handleDTO.getRespVO();
			List<DialogueFlowResult> outputList = new ArrayList<>();
			if (CollUtil.isNotEmpty(event.getBeforeOutputList())) {
				// 追加输出之前的文案
				outputList.addAll(event.getBeforeOutputList());
			}
			respVO.getFlowResult().setIndex(0);
			if (StringUtils.isNotEmpty(event.getAppendBeforeOutContent())) {
				// 不为空，是追加outContent
				respVO.getFlowResult().setOutContent(event.getAppendBeforeOutContent() + data.getOutContent());
			} else {
				respVO.getFlowResult().setOutContent(data.getOutContent());
			}
			respVO.getFlowResult().setTitle(event.getCallBackTitle());
			respVO.getFlowResult().setReasoningContent(data.getReasoningContent());
			outputList.add(chatFlowResultAssembler.getFlowResult(respVO.getFlowResult()));

			// 会议通知邮件信息
			MailInfoVO mailInfo = null;
			if (null != event.getCallBackOriginalDialogueFlowResult()) {
				mailInfo = event.getCallBackOriginalDialogueFlowResult().getMailInfo();
			}

			// 设置hbase
			AiTextResultRespParameters result = AiTextResultRespParameters.builder()
					.version(AiTextResultVersionEnum.V2.getVersion())
					.extInfoParam(
							ExtInfoParam.builder().dialogueFlowResult(event.getCallBackDialogueFlowResult()).build())
					.outputCommand(new DialogueIntentionOutput(respVO.getOutputCommand().getCommand(),
							respVO.getOutputCommand().getSubCommand(), respVO.getOutputCommand().getArgumentMap()))
					.outputList(outputList).build();

			// 保存hbase
			dataSaveService.saveHbaseAllChatResult(handleDTO, result);

			// 保存tidb
			dataSaveService.addSuccessAndModelCode(handleDTO, event.getModelCode(), OutContentTypeEnum.TEXT);

			// 流式响应结束
			respVO.setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
			if (null != handleDTO.getLingxiParamInfo() && handleDTO.getLingxiParamInfo().isLingxiRespFlag()) {
				// 结束之前，追加2个消息（追尾【编辑邮件】、追尾【已确认邮件内容，发送邮件】）
				String pptEditMailTitle = applicationAgentLingxiConfig.getTextConfig().getPptEditMailTitle();
				String pptEditMailReplyText = applicationAgentLingxiConfig.getTextConfig().getPptEditMailReplyText();
				String pptSendMailTitle = applicationAgentLingxiConfig.getTextConfig().getPptSendMailTitle();
				String pptSendMailReplyText = applicationAgentLingxiConfig.getTextConfig().getPptSendMailReplyText();
				
				// 会议标题
				String meetingTitle = (null != mailInfo
						? chatTextToolBusinessConfig.getIntelligentMeeting().removeTitlePrefix(mailInfo.getTitle())
						: handleDTO.getInputInfoDTO().getDialogue());
				List<OpenApiLingxiCardLink> bubbles = new ArrayList<>();
				bubbles.add(new OpenApiLingxiCardLink(pptEditMailTitle,
						new OpenApiLingxiCardLink.ReplyLink(String.format(pptEditMailReplyText, meetingTitle))));
				bubbles.add(new OpenApiLingxiCardLink(pptSendMailTitle,
						new OpenApiLingxiCardLink.ReplyLink(String.format(pptSendMailReplyText, meetingTitle))));
				OpenApiLingxiCardReplyMessage replyMessage = new OpenApiLingxiCardReplyMessage(StringUtils.EMPTY,
						bubbles);
				handleDTO.getSseEmitterOperate().sendAndComplete(
						OpenApiLingxiChatRespVO.getOpenApiLingxiChatReplyResp(handleDTO, replyMessage));
			} else {
				// 单独send（清空后内容发送）
				respVO.getFlowResult().setIndex(event.getSendIndex());
				respVO.getFlowResult().setTitle(null);
				respVO.getFlowResult().setOutContent("");
				respVO.getFlowResult().setReasoningContent(null);
				respVO.setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
				handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(respVO));
			}
		} catch (YunAiBusinessException e) {
			data.getEventListener().dialogueFail(e.getCode(), e.getMessage());
		} catch (Exception e) {
			data.getEventListener().dialogueFail(AiResultCode.CODE_9999);
		}
	}

}
