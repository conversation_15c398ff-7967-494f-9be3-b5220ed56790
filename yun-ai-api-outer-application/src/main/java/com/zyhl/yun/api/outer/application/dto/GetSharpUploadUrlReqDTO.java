package com.zyhl.yun.api.outer.application.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * className: GetSharpUploadUrlReqDTO
 * description: 获取个人知识库文件分片上传地址
 *
 * <AUTHOR>
 * @date 2025/4/17
 */
@Data
public class GetSharpUploadUrlReqDTO {

    /**
     * 渠道来源，参考ChannelId 枚举值
     */
    @NotEmpty(message = "渠道来源不能为空")
    private String sourceChannel;

    /**
     * 用户Id，默认从token获取，第三方平台调用时必填。
     */
    private String userId;

    /**
     * 文件id
     */
    @NotEmpty(message = "文件id不能为空")
    @Length(max = 64, message = "文件id长度不能超过64")
    private String fileId;

    /**
     * 上传id
     */
    @NotEmpty(message = "上传id不能为空")
    @Length(max = 64, message = "上传id长度不能超过64")
    private String uploadId;

    /**
     * 分片信息，仅分片上传需要指定
     * 分片串行上传时，PartInfo 仅需填写partNumber、partSize
     * 分片并行上传时，第一个分片PartInfo 仅需填
     * 写partNumber、partSize，除第一个分片外的其他分片需填写partNumber、partSize、partOffset、parallelHashCtx
     */
    @NotEmpty
    @Size(min = 1, max = 100, message = "分片信息数组长度不能超过100")
    private List<UploadPartInfo> partInfos;
}