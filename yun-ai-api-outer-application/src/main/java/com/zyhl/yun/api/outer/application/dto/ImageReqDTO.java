package com.zyhl.yun.api.outer.application.dto;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 图片工具api接口请求参数
 *
 * <AUTHOR>
 */
@Slf4j
@Data
public class ImageReqDTO {

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 厂商编号，指定厂商
     */
    private String[] supplierTypes;

    /**
     * 图片参数
     */
    private List<TaskBusinessParamDTO.ImageParam> imageParamList = new ArrayList<>();

    /**
     * 来源渠道
     */
    private String sourceChannel;

    /**
     * 算法编码
     */
    private String algorithmCode;

}
