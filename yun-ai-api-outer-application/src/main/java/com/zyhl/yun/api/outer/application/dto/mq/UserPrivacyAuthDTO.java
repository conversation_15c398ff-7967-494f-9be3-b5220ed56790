package com.zyhl.yun.api.outer.application.dto.mq;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 用户隐私授权消息
 *
 * <AUTHOR>
 */
@Data
public class UserPrivacyAuthDTO {

    /**
     * 消息ID
     */
    String messageId;

    /**
     * saas集群区域
     */
    String areaCode;

    /**
     * 发生时间, RFC 3339，2019-08-20T06:51:27.292Z
     */
    String eventTime;

    /*
     *消息类型/动作类型
     */
    String eventType;

    /**
     * 版本号
     */
    String version;

    /**
     * 消息内容，json格式，由各业务消息定义
     */
    Object content;

    /**
     * 扩展信息，keyValue结构，JSON格式
     */
    Map<String, Object> others;

    /**
     * 客户端类型,和device_info中的头信息一致。
     */
    String clientType;

    /**
     * 表示合作伙伴编码，和彩云平台分配，接入平台设置分配的枚举值。（具体数值以“和彩云”分配为准）
     * Http头：x-huawei-channelSrc
     */
    String channelsrc;
}
