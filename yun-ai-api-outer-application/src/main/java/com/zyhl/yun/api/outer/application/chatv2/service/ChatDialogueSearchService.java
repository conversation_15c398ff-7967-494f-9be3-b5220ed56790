package com.zyhl.yun.api.outer.application.chatv2.service;

import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.application.dto.SearchImageReqDTO;
import com.zyhl.yun.api.outer.domain.vo.chat.search.SearchInfo;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchCommonParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchParam;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchResult;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.KeyValueVO;

import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

/**
 * 智能搜索（异步搜索数据）
 *
 * <AUTHOR>
 */
public interface ChatDialogueSearchService {

    /**
     * 【小天助手】智能搜索意图处理
     *
     * @param handleDTO 用户输入对象
     * @return 是否直接输出
     */
    boolean commonSearchIntentionHandle(ChatAddHandleDTO handleDTO);

    /**
     * 【云邮助手】智能搜索意图处理
     *
     * @param handleDTO 用户输入对象
     */
    boolean mailSearchIntentionHandle(ChatAddHandleDTO handleDTO);

    /**
     * 智能搜索意图处理
     *
     * @param handleDTO 用户输入对象
     * @return 会话输入返回结果VO
     */
    ChatAddRespVO searchIntentionHandle(ChatAddHandleDTO handleDTO);

    /**
     * 获取对话意图响应VO
     *
     * @param dto    会话输入入参DTO
     * @param result 会话输入返回结果VO
     * @return 对话意图响应VO
     */
    DialogueIntentionVO getIntentionVO(ChatAddReqDTO dto, ChatAddRespVO result);

    /**
     * 设置授权报名记录
     *
     * @param dto       会话输入DTO
     * @param resVO     会话输入返回结果VO
     * @param intention 对话意图
     * @Author: WeiJingKun
     */
    void handleSetRegisterStatus(ChatAddReqDTO dto, ChatAddRespVO resVO, String intention);

    /**
     * 设置结果列表
     *
     * @param resultList     结果列表
     * @param keyValueVoList KeyValue格式VO列表
     */
    void setResultList(List<String> resultList, List<KeyValueVO> keyValueVoList);

    /**
     * 构建发现广场搜索参数V2-搜索类型处理
     *
     * @param queryTypeStrList 查询类型字符串列表
     * @return java.util.List<java.lang.Integer>
     * @Author: WeiJingKun
     */
    @NotNull
    List<Integer> searchDiscoveryParamV2HandleQueryTypeList(List<String> queryTypeStrList);

    /**
     * 获取搜索结果
     *
     * @param searchParam       搜索参数
     * @param searchCommonParam 搜索通用参数
     * @return 搜索结果
     * @throws ExecutionException   执行异常
     * @throws InterruptedException 打断异常
     * @throws TimeoutException     超时异常
     */
    SearchResult getSearchResult(SearchParam searchParam, SearchCommonParam searchCommonParam)
            throws ExecutionException, InterruptedException, TimeoutException;


    /**
     * 专为邮箱提供的智能搜索意图处理 搜索意图：012-搜图片，013-搜文档，014-搜视频，015-搜音频，016-搜文件夹，017-搜笔记，028-搜邮件&邮件附件
     * 没有提取到意图实体，则进行联网对话搜索
     *
     * @param handleDTO
     */
    void mailAiSearchIntentionHandle(ChatAddHandleDTO handleDTO);

    boolean mailAutoAllNetworkSearch(ChatAddHandleDTO handleDTO);

    /**
     * 搜索知识库资源
     *
     * @param handleDTO 用户输入对象
     */
    void searchKnowledgeBaseResourceHandle(ChatAddHandleDTO handleDTO);

    /**
     * 语义搜图
     *
     * @param reqDTO 请求参数
     */
    SearchInfo searchImages(SearchImageReqDTO reqDTO);

    /**
     * 笔记正文搜索
     *
     * @param handleDTO 用户输入对象
     */
    void searchForNoteHandler(ChatAddHandleDTO handleDTO);

    /**
     * 【云手机】智能搜索意图处理
     *
     * @param handleDTO the handle dto
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2025-07-14 13:50
     */
    boolean cloudPhoneSearchIntentionHandle(ChatAddHandleDTO handleDTO);
}
