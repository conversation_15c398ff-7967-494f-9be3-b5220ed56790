package com.zyhl.yun.api.outer.application.handle.chat.impl;

import cn.hutool.json.JSONObject;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.handle.chat.AbstractChatAddHandler;
import com.zyhl.yun.api.outer.application.service.chat.LeadCopyService;
import com.zyhl.yun.api.outer.config.LeadCopyProperties;
import com.zyhl.yun.api.outer.config.LinkProperties;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.enums.AcceptLanguageEnum;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.OutContentTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddResultTypeEnum;
import com.zyhl.yun.api.outer.util.IntentionUtils;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 引导语 类型1-4 的处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class LeadCopyTypeHandlerImpl extends AbstractChatAddHandler {

    @Resource
    private LeadCopyService leadCopyService;
    @Resource
    private LinkProperties linkProperties;
    @Resource
    private LeadCopyProperties copyProperties;

    @Override
    public int order() {
        return ExecuteSort.LEAD_COPY.getSort();
    }

    @Override
    public boolean execute(ChatAddInnerDTO innerDTO) {
        // 只有普通对话才执行 并且 非强制大模型对话
        if (!ApplicationTypeEnum.isChat(innerDTO.getReqParams().getApplicationType())
                && !Boolean.TRUE.equals(innerDTO.getReqParams().getEnableForceLlm())) {
            return false;
        }

		// AI工具相关意图并且没传资源id (妙云相机、AI消除、智能抠图不管是否传资源id都直接返回跳转链接)
		boolean returnLinkCondition = IntentionUtils.returnLinkCondition(innerDTO.getIntentionCode(),
				innerDTO.getContent().getResourceId());
		// 文本工具意图
		boolean textToolIntentionCondition = DialogueIntentionEnum.isTextToolIntention(innerDTO.getIntentionCode());
		
		return (returnLinkCondition || textToolIntentionCondition);
    }

    @Override
    public boolean run(ChatAddInnerDTO innerDTO) {
        log.info("进入引导语处理");
        // 处理同步返回的信息
        String intentionCode = innerDTO.getIntentionCode();
		DialogueIntentionEnum intentionEnum = DialogueIntentionEnum.getByCodeOrDefault(intentionCode);

        // 兼容旧字段，这个字段新版已作废
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("linkURL", linkProperties.getIntentionUrl(innerDTO.getContent().getSourceChannel(), intentionEnum));
        jsonObject.set("linkName", intentionEnum.getName());
        innerDTO.getRespParams().setText(jsonObject.toString());

        // 设置引导文案对象
        DialogueIntentionVO intentionVO = innerDTO.getIntentionVO();
        if (Objects.isNull(intentionVO)) {
            intentionVO = DialogueIntentionVO.newMainIntention(intentionCode);
        }
        LeadCopyVO leadCopyVO = leadCopyService.getLeadCopyVo(intentionVO, innerDTO.getContent().getSourceChannel(), innerDTO.getContent().getDialogue());
        if(DialogueIntentionEnum.isTextToolIntention(intentionCode) && null == leadCopyVO) {
        	//！！注意：文本工具意图，但是实体未匹配到leadCopyVO，重置为文本意图，继续执行handle
        	innerDTO.setTextGenerateTextIntention();
        	innerDTO.getRespParams().setText(null);
        	return true;
        }
        innerDTO.getRespParams().setLeadCopy(leadCopyVO);
        boolean enUs = AcceptLanguageEnum.EN_US.getCode().equalsIgnoreCase(RequestContextHolder.getAcceptLanguage());
        // 创建笔记意图
        if (DialogueIntentionEnum.CREATE_NOTE.equals(intentionEnum)) {
            innerDTO.getRespParams().setTitle(enUs ? copyProperties.getCreateNoteTitleEn() : copyProperties.getCreateNoteTitle());
        }

        // 创建语音笔记意图
        if (DialogueIntentionEnum.CREATE_VOICE_NOTE.equals(intentionEnum)) {
            innerDTO.getRespParams().setTitle(enUs ? copyProperties.getCreateVoiceNoteTitleEn() : copyProperties.getCreateVoiceNoteTitle());
        }

        // 保存leadCopy到hbase【LeadCopy，type=1、2、3、4】
        saveTextResult(innerDTO, "", "");

        // 保存tidb
        addSuccess(innerDTO, OutContentTypeEnum.TEXT);

        // 返回同步结果
        innerDTO.getRespParams().setResultType(ChatAddResultTypeEnum.SYNCHRONIZATION.getType());
        return false;
    }

}
