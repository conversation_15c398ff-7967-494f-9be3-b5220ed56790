package com.zyhl.yun.api.outer.application.service.chat.impl;

import static com.zyhl.yun.api.outer.constants.Const.NUM_2;

import java.util.Collections;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.HbaseAiTextResultUtil;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.event.TextModelStreamEventListener;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.yun.api.outer.application.dto.SecondStreamChatAddDTO;
import com.zyhl.yun.api.outer.application.dto.SecondStreamChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.handle.chat.listener.SecondSseDialogueEventListener;
import com.zyhl.yun.api.outer.application.service.chat.SecondChatAddService;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.config.SecondStreamChatAddProperties;
import com.zyhl.yun.api.outer.domain.dto.redis.HistoryDialogInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domainservice.QpsLimitService;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;

import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 二次对话接口实现类
 *
 * <AUTHOR>
 * @date 2024/12/16 17:15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SecondChatAddServiceImpl implements SecondChatAddService {

    private final AlgorithmChatContentRepository contentRepository;

    private final SecondStreamChatAddProperties configProperties;

    private final ModelProperties modelProperties;

    private final TextModelExternalService textModelExternalService;

    private final QpsLimitService qpslimitService;

    private final RedisOperateRepository redisRepository;

    private final AiTextResultRepository aiTextResultRepository;

    /**
     * 二次流式对话接口
     *
     * @param dto 二次流式对话接口内部数据传输对象
     */
    @Override
    public void secondStreamChatAdd(SecondStreamChatAddInnerDTO dto) {
        SecondStreamChatAddDTO reqParam = dto.getReqParams();
        String userId = reqParam.getUserId();
        String dialogueId = reqParam.getDialogueId();

        // 获取对话内容信息
        AlgorithmChatContentEntity contentEntity = contentRepository.getByIdUserId(Long.parseLong(dialogueId), userId);
        if (contentEntity == null) {
            log.info("二次流式对话处理，对话信息不存在 dto:{}", JsonUtil.toJson(dto));
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_DIALOGUE_ID);
        }

        // 判断是否为允许二次流式对话的意图
        if (!configProperties.getAllowIntention().contains(contentEntity.getToolsCommand())) {
            log.info("二次流式对话处理，当前对话意图不允许二次对话，dto:{}", JsonUtil.toJson(dto));
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_CONVERSATION_ID_INVALID_CODE);
        }

        // 获取hbase结果
        AiTextResultEntity entity = aiTextResultRepository.getByRowKey(userId, dialogueId);
        try {
            // 获取对应结果
            AiTextResultRespParameters respParameters = JSON.parseObject(entity.getRespParameters(), AiTextResultRespParameters.class);

            // hbase已经存在content的结果，直接返回
            dto.setRespParameters(respParameters);
            if (respParameters.getMailInfo() != null && StringUtils.isNotEmpty(respParameters.getMailInfo().getContent())) {
                log.info("二次流式对话处理，hbase已经存在content的结果，直接返回 dto:{}", JsonUtil.toJson(dto));
                dto.sseSendAndComplete();
                return;
            }

            // 设置对话内容dto
            String sessionId = String.valueOf(contentEntity.getSessionId());
            dto.setContent(getModeInputContent(entity, userId, dialogueId, sessionId));
            dto.setIntentionCode(contentEntity.getToolsCommand());
            dto.setSessionId(sessionId);

            // 二次流式对话事件监听
            SecondSseDialogueEventListener event = new SecondSseDialogueEventListener(dto);

            // 模版参数
            String template = configProperties.getPromptTemplate()
                    .replace("{query}", dto.getContent());

            // 调用大文本模型
            handleLargeTextModelCall(event, template);

        } catch (JSONException e) {
            log.warn("二次流式对话处理，JSON解析失败，hbaseRespParameters：{}，dto：{}", entity.getRespParameters(), JsonUtil.toJson(dto));
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_DIALOGUE_ID);
        }
    }

    /**
     * 获取大模型文本输入内容
     *
     * @param entity   hbase识别结果entity
     * @param userId     用户id
     * @param dialogueId 对话id
     * @param sessionId  会话id
     * @return 文本输入内容
     */
    private String getModeInputContent(AiTextResultEntity entity, String userId, String dialogueId, String sessionId) {
        List<HistoryDialogInfoDTO> historyList = redisRepository.getHistoryDialogInfoList(sessionId);

        // 默认当前对话用户输入内容，去换行和空格
		String content = HbaseAiTextResultUtil.getInputDialogue(entity.getReqParameters()).trim().replaceAll("\\s+",
				" ");
        if (CollectionUtils.isEmpty(historyList)) {
            log.info("二次流式对话处理，历史对话为空，直接返回当前对话输入内容 content:{}", content);
            return content;
        }

        // 上一次文本对话不存在，直接返回
        if (historyList.size() < NUM_2) {
            log.info("二次流式对话处理，历史对话只有一条，直接返回当前对话输入内容 content:{}", content);
            return content;
        }

        // 获取上一次文本对话内容，非文本对话，直接返回
        HistoryDialogInfoDTO lastItem = historyList.get(historyList.size() - 2);
        if (!DialogueIntentionEnum.isTextIntention(lastItem.getIntentionCode())) {
            log.info("二次流式对话处理，上一次对话非文本对话，直接返回当前对话输入内容 content:{} ｜ lastItem:{}",
                    content, JsonUtil.toJson(lastItem));
            return content;
        }

        // 获取上一次对话结果
        AiTextResultEntity lastEntity = aiTextResultRepository.getByRowKey(userId, lastItem.getDialogId());
        AiTextResultRespParameters lastResp = JSON.parseObject(lastEntity.getRespParameters(), AiTextResultRespParameters.class);
        // 上次文本对话结果非成功，直接返回
        if (!ResultCodeEnum.SUCCESS.getResultCode().equals(lastResp.getResultCode()) ||
                StringUtil.isEmpty(lastResp.getData())) {
            log.info("二次流式对话处理，上次文本对话结果非成功，直接返回当前对话输入内容 lastEntity:{}", JsonUtil.toJson(lastEntity));
            return content;
        }
        String lastContent = HbaseAiTextResultUtil.getInputDialogue(lastEntity.getReqParameters());
        String currContent = HbaseAiTextResultUtil.getInputDialogue(entity.getReqParameters());
        // 拼接上次文本对话输入和输出内容
		content = lastContent.trim().replaceAll("\\s+", " ") + lastResp.getData().trim().replaceAll("\\s+", " ")
				+ currContent.trim().replaceAll("\\s+", " ").trim();

        // 大模型输入内容长度限制
        if (content.length() > configProperties.getModeMaxTokens()) {
            log.info("二次流式对话处理，拼接上次文本对话输入和输出内容长度超过限制，截取最后部分 dialogueId:{} | length:{}", dialogueId, content.length());
            content = content.substring(content.length() - configProperties.getModeMaxTokens());
        }

        return content;
    }

    /**
     * 调用大文本模型
     *
     * @param event    流式监听参数
     * @param template 对话模板
     */
    private void handleLargeTextModelCall(SecondSseDialogueEventListener event, String template) {
        String code = configProperties.getModeCode();

        // qps限制
        if (!qpslimitService.modelQpsLimit(code)) {
            log.info("二次流式对话处理 请求过多，qps限流，model:{}", code);
            event.dialogueFail(ResultCodeEnum.ERROR_LIMITATION);
            return;
        }

        try {
            TextModelTextReqDTO reqDTO = event.getTextDto().toTextReqDTO(null);
            if (!modelStreamHandle(reqDTO, code, event, template)) {
                event.dialogueFail(ResultCodeEnum.ERROR_SERVER_INTERNAL);
            }
        } catch (YunAiBusinessException e) {
            log.error("二次流式对话处理 调用文本大模型异常:{}，对话id:{}", e.getMessage(), event.getDialogId(), e);
            event.dialogueFail(e.getExceptionEnum());
        } catch (Exception e) {
            log.error("二次流式对话处理 调用文本大模型异常:{}，对话id:{}", e.getMessage(), event.getDialogId(), e);
            event.dialogueFail(ResultCodeEnum.ERROR_SERVER_INTERNAL);
        }
    }

    /**
     * 调大文本模型流式处理
     *
     * @param reqDTO 请求参数
     * @param code   模型编码
     * @param event  监听事件
     */
    private boolean modelStreamHandle(TextModelTextReqDTO reqDTO, String code, TextModelStreamEventListener event, String template) {
        // 新的对话信息
        TextModelMessageDTO msgDTO = new TextModelMessageDTO();
        // TODO 这里是否需要 设置是否强制联网搜索，需要，则接口需要增加入参
        msgDTO.setRole(TextModelRoleEnum.USER.getName());
        msgDTO.setContent(template);

        TextModelTextReqDTO req = new TextModelTextReqDTO();
        req.setTaskId(reqDTO.getTaskId());
        req.setUserId(reqDTO.getUserId());
        req.setSessionId(reqDTO.getSessionId());
        req.setMessageDtoList(Collections.singletonList(msgDTO));

        return textModelExternalService.streamDialogue(code, req, event);
    }


}
