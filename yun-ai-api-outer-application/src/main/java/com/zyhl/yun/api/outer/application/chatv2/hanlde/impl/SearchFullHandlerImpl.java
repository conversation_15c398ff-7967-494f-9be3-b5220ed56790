package com.zyhl.yun.api.outer.application.chatv2.hanlde.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.zyhl.yun.api.outer.enums.chat.SseNameEnum;
import org.springframework.stereotype.Component;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.AbstractChatAddV2Handler;
import com.zyhl.yun.api.outer.application.chatv2.service.ChatDialogueSearchService;
import com.zyhl.yun.api.outer.application.enums.ChatBusinessTypeEnum;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.AssistantEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import lombok.extern.slf4j.Slf4j;

/**
 * 全部搜索相关
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SearchFullHandlerImpl extends AbstractChatAddV2Handler {

    /**
     * 当前执行顺序
     */
    private ExecuteSort thisExecuteSort = ExecuteSort.SEARCH_FULL;

    @Resource
    private ChatDialogueSearchService chatDialogueSearchService;

    @Override
    public void afterPropertiesSet() throws Exception {
        //支持的业务初始化
        List<ChatBusinessTypeEnum> thisBusinessTypes = new ArrayList<>();
        thisBusinessTypes.add(ChatBusinessTypeEnum.XIAO_TIAN);
        this.setBusinessTypes(thisBusinessTypes);
    }

    @Override
    public int order() {
        return thisExecuteSort.getSort();
    }

    @Override
    public boolean execute(ChatAddHandleDTO handleDTO) {
        /**
         * 执行的条件： 1、应用类型 == chat（普通对话） 2、小天助手渠道号 3、搜索意图
         */
        return ApplicationTypeEnum.isChat(handleDTO.getReqDTO().getApplicationType())
                && AssistantEnum.isXiaoTian(RequestContextHolder.getAssistantEnum())
                && DialogueIntentionEnum.isSearchIntentionOrOther(handleDTO.getIntentionCode());
    }

    @Override
    public boolean run(ChatAddHandleDTO handleDTO) {
        log.info("进入{}", thisExecuteSort.getDesc());

        // 执行搜索
        if(chatDialogueSearchService.commonSearchIntentionHandle(handleDTO)) {
            // 返回异步结果
            handleDTO.getRespVO().getFlowResult().setResultType(FlowResultTypeEnum.SEARCH.getType());
            // 设置sse名称
            handleDTO.getSseEmitterOperate().setSseName(SseNameEnum.AI_SEARCH.getCode());
            // 返回搜索结果
            handleDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(handleDTO.getRespVO()));
        }

        return false;
    }

}
