package com.zyhl.yun.api.outer.application.chatv2.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.yun.api.outer.application.chatv2.dto.AssistantChatV2PollingUpdateDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueInputInfoDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.DialogueIntentionDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.SecondStreamChatAddV2InnerDTO;
import com.zyhl.yun.api.outer.application.chatv2.hanlde.listener.SseEventListener;
import com.zyhl.yun.api.outer.application.chatv2.service.AlgorithmChatV2ContentService;
import com.zyhl.yun.api.outer.application.chatv2.service.SecondChatAddV2IntelligentService;
import com.zyhl.yun.api.outer.application.chatv2.service.SecondChatAddV2Service;
import com.zyhl.yun.api.outer.application.chatv2.vo.ChatAddRespVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueFlowResultVO;
import com.zyhl.yun.api.outer.application.chatv2.vo.DialogueResultV2VO;
import com.zyhl.yun.api.outer.application.dto.SecondStreamChatAddV2DTO;
import com.zyhl.yun.api.outer.config.SecondStreamChatAddProperties;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueIntentionOutput;
import com.zyhl.yun.api.outer.domainservice.QpsLimitService;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatAddFlowStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 二次对话接口实现类
 *
 * <AUTHOR>
 * @date 2025/6/4 13:40
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SecondChatAddV2ServiceImpl implements SecondChatAddV2Service {

    private static final String REPLACE_QUERY = "{query}";

    private final AlgorithmChatV2ContentService algorithmChatV2ContentService;

    private final SecondChatAddV2IntelligentService secondChatAddV2IntelligentService;

    private final TextModelExternalService textModelExternalService;

    private final QpsLimitService qpslimitService;

    private final SecondStreamChatAddProperties secondStreamChatAddProperties;

    private final AiTextResultRepository aiTextResultRepository;

    /**
     * 二次流式对话接口
     *
     * @param innerDTO 二次流式对话接口内部数据传输对象
     */
    @Override
    public void secondStreamChatAdd(SecondStreamChatAddV2InnerDTO innerDTO) {
        SecondStreamChatAddV2DTO reqParam = innerDTO.getReqParams();
        // 获取对话内容信息
        AssistantChatV2PollingUpdateDTO pollingUpdate = new AssistantChatV2PollingUpdateDTO();
        pollingUpdate.setDialogueId(Long.parseLong(reqParam.getDialogueId()));
        pollingUpdate.setUserId(reqParam.getUserId());
        DialogueResultV2VO dialogResult = algorithmChatV2ContentService.pollingUpdate(pollingUpdate);
        if (dialogResult == null) {
            log.error("二次流式对话处理，对话信息不存在 dto:{}", JsonUtil.toJson(innerDTO));
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_DIALOGUE_ID);
        }
        if (!(ChatStatusEnum.isChatSuccess(dialogResult.getStatus())
                || ChatStatusEnum.isChatStop(dialogResult.getStatus()))) {
            log.error("二次流式对话处理，对话信息不成功 dto:{}", JsonUtil.toJson(innerDTO));
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_DIALOGUE_ID);
        }
        // 设置对话信息
        innerDTO.setDialogResult(dialogResult);
        // 是否智能体对话类型
        boolean isIntelligentType = (null != dialogResult.getApplicationInfo()
                && ApplicationTypeEnum.isIntelligen(dialogResult.getApplicationInfo().getApplicationType()));
        // 获取输出意图
        DialogueIntentionOutput outputCommand = dialogResult.getOutputCommand();

        if (isIntelligentType && DialogueIntentionEnum.isTextToolIntention(outputCommand.getCommand())
                && DialogueIntentionSubEnum.isMeetingMail(outputCommand.getSubCommand())) {
            // 智能体类型对话-会议通知发邮件意图
            innerDTO.setGetOutContent(Boolean.TRUE);
            secondChatAddV2IntelligentService.runMeetingMailResult(innerDTO);
        } else if (isIntelligentType && DialogueIntentionEnum.isTextToolIntention(outputCommand.getCommand())
                && DialogueIntentionSubEnum.isAiPpt(outputCommand.getSubCommand())) {
            // 智能体类型对话-ppt生成意图
            secondChatAddV2IntelligentService.runAiPptResult(innerDTO);
        } else if (isIntelligentType && DialogueIntentionEnum.SEND_MAIL.getCode().equals(outputCommand.getCommand())) {
            // 发邮件意图
            innerDTO.setGetOutContent(Boolean.TRUE);
            secondChatAddV2IntelligentService.runSendMailAndAiPPtResult(innerDTO);
        } else if (DialogueIntentionEnum.SEND_MAIL.getCode().equals(outputCommand.getCommand())) {
            // 发邮件意图
            handleSendMailIntention(innerDTO);
        } else {
            // 其他为不允许二次流式对话的意图
            log.error("二次流式对话处理，当前对话意图不允许二次对话，reqParam:{}", JsonUtil.toJson(reqParam));
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_CONVERSATION_ID_INVALID_CODE);
        }
        // 智能体和存在邮件信息，需要先发一次FlowResultTypeEnum.MAIL
        ChatAddRespVO respVO = new ChatAddRespVO();
        if (null != innerDTO.getMailInfo()) {
            DialogueFlowResultVO flowResultVO = new DialogueFlowResultVO(0, FlowResultTypeEnum.MAIL, "", "");
            flowResultVO.setMailInfo(innerDTO.getMailInfo());
            if (CollUtil.isNotEmpty(innerDTO.getFileList())) {
                flowResultVO.setFileList(innerDTO.getFileList());
            }
            respVO.setFinishReason(ChatAddFlowStatusEnum.PROCESSING.getStatus());
            respVO.setFlowResult(flowResultVO);
            respVO.setOutputCommand(new DialogueIntentionDTO(outputCommand.getCommand(), outputCommand.getSubCommand(),
                    outputCommand.getArgumentMap()));
            // 先send一次
            innerDTO.getSseEmitterOperate().send(BaseResult.success(respVO));
        }

        if (Objects.nonNull(innerDTO.getOutContent())) {
            log.info("二次流式对话处理，获取输出内容，reqParam:{}", JsonUtil.toJson(reqParam));
            respVO.processingFlowResult(new DialogueFlowResultVO(1, FlowResultTypeEnum.TEXT_MODEL,
                    StringUtils.EMPTY, innerDTO.getOutContent()));
            respVO.setFinishReason(ChatAddFlowStatusEnum.STOP.getStatus());
            innerDTO.getSseEmitterOperate().sendAndComplete(BaseResult.success(respVO));
            return;
        }

        // 当前轮询结果
        DialogueResultV2VO thisResult = innerDTO.getDialogResult();

        // 继续执行大模型对话
        ChatAddReqDTO reqDTO = new ChatAddReqDTO();
        reqDTO.setDialogueInput(new DialogueInputInfoDTO());
        reqDTO.setUserId(thisResult.getUserId());
        ChatAddHandleDTO handlerDTO = new ChatAddHandleDTO(reqDTO, innerDTO.getSseEmitterOperate());
        respVO.setFlowResult(new DialogueFlowResultVO());
        handlerDTO.setRespVO(respVO);
        handlerDTO.setDialogueId(Long.valueOf(thisResult.getDialogueId()));
        handlerDTO.setSessionId(Long.valueOf(thisResult.getSessionId()));
        handlerDTO.setIntentionVO(DialogueIntentionVO.newMainIntention(thisResult.getOutputCommand().getCommand(),
                thisResult.getOutputCommand().getSubCommand()));
        // 监听器
        SseEventListener event = new SseEventListener(handlerDTO, null);
        // 设置当前的索引，下一次加1
        event.setSendIndex(0);
        // 设置不操作db
        event.setOperateDatabase(false);
        handleLargeTextModelCall(event, innerDTO);
    }

    /**
     * 处理发送邮件意图
     *
     * @param innerDTO 二次流式对话内部参数
     */
    private void handleSendMailIntention(SecondStreamChatAddV2InnerDTO innerDTO) {
        log.info("进入执行发邮件结果 dialogueId:{}", innerDTO.getDialogResult().getDialogueId());
        // 获取hbase结果
        AiTextResultEntity entity = aiTextResultRepository.getByRowKey(innerDTO.getReqParams().getUserId(),
                innerDTO.getReqParams().getDialogueId());
        AiTextResultRespParameters respParameters = null;
        try {
            // 获取对应结果
            respParameters = JSON.parseObject(entity.getRespParameters(), AiTextResultRespParameters.class);
        } catch (JSONException e) {
            log.warn("二次流式对话处理，JSON解析失败，hbaseRespParameters：{}，dto：{}",
                    null != entity ? entity.getRespParameters() : null, JsonUtil.toJson(innerDTO));
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_DIALOGUE_ID);
        }
        // hbase已经存在content的结果，直接返回
        if (null != respParameters && respParameters.getMailInfo() != null) {
            // v1对话
            innerDTO.setMailInfo(respParameters.getMailInfo());
        } else {
            // v2对话
            // 找到当前outputList，最后一个并且是resultType=com.zyhl.yun.api.outer.enums.chat.FlowResultTypeEnum.MAIL，就有mailInfo
            DialogueResultV2VO dialogResult = innerDTO.getDialogResult();
            List<DialogueFlowResult> outputList = dialogResult.getOutputList();
            if (CollUtil.isNotEmpty(outputList)) {
                // 获取最后一个并判断resultType是否为邮件
                DialogueFlowResult flowResult = outputList.get(outputList.size() - 1);
                if (FlowResultTypeEnum.MAIL.getType().equals(flowResult.getResultType())) {
                    innerDTO.setMailInfo(flowResult.getMailInfo());
                }
            }
        }

        if (null == innerDTO.getMailInfo()) {
            // 非发邮件意图，返回错误信息
            log.error("非发邮件意图，返回错误信息");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_INVALID_DIALOGUE_ID);
        }

        // 获取配置的模型编码
        innerDTO.setModelCode(secondStreamChatAddProperties.getModeCode());
        // 拼接提示词+用户query
        String dialogue = innerDTO.getDialogResult().getDialogueInputInfo().getDialogue();
        String template = secondStreamChatAddProperties.getPromptTemplate().replace(REPLACE_QUERY, dialogue);
        innerDTO.setQueryContent(template);
    }

    /**
     * 调用大文本模型
     *
     * @param event    流式监听参数
     * @param innerDTO 对话模板
     */
    private void handleLargeTextModelCall(SseEventListener event, SecondStreamChatAddV2InnerDTO innerDTO) {
        String code = innerDTO.getModelCode();
        // qps限制
        if (!qpslimitService.modelQpsLimit(code)) {
            log.info("二次流式对话处理 请求过多，qps限流，modelCode:{}", code);
            event.dialogueFail(ResultCodeEnum.ERROR_LIMITATION);
            return;
        }

        try {
            // 设置模型编码
            event.setModelCode(code);
            // 新的对话信息
            TextModelMessageDTO msgDTO = new TextModelMessageDTO();
            msgDTO.setRole(TextModelRoleEnum.USER.getName());
            msgDTO.setContent(innerDTO.getQueryContent());

            TextModelTextReqDTO req = new TextModelTextReqDTO();
            // 这里需要设置不联网搜索
            req.setEnableForceNetworkSearch(false);
            req.setTaskId(innerDTO.getDialogResult().getDialogueId());
            req.setUserId(innerDTO.getDialogResult().getUserId());
            req.setSessionId(innerDTO.getDialogResult().getSessionId());
            req.setMessageDtoList(Collections.singletonList(msgDTO));

            textModelExternalService.streamDialogue(code, req, event);
        } catch (YunAiBusinessException e) {
            log.error("二次流式对话处理 调用文本大模型异常:{}，对话id:{}", e.getMessage(), event.getDialogId(), e);
            event.dialogueFail(e.getExceptionEnum());
        } catch (Exception e) {
            log.error("二次流式对话处理 调用文本大模型异常:{}，对话id:{}", e.getMessage(), event.getDialogId(), e);
            event.dialogueFail(ResultCodeEnum.ERROR_SERVER_INTERNAL);
        }
    }

}
