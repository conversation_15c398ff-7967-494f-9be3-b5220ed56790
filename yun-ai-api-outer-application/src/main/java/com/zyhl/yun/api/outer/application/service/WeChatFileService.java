package com.zyhl.yun.api.outer.application.service;

import com.zyhl.yun.api.outer.application.dto.FileCompleteDTO;
import com.zyhl.yun.api.outer.application.dto.FileCreateDTO;
import com.zyhl.yun.api.outer.application.dto.GetSharpUploadUrlReqDTO;
import com.zyhl.yun.api.outer.domain.vo.FileCompleteVO;
import com.zyhl.yun.api.outer.domain.vo.FileCreateVO;
import com.zyhl.yun.api.outer.domain.vo.GetSharpUploadUrlReqVO;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description}
 * <p>
 *
 * <AUTHOR>
 * @since 4月17 2025
 */
public interface WeChatFileService {

  /**
   * 一阶段 文件上传
   * @param dto 请求参数
   * @return 文件上传
   */
  FileCreateVO createFile(FileCreateDTO dto);

  /**
   * 完成文件
   * @param dto 请求参数
   * @return 文件完成
   */
  FileCompleteVO completeFile(FileCompleteDTO dto);

  /**
   * 获取个人知识库文件分片上传地址接口
   * @param dto 请求参数
   * @return 返回结果
   */
  GetSharpUploadUrlReqVO getUploadUrl(GetSharpUploadUrlReqDTO dto);

  /**
   * 校验用户空间是否够用
   * @param userId 用户id
   * @param fileSize 待导入的文件大小
   */
  void checkOwnerDriveSize(String userId,Long fileSize);
}
