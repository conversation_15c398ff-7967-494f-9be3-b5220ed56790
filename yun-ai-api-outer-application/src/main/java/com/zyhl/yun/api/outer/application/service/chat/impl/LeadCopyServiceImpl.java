package com.zyhl.yun.api.outer.application.service.chat.impl;

import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

import com.zyhl.yun.api.outer.config.LeadCopyV2Properties;
import org.springframework.stereotype.Service;

import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.enums.UserBelongsPlatformEnum;
import com.zyhl.yun.api.outer.application.config.ChatTextToolBusinessConfig;
import com.zyhl.yun.api.outer.application.convertor.dto.dynamic.RecommendVoConvertor;
import com.zyhl.yun.api.outer.application.dto.ChatAddInnerDTO;
import com.zyhl.yun.api.outer.application.service.chat.LeadCopyService;
import com.zyhl.yun.api.outer.config.DialogueRecommendProperties;
import com.zyhl.yun.api.outer.config.LeadCopyProperties;
import com.zyhl.yun.api.outer.config.LeadCopyProperties.Copy;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.constants.CommonConstant;
import com.zyhl.yun.api.outer.domain.vo.IntentionRecommendVO;
import com.zyhl.yun.api.outer.domain.vo.LeadCopyVO;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.DialogueIntentionSubEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.search.LeadCopyTypeEnum;
import com.zyhl.yun.api.outer.util.IntentionUtils;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 引导文案对象接口实现类
 *
 * <AUTHOR>
 * @date 2024/6/3 01:16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LeadCopyServiceImpl implements LeadCopyService {

    private final SourceChannelsProperties channelsProperties;

    private final LeadCopyProperties copyProperties;

    private final LeadCopyV2Properties leadCopyV2Properties;

    private final DialogueRecommendProperties recommendProperties;

    private final RecommendVoConvertor recommendVOConvertor;

    /**
     * 获取引导文案对象VO
     *
     * @param intentionVO   意图结果对象
     * @param sourceChannel 渠道来源
     * @param inContent     对话输入文本内容
     * @return 引导文案对象VO
     */
    @Override
    public LeadCopyVO getLeadCopyVo(DialogueIntentionVO intentionVO, String sourceChannel, String inContent) {
    	//主意图编码
    	String intention = null;
    	//主意图
    	IntentionInfo mainIntention = DialogueIntentionVO.getMainIntention(intentionVO);
    	if(null != mainIntention) {
    		intention = mainIntention.getIntention();
    	}
    	// 非AI工具类意图、非创建笔记意图、非知识库意图、非文本工具意图 不处理
        if (!DialogueIntentionEnum.isAiToolIntention(intention)
                && !DialogueIntentionEnum.CREATE_NOTE.getCode().equals(intention)
                && !DialogueIntentionEnum.CREATE_VOICE_NOTE.getCode().equals(intention)
                && !DialogueIntentionEnum.KNOWLEDGE_ENTRANCE.getCode().equals(intention)
                && !DialogueIntentionEnum.TEXT_TOOL.getCode().equals(intention)) {
            return null;
        }

        // 意图枚举
        DialogueIntentionEnum intentionEnum = DialogueIntentionEnum.getByCodeOrDefault(intention);

        // 知识库入口意图
        if (DialogueIntentionEnum.KNOWLEDGE_ENTRANCE.getCode().equals(intention)) {
            LeadCopyProperties.Copy copy = copyProperties.getByInstruction(intentionEnum.getInstruction());
            Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();
			if (UserBelongsPlatformEnum.OSE.getBelongsPlatform().equals(belongsPlatform)) {
				// 旧底座，跳转到报名入口
				Copy copyOse = copyProperties.getByInstruction(intentionEnum.getInstruction() + "_" + belongsPlatform);
				if (null != copyOse) {
					copy = copyOse;
				}
			}
            return LeadCopyVO.getLeadCopyVo(copy, intentionEnum);
        }
        
		// 文本工具意图
		if (DialogueIntentionEnum.TEXT_TOOL.getCode().equals(intention)) {
			
			// 特殊处理文本工具意图的子意图-意图识别AI编程助手执行意图（没返回argumentMap的key=ARGUMENT_AI_CODER_FILE_NAME才是推荐，否则不推荐，是继续走大模型）
			if (DialogueIntentionSubEnum.isAiCoderExecute(mainIntention,
					ChatTextToolBusinessConfig.ARGUMENT_AI_CODER_FILE_NAME)) {
				return null;
			}
			
			// 特殊处理文本工具意图的子意图-意图识别AI会议纪要执行意图（没返回argumentMap才是推荐，否则不推荐，是继续走大模型）
			if (DialogueIntentionSubEnum.isAiMeetingMinutesExecute(mainIntention)) {
				return null;
			}
			
			if (DialogueIntentionSubEnum.allowTextToolExecute(mainIntention)
					&& CollUtil.isNotEmpty(mainIntention.getArgumentMap())) {
				if (null != mainIntention
						&& DialogueIntentionSubEnum.isAiPhotoSubIntentions(mainIntention.getSubIntention())) {
					log.info("识别文本工具意图-AI拍照识图，需要推荐leadCopy返回");
				} else {
					log.info("识别文本工具意图，参数不为空，是执行意图，无需要的执行意图，调用方法侧会设置为文本意图");
					return null;
				}
			}
			
			// 正常推荐逻辑
			Copy leadCopy = copyProperties.getTextToolByIntentionToolCallsList(mainIntention);
			if(null != leadCopy) {
				return LeadCopyVO.getLeadCopyVo(leadCopy, intentionEnum);
			}
		}

        // 意图结果不为妙云相机 返回跳转链接配置
        if (!DialogueIntentionEnum.CLOUD_CAMERA.equals(intentionEnum)) {
            return LeadCopyVO.getLeadCopyVo(copyProperties.getByInstruction(intentionEnum.getInstruction()), intentionEnum);
        }

        // 20241015 当前妙云相机/AI写真意图识别都是011，根据输入内容进行匹配返回
        // 判断是否匹配上妙云相机提示文案
        if (DialogueIntentionEnum.CLOUD_CAMERA.getInstruction().equals(copyProperties.getByKeyword(inContent))) {
            // 渠道来源 端内
            if (channelsProperties.isInner(sourceChannel)) {
                return LeadCopyVO.getLeadCopyVo(copyProperties.getByInstruction(CommonConstant.CLOUD_CAMERA_WITHIN), intentionEnum);
            }

            // 渠道来源 端外
            return LeadCopyVO.getLeadCopyVo(copyProperties.getByInstruction(CommonConstant.CLOUD_CAMERA_EXTERNAL), intentionEnum);
        }

        // 未匹配上妙云相机提示文案，默认返回AI写真提示文案
        // 渠道来源 端内
        if (channelsProperties.isInner(sourceChannel)) {
            return LeadCopyVO.getLeadCopyVo(copyProperties.getByInstruction(CommonConstant.AI_PHOTOGRAPHY_WITHIN), intentionEnum);
        }

        // 渠道来源 端外
        return LeadCopyVO.getLeadCopyVo(copyProperties.getByInstruction(CommonConstant.AI_PHOTOGRAPHY_EXTERNAL), intentionEnum);
    }

    /**
     * 获取引导文案对象VO（输入资源ID为空）
     *
     * @param intention     意图结果
     * @param sourceChannel 渠道来源
     * @param inResourceId  输入资源ID
     * @return 引导文案对象VO
     */
    @Override
    public LeadCopyVO getLeadCopyVo(DialogueIntentionVO intentionVO, String sourceChannel, String inResourceId, String inContent) {
    	String intention = DialogueIntentionVO.getMainIntentionCode(intentionVO);
    	// 输入资源ID为空
        if (IntentionUtils.returnLinkCondition(intention, inResourceId)) {
            return getLeadCopyVo(intentionVO, sourceChannel, inContent);
        }
        return null;
    }

    @Override
    public void leadCopyType5(ChatAddInnerDTO params) {

        // AI工具意图不返回推荐信息
        if (DialogueIntentionEnum.isAiToolIntention(params.getIntentionCode())) {
            return;
        }

        // 非图片资源类型或者输入资源id为空 不返回推荐信息
        Integer resourceType = params.getContent().getResourceType();
        boolean isImageOrDialogueId = ResourceTypeEnum.isImage(resourceType) || ResourceTypeEnum.isDialogueId(resourceType);
        boolean emptyResourceId = CharSequenceUtil.isBlank(params.getContent().getResourceId());
        if (!isImageOrDialogueId || emptyResourceId) {
            return;
        }

        // 获取推荐信息转换后返回
        List<DialogueRecommendProperties.Intention> intentionList = recommendProperties.getIntentionList();
        if (CollUtil.isEmpty(intentionList)) {
            return;
        }

        // 使用ThreadLocalRandom生成一个随机索引
        int randomIndex = ThreadLocalRandom.current().nextInt(intentionList.size());

        IntentionRecommendVO vo = recommendVOConvertor.toVO(intentionList.get(randomIndex));
        LeadCopyVO leadCopy = getLeadCopy(vo);

        params.getRespParams().setLeadCopy(leadCopy);
    }


    @Override
    public LeadCopyVO getLeadCopy(IntentionRecommendVO vo) {
        if (ObjectUtil.isNull(vo)) {
            return null;
        }
        return LeadCopyVO.builder()
                .type(LeadCopyTypeEnum.TYPE5.getCode())
                .intentionCommand(vo.getIntentionCommand())
                .promptCopy(vo.getCopy())
                .linkURL(vo.getLinkURL())
                .build();
    }

    @Override
    public LeadCopyVO getLeadCopy7(String instruction, String promptCopy) {
        if(CharSequenceUtil.isBlank(instruction)){
            return null;
        }

        LeadCopyV2Properties.Copy copy = leadCopyV2Properties.getByInstruction(instruction);
        if(ObjectUtil.isNull(copy)){
            return null;
        }

        return LeadCopyVO.builder()
                .type(LeadCopyTypeEnum.TYPE7.getCode())
                .promptCopy(CharSequenceUtil.isNotBlank(promptCopy) ? promptCopy : copy.getPromptCopy())
                .buttonCopy(copy.getButtonCopy())
                .linkURL(copy.getLinkURL())
                .imageURL(copy.getImageURL())
                .iconURL(copy.getIconURL())
                .build();
    }

}
