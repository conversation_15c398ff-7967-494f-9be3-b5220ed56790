package com.zyhl.yun.api.outer.application.service.impl;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.application.dto.CardInfoDTO;
import com.zyhl.yun.api.outer.application.service.CardOrcService;
import com.zyhl.yun.api.outer.application.vo.CardInfoVO;
import com.zyhl.yun.api.outer.domain.entity.ocr.CertificateOcrEntity;
import com.zyhl.yun.api.outer.domain.entity.ocr.CertificateOcrReqEntity;
import com.zyhl.yun.api.outer.domainservice.OuterLinkDomainService;
import com.zyhl.yun.api.outer.external.service.CertificateOcrService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.UUID;

import static com.zyhl.yun.api.outer.enums.ResultCodeEnum.ERROR_PARAMS;
import static com.zyhl.yun.api.outer.enums.ResultCodeEnum.FILE_URL_DOWN_FAIL;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CardOrcServiceImpl implements CardOrcService {

    @Resource
    private CertificateOcrService certificateOcrService;
    @Resource
    private OuterLinkDomainService outerLinkDomainService;

    private final static Integer TYPE_URL = 1;

    private final static Integer TYPE_BASE_64 =  2;

    /**
     * 获取卡片信息
     *
     * @param dto 卡片信息数据传输对象
     * @return 基础结果类
     */
    @Override
    public CardInfoVO getCardInfo(CardInfoDTO dto){
        // 如果 base64 和 fileUrl 都为空,则抛出参数异常
        if(StringUtils.isBlank(dto.getBase64()) && StringUtils.isBlank(dto.getFileUrl())) {
            throw new YunAiBusinessException(ERROR_PARAMS);
        }
        CardInfoVO cardInfoVO = new CardInfoVO();
        CertificateOcrEntity certificateOcrDTO = new CertificateOcrEntity();
        certificateOcrDTO.setRequestId(UUID.randomUUID().toString());
        certificateOcrDTO.setFileId(dto.getFileId());
        certificateOcrDTO.setBase64(dto.getBase64());
        certificateOcrDTO.setFileUrl(dto.getFileUrl());
        certificateOcrDTO.setSendType(dto.getSendType());

        // 如果传送类型为：1——url传送
        if (TYPE_URL.equals(dto.getSendType())){
            // 将文件URL转换为Base64编码
            String s = outerLinkDomainService.convertUrlToBase64(dto.getFileUrl());
            if(StringUtils.isBlank(s)){
                throw new YunAiBusinessException(FILE_URL_DOWN_FAIL);
            }
            certificateOcrDTO.setBase64(s);
            certificateOcrDTO.setSendType(TYPE_BASE_64);
            certificateOcrDTO.setFileUrl(null);
        }
        if(dto.getCardType() != null){
            certificateOcrDTO.setCardType(dto.getCardType());
        }
        if(dto.getCropState() != null){
            certificateOcrDTO.setCropState(dto.getCropState());
        }
        // 开始调用ocr接口
        CertificateOcrReqEntity certificateOcrReqEntity = certificateOcrService.certificateOcr(certificateOcrDTO);
        cardInfoVO.setFileId(certificateOcrReqEntity.getFileId());
        cardInfoVO.setCardInfo(certificateOcrReqEntity.getStructuralData());
        return cardInfoVO;
    }




}
