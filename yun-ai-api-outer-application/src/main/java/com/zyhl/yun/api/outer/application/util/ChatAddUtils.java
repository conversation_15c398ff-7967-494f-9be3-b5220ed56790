package com.zyhl.yun.api.outer.application.util;

import org.apache.commons.lang3.StringUtils;

import com.zyhl.hcy.commons.utils.JsonUtil;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONObject;

/**
 * 对话工具类
 * 
 * <AUTHOR>
 * @date 2025-02-26 16:03
 */
public class ChatAddUtils {

	private static final String ORDER_TIP = "orderTip";

	private static final String ORDER_TIP_OBJ = "orderTipObj";

	/**
	 * 获取指令，云邮orderTip，云盘orderTipObj.orderTip
	 * 
	 * @param extInfo
	 * @return
	 */
	public static String getOrderTip(String extInfo) {
		String orderTip = null;
		if (CharSequenceUtil.isNotEmpty(extInfo)) {
			JSONObject object = JsonUtil.parseObject(extInfo, JSONObject.class);
			// 云邮指令
			orderTip = String.valueOf(object.getOrDefault(ORDER_TIP, StringUtils.EMPTY));
			if (StringUtils.isEmpty(orderTip)) {
				// 云盘指令
				JSONObject orderTipObj = object.getJSONObject(ORDER_TIP_OBJ);
				if (null != orderTipObj) {
					orderTip = String.valueOf(orderTipObj.getOrDefault(ORDER_TIP, StringUtils.EMPTY));
				}
			}
		}
		return orderTip;
	}

}
