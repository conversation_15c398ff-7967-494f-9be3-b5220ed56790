package com.zyhl.yun.api.outer.application.service;

import com.zyhl.yun.api.outer.application.dto.PptTemplateDTO;
import com.zyhl.yun.api.outer.application.vo.AiPptTemplateInfoVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 阿里AI-PPT模板 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/23
 */
public interface PptTemplateService {

    /**
     * 查询AI-PPT模板列表接口
     *
     * @param dto
     * @return
     */
    Map<String, List<AiPptTemplateInfoVO>> pptTemplateList(PptTemplateDTO dto);
}
