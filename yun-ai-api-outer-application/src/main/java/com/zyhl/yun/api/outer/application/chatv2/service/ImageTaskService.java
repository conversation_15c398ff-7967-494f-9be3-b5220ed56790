package com.zyhl.yun.api.outer.application.chatv2.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.yun.ai.common.base.enums.YunAiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.client.centertask.vo.CenterTaskCreateVO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddHandleDTO;
import com.zyhl.yun.api.outer.application.chatv2.dto.ChatAddReqDTO;
import com.zyhl.yun.api.outer.application.dto.LastDialogueInfoDTO;
import com.zyhl.yun.api.outer.config.IntentionCompanyProperties;
import com.zyhl.yun.api.outer.domain.entity.centertask.CenterTaskCreateEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.ImageParamEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.TextParamEntity;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domainservice.BenefitNoDomainService;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ImageParamTypeEnum;
import com.zyhl.yun.api.outer.enums.ImageTransmissionTypeEnum;
import com.zyhl.yun.api.outer.enums.ResourceTypeEnum;
import com.zyhl.yun.api.outer.external.CenterTaskExternalService;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 对话任务管理器（2.0对话接口）
 *
 * <AUTHOR>
 */
public interface ImageTaskService {


    /**
     * 调创建任务接口生成任务
     *
     * @param handleDTO 输入处理DTO
     * @param intention 意图
     * @param taskId    任务id
     */
    CenterTaskCreateVO createImageTask(ChatAddHandleDTO handleDTO, String intention, Long taskId) throws YunAiBusinessException;


}
