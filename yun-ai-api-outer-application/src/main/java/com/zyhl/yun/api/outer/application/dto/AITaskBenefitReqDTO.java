package com.zyhl.yun.api.outer.application.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 算法任务权益扣费请求入参req
 *
 * <AUTHOR>
 * @Date 2024/05/16 09:50
 */
@Data
public class AITaskBenefitReqDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 用户id，如果有token就不需要传
	 */
	private String userId;

	/**
	 * 任务id
	 */
	@NotNull(message = "任务id不可为空")
	private String taskId;

	/**
	 * 渠道号
	 */
	@NotNull(message = "渠道号不可为空")
	private String channelId;
}
