
-- 修改 algorithm_user_knowledge_file 表
ALTER TABLE algorithm_user_knowledge_file
    MODIFY COLUMN file_type TINYINT NOT NULL COMMENT '文件类型:1-文件，2-目录',
    MODIFY COLUMN hash_name varchar(255) NULL COMMENT '文件哈希名',
    MODIFY COLUMN hash_value varchar(255) NULL COMMENT '文件哈希值';
ALTER TABLE algorithm_user_knowledge_file ADD COLUMN base_id BIGINT COMMENT '知识库id' AFTER user_id;
ALTER TABLE algorithm_user_knowledge_file ADD COLUMN parent_file_id VARCHAR(64)COMMENT '父目录id' AFTER base_id;
ALTER TABLE algorithm_user_knowledge_file ADD COLUMN parent_file_path varchar(4096)  NULL COMMENT 'from_resource_type= 0独立空间的父目录路径使用 / 拼接 目录ID用于遍历目录下面的文件以及子目录的文件from_resource_type=1 2 3 4 保存的是 algorithm_user_knowledge的folder_id';
ALTER TABLE algorithm_user_knowledge_file ADD COLUMN sort_type tinyint(4) NULL COMMENT '排序优先级 10:文件(独立空间)20:笔记30:邮件40:网址';
ALTER TABLE algorithm_user_knowledge_file ADD INDEX idx_file_id(file_id) COMMENT '知识库文件id索引';
ALTER TABLE algorithm_user_knowledge_file ADD INDEX idx_base_id_user_id(base_id, user_id) COMMENT '知识库ID和用户ID联合索引';


-- 修改 algorithm_user_knowledge_file_res 表
ALTER TABLE algorithm_user_knowledge_file_res ADD COLUMN base_id BIGINT COMMENT '知识库id' AFTER user_id;
ALTER TABLE algorithm_user_knowledge_file_res ADD COLUMN from_resource VARCHAR(2048) COMMENT 'from_resource_type=1时存储邮箱附件信息(JSON格式)' AFTER from_file_id;


-- 修改 algorithm_user_knowledge_file_task 表
ALTER TABLE algorithm_user_knowledge_file_task ADD COLUMN base_id BIGINT COMMENT '知识库id' AFTER user_id;
ALTER TABLE algorithm_user_knowledge_file_task ADD COLUMN from_resource_type TINYINT DEFAULT 0 COMMENT '来源文件资源类型：0-云盘个人云(默认)，1-邮件，2-笔记，3-网址，4-微信文件' AFTER base_id;
ALTER TABLE algorithm_user_knowledge_file_task ADD COLUMN from_file_id VARCHAR(64) COMMENT '来源文件ID：from_resource_type=1时存储邮件ID，from_resource_type=2时存储笔记ID' AFTER from_resource_type;
ALTER TABLE algorithm_user_knowledge_file_task ADD COLUMN upload_ids varchar(4096) NULL COMMENT '上传id 逗号分隔';


-- 修改 algorithm_knowledge_vector_task 表
ALTER TABLE algorithm_knowledge_vector_task ADD COLUMN base_id BIGINT COMMENT '知识库id' AFTER user_id;

-- 修改 algorithm_knowledge_vector_task_file 表
ALTER TABLE algorithm_knowledge_vector_task_file ADD COLUMN base_id BIGINT COMMENT '知识库id' AFTER task_id;

-- 修改 algorithm_knowledge_file_parse_task 表
ALTER TABLE algorithm_knowledge_file_parse_task ADD COLUMN base_id BIGINT COMMENT '知识库id' AFTER task_id;
ALTER TABLE algorithm_knowledge_file_parse_task MODIFY COLUMN file_key_id BIGINT(20) DEFAULT NULL COMMENT '用户资源表的ID';

-- 修改 algorithm_knowledge_file_parse_sub_task 表
ALTER TABLE algorithm_knowledge_file_parse_sub_task ADD COLUMN base_id BIGINT COMMENT '知识库id' AFTER task_id;

-- 创建 algorithm_user_knowledge_upload 表
CREATE TABLE `algorithm_user_knowledge_upload` (
   `id` bigint(20) NOT NULL COMMENT '上传任务ID',
   `user_id` varchar(64) NOT NULL COMMENT '分区字段（500个',
   `base_id` bigint(20) NOT NULL COMMENT '知识库id',
   `target_parent_file_id` varchar(64) DEFAULT NULL COMMENT '目标父目录ID，独立空间的目录ID(用于选择知识库目录上传文件的场景)',
   `parent_id` bigint(20) DEFAULT NULL COMMENT '来源父目录上传ID，遍历文件夹时，子文件夹和文件需要保存父文件夹的上传ID 用于判重',
   `parent_file_id` varchar(64) DEFAULT NULL COMMENT '来源父目录ID，使用的是个人云的目录ID',
   `file_id` varchar(64) DEFAULT NULL COMMENT 'from_resource_type = 0 4 5 个人云的文件id, from_resource_type = 1 邮件ID, from_resource_type = 2 笔记ID, from_resource_type = 3 空',
   `resource_type` tinyint(4) DEFAULT '0' COMMENT '0 云盘个人云（默认）, 1 邮件, 2 笔记, 3 网址, 4 微信文件, 5 本地文件 ',
   `resource` varchar(2048) DEFAULT NULL COMMENT 'from_resource_type = 3 则存储的是网址信息，json格式 {}, from_resource_type = 2 存储的是笔记类型(待定？？？) {\r\n    笔记类型，0代表普通笔记，4代表录音笔记，1,\r\n    2,\r\n    3是其他笔记\r\n}\r\n',
   `file_name` varchar(512) DEFAULT NULL COMMENT '文件名称 ',
   `owner_id` varchar(64) NOT NULL COMMENT '个人云 owner_id = user_id ',
   `owner_type` tinyint(4) NOT NULL COMMENT '业务类型： - 1 - 未知类型, 12 - 邮箱, 11 - ai 知识库, 1 - personal 个人云, 2 - group 圈子, 3 - shareGroup 共享群, 4 - family 家庭云, 10 - mount 挂载盘, 6 - note 笔记, 7 - cardpackage 卡包, 8 - system 系统空间, 9 - partner 合作空间, 5 - activity 活动空间 照片直播',
   `paas_code` varchar(20) NOT NULL COMMENT 'paas平台编码',
   `hash_name` varchar(255) DEFAULT NULL COMMENT '文件哈希名',
   `hash_value` varchar(255) DEFAULT NULL COMMENT '文件哈希值',
   `file_type` tinyint(4) NOT NULL COMMENT '文件类型: 1 - 文件，2 - 目录',
   `category` int(11) DEFAULT NULL COMMENT '文件 / 目录分类, 见字典定义 1 图片, 2 音频, 3 视频, 4 文档, 5 应用, 6 压缩文件, 0 其他, 100 普通目录',
   `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小',
   `extension` varchar(20) DEFAULT NULL COMMENT '文件后缀',
   `file_updated_at` datetime DEFAULT NULL COMMENT '文件修改时间',
   `file_created_at` datetime DEFAULT NULL COMMENT '文件创建时间',
   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   `del_flag` tinyint(2) NOT NULL DEFAULT '0' COMMENT '删除标识，0--正常；1--已删除；2--删除中 ',
   `upload_status` tinyint(4) DEFAULT '0' COMMENT '默认 0 未处理, 1 处理中（云盘文件导入使用）, 2 成功（文件夹下面没有文件也是成功状态）, 3 失败',
   `result_code` varchar(255) DEFAULT NULL COMMENT '0000 成功, 其他则错误码',
   `result_msg` varchar(1024) DEFAULT NULL COMMENT '记录错误信息',
   `target_file_id` varchar(64) DEFAULT NULL COMMENT '独立空间的文件ID，当创建独立空间的目录或文件后记录下',
   `target_parent_file_path` varchar(4096) DEFAULT NULL COMMENT '目标父目录PATH，独立空间的目录ID(用于选择知识库目录上传文件的场景)',
   PRIMARY KEY(id),
   KEY idx_user_id(user_id),
   KEY idx_base_user(base_id, user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT=' 个人知识库文件上传表 ';


-- 创建 algorithm_user_knowledge 表
CREATE TABLE `algorithm_user_knowledge` (
    `id` bigint(20) NOT NULL COMMENT '主键;知识库ID',
    `user_id` varchar(255) NOT NULL COMMENT '用户id;分区字段（100个）',
    `selected` tinyint(2) DEFAULT '0' COMMENT '用户是否选择知识库 选择，1--已选择；0--未选择；',
    `owner_id` varchar(255) DEFAULT NULL COMMENT '属主ID;用户Id/家庭Id/群组Id/圈子Id，根据ownerType界定',
    `folder_id` varchar(64) DEFAULT NULL COMMENT '知识库目录ID；知识库在独立空间的目录ID',
    `owner_type` tinyint(4) DEFAULT NULL COMMENT '业务类型',
    `profile_photo` varchar(256) DEFAULT NULL COMMENT '头像',
    `name` varchar(64) NOT NULL COMMENT '知识库名称',
    `description` varchar(255) DEFAULT NULL COMMENT '描述',
    `open_level` tinyint(2) DEFAULT '0' COMMENT '公开/私密；0私密（默认） 1 公开',
    `del_flag` tinyint(2) DEFAULT NULL COMMENT '删除标识；0--正常；1--已删除',
    `created_by` varchar(255) DEFAULT NULL COMMENT '创建人Id',
    `updated_by` varchar(255) DEFAULT NULL COMMENT '更新人Id',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_time` datetime NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) /*T![clustered_index] CLUSTERED */,
    KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='用户知识库表';


-- 创建 algorithm_user_knowledge_invite 表
CREATE TABLE algorithm_user_knowledge_invite(
    id BIGINT(20)NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    user_id VARCHAR(255)NOT NULL COMMENT '加入人ID',
    invite_user_id VARCHAR(255)NOT NULL COMMENT '邀请人ID',
    knowledge_id BIGINT(20)NOT NULL COMMENT '分享的知识库ID',
    selected TINYINT(2)DEFAULT 0 COMMENT '选择状态：1-已选择，0-未选择',
    status TINYINT DEFAULT 1 COMMENT '状态：0-停用，1-启用(默认)',
    expire_time DATETIME DEFAULT NULL COMMENT '分享库过期时间(空表示永久)',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY(id),
    KEY idx_user_id(user_id),
    KEY idx_knowledge_id(knowledge_id)
)ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '知识库邀请表';


