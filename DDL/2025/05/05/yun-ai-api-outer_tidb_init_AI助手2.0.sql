alter table algorithm_chat_content modify  `prompt` varchar(4096) DEFAULT NULL COMMENT '提示词，比如：总结概要';

-- 【增加字段】对话表-子意图编码
ALTER TABLE `algorithm_chat_content` ADD `sub_tools_command` varchar(255) NOT NULL DEFAULT '0' COMMENT '工具指令;对接意图指令-子意图';

-- 【增加字段】会话信息表-图标类型、图标子类型
alter table algorithm_chat_message add column icon_type varchar(32) default '100' comment '图标类型' after enable_star;
alter table algorithm_chat_message add column sub_icon_type varchar(32) default '0' comment '图标子类型' after icon_type;

-- 【新增字段】任务对话能力表-父任务id
ALTER TABLE `algorithm_task_ai_ability` ADD COLUMN `parent_task_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '父任务id，默认0为主任务' AFTER `id`;


-- 【新增表】知识库对话配置表
create table algorithm_knowledge_dialogue_config(
    id BIGINT NOT NULL COMMENT '主键ID',
    user_id VARCHAR(255) COMMENT '用户ID',
    type int COMMENT '参数类型：1-召回（RecallConfig），2-重排（RerankConfig），3-对话（DialogueConfig）',
    config_json text COMMENT '参数类型对应的配置，全量json格式',
    del_flag TINYINT DEFAULT 0 COMMENT '删除标识：0-正常，1-删除',
    create_time datetime comment '创建时间',
    update_time datetime comment '更新时间',
    PRIMARY KEY(id),
    KEY `idx_user_id` (`user_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '知识库对话配置表';