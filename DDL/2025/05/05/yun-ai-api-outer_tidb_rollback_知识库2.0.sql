
-- 【回滚】 algorithm_user_knowledge_file 表
ALTER TABLE algorithm_user_knowledge_file drop COLUMN base_id;
ALTER TABLE algorithm_user_knowledge_file drop COLUMN parent_file_id;
ALTER TABLE algorithm_user_knowledge_file drop COLUMN parent_file_path;
ALTER TABLE algorithm_user_knowledge_file drop COLUMN sort_type;
ALTER TABLE algorithm_user_knowledge_file drop INDEX idx_file_id;
ALTER TABLE algorithm_user_knowledge_file drop INDEX idx_base_id_user_id;


-- 【回滚】 algorithm_user_knowledge_file_res 表
ALTER TABLE algorithm_user_knowledge_file_res drop COLUMN base_id;
ALTER TABLE algorithm_user_knowledge_file_res drop COLUMN from_resource;


-- 【回滚】 algorithm_user_knowledge_file_task 表
ALTER TABLE algorithm_user_knowledge_file_task drop COLUMN base_id;
ALTER TABLE algorithm_user_knowledge_file_task drop COLUMN from_resource_type;
ALTER TABLE algorithm_user_knowledge_file_task drop COLUMN from_file_id;
ALTER TABLE algorithm_user_knowledge_file_task drop COLUMN upload_ids;


-- 【回滚】 algorithm_knowledge_vector_task 表
ALTER TABLE algorithm_knowledge_vector_task drop COLUMN base_id;

-- 【回滚】 algorithm_knowledge_vector_task_file 表
ALTER TABLE algorithm_knowledge_vector_task_file drop COLUMN base_id;

-- 【回滚】 algorithm_knowledge_file_parse_task 表
ALTER TABLE algorithm_knowledge_file_parse_task drop COLUMN base_id;

-- 【回滚】 algorithm_knowledge_file_parse_sub_task 表
ALTER TABLE algorithm_knowledge_file_parse_sub_task drop COLUMN base_id;

-- 【回滚】 algorithm_user_knowledge_upload 表
drop TABLE `algorithm_user_knowledge_upload`;


-- 【回滚】 algorithm_user_knowledge 表
drop TABLE `algorithm_user_knowledge` ;


-- 【回滚】 algorithm_user_knowledge_invite 表
drop TABLE algorithm_user_knowledge_invite;


