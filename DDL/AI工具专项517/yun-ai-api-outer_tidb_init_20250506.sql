-- 对话内容表增加输出推荐（中部）字段
ALTER TABLE `algorithm_chat_content`
ADD COLUMN `middle_recommend_info` text DEFAULT NULL COMMENT '对话结果推荐信息（中部）' after recommend_info;

-- 对话任务表增加删除标识和子意图
ALTER TABLE algorithm_task_ai_ability 
ADD del_flag INT DEFAULT 0  COMMENT '删除标记：0正常，1 已删除';

ALTER TABLE algorithm_task_ai_ability 
ADD sub_algorithm_code VARCHAR(255) NULL COMMENT '子意图编码';
    
-- 新增提示词（云邮使用）
INSERT INTO `algorithm_ai_prompt_template`(`id`, `prompt_key`, `prompt_name`,`prompt_template`) VALUES (1816790073346232427, 'YUN_MAIL_AI_EDIT_SUMMARY', 'AI编辑-首次进入针对文档解析总结的提示词', '请根据以下要求解析内容[文档内容]，生成简洁的总结输出。
1. 判断 [文档内容]：
   - 若 [文档内容] 为完整文档（doc, ppt, img格式），则对[文档内容]进行解析，解析后进行核心信息提炼。
   - 若 [文档内容] 为NONE，将 [用户输入]作为[文档内容]。
2. 首句"已完成内容解析，核心内容包括：[核心内容]
3. 根据文档提取要点，用简短列表方式列出文档关键信息
4. 结尾：补充引导用户提要求的描述，"如果您需要针对文档进行总结、扩展、回复邮件等其他操作，请告诉我，我会帮您进一步完善。"  
5. 硬性规范：  
   - 尽量简洁，不超过100字左右  
   - 保留中英专业术语
   - 风格简洁、中立的语气
文档内容: {documentContent}
用户输入: {userInput}'); 

INSERT INTO `algorithm_ai_prompt_template`(`id`, `prompt_key`, `prompt_name`,`prompt_template`) VALUES (1816790073346232428, 'YUN_MAIL_AI_EDIT_MODEL_PROCESS', 'AI编辑-模型处理提示词', '<!-- 提示词开始 -->
作为专业的内容生成AI，请严格按以下步骤处理用户指令：

**处理流程：**
1. 分析用户输入[用户输入]，识别核心需求与潜在意图
2. 根据用户意图确定最佳处理策略
3. 根据文档[文档内容]、用户意图以及处理策略，确定内容生成方向
4. 按三段式结构组织输出内容（包含处理思路、内容标题、AI优化内容）

**输出要求：**
请用markdown格式输出，并包含以下三个带标识的内容块：

<!-- segment:processing -->
[用50-100字详细阐述处理思路，需包含：
     - 首先分析用户需求和附件内容，提炼关键信息
     - 确定编辑或生成策略（如摘要、续写、回信等）
     - 优化建议与改进方向
     - 结果确认输出
    格式示例：“我将对(对象)进行(处理方法)，通过(具体技术)实现(改进目标)。建议后续可以(专业建议)。以下是为您优化的结果：” 
]

<!-- segment:title -->
# [生成准确反映核心内容的标题，保持简洁，不超过15字]

<!-- segment:content -->
[根据处理思路（关键信息、处理策略），直接输出最终生成内容，保持专业准确，无需任何附加说明]
用户输入： {userInput}
文档内容: {documentContent}
<!-- 提示词结束 -->'); 

-- 新增【用户任务评价结果表】
CREATE TABLE `algorithm_task_comment`
(
    `id`              BIGINT ( 20 ) NOT NULL COMMENT '主键',
    `user_id`         VARCHAR(64) NOT NULL COMMENT '用户id',
    `task_id`         BIGINT ( 20 ) NOT NULL COMMENT '任务id',
    `module`          VARCHAR(64)          DEFAULT NULL COMMENT '模块编码（子类型）|all:针对整个任务的评价，doc-summary:文档总结，doc-mindmap:文档脑图，doc-outline:文档大纲',
    `model_type`      VARCHAR(64)          DEFAULT NULL COMMENT '模型类型 qwen：通义千问，xfyun：讯飞星火大',
    `like_comment`    TINYINT ( 2 ) DEFAULT NULL COMMENT '是否喜欢 0:不喜欢，1:喜欢',
    `default_comment` VARCHAR(512)         DEFAULT NULL COMMENT '默认评论',
    `custom_comment`  VARCHAR(1024)        DEFAULT NULL COMMENT '用户自定义评论',
    `create_time`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY               `idx_user_id_task_id` ( `user_id`, `task_id` )
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'AI用户任务评价结果表';


ALTER TABLE `algorithm_chat_content` 
MODIFY COLUMN `out_resource_id` varchar(2048) NULL DEFAULT NULL COMMENT '输出资源ID;（笔记/邮件/图片ID/对话ID；纯文本时为空）' AFTER `out_content`;