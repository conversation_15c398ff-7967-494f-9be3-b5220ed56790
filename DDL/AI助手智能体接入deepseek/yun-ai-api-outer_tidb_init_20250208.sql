alter table `algorithm_chat_application_agent` add column `supplier_type` varchar(64) DEFAULT NULL comment '厂商编码，用于路由不同厂商的智能体 0 自研 2 阿里';
ALTER TABLE `algorithm_chat_application_agent` MODIFY COLUMN `type_relation_id` varchar(64) NULL DEFAULT NULL COMMENT '应用关联id,supplier_type=阿里，指通义星尘的角色id；supplier_type=自研，指模型名称（例如deepseek r1 7b 或 deepseek r1 671b）' AFTER `id`;
update algorithm_chat_application_agent set supplier_type=2;

INSERT INTO `algorithm_chat_application_agent` (`id`, `type_relation_id`, `type`, `title`, `avatar_url`, `opening_line`, `guid_text`, `tab_label`, `tab_label_en`, `sort`, `create_time`, `update_time`, `supplier_type`) 
VALUES (3000000001, 'deepseek_r1_7b', 'intelligent', 'DeepSeek R1 极速版', 'https://yun.mcloud.139.com/aiassistant/static/application/icon/deepseek.png', 'DeepSeek R1 极速版来啦！极速响应，助你秒速攻克各类推理任务，有难题需要解决吗？欢迎随时找我', '响应快如闪电，推理秒出，数学难题、逻辑推理轻松拿下，助你高效前行！', '助手', '助手', 0, '2025-02-06 15:00:00', '2025-02-06 15:00:00', '0');
INSERT INTO `algorithm_chat_application_agent` (`id`, `type_relation_id`, `type`, `title`, `avatar_url`, `opening_line`, `guid_text`, `tab_label`, `tab_label_en`, `sort`, `create_time`, `update_time`, `supplier_type`) 
VALUES (3000000002, 'deepseek_r1_671b', 'intelligent', 'DeepSeek R1 满血版', 'https://yun.mcloud.139.com/aiassistant/static/application/icon/deepseek.png', 'DeepSeek R1 满血版登场！推理速度超快，能全方位应对各类复杂任务，深度思考问题，为你高效提供解决方案', '重磅来袭！数学、代码、逻辑难题轻松破解，全面升级深度思考，助你效率飙升', '助手', '助手', 0, '2025-02-06 15:00:00', '2025-02-06 15:00:00', '0');
