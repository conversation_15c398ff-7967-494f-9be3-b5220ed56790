-- 重命名旧表
rename table algorithm_chat_comment to algorithm_chat_comment_temp20240516;
-- 新建会话评价结果表
CREATE TABLE `algorithm_chat_comment` (
	`id` BIGINT NOT NULL COMMENT '主键',
	`user_id` VARCHAR(64) NOT NULL COMMENT '用户id',
	`dialogue_id` bigint NOT NULL COMMENT '任务id',
	`session_id` bigint NOT NULL COMMENT '会话id',
	`model_type` VARCHAR(16) COMMENT '模型类型 qwen：通义千问，xfyun：讯飞星火大',
	`like_comment` tinyint(2) COMMENT '是否喜欢 0:不喜欢，1:喜欢',
	`default_comment` VARCHAR(512) COMMENT '默认评论',
	`custom_comment` VARCHAR(1024) COMMENT '用户自定义评论',
	`create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
	PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='AI用户评价结果表';
-- 旧表数据迁移到新表
insert into algorithm_chat_comment(id,user_id,dialogue_id,session_id,model_type,like_comment,default_comment,custom_comment,create_time,update_time) 
select id,user_id,dialogue_id,session_id,model_type,like_comment,default_comment,custom_comment,create_time,update_time from algorithm_chat_comment_temp20240516;
-- 添加索引
alter table algorithm_chat_comment add index idx_user_id_dialogue_id (`user_id`,`dialogue_id`);


-- 重命名旧表
rename table algorithm_chat_config to algorithm_chat_config_temp20240516;
-- 新建会话设置表
CREATE TABLE `algorithm_chat_config` (
	 `id` BIGINT NOT NULL COMMENT '主键',
	 `user_id` VARCHAR(64) NOT NULL COMMENT '用户id',
	 `model_type` VARCHAR(16) COMMENT '模型类型 qwen：通义千问，xfyun：讯飞星火大',
	 `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	 `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
	 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='会话配置表';
-- 旧表数据迁移到新表
insert into algorithm_chat_config(id,user_id,model_type,create_time,update_time) 
select id,user_id,model_type,create_time,update_time from algorithm_chat_config_temp20240516;
-- 添加索引
alter table algorithm_chat_config add index idx_user_id (`user_id`);


alter table algorithm_chat_content modify column `task_id` bigint  NULL DEFAULT NULL COMMENT '任务id';
alter table algorithm_chat_content drop index idx_id_user_id;
alter table algorithm_chat_content drop index idx_user_id_business_type;


alter table algorithm_chat_message drop index idx_id_user_id;


-- AI工具弹窗文案更新
UPDATE `t_ai_pop_up_protocol` 
SET
`protocol_url` = 'https://caiyun.feixin.10086.cn:7071/portal/templateView/initve.html?id=2462&marketName=hcy_yhxy',
`update_time` = NOW()
WHERE `id` = 1124311288637947905;


alter table algorithm_task_ai_ability modify `resp_param` varchar(4096)  NULL DEFAULT '' COMMENT '响应参数';