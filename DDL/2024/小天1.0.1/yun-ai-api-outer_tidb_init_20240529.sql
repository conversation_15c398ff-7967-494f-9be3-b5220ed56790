-- AI迁移表
create table algorithm_ai_migration(
                                       id bigint,
                                       user_id varchar(64) not null comment '用户id',
                                       phone varchar(32) comment '手机号码',
                                       device varchar(128) comment '设备',
                                       status tinyint(4) not null default 1 comment '状态：1已报名（默认)，2已迁移，3已计算完成（图片向量化）',
                                       belongs_platform int(2) not null comment '所属底座',
                                       create_time datetime default now() comment '创建时间',
                                       update_time datetime default now() comment '更新时间',
                                       primary key (`id`)
) COMMENT = 'AI迁移表';

alter table algorithm_ai_migration add unique index idx_uni_user_id (`user_id`);