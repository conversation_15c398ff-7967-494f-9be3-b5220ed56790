
alter table algorithm_chat_content add column scene_tag varchar(32) default '' comment '业务场景标识：app_writemail_ai（邮箱APP-写信AI）';
alter table algorithm_chat_content add column command_type tinyint(4) default '1' comment '命令类型：1--普通命令（默认），2--自动命令';

alter table algorithm_chat_content add `recommend_info` text DEFAULT NULL COMMENT '对话结果推荐信息（json格式）' after ext_info;

ALTER TABLE algorithm_chat_content MODIFY COLUMN `resource_type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '资源类型;0无,1邮件,2笔记,3图片,4对话ID';

ALTER TABLE algorithm_chat_content MODIFY COLUMN `in_resource_id` varchar(64) DEFAULT NULL COMMENT '输入资源ID;（笔记/邮件/图片ID/对话ID；纯文本时为空）';

ALTER TABLE algorithm_chat_content MODIFY COLUMN `out_resource_id` varchar(64) DEFAULT NULL COMMENT '输出资源ID;（笔记/邮件/图片ID/对话ID；纯文本时为空）';

ALTER TABLE algorithm_chat_content MODIFY COLUMN `prompt` varchar(2048) DEFAULT NULL COMMENT '提示词，比如：总结概要';