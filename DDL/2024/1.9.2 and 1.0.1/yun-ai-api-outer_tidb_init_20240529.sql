-- AI工具权益扣费相关
ALTER TABLE algorithm_task_ai_ability
 ADD `fee_type` TINYINT(2) DEFAULT '0'
 COMMENT '付费类型：0不需要付费；1需要付费；';
 
ALTER TABLE algorithm_task_ai_ability
 ADD `fee_paid_status` TINYINT(2) DEFAULT '-1'
 COMMENT '付费扣费状态：-1：不扣费（任务不涉及扣费流程设置-1）；0：未扣费；1：已扣费；';
 
ALTER TABLE algorithm_task_ai_ability
 ADD `file_expired_status` TINYINT(2) DEFAULT '0'
 COMMENT '文件过期状态：0未过期；1已过期；';


-- AI迁移表
create table algorithm_ai_migration(
                                       id bigint,
                                       user_id varchar(64) not null comment '用户id',
                                       phone varchar(32) comment '手机号码',
                                       device varchar(128) comment '设备',
                                       status tinyint(4) not null default 1 comment '状态：1已报名（默认)，2已迁移，3已计算完成（图片向量化）',
                                       belongs_platform int(2) not null comment '所属底座',
                                       create_time datetime default now() comment '创建时间',
                                       update_time datetime default now() comment '更新时间',
                                       primary key (`id`)
) COMMENT = 'AI迁移表';

alter table algorithm_ai_migration add unique index idx_uni_user_id (`user_id`);