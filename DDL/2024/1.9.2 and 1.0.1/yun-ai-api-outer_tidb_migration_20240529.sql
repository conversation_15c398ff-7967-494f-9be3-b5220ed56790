-- 智能相册报名数据迁移

-- 1、创建临时表
create table algorithm_ai_register_temp_2(
     id bigint primary key auto_increment,
     user_id varchar(64) comment '用户id',
     exist_status tinyint default 0 comment '存在状态：0-不存在，1-存在'
) AUTO_INCREMENT = 1000000000000000001 comment '智能相册开关数据迁移临时表，20240528创建，只使用一次，过后可删除';
alter table algorithm_ai_register_temp_2 add unique index idx_uni_user_id (`user_id`);

-- 2、执行插入数据，DBA操作，把需要迁移的数据导入临时表：algorithm_ai_register_temp_2
-- 相册（10.27.57.3:8989/ALBUM_SAAS_PRO）
SELECT
    USER_ID
FROM
    PHOTO_GLOBAL_CONFIG
WHERE
        AI_ENABLE = 1;
AND MIGRATE_TIME <= to_date('2024-05-18 00:00:00', 'yyyy/mm/dd hh24:mi:ss')
AND MIGRATION_MARK = 1;

-- 3、更新存在状态
update algorithm_ai_register_temp_2 temp, algorithm_ai_register register
set temp.exist_status = 1
where temp.user_id = register.user_id and register.business_type = 2;

-- 4、插入报名表
insert into algorithm_ai_register (id, user_id,belongs_platform,business_type,factory_type,algorithm_group_code,create_time,update_time,status)
select id, user_id, 0, 2, 6, 1, now(), now(), 0 from algorithm_ai_register_temp_2 where exist_status = 0;

-- 5、校验
select user_id, count(1) from algorithm_ai_register where business_type = 2 group by user_id having count(1) > 1;
select count(1) from algorithm_ai_register_temp_2 where exist_status = 0;
select count(1) from algorithm_ai_register where id <= (select max(id) from algorithm_ai_register_temp_2) and date(create_time) = date(now());

-- 开发环境数据：
-- 插入50万条数据到临时表，耗时：65s
-- 更新临时表存在状态，耗时：5s（algorithm_ai_register表开发环境50万，到生产环境150万的数据时间预计20s）
-- 50万条数据插入到报名表，耗时：12s