-- AI报名表
CREATE TABLE algorithm_ai_register (
   `id` BIGINT COMMENT 'id',
   `user_id` VARCHAR ( 32 ) COMMENT '用户id，100个分区',
   `belongs_platform` INT ( 2 ) COMMENT '所属底座',
   `business_type` INT ( 2 ) COMMENT '业务类型：0-AI工具，1-AI助手，2-智能相册',
   `module` INT ( 2 ) COMMENT '模型枚举，AI工具有模块划分',
   `module_name` VARCHAR ( 32 ) COMMENT '模型名称',
   `dir_id` VARCHAR ( 64 ) COMMENT '目录id',
   `path` VARCHAR ( 256 ) COMMENT '目录path',
   `factory_type` INT ( 2 ) COMMENT '厂商类型，通过厂商类型判断走那个搜索平台',
   `algorithm_group_code` INT ( 2 ) COMMENT '业务算法组编码，根据算法组执行那些算法：1 华为 - 图片元数据提取任务算法组 （目前只支持，2 彩讯 - 图片元数据提取任务算法组',
   `create_time` datetime COMMENT '创建时间',
   `update_time` datetime COMMENT '更新时间',
   PRIMARY KEY ( `id` ),
   KEY `algorithm_ai_register_user_id_IDX` ( `user_id` )
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'AI报名表' PARTITION BY key ( `user_id` ) PARTITIONS 100;

-- 对话应用类型信息表
CREATE TABLE algorithm_chat_application_agent (
   `id` BIGINT COMMENT '主键 id,雪花id',
   `type_relation_id` VARCHAR ( 64 ) COMMENT '应用关联id,例如通义星尘的角色id',
   `type` VARCHAR ( 64 ) COMMENT '应用类型，使用英文简称',
   `title` VARCHAR ( 64 ) COMMENT '应用标题',
   `avatar_url` VARCHAR ( 255 ) COMMENT '应用头像',
   `opening_line` VARCHAR ( 255 ) COMMENT '开场白',
   `guid_text` VARCHAR ( 255 ) COMMENT '引导语',
   `tab_label` VARCHAR ( 255 ) COMMENT 'tab标签名称，多个以英文逗号分隔',
   `tab_label_en` VARCHAR ( 255 ) COMMENT 'tab标签英文名称，多个以英文逗号分隔',
   `sort` TINYINT ( 2 ) COMMENT '值越小，优先级越大',
   `create_time` datetime COMMENT '创建时间',
   `update_time` datetime COMMENT '更新时间',
   PRIMARY KEY ( `id` )
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '对话应用类型信息表';

-- 历史对话
ALTER TABLE algorithm_chat_content
 ADD `business_type` varchar(32) DEFAULT 'e-139mail-app' NOT NULL
 COMMENT '业务类型，由渠道号映射' AFTER user_id;

ALTER TABLE algorithm_chat_content
 ADD `application_type` varchar(32) DEFAULT 'chat'
 COMMENT '应用类型:普通对话(chat); 智能体对话(intelligent); ' AFTER user_id;

ALTER TABLE algorithm_chat_content
 ADD `application_id` bigint(20) DEFAULT 0
 COMMENT '应用id' AFTER user_id;

ALTER TABLE algorithm_chat_content
    ADD INDEX idx_user_id_business_type (user_id, business_type);


-- 历史会话
ALTER TABLE algorithm_chat_message
 ADD `business_type` varchar(32) DEFAULT 'e-139mail-app' NOT NULL
 COMMENT '业务类型，由渠道号映射' AFTER user_id;

ALTER TABLE algorithm_chat_message
 ADD `application_type` varchar(32) DEFAULT 'chat'
 COMMENT '应用类型:普通对话(chat); 智能体对话(intelligent); ' AFTER user_id;

ALTER TABLE algorithm_chat_message
 ADD `application_id` bigint(20) DEFAULT 0
 COMMENT '应用id' AFTER user_id;

ALTER TABLE algorithm_chat_message
    ADD INDEX idx_user_id_business_type (user_id, business_type);

-- 数据初始化
update algorithm_chat_content
set business_type = case source_channel
                        when '10102' then 'e-mcloud-app'
                        when '10104' then 'e-139mail-app'
                        when '10106' then 'e-139mail-app'
                        when '10108' then 'e-mcloud-app'
                        when '10109' then 'e-mcloud-app'
                        when '10110' then 'e-Cloudphone'
                        when '10112' then 'e-mcloud-pc'
                        when '10114' then 'e-China Mobile Hall'
                        when '10116' then 'e-Mobile office'
                        when '10118' then 'e-Cloudphone EE'
                        when '10160' then 'e-test'
                        when '101' then 'c-mcloud-app'
                        when '102' then 'c-mcloud-app'
                        when '202' then 'c-139mail-app'
                        when '401' then 'c-mcloud-app'
                        when '400' then 'c-test'
                        else 'e-139mail-app' end;

update algorithm_chat_message a, algorithm_chat_content b
set a.business_type = b.business_type
where a.id = b.session_id;
