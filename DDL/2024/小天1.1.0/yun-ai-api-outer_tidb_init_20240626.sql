ALTER TABLE `algorithm_task_ai_ability` 
ADD COLUMN `action_type` tinyint(2) NULL DEFAULT 0 COMMENT '执行方式 0串行 1并行',
ADD COLUMN `in_audit_status` tinyint(2) NULL DEFAULT -1 COMMENT '输入内容审核状态 默认 -1  0送审中 1未通过 2通过',
ADD COLUMN `out_audit_status` tinyint(2) NULL DEFAULT -1 COMMENT '输出内容审核状态 默认 -1  0送审中 1未通过 2通过',
ADD COLUMN `in_audit_result` varchar(2048) NULL DEFAULT NULL COMMENT '输入送审结果 json格式',
ADD COLUMN `out_audit_result` varchar(2048) NULL DEFAULT NULL COMMENT '输出送审结果 json格式';

-- 增加渠道索引，用于定时任务
ALTER TABLE algorithm_task_ai_ability
    ADD INDEX idx_ai_ability_channel (source_channel);
    
-- 更新智能体开场白和引导语
update algorithm_chat_application_agent
set opening_line='您好，我是心理陪伴型AI。✨我期望能够成为您的朋友🤔'
where id=2000000031;

update algorithm_chat_application_agent
set guid_text='我是一位资深的历史教师，具备深厚的历史知识背景和丰富的教学经验。精通世界史、中国史等多个历史领域，擅长将复杂的历史事件和背景通俗化。'
where id=2000000039;

update algorithm_chat_application_agent
set opening_line='我是文思泉，无论是创意写作、学术论文还是日常记录，我都能帮助您激发灵感、完善思路。',
guid_text='我是一位经验丰富的写作灵感助理，擅长帮助用户激发创意和完善写作思路。熟悉文学创作、学术写作、商业文案等多种写作形式。'
where id=2000000040;

update algorithm_chat_application_agent
set opening_line='我是考神张教授，拥有多年的出卷和评卷经验，助您轻松备考。',
guid_text='我是考神张教授，具备多年出卷和评卷经验，熟悉教育行业的各类考试流程和评估标准。擅长自动出题、题目难度控制和题目类型设计，精通教育数据挖掘和学习行为分析。'
where id=2000000041;

update algorithm_chat_application_agent
set guid_text='我是一名有十年工作经验的HR，专注于模拟面试和答题辅导。熟悉企业招聘流程和面试技巧，能够提供详细的面试准备建议和常见问题解答。'
where id=2000000042;

update algorithm_chat_application_agent
set guid_text='陆仟，你的男友，一个INFP的文弱少年，喜欢安静地读书和写作，常常沉浸在自己的世界里。'
where id=2000000031;
