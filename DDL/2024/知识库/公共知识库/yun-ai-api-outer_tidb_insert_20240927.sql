ALTER TABLE  `algorithm_business_group` MODIFY COLUMN
    algorithm_group_code int(2) COMMENT '1 华为 - 图片元数据提取任务算法组、2 自研-文档全文检索算法组、3 自研- 文档向量化算法组（个人知识库）、4 自研- 文档向量化算法组（公共知识库）、5 自研 - 相册图片元数据提取任务算法组';

ALTER TABLE  `algorithm_business_group` MODIFY COLUMN
    algorithm_ids varchar(1024) COMMENT '逗号分隔 1 图片元数据算法id（1）、图片向量算法id（4）2 文档全文检索id（99990001）3 文档向量化算法id（5）4 文档向量化算法id（6）';

ALTER TABLE  `algorithm_business_group` ADD COLUMN
    task_type int(2) COMMENT '任务类型;任务类型 1.图片元数据分析、2.人脸聚类、3.相似度聚类、4.文档全文检索、5.文档向量化（个人知识库）、6.文档向量化（公共知识库）、7.视频提取正文';

ALTER TABLE  `algorithm_business_group` ADD COLUMN
    category int(2) COMMENT '文件/目录分类,见字典定义 1 图片 2 音频 3 视频 4 文档 5 应用 6 压缩文件 0 其他 100 普通目录 1000 笔记 1001 邮箱';

-- 【知识库配置】添加公共知识库配置
INSERT INTO `algorithm_knowledge_config` (`id`, `name`, `description`, `config`, `created_by`, `updated_by`, `create_time`, `update_time`)
VALUES ('common', '公共知识库', '公共知识库', '{\"fileTypes\":\".docx/.xlsx/.csv/.txt/.pptx/.jpg\",\"splitSize\":\"1024\",\"algorithmGroupCode\":4}', 'prod', 'prod', NOW(), NOW());

-- 【业务算法组】添加公共知识库配置
INSERT INTO `algorithm_business_group` (`id`, `algorithm_group_code`, `algorithm_ids`, `task_type`, `category`, `callback`, `create_time`, `update_time`)
VALUES (4, 4, '5', 6, 4, '',  NOW(), NOW());

-- 【算法配置】添加公共知识库配置
INSERT INTO `algorithm_config` (`id`, `task_type`, `timeline_range_flag`, `execution_order`, `algorithm_url`, `algorithm_id`, `create_time`, `update_time`, `del_flag`, `execution_type`)
VALUES (29, 4, '0', 0, 'http://ai-internal-dg-gpu.yun.139.com:30080/ziyan/yun/ai/rag/embed', 5, NOW(), NOW(), 0, 1);

INSERT INTO `algorithm_knowledge_business` (`id`, `base_id`, `business_code`, `open`, `created_by`, `updated_by`, `create_time`, `update_time`)
VALUES ('1', 'common', 'xiaotian', 1, '1', '1', now(), now());

-- 【算法信息】添加公共知识库配置
INSERT INTO `algorithm_info` (`id`, `algorithm_code`, `algorithm_name`, `algorithm_factory`, `algorithm_version`, `enable_flag`, `description`, `create_time`, `update_time`, `del_flag`, `algorithm_store_column`, `store`)
VALUES (5, 'text-vector-10001', '文本元数据处理', '自研', 'V1.0', 0, 'description', NOW(), NOW(), 0, '', NULL);

update `algorithm_business_group` set task_type = 1,category =1 where algorithm_group_code = 1;

update `algorithm_business_group` set task_type = 4,category =4 where algorithm_group_code = 2;