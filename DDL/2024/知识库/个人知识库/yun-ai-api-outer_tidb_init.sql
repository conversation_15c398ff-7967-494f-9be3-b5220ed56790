-- 个人知识库文件资源表
DROP TABLE IF EXISTS algorithm_user_knowledge_file;

CREATE TABLE `algorithm_user_knowledge_file` (
     `id` BIGINT NOT NULL COMMENT '主键id',
     `user_id` VARCHAR ( 64 ) NOT NULL COMMENT '用户id，分区字段（1000个）',
     `file_id` VARCHAR ( 64 ) NOT NULL COMMENT '独立空间文件id',
     `file_name` VARCHAR ( 128 ) NOT NULL COMMENT '文件名称',
     `old_file_id` VARCHAR (64) COMMENT '个人云文件id',
     `owner_id` VARCHAR ( 64 ) NOT NULL COMMENT '个人云 owner_id= user_id',
     `owner_type` TINYINT NOT NULL COMMENT '业务类型：-1 - 未知类型1-personal 个人云2-group 圈子3-shareGroup 共享群4-family 家庭云10 -mount 挂载盘6-note 笔记7-cardpackage 卡包8-system 系统空间9-partner 合作空间5-activity 活动空间 照片直播',
     `paas_code` VARCHAR ( 20 ) NOT NULL COMMENT 'paas平台编码 华为主平台|pds 主平台|kd 云空间|dsp 云能力DSP平台',
     `hash_name` VARCHAR ( 255 ) NOT NULL COMMENT '文件哈希名',
     `hash_value` VARCHAR ( 255 ) NOT NULL COMMENT '文件哈希值',
     `file_type` TINYINT NOT NULL COMMENT '文件类型 1文件 2文件夹',
     `content_type` TINYINT COMMENT '内容类型',
     `category` INT COMMENT '文件/目录分类:文件/目录分类,见字典定义1 图片2 音频3 视频4 文档5 应用6 压缩文件0 其他100 普通目录',
     `file_size` BIGINT DEFAULT '0' COMMENT '文件大小',
     `extension` VARCHAR ( 20 ) DEFAULT '' COMMENT '文件后缀',
     `file_updated_at` datetime COMMENT '文件修改时间',
     `file_created_at` datetime COMMENT '文件创建时间',
     `create_time` datetime NOT NULL COMMENT '创建时间',
     `update_time` datetime NOT NULL COMMENT '更新时间',
     `trashed_time` datetime DEFAULT NULL COMMENT '移入回收站时间',
     `del_flag` TINYINT UNSIGNED ZEROFILL DEFAULT '0' COMMENT '删除标识，0--正常；1--已删除；2--删除中；3--保险箱',
     `ai_status` TINYINT UNSIGNED ZEROFILL DEFAULT '0' COMMENT '算法结果状态，默认 0 未处理,1 成功,2 失败',
     `result_code` VARCHAR ( 255 ) COMMENT '算法结果码，0000 成功，其他则错误码',
     `result_msg` VARCHAR ( 1024 ) COMMENT '算法结果，记录错误信息',
     `audit_status` INT UNSIGNED ZEROFILL DEFAULT '0' COMMENT '审核状态：默认 0 未送审 状态码：2通过，3未通过',
     `audit_time` datetime COMMENT '审核时间',
     `audit_result` VARCHAR ( 2048 ) COMMENT '审核结果，json格式',
     PRIMARY KEY ( `id` ),
     UNIQUE KEY `idx_userId_fileId` ( `user_id`, `file_id` ) COMMENT '用户id、文件id唯一索引'
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '个人知识库文件资源表' PARTITION BY KEY ( `user_id` ) PARTITIONS 1000;

-- 个人知识库标签表
DROP TABLE IF EXISTS algorithm_user_knowledge_label;

CREATE TABLE `algorithm_user_knowledge_label` (
	`id` BIGINT NOT NULL COMMENT '主键id',
	`user_id` VARCHAR ( 64 ) NOT NULL COMMENT '用户id，分区字段（1000个）',
	`owner_type` TINYINT NOT NULL COMMENT '业务类型：-1 - 未知类型1-personal 个人云2-group 圈子3-shareGroup 共享群4-family 家庭云10 -mount 挂载盘6-note 笔记7-cardpackage 卡包8-system 系统空间9-partner 合作空间5-activity 活动空间 照片直播',
	`label` VARCHAR ( 64 ) NOT NULL COMMENT '标签',
	`create_time` datetime NOT NULL COMMENT '创建时间',
	`update_time` datetime NOT NULL COMMENT '更新时间',
	`sort` INT NOT NULL COMMENT '排序优先级；默认当前用户最大排序+1，第一条记录为1',
	PRIMARY KEY ( `id` ),
    KEY `idx_userId` ( `user_id` ) COMMENT '用户id索引'
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '个人知识库标签表' PARTITION BY KEY ( `user_id` ) PARTITIONS 1000;


-- 个人知识库标签与文件映射表
DROP TABLE IF EXISTS algorithm_user_knowledge_label_file;

CREATE TABLE `algorithm_user_knowledge_label_file` (
	`id` BIGINT NOT NULL COMMENT '主键id',
	`user_id` VARCHAR ( 64 ) NOT NULL COMMENT '用户id，分区字段（1000个）',
	`owner_type` TINYINT NOT NULL COMMENT '业务类型：-1 - 未知类型1-personal 个人云2-group 圈子3-shareGroup 共享群4-family 家庭云10 -mount 挂载盘6-note 笔记7-cardpackage 卡包8-system 系统空间9-partner 合作空间5-activity 活动空间 照片直播',
	`label_id` BIGINT NOT NULL COMMENT '标签id',
	`file_id` VARCHAR ( 64 ) NOT NULL COMMENT '独立空间的文件id',
	`old_file_id` VARCHAR ( 64 ) NOT NULL COMMENT '个人云转存前的文件id',
	`create_time` datetime NOT NULL COMMENT '创建时间',
	`update_time` datetime NOT NULL COMMENT '更新时间',
	PRIMARY KEY ( `id` ),
    KEY `idx_userId` ( `user_id` ) COMMENT '用户id索引'
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '个人知识库标签与文件映射表' PARTITION BY KEY ( `user_id` ) PARTITIONS 1000;


-- 个人知识库转存任务表
DROP TABLE IF EXISTS algorithm_user_knowledge_file_task;

CREATE TABLE algorithm_user_knowledge_file_task (
    id BIGINT COMMENT '主键;任务ID',
    user_id VARCHAR ( 255 ) COMMENT '用户id;分区字段（500个）',
    owner_id VARCHAR ( 255 ) COMMENT '属主ID;用户Id/家庭Id/群组Id/圈子Id，根据ownerType界定',
    owner_type TINYINT COMMENT '业务类型：-1 - 未知类型 1-personal 个人云 2-group 圈子 3-shareGroup 共享群 4-family 家庭云 10-mount 挂载盘 6-note 笔记 7-cardpackage 卡包 8-system 系统空间 9-partner 合作空间 5-activity 活动空间 照片直播',
    task_status TINYINT DEFAULT '0' NOT NULL COMMENT '任务状态;任务状态，0 未处理、1处理中、2 已完成、3处理失败 4.已过期',
    task_type VARCHAR(64) COMMENT '任务类型：TRANSFER 转存平台转存任务，DELETE 独立空间批量删除任务',
    third_task_id VARCHAR(64) COMMENT '第三方平台的任务ID',
    task_request VARCHAR ( 8192 ) COMMENT 'json格式 端侧个人知识库批量上传文档接口请求参数',
    task_response VARCHAR ( 8192 ) COMMENT 'json格式,端侧根据这个结果显示上传文件状态：[{"fielId":"","result":"","description":""} ]',
    event_time VARCHAR ( 255 ) COMMENT '请求时间; MQ消息中的操作时间',
    start_time DATETIME COMMENT '当次任务执行开始时间',
    finish_time DATETIME COMMENT '当次任务执行结束时间',
    execute_count INT DEFAULT '0' NOT NULL COMMENT '任务查询次数',
    retry_task_count INT DEFAULT '0' COMMENT '失败重试任务数量',
    pre_third_task_id VARCHAR ( 64 ) COMMENT '上一次转存平台任务ID',
    expire_time DATETIME COMMENT '过期时间：NULL--代表任务无超时限制',
    file_ids VARCHAR ( 4096 ) COMMENT '文件id 逗号分隔',
    file_num INT DEFAULT '0' NOT NULL COMMENT '转存文件数',
    success_num INT DEFAULT '0' NOT NULL COMMENT '成功文件数;处理成功文件数',
    fail_num INT DEFAULT '0' NOT NULL COMMENT '失败文件数量',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME NOT NULL COMMENT '更新时间',
    PRIMARY KEY ( `id` )
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '个人知识库文件转存任务表' PARTITION BY KEY ( `user_id` ) PARTITIONS 500;

-- 用户独立空间配置
DROP TABLE IF EXISTS algorithm_user_drive_config;

CREATE TABLE algorithm_user_drive_config (
    id BIGINT COMMENT '主键;任务ID',
    user_id VARCHAR ( 255 ) COMMENT '用户id;分区字段（500个）',
    owner_id VARCHAR ( 255 ) COMMENT '属主ID;用户Id/家庭Id/群组Id/圈子Id，根据ownerType界定',
    owner_type TINYINT COMMENT '业务类型：-1 - 未知类型 1-personal 个人云 2-group 圈子 3-shareGroup 共享群 4-family 家庭云 10-mount 挂载盘 6-note 笔记 7-cardpackage 卡包 8-system 系统空间 9-partner 合作空间 5-activity 活动空间 照片直播',
    paas_code VARCHAR ( 20 ) NOT NULL COMMENT 'paas平台编码',
    parent_file_config VARCHAR ( 512 ) COMMENT '知识库目录配置：{ "assistant":{  "name":"AI助手",  "path":"AI助手",  "catalogId": "目录id" }, "tools":{  "name":"AI工具",  "path":"AI工具",  "catalogId": "目录id" }, "knowledge":{  "name":"个人知识库",  "path":"个人知识库",  "catalogId": "目录id" }}',
    create_time DATETIME COMMENT '创建时间',
    update_time DATETIME COMMENT '更新时间',
    PRIMARY KEY ( `id` )
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '用户独立空间配置' PARTITION BY KEY ( `user_id` ) PARTITIONS 500;