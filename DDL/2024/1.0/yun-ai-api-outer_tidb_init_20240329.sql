-- 会话设置表
CREATE TABLE `algorithm_chat_config` (
                                         `id` BIGINT NOT NULL COMMENT '主键',
                                         `user_id` VARCHAR(64) NOT NULL COMMENT '用户id',
                                         `model_type` VARCHAR(16) COMMENT '模型类型 qwen：通义千问，xfyun：讯飞星火大',
                                         `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         PRIMARY KEY (`id`, `user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='会话配置表';


-- 会话评价结果表
CREATE TABLE `algorithm_chat_comment` (
                                          `id` BIGINT NOT NULL COMMENT '主键',
                                          `user_id` VARCHAR(64) NOT NULL COMMENT '用户id',
                                          `dialogue_id` VARCHAR(64) NOT NULL COMMENT '任务id',
                                          `session_id` VARCHAR(64) NOT NULL COMMENT '会话id',
                                          `model_type` VARCHAR(16) COMMENT '模型类型 qwen：通义千问，xfyun：讯飞星火大',
                                          `like_comment` tinyint(2) COMMENT '是否喜欢 0:不喜欢，1:喜欢',
                                          `default_comment` VARCHAR(512) COMMENT '默认评论',
                                          `custom_comment` VARCHAR(1024) COMMENT '用户自定义评论',
                                          `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                          `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                          PRIMARY KEY (`id`, `dialogue_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='AI用户评价结果表';

-- 会话信息表
CREATE TABLE `algorithm_chat_message` (
                                          `id` bigint(20) NOT NULL COMMENT '主键',
                                          `user_id` varchar(64) NOT NULL COMMENT '用户id',
                                          `title` varchar(4096) DEFAULT NULL COMMENT '标题;默认取会话中的第一句对话输入内容',
                                          `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                          `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                          `del_flag` int(2) NOT NULL DEFAULT '0' COMMENT '是否删除;(1是，0否)',
                                          PRIMARY KEY (`id`) /*T![clustered_index] CLUSTERED */,
                                          KEY `idx_id_user_id` (`id`,`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='会话信息表';

-- 会话内容表
CREATE TABLE `algorithm_chat_content` (
                                          `id` bigint(20) NOT NULL COMMENT '主键',
                                          `user_id` varchar(64) NOT NULL COMMENT '用户id',
                                          `session_id` bigint(20) NOT NULL COMMENT '会话ID',
                                          `task_id` varchar(64) DEFAULT NULL COMMENT '任务ID',
                                          `prompt` varchar(255) DEFAULT NULL COMMENT '提示词',
                                          `talk_type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '对话类型;0:对话历史记录,1:智囊历史记录',
                                          `resource_type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '资源类型;0-无，1 邮件， 2 笔记， 3 图片',
                                          `tools_command` varchar(16) DEFAULT NULL COMMENT '工具指令;对接意图指令',
                                          `model_type` varchar(64) DEFAULT NULL COMMENT '模型类型;模型 qwen：通义千问，xfyun：讯飞星火大模型',
                                          `in_content` longtext DEFAULT NULL COMMENT '输入内容;输入文本内容',
                                          `in_audit_time` datetime(3) DEFAULT NULL COMMENT '输入时间',
                                          `in_audit_status` int(2) DEFAULT '-1' COMMENT '输入内容审批结果;状态码：2通过，其他失败',
                                          `in_resource_id` varchar(64) DEFAULT NULL COMMENT '输入资源ID;（笔记/邮件/图片ID；纯文本时为空）',
                                          `out_content` text DEFAULT NULL COMMENT '输出内容',
                                          `out_resource_id` varchar(64) DEFAULT NULL COMMENT '输出资源ID;（笔记/邮件/图片ID；纯文本时为空）',
                                          `out_audit_time` datetime(3) DEFAULT NULL COMMENT '输出时间',
                                          `out_audit_status` int(2) DEFAULT '-1' COMMENT '输出内容审批结果;状态码：2通过，其他失败',
                                          `old_resource_id` varchar(128) DEFAULT NULL COMMENT '旧图片文件id;当用户从主平台迁移到云空间时，图片id会发生变化，这里存的是迁移前的图片id,格式：用户选择的旧文件id1,模型返回的旧文件id2',
                                          `source_channel` varchar(32) DEFAULT NULL COMMENT '渠道来源',
                                          `belongs_platform` tinyint(2) DEFAULT NULL COMMENT 'paas底座平台编码;0 华为主平台(ose) 1 阿里pds 2 华为云空间 3 云能dsp',
                                          `ext_info` text DEFAULT NULL COMMENT '扩展信息 json格式',
                                          `migration_flag` tinyint(2) DEFAULT '0' COMMENT '迁移标识;1-迁移数据，0-新数据',
                                          `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                          `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                          `del_flag` int(2) NOT NULL DEFAULT '0' COMMENT '是否删除;（1是0否）',
                                          PRIMARY KEY (`id`) /*T![clustered_index] CLUSTERED */,
                                          KEY `idx_user_id_session_id` (`user_id`,`session_id`),
                                          KEY `idx_id_user_id` (`id`,`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='会话内容表';


CREATE TABLE `algorithm_task_ai_ability` (
                                             `id` bigint(20) NOT NULL COMMENT '主键;任务ID',
                                             `user_id` bigint(20) NOT NULL COMMENT '用户id;',
                                             `algorithm_code` varchar(255) DEFAULT '' COMMENT '算法编码,多算法时以｜分割',
                                             `task_status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '任务状态，1 待处理、2处理中、3 任务完成、4 任务失败 5.已过期',
                                             `priority` tinyint(2) NOT NULL DEFAULT '0' COMMENT '数字越大优先级越高，建议取值范围为[0,99]',
                                             `result_code` varchar(64) DEFAULT '' COMMENT '错误结果码',
                                             `result_msg` varchar(255) DEFAULT '' COMMENT '错误信息',
                                             `start_time` datetime DEFAULT NULL COMMENT '当次任务执行开始时间;YYYY-MM-DD HH24:MM:SS.NNN',
                                             `finish_time` datetime DEFAULT NULL COMMENT '当次任务执行结束时间;YYYY-MM-DD HH24:MM:SS.NNN',
                                             `expire_time` datetime DEFAULT NULL COMMENT '过期时间 NULL--代表任务无超时限制',
                                             `execute_id` varchar(255) DEFAULT '' COMMENT '执行ID',
                                             `execute_count` tinyint(4) DEFAULT '0' COMMENT '执行次数',
                                             `source_channel` varchar(32) DEFAULT '' COMMENT '来源渠道',
                                             `ext_info` varchar(1024) DEFAULT '' COMMENT '扩展信息',
                                             `business_param` varchar(4096) DEFAULT '' COMMENT '业务参数',
                                             `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间;YYYY-MM-DD HH24:MM:SS.NNN',
                                             `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间;YYYY-MM-DD HH24:MM:SS.NNN',
                                             `supplier_types` varchar(64) NOT NULL DEFAULT '' COMMENT '厂商编码,多厂商时以｜分割',
                                             `task_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '任务类型，0异步任务，1同步任务',
                                             `resp_param` varchar(1024) DEFAULT '' COMMENT '响应参数',
                                             PRIMARY KEY (`id`) /*T![clustered_index] CLUSTERED */,
                                             KEY `algorithm_task_ai_aibility` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='AI输入算法任务表';