
-- 笔记同步状态增加字段
ALTER TABLE algorithm_user_knowledge_file
    ADD `revision` varchar(64) DEFAULT NULL
    COMMENT '修改版本号,当from_resource_type=5时必存';

ALTER TABLE algorithm_user_knowledge_file
    ADD `sync_status` TINYINT(2) DEFAULT '-1'
COMMENT '笔记同步状态:from_resource_type =4必存; -1 （默认） 0 同步中，1 同步成功，2 同步失败';

ALTER TABLE algorithm_user_knowledge_file
    ADD `sync_error_msg` varchar(1024) DEFAULT NULL
    COMMENT '同步信息错误描述:当from_resource_type=4时出错可存';

ALTER TABLE algorithm_user_knowledge_file
    ADD `sync_error_code` varchar(128) DEFAULT NULL
    COMMENT '同步错误码:当from_resource_type=4时出错可存';



ALTER TABLE algorithm_user_knowledge
    ADD `biz_type` TINYINT(2) DEFAULT '0'
    COMMENT '知识库业务类型 0--默认；1--笔记同步';

