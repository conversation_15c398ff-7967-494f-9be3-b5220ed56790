package com.zyhl.yun.api.outer.external.client;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.domain.entity.ocr.CertificateOcrEntity;
import com.zyhl.yun.api.outer.domain.entity.ocr.CertificateOcrReqEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 远端请求
 * <AUTHOR>
 */
@FeignClient(name = "${yun.external.certificate.name}", url = "${yun.external.certificate.url}")
public interface CertificateClient {

	/**
	 * 敏感词校验
	 *
	 * @param req
	 * @return
	 */
	@PostMapping(value = "/certificate/ocr")
	BaseResult<CertificateOcrReqEntity> certificateOcr(@RequestBody CertificateOcrEntity req);

}
