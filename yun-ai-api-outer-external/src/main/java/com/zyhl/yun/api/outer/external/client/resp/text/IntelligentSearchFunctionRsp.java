package com.zyhl.yun.api.outer.external.client.resp.text;

import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchFunction;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 文本工具Client-响应结果-功能搜索
 * @Author: WeiJingKun
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntelligentSearchFunctionRsp implements Serializable {

    /** 功能信息 */
    private List<SearchFunction> functionList;

    /** 下一页起始资源标识符，最后一页该值为空 */
    private List<Object> pageAfter;

    /** 记录总数 */
    private Integer totalCount;

}
