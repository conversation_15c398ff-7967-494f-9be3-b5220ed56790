package com.zyhl.yun.api.outer.external.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.exception.BaseException;
import com.zyhl.yun.api.outer.external.client.MarketInviteClient;
import com.zyhl.yun.api.outer.external.client.req.market.MarketAcceptInviteReq;
import com.zyhl.yun.api.outer.external.client.resp.market.MarketBaseResponse;
import com.zyhl.yun.api.outer.external.service.MarketInviteExternalService;
import com.zyhl.yun.api.outer.util.Md5Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 营销平台接口
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MarketInviteExternalServiceImpl implements MarketInviteExternalService {

    @Resource
    private MarketInviteClient marketInviteClient;

    /**
     * 已经邀请过错误码
     */
    private static final Integer HAS_INVITATION = 410;

    @Override
    public void accept(String phone) {
        String phoneMd5 = "";
        try {
            if (CharSequenceUtil.isEmpty(phone)) {
                return;
            }
            phoneMd5 = Md5Utils.encrypt(phone);
        } catch (Exception e) {
            log.error("手机号码{} MD5加密失败", phone, e);
            throw new BaseException(ResultCodeEnum.AI_FAILURE_DEFAULT);
        }

        try {
            // 调营销平台同步报名
            final MarketAcceptInviteReq inviteReq = new MarketAcceptInviteReq();
            inviteReq.setPhone(phoneMd5);
            final MarketBaseResponse response = marketInviteClient.accept(inviteReq);
            log.info("手机号码{}调用营销平台报名接口结果为：{}", phone, JsonUtil.toJson(response));

            // 0-报名成功；410-用户接受过邀请
            boolean fail = response == null || response.getCode() == null || (!response.getCode().equals(0) && !response.getCode().equals(HAS_INVITATION));
            if (fail) {
                log.error("手机号码{}调用营销平台报名接口返回状态为：{}", phone, response.getCode());
                throw new BaseException(ResultCodeEnum.AI_FAILURE_DEFAULT);
            }
        } catch (Exception e) {
            log.error("营销平台接口异常：{}", e.getMessage(), e);
        }
    }
}
