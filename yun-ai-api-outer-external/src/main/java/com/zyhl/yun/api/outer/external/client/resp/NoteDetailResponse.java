package com.zyhl.yun.api.outer.external.client.resp;

import com.zyhl.yun.api.outer.external.client.resp.market.NoteContentsResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Classname NoteDetailResponse
 * @Description 返回结果
 * @Date 2024/3/1 10:19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NoteDetailResponse implements Serializable {

    /**
     * 便签id
     */
    private String noteid;

    /**
     * 标题
     */
    private String title;

    /**
     * 描述文件版本号
     */
    private String version;

    /**
     * 内容对象
     */
    private List<NoteContentsResult> contents;

    /**
     * 附件对象信息
     */
    private Object attachments;

    /**
     * 创建时间
     */
    private String createtime;

    /**
     * 更新时间
     */
    private String updatetime;

}
