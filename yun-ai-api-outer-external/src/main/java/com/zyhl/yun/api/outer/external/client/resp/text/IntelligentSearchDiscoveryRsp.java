package com.zyhl.yun.api.outer.external.client.resp.text;

import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchDiscovery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 文本工具Client-响应结果-发现广场搜索
 * @Author: WeiJingKun
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntelligentSearchDiscoveryRsp implements Serializable {

    /** 下一页 */
    private String pageCursor;

    /** 记录总数 */
    private Integer totalCount;

    /** 发现广场信息 */
    private List<SearchDiscovery> discoveryList;

    /** 搜索关键字 */
    private String keyword;

    /** 搜索关键字列表 */
    private List<String> keywords;

    /** 搜索类型(1-全部,2-文档,3-视频,4-试卷,5-影视,6-其他,7-书籍) */
    private List<Integer> queryTypes;

}
