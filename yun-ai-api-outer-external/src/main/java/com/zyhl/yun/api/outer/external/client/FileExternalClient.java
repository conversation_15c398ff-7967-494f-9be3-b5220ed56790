package com.zyhl.yun.api.outer.external.client;


import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.domain.req.BatchGetReqEntity;
import com.zyhl.yun.api.outer.domain.req.FileGetDownloadUrlReqEntity;
import com.zyhl.yun.api.outer.domain.resp.BatchGetFileRspEntity;
import com.zyhl.yun.api.outer.domain.resp.FileGetDownloadUrlRespEntity;
import com.zyhl.yun.api.outer.external.client.interceptor.PerSaasFeignInterceptor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 远端请求
 * <AUTHOR>
 */
@FeignClient(configuration = PerSaasFeignInterceptor.class, name = "${yun.external.person.serviceId:personal}")
public interface FileExternalClient {


    /**
     * 获取文件信息（批量）
     * @param req 批量获取参数
     * @return 批量获取结果
     */
    @PostMapping(value = "/file/batchGet",headers = "x-yun-api-version=v1")
    BaseResult<BatchGetFileRspEntity> batchGet(@RequestBody BatchGetReqEntity req);


    /**
     * 获取文件下载地址
     * @param dto 文件下载参数
     * @return 文件下载地址
     */
    @PostMapping(value = "/file/getDownloadUrl",headers = "x-yun-api-version=v1")
    BaseResult<FileGetDownloadUrlRespEntity> fileGetDownloadUrl(FileGetDownloadUrlReqEntity dto);

}
