package com.zyhl.yun.api.outer.external.service;

/**
 * 路由feign client工厂
 *
 * <AUTHOR>
 */
public interface RouteFeignClientFactory<T> {
    /**
     * 根据用户域id获取feign client
     *
     * @param userId 用户域id
     * @return T
     */
    T getFeignClientByRoute(Long userId);

    /**
     * 根据服务id获取feign client
     *
     * @param serviceId 服务id
     * @param url       url
     * @param path      path
     * @return T
     */
    T getFeignClient(String serviceId, String url, String path);

}
