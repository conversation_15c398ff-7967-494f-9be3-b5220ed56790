package com.zyhl.yun.api.outer.external.resp;


import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.AddressDetail;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.resp.MediaMetaInfo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 文件查询响应参数
 * @Author: zzb
 */
@Data
public class AiFileSearchPlatformRespDto {

    private List<FileInfo> rows;

    private List<Object> pageAfter;

    private Long resultCode;

    private Integer total;

    private Integer count;

    private Boolean success;

    private Object data;

    private ErrorInfo error;

    @Data
     public static class FileInfo{
         private String fileId;
         private String name;
         private String type;
         private String category;
         private String createdAt;
         private String updatedAt;
         private String extension;
         private String size;
         private String parentFileId;
         private String contentHash;
         private String contentHashName;
         private String idPath;
         private String takenAt;

         //调个人云或主平台接口获取
         private String thumbnailUrl;
         private MediaMetaInfo mediaMetaInfo;
         private AddressDetail addressDetail;
     }

    @Data
    public static class ErrorInfo{
        private String code;
        private String message;
    }

    /**
    * 组装下一页 游标
    */
    public AiFileSearchPlatformRespDto initPageAfter(Integer pageSize){
        //判断是否有下一页数据
        if (pageSize>count||pageSize.equals(total)){
            //没有下一页数据
            this.pageAfter = new ArrayList<>();
        }
        return this;
    }

}
