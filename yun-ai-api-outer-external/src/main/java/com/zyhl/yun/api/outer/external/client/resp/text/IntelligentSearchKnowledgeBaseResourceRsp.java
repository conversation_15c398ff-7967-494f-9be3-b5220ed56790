package com.zyhl.yun.api.outer.external.client.resp.text;

import com.zyhl.yun.api.outer.domain.vo.chat.search.result.PersonalKnowledgeResource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 文本工具Client-响应结果-知识库资源搜索
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntelligentSearchKnowledgeBaseResourceRsp implements Serializable {

    private static final long serialVersionUID = 1821643813975676787L;

    /** 知识库资源列表 */
    private List<PersonalKnowledgeResource> resourceList;

    /** 查询总数 */
    private Integer totalCount;

    /** 下一页游标 */
    private List<Object> pageAfter;

}
