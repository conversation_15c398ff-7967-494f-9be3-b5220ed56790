package com.zyhl.yun.api.outer.external.service.impl;


import com.zyhl.hcy.yun.ai.common.base.enums.YunAiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.external.client.interceptor.SyncDiskConfig;
import com.zyhl.yun.api.outer.external.client.resp.UserRoutePolicyDTO;
import com.zyhl.yun.api.outer.external.service.RouteFeignClientFactory;
import com.zyhl.yun.api.outer.external.service.SyncDiskFeign;
import com.zyhl.yun.api.outer.external.service.UserEtnService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.openfeign.FeignClientBuilder;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * <p>
 * 同步盘工厂类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-04
 */
@Slf4j
@Component
public class SyncDiskServiceClientFactory implements RouteFeignClientFactory<SyncDiskFeign> {

    @Resource
    private UserEtnService userEtnService;

    @Resource
    private SyncDiskConfig syncDiskConfig;

    private final FeignClientBuilder feignClientBuilder;

    public SyncDiskServiceClientFactory(ApplicationContext applicationContext) {

        this.feignClientBuilder = new FeignClientBuilder(applicationContext);
    }

    @Override
    public SyncDiskFeign getFeignClientByRoute(Long userId) {
        UserRoutePolicyDTO routePolicyDTO = userEtnService.qryRoutePolicy(userId);
        String url = routePolicyDTO.getModeUrl(syncDiskConfig.getModName());
        if (StringUtils.isBlank(url)) {
            log.info("同步盘路由不存在,路由信息：{}", routePolicyDTO);
            throw new YunAiBusinessException(YunAiCommonResultCode.DOWNSTREAM_SERVICES_EXCEPTION);
        }
        syncDiskConfig.setDefaultUrl(url);
        return getFeignClient(syncDiskConfig);
    }

    public SyncDiskFeign getFeignClient(SyncDiskConfig syncDiskConfig) {

        return this.feignClientBuilder.forType(SyncDiskFeign.class, syncDiskConfig.getModName()).url(syncDiskConfig.getDefaultUrl()).build();
    }

    @Override
    public SyncDiskFeign getFeignClient(String serviceId, String url, String path) {

        if (syncDiskConfig.getModName().equals(serviceId)) {
            return this.feignClientBuilder.forType(SyncDiskFeign.class, serviceId).url(url).build();
        }
        return this.feignClientBuilder.forType(SyncDiskFeign.class, serviceId).url(url).path(path).build();
    }

}
