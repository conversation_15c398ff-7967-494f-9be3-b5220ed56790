package com.zyhl.yun.api.outer.external.client.req.text;

import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchKnowledgeBaseResourceParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 文本工具Client-请求参数-知识库资源搜索
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntelligentSearchKnowledgeBaseResourceReq implements Serializable {

    private static final long serialVersionUID = 4805740430449889143L;

    /** 用户Id，默认从token获取，第三方平台调用时必填。 */
    private String userId;

    /** 知识库资源搜索条件 */
    @NotNull(message ="知识库资源搜索条件不能为空")
    private @Valid SearchKnowledgeBaseResourceParam searchParam;

}
