package com.zyhl.yun.api.outer.external.client.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户割接返回参数
 * @author: yangkailun
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserDomainRspDTO {

    /**
     * 割接状态。
     * 该字段表示割接状态，具体值包括：
     * 0 - 未割接
     * 1 - 已割接
     * 2 - 割接中
     */
    private Integer cutoverStatus;

    /**
     * 所属平台。
     * 该字段表示用户所属的平台，具体值包括：
     * 0 - 华为云平台
     * 1 - PDS平台
     */
    private Integer belongsPlatform;

    /**
     * 手机号。
     * 该字段存储用户的手机号。
     */
    private String phoneNumber;

    /**
     * 用户ID。
     * 该字段存储用户的唯一标识符。
     */
    private Long userDomainId;

    /**
     * 站点代码。
     * 该字段存储用户的站点代码。
     */
    private String siteCode;
    
    /**
     * 云盘 basic token
     */
    private String basicToken;

}
