package com.zyhl.yun.api.outer.external.client.interceptor;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 中心任务-Client配置类
 * @Author: WeiJingKun
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "external.center-task")
public class CenterTaskConfig {

    /** 服务名 */
    private String name;

    /** 服务url */
    private String url;

    /** 服务接口前缀路径 */
    private String path;

}
