package com.zyhl.yun.api.outer.external.client;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.external.client.interceptor.ApiTextFeignInterceptor;
import com.zyhl.yun.api.outer.external.client.req.text.*;
import com.zyhl.yun.api.outer.external.client.resp.text.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 文本工具Client
 * @Author: WeiJingKun
 */
@FeignClient(
        name = "yun-ai-api-text",
        url = "${external.api-text.url}",
        path = "${external.api-text.path}",
        configuration = ApiTextFeignInterceptor.class)
public interface ApiTextClient {

    /**
     * 语义搜图
     * @Author: WeiJingKun
     * @param req 文本工具Client-请求参数-语义搜图
     * @return 文本工具Client-响应结果-语义搜图
     */
    @PostMapping(value = "/intelligent/search/image")
    BaseResult<IntelligentSearchImageRsp> intelligentSearchImage(IntelligentSearchImageReq req);

    /**
     * 个人云资产搜索
     * @Author: WeiJingKun
     * @param req 文本工具Client-请求参数-个人云资产搜索
     * @return 文本工具Client-响应结果-个人云资产搜索
     */
    @PostMapping(value = "/intelligent/search/file")
    BaseResult<IntelligentSearchFileRsp> intelligentSearchFile(IntelligentSearchFileReq req);

    /**
     * 笔记搜索
     * @Author: WeiJingKun
     * @param req 文本工具Client-请求参数-笔记搜索
     * @return 文本工具Client-响应结果-笔记搜索
     */
    @PostMapping(value = "/intelligent/search/note")
    BaseResult<IntelligentSearchNoteRsp> intelligentSearchNote(IntelligentSearchNoteReq req);

    /**
     * 功能搜索
     * @Author: WeiJingKun
     * @param req 文本工具Client-请求参数-功能搜索
     * @return 文本工具Client-响应结果-功能搜索
     */
    @PostMapping(value = "/intelligent/search/function")
    BaseResult<IntelligentSearchFunctionRsp> intelligentSearchFunction(IntelligentSearchFunctionReq req);

    /**
     * 活动搜索
     * @Author: WeiJingKun
     * @param req 文本工具Client-请求参数-活动搜索
     * @return 文本工具Client-响应结果-活动搜索
     */
    @PostMapping(value = "/intelligent/search/activity")
    BaseResult<IntelligentSearchActivityRsp> intelligentSearchActivity(IntelligentSearchActivityReq req);

    /**
     * 发现广场搜索
     * @Author: WeiJingKun
     * @param req 文本工具Client-请求参数-发现广场搜索
     * @return 文本工具Client-响应结果-发现广场搜索
     */
    @PostMapping(value = "/intelligent/search/discovery")
    BaseResult<IntelligentSearchDiscoveryRsp> intelligentSearchDiscovery(IntelligentSearchDiscoveryReq req);

    /**
     * 我的圈子搜索
     * @Author: WeiJingKun
     * @param req 文本工具Client-请求参数-我的圈子搜索
     * @return 文本工具Client-响应结果-我的圈子搜索
     */
    @PostMapping(value = "/intelligent/search/group/my")
    BaseResult<IntelligentSearchMyGroupRsp> intelligentSearchMyGroup(IntelligentSearchMyGroupReq req);

    /**
     * 热门圈子搜索
     * @Author: WeiJingKun
     * @param req 文本工具Client-请求参数-热门圈子搜索
     * @return 文本工具Client-响应结果-热门圈子搜索
     */
    @PostMapping(value = "/intelligent/search/group/recommend")
    BaseResult<IntelligentSearchRecommendGroupRsp> intelligentSearchRecommendGroup(IntelligentSearchRecommendGroupReq req);

    /**
     * 邮件搜索
     * @Author: WeiJingKun
     * @param req 文本工具Client-请求参数-邮件搜索
     * @return 文本工具Client-响应结果-邮件搜索
     */
    @PostMapping(value = "/intelligent/search/mail")
    BaseResult<IntelligentSearchMailRsp> intelligentSearchMail(IntelligentSearchMailReq req);

    /**
     * 邮件附件搜索
     * @param req 文本工具Client-请求参数-邮件附件搜索
     * @return 文本工具Client-响应结果-邮件附件搜索
     */
    @PostMapping(value = "/intelligent/search/mailAttachment")
    BaseResult<IntelligentSearchMailAttachmentRsp> intelligentSearchMailAttachment(IntelligentSearchMailAttachmentReq req);

    /**
     * 知识库资源搜索
     * @Author: WeiJingKun
     * @param req 文本工具Client-请求参数-知识库资源搜索
     * @return 文本工具Client-响应结果-知识库资源搜索
     */
    @PostMapping(value = "/intelligent/search/knowledgeBase/resource")
    BaseResult<IntelligentSearchKnowledgeBaseResourceRsp> intelligentSearchKnowledgeBaseResource(IntelligentSearchKnowledgeBaseResourceReq req);

}
