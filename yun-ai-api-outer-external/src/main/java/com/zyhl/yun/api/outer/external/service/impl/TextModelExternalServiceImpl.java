package com.zyhl.yun.api.outer.external.service.impl;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.hcy.yun.ai.common.model.api.client.ali.vo.GenericSearchVO;
import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.AliPptClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.dto.CreateOutlineDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.dto.PptGenDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.dto.PptGenProgressDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.vo.PptGenProgressVO;
import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.vo.PptGenVO;
import com.zyhl.hcy.yun.ai.common.model.api.client.blian.ExternalBlianClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.jiutian.ExternalJiutianClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.qwen.ExternalQwenClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.event.TextModelStreamEventListener;
import com.zyhl.hcy.yun.ai.common.model.api.client.vlmodel.ExternalVlModelClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.vo.TextModelBaseVo;
import com.zyhl.hcy.yun.ai.common.model.api.client.volc.ExternalVolcClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.volcengine.ExternalVolcengineClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.xfyun.ExternalXfyunClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.xingchen.ExternalXingchenClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.xingchen.dto.BotProfileReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelVlReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.ApiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.hcy.yun.ai.common.model.api.utils.TextModelUtil;
import com.zyhl.yun.api.outer.config.WhiteListProperties;
import com.zyhl.yun.api.outer.config.textmodel.NetworkChatConfig;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.external.AiInternetSearchExternalService;
import com.zyhl.yun.api.outer.external.service.TextModelExternalService;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

/**
 * 调用大模型接口
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TextModelExternalServiceImpl implements TextModelExternalService {

	@Resource
    private AiInternetSearchExternalService aiInternetSearchExternalService;
    @Resource
    private ExternalBlianClient externalBlianClient;
    @Resource
    private ExternalQwenClient externalQwenClient;
    @Resource
    private ExternalXfyunClient externalXfyunClient;
    @Resource
    private ExternalXingchenClient externalXingchenClient;
    @Resource
    private ExternalVolcClient externalVolcClient;
    @Resource
    private ExternalVolcengineClient externalVolcengineClient;
    @Resource
    private ExternalJiutianClient externalJiutianClient;
    @Resource
    private ExternalVlModelClient externalVlModelClient;
    @Resource
    private AliPptClient aliPptClient;
    @Resource
    private WhiteListProperties whiteListProperties;
    @Resource
    private NetworkChatConfig networkChatConfig;
    
    @Override
    public boolean qwenStream(TextModelTextReqDTO reqDTO, TextModelStreamEventListener event) {
        log.info("【流式接口】调commons包通义千问模型流式接口");
        externalQwenClient.streamCompletions(reqDTO, event);
        return true;
    }

    @Override
    public boolean xfyunStream(TextModelTextReqDTO reqDTO, TextModelStreamEventListener event) {
        log.info("【流式接口】调commons包讯飞星火模型流式接口");
        externalXfyunClient.streamCompletions(reqDTO, event);
        return true;
    }

    @Override
    public boolean jiutianStream(TextModelTextReqDTO reqDTO, TextModelStreamEventListener event) {
        log.info("【流式接口】调commons包九天模型流式接口");
        externalJiutianClient.streamCompletions(reqDTO, event);
        return true;
    }

    @Override
    public boolean blianStream(TextModelTextReqDTO reqDTO, TextModelStreamEventListener event) {
        log.info("【流式接口】调commons包阿里百炼模型流式接口");
        externalBlianClient.streamCompletions(reqDTO, event);
        return true;
    }

    @Override
    public boolean blianQwenLongStream(TextModelTextReqDTO reqDTO, TextModelStreamEventListener event) {
        log.info("【流式接口】调commons包阿里百炼模型文档流式接口");
        reqDTO.setModelValue(TextModelEnum.BLIAN_QWEN_LONG.getCode());
        externalBlianClient.streamCompletions(reqDTO, event);
        return true;
    }

    @Override
    public boolean blianCalcStream(TextModelTextReqDTO reqDTO, TextModelStreamEventListener event) {
        log.info("【流式接口】调commons包阿里百炼计费版模型流式接口");
        reqDTO.setModelValue(TextModelEnum.BLIAN_MODEL_72B_CALC.getCode());
        externalBlianClient.streamCompletions(reqDTO, event);
        return true;
    }

    @Override
    public void xchenStream(TextModelTextReqDTO reqDTO, String typeRelationId, TextModelStreamEventListener event) {
        log.info("【流式接口】调commons包阿里通义星尘模型流式接口");
        BotProfileReqDTO botReqDTO = new BotProfileReqDTO();
        botReqDTO.setCharacterId(typeRelationId);
        externalXingchenClient.streamCompletions(reqDTO, botReqDTO, event);
    }

    @Override
    public boolean hshanStream(TextModelTextReqDTO reqDTO, TextModelStreamEventListener event) {
        log.info("【流式接口】调commons包火山模型流式接口");
        reqDTO.setModelValue(TextModelEnum.HUOSHAN.getCode());
        externalVolcClient.streamCompletions(reqDTO, event);
        return true;
    }

    @Override
    public boolean hshanArkStream(TextModelTextReqDTO reqDTO, TextModelStreamEventListener event) {
        log.info("【流式接口】调commons包火山方舟模型流式接口");
        externalVolcengineClient.streamCompletions(reqDTO, event);
        return true;
    }

    @Override
    public boolean streamDialogue(String modelCode, TextModelTextReqDTO reqDTO, TextModelStreamEventListener event) {
        // VIP用户
        reqDTO.setVipUser(whiteListProperties.getTextModelVipUser().contains(RequestContextHolder.getPhoneNumber()));
        // 追加role=system消息
        appendRoleSystemMessage(modelCode, reqDTO, event);
        try {
            if (TextModelEnum.isBlian(modelCode)) {
                // 百炼大文本模型
                return blianStream(reqDTO, event);
            } else if (TextModelEnum.isBlianModel72BCalc(modelCode)) {
                // 百炼计费版
                return blianCalcStream(reqDTO, event);
            } else if (TextModelEnum.isBlianModelDeepSeekR1(modelCode)) {
                // 百炼deepseekR1
                reqDTO.setModelValue(modelCode);
                return blianStream(reqDTO, event);
            } else if (TextModelEnum.isBlianQWen3(modelCode)) {
                // 百炼千问3
                reqDTO.setModelValue(modelCode);
                return blianStream(reqDTO, event);
			} else if (TextModelEnum.isQwen(modelCode) || TextModelEnum.isQwen32B(modelCode)
					|| TextModelEnum.isQwen05B(modelCode) || TextModelEnum.isQwen2_7B(modelCode)
					|| TextModelEnum.isQwen2_72B(modelCode) || TextModelEnum.isQwen25_72B(modelCode)
					|| TextModelEnum.isZyDeepSeekR1_7B(modelCode) || TextModelEnum.isZyDeepSeekR1_32B(modelCode)
					|| TextModelEnum.isZyDeepSeekR1(modelCode)) {
                // 通义千问大文本模型
                reqDTO.setModelValue(modelCode);
                return qwenStream(reqDTO, event);
            } else if (TextModelEnum.isXfyun(modelCode)) {
                // 讯飞星火大文本模型
                return xfyunStream(reqDTO, event);
            } else if (TextModelEnum.isJiutian(modelCode)) {
                // 九天大文本模型
                return jiutianStream(reqDTO, event);
            } else if (TextModelEnum.isBlianQwenLong(modelCode)) {
                // 百炼文档文本大模型
                return blianQwenLongStream(reqDTO, event);
            } else if (TextModelEnum.isHuoshan(modelCode)) {
                // 火山文本大模型
                reqDTO.setModelValue(modelCode);
                return hshanStream(reqDTO, event);
            } else if (TextModelEnum.isHuoshanDeepseek(modelCode)) {
                // 火山方舟文本大模型（支持deepseek）
                reqDTO.setModelValue(modelCode);
                return hshanArkStream(reqDTO, event);
            } else {
                log.warn("大文本模型不存在，模型编码为：{}", modelCode);
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_NACOS_CONFIG);
            }
        } catch (Exception e) {
            if (e instanceof YunAiBusinessException) {
                String code = ((YunAiBusinessException) e).getCode();
                if (ApiCommonResultCode.DOWNSTREAM_SERVICES_EXCEPTION.getResultCode().equals(code)
                        || ApiCommonResultCode.ERROR_SERVER_INTERNAL.getResultCode().equals(code)) {
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_AI_MODEL);
                }
                throw e;
            }
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_AI_MODEL);
        }
    }

	/**
	 * 追加role=system消息
	 * 
	 * @param modelCode
	 * @param reqDTO
	 * @param event
	 */
	private void appendRoleSystemMessage(String modelCode, TextModelTextReqDTO reqDTO,
			TextModelStreamEventListener event) {
		// 是自研deepseek极速版&&联网开关开启&&event.getDialogue()不为空
		if ((TextModelEnum.isZyDeepSeekR1_32B(modelCode) || TextModelEnum.isZyDeepSeekR1(modelCode))
				&& Boolean.TRUE.equals(reqDTO.getEnableForceNetworkSearch())
				&& StringUtils.isNotBlank(event.getDialogue())) {
			log.info("是自研deepseek极速版||自研deepsee满血版&&联网开关开启&&event.getDialogue()不为空 执行手动联网搜索");
			event.setEnableManualNetwork(true);
			List<GenericSearchVO> networkList = aiInternetSearchExternalService.modelNetworkSearch(event.getDialogue());
			String systemPrompt = networkChatConfig.getSystemPrompt();
			if (CollUtil.isNotEmpty(networkList) && StringUtils.isNotEmpty(systemPrompt)) {
				event.setManualNetworkSearchInfoList(TextModelUtil.caseNetworkSearchInfos(networkList));
				String networkText = TextModelUtil.getNetworkXmlText(networkList,
						networkChatConfig.getSearchSourceField(),
						Boolean.TRUE.equals(reqDTO.getEnableNetworkSearchCitation()));
				systemPrompt = TextModelUtil.getValueSystemRoleContent(systemPrompt);
				String systemMessageContent = systemPrompt.replace("{search_results}", networkText);
				TextModelMessageDTO systemMessage = new TextModelMessageDTO(TextModelRoleEnum.SYSTEM.getName(),
						systemMessageContent);
				reqDTO.getMessageDtoList().add(0, systemMessage);
			}
		}
	}

	@Override
    public boolean streamVlDialogue(String modelCode, TextModelVlReqDTO reqDTO, TextModelStreamEventListener event) {
        try {
            if (TextModelEnum.isVlModel(modelCode)) {
                reqDTO.setModelValue(modelCode);
                externalVlModelClient.streamCompletions(reqDTO, event);
                return true;
            } else {
                log.warn("大文本模型不存在，模型编码为：{}", modelCode);
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_NACOS_CONFIG);
            }
        } catch (Exception e) {
            if (e instanceof YunAiBusinessException) {
                String code = ((YunAiBusinessException) e).getCode();
                if (ApiCommonResultCode.DOWNSTREAM_SERVICES_EXCEPTION.getResultCode().equals(code)
                        || ApiCommonResultCode.ERROR_SERVER_INTERNAL.getResultCode().equals(code)) {
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_AI_MODEL);
                }
                throw e;
            }
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_AI_MODEL);
        }
    }

    @Override
    public boolean streamAiPptOutlineDialogue(CreateOutlineDTO reqDTO, TextModelStreamEventListener event) {
        try {
            if (StringUtils.isEmpty(reqDTO.getToken())) {
                log.error("streamAiPptOutlineDialogue token不存在...");
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }
            aliPptClient.streamCompletions(reqDTO, event);
            return Boolean.TRUE;
        } catch (Exception e) {
            if (e instanceof YunAiBusinessException) {
                String code = ((YunAiBusinessException) e).getCode();
                if (ApiCommonResultCode.DOWNSTREAM_SERVICES_EXCEPTION.getResultCode().equals(code)
                        || ApiCommonResultCode.ERROR_SERVER_INTERNAL.getResultCode().equals(code)) {
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_AI_MODEL);
                }
                throw e;
            }
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_AI_MODEL);
        }
    }

    @Override
    public PptGenVO generateAiPpt(PptGenDTO dto) {
        if (StringUtils.isEmpty(dto.getToken())) {
            log.error("generateAiPpt token不存在...");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        return aliPptClient.generatePpt(dto);
    }

    @Override
    public PptGenProgressVO getAiPptGenProgress(PptGenProgressDTO dto) {
        if (StringUtils.isEmpty(dto.getToken())) {
            log.error("getAiPptGenProgress token不存在...");
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        return aliPptClient.getPptGenProgress(dto);
    }

    @Override
    public TextModelBaseVo textModelDialogue(TextModelTextReqDTO reqDTO) {
        if (TextModelEnum.isQwen(reqDTO.getModelValue())
                || TextModelEnum.isQwen05B(reqDTO.getModelValue())
                || TextModelEnum.isQwen32B(reqDTO.getModelValue())
                || TextModelEnum.isQwen2_7B(reqDTO.getModelValue())
                || TextModelEnum.isQwen2_72B(reqDTO.getModelValue())
                || TextModelEnum.isQwen2_7B_INTENT(reqDTO.getModelValue())
        ) {
            return externalQwenClient.completions(reqDTO);
        }
        return null;
    }
}
