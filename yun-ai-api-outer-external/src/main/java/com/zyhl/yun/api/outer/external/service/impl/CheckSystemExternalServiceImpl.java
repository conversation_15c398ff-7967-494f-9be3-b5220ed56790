package com.zyhl.yun.api.outer.external.service.impl;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.CheckSystemClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.dto.CheckImageReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.dto.CheckTextFileReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.dto.CheckTextReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.enums.CheckSystemContentTypeEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.properties.CheckSystemProperties;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.vo.CheckAIFileVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.check.vo.CheckResultVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.OwnerDriveClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.OwnerDriveFileDownloadReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.OwnerDriveFileDownloadVO;
import com.zyhl.hcy.yun.ai.common.platform.third.dto.GetUserInfoByPhoneNumberRespDTO;
import com.zyhl.yun.api.outer.config.CheckSystemConfig;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.external.CheckSystemExternalService;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/4/9 18:41
 */
@Service
@Slf4j
public class CheckSystemExternalServiceImpl implements CheckSystemExternalService {

    @Resource
    private CheckSystemClient checkSystemClient;
    @Resource
    private CheckSystemConfig checkSystemConfig;

    @Resource
    private UidGenerator uidGenerator;

    @Resource
    private OwnerDriveClient ownerDriveClient;

    @Resource
    private CheckSystemProperties checkSystemProperties;

    @Override
    public CheckResultVO checkText(CheckTextReqDTO checkTextReqDTO) {
        return checkText(checkSystemConfig.isOpen(), checkTextReqDTO);
    }

    @Override
    public CheckResultVO checkText(Boolean isOpen, CheckTextReqDTO checkTextReqDTO) {
        if (!Boolean.TRUE.equals(isOpen)) {
            log.info("【文本送审】送审对接关闭，直接返回送审成功 checkTextReqDTO：{}", JSON.toJSONString(checkTextReqDTO));
            return CheckResultVO.builder().success(true).taskId(checkTextReqDTO.getTaskId()).build();
        }
        String content = checkTextReqDTO.getContent();
        if (null != content) {
            //去除左右空格
            content = content.trim();
        }
        if (CharSequenceUtil.isEmpty(content)) {
            log.info("【文本送审】送审内容为空：{}", JSON.toJSONString(checkTextReqDTO));
            return CheckResultVO.builder().success(true).taskId(checkTextReqDTO.getTaskId()).build();
        }

        log.info("【文本送审】文本送审，送审参数为：{}", JsonUtil.toJson(checkTextReqDTO));
        try {
            return checkSystemClient.checkText(checkTextReqDTO);
        } catch (YunAiBusinessException e) {
            if ("********".equals(e.getCode())) {
                // 敏感词错误码转换
                throw new YunAiBusinessException(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
            }
            throw e;
        }
    }

    @Override
    public CheckResultVO checkText(Long dialogueId, String userId, String text) {
        // 构建请求参数
        final CheckTextReqDTO check = new CheckTextReqDTO();
        check.setTaskId(String.valueOf(dialogueId));
        check.setAccount(userId);
        check.setContent(text);

        return checkText(check);
    }

    @Override
    public CheckResultVO checkTextException(Long dialogueId, String userId, String text) {
        CheckResultVO checkResultVO = checkText(dialogueId, userId, text);
        if (!Boolean.TRUE.equals(checkResultVO.getSuccess())) {
            throw new YunAiBusinessException(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
        }

        return checkResultVO;
    }

    @Override
    public CheckResultVO checkTextName(Boolean isOpen, CheckTextReqDTO checkTextReqDTO) {
        if (!Boolean.TRUE.equals(isOpen)) {
            log.info("【文本送审】【知识库名称】送审对接关闭，直接返回送审成功 checkTextReqDTO：{}", JSON.toJSONString(checkTextReqDTO));
            return CheckResultVO.builder().success(true).taskId(checkTextReqDTO.getTaskId()).build();
        }
        String content = checkTextReqDTO.getContent();
        if (null != content) {
            //去除左右空格
            content = content.trim();
        }
        if (CharSequenceUtil.isEmpty(content)) {
            log.info("【文本送审】【知识库名称】送审内容为空：{}", JSON.toJSONString(checkTextReqDTO));
            return CheckResultVO.builder().success(true).taskId(checkTextReqDTO.getTaskId()).build();
        }

        log.info("【文本送审】【知识库名称】文本送审，送审参数为：{}", JsonUtil.toJson(checkTextReqDTO));
        try {
            return checkSystemClient.checkText(checkTextReqDTO);
        } catch (YunAiBusinessException e) {
            if ("********".equals(e.getCode())) {
                // 敏感词错误码转换
                throw new YunAiBusinessException(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
            }
            throw e;
        }
    }

    @Override
    public CheckResultVO checkTextDescription(Boolean isOpen, CheckTextReqDTO checkTextReqDTO) {
        if (!Boolean.TRUE.equals(isOpen)) {
            log.info("【文本送审】【知识库描述】送审对接关闭，直接返回送审成功 checkTextReqDTO：{}", JSON.toJSONString(checkTextReqDTO));
            return CheckResultVO.builder().success(true).taskId(checkTextReqDTO.getTaskId()).build();
        }
        String content = checkTextReqDTO.getContent();
        if (null != content) {
            //去除左右空格
            content = content.trim();
        }
        if (CharSequenceUtil.isEmpty(content)) {
            log.info("【文本送审】【知识库描述】送审内容为空：{}", JSON.toJSONString(checkTextReqDTO));
            return CheckResultVO.builder().success(true).taskId(checkTextReqDTO.getTaskId()).build();
        }

        log.info("【文本送审】【知识库描述】文本送审，送审参数为：{}", JsonUtil.toJson(checkTextReqDTO));
        try {
            return checkSystemClient.checkText(checkTextReqDTO);
        } catch (YunAiBusinessException e) {
            if ("********".equals(e.getCode())) {
                // 敏感词错误码转换
                throw new YunAiBusinessException(ResultCodeEnum.SENSITIVE_WORDS_ERROR);
            }
            throw e;
        }
    }

    @Override
    public CheckResultVO checkFile(String userId, String fileId, String fileSuffix, Integer type) {
        Long taskId = uidGenerator.getUID();
        log.info("开始送审附件， 任务id={}，文件id={}",taskId , fileId);
        try {
            //独立空间下载文件
            OwnerDriveFileDownloadReqDTO fileDownloadReqDTO = new OwnerDriveFileDownloadReqDTO();
            fileDownloadReqDTO.setFileId(fileId);
            fileDownloadReqDTO.setUserId(userId);
            fileDownloadReqDTO.setExpireSec(86400);
            log.info("送审附件，获取独立空间文件下载信息，入参:{}", JSONUtil.toJsonStr(fileDownloadReqDTO));
            OwnerDriveFileDownloadVO downloadVo = ownerDriveClient.getFileDownload(fileDownloadReqDTO);
            log.info("送审附件，获取独立空间文件下载信息, 响应:{}", JSONUtil.toJsonStr(downloadVo));
            if (java.util.Objects.isNull(downloadVo)) {
                return null;
            }
            String inlineUrl = downloadVo.getUrl();
            if (StringUtils.isEmpty(inlineUrl)) {
                log.info("送审附件，文件id={}, 送审时下载的文件内容为空。", fileId);
                return null;
            }
            CheckAIFileVO file = CheckAIFileVO.builder()
                    .content(inlineUrl)
                    .contentId(fileId)
                    .driveId(fileId)
                    .fileName(fileId)
                    .fileSize(downloadVo.getSize())
                    .fileSuffix(fileSuffix)
                    .build();
            // 送审对象
            CheckSystemContentTypeEnum contentTypeEnum = CheckSystemContentTypeEnum.OTHER;
            if(StrUtil.isNotBlank(fileSuffix)){
                contentTypeEnum= checkSystemProperties.getCheckSystemContentTypeBySuffix(fileSuffix);
            }
            GetUserInfoByPhoneNumberRespDTO userInfo = new GetUserInfoByPhoneNumberRespDTO();
            userInfo.setUserDomainId(Long.parseLong(userId));
            Map<String, String> map = new HashMap<>(Const.NUM_16);
            map.put("userId", userId);
            if(CheckSystemContentTypeEnum.IMAGE.equals(contentTypeEnum)){
                CheckImageReqDTO req = new CheckImageReqDTO();
                req.setTaskId(String.valueOf(taskId));
                req.setUserInfo(userInfo);
                req.setFile(file);
                req.setExtInfo(map);
                checkSystemClient.checkImageOriginal(req);
            } else {
                CheckTextFileReqDTO req = new CheckTextFileReqDTO();
                req.setTaskId(String.valueOf(taskId));
                req.setUserInfo(userInfo);
                req.setFile(file);
                req.setExtInfo(map);
                return checkSystemClient.checkFile(true, contentTypeEnum.getType(), type, req);
            }
        } catch (Exception e) {
            log.error("送审附件，任务id={}，文件id={}，送审异常:", taskId,fileId, e);
        }
        return null;
    }

}