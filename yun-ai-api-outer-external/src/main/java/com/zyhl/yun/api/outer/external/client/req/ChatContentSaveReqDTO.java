package com.zyhl.yun.api.outer.external.client.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 会话信息保存请求DTO
 *
 * <AUTHOR>
 * @date 2024/03/06
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChatContentSaveReqDTO {

    /**
     * 会话id
     */
    private String sessionId;

    /**
     * 对话ID
     */
    private String dialogueId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 对话类型;0:对话历史记录,1:智囊历史记录
     */
    private Integer talkType;

    /**
     * 资源类型;0-无，1 邮件， 2 笔记， 3 图片
     */
    private Integer resourceType;

    /**
     * 工具指令 对接意图指令
     */
    private String toolsCommand;

    /**
     * 算法编码，跟算法库表关联，指定调哪个厂商的算法。
     */
    private String algorithmCode;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 大模型类型 qwen：通义千问，xfyun：讯飞星火，blian:百炼
     */
    private String modelType;

    /**
     * 输入资源ID 笔记id/邮件id/图片文件id；纯文本时为空
     */
    private String inResourceId;

    /**
     * 输入资源内容 笔记内容/邮件内容/图片文件；纯文本时为空
     */
    private String inResourceContent;

    /**
     * 用户输入内容文本
     */
    private String inContent;

    /**
     * 输入内容审核结果，默认值为-1 状态码：2通过，其他失败
     */
    private String inAuditStatus;

    /**
     * 输出内容（非必填，存在同步结果）
     */
    private String outContent;

    /**
     * 输出内容审批结果;状态码：2通过，其他失败（默认为-1，非必填，存在输出内容需填写）
     */
    private String outAuditStatus;

    /**
     * 渠道来源
     */
    private String sourceChannel;

    /**
     * paas底座平台编码;0 华为主平台(ose) 1 阿里pds 2 华为云空间 3 云能dsp（非必填，默认为null）
     */
    private String belongsPlatform;

    /**
     * 扩展信息
     */
    private String extInfo;

}
