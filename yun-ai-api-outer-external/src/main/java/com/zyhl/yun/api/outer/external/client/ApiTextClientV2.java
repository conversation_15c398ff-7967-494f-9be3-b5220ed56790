package com.zyhl.yun.api.outer.external.client;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.external.client.interceptor.ApiTextFeignInterceptorV2;
import com.zyhl.yun.api.outer.external.client.req.text.IntelligentSearchActivityReq;
import com.zyhl.yun.api.outer.external.client.req.text.IntelligentSearchDiscoveryReq;
import com.zyhl.yun.api.outer.external.client.req.text.IntelligentSearchFunctionReq;
import com.zyhl.yun.api.outer.external.client.resp.text.IntelligentSearchActivityRsp;
import com.zyhl.yun.api.outer.external.client.resp.text.IntelligentSearchDiscoveryRsp;
import com.zyhl.yun.api.outer.external.client.resp.text.IntelligentSearchFunctionRsp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 文本工具Client（v2版本）
 * @Author: WeiJ<PERSON><PERSON><PERSON>
 */
@FeignClient(
        name = "yun-ai-api-text-v2",
        url = "${external.api-text.url}",
        path = "${external.api-text.path}",
        configuration = ApiTextFeignInterceptorV2.class)
public interface ApiTextClientV2 {

    /**
     * 发现广场搜索V2
     * @Author: WeiJingKun
     * @param req 文本工具Client-请求参数-发现广场搜索V2
     * @return 文本工具Client-响应结果-发现广场搜索V2
     */
    @PostMapping(value = "/intelligent/search/discovery")
    BaseResult<IntelligentSearchDiscoveryRsp> intelligentSearchDiscoveryV2(IntelligentSearchDiscoveryReq req);

    /**
     * 功能搜索V2
     * @Author: WeiJingKun
     * @param req 文本工具Client-请求参数-功能搜索V2
     * @return 文本工具Client-响应结果-功能搜索V2
     */
    @PostMapping(value = "/intelligent/search/function")
    BaseResult<IntelligentSearchFunctionRsp> intelligentSearchFunctionV2(IntelligentSearchFunctionReq req);

    /**
     * 活动搜索V2
     * @Author: WeiJingKun
     * @param req 文本工具Client-请求参数-活动搜索V2
     * @return 文本工具Client-响应结果-活动搜索V2
     */
    @PostMapping(value = "/intelligent/search/activity")
    BaseResult<IntelligentSearchActivityRsp> intelligentSearchActivityV2(IntelligentSearchActivityReq req);

}
