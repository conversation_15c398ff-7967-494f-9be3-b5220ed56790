package com.zyhl.yun.api.outer.external.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 文件查询请求参数
 * @Author: zzb
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AiFileSearchPlatformReqDto {
    private Condition conditions;

    private SearchPlatformPageInfo showInfo;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class  Condition{
        private String userId;
        private String owner;
        private Integer type;
        private List<String> nameList;
        private List<String> addressList;
        private List<String> tagList;
        private List<SearchPlatformTimeInfo> timeList;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class  SortInfo{

        @NotEmpty(message = "排序字段不能为空")
        private String field;


        @NotNull(message = "排序顺序不能为空")
        private Boolean reverse;


    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class  SearchPlatformTimeInfo{

        @NotEmpty(message = "搜索范围的开始时间不能为空")
        @JsonFormat(pattern = "yyyyMMddHHmmss", timezone = "GMT+8")
        private String startTime;


        @NotEmpty(message = "搜索范围的结束时间不能为空")
        @JsonFormat(pattern = "yyyyMMddHHmmss", timezone = "GMT+8")
        private String endTime;


    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class SearchPlatformPageInfo {

        @NotNull
        @Min(0)
        @Max(200)
        private Integer pageNum;
        private List<Object> pageAfter;
        private List<SortInfo> sortInfos;

    }
}
