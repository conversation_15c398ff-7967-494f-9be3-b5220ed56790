package com.zyhl.yun.api.outer.external.client.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Classname NoteDetailClientReq
 * @Description 远程调用笔记详情入参req
 * @Date 2024/3/1 10:17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NoteDetailClientReq {

    /**
     * 笔记Id
     */
    private String noteId;

    /**
     * 是否查询回收站的笔记，传true时候为是，不传或者传false时候为否
     */
    private Boolean recycleFlag = true;

}
