package com.zyhl.yun.api.outer.external.service;


import java.util.List;

import com.zyhl.yun.api.outer.domain.dto.UserInfoDTO;
import com.zyhl.yun.api.outer.external.client.req.QryRoutePolicyReqDTO;
import com.zyhl.yun.api.outer.external.client.resp.UserDomainRspDTO;
import com.zyhl.yun.api.outer.external.client.resp.UserRoutePolicyDTO;

/**
 * 用户服务类
 * 
 * <AUTHOR>
 * @description
 * @date 2023/4/6 上午11:11
 */
public interface UserEtnService {

    /**
     * 根据token获取用户信息
     *
     * @param token token
     * @return UserDomainRspDTO
     */
    UserDomainRspDTO queryUserIdByToken(String token);

    /**
     * 根据userId获取路由策略
     *
     * @param userId userId
     * @return UserRoutePolicyDTO
     */
    UserRoutePolicyDTO qryRoutePolicy(Long userId);

    /**
     * 根据userId获取路由策略
     *
     * @param qryRoutePolicyReqDTO 请求参数
     * @return UserRoutePolicyDTO
     */
    UserRoutePolicyDTO qryRoutePolicy(QryRoutePolicyReqDTO qryRoutePolicyReqDTO);

    /**
     * 获取用户信息
     *
     * @param userId userId
     * @return UserDomainRspDTO
     */
    UserDomainRspDTO getUserInfo(Long userId);

    /**
     * 获取用户信息，参数二选一
     *
     * @param userId 用户id
     * @return 用户信息
     */
    UserInfoDTO getUserInfoData(Long userId);

    /**
     * 批量查询用户头像信息
     *
     * @param userIds 用户id集合
     * @return
     */
    List<UserInfoDTO> getUserAvatarBatch(List<Long> userIds);

	/**
	 * 获取用户统一认证token
	 * 
	 * @param token 请求参数
	 * @return 统一认证token
	 */
	String querySpecToken(String token);

	/**
	 * 获取用户 basic token
	 * 
	 * @param stToken  统一认证token
	 * @param sourceId 统一认证SourceId
	 * @return basic token
	 */
	String loginByStToken(String stToken, String sourceId);

}
