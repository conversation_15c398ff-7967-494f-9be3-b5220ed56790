package com.zyhl.yun.api.outer.external.client.resp.user;

import lombok.Data;

/**
 * 用户简介信息实体类
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
public class UserProfileInfo {
    /**
     * 头像资源ID
     */
    private Long userAvatarId;

    /**
     * 缩略图头像资源ID
     */
    private String miniUserAvatarId;

    /**
     * 默认头像资源ID（默认取这个）
     */
    private String defaultUserAvatarId;

    /**
     * 默认缩略图头像资源ID
     */
    private String defaultMiniUserAvatarId;

    /**
     * 审核状态，详见审核状态枚举说明
     */
    private Integer portraitApprovalStatus;

    /**
     * 头像小缩略图地址
     */
    private String miniUserAvatatUrl;

    /**
     * 头像大缩略图地址
     */
    private String bigAvatarUrl;

    /**
     * 头像地址
     */
    private String avatarUrl;

    /**
     * 头像地址过期时间，格式 YYYYMMDDHHMMSS
     */
    private String expires;

    /**
     * 默认头像地址
     */
    private String defaultPortraitUrl;

    /**
     * 默认头像大缩略图地址
     */
    private String defaultBigAvatarUrl;

    /**
     * 默认头像小缩略图地址
     */
    private String defaultAvatarUrl;

    /**
     * 是否默认头像
     * 1：是
     * 0：否
     */
    private Integer isDefault;

    /**
     * 昵称
     */
    private String userName;

    /**
     * 审核状态，详见审核状态枚举说明
     */
    private String nameApprovalStatus;

    /**
     * 默认昵称
     */
    private String defaultUserName;

    /**
     * 昵称修改标识
     * 0-未修改
     * 1-已修改
     */
    private Integer modifyNickNameFlag;

    /**
     * 头像修改标识
     * 0-未修改
     * 1-已修改
     */
    private Integer modifyPortraitFlag;

    /**
     * 昵称最后修改时间，格式 YYYYMMDDHHMMSS, 24 小时格式
     */
    private String modifyNickNameLastTime;

    /**
     * 头像最后修改时间，格式 YYYYMMDDHHMMSS, 24 小时格式
     */
    private String modifyPortraitLastTime;

    /**
     * 头像是否同步家庭云标识字段：
     * 0：不同步（接口默认0）
     * 1：同步头像到家庭云(portraitId非空时有效)
     * 为空时默认为0
     * V2：该字段无效，代码默认设置为0
     */
    private Integer syncFlag;


}