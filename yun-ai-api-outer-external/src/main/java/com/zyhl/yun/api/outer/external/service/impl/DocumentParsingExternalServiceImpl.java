package com.zyhl.yun.api.outer.external.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;

import com.zyhl.hcy.yun.ai.common.base.document.dto.ParsingReqDTO;
import com.zyhl.hcy.yun.ai.common.base.document.dto.ParsingRespDTO;
import com.zyhl.hcy.yun.ai.common.base.document.parsing.TextExtractClient;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.FileOperationUtil;
import com.zyhl.yun.api.outer.domain.vo.DocumentParsingResultVO;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.external.DocumentParsingExternalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 描述：文档解析对外服务实现
 *
 * <AUTHOR> zhumaoxian  2025/2/26 14:14
 */
@Slf4j
@Service
public class DocumentParsingExternalServiceImpl implements DocumentParsingExternalService {


    @Override
    public DocumentParsingResultVO parsingAfterDelete(List<String> localpathList, Integer maxLength) {
    	try {
    		return parsing(localpathList, maxLength);
    	}finally {
    		log.info("删除本地共享存储:{}", JSONUtil.toJsonStr(localpathList));
            FileOperationUtil.deleteLocalFiles(localpathList);
		}
    }

    @Override
    public DocumentParsingResultVO parsing(List<String> localpathList, Integer maxLength) {

        StringBuilder sb = new StringBuilder();
        ParsingReqDTO req = new ParsingReqDTO();
        req.setResultLength(maxLength);
        for (String localpath : localpathList) {
            if (Objects.nonNull(maxLength) && sb.length() >= maxLength) {
                break;
            }
            req.setLocalPath(localpath);
            try {
                ParsingRespDTO resp = TextExtractClient.extract(req);
                if (Objects.nonNull(resp) && ObjectUtil.isNotEmpty(resp.getText())) {
                    sb.append(resp.getText()).append("\n");
                }
            } catch (Exception e) {
                log.error("【文档解析】文档解析失败 localpathList:{}, error:", JSONUtil.toJsonStr(localpathList), e);
                if(e instanceof YunAiBusinessException) {
                	throw (YunAiBusinessException)e;
                }
                // 文件解析失败
                throw new YunAiBusinessException(AiResultCode.CODE_10000016.getCode(), AiResultCode.CODE_10000016.getMsg());
            }
        }

        // 返回结果
        DocumentParsingResultVO resultDTO = new DocumentParsingResultVO();
        if (Objects.nonNull(maxLength) && sb.length() > maxLength) {
            resultDTO.setText(sb.substring(0, maxLength));
        } else {
            resultDTO.setText(sb.toString());
        }
		log.info("localpathList:{}, maxLength:{}, parsing content size:{}", JSONUtil.toJsonStr(localpathList),
				maxLength, (null != resultDTO.getText() ? resultDTO.getText().length() : 0));
        return resultDTO;
    }
}
