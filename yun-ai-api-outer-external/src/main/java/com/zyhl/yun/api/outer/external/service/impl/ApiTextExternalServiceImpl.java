package com.zyhl.yun.api.outer.external.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.annotation.MethodExecutionTimeLog;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.config.SearchResultProperties;
import com.zyhl.yun.api.outer.config.WhiteListProperties;
import com.zyhl.yun.api.outer.domain.vo.chat.search.param.*;
import com.zyhl.yun.api.outer.domain.vo.chat.search.result.*;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.external.ApiTextExternalService;
import com.zyhl.yun.api.outer.external.assembler.ApiTextAssembler;
import com.zyhl.yun.api.outer.external.client.ApiTextClient;
import com.zyhl.yun.api.outer.external.client.ApiTextClientV2;
import com.zyhl.yun.api.outer.external.client.ApiTextClientV3;
import com.zyhl.yun.api.outer.external.client.req.text.*;
import com.zyhl.yun.api.outer.external.client.resp.text.*;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.util.VersionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 文本工具-ExternalServiceImpl
 *
 * @Author: WeiJingKun
 */
@Slf4j
@Service
public class ApiTextExternalServiceImpl implements ApiTextExternalService {

    @Resource
    private ApiTextClient apiTextClient;

    @Resource
    private ApiTextClientV2 apiTextClientV2;

    @Resource
    private ApiTextClientV3 apiTextClientV3;

    @Resource
    private ApiTextAssembler apiTextAssembler;

    @Resource
    private WhiteListProperties whiteListProperties;

    @Resource
    private SearchResultProperties searchResultProperties;

    @Override
    @MethodExecutionTimeLog("语义搜图-external")
    public SearchImageResult searchImage(SearchImageParam searchParam) {
        IntelligentSearchImageReq req = IntelligentSearchImageReq.builder().build();
        BaseResult<IntelligentSearchImageRsp> baseResult = null;
        IntelligentSearchImageRsp rsp = null;
        SearchImageResult result = null;
        try {
            /** 构建接口请求参数 */
            req.setUserId(RequestContextHolder.getUserId());
            req.setSearchParam(searchParam);

            /** 语义搜图 */
            baseResult = apiTextClient.intelligentSearchImage(req);
            if (null == baseResult) {
                log.warn("语义搜图-ApiTextExternalServiceImpl-intelligentSearchImage-fail，result is null");
            } else {
                if (baseResult.isSuccess()) {
                    rsp = baseResult.getData();
                    result = apiTextAssembler.toSearchImageResult(rsp);
                } else {
                    log.warn("语义搜图-ApiTextExternalServiceImpl-intelligentSearchImage-下游响应错误结果");
                }
            }
            // set搜索结果-公共数据
            if (null != result && CollUtil.isNotEmpty(result.getFileList())) {
                SearchResultProperties.TabSort tabSort = searchResultProperties.getTabSort(searchParam.getClass());
                result.setSearchCommonResult(tabSort, searchParam.getDialogue(), searchParam, apiTextAssembler.toSearchImageResult(rsp));
            }
            return result;
        } catch (Exception e) {
            log.error("语义搜图-ApiTextExternalServiceImpl-intelligentSearchImage-异常信息: ", e);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        } finally {
            log.info("语义搜图-ApiTextExternalServiceImpl-intelligentSearchImage-finally\n req：{}\n baseResult：{}",
                    JSON.toJSONString(req), JSON.toJSONString(baseResult));
        }
    }

    @Override
    @MethodExecutionTimeLog("个人云资产搜索-external")
    public SearchFileResult searchFile(SearchFileParam searchParam) {
        IntelligentSearchFileReq req = new IntelligentSearchFileReq();
        BaseResult<IntelligentSearchFileRsp> baseResult = null;
        IntelligentSearchFileRsp rsp = null;
        SearchFileResult result = null;
        try {
            /** 构建接口请求参数 */
            req.setUserId(RequestContextHolder.getUserId());
            req.setSearchParam(searchParam);

            /** 个人云资产搜索 */
            /** h5Version < 小天1.3.0版本，调用个人云资产v1接口 */
            String h5Version = searchParam.getH5Version();
            if (CharSequenceUtil.isNotBlank(h5Version) && !VersionUtil.xtH5VersionGte130(h5Version)) {
                baseResult = apiTextClient.intelligentSearchFile(req);
                log.info("调用个人云资产v1接口");
            } else {
                baseResult = apiTextClientV3.intelligentSearchFileV3(req);
                log.info("调用个人云资产v3接口");
            }
            if (null == baseResult) {
                log.warn("个人云资产搜索-ApiTextExternalServiceImpl-intelligentSearchFile-fail，result is null");
            } else {
                if (baseResult.isSuccess()) {
                    rsp = baseResult.getData();
                    result = apiTextAssembler.toSearchFileResult(rsp);
                } else {
                    log.warn("个人云资产搜索-ApiTextExternalServiceImpl-intelligentSearchFile-下游响应错误结果");
                }
            }
            // set搜索结果-公共数据
            if (null != result && CollUtil.isNotEmpty(result.getFileList())) {
                SearchResultProperties.TabSort tabSort = searchResultProperties.getTabSort(searchParam.getClass());
                result.setSearchCommonResult(tabSort, searchParam.getDialogue(), searchParam, apiTextAssembler.toSearchFileResult(rsp));
            }
            return result;
        } catch (Exception e) {
            log.error("个人云资产搜索-ApiTextExternalServiceImpl-intelligentSearchFile-异常信息: ", e);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        } finally {
            log.info("个人云资产搜索-ApiTextExternalServiceImpl-intelligentSearchFile-finally\n req：{}\n baseResult：{}",
                    JSON.toJSONString(req), JSON.toJSONString(baseResult));
        }
    }

    @Override
    @MethodExecutionTimeLog("笔记搜索-external")
    public SearchNoteResult searchNote(SearchNoteParam searchParam) {
        IntelligentSearchNoteReq req = new IntelligentSearchNoteReq();
        BaseResult<IntelligentSearchNoteRsp> baseResult = null;
        IntelligentSearchNoteRsp rsp = null;
        SearchNoteResult result = null;
        try {
            /** 构建接口请求参数 */
            req.setUserId(RequestContextHolder.getUserId());
            req.setSearchParam(searchParam);

            /** 笔记搜索 */
            baseResult = apiTextClient.intelligentSearchNote(req);
            if (null == baseResult) {
                log.warn("笔记搜索-ApiTextExternalServiceImpl-intelligentSearchNote-fail，result is null");
            } else {
                if (baseResult.isSuccess()) {
                    rsp = baseResult.getData();
                    result = apiTextAssembler.toSearchNoteResult(rsp);
                } else {
                    log.warn("笔记搜索-ApiTextExternalServiceImpl-intelligentSearchNote-下游响应错误结果");
                }
            }
            // set搜索结果-公共数据
            if (null != result && CollUtil.isNotEmpty(result.getNoteList())) {
                SearchResultProperties.TabSort tabSort = searchResultProperties.getTabSort(searchParam.getClass());
                result.setSearchCommonResult(tabSort, searchParam.getDialogue(), searchParam, apiTextAssembler.toSearchNoteResult(rsp));
            }
            return result;
        } catch (Exception e) {
            log.error("笔记搜索-ApiTextExternalServiceImpl-intelligentSearchNote-异常信息: ", e);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        } finally {
            log.info("笔记搜索-ApiTextExternalServiceImpl-intelligentSearchNote-finally\n req：{}\n baseResult：{}",
                    JSON.toJSONString(req), JSON.toJSONString(baseResult));
        }
    }

    @Override
    @MethodExecutionTimeLog("功能搜索-external")
    public SearchFunctionResult searchFunction(SearchFunctionParam searchParam) {
        IntelligentSearchFunctionReq req = new IntelligentSearchFunctionReq();
        BaseResult<IntelligentSearchFunctionRsp> baseResult = null;
        IntelligentSearchFunctionRsp rsp = null;
        SearchFunctionResult result = null;
        try {
            /** 构建接口请求参数 */
            req.setUserId(RequestContextHolder.getUserId());
            req.setSearchParam(searchParam);

            /** h5Version < 小天2.0.3版本，调用功能搜索v1接口 */
            String h5Version = searchParam.getH5Version();
            log.info("功能搜索-版本号打印：{}", h5Version);
            if (VersionUtil.xtH5VersionLt203(h5Version)) {
                log.info("调用功能搜索v1接口");
                baseResult = apiTextClient.intelligentSearchFunction(req);
            } else {
                log.info("调用功能搜索v2接口");
                baseResult = apiTextClientV2.intelligentSearchFunctionV2(req);
            }

            if (null == baseResult) {
                log.warn("功能搜索-ApiTextExternalServiceImpl-intelligentSearchFunction-fail，result is null");
            } else {
                if (baseResult.isSuccess()) {
                    rsp = baseResult.getData();
                    result = apiTextAssembler.toSearchFunctionResult(rsp);
                } else {
                    log.warn("功能搜索-ApiTextExternalServiceImpl-intelligentSearchFunction-下游响应错误结果");
                }
            }
            // set搜索结果-公共数据
            if (null != result && CollUtil.isNotEmpty(result.getFunctionList())) {
                SearchResultProperties.TabSort tabSort = searchResultProperties.getTabSort(searchParam.getClass());
                result.setSearchCommonResult(tabSort, searchParam.getDialogue(), searchParam, apiTextAssembler.toSearchFunctionResult(rsp));
            }
            return result;
        } catch (Exception e) {
            log.error("功能搜索-ApiTextExternalServiceImpl-intelligentSearchFunction-异常信息: ", e);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        } finally {
            log.info("功能搜索-ApiTextExternalServiceImpl-intelligentSearchFunction-finally\n req：{}\n baseResult：{}",
                    JSON.toJSONString(req), JSON.toJSONString(baseResult));
        }
    }

    @Override
    @MethodExecutionTimeLog("活动搜索-external")
    public SearchActivityResult searchActivity(SearchActivityParam searchParam) {
        IntelligentSearchActivityReq req = new IntelligentSearchActivityReq();
        BaseResult<IntelligentSearchActivityRsp> baseResult = null;
        IntelligentSearchActivityRsp rsp = null;
        SearchActivityResult result = null;
        try {
            /** 构建接口请求参数 */
            req.setUserId(RequestContextHolder.getUserId());
            req.setSearchParam(searchParam);

            /** h5Version < 小天2.0.3版本，调用活动搜索v1接口 */
            String h5Version = searchParam.getH5Version();
            log.info("活动搜索-版本号打印：{}", h5Version);
            if (VersionUtil.xtH5VersionLt203(h5Version)) {
                log.info("调用活动搜索v1接口");
                baseResult = apiTextClient.intelligentSearchActivity(req);
            } else {
                log.info("调用活动搜索v2接口");
                baseResult = apiTextClientV2.intelligentSearchActivityV2(req);
            }

            if (null == baseResult) {
                log.warn("活动搜索-ApiTextExternalServiceImpl-intelligentSearchActivity-fail，result is null");
            } else {
                if (baseResult.isSuccess()) {
                    rsp = baseResult.getData();
                    result = apiTextAssembler.toSearchActivityResult(rsp);
                } else {
                    log.warn("活动搜索-ApiTextExternalServiceImpl-intelligentSearchActivity-下游响应错误结果");
                }
            }
            // set搜索结果-公共数据
            if (null != result && CollUtil.isNotEmpty(result.getActivityList())) {
                SearchResultProperties.TabSort tabSort = searchResultProperties.getTabSort(searchParam.getClass());
                result.setSearchCommonResult(tabSort, searchParam.getDialogue(), searchParam, apiTextAssembler.toSearchActivityResult(rsp));
            }
            return result;
        } catch (Exception e) {
            log.error("活动搜索-ApiTextExternalServiceImpl-intelligentSearchActivity-异常信息: ", e);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        } finally {
            log.info("活动搜索-ApiTextExternalServiceImpl-intelligentSearchActivity-finally\n req：{}\n baseResult：{}",
                    JSON.toJSONString(req), JSON.toJSONString(baseResult));
        }
    }

    @Override
    @MethodExecutionTimeLog("发现广场搜索-external")
    public SearchDiscoveryResult searchDiscovery(SearchDiscoveryParam searchParam) {
        IntelligentSearchDiscoveryReq req = new IntelligentSearchDiscoveryReq();
        BaseResult<IntelligentSearchDiscoveryRsp> baseResult = null;
        IntelligentSearchDiscoveryRsp rsp = null;
        SearchDiscoveryResult result = null;
        try {
            /** 构建接口请求参数 */
            req.setUserId(RequestContextHolder.getUserId());
            req.setSearchParam(searchParam);

            /** 发现广场搜索 */
            /** h5Version < 小天1.5.1版本，调用发现广场搜索v1接口 */
            String h5Version = searchParam.getH5Version();
            if (CharSequenceUtil.isNotBlank(h5Version) && VersionUtil.xtH5VersionLt151(h5Version)) {
                baseResult = apiTextClient.intelligentSearchDiscovery(req);
                log.info("调用发现广场搜索v1接口");
            } else {
                baseResult = apiTextClientV2.intelligentSearchDiscoveryV2(req);
                log.info("调用发现广场搜索v2接口");
            }

            if (null == baseResult) {
                log.warn("发现广场搜索-ApiTextExternalServiceImpl-intelligentSearchDiscovery-fail，result is null");
            } else {
                if (baseResult.isSuccess()) {
                    rsp = baseResult.getData();
                    result = apiTextAssembler.toSearchDiscoveryResult(rsp);
                } else {
                    log.warn("发现广场搜索-ApiTextExternalServiceImpl-intelligentSearchDiscovery-下游响应错误结果");
                }
            }
            // set搜索结果-公共数据
            if (null != result && CollUtil.isNotEmpty(result.getDiscoveryList())) {
                SearchResultProperties.TabSort tabSort = searchResultProperties.getTabSort(searchParam.getClass());
                result.setSearchCommonResult(tabSort, searchParam.getDialogue(), searchParam, apiTextAssembler.toSearchDiscoveryResult(rsp));
            }
            return result;
        } catch (Exception e) {
            log.error("发现广场搜索-ApiTextExternalServiceImpl-intelligentSearchDiscovery-异常信息: ", e);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        } finally {
            log.info("发现广场搜索-ApiTextExternalServiceImpl-intelligentSearchDiscovery-finally\n req：{}\n baseResult：{}",
                    JSON.toJSONString(req), JSON.toJSONString(baseResult));
        }
    }

    @Override
    @MethodExecutionTimeLog("我的圈子搜索-external")
    public SearchMyGroupResult searchMyGroup(SearchMyGroupParam searchParam) {
        IntelligentSearchMyGroupReq req = new IntelligentSearchMyGroupReq();
        BaseResult<IntelligentSearchMyGroupRsp> baseResult = null;
        IntelligentSearchMyGroupRsp rsp = null;
        SearchMyGroupResult result = null;
        try {
            /** 构建接口请求参数 */
            req.setUserId(RequestContextHolder.getUserId());
            req.setSearchParam(searchParam);

            /** 我的圈子搜索 */
            baseResult = apiTextClient.intelligentSearchMyGroup(req);
            if (null == baseResult) {
                log.warn("我的圈子搜索-ApiTextExternalServiceImpl-intelligentSearchMyGroup-fail，result is null");
            } else {
                if (baseResult.isSuccess()) {
                    rsp = baseResult.getData();
                    result = apiTextAssembler.toSearchGroupMyResult(rsp);
                } else {
                    log.warn("我的圈子搜索-ApiTextExternalServiceImpl-intelligentSearchMyGroup-下游响应错误结果");
                }
            }
            // set搜索结果-公共数据
            if (null != result && CollUtil.isNotEmpty(result.getGroupList())) {
                SearchResultProperties.TabSort tabSort = searchResultProperties.getTabSort(searchParam.getClass());
                result.setSearchCommonResult(tabSort, searchParam.getDialogue(), searchParam, apiTextAssembler.toSearchGroupMyResult(rsp));
            }
            return result;
        } catch (Exception e) {
            log.error("我的圈子搜索-ApiTextExternalServiceImpl-intelligentSearchMyGroup-异常信息: ", e);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        } finally {
            log.info("我的圈子搜索-ApiTextExternalServiceImpl-intelligentSearchMyGroup-finally\n req：{}\n baseResult：{}",
                    JSON.toJSONString(req), JSON.toJSONString(baseResult));
        }
    }

    @Override
    @MethodExecutionTimeLog("热门圈子搜索-external")
    public SearchRecommendGroupResult searchRecommendGroup(SearchRecommendGroupParam searchParam) {
        IntelligentSearchRecommendGroupReq req = new IntelligentSearchRecommendGroupReq();
        BaseResult<IntelligentSearchRecommendGroupRsp> baseResult = null;
        IntelligentSearchRecommendGroupRsp rsp = null;
        SearchRecommendGroupResult result = null;
        try {
            /** 构建接口请求参数 */
            req.setUserId(RequestContextHolder.getUserId());
            req.setSearchParam(searchParam);

            /** 热门圈子搜索 */
            baseResult = apiTextClient.intelligentSearchRecommendGroup(req);
            if (null == baseResult) {
                log.warn("热门圈子搜索-ApiTextExternalServiceImpl-intelligentSearchRecommendGroup-fail，result is null");
            } else {
                if (baseResult.isSuccess()) {
                    rsp = baseResult.getData();
                    result = apiTextAssembler.toSearchGroupRecommendResult(rsp);
                } else {
                    log.warn("热门圈子搜索-ApiTextExternalServiceImpl-intelligentSearchRecommendGroup-下游响应错误结果");
                }
            }
            // set搜索结果-公共数据
            if (null != result && CollUtil.isNotEmpty(result.getGroupList())) {
                SearchResultProperties.TabSort tabSort = searchResultProperties.getTabSort(searchParam.getClass());
                result.setSearchCommonResult(tabSort, searchParam.getDialogue(), searchParam, apiTextAssembler.toSearchGroupRecommendResult(rsp));
            }
            return result;
        } catch (Exception e) {
            log.error("热门圈子搜索-ApiTextExternalServiceImpl-intelligentSearchRecommendGroup-异常信息: ", e);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        } finally {
            log.info("热门圈子搜索-ApiTextExternalServiceImpl-intelligentSearchRecommendGroup-finally\n req：{}\n baseResult：{}",
                    JSON.toJSONString(req), JSON.toJSONString(baseResult));
        }
    }

    @Override
    @MethodExecutionTimeLog("邮件搜索-external")
    public SearchMailResult searchMail(SearchMailParam searchParam) {
        IntelligentSearchMailReq req = new IntelligentSearchMailReq();
        BaseResult<IntelligentSearchMailRsp> baseResult = null;
        IntelligentSearchMailRsp rsp = null;
        SearchMailResult result = null;
        try {
            /** 构建接口请求参数 */
            req.setUserId(RequestContextHolder.getUserId());
            req.setMailUserId(RequestContextHolder.getPhoneNumber() + whiteListProperties.getMailSearch().getSuffix());
            req.setSearchParam(searchParam);

            /** 邮件搜索 */
            baseResult = apiTextClient.intelligentSearchMail(req);
            if (null == baseResult) {
                log.warn("邮件搜索-ApiTextExternalServiceImpl-intelligentSearchMail-fail，result is null");
            } else {
                if (baseResult.isSuccess()) {
                    rsp = baseResult.getData();
                    result = apiTextAssembler.toSearchMailResult(rsp);
                } else {
                    log.warn("邮件搜索-ApiTextExternalServiceImpl-intelligentSearchMail-下游响应错误结果");
                }
            }
            // set搜索结果-公共数据
            if (null != result && CollUtil.isNotEmpty(result.getSearchMailList())) {
                SearchResultProperties.TabSort tabSort = searchResultProperties.getTabSort(searchParam.getClass());
                result.setSearchCommonResult(tabSort, searchParam.getDialogue(), searchParam, apiTextAssembler.toSearchMailResult(rsp));
            }
            return result;
        } catch (Exception e) {
            log.error("邮件搜索-ApiTextExternalServiceImpl-intelligentSearchMail-异常信息: ", e);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        } finally {
            log.info("邮件搜索-ApiTextExternalServiceImpl-intelligentSearchMail-finally\n req：{}\n baseResult：{}",
                    JSON.toJSONString(req), JSON.toJSONString(baseResult));
        }
    }

    @Override
    @MethodExecutionTimeLog("邮件附件搜索-external")
    public SearchMailAttachmentResult searchMailAttachment(SearchMailAttachmentParam searchAttachmentParam) {
        IntelligentSearchMailAttachmentReq req = new IntelligentSearchMailAttachmentReq();
        BaseResult<IntelligentSearchMailAttachmentRsp> baseResult = null;
        IntelligentSearchMailAttachmentRsp rsp = null;
        SearchMailAttachmentResult result = null;
        try {
            /** 构建接口请求参数 */
            req.setUserId(RequestContextHolder.getUserId());
            req.setMailUserId(RequestContextHolder.getPhoneNumber() + whiteListProperties.getMailSearch().getSuffix());
            req.setSearchParam(searchAttachmentParam);

            /** 邮件附件搜索 */
            baseResult = apiTextClient.intelligentSearchMailAttachment(req);
            if (null == baseResult) {
                log.warn("邮件附件搜索-ApiTextExternalServiceImpl-intelligentSearchMailAttachment-fail，result is null");
            } else {
                if (baseResult.isSuccess()) {
                    rsp = baseResult.getData();
                    result = apiTextAssembler.toSearchMailAttachmentResult(rsp);
                } else {
                    log.warn("邮件附件搜索-ApiTextExternalServiceImpl-intelligentSearchMailAttachment-下游响应错误结果");
                }
            }
            // 邮件附件搜索结果-公共数据
            if (null != result && CollUtil.isNotEmpty(result.getSearchMailAttachmentList())) {
                SearchResultProperties.TabSort tabSort = searchResultProperties.getTabSort(searchAttachmentParam.getClass());
                result.setSearchCommonResult(tabSort, searchAttachmentParam.getDialogue(), searchAttachmentParam, apiTextAssembler.toSearchMailAttachmentResult(rsp));
            }
            return result;
        } catch (Exception e) {
            log.error("邮件附件搜索-ApiTextExternalServiceImpl-intelligentSearchMailAttachment-异常信息: ", e);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        } finally {
            log.info("邮件附件搜索-ApiTextExternalServiceImpl-intelligentSearchMailAttachment-finally\n req：{}\n baseResult：{}",
                    JSON.toJSONString(req), JSON.toJSONString(baseResult));
        }
    }

    @Override
    @MethodExecutionTimeLog("知识库资源搜索-external")
    public SearchKnowledgeBaseResourceResult searchKnowledgeBaseResource(SearchKnowledgeBaseResourceParam searchKnowledgeBaseResourceParam) {
        IntelligentSearchKnowledgeBaseResourceReq req = new IntelligentSearchKnowledgeBaseResourceReq();
        BaseResult<IntelligentSearchKnowledgeBaseResourceRsp> baseResult = null;
        IntelligentSearchKnowledgeBaseResourceRsp rsp = null;
        SearchKnowledgeBaseResourceResult result = null;
        try {
            /** 构建接口请求参数 */
            req.setUserId(RequestContextHolder.getUserId());
            req.setSearchParam(searchKnowledgeBaseResourceParam);

            /** 知识库资源搜索 */
            baseResult = apiTextClient.intelligentSearchKnowledgeBaseResource(req);
            if (null == baseResult) {
                log.warn("知识库资源搜索-ApiTextExternalServiceImpl-intelligentSearchKnowledgeBaseResource-fail，result is null");
            } else {
                if (baseResult.isSuccess()) {
                    rsp = baseResult.getData();
                    result = apiTextAssembler.toSearchKnowledgeBaseResourceResult(rsp);
                } else {
                    log.warn("知识库资源搜索-ApiTextExternalServiceImpl-intelligentSearchKnowledgeBaseResource-下游响应错误结果");
                }
            }
            // 知识库资源搜索结果-公共数据
            if (null != result && CollUtil.isNotEmpty(result.getResourceList())) {
                SearchResultProperties.TabSort tabSort = searchResultProperties.getTabSort(searchKnowledgeBaseResourceParam.getClass());
                result.setSearchCommonResult(tabSort, searchKnowledgeBaseResourceParam.getDialogue(), searchKnowledgeBaseResourceParam, apiTextAssembler.toSearchKnowledgeBaseResourceResult(rsp));
            }
            return result;
        } catch (Exception e) {
            log.error("知识库资源搜索-ApiTextExternalServiceImpl-intelligentSearchKnowledgeBaseResource-异常信息: ", e);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        } finally {
            log.info("知识库资源搜索-ApiTextExternalServiceImpl-intelligentSearchKnowledgeBaseResource-finally\n req：{}\n baseResult：{}",
                    JSON.toJSONString(req), JSON.toJSONString(baseResult));
        }
    }

}
