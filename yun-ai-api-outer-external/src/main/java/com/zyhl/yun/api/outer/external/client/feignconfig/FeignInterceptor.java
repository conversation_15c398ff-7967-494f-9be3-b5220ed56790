package com.zyhl.yun.api.outer.external.client.feignconfig;

import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.hcy.plugin.logger.util.MdcUtil;
import com.zyhl.hcy.yun.ai.common.base.constraint.HttpConstants;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.Optional;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.external.client.feignconfig.FeignInterceptor} <br>
 * <b> description:</b>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-03-05 14:31
 **/
@Slf4j
public class FeignInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        // 设置请求头
        setHeader(requestTemplate);
    }

    /**
     * 鉴权请求头
     */
    private void setHeader(RequestTemplate requestTemplate) {
        String tid = Optional.ofNullable(MDC.get(LogConstants.TRACE_ID)).orElse(MdcUtil.getDefaultTraceId());
        requestTemplate.header(LogConstants.TRACE_ID, tid);
        requestTemplate.header(HttpConstants.X_YUN_TID, tid);
        requestTemplate.header(HttpConstants.X_YUN_REQUEST_ID_HEADER, tid);
    }
}
