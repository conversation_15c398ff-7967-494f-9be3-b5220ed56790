package com.zyhl.yun.api.outer.external.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.MailClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.dto.MailDetailReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.dto.MailLoginReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.dto.RagMailDetailReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.dto.SendMailCustomizationReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.vo.MailDetailVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.vo.MailLoginVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.vo.MailResultVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.mail.vo.RagMailDetailVO;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.external.MailExternalService;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 邮件服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MailExternalServiceImpl implements MailExternalService {

    @Resource
    private MailClient mailClient;


    @Override
    public String mailContent(String sid, String rmkey, String mailId) {
        // 构建邮件详情请求DTO
        MailDetailReqDTO mailDetailReqDTO = new MailDetailReqDTO();
        mailDetailReqDTO.setSid(StrUtil.nullToEmpty(sid));
        mailDetailReqDTO.setRmKey(StrUtil.nullToEmpty(rmkey));
        mailDetailReqDTO.setMid(mailId);
        log.warn("开始请求Mail服务，请求参数:{}", JsonUtil.toJson(mailDetailReqDTO));

        // 获取邮件内容
        MailDetailVO mailDetailVO = mailClient.getMailContent(mailDetailReqDTO);
        String text = mailDetailVO.getTxtContent();
        if (StringUtils.hasText(text)) {
            return text;
        }

        log.error("邮件内容为空，mid:{}", mailDetailReqDTO.getMid());
        throw new YunAiBusinessException(AiResultCode.CODE_10000021.getCode(), AiResultCode.CODE_10000021.getMsg());
    }

    @Override
    public RagMailDetailVO getMailInfo(String phone, String mailId) {
        RagMailDetailReqDTO reqDTO = new RagMailDetailReqDTO();
        reqDTO.setMid(mailId);
        // 查询方式，1-邮箱，2-手机号，此处为2
        reqDTO.setQueryType(2);
        reqDTO.setAccount(phone);
        return mailClient.getMailRagContent(reqDTO);
    }
    
    @Override
    public MailResultVO sendMailCustomization(SendMailCustomizationReqDTO reqDTO) {
		return mailClient.sendMailCustomization(reqDTO);
    }
    
    @Override
    public MailLoginVO mailLogin(MailLoginReqDTO reqDTO) {
		return mailClient.mainLogin(reqDTO);
    }
}
