package com.zyhl.yun.api.outer.external.service.impl;

import com.alibaba.fastjson.JSON;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.client.centertask.AiCenterTaskClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.centertask.dto.CenterTaskCreateReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.centertask.vo.CenterTaskCreateVO;
import com.zyhl.yun.api.outer.domain.entity.centertask.CenterTaskCreateEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.TextParamEntity;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.external.FastReadTaskExternalService;
import com.zyhl.yun.api.outer.external.assembler.CenterTaskAssembler;
import com.zyhl.yun.api.outer.external.client.req.centertask.BusinessParam;
import com.zyhl.yun.api.outer.external.client.req.centertask.TextParam;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * {@code @projectName}  yun-ai-api-outer
 * <p>
 * {@code @description}
 * <p>
 *
 * <AUTHOR>
 * @since 4月15 2025
 */
@Slf4j
@Service
public class FastReadTaskExternalServiceImpl implements FastReadTaskExternalService {

  @Resource
  private AiCenterTaskClient centerTaskClient;

  @Resource
  private CenterTaskAssembler centerTaskCreateAssembler;

  @Override
  public CenterTaskCreateVO createCommonTextTask(CenterTaskCreateEntity createEntity,
      TextParamEntity textParamEntity) {
    CenterTaskCreateReqDTO req = null;
    CenterTaskCreateVO result = null;
    try {
      // 转换 -> 创建ai算法任务-请求参数
      req = centerTaskCreateAssembler.toCenterTaskCreateReqDTO(createEntity);
      //构建请求参数-TextParam
      // 转换 -> 创建ai算法任务-请求参数-文本类
      TextParam textParam = centerTaskCreateAssembler.toTextParam(textParamEntity);
      // BusinessParam 业务参数，json格式
      BusinessParam businessParam = BusinessParam.builder().textParam(textParam).build();
      req.setBusinessParam(JSON.toJSONString(businessParam));

      //创建ai算法任务
      result = centerTaskClient.addAIAbilityTask(req);
      if (Objects.isNull(result)) {
        log.warn("CenterTaskExternalServiceImpl-createTextTask-fail，result is null");
      }
      return result;
    } catch (Exception e) {
      log.error("CenterTaskExternalServiceImpl-createTextTask-异常信息: ", e);
      throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
    } finally {
      log.info(
          "CenterTaskExternalServiceImpl-createTextTask-finally\n  createEntity：{}\n textParamEntity：{}\n req：{}\n result：{}",
          JSON.toJSONString(createEntity), JSON.toJSONString(textParamEntity),
          JSON.toJSONString(req), JSON.toJSONString(result));
    }
  }
}
