package com.zyhl.yun.api.outer.external.ose.enums;

/**
 * 用户所属平台（底座）
 * @Author: <PERSON><PERSON><PERSON><PERSON>_<PERSON>
 */
public enum UserBelongsPlatformEnum {

    /**
     * 0:华为主平台；1:阿里pds云空间；2: 华为云空间；3: 云能DSP云空间
     */

    OSE(0, "华为主平台"),

    PDS(1, "阿里pds云空间"),

    HW_CLOUD(2, "华为云空间"),

    DSP(3, "云能DSP云空间"),
    ;

    private Integer belongsPlatform;

    private String desc;

    UserBelongsPlatformEnum(Integer belongsPlatform, String desc) {
        this.belongsPlatform = belongsPlatform;
        this.desc = desc;
    }

    public Integer getBelongsPlatform() {
        return belongsPlatform;
    }

    public String getDesc() {
        return desc;
    }
}
