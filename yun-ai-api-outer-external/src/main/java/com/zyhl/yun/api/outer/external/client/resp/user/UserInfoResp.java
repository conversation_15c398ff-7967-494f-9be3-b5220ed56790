package com.zyhl.yun.api.outer.external.client.resp.user;

import lombok.Data;

/**
 * 用户信息实体类
 *
 * <AUTHOR>
 */
@Data
public class UserInfoResp {
    /**
     * 割接状态，详见割接状态枚举说明
     */
    private Integer cutoverStatus;

    /**
     * 所属平台，详见所属平台枚举说明
     */
    private Integer belongsPlatform;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 用户开通业务类型
     */
    private String userServiceType;

    /**
     * 用户ID
     */
    private Long userDomainId;

    /**
     * 国家码
     */
    private String nationCode;

    /**
     * 用户资产特殊标记
     * 0：未标记；1：无资产；2：资产大于4
     */
    private Integer userAssetFlag;

    /**
     * 主平台网盘用户ID
     */
    private String bmpUserId;

    /**
     * 节点编码，详见节点编码枚举说明
     */
    private String siteCode;

    /**
     * 用户简介信息，包括头像和昵称
     */
    private UserProfileInfo userProfileInfo;

    /**
     * 用户区域信息
     */
    private UserExtendInfo userExtendInfo;

    /**
     * 彩云业务开通状态，详见彩云业务开通状态枚举说明
     */
    private Integer serviceStatus;

    /**
     * 账号开户时间
     */
    private String createTime;

    /**
     * 账号更新时间
     */
    private String updateTime;

}