package com.zyhl.yun.api.outer.external.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.domain.entity.ImageCaptionEntity;
import com.zyhl.yun.api.outer.domain.entity.ImageCaptureCaptionEntity;
import com.zyhl.yun.api.outer.domainservice.CaptionService;
import com.zyhl.yun.api.outer.external.client.ImageCaptionClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.zyhl.yun.api.outer.enums.ResultCodeEnum.DOWNSTREAM_SERVER_PROCESSING_FAILED;
import static com.zyhl.yun.api.outer.enums.ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION;

/**
 * 图配文
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CaptionServiceImpl implements CaptionService {

    @Resource
    private ImageCaptionClient imageCaptionClient;


    @Override
    public ImageCaptionEntity imageCaption(ImageCaptureCaptionEntity caption) {


        try {
            BaseResult<ImageCaptionEntity> imageCaption = imageCaptionClient.imageCaption(caption);
            if (imageCaption.isSuccess() && ObjectUtil.isNotEmpty(imageCaption.getData()))
                return imageCaption.getData();
            else {
                log.error("算法测图配文返回错误, requestId: {}, Message: {}", caption.getRequestId(), imageCaption.getMessage());
                throw new YunAiBusinessException(DOWNSTREAM_SERVER_PROCESSING_FAILED);
            }
        } catch (Exception e) {
            log.error("算法测图配文调用异常, requestId: {}, error:", caption.getRequestId(), e);
            throw new YunAiBusinessException(DOWNSTREAM_SERVICES_EXCEPTION);
        }
    }


}
