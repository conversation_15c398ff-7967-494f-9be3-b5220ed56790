package com.zyhl.yun.api.outer.external.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.config.RefreshableProperties;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.external.service.NfsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Collections;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

import static com.zyhl.yun.api.outer.constants.Const.NUM_10;

/**
 * <AUTHOR>
 * @since 2023/8/1 14:46
 */
@Slf4j
@Service
@RefreshScope
public class NfsServiceImpl implements NfsService {
    public static final Set<String> LIVP_INNER_TYPE;

    static {
        Set<String> set = new HashSet<>();
        set.add("jpg");
        set.add("heic");
        LIVP_INNER_TYPE = Collections.unmodifiableSet(set);
    }

    @Resource
    private RefreshableProperties refreshableProperties;

    private final String separator = System.getProperties().getProperty("file.separator");


    @Override
    public String convertPath(String path) {
        return path.replace(refreshableProperties.getFilePath(), refreshableProperties.getAiPath());
    }

    @Override
    public String getDirPath(Long taskId) {
        return generatePathByDateTime(DateUtil.date()) + separator + taskId + separator + IdUtil.simpleUUID() + separator;
    }

    /**
     * 根据url和文件类型下载图片到指定目录
     */
    @Override
    public File downloadHttpUrl(String url, String dirPath, String fileType) {
        String fileName = "downloadFile." + fileType;
        InputStream inputStream = null;
        FileOutputStream fos = null;
        File file;
        try {
            inputStream = getInputStreamFromUrl(url);
            byte[] getData = readInputStream(inputStream);
            File saveDir = new File(dirPath);
            if (!saveDir.exists()) {
                FileUtil.mkdir(saveDir);
            }
            String pathA = saveDir + File.separator + fileName;
            file = new File(pathA);
            fos = new FileOutputStream(file);
            fos.write(getData);
        } catch (Exception ex) {
            log.error("下载文件失败url:{}", url, ex);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNLOAD_TO_NFS_ERROR);
        } finally {
            if (!Objects.isNull(fos)) {
                try {
                    fos.close();
                } catch (IOException e) {
                    log.error("关闭文件失败");
                }
            }
            if (!Objects.isNull(inputStream)) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("关闭url流失败");
                }
            }
        }
        log.info("info：{} download success", url);
        return file;

    }

    /**
     * 根据url下载图片流
     */
    @Override
    public InputStream getInputStreamFromUrl(String urlStr) throws IOException {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setConnectTimeout((int) DateUnit.SECOND.getMillis() * refreshableProperties.getConnectionTimeout());
        conn.setReadTimeout((int) DateUnit.SECOND.getMillis() * refreshableProperties.getReadTimeout());
        conn.setDoOutput(true);
        conn.setRequestMethod("GET");
        return conn.getInputStream();
    }

    public static byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        ByteArrayOutputStream bos = null;
        try {
            bos = new ByteArrayOutputStream();
            int len;
            while ((len = inputStream.read(buffer)) != -1) {
                bos.write(buffer, 0, len);
            }
        } catch (Exception ex) {
            log.warn("字节流读取失败，", ex);
            throw ex;
        } finally {
            if (Objects.nonNull(bos)) {
                bos.close();
            }
        }
        return bos.toByteArray();
    }

    private String generatePathByDateTime(DateTime date) {
        String path = refreshableProperties.getFilePath();
        path += date.year() + separator;
        String zero = "0";
        int month = date.month() + 1;
        if (month < NUM_10) {
            path += zero + month + separator;
        } else {
            path += month + separator;
        }
        if (date.dayOfMonth() < NUM_10) {
            path += zero + date.dayOfMonth();
        } else {
            path += date.dayOfMonth();
        }
        return path;
    }

}
