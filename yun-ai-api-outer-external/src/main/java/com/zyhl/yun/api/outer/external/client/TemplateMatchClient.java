package com.zyhl.yun.api.outer.external.client;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.domain.entity.TemplateMatchEntity;
import com.zyhl.yun.api.outer.domain.resp.TemplateMatchRsqEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 图片标签与影集模板匹配模型
 * <AUTHOR>
 */
@FeignClient(name = "${yun.external.templateMatch.service-name}", url = "${yun.external.templateMatch.url}")
public interface TemplateMatchClient {

	/**
	 * 图片标签与影集模板匹配模型接口
	 *
	 * @param req
	 * @return
	 */
	@PostMapping(value = "/yun/ai/template/match")
	BaseResult<TemplateMatchRsqEntity> templateMatch(@RequestBody TemplateMatchEntity req);

}
