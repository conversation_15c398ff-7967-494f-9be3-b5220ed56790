package com.zyhl.yun.api.outer.external.client;

import com.zyhl.yun.api.outer.external.client.req.market.MarketAcceptInviteReq;
import com.zyhl.yun.api.outer.external.client.resp.market.InviteCheckResponse;
import com.zyhl.yun.api.outer.external.client.resp.market.MarketBaseResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @Date 2024/1/6 17:46
 */
@FeignClient(name = "marketInviteFeign", url = "${market.content-url}", path = "${market.content-path}")
public interface MarketInviteClient {

    /**
     * 邀请接口
     * @param req
     * @return
     */
    @PostMapping("acceptClient")
    MarketBaseResponse accept(@RequestBody MarketAcceptInviteReq req);

    /**
     * 查询
     * @param req
     * @return
     */
    @PostMapping("checktClient")
    InviteCheckResponse checkt(@RequestBody MarketAcceptInviteReq req);

}
