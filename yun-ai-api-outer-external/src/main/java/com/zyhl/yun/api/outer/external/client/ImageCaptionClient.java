package com.zyhl.yun.api.outer.external.client;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.domain.entity.ImageCaptionEntity;
import com.zyhl.yun.api.outer.domain.entity.ImageCaptureCaptionEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 图配文
 * <AUTHOR>
 */
@FeignClient(name = "${yun.external.imageCaption.service-name}", url = "${yun.external.imageCaption.url}")
public interface ImageCaptionClient {

	/**
	 * 图配文
	 *
	 * @param req
	 * @return
	 */
	@PostMapping(value = "/yun/ai/image/caption")
	BaseResult<ImageCaptionEntity> imageCaption(@RequestBody ImageCaptureCaptionEntity req);

}
