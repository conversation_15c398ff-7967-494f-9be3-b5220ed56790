package com.zyhl.yun.api.outer.external.service.impl;

import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.note.NoteClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.note.dto.NoteDetailReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.note.dto.NoteListReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.note.vo.NoteDetailVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.note.vo.NoteListRespVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.notethird.NoteThirdClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.notethird.dto.NoteThirdDetailReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.notethird.req.NoteThirdListClientReq;
import com.zyhl.hcy.yun.ai.common.platform.third.client.notethird.vo.NoteThirdDetailVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.notethird.vo.NoteThirdListVO;
import com.zyhl.yun.api.outer.enums.AiResultCode;
import com.zyhl.yun.api.outer.external.NoteExternalService;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 笔记服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class NoteExternalServiceImpl implements NoteExternalService {

    @Resource
    private NoteClient noteClient;
    @Resource
    private NoteThirdClient noteThirdClient;

    @Override
    public String noteContent(String token, String noteId) {
        // 构建笔记详情请求DTO
        NoteDetailReqDTO noteDetailReqDTO = new NoteDetailReqDTO();
        noteDetailReqDTO.setToken(token);
        noteDetailReqDTO.setNoteId(noteId);
        log.info("开始请求Note服务，noteId:{}", noteDetailReqDTO.getNoteId());

        // 获取笔记内容
        NoteDetailVO noteDetailVO = noteClient.getNoteContent(noteDetailReqDTO);
        // 直接返回笔记文本内容
        String text = noteDetailVO.getTxtContent();
        if (StringUtils.hasText(text)) {
            return text;
        }

        log.error("笔记内容为空，noteId:{}", noteDetailReqDTO.getNoteId());
        throw new YunAiBusinessException(AiResultCode.CODE_10000021.getCode(), AiResultCode.CODE_10000021.getMsg());
    }

    @Override
    public String getTextContent(String token, String noteId) {
        // 构建笔记详情请求DTO
        NoteDetailReqDTO noteDetailReqDTO = new NoteDetailReqDTO();
        noteDetailReqDTO.setToken(token);
        noteDetailReqDTO.setNoteId(noteId);
        log.info("开始请求Note服务，noteId:{}", noteDetailReqDTO.getNoteId());

        // 获取笔记内容
        NoteDetailVO noteDetailVO = noteClient.getNoteContent(noteDetailReqDTO);

        return Objects.nonNull(noteDetailVO) ? noteDetailVO.getTxtContent() : "";
    }
    
    @Override
    public List<NoteDetailVO> getNoteList(NoteListReqDTO reqDTO) {
    	NoteListRespVO resp = noteClient.getNodeList(reqDTO);
    	if (null != resp && CollUtil.isNotEmpty(resp.getList())) {
    		return resp.getList();
    	}
    	return null;
    }

    @Override
    public String getTitle(String userId, String noteId) {
        NoteThirdDetailVO vo = getNoteDetail(userId, noteId);

        return Objects.nonNull(vo) ? vo.getTitle() : "";
    }

    @Override
    public NoteThirdDetailVO getNoteDetail(String userId, String noteId) {
        NoteThirdDetailReqDTO reqDTO = new NoteThirdDetailReqDTO();
        reqDTO.setUserId(Long.valueOf(userId));
        reqDTO.setNoteId(noteId);
        return noteThirdClient.getNoteInfo(reqDTO);
    }

    @Override
    public List<NoteThirdListVO> getNoteList(NoteThirdListClientReq reqDTO) {
        return noteThirdClient.getNoteList(reqDTO);
    }
}
