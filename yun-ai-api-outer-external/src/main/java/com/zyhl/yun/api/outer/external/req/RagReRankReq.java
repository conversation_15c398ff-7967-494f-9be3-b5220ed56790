package com.zyhl.yun.api.outer.external.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 算法重排请求req
 *
 * <AUTHOR>
 * @date 2024/9/9 15:01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RagReRankReq {

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 用户的查询问题
     */
    private String query;

    /**
     * 要重新排序的文档对象
     */
    private List<DocumentReq> documents;

    /**
     * 要返回的大多数相关文档或索引的数量，默认为文档的长度
     */
    private Integer topK;

    /**
     * 重排后的数据结果Resp
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DocumentReq {

        /**
         * 文档ID
         */
        private String fileId;

        /**
         * 分段ID
         */
        private String segmentId;

        /**
         * 分段内容
         */
        private String text;

        /**
         * 来源信息列表，支持多个来源
         */
//        private List<SourceInfoReq> sourceInfos;

        /**
         * 得分明细，包含各个维度的得分和权重信息
         */
//        private List<ScoreDetailsReq> scoreDetails;

    }

    /**
     * 来源信息列表，支持多个来源
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SourceInfoReq {

        /**
         * 来源类型（与parseType对应）
         * @see com.zyhl.hcy.yun.ai.common.base.enums.ParseTypeEnum
         * DOC_STRING_SEGMENT (文档字符串分片)
         * DOC_SEMANTIC_SEGMENT (文档语义分片)
         * DOC_SUMMARY (文档总结)
         * DOC_STRING_SEGMENT_SUMMARY (文档字符串分片的总结)
         * DOC_SEMANTIC_SEGMENT_SUMMARY (文档语义分片的总结)
         * DOC_STRING_SEGMENT_KEYPOINT (文档字符串分片的信息点)
         * DOC_SEMANTIC_SEGMENT_KEYPOINT (文档语义分片的信息点)
         * DOC_KEYPOINT (文档的信息点)
         * DOC_STRING_SEGMENT_QA (文档字符串分片的问答对)
         * DOC_SEMANTIC_SEGMENT_QA (文档语义分片的问答对)
         * DOC_QA (文档问答对)
         */
        private String sourceType;

        /**
         * 该分块该来源的召回统计信息
         */
        private RecallStatsReq recallStats;

        /**
         * 该分块该来源的召回统计信息
         */
        private FeedbackStatsReq feedbackStats;

    }

    /**
     * 得分明细，包含各个维度的得分和权重信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ScoreDetailsReq {

        /**
         * 最终得分
         */
        private Float finalScore;

        /**
         * 各来源的召回相关得分
         */
        private List<RecallScore> recallScores;

    }

    /**
     * 各来源的召回相关得分
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecallScore {

        /**
         * 来源类型
         */
        private String sourceType;

        /**
         * 原始召回次数
         */
        private Integer rawRecallCount;

    }

    /**
     * 该分块该来源的召回统计信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecallStatsReq {

        public RecallStatsReq (Integer recallCount) {
            this.recallCount = recallCount;
        }

        /**
         * 该分块被召回的总次数（RAG2.2新增）
         */
        private Integer recallCount;

        /**
         * 最近一次召回时间（格式：YYYY-MM-DD HH:mm:ss）
         */
        private String lastRecallTime;

    }

    /**
     * 该分块该来源的召回统计信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FeedbackStatsReq {

        /**
         * 包含该分块的回答被点赞的次数
         */
        private Integer likeCount;

        /**
         * 包含该分块的回答被点踩的次数
         */
        private Integer dislikeCount;

        /**
         * 最近一次反馈时间（格式：YYYY-MM-DD HH:mm:ss）
         */
        private String lastFeedbackTime;

    }
}
