package com.zyhl.yun.api.outer.external.service.impl;

import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.hcy.yun.ai.common.model.api.client.ali.AliApiClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.ali.dto.GenericSearchDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.ali.vo.GenericSearchVO;
import com.zyhl.yun.api.outer.external.AiInternetSearchExternalService;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * className: AiInternetSearchExternalServiceImpl
 * description: AI全网搜-第三方业务实现类
 *
 * <AUTHOR>
 * @date 2025/3/18
 */
@Service
public class AiInternetSearchExternalServiceImpl implements AiInternetSearchExternalService {

    private static final Logger log = LoggerFactory.getLogger(AiInternetSearchExternalServiceImpl.class);
    @Resource
    private AliApiClient aliApiClient;

    @Override
    public List<GenericSearchVO> genericSearch(GenericSearchDTO genericSearchDTO) {
        List<GenericSearchVO> genericSearchVOList = new ArrayList<>();
        try {
            genericSearchVOList = aliApiClient.genericSearch(genericSearchDTO);
            log.info("【AI全网搜】【阿里搜索】【子线程：{}】结果总条数：{}", Thread.currentThread().getName(), genericSearchVOList.size());
            LogCommonUtils.printlnStrLog("【AI全网搜】【阿里搜索】【子线程："+Thread.currentThread().getName()+"】结果", JsonUtil.toJson(genericSearchVOList));
        } catch (Exception e) {
            log.error("【AI全网搜】【阿里搜索】【子线程：{}】异常，请求参数：{}，异常信息：{}",
                    Thread.currentThread().getName(), JsonUtil.toJson(genericSearchDTO), e.getMessage(), e);
        }
        return genericSearchVOList;
    }

    @Override
    public List<GenericSearchVO> unifiedSearch(GenericSearchDTO genericSearchDTO) {
        List<GenericSearchVO> genericSearchVOList = new ArrayList<>();
        try {
            genericSearchVOList = aliApiClient.unifiedSearch(genericSearchDTO);
            log.info("【AI全网搜】【阿里搜索】【子线程：{}】结果总条数：{}", Thread.currentThread().getName(), genericSearchVOList.size());
            LogCommonUtils.printlnStrLog("【AI全网搜】【阿里搜索】【子线程："+Thread.currentThread().getName()+"】结果", JsonUtil.toJson(genericSearchVOList));
        } catch (Exception e) {
            log.error("【AI全网搜】【阿里搜索】【子线程：{}】异常，请求参数：{}，异常信息：{}",
                    Thread.currentThread().getName(), JsonUtil.toJson(genericSearchDTO), e.getMessage(), e);
        }
        return genericSearchVOList;
    }

    @Override
    public List<GenericSearchVO> knowledgeSearch(String query) {
        GenericSearchDTO genericSearchDTO = new GenericSearchDTO();
        genericSearchDTO.setQuery(query);

        List<GenericSearchVO> list = new ArrayList<>();
        try {
            list = aliApiClient.genericSearch(genericSearchDTO);
            LogCommonUtils.printlnListLog("【知识库对话】【RAG重要节点日志】【网页搜索】阿里搜索接口搜索结果第{}个分块：\n{}", JsonUtil.toJson(list));
        } catch (Exception e) {
            log.error("【知识库对话】【RAG重要节点日志】【网页搜索】阿里搜索接口异常，异常信息：{}，请求参数：{}", e.getMessage(), JsonUtil.toJson(genericSearchDTO), e);
        }
        return list;
    }
    
    @Override
    public List<GenericSearchVO> modelNetworkSearch(String query) {
    	if(StringUtils.isEmpty(query)) {
    		return null;
    	}
//    	if(query.length() > 100) {
//    		//超100文字，需要截取
//    		query = query.substring(0, 100);
//    	}
        GenericSearchDTO genericSearchDTO = new GenericSearchDTO();
        	
    	genericSearchDTO.setQuery(query);

        List<GenericSearchVO> list = new ArrayList<>();
        try {
            list = aliApiClient.genericSearch(genericSearchDTO);
            LogCommonUtils.printlnListLog("【模型联网搜索对话】阿里搜索接口搜索结果第{}个分块：\n{}", JsonUtil.toJson(list));
        } catch (Exception e) {
            log.error("【模型联网搜索对话】阿里搜索接口异常，异常信息：{}，请求参数：{}", e.getMessage(), JsonUtil.toJson(genericSearchDTO), e);
        }
        return list;
    }
}