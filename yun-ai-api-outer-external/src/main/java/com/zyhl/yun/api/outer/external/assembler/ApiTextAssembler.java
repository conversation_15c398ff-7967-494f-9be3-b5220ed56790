package com.zyhl.yun.api.outer.external.assembler;

import com.zyhl.yun.api.outer.domain.vo.chat.search.result.*;
import com.zyhl.yun.api.outer.external.client.resp.text.*;
import com.zyhl.yun.api.outer.external.req.RagReRankReq;
import com.zyhl.yun.api.outer.external.resp.RagReRankResp;
import com.zyhl.yun.api.outer.vo.RagReRankDataVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 文本工具-类转换器
 * @Author: WeiJingKun
 */
@Mapper(componentModel = "spring")
public interface ApiTextAssembler {

    ApiTextAssembler INSTANCE = Mappers.getMapper(ApiTextAssembler.class);

    /**
     * 转换 -> 对话信息-搜索结果-图片
     * 
     * @param rsp 参数 
     * @return 转换结果 转换结果
     */
    SearchImageResult toSearchImageResult(IntelligentSearchImageRsp rsp);

    /**
     * 转换 -> 对话信息-搜索结果-文件
     * 
     * @param rsp 参数
     * @return 转换结果
     */
    SearchFileResult toSearchFileResult(IntelligentSearchFileRsp rsp);

    /**
     * 转换 -> 对话信息-搜索结果-笔记
     * 
     * @param rsp 参数
     * @return 转换结果
     */
    SearchNoteResult toSearchNoteResult(IntelligentSearchNoteRsp rsp);

    /**
     * 转换 -> 对话信息-搜索结果-功能
     * 
     * @param rsp 参数
     * @return 转换结果
     */
    SearchFunctionResult toSearchFunctionResult(IntelligentSearchFunctionRsp rsp);

    /**
     * 转换 -> 对话信息-搜索结果-活动
     * 
     * @param rsp 参数
     * @return 转换结果
     */
    SearchActivityResult toSearchActivityResult(IntelligentSearchActivityRsp rsp);

    /**
     * 转换 -> 对话信息-搜索结果-发现广场
     * 
     * @param rsp 参数
     * @return 转换结果
     */
    SearchDiscoveryResult toSearchDiscoveryResult(IntelligentSearchDiscoveryRsp rsp);

    /**
     * 转换 -> 对话信息-搜索结果-我的圈子
     * 
     * @param rsp 参数
     * @return 转换结果
     */
    SearchMyGroupResult toSearchGroupMyResult(IntelligentSearchMyGroupRsp rsp);

    /**
     * 转换 -> 对话信息-搜索结果-热门圈子
     * 
     * @param rsp 参数
     * @return 转换结果
     */
    SearchRecommendGroupResult toSearchGroupRecommendResult(IntelligentSearchRecommendGroupRsp rsp);

    /**
     * 转换 -> 对话信息-搜索结果-邮件
     * 
     * @param rsp 参数
     * @return 转换结果
     */
    SearchMailResult toSearchMailResult(IntelligentSearchMailRsp rsp);

    /**
     * 转换 -> 对话信息-搜索结果-邮件附件
     *
     * @param rsp 参数
     * @return 转换结果
     */
    SearchMailAttachmentResult toSearchMailAttachmentResult(IntelligentSearchMailAttachmentRsp rsp);

    /**
     * 转换 -> 对话信息-搜索结果-知识库资源
     *
     * @param rsp 参数
     * @return 转换结果
     */
    SearchKnowledgeBaseResourceResult toSearchKnowledgeBaseResourceResult(IntelligentSearchKnowledgeBaseResourceRsp rsp);

    /**
     * 转换 -> rag重排结果VO
     * 
     * @param result 参数
     * @return 转换结果
     */
    @Mapping(source = "fileId", target = "document.fileId")
    @Mapping(source = "segmentId", target = "document.segmentId")
    @Mapping(source = "text", target = "document.text")
    @Mapping(source = "score", target = "document.score")
    RagReRankDataVO toRagReRankDataVO(RagReRankResp.DocumentDataResp result);

    /**
     * 被召回次数提取
     *
     * @param scoreDetails 得分明细信息集合
     * @return 字符串
     */
    @Named("getRecallInfo")
    default Integer getRecallInfo(List<RagReRankReq.ScoreDetailsReq> scoreDetails) {

        Integer rawRecallCount;
        try {
            rawRecallCount = scoreDetails.get(0).getRecallScores().get(0).getRawRecallCount();
        } catch (Exception e) {
            rawRecallCount = null;
        }
        return rawRecallCount;
    }
}
