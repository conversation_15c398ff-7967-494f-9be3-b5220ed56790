package com.zyhl.yun.api.outer.external.resp;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import com.zyhl.yun.api.outer.vo.KeyValueVO;
import lombok.Data;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.external.resp.DialogueIntentionResp} <br>
 * <b> description:</b>
 * 对话意图响应参数
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-03-05 15:17
 **/
@Data
public class DialogueIntentionResp implements Serializable {

    private static final long serialVersionUID = 6185303589538688629L;

    /**
     * 错误码
     */
    @JsonProperty("code")
    private String code;

    /**
     * 是否成功
     */
    @JsonProperty("success")
    private Boolean success;

    /**
     * data
     */
    @JsonProperty("data")
    private DialogueIntentionRespData data;

    /**
     * 描述
     */
    @JsonProperty("message")
    private String message;

    @Data
    public static class DialogueIntentionRespData {

        /**
         * 请求ID
         */
        @JsonProperty("requestId")
        private String requestId;

        /**
         * 用户ID
         */
        @JsonProperty("userId")
        private String userId;

        /**
         * 会话ID
         */
        @JsonProperty("sessionId")
        private String sessionId;

        /**
         * 对话ID
         */
        @JsonProperty("dialogueId")
        private String dialogueId;

        /**
         * 意图结果
         */
        @JsonProperty("intentionInfoList")
        private List<DialogueIntentionResp.DialogueIntentionRespData.IntentionInfo> intentionInfoList;

        /**
         * 内容推荐关键字列表
         */
        @JsonProperty("contentRecommendList")
        private List<DialogueIntentionResp.DialogueIntentionRespData.ContentRecommend> contentRecommendList;

        @Data
        public static class IntentionInfo {

            /**
             * 意图
             */
            @JsonProperty("intention")
            private String intention;

            /**
             * 分数
             */
            @JsonProperty("score")
            private Double score;

            /**
             * 实体识别结果entity
             */
            @JsonProperty("entityList")
            private List<IntentEntity> entityList;

            /**
             * 在触发 Function Calling意图后，模型回复的要调用的工具以及调用工具所需的参数。可以包含一个或多个工具响应对象。
             */
            private List<ToolObjectEntity> toolCallsList;
            
            @Data
            public static class IntentEntity {

                /**
                 * 时间列表，输出是某个时间，或者开始时间与结束时间
                 */
                @JsonProperty("timeList")
                private List<String> timeList;

                /**
                 * 地点列表
                 */
                @JsonProperty("placeList")
                private List<String> placeList;

                /**
                 * 事物标签列表，key与metadataList的key值一致，Value的类型为List<String>
                 */
                @JsonProperty("labelList")
                private List<KeyValueEntity> labelList;

                /**
                 * 实体列表，Value的类型为Value的类型为List<String>
                 */
                @JsonProperty("metaDataList")
                private List<KeyValueEntity> metaDataList;

                /**
                 * 图片标签列表
                 */
                @JsonProperty("imageNameList")
                private List<String> imageNameList;

                /**
                 * 后缀列表
                 */
                @JsonProperty("suffixList")
                private List<String> suffixList;

                /**
                 * 发件人列表，意图为“028”重要邮件时返回
                 */
                @JsonProperty("senderList")
                private List<String> senderList;

                /**
                 * 邮箱列表，意图为“028”重要邮件时返回
                 */
                @JsonProperty("emailAddressList")
                private List<String> emailAddressList;

                /**
                 * 状态列表，不同意图返回的结果不一致，需要根据意图结果来处理。
                 * 028重要邮件：全部、未读、已读
                 */
                @JsonProperty("statusList")
                private List<String> statusList;

                /**
                 * 类别列表，不同意图返回的结果不一致，需要根据意图结果来处理。
                 * 028重要邮件：重要邮件
                 */
                @JsonProperty("typeList")
                private List<String> typeList;

                /**
                 * 标题列表
                 * 032发邮件
                 */
                @JsonProperty("titleList")
                private List<String> titleList;

                /**
                 * 内容列表
                 * 032发邮件
                 */
                @JsonProperty("contentList")
                private List<String> contentList;

                /**
                 * 内容列表
                 * 032发邮件
                 */
                @JsonProperty("recipientList")
                private List<String> recipientList;

                /**
                 * 附件列表
                 * 028邮件
                 */
                @JsonProperty("attachmentList")
                private List<String> attachmentList;

                /**
                 * 人名列表，key的类型为String，Value的类型为List<String>
                 * 样例：personList = [{"key": "主演", "value": ["刘德华"]}]
                 */
                @JsonProperty("personList")
                private List<KeyValueEntity> personList;

                @Data
                public static class KeyValueEntity {

                    /**
                     * 名称
                     */
                    @JsonProperty("key")
                    private String key;

                    /**
                     * 结果列表
                     */
                    @JsonProperty("value")
                    private List<String> value;
                }

            }
            
            @Data
			public static class ToolObjectEntity {
				/**
				 * 需要被调用的函数名
				 */
				private String name;

				/**
				 * 二级意图编码
				 */
				private String code;

				/**
				 * 调用函数所需的参数
				 */
				private List<KeyValueEntity> arguments;

				@Data
				public static class KeyValueEntity {

					/**
					 * 名称
					 */
					@JsonProperty("key")
					private String key;

					/**
					 * 结果列表
					 */
					@JsonProperty("value")
					private List<String> value;
				}
			}

        }

        /**
         * 内容推荐关键词信息
         * @Author: WeiJingKun
         */
        @Data
        public static class ContentRecommend {

            /**
             * 内容推荐关键字编码
             */
            @JsonProperty("code")
            private String code;

            /**
             * 内容推荐关键字
             */
            @JsonProperty("name")
            private String name;

        }

    }
}
