package com.zyhl.yun.api.outer.external.client.req;

import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 * 年月日时光轴图片入参
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TimelineYmdReqDTO {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 同步目录固定路径，如“/手机图片
     */
    private String fixedPath;

    /**
     * 文件类型 缺省默认返回所有类型文件
     * file-普通文件
     * folder-目录文件
     */
    private String type;

    /**
     * 文件类别 缺省默认返回所有类别文件
     * image	图片
     * audio	音频
     * video	视频
     * folder 	目录
     * doc	文档
     * app	安装包
     * zip	压缩包
     * others	其他
     */
    private String category;

    /**
     * 是否包含隐藏文件
     * true：返回所有文件
     * false：仅返回非隐藏文件 缺省默认为false
     */
    private Boolean includeHidden;

    /**
     * 图片缩略图处理样式列表,可选值Small，Middle，Big，Large，默认Small
     * 注：缺省时默认为Small，只返回小图信息
     */
    private List<String> imageThumbnailStyleList;

    /**
     * 缩略图等地址过期时间，单位为秒，最长 86400 秒，默认为 900 秒
     */
    private Integer expireSec;

    /**
     * 分页信息
     */
    private PageInfoDTO pageInfo;
}
