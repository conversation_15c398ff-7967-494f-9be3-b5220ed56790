package com.zyhl.yun.api.outer.external.assembler;

import com.zyhl.yun.api.outer.domain.dto.UserInfoDTO;
import com.zyhl.yun.api.outer.domain.dto.UserProfileInfoDTO;
import com.zyhl.yun.api.outer.external.client.resp.user.UserExtendInfo;
import com.zyhl.yun.api.outer.external.client.resp.user.UserInfoResp;
import com.zyhl.yun.api.outer.external.client.resp.user.UserProfileInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;
import java.util.Optional;

/**
 * User External Assembler
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-04-17 10:12:37
 */
@Mapper(componentModel = "spring", imports = {Optional.class, UserExtendInfo.class})
public interface UserExternalAssembler {

    @Mapping(target = "userId", source = "userDomainId")
    @Mapping(target = "belongsPlatform", source = "belongsPlatform")
    @Mapping(target = "phoneNumber", source = "phoneNumber")
    @Mapping(target = "province", expression = "java(Optional.ofNullable(resp.getUserExtendInfo()).map(UserExtendInfo::getProvince).orElse(null))")
    @Mapping(target = "userProfileInfo", source = "userProfileInfo")
    UserInfoDTO toUserInfoDTO(UserInfoResp resp);

    List<UserInfoDTO> toUserInfoDTO(List<UserInfoResp> resps);

    UserProfileInfoDTO toUserInfoDTO(UserProfileInfo resp);
}
