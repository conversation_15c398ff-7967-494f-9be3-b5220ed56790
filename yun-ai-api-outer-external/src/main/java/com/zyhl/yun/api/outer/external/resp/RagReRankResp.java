package com.zyhl.yun.api.outer.external.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 算法重排结果Resp
 *
 * <AUTHOR>
 * @date 2024/9/9 14:55
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RagReRankResp {

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 重排后的数据结果
     */
    private List<DocumentDataResp> results;

    /**
     * 文档对象Resp
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DocumentDataResp {

        /**
         * 文档ID
         */
        private String fileId;

        /**
         * 分段ID
         */
        private String segmentId;

        /**
         * 分段内容
         */
        private String text;

        /**
         * 相关性分数被归一化为在[0，1]范围内。分数接近1表示与查询高度相关，分数接近0表示相关性低。
         * 搜索重排接口返回
         */
        private Float score;

        /**
         * 来源信息列表，支持多个来源
         */
//        private List<RagReRankReq.SourceInfoReq> sourceInfos;

        /**
         * 得分明细，包含各个维度的得分和权重信息
         */
//        private List<RagReRankReq.ScoreDetailsReq> scoreDetails;

    }
}
