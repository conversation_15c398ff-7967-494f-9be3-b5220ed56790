package com.zyhl.yun.api.outer.external.service.impl;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.domain.entity.FileEntity;
import com.zyhl.yun.api.outer.domain.entity.FileRspEntity;
import com.zyhl.yun.api.outer.domain.req.BatchGetReqEntity;
import com.zyhl.yun.api.outer.domain.req.FileGetDownloadUrlReqEntity;
import com.zyhl.yun.api.outer.domain.resp.BatchGetFileRspEntity;
import com.zyhl.yun.api.outer.domain.resp.FileGetDownloadUrlRespEntity;
import com.zyhl.yun.api.outer.domainservice.FileExternalService;
import com.zyhl.yun.api.outer.external.client.FileExternalClient;
import com.zyhl.yun.api.outer.external.service.RouteFeignClientFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.zyhl.hcy.yun.ai.common.base.enums.YunAiCommonResultCode.DOWNSTREAM_SERVICES_EXCEPTION;

/**
 * 调用个人云文件详情接口
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FileExternalServiceImpl implements FileExternalService {

    @Resource
    private RouteFeignClientFactory<FileExternalClient> routeFeignClientFactory;

    // @TODO 个人云有重排序，需处理，另外缩略图时间需要扩充到一天
    /**
     * 文件批量获取
     * @Author: WeiJingKun
     *
     * @param batchGetReq 请求参数
     * @return 文件信息list
     */
    @Override
    public List<FileEntity> fileBatchGet(BatchGetReqEntity batchGetReq) {
        log.info("查询文件请求参数:{}", batchGetReq);
        FileExternalClient feignClientByRoute = routeFeignClientFactory.getFeignClientByRoute(Long.valueOf(batchGetReq.getUserId()));
        BaseResult<BatchGetFileRspEntity> baseResult = feignClientByRoute.batchGet(batchGetReq);
        if (!baseResult.isSuccess()) {
            log.info("个人云SaaS远程调用异常，用户ID：{}，异常信息：{}", batchGetReq.getUserId(), baseResult.getMessage());
            throw new YunAiBusinessException(DOWNSTREAM_SERVICES_EXCEPTION);
        }
        List<FileRspEntity> batchFileResults = baseResult.getData().getBatchFileResults();
        log.info("查询到文件信息:{}", batchFileResults);
        return batchFileResults.stream()
                .map(FileRspEntity::getSrcFile)
                .filter(srcFile -> srcFile != null && srcFile.getFileId() != null)
                .collect(Collectors.toList());
    }

    /**
     * 获取文件下载地址
     *
     * @param query
     * @return
     */
    @Override
    public FileGetDownloadUrlRespEntity fileGetDownloadUrl(FileGetDownloadUrlReqEntity query) {
        FileExternalClient feignClientByRoute = routeFeignClientFactory.getFeignClientByRoute(Long.valueOf(query.getUserId()));
        BaseResult<FileGetDownloadUrlRespEntity> entityBaseResult = feignClientByRoute.fileGetDownloadUrl(query);
        if (!entityBaseResult.isSuccess()) {
            log.info("个人云SaaS远程调用异常，用户ID：{}，异常信息：{}", query.getUserId(), entityBaseResult.getMessage());
            throw new YunAiBusinessException(DOWNSTREAM_SERVICES_EXCEPTION);
        }
        if (entityBaseResult.getData() != null) {
            return entityBaseResult.getData();
        }
        return null;
    }
}
