package com.zyhl.yun.api.outer.external.client.resp;


import com.zyhl.yun.api.outer.domain.entity.FileRspEntity;
import com.zyhl.yun.api.outer.domain.valueobject.YmdFileChangeInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 * 年月日时光轴图片增量接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TimelineYmdChangeRspDTO {

    /**
     * 下一页起始游标，最后一页值为空，传入PageInfo中获取下一页
     */
    private String nextPageCursor;

    /**
     * 是否还有更多变更信息,true or false
     */
    private Boolean hasMore;

    /**
     * 文件变更信息
     */
    private List<YmdFileChangeInfo> fileList;

    /**
     * 个人云文件列表详情
     */
    private List<FileRspEntity> fileInfoList;

    /**
     * 组装个人云文件列表详情
     *
     * @param fileInfoList
     */
    public void completionFileIds(List<FileRspEntity> fileInfoList) {

        this.fileInfoList = fileInfoList;
    }
}
