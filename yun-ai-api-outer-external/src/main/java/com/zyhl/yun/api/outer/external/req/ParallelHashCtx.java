package com.zyhl.yun.api.outer.external.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 并行hash
 * @Author: <PERSON>_<PERSON><PERSON>_<PERSON>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ParallelHashCtx {
    /**
     * 上一个数据块ContentHash，长度取决于hash算法
     * 上一个数据块SHA1的第1-5个32位变量
     */
    private Long[] h;
    /**
     * 到上一个数据块为止的总长度，字节，需要为64的倍数
     */
    private Long partOffset;
}
