package com.zyhl.yun.api.outer.external.client;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.external.client.interceptor.ApiTextFeignInterceptorV3;
import com.zyhl.yun.api.outer.external.client.req.text.IntelligentSearchFileReq;
import com.zyhl.yun.api.outer.external.client.resp.text.IntelligentSearchFileRsp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 文本工具Client（v3版本）
 * @Author: WeiJingKun
 */
@FeignClient(
        name = "yun-ai-api-text-v3",
        url = "${external.api-text.url}",
        path = "${external.api-text.path}",
        configuration = ApiTextFeignInterceptorV3.class)
public interface ApiTextClientV3 {

    /**
     * 个人云资产搜索V3
     * @Author: WeiJingKun
     * @param req 文本工具Client-请求参数-个人云资产搜索V3
     * @return 文本工具Client-响应结果-个人云资产搜索V3
     */
    @PostMapping(value = "/intelligent/search/file")
    BaseResult<IntelligentSearchFileRsp> intelligentSearchFileV3(IntelligentSearchFileReq req);

}
