package com.zyhl.yun.api.outer.external.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.enums.BaseResultCodeEnum;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.constants.Const;
import com.zyhl.yun.api.outer.domain.entity.DialogueIntentionEntity;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.exception.BaseException;
import com.zyhl.yun.api.outer.external.DialogueIntentionExternalService;
import com.zyhl.yun.api.outer.external.assembler.CenterTaskAssembler;
import com.zyhl.yun.api.outer.external.client.DialogueIntentionClient;
import com.zyhl.yun.api.outer.external.req.DialogueIntentionReq;
import com.zyhl.yun.api.outer.external.resp.DialogueIntentionResp;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO.IntentionInfo;
import com.zyhl.yun.api.outer.vo.KeyValueVO;
import com.zyhl.yun.api.outer.vo.ToolObjectVO;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.external.service.impl.DialogueIntentionExternalServiceImpl}
 * <br>
 * <b> description:</b> 对话意图模型接口实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-03-05 14:48
 **/
@Slf4j
@Service
@Validated
public class DialogueIntentionExternalServiceImpl implements DialogueIntentionExternalService {

    @Resource
    private DialogueIntentionClient dialogueIntentionClient;
    
    @Resource
    private CenterTaskAssembler centerTaskAssembler;

    private final static String SPECIAL_CODE = "10015001";

    /**
     * 意图识别
     */
    @Override
    public DialogueIntentionVO dialogueIntentionFuc(String channel, DialogueIntentionEntity dialogueIntentionEntity) {
        DialogueIntentionVO vo = null;
        DialogueIntentionReq intentionReq = centerTaskAssembler.toDialogueIntentionReq(dialogueIntentionEntity);

        DialogueIntentionReq.DialogueInfo dialogueInfo = new DialogueIntentionReq.DialogueInfo();
        dialogueInfo.setDialogue(dialogueIntentionEntity.getCurrentDialogue().getDialogue());
        intentionReq.setCurrentDialogue(dialogueInfo);

        intentionReq.setTimestamp(dialogueIntentionEntity.getCurrentDialogue().getTimestamp());
        DialogueIntentionResp dialogueIntentionResp = null;
        try {
            try {
				log.info("【意图识别】==> dialogueIntentionFuc, channel:{}, 请求参数：{}", channel, JsonUtil.toJson(intentionReq));
				if (SourceChannelsProperties.isCloudPhoneChannel(channel)) {
					// 云手机渠道意图接口，独立的
					dialogueIntentionResp = dialogueIntentionClient.dialogueIntentionCloudPhoneFuc(intentionReq);
				}else {
                	dialogueIntentionResp = dialogueIntentionClient.dialogueIntentionFuc(intentionReq);
                }
            } catch (Exception e) {
                log.error("【意图识别】请求异常，异常信息：{}", e.getMessage(), e);
                throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
            }
            if (Objects.isNull(dialogueIntentionResp)) {
                log.error("【意图识别】意图识别结果为空，意图结果：{}", dialogueIntentionResp);
                throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVER_PROCESSING_FAILED);
            }
            // (意图结果列表，不为空 || 内容推荐关键字列表，不为空) && (响应码为成功 || 响应码为综合搜索意图编码-10015001)
            boolean intentionRespHasData = CollUtil.isNotEmpty(dialogueIntentionResp.getData().getIntentionInfoList()) || CollUtil.isNotEmpty(dialogueIntentionResp.getData().getContentRecommendList());
            boolean isSuccessOrComprehensiveSearch = BaseResultCodeEnum.SUCCESS.getResultCode().equals(dialogueIntentionResp.getCode()) || SPECIAL_CODE.equals(dialogueIntentionResp.getCode());
            if (intentionRespHasData && isSuccessOrComprehensiveSearch) {
                log.info("【意图识别】响应结果：{}", JsonUtil.toJson(dialogueIntentionResp));

                vo = centerTaskAssembler.toDialogueIntentionVO(dialogueIntentionResp.getData());
                
                // 设置二级意图及参数map
                setDialogueSubIntention(vo);
                
                // 10015001 错误码兼容处理，直接更新为综合搜索意图
                if (SPECIAL_CODE.equals(dialogueIntentionResp.getCode())) {
                    vo.getIntentionInfoList().get(0).setIntention(DialogueIntentionEnum.COMPREHENSIVE_SEARCH.getCode());
                }

                return vo;
            }
            log.error("【意图识别】响应结果：{}", JsonUtil.toJson(dialogueIntentionResp));
            throw new BaseException(dialogueIntentionResp.getCode(), ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION.getResultMsg());
        } catch (Exception e) {
            log.error("【意图识别】dialogueIntentionClient-dialogueIntentionFuc dto:{} | 自研对话意图执行异常error:", JsonUtil.toJson(intentionReq), e);
            throw e;
        } finally {
            log.info("【意图识别】dialogueIntentionClient-dialogueIntentionFuc dto:{} | result:{}", JsonUtil.toJson(intentionReq), JsonUtil.toJson(vo));
        }
    }

    /**
     * 设置二级意图及参数map
     * @param vo
     */
	private void setDialogueSubIntention(DialogueIntentionVO vo) {
		if (null != vo && CollUtil.isNotEmpty(vo.getIntentionInfoList())) {
			for (IntentionInfo intention : vo.getIntentionInfoList()) {
				List<ToolObjectVO> toolCalls = intention.getToolCallsList();
				if (CollUtil.isEmpty(toolCalls)) {
					continue;
				}
				intention.setSubIntention(toolCalls.get(0).getCode());
				List<KeyValueVO> args = toolCalls.get(0).getArguments();
				HashMap<String, List<String>> argumentMap = new HashMap<>(Const.NUM_16);
				if (CollUtil.isNotEmpty(args)) {
					for (KeyValueVO arg : args) {
						argumentMap.put(arg.getKey(), arg.getValue());
					}
				}
				intention.setArgumentMap(argumentMap);
			}
		}
	}
}
