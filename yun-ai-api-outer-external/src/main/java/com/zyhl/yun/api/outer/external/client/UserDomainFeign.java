package com.zyhl.yun.api.outer.external.client;


import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.external.client.interceptor.UserFeignInterceptor;
import com.zyhl.yun.api.outer.external.client.req.*;
import com.zyhl.yun.api.outer.external.client.resp.UserDomainRspDTO;
import com.zyhl.yun.api.outer.external.client.resp.UserRoutePolicyDTO;
import com.zyhl.yun.api.outer.external.client.resp.user.UserInfoResp;
import com.zyhl.yun.api.outer.external.client.resp.user.UserTokenResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * 用户域feign
 *
 * <AUTHOR>
 */
@FeignClient(url = "${user.url}", path = "${user.path}", configuration = UserFeignInterceptor.class, name = "UserDomainFeign")
public interface UserDomainFeign {

    /**
     * 根据token获取用户信息
     *
     * @param queryUserReqDTO 查询参数
     * @return 用户信息
     */
    @PostMapping("user/auth/getUserInfoByToken")
    BaseResult<UserDomainRspDTO> getUserInfoByToken(QueryUserReqDTO queryUserReqDTO);

    /**
     * 用户路由查询接口
     *
     * @param dto 路由查询参数
     * @return 路由信息
     */
    @PostMapping("user/route/qryRoutePolicy")
    BaseResult<UserRoutePolicyDTO> qryRoutePolicy(QryRoutePolicyReqDTO dto);

    /**
     * 用户信息查询接口
     *
     * @param dto 用户信息查询参数
     * @return 用户信息
     */
    @PostMapping("user/status/query")
    BaseResult<UserDomainRspDTO> getUserInfo(GetUserInfoReqDTO dto);

    /**
     * 获取指定用户的信息，包含用户扩展信息、用户简介信息
     *
     * @param req 用户信息查询参数
     * @return 用户信息
     */
    @PostMapping("/user/getUser")
    BaseResult<UserInfoResp> getUserInfoData(UserInfoReq req);

    /**
     * 批量查询用户头像信息（单次请求最大支持200个用户）
     *
     * @param reqs 用户信息查询参数集合
     * @return
     */
    @PostMapping("/user/getUserAvatarBatch")
    BaseResult<List<UserInfoResp>> getUserAvatarBatch(List<UserInfoReq> reqs);

    /**
     * 获取指定用户的统一认证token
     * 
     * @param baseToken basic token
     * @param req 用户信息查询参数
     * @return 用户的统一认证token
     */
    @PostMapping("/user/querySpecToken")
    BaseResult<UserTokenResp> querySpecToken(@RequestHeader("Authorization") String baseToken, UserTokenReq req);

}
