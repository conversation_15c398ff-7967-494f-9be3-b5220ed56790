package com.zyhl.yun.api.outer.external.strategy;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.domain.req.IntelligentSearchQueryEntity;
import com.zyhl.yun.api.outer.domain.resp.IntelligentSearchRespEntity;
import com.zyhl.yun.api.outer.external.client.BaiduIntelligentSearchClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 百度 智能查询
 * <AUTHOR>
 */
@RefreshScope
@Service
@Slf4j
public class BaiduStrategyImpl implements IntelligentSearchStrategy {

    @Resource
    private BaiduIntelligentSearchClient searchClient;

    @Value("${intelligentsearch.BaiDuThreshold}")
    private String threshold;


    /**
     * 百度 智能查询接口
     * @param query
     * @return
     */
    @Override
    public BaseResult<IntelligentSearchRespEntity> intelligentSearch(IntelligentSearchQueryEntity query) {
        query.setThreshold(Double.parseDouble(threshold));
        return searchClient.intelligentSearch(query);
    }
}
