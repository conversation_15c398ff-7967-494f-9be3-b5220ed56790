package com.zyhl.yun.api.outer.external.strategy;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.domain.req.IntelligentSearchQueryEntity;
import com.zyhl.yun.api.outer.domain.resp.IntelligentSearchRespEntity;
import com.zyhl.yun.api.outer.external.client.IflytekIntelligentSearchClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 科大讯飞 智能查询接口实现
 * @author: yangkailun
 */
@RefreshScope
@Service
@Slf4j
public class IflyTekIntelligentSearchImpl implements IntelligentSearchStrategy {


    @Resource
    private IflytekIntelligentSearchClient searchClient;

    @Value("${intelligentsearch.IFLYTEKThreshold}")
    private String threshold;

    /**
     * 科大讯飞 智能查询接口
     * @param query 查询参数
     * @return 结果
     */
    @Override
    public BaseResult<IntelligentSearchRespEntity> intelligentSearch(IntelligentSearchQueryEntity query) {
        query.setThreshold(Double.parseDouble(threshold));
        return searchClient.intelligentSearch(query);
    }
}
