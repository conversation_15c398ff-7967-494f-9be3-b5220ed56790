package com.zyhl.yun.api.outer.external.service.impl;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.domain.entity.TemplateMatchEntity;
import com.zyhl.yun.api.outer.domain.resp.TemplateMatchRsqEntity;
import com.zyhl.yun.api.outer.domainservice.TemplateMatchService;
import com.zyhl.yun.api.outer.external.client.TemplateMatchClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 模板匹配服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TemplateMatchServiceImpl implements TemplateMatchService {

    @Resource
    private TemplateMatchClient templateMatchClient;

    @Override
    public BaseResult<TemplateMatchRsqEntity> templateMatch(TemplateMatchEntity entity) {
        return templateMatchClient.templateMatch(entity);
    }
}
