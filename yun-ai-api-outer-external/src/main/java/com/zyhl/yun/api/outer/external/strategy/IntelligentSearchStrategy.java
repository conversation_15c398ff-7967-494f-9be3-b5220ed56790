package com.zyhl.yun.api.outer.external.strategy;


import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.domain.req.IntelligentSearchQueryEntity;
import com.zyhl.yun.api.outer.domain.resp.IntelligentSearchRespEntity;

/**
 * 智能搜索
 * <AUTHOR>
 */
public interface IntelligentSearchStrategy {

    /**
     * 智能搜索
     * @param query 查询参数
     * @return 搜索结果
     */
    BaseResult<IntelligentSearchRespEntity> intelligentSearch(IntelligentSearchQueryEntity query);
}
