package com.zyhl.yun.api.outer.external.client.resp.text;


import com.zyhl.yun.api.outer.domain.valueobject.File;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 文本工具Client-响应结果-个人云资产搜索
 * @Author: WeiJingKun
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntelligentSearchFileRsp implements Serializable {

    /** 文件信息列表 */
    private List<File> fileList;

    /** 查询总数 */
    private Integer totalCount;

    /** 下一页游标 */
    private List<Object> pageAfter;

}
