package com.zyhl.yun.api.outer.external.client;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.domain.req.FaceSearchEntity;
import com.zyhl.yun.api.outer.domain.resp.FaceSearchRespEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 人脸搜图
 * <AUTHOR>
 */
@FeignClient(name = "${yun.external.facialImageSearch.service-name}", url = "${yun.external.facialImageSearch.url}")
public interface FacialImageSearchClient {

	/**
	 * 人脸搜图
	 *
	 * @param req 请求参数
	 * @return 人脸搜图结果
	 */
	@PostMapping(value = "/face/search")
	BaseResult<FaceSearchRespEntity> getClassFaceInfo(@RequestBody FaceSearchEntity req);

}
