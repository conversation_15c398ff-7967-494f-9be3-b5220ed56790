package com.zyhl.yun.api.outer.external.client.feignconfig;

import cn.hutool.json.JSONUtil;
import feign.Logger;
import feign.Request;
import feign.Response;
import feign.Util;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Objects;

/**
 * Feign日志
 * @Author: zzb
 */
@Slf4j
public class FeignLogger extends Logger {

    @Override
    protected void logRequest(String configKey, Level logLevel, Request request) {

    }

    @Override
    protected Response logAndRebufferResponse(String configKey,
                                              Level logLevel,
                                              Response response,
                                              long elapsedTime)
            throws IOException {

        // url
        Request request = response.request();
        String requestUrl = request.httpMethod().name() + " " + request.url();

        // header
        String requestHeader = JSONUtil.toJsonStr(request.headers());

        // body
        String bodyMsg = Objects.isNull(request.body()) ? "" : new String(request.body());

        // 耗时
        String time = elapsedTime + "ms";

        // 响应
        String responseMsg = "";
        int status = response.status();
        boolean hasResBody = response.body() != null && !(status == 204 || status == 205);
        if (hasResBody) {
            byte[] bodyData = Util.toByteArray(response.body().asInputStream());
            response = response.toBuilder().body(bodyData).build();
            responseMsg = new String(bodyData);
        }

        log(configKey, "feign请求url: %s，请求头：%s feign请求体：%s, feign响应：%s，feign耗时：%s", requestUrl, requestHeader, bodyMsg, responseMsg, time);
        return response;
    }

    @Override
    protected void log(String configKey, String format, Object... args) {
        log.info(String.format(methodTag(configKey) + format, args));
    }
}
