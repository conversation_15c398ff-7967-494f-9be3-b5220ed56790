package com.zyhl.yun.api.outer.external.client.resp.text;

import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchActivity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 文本工具Client-响应结果-活动搜索
 * @Author: WeiJingKun
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntelligentSearchActivityRsp implements Serializable {

    /** 活动信息 */
    private List<SearchActivity> activityList;

    /** 下一页起始资源标识符，最后一页该值为空 */
    private List<Object> pageAfter;

    /** 记录总数 */
    private Integer totalCount;

}
