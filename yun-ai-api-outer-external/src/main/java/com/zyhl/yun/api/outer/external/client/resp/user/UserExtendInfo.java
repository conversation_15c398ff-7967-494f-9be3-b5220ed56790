package com.zyhl.yun.api.outer.external.client.resp.user;

import lombok.Data;

/**
 * 用户扩展信息实体类
 *
 * <AUTHOR>
 */
@Data
public class UserExtendInfo {
    /**
     * 运营商编码，详见运营商编码枚举说明
     */
    private String imsiCode;

    /**
     * 省份编码
     */
    private String province;

    /**
     * 是否实名
     * 0-未实名
     * 1-已实名
     */
    private Integer isRealVerified;

    /**
     * 城市编码
     */
    private String city;

    /**
     * 是否异地登录保护，详见是否状态枚举
     */
    private Integer isOffSiteLoginProtect;

    /**
     * 是否网页登录保护，详见是否状态枚举
     */
    private Integer isWebLoginProtect;

    /**
     * 是否账号密码登录保护，详见是否状态枚举
     */
    private Integer isAccPassProtect;

    /**
     * 是否发送登陆短信，详见是否状态枚举
     */
    private Integer isSendLoginSMS;

    /**
     * 是否需要主设备授权，详见是否状态枚举
     */
    private Integer isPrimDevAuth;

    /**
     * 是否发送用户共享群组消息更新醒短信开关，详见是否状态枚举
     */
    private Integer isGroupUpdateMessSMS;

    /**
     * 仅异地登录时发送提醒短信开关，详见是否状态枚举
     */
    private Integer isSendRemoteLoginSMS;

    /**
     * 是否TV登录保护，详见是否状态枚举
     */
    private Integer isTVLoginProtect;
}