package com.zyhl.yun.api.outer.external.client;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.domain.req.IntelligentSearchQueryEntity;
import com.zyhl.yun.api.outer.domain.resp.IntelligentSearchRespEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 远端请求
 * <AUTHOR>
 */
@FeignClient(name = "${yun.external.tencentIntelligentSearch.service-name:tencentIntelligentSearch}", url = "${yun.external.tencentIntelligentSearch.url:http://10.19.16.193:8883/ai-gpu/tencent/yun/ai/current}")
public interface TencentIntelligentSearchClient {

	/**
	 * 智能搜图
	 *
	 * @param req 接口参数
	 * @return 智能搜图结果
	 */
	@PostMapping(value = "/intelligentsearch")
	BaseResult<IntelligentSearchRespEntity> intelligentSearch(@RequestBody IntelligentSearchQueryEntity req);

}
