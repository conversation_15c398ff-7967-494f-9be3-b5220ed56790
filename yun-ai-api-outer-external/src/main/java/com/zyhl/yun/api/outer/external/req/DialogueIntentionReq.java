package com.zyhl.yun.api.outer.external.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.external.req.DialogueIntentionReq} <br>
 * <b> description:</b>
 * 对话意图请求参数
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-03-05 15:15
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DialogueIntentionReq implements Serializable {

    private static final long serialVersionUID = 5155437610834033852L;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 对话ID
     */
    private String dialogueId;

    /**
     * 本次对话内容
     */
    private DialogueInfo currentDialogue;

    /**
     * 历史对话内容
     */
    private List<DialogueInfo> historyDialogueList;

    /**
     * 语义分析跳过标识
     * 1--当意图结果为012搜图片时，跳过语义分析算法，直接返回意图结果；
     */
    private Integer skipStatus = 0;

    /**
     * 时间戳
     */
    private String timestamp;

    @Data
    public static class DialogueInfo {

        /**
         * 对话内容
         */
        private String dialogue;

        /**
         * prompt
         */
        private String prompt;

        /**
         * 时间戳
         */
        private String timestamp;

        /**
         * 本次对话内容的意图结果，历史对话内容填入
         */
        private String intention;

    }
}
