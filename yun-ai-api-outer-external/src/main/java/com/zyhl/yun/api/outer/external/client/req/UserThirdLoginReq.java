package com.zyhl.yun.api.outer.external.client.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 描述：调用户三方登录获取用户 basic token的请求参数
 *
 * <AUTHOR> liuxuwen
 * @date 2025-07-02 10:33
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserThirdLoginReq {

	/**
	 * 手机号
	 */
	private String msisdn;
	/**
	 * CPID，由AAS分配。
	 */
	private String cpid;
	/**
	 * 客户端类型，3位字符串。由AAS分配。
	 */
	private String clienttype;
	/**
	 * 版本号
	 */
	private String version;
	/**
	 * 类型 13 统一认证登录
	 */
	private String pintype;

	/**
	 * 密码，用户密码，统一认证token等等参数
	 */
	private String dycpwd;
	/**
	 * 扩展信息
	 */
	private ExtInfo extInfo;

	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	public static class ExtInfo {

		/**
		 * 是否自动开户
		 */
		private String ifOpenAccount;
	}
}
