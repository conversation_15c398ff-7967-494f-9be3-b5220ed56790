package com.zyhl.yun.api.outer.external.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.platform.third.client.membercenter.MemberCenterClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.membercenter.dto.ConsumeBenefitReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.membercenter.dto.ConsumeBenefitResultReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.membercenter.dto.ConsumeQueryReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.membercenter.dto.UserInfoReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.membercenter.enums.AccountTypeEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.membercenter.enums.ConsumeResultEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.membercenter.enums.ConsumeTypeEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.membercenter.resp.ConsumeAvailableBenefitRsp;
import com.zyhl.hcy.yun.ai.common.platform.third.client.membercenter.vo.ConsumeBenefitRspVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yunuser.YunUserClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yunuser.vo.GetUserInfoByPhoneNumberVO;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.exception.BaseException;
import com.zyhl.yun.api.outer.external.MemberCenterExternalService;

import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;
import java.util.Objects;

/**
 * 会员中心接口服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MemberCenterExternalServiceImpl implements MemberCenterExternalService {

    @Resource
    private MemberCenterClient memberCenterClient;
    @Resource
    private YunUserClient yunUserClient;

    /**
     * 权益消费不足
     */
    private static final String BENEFIT_LACK = "2000001002";
    /**
     * 重试次数
     */
    private static final int RETRY_COUNT = 3;

    @Override
    public String consumeBenefit(String userId, String benefitNo) {
        if (CharSequenceUtil.isEmpty(userId)) {
            log.info("用户id为空：{}", userId);
            return "";
        }
        // 先查询用户信息
        GetUserInfoByPhoneNumberVO userVo = yunUserClient.getByUserDomainId(userId);
        if (userVo == null || CharSequenceUtil.isEmpty(userVo.getPhoneNumber())) {
            log.info("用户{}不存在或手机号码不存在", userId);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_NOT_FOUND);
        }

        return consumeBenefitByPhone(userVo.getPhoneNumber(), benefitNo);
    }

    @Override
    public String consumeBenefitByPhone(String phone, String benefitNo) {
        if (CharSequenceUtil.isEmpty(phone) || CharSequenceUtil.isEmpty(benefitNo)) {
            log.info("参数错误，phone：{}，benefitNo：{}", phone, benefitNo);
            return "";
        }
        try {
            // 请求参数
            ConsumeBenefitReqDTO req = new ConsumeBenefitReqDTO();
            req.setAccountName(phone);
            req.setBenefitNo(benefitNo);
            req.setConsumeType(ConsumeTypeEnum.TIMES.getCode());
            req.setConsumeNum("1");
            req.setAccountType(AccountTypeEnum.PHONE.getCode());
            log.info("权益消费参数：{}", JsonUtil.toJson(req));

            // 权益消费
            ConsumeBenefitRspVO resp = memberCenterClient.consumeBenefit(req);
            if (resp == null || CharSequenceUtil.isEmpty(resp.getConsumeSeq())) {
                log.info("用户不存在，手机号码：{}", phone);
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_NOT_FOUND);
            }

            return resp.getConsumeSeq();
        } catch (YunAiBusinessException e) {
            if (BENEFIT_LACK.equals(e.getCode())) {
                //权益消费不足
                throw new YunAiBusinessException(ResultCodeEnum.MEMBER_BENEFIT_NUM);
            }
            log.error("权益消费失败，失败原因：{}", e.getMessage(), e);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        } catch (BaseException e) {
            throw new YunAiBusinessException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new YunAiBusinessException(ResultCodeEnum.UNKNOWN_ERROR);
        }
    }

    @Override
    public boolean consumeBenefitFail(String userId, String benefitNo, String consumeSeq) {
        if (CharSequenceUtil.isEmpty(userId)) {
            log.info("用户id为空：{}", userId);
            return false;
        }
        // 先查询用户信息
        GetUserInfoByPhoneNumberVO userVo = yunUserClient.getByUserDomainId(userId);
        if (userVo == null || CharSequenceUtil.isEmpty(userVo.getPhoneNumber())) {
            log.info("用户{}不存在或手机号码不存在", userId);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_NOT_FOUND);
        }

        return consumeBenefitFailByPhone(userVo.getPhoneNumber(), benefitNo, consumeSeq);
    }

    @Override
    public boolean consumeBenefitFailByPhone(String phone, String benefitNo, String consumeSeq) {
        if (CharSequenceUtil.isEmpty(phone) || CharSequenceUtil.isEmpty(benefitNo) || CharSequenceUtil.isEmpty(consumeSeq)) {
            log.info("参数错误，phone：{}，benefitNo：{}，consumeSeq：{}", phone, benefitNo, consumeSeq);
            return false;
        }
        try {
            // 请求参数
            ConsumeBenefitResultReqDTO req = new ConsumeBenefitResultReqDTO();
            req.setAccountName(phone);
            req.setBenefitNo(benefitNo);
            req.setConsumeSeq(consumeSeq);
            req.setConsumeResult(ConsumeResultEnum.ROLLBACK.getCode());
            log.info("会员{}权益消费回滚参数：{}", phone, JsonUtil.toJson(req));

            // 权益消费回滚
            for (int i = 0; i < RETRY_COUNT; i++) {
                log.info("会员{}权益消费失败，执行第{}次回滚", phone, i + 1);
                try {
                    memberCenterClient.consumeBenefitResult(req);
                    log.info("会员{}权益消费回滚成功", phone);
                    return true;
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                if (i == 2) {
                    log.error("会员的手机号码{}的会员权益回滚失败，权益项编号：{}，消费码：{}", phone, benefitNo, consumeSeq);
                }
                try {
                    Thread.sleep(100);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return false;
    }

    @Override
    public ConsumeAvailableBenefitRsp.AvailableBenefitRsp queryAvailableBenefit(String userId, String benefitNo) {
        if (StringUtils.isBlank(userId)) {
            log.info("用户id为空：{}", userId);
            return null;
        }
        // 先查询用户信息
        GetUserInfoByPhoneNumberVO userVo = yunUserClient.getByUserDomainId(userId);
        if (Objects.isNull(userVo) || StringUtils.isBlank(userVo.getPhoneNumber())) {
            log.info("用户{}不存在或手机号码不存在", userId);
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_NOT_FOUND);
        }

        return queryAvailableBenefitByPhone(userVo.getPhoneNumber(), benefitNo);
    }

    @Override
    public ConsumeAvailableBenefitRsp.AvailableBenefitRsp queryAvailableBenefitByPhone(String phoneNumber, String benefitNo) {
        ConsumeQueryReqDTO consumeQueryReqDTO = new ConsumeQueryReqDTO();
        consumeQueryReqDTO.setBenefitNo(benefitNo);
        consumeQueryReqDTO.setAccountType(AccountTypeEnum.PHONE.getCode());
        consumeQueryReqDTO.setAccountName(phoneNumber);
        return memberCenterClient.queryAvailableBenefit(consumeQueryReqDTO);
    }

	@Override
	public Boolean isMember(String phone, List<String> benefitNos) {
		UserInfoReqDTO req = new UserInfoReqDTO();
		req.setAccountType(AccountTypeEnum.PHONE.getCode());
		req.setAccountName(phone);
		req.setToken(RequestContextHolder.getToken());
		return memberCenterClient.isMember(req, benefitNos);
	}
}
