package com.zyhl.yun.api.outer.external.service.impl;

import cn.hutool.core.io.FileTypeUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.utils.ZipUtil;
import com.zyhl.yun.api.outer.domainservice.NfsFileService;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.external.service.NfsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.zyhl.yun.api.outer.constants.Const.FILE_FORMAT_ZIP;

/**
 * NFS文件服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class NfsFileServiceImpl implements NfsFileService {

    @Resource
    private NfsService nfsService;

    private static final Pattern FILENAME_PATTERN = Pattern.compile("filename=\"(.*?)\"");

    @Override
    public File getFile(String url, String requestId) {

        String fileType = getFileType(url);
        String dirPath = nfsService.getDirPath(Long.parseLong(requestId));
        File downloaded = nfsService.downloadHttpUrl(url, dirPath, fileType);
        if (!FILE_FORMAT_ZIP.equals(fileType)) {
            return downloaded;
        }
        ZipUtil.unZips(downloaded.getAbsolutePath(), dirPath);
        File[] files = new File(dirPath).listFiles();
        if (files != null) {
            for (File file : files) {
                if (!file.isDirectory()) {
                    String filePath = file.getAbsolutePath();
                    String suffix = filePath.substring(filePath.lastIndexOf(".") + 1).toLowerCase();
                    if (NfsServiceImpl.LIVP_INNER_TYPE.contains(suffix)) {
                        return file;
                    }
                }
            }
        }
        log.error("从livp文件提取图片失败，url:{}", url);
        throw new YunAiBusinessException(ResultCodeEnum.UN_SUPPORT_FILETYPE);
    }

    private String getFileType(String url) {

        try {
            String filename = cutFilename(URLDecoder.decode(url, StandardCharsets.UTF_8.name()));
            try (InputStream inputStream = nfsService.getInputStreamFromUrl(url)) {
                return FileTypeUtil.getType(inputStream, filename);
            }
        } catch (UnsupportedEncodingException e) {
            log.error("个人云返回的下载url解码错误，url：{}", url, e);
            throw new YunAiBusinessException(ResultCodeEnum.PERSON_SAAS_ERROR);
        } catch (MalformedURLException e) {
            log.error("个人云返回的下载url格式错误，url：{}", url, e);
            throw new YunAiBusinessException(ResultCodeEnum.PERSON_SAAS_ERROR);
        } catch (IOException e) {
            log.error("个人云下载文件失败，url：{}", url, e);
            throw new YunAiBusinessException(ResultCodeEnum.PERSON_SAAS_ERROR);
        }
    }

    private String cutFilename(String urlStr) {
        Matcher matcher = FILENAME_PATTERN.matcher(urlStr);
        if (matcher.find()) {
            return matcher.group(1).toLowerCase();
        }
        return null;
    }
}
