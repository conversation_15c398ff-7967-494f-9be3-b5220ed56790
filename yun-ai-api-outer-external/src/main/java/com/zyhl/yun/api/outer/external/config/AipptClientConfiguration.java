package com.zyhl.yun.api.outer.external.config;

import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.AipptClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.properties.AipptProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * AiPPT客户端配置类
 * 自动配置AipptClient Bean
 *
 * <AUTHOR> Assistant
 * @date 2025/1/25
 */
@Slf4j
@Configuration
@ConditionalOnProperty(prefix = "external.ai-model.aippt", name = "enabled", havingValue = "true")
public class AipptClientConfiguration {

    /**
     * 配置AipptProperties Bean
     */
    @Bean
    @ConfigurationProperties(prefix = "external.ai-model.aippt")
    public AipptProperties aipptProperties() {
        return new AipptProperties();
    }

    /**
     * 配置AipptClient Bean
     */
    @Bean
    public AipptClient aipptClient(AipptProperties aipptProperties) {
        log.info("【AiPPT配置】初始化AipptClient，baseUrl: {}", aipptProperties.getBaseUrl());
        return new AipptClient(aipptProperties);
    }
}
