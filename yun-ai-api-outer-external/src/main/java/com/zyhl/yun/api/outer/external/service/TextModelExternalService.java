package com.zyhl.yun.api.outer.external.service;

import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.dto.CreateOutlineDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.dto.PptGenDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.dto.PptGenProgressDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.vo.PptGenProgressVO;
import com.zyhl.hcy.yun.ai.common.model.api.client.alippt.vo.PptGenVO;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.event.TextModelStreamEventListener;
import com.zyhl.hcy.yun.ai.common.model.api.client.vo.TextModelBaseVo;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelVlReqDTO;

/**
 * 模型外部接口
 *
 * <AUTHOR>
 */
public interface TextModelExternalService {

    /**
     * 千问流式接口
     *
     * @param reqDTO 请求参数
     * @param event  流式事件监听
     * @return true 成功
     */
    boolean qwenStream(TextModelTextReqDTO reqDTO, TextModelStreamEventListener event);

    /**
     * 讯飞云流式接口
     *
     * @param reqDTO 请求参数
     * @param event  流式事件监听
     * @return true 成功
     */
    boolean xfyunStream(TextModelTextReqDTO reqDTO, TextModelStreamEventListener event);

    /**
     * 九天流式接口
     *
     * @param reqDTO 请求参数
     * @param event  流式事件监听
     * @return true 成功
     */
    boolean jiutianStream(TextModelTextReqDTO reqDTO, TextModelStreamEventListener event);

    /**
     * 阿里百炼流式接口
     *
     * @param reqDTO 请求参数
     * @param event  流式事件监听
     * @return true 成功
     */
    boolean blianStream(TextModelTextReqDTO reqDTO, TextModelStreamEventListener event);

    /**
     * 阿里百炼流式文档接口
     *
     * @param reqDTO 请求参数
     * @param event  流式事件监听
     * @return true 成功
     */
    boolean blianQwenLongStream(TextModelTextReqDTO reqDTO, TextModelStreamEventListener event);

    /**
     * 阿里百炼流式接口-计费版
     *
     * @param reqDTO 请求参数
     * @param event  流式事件监听
     * @return true 成功
     */
    boolean blianCalcStream(TextModelTextReqDTO reqDTO, TextModelStreamEventListener event);

    /**
     * 阿里通义星尘智能体流式接口
     *
     * @param reqDTO         请求参数
     * @param typeRelationId 智能体id
     * @param event          流式事件监听
     */
    void xchenStream(TextModelTextReqDTO reqDTO, String typeRelationId, TextModelStreamEventListener event);

    /**
     * 火山流式接口
     *
     * @param reqDTO 请求参数
     * @param event  流式事件监听
     * @return true 成功
     */
    boolean hshanStream(TextModelTextReqDTO reqDTO, TextModelStreamEventListener event);

    /**
     * 火山方舟流式接口
     *
     * @param reqDTO 请求参数
     * @param event  流式事件监听
     * @return true 成功
     */
    boolean hshanArkStream(TextModelTextReqDTO reqDTO, TextModelStreamEventListener event);

    /**
     * 流式对话（文本大模型）
     *
     * @param modelCode 模型编码
     * @param reqDTO    请求参数
     * @param event     流式事件监听
     * @return true 成功
     */
    boolean streamDialogue(String modelCode, TextModelTextReqDTO reqDTO, TextModelStreamEventListener event);

    /**
     * 流式对话（视觉大模型）
     *
     * @param modelCode 模型编码
     * @param reqDTO    请求参数
     * @param event     流式事件监听
     * @return true 成功
     */
    boolean streamVlDialogue(String modelCode, TextModelVlReqDTO reqDTO, TextModelStreamEventListener event);

    /**
     * AI ppt 生成大纲流式对话
     *
     * @param reqDTO 请求参数
     * @param event  流式事件监听
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2025-4-29 16:05
     */
    boolean streamAiPptOutlineDialogue(CreateOutlineDTO reqDTO, TextModelStreamEventListener event);

    /**
     * 生成PPT接口
     *
     * @param dto 请求参数
     * @return 结果
     */
    PptGenVO generateAiPpt(PptGenDTO dto);

    /**
     * 获取PPT生成进度接口
     *
     * @param dto 请求参数
     * @return 结果
     */
    PptGenProgressVO getAiPptGenProgress(PptGenProgressDTO dto);

    /**
     * 文本模型对话
     *
     * @param reqDTO 请求参数
     * @return 结果
     */
    TextModelBaseVo textModelDialogue(TextModelTextReqDTO reqDTO);

}
