package com.zyhl.yun.api.outer.external.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.HttpUtil;
import com.zyhl.hcy.yun.ai.common.base.enums.AIModuleEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.YunDiskClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.BatchFileDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.DriverGetDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.FileCatalogReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.FileGetContentReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.FilePathUploadContentReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.dto.FileReqDTO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.AICatalogVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.AIFileVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.BatchFileVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.DriveVO;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.FileUploadVO;
import com.zyhl.hcy.yun.ai.common.platform.third.dto.GetUserInfoByPhoneNumberRespDTO;
import com.zyhl.yun.api.outer.exception.BaseException;
import com.zyhl.yun.api.outer.external.YunDiskExternalService;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;

/**
 * 云盘外部服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class YunDiskExternalServiceImpl implements YunDiskExternalService {

    @Resource
    private YunDiskClient yunDiskClient;

    @Override
    public AICatalogVO createDir(Integer module) {
        final String userId = RequestContextHolder.getUserId();
        final String phone = RequestContextHolder.getPhoneNumber();
        final Integer belongsPlatform = RequestContextHolder.getBelongsPlatform();
        return createDir(userId, phone, belongsPlatform, module);
    }

    @Override
    public AICatalogVO createDir(String userId, String phone, Integer belongsPlatform, Integer module) {
        try {
            log.info("用户{}创建云盘目录{}", userId, module);
            // 调用云盘接口创建目录
            final FileCatalogReqDTO fileCatalogReqDTO = new FileCatalogReqDTO(userId, phone, belongsPlatform, module);
            return yunDiskClient.getCatalog(fileCatalogReqDTO);
        } catch (Throwable e) {
            log.error("用户{}调用云盘创建目录异常 = {}", userId, e);
        }
        return null;
    }

    @Override
    public FileUploadVO uploadFilePath(String path, AIModuleEnum module, String userId, Integer belongsPlatform, String fileSuffix, String base64) {
        FileUploadVO uploadResp = null;

        if (StringUtils.isNotBlank(base64)) {
            // 上传图片参数
            final FilePathUploadContentReqDTO uploadReq = new FilePathUploadContentReqDTO();
            uploadReq.setUserId(userId);
            uploadReq.setBelongsPlatform(belongsPlatform);
            uploadReq.setPath(path);

            uploadReq.setVirtualFile(FileReqDTO.builder().base64(base64).build());
            uploadReq.setFileSuffix(fileSuffix);

            uploadResp = yunDiskClient.uploadFilePath(uploadReq);
            if (uploadResp == null || CharSequenceUtil.isEmpty(uploadResp.getFileId())) {
                log.info("上传失败，uploadResp={}，用户id={}", uploadResp, userId);
                throw new BaseException("", "文件上传云盘失败");
            }
        }

        return uploadResp;
    }
/*

    @Override
    public FileUploadVO uploadFilePath(String path, AIModuleEnum module, String userId, Integer belongsPlatform, String fileSuffix, String base64, String fileName) {
        FileUploadVO uploadResp = null;

        if (StringUtils.isNotBlank(base64)) {
            // 上传图片参数
            final FilePathUploadContentReqDTO uploadReq = new FilePathUploadContentReqDTO();
            uploadReq.setUserId(userId);
            uploadReq.setBelongsPlatform(belongsPlatform);
            uploadReq.setPath(path);

            uploadReq.setVirtualFile(FileReqDTO.builder().base64(base64).name(fileName).build());
            uploadReq.setFileSuffix(fileSuffix);

            uploadResp = yunDiskClient.uploadFilePath(uploadReq);
            if (uploadResp == null || CharSequenceUtil.isEmpty(uploadResp.getFileId())) {
                log.info("上传失败，uploadResp={}，用户id={}", uploadResp, userId);
                throw new BaseException("", "文件上传云盘失败");
            }
        }

        return uploadResp;
    }
*/

    /**
     * 上传文件到自定义路径（不添加"我的应用收藏"前缀）
     *
     * @param path            上传路径
     * @param module          模块
     * @param userId          用户id
     * @param belongsPlatform 底座
     * @param fileSuffix      后缀
     * @param base64          文件base64
     * @return 返回文件上传结果
     */
    @Override
    public FileUploadVO uploadFileToCustomPath(String path, AIModuleEnum module, String userId, Integer belongsPlatform, String fileSuffix, String base64, String fileName) {
        FileUploadVO uploadResp = null;

        if (StringUtils.isNotBlank(base64)) {
            // 上传图片参数
            final FilePathUploadContentReqDTO uploadReq = new FilePathUploadContentReqDTO();
            uploadReq.setUserId(userId);
            uploadReq.setBelongsPlatform(belongsPlatform);
            uploadReq.setPath(path);

            uploadReq.setVirtualFile(FileReqDTO.builder().base64(base64).name(fileName).build());
            uploadReq.setFileSuffix(fileSuffix);

            // 调用自定义路径上传方法，传入模块信息
            uploadResp = yunDiskClient.uploadFileToCustomPath(uploadReq, module);
            if (uploadResp == null || CharSequenceUtil.isEmpty(uploadResp.getFileId())) {
                log.info("上传失败，uploadResp={}，用户id={}", uploadResp, userId);
                throw new BaseException("", "文件上传云盘失败");
            }
        }

        return uploadResp;
    }

    @Override
    public String urlToBase64(String url) {
        final List<String> imageBase64List = urlToBase64(Arrays.asList(url));
        return CollUtil.isEmpty(imageBase64List) ? "" : imageBase64List.get(0);
    }

    private List<String> urlToBase64(List<String> urlList) {
        final List<String> base64List = new ArrayList<>();
        try {
            final Base64.Encoder encoder = Base64.getEncoder();
            for (String url : urlList) {
                byte[] bytes = HttpUtil.downloadBytes(url);
                String base64 = encoder.encodeToString(bytes);
                base64List.add(base64);
            }
        } catch (Exception e) {
            log.error("转base64出现异常：{}，异常信息", urlList, e);
        }

        return base64List;
    }


    @Override
    public AIFileVO getFileInfo(String userId, String fileId) {
        // 用户信息
        final GetUserInfoByPhoneNumberRespDTO userInfo = new GetUserInfoByPhoneNumberRespDTO();
        userInfo.setUserDomainId(Long.valueOf(userId));

        // 下载参数
        final FileGetContentReqDTO dto = new FileGetContentReqDTO();
        dto.setFileId(fileId);
        dto.setIsOriginal(true);
        dto.setUserInfo(userInfo);
        final AIFileVO vo = yunDiskClient.getFileContent(dto);
        if (vo == null) {
            log.warn("文件不存在，用户id：{}，文件id：{}", userId, fileId);
        }

        return vo;
    }

    @Override
    public BatchFileVO fileBatchGetByAllPlatform(String userId, Integer belongsPlatform, List<String> fileIds) {
        return fileBatchGetByAllPlatform(userId, belongsPlatform, fileIds, null);
    }

    @Override
    public BatchFileVO fileBatchGetByAllPlatform(String userId, Integer belongsPlatform, List<String> fileIds, List<String> imageThumbnailStyleList) {
        BatchFileDTO dto = new BatchFileDTO();
        dto.setUserId(userId);
        dto.setBelongsPlatform(belongsPlatform);
        dto.setFileIds(fileIds);
        dto.setImageThumbnailStyleList(imageThumbnailStyleList);
        dto.setExpireSec(86400);
        return yunDiskClient.fileBatchGetByAllPlatform(dto);
    }

    @Override
    public DriveVO getDrive(String userId, Integer belongsPlatform) {
        DriverGetDTO dto = new DriverGetDTO();
        dto.setUserId(userId);
        dto.setBelongsPlatform(belongsPlatform);
        try {
            return yunDiskClient.driveGet(dto);
        } catch (Exception e) {
            log.error("获取空间信息异常，userId：{}，异常信息：{}", userId, e.getMessage(), e);
        }
        return null;
    }


}
