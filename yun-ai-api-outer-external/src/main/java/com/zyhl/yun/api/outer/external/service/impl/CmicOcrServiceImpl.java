package com.zyhl.yun.api.outer.external.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.utils.FileBase64Util;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmicocr.CmicOcrClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmicocr.dto.CmicImageOcrReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmicocr.resp.CmicImageOcrResp;
import com.zyhl.hcy.yun.ai.common.model.api.client.vo.CmicImageOcrVO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.ImageOcrSendTypeEnum;
import com.zyhl.yun.api.outer.external.CmicOcrService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 图片ocr识别服务
 *
 * <AUTHOR>
 * @date 2025/3/10 11:22
 */
@Slf4j
@Service
public class CmicOcrServiceImpl implements CmicOcrService {

	@Resource
	private CmicOcrClient cmicOcrClient;

	@Resource
	private UidGenerator uidGenerator;

	@Override
	public String getImageOcr(String url) throws Exception {
		return getImageOcrByContent(ImageOcrSendTypeEnum.BASE64.getCode(), url);
	}

	/**
	 * 获取图片ocr结果
	 * 
	 * @param sendType 发送类型
	 * @param content  url或者localPath
	 * @return
	 */
	private String getImageOcrByContent(Integer sendType, String content) throws Exception {
		try {
			if (CharSequenceUtil.isEmpty(content)) {
				log.info("图片 sendType:{}, content 为空，ocr识别直接返回空", sendType);
				return null;
			}

			StringBuilder ocrContent = new StringBuilder();
			CmicImageOcrReqDTO reqDTO = new CmicImageOcrReqDTO();
			reqDTO.setRequestId(String.valueOf(uidGenerator.getUID()));
			if (String.valueOf(ImageOcrSendTypeEnum.BASE64.getCode()).equals(String.valueOf(sendType))) {
				// 图片获取base64传入orc识别
				reqDTO.setSendType(ImageOcrSendTypeEnum.BASE64.getCode());
				reqDTO.setBase64(FileBase64Util.urlToBase64(content));
			} else if (String.valueOf(ImageOcrSendTypeEnum.LOCAL_PATH.getCode()).equals(String.valueOf(sendType))) {
				// 图片先存到共享存储传入orc识别
				reqDTO.setSendType(ImageOcrSendTypeEnum.LOCAL_PATH.getCode());
				reqDTO.setLocalPath(content);
			}
			// base64请求图片ocr
			CmicImageOcrVO cmicImageOcrVO = cmicOcrClient.getImageOcr(reqDTO);
			if (ObjectUtil.isNotEmpty(cmicImageOcrVO) && CollUtil.isNotEmpty(cmicImageOcrVO.getLines())) {
				List<String> ocrContentList = cmicImageOcrVO.getLines().stream()
						.map(CmicImageOcrResp.CmicImageOcrTextInfo::getText).collect(Collectors.toList());
				// 文字换行显示
				ocrContent.append(String.join(StrPool.COMMA, ocrContentList));
				return ocrContent.toString();
			}
		} catch (Exception e) {
			log.error("图片 sendType:{}, content:{}, ocr识别异常:", sendType, content, e);
			throw e;
		}
		return null;
	}

}
