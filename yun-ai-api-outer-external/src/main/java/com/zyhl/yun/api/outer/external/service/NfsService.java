package com.zyhl.yun.api.outer.external.service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @since 2023/8/1 14:45
 */
public interface NfsService {

    /**
     * path 转换为 nfs 路径
     *
     * @param path path
     * @return nfs 路径
     */
    String convertPath(String path);

    /**
     * 获取任务目录
     *
     * @param taskId 任务id
     * @return 目录
     */
    String getDirPath(Long taskId);

    /**
     * 下载文件
     *
     * @param url      url
     * @param dirPath  path
     * @param fileType 文件类型
     * @return 文件
     */
    File downloadHttpUrl(String url, String dirPath, String fileType);

    /**
     * 获取文件流
     *
     * @param urlStr url
     * @return 流
     * @throws IOException expception
     */
    InputStream getInputStreamFromUrl(String urlStr) throws IOException;
}
