package com.zyhl.yun.api.outer.external.client.resp;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户路由策略
 * <AUTHOR>
 */
@Data
public class UserRoutePolicyDTO {

    private List<UserRoutePolicy> routePolicyList;

    @Data
    public static class UserRoutePolicy {
        /**
         * 站点ID。
         * 该字段存储站点的唯一标识符。
         */
        private String siteID;

        /**
         * 站点代码。
         * 该字段存储站点的代码。
         */
        private String siteCode;

        /**
         * 模块名称。
         * 该字段存储模块的名称。
         */
        private String modName;

        /**
         * HTTP URL。
         * 该字段存储HTTP协议的URL地址。
         */
        private String httpUrl;

        /**
         * HTTPS URL。
         * 该字段存储HTTPS协议的URL地址。
         */
        private String httpsUrl;

        /**
         * 哈希名称。
         * 该字段存储哈希名称。
         */
        private String hashName;

    }

    public String getModeUrl(String modName) {
        if (StringUtils.isBlank(modName)) {
            return null;
        }
        List<UserRoutePolicy> policyList = this.getRoutePolicyList();
        for (UserRoutePolicy routePolicy : policyList) {
            if (modName.equals(routePolicy.getModName())) {
                if (StringUtils.isNotBlank(routePolicy.getHttpsUrl())) {
                    return routePolicy.getHttpsUrl();
                }
                return routePolicy.getHttpUrl();
            }
        }
        return null;
    }

    public List<UserRoutePolicy> getUserRoutePolicys(List<String> modName) {

        List<UserRoutePolicy> results = new ArrayList<>();
        if (CollUtil.isEmpty(modName)) {
            return this.getRoutePolicyList();
        }
        List<UserRoutePolicy> policyList = this.getRoutePolicyList();
        for (UserRoutePolicy routePolicy : policyList) {
            if (modName.contains(routePolicy.getModName())) {
                results.add(routePolicy);
            }
        }
        return results;
    }
}
