package com.zyhl.yun.api.outer.external.service.impl;


import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.domain.entity.ocr.CertificateOcrEntity;
import com.zyhl.yun.api.outer.domain.entity.ocr.CertificateOcrReqEntity;
import com.zyhl.yun.api.outer.external.client.CertificateClient;
import com.zyhl.yun.api.outer.external.service.CertificateOcrService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.zyhl.yun.api.outer.enums.ResultCodeEnum.DOWNSTREAM_SERVER_PROCESSING_FAILED;
import static com.zyhl.yun.api.outer.enums.ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION;
import static com.zyhl.yun.api.outer.enums.ResultCodeEnum.FILE_BASE64_DATA_ERROR;

/**
 * 算法OCR识别
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CertificateOcrServiceImpl implements CertificateOcrService {
    @Resource
    private CertificateClient certificateOcr;

    @Override
    public CertificateOcrReqEntity certificateOcr(CertificateOcrEntity req) {
        BaseResult<CertificateOcrReqEntity> result;
        try {
            result = certificateOcr.certificateOcr(req);
            log.info("result算法返回状态码:{},结果：{}", result.getCode(), result.getData());
            if (!result.isSuccess()) {
                if (FILE_BASE64_DATA_ERROR.getResultCode().equals(result.getCode())) {
                    throw new YunAiBusinessException(FILE_BASE64_DATA_ERROR);
                }
                throw new YunAiBusinessException(DOWNSTREAM_SERVER_PROCESSING_FAILED);
            }
        } catch (Exception e) {
            log.error("算法OCR接口调用异常：", e);
            if (e instanceof YunAiBusinessException) {
                throw e;
            }
            throw new YunAiBusinessException(DOWNSTREAM_SERVICES_EXCEPTION);
        }
        return result.getData();
    }
}
