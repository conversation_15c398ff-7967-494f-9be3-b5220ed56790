package com.zyhl.yun.api.outer.external.client.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 获取增量操作游标reqDTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TimelineYmdStartCursorReqDTO {

    /**
     * 指定空间或者指定同步目录下增量信息的最新游标
     */
    private String cursor;
}
