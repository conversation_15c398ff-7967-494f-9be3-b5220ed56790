package com.zyhl.yun.api.outer.external.client.interceptor;

import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.hcy.plugin.neauth.util.ThirdAuthHeaderUtil;
import com.zyhl.yun.api.outer.constants.CommentConstants;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.Map;


/**
 * 个人云saasfeign拦截器
 *
 * <AUTHOR>
 */
@Slf4j
public class PerSaasFeignInterceptor implements RequestInterceptor {

    private final PerSaasConfig config = SpringUtil.getBean(PerSaasConfig.class);

    @Override
    public void apply(RequestTemplate requestTemplate) {
        // 设置请求头
        setHeader(requestTemplate);
    }

    /**
     * 鉴权请求头
     */
    private void setHeader(RequestTemplate requestTemplate) {
        // 获取请求头
        Map<String, String> headerMap = ThirdAuthHeaderUtil.generateHeaderMap(
                config.getAppKey(), config.getAppSecretId(), config.getAppSecret(), MDC.get(LogConstants.TRACE_ID), CommentConstants.NEAUTH_VERSION);
        if (MapUtil.isEmpty(headerMap)) {
            return;
        }
        headerMap.forEach(requestTemplate::header);
    }

}
