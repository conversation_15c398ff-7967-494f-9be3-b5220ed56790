package com.zyhl.yun.api.outer.external.service;

import com.zyhl.yun.api.outer.domain.entity.ocr.CertificateOcrEntity;
import com.zyhl.yun.api.outer.domain.entity.ocr.CertificateOcrReqEntity;

/**
 * Copyright © 2022 ChinaMobile Info. Tech Ltd. All rights reserved.
 * <p>
 * 敏感词校验
 *
 * <AUTHOR> href="<EMAIL>">ZhiFeng.Wu</a>
 * @date 2022/7/25 10:13
 */
public interface CertificateOcrService {

   /**
    * 敏感词校验
    * @Author: WeiJingKun
    *
    * @param req 请求参数
    * @return 卡证识别请求实体
    */
   CertificateOcrReqEntity certificateOcr(CertificateOcrEntity req);

}
