package com.zyhl.yun.api.outer.external.client.resp.market;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Classname NoteContentsResult
 * @Description 笔记内容Result
 * @Date 2024/3/1 11:33
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NoteContentsResult implements Serializable {

    /**
     * 文本内容
     */
    private String txtcontent;

    /**
     * 笔记对应的数据
     */
    private String data;

    /**
     * 类型
     */
    private String type;

    /**
     * 便签id
     */
    private String noteId;

    /**
     * 图文混排时的排序字段
     */
    private String sortOrder;

}
