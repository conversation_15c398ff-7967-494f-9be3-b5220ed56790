package com.zyhl.yun.api.outer.external.client;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.domain.req.IntelligentSearchQueryEntity;
import com.zyhl.yun.api.outer.domain.resp.IntelligentSearchRespEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 远端请求
 * <AUTHOR>
 */
@FeignClient(name = "${yun.external.intelligentSearch.service-name}", url = "${yun.external.intelligentSearch.url}")
public interface IflytekIntelligentSearchClient {

	/**
	 * 智能搜图
	 *
	 * @param req 智能搜索查询参数
	 * @return 智能搜索查询结果
	 */
	@PostMapping(value = "/intelligentsearch")
	BaseResult<IntelligentSearchRespEntity> intelligentSearch(@RequestBody IntelligentSearchQueryEntity req);

}
