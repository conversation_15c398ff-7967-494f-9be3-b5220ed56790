package com.zyhl.yun.api.outer.external.service.impl;


import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Base64;
import java.util.UUID;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.zyhl.hcy.yun.ai.common.base.eos.ObjectStoreClient;
import com.zyhl.yun.api.outer.exception.BaseException;
import com.zyhl.yun.api.outer.external.service.EOSExternalService;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import lombok.extern.slf4j.Slf4j;

/**
 * EOS文件存储
 * @author: shixiaokang
 * @date 2024-05-30
 */
@Slf4j
@Service
public class EOSExternalServiceImpl implements EOSExternalService {


    @Resource
    private ObjectStoreClient objectStoreClient;

    /**
     * 文件key前缀
     */
    private final String FILE_KEY_PREFIX = "YUN_AI";

    @Override
    public String upload(String userId, String base64, String ext) {
        if (CharSequenceUtil.isEmpty(base64)) {
            throw new BaseException("", "上传EOS的base64不能为空");
        }

        return upload(userId, Base64.getDecoder().decode(base64), ext);
    }

    @Override
    public String upload(String userId, byte[] file, String ext) {
        if (file == null || file.length == 0) {
            throw new BaseException("", "文件字节数组不能为空");
        }

        final String fileKey = FILE_KEY_PREFIX + StrPool.UNDERLINE + userId + StrPool.UNDERLINE + UUID.randomUUID().toString().replace("-", "");
        final InputStream inputStream = new ByteArrayInputStream(file);

        log.info("上传EOS，fileKey={}", fileKey);
        final Boolean blean = objectStoreClient.uploadObjectSimple(fileKey, inputStream);
        if (blean == null || !blean) {
            throw new BaseException("", "上传EOS失败");
        }

        log.info("上传EOS成功");
        return fileKey;
    }

    @Override
    public String uploadAndGetUrl(String userId, byte[] file, String ext, long expireTime) {
        String fileKey = upload(userId, file, ext);
        final String fileName = CharSequenceUtil.isEmpty(ext) ? "file" : ("file." + ext);

        return getFileUrl(fileKey, fileName, expireTime);
    }

    @Override
    public String getFileUrl(String fileKey, String fileName, long expireTime) {

        log.info("获取EOS地址，fileKey={}，过期时间={}ms", fileKey, expireTime);
        final String url;
        if (StringUtils.isBlank(fileName)) {
            url = objectStoreClient.generateSignedInline(fileKey, expireTime);
        } else {
            url = objectStoreClient.generateSignedAttachment(fileKey, fileName, expireTime);
        }
        if (CharSequenceUtil.isEmpty(url)) {
            throw new BaseException("", "获取EOS地址失败");
        }

        return url;
    }

	@Override
	public Boolean downloadEos(String downloadPath, String objectKey) {
		log.info("downloadEos downloadPath:{}, objectKey:{}", downloadPath, objectKey);
        return objectStoreClient.internalDownloadObjectSimple(objectKey, downloadPath);
	}
}
