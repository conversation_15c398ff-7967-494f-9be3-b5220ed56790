package com.zyhl.yun.api.outer.external.client.resp.text;

import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchMailAttachment;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 文本工具Client-响应结果-邮件附件搜索
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntelligentSearchMailAttachmentRsp implements Serializable {

    /**
     * 邮件附件搜索结果列表
     */
    private List<SearchMailAttachment> searchMailAttachmentList;
    /**
     * 卡片标题
     */
    private String title;
    /**
     * 消息提示
     */
    private String tips;
    /**
     * 总记录数
     */
    private Integer totalCount;

}
