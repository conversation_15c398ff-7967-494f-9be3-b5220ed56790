package com.zyhl.yun.api.outer.external.client.resp;


import com.zyhl.yun.api.outer.domain.valueobject.YmdFileInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 * 查询指定目录下时光轴照片（新，用于年月日时光轴）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TimelineYmdFileInfRspDTO {

    /**
     * 文件列表
     */
    private List<YmdFileInfo> fileList;

    /**
     * 下一页起始资源标识符, 最后一页该值为空。
     */
    private String nextPageCursor;
}
