package com.zyhl.yun.api.outer.external.client.interceptor;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 个人云saasfeign配置类
 * @author: yangkailun
 */
@Configuration
@ConfigurationProperties(
		prefix = "yun.external.person"
)
public class PerSaasConfig {

	private String appKey;
	private String appSecretId;
	private String appSecret;
	private String serviceId;
	private String defaultUrl;
	private String path;

	public String getAppKey() {
		return appKey;
	}

	public String getAppSecretId() {
		return appSecretId;
	}

	public String getAppSecret() {
		return appSecret;
	}

	public void setAppKey(String appKey) {
		this.appKey = appKey;
	}

	public void setAppSecretId(String appSecretId) {
		this.appSecretId = appSecretId;
	}

	public void setAppSecret(String appSecret) {
		this.appSecret = appSecret;
	}

	public String getServiceId() {
		return serviceId;
	}

	public void setServiceId(String serviceId) {
		this.serviceId = serviceId;
	}

	public String getDefaultUrl() {
		return defaultUrl;
	}

	public void setDefaultUrl(String defaultUrl) {
		this.defaultUrl = defaultUrl;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}
}
