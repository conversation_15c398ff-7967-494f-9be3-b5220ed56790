package com.zyhl.yun.api.outer.external.service.impl;


import com.zyhl.hcy.yun.ai.common.base.enums.YunAiCommonResultCode;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.external.client.FileExternalClient;
import com.zyhl.yun.api.outer.external.client.interceptor.PerSaasConfig;
import com.zyhl.yun.api.outer.external.client.resp.UserRoutePolicyDTO;
import com.zyhl.yun.api.outer.external.service.RouteFeignClientFactory;
import com.zyhl.yun.api.outer.external.service.UserEtnService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.FeignClientBuilder;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.zyhl.yun.api.outer.constants.Const.PERSONAL_WORD;


/**
 * 个人云client工厂类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PerSaasServiceClientFactory implements RouteFeignClientFactory<FileExternalClient> {

	@Resource
	private UserEtnService userEtnService;

	@Autowired
	private PerSaasConfig perSaasConfig;

	private final FeignClientBuilder feignClientBuilder;

	public PerSaasServiceClientFactory(ApplicationContext applicationContext) {
		this.feignClientBuilder = new FeignClientBuilder(applicationContext);
	}


	@Override
	public FileExternalClient getFeignClientByRoute(Long userId) {
		UserRoutePolicyDTO routePolicyDTO = userEtnService.qryRoutePolicy(userId);
		String url = routePolicyDTO.getModeUrl(perSaasConfig.getServiceId());
		if (StringUtils.isBlank(url)) {
			log.info("个人云路由不存在,路由信息：{}",routePolicyDTO);
			throw new YunAiBusinessException(YunAiCommonResultCode.DOWNSTREAM_SERVICES_EXCEPTION);
		}
		perSaasConfig.setDefaultUrl(url);
		return getFeignClient(perSaasConfig);
	}

	public FileExternalClient getFeignClient(PerSaasConfig perSaasConfig) {
		return this.feignClientBuilder.forType(FileExternalClient.class, perSaasConfig.getServiceId()).url(perSaasConfig.getDefaultUrl()).build();
	}

	@Override
	public FileExternalClient getFeignClient(String serviceId, String url, String path) {
		if(PERSONAL_WORD.equals(serviceId)){
			return this.feignClientBuilder.forType(FileExternalClient.class, serviceId).url(url).build();
		}
		return this.feignClientBuilder.forType(FileExternalClient.class, serviceId).url(url).path(path).build();
	}

}
