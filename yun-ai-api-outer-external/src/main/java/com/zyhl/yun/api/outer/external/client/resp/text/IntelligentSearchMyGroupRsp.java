package com.zyhl.yun.api.outer.external.client.resp.text;

import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchGroup;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 文本工具Client-响应结果-我的圈子搜索
 * @Author: WeiJingKun
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntelligentSearchMyGroupRsp implements Serializable {

    /** 圈子信息列表 */
    private List<SearchGroup> groupList;

    /** 记录总数 */
    private Integer totalCount;

}
