package com.zyhl.yun.api.outer.external.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.rag.client.*;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.RecallQueryDTO;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.TextFeatureDTO;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.dialogue.RecallConfig;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.dialogue.RerankConfig;
import com.zyhl.hcy.yun.ai.common.rag.enums.KnowledgeRerankTypeEnum;
import com.zyhl.hcy.yun.ai.common.rag.enums.RewriteQueryTypeEnum;
import com.zyhl.hcy.yun.ai.common.rag.vo.RecallResultVO;
import com.zyhl.hcy.yun.ai.common.rag.vo.RerankResultVO;
import com.zyhl.hcy.yun.ai.common.rag.vo.RewriteResultVO;
import com.zyhl.yun.api.outer.config.NoteKnowledgeDialogueProperties;
import com.zyhl.yun.api.outer.config.WhiteListProperties;
import com.zyhl.yun.api.outer.domain.dto.RecallDTO;
import com.zyhl.yun.api.outer.domain.dto.RerankDTO;
import com.zyhl.yun.api.outer.external.NoteRagExternalService;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 描述：笔记助手-rag外部服务实现
 *
 * <AUTHOR> yinxin  2025/6/24 14:53
 */
@Slf4j
@Service
public class NoteRagExternalServiceImpl implements NoteRagExternalService {

    @Resource
    private RewriteClient rewriteClient;

    @Resource
    private KeywordClient keywordClient;
    @Resource
    private VectorClient vectorClient;
    @Resource
    private MultiRouteRecallClient multiRouteRecallClient;
    @Resource
    private RerankClient rerankClient;
    @Resource
    private RelevancyClient relevancyClient;
    @Resource
    private WhiteListProperties whiteListProperties;
    @Resource
    private NoteKnowledgeDialogueProperties noteKnowledgeDialogueProperties;

    @Override
    public RewriteResultVO rewrite(TextModelTextReqDTO reqDTO) {
        RewriteResultVO resultVO = rewriteClient.rewrite(reqDTO, noteKnowledgeDialogueProperties.getRewriteConfig());
        if (!RewriteQueryTypeEnum.isExist(resultVO.getQueryType())) {
            resultVO.setQueryType(RewriteQueryTypeEnum.TYPE_0.getQueryType());
        }
        if (ObjectUtil.isNotEmpty(whiteListProperties.getKnowledgeSummaryWhiteUser())) {
            if (!whiteListProperties.getKnowledgeSummaryWhiteUser().contains(RequestContextHolder.getPhoneNumber())) {
                log.info("【RAG重要节点日志】【笔记助手-rag问题重写】非白名单用户，重写后的查询类型改为其他");
                resultVO.setQueryType(RewriteQueryTypeEnum.TYPE_0.getQueryType());
            }
        }
        return resultVO;
    }

    @Override
    public List<List<String>> keywordExtract(List<String> textList) {
        return keywordClient.keywordExtract(textList, noteKnowledgeDialogueProperties.getKeywordConfig());
    }

    @Override
    public List<BigDecimal> textEmbed(String text, Long dialogueId) {
        TextFeatureDTO dto = new TextFeatureDTO();
        dto.setText(text);
        dto.setFileId(String.valueOf(dialogueId));
        return vectorClient.textEmbed(dto);
    }

    @Override
    public List<RecallResultVO> recall(RecallDTO dto) {
        //标签（取意图MetaDataList）
        List<String> labelList = new ArrayList<>();
        if (Objects.nonNull(dto.getIntentionVO()) && ObjectUtil.isNotEmpty(dto.getIntentionVO().getIntentionInfoList())) {
            DialogueIntentionVO.IntentionInfo intentionInfo = dto.getIntentionVO().getIntentionInfoList().get(0);
            if (ObjectUtil.isNotEmpty(intentionInfo.getEntityList()) && ObjectUtil.isNotEmpty(intentionInfo.getEntityList().get(0).getMetaDataList())) {
                intentionInfo.getEntityList().get(0).getMetaDataList().forEach(item -> labelList.addAll(item.getValue()));
            }
        }

        // 封装参数
        RecallQueryDTO queryDTO = new RecallQueryDTO();
        queryDTO.setText(dto.getText());
        queryDTO.setFeature(dto.getFeature());
        queryDTO.setUserId(dto.getUserId());
        queryDTO.setLabelList(labelList);
        queryDTO.setFileIdList(dto.getFileIdList());
        queryDTO.setKnowledgeGroupList(dto.getKnowledgeGroupList());
        queryDTO.setVersion(dto.getVersion());
        if (CollUtil.isNotEmpty(dto.getTextKeywordList())) {
            //将提取到的关键字列表转换为空格分隔的字符串
            queryDTO.setKeyword(StringUtils.join(dto.getTextKeywordList(), StrPool.C_SPACE));
        }
        // 召回配置
        RecallConfig config = dto.getConfig();
        if (ObjectUtil.isEmpty(dto.getConfig())) {
            if (RewriteQueryTypeEnum.isType0(dto.getQueryType())) {
                config = noteKnowledgeDialogueProperties.getRecallConfig();
            } else {
                config = noteKnowledgeDialogueProperties.getRecallConfigSummary();
            }
        }

        //config.getPersonalBase().setEnabled(true);
        log.info("【笔记助手-知识库对话】【RAG重要节点日志】【多路召回】入参queryDTO:{} | config:{}", JsonUtil.toJson(queryDTO), JsonUtil.toJson(config));
        return multiRouteRecallClient.recall(queryDTO, config);
    }

    @Override
    public List<RerankResultVO> rerank(RerankDTO dto) {
        RerankConfig config;
        String rerankType = dto.getRerankType();
        // 个性化重排配置
        if (ObjectUtil.isNotEmpty(dto.getConfig())) {
            config = dto.getConfig();
        } else if (KnowledgeRerankTypeEnum.VECTOR.getType().equals(rerankType)) {
            //【向量】算法重排配置
            if (RewriteQueryTypeEnum.isType0(dto.getQueryType())) {
                config = noteKnowledgeDialogueProperties.getVectorRerankConfig();
            } else {
                config = noteKnowledgeDialogueProperties.getVectorRerankConfigSummary();
            }
        } else if (KnowledgeRerankTypeEnum.TEXT.getType().equals(rerankType)) {
            //【全文】算法重排配置
            if (RewriteQueryTypeEnum.isType0(dto.getQueryType())) {
                config = noteKnowledgeDialogueProperties.getTextRerankConfig();
            } else {
                config = noteKnowledgeDialogueProperties.getTextRerankConfigSummary();
            }
        } else if (KnowledgeRerankTypeEnum.KEYWORD.getType().equals(rerankType)) {
            //【关键字】算法重排配置
            if (RewriteQueryTypeEnum.isType0(dto.getQueryType())) {
                config = noteKnowledgeDialogueProperties.getKeywordRerankConfig();
            } else {
                config = noteKnowledgeDialogueProperties.getKeywordRerankConfigSummary();
            }
        } else {
            //【默认】算法重排配置
            if (RewriteQueryTypeEnum.isType0(dto.getQueryType())) {
                config = noteKnowledgeDialogueProperties.getRerankConfig();
            } else {
                config = noteKnowledgeDialogueProperties.getRerankConfigSummary();
            }
        }
        String rerankTypeDesc = KnowledgeRerankTypeEnum.getDescByType(rerankType);
        log.info("【笔记助手-知识库对话】【RAG重要节点日志】【rag算法重排】【{}】参数配置config:{}", rerankTypeDesc, JsonUtil.toJson(config));
        return rerankClient.rerank(dto.getText(), dto.getRecallList(), config, rerankTypeDesc);
    }


    @Override
    public List<RerankResultVO> relevancy(TextModelTextReqDTO reqDTO, List<RerankResultVO> rerankResult) {
        return relevancyClient.relevancy(reqDTO, rerankResult, noteKnowledgeDialogueProperties.getRelevancyConfig());
    }
}
