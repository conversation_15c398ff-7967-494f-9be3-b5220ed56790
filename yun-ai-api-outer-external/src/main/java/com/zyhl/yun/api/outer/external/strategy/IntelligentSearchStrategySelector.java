package com.zyhl.yun.api.outer.external.strategy;


import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.enums.AlgorithmInformationEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 智能搜索策略选择器
 * <AUTHOR>
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Component
public class IntelligentSearchStrategySelector {


    private IntelligentSearchStrategy intelligentSearchStrategy;

    @Resource
    private IflyTekIntelligentSearchImpl intelligentSearch;

    @Resource
    private BaiduStrategyImpl baiduStrategy;

    @Resource
    private TencentStrategyImpl tencentStrategy;


    public IntelligentSearchStrategy strategySelection(String type) {
        AlgorithmInformationEnum algorithmEnum = AlgorithmInformationEnum.getByCode(type);
        if (ObjectUtil.isEmpty(algorithmEnum)){
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        switch (Objects.requireNonNull(algorithmEnum)){
            case IFLYTEK:
                intelligentSearchStrategy = intelligentSearch;
                break;
            case BAIDU:
                intelligentSearchStrategy = baiduStrategy;
                break;
            case TENCENT:
                intelligentSearchStrategy = tencentStrategy;
                break;
            default:
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        return intelligentSearchStrategy;
    }
}
