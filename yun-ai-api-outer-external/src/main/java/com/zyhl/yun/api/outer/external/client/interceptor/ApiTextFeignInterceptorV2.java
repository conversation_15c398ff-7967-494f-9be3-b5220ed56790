package com.zyhl.yun.api.outer.external.client.interceptor;

import cn.hutool.core.collection.CollUtil;
import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.hcy.plugin.logger.util.MdcUtil;
import com.zyhl.hcy.yun.ai.common.base.constraint.HttpConstants;
import com.zyhl.yun.api.outer.constants.ReqHeadConst;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.http.HttpHeaders;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;

/**
 * 文本工具-feign拦截器（v2版本）
 * @Author: WeiJingKun
 */
@Slf4j
public class ApiTextFeignInterceptorV2 implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        // 设置请求头
        setHeader(requestTemplate);
        logCUrl(requestTemplate);
    }

    /**
     * 鉴权请求头
     */
    private void setHeader(RequestTemplate requestTemplate) {
        String tid = Optional.ofNullable(MDC.get(LogConstants.TRACE_ID)).orElse(MdcUtil.getDefaultTraceId());

        // 获取主线程请求头信息
        Map<String, String> headers = RequestContextHolder.getRequestHeaders();
        // 透传header
        headers.forEach(requestTemplate::header);

        requestTemplate.header("Content-Type", "application/json;charset=UTF-8");
        /** 去掉请求头的api版本号 */
        requestTemplate.removeHeader(ReqHeadConst.API_VERSION);
        // 设置api版本号V2
        requestTemplate.header(ReqHeadConst.API_VERSION, "v2");
        requestTemplate.header(LogConstants.TRACE_ID, tid);
        requestTemplate.header(HttpConstants.X_YUN_TID, tid);
        requestTemplate.header(HttpConstants.X_YUN_REQUEST_ID_HEADER, tid);
        requestTemplate.header(HttpHeaders.AUTHORIZATION, RequestContextHolder.getToken());
    }

    /**
     * 打印curl日志
     *
     * @param request
     */
    private void logCUrl(RequestTemplate request) {
        try {
            log.info("==feign接口请求信息== 请求curl: {};", generateCurl(request));
        } catch (Exception e) {
            log.error("打印curl日志异常:{} ", request, e);
        }
    }

    /**
     * 生成curl文本
     *
     * @param request
     * @return
     */
    private String generateCurl(RequestTemplate request) {
        Map<String, Collection<String>> headers = request.headers();
        byte[] body = request.body();
        String url = request.url();
        url = request.feignTarget().url() + url;
        String entity = body == null ? "" : new String(body).replace("\n", "").replaceAll(">\\s+<", "><");
        StringBuilder builder = new StringBuilder("curl --request POST '").append(url).append("' ");
        headers.forEach((key, values) -> {
            if (!key.equalsIgnoreCase(HttpHeaders.CONTENT_LENGTH)) {
                builder.append("--header '").append(key).append(": ").append(CollUtil.getFirst(values)).append("' ");
            }
        });
        builder.append("--data '").append(entity).append('\'');
        return builder.toString();
    }

}
