package com.zyhl.yun.api.outer.external.client.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取用户信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QryRoutePolicyReqDTO {
    /**
     * 用户信息
     */
    private RoutePolicyUserInfo userInfo;

    /**
     * 模块地址类型
     */
    private Integer modAddrType;

    @Data
    @NoArgsConstructor
    public static class RoutePolicyUserInfo {
        /**
         * 账户名称。
         * 该字段存储账户的名称。
         */
        private String accountName;

        /**
         * 账户类型。
         * 该字段存储账户的类型。
         */
        private Integer accountType;

        /**
         * 用户类型。
         * 该字段存储用户的类型。
         */
        private Integer userType;

        /**
         * 用户域ID。
         * 该字段存储用户的唯一标识符。
         */
        private Long userDomainId;


        RoutePolicyUserInfo(Long userDomainId) {
            this.userDomainId = userDomainId;
        }
    }

    public QryRoutePolicyReqDTO(Long userId) {
        this.modAddrType = 2;
        this.userInfo = new RoutePolicyUserInfo(userId);
    }
}
