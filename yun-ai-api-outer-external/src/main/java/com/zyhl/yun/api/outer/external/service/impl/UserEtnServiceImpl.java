package com.zyhl.yun.api.outer.external.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.redis.RedisRepository;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.common.okhttp.CommonOkHttpProxy;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.config.CommonOkHttpProxyProperties;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.domain.dto.UserInfoDTO;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.external.assembler.UserExternalAssembler;
import com.zyhl.yun.api.outer.external.client.UserDomainFeign;
import com.zyhl.yun.api.outer.external.client.interceptor.UserDomainConfig;
import com.zyhl.yun.api.outer.external.client.req.*;
import com.zyhl.yun.api.outer.external.client.req.UserThirdLoginReq.ExtInfo;
import com.zyhl.yun.api.outer.external.client.resp.UserDomainRspDTO;
import com.zyhl.yun.api.outer.external.client.resp.UserRoutePolicyDTO;
import com.zyhl.yun.api.outer.external.client.resp.user.UserInfoResp;
import com.zyhl.yun.api.outer.external.client.resp.user.UserTokenResp;
import com.zyhl.yun.api.outer.external.service.UserEtnService;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/11 下午9:55
 */
@Slf4j
@Service
public class UserEtnServiceImpl implements UserEtnService {

    private static final String KEY_OF_DATA = "data";

	private static final String KEY_OF_MESSAGE = "message";

	private static final String KEY_OF_CODE = "code";

	private static final String KEY_OF_SUCCESS = "success";

	/**
     * 用户域返回令牌失效错误码
     */
    private static final String USER_DOMAIN_TOKEN_EXPIRE = "05050006";

    /**
     * 2025/1/2获取迁移状态token校验失败暂时返回鉴权失败，后续等前端改好了再取消这个逻辑
     */
    private static final String MIGRATION_STATUS_GET_URI = "/migration/status/get";

    @Value("${server.servlet.context-path}")
    private String contextPath;

    @Resource
    private UserDomainFeign userDomainFeign;


    @Resource
    private RedisRepository redisRepository;
    @Resource
    private RedisOperateRepository redisOperateRepository;
    @Resource
    private UserExternalAssembler userExternalAssembler;
    @Resource
    private CommonOkHttpProxyProperties commonOkHttpProxyProperties;

    private final UserDomainConfig config = SpringUtil.getBean(UserDomainConfig.class);

    @Value("${user.mod-addr-type:2}")
    private Integer modAddrType;

    private final String SUCCESS_CODE = "104000";

    /**
     * 通过华为token获取用户信息接口
     *
     * @param token token
     * @return UserDomainRspDTO
     */
    @Override
    public UserDomainRspDTO queryUserIdByToken(String token) {

        QueryUserReqDTO queryUserReqDTO = QueryUserReqDTO.builder().token(token).build();
        BaseResult<UserDomainRspDTO> baseResult = null;
        try {
            baseResult = userDomainFeign.getUserInfoByToken(queryUserReqDTO);
            if (!baseResult.isSuccess()) {
                log.info("用户域请求响应结果：{}", baseResult.getMessage());
                String errorCode = baseResult.getCode();
                if (StringUtils.isNotBlank(errorCode)) {
                    if (USER_DOMAIN_TOKEN_EXPIRE.equals(errorCode)) {
                        log.warn("请求用户域获取用户信息接口异常, token:{}, code:{}", token, errorCode);
                        if ((contextPath + MIGRATION_STATUS_GET_URI).equals(RequestContextHolder.getUri())) {
                            throw new YunAiBusinessException(ResultCodeEnum.ERROR_NOT_AUTH);
                        }
                        throw new YunAiBusinessException(ResultCodeEnum.ERROR_TOKEN_EXPIRE);
                    }
                    log.error("请求用户域获取用户信息接口异常, token:{}, code:{}", token, errorCode);
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_NOT_AUTH);
                }
                throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVER_PROCESSING_FAILED);
            }
        } catch (Exception e) {
            log.error("请求用户域获取用户信息接口异常", e);
            if (e instanceof YunAiBusinessException) {
                throw e;
            }
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        }
        UserDomainRspDTO userDomainRspDTO = baseResult.getData();
        if (Objects.isNull(userDomainRspDTO) || Objects.isNull(userDomainRspDTO.getUserDomainId())) {
            return null;
        }
        return userDomainRspDTO;
    }

    @Override
    public UserRoutePolicyDTO qryRoutePolicy(Long userId) {

        String redisKey = RedisConstants.ROUTE_KEY + userId.toString();
        UserRoutePolicyDTO cacheRoute = redisRepository.getCacheObject(redisKey);
        if (Objects.isNull(cacheRoute)) {
            //查询路由策略
            QryRoutePolicyReqDTO qryRoutePolicyReqDTO = new QryRoutePolicyReqDTO(userId);
            UserRoutePolicyDTO routePolicyDTO = qryRoutePolicy(qryRoutePolicyReqDTO);
            //加入缓存
            cacheRoute = new UserRoutePolicyDTO();
            //TODO
            String[] modNames = RedisConstants.ROUTE_VALUE_KEY.split(",");
            cacheRoute.setRoutePolicyList(routePolicyDTO.getUserRoutePolicys(Arrays.asList(modNames)));
            redisRepository.setCacheObject(redisKey, cacheRoute, RedisConstants.ROUTE_KEY_TIMEOUT, TimeUnit.SECONDS);
        }
        return cacheRoute;
    }

    @Override
    public UserRoutePolicyDTO qryRoutePolicy(QryRoutePolicyReqDTO qryRoutePolicyDTO) {
        // 测试环境使用外网配置
        qryRoutePolicyDTO.setModAddrType(modAddrType);
        BaseResult<UserRoutePolicyDTO> baseResult = userDomainFeign.qryRoutePolicy(qryRoutePolicyDTO);
        if (!baseResult.isSuccess()) {
            log.error("获取用户域路由策略失败");
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        }
        return baseResult.getData();
    }

    @Override
    public UserDomainRspDTO getUserInfo(Long userId) {
        GetUserInfoReqDTO getUserInfoReqDTO = new GetUserInfoReqDTO(userId);
        return getUserInfo(getUserInfoReqDTO);
    }

    @Override
    public UserInfoDTO getUserInfoData(Long userId) {
        long startTime = System.currentTimeMillis();
        // 先从redis获取
        UserInfoDTO userInfoDTO = redisOperateRepository.getUserInfo(String.valueOf(userId));
        if (Objects.nonNull(userInfoDTO)) {
            log.info("从redis获取用户信息成功, 耗时:{}ms", System.currentTimeMillis() - startTime);
            return userInfoDTO;
        } else {
            userInfoDTO = new UserInfoDTO();
        }

        BaseResult<UserInfoResp> result = userDomainFeign.getUserInfoData(new UserInfoReq(userId));
        if (!result.isSuccess() || Objects.isNull(result.getData())) {
            log.error("获取用户信息失败");
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        }
        log.info("从用户域获取用户信息成功, 耗时:{}ms", System.currentTimeMillis() - startTime);

        // 数据转换
        UserInfoResp resp = result.getData();
        userInfoDTO.setUserId(resp.getUserDomainId());
        userInfoDTO.setPhoneNumber(resp.getPhoneNumber());
        userInfoDTO.setBelongsPlatform(resp.getBelongsPlatform());
        if (Objects.nonNull(resp.getUserExtendInfo())) {
            userInfoDTO.setProvince(resp.getUserExtendInfo().getProvince());
        }

        redisOperateRepository.setUserInfo(userInfoDTO);
        return userInfoDTO;
    }

    private UserDomainRspDTO getUserInfo(GetUserInfoReqDTO getUserInfoReqDTO) {
        BaseResult<UserDomainRspDTO> baseResult = userDomainFeign.getUserInfo(getUserInfoReqDTO);
        if (!baseResult.isSuccess()) {
            log.error("获取用户信息失败");
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        }

        UserDomainRspDTO rspDTO = baseResult.getData();
        RequestContextHolder.setUserInfo(rspDTO.getUserDomainId(), rspDTO.getPhoneNumber(), rspDTO.getBelongsPlatform());

        return rspDTO;
    }

    public List<UserInfoDTO> getUserAvatarBatch(List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)){
            return Collections.emptyList();
        }

        BaseResult<List<UserInfoResp>> baseResult = userDomainFeign.getUserAvatarBatch(
                userIds.stream().map(UserInfoReq::new).collect(Collectors.toList())
        );

        if (!baseResult.isSuccess()) {
            log.error("【用户域外部调用】【批量查询用户头像信息】【请求异常，result：{}】", baseResult);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        }

        return CollUtil.isEmpty(baseResult.getData()) ? Collections.emptyList() : userExternalAssembler.toUserInfoDTO(baseResult.getData());
    }

    @Override
	public String querySpecToken(String token) {
		UserTokenReq req = new UserTokenReq();
		req.setToSourceId(config.getToSourceId());
		BaseResult<UserTokenResp> result = userDomainFeign.querySpecToken(token, req);
		if (!result.isSuccess()) {
			log.error("【用户域外部调用】【查询token异常】【请求异常，result:{}】", JSONUtil.toJsonStr(result));
			throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
		}
		UserTokenResp data = result.getData();
		if (!SUCCESS_CODE.equals(data.getResultCode())) {
			log.error("【用户域外部调用】【查询token异常】【请求异常，result:{}】", JSONUtil.toJsonStr(result));
			throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
		}
		return data.getToken();
	}

	@Override
	public String loginByStToken(String stToken, String sourceId) {
		UserThirdLoginReq req = new UserThirdLoginReq(null, config.getThirdLoginCpid(),
				config.getThirdLoginClientType(), config.getThirdLoginVersion(), config.getThirdLoginPinType(), stToken,
				new ExtInfo(config.getThirdLoginIfOpenAccount()));
		// 请求头
		Map<String, String> headers = null;
		JSONObject result = null;
		String out = null;
		String reqJson = null;
		try {
			reqJson = JSONUtil.toJsonStr(req);
			out = CommonOkHttpProxy.doPostJson(config.getThirdLoginUrl(), reqJson, headers);
			if (StringUtils.isNotEmpty(out)) {
				result = JSONUtil.parseObj(out);
			}
		} catch (Exception e) {
			log.error("thirdLoginUrl doPostJson error:", config.getThirdLoginUrl(), e);
		} finally {
			log.info("req:{}, out:{}", reqJson, out);
		}
		if (null == result || (null != result && !result.getBool(KEY_OF_SUCCESS))) {
			log.error("【用户域thirdlogin外部调用】【查询token异常】【请求异常，sourceId:{}, result:{}】", sourceId,
					JSONUtil.toJsonStr(result));
			if (null != result && StringUtils.isNotEmpty(result.getStr(KEY_OF_CODE))) {
				throw new YunAiBusinessException(result.getStr(KEY_OF_CODE), result.getStr(KEY_OF_MESSAGE));
			} else {
				throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
			}
		}
		if (StringUtils.isEmpty(result.getStr(KEY_OF_DATA))) {
			return null;
		}
		return new String(config.getThirdLoginAes().decrypt(result.getStr(KEY_OF_DATA)));
	}
}