package com.zyhl.yun.api.outer.external.service.impl;

import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.client.blian.ExternalBlianClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.qwen.ExternalQwenClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.vo.TextModelBaseVo;
import com.zyhl.hcy.yun.ai.common.model.api.client.volc.ExternalVolcClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.xfyun.ExternalXfyunClient;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelConfigDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum;
import com.zyhl.yun.api.outer.domain.req.LlmChatReqDTO;
import com.zyhl.yun.api.outer.domain.valueobject.llm.LlmChatInputConfig;
import com.zyhl.yun.api.outer.domain.valueobject.llm.LlmChatMessage;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.external.LlmChatExternalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 大模型调用
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LlmChatExternalServiceImpl implements LlmChatExternalService {

    @Resource
    private ExternalBlianClient externalBlianClient;
    @Resource
    private ExternalQwenClient externalQwenClient;
    @Resource
    private ExternalXfyunClient externalXfyunClient;
    @Resource
    private ExternalVolcClient externalVolcClient;

    @Override
    public TextModelBaseVo chatNormal(LlmChatReqDTO reqDto) {
        TextModelBaseVo vo = null;
        String model = null;
        try {
            TextModelTextReqDTO textReqDTO = getTextModelReqDTO(reqDto);
            model = reqDto.getModel();
            if (TextModelEnum.isBlian(model)) {
                vo = externalBlianClient.completions(textReqDTO);
            } else if (TextModelEnum.isBlianModel72BCalc(model) || TextModelEnum.isBlianModel72BInstruct(model) || TextModelEnum.isBlianQWen3(model)) {
                // 其他百炼模型
                textReqDTO.setModelValue(model);
                vo = externalBlianClient.completions(textReqDTO);
            } else if (TextModelEnum.isQwen(model)) {
                vo = externalQwenClient.completions(textReqDTO);
            } else if (TextModelEnum.isQwenOthers(model)) {
                // 千问其他模型调用大模型
                TextModelEnum qwenOthersModel = TextModelEnum.getByCode(model);
                if (null == qwenOthersModel) {
                    log.error("chatNormal qwen model not exist model:{}, reqDto:{}", model, JsonUtil.toJson(reqDto));
                    throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
                }
                textReqDTO.setModelValue(qwenOthersModel.getCode());
                vo = externalQwenClient.completions(textReqDTO);
            } else if (TextModelEnum.isXfyun(model)) {
                vo = externalXfyunClient.completions(textReqDTO);
            } else if (TextModelEnum.isHuoshan(model)) {
                vo = externalVolcClient.completions(textReqDTO);
            } else {
                log.error("chatNormal model not exist model:{}, reqDto:{}", model, JsonUtil.toJson(reqDto));
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }
            return vo;
        } catch (Exception e) {
            log.error("chatNormal model reqDto:{} error:", JsonUtil.toJson(reqDto), e);
            if (e instanceof YunAiBusinessException) {
                throw e;
            }
            // 默认下游服务异常错误码
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        } finally {
            log.info("chatNormal finally model:{}, reqDto:{} result:{}", model, JsonUtil.toJson(reqDto),
                    JsonUtil.toJson(vo));
        }
    }

    private TextModelTextReqDTO getTextModelReqDTO(LlmChatReqDTO reqDto) {
        if (null == reqDto.getMessages()) {
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
        }
        TextModelTextReqDTO textReqDTO = new TextModelTextReqDTO();
        // TODO 这里是否需要 设置是否强制联网搜索，需要，则接口需要增加入参
        LlmChatInputConfig modelConfig = reqDto.getModelConfig();
        if (null != modelConfig) {
            // 模型输入配置参数
            TextModelConfigDTO textModelConfig = new TextModelConfigDTO(modelConfig.getMaxTokens(),
                    modelConfig.getTemperature(), modelConfig.getSeed(), modelConfig.getTopP(), modelConfig.getN());
            textReqDTO.setTextModelConfig(textModelConfig);
        }
        textReqDTO.setUserId(reqDto.getUserId());
        textReqDTO.setTaskId(reqDto.getUserId() + System.currentTimeMillis());
        List<TextModelMessageDTO> messages = new ArrayList<>();
        // 组装参数
        for (LlmChatMessage msg : reqDto.getMessages()) {
            new TextModelMessageDTO();
            TextModelMessageDTO textModelMessage = new TextModelMessageDTO();
            textModelMessage.setRole(msg.getRole());
            textModelMessage.setContent(msg.getContent());
            messages.add(textModelMessage);
        }
        textReqDTO.setMessageDtoList(messages);
        textReqDTO.setEnableNetworkSearch(reqDto.getEnableNetworkSearch());
        textReqDTO.setDisabledReasoningFlag(reqDto.isDisabledReasoningFlag());
        textReqDTO.setSearchOption(reqDto.getSearchOption());
        return textReqDTO;
    }

}
