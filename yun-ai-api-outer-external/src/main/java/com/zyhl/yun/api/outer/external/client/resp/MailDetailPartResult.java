package com.zyhl.yun.api.outer.external.client.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Classname MailDetailHtmlResult
 * @Description TODO
 * @Date 2024/3/1 16:12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MailDetailPartResult implements Serializable {

    /**
     * 正文在邮件原文中的contentId
     */
    private String id;

    /**
     * 正文类型，缺省值为text/plain
     * ContentType为 "text/plain", "text/html"
     * read的参数控制是否返回
     * 或 mime message info object     或 null (read level之外)                   (ContentType 为"message/rfc822")
     */
    private String contentType;

    /**
     * 正文长度，base64 encode之后的大小
     */
    private Integer contentLength;

    /**
     * 邮件正文内容
     */
    private String content;

    @JsonIgnore
    public MailDetailPartResult(String contentType, String content) {
        this.contentType = contentType;
        this.content = content;
    }
}
