package com.zyhl.yun.api.outer.external.client.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取用户信息
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetUserInfoReqDTO {

    /**
    * 用户id，非必传，与phoneNumber二选
    */
    private Long userDomainId;

    /**
     * 用户账号，非必传，
     * 与userDomainid二选一;
     * 若是使用basic鉴权，可以不传此字段，系统通过token获取手机号。
    */
    private String phoneNumber;

    public GetUserInfoReqDTO(Long userId){
        this.userDomainId = userId;
    }

}
