package com.zyhl.yun.api.outer.external.client.resp.text;

import com.zyhl.yun.api.outer.domain.vo.chat.search.result.NoteApiSearchResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 文本工具Client-响应结果-笔记搜索
 * @Author: WeiJingKun
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntelligentSearchNoteRsp implements Serializable {

    /** 下一页起始资源标识符，最后一页该值为空 */
    private String nextPageCursor;

    /** 记录总数，默认不返回 */
    private Integer totalCount;

    /** 命中关键词的笔记列表 */
    private List<NoteApiSearchResult> noteList;

}
