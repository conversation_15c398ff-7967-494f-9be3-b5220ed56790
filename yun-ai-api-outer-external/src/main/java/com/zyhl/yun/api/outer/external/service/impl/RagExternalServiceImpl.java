package com.zyhl.yun.api.outer.external.service.impl;

import cn.hutool.core.text.StrPool;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.utils.LogCommonUtils;
import com.zyhl.hcy.yun.ai.common.base.utils.StopWatchUtil;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmicrag.req.ExtractionRuleReq;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelTextReqDTO;
import com.zyhl.hcy.yun.ai.common.rag.client.KeywordClient;
import com.zyhl.hcy.yun.ai.common.rag.client.MultiRouteRecallClient;
import com.zyhl.hcy.yun.ai.common.rag.client.RelevancyClient;
import com.zyhl.hcy.yun.ai.common.rag.client.RerankClient;
import com.zyhl.hcy.yun.ai.common.rag.client.RewriteClient;
import com.zyhl.hcy.yun.ai.common.rag.client.VectorClient;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.RecallQueryDTO;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.TextFeatureDTO;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.dialogue.RecallConfig;
import com.zyhl.hcy.yun.ai.common.rag.dto.validate.dialogue.RerankConfig;
import com.zyhl.hcy.yun.ai.common.rag.enums.KnowledgeRerankTypeEnum;
import com.zyhl.hcy.yun.ai.common.rag.enums.RewriteQueryTypeEnum;
import com.zyhl.hcy.yun.ai.common.rag.vo.RecallResultVO;
import com.zyhl.hcy.yun.ai.common.rag.vo.RerankResultVO;
import com.zyhl.hcy.yun.ai.common.rag.vo.RewriteResultVO;
import com.zyhl.yun.api.outer.config.KnowledgeDialogueProperties;
import com.zyhl.yun.api.outer.config.WhiteListProperties;
import com.zyhl.yun.api.outer.config.knowledge.DigitalSummitProperties;
import com.zyhl.yun.api.outer.constants.KnowledgeConstants;
import com.zyhl.yun.api.outer.domain.dto.RecallDTO;
import com.zyhl.yun.api.outer.domain.dto.RerankDTO;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiRegisterEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmBusinessGroupEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmConfigEntity;
import com.zyhl.yun.api.outer.enums.AIModuleEnum;
import com.zyhl.yun.api.outer.enums.BusinessSourceEnum;
import com.zyhl.yun.api.outer.external.RagExternalService;
import com.zyhl.yun.api.outer.repository.AlgorithmBusinessGroupRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmConfigRepository;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 描述：rag外部服务实现
 *
 * <AUTHOR> zhumaoxian  2025/2/17 14:53
 */
@Slf4j
@Service
public class RagExternalServiceImpl implements RagExternalService {

    @Resource
    private RewriteClient rewriteClient;

    @Resource
    private KeywordClient keywordClient;
    @Resource
    private VectorClient vectorClient;
    @Resource
    private MultiRouteRecallClient multiRouteRecallClient;
    @Resource
    private RerankClient rerankClient;
    @Resource
    private RelevancyClient relevancyClient;

    @Resource
    private KnowledgeDialogueProperties knowledgeDialogueProperties;
    @Resource
    private AlgorithmBusinessGroupRepository businessGroupRepository;
    @Resource
    private AlgorithmConfigRepository configRepository;
    @Resource
    private RedisOperateRepository redisOperateRepository;
    @Resource
    private DigitalSummitProperties summitProperties;
    @Resource
    private WhiteListProperties whiteListProperties;

    @Override
    public String questionRewrite(TextModelTextReqDTO reqDTO) {
        return rewriteClient.questionRewrite(reqDTO, knowledgeDialogueProperties.getRewriteConfig());
    }

    @Override
    public RewriteResultVO rewrite(TextModelTextReqDTO reqDTO) {
        reqDTO.setEnableExtraction(true);
        List<ExtractionRuleReq> extractionRules = new ArrayList<>();
        extractionRules.add(ExtractionRuleReq.builder().keyName(KnowledgeConstants.REQUIRE_WORD_NUM_KEY).valueType(KnowledgeConstants.INT_KEY)
                .defaultValue(-1)
                .desc(KnowledgeConstants.REQUIRE_WORD_NUM_DESC).build());
        extractionRules.add(ExtractionRuleReq.builder().keyName(KnowledgeConstants.DURATION_MIN_KEY).valueType(KnowledgeConstants.INT_KEY)
                .defaultValue(-1)
                .desc(KnowledgeConstants.DURATION_MIN_DESC).build());
        reqDTO.setExtractionRules(extractionRules);

        RewriteResultVO resultVO = rewriteClient.rewrite(reqDTO, knowledgeDialogueProperties.getRewriteConfig());
        if (!RewriteQueryTypeEnum.isExist(resultVO.getQueryType())) {
            resultVO.setQueryType(RewriteQueryTypeEnum.TYPE_0.getQueryType());
        }
        if (ObjectUtil.isNotEmpty(whiteListProperties.getKnowledgeSummaryWhiteUser())) {
            if (!whiteListProperties.getKnowledgeSummaryWhiteUser().contains(RequestContextHolder.getPhoneNumber())) {
                log.info("【RAG重要节点日志】【rag问题重写】非白名单用户，重写后的查询类型改为其他");
                resultVO.setQueryType(RewriteQueryTypeEnum.TYPE_0.getQueryType());
            }
        }
        return resultVO;
    }

    @Override
    public List<List<String>> keywordExtract(List<String> textList) {
        return keywordClient.keywordExtract(textList, knowledgeDialogueProperties.getKeywordConfig());
    }

    @Override
    public List<RecallResultVO> noteContentRecall(RecallQueryDTO queryDTO, RecallConfig config) {
        log.info("【笔记正文搜索】【2路召回】入参queryDTO:{} | config:{}", JsonUtil.toJson(queryDTO), JsonUtil.toJson(config));
        return multiRouteRecallClient.recall(queryDTO, config);
    }

    @Override
    public List<BigDecimal> textFeature(String userId, String text, Long dialogueId) {
        log.info("【RAG重要节点日志】【文本向量化】开始");
        // 开启计时
        StopWatch stopWatch = StopWatchUtil.createStarted();
        // 查缓存
        String featureUrl = redisOperateRepository.getFeatureUrl();
        TextFeatureDTO dto = null;
        List<BigDecimal> bigDecimalList = null;
        try {
            if (ObjectUtil.isEmpty(featureUrl)) {
                // 获取算法组编码
                final Integer businessType = BusinessSourceEnum.ASSISTANT.getCode();
                final Integer module = AIModuleEnum.AI_FILE_LIBRARY.getModule();
                AlgorithmAiRegisterEntity redisEntity = redisOperateRepository.getRegisterEntity(userId, businessType, module);

                // 业务算法组配置
                AlgorithmBusinessGroupEntity businessGroup = businessGroupRepository.queryByAlgorithmGroupCode(redisEntity.getAlgorithmGroupCode());
                if (null == businessGroup) {
                    log.warn("【RAG重要节点日志】【文本向量化】业务算法组不存在，算法编码：{}", redisEntity.getAlgorithmGroupCode());
                    return null;
                }

                // 算法id
                if (CharSequenceUtil.isBlank(businessGroup.getAlgorithmIds())) {
                    log.warn("【RAG重要节点日志】【文本向量化】算法id为空");
                    return null;
                }

                // 算法配置
                List<Long> idList = Arrays.stream(businessGroup.getAlgorithmIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
                List<AlgorithmConfigEntity> entityList = configRepository.getByAlgorithmIds(idList);
                if (CollUtil.isEmpty(entityList)) {
                    log.warn("【RAG重要节点日志】【文本向量化】算法配置为空");
                    return null;
                }

                // 向量化地址
                featureUrl = entityList.get(0).getAlgorithmUrl();
                if (CharSequenceUtil.isBlank(featureUrl)) {
                    log.warn("【RAG重要节点日志】【文本向量化】算法接口地址为空");
                    return null;
                }

                // 缓存
                redisOperateRepository.setFeatureUrl(featureUrl);
            }

            // 文本向量化
            dto = new TextFeatureDTO(featureUrl, text, String.valueOf(dialogueId));
            bigDecimalList = vectorClient.textFeature(dto);
            return bigDecimalList;
        } catch (Exception e) {
            log.error("【RAG重要节点日志】【文本向量化】异常：", e);
        } finally {
            log.info("【RAG重要节点日志】【文本向量化】结束，耗时：{}\n 入参：{}",
                    StopWatchUtil.logTime(stopWatch), JsonUtil.toJson(dto));
            StopWatchUtil.clearDuration();

            /** 把列表拆分，按logGroupSize个一组，方便日志打印 */
            LogCommonUtils.printlnListLog("【RAG重要节点日志】【文本向量化】结果", bigDecimalList, 256);
        }
        return null;
    }

    @Override
    public List<BigDecimal> textEmbed(String text, Long dialogueId) {
        TextFeatureDTO dto = new TextFeatureDTO();
        dto.setText(text);
        dto.setFileId(String.valueOf(dialogueId));
        return vectorClient.textEmbed(dto);
    }

    @Override
    public List<RecallResultVO> recall(RecallDTO dto) {
        //标签（取意图MetaDataList）
        List<String> labelList = new ArrayList<>();
        if (Objects.nonNull(dto.getIntentionVO()) && ObjectUtil.isNotEmpty(dto.getIntentionVO().getIntentionInfoList())) {
            DialogueIntentionVO.IntentionInfo intentionInfo = dto.getIntentionVO().getIntentionInfoList().get(0);
            if (ObjectUtil.isNotEmpty(intentionInfo.getEntityList()) && ObjectUtil.isNotEmpty(intentionInfo.getEntityList().get(0).getMetaDataList())) {
                intentionInfo.getEntityList().get(0).getMetaDataList().forEach(item -> labelList.addAll(item.getValue()));
            }
        }

        // 封装参数
        RecallQueryDTO queryDTO = new RecallQueryDTO();
        queryDTO.setText(dto.getText());
        queryDTO.setFeature(dto.getFeature());
        queryDTO.setUserId(dto.getUserId());
        queryDTO.setLabelList(labelList);
        queryDTO.setFileIdList(dto.getFileIdList());
        queryDTO.setKnowledgeGroupList(dto.getKnowledgeGroupList());
        queryDTO.setVersion(dto.getVersion());
        if (CollUtil.isNotEmpty(dto.getTextKeywordList())) {
            //将提取到的关键字列表转换为空格分隔的字符串
            queryDTO.setKeyword(StringUtils.join(dto.getTextKeywordList(), StrPool.C_SPACE));
        }
        // 召回配置
        RecallConfig config = dto.getConfig();
        if (ObjectUtil.isEmpty(dto.getConfig())) {
            if (RewriteQueryTypeEnum.isType0(dto.getQueryType())) {
                config = knowledgeDialogueProperties.getRecallConfig();
            } else {
                config = knowledgeDialogueProperties.getRecallConfigSummary();
            }
        }
        // 公共知识库和VIP专属智能体只要有一个开关打开，就允许召回公共知识库
        config.getCommonBase().setEnabled(dto.getCommonBase().isEnabled() || dto.getVipCommonBase().isEnabled());
        config.getPersonalBase().setEnabled(dto.getPersonalBase().isEnabled());

        // 设置公共知识库的baseId
        String baseId = knowledgeDialogueProperties.getKnowledgeBaseId();
        config.getCommonSplit().setCommonBaseId(StrUtil.emptyToDefault(config.getCommonSplit().getCommonBaseId(), baseId));
        config.getCommonQa().setCommonBaseId(StrUtil.emptyToDefault(config.getCommonQa().getCommonBaseId(), baseId));
        config.getCommonSplitGqa().setCommonBaseId(StrUtil.emptyToDefault(config.getCommonSplitGqa().getCommonBaseId(), baseId));
        config.getCommonGsplit().setCommonBaseId(StrUtil.emptyToDefault(config.getCommonGsplit().getCommonBaseId(), baseId));
        config.getCommonSummary().setCommonBaseId(StrUtil.emptyToDefault(config.getCommonSummary().getCommonBaseId(), baseId));
        config.getCommonText().setCommonBaseId(StrUtil.emptyToDefault(config.getCommonText().getCommonBaseId(), baseId));

        // 多路召回
        queryDTO.setBaseId(baseId);
        log.info("【知识库对话】【RAG重要节点日志】【多路召回】入参queryDTO:{} | config:{}", JsonUtil.toJson(queryDTO), JsonUtil.toJson(config));
        return multiRouteRecallClient.recall(queryDTO, config);
    }

    @Override
    public List<RerankResultVO> rerank(RerankDTO dto) {
        RerankConfig config;
        String rerankType = dto.getRerankType();
        // 个性化重排配置
        if (ObjectUtil.isNotEmpty(dto.getConfig())) {
            config = dto.getConfig();
        } else if (KnowledgeRerankTypeEnum.VECTOR.getType().equals(rerankType)) {
            //【向量】算法重排配置
            if (RewriteQueryTypeEnum.isType0(dto.getQueryType())) {
                config = knowledgeDialogueProperties.getVectorRerankConfig();
            } else {
                config = knowledgeDialogueProperties.getVectorRerankConfigSummary();
            }
        } else if (KnowledgeRerankTypeEnum.TEXT.getType().equals(rerankType)) {
            //【全文】算法重排配置
            if (RewriteQueryTypeEnum.isType0(dto.getQueryType())) {
                config = knowledgeDialogueProperties.getTextRerankConfig();
            } else {
                config = knowledgeDialogueProperties.getTextRerankConfigSummary();
            }
        } else if (KnowledgeRerankTypeEnum.KEYWORD.getType().equals(rerankType)) {
            //【关键字】算法重排配置
            if (RewriteQueryTypeEnum.isType0(dto.getQueryType())) {
                config = knowledgeDialogueProperties.getKeywordRerankConfig();
            } else {
                config = knowledgeDialogueProperties.getKeywordRerankConfigSummary();
            }
        } else {
            //【默认】算法重排配置
            if (RewriteQueryTypeEnum.isType0(dto.getQueryType())) {
                config = knowledgeDialogueProperties.getRerankConfig();
            } else {
                config = knowledgeDialogueProperties.getRerankConfigSummary();
            }
        }
        String rerankTypeDesc = KnowledgeRerankTypeEnum.getDescByType(rerankType);
        log.info("【知识库对话】【RAG重要节点日志】【rag算法重排】【{}】参数配置config:{}", rerankTypeDesc, JsonUtil.toJson(config));
        return rerankClient.rerank(dto.getText(), dto.getRecallList(), config, rerankTypeDesc);
    }

    @Override
    public List<RerankResultVO> digitalSummitRerank(String text, List<RecallResultVO> recallList) {
        RerankConfig rerankConfig = knowledgeDialogueProperties.getVectorRerankConfig();

        RerankConfig newRerankConfig = new RerankConfig();
        newRerankConfig.setBatchSize(rerankConfig.getBatchSize());
        newRerankConfig.setMaxLength(rerankConfig.getMaxLength());
        newRerankConfig.setLogGroupSize(rerankConfig.getLogGroupSize());
        newRerankConfig.setTopN(summitProperties.getTopN());
        newRerankConfig.setMinScore(summitProperties.getMinScore());
        newRerankConfig.setTextMaxLength(summitProperties.getTextMaxLength());

        log.info("【知识库对话】【RAG重要节点日志】【数字峰会】【rag算法重排】 配置config:{}", JsonUtil.toJson(newRerankConfig));
        return rerankClient.rerank(text, recallList, newRerankConfig);
    }

    @Override
    public List<RerankResultVO> relevancy(TextModelTextReqDTO reqDTO, List<RerankResultVO> rerankResult) {
        return relevancyClient.relevancy(reqDTO, rerankResult, knowledgeDialogueProperties.getRelevancyConfig());
    }
}
