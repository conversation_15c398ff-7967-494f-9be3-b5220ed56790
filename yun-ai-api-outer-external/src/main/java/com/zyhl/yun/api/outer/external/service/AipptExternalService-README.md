# AiPPT外部服务使用说明

## 概述

`AipptExternalService` 是基于 `yun-ai-common` 包下的 `AipptClient` 封装的外部服务，提供了以下核心功能：

1. **Token缓存机制** - 自动缓存Token到Redis，避免频繁调用获取Token接口
2. **重试机制** - 当API调用失败时，自动清除缓存并重新获取Token后重试
3. **统一异常处理** - 遵循项目统一的异常处理规范
4. **性能日志** - 记录各个环节的耗时，便于性能监控

## 配置说明

### 1. 在配置文件中添加AiPPT相关配置

在 `feign-local.yml` 或 `feign-test.yml` 等配置文件中添加：

```yaml
external:
  ai-model:
    # AiPPT配置
    aippt:
      enabled: true
      apiKey: "your-api-key"
      apiSecret: "your-api-secret"
      baseUrl: "https://co.aippt.cn"
      connectTimeout: 60000
      readTimeout: 60000
      maxRetries: 3
      retryInterval: 1000

# AiPPT外部服务配置
aippt:
  enabled: true
  cache:
    enabled: true
    tokenKeyPrefix: "aippt:token:"
    defaultExpireSeconds: 7200
    expireAdvanceSeconds: 300
  retry:
    enabled: true
    maxAttempts: 1
    intervalMillis: 1000
```

### 2. 配置项说明

#### external.ai-model.aippt (AipptClient配置)
- `enabled`: 是否启用AiPPT客户端
- `apiKey`: API密钥
- `apiSecret`: API密钥
- `baseUrl`: 基础URL
- `connectTimeout`: 连接超时时间（毫秒）
- `readTimeout`: 读取超时时间（毫秒）
- `maxRetries`: 最大重试次数
- `retryInterval`: 重试间隔时间（毫秒）

#### aippt (AipptExternalService配置)
- `enabled`: 是否启用外部服务
- `cache.enabled`: 是否启用Token缓存
- `cache.tokenKeyPrefix`: Token缓存Key前缀
- `cache.defaultExpireSeconds`: 默认Token过期时间（秒）
- `cache.expireAdvanceSeconds`: Token过期时间提前量（秒）
- `retry.enabled`: 是否启用重试机制
- `retry.maxAttempts`: 最大重试次数
- `retry.intervalMillis`: 重试间隔时间（毫秒）

## 使用方法

### 1. 注入服务

```java
@Service
public class YourService {
    
    @Resource
    private AipptExternalService aipptExternalService;
    
    // 你的业务方法
}
```

### 2. 获取Token（带缓存）

```java
public void getToken() {
    String uid = "user-123";
    String channel = "web";

    AipptTokenResponseVO tokenResponse = aipptExternalService.getTokenWithCache(uid, channel);
    if (tokenResponse.getCode() == 0) {
        String token = tokenResponse.getData().getToken();
        Long timeExpire = tokenResponse.getData().getTimeExpire();
        // 使用token进行后续操作
    }
}
```

### 3. 获取Code

```java
public void getCode() {
    String uid = "user-123";
    String channel = "web";
    String type = "1"; // 不传则为PC，2则为移动端

    AipptCodeResponseVO codeResponse = aipptExternalService.getCode(uid, channel, type);
    if (codeResponse.getCode() == 0) {
        String code = codeResponse.getData().getCode();
        // 使用code进行页面跳转等操作
    }
}
```

### 4. 作品导出（带重试）

```java
public void exportFile() {
    String uid = "user-123";
    String channel = "web";
    
    AipptExportRequestDTO exportRequest = AipptExportRequestDTO.builder()
        .id(123456L)
        .format("ppt")
        .edit("true")
        .filesToZip("false")
        .build();
    
    AipptExportResponseVO exportResponse = aipptExternalService.exportFileWithRetry(exportRequest, uid, channel);
    if (exportResponse.getCode() == 0) {
        String taskKey = exportResponse.getData();
        // 使用taskKey查询导出状态
    }
}
```

### 5. 查询导出状态（带重试）

```java
public void queryExportStatus() {
    String uid = "user-123";
    String channel = "web";
    String taskKey = "task-key-from-export";
    
    AipptExportResultResponseVO statusResponse = aipptExternalService.queryExportStatusWithRetry(taskKey, uid, channel);
    if (statusResponse.getCode() == 0) {
        List<String> downloadUrls = statusResponse.getData();
        // 处理下载链接
    }
}
```

### 6. 查询作品详情（带重试）

```java
public void getDesignInfo() {
    String uid = "user-123";
    String channel = "web";
    
    AipptDesignInfoRequestDTO designInfoRequest = AipptDesignInfoRequestDTO.builder()
        .userDesignId(123456L)
        .build();
    
    AipptDesignInfoResponseVO designInfoResponse = aipptExternalService.getDesignInfoWithRetry(designInfoRequest, uid, channel);
    if (designInfoResponse.getCode() == 0) {
        // 处理作品详情
        String name = designInfoResponse.getData().getName();
        String coverUrl = designInfoResponse.getData().getCoverUrl();
    }
}
```

### 7. 清除Token缓存

```java
public void clearCache() {
    String uid = "user-123";
    String channel = "web";
    
    aipptExternalService.clearTokenCache(uid, channel);
}
```

## 核心特性

### 1. Token缓存机制

- 自动将Token缓存到Redis，避免频繁调用获取Token接口
- 缓存Key格式：`aippt:token:{uid}:{channel}`
- 支持自定义过期时间，默认使用API返回的`timeExpire`
- 支持提前刷新机制，避免Token在使用时过期

### 2. 重试机制

- 当`exportFile`、`queryExportStatus`、`getDesignInfo`方法调用失败时自动重试
- 重试前会清除Redis中的Token缓存
- 重新获取Token并缓存后重新发起请求
- 支持配置重试次数和重试间隔

### 3. 异常处理

- 统一使用`YunAiBusinessException`处理异常
- 详细的日志记录，包含请求参数和异常信息
- 性能日志统计，记录各个环节的耗时

### 4. 配置灵活性

- 支持开关控制缓存和重试功能
- 支持自定义缓存Key前缀和过期时间
- 支持自定义重试次数和间隔时间

## 注意事项

1. 确保Redis服务正常运行，否则缓存功能将失效
2. 合理配置Token过期时间，避免频繁刷新
3. 重试机制最多重试一次，避免无限重试
4. 生产环境请替换配置文件中的测试API密钥

## 依赖关系

- `yun-ai-common-model-api` - AipptClient
- `yun-ai-common-base` - RedisRepository、YunAiBusinessException
- Spring Boot Configuration Properties
- Lombok

## 版本信息

- 创建时间：2025/1/25
- 作者：AI Assistant
- Java版本：Java 8
- 架构模式：DDD（领域驱动设计）
