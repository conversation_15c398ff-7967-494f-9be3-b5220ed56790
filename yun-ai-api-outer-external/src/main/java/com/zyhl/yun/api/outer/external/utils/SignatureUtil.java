package com.zyhl.yun.api.outer.external.utils;

import org.apache.commons.lang3.RandomStringUtils;

import java.security.MessageDigest;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;

/**
 * @ClassName: SignatureUtil
 * @Description:
 * @Author: yangs
 * @Date: 2023/6/15 11:28:22
 */
public class SignatureUtil {

    /**
     * 创建SHA-256签名
     *
     * @param params params
     * @return SHA-256签名
     */
    private static String createSignature(SortedMap<String, String> params) {
        return sha1Encrypt(sortParams(params));
    }

    /**
     * 创建SHA-256签名
     */
    public static String createSignature(String channelId, String channelKey, String msgId, String timeStamp, String version) {
        SortedMap<String, String> signParams = new TreeMap<String, String>();
        signParams.put("channelKey", channelKey);
        signParams.put("timeStamp", timeStamp);
        signParams.put("version", version);
        signParams.put("channelId", channelId);
        signParams.put("msgId", msgId);
        return createSignature(signParams);
    }

    /**
     * 使用SHA-256算法对字符串进行加密
     *
     * @param str str
     * @return res
     */
    private static String sha1Encrypt(String str) {

        if (str == null || str.length() == 0) {
            return null;
        }

        char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                'a', 'b', 'c', 'd', 'e', 'f'};

        try {

            MessageDigest mdTemp = MessageDigest.getInstance("SHA-256");
            mdTemp.update(str.getBytes("UTF-8"));

            byte[] md = mdTemp.digest();
            int j = md.length;
            char[] buf = new char[j * 2];
            int k = 0;

            for (int i = 0; i < j; i++) {
                byte byte0 = md[i];
                buf[k++] = hexDigits[byte0 >>> 4 & 0xf];
                buf[k++] = hexDigits[byte0 & 0xf];
            }

            return new String(buf);

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 生成时间戳
     *
     * @return res
     */
    public static String getTimeStamp() {
        return String.valueOf(System.currentTimeMillis());
    }

    /**
     * 生成随机字符串
     *
     * @return res
     */
    public static String getRandomStr() {
        return RandomStringUtils.randomAlphanumeric(6);
    }

    /**
     * 根据参数名称对参数进行字典排序
     *
     * @param params params
     * @return return
     */
    private static String sortParams(SortedMap<String, String> params) {
        StringBuilder sb = new StringBuilder();
        Set<Map.Entry<String, String>> es = params.entrySet();
        Iterator<Map.Entry<String, String>> it = es.iterator();
        while (it.hasNext()) {
            Map.Entry<String, String> entry = it.next();
            String k = entry.getKey();
            String v = entry.getValue();
            sb.append(k + "=" + v + "&");
        }
        return sb.substring(0, sb.lastIndexOf("&"));
    }
}
