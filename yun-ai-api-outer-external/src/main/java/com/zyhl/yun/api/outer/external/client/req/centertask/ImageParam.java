package com.zyhl.yun.api.outer.external.client.req.centertask;

import java.io.Serializable;

import lombok.Data;

/**
 * 创建ai算法任务-图片类-请求参数
 *
 * @Author: WeiJingKun
 */
@Data
public class ImageParam implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    /**
     * userId_对话id 用于查询hbase数据
     */
    private String rowkey;

    /**
     * 文件fileId fileId、localPath、eosObjectKey三选一
     */
    private String fileId;

    /**
     * 文件存储路径 fileId、localPath、eosObjectKey三选一
     */
    private String localPath;

    /**
     * 文件存储路径 fileId、localPath、eosObjectKey三选一
     */
    private String eosObjectKey;

    /**
     * 图片扩展名，例如：jpg、png等
     */
    private String imageExt;

    /**
     * 风格类型
     */
    private String style;

    /**
     * 图片存储类型枚举
     *
     * @see com.zyhl.yun.api.outer.enums.ImageTransmissionTypeEnum
     */
    private Integer imageTransmissionType;

    /**
     * 工具是否付费false不需要付费；true需要付费，不传默认不需要付费
     */
    private Boolean toolPay;

    /**
     * 个人云保存路径。例如：/我的应用收藏/AI助手
     */
    private String yunPath;

    /**
     * 非必须	扩展字段，根据接口需要自行处理
     */
    private String extendField;
}
