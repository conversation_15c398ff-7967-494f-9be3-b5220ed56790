package com.zyhl.yun.api.outer.external.ose.common;

/**
 * ose常量
 * @Author: <PERSON><PERSON><PERSON><PERSON>_<PERSON>
 */
public class OseConstant {

    public static final String AUTHORIZATION = "Authorization";

    public static final String JWT_TOKEN = "jwttoken";

    public static final String X_YUN_APP_AUTHORIZATION = "x-yun-app-authorization";

    public static final String X_YUN_APP_CHANNEL = "x-yun-app-channel";

    public static final String X_YUN_API_VERSION = "x-yun-api-version";

    public static final String X_YUN_CLIENT_INFO = "x-yun-client-info";

    /**
     * 成功
     */
    public static final String RESULT_CODE_0 = "0";

    /**
     * 我的应用收藏 文件夹ID
     */
    public static final String MY_APPLICATION_COLLECTION_CATALOG_ID = "00019700101000000071";

    /**
     * 空间不足
     */
    public static final String SPACE_NOT_ENOUGH = "9424";

}
