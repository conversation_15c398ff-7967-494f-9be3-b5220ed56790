package com.zyhl.yun.api.outer.external.client.interceptor;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <p>
 * 同步盘saasFeign配置类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-04
 */
@Configuration
@ConfigurationProperties(prefix = "hcy.feignclient.sync-disk")
@Data
public class SyncDiskConfig {

    private String appKey;

    private String appSecretId;

    private String appSecret;

    /**
     * 模块名称
     */
    private String modName;

    private String defaultUrl;
}
