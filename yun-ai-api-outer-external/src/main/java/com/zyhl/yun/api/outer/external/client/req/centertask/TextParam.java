package com.zyhl.yun.api.outer.external.client.req.centertask;

import java.io.Serializable;

import lombok.Data;

/**
 * 创建ai算法任务-文本类-请求参数
 *
 * @Author: WeiJingKun
 */
@Data
public class TextParam implements Serializable {

    private static final long serialVersionUID = -7771681230707792555L;
    /**
     * userId_对话id 用于查询hbase数据
     */
    private String rowkey;

    /**
     * 文件fileId
     */
    private String fileId;

    /**
     * 宽度，默认512
     */
    private Integer width = 512;

    /**
     * 高度，默认512
     */
    private Integer height = 512;

    /**
     * 风格类型
     */
    private String style;

    /**
     * 存储类型
     *
     * @see com.zyhl.yun.api.outer.enums.ImageTransmissionTypeEnum
     */
    private Integer imageTransmissionType;

    /**
     * 扩展参数
     */
    private String extendField;
}
