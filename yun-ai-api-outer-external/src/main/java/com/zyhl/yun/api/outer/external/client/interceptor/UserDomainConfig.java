package com.zyhl.yun.api.outer.external.client.interceptor;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;

import java.util.List;


/**
 * 用户域feign配置类
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(
        prefix = "user"
)
public class UserDomainConfig {

	private static AES aes = null;

    /**
     * 应用密钥。
     * 该字段存储应用的密钥。
     */
    private String appKey;

    /**
     * 应用密钥ID。
     * 该字段存储应用密钥的唯一标识符。
     */
    private String appSecretId;

    /**
     * 应用密钥。
     * 该字段存储应用的密钥。
     */
    private String appSecret;

    /**
     * 错误码列表。
     * 该字段存储应用可能返回的错误码列表。
     */
    private List<String> errorCode;
    
    /**
     * 使用统一认证的sourceId
     */
    private String toSourceId = "001005";

    /**
     * 三方登录接口url
     */
    private String thirdLoginUrl;
    
    /**
     * 三方登录使用的aes 秘钥
     */
    private String thirdLoginAesKey;
    /**
     * 三方登录使用的版本号
     */
    private String thirdLoginVersion = "2309600";
  
    /**
     * 三方登录使用的cpid
     */
    private String thirdLoginCpid = "58";
    
    /**
     * 三方登录使用的客户端类型
     */
    private String thirdLoginClientType = "414";
    
    
    /**
     * 三方登录使用的pinType
     */
    private String thirdLoginPinType = "13";
    
    /**
     * 是否开户，1是
     */
    private String thirdLoginIfOpenAccount = "1";
    

    public String getAppKey() {
        return appKey;
    }

    public String getAppSecretId() {
        return appSecretId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public void setAppSecretId(String appSecretId) {
        this.appSecretId = appSecretId;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public List<String> getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(List<String> errorCode) {
        this.errorCode = errorCode;
    }

	public String getToSourceId() {
		return toSourceId;
	}

	public void setToSourceId(String toSourceId) {
		this.toSourceId = toSourceId;
	}

	public String getThirdLoginUrl() {
		return thirdLoginUrl;
	}

	public void setThirdLoginUrl(String thirdLoginUrl) {
		this.thirdLoginUrl = thirdLoginUrl;
	}

	public String getThirdLoginAesKey() {
		return thirdLoginAesKey;
	}

	public void setThirdLoginAesKey(String thirdLoginAesKey) {
		this.thirdLoginAesKey = thirdLoginAesKey;
	}

	public String getThirdLoginVersion() {
		return thirdLoginVersion;
	}

	public void setThirdLoginVersion(String thirdLoginVersion) {
		this.thirdLoginVersion = thirdLoginVersion;
	}

	public String getThirdLoginCpid() {
		return thirdLoginCpid;
	}

	public void setThirdLoginCpid(String thirdLoginCpid) {
		this.thirdLoginCpid = thirdLoginCpid;
	}

	public String getThirdLoginClientType() {
		return thirdLoginClientType;
	}

	public void setThirdLoginClientType(String thirdLoginClientType) {
		this.thirdLoginClientType = thirdLoginClientType;
	}

	public String getThirdLoginPinType() {
		return thirdLoginPinType;
	}

	public void setThirdLoginPinType(String thirdLoginPinType) {
		this.thirdLoginPinType = thirdLoginPinType;
	}

	public String getThirdLoginIfOpenAccount() {
		return thirdLoginIfOpenAccount;
	}

	public void setThirdLoginIfOpenAccount(String thirdLoginIfOpenAccount) {
		this.thirdLoginIfOpenAccount = thirdLoginIfOpenAccount;
	}
	
	/**
	 * 获取三方登录aes对象
	 * 
	 * @return aes单例对象
	 */
	public AES getThirdLoginAes() {
		if (null == aes) {
			aes = SecureUtil.aes(this.getThirdLoginAesKey().getBytes());
		}
		return aes;
	}
}
