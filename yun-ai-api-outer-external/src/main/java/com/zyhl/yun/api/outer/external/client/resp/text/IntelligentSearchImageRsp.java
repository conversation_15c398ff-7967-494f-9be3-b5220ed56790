package com.zyhl.yun.api.outer.external.client.resp.text;


import com.zyhl.yun.api.outer.domain.valueobject.File;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 文本工具Client-响应结果-语义搜图
 * @Author: WeiJingKun
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntelligentSearchImageRsp implements Serializable {

    /** 文件信息 */
    private List<File> fileList;

    /** 下一页起始资源标识符，最后一页该值为空 */
    private String nextPageCursor;

    /** 记录总数，默认不返回 */
    private Integer totalCount;
    
    /**
     * 命中的用户人名
     */
    private List<String> hitPeopleNameList;

    /**
     * 命中的关系名称，如果命中的是原词，value为空，如果命中的是扩词，把原词和扩词都返回
     */
    private Map<String,List<String>> hitRelationshipNameMap;

}
