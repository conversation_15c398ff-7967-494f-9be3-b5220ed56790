package com.zyhl.yun.api.outer.external.client.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Classname MailDetailResponse
 * @Description 返回结果
 * @Date 2024/3/1 10:19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MailDetailResponse implements Serializable {

    /**
     * 邮件id
     */
    private String omid;

    /**
     * 邮件主题
     */
    private String subject;

    /**
     * 发件人地址数组
     */
    private String account;

    /**
     * 收信人，unicode 名称/地址
     * string类型，多个用,分隔
     */
    private String to;

    /**
     * 抄送人，unicode 名称/地址
     * string类型，多个用,分隔
     */
    private String cc;

    /**
     * 1：html邮件
     * 0：文本邮件
     */
    private Integer isHtml;

    /**
     * 邮件正文text信息对象
     * (part object)
     */
    private MailDetailPartResult text;

    /**
     * 邮件正文html信息对象
     * (part object)
     */
    private MailDetailPartResult html;

    /**
     * 邮件发送日期
     */
    private String sendDate;

    /**
     * 优先级：
     * 1, 2: 高优先级
     * 3 : 普通优先级
     * 4, 5: 低优先级
     */
    private Integer priority;

}
