package com.zyhl.yun.api.outer.external.service.impl;

import cn.hutool.core.util.IdUtil;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.domain.req.IntelligentSearchQueryEntity;
import com.zyhl.yun.api.outer.domain.req.SearchEntity;
import com.zyhl.yun.api.outer.domain.resp.IntelligentSearchRespEntity;
import com.zyhl.yun.api.outer.domainservice.IntelligentSearchService;
import com.zyhl.yun.api.outer.external.strategy.IntelligentSearchStrategy;
import com.zyhl.yun.api.outer.external.strategy.IntelligentSearchStrategySelector;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.zyhl.yun.api.outer.enums.ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION;


/**
 * 调用 智能搜索接口
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class IntelligentSearchServiceImpl implements IntelligentSearchService {

    @Resource
    private IntelligentSearchStrategySelector selector;


    /**
     * 调用 智能查询 接口
     *
     * @param entity
     * @return
     */
    @Override
    public BaseResult<IntelligentSearchRespEntity> intelligentSearch(SearchEntity entity) {

        IntelligentSearchStrategy intelligentSearchStrategy = selector.strategySelection(entity.getConditions().getAlgorithmType());
        String userId = RequestContextHolder.getUserId();
        IntelligentSearchQueryEntity query = new IntelligentSearchQueryEntity();
        query.setUserId(userId);
        query.setQuery(entity.getConditions().getKeyword());
        query.setRequestId(IdUtil.simpleUUID());
        try {
            return intelligentSearchStrategy.intelligentSearch(query);
        } catch (Exception e) {
            log.error("算法测智能查询调用异常, userId: {}, exception: {}", userId, e);
            throw new YunAiBusinessException(DOWNSTREAM_SERVICES_EXCEPTION);
        }
    }
}
