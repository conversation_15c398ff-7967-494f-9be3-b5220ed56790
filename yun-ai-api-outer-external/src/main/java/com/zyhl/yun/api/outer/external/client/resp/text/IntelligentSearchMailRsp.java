package com.zyhl.yun.api.outer.external.client.resp.text;

import com.zyhl.yun.api.outer.domain.vo.chat.search.result.SearchMail;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 文本工具Client-响应结果-邮件搜索
 * @Author: WeiJingKun
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntelligentSearchMailRsp implements Serializable {

    /** 邮件信息列表 */
    private List<SearchMail> searchMailList;

    /** 卡片标题 */
    private String title;

    /** 消息提示 */
    private String tips;

    /** 记录总数 */
    private Integer totalCount;

}
