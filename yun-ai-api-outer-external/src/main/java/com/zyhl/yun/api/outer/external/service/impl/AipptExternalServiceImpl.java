package com.zyhl.yun.api.outer.external.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.redis.RedisRepository;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.AipptClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.dto.AipptDesignInfoRequestDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.dto.AipptExportRequestDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.vo.AipptCodeResponseVO;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.vo.AipptDesignInfoResponseVO;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.vo.AipptExportResponseVO;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.vo.AipptExportResultResponseVO;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.vo.AipptTokenResponseVO;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.external.config.AipptExternalProperties;
import com.zyhl.yun.api.outer.external.service.AipptExternalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * AiPPT外部服务实现类
 * 提供带缓存和重试机制的AiPPT API调用
 *
 * <AUTHOR> Assistant
 * @date 2025/1/25
 */
@Slf4j
@Service
public class AipptExternalServiceImpl implements AipptExternalService {

    @Resource
    private AipptClient aipptClient;

    @Resource
    private RedisRepository redisRepository;

    @Resource
    private AipptExternalProperties aipptExternalProperties;

    @Override
    public AipptTokenResponseVO getTokenWithCache(String uid, String channel) {
        long startTime = System.currentTimeMillis();
        log.info("【AiPPT外部服务】【获取Token】开始执行，uid: {}, channel: {}", uid, channel);

        try {
            // 参数校验
            if (StrUtil.isBlank(uid)) {
                log.error("【AiPPT外部服务】【获取Token】uid不能为空");
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }

            // 构建缓存Key
            String cacheKey = buildTokenCacheKey(uid, channel);
            
            // 尝试从缓存获取Token
            AipptTokenResponseVO cachedToken = redisRepository.getCacheObject(cacheKey);
            if (ObjectUtil.isNotNull(cachedToken) && isTokenValid(cachedToken)) {
                log.info("【AiPPT外部服务】【获取Token】从缓存获取成功，uid: {}, channel: {}", uid, channel);
                return cachedToken;
            }

            // 缓存中没有或已过期，调用API获取新Token
            AipptTokenResponseVO tokenResponse = aipptClient.getToken(uid, channel);
            
            // 缓存Token
            cacheToken(cacheKey, tokenResponse);
            
            log.info("【AiPPT外部服务】【获取Token】API调用成功，uid: {}, channel: {}", uid, channel);
            return tokenResponse;

        } catch (Exception e) {
            log.error("【AiPPT外部服务】【获取Token】处理异常，uid: {}, channel: {}", uid, channel, e);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        } finally {
            long costTime = System.currentTimeMillis() - startTime;
            log.info("【AiPPT外部服务】【获取Token】执行完毕，总耗时: {}ms", costTime);
        }
    }

    @Override
    public AipptCodeResponseVO getCode(String uid, String channel, String type) {
        long startTime = System.currentTimeMillis();
        log.info("【AiPPT外部服务】【获取Code】开始执行，uid: {}, channel: {}, type: {}", uid, channel, type);

        try {
            // 参数校验
            if (StrUtil.isBlank(uid)) {
                log.error("【AiPPT外部服务】【获取Code】uid不能为空");
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_PARAMS);
            }

            // 直接调用API获取Code（Code通常不需要缓存，因为它是用于跳转的临时凭证）
            AipptCodeResponseVO codeResponse = aipptClient.getCode(uid, channel, type);

            log.info("【AiPPT外部服务】【获取Code】API调用成功，uid: {}, channel: {}, type: {}", uid, channel, type);
            return codeResponse;

        } catch (Exception e) {
            log.error("【AiPPT外部服务】【获取Code】处理异常，uid: {}, channel: {}, type: {}", uid, channel, type, e);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        } finally {
            long costTime = System.currentTimeMillis() - startTime;
            log.info("【AiPPT外部服务】【获取Code】执行完毕，总耗时: {}ms", costTime);
        }
    }

    @Override
    public AipptExportResponseVO exportFileWithRetry(AipptExportRequestDTO exportRequest, String uid, String channel) {
        long startTime = System.currentTimeMillis();
        log.info("【AiPPT外部服务】【作品导出】开始执行，uid: {}, channel: {}, id: {}", 
                uid, channel, exportRequest.getId());

        try {
            return executeWithRetry(() -> {
                // 获取Token
                AipptTokenResponseVO tokenResponse = getTokenWithCache(uid, channel);
                String token = tokenResponse.getData().getToken();
                
                // 调用导出API
                return aipptClient.exportFile(exportRequest, token);
            }, uid, channel, "作品导出");

        } finally {
            long costTime = System.currentTimeMillis() - startTime;
            log.info("【AiPPT外部服务】【作品导出】执行完毕，总耗时: {}ms", costTime);
        }
    }

    @Override
    public AipptExportResultResponseVO queryExportStatusWithRetry(String taskKey, String uid, String channel) {
        long startTime = System.currentTimeMillis();
        log.info("【AiPPT外部服务】【查询导出状态】开始执行，uid: {}, channel: {}, taskKey: {}", 
                uid, channel, taskKey);

        try {
            return executeWithRetry(() -> {
                // 获取Token
                AipptTokenResponseVO tokenResponse = getTokenWithCache(uid, channel);
                String token = tokenResponse.getData().getToken();
                
                // 调用查询状态API
                return aipptClient.queryExportStatus(taskKey, token);
            }, uid, channel, "查询导出状态");

        } finally {
            long costTime = System.currentTimeMillis() - startTime;
            log.info("【AiPPT外部服务】【查询导出状态】执行完毕，总耗时: {}ms", costTime);
        }
    }

    @Override
    public AipptDesignInfoResponseVO getDesignInfoWithRetry(AipptDesignInfoRequestDTO designInfoRequest, String uid, String channel) {
        long startTime = System.currentTimeMillis();
        log.info("【AiPPT外部服务】【查询作品详情】开始执行，uid: {}, channel: {}, userDesignId: {}", 
                uid, channel, designInfoRequest.getUserDesignId());

        try {
            return executeWithRetry(() -> {
                // 获取Token
                AipptTokenResponseVO tokenResponse = getTokenWithCache(uid, channel);
                String token = tokenResponse.getData().getToken();
                
                // 调用查询详情API
                return aipptClient.getDesignInfo(designInfoRequest, token);
            }, uid, channel, "查询作品详情");

        } finally {
            long costTime = System.currentTimeMillis() - startTime;
            log.info("【AiPPT外部服务】【查询作品详情】执行完毕，总耗时: {}ms", costTime);
        }
    }

    @Override
    public void clearTokenCache(String uid, String channel) {
        String cacheKey = buildTokenCacheKey(uid, channel);
        redisRepository.deleteObject(cacheKey);
        log.info("【AiPPT外部服务】【清除Token缓存】uid: {}, channel: {}", uid, channel);
    }

    /**
     * 带重试机制的执行方法
     */
    private <T> T executeWithRetry(ApiCallable<T> callable, String uid, String channel, String operation) {
        try {
            // 第一次尝试
            return callable.call();
        } catch (Exception e) {
            log.warn("【AiPPT外部服务】【{}】第一次调用失败，准备重试，uid: {}, channel: {}", operation, uid, channel, e);

            if (!aipptExternalProperties.getRetry().getEnabled()) {
                log.info("【AiPPT外部服务】【{}】重试机制已禁用，直接抛出异常", operation);
                throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
            }

            try {
                // 清除Token缓存
                clearTokenCache(uid, channel);

                // 等待重试间隔
                Thread.sleep(aipptExternalProperties.getRetry().getIntervalMillis());

                // 第二次尝试
                log.info("【AiPPT外部服务】【{}】开始重试，uid: {}, channel: {}", operation, uid, channel);
                return callable.call();

            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
                log.error("【AiPPT外部服务】【{}】重试过程中被中断，uid: {}, channel: {}", operation, uid, channel, ie);
                throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
            } catch (Exception retryException) {
                log.error("【AiPPT外部服务】【{}】重试失败，uid: {}, channel: {}", operation, uid, channel, retryException);
                throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
            }
        }
    }

    /**
     * 构建Token缓存Key
     */
    private String buildTokenCacheKey(String uid, String channel) {
        String channelPart = StrUtil.isBlank(channel) ? "default" : channel;
        return aipptExternalProperties.getCache().getTokenKeyPrefix() + uid + ":" + channelPart;
    }

    /**
     * 检查Token是否有效（未过期）
     */
    private boolean isTokenValid(AipptTokenResponseVO tokenResponse) {
        if (ObjectUtil.isNull(tokenResponse) || ObjectUtil.isNull(tokenResponse.getData())) {
            return false;
        }
        
        // 这里可以添加更复杂的Token有效性检查逻辑
        // 比如检查过期时间等
        return true;
    }

    /**
     * 缓存Token到Redis
     */
    private void cacheToken(String cacheKey, AipptTokenResponseVO tokenResponse) {
        if (!aipptExternalProperties.getCache().getEnabled()) {
            log.info("【AiPPT外部服务】【缓存Token】缓存已禁用，跳过缓存");
            return;
        }

        try {
            Long expireSeconds = aipptExternalProperties.getCache().getDefaultExpireSeconds();
            
            // 如果API返回了过期时间，使用API返回的时间
            if (ObjectUtil.isNotNull(tokenResponse.getData()) && 
                ObjectUtil.isNotNull(tokenResponse.getData().getTimeExpire())) {
                expireSeconds = tokenResponse.getData().getTimeExpire();
                
                // 减去提前量，提前刷新Token
                expireSeconds -= aipptExternalProperties.getCache().getExpireAdvanceSeconds();
                if (expireSeconds <= 0) {
                    expireSeconds = aipptExternalProperties.getCache().getDefaultExpireSeconds();
                }
            }

            redisRepository.setCacheObject(cacheKey, tokenResponse, expireSeconds, TimeUnit.SECONDS);
            log.info("【AiPPT外部服务】【缓存Token】缓存成功，过期时间: {}秒", expireSeconds);
            
        } catch (Exception e) {
            log.error("【AiPPT外部服务】【缓存Token】缓存失败", e);
            // 缓存失败不影响主流程
        }
    }

    /**
     * API调用函数式接口
     */
    @FunctionalInterface
    private interface ApiCallable<T> {
        T call() throws Exception;
    }
}
