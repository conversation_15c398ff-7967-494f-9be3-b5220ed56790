package com.zyhl.yun.api.outer.external.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.enums.PantaLabelEnum;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.CmicTextClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.dto.TextFeatureExtractDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.dto.TextNerExtractDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.entity.KeyValueEntity;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.enums.TaskTypeEnum;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.vo.TextFeatureExtractVO;
import com.zyhl.hcy.yun.ai.common.model.api.client.cmictext.vo.TextNerExtractVO;
import com.zyhl.yun.api.outer.config.AllNetworkSearchProperties;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.external.CmicTextService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 文本特征提取
 * @author: shixiaokang
 */
@Slf4j
@Service
public class CmicTextServiceImpl implements CmicTextService {

    @Resource
    private CmicTextClient cmicTextClient;

    @Resource
    private UidGenerator uidGenerator;

    @Override
    public TextFeatureExtractVO getTextFeature(String text) {
        TextFeatureExtractDTO textFeatureExtractDTO = new TextFeatureExtractDTO();
        textFeatureExtractDTO.setRequestId(String.valueOf(uidGenerator.getUID()));
        textFeatureExtractDTO.setText(text);
        textFeatureExtractDTO.setTaskType(TaskTypeEnum.TEXT.getCode());
        return cmicTextClient.textFeatureExtract(textFeatureExtractDTO);
    }

    @Override
    public TextNerExtractVO textNerExtract(String dialogue, AllNetworkSearchProperties.SearchEntityExtractDTO searchEntityExtract) {
        TextNerExtractDTO dto = new TextNerExtractDTO();
        dto.setText(dialogue);
        dto.setMaxTextLength(searchEntityExtract.getMaxLength());
        dto.setEnableAllEntity(searchEntityExtract.isReturnAll());
        dto.setEntityTypeList(searchEntityExtract.getEntityTypeList());
        dto.setAllEntityExample(searchEntityExtract.getAllEntityExample());
        dto.setTypeEntityExample(searchEntityExtract.getTypeEntityExample());
        dto.setRequestId(MDC.get(LogConstants.TRACE_ID));
        log.info("【AI全网搜】搜索实体抽取接口TextNerExtractDTO入参：{}", dto);
        return cmicTextClient.textNerExtract(dto);
    }

    @Override
    public Map<PantaLabelEnum, List<String>> searchEntityExtract(String logPrefix, TextNerExtractDTO dto) {
        long startTime = System.currentTimeMillis();
        log.info("{}搜索实体抽取接口TextNerExtractDTO入参：{}", logPrefix, JsonUtil.toJson(dto));
        TextNerExtractVO extractVO = cmicTextClient.textNerExtract(dto);
        log.info("{}调用算法搜索实体接口，返回结果：{}", logPrefix, extractVO);
        if (ObjectUtil.isNull(extractVO)) {
            log.error("{}调用算法搜索实体接口返回值为null，流程中断", logPrefix);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        }
        if (CollUtil.isEmpty(extractVO.getResultList())) {
            log.info("{}调用算法搜索实体接口返回的resultList为null，流转到下游处理返回配置提示语", logPrefix);
            return Collections.emptyMap();
        }

        // 实体抽取结果转换
        Map<PantaLabelEnum, List<String>> resultMap = extractVO.getResultList().stream()
                .filter(s -> ObjectUtil.isNotNull(PantaLabelEnum.getLabelByDesc(s.getKey())))
                .collect(Collectors.toMap(s -> PantaLabelEnum.getLabelByDesc(s.getKey()), KeyValueEntity::getValue));
        log.info("{}组装参数，调用算法搜索实体接口，耗时：{}ms，获取结果：{}", logPrefix, System.currentTimeMillis() - startTime, JsonUtil.toJson(resultMap));
        return resultMap;
    }

}
