package com.zyhl.yun.api.outer.external.client.req.centertask;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 创建ai算法任务-client请求参数
 * @Author: WeiJingKun
 */
@Data
public class CenterTaskCreateReq implements Serializable {

    /** 用户id */
    private String userId;

    /** 算法编号，详见 DialogueIntentionEnum */
    private List<String> algorithmCodes;

    /** 厂商编号，详见 SupplierTypeEnum */
    private List<String> supplierTypes;

    /**
     * 详见 BusinessParam
     * 业务参数，透传至中心算法执行任务。
     * json格式，内容字段及值由各个api服务与中心算法商定。
     */
    private String businessParam;

}
