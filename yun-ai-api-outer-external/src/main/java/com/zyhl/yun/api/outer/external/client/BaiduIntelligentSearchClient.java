package com.zyhl.yun.api.outer.external.client;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.domain.req.IntelligentSearchQueryEntity;
import com.zyhl.yun.api.outer.domain.resp.IntelligentSearchRespEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 百度智能搜索
 * <AUTHOR>
 */
@FeignClient(name = "${yun.external.baiduIntelligentSearch.service-name}", url = "${yun.external.baiduIntelligentSearch.url}")
public interface BaiduIntelligentSearchClient {

	/**
	 * 智能搜图
	 *
	 * @param req
	 * @return
	 */
	@PostMapping(value = "/intelligentsearch")
	BaseResult<IntelligentSearchRespEntity> intelligentSearch(@RequestBody IntelligentSearchQueryEntity req);

}
