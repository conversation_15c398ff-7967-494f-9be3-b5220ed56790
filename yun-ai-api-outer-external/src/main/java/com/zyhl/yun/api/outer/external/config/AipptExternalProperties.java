package com.zyhl.yun.api.outer.external.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * AiPPT外部服务配置属性
 *
 * <AUTHOR> Assistant
 * @date 2025/1/25
 */
@Data
@Component
@ConfigurationProperties(prefix = "external.aippt")
public class AipptExternalProperties {

    /**
     * 是否启用AiPPT外部服务
     */
    private Boolean enabled = true;

    /**
     * Redis缓存配置
     */
    private Cache cache = new Cache();

    /**
     * 重试配置
     */
    private Retry retry = new Retry();

    @Data
    public static class Cache {
        /**
         * Token缓存Key前缀
         */
        private String tokenKeyPrefix = "aippt:token:";

        /**
         * 是否启用Token缓存
         */
        private Boolean enabled = true;

        /**
         * 默认Token过期时间（秒），当API返回的timeExpire为空时使用
         */
        private Long defaultExpireSeconds = 7200L;

        /**
         * Token过期时间提前量（秒），提前这个时间刷新token
         */
        private Long expireAdvanceSeconds = 300L;
    }

    @Data
    public static class Retry {
        /**
         * 是否启用重试机制
         */
        private Boolean enabled = true;

        /**
         * 最大重试次数
         */
        private Integer maxAttempts = 1;

        /**
         * 重试间隔时间（毫秒）
         */
        private Long intervalMillis = 1000L;
    }
}
