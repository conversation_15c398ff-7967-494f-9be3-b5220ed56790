package com.zyhl.yun.api.outer.external.service;

/**
 * 云盘接口
 * @author: shixiaokang
 * @date 2024-05-30
 */
public interface EOSExternalService {

    /**
     * 上传EOS
     *
     * @param userId 用户id，用于标识以及日志，没有具体含义
     * @param base64 图片base64
     * @param ext    文件扩展
     * @return fileKey
     */
    String upload(String userId, String base64, String ext);

    /**
     * 上传EOS
     *
     * @param userId 用户id，用于标识以及日志，没有具体含义
     * @param file   文件字节数组
     * @param ext    文件扩展
     * @return fileKey
     */
    String upload(String userId, byte[] file, String ext);

	/**
	 * 上传EOS并返回url
	 *
	 * @param userId     用户id，用于标识以及日志，没有具体含义
	 * @param file       文件字节数组
	 * @param ext        文件扩展
	 * @param expireTime 过期时间
	 * @return url
	 */
    String uploadAndGetUrl(String userId, byte[] file, String ext, long expireTime);

    /**
     * 获取文件地址
     *
     * @param fileKey    文件的key
     * @param fileName   文件名称
     * @param expireTime 过期时间，毫秒
     * @return 文件地址
     */
    String getFileUrl(String fileKey, String fileName, long expireTime);

    /**
     * 下载eos
     * @param downloadPath 下载地址全路径
     * @param eosObjectKey eos key
     * @return
     */
    Boolean downloadEos(String downloadPath, String eosObjectKey);
}
