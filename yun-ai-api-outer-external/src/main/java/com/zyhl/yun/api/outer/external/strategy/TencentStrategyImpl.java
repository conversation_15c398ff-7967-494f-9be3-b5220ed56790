package com.zyhl.yun.api.outer.external.strategy;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.domain.req.IntelligentSearchQueryEntity;
import com.zyhl.yun.api.outer.domain.resp.IntelligentSearchRespEntity;
import com.zyhl.yun.api.outer.external.client.TencentIntelligentSearchClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 策略实现类-腾讯
 * <AUTHOR>
 */
@RefreshScope
@Service
@Slf4j
public class TencentStrategyImpl implements IntelligentSearchStrategy {

    @Resource
    private TencentIntelligentSearchClient searchClient;

    @Value("${intelligentsearch.TencentThreshold:22}")
    private String threshold;


    /**
     * 腾讯 智能查询接口
     *
     * @param query
     * @return
     */
    @Override
    public BaseResult<IntelligentSearchRespEntity> intelligentSearch(IntelligentSearchQueryEntity query) {
        query.setThreshold(Double.parseDouble(threshold));
        return searchClient.intelligentSearch(query);
    }
}
