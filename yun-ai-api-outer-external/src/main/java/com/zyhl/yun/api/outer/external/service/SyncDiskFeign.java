package com.zyhl.yun.api.outer.external.service;


import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.external.client.interceptor.SyncDiskNeauthFeignInterceptor;
import com.zyhl.yun.api.outer.external.client.req.TimelineYmdReqDTO;
import com.zyhl.yun.api.outer.external.client.resp.TimelineYmdChangeRspDTO;
import com.zyhl.yun.api.outer.external.client.resp.TimelineYmdFileInfRspDTO;
import com.zyhl.yun.api.outer.external.client.resp.TimelineYmdStartCursorReqDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * 同步盘Feign
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
@FeignClient(name = "${syncDiskFeign.modName:syncDisk}", configuration = SyncDiskNeauthFeignInterceptor.class)
public interface SyncDiskFeign {

    /**
     * 遍历文件
     *
     * @param reqDTO reqDTO
     * @return BaseResult
     */
    @PostMapping(value = "/fixedPath/walkFile")
    BaseResult<TimelineYmdFileInfRspDTO> walkFile(@RequestBody TimelineYmdReqDTO reqDTO);

    /**
     * 获取增量操作游标
     *
     * @param reqDTO reqDTO
     * @return BaseResult
     */
    @PostMapping(value = "/fixedPath/getStartCursor")
    BaseResult<TimelineYmdStartCursorReqDTO> getStartCursor(@RequestBody TimelineYmdReqDTO reqDTO);

    /**
     * 列举增量信息
     *
     * @param reqDTO reqDTO
     * @return BaseResult
     */
    @PostMapping(value = "/fixedPath/listChange")
    BaseResult<TimelineYmdChangeRspDTO> listChange(@RequestBody TimelineYmdReqDTO reqDTO);
}
