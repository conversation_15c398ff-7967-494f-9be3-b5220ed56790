package com.zyhl.yun.api.outer.external.service.impl;

import com.alibaba.fastjson.JSON;
import com.zyhl.hcy.yun.ai.common.base.enums.SupplierTypeEnum;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.client.centertask.AiCenterTaskClient;
import com.zyhl.hcy.yun.ai.common.model.api.client.centertask.dto.CenterTaskCreateReqDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.centertask.vo.CenterTaskCreateVO;
import com.zyhl.yun.api.outer.domain.dto.ImageToolSettingDTO;
import com.zyhl.yun.api.outer.domain.entity.centertask.CenterTaskCreateEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.ImageParamEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.TextModelParamEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.TextParamEntity;
import com.zyhl.yun.api.outer.domainservice.IImageCommonService;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.ImageParamTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.external.CenterTaskExternalService;
import com.zyhl.yun.api.outer.external.assembler.CenterTaskAssembler;
import com.zyhl.yun.api.outer.external.client.req.centertask.BusinessParam;
import com.zyhl.yun.api.outer.external.client.req.centertask.ImageParam;
import com.zyhl.yun.api.outer.external.client.req.centertask.TextModelParam;
import com.zyhl.yun.api.outer.external.client.req.centertask.TextParam;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 中心任务-ExternalServiceImpl
 *
 * @Author: WeiJingKun
 */
@Slf4j
@Service
public class CenterTaskExternalServiceImpl implements CenterTaskExternalService {

    @Resource
    private IImageCommonService imageCommonService;

    @Resource
    private AiCenterTaskClient centerTaskClient;

    @Resource
    private CenterTaskAssembler centerTaskCreateAssembler;

    @Override
    public CenterTaskCreateVO createImageTask(DialogueIntentionEnum dialogueIntentionEnum, CenterTaskCreateEntity createEntity, ImageParamEntity imageParamEntity) {
        CenterTaskCreateReqDTO req = null;
        CenterTaskCreateVO result = null;
        try {
            /** 构建接口请求参数-Image */
            req = imageTaskParamCreate(dialogueIntentionEnum, createEntity, imageParamEntity);

            /** 创建ai算法任务 */
            result = centerTaskClient.addAIAbilityTask(req);
            if (null == result) {
                log.warn("CenterTaskExternalServiceImpl-createImageTask-fail，result is null");
            }
            return result;
        } catch (YunAiBusinessException e) {
            log.error("CenterTaskExternalServiceImpl-createImageTask-异常信息: ", e);
            throw e;
        } catch (Exception e) {
            log.error("CenterTaskExternalServiceImpl-createImageTask-异常信息: ", e);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        } finally {
            log.info("CenterTaskExternalServiceImpl-createImageTask-finally\n dialogueIntentionEnum：{}\n createEntity：{}\n imageParamEntity：{}\n req：{}\n result：{}",
                    JSON.toJSONString(dialogueIntentionEnum), JSON.toJSONString(createEntity), JSON.toJSONString(imageParamEntity), JSON.toJSONString(req), JSON.toJSONString(result));
        }
    }

    /**
     * 构建接口请求参数-Image
     *
     * @Author: WeiJingKun
     */
    private CenterTaskCreateReqDTO imageTaskParamCreate(DialogueIntentionEnum dialogueIntentionEnum, CenterTaskCreateEntity createEntity, ImageParamEntity imageParamEntity) {
        /** 构建接口请求参数 */
        List<String> supplierTypes = createEntity.getSupplierTypes();
        // 转换 -> 创建ai算法任务-请求参数
        CenterTaskCreateReqDTO req = centerTaskCreateAssembler.toCenterTaskCreateReqDTO(createEntity);
        if (null != dialogueIntentionEnum) {
            switch (dialogueIntentionEnum) {
                case PICTURE_GENERATE_TEXT:
                    /** 图片配文 */
                    if (CollUtil.isEmpty(supplierTypes)) {
                        // 默认厂商【自研】
                        req.setSupplierTypes(ListUtil.toList(String.valueOf(SupplierTypeEnum.ZY.getCode())));
                    }
                    break;
                case PICTURE_COMIC_STYLE:
                    if (CollUtil.isEmpty(supplierTypes)) {
                        // 默认厂商【自研】，兼容旧版本
                        req.setSupplierTypes(ListUtil.toList(String.valueOf(SupplierTypeEnum.ZY.getCode())));
                    } else {
                        //nacos存在配置走nacos配置
                        imageParamEntity.setStyle(createEntity.getExtendField());
                        imageParamEntity.setExtendField(Boolean.TRUE.toString());
                    }
                    //设置用户选择的厂商风格
                    if (Optional.ofNullable(createEntity.getImageToolSettingDTO()).map(ImageToolSettingDTO::getSupplierType).isPresent()) {
                        req.setSupplierTypes(ListUtil.toList(String.valueOf(createEntity.getImageToolSettingDTO().getSupplierType())));
                    }
                    if (Optional.ofNullable(createEntity.getImageToolSettingDTO()).map(ImageToolSettingDTO::getStyle).isPresent()) {
                        imageParamEntity.setStyle(createEntity.getImageToolSettingDTO().getStyle());
                    }
                    break;
                case AI_HEAD_SCULPTURE:
                    /** AI头像 */
                    if (CollUtil.isEmpty(supplierTypes)) {
                        // 默认厂商【腾讯】
                        req.setSupplierTypes(ListUtil.toList(String.valueOf(SupplierTypeEnum.TX.getCode())));
                    }
                    break;
                case OLD_PHOTOS_REPAIR:
                    /** 老照片修复 */
                    if (CollUtil.isEmpty(supplierTypes)) {
                        // 默认厂商【美图】
                        req.setSupplierTypes(ListUtil.toList(String.valueOf(SupplierTypeEnum.MT.getCode())));
                    }
                    break;
                case IMAGE_QUALITY_RESTORATION:
                    /** 画质修复 */
                    if (CollUtil.isEmpty(supplierTypes)) {
                        // 默认厂商【自研】
                        req.setSupplierTypes(ListUtil.toList(String.valueOf(SupplierTypeEnum.ZY.getCode())));
                    } else {
                        //nacos存在配置走nacos配置
                        imageParamEntity.setStyle(createEntity.getExtendField());
                    }
                    break;
                case INTELLIGENT_BEAUTY:
                    /** 智能美颜 */
                    if (CollUtil.isEmpty(supplierTypes)) {
                        // 默认厂商【美图】
                        req.setSupplierTypes(ListUtil.toList(String.valueOf(SupplierTypeEnum.MT.getCode())));
                    }
                    break;
                case SMART_FAKE_CHECK:
                    /** 图片智能鉴伪 */
                    if (CollUtil.isEmpty(supplierTypes)) {
                        // 默认厂商【自研】
                        req.setSupplierTypes(ListUtil.toList(String.valueOf(SupplierTypeEnum.ZY.getCode())));
                    }
                    break;
                case AI_EXPANSION_MAP:
                    //扩图
                    if (CollUtil.isEmpty(supplierTypes)) {
                        req.setSupplierTypes(ListUtil.toList(String.valueOf(SupplierTypeEnum.MT.getCode())));
                    }
                    imageParamEntity.setExtendField(createEntity.getExtendField());
                    //选择用户选择的比例
                    if (Optional.ofNullable(createEntity.getImageToolSettingDTO()).map(ImageToolSettingDTO::getRatio).isPresent()) {
                        imageParamEntity.setExtendField(createEntity.getImageToolSettingDTO().getRatio());
                    }
                    if (Optional.ofNullable(createEntity.getImageToolSettingDTO()).map(ImageToolSettingDTO::getUrl).isPresent()
                            && Optional.ofNullable(imageParamEntity.getImageExt()).isPresent()) {
                        //传了url过来，使用url下载图片，解决前端传的比例不对问题
                        log.info("图片类参数处理-ImageParam-url:{}", createEntity.getImageToolSettingDTO().getUrl());
                        imageParamEntity.setFileId(null);
                        imageParamEntity.setImageParamType(ImageParamTypeEnum.URL.getCode());
                        imageParamEntity.setFileUrl(createEntity.getImageToolSettingDTO().getUrl());
                    }
                    break;
                case AI_PHOTO_EDIT:
                    //AI改图
                    if (CollUtil.isEmpty(supplierTypes)) {
                        req.setSupplierTypes(ListUtil.toList(String.valueOf(SupplierTypeEnum.HS.getCode())));
                    }
                    //AI改图用户有输入对话，以对话内容为主，没对话内容以nacos配置的为主
                    String extendField = StringUtils.isNotEmpty(createEntity.getDialogue()) ? createEntity.getDialogue() : createEntity.getExtendField();
                    imageParamEntity.setExtendField(extendField);
                    break;
                default:
                    break;
            }
        }

        /** 图片类参数处理-ImageParam */
        imageParamEntity.setUserId(createEntity.getUserId());
        ImageParam imageParam = getImageParam(imageParamEntity);

        // BusinessParam 业务参数，json格式
        BusinessParam businessParam = BusinessParam.builder().imageParam(imageParam).build();
        req.setBusinessParam(JSON.toJSONString(businessParam));
        req.setNumber(createEntity.getNumber());
        return req;
    }

    /**
     * 图片类参数处理-ImageParam
     *
     * @Author: WeiJingKun
     */
    @NotNull
    private ImageParam getImageParam(ImageParamEntity imageParamEntity) {
        /** 转换 -> 创建ai算法任务-请求参数-图片类 */
        ImageParam imageParam = centerTaskCreateAssembler.toImageParam(imageParamEntity);

        /** 图片类参数处理 */
        String imageExt = imageParamEntity.getImageExt();
        Integer imageParamType = imageParamEntity.getImageParamType();
        log.info("getImageParam imageParamEntity:{}", JSONUtil.toJsonStr(imageParamEntity));
        switch (ImageParamTypeEnum.getByCode(imageParamType)) {
            case URL:
                /** 图片url地址 */
                String fileUrl = imageParamEntity.getFileUrl();
                if (CharSequenceUtil.isNotBlank(fileUrl)) {
                    String localPath = imageCommonService.urlToLocalPath(fileUrl, imageExt, imageParamEntity.getUserId());
                    imageParam.setLocalPath(localPath);
                }
                break;
            case BASE64:
                /** 图片base64数据，不带data:image/jpeg;base64头 */
                String base64 = imageParamEntity.getBase64();
                if (CharSequenceUtil.isNotBlank(base64)) {
                    String localPath = imageCommonService.base64ToLocalPath(base64, imageExt, imageParamEntity.getUserId());
                    imageParam.setLocalPath(localPath);
                }
                break;
            case FILE_ID:
                /** 图片文件fileId */
                imageParam.setFileId(imageParamEntity.getFileId());
                break;
            case LOCAL_PATH:
                /** 图片文件共享存储 */
                imageParam.setLocalPath(imageParamEntity.getLocalPath());
                break;
            default:
                break;
        }
        return imageParam;
    }

    @Override
    public CenterTaskCreateVO createTextTask(DialogueIntentionEnum dialogueIntentionEnum, CenterTaskCreateEntity createEntity, TextParamEntity textParamEntity) {
        if (DialogueIntentionEnum.TEXT_GENERATE_PICTURE != dialogueIntentionEnum) {
            return null;
        }

        CenterTaskCreateReqDTO req = null;
        CenterTaskCreateVO result = null;
        try {
            /** 构建接口请求参数-Text */
            req = textTaskParamCreate(createEntity, textParamEntity);

            /** 创建ai算法任务 */
            result = centerTaskClient.addAIAbilityTask(req);
            if (null == result) {
                log.warn("CenterTaskExternalServiceImpl-createTextTask-fail，result is null");
            }
            return result;
        } catch (Exception e) {
            log.error("CenterTaskExternalServiceImpl-createTextTask-异常信息: ", e);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        } finally {
            log.info("CenterTaskExternalServiceImpl-createTextTask-finally\n dialogueIntentionEnum：{}\n createEntity：{}\n textParamEntity：{}\n req：{}\n result：{}",
                    JSON.toJSONString(dialogueIntentionEnum), JSON.toJSONString(createEntity), JSON.toJSONString(textParamEntity), JSON.toJSONString(req), JSON.toJSONString(result));
        }
    }

    /**
     * 构建接口请求参数-Text
     *
     * @Author: WeiJingKun
     */
    @NotNull
    private CenterTaskCreateReqDTO textTaskParamCreate(CenterTaskCreateEntity createEntity, TextParamEntity textParamEntity) {
        /** 转换 -> 创建ai算法任务-请求参数 */
        CenterTaskCreateReqDTO req = centerTaskCreateAssembler.toCenterTaskCreateReqDTO(createEntity);
        if (CollUtil.isEmpty(createEntity.getSupplierTypes())) {
            // 默认厂商【自研】
            req.setSupplierTypes(ListUtil.toList(String.valueOf(SupplierTypeEnum.ZY.getCode())));
        }

        /** 构建请求参数-TextParam */
        // 转换 -> 创建ai算法任务-请求参数-文本类
        TextParam textParam = centerTaskCreateAssembler.toTextParam(textParamEntity);
        // 构建接口请求参数-rowkey
        textParam.setRowkey(createTaskRowkey(textParamEntity.getRowkey(), textParamEntity.getUserId(), textParamEntity.getDialogueId()));

        // BusinessParam 业务参数，json格式
        BusinessParam businessParam = BusinessParam.builder().textParam(textParam).build();
        req.setBusinessParam(JSON.toJSONString(businessParam));
        return req;
    }

    /**
     * 构建接口请求参数-rowkey
     *
     * @Author: WeiJingKun
     */
    private String createTaskRowkey(String rowkey, String userId, String dialogueId) {
        if (CharSequenceUtil.isNotBlank(rowkey)) {
            return rowkey;
        }
        return userId + StrPool.UNDERLINE + dialogueId;
    }

    @Override
    public CenterTaskCreateVO createTextModelTask(DialogueIntentionEnum dialogueIntentionEnum, CenterTaskCreateEntity createEntity, TextModelParamEntity textModelParamEntity) {
        CenterTaskCreateReqDTO req = null;
        CenterTaskCreateVO result = null;
        try {
            /** 构建接口请求参数-TextModel */
            req = textModelTaskParamCreate(createEntity, textModelParamEntity);

            /** 创建ai算法任务 */
            result = centerTaskClient.addAIAbilityTask(req);
            if (null == result) {
                log.warn("CenterTaskExternalServiceImpl-createTextModelTask-fail，result is null");
            }
            return result;
        } catch (Exception e) {
            log.error("CenterTaskExternalServiceImpl-createTextModelTask-异常信息: ", e);
            throw new YunAiBusinessException(ResultCodeEnum.DOWNSTREAM_SERVICES_EXCEPTION);
        } finally {
            log.info("CenterTaskExternalServiceImpl-createTextModelTask-finally\n dialogueIntentionEnum：{}\n createEntity：{}\n textModelParamEntity：{}\n req：{}\n result：{}",
                    JSON.toJSONString(dialogueIntentionEnum), JSON.toJSONString(createEntity), JSON.toJSONString(textModelParamEntity), JSON.toJSONString(req), JSON.toJSONString(result));
        }
    }

    /**
     * 构建接口请求参数-TextModel
     *
     * @Author: WeiJingKun
     */
    @NotNull
    private CenterTaskCreateReqDTO textModelTaskParamCreate(CenterTaskCreateEntity createEntity, TextModelParamEntity textModelParamEntity) {
        /** 转换 -> 创建ai算法任务-请求参数 */
        CenterTaskCreateReqDTO req = centerTaskCreateAssembler.toCenterTaskCreateReqDTO(createEntity);
        if (CollUtil.isEmpty(createEntity.getSupplierTypes())) {
            // 默认厂商【阿里】
            req.setSupplierTypes(ListUtil.toList(String.valueOf(SupplierTypeEnum.ALI.getCode())));
        }

        /** 构建请求参数-TextModelParam */
        TextModelParam textModelParam = centerTaskCreateAssembler.toTextModelParam(textModelParamEntity);
        // 构建接口请求参数-rowkey
        textModelParam.setRowkey(createTaskRowkey(textModelParamEntity.getRowkey(), textModelParamEntity.getUserId(), textModelParamEntity.getDialogueId()));

        // BusinessParam 业务参数，json格式
        BusinessParam businessParam = BusinessParam.builder().textModelParam(textModelParam).build();
        req.setBusinessParam(JSON.toJSONString(businessParam));
        return req;
    }

    @Override
    public String createTaskRowkey(String userId, String dialogueId) {
        return createTaskRowkey(null, userId, dialogueId);
    }

    /**
     * 构建任务rowKey，用list返回
     *
     * @param userId         用户id
     * @param dialogueIdList 对话id
     * @return 响应结果
     */
    @Override
    public List<String> createTaskRowkeyToList(String userId, List<String> dialogueIdList) {
        return dialogueIdList.stream()
                .map(dialogueId -> createTaskRowkey(userId, dialogueId))
                .collect(Collectors.toList());
    }

}
