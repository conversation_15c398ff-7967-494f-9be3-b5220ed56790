package com.zyhl.yun.api.outer.external.utils;

import lombok.SneakyThrows;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;

/**
 * JavaBean转换成xml
 * https://www.cnblogs.com/zyf-yxm/p/9036615.html
 * @Author: Wu_<PERSON><PERSON>_<PERSON>
 */
public class JaxbUtil {

    private JaxbUtil() {
    }

    /**
     * JavaBean转换成xml
     * 默认编码UTF-8
     *
     * @param obj
     * @return
     */
    public static String beanToXml(Object obj) {
        return beanToXml(obj, StandardCharsets.UTF_8.name());
    }

    /**
     * JavaBean转换成xml
     *
     * @param obj
     * @param encoding
     * @return
     */
    public static String beanToXml(Object obj, String encoding) {
        return beanToXmlIgnoreXmlHead(obj, encoding, false, true);
    }

    /**
     * JavaBean转换成xml
     *
     * @param obj
     * @param encoding
     * @param format
     * @return
     */
    public static String beanToXml(Object obj, String encoding, boolean format) {
        return beanToXmlIgnoreXmlHead(obj, encoding, false, format);
    }

    /**
     * JavaBean转换成xml去除xml声明部分
     *
     * @param obj
     * @param encoding
     * @param jaxbFragment 是否去除xml声明部分，false-不去除，true去除
     * @return
     */
    @SneakyThrows
    public static String beanToXmlIgnoreXmlHead(Object obj, String encoding, boolean jaxbFragment, boolean format) {
        JAXBContext context = JAXBContext.newInstance(obj.getClass());
        Marshaller marshaller = context.createMarshaller();
        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, format);
        marshaller.setProperty(Marshaller.JAXB_ENCODING, encoding);
        marshaller.setProperty(Marshaller.JAXB_FRAGMENT, jaxbFragment);
        StringWriter writer = new StringWriter();
        marshaller.marshal(obj, writer);
        return writer.toString();
    }


    /**
     * xml转换成JavaBean
     *
     * @param xml
     * @param c
     * @return
     */
    @SneakyThrows
    public static <T> T xmlToBean(String xml, Class<T> c) {
        JAXBContext context = JAXBContext.newInstance(c);
        Unmarshaller unmarshaller = context.createUnmarshaller();
        return (T) unmarshaller.unmarshal(new StringReader(xml));
    }

}