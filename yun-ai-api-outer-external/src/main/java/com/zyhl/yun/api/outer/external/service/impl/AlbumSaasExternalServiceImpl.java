package com.zyhl.yun.api.outer.external.service.impl;

import com.zyhl.hcy.yun.ai.common.platform.third.client.album.enums.StandardModuleTypeEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.req.AddMemoryStoryReq;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.req.QueryAlbumByAlbumIdReq;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.resp.AddMemoryStoryResp;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.resp.QueryAlbumByAlbumIdResp;
import com.zyhl.hcy.yun.ai.common.platform.third.client.album.service.AlbumSaasService;
import com.zyhl.yun.api.outer.domain.dto.QualityAlbumSelectionRespDTO;
import com.zyhl.yun.api.outer.domain.valueobject.AlbumInfo;
import com.zyhl.yun.api.outer.external.AlbumSaasExternalService;
import com.zyhl.yun.api.outer.util.RequestContextHolder;

import org.springframework.stereotype.Service;

import java.util.Objects;

import javax.annotation.Resource;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.external.service.impl.AlbumSaasExternalServiceImpl} <br>
 * <b> description:</b>
 * 相册接口层实现类
 *
 * <AUTHOR>
 * @date 2025-05-21 14:04
 **/
@Slf4j
@Service
public class AlbumSaasExternalServiceImpl implements AlbumSaasExternalService {

    @Resource
    private AlbumSaasService albumSaasService;

    @Override
    public AddMemoryStoryResp addMemoryStory(QualityAlbumSelectionRespDTO qualityAlbumSelectionRespDTO) {
        AddMemoryStoryReq addMemoryStoryReq = new AddMemoryStoryReq();
        addMemoryStoryReq.setUserId(RequestContextHolder.getUserId());
        addMemoryStoryReq.setName(qualityAlbumSelectionRespDTO.getAlbumName());
        addMemoryStoryReq.setSecondTitle(qualityAlbumSelectionRespDTO.getAlbumSecondTittle());
        addMemoryStoryReq.setFileIds(qualityAlbumSelectionRespDTO.getFileIds().toArray(new String[0]));
        addMemoryStoryReq.setCover(qualityAlbumSelectionRespDTO.getAlbumCover());
        addMemoryStoryReq.setOwnerType(StandardModuleTypeEnum.getOwnerTypeEnum(qualityAlbumSelectionRespDTO.getOwnerType()));
        addMemoryStoryReq.setToken(RequestContextHolder.getToken());
        addMemoryStoryReq.setMusicId(qualityAlbumSelectionRespDTO.getMusicId());
        AddMemoryStoryResp addMemoryStoryResp = albumSaasService.addMemoryStory(addMemoryStoryReq);
        log.info("==> addMemoryStoryResp:{}", addMemoryStoryResp);
        if (Objects.nonNull(addMemoryStoryResp)) {
            qualityAlbumSelectionRespDTO.setAlbumId(addMemoryStoryResp.getId());
        }
        return addMemoryStoryResp;
    }

    @Override
    public AlbumInfo queryAlbumByAlbumId(String id) {
        QueryAlbumByAlbumIdReq queryAlbumByAlbumIdReq = new QueryAlbumByAlbumIdReq();
        queryAlbumByAlbumIdReq.setAlbumId(id);
        queryAlbumByAlbumIdReq.setUserId(RequestContextHolder.getUserId());
        queryAlbumByAlbumIdReq.setCreateUserId(RequestContextHolder.getUserId());
        queryAlbumByAlbumIdReq.setOwnerType(StandardModuleTypeEnum.PERSONAL.getOwnerType());
        QueryAlbumByAlbumIdResp queryAlbumByAlbumIdResp = albumSaasService.queryAlbumByAlbumId(queryAlbumByAlbumIdReq);
        log.info("==> queryAlbumByAlbumIdResp:{}", queryAlbumByAlbumIdResp);
        if (Objects.nonNull(queryAlbumByAlbumIdResp)) {
            AlbumInfo albumInfo = new AlbumInfo();
            albumInfo.setAlbumId(queryAlbumByAlbumIdResp.getId());
            albumInfo.setName(queryAlbumByAlbumIdResp.getName());
            albumInfo.setSecondTitle(queryAlbumByAlbumIdResp.getSecondTitle());
            albumInfo.setCover(queryAlbumByAlbumIdResp.getCover());
            albumInfo.setDescription(queryAlbumByAlbumIdResp.getDescription());
            albumInfo.setFileNumber(queryAlbumByAlbumIdResp.getFileNumber());
            albumInfo.setTopFlag(queryAlbumByAlbumIdResp.getTopFlag());
            albumInfo.setTopTime(queryAlbumByAlbumIdResp.getTopTime());
            albumInfo.setCreateTime(queryAlbumByAlbumIdResp.getCreateTime());
            albumInfo.setUpdateTime(queryAlbumByAlbumIdResp.getUpdateTime());
            albumInfo.setRelationName(queryAlbumByAlbumIdResp.getRelationName());
            if (CharSequenceUtil.isNotEmpty(queryAlbumByAlbumIdResp.getRelationType())) {
                albumInfo.setRelationType(Integer.parseInt(queryAlbumByAlbumIdResp.getRelationType()));
            }
            return albumInfo;
        }
        return null;
    }
}
