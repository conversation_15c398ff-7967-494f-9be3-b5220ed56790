package com.zyhl.yun.api.outer.external.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.enums.RenameModeEnum;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.enums.BaseResultCodeEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.OwnerDriveClient;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.req.*;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.resp.OwnerDriveResponse;
import com.zyhl.hcy.yun.ai.common.platform.third.client.ownerdrive.vo.*;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.enums.ThumbnailStyleEnum;
import com.zyhl.hcy.yun.ai.common.platform.third.client.yundisk.vo.DriveVO;
import com.zyhl.yun.api.outer.enums.knowledge.FileTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.external.UserDriveExternalService;
import com.zyhl.yun.api.outer.external.YunDiskExternalService;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 用户独立空间接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserDriveExternalServiceImpl implements UserDriveExternalService {

    @Resource
    private OwnerDriveClient ownerDriveClient;
    @Resource
    private RedisOperateRepository redisOperateRepository;
    @Resource
    private YunDiskExternalService yunDiskExternalService;


    /**
     * 独立空间文件名非法
     */
    private static final String OWNER_DRIVE_FILE_NAME_INVALID = "13000002";

    @Override
    public String getUserDriveId(String userId) {
        // 先查缓存
        String driveId = redisOperateRepository.getUserDriveId(userId);
        if (CharSequenceUtil.isNotEmpty(driveId)) {
            return driveId;
        }

        // 获取独立空间
        OwnerDriveReqDTO reqDTO = new OwnerDriveReqDTO();
        reqDTO.setUserId(userId);
        reqDTO.setClientInfo(RequestContextHolder.getClientInfo());
        reqDTO.setAppChannel(RequestContextHolder.getAppChannel());

        OwnerDriveVO vo = ownerDriveClient.getOwnerDrive(reqDTO);
        log.info("获取用户独立空间ID，空间id：{}，状态：{}，总空间：{}，已使用空间：{}", vo.getDriveId(), vo.getStatus(), vo.getTotalSize(), vo.getUsedSize());

        // 缓存
        redisOperateRepository.setUserDriveId(userId, vo.getDriveId());

        return vo.getDriveId();
    }

    @Override
    public String createCatalog(String userId, String dirName) {
        // 创建目录
        OwnerDriveDirVO vo = createCatalog(userId, dirName, "/", RenameModeEnum.REFUSE.getCode());

        return vo.getFileId();
    }

    /**
     * 指定父目录ID创建目录，支持重复创建自动重命名
     *
     * @param userId
     * @param parentFileId
     * @param dirName
     * @return
     */
    @Override
    public OwnerDriveDirVO createCatalog(String userId, String dirName, String parentFileId) {
        return createCatalog(userId, dirName, parentFileId, RenameModeEnum.FORCE_RENAME.getCode());
    }

    @Override
    public OwnerDriveDirVO createCatalog(String userId, String dirName, String parentFileId, String renameMode) {
        // 创建目录
        OwnerDriveDirReqDTO reqDTO = new OwnerDriveDirReqDTO();
        reqDTO.setUserId(userId);
        reqDTO.setName(dirName);
        reqDTO.setClientInfo(RequestContextHolder.getClientInfo());
        reqDTO.setAppChannel(RequestContextHolder.getAppChannel());
        reqDTO.setParentFileId(parentFileId);
        reqDTO.setFileRenameMode(renameMode);
        try {
            OwnerDriveDirVO vo = ownerDriveClient.getDirectory(reqDTO);
            log.info("【独立空间】创建目录请求参数：{}", JsonUtil.toJson(reqDTO));
            return vo;
        } catch (YunAiBusinessException e) {
            if (OWNER_DRIVE_FILE_NAME_INVALID.equals(e.getCode())) {
                throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_FILE_INVALID_NAME);
            }
            throw e;
        }
    }

    @Override
    public String transTask(RequestContextHolder.UserInfo userInfo, String dirId, List<String> fileIdsList) {
        // 转存参数
        OwnerDriveYunFileTransReqDTO reqDTO = new OwnerDriveYunFileTransReqDTO();
        reqDTO.setUserId(userInfo.getUserId());
        reqDTO.setBelongsPlatform(userInfo.getBelongsPlatform());
        reqDTO.setDriveId(getUserDriveId(userInfo.getUserId()));
        reqDTO.setDirId(dirId);
        reqDTO.setMobile(userInfo.getPhoneNumber());

        // 个人云空间信息
        DriveVO vo = yunDiskExternalService.getDrive(userInfo.getUserId(), userInfo.getBelongsPlatform());

        // 转存文件
        List<OwnerDriveYunFileTransReqDTO.YunFileInfo> yunFiles = new ArrayList<>();
        for (String fileId : fileIdsList) {
            OwnerDriveYunFileTransReqDTO.YunFileInfo yunFile = new OwnerDriveYunFileTransReqDTO.YunFileInfo();
            yunFile.setFileId(fileId);
            yunFile.setDriveId(vo.getDriveId());
            yunFiles.add(yunFile);
        }
        reqDTO.setYunFiles(yunFiles);

        // 开始转存
        OwnerDriveTransTaskVO taskVO = ownerDriveClient.yunFileTransOwnerDrive(reqDTO);

        return taskVO.getTaskId();
    }

    @Override
    public String getDownloadUrl(String userId, String fileId) {
        OwnerDriveFileDownloadReqDTO reqDTO = new OwnerDriveFileDownloadReqDTO();
        reqDTO.setUserId(userId);
        reqDTO.setFileId(fileId);
        OwnerDriveFileDownloadVO vo = ownerDriveClient.getFileDownload(reqDTO);

        return vo.getUrl();
    }

    /**
     * 批量查询文件下载地址
     *
     * @param userId
     * @param fileIds
     * @return
     */
    @Override
    public OwnerDriveFileDownloadBatchVO getBatchFilesDownloadUrl(String userId, List<String> fileIds) {
        if (CollUtil.isEmpty(fileIds)) {
            return null;
        }
        OwnerDriveBatchFileDownloadReqDTO reqDTO = new OwnerDriveBatchFileDownloadReqDTO();
        reqDTO.setUserId(userId);
        reqDTO.setFileIds(fileIds.toArray(new String[0]));
        return ownerDriveClient.getBatchFilesDownload(reqDTO);
    }

    @Override
    public OwnerDriveFileVO getFileInfo(String userId, String fileId) {
        OwnerDriveFileReqDTO reqDTO = new OwnerDriveFileReqDTO();
        reqDTO.setUserId(userId);
        reqDTO.setFileId(fileId);

        return ownerDriveClient.getFile(reqDTO);
    }

    @Override
    public OwnerDriveFileVO getFileInfo(String userId, String fileId, ThumbnailStyleEnum... styleList) {
        OwnerDriveFileReqDTO reqDTO = new OwnerDriveFileReqDTO();
        reqDTO.setUserId(userId);
        reqDTO.setFileId(fileId);

        if (ObjectUtil.isNotEmpty(styleList)) {
            String[] styleArray = new String[styleList.length];
            for (int i = 0; i < styleList.length; i++) {
                styleArray[i] = styleList[i].getType();
            }
            reqDTO.setImageThumbnailStyleList(styleArray);
        }

        return ownerDriveClient.getFile(reqDTO);
    }

    @Override
    public String deleteByFileIds(String userId, List<String> fileIds) {

        OwnerDriveBatchDeleteFileReqDTO reqDTO = new OwnerDriveBatchDeleteFileReqDTO();
        reqDTO.setUserId(userId);
        reqDTO.setFileIds(fileIds.toArray(new String[0]));

        // 删除
        OwnerDriveTaskVO vo = ownerDriveClient.batchDeleteFiles(reqDTO);

        return vo.getTaskId();
    }

    @Override
    public String moveByFileIds(String userId, List<String> fileIds, String toParentFileId) {

        OwnerDriveBatchMoveFileReqDTO reqDTO = new OwnerDriveBatchMoveFileReqDTO();
        reqDTO.setUserId(userId);
        reqDTO.setFileIds(fileIds.toArray(new String[0]));
        reqDTO.setToParentFileId(toParentFileId);

        // 移动
        OwnerDriveTaskVO vo = ownerDriveClient.batchMoveFiles(reqDTO);

        return vo.getTaskId();
    }

    @Override
    public OwnerDriveTaskResultVO getDeleteTaskResult(String userId, String taskId) {
        OwnerDriveTaskQueryReqDTO reqDTO = new OwnerDriveTaskQueryReqDTO();
        reqDTO.setUserId(userId);
        reqDTO.setTaskId(taskId);

        return ownerDriveClient.ownerDriveTaskQuery(reqDTO);
    }

    @Override
    public List<OwnerDriveFileVO> getBatchFileInfo(String userId, List<String> fileIdList) {
        OwnerDriveBatchFileReqDTO reqDTO = new OwnerDriveBatchFileReqDTO();
        reqDTO.setUserId(userId);
        reqDTO.setFileIds(fileIdList.toArray(new String[0]));
        return getBatchFileInfo(reqDTO);
    }

    @Override
    public List<OwnerDriveFileVO> getBatchFileInfo(OwnerDriveBatchFileReqDTO req) {
        List<OwnerDriveFileVO> fileList = new ArrayList<>();
        OwnerDriveFileBatchVO vo = ownerDriveClient.getBatchFiles(req);
        if (Objects.nonNull(vo) && Objects.nonNull(vo.getBatchFileResults())) {
            for (int i = 0, len = vo.getBatchFileResults().length; i < len; i++) {
                OwnerDriveFileBatchVO.OwnerDriveFileResult fileResult = vo.getBatchFileResults()[i];
                if (!BaseResultCodeEnum.SUCCESS.getResultCode().equals(fileResult.getErrCode())) {
                    continue;
                }
                if (Objects.nonNull(fileResult.getSrcFile())) {
                    fileList.add(vo.getBatchFileResults()[i].getSrcFile());
                }
            }
        }
        return fileList;
    }

    @Override
    public OwnerDriveVO getSpace(OwnerDriveReqDTO dto) {
        return ownerDriveClient.getOwnerDrive(dto);
    }

    @Override
    public OwnerDriveResponse updateFileInfo(OwnerDriveFileUpdateReqDTO reqDTO) {
        return ownerDriveClient.updateFile(reqDTO);
    }

    @Override
    public boolean checkFileExist(String userId, String fileId) {
        OwnerDriveFileCheckExistsReqDTO reqDTO = new OwnerDriveFileCheckExistsReqDTO();
        reqDTO.setUserId(userId);
        reqDTO.setParentFileId("/");
        reqDTO.setFileName(fileId);
        OwnerDriveCheckExistsVO vo = ownerDriveClient.checkFileExist(reqDTO);
        if (vo.getExist()) {
            FileTypeEnum type = FileTypeEnum.getByYunDiskFileType(vo.getType());
            return FileTypeEnum.FOLDER.equals(type);
        }
        return false;
    }

    @Override
    public Map<String, String> batchFileGetPath(String userId, List<String> fileIds) {
        OwnerDriveBatchGetFilePathReqDTO reqDTO = new OwnerDriveBatchGetFilePathReqDTO();
        reqDTO.setUserId(userId);
        reqDTO.setFileIds(fileIds.toArray(new String[0]));
        OwnerDriveBatchFilePathVO vo = ownerDriveClient.batchFileGetPath(reqDTO);
        if (vo != null && vo.getItems() != null) {
            Map<String, String> map = new HashMap<>();

            for (OwnerDriveBatchFilePathVO.FilePathInfo item : vo.getItems()) {
                String idPath = item.getIdPath();
                String[] parts = idPath.split("/");
                String lastPart = parts.length > 0 ? parts[parts.length - 1] : "";
                map.put(lastPart, idPath);
            }

            return map;
        }

        return null;
    }
}
