package com.zyhl.yun.api.outer.external.client;

import com.zyhl.yun.api.outer.external.client.feignconfig.FeignInterceptor;
import com.zyhl.yun.api.outer.external.req.DialogueIntentionReq;
import com.zyhl.yun.api.outer.external.resp.DialogueIntentionResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.external.client.DialogueIntentionClient} <br>
 * <b> description:</b>
 * 对话意图Feign
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-03-05 14:49
 **/
@FeignClient(name = "${external.ai-model.cmic-dialogue.intention.service-name}",
        url = "${external.ai-model.cmic-dialogue.intention.service-url}",
        configuration = FeignInterceptor.class)
public interface DialogueIntentionClient {

    /**
     * 输入用户对话/历史记录，返回用户最新的意图
     *
     * @param intentionReq the intent req
     * @return {@link DialogueIntentionResp}
     * <AUTHOR>
     * @date 2024-3-5 15:27
     */
    @PostMapping(value = "/yun/ai/intention/dialogue")
    DialogueIntentionResp dialogueIntentionFuc(DialogueIntentionReq intentionReq);
    
    /**
     * 输入用户对话/历史记录，返回用户最新的意图（云手机）
     *
     * @param intentionReq the intent req
     * @return {@link DialogueIntentionResp}
     * <AUTHOR>
     * @date 2024-3-5 15:27
     */
    @PostMapping(value = "/yun/ai/intention/dialogue/cloudphone")
    DialogueIntentionResp dialogueIntentionCloudPhoneFuc(DialogueIntentionReq intentionReq);
}
