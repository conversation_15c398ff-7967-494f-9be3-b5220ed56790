package com.zyhl.yun.api.outer.external.assembler;

import com.zyhl.hcy.yun.ai.common.model.api.client.centertask.dto.CenterTaskCreateReqDTO;
import com.zyhl.yun.api.outer.domain.entity.DialogueIntentionEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.CenterTaskCreateEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.ImageParamEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.TextModelParamEntity;
import com.zyhl.yun.api.outer.domain.entity.centertask.TextParamEntity;
import com.zyhl.yun.api.outer.external.client.req.centertask.ImageParam;
import com.zyhl.yun.api.outer.external.client.req.centertask.TextModelParam;
import com.zyhl.yun.api.outer.external.client.req.centertask.TextParam;
import com.zyhl.yun.api.outer.external.req.DialogueIntentionReq;
import com.zyhl.yun.api.outer.external.resp.DialogueIntentionResp;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 中心任务-类转换器
 *
 * @Author: WeiJingKun
 */
@Mapper(componentModel = "spring")
public interface CenterTaskAssembler {

    CenterTaskAssembler INSTANCE = Mappers.getMapper(CenterTaskAssembler.class);

    /**
     * 转换 -> 创建ai算法任务-请求参数
     * @param entity entity
     * @return dto
     */
    CenterTaskCreateReqDTO toCenterTaskCreateReqDTO(CenterTaskCreateEntity entity);

    /**
     * 转换 -> 创建ai算法任务-请求参数-图片类
     * @param entity entity
     * @return imageParam
     */
    ImageParam toImageParam(ImageParamEntity entity);

    /**
     * 转换 -> 创建ai算法任务-请求参数-文本类
     * @param entity entity
     * @return textParam
     */
    TextParam toTextParam(TextParamEntity entity);

    /**
     * 转换 -> 创建ai算法任务-请求参数-文本模型类
     * @param entity entity
     * @return textModelParam
     */
    TextModelParam toTextModelParam(TextModelParamEntity entity);

    /**
     * 转换成dialogueIntentionEntity
     *
     * @param dialogueIntentionEntity the dialogue intention entity
     * @return {@link DialogueIntentionReq}
     * <AUTHOR>
     * @date 2024-3-19 11:02
     */
    @Mapping(target = "currentDialogue", ignore = true)
    DialogueIntentionReq toDialogueIntentionReq(DialogueIntentionEntity dialogueIntentionEntity);

    /**
     * 转换成dialogueIntentionResp
     *
     * @param dialogueIntentionResp the dialogue intention resp
     * @return {@link DialogueIntentionVO}
     * <AUTHOR>
     * @date 2024-3-19 11:03
     */
    DialogueIntentionVO toDialogueIntentionVO(DialogueIntentionResp.DialogueIntentionRespData dialogueIntentionResp);
}
