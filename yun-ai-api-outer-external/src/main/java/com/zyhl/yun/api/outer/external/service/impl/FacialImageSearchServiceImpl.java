package com.zyhl.yun.api.outer.external.service.impl;

import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.model.api.client.reusable.enums.BaseResultCodeEnum;
import com.zyhl.yun.api.outer.domain.req.FaceSearchEntity;
import com.zyhl.yun.api.outer.domain.resp.FaceSearchRespEntity;
import com.zyhl.yun.api.outer.external.client.FacialImageSearchClient;
import com.zyhl.yun.api.outer.external.service.FacialImageSearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 人脸识别
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FacialImageSearchServiceImpl implements FacialImageSearchService {

    @Resource
    private FacialImageSearchClient facialImageSearchClient;


    @Override
    public BaseResult<FaceSearchRespEntity> getClassFaceInfo(FaceSearchEntity faceSearchEntity) {
        BaseResult<FaceSearchRespEntity> classFaceInfo = null;
        try {
            classFaceInfo = facialImageSearchClient.getClassFaceInfo(faceSearchEntity);
        } catch (Exception e) {
            log.error("人脸识别-接口异常：,ownerId:{},ownerType:{},requestId:{}", faceSearchEntity.getOwnerId(), faceSearchEntity.getOwnerType(), faceSearchEntity.getRequestId(), e);
            throw new YunAiBusinessException(BaseResultCodeEnum.DOWNSTREAM_EXCEPTION.getResultCode(), BaseResultCodeEnum.DOWNSTREAM_EXCEPTION.getResultMsg());
        }
        if (!classFaceInfo.isSuccess()) {
            log.error("人脸识别-接口异常：,ownerId:{},ownerType:{},requestId:{}", faceSearchEntity.getOwnerId(), faceSearchEntity.getOwnerType(), faceSearchEntity.getRequestId());
            throw new YunAiBusinessException(classFaceInfo.getCode(), classFaceInfo.getMessage());
        }
        return classFaceInfo;
    }
}
