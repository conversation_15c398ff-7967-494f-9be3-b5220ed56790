package com.zyhl.yun.api.outer.external.service;

import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.dto.AipptDesignInfoRequestDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.dto.AipptExportRequestDTO;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.vo.AipptCodeResponseVO;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.vo.AipptDesignInfoResponseVO;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.vo.AipptExportResponseVO;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.vo.AipptExportResultResponseVO;
import com.zyhl.hcy.yun.ai.common.model.api.client.aippt.vo.AipptTokenResponseVO;

/**
 * AiPPT外部服务接口
 * 提供带缓存和重试机制的AiPPT API调用
 *
 * <AUTHOR> Assistant
 * @date 2025/1/25
 */
public interface AipptExternalService {

    /**
     * 获取认证Token（带缓存）
     *
     * @param uid 企业方自己用户的唯一标识
     * @param channel 渠道标识，存在则进行传递，没有传空
     * @return Token响应
     */
    AipptTokenResponseVO getTokenWithCache(String uid, String channel);

    /**
     * 获取认证Code
     *
     * @param uid 企业方自己用户的唯一标识
     * @param channel 渠道标识，存在则进行传递，没有传空
     * @param type 不传则为PC，2则为移动端
     * @return Code响应
     */
    AipptCodeResponseVO getCode(String uid, String channel, String type);

    /**
     * 作品导出（带重试机制）
     * 
     * @param exportRequest 导出请求参数
     * @param uid 企业方自己用户的唯一标识
     * @param channel 渠道标识
     * @return 导出响应
     */
    AipptExportResponseVO exportFileWithRetry(AipptExportRequestDTO exportRequest, String uid, String channel);

    /**
     * 查询导出任务状态（带重试机制）
     * 
     * @param taskKey 任务标识
     * @param uid 企业方自己用户的唯一标识
     * @param channel 渠道标识
     * @return 导出结果响应
     */
    AipptExportResultResponseVO queryExportStatusWithRetry(String taskKey, String uid, String channel);

    /**
     * 查询作品详情（带重试机制）
     * 
     * @param designInfoRequest 作品详情查询请求参数
     * @param uid 企业方自己用户的唯一标识
     * @param channel 渠道标识
     * @return 作品详情响应
     */
    AipptDesignInfoResponseVO getDesignInfoWithRetry(AipptDesignInfoRequestDTO designInfoRequest, String uid, String channel);

    /**
     * 清除指定用户的Token缓存
     * 
     * @param uid 企业方自己用户的唯一标识
     * @param channel 渠道标识
     */
    void clearTokenCache(String uid, String channel);
}
