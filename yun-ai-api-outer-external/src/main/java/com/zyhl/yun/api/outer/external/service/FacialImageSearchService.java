package com.zyhl.yun.api.outer.external.service;


import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.api.outer.domain.req.FaceSearchEntity;
import com.zyhl.yun.api.outer.domain.resp.FaceSearchRespEntity;

/**
 * 人脸搜图
 * <AUTHOR>
 */
public interface FacialImageSearchService {

    /**
     * 人脸搜图
     * @param faceSearchEntity 请求参数
     * @return 人脸搜图结果
     */
    BaseResult<FaceSearchRespEntity> getClassFaceInfo(FaceSearchEntity faceSearchEntity);

}
