package com.zyhl.yun.api.outer.external.client.req.text;


import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchNoteParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 文本工具Client-请求参数-笔记搜索
 * @Author: WeiJingKun
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntelligentSearchNoteReq implements Serializable {

    private String userId;

    @NotNull(message ="搜索条件不能为空")
    private @Valid SearchNoteParam searchParam;

}
