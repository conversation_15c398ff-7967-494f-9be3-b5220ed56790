package com.zyhl.yun.api.outer.external.client.req.text;

import com.zyhl.yun.api.outer.domain.vo.chat.search.param.SearchMailParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 文本工具Client-请求参数-邮件搜索
 * @Author: WeiJingKun
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntelligentSearchMailReq implements Serializable {

    /** 用户Id，默认从token获取，第三方平台调用时必填。 */
    private String userId;

    /** 邮箱用户Id,默认从token获取拼接@139.com，第三方平台调用时必填。 */
    private String mailUserId;

    /** 邮件搜索条件 */
    @NotNull(message ="搜索条件不能为空")
    private @Valid SearchMailParam searchParam;

}
