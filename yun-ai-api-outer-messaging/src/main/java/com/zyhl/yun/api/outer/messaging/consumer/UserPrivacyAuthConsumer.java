package com.zyhl.yun.api.outer.messaging.consumer;

import cn.hutool.json.JSONUtil;
import com.chinamobile.tuxedo.sdk.api.Action;
import com.chinamobile.tuxedo.sdk.api.ConsumeContext;
import com.chinamobile.tuxedo.sdk.api.Message;
import com.zyhl.hcy.yun.ai.common.base.rocketmq.ConcurrentlyConsumer;
import com.zyhl.yun.api.outer.application.dto.AiRegisterReqDTO;
import com.zyhl.yun.api.outer.application.dto.mq.UserPrivacyAuthContentDTO;
import com.zyhl.yun.api.outer.application.dto.mq.UserPrivacyAuthDTO;
import com.zyhl.yun.api.outer.application.service.AlgorithmAiRegisterService;
import com.zyhl.yun.api.outer.application.service.external.UserAuthService;
import com.zyhl.yun.api.outer.config.RocketmqAclUserDomainProperties;
import com.zyhl.yun.api.outer.config.RocketmqConsumerProperties;
import com.zyhl.yun.api.outer.domain.dto.UserInfoDTO;
import com.zyhl.yun.api.outer.enums.BusinessSourceEnum;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 监听用户域用户隐私协议事件
 * 为用户打开智能开关
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Slf4j
@Component
public class UserPrivacyAuthConsumer extends ConcurrentlyConsumer {

    @Resource
    private RocketmqAclUserDomainProperties userDomainProperties;
    @Resource
    private RocketmqConsumerProperties consumerProperties;
    @Resource
    private AlgorithmAiRegisterService algorithmAiRegisterService;
    @Resource
    private UserAuthService userAuthService;

    @Override
    protected void setSubscribeConfig() {
        super.setNamesrvAddr(userDomainProperties.getNamesrvAddr());
        super.setNamespace(userDomainProperties.getNamespace());
        super.setAccessKey(userDomainProperties.getAccessKey());
        super.setSecretKey(userDomainProperties.getSecretKey());
        super.setTopic(consumerProperties.getUserPrivacyAuth().getTopic());
        super.setGroupId(consumerProperties.getUserPrivacyAuth().getGroupName());
        super.setTag(consumerProperties.getUserPrivacyAuth().getTag());
        super.setInstanceName(consumerProperties.getUserPrivacyAuth().getInstanceName());
        super.setConsumeThreadNums(consumerProperties.getUserPrivacyAuth().getConsumeThreadNums());
        super.setConsumeTimeout(consumerProperties.getUserPrivacyAuth().getConsumeTimeout());
    }

    @Override
    protected Action executeConsume(List<Message> list, ConsumeContext consumeContext) {
        log.info("【用户域-用户隐私协议事件】MQ开始消费，收到消息条数：{}，消息内容：{}", list.size(), JSONUtil.toJsonStr(list));
        for (Message msg : list) {
            String body = new String(msg.getBody());
            log.info("【用户域-用户隐私协议事件】MQ开始消费，收到消息msg body：{}", body);
            // 获取MQ消息体
            UserPrivacyAuthDTO msgDTO = JSONUtil.toBean(body, UserPrivacyAuthDTO.class);
            String messageId = msgDTO.getMessageId();
            if (Objects.isNull(msgDTO.getContent())) {
                log.error("【用户域-用户隐私协议事件】消费参数为空，messageId：{}", msgDTO.getMessageId());
                continue;
            }
            UserPrivacyAuthContentDTO contentDTO = JSONUtil.parseObj(msgDTO.getContent()).toBean(UserPrivacyAuthContentDTO.class);
            if (Objects.isNull(contentDTO) || Objects.isNull(contentDTO.getUserDomainId())) {
                log.error("【用户域-用户隐私协议事件】UserDomainId为空，messageId：{}", msgDTO.getMessageId());
                continue;
            }
            try {
                auth(contentDTO);
            } catch (Exception e) {
                log.error("【用户域-用户隐私协议事件】消费出现异常，messageId：{}，error msg：{}", messageId, e.getMessage(), e);
            }
        }
        return Action.CommitMessage;
    }

    /**
     * 授权
     *
     * @param contentDTO 消息体
     */
    private void auth(UserPrivacyAuthContentDTO contentDTO) {
        // 获取用户信息
        UserInfoDTO userInfoDTO = userAuthService.validateUserId(contentDTO.getUserDomainId());
        if (Objects.isNull(userInfoDTO)) {
            log.error("【用户域-用户隐私协议事件】用户信息为空，messageId：{}", contentDTO.getUserDomainId());
            return;
        }
        RequestContextHolder.setUserInfo(userInfoDTO.getUserId(), userInfoDTO.getPhoneNumber(), userInfoDTO.getBelongsPlatform());

        // 授权
        AiRegisterReqDTO dto = new AiRegisterReqDTO();
        dto.setUserId(contentDTO.getUserDomainId());
        dto.setSourceBusiness(BusinessSourceEnum.ASSISTANT.getCode());
        dto.setModule(0);
        algorithmAiRegisterService.accredit(dto);
    }
}
