package com.zyhl.yun.api.outer.messaging.producer;

import com.chinamobile.tuxedo.sdk.api.Producer;
import com.chinamobile.tuxedo.sdk.api.PropertyKeyConst;
import com.chinamobile.tuxedo.sdk.api.TuxeFactory;
import com.zyhl.yun.api.outer.config.RocketmqProducerProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.Properties;

/**
 * className: KnowledgeFileDeleteTaskProducerInitialize
 * description: 知识库文件删除任务生产者初始化
 *
 * <AUTHOR>
 * @date 2025/5/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
@Configuration
public class KnowledgeFileDeleteTaskProducerInitialize extends BaseProducerInitialize {

    @Resource
    private RocketmqProducerProperties rocketmqProducerProperties;

    private Producer producer;

    @Bean("knowledgeFileDeleteTaskProducer")
    public Producer getKnowledgeFileDeleteTaskProducer() {
        return producer;
    }

    @Override
    public void afterPropertiesSet() {
        Properties properties = getBaseProperties();
        // 移动云控制台创建的Group ID
        properties.setProperty(PropertyKeyConst.GROUP_ID, rocketmqProducerProperties.getPersonalKnowledgeFileDeleteTask().getGroupId());
        producer = TuxeFactory.createProducer(properties);
        this.producer.start();
        log.info("知识库文件删除任务发送MQ启动成功");
    }

    @Override
    public void destroy() {

        if (producer != null) {
            producer.shutdown();
        }
    }
}
