package com.zyhl.yun.api.outer.messaging.producer;

import com.chinamobile.tuxedo.sdk.api.Producer;
import com.chinamobile.tuxedo.sdk.api.PropertyKeyConst;
import com.chinamobile.tuxedo.sdk.api.TuxeFactory;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2024/7/30 11:25
 * @desc TOPIC_LOCAL_ALGORITHM_AUTHORIZE topic的生产者 发送AI授权MQ
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
@Configuration
public class TopicLocalAlgorithmAuthorizeProducerInitialize extends BaseProducerInitialize {

	@Value("${rocketmq.producer.topic-local-algorithm-authorize.groupId}")
	private String groupId;

	private Producer producer;

	@Bean("topicLocalAlgorithmAuthorizeProducer")
	public Producer getTopicLocalAlgorithmAuthorizeProducer() {
		return producer;
	}

	@Override
	public void afterPropertiesSet() {
		Properties properties = getBaseProperties();
		// 移动云控制台创建的Group ID
		properties.setProperty(PropertyKeyConst.GROUP_ID, groupId);
		producer = TuxeFactory.createProducer(properties);
		this.producer.start();
		log.info("###afterPropertiesSet AI授权发送MQ启动成功 groupId:{}",groupId);
	}

	@Override
	public void destroy() {

		if (producer != null) {
			producer.shutdown();
		}
	}
}
