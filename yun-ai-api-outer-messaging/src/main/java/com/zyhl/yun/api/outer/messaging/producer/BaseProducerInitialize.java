package com.zyhl.yun.api.outer.messaging.producer;

import com.chinamobile.tuxedo.sdk.api.PropertyKeyConst;
import com.zyhl.hcy.yun.ai.common.base.enums.AccessPointTypeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * <AUTHOR>
 * @since 2024/4/12 11:58
 */
@Data
@Slf4j
@Configuration
public class BaseProducerInitialize implements InitializingBean, DisposableBean {

    @Value("${rocketmq.acl.default.instanceId}")
    private String instanceId;

    @Value("${rocketmq.acl.default.instanceName}")
    private String instanceName;

    @Value("${rocketmq.acl.default.nameServer}")
    private String nameServer;

    @Value("${rocketmq.acl.default.access_point_type}")
    private String accessPointType;

    @Value("${rocketmq.acl.default.accessKey}")
    private String accessKey;

    @Value("${rocketmq.acl.default.secretKey}")
    private String secretKey;

    @Value("${rocketmq.acl.default.sendMsgTimeoutMillis}")
    private String sendMsgTimeoutMillis;


    public Properties getBaseProperties() {
        Properties properties = new Properties();
        AccessPointTypeEnum pointTypeEnum = AccessPointTypeEnum.getByName(getAccessPointType());
        // 设置 TCP 接入域名，到控制台的实例基本信息中查看
        properties.setProperty(pointTypeEnum.getName(), nameServer);
        // 移动云控制台创建的实例ID
        properties.setProperty(PropertyKeyConst.INSTANCE_ID, instanceId);
        // 移动云控制台创建的实例名
        properties.put(PropertyKeyConst.InstanceName, instanceName);
        // AccessKey身份验证，移动云控制台创建
        properties.put(PropertyKeyConst.AccessKey, accessKey);
        // SecretKey身份验证，移动云控制台创建
        properties.put(PropertyKeyConst.SecretKey, secretKey);
        // 设置是否保存消息轨迹
        properties.put(PropertyKeyConst.MsgTraceSwitch, true);
        // 设置是否需要鉴权
        properties.put(PropertyKeyConst.AuthenticationRequired, "true");
        // 设置发送超时时间，单位毫秒
        properties.setProperty(PropertyKeyConst.SendMsgTimeoutMillis, sendMsgTimeoutMillis);

        return properties;
    }


    @Override
    public void destroy() throws Exception {
        // default implementation ignored
    }

    @Override
    public void afterPropertiesSet() throws Exception {
       // default implementation ignored
    }
}
