package com.zyhl.yun.api.outer.messaging.producer;

import com.chinamobile.tuxedo.sdk.api.Producer;
import com.chinamobile.tuxedo.sdk.api.PropertyKeyConst;
import com.chinamobile.tuxedo.sdk.api.TuxeFactory;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
@Configuration
public class AiAssistantCompletedProducerInitialize extends BaseProducerInitialize {

	@Value("${rocketmq.producer.ai-assistant-dialogue-completed.groupId}")
	private String groupId;

	private Producer producer;

	@Bean("aiAssistantCompletedProducer")
	public Producer getAiAssistantCompletedProducer() {
		return producer;
	}

	@Override
	public void afterPropertiesSet() {
		Properties properties = getBaseProperties();
		// 移动云控制台创建的Group ID
		properties.setProperty(PropertyKeyConst.GROUP_ID, groupId);
		producer = TuxeFactory.createProducer(properties);
		this.producer.start();
		log.info("AI助手对话完成发送MQ启动成功");
	}

	@Override
	public void destroy() {

		if (producer != null) {
			producer.shutdown();
		}
	}
}
