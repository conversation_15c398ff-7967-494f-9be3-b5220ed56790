package com.zyhl.yun.api.outer.messaging.producer;

import com.chinamobile.tuxedo.sdk.api.Producer;
import com.chinamobile.tuxedo.sdk.api.PropertyKeyConst;
import com.chinamobile.tuxedo.sdk.api.TuxeFactory;
import com.zyhl.yun.api.outer.config.RocketmqProducerProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.Properties;

/**
 * 个人知识库转存任务状态查询
 *
 * <AUTHOR>
 */
@Data
@Slf4j
@Configuration
@EqualsAndHashCode(callSuper = true)
public class KnowledgeTransTaskProducerInitialize extends BaseProducerInitialize {

    @Resource
    private RocketmqProducerProperties rocketmqProducerProperties;

    private Producer producer;

    @Bean("knowledgeDocVectorProducer")
    public Producer getAiAssistantCompletedProducer() {
        return producer;
    }

    @Override
    public void afterPropertiesSet() {
        Properties properties = getBaseProperties();
        // 移动云控制台创建的Group ID
        properties.setProperty(PropertyKeyConst.GROUP_ID, rocketmqProducerProperties.getPersonalKnowledgeTransTask().getGroupId());
        producer = TuxeFactory.createProducer(properties);
        this.producer.start();
        log.info("个人知识库转存任务状态查询发送MQ启动成功");
    }

    @Override
    public void destroy() {

        if (producer != null) {
            producer.shutdown();
        }
    }
}
