package com.zyhl.yun.api.outer.messaging.producer;

import com.chinamobile.tuxedo.sdk.api.Producer;
import com.chinamobile.tuxedo.sdk.api.PropertyKeyConst;
import com.chinamobile.tuxedo.sdk.api.TuxeFactory;
import com.zyhl.yun.api.outer.config.RocketmqProducerProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.Properties;

/**
 * className: KnowledgeDispatchTaskProducerInitialize
 * description: 知识库向量化提取任务生产者初始化
 *
 * <AUTHOR>
 * @date 2025/2/10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
@Configuration
public class KnowledgeDispatchTaskProducerInitialize extends BaseProducerInitialize {

    @Resource
    private RocketmqProducerProperties rocketmqProducerProperties;

    private Producer producer;

    @Bean("knowledgeVectorDispatchProducer")
    public Producer getKnowledgeVectorDispatchProducer() {
        return producer;
    }

    @Override
    public void afterPropertiesSet() {
        Properties properties = getBaseProperties();
        // 移动云控制台创建的Group ID
        properties.setProperty(PropertyKeyConst.GROUP_ID, rocketmqProducerProperties.getPersonalKnowledgeDispatchTask().getGroupId());
        producer = TuxeFactory.createProducer(properties);
        this.producer.start();
        log.info("个人知识库向量化提取发送MQ启动成功");
    }

    @Override
    public void destroy() {

        if (producer != null) {
            producer.shutdown();
        }
    }
}
