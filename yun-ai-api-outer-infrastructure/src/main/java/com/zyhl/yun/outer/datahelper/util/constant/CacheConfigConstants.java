package com.zyhl.yun.outer.datahelper.util.constant;

/**
 * 缓存配置常量
 * @author: wu<PERSON><PERSON><PERSON>
 */
public interface CacheConfigConstants {

    /**
     * cache manager
     */
    String REDIS_CACHE = "redisCacheManager";

    /** 序列化方式为Jackson2JsonRedisSerializer */
    String BASE_RESULT_CACHE = RedisConstants.PREFIX + "baseResultCache";

    /** 以下序列化为通用序列化 */
    String EXPIRE_15_SECONDS_CACHE = RedisConstants.PREFIX + "expire15SecondCache";

    String EXPIRE_30_SECONDS_CACHE = RedisConstants.PREFIX + "expire30SecondCache";

    String EXPIRE_60_SECONDS_CACHE = RedisConstants.PREFIX + "expire60SecondCache";

    String EXPIRE_90_SECONDS_CACHE = RedisConstants.PREFIX + "expire90SecondCache";

    String EXPIRE_150_SECONDS_CACHE = RedisConstants.PREFIX + "expire150SecondCache";

    String EXPIRE_300_SECONDS_CACHE = RedisConstants.PREFIX + "expire300SecondCache";

    String EXPIRE_LONG_CACHE = RedisConstants.PREFIX + "expireLongTimeCache";

    String AI_PRE_USER = RedisConstants.PREFIX + "user:auth:";

    /**
     * 30分钟过期
     */
    String EXPIRE_30_MINUTES_CACHE = RedisConstants.PREFIX + "expire30MinuteCache";

}
