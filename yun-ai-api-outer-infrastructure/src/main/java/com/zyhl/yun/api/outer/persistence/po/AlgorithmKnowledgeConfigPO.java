package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 公共知识库配置表-PO
 * @Author: WeiJingKun
 */
@Data
@TableName("algorithm_knowledge_config")
public class AlgorithmKnowledgeConfigPO {
    /**
     * id
     */
    @TableId
    private String id;

    /**
     * 知识库名称
     */
    @TableField("name")
    private String name;

    /**
     * 知识库描述
     */
    @TableField("description")
    private String description;

    /**
     * 配置json格式
     * {
     * fileTypes:".docx/.xlsx/.csv/.txt/.pptx", //文档类型
     * splitSize:"1024",  // 文档解析（切片大小）
     * algorithmGroupCode:4 // 文档向量化算法组-公共知识库
     * }
     */
    @TableField("config")
    private String config;

    /**
     * 创建人id
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新人id
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 创建时间，默认值用current_timestamp
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间，默认值用current_timestamp
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 删除标识，0--正常；1--已删除
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 是否生效，0--不生效；1--已生效
     */
    @TableField("is_effect")
    private Integer isEffect;
}
