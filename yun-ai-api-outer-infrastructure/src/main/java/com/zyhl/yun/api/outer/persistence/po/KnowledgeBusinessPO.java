package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * 知识库业务映射表
 * <AUTHOR>
 */
@Data
@Builder
@TableName("algorithm_knowledge_business")
public class KnowledgeBusinessPO {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 知识库的标识
     * common 公共知识库
     * customer 客服
     */
    @TableField("base_id")
    private String baseId;

    /**
     * xiaotian 云盘-小天
     * yunmail  邮箱
     */
    @TableField("business_code")
    private String businessCode;

    /**
     * 1 打开（默认）
     * 0 关闭
     */
    @TableField("open")
    private int open;

    /**
     * 创建人Id
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新人Id
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;
}
