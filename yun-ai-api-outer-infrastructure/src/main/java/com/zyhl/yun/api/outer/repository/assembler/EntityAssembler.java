package com.zyhl.yun.api.outer.repository.assembler;


import com.zyhl.yun.api.outer.domain.entity.AiPopUpProtocolEntity;
import com.zyhl.yun.api.outer.domain.entity.AiToolsAccreditEntity;
import com.zyhl.yun.api.outer.domain.entity.AIToolsConsumeRecordEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiPromptEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiRegisterEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatMessageEntity;
import com.zyhl.yun.api.outer.persistence.po.AiPopUpProtocolPo;
import com.zyhl.yun.api.outer.persistence.po.AiToolsAccreditPo;
import com.zyhl.yun.api.outer.persistence.po.AIToolsConsumeRecordPO;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmAiPromptPO;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmAiRegisterPO;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmChatContentPO;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmChatMessagePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/11/15 19:44
 */
@Mapper(componentModel = "spring")
public interface EntityAssembler {

	EntityAssembler INSTANCE = Mappers.getMapper(EntityAssembler.class);

	/**
	 * toAIToolsAccreditEntity
	 * @param po po
	 * @return entity
	 */
	AiToolsAccreditEntity toAiToolsAccreditEntity(AiToolsAccreditPo po);

	/**
	 * toAIToolsAccreditPO
	 * @param entity entity
	 * @return po
	 */
	AiToolsAccreditPo toAiToolsAccreditPo(AiToolsAccreditEntity entity);

	/**
	 * AI弹窗协议配置-PO -> AI弹窗协议配置-Entity
	 * @param po po
	 * @return entity
	 */
	AiPopUpProtocolEntity toAiPopUpProtocolEntity(AiPopUpProtocolPo po);

	/**
	 * toAlgorithmChatMessagePO
	 * @param entity entity
	 * @return po
	 */
	AlgorithmChatMessagePO toAlgorithmChatMessagePo(AlgorithmChatMessageEntity entity);

	/**
	 * toAlgorithmChatMessageEntity
	 * @param po po
	 * @return entity
	 */
	AlgorithmChatMessageEntity toAlgorithmChatMessageEntity(AlgorithmChatMessagePO po);

	/**
	 * toAlgorithmChatMessageEntityList
	 * @param poList poList
	 * @return List - entity
	 */
	List<AlgorithmChatMessageEntity> toAlgorithmChatMessageEntityList(List<AlgorithmChatMessagePO> poList);

	/**
	 * toAlgorithmChatContentPO
	 * @param entity entity
	 * @return po
	 */
	AlgorithmChatContentPO toAlgorithmChatContentPo(AlgorithmChatContentEntity entity);

	/**
	 * toAlgorithmChatContentEntity
	 * @param po po
	 * @return entity
	 */
	AlgorithmChatContentEntity toAlgorithmChatContentEntity(AlgorithmChatContentPO po);

	/**
	 * toAlgorithmChatContentList
	 * @param poList poList
	 * @return List - entity
	 */
	List<AlgorithmChatContentEntity> toAlgorithmChatContentList(List<AlgorithmChatContentPO> poList);

	/**
	 * toAlgorithmAiRegisterPO
	 * @param entity entity
	 * @return po
	 */
	AlgorithmAiRegisterPO toAlgorithmAiRegisterPo(AlgorithmAiRegisterEntity entity);

	/**
	 * toAlgorithmAiRegisterEntity
	 * @param entity po
	 * @return entity
	 */
	AlgorithmAiRegisterEntity toAlgorithmAiRegisterEntity(AlgorithmAiRegisterPO entity);

	/**
	 * AI提示词配置-PO -> AI提示词配置-Entity
	 *
	 * @param aiPromptPOList the aiPromptPOList
	 * @return {@link List<AlgorithmAiPromptEntity>}
	 * <AUTHOR>
	 * @date 2024-6-28 13:41
	 */
	List<AlgorithmAiPromptEntity> toAlgorithmAiPromptEntityList(List<AlgorithmAiPromptPO> aiPromptPOList);

	/**
	 * AI消费记录-PO -> AI消费记录-Entity
	 *
	 * @param recordPOList the recordPOList
	 * @return {@link List< AIToolsConsumeRecordEntity>}
	 * <AUTHOR>
	 * @date 2024-8-16 18:37
	 */
	List<AIToolsConsumeRecordEntity> toAIToolsConsumeRecordEntityList(List<AIToolsConsumeRecordPO> recordPOList);

	/**
	 * AI消费记录-Entity -> AI消费记录-PO
	 *
	 * @param consumeRecord the consumeRecord
	 * @return {@link AIToolsConsumeRecordPO}
	 * <AUTHOR>
	 * @date 2024-8-16 19:36
	 */
	AIToolsConsumeRecordPO toAIToolsConsumeRecordPO(AIToolsConsumeRecordEntity consumeRecord);
}

