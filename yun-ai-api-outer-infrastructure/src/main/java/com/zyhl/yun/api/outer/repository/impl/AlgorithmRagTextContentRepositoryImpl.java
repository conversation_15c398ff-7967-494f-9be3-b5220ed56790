package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.collection.CollUtil;
import com.zyhl.hcy.yun.ai.common.base.hbase.domain.datahelper.repository.HbaseRepository;
import com.zyhl.yun.api.outer.domain.entity.knowledge.AlgorithmRagTextContentEntity;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmRagTextContentPO;
import com.zyhl.yun.api.outer.repository.AlgorithmRagTextContentRepository;
import com.zyhl.yun.api.outer.repository.assembler.AlgorithmRagTextContentAssembler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description 知识库资源的正文内容HBASE查询
 * @date 2025/4/17 18:07
 */
@Slf4j
@Repository
public class AlgorithmRagTextContentRepositoryImpl implements AlgorithmRagTextContentRepository {

    @Resource
    private HbaseRepository hbaseRepository;

    @Resource
    private AlgorithmRagTextContentAssembler assembler;


    @Override
    public AlgorithmRagTextContentEntity getByRowKey(String rowKey) {
        long start = System.currentTimeMillis();
        List<AlgorithmRagTextContentPO> list = hbaseRepository.selectList(Collections.singletonList(rowKey), AlgorithmRagTextContentPO.class);
        log.info("【hbase】查询记录，rowKey:{}，耗时：{}", rowKey, System.currentTimeMillis() - start);
        if (CollUtil.isEmpty(list)) {
            log.info("【hbase】数据不存在，rowKey:{}", rowKey);
            return null;
        }
        return assembler.toAlgorithmRagTextContentEntity(list.get(0));
    }

}
