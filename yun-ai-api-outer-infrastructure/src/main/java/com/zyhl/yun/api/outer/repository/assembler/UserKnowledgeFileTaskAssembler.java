package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileTaskEntity;
import com.zyhl.yun.api.outer.persistence.po.UserKnowledgeFileTaskPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 类转换 UserKnowledgeFileTaskEntity <--> UserKnowledgeFileTaskPO
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface UserKnowledgeFileTaskAssembler {

    UserKnowledgeFileTaskAssembler INSTANCE = Mappers.getMapper(UserKnowledgeFileTaskAssembler.class);

    /**
     * po转entity
     * @param po po
     * @return entity
     */
    UserKnowledgeFileTaskEntity toUserKnowledgeFileTaskEntityEntity(UserKnowledgeFileTaskPO po);

    /**
     * po转entity
     * @param poList poList
     * @return entity列表
     */
    List<UserKnowledgeFileTaskEntity> toUserKnowledgeFileTaskEntityList(List<UserKnowledgeFileTaskPO> poList);

    /**
     * entity转po
     * @param entity po
     * @return po
     */
    UserKnowledgeFileTaskPO toUserKnowledgeFileTaskPo(UserKnowledgeFileTaskEntity entity);

}
