package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * AI全网搜黑名单资源表PO
 * 
 * <AUTHOR>
 * @date 2025-04-10
 */
@Data
@TableName("algorithm_ai_black_resource")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AlgorithmAiBlackResourcePO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@TableId(value = "id")
	private Long id;

	/**
	 * 资源名称
	 */
	@TableField(value = "resource_name")
	private String resourceName;

	/**
	 * 类型
	 */
	@TableField(value = "resource_type")
	private String resourceType;

	/**
	 * 风险类型
	 */
	@TableField(value = "risk_type")
	private String riskType;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time")
	private Date updateTime;

	/**
	 * 默认是 0 原有黑名单资源，1，国内影视资源；2，国内演员，3，国内导演
	 */
	@TableField(value = "shielded_type")
	private Integer shieldedType;

}
