package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiBlackResourceEntity;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmAiBlackResourcePO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * className:BlackResourceAssembler
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface BlackResourceAssembler {

	/**
	 * po转entity
	 *
	 * @param pos po
	 * @return entity
	 */
	List<AlgorithmAiBlackResourceEntity> toBlackResourceEntityList(List<AlgorithmAiBlackResourcePO> pos);

	/**
	 * po转entity
	 *
	 * @param po po
	 * @return entity
	 */
	AlgorithmAiBlackResourceEntity toIntentionInfoEntity(AlgorithmAiBlackResourcePO po);

	/**
	 * entity转po
	 *
	 * @param entity entity
	 * @return po
	 */
	AlgorithmAiBlackResourcePO toIntentionInfoPO(AlgorithmAiBlackResourceEntity entity);

}
