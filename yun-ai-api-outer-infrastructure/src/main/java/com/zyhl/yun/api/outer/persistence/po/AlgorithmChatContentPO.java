package com.zyhl.yun.api.outer.persistence.po;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @data 2024/2/29 14:04
 */
@Data
@TableName("algorithm_chat_content")
public class AlgorithmChatContentPO {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;
    /**
     * 应用id
     */
    @TableField("application_id")
    private Long applicationId;
    /**
     * 应用类型
     * @see com.zyhl.yun.api.outer.enums.ApplicationTypeEnum
     */
    @TableField("application_type")
    private String applicationType;
    /**
     * 业务类型
     * 详见，配置文件：source-channels
     */
    @TableField("business_type")
    private String businessType;
    /**
     * 会话ID
     */
    @TableField("session_id")
    private Long sessionId;
    /**
     * 任务id
     */
    @TableField("task_id")
    private Long taskId;
    /**
     * 对话类型;0:对话历史记录,1:智囊历史记录, 2:任务对话历史记录
     */
    @TableField("talk_type")
    private Integer talkType;
    /**
     * 资源类型;0-无，1 邮件， 2 笔记， 3 图片
     */
    @TableField("resource_type")
    private Integer resourceType;
    /**
     * 工具指令;对接意图指令
     */
    @TableField("tools_command")
    private String toolsCommand;
    /**
     * 工具指令;对接意图指令-子意图
     */
    @TableField("sub_tools_command")
    private String subToolsCommand;
    /**
     * 模型类型;模型 qwen：通义千问，xfyun：讯飞星火大模型
     */
    @TableField("model_type")
    private String modelType;
    /**
     * 输入内容;输入文本内容
     */
    @TableField("in_content")
    private String inContent;
    /**
     * 输入时间
     */
    @TableField("in_audit_time")
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date inAuditTime;
    /**
     * 输入内容审批结果;状态码：2通过，其他失败
     */
    @TableField("in_audit_status")
    private Integer inAuditStatus;
    /**
     * 输入资源ID;（笔记/邮件/图片ID；纯文本时为空）
     */
    @TableField("in_resource_id")
    private String inResourceId;
    /**
     * 输出内容
     */
    @TableField("out_content")
    private String outContent;
    /**
     * 输出资源ID;（笔记/邮件/图片ID；纯文本时为空）
     */
    @TableField("out_resource_id")
    private String outResourceId;
    /**
     * 输出文本类型：1--普通文本（默认），2--富文本
     */
    @TableField("out_content_type")
    private String outContentType;
    /**
     * 输出时间
     */
    @TableField("out_audit_time")
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date outAuditTime;
    /**
     * 输出内容审批结果;状态码：2通过，其他失败
     */
    @TableField("out_audit_status")
    private Integer outAuditStatus;
    /**
     * 旧图片文件id;当用户从主平台迁移到云空间时，图片id会发生变化，这里存的是迁移前的图片id,格式：用户选择的旧文件id1,模型返回的旧文件id2
     */
    @TableField("old_resource_id")
    private String oldResourceId;
    /**
     * 渠道来源
     */
    @TableField("source_channel")
    private String sourceChannel;
    /**
     * paas底座平台编码;0 华为主平台(ose) 1 阿里pds 2 华为云空间 3 云能dsp
     */
    @TableField("belongs_platform")
    private Integer belongsPlatform;
    /**
     * 扩展信息 json格式
     */
    @TableField("ext_info")
    private String extInfo;
    /**
     * 迁移标识;1-迁移数据，0-新数据
     */
    @TableField("migration_flag")
    private Integer migrationFlag;
    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date updateTime;
    /**
     * 是否删除;（1是0否）
     */
    @TableField("del_flag")
    private Integer delFlag;
    /**
     * 提示词，比如：总结概要
     */
    @TableField("prompt")
    private String prompt;
    /**
     * 对话状态
     */
    @TableField("chat_status")
    private Integer chatStatus;
    /**
     * 业务场景标识：app_writemail_ai（邮箱APP-写信AI）
     */
    @TableField("scene_tag")
    private String sceneTag;
    /**
     * 命令类型：1--普通命令（默认），2--自动命令
     */
    @TableField("command_type")
    private Integer commandType;

    /**
     * 对话结果推荐信息（json格式）
     */
    @TableField("recommend_info")
    private String recommendInfo;
    
    /**
     * 对话结果推荐信息-中部（json格式）
     */
    @TableField("middle_recommend_info")
    private String middleRecommendInfo;
}
