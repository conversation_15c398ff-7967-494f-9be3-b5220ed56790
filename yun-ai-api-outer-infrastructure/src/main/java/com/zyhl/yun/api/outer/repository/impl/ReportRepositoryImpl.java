package com.zyhl.yun.api.outer.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.yun.api.outer.domain.entity.ReportInfoEntity;
import com.zyhl.yun.api.outer.persistence.mapper.ReportInfoMapper;
import com.zyhl.yun.api.outer.persistence.po.ReportInfoPO;
import com.zyhl.yun.api.outer.repository.ReportRepository;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.zyhl.yun.api.outer.constants.Const.*;

/**
 * <AUTHOR>
 */
@Service
public class ReportRepositoryImpl extends ServiceImpl<ReportInfoMapper, ReportInfoPO> implements ReportRepository {


    @Override
    public Integer save(ReportInfoEntity entity) {
        ReportInfoPO po = new ReportInfoPO();
        po.setUserId(entity.getUserId());
        po.setBusinessType(entity.getBusinessType());
        po.setContactInfo(entity.getContactInfo());
        po.setProblemDescription(entity.getDescribe());
        po.setReason(entity.getReasons());
        if (entity.getObjectKeys() != null) {
            if (entity.getObjectKeys().length > 0) {
                po.setPicture1(entity.getObjectKeys()[0]);
            }
            if (entity.getObjectKeys().length > 1) {
                po.setPicture2(entity.getObjectKeys()[1]);
            }
            if (entity.getObjectKeys().length > NUM_2) {
                po.setPicture3(entity.getObjectKeys()[2]);
            }
            if (entity.getObjectKeys().length > NUM_3) {
                po.setPicture4(entity.getObjectKeys()[3]);
            }
            if (entity.getObjectKeys().length > NUM_4) {
                po.setPicture5(entity.getObjectKeys()[4]);
            }
        }
        entity.setCreateTime(new Date());
        return baseMapper.insert(po);
    }
}
