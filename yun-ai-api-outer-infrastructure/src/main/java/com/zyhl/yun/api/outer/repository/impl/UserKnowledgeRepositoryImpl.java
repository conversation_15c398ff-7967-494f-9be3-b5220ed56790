package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.enums.knowledge.BizTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeSelectedEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeStatusEnum;
import com.zyhl.yun.api.outer.persistence.mapper.AlgorithmUserKnowledgeMapper;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmUserKnowledgePO;
import com.zyhl.yun.api.outer.repository.UserKnowledgeRepository;
import com.zyhl.yun.api.outer.repository.assembler.UserKnowledgeAssembler;

import java.util.ArrayList;
import java.util.Collections;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 个人知识库
 *
 * <AUTHOR>
 * @date 2025/04/14
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class UserKnowledgeRepositoryImpl extends ServiceImpl<AlgorithmUserKnowledgeMapper, AlgorithmUserKnowledgePO> implements UserKnowledgeRepository {

    private final UserKnowledgeAssembler assembler;

    @Override
    public int add(UserKnowledgeEntity entity) {
        return baseMapper.insert(assembler.toAlgorithmUserKnowledgePO(entity));
    }

    @Override
    public List<UserKnowledgeEntity> getInfoListByIds(List<Long> ids) {
        if (ObjectUtil.isNotEmpty(ids)) {
            QueryWrapper<AlgorithmUserKnowledgePO> wrapper = new QueryWrapper<>();
            wrapper.lambda()
                    .eq(AlgorithmUserKnowledgePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                    .in(AlgorithmUserKnowledgePO::getId, ids);
            List<AlgorithmUserKnowledgePO> list = baseMapper.selectList(wrapper);
            return assembler.toUserKnowledgeEntityList(list);
        }
        return Collections.emptyList();
    }

    @Override
    public UserKnowledgeEntity selectInfoById(@NotNull Long id) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<AlgorithmUserKnowledgePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(AlgorithmUserKnowledgePO::getId, id);
            return assembler.toUserKnowledgeEntity(baseMapper.selectOne(lambdaQueryWrapper));
        } catch (Exception e) {
            log.error("selectInfoById id:{} error:", id, e);
        }
        return null;
    }

    @Override
    public List<UserKnowledgeEntity> selectByIds(List<Long> idList) {
        if (ObjectUtil.isEmpty(idList)) {
            return Collections.emptyList();
        }

        // 查询数据
        List<AlgorithmUserKnowledgePO> list = this.lambdaQuery()
                .in(AlgorithmUserKnowledgePO::getId, idList)
                .eq(AlgorithmUserKnowledgePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                .list();

        return assembler.toUserKnowledgeEntityList(list);
    }


    @Override
    public List<UserKnowledgeEntity> getAllByUserId(String userId) {
        // 查询数据
        List<AlgorithmUserKnowledgePO> list = this.lambdaQuery()
                .eq(AlgorithmUserKnowledgePO::getUserId, userId)
                .list();
        return assembler.toUserKnowledgeEntityList(list);
    }

    @Override
    public void updateSelected(String userId, List<String> baseIdList) {
        // 先更新为未选择
        this.lambdaUpdate()
                .eq(AlgorithmUserKnowledgePO::getUserId, userId)
                .eq(AlgorithmUserKnowledgePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                .eq(AlgorithmUserKnowledgePO::getSelected, KnowledgeSelectedEnum.SELECTED.getStatus())
                .set(AlgorithmUserKnowledgePO::getSelected, KnowledgeSelectedEnum.NOT_SELECTED.getStatus())
                .set(AlgorithmUserKnowledgePO::getUpdateTime, new Date())
                .update();

        // 再更新为已选择
        if (ObjectUtil.isNotEmpty(baseIdList)) {
            this.lambdaUpdate()
                    .eq(AlgorithmUserKnowledgePO::getUserId, userId)
                    .eq(AlgorithmUserKnowledgePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                    .in(AlgorithmUserKnowledgePO::getId, baseIdList)
                    .set(AlgorithmUserKnowledgePO::getSelected, KnowledgeSelectedEnum.SELECTED.getStatus())
                    .set(AlgorithmUserKnowledgePO::getUpdateTime, new Date())
                    .update();
        }
    }

    @Override
    public int update(UserKnowledgeEntity entity) {
        return baseMapper.updateById(assembler.toAlgorithmUserKnowledgePO(entity));
    }

    @Override
    public UserKnowledgeEntity selectById(Long id) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<AlgorithmUserKnowledgePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            // 逻辑删除的不查询出来
            lambdaQueryWrapper.eq(AlgorithmUserKnowledgePO::getId, id)
                    .eq(AlgorithmUserKnowledgePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus());
            return assembler.toUserKnowledgeEntity(baseMapper.selectOne(lambdaQueryWrapper));
        } catch (Exception e) {
            log.error("selectById id:{} error:", id, e);
        }
        return null;
    }

    @Override
    public UserKnowledgeEntity selectByIdAndUserId(Long id, String userId) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<AlgorithmUserKnowledgePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(AlgorithmUserKnowledgePO::getId, id)
                    .eq(AlgorithmUserKnowledgePO::getUserId, userId)
                    // 逻辑删除的不查询出来
                    .eq(AlgorithmUserKnowledgePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus());
            return assembler.toUserKnowledgeEntity(baseMapper.selectOne(lambdaQueryWrapper));
        } catch (Exception e) {
            log.error("selectById id:{} error:", id, e);
        }
        return null;
    }

    @Override
    public UserKnowledgeEntity selectFirstOne(String userId) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<AlgorithmUserKnowledgePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(AlgorithmUserKnowledgePO::getUserId, userId)
                    .notIn(AlgorithmUserKnowledgePO::getDelFlag, KnowledgeStatusEnum.DELETED.getStatus())
                    .last("LIMIT 1");
            return assembler.toUserKnowledgeEntity(baseMapper.selectOne(lambdaQueryWrapper));
        } catch (Exception e) {
            log.error("selectFirstOne userId:{} error:", userId, e);
        }
        return null;
    }

    @Override
    public List<UserKnowledgeEntity> selectByUserId(String userId) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<AlgorithmUserKnowledgePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(AlgorithmUserKnowledgePO::getUserId, userId)
                    // 逻辑删除的不查询出来
                    .eq(AlgorithmUserKnowledgePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                    .eq(AlgorithmUserKnowledgePO::getBizType, BizTypeEnum.DEFAULT.getCode());
            return assembler.toUserKnowledgeEntityList(baseMapper.selectList(lambdaQueryWrapper));
        } catch (Exception e) {
            log.error("selectByUserId userId:{} error:", userId, e);
        }
        return Collections.emptyList();
    }

    @Override
    public UserKnowledgeEntity selectByName(String name, String userId) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<AlgorithmUserKnowledgePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(AlgorithmUserKnowledgePO::getName, name)
                    .eq(AlgorithmUserKnowledgePO::getUserId, userId)
                    // 逻辑删除的不查询出来
                    .eq(AlgorithmUserKnowledgePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus());
            return assembler.toUserKnowledgeEntity(baseMapper.selectOne(lambdaQueryWrapper));
        } catch (Exception e) {
            log.error("selectByName name:{} error:", name, e);
        }
        return null;
    }

    @Override
    public void deleteByIdAndUserId(Long id, String userId) {
        // 逻辑删除
        this.lambdaUpdate()
                .eq(AlgorithmUserKnowledgePO::getId, id)
                .eq(AlgorithmUserKnowledgePO::getUserId, userId)
                .set(AlgorithmUserKnowledgePO::getUpdateTime, new Date())
                .set(AlgorithmUserKnowledgePO::getDelFlag, KnowledgeStatusEnum.DELETED.getStatus())
                .update();
    }

    @Override
    public PageInfo<UserKnowledgeEntity> list(String userId, Integer baseType, PageInfoDTO pageInfo) {
        PageHelper.offsetPage(Integer.parseInt(pageInfo.getPageCursor()), pageInfo.getPageSize(), pageInfo.isNeedTotal());

        String sql = "select knowledge_id from algorithm_user_knowledge_invite where user_id = '" + userId + "'";

        // 查询条件
        List<AlgorithmUserKnowledgePO> list = new ArrayList<>();
        if (ObjectUtil.isEmpty(baseType)) {
            // 查出个人知识库和所在的共享知识库
            list = this.lambdaQuery()
                    .eq(AlgorithmUserKnowledgePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                    .eq(AlgorithmUserKnowledgePO::getUserId, userId)
                    .or()
                    //子查询
                    .inSql(AlgorithmUserKnowledgePO::getId, sql)
                    .orderByDesc(AlgorithmUserKnowledgePO::getUpdateTime)
                    .orderByDesc(AlgorithmUserKnowledgePO::getId)
                    .list();
        } else if (baseType == 1) {
            // 查出个人知识库
            list = this.lambdaQuery()
                    .eq(AlgorithmUserKnowledgePO::getUserId, userId)
                    .eq(AlgorithmUserKnowledgePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                    .orderByDesc(AlgorithmUserKnowledgePO::getUpdateTime)
                    .orderByDesc(AlgorithmUserKnowledgePO::getId)
                    .list();
        } else if (baseType == 2) {
            // 查出共享知识库
            list = this.lambdaQuery()
                    .eq(AlgorithmUserKnowledgePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                    .inSql(AlgorithmUserKnowledgePO::getId, sql)
                    .orderByDesc(AlgorithmUserKnowledgePO::getUpdateTime)
                    .orderByDesc(AlgorithmUserKnowledgePO::getId)
                    .list();
        } else if (baseType == 3) {
            // 查出公共知识库
        }

        // 数据转换
        Page<UserKnowledgeEntity> page = new Page<>();
        BeanUtils.copyProperties(list, page);
        page.addAll(assembler.toUserKnowledgeEntityList(list));
        return PageInfo.of(page);
    }

    @Override
    public UserKnowledgeEntity selectByKnowledgeId(Long knowledgeId) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<AlgorithmUserKnowledgePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(AlgorithmUserKnowledgePO::getId, knowledgeId)
                    .eq(AlgorithmUserKnowledgePO::getOpenLevel, KnowledgeStatusEnum.OPEN.getStatus())
                    // 逻辑删除的不查询出来
                    .eq(AlgorithmUserKnowledgePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus());
            return assembler.toUserKnowledgeEntity(baseMapper.selectOne(lambdaQueryWrapper));
        } catch (Exception e) {
            log.error("selectByKnowledgeId knowledgeId:{} error:", knowledgeId, e);
        }
        return null;
    }

    @Override
    public int count(Long baseId, String userId) {
        return this.lambdaQuery()
                .eq(AlgorithmUserKnowledgePO::getId, baseId)
                .eq(AlgorithmUserKnowledgePO::getUserId, userId)
                .eq(AlgorithmUserKnowledgePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                .count();
    }

    @Override
    public int countByUserId(String userId) {
        return this.lambdaQuery()
                .eq(AlgorithmUserKnowledgePO::getUserId, userId)
                .eq(AlgorithmUserKnowledgePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                .eq(AlgorithmUserKnowledgePO::getBizType, BizTypeEnum.DEFAULT.getCode())
                .count();
    }

    @Override
    public int countByBaseId(Long baseId) {
        return this.lambdaQuery()
                .eq(AlgorithmUserKnowledgePO::getId, baseId)
                .eq(AlgorithmUserKnowledgePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                .count();
    }

    @Override
    public List<UserKnowledgeEntity> getSelectedBase(String userId) {

        String sql = "select knowledge_id from algorithm_user_knowledge_invite where user_id = '" + userId + "' and status = 1 and selected = " + KnowledgeSelectedEnum.SELECTED.getStatus();

        long start = System.currentTimeMillis();
        List<AlgorithmUserKnowledgePO> list = this.lambdaQuery()
                .eq(AlgorithmUserKnowledgePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                .and(item -> item
                        .and(owner -> owner
                                .eq(AlgorithmUserKnowledgePO::getUserId, userId)
                                .eq(AlgorithmUserKnowledgePO::getSelected, KnowledgeSelectedEnum.SELECTED.getStatus())
                        )
                        .or(join -> join
                                .inSql(AlgorithmUserKnowledgePO::getId, sql)
                                .eq(AlgorithmUserKnowledgePO::getOpenLevel, KnowledgeStatusEnum.OPEN.getStatus())
                        )

                )
                .list();

        log.info("【tidb】查询选中的知识库（自己的 + 加入的），耗时：{}", System.currentTimeMillis() - start);
        return assembler.toUserKnowledgeEntityList(list);
    }

    @Override
    public UserKnowledgeEntity selectNoteSync(String userId, Integer bizType) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<AlgorithmUserKnowledgePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            // 逻辑删除的不查询出来
            lambdaQueryWrapper.eq(AlgorithmUserKnowledgePO::getUserId, userId)
                    .eq(AlgorithmUserKnowledgePO::getBizType, bizType)
                    .eq(AlgorithmUserKnowledgePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus());
            return assembler.toUserKnowledgeEntity(baseMapper.selectOne(lambdaQueryWrapper));
        } catch (Exception e) {
            log.error("查询笔记同步记录异常,fuc:selectNoteSync,param: id:{},bizType:{}, error:", userId, bizType, e);
        }
        return null;
    }

}