package com.zyhl.yun.api.outer.datahelper.util;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundSetOperations;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Component;

import com.zyhl.hcy.plugin.redis.HcyRedisTemplate;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * spring redis 工具类
 * <AUTHOR>
 */
@SuppressWarnings(value = {"unchecked", "rawtypes"})
@Component
@Slf4j
public class RedisService {
	
	@Autowired
	public HcyRedisTemplate redisTemplate;
	
	@Resource
	private RedissonClient redissonClient;
	
	/**
	 * 缓存基本的对象，Integer、String、实体类等
	 *
	 * @param key   缓存的键值
	 * @param value 缓存的值
	 */
	public <T> void setCacheObject(final String key, final T value) {
		redisTemplate.opsForValue().set(key, value);
	}
	
	/**
	 * 缓存基本的对象，Integer、String、实体类等
	 *
	 * @param key      缓存的键值
	 * @param value    缓存的值
	 * @param timeout  时间
	 * @param timeUnit 时间颗粒度
	 */
	public <T> void setCacheObject(final String key, final T value, final Long timeout, final TimeUnit timeUnit) {
		redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
	}
	
	/**
	 * 设置有效时间
	 *
	 * @param key     Redis键
	 * @param timeout 超时时间
	 * @return true=设置成功；false=设置失败
	 */
	public boolean expire(final String key, final long timeout) {
		return expire(key, timeout, TimeUnit.SECONDS);
	}
	
	/**
	 * 设置有效时间
	 *
	 * @param key     Redis键
	 * @param timeout 超时时间
	 * @param unit    时间单位
	 * @return true=设置成功；false=设置失败
	 */
	public boolean expire(final String key, final long timeout, final TimeUnit unit) {
		return redisTemplate.expire(key, timeout, unit);
	}
	
	/**
	 * 获取有效时间
	 *
	 * @param key Redis键
	 * @return 有效时间
	 */
	public long getExpire(final String key) {
		return redisTemplate.getExpire(key);
	}
	
	/**
	 * 获取有效时间
	 *
	 * @param key Redis键
	 * @return 有效时间
	 */
	public Long getExpire(final String key, final TimeUnit timeUnit) {
		return redisTemplate.getExpire(key, timeUnit);
	}
	
	/**
	 * 判断 key是否存在
	 *
	 * @param key 键
	 * @return true 存在 false不存在
	 */
	public Boolean hasKey(String key) {
		return redisTemplate.hasKey(key);
	}
	
	/**
	 * 获得缓存的基本对象。
	 *
	 * @param key 缓存键值
	 * @return 缓存键值对应的数据
	 */
	public <T> T getCacheObject(final String key) {
		ValueOperations<String, T> operation = redisTemplate.opsForValue();
		return operation.get(key);
	}
	
	/**
	 * 删除单个对象
	 *
	 * @param key
	 */
	public boolean deleteObject(final String key) {
		return redisTemplate.delete(key);
	}
	
	/**
	 * 删除集合对象
	 *
	 * @param collection 多个对象
	 * @return
	 */
	public long deleteObject(final Collection collection) {
		return redisTemplate.delete(collection);
	}
	
	/**
	 * 缓存List数据
	 *
	 * @param key      缓存的键值
	 * @param dataList 待缓存的List数据
	 * @return 缓存的对象
	 */
	public <T> long setCacheList(final String key, final List<T> dataList) {
		Long count = redisTemplate.opsForList().rightPushAll(key, dataList);
		return count == null ? 0 : count;
	}

	/**
	 * 缓存List数据，并根据最大长度限制List的大小，并设置过期时间
	 * 注意：这里只能单线程使用，不支持并发，并发需要用lua脚本
	 *
	 * @param key        缓存的键值
	 * @param dataList   待缓存的List数据
	 * @param maxLength  List的最大长度
	 * @param expireTime 过期时间（单位：秒）
	 * @return 最终列表的长度
	 */
	public <T> long cacheListWithMaxLengthAndExpire(final String key, final List<T> dataList, final int maxLength,
													final long expireTime) {
		// 批量添加新元素到List的末尾
		Long count = redisTemplate.opsForList().rightPushAll(key, dataList);

		// 设置过期时间
		redisTemplate.expire(key, expireTime, TimeUnit.SECONDS);

		// 如果没有成功插入任何元素，则返回0
		if (count == null || count <= 0) {
			return 0;
		}

		// 如果当前列表长度超过最大长度，将列表从后往前截断，并且返回最大长度
		if (maxLength > 0 && count > maxLength) {
			long startIndex = count - maxLength;
			redisTemplate.opsForList().trim(key, startIndex, -1);
			return maxLength;
		}

		return (null != count) ? count.longValue() : 0;
	}
	
	/**
	 * 获得缓存的list对象
	 *
	 * @param key 缓存的键值
	 * @return 缓存键值对应的数据
	 */
	public <T> List<T> getCacheList(final String key) {
		return redisTemplate.opsForList().range(key, 0, -1);
	}
	
	/**
	 * 缓存Set
	 *
	 * @param key     缓存键值
	 * @param dataSet 缓存的数据
	 * @return 缓存数据的对象
	 */
	public <T> BoundSetOperations<String, T> setCacheSet(final String key, final Set<T> dataSet) {
		BoundSetOperations<String, T> setOperation = redisTemplate.boundSetOps(key);
		Iterator<T> it = dataSet.iterator();
		while (it.hasNext()) {
			setOperation.add(it.next());
		}
		return setOperation;
	}
	
	/**
	 * 获得缓存的set
	 *
	 * @param key
	 * @return
	 */
	public <T> Set<T> getCacheSet(final String key) {
		return redisTemplate.opsForSet().members(key);
	}
	
	/**
	 * 缓存Map
	 *
	 * @param key
	 * @param dataMap
	 */
	public <T> void setCacheMap(final String key, final Map<String, T> dataMap) {
		if (dataMap != null) {
			redisTemplate.opsForHash().putAll(key, dataMap);
		}
	}
	
	/**
	 * 获得缓存的Map
	 *
	 * @param key
	 * @return
	 */
	public <T> Map<String, T> getCacheMap(final String key) {
		return redisTemplate.opsForHash().entries(key);
	}
	
	/**
	 * 往Hash中存入数据
	 *
	 * @param key   Redis键
	 * @param hKey  Hash键
	 * @param value 值
	 */
	public <T> void setCacheMapValue(final String key, final String hKey, final T value) {
		redisTemplate.opsForHash().put(key, hKey, value);
	}
	
	/**
	 * 获取Hash中的数据
	 *
	 * @param key  Redis键
	 * @param hKey Hash键
	 * @return Hash中的对象
	 */
	public <T> T getCacheMapValue(final String key, final String hKey) {
		HashOperations<String, String, T> opsForHash = redisTemplate.opsForHash();
		return opsForHash.get(key, hKey);
	}
	
	/**
	 * 获取多个Hash中的数据
	 *
	 * @param key   Redis键
	 * @param hKeys Hash键集合
	 * @return Hash对象集合
	 */
	public <T> List<T> getMultiCacheMapValue(final String key, final Collection<Object> hKeys) {
		return redisTemplate.opsForHash().multiGet(key, hKeys);
	}
	
	
	/**
	 * 获取自增主键incr
	 */
	public Long incr(String key, long liveTime) {
		RedisAtomicLong entityIdCounter = new RedisAtomicLong(key, Objects.requireNonNull(redisTemplate.getConnectionFactory()));
		long increment = entityIdCounter.getAndIncrement();
		
		//初始设置过期时间
		if (increment == 0 && liveTime > 0) {
			entityIdCounter.expire(liveTime, TimeUnit.SECONDS);
		}
		
		return increment;
	}
	
	/**
	 * 获取自增主键incr
	 */
	public Long incr(String key) {
		RedisAtomicLong entityIdCounter = new RedisAtomicLong(key, Objects.requireNonNull(redisTemplate.getConnectionFactory()));
		return entityIdCounter.getAndIncrement();
	}
	
	public <T> T getAndSet(final String key, T value) {
		T oldValue = null;
		try {
			ValueOperations<String, Object> operations = redisTemplate.opsForValue();
			oldValue = (T) operations.getAndSet(key, value);
		} catch (Exception e) {
			log.error("getAndSet key:{}, error:", key, e);
		}
		return oldValue;
	}
	
	/***
	 * 获取缓存数组对象：对应类型
	 * @param key
	 * @param deskClass
	 * @param <T>
	 * @return
	 */
	private <T> T getCacheList(String key, Class deskClass) {
		String redisValue = this.getCacheObject(key);
		if (null == redisValue) {
			return null;
		}
		T redisObj = (T) JSONUtil.toList(redisValue, deskClass);
		return redisObj;
	}
	
	/**
	 * 获取缓存对象：对应类型
	 *
	 * @param key
	 * @param deskClass
	 * @param <T>
	 * @return
	 */
	private <T> T getCacheObject(String key, Class deskClass) {
		String redisValue = this.getCacheObject(key);
		if (null == redisValue) {
			return null;
		}
		T redisObj = (T) JSONUtil.toBean(redisValue, deskClass);
		return redisObj;
	}
	
	/**
	 * 缓存基本的对象，Integer、String、实体类等
	 *
	 * @param key   缓存的键值
	 * @param value 缓存的值 对象集合等要用fastJosn转成String
	 * @return 缓存的对象
	 */
	private void setCacheObject(String key, Object value, Long expire) {
		String redisValue = null;
		if (null == value) {
			return;
		}
		if (value instanceof String) {
			redisValue = (String) value;
		} else {
			redisValue = JSONUtil.toJsonStr(value);
		}
		ValueOperations<String, String> operation = redisTemplate.opsForValue();
		operation.set(key, redisValue);
		if (null != expire) {
			this.redisTemplate.expire(key, expire, TimeUnit.SECONDS);
		}
	}
	
}
