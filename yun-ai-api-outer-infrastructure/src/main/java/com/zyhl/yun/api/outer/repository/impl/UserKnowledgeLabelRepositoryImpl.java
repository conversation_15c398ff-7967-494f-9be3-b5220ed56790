package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeLabelEntity;
import com.zyhl.yun.api.outer.persistence.mapper.UserKnowledgeLabelMapper;
import com.zyhl.yun.api.outer.persistence.po.UserKnowledgeLabelPO;
import com.zyhl.yun.api.outer.repository.UserKnowledgeLabelRepository;
import com.zyhl.yun.api.outer.repository.assembler.UserKnowledgeLabelAssembler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 知识库标签
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class UserKnowledgeLabelRepositoryImpl extends ServiceImpl<UserKnowledgeLabelMapper, UserKnowledgeLabelPO> implements UserKnowledgeLabelRepository {

    private final UserKnowledgeLabelAssembler assembler;

    private final UidGenerator uidGenerator;


    @Override
    public int add(UserKnowledgeLabelEntity entity) {
        entity.setId(uidGenerator.getUID());
        entity.setOwnerType(OwnerTypeEnum.PERSONAL.getOwnerValue());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());

        // 排序从1开始
        List<UserKnowledgeLabelPO> poList = this.lambdaQuery().eq(UserKnowledgeLabelPO::getUserId, entity.getUserId()).list();
        if (ObjectUtil.isEmpty(poList)) {
            entity.setSort(1);
        } else {
            entity.setSort(poList.stream().map(UserKnowledgeLabelPO::getSort).max(Integer::compare).get() + 1);
        }

        return baseMapper.insert(assembler.toUserKnowledgeLabelPo(entity));
    }

    @Override
    public int delete(Long labelId) {
        return baseMapper.deleteById(labelId);
    }

    @Override
    public int update(UserKnowledgeLabelEntity entity) {
        UserKnowledgeLabelPO po = assembler.toUserKnowledgeLabelPo(entity);
        po.setUpdateTime(new Date());
        return baseMapper.updateById(po);
    }

    @Override
    public UserKnowledgeLabelEntity selectById(Long id) {

        UserKnowledgeLabelPO po = baseMapper.selectById(id);

        return assembler.toUserKnowledgeLabelEntity(po);
    }

    @Override
    public UserKnowledgeLabelEntity selectByName(String userId, String labelName) {
        // 查询条件
        LambdaQueryChainWrapper<UserKnowledgeLabelPO> wrapper = this.lambdaQuery()
                .eq(UserKnowledgeLabelPO::getUserId, userId)
                .eq(UserKnowledgeLabelPO::getLabel, labelName);

        return assembler.toUserKnowledgeLabelEntity(wrapper.one());
    }


    @Override
    public List<UserKnowledgeLabelEntity> selectByUserId(String userId) {
        // 查询条件
        LambdaQueryChainWrapper<UserKnowledgeLabelPO> wrapper = this.lambdaQuery()
                .eq(UserKnowledgeLabelPO::getUserId, userId)
                .orderByDesc(UserKnowledgeLabelPO::getSort);

        // 查询数据
        List<UserKnowledgeLabelPO> poList = wrapper.list();

        return assembler.toUserKnowledgeLabelEntityList(poList);
    }

    @Override
    public int count(String userId) {
        return this.lambdaQuery()
                .eq(UserKnowledgeLabelPO::getUserId, userId)
                .count();
    }
}
