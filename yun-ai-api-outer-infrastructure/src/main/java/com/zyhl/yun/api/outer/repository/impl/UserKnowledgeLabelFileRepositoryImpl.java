package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeLabelFileEntity;
import com.zyhl.yun.api.outer.persistence.mapper.UserKnowledgeLabelFileMapper;
import com.zyhl.yun.api.outer.persistence.po.UserKnowledgeLabelFilePO;
import com.zyhl.yun.api.outer.repository.UserKnowledgeLabelFileRepository;
import com.zyhl.yun.api.outer.repository.assembler.UserKnowledgeLabelFileAssembler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 知识库标签与文件的关系
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class UserKnowledgeLabelFileRepositoryImpl extends ServiceImpl<UserKnowledgeLabelFileMapper, UserKnowledgeLabelFilePO> implements UserKnowledgeLabelFileRepository {

    private final UserKnowledgeLabelFileAssembler assembler;

    private final UidGenerator uidGenerator;


    @Override
    public int add(UserKnowledgeLabelFileEntity entity) {
        entity.setId(uidGenerator.getUID());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        return baseMapper.insert(assembler.toUserKnowledgeLabelFilePo(entity));
    }

    @Override
    public int updateById(UserKnowledgeLabelFileEntity entity) {
        entity.setUpdateTime(new Date());

        baseMapper.updateById(assembler.toUserKnowledgeLabelFilePo(entity));
        return 1;
    }


    @Override
    public int updateLabelId(String userId, Long labelId, Long newLabelId) {
        // 更新
        this.lambdaUpdate()
                .eq(UserKnowledgeLabelFilePO::getUserId, userId)
                .eq(UserKnowledgeLabelFilePO::getLabelId, labelId)
                .set(UserKnowledgeLabelFilePO::getLabelId, newLabelId)
                .update();

        return 1;
    }

    @Override
    public int deleteById(Long id) {
        return baseMapper.deleteById(id);
    }


    @Override
    public void deleteByFileIds(String userId, List<String> fileIds) {
        // 删除
        this.lambdaUpdate()
                .eq(UserKnowledgeLabelFilePO::getUserId, userId)
                .in(UserKnowledgeLabelFilePO::getFileId, fileIds)
                .remove();
    }

    @Override
    public void batchDeleteByUserId(String userId, List<String> fileIds) {
    	// 每次删除的最大文件 ID 数量
        int batchSize = 500; 
        for (int i = 0; i < fileIds.size(); i += batchSize) {
            // 获取当前批次的文件 ID 子列表
            List<String> subFileIds = fileIds.subList(i, Math.min(i + batchSize, fileIds.size()));

            // 执行删除操作
            this.lambdaUpdate()
                    .eq(UserKnowledgeLabelFilePO::getUserId, userId)
                    .in(UserKnowledgeLabelFilePO::getFileId, subFileIds)
                    .remove();
        }
    }

    @Override
    public void deleteByUserId(String userId, Long lableId, String fileId) {
        // 删除
        this.lambdaUpdate()
                .eq(UserKnowledgeLabelFilePO::getUserId, userId)
                .eq(UserKnowledgeLabelFilePO::getLabelId, lableId)
                .eq(UserKnowledgeLabelFilePO::getFileId, fileId)
                .remove();
    }

    @Override
    public UserKnowledgeLabelFileEntity selectOne(String userId, Long labelId, String fileId) {
        // 查询条件
        LambdaQueryChainWrapper<UserKnowledgeLabelFilePO> wrapper = this.lambdaQuery()
                .eq(UserKnowledgeLabelFilePO::getUserId, userId)
                .eq(UserKnowledgeLabelFilePO::getLabelId, labelId)
                .eq(UserKnowledgeLabelFilePO::getFileId, fileId);

        // 查询数据
        UserKnowledgeLabelFilePO po = wrapper.one();

        // 对象转换
        return assembler.toUserKnowledgeLabelFileEntity(po);
    }

    @Override
    public List<UserKnowledgeLabelFileEntity> selectByUserId(String userId, Long labelId, List<String> oldFileIds) {
        // 查询条件
        LambdaQueryChainWrapper<UserKnowledgeLabelFilePO> wrapper = this.lambdaQuery()
                .eq(UserKnowledgeLabelFilePO::getUserId, userId)
                .eq(labelId != null, UserKnowledgeLabelFilePO::getLabelId, labelId)
                .in(CollUtil.isNotEmpty(oldFileIds), UserKnowledgeLabelFilePO::getOldFileId, oldFileIds);

        // 查询数据
        List<UserKnowledgeLabelFilePO> poList = wrapper.list();

        // 对象转换
        return assembler.toUserKnowledgeLabelFileEntityList(poList);
    }

    @Override
    public List<UserKnowledgeLabelFileEntity> selectByUserId(String userId, List<String> fileIds) {
        // 查询条件
        LambdaQueryChainWrapper<UserKnowledgeLabelFilePO> wrapper = this.lambdaQuery()
                .eq(UserKnowledgeLabelFilePO::getUserId, userId)
                .in(CollUtil.isNotEmpty(fileIds), UserKnowledgeLabelFilePO::getFileId, fileIds);

        // 查询数据
        List<UserKnowledgeLabelFilePO> poList = wrapper.list();

        // 对象转换
        return assembler.toUserKnowledgeLabelFileEntityList(poList);
    }


}
