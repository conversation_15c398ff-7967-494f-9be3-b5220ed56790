package com.zyhl.yun.api.outer.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserDriveConfigEntity;
import com.zyhl.yun.api.outer.persistence.mapper.UserDriveConfigMapper;
import com.zyhl.yun.api.outer.persistence.po.UserDriveConfigPO;
import com.zyhl.yun.api.outer.repository.UserDriveConfigRepository;
import com.zyhl.yun.api.outer.repository.assembler.UserDriveConfigAssembler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * 知识库文件
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class UserDriveConfigRepositoryImpl extends ServiceImpl<UserDriveConfigMapper, UserDriveConfigPO> implements UserDriveConfigRepository {

    private final UserDriveConfigAssembler assembler;

    private final UidGenerator uidGenerator;


    @Override
    public UserDriveConfigEntity get(String userId) {
        UserDriveConfigPO po = this.lambdaQuery().eq(UserDriveConfigPO::getUserId, userId).one();
        return assembler.toUserDriveConfigEntityEntity(po);
    }

    @Override
    public void add(UserDriveConfigEntity entity) {
        entity.setId(uidGenerator.getUID());
        if (entity.getOwnerId() == null) {
            entity.setOwnerId(entity.getUserId());
        }
        if (entity.getOwnerType() == null) {
            entity.setOwnerType(OwnerTypeEnum.PERSONAL.getOwnerValue());
        }
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());

        baseMapper.insert(assembler.toUserDriveConfigPo(entity));
    }

    @Override
    public void update(UserDriveConfigEntity entity) {
        entity.setUpdateTime(new Date());

        baseMapper.updateById(assembler.toUserDriveConfigPo(entity));
    }
}
