package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeInviteEntity;
import com.zyhl.yun.api.outer.enums.UserKnowledgeInviteLevelEnum;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmUserKnowledgeInvitePO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 知识库邀请转换类
 * 
 * <AUTHOR>
 * @date 2025-04-16
 */
@Mapper(componentModel = "spring", imports = {UserKnowledgeInviteLevelEnum.class})
public interface UserKnowledgeInviteAssembler {
    
	/**
	 * 转换邀请信息
	 * 
	 * @param po po信息
	 * @return 邀请信息
	 */
	@Mapping(target = "level", expression = "java(UserKnowledgeInviteLevelEnum.MEMBER)")
    UserKnowledgeInviteEntity toUserKnowledgeInviteEntity(AlgorithmUserKnowledgeInvitePO po);

	/**
	 * 转换邀请PO
	 * 
	 * @param entity 实体
	 * @return 邀请PO
	 */
    AlgorithmUserKnowledgeInvitePO toAlgorithmUserKnowledgeInvitePO(UserKnowledgeInviteEntity entity);

	/**
	 * 转换邀请信息列表
	 * 
	 * @param poList po列表信息
	 * @return 邀请信息列表
	 */
    List<UserKnowledgeInviteEntity> toUserKnowledgeInviteEntityList(List<AlgorithmUserKnowledgeInvitePO> poList);

}
