package com.zyhl.yun.api.outer.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.yun.api.outer.domain.entity.knowledge.KnowledgeBusinessEntity;
import com.zyhl.yun.api.outer.persistence.mapper.KnowledgeBusinessMapper;
import com.zyhl.yun.api.outer.persistence.po.KnowledgeBusinessPO;
import com.zyhl.yun.api.outer.repository.KnowledgeBusinessRepository;
import com.zyhl.yun.api.outer.repository.assembler.KnowledgeBusinessAssembler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 知识库业务映射表
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class KnowledgeBusinessRepositoryImpl extends ServiceImpl<KnowledgeBusinessMapper, KnowledgeBusinessPO> implements KnowledgeBusinessRepository {

    private final KnowledgeBusinessAssembler knowledgeBusinessAssembler;

    @Override
    public KnowledgeBusinessEntity selectOne(String baseId, String businessCode) {
        return knowledgeBusinessAssembler.toKnowledgeBusinessEntity(this.lambdaQuery()
                .eq(KnowledgeBusinessPO::getBaseId, baseId)
                .eq(KnowledgeBusinessPO::getBusinessCode, businessCode)
                .one());
    }
}
