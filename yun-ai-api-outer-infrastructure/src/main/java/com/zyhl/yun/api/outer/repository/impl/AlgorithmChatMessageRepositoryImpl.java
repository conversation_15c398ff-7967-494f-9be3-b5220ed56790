package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.zyhl.hcy.yun.ai.common.base.annotation.MethodExecutionTimeLog;
import com.zyhl.yun.api.outer.config.BusinessParamProperties;
import com.zyhl.yun.api.outer.config.EmailAssistantQueryAllDataTempProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatMessageEntity;
import com.zyhl.yun.api.outer.enums.chat.DelFlagEnum;
import com.zyhl.yun.api.outer.enums.ApplicationTypeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatMessageSortTypeEnum;
import com.zyhl.yun.api.outer.persistence.mapper.AlgorithmChatMessageMapper;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmChatMessagePO;
import com.zyhl.yun.api.outer.repository.AlgorithmChatMessageRepository;
import com.zyhl.yun.api.outer.repository.assembler.ChatMessageAssembler;
import com.zyhl.yun.api.outer.repository.assembler.EntityAssembler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/2/29 14:15
 */
@Slf4j
@Service
public class AlgorithmChatMessageRepositoryImpl extends ServiceImpl<AlgorithmChatMessageMapper, AlgorithmChatMessagePO> implements AlgorithmChatMessageRepository {

    @Resource
    private ChatMessageAssembler messageAssembler;

    @Resource
    private EntityAssembler entityAssembler;

    @Resource
    private SourceChannelsProperties sourceChannelsProperties;

    @Resource
    private EmailAssistantQueryAllDataTempProperties emailAssistantQueryAllDataTempProperties;

    @Resource
    private BusinessParamProperties businessParamProperties;

    @Override
    @MethodExecutionTimeLog("获取会话信息分页数据-repository")
    public PageInfo<AlgorithmChatMessageEntity> chatList(AlgorithmChatMessageEntity entity, int offset, int pageSize, int needTotalCount) {
        PageMethod.offsetPage(offset, pageSize, needTotalCount == 1);

        /** 构建查询条件 */
        LambdaQueryChainWrapper<AlgorithmChatMessagePO> lambdaQueryChainWrapper = this.lambdaQuery()
                .eq(AlgorithmChatMessagePO::getUserId, entity.getUserId());
        /**
         * 业务类型判断
         * 如果是云邮助手的渠道，历史数据需要查询全部
         * 1、业务类型不能为空
         * 2、云邮的渠道
         * 3、排除unknown渠道
         * 4、查询全部的时间节点
         */
        String businessType = entity.getBusinessType();
        String sourceChannel = entity.getSourceChannel();
        if (CharSequenceUtil.isNotBlank(businessType) && sourceChannelsProperties.isMail(sourceChannel) && !businessType.contains(emailAssistantQueryAllDataTempProperties.getRejectBusinessType())) {
            // 针对邮箱webAI业务类型，配置展示云盘app、邮箱app、邮箱webAI渠道的历史记录
            List<String> businessTypeList = businessParamProperties.getChatContentList().getBusinessTypeMap().get(businessType);
//            // 获取所有云邮助手业务类型（去掉包含unknown的类型）
//            List<String> yunMailAllBusinessTypeList = sourceChannelsProperties.getYunMailAllBusinessTypeRemoveUnknown(sourceChannel);
//            yunMailAllBusinessTypeList.addAll(businessTypeList);
//            if(CollUtil.isEmpty(yunMailAllBusinessTypeList)){
//                // 无配置映射渠道，则用接口传入的
//                lambdaQueryChainWrapper.eq(AlgorithmChatMessagePO::getBusinessType, businessType);
//            } else {
//                lambdaQueryChainWrapper.in(AlgorithmChatMessagePO::getBusinessType, businessTypeList);
//            }

            if(CollUtil.isEmpty(businessTypeList)){
                businessTypeList = new ArrayList<>();
                businessTypeList.add(businessType);
            }
            List<String> finalBusinessTypeList = businessTypeList;
            lambdaQueryChainWrapper.and(qcw -> qcw
                    .and(qcwAnd -> qcwAnd
                            // 2024-08-13 00:00:00后，查询配置展示的渠道
                            .in(AlgorithmChatMessagePO::getBusinessType, finalBusinessTypeList)
                            .ge(AlgorithmChatMessagePO::getCreateTime, emailAssistantQueryAllDataTempProperties.getTimeNodes())
                    )
                    .or(qcwOr -> qcwOr
                            // 2024-08-13 00:00:00前，查询全部邮箱渠道
                            .in(AlgorithmChatMessagePO::getBusinessType, sourceChannelsProperties.getYunMailAllBusinessTypeRemoveUnknown(sourceChannel))
                            .lt(AlgorithmChatMessagePO::getCreateTime, emailAssistantQueryAllDataTempProperties.getTimeNodes())
                    )

            );
        } else {
            lambdaQueryChainWrapper.eq(CharSequenceUtil.isNotBlank(businessType), AlgorithmChatMessagePO::getBusinessType, businessType);
        }
        /**
         * 【非小天】渠道，限制搜索X天内的历史记录【更新时间】
         */
        if (!sourceChannelsProperties.isXiaoTian(sourceChannel)) {
            lambdaQueryChainWrapper.ge(AlgorithmChatMessagePO::getUpdateTime, DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -businessParamProperties.getChatContentList().getQueryMaxDays())));
        }
        // 应用类型 and 删除标识
        lambdaQueryChainWrapper.eq(CharSequenceUtil.isNotBlank(entity.getApplicationType()), AlgorithmChatMessagePO::getApplicationType, entity.getApplicationType())
                .eq(AlgorithmChatMessagePO::getDelFlag, DelFlagEnum.NO.getCode());

        // 排序条件判断
        Integer sortType = entity.getSortType();
        if (ChatMessageSortTypeEnum.UPDATE_TIME_DESC.getCode().equals(sortType)) {
            // 【更新时间】倒序排序
            lambdaQueryChainWrapper.orderByDesc(AlgorithmChatMessagePO::getUpdateTime);
        } else {
            //【创建时间】倒序排序
            lambdaQueryChainWrapper.orderByDesc(AlgorithmChatMessagePO::getCreateTime);
        }

        /** 查询数据 */
        List<AlgorithmChatMessagePO> list = lambdaQueryChainWrapper.list();

        /** 数据分页 */
        PageInfo<AlgorithmChatMessagePO> poPageInfo = PageInfo.of(list);
        PageInfo<AlgorithmChatMessageEntity> pageInfo = new PageInfo<>(messageAssembler.toChatMessageEntityList(list));
        pageInfo.setTotal(poPageInfo.getTotal());
        pageInfo.setNextPage(poPageInfo.getNextPage());
        return pageInfo;
    }

    /**
     * 根据会话id、用户id查询会话信息
     *
     * @param sessionId 会话id
     * @param userId    用户id
     * @return 会话信息entity
     */
    @Override
    public AlgorithmChatMessageEntity queryBySessionId(String sessionId, String userId) {
        QueryWrapper<AlgorithmChatMessagePO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AlgorithmChatMessagePO::getId, sessionId)
                .eq(AlgorithmChatMessagePO::getUserId, userId);
        AlgorithmChatMessagePO po = baseMapper.selectOne(wrapper);
        return entityAssembler.toAlgorithmChatMessageEntity(po);
    }

    @Override
    public List<AlgorithmChatMessageEntity> queryBySessionIdList(AlgorithmChatMessageEntity entity) {
        String userId = entity.getUserId();
        List<Long> sessionIdList = entity.getSessionIdList();
        String businessType = entity.getBusinessType();

        QueryWrapper<AlgorithmChatMessagePO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(AlgorithmChatMessagePO::getUserId, userId)
                .eq(CharSequenceUtil.isNotBlank(businessType), AlgorithmChatMessagePO::getBusinessType, businessType)
                .in(CollUtil.isNotEmpty(sessionIdList), AlgorithmChatMessagePO::getId, sessionIdList)
        ;
        List<AlgorithmChatMessagePO> poList = baseMapper.selectList(wrapper);
        return entityAssembler.toAlgorithmChatMessageEntityList(poList);
    }

    @Override
    public boolean deleteByIds(AlgorithmChatMessageEntity entity) {
        String userId = entity.getUserId();
        List<Long> sessionIdList = entity.getSessionIdList();
        String businessType = entity.getBusinessType();

        return this.lambdaUpdate()
                .set(AlgorithmChatMessagePO::getDelFlag, DelFlagEnum.YES.getCode())
                .set(AlgorithmChatMessagePO::getUpdateTime, new Date())
                .eq(AlgorithmChatMessagePO::getUserId, userId)
                .eq(CharSequenceUtil.isNotBlank(businessType), AlgorithmChatMessagePO::getBusinessType, businessType)
                // 没有会话id，则删除该用户的所有会话记录
                .in(CollUtil.isNotEmpty(sessionIdList), AlgorithmChatMessagePO::getId, sessionIdList)
                .update();
    }


    @Override
    public Boolean saveMessage(AlgorithmChatMessageEntity entity) {
        AlgorithmChatMessagePO po = messageAssembler.toChatMessage(entity);
        if (po != null && !CharSequenceUtil.isBlank(po.getTitle())) {
            po.setTitle(po.getTitle().substring(0, Math.min(po.getTitle().length(), 4096)));
        }
        return baseMapper.insert(po) > 0;
    }

    @Override
    public Boolean updateMessage(AlgorithmChatMessageEntity entity) {
        AlgorithmChatMessagePO po = messageAssembler.toChatMessage(entity);
        return baseMapper.updateById(po) > 0;
    }

    /**
     * 根据业务类型查询会话id
     *
     * @param userId       用户id
     * @param businessType 业务类型
     * @return 应用id，会话id
     */
    @Override
    public Map<String, AlgorithmChatMessageEntity> findSessionIdByBusinessType(String userId, String businessType) {
        return this.baseMapper.findSessionIdByUserIdAndBusinessType(userId, businessType);
    }

    @Override
	public boolean existNormalBySessionIdAndBusinessType(String userId, Long sessionId, String businessType) {
		// 查询普通对话类型
    	QueryWrapper<AlgorithmChatMessagePO> wrapper = new QueryWrapper<>();
		wrapper.lambda().eq(AlgorithmChatMessagePO::getUserId, userId).eq(AlgorithmChatMessagePO::getId, sessionId)
				.eq(AlgorithmChatMessagePO::getBusinessType, businessType)
				.eq(AlgorithmChatMessagePO::getApplicationType, ApplicationTypeEnum.CHAT.getCode())
				.eq(AlgorithmChatMessagePO::getDelFlag, DelFlagEnum.NO.getCode());
		return baseMapper.selectCount(wrapper) > 0;
	}
    
    /**
     * 根据业务类型查询会话信息
     *
     * @param userId        用户id
     * @param applicationId 应用id
     * @param businessType  业务类型
     * @return 会话信息Entity
     */
    @Override
    public AlgorithmChatMessageEntity selectOneByApplicationId(String userId, String applicationId, String businessType) {
        QueryWrapper<AlgorithmChatMessagePO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AlgorithmChatMessagePO::getUserId, userId)
                .eq(AlgorithmChatMessagePO::getBusinessType, businessType)
                .eq(AlgorithmChatMessagePO::getApplicationId, applicationId)
                .eq(AlgorithmChatMessagePO::getDelFlag, DelFlagEnum.NO.getCode())
                .orderByDesc(AlgorithmChatMessagePO::getId)
                .last("limit 1");
        AlgorithmChatMessagePO po = baseMapper.selectOne(wrapper);
        return entityAssembler.toAlgorithmChatMessageEntity(po);
    }

}
