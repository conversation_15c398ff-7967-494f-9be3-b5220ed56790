package com.zyhl.yun.api.outer.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmConfigEntity;
import com.zyhl.yun.api.outer.enums.chat.DelFlagEnum;
import com.zyhl.yun.api.outer.enums.config.TaskTypeEnum;
import com.zyhl.yun.api.outer.persistence.mapper.AlgorithmConfigMapper;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmConfigPO;
import com.zyhl.yun.api.outer.repository.AlgorithmConfigRepository;
import com.zyhl.yun.api.outer.repository.assembler.AlgorithmConfigAssembler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 算法配置表-RepositoryImpl
 *
 * @Author: WeiJingKun
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class AlgorithmConfigRepositoryImpl extends ServiceImpl<AlgorithmConfigMapper, AlgorithmConfigPO> implements AlgorithmConfigRepository {

    private final AlgorithmConfigAssembler assembler;

    @Override
    public AlgorithmConfigEntity queryByAlgorithmId(Long algorithmId) {
        /** 构建查询条件 */
        QueryWrapper<AlgorithmConfigPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AlgorithmConfigPO::getAlgorithmId, algorithmId)
                .eq(AlgorithmConfigPO::getTaskType, TaskTypeEnum.DOC_VECTOR.getCode())
                .eq(AlgorithmConfigPO::getDelFlag, DelFlagEnum.NO.getCode());

        /** 查询数据返回 */
        final AlgorithmConfigPO po = baseMapper.selectOne(queryWrapper);
        if (null != po) {
            return assembler.toEntity(po);
        }
        return null;
    }

    @Override
    public List<AlgorithmConfigEntity> getByAlgorithmIds(List<Long> algorithmIds) {
        List<AlgorithmConfigPO> poList = this.lambdaQuery()
                .in(AlgorithmConfigPO::getAlgorithmId, algorithmIds)
                .eq(AlgorithmConfigPO::getDelFlag, DelFlagEnum.NO.getCode())
                .list();

        return assembler.toEntityList(poList);
    }
}
