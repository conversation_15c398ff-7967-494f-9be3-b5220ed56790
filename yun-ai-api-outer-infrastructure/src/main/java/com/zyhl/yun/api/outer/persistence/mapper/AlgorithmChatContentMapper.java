package com.zyhl.yun.api.outer.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmChatContentPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @data 2024/2/29 14:11
 */
public interface AlgorithmChatContentMapper extends BaseMapper<AlgorithmChatContentPO> {

    /**
     * 获取对话内容【列表】数据
     * @Author: WeiJingKun
     *
     * @param entity 对话内容查询条件
     * @return 对话内容list
     */
    List<AlgorithmChatContentEntity> findList(@Param("entity") AlgorithmChatContentEntity entity);

    /**
     * 获取对话内容【列表】数据V2
     * @Author: WeiJingKun
     *
     * @param entity 对话内容查询条件
     * @return 对话内容list
     */
    List<AlgorithmChatContentEntity> findListV2(@Param("entity") AlgorithmChatContentEntity entity);

    /**
     * 获取对话内容【轮巡】数据
     * @Author: WeiJingKun
     *
     * @param entity 对话内容查询条件
     * @return 对话内容【轮巡】数据
     */
    List<AlgorithmChatContentEntity> findPollingUpdateData(@Param("entity") AlgorithmChatContentEntity entity);

}
