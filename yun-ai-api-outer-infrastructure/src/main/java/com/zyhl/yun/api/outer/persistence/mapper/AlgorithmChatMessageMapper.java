package com.zyhl.yun.api.outer.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatMessageEntity;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmChatMessagePO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Map;

/**
 * <AUTHOR>
 * @data 2024/2/29 14:08
 */
public interface AlgorithmChatMessageMapper extends BaseMapper<AlgorithmChatMessagePO> {

    /**
     * 查询会话信息
     * @param userId 用户id
     * @param businessType 业务类型
     * @return 会话信息
     */
    @Select("SELECT MAX(c.id) AS id, c.application_id AS applicationId FROM algorithm_chat_message c WHERE c.user_id = #{userId} AND c.business_type = #{businessType} AND c.del_flag = 0 GROUP BY applicationId ORDER BY id DESC")
    @MapKey("applicationId")
    Map<String, AlgorithmChatMessageEntity> findSessionIdByUserIdAndBusinessType(@Param("userId") String userId, @Param("businessType") String businessType);

}
