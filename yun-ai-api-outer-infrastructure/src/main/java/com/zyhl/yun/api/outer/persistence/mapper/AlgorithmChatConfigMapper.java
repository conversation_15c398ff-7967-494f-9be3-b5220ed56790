package com.zyhl.yun.api.outer.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmChatConfigPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 会话设置表-Mapper
 * @Author: 丁浩
 */
@Mapper
public interface AlgorithmChatConfigMapper extends BaseMapper<AlgorithmChatConfigPO> {

    /**
     * 根据用户id获取模型类型
     * @Author: WeiJingKun
     *
     * @param userId 用户id
     * @return 模型类型
     */
    @Select("select model_type from algorithm_chat_config where user_id = #{userId} limit 1")
    String getModelTypeByUserId(String userId);

}
