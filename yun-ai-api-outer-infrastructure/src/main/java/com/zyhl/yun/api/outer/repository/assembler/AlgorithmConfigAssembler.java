package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.AlgorithmConfigEntity;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmConfigPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 算法配置表-Assembler
 *
 * @Author: WeiJingKun
 */
@Mapper(componentModel = "spring")
public interface AlgorithmConfigAssembler {

    AlgorithmConfigAssembler INSTANCE = Mappers.getMapper(AlgorithmConfigAssembler.class);

    /**
     * po转entity
     * @param po po
     * @return entity
     */
    AlgorithmConfigEntity toEntity(AlgorithmConfigPO po);

    /**
     * po转entity列表
     * @param poList po列表
     * @return entity列表
     */
    List<AlgorithmConfigEntity> toEntityList(List<AlgorithmConfigPO> poList);
}
