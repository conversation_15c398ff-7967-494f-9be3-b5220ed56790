package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * AI人物关系资源表PO
 * <AUTHOR>
 */
@Data
@TableName("algorithm_ai_person_resource")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AlgorithmAiPersonResourcePO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@TableId(value = "id")
	private Long id;

	/**
	 * 人名
	 */
	@TableField(value = "name")
	private String name;

	/**
	 * 关系
	 */
	@TableField(value = "relationship")
	private String relationship;

	/**
	 * 资源名称
	 */
	@TableField(value = "resource_name")
	private String resourceName;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time")
	private Date updateTime;

}
