package com.zyhl.yun.api.outer.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmUserKnowledgeInvitePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * className:UserKnowledgeInviteMapper
 *
 * <AUTHOR>
 * @date 2025/04/12
 */
@Mapper
public interface UserKnowledgeInviteMapper extends BaseMapper<AlgorithmUserKnowledgeInvitePO> {

}
