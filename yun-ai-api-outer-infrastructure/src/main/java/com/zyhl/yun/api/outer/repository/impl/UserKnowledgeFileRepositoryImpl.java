package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.dashscope.utils.JsonUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.config.SearchParamProperties;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileStatisticsEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeResourceListReqEntity;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeCountVO;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.*;
import com.zyhl.yun.api.outer.persistence.mapper.UserKnowledgeFileMapper;
import com.zyhl.yun.api.outer.persistence.po.UserKnowledgeFilePO;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileRepository;
import com.zyhl.yun.api.outer.repository.UserKnowledgeRepository;
import com.zyhl.yun.api.outer.repository.assembler.UserKnowledgeFileAssembler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 知识库文件
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class UserKnowledgeFileRepositoryImpl extends ServiceImpl<UserKnowledgeFileMapper, UserKnowledgeFilePO> implements UserKnowledgeFileRepository {

    private final UserKnowledgeFileAssembler assembler;

    private final UidGenerator uidGenerator;

    private final UserKnowledgeRepository userKnowledgeRepository;

    private static final int NOT_DELETED = 0;


    @Override
    public int add(UserKnowledgeFileEntity entity) {
        if (Objects.isNull(entity.getId())) {
            entity.setId(uidGenerator.getUID());
        }
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());

        if (OwnerTypeEnum.isPersonal(entity.getOwnerType())) {
            entity.setOwnerId(entity.getUserId());
        }

        return baseMapper.insert(assembler.toUserKnowledgeFilePo(entity));
    }

    @Override
    @Transactional
    public boolean batchAdd(List<UserKnowledgeFileEntity> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return false;
        }
        List<UserKnowledgeFilePO> poList = new ArrayList<>(entityList.size());
        for (UserKnowledgeFileEntity entity : entityList) {
            if (Objects.isNull(entity.getId())) {
                entity.setId(uidGenerator.getUID());
            }
            entity.setCreateTime(new Date());
            entity.setUpdateTime(new Date());
            if (OwnerTypeEnum.isPersonal(entity.getOwnerType())) {
                entity.setOwnerId(entity.getUserId());
            }
            poList.add(assembler.toUserKnowledgeFilePo(entity));
        }
        return this.saveBatch(poList, poList.size());
    }

    @Override
    public int update(UserKnowledgeFileEntity entity) {
        entity.setUpdateTime(new Date());
        return baseMapper.updateById(assembler.toUserKnowledgeFilePo(entity));
    }

    @Override
    public int delete(Long id) {
        // 逻辑删除
        this.lambdaUpdate()
                .eq(UserKnowledgeFilePO::getId, id)
                .set(UserKnowledgeFilePO::getUpdateTime, new Date())
                .set(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.DELETED.getStatus())
                .update();

        return 1;
    }

    @Override
    public int deleteByIds(List<Long> ids) {
        // 逻辑删除
        this.lambdaUpdate()
                .in(UserKnowledgeFilePO::getId, ids)
                .set(UserKnowledgeFilePO::getUpdateTime, new Date())
                .set(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.DELETED.getStatus())
                .update();

        return 1;
    }

    @Override
    public void deleteByFileIds(String userId, List<String> fileIds, KnowledgeStatusEnum status) {
        // 逻辑删除
        this.lambdaUpdate()
                .eq(UserKnowledgeFilePO::getUserId, userId)
                .in(UserKnowledgeFilePO::getFileId, fileIds)
                .set(UserKnowledgeFilePO::getUpdateTime, new Date())
                .set(UserKnowledgeFilePO::getDelFlag, status.getStatus())
                .update();

    }

    @Override
    public void batchDeleteByFileIds(String userId, List<String> fileIds, KnowledgeStatusEnum status) {
    	// 每次更新的最大文件 ID 数量
        int batchSize = 500; 
        for (int i = 0; i < fileIds.size(); i += batchSize) {
            // 获取当前批次的文件 ID 子列表
            List<String> subFileIds = fileIds.subList(i, Math.min(i + batchSize, fileIds.size()));

            // 执行逻辑删除
            this.lambdaUpdate()
                    .eq(UserKnowledgeFilePO::getUserId, userId)
                    .in(UserKnowledgeFilePO::getFileId, subFileIds)
                    .set(UserKnowledgeFilePO::getUpdateTime, new Date())
                    .set(UserKnowledgeFilePO::getDelFlag, status.getStatus())
                    .update();
        }
    }

    @Override
    public UserKnowledgeFileEntity selectById(Long id) {
        return assembler.toUserKnowledgeFileEntity(baseMapper.selectById(id));
    }

    @Override
    public List<UserKnowledgeFileEntity> selectByUserId(String userId) {
        // 查询条件
        LambdaQueryChainWrapper<UserKnowledgeFilePO> wrapper = this.lambdaQuery()
                .eq(UserKnowledgeFilePO::getUserId, userId);

        // 查询数据
        return assembler.toUserKnowledgeFileEntityList(wrapper.list());
    }

    @Override
    public List<UserKnowledgeFileEntity> selectByBaseId(String baseId) {
        // 查询条件
        LambdaQueryChainWrapper<UserKnowledgeFilePO> wrapper = this.lambdaQuery()
                .eq(UserKnowledgeFilePO::getBaseId, baseId);

        // 查询数据
        return assembler.toUserKnowledgeFileEntityList(wrapper.list());
    }


    @Override
    public UserKnowledgeFileEntity selectByFileId(String userId, String fileId) {
        // 查询条件
        LambdaQueryChainWrapper<UserKnowledgeFilePO> wrapper = this.lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(userId), UserKnowledgeFilePO::getUserId, userId)
                .eq(UserKnowledgeFilePO::getFileId, fileId)
                .eq(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus());

        // 查询数据
        return assembler.toUserKnowledgeFileEntity(wrapper.one());
    }

    @Override
    public List<UserKnowledgeFileEntity> selectByFileIds(String userId, List<String> fileIds) {
        if (ObjectUtil.isEmpty(fileIds)) {
            return new ArrayList<>();
        }
        // 查询条件
        LambdaQueryChainWrapper<UserKnowledgeFilePO> wrapper = this.lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(userId), UserKnowledgeFilePO::getUserId, userId)
                .in(UserKnowledgeFilePO::getFileId, fileIds)
                .eq(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus());

        // 查询数据
        return assembler.toUserKnowledgeFileEntityList(wrapper.list());
    }

    @Override
    public List<UserKnowledgeFileEntity> selectByOldFileIds(String userId, List<String> fileIds) {
        // 查询条件
        LambdaQueryChainWrapper<UserKnowledgeFilePO> wrapper = this.lambdaQuery()
                .eq(UserKnowledgeFilePO::getUserId, userId)
                .in(UserKnowledgeFilePO::getOldFileId, fileIds)
                .eq(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus());

        // 查询数据
        return assembler.toUserKnowledgeFileEntityList(wrapper.list());
    }

    @Override
    public List<UserKnowledgeFileEntity> selectHtmlResource(String userId, List<String> urlList) {
        // 查询条件
        LambdaQueryChainWrapper<UserKnowledgeFilePO> wrapper = this.lambdaQuery()
                .eq(UserKnowledgeFilePO::getUserId, userId)
                .in(UserKnowledgeFilePO::getFromResourceType, KnowledgeResourceTypeEnum.HTML.getCode())
                .eq(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus());

        // 查询数据
        List<UserKnowledgeFileEntity> list = assembler.toUserKnowledgeFileEntityList(wrapper.list());

        // 过滤数据
        List<UserKnowledgeFileEntity> resultList = new ArrayList<>();
        for (UserKnowledgeFileEntity entity : list) {
            entity.setHtmlInfo(JsonUtils.fromJson(entity.getFromResource(), UserKnowledgeFileEntity.HtmlInfo.class));
            if (Objects.nonNull(entity.getHtmlInfo()) && urlList.contains(entity.getHtmlInfo().getUrl())) {
                resultList.add(entity);
            }
        }

        return resultList;
    }

    @Override
    public int count(String userId) {
        return this.lambdaQuery()
                .eq(UserKnowledgeFilePO::getUserId, userId)
                .eq(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                .count();
    }

    @Override
    public int count(String userId, Integer status) {
        return this.lambdaQuery()
                .eq(UserKnowledgeFilePO::getUserId, userId)
                .eq(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                .eq(UserKnowledgeFilePO::getAiStatus, status)
                .in(status == 0, UserKnowledgeFilePO::getAuditStatus, Arrays.asList(0, 1, 2))
                .isNull(UserKnowledgeFilePO::getBaseId)
                .count();
    }

    @Override
    public int countCanUse(String userId) {
        return this.lambdaQuery()
                .eq(UserKnowledgeFilePO::getUserId, userId)
                .eq(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                .eq(UserKnowledgeFilePO::getAiStatus, FileAiStatusParamEnum.SUCCESS.getCode())
                .count();
    }

    @Override
    public PageInfo<UserKnowledgeFileEntity> list(String userId, PageInfoDTO pageInfo, Long labelId) {
        PageHelper.offsetPage(Integer.parseInt(pageInfo.getPageCursor()), pageInfo.getPageSize(), pageInfo.isNeedTotal());

        // 标签下的文件
        String sql = "select file_id from algorithm_user_knowledge_label_file where user_id = '" + userId + "' and label_id = " + labelId;

        // 查询条件
        List<UserKnowledgeFilePO> list = this.lambdaQuery()
                .eq(UserKnowledgeFilePO::getUserId, userId)
                .eq(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                .inSql(!KnowledgeLabelEnum.isAll(labelId), UserKnowledgeFilePO::getFileId, sql)
                .isNull(UserKnowledgeFilePO::getBaseId)
                .orderByDesc(UserKnowledgeFilePO::getUpdateTime)
                .orderByDesc(UserKnowledgeFilePO::getId)
                .list();

        // 数据转换
        Page<UserKnowledgeFileEntity> page = new Page<>();
        BeanUtils.copyProperties(list, page);
        page.addAll(assembler.toUserKnowledgeFileEntityList(list));
        return PageInfo.of(page);
    }

    @Override
    public PageInfo<UserKnowledgeFileEntity> list180(String userId, PageInfoDTO pageInfo, Integer status, Integer orderBy) {
        PageHelper.offsetPage(Integer.parseInt(pageInfo.getPageCursor()), pageInfo.getPageSize(), pageInfo.isNeedTotal());

        // aiStatus
        List<Integer> aiStatusList = new ArrayList<>();
        if (Objects.nonNull(status)) {
            if (status == -1) {
                aiStatusList.add(2);
            } else if (status == 0) {
                aiStatusList.add(0);
                aiStatusList.add(2);
            } else if (status == 1) {
                aiStatusList.add(1);
            }
        }

        // 查询条件
        List<UserKnowledgeFilePO> list = this.lambdaQuery()
                .eq(UserKnowledgeFilePO::getUserId, userId)
                .eq(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                .in(Objects.nonNull(status), UserKnowledgeFilePO::getAiStatus, aiStatusList)
                .isNull(UserKnowledgeFilePO::getBaseId)
                .orderByDesc(orderBy == 1, UserKnowledgeFilePO::getCreateTime)
                .orderByDesc(orderBy == 2, UserKnowledgeFilePO::getUpdateTime)
                .orderByDesc(UserKnowledgeFilePO::getId)
                .list();

        // 数据转换
        Page<UserKnowledgeFileEntity> page = new Page<>();
        BeanUtils.copyProperties(list, page);
        page.addAll(assembler.toUserKnowledgeFileEntityList(list));
        return PageInfo.of(page);
    }

    @Override
    @SuppressWarnings("unchecked")
    public PageInfo<UserKnowledgeFileEntity> getCreatorList(UserKnowledgeResourceListReqEntity reqEntity, String userId) {
        Integer fileType = ObjectUtil.isNotEmpty(reqEntity.getType()) ? FileTypeEnum.getKnowledgeFileType(reqEntity.getType()) : null;
        Integer aiStatus = reqEntity.getAiStatus();
        String baseId = reqEntity.getBaseId();
        String parentResoureId = reqEntity.getParentResoureId();
        PageInfoDTO pageInfo = reqEntity.getPageInfo();
        Integer sortDirection = reqEntity.getSortDirection();
        Integer sortType = reqEntity.getSortType();
        Integer resourceType = reqEntity.getResourceType();
        List<Integer> categoryList = reqEntity.getCategoryList();

        // aiStatus
        List<Integer> dbAiStatus = new ArrayList<>();
        if (Objects.nonNull(aiStatus)) {
            if (aiStatus.equals(FileAiStatusParamEnum.FAIL.getCode())) {
                dbAiStatus.add(FileProcessStatusEnum.FAIL.getStatus());
            } else if (aiStatus.equals(FileAiStatusParamEnum.PROCESSING.getCode())) {
                dbAiStatus.add(FileProcessStatusEnum.UNPROCESSED.getStatus());
            } else if (aiStatus.equals(FileAiStatusParamEnum.SUCCESS.getCode())) {
                dbAiStatus.add(FileProcessStatusEnum.SUCCESS.getStatus());
            }
        }

        // 默认按导入时间排序
        if (Objects.isNull(sortType)) {
            sortType = FileSortTypeEnum.IMPORT_TIME.getCode();
        }

        // 默认按降序排序
        if (Objects.isNull(sortDirection)) {
            sortDirection = FileSortDirectionEnum.DESC.getCode();
        }

        // 是否有筛选条件 有的话为true
        boolean isFilter = Objects.nonNull(resourceType) || Objects.nonNull(aiStatus) || ObjectUtil.isNotEmpty(categoryList);

        // 基本条件
        LambdaQueryChainWrapper<UserKnowledgeFilePO> wrapper = this.lambdaQuery()
                .isNotNull(UserKnowledgeFilePO::getBaseId)
                .eq(UserKnowledgeFilePO::getBaseId, baseId)
                .eq(UserKnowledgeFilePO::getUserId, userId)
                .eq(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                .eq(Objects.nonNull(resourceType), UserKnowledgeFilePO::getFromResourceType, resourceType)
                .in(Objects.nonNull(aiStatus), UserKnowledgeFilePO::getAiStatus, dbAiStatus);

        // 类型
        if (fileType != null) {
            wrapper.eq(UserKnowledgeFilePO::getFileType, fileType);
        }
        // 文件分类列表
        if (CollectionUtil.isNotEmpty(categoryList)) {
            wrapper.in(UserKnowledgeFilePO::getCategory, categoryList);
            // 包含文档搜索，需过滤掉邮件类型
            if (categoryList.contains(FileCategoryEnum.DOC.getKnowledgeCategory())) {
                wrapper.ne(UserKnowledgeFilePO::getExtension, KnowledgeResourceTypeEnum.MAIL.getExt());
            }
        }

        if (isFilter) {
            if (StringUtils.isNotBlank(parentResoureId)) {
                UserKnowledgeFilePO fileBean = this.lambdaQuery()
                        .eq(UserKnowledgeFilePO::getBaseId, baseId)
                        .eq(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                        .eq(UserKnowledgeFilePO::getFromResourceType, KnowledgeResourceTypeEnum.PERSONAL_FILE.getCode())
                        .eq(UserKnowledgeFilePO::getFileId, parentResoureId)
                        .one();
                if (Objects.isNull(fileBean)) {
                    throw new YunAiBusinessException(ResultCodeEnum.PARENT_FILE_ID_IS_NULL);
                }
                // 主动传parentResoureId的情况 一定得是个人云 才需要使用前缀匹配
                wrapper.likeRight(UserKnowledgeFilePO::getParentFilePath, fileBean.getParentFilePath() + "/" + parentResoureId);
            }
            // 文件类型前端没传入的情况下，有筛选条件必须只查文件类型的
            if (fileType == null) {
                wrapper.eq(UserKnowledgeFilePO::getFileType, FileTypeEnum.FILE.getKnowledgeFileType());
            }
        } else {
            if (StringUtils.isBlank(parentResoureId)) {
                // 查询个人知识库根目录
                UserKnowledgeEntity userKnowledgeEntity = userKnowledgeRepository.selectById(Long.valueOf(baseId));
                if (Objects.isNull(userKnowledgeEntity)) {
                    throw new YunAiBusinessException(ResultCodeEnum.KNOWLEDGE_ID_NOT_EXIST);
                }
                // 没有值使用查询出来的值
                wrapper.eq(UserKnowledgeFilePO::getParentFileId, userKnowledgeEntity.getFolderId());
            } else {
                // 有值直接使用传进来的值
                wrapper.eq(UserKnowledgeFilePO::getParentFileId, parentResoureId);
            }
        }

        // 排序条件
        wrapper.orderByDesc(UserKnowledgeFilePO::getFileType)
                .orderBy(Objects.equals(sortType, FileSortTypeEnum.IMPORT_TIME.getCode()),
                        Objects.equals(sortDirection, FileSortDirectionEnum.ASC.getCode()), UserKnowledgeFilePO::getCreateTime)
                .orderByDesc(Objects.equals(sortType, FileSortTypeEnum.IMPORT_TIME.getCode()), UserKnowledgeFilePO::getId)
                .orderBy(Objects.equals(sortType, FileSortTypeEnum.RESOURCE_TYPE.getCode()),
                        Objects.equals(sortDirection, FileSortDirectionEnum.DESC.getCode()), UserKnowledgeFilePO::getSortType)
                .orderBy(Objects.equals(sortType, FileSortTypeEnum.RESOURCE_SIZE.getCode()),
                        Objects.equals(sortDirection, FileSortDirectionEnum.ASC.getCode()), UserKnowledgeFilePO::getFileSize);

        // 使用 last 实现 LOWER(file_name) 排序
        if (Objects.equals(sortType, FileSortTypeEnum.RESOURCE_NAME.getCode())) {
            boolean isAsc = Objects.equals(sortDirection, FileSortDirectionEnum.ASC.getCode());
            wrapper.last(",LOWER(file_name) " + (isAsc ? "ASC" : "DESC"));
        }

        // 最后在排序前 分页
        PageHelper.offsetPage(Integer.parseInt(pageInfo.getPageCursor()), pageInfo.getPageSize(), pageInfo.isNeedTotal());

        // 查询数据
        List<UserKnowledgeFilePO> list = wrapper.list();

        // 数据转换
        Page<UserKnowledgeFileEntity> page = new Page<>();
        BeanUtils.copyProperties(list, page);
        page.addAll(assembler.toUserKnowledgeFileEntityList(list));
        return PageInfo.of(page);
    }

    @Override
    public PageInfo<UserKnowledgeFileEntity> getList(UserKnowledgeResourceListReqEntity reqEntity, String userId) {
        Integer fileType = ObjectUtil.isNotEmpty(reqEntity.getType()) ? FileTypeEnum.getKnowledgeFileType(reqEntity.getType()) : null;
        String baseId = reqEntity.getBaseId();
        String parentResoureId = reqEntity.getParentResoureId();
        PageInfoDTO pageInfo = reqEntity.getPageInfo();
        Integer sortDirection = reqEntity.getSortDirection();
        Integer sortType = reqEntity.getSortType();
        Integer resourceType = reqEntity.getResourceType();
        List<Integer> categoryList = reqEntity.getCategoryList();

        // 默认按导入时间排序
        if (Objects.isNull(sortType)) {
            sortType = FileSortTypeEnum.IMPORT_TIME.getCode();
        }

        // 默认按降序排序
        if (Objects.isNull(sortDirection)) {
            sortDirection = FileSortDirectionEnum.DESC.getCode();
        }

        // 基本条件
        LambdaQueryChainWrapper<UserKnowledgeFilePO> wrapper = this.lambdaQuery()
                .isNotNull(UserKnowledgeFilePO::getBaseId)
                .eq(UserKnowledgeFilePO::getBaseId, baseId)
                .eq(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                .eq(Objects.nonNull(resourceType), UserKnowledgeFilePO::getFromResourceType, resourceType)
                .eq(UserKnowledgeFilePO::getAiStatus, FileProcessStatusEnum.SUCCESS.getStatus());

        // 文件类型
        if (fileType != null) {
            wrapper.eq(UserKnowledgeFilePO::getFileType, fileType);
        }
        // 文件分类列表
        if (CollectionUtil.isNotEmpty(categoryList)) {
            wrapper.in(UserKnowledgeFilePO::getCategory, categoryList);
            // 包含文档搜索，需过滤掉邮件类型
            if (categoryList.contains(FileCategoryEnum.DOC.getKnowledgeCategory())) {
                wrapper.ne(UserKnowledgeFilePO::getExtension, KnowledgeResourceTypeEnum.MAIL.getExt());
            }
        }

        if (Objects.nonNull(resourceType)) {
            if (StringUtils.isNotBlank(parentResoureId)) {
                UserKnowledgeFilePO fileBean = this.lambdaQuery()
                        .eq(UserKnowledgeFilePO::getBaseId, baseId)
                        .eq(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                        .eq(UserKnowledgeFilePO::getFromResourceType, KnowledgeResourceTypeEnum.PERSONAL_FILE.getCode())
                        .eq(UserKnowledgeFilePO::getFileId, parentResoureId)
                        .one();
                if (Objects.isNull(fileBean)) {
                    throw new YunAiBusinessException(ResultCodeEnum.PARENT_FILE_ID_IS_NULL);
                }
                // 主动传parentResoureId的情况 一定得是个人云 才需要使用前缀匹配
                wrapper.likeRight(UserKnowledgeFilePO::getParentFilePath, fileBean.getParentFilePath() + "/" + parentResoureId);
            }
            // 文件类型前端没传入的情况下，只查文件类型的
            if (fileType == null) {
                wrapper.eq(UserKnowledgeFilePO::getFileType, FileTypeEnum.FILE.getKnowledgeFileType());
            }
        } else {
            if (StringUtils.isBlank(parentResoureId)) {
                // 查询个人知识库根目录
                UserKnowledgeEntity userKnowledgeEntity = userKnowledgeRepository.selectById(Long.valueOf(baseId));
                // 没有值使用查询出来的值
                wrapper.eq(UserKnowledgeFilePO::getParentFileId, userKnowledgeEntity.getFolderId());
            } else {
                // 有值直接使用传进来的值
                wrapper.eq(UserKnowledgeFilePO::getParentFileId, parentResoureId);
            }
        }

        // 排序条件
        wrapper.orderByDesc(UserKnowledgeFilePO::getFileType)
                .orderBy(Objects.equals(sortType, FileSortTypeEnum.IMPORT_TIME.getCode()),
                        Objects.equals(sortDirection, FileSortDirectionEnum.ASC.getCode()), UserKnowledgeFilePO::getCreateTime)
                .orderByDesc(Objects.equals(sortType, FileSortTypeEnum.IMPORT_TIME.getCode()), UserKnowledgeFilePO::getId)
                .orderBy(Objects.equals(sortType, FileSortTypeEnum.RESOURCE_TYPE.getCode()),
                        Objects.equals(sortDirection, FileSortDirectionEnum.DESC.getCode()), UserKnowledgeFilePO::getSortType)
                .orderBy(Objects.equals(sortType, FileSortTypeEnum.RESOURCE_SIZE.getCode()),
                        Objects.equals(sortDirection, FileSortDirectionEnum.ASC.getCode()), UserKnowledgeFilePO::getFileSize);

        // 使用 last 实现 LOWER(file_name) 排序
        if (Objects.equals(sortType, FileSortTypeEnum.RESOURCE_NAME.getCode())) {
            boolean isAsc = Objects.equals(sortDirection, FileSortDirectionEnum.ASC.getCode());
            wrapper.last(",LOWER(file_name) " + (isAsc ? "ASC" : "DESC"));
        }

        // 在执行最终查询前 分页
        PageHelper.offsetPage(Integer.parseInt(pageInfo.getPageCursor()), pageInfo.getPageSize(), pageInfo.isNeedTotal());
        // 查询数据
        List<UserKnowledgeFilePO> list = wrapper.list();

        // 数据转换
        Page<UserKnowledgeFileEntity> page = new Page<>();
        BeanUtils.copyProperties(list, page);
        page.addAll(assembler.toUserKnowledgeFileEntityList(list));
        return PageInfo.of(page);
    }

    @Override
    public void saveBatch(List<UserKnowledgeFileEntity> entityList) {

        entityList.forEach(entity -> {
            UserKnowledgeFilePO po = assembler.toUserKnowledgeFilePo(entity);
            baseMapper.insert(po);
        });
    }

    @Override
    public PersonalKnowledgeCountVO countByUserId(String userId, String countTime) {
        return baseMapper.countByUserId(userId, countTime);
    }

    public List<UserKnowledgeFileStatisticsEntity> findStatisticsByBaseId(List<Long> baseIds) {
        return baseMapper.findStatisticsByBaseId(baseIds);
    }

    public List<UserKnowledgeFileStatisticsEntity> findStatisticsByBaseIdForInfo(List<Long> baseIds) {
        return baseMapper.findStatisticsByBaseIdForInfo(baseIds);
    }

    @Override
    public List<UserKnowledgeFileEntity> batchGet(String userId, List<String> resourceIdList) {
        return baseMapper.batchGetByUserIdAndFileIds(userId, resourceIdList);
    }

    @Override
    public List<UserKnowledgeFileStatisticsEntity> findStatisticsByUserIdAndBaseIds(String userId, List<Long> baseIdList) {
        return baseMapper.findStatisticsByUserIdAndBaseIds(userId, baseIdList);
    }

    /**
     * 根据baseId和fileId和个人知识库文件信息
     *
     * @param baseId
     * @param resourceId
     * @return
     */
    @Override
    public UserKnowledgeFileEntity getOne(String baseId, String resourceId) {

        /** 构建查询条件 */
        List<UserKnowledgeFilePO> list = this.lambdaQuery()
                .eq(UserKnowledgeFilePO::getBaseId, Long.valueOf(baseId))
                .eq(UserKnowledgeFilePO::getFileId, resourceId)
                .eq(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                .list();
        if (CollUtil.isNotEmpty(list)) {
            // 用户设置的模型
            return assembler.toUserKnowledgeFileEntity(list.get(0));
        }
        return null;
    }


    /**
     * 根据baseId和fileId和个人知识库文件信息
     *
     * @param baseId
     * @param resourceId
     * @return
     */
    @Override
    public UserKnowledgeFileEntity getOne(String baseId, String resourceId, Integer fromResourceType) {

        /** 构建查询条件 */
        List<UserKnowledgeFilePO> list = this.lambdaQuery()
                .eq(UserKnowledgeFilePO::getBaseId, Long.valueOf(baseId))
                .eq(UserKnowledgeFilePO::getFileId, resourceId)
                .eq(UserKnowledgeFilePO::getFromResourceType, fromResourceType)
                .eq(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                .list();
        if (CollUtil.isNotEmpty(list)) {
            // 用户设置的模型
            return assembler.toUserKnowledgeFileEntity(list.get(0));
        }
        return null;
    }

    @Override
    public UserKnowledgeFileEntity getFileByBaseIdAndFileId(String userId, String baseId, String resourceId) {
        /** 构建查询条件 */
        //查询未删除的文件
        List<UserKnowledgeFilePO> list = this.lambdaQuery()
                .eq(UserKnowledgeFilePO::getBaseId, Long.valueOf(baseId))
                .eq(UserKnowledgeFilePO::getFileId, resourceId)
                .eq(UserKnowledgeFilePO::getUserId, userId)
                .eq(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                .list();
        if (CollUtil.isNotEmpty(list)) {
            // 用户设置的模型
            return assembler.toUserKnowledgeFileEntity(list.get(0));
        }
        return null;
    }

    @Override
    public Set<String> findRepeatImportPersonalCloudFile(String userId, String baseId, List<String> fileIdList, String parentFileId) {
        LambdaQueryWrapper<UserKnowledgeFilePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(UserKnowledgeFilePO::getOldFileId)
                .eq(UserKnowledgeFilePO::getUserId, userId)
                .eq(UserKnowledgeFilePO::getBaseId, baseId)
                .eq(UserKnowledgeFilePO::getDelFlag, NOT_DELETED)
                .in(UserKnowledgeFilePO::getOldFileId, fileIdList)
                // 查首层
                .eq(UserKnowledgeFilePO::getParentFileId, parentFileId);
        List<UserKnowledgeFilePO> algorithmUserKnowledgeUploads = baseMapper.selectList(wrapper);
        return algorithmUserKnowledgeUploads
                .stream()
                .map(UserKnowledgeFilePO::getOldFileId)
                .collect(Collectors.toSet());
    }

    @Override
    public List<UserKnowledgeFileEntity> selectHtmlResource(String userId, List<String> urlList, Long baseId) {
        // 查询条件
        LambdaQueryChainWrapper<UserKnowledgeFilePO> wrapper = this.lambdaQuery()
                .eq(UserKnowledgeFilePO::getUserId, userId)
                .eq(UserKnowledgeFilePO::getBaseId, baseId)
                .in(UserKnowledgeFilePO::getFromResourceType, KnowledgeResourceTypeEnum.HTML.getCode())
                .eq(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus());

        // 查询数据
        List<UserKnowledgeFileEntity> list = assembler.toUserKnowledgeFileEntityList(wrapper.list());

        // 过滤数据
        List<UserKnowledgeFileEntity> resultList = new ArrayList<>();
        for (UserKnowledgeFileEntity entity : list) {
            entity.setHtmlInfo(JsonUtils.fromJson(entity.getFromResource(), UserKnowledgeFileEntity.HtmlInfo.class));
            if (Objects.nonNull(entity.getHtmlInfo()) && urlList.contains(entity.getHtmlInfo().getUrl())) {
                resultList.add(entity);
            }
        }

        return resultList;
    }

    @Override
    public UserKnowledgeFileEntity selectByUserIdAndFileId(String userId, String fileId) {
        return baseMapper.selectByUserIdAndFileId(userId, fileId);
    }

    @Override
    public List<UserKnowledgeFileEntity> selectChildrenByParentId(String userId, String parentFileId) {
        return baseMapper.selectChildrenByParentId(userId, parentFileId);
    }

    @Override
    public List<UserKnowledgeFileEntity> selectByOldFileIds(Long baseId, String userId, List<String> fileIds) {
        // 查询条件
        LambdaQueryChainWrapper<UserKnowledgeFilePO> wrapper = this.lambdaQuery()
                .eq(UserKnowledgeFilePO::getUserId, userId)
                .eq(UserKnowledgeFilePO::getBaseId, baseId)
                .in(UserKnowledgeFilePO::getOldFileId, fileIds)
                .eq(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus());

        // 查询数据
        return assembler.toUserKnowledgeFileEntityList(wrapper.list());
    }

    /**
     * 根据文件id查询
     *
     * @param fileId
     * @return
     */
    @Override
    public UserKnowledgeFileEntity getByFileId(String fileId) {
        // 查询条件
        LambdaQueryChainWrapper<UserKnowledgeFilePO> wrapper = this.lambdaQuery()
                .eq(UserKnowledgeFilePO::getFileId, fileId)
                .eq(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus());
        UserKnowledgeFilePO knowledgeFile = wrapper.one();
        return assembler.toUserKnowledgeFileEntity(knowledgeFile);
    }

    @Override
    public List<UserKnowledgeFileEntity> selectByBaseIdAndResourceType(String baseId, List<Integer> resourceTypeList) {
        // 查询条件
        LambdaQueryChainWrapper<UserKnowledgeFilePO> wrapper = this.lambdaQuery()
                .eq(UserKnowledgeFilePO::getBaseId, baseId)
                .in(UserKnowledgeFilePO::getFromResourceType, resourceTypeList)
                .eq(UserKnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus());

        // 查询数据
        return assembler.toUserKnowledgeFileEntityList(wrapper.list());
    }


    @Override
    public UserKnowledgeFileEntity hasNoteSync(String fileId, Long baseId) {
        LambdaQueryChainWrapper<UserKnowledgeFilePO> wrapper = this.lambdaQuery()
                .eq(UserKnowledgeFilePO::getFileId, fileId)
                .eq(UserKnowledgeFilePO::getBaseId, baseId);

        UserKnowledgeFilePO knowledgeFile = wrapper.one();
        log.info("查询用户是否有笔记同步记录,info:{}", knowledgeFile);
        return assembler.toUserKnowledgeFileEntity(knowledgeFile);

    }


}
