package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zyhl.yun.api.outer.domain.entity.image.AlgorithmUserFileEntity;
import com.zyhl.yun.api.outer.enums.DelFlagEnum;
import com.zyhl.yun.api.outer.persistence.mapper.AlgorithmUserFileMapper;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmUserFilePo;
import com.zyhl.yun.api.outer.repository.AlgorithmUserFileRepository;
import com.zyhl.yun.api.outer.repository.assembler.AlgorithmUserFileAssembler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/12 18:09
 */
@Slf4j
@Repository
public class AlgorithmUserFileRepositoryImpl implements AlgorithmUserFileRepository {

    @Resource
    private AlgorithmUserFileMapper userFileMapper;
    @Resource
    private AlgorithmUserFileAssembler assembler;

    @Value("${memory.album.quality.userFileQuerySize:100}")
    private int userFileQuerySize;

    @Override
    public List<AlgorithmUserFileEntity> selectUserFileInfoHaveMetadataRowKey(String userId, Integer ownerType, List<String> fileIds) {
        if (CollUtil.isEmpty(fileIds)){
           return Collections.emptyList();
        }
        List<List<String>> splitUserIdList = CollUtil.split(fileIds, userFileQuerySize);
        ArrayList<AlgorithmUserFilePo> resultList = new ArrayList<>();
        splitUserIdList.forEach(fileList ->{
            LambdaQueryWrapper<AlgorithmUserFilePo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(AlgorithmUserFilePo::getUserId,AlgorithmUserFilePo::getFileId,
                            AlgorithmUserFilePo::getMetaRowKey,AlgorithmUserFilePo::getShotTime)
                    .eq(AlgorithmUserFilePo::getUserId, userId)
                    .eq(AlgorithmUserFilePo::getOwnerType, ownerType)
                    .in(AlgorithmUserFilePo::getFileId, fileList)
                    .eq(AlgorithmUserFilePo::getDelFlag, DelFlagEnum.NO.getCode())
                    .isNotNull(AlgorithmUserFilePo::getMetaRowKey);
            List<AlgorithmUserFilePo> userFilePoList = userFileMapper.selectList(queryWrapper);
            if (CollUtil.isNotEmpty(userFilePoList)){
                resultList.addAll(userFilePoList);
            }
        });
        return assembler.toAlgorithmUserFileEntity(resultList);
    }

    @Override
    public List<AlgorithmUserFileEntity> selectHbaseUserFileByTime(String userId, Integer ownerType, List<String> fileIds, LocalDateTime startDate, LocalDateTime endDate) {
        if (CollUtil.isEmpty(fileIds)){
            return Collections.emptyList();
        }
        List<List<String>> splitUserIdList = CollUtil.split(fileIds, userFileQuerySize);
        ArrayList<AlgorithmUserFilePo> resultList = new ArrayList<>();
        splitUserIdList.forEach(fileList ->{
            LambdaQueryWrapper<AlgorithmUserFilePo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(AlgorithmUserFilePo::getUserId,AlgorithmUserFilePo::getFileId,
                            AlgorithmUserFilePo::getMetaRowKey,AlgorithmUserFilePo::getShotTime)
                    .eq(AlgorithmUserFilePo::getUserId, userId)
                    .eq(AlgorithmUserFilePo::getOwnerType, ownerType)
                    .in(AlgorithmUserFilePo::getFileId, fileList)
                    .eq(AlgorithmUserFilePo::getDelFlag, DelFlagEnum.NO.getCode())
                    .between(AlgorithmUserFilePo::getShotTime, startDate, endDate)
                    .isNotNull(AlgorithmUserFilePo::getMetaRowKey);
            List<AlgorithmUserFilePo> userFilePoList = userFileMapper.selectList(queryWrapper);
            if (CollUtil.isNotEmpty(userFilePoList)){
                resultList.addAll(userFilePoList);
            }
        });
        return assembler.toAlgorithmUserFileEntity(resultList);
    }
}
