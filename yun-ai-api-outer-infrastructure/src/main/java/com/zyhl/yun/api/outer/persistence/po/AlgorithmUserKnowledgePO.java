package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户知识库表
 * <AUTHOR>
 * @date 2025/04/14
 */
@Data
@TableName("algorithm_user_knowledge")
@EqualsAndHashCode(callSuper = false)
public class AlgorithmUserKnowledgePO implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 用户id;分区字段（100个）
     */
    @TableField("user_id")
    private String userId;

    /**
     * 用户是否选择知识库 选择，1--已选择；0--未选择；
     */
    @TableField("selected")
    private Integer selected;

    /**
     * 属主ID
     */
    @TableField("owner_id")
    private String ownerId;

    /**
     * 知识库目录ID
     */
    @TableField("folder_id")
    private String folderId;

    /**
     * 业务类型
     */
    @TableField("owner_type")
    private Integer ownerType;

    /**
     * 头像
     */
    @TableField("profile_photo")
    private String profilePhoto;

    /**
     * 知识库名称
     */
    @TableField("name")
    private String name;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 公开/私密；0私密（默认） 1 公开
     */
    @TableField("open_level")
    private Integer openLevel;

    /**
     * 删除标识；0--正常；1--已删除
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 创建人Id
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新人Id
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 0--默认；1--笔记同步；
     */
    @TableField("biz_type")
    private Integer bizType;

}