package com.zyhl.yun.api.outer.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileStatisticsEntity;
import com.zyhl.yun.api.outer.domain.vo.knowledge.PersonalKnowledgeCountVO;
import com.zyhl.yun.api.outer.persistence.po.UserKnowledgeFilePO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 个人知识库文件
 * <AUTHOR>
 */
public interface UserKnowledgeFileMapper extends BaseMapper<UserKnowledgeFilePO> {

    @Select("SELECT \n" +
            "    COUNT(*) AS fileCount,\n" +
            "    IFNULL(SUM(CASE WHEN ai_status = 1 THEN 1 ELSE 0 END), 0) AS fileParsedCount,\n" +
            "    IFNULL(SUM(CASE WHEN ai_status IN (0, 2) THEN 1 ELSE 0 END), 0) AS fileUnparsedCount,\n" +
            "    IFNULL(SUM(CASE WHEN ai_status = 1 AND create_time >= #{countTime} THEN 1 ELSE 0 END), 0) AS newFileParsedCount\n" +
            "FROM \n" +
            "    algorithm_user_knowledge_file\n" +
            "WHERE \n" +
            "    user_id = #{userId} " +
            "   AND file_type = 1 " +
            "   AND del_flag = 0 " +
            "   AND base_id in (select id from algorithm_user_knowledge where del_flag = 0 and biz_type = 0 and user_id = #{userId})")
    PersonalKnowledgeCountVO countByUserId(@Param("userId") String userId,@Param("countTime") String countTime);

    @Select({
            "<script>",
            "SELECT ",
            "  base_id as baseId, ",
            "  count(ai_status) as totalCount, ",
            "  IFNULL(SUM(CASE WHEN ai_status = 1 THEN 1 ELSE 0 END), 0) as processed, ",
            "  IFNULL(SUM(CASE WHEN ai_status = 0 THEN 1 ELSE 0 END), 0) as untreated ",
            "FROM algorithm_user_knowledge_file ",
            "WHERE base_id IN ",
            "  <foreach collection='baseIds' item='id' open='(' separator=',' close=')'>",
            "    #{id}",
            "  </foreach> ",
            "  AND file_type = 1 AND del_flag = 0 ",
            "GROUP BY base_id",
            "</script>"
    })
    List<UserKnowledgeFileStatisticsEntity> findStatisticsByBaseId(@Param("baseIds") List<Long> baseIds);

    @Select({
            "<script>",
            "SELECT ",
            "  base_id as baseId, ",
            "  count(ai_status) as totalCount, ",
            "  IFNULL(SUM(CASE WHEN ai_status = 1 THEN 1 ELSE 0 END), 0) as processed, ",
            "  IFNULL(SUM(CASE WHEN ai_status = 0 THEN 1 ELSE 0 END), 0) as untreated ",
            "FROM algorithm_user_knowledge_file ",
            "WHERE base_id IN ",
            "  <foreach collection='baseIds' item='id' open='(' separator=',' close=')'>",
            "    #{id}",
            "  </foreach> ",
            "  AND file_type = 1 AND del_flag = 0 ",
            "GROUP BY base_id",
            "</script>"
    })
    List<UserKnowledgeFileStatisticsEntity> findStatisticsByBaseIdForInfo(@Param("baseIds") List<Long> baseIds);

    @Select({
            "<script>",
            "SELECT * ",
            "FROM algorithm_user_knowledge_file ",
            "WHERE user_id = #{userId} and file_id IN ",
            "  <foreach collection='fileIds' item='id' open='(' separator=',' close=')'>",
            "    #{id}",
            "  </foreach> ",
            "  AND del_flag = 0 ",
            "</script>"
    })
    List<UserKnowledgeFileEntity> batchGetByUserIdAndFileIds(@Param("userId") String userId,@Param("fileIds") List<String> fileIds);

    @Select({
            "<script>",
            "SELECT ",
            "  base_id as baseId, ",
            "  count(ai_status) as totalCount, ",
            "  IFNULL(SUM(CASE WHEN ai_status = 1 THEN 1 ELSE 0 END), 0) as processed, ",
            "  IFNULL(SUM(CASE WHEN ai_status IN (0, 2) THEN 1 ELSE 0 END), 0) as untreated ",
            "FROM algorithm_user_knowledge_file ",
            "WHERE base_id IN ",
            "  <foreach collection='baseIds' item='id' open='(' separator=',' close=')'>",
            "    #{id}",
            "  </foreach> ",
            "  AND  user_id = #{userId} AND file_type = 1 AND del_flag = 0 ",
            "GROUP BY base_id",
            "</script>"
    })
    List<UserKnowledgeFileStatisticsEntity> findStatisticsByUserIdAndBaseIds(@Param("userId") String userId, @Param("baseIds") List<Long> baseIds);

    // 根据用户ID和文件ID查询记录
    @Select("SELECT * FROM algorithm_user_knowledge_file \n" +
            "WHERE user_id = #{userId} AND file_id = #{fileId} AND del_flag = 0 ")
    UserKnowledgeFileEntity selectByUserIdAndFileId(
            @Param("userId") String userId,
            @Param("fileId") String fileId);

    // 查询指定父文件下的所有子文件
    @Select(" SELECT * FROM algorithm_user_knowledge_file \n" +
            "  WHERE user_id = #{userId} AND parent_file_id = #{parentFileId} AND del_flag = 0 ")
    List<UserKnowledgeFileEntity> selectChildrenByParentId(
            @Param("userId") String userId,
            @Param("parentFileId") String parentFileId);
}
