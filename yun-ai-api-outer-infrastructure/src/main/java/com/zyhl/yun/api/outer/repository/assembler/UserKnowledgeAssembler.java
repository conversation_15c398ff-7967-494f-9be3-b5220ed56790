package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeEntity;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmUserKnowledgePO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 类转换 UserKnowledgeEntity <--> AlgorithmUserKnowledgePO
 *
 * <AUTHOR>
 * @date 2025/04/14
 */
@Mapper(componentModel = "spring")
public interface UserKnowledgeAssembler {


    /**
     * entity转po
     * @param entity po
     * @return po
     */
    AlgorithmUserKnowledgePO toAlgorithmUserKnowledgePO(UserKnowledgeEntity entity);

    /**
     * po转entity
     * @param po po
     * @return entity
     */
    UserKnowledgeEntity toUserKnowledgeEntity(AlgorithmUserKnowledgePO po);

    /**
     * 转换列表
     * @param poList polist
     * @return 列表
     */
    List<UserKnowledgeEntity> toUserKnowledgeEntityList(List<AlgorithmUserKnowledgePO> poList);

}