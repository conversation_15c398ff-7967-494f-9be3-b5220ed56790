package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.hbase.domain.datahelper.repository.HbaseRepository;
import com.zyhl.hcy.yun.ai.common.base.utils.HbaseAiTextResultUtil;
import com.zyhl.hcy.yun.ai.common.model.api.dto.TextModelMessageDTO;
import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelRoleEnum;
import com.zyhl.yun.api.outer.config.IntentionContextProperties;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.constants.RegConst;
import com.zyhl.yun.api.outer.domain.dto.redis.HistoryDialogInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.resp.AiTextResultRespParameters;
import com.zyhl.yun.api.outer.domain.valueobject.DialogueFlowResult;
import com.zyhl.yun.api.outer.enums.DialogueIntentionEnum;
import com.zyhl.yun.api.outer.enums.chat.TalkTypeEnum;
import com.zyhl.yun.api.outer.persistence.po.AiTextResultPO;
import com.zyhl.yun.api.outer.repository.AiTextResultRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;
import com.zyhl.yun.api.outer.repository.assembler.AiTextResultAssembler;
import com.zyhl.yun.api.outer.vo.DialogueIntentionVO;
import com.zyhl.yun.api.outer.vo.IntentEntityVO;
import com.zyhl.yun.api.outer.vo.KeyValueVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AI任务hbase记录
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
public class AiTextResultRepositoryImpl implements AiTextResultRepository {

    @Resource
    private HbaseRepository hbaseRepository;

    @Resource
    private AiTextResultAssembler assembler;

    @Resource
    private RedisOperateRepository redisOperateRepository;

    @Resource
    private AlgorithmChatContentRepository algorithmChatContentRepository;

    @Resource
    private ModelProperties modelProperties;

    @Resource
    private IntentionContextProperties introspectionProperties;

    @Override
    public AiTextResultEntity getByRowKey(String rowKey) {
        long start = System.currentTimeMillis();
        List<AiTextResultPO> list = hbaseRepository.selectList(Arrays.asList(rowKey), AiTextResultPO.class);
        log.info("【hbase】查询记录，rowKey:{}，耗时：{}", rowKey, System.currentTimeMillis() - start);
        if (CollUtil.isEmpty(list)) {
            log.info("【hbase】数据不存在，rowKey:{}", rowKey);
            return null;
        }
        return assembler.toAiTextResultEntity(list.get(0));
    }

    @Override
    public AiTextResultEntity getByRowKey(String userId, Long dialogId) {
        return getByRowKey(AiTextResultRepository.createRowKey(userId, dialogId));
    }

    @Override
    public AiTextResultEntity getByRowKey(String userId, String dialogId) {
        return getByRowKey(AiTextResultRepository.createRowKey(userId, dialogId));
    }

    @Override
    public Boolean save(AiTextResultEntity entity) {
        if (CharSequenceUtil.isEmpty(entity.getRowKey())) {
            entity.setRowKey(AiTextResultRepository.createRowKey(entity.getUserId(), entity.getDialogueId()));
        }
        AiTextResultPO po = assembler.toAiTextResult(entity);
        long start = System.currentTimeMillis();
        Boolean result = hbaseRepository.saveList(Collections.singletonList(po), AiTextResultPO.class);
        log.info("【hbase】保存记录，rowKey:{}，耗时：{}", entity.getRowKey(), System.currentTimeMillis() - start);
        return result;
    }


    @Override
    public Boolean add(String userId, Long dialogId, String reqParameters, String attachment, String history) {
        AiTextResultPO po = new AiTextResultPO();
        po.setRowKey(AiTextResultRepository.createRowKey(userId, dialogId));
        po.setUserId(userId);
        po.setReqParameters(reqParameters);
        po.setAttachment(attachment);
        po.setHistory(history);

        long start = System.currentTimeMillis();
        Boolean result = hbaseRepository.saveList(Collections.singletonList(po), AiTextResultPO.class);
        log.info("【hbase】新增记录，rowKey:{}，耗时：{}", po.getRowKey(), System.currentTimeMillis() - start);
        return result;
    }

    @Override
    public Boolean update(String userId, Long dialogId, String historyJson, String respJson) {
        // 设置需要更新的字段
        AiTextResultPO po = new AiTextResultPO();
        po.setRowKey(AiTextResultRepository.createRowKey(userId, dialogId));
        po.setUserId(userId);
        if (!CharSequenceUtil.isEmpty(historyJson)) {
            po.setHistory(historyJson);
        }
        if (!CharSequenceUtil.isEmpty(respJson)) {
            po.setRespParameters(respJson);
        }

        long start = System.currentTimeMillis();
        Boolean result = hbaseRepository.saveList(Collections.singletonList(po), AiTextResultPO.class);
        log.info("【hbase】更新记录，rowKey:{}，耗时：{}", po.getRowKey(), System.currentTimeMillis() - start);
        return result;
    }

    @Override
    public Boolean update(String userId, Long dialogId, String taskId) {
        if (CharSequenceUtil.isEmpty(taskId)) {
            return true;
        }

        // 设置需要更新的字段
        AiTextResultPO po = new AiTextResultPO();
        po.setRowKey(AiTextResultRepository.createRowKey(userId, dialogId));
        po.setUserId(userId);
        po.setTaskId(taskId);

        long start = System.currentTimeMillis();
        Boolean result = hbaseRepository.saveList(Collections.singletonList(po), AiTextResultPO.class);
        log.info("【hbase】更新记录，rowKey:{}，耗时：{}", po.getRowKey(), System.currentTimeMillis() - start);
        return result;
    }

    @Override
    public Boolean update(String userId, Long dialogId, List<TextModelMessageDTO> historyList, String respJson) {
        String historyJson = JsonUtil.toJson(historyList);

        return update(userId, dialogId, historyJson, respJson);
    }

    @Override
    public Boolean update(String userId, Long dialogId, List<TextModelMessageDTO> historyList, AiTextResultRespParameters respResult) {
        String historyJson = JsonUtil.toJson(historyList);
        String respJson = JsonUtil.toJson(respResult);

        return update(userId, dialogId, historyJson, respJson);
    }

    @Override
    public void updateRespParameters(String userId, Long dialogId, String respParameters) {
        update(userId, dialogId, StringUtils.EMPTY, respParameters);
    }

    @Override
    public String getHistory(String userId, Long dialogId) {
        String rowKey = AiTextResultRepository.createRowKey(userId, dialogId);
        long start = System.currentTimeMillis();
        List<AiTextResultPO> list = hbaseRepository.selectList(Arrays.asList(rowKey), AiTextResultPO.class);
        log.info("【hbase】查询历史对话，rowKey:{}，耗时：{}", rowKey, System.currentTimeMillis() - start);
        if (CollUtil.isEmpty(list)) {
            log.info("【hbase】数据不存在，rowKey:{}", rowKey);
            return "";
        }

        return CharSequenceUtil.nullToDefault(list.get(0).getHistory(), "");
    }

    @Override
    public String getHistory(AlgorithmChatContentEntity contentEntity) {
        if (contentEntity == null) {
            return "";
        }
        return getHistory(contentEntity.getUserId(), contentEntity.getId());
    }


    @Override
    public List<TextModelMessageDTO> getHistoryList(String userId, String sessionId) {
        return getHistoryList(userId, CharSequenceUtil.isEmpty(sessionId) ? null : Long.valueOf(sessionId));
    }

    @Override
    public List<TextModelMessageDTO> getHistoryList(String userId, Long sessionId) {
        List<TextModelMessageDTO> result = new ArrayList<>();
        if (sessionId == null) {
            return result;
        }

        // 查询对话记录，先查redis，没有则查tidb
        List<HistoryDialogInfoDTO> historyDialogInfoList = redisOperateRepository.getHistoryDialogInfoList(String.valueOf(sessionId));
        if (ObjectUtil.isEmpty(historyDialogInfoList)) {
            // redis 没数据则查询tidb数据库
            historyDialogInfoList = new ArrayList<>();
            List<AlgorithmChatContentEntity> contentList = algorithmChatContentRepository.getContentListBySessionId(sessionId, introspectionProperties.getMaxCount(), userId);
            if (null != contentList) {
                for (int i = contentList.size() - 1; i >= 0; i--) {
                    AlgorithmChatContentEntity content = contentList.get(i);
                    if (TalkTypeEnum.isTask(content.getTalkType())) {
                        log.info("不记录历史 dialogueId:{}", content.getId());
                        continue;
                    }
                    historyDialogInfoList.add(HistoryDialogInfoDTO.builder()
                            .dialogId(String.valueOf(content.getId()))
                            .intentionCode(content.getToolsCommand())
                            .build());
                }
            }
        }
        if (ObjectUtil.isEmpty(historyDialogInfoList)) {
            log.info("【hbase】没有历史对话记录，userId:{}，sessionId:{}", userId, sessionId);
            return result;
        }

        // 生成对话id列表，只要文本意图(000,036,云手机)的对话
        List<String> dialogIdList = new ArrayList<>();
        historyDialogInfoList.stream()
                .filter(dialogInfo -> {
                    if (DialogueIntentionEnum.isTextIntention(dialogInfo.getIntentionCode())) {
                        return true;
                    } else if (DialogueIntentionEnum.isTextToolIntention(dialogInfo.getIntentionCode())) {
                        return true;
                    }
                    return DialogueIntentionEnum.isCloudPhoneTypeIntentionForAll(dialogInfo.getIntentionCode());
                })
                .forEach(dialogInfo -> dialogIdList.add(dialogInfo.getDialogId()));

        // 查询hbase对话记录
        List<AiTextResultEntity> entityList = getByRowKeyList(userId, dialogIdList.subList(Math.max(0, dialogIdList.size() - modelProperties.getHistoryDialogueCount()), dialogIdList.size()));
        if (ObjectUtil.isEmpty(entityList)) {
            return result;
        }

        // 转换历史对话列表
        for (AiTextResultEntity entity : entityList) {
            if (CharSequenceUtil.isEmpty(entity.getReqParameters()) || CharSequenceUtil.isEmpty(entity.getRespParameters())) {
                continue;
            }

            // 没有响应信息的跳过
            AiTextResultRespParameters resp = JsonUtil.parseObject(entity.getRespParameters(), AiTextResultRespParameters.class);
            String answer = CharSequenceUtil.nullToDefault(resp.getData(), "").replaceAll(RegConst.REG_MODEL_THINK, "");
            if (ObjectUtil.isEmpty(answer)) {
                DialogueIntentionVO.IntentionInfo mainIntention = DialogueIntentionVO.getMainIntention(resp.getIntentionVO());
                if (Objects.nonNull(mainIntention)) {
                    if (!DialogueIntentionEnum.isCloudPhoneTypeIntentionForAll(mainIntention.getIntention())) {
                        continue;
                    } else if (ObjectUtil.isEmpty(mainIntention.getEntityList())) {
                        continue;
                    }
                    // 云手机业务拼接主意图的实体
                    StringBuilder keyword = new StringBuilder();
                    for (IntentEntityVO intentEntityVO : mainIntention.getEntityList()) {
                        for (KeyValueVO keyValueVO : intentEntityVO.getMetaDataList()) {
                            keyword.append(" ").append(String.join(" ", keyValueVO.getValue()));
                        }
                    }
                    if (ObjectUtil.isNotEmpty(keyword)) {
                        answer = keyword.substring(1);
                    } else {
                        continue;
                    }
                } else {
                    continue;
                }
            }

            // 没有问的跳过
            String inputDialogue = HbaseAiTextResultUtil.getInputDialogue(entity.getReqParameters());
            if (ObjectUtil.isEmpty(inputDialogue)) {
                // 输入空，查询附件内容做为输入
                inputDialogue = entity.getAttachment();
                if (ObjectUtil.isEmpty(inputDialogue)) {
                    continue;
                }
                log.info("entity dialogueId:{}, 查询附件内容 length:{},", entity.getDialogueId(), inputDialogue.length());
            }

            // 问
            TextModelMessageDTO user = new TextModelMessageDTO();
            user.setRole(TextModelRoleEnum.USER.getName());
            user.setContent(inputDialogue);
            result.add(user);
            // 答
            TextModelMessageDTO assistant = new TextModelMessageDTO();
            assistant.setRole(TextModelRoleEnum.ASSISTANT.getName());
            //需要替换大模型角标[1],[2],[3]...
            assistant.setContent(answer.replaceAll(RegConst.TEXT_MODE_CITATION, ""));
            result.add(assistant);
        }

        return result;
    }

    /**
     * 根据rowKey列表查询任务结果
     *
     * @param userId       用户id
     * @param dialogIdList 对话id列表
     * @return 实体对象列表
     */
    @Override
    public List<AiTextResultEntity> getByRowKeyList(String userId, List<String> dialogIdList) {
        if (CollectionUtils.isEmpty(dialogIdList)) {
            log.info("【hbase】查询数据，dialogIdList为空 userId:{}", dialogIdList);
            return null;
        }

        long start = System.currentTimeMillis();

        // 获取rowKeyList列表
        List<String> rowKeyList = dialogIdList.stream()
                .map(dialogId -> AiTextResultRepository.createRowKey(userId, dialogId))
                .collect(Collectors.toList());

        // 根据rowKey列表查询任务结果
        List<AiTextResultPO> list = hbaseRepository.selectList(rowKeyList, AiTextResultPO.class);
        log.info("【hbase】查询记录，dialogIdList:{}，耗时：{}", dialogIdList, System.currentTimeMillis() - start);

        if (CollUtil.isEmpty(list)) {
            log.info("【hbase】数据不存在，dialogIdList:{}", dialogIdList);
            return null;
        }

        return assembler.toAiTextResultEntityList(list);
    }

    @Override
    public void updateAddFlowResult(String userId, Long dialogId, AiTextResultRespParameters respParameters) {
        List<AiTextResultEntity> entityList = getByRowKeyList(userId,
                Collections.singletonList(String.valueOf(dialogId)));
        List<DialogueFlowResult> newList = respParameters.getOutputList();
        if (CollUtil.isEmpty(entityList) || CollUtil.isEmpty(newList)) {
            return;
        }
        AiTextResultEntity aiTextResultEntity = entityList.get(0);
        AiTextResultRespParameters oldRespParameters = JSONObject.parseObject(aiTextResultEntity.getRespParameters(),
                AiTextResultRespParameters.class);
        List<DialogueFlowResult> allList = new ArrayList<>();
        List<DialogueFlowResult> oldList = oldRespParameters.getOutputList();
        int index = 0;
        // 追加旧的
        if (CollUtil.isNotEmpty(oldList)) {
            allList.addAll(oldList);
            index = oldList.get(oldList.size() - 1).getIndex() + 1;
        }
        // 添加新的
        for (DialogueFlowResult newBean : newList) {
            newBean.setIndex(index++);
            allList.add(newBean);
        }
        // 重新设置
        respParameters.setOutputList(allList);

        // 定义po，其他值空，只更新respParameters
        AiTextResultPO po = new AiTextResultPO();
        po.setRowKey(AiTextResultRepository.createRowKey(userId, dialogId));
        po.setUserId(userId);
        po.setRespParameters(JSONUtil.toJsonStr(respParameters));
        hbaseRepository.saveList(Collections.singletonList(po), AiTextResultPO.class);
    }

}
