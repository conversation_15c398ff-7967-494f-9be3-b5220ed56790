package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserDriveConfigEntity;
import com.zyhl.yun.api.outer.persistence.po.UserDriveConfigPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 类转换 UserDriveConfigEntity <--> UserDriveConfigPO
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface UserDriveConfigAssembler {

    UserDriveConfigAssembler INSTANCE = Mappers.getMapper(UserDriveConfigAssembler.class);


    /**
     * po转entity
     * @param po po
     * @return entity
     */
    UserDriveConfigEntity toUserDriveConfigEntityEntity(UserDriveConfigPO po);

    /**
     * entity转po
     * @param entity po
     * @return po
     */
    UserDriveConfigPO toUserDriveConfigPo(UserDriveConfigEntity entity);


}
