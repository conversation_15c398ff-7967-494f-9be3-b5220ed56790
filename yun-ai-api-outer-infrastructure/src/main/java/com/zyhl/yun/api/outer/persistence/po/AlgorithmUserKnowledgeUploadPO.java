package com.zyhl.yun.api.outer.persistence.po;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 算法用户知识上传持久化对象
 *
 * <AUTHOR>
 * @date 2025-04-16 15:45:13
 */
@Data
@TableName("algorithm_user_knowledge_upload")
public class AlgorithmUserKnowledgeUploadPO {

    /**
     * 主键 ID，上传任务ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID，分区字段（500个）
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 知识库ID
     */
    @TableField(value = "base_id")
    private Long baseId;

    /**
     * 目标父目录ID，独立空间的目录ID(用于选择知识库目录上传文件的场景)
     */
    @TableField(value = "target_parent_file_id")
    private String targetParentFileId;

    /**
     * 目标父目录路径
     */
    @TableField(value = "target_parent_file_path")
    private String targetParentFilePath;

    /**
     * 父目录上传任务ID，来源父目录上传任务ID，遍历文件夹时，子文件夹和文件需要保存父文件夹的上传ID 用于判重
     */
    @TableField(value = "parent_id")
    private Long parentId;

    /**
     * 父目录ID，使用的是个人云的目录ID
     */
    @TableField(value = "parent_file_id")
    private String parentFileId;

    /**
     * 文件ID，根据 fromResourceType 不同，存储不同的ID
     * - fromResourceType=0, 4, 5 个人云的文件ID
     * - fromResourceType=1 邮件ID
     * - fromResourceType=2 笔记ID
     * - fromResourceType=3 空
     */
    @TableField(value = "file_id")
    private String fileId;

    /**
     * 独立空间的文件ID，当创建独立空间的目录或文件后记录下
     */
    @TableField(value = "target_file_id")
    private String targetFileId;

    /**
     * 文件资源类型
     * - 0 云盘个人云（默认）
     * - 1 邮件
     * - 2 笔记
     * - 3 网址
     * - 4 本地文件(端侧上传)
     */
    @TableField(value = "resource_type")
    private Integer resourceType;

    /**
     * 资源信息
     * - fromResourceType=3 则存储的是网址信息，json格式
     * - fromResourceType=2 存储的是笔记类型(待定？？？)
     */
    @TableField(value = "resource")
    private String resource;

    /**
     * 文件名称
     */
    @TableField(value = "file_name")
    private String fileName;

    /**
     * 文件归属，个人云 owner_id= user_id
     */
    @TableField(value = "owner_id")
    private String ownerId;

    /**
     * 业务类型
     * -1 - 未知类型
     * 12 - 邮箱
     * 11-ai 知识库
     * 1-personal 个人云
     * 2-group 圈子
     * 3-shareGroup 共享群
     * 4-family 家庭云
     * 10 -mount 挂载盘
     * 6-note 笔记
     * 7-cardpackage 卡包
     * 8-system 系统空间
     * 9-partner 合作空间
     * 5-activity 活动空间 照片直播
     */
    @TableField(value = "owner_type")
    private Integer ownerType;

    /**
     * paas平台编码
     */
    @TableField(value = "paas_code")
    private String paasCode;

    /**
     * 文件哈希名
     */
    @TableField(value = "hash_name")
    private String hashName;

    /**
     * 文件哈希值
     */
    @TableField(value = "hash_value")
    private String hashValue;

    /**
     * 文件类型
     * - 1-文件
     * - 2-目录
     */
    @TableField(value = "file_type")
    private Integer fileType;

    /**
     * 文件/目录分类，见字典定义
     * 1 图片
     * 2 音频
     * 3 视频
     * 4 文档
     * 5 应用
     * 6 压缩文件
     * 0 其他
     * 100 普通目录
     */
    @TableField(value = "category")
    private Integer category;

    /**
     * 文件大小
     */
    @TableField(value = "file_size")
    private Long fileSize;

    /**
     * 文件后缀
     */
    @TableField(value = "extension")
    private String extension;

    /**
     * 文件修改时间
     */
    @TableField(value = "file_updated_at")
    private Date fileUpdatedAt;

    /**
     * 文件创建时间
     */
    @TableField(value = "file_created_at")
    private Date fileCreatedAt;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date updateTime;

    /**
     * 删除标识
     * - 0--正常
     * - 1--已删除
     * - 2--删除中
     */
    @TableField(value = "del_flag")
    private Integer delFlag;

    /**
     * 上传结果状态
     * - 0 未处理
     * - 1 处理中（云盘文件导入使用）
     * - 2 成功（文件夹下面没有文件也是成功状态）
     * - 3 失败
     */
    @TableField(value = "upload_status")
    private Integer uploadStatus;

    /**
     * 上传结果码
     * - 0000 成功
     * - 其他则错误码
     */
    @TableField(value = "result_code")
    private String resultCode;

    /**
     * 上传结果，记录错误信息
     */
    @TableField(value = "result_msg")
    private String resultMsg;
}
