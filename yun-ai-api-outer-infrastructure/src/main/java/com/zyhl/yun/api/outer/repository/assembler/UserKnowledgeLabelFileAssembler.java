package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeLabelFileEntity;
import com.zyhl.yun.api.outer.persistence.po.UserKnowledgeLabelFilePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 类转换 UserKnowledgeLabelFileEntity <--> UserKnowledgeLabelFilePO
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface UserKnowledgeLabelFileAssembler {

    UserKnowledgeLabelFileAssembler INSTANCE = Mappers.getMapper(UserKnowledgeLabelFileAssembler.class);

    /**
     * po转entity
     * @param po po
     * @return entity
     */
    UserKnowledgeLabelFileEntity toUserKnowledgeLabelFileEntity(UserKnowledgeLabelFilePO po);

    /**
     * po转entity
     * @param poList poList
     * @return entity列表
     */
    List<UserKnowledgeLabelFileEntity> toUserKnowledgeLabelFileEntityList(List<UserKnowledgeLabelFilePO> poList);

    /**
     * entity转po
     * @param entity po
     * @return po
     */
    UserKnowledgeLabelFilePO toUserKnowledgeLabelFilePo(UserKnowledgeLabelFileEntity entity);

}
