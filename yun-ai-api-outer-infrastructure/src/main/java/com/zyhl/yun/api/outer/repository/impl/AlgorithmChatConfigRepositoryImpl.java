package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.yun.api.outer.domain.entity.ChatConfigEntity;
import com.zyhl.yun.api.outer.persistence.mapper.AlgorithmChatConfigMapper;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmChatConfigPO;
import com.zyhl.yun.api.outer.repository.AlgorithmChatConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2024年02月28日 16:23
 */
@Repository
@Slf4j
public class AlgorithmChatConfigRepositoryImpl extends ServiceImpl<AlgorithmChatConfigMapper, AlgorithmChatConfigPO> implements AlgorithmChatConfigRepository {

    @Resource
    protected UidGenerator uidGenerator;

    @Override
    public boolean saveOrUpdate(ChatConfigEntity chatConfigEntity) {
        AlgorithmChatConfigPO po = getOne(chatConfigEntity.getUserId(), chatConfigEntity.getBusinessCode());
        LocalDateTime now = LocalDateTime.now();
		if (po != null) {
			log.info("【会话设置】已经存在，更新会话设置，业务数据：{}，用户id：{}", JSONUtil.toJsonStr(chatConfigEntity.getModelType()),
					chatConfigEntity.getUserId());
            po.setModelType(chatConfigEntity.getModelType());
            po.setNetworkSearchStatus(chatConfigEntity.getNetworkSearchStatus());
            po.setUpdateTime(now);
            return baseMapper.updateById(po) == 1;
        }

        // 保存
		log.info("【会话设置】不存在，新增会话设置，业务数据：{}，用户id：{}", JSONUtil.toJsonStr(chatConfigEntity.getModelType()),
				chatConfigEntity.getUserId());
        po = new AlgorithmChatConfigPO();
        po.setId(uidGenerator.getUID());
        po.setUserId(chatConfigEntity.getUserId());
        po.setModelType(chatConfigEntity.getModelType());
        po.setBusinessCode(chatConfigEntity.getBusinessCode());
        po.setNetworkSearchStatus(chatConfigEntity.getNetworkSearchStatus());
        po.setCreateTime(now);
        po.setUpdateTime(now);
        return baseMapper.insert(po) == 1;
    }

    /**
     * 获取用户设置模型
     * @Author: WeiJingKun
     * @param userId 用户id
     * @param businessCode 业务编码
     * @return 会话设置po
     */
    private AlgorithmChatConfigPO getOne(String userId, String businessCode){
        /** 构建查询条件 */
        LambdaQueryChainWrapper<AlgorithmChatConfigPO> lambdaQueryChainWrapper = this.lambdaQuery()
                .eq(AlgorithmChatConfigPO::getUserId, userId)
                .eq(AlgorithmChatConfigPO::getBusinessCode, businessCode);
        /** 查询数据 */
        List<AlgorithmChatConfigPO> list = lambdaQueryChainWrapper.list();
        if(CollUtil.isNotEmpty(list)){
            // 用户设置的模型
            return list.get(0);
        }
        return null;
    }

    @Override
    public ChatConfigEntity get(String userId, String businessCode) {
        AlgorithmChatConfigPO po = getOne(userId, businessCode);
        if (po != null) {
            return new ChatConfigEntity(userId, po.getModelType(), businessCode, po.getNetworkSearchStatus());
        }
        return null;
    }
}
