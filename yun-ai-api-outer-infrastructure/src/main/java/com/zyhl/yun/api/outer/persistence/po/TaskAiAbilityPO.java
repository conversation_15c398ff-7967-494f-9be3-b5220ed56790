package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zyhl.yun.api.outer.enums.task.FileExpiredStatusEnum;
import com.zyhl.yun.api.outer.enums.task.TaskFeePaidStatusEnum;
import com.zyhl.yun.api.outer.enums.task.TaskFeeTypeEnum;
import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * AI输入算法任务表PO
 *
 * <AUTHOR>
 * @since 2024-03-06
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("algorithm_task_ai_ability")
@EqualsAndHashCode(callSuper = false)
public class TaskAiAbilityPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键;任务ID
     */
    @TableField("id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 算法编码，跟算法库表关联，指定调哪个厂商的算法。
     */
    @TableField("algorithm_code")
    private String algorithmCode;

    /**
     * 厂商编码,多厂商时以｜分割
     */
    @TableField("supplier_types")
    private String supplierTypes;

    /**
     * 任务状态
     *
     * @see TaskStatusEnum
     */
    @TableField("task_status")
    private String taskStatus;

    /**
     * 响应参数
     */
    @TableField("resp_param")
    private String respParam;

    /**
     * 优先级 数字越大优先级越高，建议取值范围为[0,99]
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 错误结果码
     */
    @TableField("result_code")
    private String resultCode;

    /**
     * 错误信息
     */
    @TableField("result_msg")
    private String resultMsg;

    /**
     * 当次任务执行开始时间;YYYY-MM-DD HH24:MM:SS.NNN
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 当次任务执行结束时间;YYYY-MM-DD HH24:MM:SS.NNN
     */
    @TableField("finish_time")
    private Date finishTime;

    /**
     * 过期时间 NULL--代表任务无超时限制
     */
    @TableField("expire_time")
    private Date expireTime;

    /**
     * 执行ID
     */
    @TableField("execute_id")
    private String executeId;

    /**
     * 执行次数
     */
    @TableField("execute_count")
    private Integer executeCount;

    /**
     * 来源渠道
     */
    @TableField("source_channel")
    private String sourceChannel;

    /**
     * 扩展信息
     */
    @TableField("ext_info")
    private String extInfo;

    /**
     * 业务参数
     */
    @TableField("business_param")
    private String businessParam;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 付费类型
     *
     * @see TaskFeeTypeEnum
     */
    @TableField("fee_type")
    private Integer feeType;

    /**
     * 扣费标识
     *
     * @see TaskFeePaidStatusEnum
     */
    @TableField("fee_paid_status")
    private Integer feePaidStatus;

    /**
     * 文件过期状态
     *
     * @see FileExpiredStatusEnum
     */
    @TableField("file_expired_status")
    private Integer fileExpiredStatus;

    /**
     * 删除标记：0正常，1 已删除
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 子意图编码
     */
    @TableField("sub_algorithm_code")
    private String subAlgorithmCode;

    /**
     * 父任务ID
     */
    @TableField("parent_task_id")
    private Long parentTaskId;
}
