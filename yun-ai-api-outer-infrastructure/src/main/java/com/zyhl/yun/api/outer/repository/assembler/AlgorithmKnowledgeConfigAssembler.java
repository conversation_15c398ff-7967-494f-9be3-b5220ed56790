package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.knowledge.AlgorithmKnowledgeConfigEntity;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmKnowledgeConfigPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 公共知识库配置表-Assembler
 *
 * @Author: WeiJingKun
 */
@Mapper(componentModel = "spring")
public interface AlgorithmKnowledgeConfigAssembler {

    AlgorithmKnowledgeConfigAssembler INSTANCE = Mappers.getMapper(AlgorithmKnowledgeConfigAssembler.class);

    /**
     * po转entity
     *
     * @param po po
     * @return entity
     */
    AlgorithmKnowledgeConfigEntity toEntity(AlgorithmKnowledgeConfigPO po);

    AlgorithmKnowledgeConfigPO toPo(AlgorithmKnowledgeConfigEntity entity);

    List<AlgorithmKnowledgeConfigEntity> toEntityList(List<AlgorithmKnowledgeConfigPO> poList);

    List<AlgorithmKnowledgeConfigPO> toPoList(List<AlgorithmKnowledgeConfigEntity> entityList);

}
