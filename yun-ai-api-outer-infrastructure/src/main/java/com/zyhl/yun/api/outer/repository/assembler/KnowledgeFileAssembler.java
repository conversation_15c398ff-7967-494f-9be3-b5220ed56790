package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.knowledge.KnowledgeFileEntity;
import com.zyhl.yun.api.outer.persistence.po.KnowledgeFilePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 类转换 KnowledgeFileEntity <--> KnowledgeFilePO
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface KnowledgeFileAssembler {

    KnowledgeFileAssembler INSTANCE = Mappers.getMapper(KnowledgeFileAssembler.class);


    KnowledgeFilePO toKnowledgeFilePo(KnowledgeFileEntity entity);

    KnowledgeFileEntity toKnowledgeFileEntity(KnowledgeFilePO po);

    List<KnowledgeFilePO> toKnowledgeFilePoList(List<KnowledgeFileEntity> entityList);

    List<KnowledgeFileEntity> toKnowledgeFileEntityList(List<KnowledgeFilePO> poList);

}
