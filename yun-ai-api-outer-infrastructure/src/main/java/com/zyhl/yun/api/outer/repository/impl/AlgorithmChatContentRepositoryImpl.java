package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.annotation.MethodExecutionTimeLog;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.hcy.yun.ai.common.base.hbase.domain.datahelper.repository.HbaseRepository;
import com.zyhl.hcy.yun.ai.common.base.utils.StopWatchUtil;
import com.zyhl.yun.api.outer.config.BusinessParamProperties;
import com.zyhl.yun.api.outer.config.EmailAssistantQueryAllDataTempProperties;
import com.zyhl.yun.api.outer.config.SourceChannelsProperties;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.entity.TaskAiAbilityEntity;
import com.zyhl.yun.api.outer.domain.vo.ChatCommentGetResult;
import com.zyhl.yun.api.outer.enums.ImageTransmissionTypeEnum;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.ChatStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.DelFlagEnum;
import com.zyhl.yun.api.outer.enums.chat.OutAuditStatusEnum;
import com.zyhl.yun.api.outer.enums.chat.OutResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;
import com.zyhl.yun.api.outer.external.CenterTaskExternalService;
import com.zyhl.yun.api.outer.persistence.mapper.AlgorithmChatContentMapper;
import com.zyhl.yun.api.outer.persistence.po.AiTextResultPO;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmChatContentPO;
import com.zyhl.yun.api.outer.repository.AlgorithmChatCommentRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.repository.TaskAiAbilityRepository;
import com.zyhl.yun.api.outer.repository.assembler.ChatContentAssembler;
import com.zyhl.yun.api.outer.util.RequestContextHolder;
import com.zyhl.yun.api.outer.vo.TaskRespParamVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/2/29 14:18
 */
@Slf4j
@Service
public class AlgorithmChatContentRepositoryImpl extends ServiceImpl<AlgorithmChatContentMapper, AlgorithmChatContentPO>
        implements AlgorithmChatContentRepository {

    @Resource
    private HbaseRepository hbaseRepository;

    @Resource
    private ChatContentAssembler contentAssembler;

    @Resource
    private CenterTaskExternalService centerTaskExternalService;

    @Resource
    private AlgorithmChatCommentRepository algorithmChatCommentRepository;

    @Resource
    private SourceChannelsProperties sourceChannelsProperties;

    @Resource
    private EmailAssistantQueryAllDataTempProperties emailAssistantQueryAllDataTempProperties;

    @Resource
    private BusinessParamProperties businessParamProperties;

    @Resource
    private TaskAiAbilityRepository taskAiAbilityRepository;

    /**
     * 父任务id
     */
    private static final Long PARENT_TASK_ID = 0L;

    private static final String JOIN_STR = ", ";

    @Override
    @MethodExecutionTimeLog("获取对话信息分页数据-repository")
    public PageInfo<AlgorithmChatContentEntity> contentList(AlgorithmChatContentEntity entity, int offset, int pageSize,
                                                            int needTotalCount) {
        PageMethod.offsetPage(offset, pageSize, needTotalCount == 1);

        /** 查询并过滤对话内容【列表】数据 */
        long pageStartTime = System.currentTimeMillis();
        PageInfo<AlgorithmChatContentEntity> pageInfo = PageInfo.of(queryAndCreateList(entity));
        log.info("【tidb】对话列表分页查询，耗时：{}", System.currentTimeMillis() - pageStartTime);
        List<AlgorithmChatContentEntity> dataList = pageInfo.getList();
        if (CollUtil.isEmpty(dataList)) {
            return pageInfo;
        }

        /**
         * 查询hbase数据，放入对话列表中
         * 只返回有结果的数据
         * 例如，rowKeyList有10个值，只有1个rowKey在hbase有结果，则textResultPOList只返回这1个有结果的rowKey数据
         */
        List<String> rowKeyList = dataList.stream().map(content -> centerTaskExternalService
                .createTaskRowkey(content.getUserId(), String.valueOf(content.getId()))).collect(Collectors.toList());
        long hbaseStartTime = System.currentTimeMillis();
        List<AiTextResultPO> textResultPoList = hbaseRepository.selectList(rowKeyList, AiTextResultPO.class);
        log.info("【hbase】查询历史对话，耗时：{}，rowKeyList:{}", System.currentTimeMillis() - hbaseStartTime, rowKeyList);
        if (CollUtil.isNotEmpty(textResultPoList)) {
            dataList.forEach(content -> {
                for (AiTextResultPO resultPo : textResultPoList) {
                    if (resultPo.getRowKey().equals(centerTaskExternalService.createTaskRowkey(content.getUserId(),
                            String.valueOf(content.getId())))) {
                        content.setHbaseReqParameters(resultPo.getReqParameters());
                        content.setHbaseRespParameters(resultPo.getRespParameters());
                    }
                }
            });
        }
        return pageInfo;
    }

    /**
     * 查询并构建对话内容【列表】数据
     *
     * @param entity 对话内容实体
     * @return java.util.List<com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity>
     * @Author: WeiJingKun
     */
    private List<AlgorithmChatContentEntity> queryAndCreateList(AlgorithmChatContentEntity entity) {
        // 获取对话内容【列表】数据 */
        StopWatch stopWatch = StopWatchUtil.createStarted();
        String businessType = entity.getBusinessType();
        String sourceChannel = entity.getSourceChannel();

        /**
         * 云邮渠道查询条件处理
         */
        if (sourceChannelsProperties.isMail(sourceChannel)) {
            // 针对邮箱webAI业务类型，配置展示云盘app、邮箱app、邮箱webAI渠道的历史记录
            List<String> businessTypeList = businessParamProperties.getChatContentList().getBusinessTypeMap().get(businessType);
            if (CollUtil.isNotEmpty(businessTypeList)) {
                entity.setBusinessTypeList(businessTypeList);
            } else {
                /*如果是云邮助手的渠道，历史数据需要查询全部
                 * 1、业务类型不能为空
                 * 2、云邮的渠道
                 * 3、排除unknown渠道
                 * 4、查询全部的时间节点
                 */
                if (CharSequenceUtil.isNotBlank(businessType) && !businessType.contains(emailAssistantQueryAllDataTempProperties.getRejectBusinessType())) {
                    entity.setQueryAllOldDataFlag(emailAssistantQueryAllDataTempProperties.getQueryFlag());
                    entity.setQueryAllOldDataTimeNodes(emailAssistantQueryAllDataTempProperties.getTimeNodes());
                    entity.setYunMailAllBusinessTypeList(sourceChannelsProperties.getYunMailAllBusinessTypeRemoveUnknown(sourceChannel));
                }
            }
        }

        // 【非小天】渠道，限制搜索X天内的历史记录【创建时间】
        if (!sourceChannelsProperties.isXiaoTian(sourceChannel)) {
            entity.setGeCreateTime(DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -businessParamProperties.getChatContentList().getQueryMaxDays())));
        }

        List<AlgorithmChatContentEntity> chatContentEntityList = new ArrayList<>();
        if (entity.isContentListV2()) {
            // 历史对话列表V2，走新的查询语句
            chatContentEntityList = baseMapper.findListV2(entity);
        } else {
            chatContentEntityList = baseMapper.findList(entity);
        }
        log.info("历史对话查询-AlgorithmChatContentRepositoryImpl-queryAndCreateList，baseMapper.findList：{}", StopWatchUtil.logTime(stopWatch));
        // 构建对话内容数据 */
        return createContentData(entity.getUserId(), chatContentEntityList);
    }


    /**
     * 查询并构建对话内容【单个】数据
     *
     * @param entity 对话内容实体
     * @return java.util.List<com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity>
     * @Author: WeiJingKun
     */
    private AlgorithmChatContentEntity queryAndCreateOne(AlgorithmChatContentEntity entity) {
        // 获取对话内容【轮巡】数据 */
        List<AlgorithmChatContentEntity> chatContentEntityList = baseMapper.findPollingUpdateData(entity);

        // 构建对话内容数据 */
        List<AlgorithmChatContentEntity> contentEntityList = createContentData(entity.getUserId(), chatContentEntityList);
        if (CollUtil.isNotEmpty(contentEntityList)) {
            return contentEntityList.get(0);
        }
        return null;
    }

    /**
     * 查询并构建对话内容【单个】数据
     *
     * @param entity 对话内容实体
     * @return java.util.List<com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity>
     * @Author: WeiJingKun
     */
    private AlgorithmChatContentEntity queryAndCreateOneV2(AlgorithmChatContentEntity entity) {
        // 获取对话内容【轮巡】数据 */
        AlgorithmChatContentPO algorithmChatContent = this.baseMapper.selectById(entity.getId());
        if (Objects.isNull(algorithmChatContent)) {
            log.info("对话内容数据为空，id：{}", entity.getId());
            return null;
        }
        //转换获得需要返回的实体
        AlgorithmChatContentEntity contentEntity = contentAssembler.toChatContentEntity(algorithmChatContent);

        String userId = contentEntity.getUserId();
        //数据越权
        if (StringUtils.isNotEmpty(userId) && !RequestContextHolder.getUserId().equals(userId)) {
            throw new YunAiBusinessException(ResultCodeEnum.ERROR_FORBIDDEN);
        }
        if (Objects.isNull(algorithmChatContent.getTaskId())) {
            log.info("对话内容任务为空，taskId：{}", entity.getTaskId());
            return contentEntity;
        }

        //获取多任务, 把父子都查出来
        List<TaskAiAbilityEntity> taskAiAbilityEntityList =
                taskAiAbilityRepository.getMultipleTaskById(algorithmChatContent.getTaskId());
        if (CollectionUtils.isEmpty(taskAiAbilityEntityList)) {
            log.info("对话内容任务为空，taskId：{}", entity.getTaskId());
            return contentEntity;
        }
        log.info("查询对话任务详情：{}", JsonUtil.toJson(taskAiAbilityEntityList));
        Optional<TaskAiAbilityEntity> parentOptional = taskAiAbilityEntityList.stream()
                .filter(taskAiAbilityEntity -> PARENT_TASK_ID.equals(taskAiAbilityEntity.getParentTaskId()))
                .findFirst();
        if (Boolean.FALSE.equals(parentOptional.isPresent())) {
            log.info("对话内容任务为空，taskId：{}", entity.getTaskId());
            return contentEntity;
        }
        //若对话内容表关联的任务没有子任务，则直接用任务表数据
        if (taskAiAbilityEntityList.size() == 1) {
            TaskAiAbilityEntity taskAiAbilityEntity = parentOptional.get();
            contentEntity.setTaskStatus(taskAiAbilityEntity.getTaskStatus());
            contentEntity.setFeePaidStatus(taskAiAbilityEntity.getFeePaidStatus());
            contentEntity.setFileExpiredStatus(taskAiAbilityEntity.getFileExpiredStatus());
            contentEntity.setResultCode(taskAiAbilityEntity.getResultCode());
            contentEntity.setResultMsg(taskAiAbilityEntity.getResultMsg());
            if (TaskStatusEnum.PROCESS_FINISH.getCode().equals(taskAiAbilityEntity.getTaskStatus())) {
                contentEntity.setRespParam(taskAiAbilityEntity.getRespParam());
            }
            return contentEntity;
        }
        //关联子任务，需要根据子任务来判断, 有一个任务进行中，则整个是进行中，当存在一个成功并且没用进行中的，则整个是成功，当两个都是失败，则整个是失败
        boolean hasInProcessTask = false;
        boolean hasSuccessTask = false;

        List<TaskRespParamVO> respParamVoList = new ArrayList<>();
        TaskAiAbilityEntity parentTask = null;
        taskAiAbilityEntityList = taskAiAbilityEntityList.stream()
                .filter(taskAiAbilityEntity -> !PARENT_TASK_ID.equals(taskAiAbilityEntity.getParentTaskId()))
                .collect(Collectors.toList());
        for (TaskAiAbilityEntity taskAiAbilityEntity : taskAiAbilityEntityList) {
            if (TaskStatusEnum.IN_PROCESS.getCode().equals(taskAiAbilityEntity.getTaskStatus())
                    || TaskStatusEnum.NO_PROCESS.getCode().equals(taskAiAbilityEntity.getTaskStatus())) {
                hasInProcessTask = true;
            } else if (TaskStatusEnum.PROCESS_FINISH.getCode().equals(taskAiAbilityEntity.getTaskStatus())) {
                hasSuccessTask = true;
                String successRespParam = taskAiAbilityEntity.getRespParam();
                if (CharSequenceUtil.isNotBlank(successRespParam)) {
                    respParamVoList.addAll(JSONArray.parseArray(successRespParam, TaskRespParamVO.class));
                }
            }
            if (PARENT_TASK_ID.equals(taskAiAbilityEntity.getParentTaskId())) {
                parentTask = taskAiAbilityEntity;
            }
        }
        //扣费状态拿主任务的即可
        contentEntity.setFeePaidStatus(Optional.ofNullable(parentTask).map(TaskAiAbilityEntity::getFeePaidStatus).orElse(null));
        contentEntity.setFileExpiredStatus(Optional.ofNullable(parentTask).map(TaskAiAbilityEntity::getFileExpiredStatus).orElse(null));
        if (hasInProcessTask) {
            contentEntity.setTaskStatus(TaskStatusEnum.IN_PROCESS.getCode());
            if (hasSuccessTask) {
                contentEntity.setRespParam(JsonUtil.toJson(respParamVoList));
            }
        } else if (hasSuccessTask) {
            contentEntity.setTaskStatus(TaskStatusEnum.PROCESS_FINISH.getCode());
            contentEntity.setRespParam(JsonUtil.toJson(respParamVoList));
            contentEntity.setResultCode(ResultCodeEnum.SUCCESS.getResultCode());
            contentEntity.setResultMsg(ResultCodeEnum.SUCCESS.getResultMsg());
        } else {
            contentEntity.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
            contentEntity.setResultMsg(taskAiAbilityEntityList.stream()
                    .map(TaskAiAbilityEntity::getResultMsg).collect(Collectors.joining(JOIN_STR)));
            Optional<String> optional = taskAiAbilityEntityList.stream().findFirst().map(TaskAiAbilityEntity::getResultCode);
            contentEntity.setResultCode(optional.orElse(ResultCodeEnum.UNKNOWN_ERROR.getResultCode()));
        }
        // 构建对话内容数据 */
        List<AlgorithmChatContentEntity> contentEntityList = createContentData(entity.getUserId(), ListUtil.toList(contentEntity));
        if (CollUtil.isNotEmpty(contentEntityList)) {
            return contentEntityList.get(0);
        }
        return null;
    }

    /**
     * 构建对话内容数据
     *
     * @param chatContentEntityList 对话内容列表数据（查询数据库的数据）
     * @return java.util.List<com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity>
     * @Author: WeiJingKun
     */
    private List<AlgorithmChatContentEntity> createContentData(String userId, List<AlgorithmChatContentEntity> chatContentEntityList) {
        // 获取对话【评价】数据 */
        // 获取对话idList
        List<Long> dialogueIdList = chatContentEntityList.stream().map(AlgorithmChatContentEntity::getId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, ChatCommentGetResult> chatCommentGetResultMap = algorithmChatCommentRepository.queryMapByDialogueIdList(userId, dialogueIdList);

        // 构建对话内容列表数据 */
        chatContentEntityList.forEach(chatContentEntity -> {
            // set对话【评价】数据
            Long dialogueId = chatContentEntity.getId();
            ChatCommentGetResult chatCommentGetResult = chatCommentGetResultMap.get(dialogueId);
            if (chatCommentGetResult != null) {
                chatContentEntity.setLikeComment(chatCommentGetResult.getLikeComment());
                chatContentEntity.setDefaultComment(chatCommentGetResult.getDefaultComment());
                chatContentEntity.setCustomComment(chatCommentGetResult.getCustomComment());
            }
        });
        return chatContentEntityList;
    }

    @Override
    public void deleteBySessionIds(AlgorithmChatContentEntity entity) {
        String userId = entity.getUserId();
        List<Long> sessionIdList = entity.getSessionIdList();
        String businessType = entity.getBusinessType();

        long start = System.currentTimeMillis();
        this.lambdaUpdate().set(AlgorithmChatContentPO::getDelFlag, DelFlagEnum.YES.getCode())
                .set(AlgorithmChatContentPO::getUpdateTime, new Date()).eq(AlgorithmChatContentPO::getUserId, userId)
                .eq(CharSequenceUtil.isNotBlank(businessType), AlgorithmChatContentPO::getBusinessType, businessType)
                // 没有会话id，则删除该用户的所有对话记录
                .in(CollUtil.isNotEmpty(sessionIdList), AlgorithmChatContentPO::getSessionId, sessionIdList).update();
        log.info("【tidb】根据会话id删除对话内容，耗时：{}", System.currentTimeMillis() - start);
    }

    @Override
    public Boolean saveChatContent(AlgorithmChatContentEntity entity) {
        AlgorithmChatContentPO po = contentAssembler.toChatContent(entity);
        if (null == po.getChatStatus()) {
            // 默认对话中
            po.setChatStatus(ChatStatusEnum.CHAT_IN.getCode());
        }
        long start = System.currentTimeMillis();
        Boolean result = baseMapper.insert(po) > 0;
        log.info("【tidb】保存对话，耗时：{}", System.currentTimeMillis() - start);
        return result;
    }

    @MethodExecutionTimeLog("获取并更新对话数据-repository")
    @Override
    public AlgorithmChatContentEntity pollingUpdate(AlgorithmChatContentEntity entity) {
        // 参数初始化 */
        Long dialogueId = entity.getId();
        String userId = entity.getUserId();

        // 获取对话内容数据 */
        AlgorithmChatContentEntity info = queryAndCreateOne(entity);
        log.info("AlgorithmChatContentRepositoryImpl-pollingUpdate-getById，条件：{}，结果：{}", JSON.toJSON(entity),
                JSON.toJSON(info));
        if (null == info) {
            return null;
        }
        // 抽取多使用的变量
        Long taskId = info.getTaskId();
        Integer outAuditStatus = info.getOutAuditStatus();
        String outResourceId = info.getOutResourceId();
        // 任务相关
        String respParam = info.getRespParam();
        Integer taskStatus = info.getTaskStatus();

        /* 输出内容审批【成功】 */
        if (OutAuditStatusEnum.SUCCESS.getCode().equals(outAuditStatus)) {
            // 根据rowkey获取HBASE的输出内容
            long start = System.currentTimeMillis();
            List<AiTextResultPO> list = hbaseRepository.selectList(
                    centerTaskExternalService.createTaskRowkeyToList(userId, Collections.singletonList(String.valueOf(dialogueId))),
                    AiTextResultPO.class);
            log.info("【hbase】查询历史对话，耗时：{}", System.currentTimeMillis() - start);
            if (CollUtil.isNotEmpty(list)) {
                // 将结果set到输出内容
                info.setHbaseRespParameters(list.get(0).getRespParameters());
            }
        }
        if (null == taskId || null != info.getOutAuditTime()) {
            return info;
        }

        // 任务未完成，直接返回
        if (CharSequenceUtil.isBlank(respParam) || null == taskStatus || TaskStatusEnum.isProcessing(taskStatus)) {
            return info;
        }
        info.setTaskStatus(taskStatus);
        info.setOutAuditTime(new Date());
        // 输出内容审批状态处理
        if (!OutAuditStatusEnum.SUCCESS.getCode().equals(outAuditStatus)) {
            info.setOutAuditStatus(TaskStatusEnum.PROCESS_FINISH.getCode().equals(taskStatus) ? OutAuditStatusEnum.SUCCESS.getCode() : OutAuditStatusEnum.FAIL.getCode());
        }

        // 任务完成，输出资源处理
        taskFinishOutResourceHandle(respParam, taskStatus, userId, dialogueId, info, outResourceId);

        /* 输出数据更新，这里都通过get去获取最新属性值 */
        this.lambdaUpdate().set(AlgorithmChatContentPO::getUpdateTime, new Date())
                .set(AlgorithmChatContentPO::getOutAuditStatus, info.getOutAuditStatus())
                .set(AlgorithmChatContentPO::getOutAuditTime, info.getOutAuditTime())
                .set(AlgorithmChatContentPO::getOutResourceId, info.getOutResourceId())
                .eq(AlgorithmChatContentPO::getId, dialogueId).update();
        return info;
    }

    /**
     * 任务完成，输出资源处理
     *
     * @Author: WeiJingKun
     */
    private void taskFinishOutResourceHandle(String respParam, Integer taskStatus, String userId, Long dialogueId, AlgorithmChatContentEntity info, String outResourceId) {
        List<TaskRespParamVO> respParamVOList = JSON.parseArray(respParam, TaskRespParamVO.class);
        // 任务完成，输出资源处理
        if (TaskStatusEnum.PROCESS_FINISH.getCode().equals(taskStatus) && CollUtil.isNotEmpty(respParamVOList)) {
            TaskRespParamVO respParamVO = respParamVOList.get(0);
            if (null != respParamVO) {
                switch (OutResourceTypeEnum.getByCode(respParamVO.getOutResourceType())) {
                    case TEXT:
                        /* 【文本】输出资源处理 */
                        textOutResourceHandle(userId, dialogueId, info);
                        break;
                    case IMAGE:
                        /* 图片 */
                        if (CharSequenceUtil.isEmpty(outResourceId) && ImageTransmissionTypeEnum.YUN_DISK.getCode() == respParamVO.getImageTransmissionType()) {
                            info.setOutResourceId(respParamVO.getOutResourceId());
                        }
                        break;
                    default:
                        break;
                }
            }
        }
    }

    /**
     * 【文本】输出资源处理
     *
     * @Author: WeiJingKun
     */
    private void textOutResourceHandle(String userId, Long dialogueId, AlgorithmChatContentEntity info) {
        long start = System.currentTimeMillis();
        List<AiTextResultPO> list = hbaseRepository.selectList(
                centerTaskExternalService.createTaskRowkeyToList(userId, Collections.singletonList(String.valueOf(dialogueId))),
                AiTextResultPO.class);
        log.info("【hbase】查询历史对话，耗时：{}", System.currentTimeMillis() - start);
        if (CollUtil.isNotEmpty(list)) {
            info.setHbaseRespParameters(list.get(0).getRespParameters());
        }
    }

    @Override
    public boolean updateChatStopById(Long id) {
        // 不需要检查对话状态
        return updateChatStatus(false, null,
                AlgorithmChatContentEntity.builder().id(id).chatStatus(ChatStatusEnum.CHAT_STOP.getCode()).build());
    }

    @Override
    public AlgorithmChatContentEntity getById(Long id) {
        QueryWrapper<AlgorithmChatContentPO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AlgorithmChatContentPO::getId, id);
        AlgorithmChatContentPO po = baseMapper.selectOne(wrapper);
        return contentAssembler.toChatContentEntity(po);
    }

    @Override
    public AlgorithmChatContentEntity getByIdUserId(Long id, String userId) {
        QueryWrapper<AlgorithmChatContentPO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AlgorithmChatContentPO::getId, id);
        wrapper.lambda().eq(AlgorithmChatContentPO::getUserId, userId);
        AlgorithmChatContentPO po = baseMapper.selectOne(wrapper);
        return contentAssembler.toChatContentEntity(po);
    }

    @Override
    public void updateOutResult(Long id, Integer outAuditStatus, Integer chatStatus, String outContent, String recommendInfo) {
        DateTime date = DateUtil.date();
        UpdateWrapper<AlgorithmChatContentPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id);
        updateWrapper.eq("chat_status", ChatStatusEnum.CHAT_IN.getCode());
        updateWrapper.set("chat_status", chatStatus);
        updateWrapper.set("out_content", outContent);
        updateWrapper.set("out_audit_status", outAuditStatus);
        updateWrapper.set(CharSequenceUtil.isNotBlank(recommendInfo), "recommend_info", recommendInfo);
        updateWrapper.set("out_audit_time", date);
        updateWrapper.set("update_time", date);
        baseMapper.update(null, updateWrapper);
    }

    @Override
    public void updateOutResultStop(Long id) {
        DateTime date = DateUtil.date();
        UpdateWrapper<AlgorithmChatContentPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id);
        updateWrapper.eq("chat_status", ChatStatusEnum.CHAT_STOP.getCode());
        updateWrapper.set("out_audit_status", OutAuditStatusEnum.SUCCESS.getCode());
        updateWrapper.set("out_audit_time", date);
        updateWrapper.set("update_time", date);
        baseMapper.update(null, updateWrapper);
    }

    /**
     * 更新对话中的对话状态
     */
    @Override
    public boolean updateChatStatusForInStatus(AlgorithmChatContentEntity entity) {
        // 需要检查对话状态是CHAT_IN
        return updateChatStatus(true, ChatStatusEnum.CHAT_IN.getCode(), entity);
    }

    private boolean updateChatStatus(boolean isCheckChatStatus, Integer checkChatStatus,
                                     AlgorithmChatContentEntity entity) {
        LambdaUpdateChainWrapper<AlgorithmChatContentPO> lambdaUpdateChainWrapper = this.lambdaUpdate()
                .set(AlgorithmChatContentPO::getUpdateTime, new Date())
                .set(AlgorithmChatContentPO::getChatStatus, entity.getChatStatus())
                .eq(AlgorithmChatContentPO::getId, entity.getId());
        if (isCheckChatStatus) {
            lambdaUpdateChainWrapper.eq(AlgorithmChatContentPO::getChatStatus, checkChatStatus);
        }
        return lambdaUpdateChainWrapper.update();
    }

    @Override
    public boolean updateOutResourceId(AlgorithmChatContentEntity entity) {
        AlgorithmChatContentPO po = contentAssembler.toChatContent(entity);
        // 需要检查对话状态是CHAT_IN
        return baseMapper.updateById(po) > 0;
    }

    @Override
    public AlgorithmChatContentEntity getLastOne(Long sessionId, String userId) {
        if (sessionId == null) {
            return null;
        }

        // 查询最近一条记录
        long startTime = System.currentTimeMillis();
        List<AlgorithmChatContentPO> list = baseMapper.selectList(
                new QueryWrapper<AlgorithmChatContentPO>()
                        .lambda()
                        .eq(AlgorithmChatContentPO::getUserId, userId)
                        .eq(AlgorithmChatContentPO::getSessionId, sessionId)
                        .orderByDesc(AlgorithmChatContentPO::getCreateTime)
                        .last("limit 1")
        );
        log.info("【tidb】查询最新一条记录，耗时：{}", System.currentTimeMillis() - startTime);
        if (CollUtil.isNotEmpty(list)) {
            return contentAssembler.toChatContentEntity(list.get(0));
        }
        return null;
    }

    @Override
    public void updateModelCode(Long dialogueId, String modelCode) {
        if ((dialogueId == null) || CharSequenceUtil.isEmpty(modelCode)) {
            return;
        }
        long startTime = System.currentTimeMillis();
        LambdaUpdateChainWrapper<AlgorithmChatContentPO> lambdaUpdateChainWrapper = this.lambdaUpdate()
                .eq(AlgorithmChatContentPO::getId, dialogueId)
                .set(AlgorithmChatContentPO::getUpdateTime, new Date())
                .set(AlgorithmChatContentPO::getModelType, modelCode);
        lambdaUpdateChainWrapper.update();
        log.info("【tidb】更新对话模型，耗时：{}", System.currentTimeMillis() - startTime);
    }

    @Override
    public List<AlgorithmChatContentEntity> getContentListBySessionId(Long sessionId, Integer maxCount, String userId) {
        if (sessionId == null) {
            return null;
        }

        // 根据会话id查询对话列表并指定最大返回条数
        long startTime = System.currentTimeMillis();
        List<AlgorithmChatContentPO> list = baseMapper.selectList(
                new QueryWrapper<AlgorithmChatContentPO>()
                        .lambda()
                        .eq(AlgorithmChatContentPO::getUserId, userId)
                        .eq(AlgorithmChatContentPO::getSessionId, sessionId)
                        .orderByDesc(AlgorithmChatContentPO::getCreateTime)
                        .last("limit " + maxCount)
        );

        log.info("【tidb】根据会话id查询对话列表并指定最大返回条数，耗时：{}", System.currentTimeMillis() - startTime);
        if (CollUtil.isNotEmpty(list)) {
            return contentAssembler.toChatContentEntityList(list);
        }

        return null;
    }

    @Override
    public List<AlgorithmChatContentEntity> getShareContentList(Long sessionId, String applicationType, String userId, List<String> dialogueIdList) {
        QueryWrapper<AlgorithmChatContentPO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(AlgorithmChatContentPO::getUserId, userId)
                .eq(ObjectUtil.isNotEmpty(sessionId), AlgorithmChatContentPO::getSessionId, sessionId)
                .eq(ObjectUtil.isNotEmpty(applicationType), AlgorithmChatContentPO::getApplicationType, applicationType)
                .eq(AlgorithmChatContentPO::getDelFlag, DelFlagEnum.NO.getCode())
                .in(AlgorithmChatContentPO::getId, dialogueIdList)
                .orderByDesc(AlgorithmChatContentPO::getCreateTime);

        long startTime = System.currentTimeMillis();
        List<AlgorithmChatContentPO> list = baseMapper.selectList(wrapper);
        log.info("【tidb】获取分享内容列表，耗时：{}", System.currentTimeMillis() - startTime);

        return contentAssembler.toChatContentEntityList(list);
    }

    @MethodExecutionTimeLog("获取并更新对话数据V2-repository")
    @Override
    public AlgorithmChatContentEntity pollingUpdateV2(AlgorithmChatContentEntity entity) {
        // 参数初始化 */
        Long dialogueId = entity.getId();
        String userId = entity.getUserId();

        // 获取对话内容数据 */
        AlgorithmChatContentEntity info = queryAndCreateOneV2(entity);
        log.info("AlgorithmChatContentRepositoryImpl-pollingUpdateV2-getById，条件：{}，结果：{}", JSON.toJSON(entity),
                JSON.toJSON(info));
        if (null == info) {
            return null;
        }
        // 抽取多使用的变量
        Integer outAuditStatus = info.getOutAuditStatus();
        // 任务相关
        String respParam = info.getRespParam();
        Integer taskStatus = info.getTaskStatus();

        /* 输出内容审批【成功】 */
        if (OutAuditStatusEnum.SUCCESS.getCode().equals(outAuditStatus)) {
            // 根据rowkey获取HBASE的输出内容
            long start = System.currentTimeMillis();
            List<AiTextResultPO> list = hbaseRepository.selectList(
                    centerTaskExternalService.createTaskRowkeyToList(userId, Collections.singletonList(String.valueOf(dialogueId))),
                    AiTextResultPO.class);
            log.info("【hbase】查询历史对话，耗时：{}", System.currentTimeMillis() - start);
            if (CollUtil.isNotEmpty(list)) {
                // 将结果set到输出内容
                info.setHbaseRespParameters(list.get(0).getRespParameters());
                info.setHbaseReqParameters(list.get(0).getReqParameters());
            }
        }

        info.setTaskStatus(taskStatus);
        info.setOutAuditTime(new Date());
        // 输出内容审批状态处理
        if (!OutAuditStatusEnum.SUCCESS.getCode().equals(outAuditStatus)) {
            info.setOutAuditStatus(TaskStatusEnum.PROCESS_FINISH.getCode().equals(taskStatus) ? OutAuditStatusEnum.SUCCESS.getCode() : OutAuditStatusEnum.FAIL.getCode());
        }

        // 任务完成，输出资源处理
        taskFinishOutResourceHandleV2(respParam, userId, dialogueId, info);

        /* 输出数据更新，这里都通过get去获取最新属性值 */
        this.lambdaUpdate().set(AlgorithmChatContentPO::getUpdateTime, new Date())
                .set(AlgorithmChatContentPO::getOutAuditStatus, info.getOutAuditStatus())
                .set(AlgorithmChatContentPO::getOutAuditTime, info.getOutAuditTime())
                .set(AlgorithmChatContentPO::getOutResourceId, info.getOutResourceId())
                .eq(AlgorithmChatContentPO::getId, dialogueId).update();
        return info;
    }

    @Override
    public void updateChatStatusAndOutResource(AlgorithmChatContentEntity chatContentEntity) {
        this.lambdaUpdate().set(AlgorithmChatContentPO::getUpdateTime, new Date())
                .set(AlgorithmChatContentPO::getChatStatus, chatContentEntity.getChatStatus())
                .set(AlgorithmChatContentPO::getOutContent, chatContentEntity.getOutContent())
                .set(AlgorithmChatContentPO::getOutResourceId, chatContentEntity.getOutResourceId())
                .set(AlgorithmChatContentPO::getUpdateTime, chatContentEntity.getUpdateTime())
                .eq(AlgorithmChatContentPO::getId, chatContentEntity.getId()).update();
    }

    /**
     * 任务完成，输出资源处理
     *
     * @Author: WeiJingKun
     */
    private void taskFinishOutResourceHandleV2(String respParam, String userId, Long dialogueId,
                                               AlgorithmChatContentEntity info) {
        if (StringUtils.isEmpty(respParam)) {
            return;
        }
        List<TaskRespParamVO> respParamVOList = JSON.parseArray(respParam, TaskRespParamVO.class);
        // 任务完成，输出资源处理
        if (CollUtil.isNotEmpty(respParamVOList)) {
            TaskRespParamVO respParamVO = respParamVOList.get(0);
            if (null != respParamVO) {
                switch (OutResourceTypeEnum.getByCode(respParamVO.getOutResourceType())) {
                    case TEXT:
                        /* 【文本】输出资源处理 */
                        textOutResourceHandleV2(userId, dialogueId, info);
                        break;
                    case IMAGE:
                        /* 图片 */
                        if (ImageTransmissionTypeEnum.YUN_DISK.getCode() == respParamVO.getImageTransmissionType()) {
                            //新版本直接设置为输出内容
                            info.setOutResourceId(respParam);
                        }
                        break;
                    default:
                        break;
                }
            }
        }
    }

    /**
     * 【文本】输出资源处理
     *
     * @Author: WeiJingKun
     */
    private void textOutResourceHandleV2(String userId, Long dialogueId, AlgorithmChatContentEntity info) {
        long start = System.currentTimeMillis();
        List<AiTextResultPO> list = hbaseRepository.selectList(
                centerTaskExternalService.createTaskRowkeyToList(userId, Collections.singletonList(String.valueOf(dialogueId))),
                AiTextResultPO.class);
        log.info("【hbase】查询历史对话，耗时：{}", System.currentTimeMillis() - start);
        if (CollUtil.isNotEmpty(list)) {
            info.setHbaseRespParameters(list.get(0).getRespParameters());
        }
    }

	@Override
	public boolean updateChatFailById(Long id) {
        // 不需要检查对话状态
        return updateChatStatus(false, null,
                AlgorithmChatContentEntity.builder().id(id).chatStatus(ChatStatusEnum.CHAT_FAIL.getCode()).build());
    }

    @Override
    public AlgorithmChatContentEntity selectLastIntentionDialogue(AlgorithmChatContentEntity condition) {
    	Long thisId = condition.getId();
    	AlgorithmChatContentEntity bean = getById(thisId);
    	Date createTime = null;
		if (null == bean) {
			// 空对话，取当前时间
			createTime = new Date();
		} else {
			createTime = bean.getCreateTime();
		}
		log.info("selectLastIntentionDialogue bean exist:{}, thisId:{} createTime:{}", (null != bean), thisId,
				createTime);
		AlgorithmChatContentPO chatContent = baseMapper.selectOne(
                new QueryWrapper<AlgorithmChatContentPO>()
                        .lambda()
                        .eq(AlgorithmChatContentPO::getUserId, condition.getUserId())
                        .eq(AlgorithmChatContentPO::getSessionId, condition.getSessionId())
                        .eq(StringUtils.isNotBlank(condition.getToolsCommand()), AlgorithmChatContentPO::getToolsCommand, condition.getToolsCommand())
                        .eq(StringUtils.isNotBlank(condition.getSubToolsCommand()), AlgorithmChatContentPO::getSubToolsCommand, condition.getSubToolsCommand())
                        .lt(AlgorithmChatContentPO::getCreateTime, createTime)
                        .orderByDesc(AlgorithmChatContentPO::getCreateTime)
                        .last("limit " + 1)
                );
        return contentAssembler.toChatContentEntity(chatContent);
    }

	@Override
	public List<AlgorithmChatContentEntity> selectIntentionDialogueWithDesc(String userId, Long sessionId,
			String intentionCode, String subIntentionCode, int querySize) {
		log.info(
				"selectIntentionDialogueWithDesc userId:{}, sessionId:{}, intentionCode:{}, subIntentionCode:{}, querySize:{}",
				userId, sessionId, intentionCode, subIntentionCode, querySize);
		List<AlgorithmChatContentPO> list = baseMapper.selectList(new QueryWrapper<AlgorithmChatContentPO>().lambda()
				.eq(AlgorithmChatContentPO::getUserId, userId)
				.eq(AlgorithmChatContentPO::getSessionId, sessionId)
				.eq(StringUtils.isNotBlank(intentionCode), AlgorithmChatContentPO::getToolsCommand, intentionCode)
				.eq(StringUtils.isNotBlank(subIntentionCode), AlgorithmChatContentPO::getSubToolsCommand, subIntentionCode)
				.orderByDesc(AlgorithmChatContentPO::getCreateTime)
				.last("limit " + querySize));
		return contentAssembler.toChatContentEntityList(list);
	}
}
