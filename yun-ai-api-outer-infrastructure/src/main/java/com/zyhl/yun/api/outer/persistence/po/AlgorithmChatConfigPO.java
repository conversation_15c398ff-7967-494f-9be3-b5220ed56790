package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 对话配置表
 * <AUTHOR>
 */
@Data
@TableName("algorithm_chat_config")
public class AlgorithmChatConfigPO {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 模型类型
     * qwen：通义千问，xfyun：讯飞星火大模型, blian:百炼
     */
    @TableField("model_type")
    private String modelType;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 变更时间时间戳，默认值用current_timestamp
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 助手编码
     * @see com.zyhl.yun.api.outer.enums.AssistantEnum
     */
    @TableField("business_code")
    private String businessCode;
    
    /**
     * 大模型联网搜索状态
     * @see com.zyhl.yun.api.outer.enums.ChatNetworkSearchStatusEnum
     */
    @TableField("network_search_status")
    private Integer networkSearchStatus;

}
