package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiRegisterEntity;
import com.zyhl.yun.api.outer.enums.BusinessSourceEnum;
import com.zyhl.yun.api.outer.persistence.mapper.AlgorithmAiRegisterMapper;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmAiRegisterPO;
import com.zyhl.yun.api.outer.repository.AlgorithmAiRegisterRepository;
import com.zyhl.yun.api.outer.repository.assembler.EntityAssembler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/6 16:53
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlgorithmAiRegisterRepositoryImpl extends ServiceImpl<AlgorithmAiRegisterMapper, AlgorithmAiRegisterPO> implements AlgorithmAiRegisterRepository {

    private final EntityAssembler entityAssembler;

    private final UidGenerator uidGenerator;

    /**
     * 插入报名（授权记录）
     *
     * @param entity
     */
    @Override
    public void insert(AlgorithmAiRegisterEntity entity) {
        final AlgorithmAiRegisterPO po = entityAssembler.toAlgorithmAiRegisterPo(entity);
        po.setId(uidGenerator.getUID());
        po.setCreateTime(new Date());
        po.setUpdateTime(new Date());
        baseMapper.insert(po);

        // 返回id
        entity.setId(po.getId());
    }

    @Override
    public void updateById(AlgorithmAiRegisterEntity entity) {
        final AlgorithmAiRegisterPO po = entityAssembler.toAlgorithmAiRegisterPo(entity);
        po.setUpdateTime(new Date());
        baseMapper.updateById(po);
    }

    @Override
    public List<AlgorithmAiRegisterEntity> queryByUserId(String userId) {
        log.info("查询用户{}所有的报名", userId);
        final QueryWrapper<AlgorithmAiRegisterPO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AlgorithmAiRegisterPO::getUserId, userId);
        final List<AlgorithmAiRegisterPO> poList = baseMapper.selectList(wrapper);

        // 数据转换
        final List<AlgorithmAiRegisterEntity> resultList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(poList)) {
            poList.forEach(po -> resultList.add(entityAssembler.toAlgorithmAiRegisterEntity(po)));
        }
        return resultList;
    }

    @Override
    public AlgorithmAiRegisterEntity queryByUserId(String userId, Integer businessType) {
        log.info("查询用户{}业务类型为{}:{}的报名记录", userId, businessType, BusinessSourceEnum.getByCode(businessType).getName());
        final QueryWrapper<AlgorithmAiRegisterPO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AlgorithmAiRegisterPO::getUserId, userId)
                .eq(AlgorithmAiRegisterPO::getBusinessType, businessType);
        final List<AlgorithmAiRegisterPO> poList = baseMapper.selectList(wrapper);

        // 数据转换
        if (!CollUtil.isEmpty(poList)) {
            return entityAssembler.toAlgorithmAiRegisterEntity(poList.get(0));
        }

        return null;
    }
}
