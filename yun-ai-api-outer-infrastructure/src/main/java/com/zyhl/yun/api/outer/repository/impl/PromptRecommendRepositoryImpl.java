package com.zyhl.yun.api.outer.repository.impl;

import com.zyhl.yun.api.outer.domain.vo.PromptRecommendVO;
import com.zyhl.yun.api.outer.persistence.mapper.PromptRecommendMapper;
import com.zyhl.yun.api.outer.repository.PromptRecommendRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/17 11:21
 */
@Service
public class PromptRecommendRepositoryImpl implements PromptRecommendRepository {

    @Resource
    private PromptRecommendMapper promptRecommendMapper;

    @Override
    public List<PromptRecommendVO> listPromptRecommendList(String businessType) {
        return promptRecommendMapper.listPromptRecommendList(businessType);
    }
}
