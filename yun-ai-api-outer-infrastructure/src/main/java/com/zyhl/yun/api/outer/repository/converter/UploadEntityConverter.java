package com.zyhl.yun.api.outer.repository.converter;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.api.outer.domain.entity.PersonalKnowledgeImportTaskEntity;
import com.zyhl.yun.api.outer.domain.valueobject.File;
import com.zyhl.yun.api.outer.domain.vo.ImportHtmlInfoVO;
import com.zyhl.yun.api.outer.domain.vo.ImportMailInfoVO;
import com.zyhl.yun.api.outer.domain.vo.ImportNoteInfoVO;
import com.zyhl.yun.api.outer.enums.knowledge.FileCategoryEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeUploadStatusEnum;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmUserKnowledgeUploadPO;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 算法用户知识上传PO到实体的转换器
 *
 * <AUTHOR>
 * @date 2025-04-16 16:13:18
 */
@Slf4j
@Component
@Mapper(componentModel = "spring")
public class UploadEntityConverter {

    /**
     * 将单个PO转换为实体
     *
     * @param po PO对象
     * @return 实体对象
     */
    public PersonalKnowledgeImportTaskEntity toEntity(AlgorithmUserKnowledgeUploadPO po) {
        if (po == null) {
            return null;
        }
        try {
            // 构建基本属性
            PersonalKnowledgeImportTaskEntity.PersonalKnowledgeImportTaskEntityBuilder builder = PersonalKnowledgeImportTaskEntity.builder()
                    .taskId(String.valueOf(po.getId()))
                    .baseId(String.valueOf(po.getBaseId()))
                    .resourceType(po.getResourceType())
                    .status(KnowledgeUploadStatusEnum.convertDbStatusToRequestStatus(po.getUploadStatus()))
                    .createdAt(formatDate(po.getCreateTime()))
                    .updatedAt(formatDate(po.getUpdateTime()))
                    .errorMessage(po.getResultMsg())
                    .errorCode(po.getResultCode())
                    .fileId(po.getFileId())
                    .resource(po.getResource())
                    .ownerType(po.getOwnerType())
                    .uploadStatus(po.getUploadStatus())
                    .ownerId(po.getOwnerId())
                    .userId(po.getUserId())
                    .delFlag(po.getDelFlag())
                    .targetParentFileId(po.getTargetParentFileId())
                    .targetParentFilePath(po.getTargetParentFilePath());

            // 根据资源类型填充对应属性
            if (po.getResourceType() != null) {
                KnowledgeResourceTypeEnum resourceType = KnowledgeResourceTypeEnum.getByCode(po.getResourceType());
                if (resourceType != null) {
                    switch (resourceType) {
                        case FILE_LOCAL:
                        case PERSONAL_FILE:
                            builder.file(mapToFile(po));
                            break;
                        case MAIL:
                            builder.mail(mapToMailInfo(po));
                            break;
                        case NOTE:
                            builder.note(mapToNoteInfo(po));
                            break;
                        case HTML:
                            builder.htmlInfo(mapToHtmlInfo(po));
                            break;
                        default:
                            log.warn("Unknown resource type: {}", po.getResourceType());
                    }
                } else {
                    log.warn("Unknown resource type: {}", po.getResourceType());
                }
            }

            return builder.build();
        } catch (Exception e) {
            log.error("Failed to convert PO to entity: {}", po.getId(), e);
            return null;
        }
    }

    /**
     * 格式化日期为ISO 8601标准格式：yyyy-MM-dd'T'HH:mm:ss.SSSXXX
     *
     * @param date 待格式化的日期
     * @return 格式化后的日期字符串
     */
    private String formatDate(Date date) {
        if (ObjectUtil.isEmpty(date)) {
            return null;
        }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        return formatter.format(date);
    }


    /**
     * 将PO对象映射到File对象
     */
    public File mapToFile(AlgorithmUserKnowledgeUploadPO po) {
        if (po == null || (!po.getResourceType().equals(KnowledgeResourceTypeEnum.FILE_LOCAL.getCode())
                && !po.getResourceType().equals(KnowledgeResourceTypeEnum.PERSONAL_FILE.getCode()))) {
            return null;
        }
        try {
            return File.builder()
                    .fileId(po.getFileId())
                    .parentFileId(po.getParentFileId())
                    .name(po.getFileName())
                    .type(po.getFileType() == 1 ? "file" : "folder")
                    .fileExtension(po.getExtension())
                    .category(mapCategory(po.getCategory()))
                    .createdAt(formatDate(po.getCreateTime()))
                    .updatedAt(formatDate(po.getUpdateTime()))
                    .size(po.getFileSize())
                    .targetParentFileId(po.getTargetParentFileId())
                    .targetParentFilePath(po.getTargetParentFilePath())
                    .contentHash(po.getHashValue())
                    .build();
        } catch (Exception e) {
            log.error("Failed to map file: {}", po.getFileId(), e);
            return null;
        }
    }

    /**
     * 映射文件分类
     */
    public String mapCategory(Integer category) {
        if (category == null) {
            return null;
        }
        FileCategoryEnum byKnowledgeCategory = FileCategoryEnum.getByKnowledgeCategory(category);
        return byKnowledgeCategory != null ? byKnowledgeCategory.getYundiskCategory() : FileCategoryEnum.OTHERS.getYundiskCategory();
    }

    /**
     * 映射到邮件信息，从resource字段解析JSON
     */
    public ImportMailInfoVO mapToMailInfo(AlgorithmUserKnowledgeUploadPO po) {
        if (po == null || !StringUtils.hasText(po.getResource())) {
            return null;
        }
        try {
            return JSONUtil.toBean(po.getResource(), ImportMailInfoVO.class);
        } catch (Exception e) {
            log.error("Failed to parse mail info from resource: {}", po.getResource(), e);
            return null;
        }
    }

    /**
     * 映射到笔记信息，从resource字段解析JSON
     */
    public ImportNoteInfoVO mapToNoteInfo(AlgorithmUserKnowledgeUploadPO po) {
        if (po == null || !StringUtils.hasText(po.getResource())) {
            return null;
        }
        try {
            return JSONUtil.toBean(po.getResource(), ImportNoteInfoVO.class);
        } catch (Exception e) {
            log.error("Failed to parse note info from resource: {}", po.getResource(), e);
            return null;
        }
    }

    /**
     * 映射到HTML信息，从resource字段解析JSON
     */
    public ImportHtmlInfoVO mapToHtmlInfo(AlgorithmUserKnowledgeUploadPO po) {
        if (po == null || !StringUtils.hasText(po.getResource())) {
            return null;
        }
        try {
            return JSONUtil.toBean(po.getResource(), ImportHtmlInfoVO.class);
        } catch (Exception e) {
            log.error("Failed to parse html info from resource: {}", po.getResource(), e);
            return null;
        }
    }
} 