package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.knowledge.KnowledgeBusinessEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.KnowledgeDialogueConfigEntity;
import com.zyhl.yun.api.outer.persistence.po.KnowledgeBusinessPO;
import com.zyhl.yun.api.outer.persistence.po.KnowledgeDialogueConfigPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 类转换 KnowledgeBusinessEntity <--> KnowledgeBusinessPO
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface KnowledgeDialogueConfigAssembler {

    KnowledgeDialogueConfigAssembler INSTANCE = Mappers.getMapper(KnowledgeDialogueConfigAssembler.class);

    KnowledgeDialogueConfigEntity toKnowledgeDialogueConfigEntity(KnowledgeDialogueConfigPO po);

    List<KnowledgeDialogueConfigEntity> toKnowledgeDialogueConfigEntityList(List<KnowledgeDialogueConfigPO> poList);

}
