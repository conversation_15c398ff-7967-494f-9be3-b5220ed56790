package com.zyhl.yun.api.outer.repository.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileTaskEntity;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeTaskTypeEnum;
import com.zyhl.yun.api.outer.persistence.mapper.UserKnowledgeFileTaskMapper;
import com.zyhl.yun.api.outer.persistence.po.UserKnowledgeFileTaskPO;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileTaskRepository;
import com.zyhl.yun.api.outer.repository.assembler.UserKnowledgeFileTaskAssembler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 个人知识库文件转存任务
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class UserKnowledgeFileTaskRepositoryImpl extends ServiceImpl<UserKnowledgeFileTaskMapper, UserKnowledgeFileTaskPO> implements UserKnowledgeFileTaskRepository {

    private final UserKnowledgeFileTaskAssembler assembler;

    private final UidGenerator uidGenerator;


    @Override
    public void add(UserKnowledgeFileTaskEntity entity) {
        entity.setId(uidGenerator.getUID());
        if (entity.getOwnerId() == null) {
            entity.setOwnerId(entity.getUserId());
        }
        if (entity.getOwnerType() == null) {
            entity.setOwnerType(OwnerTypeEnum.PERSONAL.getOwnerValue());
        }

        entity.setExecuteCount(0);
        if (entity.getSuccessNum() == null) {
            entity.setSuccessNum(0);
        }
        if (entity.getFailNum() == null) {
            entity.setFailNum(0);
        }
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());

        baseMapper.insert(assembler.toUserKnowledgeFileTaskPo(entity));
    }

    @Override
    public void update(UserKnowledgeFileTaskEntity entity) {
        entity.setUpdateTime(new Date());

        baseMapper.updateById(assembler.toUserKnowledgeFileTaskPo(entity));
    }

    @Override
    public UserKnowledgeFileTaskEntity getById(Long id) {
        UserKnowledgeFileTaskPO po = this.lambdaQuery().eq(UserKnowledgeFileTaskPO::getId, id).one();
        return assembler.toUserKnowledgeFileTaskEntityEntity(po);
    }

    @Override
    public void deleteById(Long id) {
        baseMapper.deleteById(id);
    }

    @Override
    public List<UserKnowledgeFileTaskEntity> getByStatus(String userId, List<Integer> statusList, KnowledgeTaskTypeEnum type) {
        // 查询条件
        LambdaQueryChainWrapper<UserKnowledgeFileTaskPO> wrapper = this.lambdaQuery()
                .eq(UserKnowledgeFileTaskPO::getUserId, userId)
                .eq(type != null, UserKnowledgeFileTaskPO::getTaskType, type.getCode())
                .in(UserKnowledgeFileTaskPO::getTaskStatus, statusList);

        // 查询数据
        return assembler.toUserKnowledgeFileTaskEntityList(wrapper.list());
    }
}
