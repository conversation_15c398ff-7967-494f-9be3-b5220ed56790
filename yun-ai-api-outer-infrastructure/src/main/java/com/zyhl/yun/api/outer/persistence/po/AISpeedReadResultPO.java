package com.zyhl.yun.api.outer.persistence.po;

import com.zyhl.hcy.yun.ai.common.base.hbase.infrastructure.persistence.aspect.HbaseColumn;
import com.zyhl.hcy.yun.ai.common.base.hbase.infrastructure.persistence.aspect.HbaseTable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@HbaseTable(tableName = "ai_speedread_result")
public class AISpeedReadResultPO {
  @HbaseColumn(family = "info")
  private String rowKey;

  /**
   * 用户ID
   */
  @HbaseColumn(family = "info")
  private String userId;

  /**
   * 任务id
   */
  @HbaseColumn(family = "info")
  private String taskId;

  /**
   * 全文
   */
  @HbaseColumn(family = "details")
  private String fullText;

  /**
   * 总结
   */
  @HbaseColumn(family = "details")
  private String summary;

  /**
   * 脑图
   */
  @HbaseColumn(family = "details")
  private String mindMap;

  /**
   * 大纲
   */
  @HbaseColumn(family = "details")
  private String outline;

  /**
   * 翻译
   */
  @HbaseColumn(family = "details")
  private String translate;

}
