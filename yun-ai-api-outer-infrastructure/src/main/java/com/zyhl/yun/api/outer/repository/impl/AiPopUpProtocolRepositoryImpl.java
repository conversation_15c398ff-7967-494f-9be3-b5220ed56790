package com.zyhl.yun.api.outer.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.yun.api.outer.domain.entity.AiPopUpProtocolEntity;
import com.zyhl.yun.api.outer.persistence.mapper.AiPopUpProtocolMapper;
import com.zyhl.yun.api.outer.persistence.po.AiPopUpProtocolPo;
import com.zyhl.yun.api.outer.repository.IAiPopUpProtocolRepository;
import com.zyhl.yun.api.outer.repository.assembler.EntityAssembler;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AiPopUpProtocolRepositoryImpl extends ServiceImpl<AiPopUpProtocolMapper, AiPopUpProtocolPo> implements IAiPopUpProtocolRepository {

    @Resource
    private EntityAssembler entityAssembler;

    @Override
    public AiPopUpProtocolEntity query(AiPopUpProtocolEntity entity) {
        AiPopUpProtocolPo po = getOne(new QueryWrapper<AiPopUpProtocolPo>().lambda()
                .eq(AiPopUpProtocolPo::getChannel, entity.getChannel())
                .eq(AiPopUpProtocolPo::getType, entity.getType())
        );
        // AI弹窗协议配置-PO -> AI弹窗协议配置-Entity
        return entityAssembler.toAiPopUpProtocolEntity(po);
    }

}
