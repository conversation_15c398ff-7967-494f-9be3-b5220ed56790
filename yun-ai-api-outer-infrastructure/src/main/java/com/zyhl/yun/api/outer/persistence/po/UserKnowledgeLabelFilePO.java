package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 个人知识库标签与文件关系表
 * <AUTHOR>
 */
@Data
@TableName("algorithm_user_knowledge_label_file")
@EqualsAndHashCode(callSuper = false)
public class UserKnowledgeLabelFilePO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 业务类型：
     * -1 - 未知类型
     * 1-personal 个人云
     * 2-group 圈子
     * 3-shareGroup 共享群
     * 4-family 家庭云
     * 5-activity 活动空间 照片直播
     * 6-note 笔记
     * 7-cardpackage 卡包
     * 8-system 系统空间
     * 9-partner 合作空间
     * 10 -mount 挂载盘
     *
     * @see com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum
     */
    @TableField("owner_type")
    private Integer ownerType;

    /**
     * 标签id
     */
    @TableField("label_id")
    private Long labelId;

    /**
     * 独立空间的文件id
     */
    @TableField("file_id")
    private String fileId;

    /**
     * 个人云的文件id
     */
    @TableField("old_file_id")
    private String oldFileId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


}
