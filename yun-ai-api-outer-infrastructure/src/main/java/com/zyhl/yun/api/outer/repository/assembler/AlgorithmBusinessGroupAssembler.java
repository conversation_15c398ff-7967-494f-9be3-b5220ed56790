package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.AlgorithmBusinessGroupEntity;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmBusinessGroupPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 算法业务组表-Assembler
 * @Author: WeiJingKun
 */
@Mapper(componentModel = "spring")
public interface AlgorithmBusinessGroupAssembler {

    AlgorithmBusinessGroupAssembler INSTANCE = Mappers.getMapper(AlgorithmBusinessGroupAssembler.class);

    /**
     * po转entity
     * @param po po
     * @return entity
     */
    AlgorithmBusinessGroupEntity toEntity(AlgorithmBusinessGroupPO po);

}
