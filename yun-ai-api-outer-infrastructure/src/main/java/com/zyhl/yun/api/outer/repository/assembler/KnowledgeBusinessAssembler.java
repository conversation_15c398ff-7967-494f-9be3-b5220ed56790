package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.knowledge.KnowledgeBusinessEntity;
import com.zyhl.yun.api.outer.persistence.po.KnowledgeBusinessPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 类转换 KnowledgeBusinessEntity <--> KnowledgeBusinessPO
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface KnowledgeBusinessAssembler {

    KnowledgeBusinessAssembler INSTANCE = Mappers.getMapper(KnowledgeBusinessAssembler.class);

    /**
     * po转entity
     * @param po po
     * @return entity
     */
    KnowledgeBusinessEntity toKnowledgeBusinessEntity(KnowledgeBusinessPO po);


}
