package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.hcy.yun.ai.common.base.annotation.MethodExecutionTimeLog;
import com.zyhl.yun.api.outer.domain.entity.ApplicationTypeListEntity;
import com.zyhl.yun.api.outer.persistence.mapper.AlgorithmChatApplicationAgentMapper;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmChatApplicationAgentPO;
import com.zyhl.yun.api.outer.repository.IAlgorithmChatApplicationAgentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2024年03月05日 16:14
 */
@Service
@RequiredArgsConstructor
public class AlgorithmAlgorithmChatApplicationAgentServiceImpl extends ServiceImpl<AlgorithmChatApplicationAgentMapper, AlgorithmChatApplicationAgentPO> implements IAlgorithmChatApplicationAgentService {


    private final AlgorithmChatApplicationAgentMapper algorithmChatApplicationAgentMapper;

    @Override
    @MethodExecutionTimeLog("对话应用类型信息列表查询-serviceImpl")
    public List<AlgorithmChatApplicationAgentPO> listOrderedByAsc(ApplicationTypeListEntity entity) {
        // 创建LambdaQueryWrapper对象
        LambdaQueryWrapper<AlgorithmChatApplicationAgentPO> lambdaQuery = new LambdaQueryWrapper<>();
        lambdaQuery.eq(StringUtils.hasText(entity.getApplicationId()), AlgorithmChatApplicationAgentPO::getId, entity.getApplicationId());
        // 添加排序条件，按order字段升序排列
        lambdaQuery.orderByAsc(AlgorithmChatApplicationAgentPO::getSort);
        // 执行查询
        return algorithmChatApplicationAgentMapper.selectList(lambdaQuery);
    }

    @Override
    public AlgorithmChatApplicationAgentPO getById(String applicationId) {
        return algorithmChatApplicationAgentMapper.selectById(applicationId);
    }

    @Override
    public List<AlgorithmChatApplicationAgentPO> getList(List<String> idList) {
        return this.lambdaQuery()
                .in(ObjectUtil.isNotEmpty(idList), AlgorithmChatApplicationAgentPO::getId, idList)
                .list();
    }


}
