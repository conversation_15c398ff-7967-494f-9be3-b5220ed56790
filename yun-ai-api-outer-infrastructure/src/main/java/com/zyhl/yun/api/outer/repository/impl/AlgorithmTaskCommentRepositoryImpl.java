package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.yun.api.outer.domain.entity.TaskCommentEntity;
import com.zyhl.yun.api.outer.persistence.mapper.AlgorithmTaskCommentMapper;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmTaskCommentPO;
import com.zyhl.yun.api.outer.repository.AlgorithmTaskCommentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 【算法任务评论】Repository 实现类
 * @date 2025/4/21 17:18
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AlgorithmTaskCommentRepositoryImpl extends ServiceImpl<AlgorithmTaskCommentMapper, AlgorithmTaskCommentPO> implements AlgorithmTaskCommentRepository {

    private final UidGenerator uidGenerator;

    @Override
    public boolean bulkSave(List<TaskCommentEntity> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return false;
        }

        TaskCommentEntity taskComment = entityList.get(0);
        final QueryWrapper<AlgorithmTaskCommentPO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AlgorithmTaskCommentPO::getUserId, taskComment.getUserId());
        wrapper.lambda().eq(AlgorithmTaskCommentPO::getTaskId, taskComment.getTaskId());
        List<AlgorithmTaskCommentPO> retList = baseMapper.selectList(wrapper);

        List<AlgorithmTaskCommentPO> poList = new ArrayList<>();
        for (TaskCommentEntity entity : entityList) {
            AlgorithmTaskCommentPO po = new AlgorithmTaskCommentPO();
            // 拷贝属性
            BeanUtil.copyProperties(entity, po);

            if (CollectionUtils.isEmpty(retList)) {
                // 生成唯一ID
                po.setId(uidGenerator.getUID());
                poList.add(po);
                continue;
            }

            // 判断是否已存在评论。存在，则更新。不存在，则新增
            Optional<AlgorithmTaskCommentPO> existing = retList.stream()
                    .filter(Objects::nonNull)
                    .filter(item -> item.getUserId().equals(entity.getUserId())
                            && item.getTaskId().equals(entity.getTaskId())
                            && item.getModule().equals(entity.getModule()))
                    .findFirst();
            po.setId(existing.map(AlgorithmTaskCommentPO::getId)
            		// 生成唯一ID，如果已存在则不生成
                    .orElseGet(uidGenerator::getUID)); 
            poList.add(po);
        }
        return saveOrUpdateBatch(poList);
    }

    @Override
    public List<TaskCommentEntity> getTaskComment(TaskCommentEntity entity) {
        final QueryWrapper<AlgorithmTaskCommentPO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AlgorithmTaskCommentPO::getUserId, entity.getUserId());
        wrapper.lambda().eq(AlgorithmTaskCommentPO::getTaskId, entity.getTaskId());
        List<AlgorithmTaskCommentPO> poList = baseMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(poList)) {
            return new ArrayList<>();
        }

        List<TaskCommentEntity> taskCommentList = new ArrayList<>();
        for (AlgorithmTaskCommentPO po : poList) {
            TaskCommentEntity taskCommentEntity = new TaskCommentEntity();
            BeanUtil.copyProperties(po, taskCommentEntity);
            taskCommentList.add(taskCommentEntity);
        }
        return taskCommentList;
    }

}