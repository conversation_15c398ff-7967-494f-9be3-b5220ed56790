package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * className:AlgorithmUserKnowledgeFileResPO
 * description: 个人知识库文件内的附件资源表PO
 *
 * <AUTHOR>
 * @date 2025/02/12
 */
@Data
@TableName("algorithm_user_knowledge_file_res")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AlgorithmUserKnowledgeFileResPO implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 知识库id
     */
    @TableField(value = "base_id")
    private Long baseId;

    /**
     * 来源文件id
     */
    @TableField(value = "from_file_id")
    private String fromFileId;

    /**
     * 文件id
     */
    @TableField(value = "file_id")
    private String fileId;

    /**
     * 来源资源信息
     * from_resource_type=1 则存储的是邮箱附件信息，json格式
     */
    @TableField(value = "from_resource")
    private String fromResource;

    /**
     * 来源文件资源类型
     * 0 云盘个人云（默认）
     * 1 邮件
     * 2 笔记
     */
    @TableField(value = "from_resource_type")
    private Integer fromResourceType;

    /**
     * 资源文件md5
     */
    @TableField(value = "res_md5")
    private String resMd5;

    /**
     * 资源类型
     * 1 图片
     * 2 音频
     * 3 视频
     * 4 文档
     * 5 应用
     * 6 压缩文件
     * 0 其他
     * 100 普通目录
     */
    @TableField(value = "res_type")
    private Integer resType;

    /**
     * 文件归属
     */
    @TableField(value = "owner_id")
    private String ownerId;

    /**
     * 业务类型
     * -1 - 未知类型
     * 12 - 邮箱
     * 11-ai 知识库
     * 1-personal 个人云
     * 2-group 圈子
     * 3-shareGroup 共享群
     * 4-family 家庭云
     * 10 -mount 挂载盘
     * 6-note 笔记
     * 7-cardpackage 卡包
     * 8-system 系统空间
     * 9-partner 合作空间
     * 5-activity 活动空间 照片直播
     */
    @TableField(value = "owner_type")
    private Integer ownerType;

    /**
     * paas平台编码
     */
    @TableField(value = "paas_code")
    private String paasCode;

    /**
     * 文件大小
     */
    @TableField(value = "file_size")
    private Long fileSize;

    /**
     * 文件后缀
     */
    @TableField(value = "extension")
    private String extension;

    /**
     * 文件修改时间
     */
    @TableField(value = "file_updated_at")
    private Date fileUpdatedAt;

    /**
     * 文件创建时间
     */
    @TableField(value = "file_created_at")
    private Date fileCreatedAt;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 删除标识
     */
    @TableField(value = "del_flag")
    private Integer delFlag;

    /**
     * 审核状态
     */
    @TableField(value = "audit_status")
    private Integer auditStatus;

    /**
     * 审核时间
     */
    @TableField(value = "audit_time")
    private Date auditTime;

    /**
     *
     * 审核结果
     */
    @TableField(value = "audit_result")
    private String auditResult;

}
