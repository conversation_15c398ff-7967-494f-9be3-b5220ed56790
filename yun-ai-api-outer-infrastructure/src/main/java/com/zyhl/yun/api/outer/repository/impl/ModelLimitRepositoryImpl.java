package com.zyhl.yun.api.outer.repository.impl;

import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.config.ModelProperties;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.repository.ModelLimitRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import static com.zyhl.yun.api.outer.constants.Const.NUM_16;

/**
 * <AUTHOR>
 * @version 2024年03月22日 14:09
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class ModelLimitRepositoryImpl implements ModelLimitRepository {

    private final ModelProperties modelProperties;

    @Override
    public Map<String, Object> queryModelLimit() {
        Map<String, Object> limit = modelProperties.limitMap(null);
        Map<String, Object> map = new HashMap<>(NUM_16);
        if (limit != null) {
            map.putAll(limit);
        } else {
            log.warn("ModelLimitServiceImpl:大模型限制配置nacos配置为空");
        }
        return map;
    }

    /**
     * 从Object中获取String类型的值
     *
     * @param valueObject Object
     * @return String
     */
    private String getStringValueFromObject(Object valueObject) {
        if (valueObject instanceof Integer) {
            return String.valueOf(valueObject);
        } else if (valueObject instanceof String) {
            try {
                return (String) valueObject;
            } catch (NumberFormatException e) {
                log.error("ModelLimitServiceImpl: Nacos配置无效", e);
                throw new YunAiBusinessException(ResultCodeEnum.ERROR_NACOS_CONFIG);
            }
        }
        return null;
    }
}
