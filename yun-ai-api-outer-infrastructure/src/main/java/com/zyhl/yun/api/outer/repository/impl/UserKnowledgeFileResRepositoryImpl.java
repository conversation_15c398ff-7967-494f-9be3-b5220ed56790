package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileResEntity;
import com.zyhl.yun.api.outer.enums.knowledge.UserKnowledgeFileResDelFlagEnum;
import com.zyhl.yun.api.outer.persistence.mapper.AlgorithmUserKnowledgeFileResMapper;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmUserKnowledgeFileResPO;
import com.zyhl.yun.api.outer.repository.UserKnowledgeFileResRepository;
import com.zyhl.yun.api.outer.repository.assembler.UserKnowledgeFileResAssembler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * className:UserKnowledgeFileResRepositoryImpl
 *
 * <AUTHOR>
 * @date 2025/02/13
 */
@Slf4j
@Repository
public class UserKnowledgeFileResRepositoryImpl extends ServiceImpl<AlgorithmUserKnowledgeFileResMapper, AlgorithmUserKnowledgeFileResPO> implements UserKnowledgeFileResRepository {

    @Resource
    private final UserKnowledgeFileResAssembler userKnowledgeFileResAssembler;

    @Resource
    private AlgorithmUserKnowledgeFileResMapper algorithmUserKnowledgeFileResMapper;

    public UserKnowledgeFileResRepositoryImpl(UserKnowledgeFileResAssembler userKnowledgeFileResAssembler) {
        this.userKnowledgeFileResAssembler = userKnowledgeFileResAssembler;
    }

    @Override
    public List<UserKnowledgeFileResEntity> selectByFileIds(String userId, List<String> fromFileIds) {
        // 查询条件
        LambdaQueryChainWrapper<AlgorithmUserKnowledgeFileResPO> wrapper = this.lambdaQuery()
                .eq(AlgorithmUserKnowledgeFileResPO::getUserId, userId)
                .in(AlgorithmUserKnowledgeFileResPO::getFromFileId, fromFileIds)
                .eq(AlgorithmUserKnowledgeFileResPO::getDelFlag, UserKnowledgeFileResDelFlagEnum.NORMAL.getDelFlag());

        // 查询数据
        return userKnowledgeFileResAssembler.toUserKnowledgeFileResEntityList(wrapper.list());
    }

    @Override
    public List<UserKnowledgeFileResEntity> getResourceByFileIds(String userId, List<String> fileIds) {
        // 查询条件
        LambdaQueryChainWrapper<AlgorithmUserKnowledgeFileResPO> wrapper = this.lambdaQuery()
                .eq(AlgorithmUserKnowledgeFileResPO::getUserId, userId)
                .in(AlgorithmUserKnowledgeFileResPO::getFileId, fileIds)
                .eq(AlgorithmUserKnowledgeFileResPO::getDelFlag, UserKnowledgeFileResDelFlagEnum.NORMAL.getDelFlag());

        // 查询数据
        return userKnowledgeFileResAssembler.toUserKnowledgeFileResEntityList(wrapper.list());
    }

    /**
     * 查询文件列表
     *
     * @param userId           用户id
     * @param baseId           知识库id
     * @param fromFileIds      文件id集合
     * @param fromResourceType 来源文件资源类型；0 云盘个人云（默认） 1 邮件  2 笔记
     * @return 实体对象集合
     */
    public List<UserKnowledgeFileResEntity> selectByFromFileIds(@NotNull String userId, @NotNull Long baseId, @NotNull List<String> fromFileIds, @NotNull Integer fromResourceType){
        // 查询条件
        LambdaQueryChainWrapper<AlgorithmUserKnowledgeFileResPO> wrapper = this.lambdaQuery()
                .eq(AlgorithmUserKnowledgeFileResPO::getUserId, userId)
                .eq(AlgorithmUserKnowledgeFileResPO::getBaseId, baseId)
                .eq(AlgorithmUserKnowledgeFileResPO::getFromResourceType, fromResourceType)
                .in(AlgorithmUserKnowledgeFileResPO::getFromFileId, fromFileIds)
                .eq(AlgorithmUserKnowledgeFileResPO::getDelFlag, UserKnowledgeFileResDelFlagEnum.NORMAL.getDelFlag());

        // 查询数据
        return userKnowledgeFileResAssembler.toUserKnowledgeFileResEntityList(wrapper.list());
    }

    /**
     * 查询文件列表
     *
     * @param userId           用户id
     * @param baseId           知识库id
     * @param fromFileId      文件id
     * @param fromResourceType 来源文件资源类型；0 云盘个人云（默认） 1 邮件  2 笔记
     * @return 实体对象集合
     */
    public List<UserKnowledgeFileResEntity> selectByFromFileId(@NotNull String userId, @NotNull Long baseId, @NotNull String fromFileId, @NotNull Integer fromResourceType){
        // 查询条件
        LambdaQueryChainWrapper<AlgorithmUserKnowledgeFileResPO> wrapper = this.lambdaQuery()
                .eq(AlgorithmUserKnowledgeFileResPO::getUserId, userId)
                .eq(AlgorithmUserKnowledgeFileResPO::getBaseId, baseId)
                .eq(AlgorithmUserKnowledgeFileResPO::getFromResourceType, fromResourceType)
                .eq(AlgorithmUserKnowledgeFileResPO::getFromFileId, fromFileId)
                .eq(AlgorithmUserKnowledgeFileResPO::getDelFlag, UserKnowledgeFileResDelFlagEnum.NORMAL.getDelFlag());

        // 查询数据
        return userKnowledgeFileResAssembler.toUserKnowledgeFileResEntityList(wrapper.list());
    }


    /**
     * 查询文件列表
     *
     * @param userId           用户id
     * @param baseId           知识库id
     * @param fileId      文件id
     * @param fromResourceType 来源文件资源类型；0 云盘个人云（默认） 1 邮件  2 笔记
     * @return 实体对象集合
     */
    public List<UserKnowledgeFileResEntity> getResourceByFileId(@NotNull String userId, @NotNull Long baseId, @NotNull String fileId, @NotNull Integer fromResourceType) {
        // 查询条件
        LambdaQueryChainWrapper<AlgorithmUserKnowledgeFileResPO> wrapper = this.lambdaQuery()
                .eq(AlgorithmUserKnowledgeFileResPO::getUserId, userId)
                .eq(AlgorithmUserKnowledgeFileResPO::getBaseId, baseId)
                .eq(AlgorithmUserKnowledgeFileResPO::getFromResourceType, fromResourceType)
                .eq(AlgorithmUserKnowledgeFileResPO::getFromFileId, fileId)
                .eq(AlgorithmUserKnowledgeFileResPO::getDelFlag, UserKnowledgeFileResDelFlagEnum.NORMAL.getDelFlag())
                .isNotNull(AlgorithmUserKnowledgeFileResPO::getFromResource);

        // 查询数据
        return userKnowledgeFileResAssembler.toUserKnowledgeFileResEntityList(wrapper.list());
    }

    @Override
    public List<UserKnowledgeFileResEntity> batchSelectByFileIds(String userId, List<String> fromFileIds) {
    	// 每次查询的最大文件 ID 数量
        int batchSize = 500; 
        List<UserKnowledgeFileResEntity> resultList = new ArrayList<>();

        for (int i = 0; i < fromFileIds.size(); i += batchSize) {
            // 获取当前批次的文件 ID 子列表
            List<String> subFileIds = fromFileIds.subList(i, Math.min(i + batchSize, fromFileIds.size()));

            // 构建查询条件
            LambdaQueryChainWrapper<AlgorithmUserKnowledgeFileResPO> wrapper = this.lambdaQuery()
                    .eq(AlgorithmUserKnowledgeFileResPO::getUserId, userId)
                    .in(AlgorithmUserKnowledgeFileResPO::getFromFileId, subFileIds)
                    .eq(AlgorithmUserKnowledgeFileResPO::getDelFlag, UserKnowledgeFileResDelFlagEnum.NORMAL.getDelFlag());

            // 查询数据并合并结果
            resultList.addAll(userKnowledgeFileResAssembler.toUserKnowledgeFileResEntityList(wrapper.list()));
        }

        return resultList;
    }

    @Override
    public int update(UserKnowledgeFileResEntity entity) {
        AlgorithmUserKnowledgeFileResPO po = userKnowledgeFileResAssembler.toAlgorithmUserKnowledgeFileResPO(entity);
        return algorithmUserKnowledgeFileResMapper.updateById(po);
    }

    @Override
    public UserKnowledgeFileResEntity selectNoteByFileId(String userId, String fileId) {
        LambdaQueryChainWrapper<AlgorithmUserKnowledgeFileResPO> wrapper = this.lambdaQuery()
                .eq(StringUtils.isNotBlank(userId), AlgorithmUserKnowledgeFileResPO::getUserId, userId)
                .in(AlgorithmUserKnowledgeFileResPO::getFileId, fileId)
                .eq(AlgorithmUserKnowledgeFileResPO::getDelFlag, UserKnowledgeFileResDelFlagEnum.NORMAL.getDelFlag());
        AlgorithmUserKnowledgeFileResPO fileRes = wrapper.one();
        if(ObjectUtil.isNotNull(fileRes) && OwnerTypeEnum.NOTE.getOwnerValue().equals(fileRes.getOwnerType())){
            return userKnowledgeFileResAssembler.toAlgorithmUserKnowledgeFileResEntity(fileRes);
        }
        return null;
    }
}
