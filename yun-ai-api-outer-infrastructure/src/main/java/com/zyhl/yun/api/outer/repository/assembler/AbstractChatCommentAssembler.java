package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.hcy.yun.ai.common.model.api.enums.TextModelEnum;
import com.zyhl.yun.api.outer.domain.entity.ChatCommentEntity;
import com.zyhl.yun.api.outer.domain.vo.ChatCommentGetResult;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmChatCommentPO;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2024年02月28日 16:45
 */


@Mapper(componentModel = "spring", uses = TextModelEnum.class, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public abstract class AbstractChatCommentAssembler {

    /**
     * entity转po
     * @param entity entity
     * @return po
     */
    public abstract AlgorithmChatCommentPO toAlgorithmChatComment(ChatCommentEntity entity);

    /**
     * po转entity
     * @param po po
     * @return entity
     */
    public abstract ChatCommentEntity toChatCommentEntity(AlgorithmChatCommentPO po);

    /**
     * po转result
     * @param algorithmChatCommentPo algorithmChatCommentPo
     * @return ChatCommentGetResult
     */
    public abstract ChatCommentGetResult toChatCommentGet(AlgorithmChatCommentPO algorithmChatCommentPo);

    /**
     * po转result 列表
     * @param algorithmChatCommentPoList algorithmChatCommentPoList
     * @return ChatCommentGetResult 列表
     */
    public abstract List<ChatCommentGetResult> toChatCommentGetList(List<AlgorithmChatCommentPO> algorithmChatCommentPoList);

}
