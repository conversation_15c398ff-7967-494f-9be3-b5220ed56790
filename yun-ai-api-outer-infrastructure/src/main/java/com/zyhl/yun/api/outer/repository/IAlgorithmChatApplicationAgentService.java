package com.zyhl.yun.api.outer.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zyhl.yun.api.outer.domain.entity.ApplicationTypeListEntity;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmChatApplicationAgentPO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2024年03月05日 16:06
 */

public interface IAlgorithmChatApplicationAgentService extends IService<AlgorithmChatApplicationAgentPO> {

    /**
     * 获取对话类型列表 升序
     *
     * @param entity 实体参数
     * @return 列表
     */
    List<AlgorithmChatApplicationAgentPO> listOrderedByAsc(ApplicationTypeListEntity entity);

    /**
     * 根据id获取对话类型
     *
     * @param applicationId 应用id
     * @return 对话类型
     */
    AlgorithmChatApplicationAgentPO getById(String applicationId);

    /**
     * 根据id获取对话类型
     *
     * @param idList id集合
     * @return list
     */
    List<AlgorithmChatApplicationAgentPO> getList(List<String> idList);
}
