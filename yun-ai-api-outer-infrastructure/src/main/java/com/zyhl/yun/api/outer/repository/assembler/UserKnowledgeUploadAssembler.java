package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeUploadEntity;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmUserKnowledgeUploadPO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface UserKnowledgeUploadAssembler {

    /**
     * po转entity
     * @param po po
     * @return entity
     */
    UserKnowledgeUploadEntity toUserKnowledgeUploadEntity(AlgorithmUserKnowledgeUploadPO po);

    /**
     * po转entity列表
     * @param poList po
     * @return entity列表
     */
    List<UserKnowledgeUploadEntity> toUserKnowledgeUploadEntityList(List<AlgorithmUserKnowledgeUploadPO> poList);

    /**
     * entity转po
     * @param entity po
     * @return po
     */
    AlgorithmUserKnowledgeUploadPO toUserKnowledgeUploadPo(UserKnowledgeUploadEntity entity);
}
