package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.yun.api.outer.persistence.mapper.AlgorithmAiMigrationMapper;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmAiMigrationPO;
import com.zyhl.yun.api.outer.repository.AlgorithmAiMigrationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * AI迁移表：小天助手1.0.1
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlgorithmAiMigrationRepositoryImpl extends ServiceImpl<AlgorithmAiMigrationMapper, AlgorithmAiMigrationPO> implements AlgorithmAiMigrationRepository {

    private final UidGenerator uidGenerator;


    @Override
    public void add(String userId, String phone, Integer belongsPlatform, String device, String sourceChannel, Integer type) {
        AlgorithmAiMigrationPO po = new AlgorithmAiMigrationPO();
        po.setId(uidGenerator.getUID());
        po.setUserId(userId);
        po.setDevice(device);
        po.setBelongsPlatform(belongsPlatform);
        po.setSourceChannel(sourceChannel);
        po.setType(type);

        baseMapper.insert(po);
    }

    @Override
    public Integer getStatus(String userId) {
        return getStatus(userId, null);
    }

    @Override
    public Integer getStatus(String userId, Integer type) {
        QueryWrapper<AlgorithmAiMigrationPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AlgorithmAiMigrationPO::getUserId, userId)
                .eq(type != null, AlgorithmAiMigrationPO::getType, type);
        List<AlgorithmAiMigrationPO> list = baseMapper.selectList(queryWrapper);

        return ObjectUtil.isEmpty(list) ? null : list.get(0).getStatus();
    }
}
