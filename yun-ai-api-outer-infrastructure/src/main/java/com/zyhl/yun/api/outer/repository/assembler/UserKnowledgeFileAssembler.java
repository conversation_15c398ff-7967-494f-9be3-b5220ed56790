package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileEntity;
import com.zyhl.yun.api.outer.persistence.po.UserKnowledgeFilePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 类转换 UserKnowledgeFileEntity <--> UserKnowledgeFilePO
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface UserKnowledgeFileAssembler {

    UserKnowledgeFileAssembler INSTANCE = Mappers.getMapper(UserKnowledgeFileAssembler.class);

    /**
     * po转entity
     * @param po po
     * @return entity
     */
    UserKnowledgeFileEntity toUserKnowledgeFileEntity(UserKnowledgeFilePO po);

    /**
     * po转entity列表
     * @param poList po
     * @return entity列表
     */
    List<UserKnowledgeFileEntity> toUserKnowledgeFileEntityList(List<UserKnowledgeFilePO> poList);

    /**
     * entity转po
     * @param entity po
     * @return po
     */
    UserKnowledgeFilePO toUserKnowledgeFilePo(UserKnowledgeFileEntity entity);

}
