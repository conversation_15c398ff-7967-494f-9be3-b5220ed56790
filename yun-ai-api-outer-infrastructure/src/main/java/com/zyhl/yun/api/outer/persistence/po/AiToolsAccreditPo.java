package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/1/6 16:58
 */
@Data
@TableName("t_ai_tools_accredit")
public class AiToolsAccreditPo {

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 算法工具
     */
    private Integer module;

    /**
     * 名称
     */
    @TableField("module_name")
    private String moduleName;

    /**
     * 云盘目录path
     */
    private String path;

    /**
     * 用户所属底座
     */
    @TableField("belongs_platform")
    private Integer belongsPlatform;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 业务来源
     */
    @TableField("business_type")
    private Integer sourceBusiness;

    /**
     * 目录id
     */
    @TableField("dir_id")
    private String catalogId;
}
