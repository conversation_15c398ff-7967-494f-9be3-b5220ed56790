package com.zyhl.yun.api.outer.persistence.mapper;

import com.zyhl.yun.api.outer.domain.vo.PromptRecommendVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-17 10:17
 * @description 提示词数据查询mapper
 **/
@Mapper
public interface PromptRecommendMapper {

    /**
     * 调用db查询提示词数据
     * @return 提示词数据集合
     */
    @Select("select prompt_key as code, prompt_name as name, '000' as intentionCommand, business_type as businessType from algorithm_ai_prompt_template where business_type= #{businessType} ")
    List<PromptRecommendVO> listPromptRecommendList(@Param("businessType") String businessType);
}
