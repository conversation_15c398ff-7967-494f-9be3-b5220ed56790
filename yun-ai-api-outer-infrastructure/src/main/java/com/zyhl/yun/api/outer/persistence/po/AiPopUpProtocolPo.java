package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * AI弹窗协议配置-PO
 * @Author: WeiJingKun
 */
@Data
@TableName("t_ai_pop_up_protocol")
public class AiPopUpProtocolPo {

    /** 主键 */
    @TableId
    private Long id;

    /** 渠道 */
    @TableField("channel")
    private String channel;

    /** 类别 */
    @TableField("type")
    private String type;

    /** 标题 */
    @TableField("title")
    private String title;

    /** 正文文案 */
    @TableField("text")
    private String text;

    /** 协议文案 */
    @TableField("protocol")
    private String protocol;

    /** 协议跳转的url */
    @TableField("protocol_url")
    private String protocolUrl;

    /** 确认按钮文案 */
    @TableField("confirm")
    private String confirm;

    /** 取消按钮文案 */
    @TableField("cancel")
    private String cancel;

    /** 创建时间 */
    @TableField("create_time")
    private Date createTime;

    /** 更新时间 */
    @TableField("update_time")
    private Date updateTime;

}
