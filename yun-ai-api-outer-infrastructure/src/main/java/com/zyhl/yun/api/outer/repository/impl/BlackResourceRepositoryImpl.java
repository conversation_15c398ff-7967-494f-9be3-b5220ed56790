package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiBlackResourceEntity;
import com.zyhl.yun.api.outer.persistence.mapper.AlgorithmAiBlackResourceMapper;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmAiBlackResourcePO;
import com.zyhl.yun.api.outer.repository.BlackResourceRepository;
import com.zyhl.yun.api.outer.repository.assembler.BlackResourceAssembler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * className:IntentionInfoRepositoryImpl
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
public class BlackResourceRepositoryImpl extends ServiceImpl<AlgorithmAiBlackResourceMapper, AlgorithmAiBlackResourcePO>
		implements BlackResourceRepository {

	@Resource
	private BlackResourceAssembler blackResourceAssembler;

	@Resource
	private AlgorithmAiBlackResourceMapper blackResourceMapper;

	@Override
	public List<AlgorithmAiBlackResourceEntity> getAllDate() {
		return blackResourceAssembler.toBlackResourceEntityList(blackResourceMapper.selectList(null));
	}

	@Override
	public List<String> getResourceNameList(List<String> names, List<Integer> types) {
		if (ObjectUtil.isEmpty(names) || ObjectUtil.isEmpty(types)) {
			return new ArrayList<>();
		}
		/** 构建查询条件 */
		LambdaQueryWrapper<AlgorithmAiBlackResourcePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
		lambdaQueryWrapper.in(AlgorithmAiBlackResourcePO::getResourceName, names)
				.in(AlgorithmAiBlackResourcePO::getShieldedType, types);
		/** 查询数据 */
		List<AlgorithmAiBlackResourcePO> list = blackResourceMapper.selectList(lambdaQueryWrapper);
		if(CollUtil.isNotEmpty(list)){
			// 获取resourceName列表，去重
			return list.stream().map(AlgorithmAiBlackResourcePO::getResourceName).distinct().collect(Collectors.toList());
		}
		return new ArrayList<>();
	}

}
