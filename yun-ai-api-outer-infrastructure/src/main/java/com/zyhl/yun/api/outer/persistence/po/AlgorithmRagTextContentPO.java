package com.zyhl.yun.api.outer.persistence.po;

import com.zyhl.hcy.yun.ai.common.base.hbase.infrastructure.persistence.aspect.HbaseColumn;
import com.zyhl.hcy.yun.ai.common.base.hbase.infrastructure.persistence.aspect.HbaseTable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * className: AlgorithmRagTextContentPO
 * description: RAG - 邮件/笔记 - 资源的正文内容hbasePO类
 *
 * <AUTHOR>
 * @date 2025/2/14
 */
@Builder
@Data
@HbaseTable(tableName = "algorithm_rag_text_content")
@AllArgsConstructor
@NoArgsConstructor
public class AlgorithmRagTextContentPO {

    @HbaseColumn(family = "info")
    private String rowKey;

    @HbaseColumn(family = "info")
    private String fileId;

    @HbaseColumn(family = "info")
    private String userId;

    @HbaseColumn(family = "info")
    private String resourceType;

    @HbaseColumn(family = "details")
    private String content;

    /**
     * 附件信息列表
     * json格式：邮箱的附件，笔记的附件在content里面
     */
    @HbaseColumn(family = "details")
    private String attachment;

    /**
     * 正文（新）
     */
    @HbaseColumn(family = "details")
    private String newContent;
}
