package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.image.AlgorithmUserFileEntity;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmUserFilePo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface AlgorithmUserFileAssembler {

    AlgorithmUserFileAssembler INSTANCE = Mappers.getMapper(AlgorithmUserFileAssembler.class);


    /**
     * 将算法用户文件实体列表转换为算法用户文件PO列表
     *
     * @param taskFileEntity 算法用户文件实体列表
     * @return 返回转换后的算法用户文件PO列表
     */
    List<AlgorithmUserFilePo> toAlgorithmUserFilePO(List<AlgorithmUserFileEntity> taskFileEntity);


    /**
     * 将算法用户文件实体列表转换为算法用户文件PO列表
     *
     * @param userFilePoList 算法用户文件PO列表
     * @return 返回转换后的算法用户文件实体列表
     */
    List<AlgorithmUserFileEntity> toAlgorithmUserFileEntity(List<AlgorithmUserFilePo> userFilePoList);
}
