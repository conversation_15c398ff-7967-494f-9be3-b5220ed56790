package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 公共知识库文件资源表
 *
 * <AUTHOR>
 */
@Data
@Builder
@TableName("algorithm_knowledge_file")
public class KnowledgeFilePO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 知识库的标识
     * common 公共知识库
     * customer 客服
     */
    @TableField("base_id")
    private String baseId;

    /**
     * 文件内容md5
     */
    @TableField("file_id")
    private String fileId;

    /**
     * 文件存储类型
     * eos-eos对象存储
     */
    @TableField("store_type")
    private String storeType;

    /**
     * 存储的key
     * 如果是eos，则是eos的key
     */
    @TableField("store_key")
    private String storeKey;

    /**
     * 文件哈希名
     */
    @TableField("hash_name")
    private String hashName;

    /**
     * 文件哈希值
     */
    @TableField("hash_value")
    private String hashValue;

    /**
     * 文件名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 文件大小
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 文件后缀
     */
    @TableField("extension")
    private String extension;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建人id
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新人id
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 业务算法组编码
     * 4-文档向量化算法组（公共知识库）
     */
    @TableField("algorithm_group_code")
    private int algorithmGroupCode;

    /**
     * 算法id
     */
    @TableField("algorithm_ids")
    private String algorithmIds;

    /**
     * 算法结果状态
     * 默认 0 未处理
     * 1 成功
     * 2 失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 算法结果码
     * 0000 成功
     * 其他则错误码
     */
    @TableField("result_code")
    private String resultCode;

    /**
     * 算法结果
     * 记录错误信息；成功的结果保存到hbase;文本和图片向量不写入这里
     */
    @TableField("result_msg")
    private String resultMsg;

    /**
     * 执行次数
     */
    @TableField("execute_count")
    private Integer executeCount;

    /**
     * 删除标识，0-正常，1-已删除
     */
    @TableField("del_flag")
    private Integer delFlag;
}
