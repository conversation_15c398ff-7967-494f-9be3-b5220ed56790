package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.yun.api.outer.domain.entity.knowledge.AlgorithmKnowledgeConfigEntity;
import com.zyhl.yun.api.outer.persistence.mapper.AlgorithmKnowledgeConfigMapper;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmKnowledgeConfigPO;
import com.zyhl.yun.api.outer.repository.AlgorithmKnowledgeConfigRepository;
import com.zyhl.yun.api.outer.repository.assembler.AlgorithmKnowledgeConfigAssembler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * 公共知识库配置表-RepositoryImpl
 *
 * @Author: WeiJingKun
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class AlgorithmKnowledgeConfigRepositoryImpl extends ServiceImpl<AlgorithmKnowledgeConfigMapper, AlgorithmKnowledgeConfigPO> implements AlgorithmKnowledgeConfigRepository {

    private final AlgorithmKnowledgeConfigAssembler assembler;

    @Override
    public AlgorithmKnowledgeConfigEntity queryById(String id) {
        /** 查询数据返回 */
        AlgorithmKnowledgeConfigEntity entity = assembler.toEntity(baseMapper.selectById(id));

        /** config转ConfigDTO */
        if (null != entity) {
            entity.convertConfigDTO();
        }
        return entity;
    }

    @Override
    public List<AlgorithmKnowledgeConfigEntity> selectByIds(List<String> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return assembler.toEntityList(baseMapper.selectBatchIds(ids));
    }

}
