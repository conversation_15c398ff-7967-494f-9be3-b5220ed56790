package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiPersonResourceEntity;
import com.zyhl.yun.api.outer.persistence.mapper.AlgorithmAiPersonResourceMapper;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmAiPersonResourcePO;
import com.zyhl.yun.api.outer.repository.AlgorithmAiPersonResourceRepository;
import com.zyhl.yun.api.outer.repository.assembler.AlgorithmAiPersonResourceAssembler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * className:IntentionInfoRepositoryImpl
 * 
 * <AUTHOR>
 */
@Slf4j
@Repository
public class AlgorithmAiPersonResourceRepositoryImpl extends ServiceImpl<AlgorithmAiPersonResourceMapper, AlgorithmAiPersonResourcePO>
		implements AlgorithmAiPersonResourceRepository {

	@Resource
	private AlgorithmAiPersonResourceAssembler assembler;

	@Resource
	private UidGenerator uidGenerator;

	@Resource
	private AlgorithmAiPersonResourceMapper resourceMapper;

	@Override
	public List<AlgorithmAiPersonResourceEntity> getAllDate() {
		return assembler.toEntityList(resourceMapper.selectList(null));
	}

	@Override
	public boolean batchAdd(List<AlgorithmAiPersonResourceEntity> list) {
		if (CollectionUtil.isEmpty(list)) {
			return false;
		}
		List<AlgorithmAiPersonResourcePO> poList = new ArrayList<>(list.size());
		for (AlgorithmAiPersonResourceEntity entity : list) {
			AlgorithmAiPersonResourcePO po = assembler.toPo(entity);
			po.setId(uidGenerator.getUID());
			po.setCreateTime(new Date());
			po.setUpdateTime(new Date());
			poList.add(po);
		}
		return this.saveBatch(poList, poList.size());
	}
}
