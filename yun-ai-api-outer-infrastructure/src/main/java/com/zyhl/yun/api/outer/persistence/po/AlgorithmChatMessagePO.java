package com.zyhl.yun.api.outer.persistence.po;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @data 2024/2/29 13:51
 */
@Data
@TableName("algorithm_chat_message")
public class AlgorithmChatMessagePO {
    /** 主键 会话ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;
    /**
     * 应用id
     */
    @TableField("application_id")
    private Long applicationId;
    /**
     * 应用类型
     * @see com.zyhl.yun.api.outer.enums.ApplicationTypeEnum
     */
    @TableField("application_type")
    private String applicationType;
    /**
     * 业务类型
     * 详见，配置文件：source-channels
     */
    @TableField("business_type")
    private String businessType;
    /**
     * 标题;默认取会话中的第一句对话输入内容
     */
    @TableField("title")
    private String title;
    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN, timezone = "GMT+8")
    private Date updateTime;
    
    /**
     * 是否星标;(1是，0否)
     * @see com.zyhl.yun.api.outer.enums.ChatMessageStarEnum
     */
    @TableField("enable_star")
    private Integer enableStar;

    /**
     * 图标类型
     */
    @TableField("icon_type")
    private String iconType;

    /**
     * 图标子类型
     */
    @TableField("sub_icon_type")
    private String subIconType;

    /**
     * 是否删除;(1是，0否)
     */
    @TableField("del_flag")
    private Integer delFlag;
}
