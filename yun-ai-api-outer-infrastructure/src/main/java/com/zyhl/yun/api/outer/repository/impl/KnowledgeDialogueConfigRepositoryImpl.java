package com.zyhl.yun.api.outer.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.yun.api.outer.domain.entity.knowledge.KnowledgeDialogueConfigEntity;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeStatusEnum;
import com.zyhl.yun.api.outer.persistence.mapper.KnowledgeDialogueConfigMapper;
import com.zyhl.yun.api.outer.persistence.po.KnowledgeDialogueConfigPO;
import com.zyhl.yun.api.outer.repository.KnowledgeDialogueConfigRepository;
import com.zyhl.yun.api.outer.repository.assembler.KnowledgeDialogueConfigAssembler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 知识库对话配置
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class KnowledgeDialogueConfigRepositoryImpl extends ServiceImpl<KnowledgeDialogueConfigMapper, KnowledgeDialogueConfigPO> implements KnowledgeDialogueConfigRepository {

    private final KnowledgeDialogueConfigAssembler knowledgeDialogueConfigAssembler;


    @Override
    public List<KnowledgeDialogueConfigEntity> selectByUserId(String userId) {
        List<KnowledgeDialogueConfigPO> list = this.lambdaQuery()
                .eq(KnowledgeDialogueConfigPO::getUserId, userId)
                .eq(KnowledgeDialogueConfigPO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                .list();
        return knowledgeDialogueConfigAssembler.toKnowledgeDialogueConfigEntityList(list);
    }
}
