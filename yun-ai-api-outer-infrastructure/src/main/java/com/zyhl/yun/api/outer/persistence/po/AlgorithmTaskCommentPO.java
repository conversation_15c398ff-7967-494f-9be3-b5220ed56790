package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 算法任务评论表
 * @date 2025/4/21 17:19
 */
@Data
@TableName("algorithm_task_comment")
public class AlgorithmTaskCommentPO {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 任务id
     */
    @TableField(value = "task_id")
    private Long taskId;

    /**
     * 模块编码
     */
    private String module;

    /**
     * 模型类型
     * qwen：通义千问，xfyun：讯飞星火大模型
     */
    @TableField("model_type")
    private String modelType;

    /**
     * 是否喜欢
     *
     * @see com.zyhl.yun.api.outer.enums.LikeEnum 中的module
     */
    @TableField("like_comment")
    private Integer likeComment;

    /**
     * 默认评论
     */
    @TableField("default_comment")
    private String defaultComment;

    /**
     * 用户自定义评论
     */
    @TableField("custom_comment")
    private String customComment;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 变更时间时间戳，默认值用current_timestamp
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

}