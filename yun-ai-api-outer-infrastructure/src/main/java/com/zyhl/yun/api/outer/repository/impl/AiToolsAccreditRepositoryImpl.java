package com.zyhl.yun.api.outer.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.yun.api.outer.domain.entity.AiToolsAccreditEntity;
import com.zyhl.yun.api.outer.persistence.mapper.AiToolsAccreditMapper;
import com.zyhl.yun.api.outer.persistence.po.AiToolsAccreditPo;
import com.zyhl.yun.api.outer.repository.AiToolsAccreditRepository;
import com.zyhl.yun.api.outer.repository.assembler.EntityAssembler;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/6 16:53
 */
@Service
@RequiredArgsConstructor
public class AiToolsAccreditRepositoryImpl extends ServiceImpl<AiToolsAccreditMapper, AiToolsAccreditPo> implements AiToolsAccreditRepository {

    private final EntityAssembler entityAssembler;

    private final UidGenerator uidGenerator;

    /**
     * 查询单个记录
     *
     * @param entity
     * @return
     */
    @Override
    public AiToolsAccreditEntity selectOne(AiToolsAccreditEntity entity) {
        QueryWrapper<AiToolsAccreditPo> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(!ObjectUtils.isEmpty(entity.getModule()), AiToolsAccreditPo::getModule, entity.getModule())
                .eq(AiToolsAccreditPo::getUserId, entity.getUserId())
                .eq(AiToolsAccreditPo::getSourceBusiness, entity.getSourceBusiness());
        AiToolsAccreditPo po = baseMapper.selectOne(wrapper);
        return entityAssembler.toAiToolsAccreditEntity(po);
    }

    /**
     * 插入报名（授权记录）
     *
     * @param entity
     */
    @Override
    public void insert(AiToolsAccreditEntity entity) {
        AiToolsAccreditPo po = entityAssembler.toAiToolsAccreditPo(entity);
        po.setId(uidGenerator.getUID());
        baseMapper.insert(po);
    }

    @Override
    public void updateById(AiToolsAccreditEntity entity) {
        AiToolsAccreditPo po = entityAssembler.toAiToolsAccreditPo(entity);
        baseMapper.updateById(po);
    }

    @Override
    public List<AiToolsAccreditEntity> queryByUserId(AiToolsAccreditEntity entity) {
        QueryWrapper<AiToolsAccreditPo> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AiToolsAccreditPo::getUserId, entity.getUserId());
        List<AiToolsAccreditPo> poList = baseMapper.selectList(wrapper);
        if (ObjectUtils.isEmpty(poList)) {
            return null;
        }
        List<AiToolsAccreditEntity> aiToolsAccreditEntityList = new ArrayList<>();
        for (AiToolsAccreditPo po : poList) {
            AiToolsAccreditEntity aiToolsAccreditEntity = entityAssembler.toAiToolsAccreditEntity(po);
            aiToolsAccreditEntityList.add(aiToolsAccreditEntity);
        }
        return aiToolsAccreditEntityList;
    }

    @Override
    public List<AiToolsAccreditEntity> queryByUserId(String userId) {
        // 查询数据
        final QueryWrapper<AiToolsAccreditPo> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AiToolsAccreditPo::getUserId, userId);
        final List<AiToolsAccreditPo> poList = baseMapper.selectList(wrapper);

        // 数据转换
        final List<AiToolsAccreditEntity> result = new ArrayList<>();
        if (!ObjectUtils.isEmpty(poList)) {
            poList.forEach(po -> result.add(entityAssembler.toAiToolsAccreditEntity(po)));
        }

        return result;
    }
}
