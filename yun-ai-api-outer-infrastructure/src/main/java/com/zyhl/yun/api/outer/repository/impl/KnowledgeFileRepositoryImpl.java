package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.yun.api.outer.domain.entity.knowledge.KnowledgeFileEntity;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeStatusEnum;
import com.zyhl.yun.api.outer.persistence.mapper.KnowledgeFileMapper;
import com.zyhl.yun.api.outer.persistence.po.KnowledgeFilePO;
import com.zyhl.yun.api.outer.repository.KnowledgeFileRepository;
import com.zyhl.yun.api.outer.repository.assembler.KnowledgeFileAssembler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 公共知识库文件资源表
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class KnowledgeFileRepositoryImpl extends ServiceImpl<KnowledgeFileMapper, KnowledgeFilePO> implements KnowledgeFileRepository {

    @Resource
    private KnowledgeFileAssembler assembler;

    @Override
    public int count(String baseId) {
        return this.lambdaQuery()
                .eq(KnowledgeFilePO::getBaseId, baseId)
                .eq(KnowledgeFilePO::getDelFlag, 0)
                .count();
    }

    @Override
    public List<KnowledgeFileEntity> selectByFileIds(String baseId, List<String> fileIds) {
        if (ObjectUtil.isEmpty(fileIds)) {
            return new ArrayList<>();
        }
        LambdaQueryChainWrapper<KnowledgeFilePO> wrapper = this.lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(baseId), KnowledgeFilePO::getBaseId, baseId)
                .eq(KnowledgeFilePO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus())
                .in(KnowledgeFilePO::getFileId, fileIds);

        return assembler.toKnowledgeFileEntityList(wrapper.list());
    }
}
