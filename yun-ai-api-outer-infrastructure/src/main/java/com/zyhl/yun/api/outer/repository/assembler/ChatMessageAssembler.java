package com.zyhl.yun.api.outer.repository.assembler;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatMessageEntity;
import com.zyhl.yun.api.outer.domain.vo.chat.MessageVO;
import com.zyhl.yun.api.outer.enums.ChatMessageStarEnum;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmChatMessagePO;

/**
 * <AUTHOR>
 * @Date 2024/03/09 14:00
 */
@Mapper(componentModel = "spring")
public interface ChatMessageAssembler {

    ChatMessageAssembler INSTANCE = Mappers.getMapper(ChatMessageAssembler.class);

    /**
     * entity转po
     * @param entity po
     * @return po
     */
    AlgorithmChatMessagePO toChatMessage(AlgorithmChatMessageEntity entity);

    /**
     * po转entity
     * @param po po
     * @return entity列表
     */
    List<AlgorithmChatMessageEntity> toChatMessageEntityList(List<AlgorithmChatMessagePO> po);

    /**
     * entity转MessageVO
     * @param entity entity
     * @return MessageVO
     */
	@Mapping(target = "enableStar", source = "enableStar", qualifiedByName = "caseToEnableStar")
    MessageVO toMessageVO(AlgorithmChatMessageEntity entity);
	
	/**
	 * 转换为启用star
	 * 
	 * @param enableStar 是否启用star
	 * @return 是否star
	 */
	@Named("caseToEnableStar")
	default Boolean caseToEnableStar(Integer enableStar) {
		return ChatMessageStarEnum.isStar(enableStar);
	}
}
