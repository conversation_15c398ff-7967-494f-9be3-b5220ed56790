package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.yun.api.outer.domain.entity.AIToolsConsumeRecordEntity;
import com.zyhl.yun.api.outer.persistence.mapper.AIToolsConsumeRecordMapper;
import com.zyhl.yun.api.outer.persistence.po.AIToolsConsumeRecordPO;
import com.zyhl.yun.api.outer.repository.AIToolsConsumeRecordRepository;
import com.zyhl.yun.api.outer.repository.assembler.EntityAssembler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.repository.impl.AIToolsConsumeRecordRepositoryImpl} <br>
 * <b> description:</b>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-08-16 18:30
 **/

@Slf4j
@RequiredArgsConstructor
@Service
public class AIToolsConsumeRecordRepositoryImpl extends ServiceImpl<AIToolsConsumeRecordMapper, AIToolsConsumeRecordPO>
        implements AIToolsConsumeRecordRepository {

    private final EntityAssembler entityAssembler;

    private final UidGenerator uidGenerator;

    @Override
    public List<AIToolsConsumeRecordEntity> selectTodayListByChannelId(AIToolsConsumeRecordEntity entity) {
        List<AIToolsConsumeRecordPO> recordPOList = selectList(entity);
        return entityAssembler.toAIToolsConsumeRecordEntityList(recordPOList);
    }

    @Override
    public AIToolsConsumeRecordEntity save(AIToolsConsumeRecordEntity consumeRecord) {
        AIToolsConsumeRecordPO consumeRecordPO = entityAssembler.toAIToolsConsumeRecordPO(consumeRecord);
        if (consumeRecord.getId() != null) {
            baseMapper.updateById(consumeRecordPO);
        } else {
            consumeRecordPO.setId(uidGenerator.getUID());
            consumeRecordPO.setConsumeDate(getToday());
            baseMapper.insert(consumeRecordPO);
            consumeRecord.setId(consumeRecordPO.getId());
        }
        return consumeRecord;
    }

    @Override
    public Integer getTodayConsumeTimes(AIToolsConsumeRecordEntity condition) {
        List<AIToolsConsumeRecordPO> list = selectList(condition);
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return list.stream().mapToInt(AIToolsConsumeRecordPO::getUseTimes).sum();
    }

    /**
     * 获取查询list
     *
     * @param condition the condition
     * @return {@link List<AIToolsConsumeRecordPO>}
     * <AUTHOR>
     * @date 2024-8-16 18:39
     */
    private List<AIToolsConsumeRecordPO> selectList(AIToolsConsumeRecordEntity condition) {
        QueryWrapper<AIToolsConsumeRecordPO> wrapper = new QueryWrapper<>();
        LambdaQueryWrapper<AIToolsConsumeRecordPO> lambda = wrapper.lambda();
        lambda.eq(condition.getId() != null, AIToolsConsumeRecordPO::getId, condition.getId())
                .eq(AIToolsConsumeRecordPO::getUserId, condition.getUserId())
                .eq(AIToolsConsumeRecordPO::getConsumeDate, getToday())
                .eq(condition.getModule() != null, AIToolsConsumeRecordPO::getModule, condition.getModule())
                .eq(condition.getChannelId() != null, AIToolsConsumeRecordPO::getChannelId, condition.getChannelId());
        return baseMapper.selectList(wrapper);
    }

    /**
     * 获取今日日期格式
     *
     * @return {@link String}
     * <AUTHOR>
     * @date 2024-8-16 18:40
     */
    private String getToday() {
        return DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN);
    }
}
