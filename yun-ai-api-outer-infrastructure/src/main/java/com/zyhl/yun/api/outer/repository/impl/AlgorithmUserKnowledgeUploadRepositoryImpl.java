package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.dashscope.utils.JsonUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.PersonalKnowledgeImportTaskEntity;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeUploadEntity;
import com.zyhl.yun.api.outer.domain.vo.ImportHtmlInfoVO;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeUploadStatusEnum;
import com.zyhl.yun.api.outer.persistence.mapper.AlgorithmUserKnowledgeUploadMapper;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmUserKnowledgeUploadPO;
import com.zyhl.yun.api.outer.repository.AlgorithmUserKnowledgeUploadRepository;
import com.zyhl.yun.api.outer.repository.assembler.AlgorithmUserKnowledgeUploadAssembler;
import com.zyhl.yun.api.outer.repository.assembler.UserKnowledgeUploadAssembler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 算法用户知识上传Repository实现类
 *
 * <AUTHOR>
 * @date 2025-04-16 15:43:42
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class AlgorithmUserKnowledgeUploadRepositoryImpl extends ServiceImpl<AlgorithmUserKnowledgeUploadMapper, AlgorithmUserKnowledgeUploadPO> implements AlgorithmUserKnowledgeUploadRepository {


    @Resource
    private final UidGenerator uidGenerator;
    /**
     * 删除标记常量
     */
    private static final int NOT_DELETED = 0;
    private static final int DELETED = 1;

    @Resource
    private AlgorithmUserKnowledgeUploadAssembler assembler;
    @Resource
    private UserKnowledgeUploadAssembler uploadAssembler;

    @Override
    public int deleteByUserIdAndId(String userId, List<Long> ids) {
        try {
            // 使用更新操作进行逻辑删除
            return this.lambdaUpdate()
                    .eq(AlgorithmUserKnowledgeUploadPO::getUserId, userId)
                    .in(AlgorithmUserKnowledgeUploadPO::getId, ids)
                    .eq(AlgorithmUserKnowledgeUploadPO::getDelFlag, NOT_DELETED)
                    .set(AlgorithmUserKnowledgeUploadPO::getDelFlag, DELETED)
                    .set(AlgorithmUserKnowledgeUploadPO::getUpdateTime, new Date())
                    .update() ? ids.size() : 0;
        } catch (Exception e) {
            log.error("Error occurred while deleting knowledge upload records for userId={}, ids={}", userId, ids, e);
            return 0;
        }
    }

    @Override
    public PageInfo<PersonalKnowledgeImportTaskEntity> findByUserIdAndBaseId(String userId, String baseId, Integer[] statusArray, PageInfoDTO pageInfo) {
        try {
            // 使用PageHelper进行分页
            PageHelper.offsetPage(Integer.parseInt(pageInfo.getPageCursor()), pageInfo.getPageSize(), true);
            LambdaQueryWrapper<AlgorithmUserKnowledgeUploadPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AlgorithmUserKnowledgeUploadPO::getUserId, userId)
                    .eq(AlgorithmUserKnowledgeUploadPO::getBaseId, baseId)
                    .eq(AlgorithmUserKnowledgeUploadPO::getFileType, 1)
                    .eq(AlgorithmUserKnowledgeUploadPO::getDelFlag, NOT_DELETED);
            List<Integer> dbStatusList ;
            if (ObjectUtil.isNotEmpty(statusArray)) {
                // 转换请求状态为数据库状态，并过滤掉空值
                dbStatusList = Arrays.stream(statusArray)
                        .flatMap(status -> KnowledgeUploadStatusEnum.convertRequestStatusToDbStatus(status).stream())
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                // 如果转换后的状态列表不为空，则添加到查询条件中
                if (!dbStatusList.isEmpty()) {
                    wrapper.in(AlgorithmUserKnowledgeUploadPO::getUploadStatus, dbStatusList);
                }
            } else {
                // 默认返回处理失败和处理中的数据
                dbStatusList = Arrays.asList(
                        KnowledgeUploadStatusEnum.NOT_PROCESSED.getStatus(),
                        KnowledgeUploadStatusEnum.FAIL.getStatus(),
                        KnowledgeUploadStatusEnum.PROCESSING.getStatus()
                );
                wrapper.in(AlgorithmUserKnowledgeUploadPO::getUploadStatus, dbStatusList);
            }
            wrapper.orderByDesc(AlgorithmUserKnowledgeUploadPO::getUpdateTime);
            // 执行查询
            List<AlgorithmUserKnowledgeUploadPO> records = baseMapper.selectList(wrapper);
            // 检查结果是否为空
            if (CollectionUtils.isEmpty(records)) {
                log.info("上传表数据为空: userId={}, baseId={}", userId, baseId);
                return PageInfo.of(new Page<>());
            }
            //  根据上传状态排序：未处理 > 处理中 > 处理失败 > 成功
            Map<Integer, Integer> statusOrderMap = new HashMap<>(4);
            statusOrderMap.put(KnowledgeUploadStatusEnum.NOT_PROCESSED.getStatus(), 0);
            statusOrderMap.put(KnowledgeUploadStatusEnum.PROCESSING.getStatus(), 1);
            statusOrderMap.put(KnowledgeUploadStatusEnum.FAIL.getStatus(), 2);
            statusOrderMap.put(KnowledgeUploadStatusEnum.SUCCESS.getStatus(), 3);
            // 只按照状态优先级排序，保持SQL查询的时间排序
            records.sort(Comparator.comparing(
                    AlgorithmUserKnowledgeUploadPO::getUploadStatus,
                    Comparator.nullsLast(Comparator.comparingInt(status ->
                            statusOrderMap.getOrDefault(status, 999)
                    ))
            ));
            // 转换为实体对象并返回
            List<PersonalKnowledgeImportTaskEntity> entityList = assembler.toPersonalKnowledgeImportTaskEntity(records);
            // 送审不通过
            entityList.forEach(entity -> {if(ResultCodeEnum.SENSITIVE_WORDS_ERROR.getResultCode().equals(entity.getErrorCode())){
                entity.setStatus(Integer.valueOf(entity.getErrorCode()));
            }});
            Page<PersonalKnowledgeImportTaskEntity> page = new Page<>();
            page.addAll(entityList);
            page.setTotal(((Page<?>) records).getTotal());
            page.setPageSize(pageInfo.getPageSize());
            return PageInfo.of(page);
        } catch (Exception e) {
            log.error("查询上传表报错: userId={}, baseId={}", userId, baseId, e);
            return null;
        }
    }

    @Override
    public List<PersonalKnowledgeImportTaskEntity> findByUserIdAndBaseIdAndFileId(String userId, String baseId, Integer resourceType, List<String> fileIdList) {
        try {
            log.debug("Querying knowledge upload records with pagination: userId={}, baseId={}, resourceType={}, fileIdList={}",
                    userId, baseId, resourceType, fileIdList);
            LambdaQueryWrapper<AlgorithmUserKnowledgeUploadPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AlgorithmUserKnowledgeUploadPO::getUserId, userId)
                    .eq(AlgorithmUserKnowledgeUploadPO::getBaseId, baseId)
                    .eq(AlgorithmUserKnowledgeUploadPO::getDelFlag, NOT_DELETED)
                    .eq(AlgorithmUserKnowledgeUploadPO::getResourceType, resourceType)
                    .in(AlgorithmUserKnowledgeUploadPO::getFileId, fileIdList);
            // 执行查询
            List<AlgorithmUserKnowledgeUploadPO> result = baseMapper.selectList(wrapper);
            // 检查结果是否为空
            if (Objects.isNull(result) || CollectionUtils.isEmpty(result)) {
                log.info("No knowledge upload records found: userId={}, baseId={}", userId, baseId);
                return Collections.emptyList();
            }
            log.debug("Found {} knowledge upload records: userId={}, baseId={}, resourceType={}",
                    result.size(), userId, baseId, resourceType);
            // 转换为实体对象并返回
            return assembler.toPersonalKnowledgeImportTaskEntity(result);
        } catch (Exception e) {
            log.error("Error occurred while finding knowledge upload records: userId={}, baseId={}", userId, baseId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<PersonalKnowledgeImportTaskEntity> findNotSuccessByFileIds(String userId, String baseId, Integer resourceType, List<String> fileIdList) {
        try {
            log.debug("Querying knowledge upload records with pagination: userId={}, baseId={}, resourceType={}, fileIdList={}",
                    userId, baseId, resourceType, fileIdList);
            LambdaQueryWrapper<AlgorithmUserKnowledgeUploadPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AlgorithmUserKnowledgeUploadPO::getUserId, userId)
                    .eq(AlgorithmUserKnowledgeUploadPO::getBaseId, baseId)
                    .eq(AlgorithmUserKnowledgeUploadPO::getDelFlag, NOT_DELETED)
                    .eq(AlgorithmUserKnowledgeUploadPO::getResourceType, resourceType)
                    .ne(AlgorithmUserKnowledgeUploadPO::getUploadStatus, KnowledgeUploadStatusEnum.SUCCESS.getStatus())
                    .in(AlgorithmUserKnowledgeUploadPO::getFileId, fileIdList);
            // 执行查询
            List<AlgorithmUserKnowledgeUploadPO> result = baseMapper.selectList(wrapper);
            // 检查结果是否为空
            if (Objects.isNull(result) || CollectionUtils.isEmpty(result)) {
                log.info("No knowledge upload records found: userId={}, baseId={}", userId, baseId);
                return Collections.emptyList();
            }
            log.debug("Found {} knowledge upload records: userId={}, baseId={}, resourceType={}",
                    result.size(), userId, baseId, resourceType);
            // 转换为实体对象并返回
            return assembler.toPersonalKnowledgeImportTaskEntity(result);
        } catch (Exception e) {
            log.error("Error occurred while finding knowledge upload records: userId={}, baseId={}", userId, baseId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public boolean insertBatch(List<PersonalKnowledgeImportTaskEntity> entities) {
        List<AlgorithmUserKnowledgeUploadPO> pos = new ArrayList<>();
        for (PersonalKnowledgeImportTaskEntity entity : entities) {
            AlgorithmUserKnowledgeUploadPO po = new AlgorithmUserKnowledgeUploadPO();
            BeanUtils.copyProperties(entity, AlgorithmUserKnowledgeUploadPO.class);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
            String date = sdf.format(new Date());
            try {
                po.setCreateTime(sdf.parse(date));
            } catch (Exception e) {
                log.error("Failed to format date: {}", date, e);
            }
            pos.add(po);
        }
        return this.saveBatch(pos, pos.size());
    }

    @Override
    public boolean batchAdd(List<UserKnowledgeUploadEntity> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return false;
        }
        List<AlgorithmUserKnowledgeUploadPO> pos = new ArrayList<>(entityList.size());
        for (UserKnowledgeUploadEntity entity : entityList) {
            entity.setId(uidGenerator.getUID());
            entity.setCreateTime(new Date());
            entity.setUpdateTime(new Date());
            if (OwnerTypeEnum.isPersonal(entity.getOwnerType())) {
                entity.setOwnerId(entity.getUserId());
            }
            pos.add(uploadAssembler.toUserKnowledgeUploadPo(entity));
        }
        return this.saveBatch(pos, pos.size());
    }

    @Override
    public int add(UserKnowledgeUploadEntity entity) {
        entity.setId(uidGenerator.getUID());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());

        if (OwnerTypeEnum.isPersonal(entity.getOwnerType())) {
            entity.setOwnerId(entity.getUserId());
        }
        return baseMapper.insert(uploadAssembler.toUserKnowledgeUploadPo(entity));
    }

    @Override
    public PersonalKnowledgeImportTaskEntity findById(Long id) {
        try {
            AlgorithmUserKnowledgeUploadPO po = baseMapper.selectById(id);
            if (po == null) {
                log.info("查不到任务信息: {}", id);
                return null;
            }
            return assembler.toPersonalKnowledgeImportTaskEntity(Collections.singletonList(po)).get(0);
        } catch (Exception e) {
            log.error("Error occurred while finding knowledge upload record by id: {}", id, e);
            return null;
        }
    }

    @Override
    public void updateById(PersonalKnowledgeImportTaskEntity taskEntity) {
        try {
            this.lambdaUpdate()
                    .eq(AlgorithmUserKnowledgeUploadPO::getUserId, taskEntity.getUserId())
                    .eq(AlgorithmUserKnowledgeUploadPO::getId, Long.valueOf(taskEntity.getTaskId()))
                    .set(AlgorithmUserKnowledgeUploadPO::getUploadStatus, taskEntity.getUploadStatus())
                    .set(AlgorithmUserKnowledgeUploadPO::getUpdateTime, new Date())
                    .set(AlgorithmUserKnowledgeUploadPO::getResultMsg, "")
                    .set(AlgorithmUserKnowledgeUploadPO::getResultCode, "")
                    .update();
        } catch (Exception e) {
            log.error("Error occurred while updating knowledge upload record: id={}", taskEntity.getTaskId(), e);
            throw new RuntimeException("Failed to update knowledge upload record", e);
        }
    }

    @Override
    public List<PersonalKnowledgeImportTaskEntity> selectHtmlResource(String userId, Long baseId,List<String> urlList) {
        // 查询条件
        LambdaQueryChainWrapper<AlgorithmUserKnowledgeUploadPO> wrapper = this.lambdaQuery()
                .eq(AlgorithmUserKnowledgeUploadPO::getUserId, userId)
                .eq(AlgorithmUserKnowledgeUploadPO::getBaseId, baseId)
                .in(AlgorithmUserKnowledgeUploadPO::getResourceType, KnowledgeResourceTypeEnum.HTML.getCode())
                .in(AlgorithmUserKnowledgeUploadPO::getUploadStatus, KnowledgeUploadStatusEnum.NOT_PROCESSED.getStatus()
                        , KnowledgeUploadStatusEnum.PROCESSING.getStatus())
                .eq(AlgorithmUserKnowledgeUploadPO::getDelFlag, KnowledgeStatusEnum.NORMAL.getStatus());


        // 查询数据
        List<PersonalKnowledgeImportTaskEntity> entities = assembler.toPersonalKnowledgeImportTaskEntity(wrapper.list());

        ArrayList<PersonalKnowledgeImportTaskEntity> list = new ArrayList<>();
        // 过滤数据
        for (PersonalKnowledgeImportTaskEntity entity : entities) {
            entity.setHtmlInfo(JsonUtils.fromJson(entity.getResource(), ImportHtmlInfoVO.class));
            if (Objects.nonNull(entity.getHtmlInfo()) && urlList.contains(entity.getHtmlInfo().getUrl())) {
                list.add(entity);
            }
        }

        return list;
    }

    @Override
    public void updateRetryById(UserKnowledgeUploadEntity entity) {
        AlgorithmUserKnowledgeUploadPO userKnowledgeUploadPo = uploadAssembler.toUserKnowledgeUploadPo(entity);
        userKnowledgeUploadPo.setUpdateTime(new Date());
        this.updateById(userKnowledgeUploadPo);
    }

    @Override
    public List<PersonalKnowledgeImportTaskEntity> findListByParam(String userId, String baseId, Integer resourceType, List<String> fileIdList, Integer status) {
        try {
            log.debug("Querying knowledge upload records with pagination: userId={}, baseId={}, resourceType={}, fileIdList={}, status",
                    userId, baseId, resourceType, fileIdList, status);
            LambdaQueryWrapper<AlgorithmUserKnowledgeUploadPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AlgorithmUserKnowledgeUploadPO::getUserId, userId)
                    .eq(AlgorithmUserKnowledgeUploadPO::getBaseId, baseId)
                    .eq(AlgorithmUserKnowledgeUploadPO::getDelFlag, NOT_DELETED)
                    .eq(AlgorithmUserKnowledgeUploadPO::getResourceType, resourceType)
                    .eq(AlgorithmUserKnowledgeUploadPO::getUploadStatus, status)
                    .in(AlgorithmUserKnowledgeUploadPO::getFileId, fileIdList);
            // 执行查询
            List<AlgorithmUserKnowledgeUploadPO> result = baseMapper.selectList(wrapper);
            // 检查结果是否为空
            if (Objects.isNull(result) || CollectionUtils.isEmpty(result)) {
                log.info("No knowledge upload records found: userId={}, baseId={}", userId, baseId);
                return Collections.emptyList();
            }
            log.debug("Found {} knowledge upload records: userId={}, baseId={}, resourceType={}",
                    result.size(), userId, baseId, resourceType);
            // 转换为实体对象并返回
            return assembler.toPersonalKnowledgeImportTaskEntity(result);
        } catch (Exception e) {
            log.error("Error occurred while finding knowledge upload records: userId={}, baseId={}", userId, baseId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public Set<String> findRepeatImportPersonalCloudFile(String userId, String baseId, List<String> fileIdList) {
        LambdaQueryWrapper<AlgorithmUserKnowledgeUploadPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(AlgorithmUserKnowledgeUploadPO::getFileId)
                .eq(AlgorithmUserKnowledgeUploadPO::getUserId, userId)
                .eq(AlgorithmUserKnowledgeUploadPO::getBaseId, baseId)
                .eq(AlgorithmUserKnowledgeUploadPO::getDelFlag, NOT_DELETED)
                .eq(AlgorithmUserKnowledgeUploadPO::getResourceType, KnowledgeResourceTypeEnum.PERSONAL_FILE.getCode())
                .in(AlgorithmUserKnowledgeUploadPO::getUploadStatus, KnowledgeUploadStatusEnum.NOT_PROCESSED.getStatus(), KnowledgeUploadStatusEnum.PROCESSING.getStatus())
                .in(AlgorithmUserKnowledgeUploadPO::getFileId, fileIdList)
                // 查首层
                .isNull(AlgorithmUserKnowledgeUploadPO::getParentId);
        List<AlgorithmUserKnowledgeUploadPO> algorithmUserKnowledgeUploads = baseMapper.selectList(wrapper);
        return algorithmUserKnowledgeUploads
                .stream()
                .map(AlgorithmUserKnowledgeUploadPO::getFileId)
                .collect(Collectors.toSet());
    }
}

