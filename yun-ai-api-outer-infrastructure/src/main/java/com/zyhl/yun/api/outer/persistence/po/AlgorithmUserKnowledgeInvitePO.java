package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * className:AlgorithmUserKnowledgeInvitePO
 * description: 知识库邀请表PO
 *
 * <AUTHOR>
 * @date 2025/04/12
 */
@Data
@TableName("algorithm_user_knowledge_invite")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AlgorithmUserKnowledgeInvitePO implements Serializable {

    @TableId(value = "id")
    private Long id;

    /**
     * 加入人id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 邀请人id
     */
    @TableField(value = "invite_user_id")
    private String inviteUserId;

   /**
     * 分享的知识库id
     */
    @TableField(value = "knowledge_id")
    private Long knowledgeId;

    /**
     * 选择，1--已选择；0--未选择；
     */
    @TableField(value = "selected")
    private Integer selected;

   /**
     * 0-停用，1-启用（默认）
     */
    @TableField(value = "status")
    private Integer status;

   /**
     * 分享库使用的过期时间，空表示永久
     */
    @TableField(value = "expire_time")
    private Date expireTime;

   /**
     * 加入时间
     */
    @TableField(value = "create_time")
    private Date createTime;

   /**
     * 最后更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

}
