package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.persistence.po.AIToolsConsumeRecordPO} <br>
 * <b> description:</b>
 * 算法任务权益核销记录PO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-08-16 18:31
 **/
@Data
@TableName("T_AI_TOOLS_CONSUME_RECORD")
public class AIToolsConsumeRecordPO {

	/**
	 * 主键id
	 */
	@TableId
	private Long id;

	/**
	 * 用户id
	 */
	@TableField("USER_ID")
	private Long userId;

	/**
	 * 核销日期(20240306)
	 */
	@TableField("CONSUME_DATE")
	private String consumeDate;

	/**
	 * 渠道id
	 */
	@TableField("CHANNEL_ID")
	private String channelId;

	/**
	 * 模型
	 */
	private Integer module;

	/**
	 * 限制总次数
	 */
	@TableField("LIMIT_TOTAL")
	private Integer limitTotal;

	/**
	 * 使用总次数
	 */
	@TableField("USE_TIMES")
	private Integer useTimes;

	/**
	 * 创建时间
	 */
	@TableField("CREATE_TIME")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("UPDATE_TIME")
	private Date updateTime;
}
