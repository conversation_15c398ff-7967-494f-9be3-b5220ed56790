package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.knowledge.AlgorithmRagTextContentEntity;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmRagTextContentPO;
import org.mapstruct.Mapper;


/**
 * <AUTHOR>
 * @description AlgorithmRagTextContentAssembler
 * @date 2025/4/17 18:07
 */
@Mapper(componentModel = "spring")
public interface AlgorithmRagTextContentAssembler {

    /**
     * 转换实体
     *
     * @param po po
     * @return 实体
     */
    AlgorithmRagTextContentEntity toAlgorithmRagTextContentEntity(AlgorithmRagTextContentPO po);

}
