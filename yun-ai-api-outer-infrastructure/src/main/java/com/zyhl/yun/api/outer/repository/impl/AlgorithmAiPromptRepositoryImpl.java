package com.zyhl.yun.api.outer.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiPromptEntity;
import com.zyhl.yun.api.outer.persistence.mapper.AlgorithmAiPromptMapper;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmAiPromptPO;
import com.zyhl.yun.api.outer.repository.AlgorithmAiPromptRepository;
import com.zyhl.yun.api.outer.repository.assembler.EntityAssembler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.repository.impl.AlgorithmAiPromptRepositoryImpl} <br>
 * <b> description:</b>
 * AI提示词模板接口实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-06-28 10:31
 **/
@Slf4j
@Service
public class AlgorithmAiPromptRepositoryImpl extends ServiceImpl<AlgorithmAiPromptMapper, AlgorithmAiPromptPO>
        implements AlgorithmAiPromptRepository {

    @Resource
    private EntityAssembler entityAssembler;

    @Override
    public List<AlgorithmAiPromptEntity> queryByPromptKey(String promptKey) {
        LambdaQueryWrapper<AlgorithmAiPromptPO> queryWrapper = Wrappers.<AlgorithmAiPromptPO>query()
                .lambda()
                .eq(AlgorithmAiPromptPO::getPromptKey, promptKey);
        List<AlgorithmAiPromptPO> aiPromptPOList = this.baseMapper.selectList(queryWrapper);
        return entityAssembler.toAlgorithmAiPromptEntityList(aiPromptPOList);
    }
}
