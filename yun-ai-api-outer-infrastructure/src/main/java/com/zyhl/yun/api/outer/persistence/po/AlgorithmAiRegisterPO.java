package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 云邮AI助手1.1版本报名
 * <AUTHOR>
 */
@Data
@TableName("algorithm_ai_register")
public class AlgorithmAiRegisterPO {

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 用户所属底座
     */
    @TableField("belongs_platform")
    private Integer belongsPlatform;

    /**
     * 业务类型：0-AI工具，1-AI助手，2-智能相册 3-文档智能搜索
     *
     * @see com.zyhl.yun.api.outer.enums.BusinessSourceEnum
     */
    @TableField("business_type")
    private Integer businessType;

    /**
     * 模型枚举，AI工具有模块划分
     *
     * @see com.zyhl.yun.api.outer.enums.AIModuleEnum
     */
    @TableField("module")
    private Integer module;

    /**
     * 名称
     *
     * @see com.zyhl.yun.api.outer.enums.AIModuleEnum
     */
    @TableField("module_name")
    private String moduleName;

    /**
     * 目录id
     */
    @TableField("dir_id")
    private String dirId;

    /**
     * 云盘目录path
     */
    @TableField("path")
    private String path;

    /**
     * 厂商类型，通过厂商类型判断走那个搜索平台
     *
     * @see com.zyhl.hcy.yun.ai.common.base.enums.SupplierTypeEnum
     */
    @TableField("factory_type")
    private Integer factoryType;

    /**
     * 业务算法组编码，根据算法组执行那些算法：1 华为 - 图片元数据提取任务算法组 （目前只支持，2 彩讯 - 图片元数据提取任务算法组
     */
    @TableField("algorithm_group_code")
    private Integer algorithmGroupCode;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 状态：0-启用，1-停用
     */
    @TableField("status")
    private Integer status;

}
