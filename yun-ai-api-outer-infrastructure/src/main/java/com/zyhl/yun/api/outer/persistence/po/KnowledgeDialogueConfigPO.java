package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeDialogueConfigTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 知识库对话配置表-PO
 *
 * @Author: zhumaoxian
 */
@Data
@TableName("algorithm_knowledge_dialogue_config")
public class KnowledgeDialogueConfigPO {
    /**
     * id
     */
    @TableId
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 参数类型：1-召回（RecallConfig），2-重排（RerankConfig），3-对话（DialogueConfig）
     *
     * @see KnowledgeDialogueConfigTypeEnum
     */
    @TableField("type")
    private Integer type;

    /**
     * 参数类型对应的配置，全量json格式
     */
    @TableField("config_json")
    private String configJson;

    /**
     * 删除标识，0--正常；1--已删除
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 创建时间，默认值用current_timestamp
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间，默认值用current_timestamp
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

}
