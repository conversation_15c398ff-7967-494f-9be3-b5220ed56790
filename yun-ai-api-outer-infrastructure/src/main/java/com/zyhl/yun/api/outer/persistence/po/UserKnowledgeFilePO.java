package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zyhl.yun.api.outer.enums.knowledge.FileCategoryEnum;
import com.zyhl.yun.api.outer.enums.knowledge.FileProcessStatusEnum;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 个人知识库文件表
 *
 * <AUTHOR>
 */
@Data
@TableName("algorithm_user_knowledge_file")
@EqualsAndHashCode(callSuper = false)
public class UserKnowledgeFilePO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 文件id（独立空间文件id）
     */
    @TableField("file_id")
    private String fileId;

    /**
     * 知识库id
     */
    @TableField("base_id")
    private Long baseId;

    /**
     * 父目录id
     */
    @TableField("parent_file_id")
    private String parentFileId;



    /**
     * 文件名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 旧文件id（个人云文件id）
     */
    @TableField("old_file_id")
    private String oldFileId;

    /**
     * 文件归属，个人云 owner_id= user_id
     */
    @TableField("owner_id")
    private String ownerId;

    /**
     * 业务类型：
     * -1 - 未知类型
     * 1-personal 个人云
     * 2-group 圈子
     * 3-shareGroup 共享群
     * 4-family 家庭云
     * 5-activity 活动空间 照片直播
     * 6-note 笔记
     * 7-cardpackage 卡包
     * 8-system 系统空间
     * 9-partner 合作空间
     * 10 -mount 挂载盘
     *
     * @see com.zyhl.hcy.yun.ai.common.base.enums.OwnerTypeEnum
     */
    @TableField("owner_type")
    private Integer ownerType;

    /**
     * paas平台编码
     */
    @TableField("paas_code")
    private String paasCode;

    /**
     * 文件哈希名
     */
    @TableField("hash_name")
    private String hashName;

    /**
     * 文件哈希值
     */
    @TableField("hash_value")
    private String hashValue;

    /**
     * 文件类型:1-文件，2-目录
     */
    @TableField("file_type")
    private Integer fileType;

    /**
     * 内容类型
     */
    @TableField("content_type")
    private Integer contentType;

    /**
     * 文件/目录分类
     * 文件/目录分类,见字典定义
     * 1 图片
     * 2 音频
     * 3 视频
     * 4 文档
     * 5 应用
     * 6 压缩文件
     * 0 其他
     * 100 普通目录
     *
     * @see FileCategoryEnum
     */
    @TableField("category")
    private Integer category;

    /**
     * 文件大小
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 文件后缀
     */
    @TableField("extension")
    private String extension;

    /**
     * 文件修改时间
     */
    @TableField("file_updated_at")
    private Date fileUpdatedAt;

    /**
     * 文件创建时间
     */
    @TableField("file_created_at")
    private Date fileCreatedAt;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 移入回收站时间
     */
    @TableField("trashed_time")
    private Date trashedTime;

    /**
     * 删除标识，0--正常；1--已删除；2--删除中；3--保险箱
     *
     * @see com.zyhl.yun.api.outer.enums.knowledge.KnowledgeStatusEnum
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 算法结果状态
     * 默认 0 未处理
     * 1 成功
     * 2 失败
     *
     * @see FileProcessStatusEnum
     */
    @TableField("ai_status")
    private Integer aiStatus;

    /**
     * 算法结果码
     * 0000 成功
     * 其他则错误码
     */
    @TableField("result_code")
    private String resultCode;

    /**
     * 算法结果
     */
    @TableField("result_msg")
    private String resultMsg;

    /**
     * 审核状态
     * 默认 0 未送审
     * 状态码：2通过，3未通过
     */
    @TableField("audit_status")
    private Integer auditStatus;

    /**
     * 审核时间
     */
    @TableField("audit_time")
    private Date auditTime;

    /**
     * 审核结果，json格式
     */
    @TableField("audit_result")
    private String auditResult;

    /**
     * 来源文件资源类型
     *
     * @see com.zyhl.yun.api.outer.enums.knowledge.KnowledgeResourceTypeEnum
     */
    @TableField("from_resource_type")
    private Integer fromResourceType;

    /**
     * 排序优先级
     */
    @TableField("sort_type")
    private Integer sortType;

    /**
     * 来源资源信息
     * from_resource_type=3 则存储的是网址
     */
    @TableField("from_resource")
    private String fromResource;

    /**
     * 附件信息
     * json格式
     * 邮件附件
     * 笔记附件
     */
    @TableField("attachment")
    private String attachment;

    /**
     * 算法完成步骤
     * 保存文档解析各个节点最终状态（parseType不能重复）
     * [{
     * "parseType":"文档解析类型", // SPLIT
     * "status": 1 成功 2失败
     * "code": 算法结果码
     * }]
     * 文档解析类型:
     * SPLIT 文档切片（默认）
     * QA 问答对解析
     * SPLIT_GQA 切片假设性问题
     * GQA 假设性问题
     * GSPLIT 语义切块
     * SUMMARY 文档总结
     * SUMMARY.SPLIT 分块文档总结
     * SUMMARY.GSPLIT 语义分块文档总结
     */
    @TableField("result_step_logs")
    private String resultStepLogs;
    /**
     * 父目录路径
     */
    @TableField("parent_file_path")
    private String parentFilePath;
    /**
     * 当 from_resource_type = 5
     * 修改版本号
     */
    @TableField("revision")
    private String revision;
    /**
     * 当 from_resource_type = 4
     * -1 （默认） 0 同步中，1 同步成功，2 同步失败
     */
    @TableField("sync_status")
    private Integer syncStatus;
    /**
     * 当 from_resource_type = 4
     *
     * 同步信息错误描述
     */
    @TableField("sync_error_msg")
    private String syncErrorMsg;
    /**
     * 同步错误码 当 from_resource_type = 4
     */
    @TableField("sync_error_code")
    private String syncErrorCode;

    /**
     * 完成时间
     */
    @TableField("finish_time")
    private Date finishTime;
}
