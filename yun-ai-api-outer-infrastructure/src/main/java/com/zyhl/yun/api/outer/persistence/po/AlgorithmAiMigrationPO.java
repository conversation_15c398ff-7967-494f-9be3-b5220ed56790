package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * AI迁移表：小天助手1.0.1
 * <AUTHOR>
 */
@Data
@TableName("algorithm_ai_migration")
public class AlgorithmAiMigrationPO {

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 用户所属底座
     */
    @TableField("belongs_platform")
    private Integer belongsPlatform;

    /**
     * 设备
     */
    @TableField("device")
    private String device;

    /**
     * 状态：1已报名（默认)，2已迁移，3已计算完成（图片向量化）
     *
     * @see com.zyhl.yun.api.outer.enums.MigrationStatusEnum
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 来源渠道
     */
    @TableField("source_channel")
    private String sourceChannel;


    /**
     * 迁移类型 1--图片搜索页面(默认) 2--文档搜索页面
     *
     * @see com.zyhl.yun.api.outer.enums.MigrationTypeEnum
     */
    @TableField("type")
    private Integer type;
}
