package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiPersonResourceEntity;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmAiPersonResourcePO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * className:AlgorithmAiPersonResourceAssembler
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface AlgorithmAiPersonResourceAssembler {

	/**
	 * po转entity
	 *
	 * @param pos po
	 * @return entity
	 */
	List<AlgorithmAiPersonResourceEntity> toEntityList(List<AlgorithmAiPersonResourcePO> pos);

	/**
	 * po转entity
	 *
	 * @param po po
	 * @return entity
	 */
	AlgorithmAiPersonResourceEntity toEntity(AlgorithmAiPersonResourcePO po);

	/**
	 * entity转po
	 *
	 * @param entity entity
	 * @return po
	 */
	AlgorithmAiPersonResourcePO toPo(AlgorithmAiPersonResourceEntity entity);

}
