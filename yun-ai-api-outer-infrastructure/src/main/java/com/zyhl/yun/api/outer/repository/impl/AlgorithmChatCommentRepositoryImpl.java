package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.hcy.plugin.uidgenerator.UidGenerator;
import com.zyhl.yun.api.outer.domain.entity.ChatCommentEntity;
import com.zyhl.yun.api.outer.domain.vo.ChatCommentGetResult;
import com.zyhl.yun.api.outer.enums.LikeEnum;
import com.zyhl.yun.api.outer.persistence.mapper.AlgorithmChatCommentMapper;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmChatCommentPO;
import com.zyhl.yun.api.outer.repository.AlgorithmChatCommentRepository;
import com.zyhl.yun.api.outer.repository.assembler.AbstractChatCommentAssembler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zyhl.yun.api.outer.constants.Const.NUM_16;

/**
 * <AUTHOR>
 * @version 2024年02月28日 16:23
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class AlgorithmChatCommentRepositoryImpl extends ServiceImpl<AlgorithmChatCommentMapper, AlgorithmChatCommentPO> implements AlgorithmChatCommentRepository {


    private final AbstractChatCommentAssembler chatCommentAssembler;

    private final UidGenerator uidGenerator;

    @Override
    public boolean save(ChatCommentEntity entity) {
        // 先查询
        final QueryWrapper<AlgorithmChatCommentPO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AlgorithmChatCommentPO::getUserId, entity.getUserId());
        wrapper.lambda().eq(AlgorithmChatCommentPO::getDialogueId, entity.getDialogueId());
        final List<AlgorithmChatCommentPO> poList = baseMapper.selectList(wrapper);
        AlgorithmChatCommentPO item = ObjectUtil.isEmpty(poList) ? null : poList.get(0);


        if (LikeEnum.isCancel(entity.getLikeComment())) {
            log.info("【评论对话】取消评论，对话id：{}", entity.getDialogueId());
            if (item == null) {
                log.info("【评论对话】评论不存在，不能取消");
                return true;
            }
            return baseMapper.deleteById(item.getDialogueId()) == 1;
        } else {
            if (item != null) {
                log.info("【评论对话】评论已存在，不能重复评论");
                return true;
            }

            // 对象转换
            AlgorithmChatCommentPO po = chatCommentAssembler.toAlgorithmChatComment(entity);
            po.setId(uidGenerator.getUID());
            log.info("【评论对话】添加评论，对话id：{}", entity.getDialogueId());
            return baseMapper.insert(po) == 1;
        }
    }

    @Override
    public List<ChatCommentGetResult> get(ChatCommentEntity entity) {
        QueryWrapper<AlgorithmChatCommentPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AlgorithmChatCommentPO::getUserId, entity.getUserId())
                .eq(AlgorithmChatCommentPO::getSessionId, entity.getSessionId())
                .eq(null != entity.getDialogueId(), AlgorithmChatCommentPO::getDialogueId, entity.getDialogueId());
        List<AlgorithmChatCommentPO> result = baseMapper.selectList(queryWrapper);
        log.info("【评论结果】查询评论结果，用户id：{}，会话id：{}，对话id：{}", entity.getUserId(), entity.getSessionId(), entity.getDialogueId());
        if (!CollectionUtils.isEmpty(result)) {
            return chatCommentAssembler.toChatCommentGetList(result);
        }
        return null;
    }

    @Override
    public Map<Long, ChatCommentGetResult> queryMapByDialogueIdList(String userId, List<Long> dialogueIdList) {
        if (CollUtil.isNotEmpty(dialogueIdList)) {
            QueryWrapper<AlgorithmChatCommentPO> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
            .eq(AlgorithmChatCommentPO::getUserId, userId)
            .in(AlgorithmChatCommentPO::getDialogueId, dialogueIdList);
            List<AlgorithmChatCommentPO> chatCommentPoList = baseMapper.selectList(queryWrapper);
            if (CollUtil.isNotEmpty(chatCommentPoList)) {
                // list转map
                Map<Long, ChatCommentGetResult> chatCommentGetResultMap = chatCommentPoList.stream().collect(Collectors.toMap(
                        AlgorithmChatCommentPO::getDialogueId, chatCommentAssembler::toChatCommentGet
                ));
                log.info("AlgorithmChatCommentRepositoryImpl-queryMapByDialogueIdList，结果：{}", JSON.toJSON(chatCommentGetResultMap));
                return chatCommentGetResultMap;
            }
        }
        return new HashMap<>(NUM_16);
    }

    @Override
    public ChatCommentEntity getByDialogueId(String userId, Long dialogueId) {
        QueryWrapper<AlgorithmChatCommentPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
        .eq(AlgorithmChatCommentPO::getUserId, userId)
        .eq(AlgorithmChatCommentPO::getDialogueId, dialogueId);
        List<AlgorithmChatCommentPO> result = baseMapper.selectList(queryWrapper);
        if (ObjectUtil.isEmpty(result)) {
            return null;
        }

        return chatCommentAssembler.toChatCommentEntity(result.get(0));
    }


}
