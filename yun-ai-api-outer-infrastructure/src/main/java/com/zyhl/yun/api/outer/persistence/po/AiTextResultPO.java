package com.zyhl.yun.api.outer.persistence.po;


import com.zyhl.hcy.yun.ai.common.base.hbase.infrastructure.persistence.aspect.HbaseColumn;
import com.zyhl.hcy.yun.ai.common.base.hbase.infrastructure.persistence.aspect.HbaseTable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 文本识别结果
 * <AUTHOR>
 */
@Builder
@Data
@HbaseTable(tableName = "ai_text_result")
@AllArgsConstructor
@NoArgsConstructor
public class AiTextResultPO  {
    @HbaseColumn(family = "info")
    private String rowKey;

    @HbaseColumn(family = "info")
    private String userId;

    @HbaseColumn(family = "info")
    private String taskId;

    @HbaseColumn(family = "details")
    private String reqParameters;

    @HbaseColumn(family = "details")
    private String attachment;

    @HbaseColumn(family = "details")
    private String history;

    @HbaseColumn(family = "details")
    private String respParameters;
}
