package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 对话应用类型信息表
 * <AUTHOR>
 */
@Data
@TableName("algorithm_chat_application_agent")
public class AlgorithmChatApplicationAgentPO {

    @TableId("id")
    private Long id;

    /**
     * 应用关联id,supplier_type=阿里，指通义星尘的角色id；supplier_type=自研，指模型名称（例如deepseek r1 7b 或 deepseek r1 671b）
     */
    @TableField("type_relation_id")
    private String typeRelationId;

    /**
     * 厂商编码，用于路由不同厂商的智能体 0 自研 2 阿里
     */
    @TableField("supplier_type")
    private String supplierType;

    @TableField("type")
    private String type;

    @TableField("title")
    private String title;

    @TableField("avatar_url")
    private String avatarUrl;

    @TableField("opening_line")
    private String openingLine;

    @TableField("guid_text")
    private String guidText;

    @TableField("tab_label")
    private String tabLabel;

    @TableField("tab_label_en")
    private String tabLabelEn;

    @TableField("sort")
    private Integer sort;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "update_time" , fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;


}
