package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeFileResEntity;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmUserKnowledgeFileResPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 类转换 UserKnowledgeFileResEntity <--> AlgorithmUserKnowledgeFileResPO
 *
 * <AUTHOR>
 * @date 2025/02/13
 */
@Mapper(componentModel = "spring")
public interface UserKnowledgeFileResAssembler {

    UserKnowledgeFileResAssembler INSTANCE = Mappers.getMapper(UserKnowledgeFileResAssembler.class);

    /**
     * po转entity列表
     * @param poList po
     * @return entity列表
     */
    List<UserKnowledgeFileResEntity> toUserKnowledgeFileResEntityList(List<AlgorithmUserKnowledgeFileResPO> poList);

    /**
     * entity转po
     *
     * @param entity 参数
     * @return po
     */
    AlgorithmUserKnowledgeFileResPO toAlgorithmUserKnowledgeFileResPO(UserKnowledgeFileResEntity entity);

    /**
     * po转entity
     *
     * @param fileResPo 参数
     * @return po
     */
    UserKnowledgeFileResEntity toAlgorithmUserKnowledgeFileResEntity(AlgorithmUserKnowledgeFileResPO fileResPo);
}
