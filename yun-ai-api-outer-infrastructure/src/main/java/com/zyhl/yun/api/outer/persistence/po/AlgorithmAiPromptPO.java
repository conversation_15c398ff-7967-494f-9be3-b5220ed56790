package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <b>className:</b>
 * {@link com.zyhl.yun.api.outer.persistence.po.AlgorithmAiPromptPO} <br>
 * <b> description:</b>
 * AI提示词模板PO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-06-28 11:20
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("algorithm_ai_prompt_template")
public class AlgorithmAiPromptPO {

    /**
     * id
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 提示词编码
     */
    @TableField("PROMPT_KEY")
    private String promptKey;

    /**
     * 提示词名称
     */
    @TableField("PROMPT_NAME")
    private String promptName;

    /**
     * 提示词模板
     */
    @TableField("PROMPT_TEMPLATE")
    private String promptTemplate;

    /**
     * 记录历史版本  默认1，更新累加+
     */
    @TableField("VERSION")
    private Integer version;

    /**
     * 用户流量调度做A/Btest, 默认1,分子是当前权重/分母是相同key的权重之和
     */
    @TableField("WEIGHT")
    private Integer weight;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("UPDATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 大业务类型
     */
    @TableField("BUSINESS_TYPE")
    private String businessType;
}
