package com.zyhl.yun.api.outer.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zyhl.yun.api.outer.persistence.po.TaskAiAbilityPO;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI输入算法任务表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-03-06
 */
public interface TaskAiAbilityMapper extends BaseMapper<TaskAiAbilityPO> {

    /**
     * 根据任务ID查询任务信息
     *
     * @param taskId the task id
     * @return {@link List<TaskAiAbilityPO>}
     * <AUTHOR>
     * @date 2025-07-02 17:36
     */
    List<TaskAiAbilityPO> selectWithUnionAll(@Param("taskId") Long taskId);

}
