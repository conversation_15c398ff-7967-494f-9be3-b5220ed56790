package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.PersonalKnowledgeImportTaskEntity;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmUserKnowledgeUploadPO;
import com.zyhl.yun.api.outer.repository.converter.UploadEntityConverter;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-16 16:13:18
 */
@Mapper(componentModel = "spring", uses = {UploadEntityConverter.class})
public interface AlgorithmUserKnowledgeUploadAssembler {
    
    /**
     * 将PO列表转换为实体列表
     * @param uploads PO列表
     * @return 实体列表
     */
    List<PersonalKnowledgeImportTaskEntity> toPersonalKnowledgeImportTaskEntity(List<AlgorithmUserKnowledgeUploadPO> uploads);

}
