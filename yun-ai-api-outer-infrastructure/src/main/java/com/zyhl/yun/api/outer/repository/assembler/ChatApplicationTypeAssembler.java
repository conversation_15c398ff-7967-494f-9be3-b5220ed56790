package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.vo.ChatApplicationType;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmChatApplicationAgentPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 对话应用类型信息类转换器
 *
 * <AUTHOR>
 * @version 2024/4/16 15:30
 */
@Mapper(componentModel = "spring")
public interface ChatApplicationTypeAssembler {


    /**
     * po转entity
     * @param algorithmChatApplicationAgentPo po
     * @return entity
     */
    @Mapping(target = "applicationType", source = "type")
    @Mapping(target = "applicationName", source = "title")
    @Mapping(target = "applicationId", source = "id")
    ChatApplicationType toChatApplicationType(AlgorithmChatApplicationAgentPO algorithmChatApplicationAgentPo);

    /**
     * po转entity
     * @param algorithmChatApplicationAgentPoList po
     * @return entityList
     */
    List<ChatApplicationType> toChatApplicationTypeList(List<AlgorithmChatApplicationAgentPO> algorithmChatApplicationAgentPoList);
}
