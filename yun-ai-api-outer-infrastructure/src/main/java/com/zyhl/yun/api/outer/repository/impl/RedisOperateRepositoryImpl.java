package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.config.ChatProperties;
import com.zyhl.yun.api.outer.constants.RedisConstants;
import com.zyhl.yun.api.outer.datahelper.util.RedisService;
import com.zyhl.yun.api.outer.domain.dto.UserInfoDTO;
import com.zyhl.yun.api.outer.domain.dto.redis.HistoryDialogInfoDTO;
import com.zyhl.yun.api.outer.domain.dto.redis.MemberBenefitDTO;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmAiRegisterEntity;
import com.zyhl.yun.api.outer.domain.vo.ChatApplicationType;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.repository.RedisOperateRepository;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * redis数据库操作
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class RedisOperateRepositoryImpl implements RedisOperateRepository {

    private static final String SUCCESS_FLAG = "1";
    @Resource
    private RedisService redisService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ChatProperties chatProperties;

    @Override
    public void setConsumeSeq(String userId, Long dialogueId, String benefitNo, String consumeSeq) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.MEMBER_BENEFIT, userId, dialogueId);
            MemberBenefitDTO dto = new MemberBenefitDTO(benefitNo, consumeSeq);
            redisService.setCacheObject(redisKey, JsonUtil.toJson(dto), 6L, TimeUnit.HOURS);
        } catch (Exception e) {
            log.error("setConsumeSeq redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
    }

    @Override
    public MemberBenefitDTO getConsumeSeq(String userId, Long dialogueId) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.MEMBER_BENEFIT, userId, dialogueId);
            String jsonStr = redisService.getCacheObject(redisKey);
            if (!StrUtil.isEmpty(jsonStr)) {
                return JsonUtil.parseObject(jsonStr, MemberBenefitDTO.class);
            }
        } catch (Exception e) {
            log.error("getConsumeSeq redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
        return null;
    }

    @Override
    public void delConsumeSeq(String userId, Long dialogueId) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.MEMBER_BENEFIT, userId, dialogueId);
            redisService.deleteObject(redisKey);
        } catch (Exception e) {
            log.error("delConsumeSeq redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
    }

    @Override
    public void setMessageSessionId(String userId, String applicationId, String businessType, String sessionId) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.MESSAGE_SESSION_ID, userId, applicationId, businessType);
            redisService.setCacheObject(redisKey, sessionId, 30L, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("setMessageSessionId redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
    }

    @Override
    public String getMessageSessionId(String userId, String applicationId, String businessType) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.MESSAGE_SESSION_ID, userId, applicationId, businessType);
            return redisService.getCacheObject(redisKey);
        } catch (Exception e) {
            log.error("getMessageSessionId redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
        return "";
    }

    @Override
    public void delMessageSessionId(String userId, String applicationId, String businessType) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.MESSAGE_SESSION_ID, userId, applicationId, businessType);
            redisService.deleteObject(redisKey);
        } catch (Exception e) {
            log.error("delMessageSessionId redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
    }

    @Override
    public void setRegisterEntity(String userId, Integer businessType, Integer module, AlgorithmAiRegisterEntity entity) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.AI_REGISTER, userId, businessType, module == null ? 0 : module);
            redisService.setCacheObject(redisKey, JsonUtil.toJson(entity), 30L, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("setRegisterEntity redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
    }

    @Override
    public AlgorithmAiRegisterEntity getRegisterEntity(String userId, Integer businessType, Integer module) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.AI_REGISTER, userId, businessType, module == null ? 0 : module);
            final String cacheValue = redisService.getCacheObject(redisKey);
            if (!StrUtil.isEmpty(cacheValue)) {
                final AlgorithmAiRegisterEntity entity = JsonUtil.parseObject(cacheValue, AlgorithmAiRegisterEntity.class);
                if (entity != null) {
                    return entity;
                }
            }
        } catch (Exception e) {
            log.error("getRegisterEntity redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
        return null;
    }

    @Override
    public void delRegisterEntity(String userId, Integer businessType, Integer module) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.AI_REGISTER, userId, businessType, module == null ? 0 : module);
            redisService.deleteObject(redisKey);
        } catch (Exception e) {
            log.error("delRegisterEntity redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
    }

    @Override
    public String getTypeRelationId(String applicationId) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.TYPE_RELATION_ID, applicationId);
            return redisService.getCacheObject(redisKey);
        } catch (Exception e) {
            log.error("getTypeRelationId redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
        return "";
    }

    @Override
    public void setTypeRelationId(String applicationId, String typeRelationId) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.TYPE_RELATION_ID, applicationId);
            redisService.setCacheObject(redisKey, typeRelationId, 6L, TimeUnit.HOURS);
        } catch (Exception e) {
            log.error("setTypeRelationId redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
    }

    @Override
    public ChatApplicationType getBeanApplicationId(String applicationId) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.AGENT_CHAT_APPLICATION_ID, applicationId);
            Object json = redisService.getCacheObject(redisKey);
            if (json instanceof String) {
                return JSONUtil.toBean(String.valueOf(json), ChatApplicationType.class);
            }
        } catch (Exception e) {
            log.error("getBeanApplicationId redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
        return null;
    }

    @Override
    public void setBeanApplicationId(String applicationId, ChatApplicationType chatApplicationType) {
        String redisKey = null;
        try {
            if (null != chatApplicationType) {
                redisKey = String.format(RedisConstants.AGENT_CHAT_APPLICATION_ID, applicationId);
                redisService.setCacheObject(redisKey, JSONUtil.toJsonStr(chatApplicationType), 6L, TimeUnit.HOURS);
            }
        } catch (Exception e) {
            log.error("setBeanApplicationId redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
    }

    @Override
    public void setStopDialogue(Long dialogueId) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.STOP_DIALOGUE, dialogueId);
            redisService.setCacheObject(redisKey, SUCCESS_FLAG, 30L, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("setStopDialogue redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
    }

    @Override
    public boolean getStopDialogue(Long dialogueId) {
        String redisKey = null;
        String value = null;
        try {
            redisKey = String.format(RedisConstants.STOP_DIALOGUE, dialogueId);
            value = redisService.getCacheObject(redisKey);
            if (SUCCESS_FLAG.equals(value)) {
                return true;
            }
        } catch (Exception e) {
            log.error("getStopDialogue redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
        return false;
    }

    @Override
    public void setModelDisabled(String modeCode) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.MODEL_DISABLED, modeCode);
            redisService.setCacheObject(redisKey, SUCCESS_FLAG, 30L, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("setModelDisabled redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
    }

    @Override
    public boolean getModelDisabled(String modeCode) {
        String redisKey = null;
        String value = null;
        try {
            redisKey = String.format(RedisConstants.MODEL_DISABLED, modeCode);
            value = redisService.getCacheObject(redisKey);
            return SUCCESS_FLAG.equals(value);
        } catch (Exception e) {
            log.error("getModelDisabled redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
        return false;
    }

    @Override
    public void setUserKnowledgeExistFile(String userId, boolean existFile) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.USER_KNOWLEDGE_EXIST_FILE_KEY, userId);
            redisService.setCacheObject(redisKey, existFile, 5L, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("setUserKnowledgeUse redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
    }

    @Override
    public Boolean getUserKnowledgeExistFile(String userId) {
        String redisKey = null;
        Boolean value = null;
        try {
            redisKey = String.format(RedisConstants.USER_KNOWLEDGE_EXIST_FILE_KEY, userId);
            value = redisService.getCacheObject(redisKey);
            return value;
        } catch (Exception e) {
            log.error("getUserKnowledgeUse redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
        return null;
    }

    @Override
    public void setCommonKnowledgeExistFile(String baseId, boolean existFile) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.COMMON_KNOWLEDGE_EXIST_FILE_KEY, baseId);
            redisService.setCacheObject(redisKey, existFile, 5L, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("setCommonKnowledgeUse redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
    }

    @Override
    public Boolean getCommonKnowledgeExistFile(String baseId) {
        String redisKey = null;
        Boolean value = null;
        try {
            redisKey = String.format(RedisConstants.COMMON_KNOWLEDGE_EXIST_FILE_KEY, baseId);
            value = redisService.getCacheObject(redisKey);
            return value;
        } catch (Exception e) {
            log.error("getCommonKnowledgeUse redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
        return null;
    }

    @Override
    public void setFeatureUrl(String url) {
        String redisKey = null;
        try {
            redisKey = RedisConstants.FEATURE_URL;
            redisService.setCacheObject(redisKey, url, 5L, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("setFeatureUrl redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
    }

    @Override
    public String getFeatureUrl() {
        String redisKey = null;
        try {
            redisKey = RedisConstants.FEATURE_URL;
            return redisService.getCacheObject(redisKey);
        } catch (Exception e) {
            log.error("getFeatureUrl redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
        return "";
    }

    @Override
    public void setUserDriveId(String userId, String driveId) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.USER_DRIVEID, userId);
            redisService.setCacheObject(redisKey, driveId, 30L, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("setUserDriveId redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
    }

    @Override
    public String getUserDriveId(String userId) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.USER_DRIVEID, userId);
            return redisService.getCacheObject(redisKey);
        } catch (Exception e) {
            log.error("getUserDriveId redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
        return "";
    }

    /**
     * 存储对话信息到历史会话
     *
     * @param sessionId  会话ID
     * @param dialogInfo 对话信息对象
     * @param maxLength  List的最大长度
     * @param expireTime 过期时间（单位：秒）
     */
    @Override
    public void setHistoryDialogInfo(String sessionId, HistoryDialogInfoDTO dialogInfo, Integer maxLength, Long expireTime) {
        if (sessionId == null) {
            return;
        }

        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.AI_HISTORY_SESSION, sessionId);
            redisService.cacheListWithMaxLengthAndExpire(redisKey, Collections.singletonList(dialogInfo), maxLength, expireTime);
        } catch (Exception e) {
            log.error("setSessionDialogInfo redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
    }

    /**
     * 获取历史会话中的对话信息列表
     *
     * @param sessionId 会话ID
     * @return 包含 DialogInfo 对象的集合
     */
    @Override
    public List<HistoryDialogInfoDTO> getHistoryDialogInfoList(String sessionId) {
        if (sessionId == null) {
            return new ArrayList<>();
        }

        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.AI_HISTORY_SESSION, sessionId);
            return redisService.getCacheList(redisKey);
        } catch (Exception e) {
            log.error("getHistoryDialogInfoList redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    @Override
    public boolean saveXYunTid(String url, String tid) {
        if (ObjectUtil.isEmpty(url) || ObjectUtil.isEmpty(tid)) {
            return true;
        }
        // 加锁，防止频繁新增
        RLock lock = null;
        try {
            lock = redissonClient.getLock(String.format(RedisConstants.URL_REQUEST_REPLAY_LOCK, url, tid));
            if (!lock.tryLock(RedisConstants.WAIT_TIME_1, RedisConstants.LEASE_TIME_5, TimeUnit.SECONDS)) {
                log.info("获取锁失败");
                return false;
            }

            // 先判断是否已经存在
            String redisKey = String.format(RedisConstants.URL_REQUEST_REPLAY, url, tid);
            if (redisService.hasKey(redisKey)) {
                log.info("已存在,redisKey:{}", redisKey);
                return false;
            }

            // 不存在则保存
            redisService.setCacheObject(redisKey, tid, RedisConstants.TIMEOUT_60, TimeUnit.SECONDS);

            return true;
        } catch (Exception e) {
            log.error("【知识库文件新增】新增文件异常：{}", e.getMessage(), e);
        } finally {
            if (Objects.nonNull(lock) && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        // 默认通过
        return true;
    }

    @Override
    public void setUserInfo(UserInfoDTO userInfoDTO) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.USER_INFO, userInfoDTO.getUserId());
            redisService.setCacheObject(redisKey, JsonUtil.toJson(userInfoDTO), RedisConstants.TIMEOUT_10, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("setUserInfo redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
    }

    @Override
    public UserInfoDTO getUserInfo(String userId) {
        UserInfoDTO userInfoDTO = null;
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.USER_INFO, userId);
            String userInfo = redisService.getCacheObject(redisKey);
            if (ObjectUtil.isNotEmpty(userInfo)) {
                userInfoDTO = JsonUtil.parseObject(userInfo, UserInfoDTO.class);
            }
        } catch (Exception e) {
            log.error("setUserInfo redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
        return userInfoDTO;
    }

    @Override
    public void setFlowResult(Long dialogueId, Integer index, String flowTypeResultJson) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.CONTINUE_TRANS_KEY, dialogueId, index);
            redisService.setCacheObject(redisKey, flowTypeResultJson, RedisConstants.TIMEOUT_30, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("setFlowTypeResult redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
    }

    @Override
    public String getFlowResult(Long dialogueId, Integer index) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.CONTINUE_TRANS_KEY, dialogueId, index);
            String jsonVal = "";
            int times = 0;
            do {
                jsonVal = redisService.getCacheObject(redisKey);
                if (ObjectUtil.isEmpty(jsonVal)) {
                    try {
                        Thread.sleep(chatProperties.getContinueTrans().getSleepTime());
                    } catch (Exception e) {
                        log.error("getFlowTypeResult redisKey:{}, error:{}", redisKey, e.getMessage(), e);
                    }
                    times++;
                } else {
                    return jsonVal;
                }
            } while (times <= chatProperties.getContinueTrans().getMaxRetryCount());

            // 轮询次数达到最大值，则抛超时异常
            if (times >= chatProperties.getContinueTrans().getMaxRetryCount()) {
                throw new YunAiBusinessException(ResultCodeEnum.TIMEOUT_ERROR);
            }
        } catch (YunAiBusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("getFlowTypeResult redisKey:{}, error:{}", redisKey, e.getMessage(), e);
            throw new YunAiBusinessException(ResultCodeEnum.MIDDLEWARE_RESPONDS_ABNORMALLY);
        }
        return "";
    }

    @Override
    public void setDialogueConfig(String userId, String dialogueConfig) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.USER_DIALOGUE_CONFIG, userId);
            redisService.setCacheObject(redisKey, dialogueConfig, RedisConstants.TIMEOUT_10, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("setDialogueConfig redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
    }

    @Override
    public String getDialogueConfig(String userId) {
        String redisKey = null;
        try {
            redisKey = String.format(RedisConstants.USER_DIALOGUE_CONFIG, userId);
            return redisService.getCacheObject(redisKey);
        } catch (Exception e) {
            log.error("getDialogueConfig redisKey:{}, error:{}", redisKey, e.getMessage(), e);
        }
        return "";
    }

}
