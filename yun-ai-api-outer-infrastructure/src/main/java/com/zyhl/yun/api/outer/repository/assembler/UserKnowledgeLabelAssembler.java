package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeLabelEntity;
import com.zyhl.yun.api.outer.persistence.po.UserKnowledgeLabelPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 类转换 UserKnowledgeLabelEntity <--> UserKnowledgeLabelPO
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface UserKnowledgeLabelAssembler {

    UserKnowledgeLabelAssembler INSTANCE = Mappers.getMapper(UserKnowledgeLabelAssembler.class);

    /**
     * po转entity
     * @param po po
     * @return entity
     */
    UserKnowledgeLabelEntity toUserKnowledgeLabelEntity(UserKnowledgeLabelPO po);

    /**
     * po转entity
     * @param poList poList
     * @return entity列表
     */
    List<UserKnowledgeLabelEntity> toUserKnowledgeLabelEntityList(List<UserKnowledgeLabelPO> poList);

    /**
     * entity转po
     * @param entity po
     * @return po
     */
    UserKnowledgeLabelPO toUserKnowledgeLabelPo(UserKnowledgeLabelEntity entity);

}
