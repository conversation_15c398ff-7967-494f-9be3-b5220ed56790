package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.persistence.po.AiTextResultPO;
import org.mapstruct.Mapper;

import java.util.List;

import com.zyhl.yun.api.outer.domain.entity.AiTextResultEntity;
import com.zyhl.yun.api.outer.persistence.po.AiTextResultPO;


/**
 * AiTextResultAssembler
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface AiTextResultAssembler {

    /**
     * 转换实体
     *
     * @param po po
     * @return 实体
     */
    AiTextResultEntity toAiTextResultEntity(AiTextResultPO po);

    /**
     * 转换po
     *
     * @param entity 实体
     * @return po
     */
    AiTextResultPO toAiTextResult(AiTextResultEntity entity);

    /**
     * 转换实体列表
     *
     * @param poList po列表
     * @return 实体列表
     */
    List<AiTextResultEntity> toAiTextResultEntityList(List<AiTextResultPO> poList);
}
