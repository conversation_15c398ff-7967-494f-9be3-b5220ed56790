package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 算法配置表-PO
 * @Author: WeiJingKun
 */
@Data
@TableName("algorithm_config")
public class AlgorithmConfigPO {
    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 任务类型
     * 1.图片元数据分析
     * 2.人脸聚类
     * 3.相似度聚类
     * 4.文档向量类
     * 5.视频提取文本
     */
    @TableField("task_type")
    private Integer taskType;

    /**
     * 节点标识;建议采用areaCode,中心节点-0，
     */
    @TableField("timeline_range_flag")
    private Integer timelineRangeFlag;

    /**
     * 执行类型;默认0
     * 0.默认元数据提取算法顺序执行（多个算法按照execution_order顺序执行）
     * 1.执行一次（例如图片向量化算法）
     */
    @TableField("execution_type")
    private Integer executionType;

    /**
     * 执行顺序
     */
    @TableField("execution_order")
    private Integer executionOrder;

    /**
     * 算法URL
     */
    @TableField("algorithm_url")
    private String algorithmUrl;

    /**
     * 算法ID
     */
    @TableField("algorithm_id")
    private Long algorithmId;

    /**
     * 创建时间，默认值用current_timestamp
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间，默认值用current_timestamp
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 删除标识，0--正常；1--已删除
     */
    @TableField("del_flag")
    private String delFlag;

}
