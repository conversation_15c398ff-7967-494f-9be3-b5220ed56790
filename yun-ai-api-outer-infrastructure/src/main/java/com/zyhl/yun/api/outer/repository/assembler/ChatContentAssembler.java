package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.vo.chat.ContentResultVO;
import com.zyhl.yun.api.outer.domain.vo.chat.ContentResultVOV2;
import com.zyhl.yun.api.outer.domain.vo.chat.ContentVO;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmChatContentPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * <AUTHOR>
 * @Date 2024/03/09 14:00
 */
@Mapper(componentModel = "spring")
public interface ChatContentAssembler {

    ChatContentAssembler INSTANCE = Mappers.getMapper(ChatContentAssembler.class);

    /**
     * po转entity
     * @param po po
     * @return entity
     */
    AlgorithmChatContentEntity toChatContentEntity(AlgorithmChatContentPO po);

    /**
     * po列表转entity列表
     * @param poList po
     * @return entity
     */
    List<AlgorithmChatContentEntity> toChatContentEntityList(List<AlgorithmChatContentPO> poList);

    /**
     * entity转po
     * @param entity po
     * @return po
     */
    AlgorithmChatContentPO toChatContent(AlgorithmChatContentEntity entity);

    /**
     * entity转vo
     * @param entity entity
     * @return ContentVO
     */
    @Mapping(target = "status", source = "chatStatus")
    ContentVO toContentVo(AlgorithmChatContentEntity entity);

    /**
     * contentVO转ContentResultVO
     * @param contentVO contentVO
     * @return ContentResultVO
     */
    ContentResultVO toContentResultVo(ContentVO contentVO);

    /**
     * contentVO转ContentResultVOV2
     * @param contentVO contentVO
     * @return ContentResultVOV2
     */
    @Named("toContentResultV2Vo")
	ContentResultVOV2 toContentResultV2Vo(ContentVO contentVO);

    /**
     * 转ContentResultVO列表
     * @param contentVoList contentVoList
     * @return List<ContentResultVO>
     */
    List<ContentResultVO> toContentResultVoList(List<ContentVO> contentVoList);

    /**
     * 转ContentResultVOV2列表
     * @param contentVoList contentVoList
     * @return List<ContentResultVOV2>
     */
    List<ContentResultVOV2> toContentResultVoV2List(List<ContentVO> contentVoList);

}
