package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <p>
 * AlgorithmUserFilePO
 * </p>
 *
 * <AUTHOR>
 * @description 用户文件资源表
 * @since 2024/4/8 15:38
 */
@Data
@TableName("algorithm_user_file")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AlgorithmUserFilePo {

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 文件id
     */
    @TableField(value = "file_id")
    private String fileId;


    /**
     * 文件id
     */
    @TableField(value = "meta_rowKey")
    private String metaRowKey;


    /**
     * 旧文件id
     */
    @TableField(value = "old_file_id")
    private String oldFileId;

    /**
     * 文件归属
     */
    @TableField(value = "owner_id")
    private String ownerId;

    /**
     * 业务类型：
     * -1 - 未知类型
     * 1-personal 个人云
     * 2-group 圈子
     * 3-shareGroup 共享群
     * 4-family 家庭云
     * 5-mount 挂载盘
     * 6-note 笔记
     * 7-cardpackage 卡包
     * 8-system 系统空间
     * 9-partner 合作空间
     * 10-activity 活动空间 照片直播
     */
    @TableField(value = "owner_type")
    private String ownerType;

    /**
     * paas平台编码
     */
    @TableField("paas_code")
    private String paasCode;


    /**
     * 文件所属节点
     */
    @TableField("drive_id")
    private String driveId;

    /**
     * 父目录Id
     */
    @TableField("parent_file_id")
    private String parentFileId;

    /**
     * 文件id全路径
     */
    @TableField("full_file_id_path")
    private String fullFileIdPath;

    /**
     * 文件哈希算法名
     */
    @TableField("hash_name")
    private String hashName;

    /**
     * 文件哈希值
     */
    @TableField("hash_value")
    private String hashValue;


    /**
     * 文件类型:1-文件，2-目录
     */
    @TableField("file_type")
    private String fileType;

    /**
     * 内容类型
     */
    @TableField("content_type")
    private Integer contentType;

    /**
     * 文件名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 文件/目录分类,见字典定义
     * 1 图片
     * 2 音频
     * 3 视频
     * 4 文档
     * 5 应用
     * 6 压缩文件
     * 0 其他
     * 100 普通目录
     */
    @TableField("category")
    private String category;


    /**
     * 文件位置
     */
    @TableField("file_position")
    private String filePosition;


    /**
     * 文件大小
     */
    @TableField("file_size")
    private String fileSize;

    /**
     * 宽度
     */
    @TableField("width")
    private String width;

    /**
     * 长度
     */
    @TableField("height")
    private String height;


    /**
     * 音视频时⻓
     */
    @TableField("duration")
    private Double duration;


    /**
     * 文件后缀
     */
    @TableField(value = "extension")
    private String extension;


    /**
     * 原文件id全路径
     */
    @TableField(value = "origin_full_file_id_path")
    private String originFullFileIdPath;


    /**
     * 拍摄时间
     */
    @TableField("shot_time")
    private Date shotTime;

    /**
     * 上传时间
     */
    @TableField("upload_time")
    private Date uploadTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 移入回收站时间
     */
    @TableField("trashed_time")
    private Date trashedTime;

    /**
     * 删除标识
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 创建人Id
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新人Id
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 本地更新时间
     */
    @TableField("local_updated_at")
    private Date localUpdatedAt;

    /**
     * 本地创建时间
     */
    @TableField("local_created_at")
    private Date localCreatedAt;

    /**
     * 文件创建时间
     */
    @TableField("file_created_at")
    private Date fileCreatedAt;

    /**
     * 文件更新时间
     */
    @TableField("file_updated_at")
    private Date fileUpdatedAt;


}
