package com.zyhl.yun.api.outer.persistence.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 妙云举报表
 * @author: wu<PERSON><PERSON><PERSON>
 */
@Data
@TableName("T_AI_YUNYOU_REPORTINFO")
public class ReportInfoPO {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 举报业务
     */
    @TableField("business_type")
    private Integer businessType;

    /**
     * 举报原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 联系方式
     */
    @TableField("contact_info")
    private String contactInfo;

    /**
     * 问题描述
     */
    @TableField("problem_description")
    private String problemDescription;

    /**
     * 图片
     */
    @TableField("picture1")
    private String picture1;
    /**
     * 图片
     */
    @TableField("picture2")
    private String picture2;
    /**
     * 图片
     */
    @TableField("picture3")
    private String picture3;
    /**
     * 图片
     */
    @TableField("picture4")
    private String picture4;
    /**
     * 图片
     */
    @TableField("picture5")
    private String picture5;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 变更时间时间戳，默认值用current_timestamp
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 删除标识
     */
    @TableField("del_flag")
    private Integer del_flag;
}
