package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.hcy.yun.ai.common.base.exception.YunAiBusinessException;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmChatMessageEntity;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.repository.AlgorithmChatContentRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatMessageRepository;
import com.zyhl.yun.api.outer.repository.AlgorithmChatRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 2024年02月28日 16:23
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class AlgorithmChatRepositoryImpl implements AlgorithmChatRepository {

    private final AlgorithmChatMessageRepository algorithmChatMessageRepository;

    private final AlgorithmChatContentRepository algorithmChatContentRepository;


    /**
     * 会话信息保存接口
     *
     * @param messageEntity 会话信息entity
     * @param contentEntity 会话内容entity
     * @return Boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveChatInfo(AlgorithmChatMessageEntity messageEntity, AlgorithmChatContentEntity contentEntity) {
        // 保存会话信息
        Boolean messageRes;
        if (ObjectUtil.isNotNull(messageEntity)) {
            messageRes = algorithmChatMessageRepository.saveMessage(messageEntity);
        } else {
            // 只更新会话信息表更新时间字段
            messageEntity = AlgorithmChatMessageEntity.builder().id(contentEntity.getSessionId()).updateTime(DateUtil.date()).build();
            messageRes = algorithmChatMessageRepository.updateMessage(messageEntity);
        }

        if (!messageRes) {
            log.error("保存会话信息失败 algorithmChatMessageEntity:{}", JsonUtil.toJson(messageEntity));
            throw new YunAiBusinessException(ResultCodeEnum.SAVE_CHAT_MESSAGE_FAIL);
        }

        // 保存会话内容
        Boolean contentRes = algorithmChatContentRepository.saveChatContent(contentEntity);
        if (!contentRes) {
            log.error("保存会话内容失败 algorithmChatContentEntity:{}", JsonUtil.toJson(contentEntity));
            throw new YunAiBusinessException(ResultCodeEnum.SAVE_CHAT_CONTENT_FAIL);
        }

        return true;
    }
}
