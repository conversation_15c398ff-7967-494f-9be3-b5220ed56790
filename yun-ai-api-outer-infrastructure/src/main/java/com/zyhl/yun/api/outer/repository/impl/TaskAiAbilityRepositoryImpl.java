package com.zyhl.yun.api.outer.repository.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.zyhl.hcy.commons.utils.JsonUtil;
import com.zyhl.yun.api.outer.domain.entity.TaskAiAbilityEntity;
import com.zyhl.yun.api.outer.enums.ResultCodeEnum;
import com.zyhl.yun.api.outer.enums.chat.DelFlagEnum;
import com.zyhl.yun.api.outer.enums.task.TaskFeePaidStatusEnum;
import com.zyhl.yun.api.outer.enums.task.TaskStatusEnum;
import com.zyhl.yun.api.outer.persistence.mapper.TaskAiAbilityMapper;
import com.zyhl.yun.api.outer.persistence.po.TaskAiAbilityPO;
import com.zyhl.yun.api.outer.repository.TaskAiAbilityRepository;
import com.zyhl.yun.api.outer.repository.assembler.TaskAiAbilityAssembler;
import com.zyhl.yun.api.outer.vo.TaskRespParamVO;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.text.CharSequenceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.zyhl.yun.api.outer.constants.Const.NUM_16;

/**
 * 算法任务Repository实现类
 *
 * <AUTHOR>
 * @since 2024-03-06
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class TaskAiAbilityRepositoryImpl extends ServiceImpl<TaskAiAbilityMapper, TaskAiAbilityPO>
        implements TaskAiAbilityRepository {

    private final TaskAiAbilityAssembler assembler;

    /**
     * 父任务id
     */
    private static final Long PARENT_TASK_ID = 0L;

    private static final String JOIN_STR = ", ";

    /**
     * 根据任务id获取算法任务信息
     *
     * @param taskId 任务id
     * @return AI输入算法任务Entity
     */
    @Override
    public TaskAiAbilityEntity getTaskEntity(Long taskId) {
        TaskAiAbilityPO po = baseMapper.selectById(taskId);
        TaskAiAbilityEntity taskRecordEntity = assembler.toTaskRecordEntity(po);
        //获取多任务, 把父子都查出来
        List<TaskAiAbilityEntity> taskAiAbilityEntityList = this.getMultipleTaskById(taskRecordEntity.getId());
        if (CollectionUtils.isEmpty(taskAiAbilityEntityList)) {
            log.info("任务为空，taskId：{}", taskRecordEntity.getId());
            return taskRecordEntity;
        }
        Optional<TaskAiAbilityEntity> parentOptional = taskAiAbilityEntityList.stream()
                .filter(taskAiAbilityEntity -> PARENT_TASK_ID.equals(taskAiAbilityEntity.getParentTaskId()))
                .findFirst();
        if (Boolean.FALSE.equals(parentOptional.isPresent())) {
            log.info("任务为空，taskId：{}", taskRecordEntity.getId());
            return taskRecordEntity;
        }
        //若对话内容表关联的任务没有子任务，则直接用任务表数据
        if (taskAiAbilityEntityList.size() == 1) {
            return parentOptional.get();
        }
        //关联子任务，需要根据子任务来判断, 有一个任务进行中，则整个是进行中，当存在一个成功并且没用进行中的，则整个是成功，当两个都是失败，则整个是失败
        boolean hasInProcessTask = false;
        boolean hasSuccessTask = false;

        List<TaskRespParamVO> respParamVoList = new ArrayList<>();
        TaskAiAbilityEntity parentTask = null;
        taskAiAbilityEntityList = taskAiAbilityEntityList.stream()
                .filter(taskAiAbilityEntity -> !PARENT_TASK_ID.equals(taskAiAbilityEntity.getParentTaskId()))
                .collect(Collectors.toList());
        for (TaskAiAbilityEntity taskAiAbilityEntity : taskAiAbilityEntityList) {
            if (TaskStatusEnum.IN_PROCESS.getCode().equals(taskAiAbilityEntity.getTaskStatus())
                    || TaskStatusEnum.NO_PROCESS.getCode().equals(taskAiAbilityEntity.getTaskStatus())) {
                hasInProcessTask = true;
            } else if (TaskStatusEnum.PROCESS_FINISH.getCode().equals(taskAiAbilityEntity.getTaskStatus())) {
                hasSuccessTask = true;
                String successRespParam = taskAiAbilityEntity.getRespParam();
                if (CharSequenceUtil.isNotBlank(successRespParam)) {
                    respParamVoList.addAll(JSON.parseArray(successRespParam, TaskRespParamVO.class));
                }
            }
            if (PARENT_TASK_ID.equals(taskAiAbilityEntity.getParentTaskId())) {
                parentTask = taskAiAbilityEntity;
            }
        }
        //扣费状态拿主任务的即可
        taskRecordEntity.setFeePaidStatus(Optional.ofNullable(parentTask).map(TaskAiAbilityEntity::getFeePaidStatus).orElse(null));
        taskRecordEntity.setFileExpiredStatus(Optional.ofNullable(parentTask).map(TaskAiAbilityEntity::getFileExpiredStatus).orElse(null));
        if (hasInProcessTask) {
            taskRecordEntity.setTaskStatus(TaskStatusEnum.IN_PROCESS.getCode());
            if (hasSuccessTask) {
                taskRecordEntity.setRespParam(JsonUtil.toJson(respParamVoList));
            }
        } else if (hasSuccessTask) {
            taskRecordEntity.setTaskStatus(TaskStatusEnum.PROCESS_FINISH.getCode());
            taskRecordEntity.setRespParam(JsonUtil.toJson(respParamVoList));
            taskRecordEntity.setResultCode(ResultCodeEnum.SUCCESS.getResultCode());
            taskRecordEntity.setResultMsg(ResultCodeEnum.SUCCESS.getResultMsg());
        } else {
            taskRecordEntity.setTaskStatus(TaskStatusEnum.PROCESS_FAILURE.getCode());
            taskRecordEntity.setResultMsg(taskAiAbilityEntityList.stream()
                    .map(TaskAiAbilityEntity::getResultMsg).collect(Collectors.joining(JOIN_STR)));
            Optional<String> optional = taskAiAbilityEntityList.stream().findFirst().map(TaskAiAbilityEntity::getResultCode);
            taskRecordEntity.setResultCode(optional.orElse(ResultCodeEnum.UNKNOWN_ERROR.getResultCode()));
        }
        return taskRecordEntity;
    }

    @Override
    public PageInfo<TaskAiAbilityEntity> getTaskEntityList(Integer page, Integer pageSize, String userId,
                                                           String command, String channelId, Integer needTotalCount) {
        PageMethod.offsetPage(page, pageSize, needTotalCount == 1);
        LocalDate today = LocalDate.now();
        LocalDate sevenDaysAgo = today.minus(7, ChronoUnit.DAYS);
        LambdaQueryChainWrapper<TaskAiAbilityPO> lambdaQueryChainWrapper = this.lambdaQuery()
                .eq(TaskAiAbilityPO::getUserId, userId)
                .eq(StringUtils.isNotBlank(command), TaskAiAbilityPO::getAlgorithmCode, command)
                .eq(StringUtils.isNotBlank(channelId), TaskAiAbilityPO::getSourceChannel, channelId)
                .ge(TaskAiAbilityPO::getCreateTime, sevenDaysAgo)
                .orderByDesc(TaskAiAbilityPO::getCreateTime);
        List<TaskAiAbilityPO> list = lambdaQueryChainWrapper.list();
        PageInfo<TaskAiAbilityPO> poPageInfo = PageInfo.of(list);
        PageInfo<TaskAiAbilityEntity> pageInfo = new PageInfo<>(assembler.toTaskRecordEntityList(list));
        pageInfo.setTotal(poPageInfo.getTotal());
        pageInfo.setNextPage(poPageInfo.getNextPage());
        return pageInfo;
    }


    @Override
    public Map<Long, TaskAiAbilityEntity> getTaskEntityMap(List<Long> taskIdList) {
        if (CollUtil.isNotEmpty(taskIdList)) {
            QueryWrapper<TaskAiAbilityPO> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(TaskAiAbilityPO::getId, taskIdList);
            List<TaskAiAbilityPO> taskAiAbilityPOList = baseMapper.selectList(queryWrapper);
            if (CollUtil.isNotEmpty(taskAiAbilityPOList)) {
                List<TaskAiAbilityEntity> taskRecordEntityList = assembler.toTaskRecordEntityList(taskAiAbilityPOList);
                // list转map
                Map<Long, TaskAiAbilityEntity> taskRecordEntityMap = taskRecordEntityList.stream().collect(Collectors.toMap(TaskAiAbilityEntity::getId, t -> t));
                log.info("AlgorithmChatCommentRepositoryImpl-getTaskEntityMap，结果：{}", JSON.toJSON(taskRecordEntityMap));
                return taskRecordEntityMap;
            }
        }
        return new HashMap<>(NUM_16);
    }

    @Override
    public boolean updateFeePaidStatus(Long id, Integer feeType, Integer feePaidStatus, String extInfo) {
        LambdaUpdateChainWrapper<TaskAiAbilityPO> lambda = this.lambdaUpdate().set(TaskAiAbilityPO::getFeeType, feeType)
                .set(TaskAiAbilityPO::getFeePaidStatus, feePaidStatus);
        if (StringUtils.isNotBlank(extInfo)) {
            lambda.set(TaskAiAbilityPO::getExtInfo, extInfo);
        }
        return lambda.eq(TaskAiAbilityPO::getId, id).update();
    }

    @Override
    public Integer countChannelUse(TaskAiAbilityEntity taskAiAbilityEntity, Date beginTime, Date endTime) {
        QueryWrapper<TaskAiAbilityPO> wrapper = new QueryWrapper<>();
        LambdaQueryWrapper<TaskAiAbilityPO> lambda = wrapper.lambda();
        lambda.eq(TaskAiAbilityPO::getUserId, taskAiAbilityEntity.getUserId())
                .eq(TaskAiAbilityPO::getAlgorithmCode, taskAiAbilityEntity.getAlgorithmCode())
                .eq(TaskAiAbilityPO::getTaskStatus, taskAiAbilityEntity.getTaskStatus())
                .between(TaskAiAbilityPO::getFinishTime, beginTime, endTime)
                .eq(TaskAiAbilityPO::getSourceChannel, taskAiAbilityEntity.getSourceChannel())
                .ne(TaskAiAbilityPO::getFeePaidStatus, TaskFeePaidStatusEnum.PAID.getCode());
        return baseMapper.selectCount(wrapper);
    }

    @Override
    public List<TaskAiAbilityEntity> countChannelUseList(TaskAiAbilityEntity taskAiAbilityEntity, List<Integer> list,
                                                         DateTime startTime, Date endTime) {
        QueryWrapper<TaskAiAbilityPO> wrapper = new QueryWrapper<>();
        wrapper.select("distinct algorithm_code, source_channel");
        LambdaQueryWrapper<TaskAiAbilityPO> lambda = wrapper.lambda();
        lambda.eq(TaskAiAbilityPO::getUserId, taskAiAbilityEntity.getUserId())
                .in(!CollectionUtils.isEmpty(list), TaskAiAbilityPO::getAlgorithmCode,
                        list.stream().map(code -> String.format("%03d", code)).collect(Collectors.toList()))
                .eq(TaskAiAbilityPO::getTaskStatus, taskAiAbilityEntity.getTaskStatus())
                .between(TaskAiAbilityPO::getFinishTime, startTime, endTime)
                .eq(TaskAiAbilityPO::getSourceChannel, taskAiAbilityEntity.getSourceChannel())
                .ne(TaskAiAbilityPO::getFeePaidStatus, TaskFeePaidStatusEnum.PAID.getCode());
        return assembler.toTaskRecordEntityList(baseMapper.selectList(wrapper));
    }

    @Override
    public PageInfo<TaskAiAbilityEntity> getFastReadTaskList(TaskAiAbilityEntity taskAiAbilityEntity, Integer pageCursor, Integer pageSize, Integer needTotalCount) {
        PageMethod.offsetPage(pageCursor, pageSize, Objects.equals(NumberUtils.INTEGER_ONE, needTotalCount));
        LambdaQueryWrapper<TaskAiAbilityPO> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .orderByDesc(TaskAiAbilityPO::getCreateTime)
                .eq(TaskAiAbilityPO::getTaskStatus, TaskStatusEnum.PROCESS_FINISH.getCode())
                .eq(TaskAiAbilityPO::getUserId, taskAiAbilityEntity.getUserId())
                .eq(TaskAiAbilityPO::getAlgorithmCode, taskAiAbilityEntity.getAlgorithmCode())
                .eq(TaskAiAbilityPO::getSubAlgorithmCode, taskAiAbilityEntity.getSubAlgorithmCode())
                .eq(TaskAiAbilityPO::getDelFlag, taskAiAbilityEntity.getDelFlag());
        List<TaskAiAbilityPO> list = baseMapper.selectList(wrapper);
        PageInfo<TaskAiAbilityPO> poPageInfo = PageInfo.of(list);
        PageInfo<TaskAiAbilityEntity> pageInfo = new PageInfo<>(assembler.toTaskRecordEntityList(list));
        pageInfo.setTotal(poPageInfo.getTotal());
        pageInfo.setNextPage(poPageInfo.getNextPage());
        return pageInfo;
    }

    @Override
    public Boolean readTaskDelete(TaskAiAbilityEntity taskAiAbilityEntity) {
        LambdaUpdateWrapper<TaskAiAbilityPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(TaskAiAbilityPO::getDelFlag, DelFlagEnum.YES.getCode())
                .set(TaskAiAbilityPO::getUpdateTime, new Date())
                .eq(TaskAiAbilityPO::getId, taskAiAbilityEntity.getId())
                .eq(TaskAiAbilityPO::getUserId, taskAiAbilityEntity.getUserId());
        int update = baseMapper.update(null, wrapper);
        return update > 0 ? Boolean.TRUE : Boolean.FALSE;
    }

    @Override
    public List<TaskAiAbilityEntity> getMultipleTaskById(Long taskId) {
        return assembler.toTaskRecordEntityList(this.baseMapper.selectWithUnionAll(taskId));
    }

    @Override
    public TaskAiAbilityEntity getTaskEntityForSpeedRead(Long taskId) {
        LambdaQueryWrapper<TaskAiAbilityPO> wrapper = Wrappers.<TaskAiAbilityPO>lambdaQuery()
                .eq(TaskAiAbilityPO::getId, taskId)
                .eq(TaskAiAbilityPO::getDelFlag, DelFlagEnum.NO.getCode());
        TaskAiAbilityPO po = baseMapper.selectOne(wrapper);
        return assembler.toTaskRecordEntity(po);

    }

}
