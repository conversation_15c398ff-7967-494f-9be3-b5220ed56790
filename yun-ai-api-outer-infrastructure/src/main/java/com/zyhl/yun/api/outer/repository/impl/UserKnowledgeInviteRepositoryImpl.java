package com.zyhl.yun.api.outer.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zyhl.yun.api.outer.domain.dto.common.PageInfoDTO;
import com.zyhl.yun.api.outer.domain.entity.knowledge.UserKnowledgeInviteEntity;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeInviteStatusEnum;
import com.zyhl.yun.api.outer.enums.knowledge.KnowledgeSelectedEnum;
import com.zyhl.yun.api.outer.persistence.mapper.UserKnowledgeInviteMapper;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmUserKnowledgeInvitePO;
import com.zyhl.yun.api.outer.repository.UserKnowledgeInviteRepository;
import com.zyhl.yun.api.outer.repository.assembler.UserKnowledgeInviteAssembler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Repository
public class UserKnowledgeInviteRepositoryImpl extends ServiceImpl<UserKnowledgeInviteMapper, AlgorithmUserKnowledgeInvitePO> implements UserKnowledgeInviteRepository {

    @Resource
    private UserKnowledgeInviteAssembler userKnowledgeInviteAssembler;

    @Resource
    private UserKnowledgeInviteMapper userKnowledgeInviteMapper;

    @Override
    public List<UserKnowledgeInviteEntity> getListByUserId(List<Long> knowledgeId, String userId) {
        // 构建查询条件
        LambdaQueryWrapper<AlgorithmUserKnowledgeInvitePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AlgorithmUserKnowledgeInvitePO::getUserId, userId)
                .eq(AlgorithmUserKnowledgeInvitePO::getStatus, KnowledgeInviteStatusEnum.ENABLE.getStatus())
                .in(AlgorithmUserKnowledgeInvitePO::getKnowledgeId, knowledgeId);
        List<AlgorithmUserKnowledgeInvitePO> poList = userKnowledgeInviteMapper.selectList(lambdaQueryWrapper);
        return userKnowledgeInviteAssembler.toUserKnowledgeInviteEntityList(poList);
    }

    @Override
    public PageInfo<UserKnowledgeInviteEntity> pageByInviteUserId(List<Long> knowledgeId, String inviteUserId, PageInfoDTO pageInfo) {
        // 分页准备
        PageHelper.offsetPage(Integer.parseInt(pageInfo.getPageCursor()), pageInfo.getPageSize(), pageInfo.isNeedTotal());

        // 查询条件
        List<AlgorithmUserKnowledgeInvitePO> pos = this.lambdaQuery()
                .eq(AlgorithmUserKnowledgeInvitePO::getInviteUserId, inviteUserId)
                .in(AlgorithmUserKnowledgeInvitePO::getKnowledgeId, knowledgeId)
                .eq(AlgorithmUserKnowledgeInvitePO::getStatus, KnowledgeInviteStatusEnum.ENABLE.getStatus())
                .list();

        // 数据转换
        Page<UserKnowledgeInviteEntity> page = new Page<>();
        BeanUtils.copyProperties(pos, page);
        page.addAll(userKnowledgeInviteAssembler.toUserKnowledgeInviteEntityList(pos));
        return PageInfo.of(page);
    }

    @Override
    public boolean deleteByUserId(List<Long> knowledgeId, String userId) {
        // 删除
        return this.lambdaUpdate()
                .eq(AlgorithmUserKnowledgeInvitePO::getUserId, userId)
                .in(AlgorithmUserKnowledgeInvitePO::getKnowledgeId, knowledgeId)
                .set(AlgorithmUserKnowledgeInvitePO::getStatus, KnowledgeInviteStatusEnum.DISABLE.getStatus())
                .set(AlgorithmUserKnowledgeInvitePO::getUpdateTime, new Date())
                .update();
    }

    @Override
    public int save(UserKnowledgeInviteEntity entity) {
        AlgorithmUserKnowledgeInvitePO po = userKnowledgeInviteAssembler.toAlgorithmUserKnowledgeInvitePO(entity);
        return userKnowledgeInviteMapper.insert(po);
    }

    @Override
    public List<UserKnowledgeInviteEntity> get(Long knowledgeId, String userId) {
        List<AlgorithmUserKnowledgeInvitePO> pos = this.lambdaQuery()
                .eq(AlgorithmUserKnowledgeInvitePO::getKnowledgeId, knowledgeId)
                .eq(AlgorithmUserKnowledgeInvitePO::getUserId, userId)
                .eq(AlgorithmUserKnowledgeInvitePO::getStatus, KnowledgeInviteStatusEnum.ENABLE.getStatus())
                .list();
        return userKnowledgeInviteAssembler.toUserKnowledgeInviteEntityList(pos);
    }

    @Override
    public List<UserKnowledgeInviteEntity> getKnowledgeListByUserId(String userId) {
        List<AlgorithmUserKnowledgeInvitePO> list = this.lambdaQuery()
                .eq(AlgorithmUserKnowledgeInvitePO::getUserId, userId)
                .eq(AlgorithmUserKnowledgeInvitePO::getStatus, KnowledgeInviteStatusEnum.ENABLE.getStatus())
                .list();
        return userKnowledgeInviteAssembler.toUserKnowledgeInviteEntityList(list);
    }

    @Override
    public List<UserKnowledgeInviteEntity> getAllListByKnowledgeId(Long knowledgeId, boolean condition, boolean isAsc, String... columns) {
        // 全量查询
        List<AlgorithmUserKnowledgeInvitePO> pos = this.query()
                .eq("knowledge_id", knowledgeId)
                .eq("status", KnowledgeInviteStatusEnum.ENABLE.getStatus())
                .orderBy(condition, isAsc, columns)
                .list();
        return userKnowledgeInviteAssembler.toUserKnowledgeInviteEntityList(pos);
    }

    @Override
    public void updateSelected(String userId, List<String> baseIdList) {
        // 先更新为未选择
        this.lambdaUpdate()
                .eq(AlgorithmUserKnowledgeInvitePO::getUserId, userId)
                .eq(AlgorithmUserKnowledgeInvitePO::getStatus, KnowledgeInviteStatusEnum.ENABLE.getStatus())
                .eq(AlgorithmUserKnowledgeInvitePO::getSelected, KnowledgeSelectedEnum.SELECTED.getStatus())
                .set(AlgorithmUserKnowledgeInvitePO::getSelected, KnowledgeSelectedEnum.NOT_SELECTED.getStatus())
                .set(AlgorithmUserKnowledgeInvitePO::getUpdateTime, new Date())
                .update();

        // 再更新为已选择
        if (ObjectUtil.isNotEmpty(baseIdList)) {
            this.lambdaUpdate()
                    .eq(AlgorithmUserKnowledgeInvitePO::getUserId, userId)
                    .eq(AlgorithmUserKnowledgeInvitePO::getStatus, KnowledgeInviteStatusEnum.ENABLE.getStatus())
                    .in(AlgorithmUserKnowledgeInvitePO::getKnowledgeId, baseIdList)
                    .set(AlgorithmUserKnowledgeInvitePO::getSelected, KnowledgeSelectedEnum.SELECTED.getStatus())
                    .set(AlgorithmUserKnowledgeInvitePO::getUpdateTime, new Date())
                    .update();
        }
    }

    @Override
    public int count(Long knowledgeId, String userId) {
        return this.lambdaQuery()
                .eq(AlgorithmUserKnowledgeInvitePO::getKnowledgeId, knowledgeId)
                .eq(AlgorithmUserKnowledgeInvitePO::getUserId, userId)
                .eq(AlgorithmUserKnowledgeInvitePO::getStatus, KnowledgeInviteStatusEnum.ENABLE.getStatus())
                .count();
    }

    @Override
    public int countByKnowledgeId(Long knowledgeId) {
        return this.lambdaQuery()
                .eq(AlgorithmUserKnowledgeInvitePO::getKnowledgeId, knowledgeId)
                .eq(AlgorithmUserKnowledgeInvitePO::getStatus, KnowledgeInviteStatusEnum.ENABLE.getStatus())
                .count();
    }

    @Override
    public void updateStatus(Long knowledgeId) {
        // 把该知识库id对应的所有记录的status改成0-停用
        this.lambdaUpdate()
                .eq(AlgorithmUserKnowledgeInvitePO::getKnowledgeId, knowledgeId)
                .set(AlgorithmUserKnowledgeInvitePO::getStatus, KnowledgeInviteStatusEnum.DISABLE.getStatus())
                .set(AlgorithmUserKnowledgeInvitePO::getUpdateTime, new Date())
                .update();
    }

}
