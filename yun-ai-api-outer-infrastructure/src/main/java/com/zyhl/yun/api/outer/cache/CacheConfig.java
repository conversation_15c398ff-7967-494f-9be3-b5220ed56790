package com.zyhl.yun.api.outer.cache;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zyhl.hcy.commons.result.BaseResult;
import com.zyhl.yun.outer.datahelper.util.constant.CacheConfigConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.cache.CacheProperties;
import org.springframework.boot.autoconfigure.cache.CacheType;
import org.springframework.cache.Cache;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext.SerializationPair;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 初始化redis cachemanager，用于redis缓存和ehcache缓存注解配置
 * <AUTHOR>
 **/
@Configuration
@Slf4j
public class CacheConfig extends CachingConfigurerSupport {

    private ObjectMapper getObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        // 序列化时不包含null字段
        objectMapper.setSerializationInclusion(Include.NON_NULL);

        // 忽略不存在的字段
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

        // For Redis Serialization
        objectMapper.activateDefaultTyping(objectMapper.getPolymorphicTypeValidator(),
                ObjectMapper.DefaultTyping.NON_FINAL, JsonTypeInfo.As.PROPERTY);

        return objectMapper;
    }

    /**
     * com.zyhl.hcy包的CacheAutoConfiguration需要依赖该对象，在yaml配置相应配置仍无法转换，因此在这里手动注入
     *
     * @return com.zyhl.hcy包的CacheAutoConfiguration需要依赖该对象注入到spring容器中
     */
    @Bean("cacheProperties")
    public CacheProperties initCacheProperties() {
        CacheProperties cacheProperties = new CacheProperties();
        cacheProperties.setType(CacheType.REDIS);
        return cacheProperties;
    }

    @Bean("redisCacheManager")
    @Primary
    public RedisCacheManager redisCacheManager(RedisConnectionFactory redisConnectionFactory) {
        Map<String, RedisCacheConfiguration> redisCacheConfigurationMap = new HashMap<>(2);

        // 采用Jackson2JsonRedisSerializer
        Jackson2JsonRedisSerializer<BaseResult> baseResultJackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(
                BaseResult.class);
        baseResultJackson2JsonRedisSerializer.setObjectMapper(getObjectMapper());

        // 给ConfigConstants.BASE_RESULT_CACHE
        // 这个key设置过期时间30s，序列化方式为Jackson2JsonRedisSerializer
        redisCacheConfigurationMap.put(CacheConfigConstants.BASE_RESULT_CACHE,
                RedisCacheConfiguration.defaultCacheConfig().entryTtl(Duration.ofSeconds(30L))
                        .serializeValuesWith(SerializationPair.fromSerializer(baseResultJackson2JsonRedisSerializer)));

        // 采用GenericJackson2JsonRedisSerializer
        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer(getObjectMapper());
        SerializationPair<Object> valueSerializationPair = SerializationPair.fromSerializer(jsonSerializer);
        redisCacheConfigurationMap.put(CacheConfigConstants.EXPIRE_15_SECONDS_CACHE,
                RedisCacheConfiguration.defaultCacheConfig().entryTtl(Duration.ofSeconds(15L))
                        .serializeKeysWith(SerializationPair.fromSerializer(new StringRedisSerializer()))
                        .serializeValuesWith(valueSerializationPair));

        defineCommonRedisCache(redisCacheConfigurationMap);
        RedisCacheManager redisCacheManager = RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(RedisCacheConfiguration.defaultCacheConfig().entryTtl(Duration.ofSeconds(120)))
                .withInitialCacheConfigurations(redisCacheConfigurationMap).build();
        redisCacheManager.afterPropertiesSet();

        return redisCacheManager;
    }

    private void defineCommonRedisCache(Map<String, RedisCacheConfiguration> redisCacheConfigurationMap) {
        // 通用 json 序列化
        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer(getObjectMapper());
        SerializationPair<Object> valueSerializationPair = SerializationPair.fromSerializer(jsonSerializer);

        redisCacheConfigurationMap.put(CacheConfigConstants.EXPIRE_30_SECONDS_CACHE, RedisCacheConfiguration
                .defaultCacheConfig().entryTtl(Duration.ofSeconds(30L)).serializeValuesWith(valueSerializationPair));
        redisCacheConfigurationMap.put(CacheConfigConstants.EXPIRE_60_SECONDS_CACHE, RedisCacheConfiguration
                .defaultCacheConfig().entryTtl(Duration.ofSeconds(60L)).serializeValuesWith(valueSerializationPair));
        redisCacheConfigurationMap.put(CacheConfigConstants.EXPIRE_90_SECONDS_CACHE, RedisCacheConfiguration
                .defaultCacheConfig().entryTtl(Duration.ofSeconds(90L)).serializeValuesWith(valueSerializationPair));

        redisCacheConfigurationMap.put(CacheConfigConstants.EXPIRE_LONG_CACHE, RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(10L)).serializeValuesWith(valueSerializationPair));

        redisCacheConfigurationMap.put(CacheConfigConstants.EXPIRE_150_SECONDS_CACHE, RedisCacheConfiguration
                .defaultCacheConfig().entryTtl(Duration.ofSeconds(150L)).serializeValuesWith(valueSerializationPair));

        redisCacheConfigurationMap.put(CacheConfigConstants.EXPIRE_300_SECONDS_CACHE, RedisCacheConfiguration
                .defaultCacheConfig().entryTtl(Duration.ofSeconds(300L)).serializeValuesWith(valueSerializationPair));

        redisCacheConfigurationMap.put(CacheConfigConstants.AI_PRE_USER, RedisCacheConfiguration
                .defaultCacheConfig().entryTtl(Duration.ofSeconds(300L)).computePrefixWith(name -> CacheConfigConstants.AI_PRE_USER).serializeValuesWith(valueSerializationPair));

        redisCacheConfigurationMap.put(CacheConfigConstants.EXPIRE_30_MINUTES_CACHE, RedisCacheConfiguration
                .defaultCacheConfig().entryTtl(Duration.ofMinutes(30L)).serializeValuesWith(valueSerializationPair));
    }

    /**
     * redis数据操作异常处理。该方法处理逻辑：在日志中打印出错误信息，但是放行。 保证redis服务器出现连接等问题的时候不影响程序的正常运行
     */
    @Override
    public CacheErrorHandler errorHandler() {
        return new CacheErrorHandler() {
            @Override
            public void handleCachePutError(RuntimeException exception, Cache cache, Object key, Object value) {
                handleRedisErrorException(exception, key);
            }

            @Override
            public void handleCacheGetError(RuntimeException exception, Cache cache, Object key) {
                handleRedisErrorException(exception, key);
            }

            @Override
            public void handleCacheEvictError(RuntimeException exception, Cache cache, Object key) {
                handleRedisErrorException(exception, key);
            }

            @Override
            public void handleCacheClearError(RuntimeException exception, Cache cache) {
                handleRedisErrorException(exception, null);
            }
        };
    }

    protected void handleRedisErrorException(RuntimeException exception, Object key) {
        log.error("redis异常：key=[{}]", key, exception);
    }
}
