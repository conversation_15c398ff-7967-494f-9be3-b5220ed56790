package com.zyhl.yun.api.outer.repository.assembler;

import com.zyhl.yun.api.outer.domain.entity.TaskAiAbilityEntity;
import com.zyhl.yun.api.outer.persistence.po.TaskAiAbilityPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 类转换 TaskAiAbilityEntity <--> TaskAiAbilityPO
 *
 * <AUTHOR>
 * @since 2024-03-06
 */
@Mapper(componentModel = "spring")
public interface TaskAiAbilityAssembler {

    TaskAiAbilityAssembler INSTANCE = Mappers.getMapper(TaskAiAbilityAssembler.class);

    /**
     * po转entity
     * @param po po
     * @return entity
     */
    TaskAiAbilityEntity toTaskRecordEntity(TaskAiAbilityPO po);

    /**
     * po转entity列表
     * @param poList poList
     * @return entity列表
     */
    List<TaskAiAbilityEntity> toTaskRecordEntityList(List<TaskAiAbilityPO> poList);

}
