package com.zyhl.yun.api.outer.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.yun.api.outer.domain.entity.AlgorithmBusinessGroupEntity;
import com.zyhl.yun.api.outer.persistence.mapper.AlgorithmBusinessGroupMapper;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmBusinessGroupPO;
import com.zyhl.yun.api.outer.repository.AlgorithmBusinessGroupRepository;
import com.zyhl.yun.api.outer.repository.assembler.AlgorithmBusinessGroupAssembler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 算法业务组表-RepositoryImpl
 * @Author: WeiJingKun
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class AlgorithmBusinessGroupRepositoryImpl extends ServiceImpl<AlgorithmBusinessGroupMapper, AlgorithmBusinessGroupPO> implements AlgorithmBusinessGroupRepository {

    private final AlgorithmBusinessGroupAssembler assembler;

    @Override
    public AlgorithmBusinessGroupEntity queryByAlgorithmGroupCode(Integer algorithmGroupCode) {
        /** 构建查询条件 */
        QueryWrapper<AlgorithmBusinessGroupPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AlgorithmBusinessGroupPO::getAlgorithmGroupCode, algorithmGroupCode);
        /** 查询数据返回 */
        AlgorithmBusinessGroupPO businessGroupPO = baseMapper.selectOne(queryWrapper);
        if(null != businessGroupPO){
            return assembler.toEntity(businessGroupPO);
        }
        return null;
    }
}
