package com.zyhl.yun.api.outer.repository.impl;

import com.alibaba.fastjson.JSON;
import com.zyhl.yun.api.outer.domain.entity.ApplicationTypeListEntity;
import com.zyhl.yun.api.outer.domain.vo.ChatApplicationType;
import com.zyhl.yun.api.outer.persistence.po.AlgorithmChatApplicationAgentPO;
import com.zyhl.yun.api.outer.repository.ChatApplicationTypeRepository;
import com.zyhl.yun.api.outer.repository.IAlgorithmChatApplicationAgentService;
import com.zyhl.yun.api.outer.repository.assembler.ChatApplicationTypeAssembler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2024年04月16日 16:23
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class ChatApplicationTypeRepositoryImpl implements ChatApplicationTypeRepository {


    private final IAlgorithmChatApplicationAgentService iAlgorithmChatApplicationAgentService;

    private final ChatApplicationTypeAssembler chatApplicationTypeAssembler;


    @Override
    public List<ChatApplicationType> typeList(ApplicationTypeListEntity entity) {
        List<AlgorithmChatApplicationAgentPO> algorithmChatApplicationAgentList = iAlgorithmChatApplicationAgentService.listOrderedByAsc(entity);
        return chatApplicationTypeAssembler.toChatApplicationTypeList(algorithmChatApplicationAgentList);
    }

    @Override
    public Map<String, ChatApplicationType> listToMapKeyIsId() {
        List<AlgorithmChatApplicationAgentPO> algorithmChatApplicationAgentList = iAlgorithmChatApplicationAgentService.listOrderedByAsc(new ApplicationTypeListEntity());
        List<ChatApplicationType> chatApplicationTypeList = chatApplicationTypeAssembler.toChatApplicationTypeList(algorithmChatApplicationAgentList);
        // list转map
        Map<String, ChatApplicationType> chatApplicationTypeMap = chatApplicationTypeList.stream().collect(Collectors.toMap(ChatApplicationType::getApplicationId, t -> t));
        log.info("ChatApplicationTypeRepositoryImpl-listToMapKeyIsId，结果：{}", JSON.toJSON(chatApplicationTypeMap));
        return chatApplicationTypeMap;
    }

    @Override
    public ChatApplicationType getByApplicationId(String applicationId) {
        AlgorithmChatApplicationAgentPO po = iAlgorithmChatApplicationAgentService.getById(applicationId);
        return chatApplicationTypeAssembler.toChatApplicationType(po);
    }

    @Override
    public List<ChatApplicationType> getByAppList(List<String> idList) {
        List<AlgorithmChatApplicationAgentPO> poList = iAlgorithmChatApplicationAgentService.getList(idList);
        return chatApplicationTypeAssembler.toChatApplicationTypeList(poList);
    }
}
