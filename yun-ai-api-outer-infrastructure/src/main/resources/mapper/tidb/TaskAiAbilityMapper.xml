<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zyhl.yun.api.outer.persistence.mapper.TaskAiAbilityMapper">

    <select id="selectWithUnionAll"
            resultType="com.zyhl.yun.api.outer.persistence.po.TaskAiAbilityPO">
        SELECT *
        FROM `algorithm_task_ai_ability`
        WHERE `parent_task_id` = #{taskId}
        UNION ALL
        SELECT *
        FROM `algorithm_task_ai_ability`
        WHERE `id` = #{taskId}
    </select>

</mapper>