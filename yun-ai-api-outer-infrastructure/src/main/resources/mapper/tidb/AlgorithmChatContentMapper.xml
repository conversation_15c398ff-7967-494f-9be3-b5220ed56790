<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zyhl.yun.api.outer.persistence.mapper.AlgorithmChatContentMapper">

    <!-- 公共sql -->
    <sql id="findCommon">
        SELECT t.id, t.user_id, t.session_id, t.application_id, t.application_type, t.business_type, t.task_id, t.talk_type,
               t.resource_type, t.tools_command, t.sub_tools_command, t.model_type, t.in_content, t.in_audit_time, t.in_audit_status, t.in_resource_id,
               t.out_content, t.out_audit_time, t.out_audit_status, t.out_resource_id, t.source_channel, t.create_time, t.update_time, t.ext_info,
               t.recommend_info, t.middle_recommend_info, t.chat_status, t.out_content_type, t.command_type, t.scene_tag,
               task.task_status, task.fee_paid_status, task.file_expired_status, task.result_code, task.result_msg, task.resp_param
        FROM algorithm_chat_content t
        LEFT JOIN algorithm_task_ai_ability task ON t.task_id = task.id
    </sql>

    <select id="findList" resultType="com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity">
        <include refid="findCommon"/>
        <where>
            <if test='entity.id!=null'>
                AND t.id = #{entity.id}
            </if>
            <if test="entity.dialogueIdList != null and entity.dialogueIdList.size() > 0">
                AND t.id IN <foreach collection="entity.dialogueIdList" item="dialogueId" open="(" close=")" separator=",">
                    #{dialogueId}
                </foreach>
            </if>
            <if test='entity.userId!=null and entity.userId!=""'>
                AND t.user_id = #{entity.userId}
            </if>
            <if test='entity.sessionId!=null'>
                AND t.session_id = #{entity.sessionId}
            </if>
            <if test='entity.businessType!=null and entity.businessType!=""'>
                <!-- 查询小邮助手旧数据的条件为null或空串，则走正常逻辑 -->
                <if test="entity.queryAllOldDataFlag == null or entity.queryAllOldDataFlag == ''">
                    AND t.business_type = #{entity.businessType}
                </if>
                <!--
                    查询小邮助手旧数据的条件：1-查询
                    1、新产生的数据则按业务类型隔离数据展示（根据配置的时间节点判断）
                    2、旧数据查询全部（根据配置的时间节点判断）
                -->
                <if test='entity.queryAllOldDataFlag != null and entity.queryAllOldDataFlag == "1"'>
                    AND (
                        <!-- 新产生的数据则按业务类型隔离数据展示（根据配置的时间节点判断） -->
                        (t.create_time <![CDATA[ >= ]]> #{entity.queryAllOldDataTimeNodes} AND t.business_type = #{entity.businessType})
                        <!-- 旧数据查询全部（根据配置的时间节点判断） -->
                        OR (
                            t.create_time <![CDATA[ < ]]> #{entity.queryAllOldDataTimeNodes}
                            <!-- 遍历所有云邮助手业务类型 -->
                            <if test="entity.yunMailAllBusinessTypeList != null and entity.yunMailAllBusinessTypeList.size() > 0">
                                AND t.business_type IN <foreach collection="entity.yunMailAllBusinessTypeList" item="businessType" open="(" close=")" separator=",">
                                    #{businessType}
                                </foreach>
                            </if>
                        )
                    )
                </if>
            </if>
            <if test='entity.applicationType!=null and entity.applicationType!=""'>
                AND t.application_type = #{entity.applicationType}
            </if>
            <!-- 大于等于创建时间节点 -->
            <if test='entity.geCreateTime!=null and entity.geCreateTime!=""'>
                AND t.create_time <![CDATA[ >= ]]> #{entity.geCreateTime}
            </if>
            AND t.del_flag = 0
            AND t.talk_type = 0
            AND (
                <!-- 【无任务数据】，查内容表的chat_status IN (1-对话停止, 3-对话成功, 4-对话终止) -->
                (t.task_id IS NULL AND t.chat_status IN (1,3,4))
                <!-- 多子任务处理，待处理要展示 -->
                OR ( task.task_status = 1 and t.chat_status IN ( 1, 3, 4 )) 
                <!--
                    【有任务数据】
                    1、result_code = 3-任务完成 AND file_expired_status  != 1-已过期，返回【有引导语返回时，result_code是3】
                    2、result_code != 3-任务完成，如果引导或者推荐不为空的： NOT IN ('{}','{"contextList":[]}')，也返回
                -->
                OR (task.task_status = 3 AND task.file_expired_status != 1)
                OR (task.task_status != 3 AND t.recommend_info IS NOT NULL AND t.recommend_info NOT IN ('{}','{"contextList":[]}') )
                OR (t.chat_status = 0)
            )
        </where>
        <if test="null != entity.sortType and entity.sortType == 2">
                ORDER BY t.create_time ASC
        </if>
        <if test="null == entity.sortType or entity.sortType == 1">
		        ORDER BY t.create_time DESC
        </if>
    </select>

    <select id="findListV2" resultType="com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity">
        <include refid="findCommon"/>
        <where>
            <if test='entity.id!=null'>
                AND t.id = #{entity.id}
            </if>
            <if test="entity.dialogueIdList != null and entity.dialogueIdList.size() > 0">
                AND t.id IN <foreach collection="entity.dialogueIdList" item="dialogueId" open="(" close=")" separator=",">
                    #{dialogueId}
                </foreach>
            </if>
            <if test='entity.userId!=null and entity.userId!=""'>
                AND t.user_id = #{entity.userId}
            </if>
            <if test='entity.sessionId!=null'>
                AND t.session_id = #{entity.sessionId}
            </if>
            <if test='entity.businessType!=null and entity.businessType!=""'>
                <!-- 查询小邮助手旧数据的条件为null或空串，则走正常逻辑 -->
                <if test="entity.queryAllOldDataFlag == null or entity.queryAllOldDataFlag == ''">
                    <!-- businessTypeList没有值，则只查询businessType -->
                    <if test="entity.businessTypeList == null or entity.businessTypeList.size() == 0">
                        AND t.business_type = #{entity.businessType}
                    </if>
                    <!-- businessTypeList有值，则查询businessTypeList -->
                    <if test="entity.businessTypeList != null and entity.businessTypeList.size() > 0">
                        AND t.business_type IN <foreach collection="entity.businessTypeList" item="businessType" open="(" close=")" separator=",">
                            #{businessType}
                        </foreach>
                    </if>
                </if>
                <!--
                    查询小邮助手旧数据的条件：1-查询
                    1、新产生的数据则按业务类型隔离数据展示（根据配置的时间节点判断）
                    2、旧数据查询全部（根据配置的时间节点判断）
                -->
                <if test='entity.queryAllOldDataFlag != null and entity.queryAllOldDataFlag == "1"'>
                    AND (
                        <!-- 新产生的数据则按业务类型隔离数据展示（根据配置的时间节点判断） -->
                        (t.create_time <![CDATA[ >= ]]> #{entity.queryAllOldDataTimeNodes} AND t.business_type = #{entity.businessType})
                        <!-- 旧数据查询全部（根据配置的时间节点判断） -->
                        OR (
                            t.create_time <![CDATA[ < ]]> #{entity.queryAllOldDataTimeNodes}
                            <!-- 遍历所有云邮助手业务类型 -->
                            <if test="entity.yunMailAllBusinessTypeList != null and entity.yunMailAllBusinessTypeList.size() > 0">
                                AND t.business_type IN <foreach collection="entity.yunMailAllBusinessTypeList" item="businessType" open="(" close=")" separator=",">
                                    #{businessType}
                                </foreach>
                            </if>
                        )
                    )
                </if>
            </if>
            <if test='entity.applicationType!=null and entity.applicationType!=""'>
                AND t.application_type = #{entity.applicationType}
            </if>
            <!-- 大于等于创建时间节点 -->
            <if test='entity.geCreateTime!=null and entity.geCreateTime!=""'>
                AND t.create_time <![CDATA[ >= ]]> #{entity.geCreateTime}
            </if>
            AND t.del_flag = 0
            AND t.talk_type = 0
            AND (
                <!-- 【无任务数据】，查内容表的chat_status IN (1-对话停止, 3-对话成功, 4-对话终止) -->
                (t.task_id IS NULL AND t.chat_status IN (1,3,4))
                <!-- 多子任务处理，待处理要展示 -->
                OR ( task.task_status = 1 and t.chat_status IN ( 1, 3, 4 ))
                <!--
                    【有任务数据】
                    1、result_code = 3-任务完成 AND file_expired_status  != 1-已过期，返回【有引导语返回时，result_code是3】
                    2、result_code != 3-任务完成，如果引导或者推荐不为空的： NOT IN ('{}','{"contextList":[]}')，也返回
                -->
                OR (task.task_status = 3)
                OR (task.task_status != 3 AND t.recommend_info IS NOT NULL AND t.recommend_info NOT IN ('{}','{"contextList":[]}') )
                OR (t.chat_status = 0)
            )
        </where>
        <if test="null != entity.sortType and entity.sortType == 2">
                ORDER BY t.create_time ASC
        </if>
        <if test="null == entity.sortType or entity.sortType == 1">
		        ORDER BY t.create_time DESC
        </if>
    </select>

    <select id="findPollingUpdateData" resultType="com.zyhl.yun.api.outer.domain.entity.AlgorithmChatContentEntity">
        <include refid="findCommon"/>
        <where>
            <if test='entity.id!=null'>
                AND t.id = #{entity.id}
            </if>
            <if test='entity.userId!=null and entity.userId!=""'>
                AND t.user_id = #{entity.userId}
            </if>
            AND t.del_flag = 0
        </where>
    </select>

</mapper>