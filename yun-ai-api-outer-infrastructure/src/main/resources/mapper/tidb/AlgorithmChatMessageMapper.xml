<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zyhl.yun.api.outer.persistence.mapper.AlgorithmChatMessageMapper">

    <insert id="insertMessage" parameterType="com.zyhl.yun.api.outer.persistence.po.AlgorithmChatMessagePO">
        INSERT INTO algorithm_chat_message (
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="title != null">title,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag</if>
        </trim>
        ) VALUES (
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="title != null">#{title},</if>
            <if test="createTime != null">#{createTime, jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime, jdbcType=TIMESTAMP},</if>
            <if test="delFlag != null">#{delFlag}</if>
        </trim>
        )
    </insert>

</mapper>