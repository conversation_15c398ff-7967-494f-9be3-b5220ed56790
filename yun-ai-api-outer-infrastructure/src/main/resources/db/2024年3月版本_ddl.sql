-- 会话设置表
CREATE TABLE `algorithm_chat_config` (
                                         `id` BIGINT NOT NULL COMMENT '主键',
                                         `user_id` VARCHAR(64) NOT NULL COMMENT '用户id',
                                         `model_type` VARCHAR(16) COMMENT '模型类型 qwen：通义千问，xfyun：讯飞星火大',
                                         `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         PRIMARY KEY (`id`, `user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='会话配置表';


-- AI用户评价结果表
CREATE TABLE `algorithm_chat_comment` (
                                         `id` BIGINT NOT NULL COMMENT '主键',
                                         `user_id` VARCHAR(64) NOT NULL COMMENT '用户id',
                                         `dialogue_id` VARCHAR(64) NOT NULL COMMENT '任务id',
                                         `session_id` VARCHAR(64) NOT NULL COMMENT '会话id',
                                         `model_type` VARCHAR(16) COMMENT '模型类型 qwen：通义千问，xfyun：讯飞星火大',
                                         `like_comment` tinyint(2) COMMENT '是否喜欢 0:不喜欢，1:喜欢',
                                         `default_comment` VARCHAR(512) COMMENT '默认评论',
                                         `custom_comment` VARCHAR(1024) COMMENT '用户自定义评论',
                                         `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         PRIMARY KEY (`id`, `dialogue_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='AI用户评价结果表';