<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.zyhl.hcy</groupId>
    <artifactId>yun-ai-api-outer</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <modelVersion>4.0.0</modelVersion>
  <groupId>com.zyhl.hcy.yun-ai-api-outer</groupId>
  <artifactId>yun-ai-api-outer-infrastructure</artifactId>
  <name>yun-ai-api-outer-infrastructure</name>


  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.zyhl.hcy.yun-ai-api-outer</groupId>
      <artifactId>yun-ai-api-outer-domain</artifactId>
    </dependency>

    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-boot-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
    </dependency>

    <!-- p6spy sql性能分析-->
    <dependency>
      <groupId>p6spy</groupId>
      <artifactId>p6spy</artifactId>
    </dependency>

    <dependency>
      <groupId>com.oceanbase</groupId>
      <artifactId>oceanbase-client</artifactId>
    </dependency>

    <!-- druid 数据库连接池-->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid-spring-boot-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>com.zyhl.hcy</groupId>
      <artifactId>hcy-plugins-starter-redis</artifactId>
    </dependency>
      <dependency>
          <groupId>com.zyhl.hcy</groupId>
          <artifactId>yun-ai-common-base</artifactId>
      </dependency>

  </dependencies>
</project>
