<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">


    <parent>
        <groupId>com.zyhl.hcy.platfrom</groupId>
        <artifactId>hcy-platform-parent</artifactId>
        <version>2.2.0-M</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zyhl.hcy</groupId>
    <artifactId>yun-ai-api-outer</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>

    <name>yun-ai-api-outer</name>

    <modules>
        <module>yun-ai-api-outer-web</module>
        <module>yun-ai-api-outer-application</module>
        <module>yun-ai-api-outer-domain</module>
        <module>yun-ai-api-outer-external</module>
        <module>yun-ai-api-outer-infrastructure</module>
        <module>yun-ai-api-outer-messaging</module>
    </modules>

    <properties>
        <project.pversion>1.0.0-SNAPSHOT</project.pversion>
        <hcy-platform-commons.version>2.0.4</hcy-platform-commons.version>
        <hcy-plugins-starter-uidgenerator.version>2.0.0-M</hcy-plugins-starter-uidgenerator.version>
        <hcy-plugins-starter-logger.version>2.3.1-SNAPSHOT</hcy-plugins-starter-logger.version>
        <hcy-plugins-starter-redis.version>2.1.0-M</hcy-plugins-starter-redis.version>
        <yun-ai-common.version>3.5.0.1-SNAPSHOT</yun-ai-common.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.zyhl.hcy.yun-ai-api-outer</groupId>
                <artifactId>yun-ai-api-outer-web</artifactId>
                <version>${project.pversion}</version>
            </dependency>

            <dependency>
                <groupId>com.zyhl.hcy.yun-ai-api-outer</groupId>
                <artifactId>yun-ai-api-outer-application</artifactId>
                <version>${project.pversion}</version>
            </dependency>

            <dependency>
                <groupId>com.zyhl.hcy.yun-ai-api-outer</groupId>
                <artifactId>yun-ai-api-outer-domain</artifactId>
                <version>${project.pversion}</version>
            </dependency>

            <dependency>
                <groupId>com.zyhl.hcy.yun-ai-api-outer</groupId>
                <artifactId>yun-ai-api-outer-external</artifactId>
                <version>${project.pversion}</version>
            </dependency>

            <dependency>
                <groupId>com.zyhl.hcy.yun-ai-api-outer</groupId>
                <artifactId>yun-ai-api-outer-infrastructure</artifactId>
                <version>${project.pversion}</version>
            </dependency>

            <dependency>
                <groupId>com.zyhl.hcy.yun-ai-api-outer</groupId>
                <artifactId>yun-ai-api-outer-messaging</artifactId>
                <version>${project.pversion}</version>
            </dependency>

            <dependency>
                <groupId>com.zyhl.hcy</groupId>
                <artifactId>hcy-platform-commons</artifactId>
                <version>${hcy-platform-commons.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zyhl.hcy</groupId>
                <artifactId>hcy-plugins-starter-uidgenerator</artifactId>
                <version>${hcy-plugins-starter-uidgenerator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zyhl.hcy</groupId>
                <artifactId>hcy-plugins-starter-logger</artifactId>
                <version>${hcy-plugins-starter-logger.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zyhl.hcy</groupId>
                <artifactId>hcy-plugins-starter-redis</artifactId>
                <version>${hcy-plugins-starter-redis.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-okhttp</artifactId>
                <version>${feign.version}</version>
            </dependency>

            <!-- 引入common新版本依赖 -->
            <dependency>
                <groupId>com.zyhl.hcy</groupId>
                <artifactId>yun-ai-common-base</artifactId>
                <version>${yun-ai-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zyhl.hcy</groupId>
                <artifactId>yun-ai-common-model-api</artifactId>
                <version>${yun-ai-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zyhl.hcy</groupId>
                <artifactId>yun-ai-common-platform-third</artifactId>
                <version>${yun-ai-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zyhl.hcy</groupId>
                <artifactId>yun-ai-common-intention</artifactId>
                <version>${yun-ai-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zyhl.hcy</groupId>
                <artifactId>yun-ai-common-rag</artifactId>
                <version>${yun-ai-common.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>

            <!-- 自然语言分词 https://github.com/hankcs/HanLP/tree/1.x -->
            <dependency>
                <groupId>com.hankcs</groupId>
                <artifactId>hanlp</artifactId>
                <version>portable-1.8.4</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
